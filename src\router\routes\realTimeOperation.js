import Layout from '@/layout'

// 功能模块-首页
export default [
  {
    path: '/realTimeDevelop',
    component: Layout,
    redirect: '/realTimeDevelop/realTimeOperation',
    name: 'realTimeDevelop',
    code: 'realTimeDevelop',
    meta: {
      title: '实时开发',
      icon: 'icon-quality',
      parentRouterName: 'assetsManage',
      code: 'realTimeDevelop',
    },
    children: [
      {
        path: 'realTimeOperation',
        redirect: '/realTimeDevelop/realTimeOperation/List',
        name: 'realTimeOperation',
        code: 'realTimeOperation',
        meta: {
          title: '实时作业',
          icon: 'icon-quality',
          parentRouterName: 'assetsManage',
          code: 'realTimeOperation',
        },
        component: () => import('@/views/real-time-operation/realTimeDevelop/index'),
        children: [
          {
            path: 'List',
            name: 'realTimeOperationList',
            hidden: true,
            component: () => import('@/views/real-time-operation/realTimeDevelop/list'),
            meta: {
              title: '实时作业',
              code: 'realTimeOperation',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
      {
        path: 'realTimeMonitor',
        redirect: '/realTimeDevelop/realTimeMonitor/List',
        name: 'realTimeMonitor',
        code: 'realTimeMonitor',
        hidden: true,
        component: () => import('@/views/real-time-operation/realTimeMonitor/index'),
        meta: { title: '实时监控', parentRouterName: 'assetsManage', code: 'realTimeMonitor' },
        children: [
          {
            path: 'List',
            name: 'realTimeMonitorList',
            hidden: true,
            component: () => import('@/views/real-time-operation/realTimeMonitor/list'),
            meta: {
              title: '实时监控',
              code: 'realTimeMonitor',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
    ],
  },
]
