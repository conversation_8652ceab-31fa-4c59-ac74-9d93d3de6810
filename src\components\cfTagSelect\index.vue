<script>
  import { ElSelect } from 'element-plus'
  export default {
    name: '',
    props: ['tagList', 'targetOptions'],
    emit: [''],
    setup(props, { emit, slots, attrs }) {
      const cssText = computed(
        () => `
      .cf-select-box{
        ${props?.tagList?.map((_, i) => {
          const item = props?.targetOptions?.find((item) => item.name === _)
          const [color, background] = item?.color?.split('_') || []
          return `
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag {
          background: ${background} !important;
          border: 1px solid var(---, ${color}) !important;
          color: ${color} !important;
        }
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag  .el-tag__close{
            color: ${color} !important;
         }
        `
        }).join(`
        `)})
        }
      }
      `,
      )
      return () =>
        h(
          'div',
          {
            class: ['cf-select-box'],
          },
          [
            h(ElSelect, { ...attrs, style: { width: '100%' } }, slots.default()),
            h('style', {}, cssText.value),
          ],
        )
    },
  }
</script>
<style lang="scss" scoped>
  .cf-select-box {
    width: 100%;
    :deep(.el-select) {
      margin-right: 4px;

      .el-select__wrapper {
        overflow-y: auto;
      }
    }
  }
</style>
