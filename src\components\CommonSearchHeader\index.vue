<template>
  <div class="container-search-header">
    <SvgIcon icon="icon-fastener" class="icon" />
    <div class="container-search-header-top">
      <n-button
        class="add-button"
        variant="solid"
        color="primary"
        @click="emits('add')"
        :disabled="!buttonAuthList.includes(props.code)"
        :code="props.code"
      >
        <SvgIcon icon="new-add" style="width: 16px; height: 16px; position: relative; top: -1px" />
        新建
      </n-button>
    </div>
    <div class="container-search-header-bottom">
      <div class="left-search-form">
        <slot name="searchForm"></slot>
      </div>
      <n-button
        class="search-button"
        style="width: 44px"
        variant="text"
        color="primary"
        @click="emits('clear')"
      >
        重置
      </n-button>
      <n-button class="search-button" color="primary" @click="emits('search')">查询</n-button>
    </div>
  </div>
</template>

<script setup>
  import { useStore } from 'vuex'
  const props = defineProps({
    code: {
      type: String,
      default: '',
    },
  })
  const emits = defineEmits(['add', 'search', 'clear'])
  const store = useStore()
  const { buttonAuthList } = toRefs(store.state.user)
  console.log(buttonAuthList)
</script>

<style lang="scss" scoped>
  .container-search {
    &-header {
      position: relative;
      width: 100%;
      height: 105px;
      margin-bottom: 8px;
      background: #ffffff;
      border-radius: 8px;
      .icon {
        position: absolute;
        top: 44px;
        left: -3px;
        width: 17px;
        height: 8px;
      }
      &-top {
        align-items: center;
        align-self: stretch;
        height: 52px;
        padding: 0px 8px 0px 16px;
        border-bottom: 1px solid var(---, #c5d0ea);
        .add-button {
          width: 80px;
          height: 32px;
          margin-top: 10px;
          font-size: 14px;
        }
      }
      &-bottom {
        height: 52px;
        padding: 0px 8px 0px 16px;
        .left-search-form {
          float: left;
          height: 32px;
          margin-top: 10px;
        }
        .search-button {
          float: right;
          width: 60px;
          height: 32px;
          margin-top: 10px;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
</style>
