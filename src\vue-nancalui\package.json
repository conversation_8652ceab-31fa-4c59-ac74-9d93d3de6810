{"name": "vue-nancalui", "version": "1.0.1", "license": "MIT", "description": "nancalui components based on Vite and Vue3", "keywords": ["frontend", "typescript", "ui-component", "components", "vue-components", "vue", "vue3", "vite", "jsx", "nanc<PERSON><PERSON>"], "homepage": "https://vue-nancalui.github.io/", "repository": {"type": "git", "url": "**************/vue-nancalui.git"}, "main": "vue-nancalui.umd.js", "module": "vue-nancalui.es.js", "style": "style.css", "peerDependencies": {"vue": "^3.2"}, "dependencies": {"@devui-design/icons": "^1.3.0", "@floating-ui/dom": "^0.4.4", "@types/lodash-es": "^4.17.4", "@vue/shared": "^3.2.33", "@vueuse/core": "8.9.4", "async-validator": "^4.0.7", "dayjs": "^1.11.7", "fs-extra": "^10.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.20", "memoize-one": "^6.0.0", "mitt": "^3.0.0", "nancalui-theme": "^0.0.1", "number-precision": "^1.6.0", "resize-observer-polyfill": "^1.5.1", "vue-router": "^4.0.3"}}