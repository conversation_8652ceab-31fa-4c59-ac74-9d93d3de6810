<template>
  <n-drawer
    v-model="addModalShow"
    :size="680"
    :close-on-click-overlay="false"
    :esc-key-closeable="false"
    @close="onCancel"
    title="配置"
  >
    <div class="drawer-body">
      <n-form
        ref="formRef"
        :data="formData"
        label-align="start"
        label-width="100px"
        label-suffix=":"
      >
        <n-form-item
          label="数据源名称"
          field="name"
          :rules="[{ required: true, message: '请输入数据源名称', trigger: 'blur' }]"
        >
          <n-input clearable v-model="formData.name" />
        </n-form-item>
        <n-form-item
          label="请求地址"
          field="url"
          :rules="[{ required: true, message: '请输入请求地址!' }]"
        >
          <n-input v-model="formData.url" :maxlength="100">
            <template #prepend>
              <n-select
                style="width: 80px"
                v-model="formData.method"
                placeholder="请选择"
                show-arrow
              >
                <n-option name="GET" value="GET" key="GET" />
                <n-option name="POST" value="POST" key="POST" />
              </n-select>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="请求头">
          <div class="params-box">
            <div
              class="params-item"
              :key="`requestHeader-${index}`"
              v-for="(item, index) in formData.requestHeader"
            >
              <n-input v-model="item.filedName" placeholder="键" :maxlength="50" />
              <n-input v-model="item.value" placeholder="值" />
              <n-input v-model="item.description" placeholder="备注" :maxlength="50" />
              <SvgIcon class="pointer" icon="add-circle" @click.stop.prevent="onHeadAdd()" />
              <SvgIcon
                class="pointer"
                v-if="formData.requestHeader.length > 1"
                icon="icon-del"
                @click.stop.prevent="onHeadDel(index)"
              />
            </div>
          </div>
        </n-form-item>
        <n-form-item class="request-form" label="请求体">
          <div class="mt-5">
            <formTable ref="requestBodyRefForm" :data="formData.requestBodyForm" />
          </div>
        </n-form-item>
        <n-form-item class="request-form" label="响应体">
          <div class="mt-5">
            <div class="params-box mt-13">
              <jsonTree ref="responseBodyRef" :data="formData.responseBody" />
            </div>
          </div>
        </n-form-item>
      </n-form>
    </div>
    <div class="drawerFooter">
      <n-button :loading="loadingCheck" @click="onCancel">取消</n-button>
      <n-button variant="solid" :loading="loadingCheck" @click="onSubmit">确定</n-button>
    </div>
  </n-drawer>
</template>
<script setup>
  import { defineEmits, ref, defineProps, watchEffect, nextTick } from 'vue'
  import api from '@/api'
  import { ElNotification } from 'element-plus'
  import { objToArray, arryToObj } from './utils'
  import { generateGuid } from '@/utils/tools'
  import jsonTree from './components/jsonTree.vue'
  import formTable from './components/formTable.vue'
  
  const emits = defineEmits(['onClose'])
  const props = defineProps({
    addModal: {
      type: Boolean,
      default: false,
    },
    selectRow: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  let addModalShow = ref(props.addModal)
  const formData = reactive({
    name: '',
    method: 'GET',
    requestHeader: [{ filedName: '', value: '', description: '' }],
    requestBodyForm: [],
    responseBody: [],
  })
  let loadingCheck = ref(false)
  const formRef = ref()
  const requestBodyRefForm = ref()
  const responseBodyRef = ref()
  watchEffect(() => {
    addModalShow.value = props.addModal
    if (props.selectRow.id) {
      getDetail(props.selectRow.id)
      // formData.method = props.selectRow.method
      // formData.url = props.selectRow.url
      // nextTick(() => {
      //   const headerList = Object.values(props.selectRow.requestHeader)
      //   formData.requestHeader = headerList || [{ key: '', value: '', desc: '' }]
      //   const formList = Object.values(props.selectRow.requestBody)
      //   formData.requestBodyForm = formList || [
      //     {
      //       filedName: '',
      //       type: 'txt',
      //       description: '',
      //     },
      //   ]
      //   formData.responseBody = objToArray(props.selectRow.responseBody) || [
      //     {
      //       filedName: 'root',
      //       type: 'object',
      //       description: '根节点',
      //       disabled: true,
      //       children: [],
      //       expanded: true,
      //       id: generateGuid(),
      //     },
      //   ]
      // })
    } else {
      nextTick(() => {
        formData.id = null
        formData.name = ''
        formData.method = 'GET'
        formData.requestHeader = [{ filedName: '', value: '', description: '' }]
        formData.requestBodyForm = [
          {
            filedName: '',
            type: 'txt',
            description: '',
          },
        ]
        formData.responseBody = [
          {
            filedName: 'root',
            type: 'object',
            description: '根节点',
            disabled: true,
            children: [],
            expanded: true,
            id: generateGuid(),
          },
        ]
      })
    }
  })
  const getDetail = async (id) => {
    const detailData = await api.documentManage.getSourceApDetail({ id })
    if (detailData.success) {
      formData.name = detailData.data.name
      formData.method = detailData.data.method
      formData.url = detailData.data.url
      formData.id = detailData.data.id
      nextTick(() => {
        const headerList = Object.values(detailData.data.requestHeader)
        formData.requestHeader =
          headerList.length > 0 ? headerList : [{ filedName: '', value: '', description: '' }]
        const formList = Object.values(detailData.data.requestBody)
        formData.requestBodyForm =
          formList.length > 0
            ? formList
            : [
                {
                  filedName: '',
                  type: 'txt',
                  description: '',
                },
              ]
        formData.responseBody = detailData.data.responseBody.root ? objToArray(detailData.data.responseBody) : [
          {
            filedName: 'root',
            type: 'object',
            description: '根节点',
            disabled: true,
            children: [],
            expanded: true,
            id: generateGuid(),
          },
        ]
      })
    }
  }
  const onHeadAdd = () => {
    formData.requestHeader.push({ filedName: '', value: '', description: '' })
  }
  const onHeadDel = (index) => {
    formData.requestHeader.splice(index, 1)
  }
  const onCancel = (type = false) => {
    formRef.value.resetFields()
    emits('onClose', type)
  }
  const onSubmit = () => {
    let isPAss = false
    let data = {}
    formRef.value.validate((valid) => {
      if (valid) {
        let requestHeaderObj = {}
        formData.requestHeader.forEach((i) => {
          if (i.filedName) {
            requestHeaderObj[i.filedName] = i
          }
        })
        data = {
          ...formData,
          requestHeader: requestHeaderObj,
        }
        isPAss = true
        if (formData.requestHeader.some((i) => i.filedName && !i.value)) {
          isPAss = false
          ElNotification({
            title: '提示',
            message: '请求头键值对，值未填写！',
            type: 'error',
          })
        }
        const responseBody = responseBodyRef.value.finshData()
        const requestBody = requestBodyRefForm.value.finshData()
        data.requestBody = JSON.parse(requestBody)
        data.responseBody = arryToObj(responseBody)
      }
    })
    setTimeout(() => {
      delete data.requestBodyForm
      console.log('submit3', data)
      if (isPAss) {
        if (formData.id) {
          api.documentManage.updateDataSourceApi(data).then((res) => {
            if (res.success) {
              ElMessage({
                message: '修改成功！',
                type: 'success',
              })
              onCancel(true)
            }
          })
        } else {
          api.documentManage.addDataSourceApi(data).then((res) => {
            if (res.success) {
              ElMessage({
                message: '操作成功！',
                type: 'success',
              })
              onCancel(true)
            }
          })
        }
      }
    })
  }
  onMounted(() => {})
</script>
<style lang="scss" scoped>
  .params-box {
    position: relative;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 16px;
    .params-item {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 21px 21px;
      grid-column-gap: 4px;
      align-items: center;
      justify-items: end;
      position: relative;
      &.col-2 {
        grid-template-columns: 1fr 1fr 21px 21px;
      }
      & + .params-item {
        margin-top: 8px;
      }
    }
    .import-btn {
      position: absolute;
      top: 24px;
      left: -70px;
    }
  }
  .request-form {
    :deep(.nancalui-form__control-container--horizontal) {
      display: block;
    }
  }
  .drawer-body {
    overflow-x: hidden;
    overflow-y: scroll;
    height: calc(100% - 90px);
    padding: 16px;
  }
  :deep(.nancalui-drawer.nancalui-drawer--right) {
    overflow: hidden !important;
  }
  :deep(.nancalui-input-slot__prepend){
    .nancalui-select{
      overflow: unset;
    }
  }
  .drawerFooter {
    .nancalui-button {
      margin-left: 8px;
    }
  }
</style>
