<template>
  <div class="pagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      layout="prev, pager, next, sizes, total, jumper"
      :total="total"
      :background="true"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { toRefs } from 'vue'

  const props = defineProps<{
    currentPage: number
    pageSize: number
    pageSizes: number[]
    total: number
  }>()
  const { currentPage, pageSize, pageSizes, total } = toRefs(props)

  const emits = defineEmits(['sizeChange', 'currentChange'])

  const handleSizeChange = (size: number) => {
    emits('sizeChange', size)
  }

  const handleCurrentChange = (current: number) => {
    emits('currentChange', current)
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .pagination {
    width: 100%;
    background-color: white;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.el-pagination) {
      padding: 20px 0;

      button {
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      ul li {
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }
        .el-input__wrapper {
          height: $paginationChildHeight;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
  }
</style>
