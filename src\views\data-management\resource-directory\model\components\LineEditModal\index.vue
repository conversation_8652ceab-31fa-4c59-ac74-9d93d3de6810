<template>
  <n-modal
    draggable
    v-model="showDialog"
    title="编辑关系线"
    class="line-edit-modal"
    width="1020px"
    :before-close="beforeClose"
  >
    <section class="entity content-table">
      <div class="entity-left">
        <div class="relation-div">
          <span class="name">{{ data.parentEntityName || '未获取到' }}</span>
          <span
            :class="[
              'line',
              data.lineType === '1' ? 'one-m' : data.lineType === '2' ? 'm-m' : 'one-one',
            ]"
          ></span>
          <span class="name">{{ data.subEntityName || '未获取到' }}</span>
        </div>
        <div class="table">
          <h3>关系编辑</h3>
          <div class="table-box">
            <div class="table-node">关系名</div>
            <div class="table-node"
              ><input v-model="data.name" :disabled="disabled" type="text"
            /></div>

            <div class="table-node">类型</div>
            <div class="table-node">
              <n-select v-model="data.type" :disabled="disabled" placeholder="请选择">
                <n-option
                  v-for="item in typeOptions"
                  :key="item.label"
                  :name="item.label"
                  :value="item.label"
                />
              </n-select>
            </div>

            <div class="table-node">关系</div>
            <div class="table-node">
              <n-select
                v-model="data.lineType"
                placeholder="请选择"
                :disabled="disabled"
                @value-change="changeLineType"
              >
                <n-option
                  v-for="item in relationOptions"
                  :key="item.label"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </div>
            <div class="table-node">键使用</div>
            <div class="table-node">
              <n-select v-model="data.keyUse" placeholder="请选择" :disabled="disabled">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>
            <div class="table-node">非继承处理</div>
            <div class="table-node">
              <n-select v-model="data.nonInheritanceProces" placeholder="请选择" disabled>
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>
          </div>

          <h3>父实体</h3>
          <div class="table-box">
            <div class="table-node">实体名</div>
            <div class="table-node">
              <input v-model="data.parentEntityName" type="text" disabled />
            </div>

            <div class="table-node">必需</div>
            <div class="table-node">
              <n-select v-model="data.parentMust" placeholder="请选择" :disabled="disabled">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>
          </div>

          <h3>子实体</h3>
          <div class="table-box">
            <div class="table-node">实体名</div>
            <div class="table-node">
              <input v-model="data.subEntityName" type="text" disabled />
            </div>

            <div class="table-node">必需</div>
            <div class="table-node">
              <n-select v-model="data.subMust" placeholder="请选择" :disabled="disabled">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>
          </div>
        </div>
      </div>

      <div class="entity-right">
        <n-tabs v-model="activeName" type="card">
          <n-tab title="键信息" id="first">
            <div class="table-box" style="grid-template-columns: 180px auto; margin-top: 28px">
              <div class="table-node">选择主键</div>
              <div class="table-node">
                <n-select v-model="mainKey" @value-change="changeFatherList" :disabled="disabled">
                  <n-option value="pk" name="主键" />
                  <n-option value="ohter" name="业务主键" v-if="$route.query.modType !== '3'" />
                </n-select>
              </div>
            </div>

            <h3>主外键属性</h3>
            <n-my-table
              class="multipleTable"
              :attrList="attrList"
              :tableData="fatherChildKeys"
              :isAction="false"
              :isPage="false"
              tooltip-effect="dark"
              maxHeight="400"
              row-key="guid"
            />
          </n-tab>
          <n-tab title="定义" id="second">
            <n-textarea v-model="data.definition" placeholder="请输入内容" :disabled="disabled" />
          </n-tab>
        </n-tabs>
      </div>
    </section>
    <template #footer v-if="!disabled">
      <n-modal-footer>
        <n-button variant="solid" color="primary" @click="onOk">确 定</n-button>
        <n-button @click="closeModal">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script lang="jsx">
  import FlowGraph from '../../graph'
  import { addName } from '../../utils/index.js'
  export default {
    props: {
      isShow: { type: Boolean, dufault: false },
      cell: { type: Boolean, dufault: null },
      graphList: { type: Object, default: null },
      subjectGuid: { type: String, default: '' },
    },
    data() {
      return {
        typeOptions: [
          {
            label: 'Normal',
          },
          {
            label: 'Pseudo',
          },
        ],
        relationOptions: [
          {
            value: '1',
            label: '1:M',
          },
          {
            value: '2',
            label: 'M:M',
          },
          {
            value: '3',
            label: '1:1',
          },
        ],
        activeName: 'first', // 当前tabs
        data: {},
        require: [
          {
            name: 'keyUse',
            message: '请选择键使用！',
          },
          {
            name: 'lineType',
            message: '请选择关系线类型！',
          },
          {
            name: 'nonInheritanceProces',
            message: '请选择非继承处理！',
          },
          {
            name: 'parentMust',
            message: '请选择父实体必须选项！',
          },
          {
            name: 'subMust',
            message: '请选择子实体必须选项！',
          },
        ],
        showDialog: false,
        fatherList: [],
        childList: [],
        fatherChildKeys: [],
        mainKey: 'pk',
        attrList: [
          { name: '属性名', prop: 'fatherKey' },
          {
            name: '处理方式',
            prop: 'keyType',
            renders: (row, index) => (
              <n-select
                v-model={row.keyType}
                disabled={this.disabled}
                onValueChange={(val) => {
                  this.changeKeyType(val, index)
                  // eslint-disable-next-line prettier/prettier
                }}
              >
                <n-option value='1' name='将同一属性指定为FK' disabled={!row.hasSameKey} />
                <n-option value='2' name='将其他属性指定为FK' />
                <n-option value='3' name='生成新的属性名' />
              </n-select>
            ),
          },
          {
            name: 'FK属性名',
            prop: 'childKey',
            renders: (row) => {
              if (row.keyType === '1') {
                return <span>{row.childKey}</span>
              } else if (row.keyType === '2') {
                return (
                  <n-select v-model={row.childKey} disabled={this.disabled}>
                    {this.childList &&
                      this.childList.map((item) => {
                        return <n-option key={item.key} value={item.key} name={item.key} />
                      })}
                  </n-select>
                )
              } else if (row.keyType === '3') {
                return <n-input v-model={row.childKey} disabled={this.disabled} />
              }
            },
          },
          {
            name: '字段名同步',
            prop: 'synchronization',
            renders: (row) => {
              if (row.keyType !== '3') {
                return <n-checkbox v-model={row.synchronization} disabled={this.disabled} />
              } else {
                return null
              }
            },
          },
        ],
      }
    },
    computed: {
      cellData() {
        return this.cell?.getData()
      },
      disabled() {
        return this.cell?.data?.disableMove
      },
    },
    watch: {
      isShow: {
        handler(val) {
          this.showDialog = val
          if (val) {
            this.loadData()
          }
        },
        immediate: true,
      },
    },
    methods: {
      changeKeyType(val, index) {
        if (val === '1') {
          const keyIndex = this.childList?.findIndex(
            (child) => child.key === this.fatherChildKeys[index].fatherKey,
          )
          this.fatherChildKeys[index].childKey = keyIndex > -1 ? this.childList[keyIndex]?.key : ''
        }
      },
      // 子实体必需改变，需重新设置外键的是否为空
      subMustChange() {
        const { graph } = FlowGraph
        const subNode = graph?.getCellById({ id: this.cell.target.cell })
        // 非共享实体才可以改变实体属性
        if (!subNode?.data?.isShare) {
          if (this.$route.query.modType === '3') {
            const newCol = subNode?.data.col?.map((item) => {
              const subMust = this.data.subMust === 'true' ? true : false
              // 外键且不是主键
              const isFK =
                this.fatherChildKeys?.findIndex((key) => key.childKey === item.col && !item.pk) > -1
              return {
                ...item,
                notNull: isFK ? subMust : item.notNull,
                notNullDisabled: isFK && subMust, // 必需不可编辑
              }
            })
            subNode.setData(
              {
                ...subNode.data,
                col: newCol,
              },
              { overwrite: true },
            )
          } else {
            const newAttr = subNode?.data.attr?.map((item) => {
              const subMust = this.data.subMust === 'true' ? true : false
              // 外键且不是主键
              const isFK =
                this.fatherChildKeys?.findIndex(
                  (key) => key.childKey === item.name && !item.substantiveKey,
                ) > -1
              return {
                ...item,
                notNull: isFK ? subMust : item.notNull,
                notNullDisabled: isFK && subMust, // 必需不可编辑
              }
            })
            subNode.setData(
              {
                ...subNode.data,
                attr: newAttr,
              },
              { overwrite: true },
            )
          }
        }
      },
      checkRepeate(changeName, val, checkList, key = '') {
        if (!val) {
          return ''
        }
        let newVal = val
        // 判断是否有重名
        let hasRepeat = false
        checkList.forEach((node) => {
          if (node[changeName] === newVal && key !== node.name) {
            hasRepeat = true
          }
        })
        if (hasRepeat) {
          if (newVal?.indexOf('_') !== -1) {
            const oldName = newVal?.split('_')[0]
            const index = Number(newVal?.split('_')[1])
            newVal = oldName + `_${index + 1}`
          } else {
            newVal = newVal + '_1'
          }
          return this.checkRepeate(changeName, newVal, checkList, key)
        } else {
          return newVal
        }
      },
      // 键使用改变，重新设置外键是否为主键
      onKeyUseChange(pIndex) {
        const allKeysList = []
        this.graphList?.forEach((graph) => {
          if (graph.subjectGuid === this.subjectGuid) {
            graph?.json.forEach((item) => {
              // 找到和当前线连接的实体相同的线
              if (
                item.shape === 'edge' &&
                item.data?.fatherChildKeys?.length &&
                item.id !== this.cell?.id
              ) {
                allKeysList.push(...item.data.fatherChildKeys)
              } else if (item.id === this.cell?.id) {
                allKeysList.push(...this.fatherChildKeys)
              }
            })
          }
        })
        const { graph } = FlowGraph
        const subNode = graph?.getCellById({ id: this.cell.target.cell })
        const fatherNode = graph?.getCellById({ id: this.cell.source.cell })
        // 非共享实体才可以改变实体属性
        if (this.$route.query.modType === '3') {
          const newCol = subNode?.data.col || []
          allKeysList.forEach((item) => {
            const subIndex = newCol?.findIndex((sub) => sub.col === item.childKey)
            const fatherCol = fatherNode?.data.col?.filter((sub) => sub.col === item.fatherKey)[0]
            if (item.keyType === '1' || item.keyType === '2') {
              // 在子集去找对应的属性，并判断是否同步，同步的话把父级的带过去
              if (item.synchronization) {
                newCol[subIndex].col = this.checkRepeate(
                  'col',
                  fatherCol.col,
                  newCol,
                  item.childKey,
                )
                newCol[subIndex].dataType = fatherCol.dataType
                newCol[subIndex].length = fatherCol.length
                newCol[subIndex].scale = fatherCol.scale
              }
            } else if (item.keyType === '3') {
              // 直接新增一个
              newCol.push({
                col: this.checkRepeate('col', fatherCol.col, newCol),
                dataType: fatherCol.dataType,
                length: fatherCol.length,
                scale: fatherCol.scale,
              })
            }
          })

          const finalCol = newCol?.map((item) => {
            const pk = this.data.keyUse === 'true' ? true : false
            const isFK = allKeysList?.findIndex((key) => key.childKey === item.col) > -1
            let fatherKey = ''
            if (isFK) {
              fatherKey = this.cellData.fatherChildKeys.find(
                (key) => key.childKey === item.name,
              )?.fatherKey
            }
            return {
              ...item,
              pk: isFK && !item.pk ? pk : item.pk,
              notNull: isFK && !item.pk ? pk : item.notNull,
              remarks:
                isFK && fatherKey
                  ? addName(item.remarks, `${this.cellData.parentEntityName}(${fatherKey})`)
                  : item.remarks,
              fk: item.remarks ? true : isFK,
            }
          })

          subNode.setData(
            {
              ...subNode.data,
              col: pIndex === -1 ? finalCol : newCol, // 有P标签的线，不能设置主外键，只能使用本身的数据
            },
            { overwrite: true },
          )
        } else {
          const newAttr = subNode?.data?.attr || []
          allKeysList.forEach((item) => {
            const subIndex = newAttr?.findIndex((sub) => sub.name === item.childKey)
            const fatherAttr = fatherNode?.data?.attr?.filter(
              (sub) => sub.name === item.fatherKey,
            )[0]
            if (item.keyType === '1' || item.keyType === '2') {
              // 在子集去找对应的属性，并判断是否同步，同步的话把父级的带过去
              // 字段名同步主要包括：列名、类型、数据类型、数据长度、小数点
              if (item.synchronization) {
                newAttr[subIndex].col = this.checkRepeate(
                  'col',
                  fatherAttr.col,
                  newAttr,
                  item.childKey,
                )
                newAttr[subIndex].type = fatherAttr.type
                newAttr[subIndex].dataType = fatherAttr.dataType
                newAttr[subIndex].length = fatherAttr.length
                newAttr[subIndex].scale = fatherAttr.scale
              }
            } else if (item.keyType === '3') {
              // 直接新增一个
              newAttr.push({
                name: this.checkRepeate('name', item.childKey, newAttr),
                col: this.checkRepeate('col', fatherAttr.col, newAttr),
                type: fatherAttr.type,
                dataType: fatherAttr.dataType,
                length: fatherAttr.length,
                scale: fatherAttr.scale,
              })
            }
          })

          const finalAttr = newAttr?.map((item) => {
            const substantiveKey = this.data.keyUse === 'true' ? true : false
            const isFK = allKeysList?.findIndex((key) => key.childKey === item.name) > -1
            let fatherKey = ''
            if (isFK) {
              fatherKey = this.cellData.fatherChildKeys.find(
                (key) => key.childKey === item.name,
              )?.fatherKey
            }
            const result = {
              ...item,
              substantiveKey: isFK && !item.substantiveKey ? substantiveKey : item.substantiveKey,
              notNull: isFK && !item.substantiveKey ? substantiveKey : item.notNull,
              remarks:
                isFK && fatherKey
                  ? addName(item.remarks, `${this.cellData.parentEntityName}(${fatherKey})`)
                  : item.remarks,
              fk: item.remarks ? true : isFK,
            }
            return result
          })
          const setAttr = pIndex === -1 ? finalAttr : newAttr
          subNode.data.attr = []
          subNode.data.attr = setAttr
          subNode.setData(
            {
              ...subNode.data,
            },
            { overwrite: true },
          )
        }
      },
      getKeyList() {
        const { graph } = FlowGraph
        const parentNode = graph?.getCellById({ id: this.cell?.source.cell })
        const subNode = graph?.getCellById({ id: this.cell?.target.cell })
        // 物理模型
        if (this.$route.query.modType === '3') {
          this.fatherList = parentNode?.data.col.filter((item) => {
            item.key = item.col
            if (this.mainKey === 'pk') {
              return item.pk
            } else {
              return !item.pk
            }
          })
          this.childList = subNode?.data?.col?.map((item) => {
            return { ...item, key: item.col }
          })
        } else {
          this.fatherList = parentNode?.data?.attr?.filter((item) => {
            item.key = item.name
            if (this.mainKey === 'pk') {
              return item.substantiveKey
            } else {
              return item.candidate === 'true'
            }
          })
          this.childList = subNode?.data?.attr?.filter((item) => {
            item.key = item.name
            return item
          })
        }
      },
      getFatherChildKeys() {
        this.getKeyList()
        this.fatherChildKeys = this.fatherList?.map((item) => {
          const newKey = {
            fatherKey: item.key,
            childKey: this.childList?.filter((child) => child.key === item.key)[0]?.key || '',
            keyType: this.childList?.filter((child) => child.key === item.key).length ? '1' : '',
            synchronization: false,
            hasSameKey: this.childList?.findIndex((child) => child.key === item.key) > -1,
          }
          return newKey
        })
      },
      changeOldKeysToNew() {
        this.getKeyList()
        const oldFatherChildKeys = this.data.fatherChildKeys
        this.fatherChildKeys = this.fatherList?.map((item) => {
          const hasKey = oldFatherChildKeys.find((i) => i.fatherKey === item.key)
          const newKey = {
            fatherKey: item.key,
            childKey: hasKey
              ? hasKey.childKey
              : this.childList?.find((child) => child.key === item.key)?.key || '',
            keyType: hasKey
              ? hasKey.keyType
              : this.childList?.filter((child) => child.key === item.key).length
              ? '1'
              : '',
            synchronization: hasKey ? hasKey.synchronization : false,
            hasSameKey: this.childList?.findIndex((child) => child.key === item.key) > -1,
          }
          return newKey
        })
      },
      changeFatherList() {
        if (this.data.mainKey === this.mainKey) {
          this.changeOldKeysToNew()
        } else {
          this.getFatherChildKeys()
        }
      },
      changeLineType(value) {
        const val = value.value
        if (val === '1' || val === '3') {
          this.data.parentMust = 'true'
        } else {
          this.data.parentMust = 'false'
        }
        if (val === '3') {
          this.data.subMust = 'true'
        } else {
          this.data.subMust = 'false'
        }
      },

      // 获取实体列名
      loadData() {
        this.data = { ...this.cell?.getData() }
        this.mainKey = this.data.mainKey || 'pk'
        if (this.data.fatherChildKeys) {
          this.changeOldKeysToNew()
        } else {
          this.getFatherChildKeys()
        }
      },
      beforeClose(done) {
        this.data = null
        done()
        this.closeModal()
      },
      closeModal() {
        this.$emit('closeModal')
      },
      onOk() {
        for (const item of this.require) {
          if (!this.data[item.name]) {
            this.$message.error(item.message)
            break
          }
        }
        const index = this.cell?.getLabels()?.findIndex((item) => item.position.distance === 0.25)
        if (index > -1) {
          this.cell?.setLabelAt(index, {
            attrs: {
              label: { text: this.data.name || '' },
            },
            position: {
              distance: 0.25,
            },
          })
        } else {
          this.cell?.appendLabel({
            attrs: {
              label: { text: this.data.name || '' },
            },
            position: {
              distance: 0.25,
            },
          })
        }
        //设置主外键改变
        const pIndex = this.cell?.getLabels()?.findIndex((item) => item.attrs.label.text === 'P')
        this.onKeyUseChange(pIndex)
        // 设置子实体必需
        this.subMustChange()
        this.$message.success('修改成功！')
        this.showDialog = false
        this.cell?.setData({
          ...this.data,
          fatherChildKeys: this.fatherChildKeys,
          mainKey: this.mainKey,
        })
        // const nowLabel = this.cell.getLa
        this.$emit('closeModal', true)
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .line-edit-modal {
    :deep(.el-dialog) {
      width: 1020px;

      .el-tabs {
        &__header {
          padding: 0;
          border: none;
        }
        &__item {
          border: 1px solid #cfcfcf !important;
          margin-right: 4px;
          background: #f4f4f4;

          &.is-active {
            background: #fff;
            border-radius: 2px 2px 0px 0px;
            color: $themeFontColor !important;
            border-bottom-color: #fff !important;
          }
        }
        &__content {
          border: 1px solid #cfcfcf;
        }
      }
    }
  }
  :deep(.table-node .nancalui-select__selection) {
    height: 100%;
    border-color: transparent;
    padding: 0 11px;
  }
  h3 {
    line-height: 28px;
    background: rgba(37, 123, 255, 0.08);
    font-size: 14px;
    margin: 0;
    padding-left: 10px;
  }
  .entity {
    display: flex;
    padding: 14px;

    &-left {
      width: 290px;
      height: 420px;
      margin-right: 14px;

      .table {
        height: calc(100% - 58px);
        border: 1px solid #cfcfcf;
        overflow: auto;
      }
      .relation-div {
        height: 44px;
        margin-bottom: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        .name {
          display: inline-block;
          width: 98px;
          height: 44px;
          background: rgba(37, 123, 255, 0.08);
          border-radius: 2px;
          border: 1px solid #4991ff;
          line-height: 44px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
          text-align: center;
          word-break: keep-all;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .line {
          display: inline-block;
          width: 92px;
          height: 12px;
          background-size: 100% 100%;
          &.one-m {
            background-image: url('@img/data_model/1-m.png');
          }
          &.m-m {
            background-image: url('@img/data_model/m-m.png');
          }
          &.one-one {
            height: 2px;
            background-image: url('@img/data_model/1-1.png');
          }
          &.m-one {
            background-image: url('@img/data_model/m-1.png');
          }
        }
      }
    }
    &-right {
      width: calc(100% - 304px);
      // border: 1px solid #cfcfcf;

      :deep(.el-tabs) {
        height: 100%;
        .el-tabs__content {
          height: calc(100% - 31px);

          > div {
            height: 100%;
          }
          .el-textarea {
            height: 100%;

            &__inner {
              height: 100%;
              background: #fff;
              border: none;
            }
          }
        }
      }
    }

    .table {
      &-box {
        display: grid;
        grid-template-columns: 90px auto;
        border-bottom: 1px solid #e1e1e1;
      }
      &-node {
        height: 30px;
        line-height: 30px;
        border-top: 1px solid #e1e1e1;
        padding: 0 10px;
        box-sizing: border-box;
        color: #333333;
        font-size: 12px;

        &:nth-child(odd) {
          border-right: 1px solid #e1e1e1;
        }
        input {
          width: 100%;
          height: 100%;
          border: none;
          outline: none;
          padding: 0 15px;
        }
        :deep(.el-input__inner) {
          border: none;
        }
      }
    }
  }
</style>
