<template>
  <div class="container">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">标签名称：</span>
          <n-input
            v-model="state.filterSearch.name"
            placeholder="请输入标签名称"
            size="small"
            clearable
          />
          <span class="label">状态：</span>
          <n-select v-model="state.filterSearch.status" placeholder="状态" allow-clear filter>
            <n-option
              v-for="item in state.statusOptions"
              :key="item.value"
              :name="item.label"
              :value="item.value"
            />
          </n-select>
          <span class="label">创建时间：</span>
          <n-range-date-picker-pro
            v-model="state.filterSearch.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :shortcuts="state.shortcuts"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>标签分类</div>
          <SvgIcon
            class="nc-right nc-m-r-12"
            icon="icon-tree-add2"
            size="16"
            @click="onClassAdd('')"
          />
        </div>
        <div class="class-list nc-m-t-10">
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="classState.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchClassFn"
          />
          <div class="nc-m-t-8 class-list-wrap class-tree">
            <n-tree :data="classState.data" ref="treeRef" @node-click="onClassClick">
              <template #content="{ nodeData }">
                <SvgIcon class="tree-icon" v-if="nodeData.children?.length" icon="open-file" />
                <SvgIcon class="tree-icon" v-else icon="icon-file-tree" />
                <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
                <div class="tree-tool">
                  <SvgIcon
                    class="tree-icon-tool"
                    icon="icon-tree-add2"
                    size="16"
                    title="新增"
                    @click.stop="onClassAdd(nodeData)"
                  />
                  <SvgIcon
                    v-if="nodeData.id !== 'all'"
                    class="tree-icon-tool"
                    icon="icon-new-edit"
                    size="16"
                    title="编辑"
                    @click.stop="onClassEdit(nodeData)"
                  />
                  <SvgIcon
                    v-if="nodeData.id !== 'all'"
                    class="tree-icon-tool"
                    icon="filter-cut"
                    size="16"
                    title="删除"
                    @click.stop="onClassDelete(nodeData)"
                  />
                </div>
              </template>
              <template #icon="{ nodeData, toggleNode }">
                <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
                <span
                  v-else
                  @click="
                    (event) => {
                      event.stopPropagation()
                      toggleNode(nodeData)
                    }
                  "
                >
                  <SvgIcon
                    v-if="nodeData.expanded"
                    class="nancalui-tree-switch"
                    icon="tree-contract-new"
                  />
                  <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
                </span>
              </template>
            </n-tree>
          </div>
        </div>
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onTagAdd" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          新建标签</n-button
        >
        <div class="table" v-loading="state.isLoad">
          <CfTable
            actionWidth="200"
            :table-head-titles="state.tableHeadTitles"
            :tableConfig="{
              data: state.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: state.pagination.total,
              pageSize: state.pagination.pageSize,
              currentPage: state.pagination.currentPage,
              onCurrentChange: (v) => {
                state.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #status="{ row }">
              <div class="status-box">
                <span :class="row.status">{{
                  state.statusOptions.filter((item) => item.value === row.status)[0].label
                }}</span>
              </div>
            </template>
            <template #editor="{ row }">
              <n-button
                v-if="row.status === 'wait_publish' || row.status === 'off'"
                variant="text"
                color="primary"
                @click="tagPublish(row)"
                >发布</n-button
              >
              <n-button
                v-if="row.status === 'publish'"
                variant="text"
                color="primary"
                @click="tagUnpublish(row)"
                >下架</n-button
              >
              <n-button
                v-if="row.status === 'wait_publish' || row.status === 'off'"
                variant="text"
                color="primary"
                @click="tagEdit(row)"
                >编辑</n-button
              >
              <n-button
                v-if="row.status === 'wait_publish' || row.status === 'off'"
                variant="text"
                color="primary"
                :disabled="row.status === 'off' && row.isRef"
                @click="tagDelete(row)"
                >删除</n-button
              >
              <n-button variant="text" color="primary" @click="tagView(row)">查看</n-button>
            </template>
          </CfTable>
        </div>
      </div>
      <n-modal
        v-model="classState.open"
        title="新建标签分类"
        class="largeDialog has-top-padding"
        width="560px"
        :close-on-click-overlay="false"
        @close="classState.open = false"
      >
        <n-form
          :data="classState.editData"
          labelSuffix="："
          :rules="classState.rules"
          ref="classFormRef"
        >
          <n-form-item field="name" label="分类名称">
            <n-input
              v-model="classState.editData.name"
              placeholder="请输入分类名称"
              maxLength="200"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-modal-footer>
            <n-button @click="classState.open = false">取消</n-button>
            <n-button color="primary" variant="solid" @click="onClassSave">确定</n-button>
          </n-modal-footer>
        </template>
      </n-modal>

      <n-modal
        v-model="addTagState.open"
        :title="addTagState.modalTitle"
        class="largeDialog has-top-padding add-tag"
        width="560px"
        :close-on-click-overlay="false"
        @close="closeTagModal"
      >
        <n-form
          :data="addTagState.data"
          labelSuffix="："
          :rules="addTagState.rules"
          ref="addTagFormRef"
        >
          <n-form-item field="categoryId" label="标签分类">
            <span class="view-span" v-if="addTagState.isView">{{
              addTagState.data.categoryName
            }}</span>
            <el-tree-select
              v-else
              v-model="addTagState.data.categoryId"
              :data="classState.data"
              style="width: 100%"
              node-key="id"
              check-strictly
              :default-expanded-keys="['all']"
              placeholder="请选择标签分类"
              :props="{
                label: 'name',
                value: 'id',
                children: 'children',
              }"
              filterable
              clearable
            />
          </n-form-item>

          <n-form-item field="text" label="标签名称">
            <span class="view-span" v-if="addTagState.isView">{{ addTagState.data.text }}</span>
            <n-input
              v-else
              v-model="addTagState.data.text"
              placeholder="请输入标签名称"
              maxLength="50"
            />
          </n-form-item>
          <n-form-item label="标签描述">
            <span class="view-span" v-if="addTagState.isView">{{
              addTagState.data.description
            }}</span>
            <n-textarea
              v-else
              v-model="addTagState.data.description"
              rows="5"
              placeholder="请输入描述信息"
            />
          </n-form-item>

          <n-form-item label="标签颜色">
            <span class="view-span" v-if="addTagState.isView">
              <div
                :style="{
                  'background-color': addTagState.data.color.split('_')[0],
                  borderRadius: '4px',
                  width: '20px',
                  height: '20px',
                  display: 'inline-block',
                  'vertical-align': 'middle',
                }"
              ></div>
            </span>
            <div v-else class="custom">
              <div class="custom-color"
                >随机颜色<n-switch v-model="addTagState.data.randomColor" size="sm"
              /></div>
              <div v-if="!addTagState.data.randomColor" class="custom-color second">
                <div
                  v-for="(item, index) in addTagState.colorList"
                  :key="index"
                  :style="'background-color:' + item.value"
                  :class="{ 'custom-color-label': true, checked: item.checked }"
                  @click.prevent.stop="checkFn(index)"
                >
                  <SvgIcon class="icon" icon="icon-check-white" title="选中" />
                </div>
              </div>
            </div>
          </n-form-item>
        </n-form>
        <template #footer>
          <n-modal-footer>
            <n-button @click="closeTagModal">取消</n-button>
            <n-button color="primary" variant="solid" @click="onTagSave">确定</n-button>
          </n-modal-footer>
        </template>
      </n-modal>
    </section>
  </div>
</template>
<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'
  import api from '@/api/index'
  import { cloneDeep } from 'lodash'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const route = useRoute()

  const classState = reactive({
    rules: {
      name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    },
    treeSearchText: '',
    open: false,
    editData: {
      name: '',
      pid: null,
      id: null,
    },
    data: [],
  })
  const addTagState = reactive({
    modalTitle: '新增标签',
    open: false,
    isView: false,
    rules: {
      categoryId: [
        { required: true, type: 'number', message: '请选择标签分类', trigger: 'change' },
      ],
      text: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
    },
    treeData: [],
    data: {
      categoryId: '',
      text: '',
      description: '',
      color: '',
      randomColor: true,
      id: '',
    },
    colorList: [
      { value: '#ED4EA5', bgColor: '#FFF0F6', borderColor: '#FFD4E6', checked: true },
      { value: '#FA5924', bgColor: '#FFF2E8', borderColor: '#FFD0B6', checked: false },
      { value: '#FF8500', bgColor: '#FFF7E4', borderColor: '#FFD386', checked: false },
      { value: '#8FDB00', bgColor: '#FBFFE3', borderColor: '#E5FF7E', checked: false },
      { value: '#0093FF', bgColor: '#E2F8FF', borderColor: '#7BD7FF', checked: false },
      { value: '#7C28D9', bgColor: '#FBF0FF', borderColor: '#DAABFC', checked: false },
      { value: '#FFA900', bgColor: '#FFFBE4', borderColor: '#FFE481', checked: false },
      { value: '#FF0019', bgColor: '#FFF0F0', borderColor: '#FF9E9B', checked: false },
      { value: '#00C6C4', bgColor: '#E0FFFB', borderColor: '#5FEBDE', checked: false },
      { value: '#00C700', bgColor: '#F4FFEB', borderColor: '#A8ED83', checked: false },
    ],
  })
  const state = reactive({
    shortcuts: ENUM.SHORTCUTS,
    statusOptions: [
      { label: '待发布', value: 'wait_publish' },
      { label: '审批中', value: 'approval' },
      { label: '已发布', value: 'publish' },
      { label: '已下架', value: 'off' },
    ],
    tableList: [],
    filterSearch: {
      name: '',
      status: '',
      time: [],
      // time: [new Date(), new Date()],
    },
    searchData: {
      name: '',
      status: '',
      time: [],
      // time: [formartTime(new Date()), formartTime(new Date(), true)],
    },
    tableHeadTitles: [
      { prop: 'text', name: '标签名称' },
      { prop: 'categoryName', name: '标签分类' },
      { prop: 'status', name: '状态', slot: 'status' },
      { prop: 'description', name: '描述' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间' },
    ],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    isLoad: false,
  })
  const treeRef = ref(null)
  const classFormRef = ref(null)
  const addTagFormRef = ref(null)
  // 获取左侧树分类
  function getTreeList() {
    api.documentManage.getTagLibraryClassTree().then((res) => {
      let { success, data } = res
      if (success) {
        classState.data = [
          {
            id: 'all',
            pid: null,
            name: '全部',
            selected: true,
            expanded: true,
            disabled: true,
            children: data,
          },
        ]
      }
    })
  }
  // 选择分类
  function onClassClick(row) {
    classState.editData = {
      name: row.name,
      id: row.id,
      pid: row.pid,
    }
    onSearch()
  }
  // 新增分类
  function onClassAdd(node) {
    classState.open = true
    classState.editData.name = ''
    if (node) {
      classState.editData.pid = node.id === 'all' ? null : node.id
    } else {
      classState.editData.pid = classState.editData.id
    }
    classState.editData.id = null
  }
  // 编辑分类-打开弹框
  function onClassEdit(row) {
    classState.open = true
    classState.editData = cloneDeep(row)
  }
  //新增/编辑分类
  const onClassSave = () => {
    classFormRef.value.validate().then((valid) => {
      if (valid) {
        const apiUrl = classState.editData.id ? 'updateTagLibraryClass' : 'addTagLibraryClass'
        api.documentManage[apiUrl](classState.editData).then((res) => {
          if (res.success) {
            classState.open = false
            classState.treeSearchText = ''
            proxy.$message.success(classState.editData.id ? '保存成功' : '新增成功')
            getTreeList()
          }
        })
      }
    })
  }
  // 删除分类
  async function onClassDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条分类',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.deleteTagLibraryClass({ id: row.id }).then((res) => {
          if (res.success) {
            getTreeList()
            proxy.$message.success('删除成功！')
          }
        })
      },
    })
  }
  //左侧树搜索
  function searchClassFn() {
    treeRef.value.treeFactory.searchTree(classState.treeSearchText, {
      isFilter: true,
      matchKey: 'name',
    })
  }

  // 查询右侧列表
  function onSearch(init = true) {
    if (init) {
      state.pagination.currentPage = 1
    }
    state.isLoad = true
    api.documentManage
      .getTagLibraryTagList({
        pageNum: state.pagination.currentPage,
        pageSize: state.pagination.pageSize,
        condition: {
          categoryId: classState.editData.id === 'all' ? '' : classState.editData.id,
          text: state.searchData.name,
          status: state.searchData.status,
          startTime: state.searchData.time[0] || null,
          endTime: state.searchData.time[1] || null,
        },
      })
      .then((res) => {
        state.tableList = res.data.list
        state.pagination.total = res.data.total
        state.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        state.isLoad = false
      })
  }

  // 选中颜色
  const checkFn = (index) => {
    addTagState.colorList.forEach((val, ind) => {
      val.checked = false
      if (index === ind) {
        val.checked = true
      }
    })
  }

  // 新增标签弹框-开
  const onTagAdd = () => {
    addTagState.open = true
    if (classState.editData && classState.editData?.id !== 'all') {
      addTagState.data.categoryId = classState.editData.id
    }
  }

  // 新增标签
  const onTagSave = () => {
    addTagFormRef.value.validate().then((valid) => {
      if (valid) {
        let checkColorItem = addTagState.colorList.filter((val) => val.checked)[0]

        if (addTagState.data.randomColor) {
          let index = Math.floor(Math.random() * 9)
          checkColorItem = addTagState.colorList[index]
        }
        addTagState.data.color =
          checkColorItem.value + '_' + checkColorItem.bgColor + '_' + checkColorItem.borderColor
        let interFaceName = 'addTagLibraryTag'
        if (addTagState.data?.id) {
          interFaceName = 'updateTagLibraryTag'
        }

        api.documentManage[interFaceName](addTagState.data).then((res) => {
          let { success, data } = res
          if (success) {
            proxy.$message.success('操作成功!')
            closeTagModal()
            onSearch()
          }
        })
      }
    })
  }
  // 标签发布
  const tagPublish = (row) => {
    api.documentManage.tagLibraryTagPublish({ id: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        proxy.$message.success('操作成功!')
      }
    })
  }
  // 标签下架
  const tagUnpublish = (row) => {
    proxy.$MessageBoxService.open({
      title: '下架标签',
      content: '下架后标签将不可使用，请确认是否下架？',
      save: () => {
        api.documentManage.tagLibraryTagOff({ id: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            proxy.$message.success('操作成功!')
          }
        })
      },
    })
    // api.documentManage.validTagLibraryTag({ id: row.id }).then((res) => {
    //   let { success, data } = res
    //   if (success) {
    //     if (data) {
    //       proxy.$MessageBoxService.open({
    //         title: '下架标签',
    //         content: '下架后标签将不可使用，请确认是否下架？',
    //         save: () => {
    //           api.documentManage.tagLibraryTagOff({ id: row.id }).then((res) => {
    //             if (res.success) {
    //               onSearch()
    //               proxy.$message.success('操作成功!')
    //             }
    //           })
    //         },
    //       })
    //     } else {
    //       proxy.$MessageBoxService.open({
    //         title: '下架标签',
    //         content: '该标签已被使用，不可下架。',
    //       })
    //     }
    //   }
    // })
  }
  // 标签编辑
  const tagEdit = (row) => {
    addTagState.modalTitle = '编辑标签'
    addTagState.data = {
      categoryId: row.categoryId,
      text: row.text,
      description: row.description,
      color: row.color,
      randomColor: row.randomColor,
      id: row.id,
    }
    if (!row.randomColor)
      addTagState.colorList.forEach((item) => {
        if (item.value === row.color.split('_')[0]) {
          item.checked = true
        } else {
          item.checked = false
        }
      })
    addTagState.open = true
  }

  // 标签查看
  const tagView = (row) => {
    addTagState.modalTitle = '查看标签'
    addTagState.rules = {}
    addTagState.isView = true
    addTagState.open = true
    addTagState.data = {
      categoryId: row.categoryId,
      categoryName: row.categoryName,
      text: row.text,
      description: row.description,
      color: row.color,
      id: row.id,
    }
  }
  // 标签删除
  const tagDelete = (row) => {
    api.documentManage.validTagLibraryTag({ id: row.id }).then((res) => {
      let { success, data } = res
      if (success) {
        if (data) {
          proxy.$MessageBoxService.open({
            title: '删除标签',
            content: '删除后将不可恢复，请确认是否删除？',
            save: () => {
              api.documentManage.deleteTagLibraryTag({ id: row.id }).then((res) => {
                if (res.success) {
                  onSearch()
                  proxy.$message.success('操作成功!')
                }
              })
            },
          })
        } else {
          proxy.$MessageBoxService.open({
            title: '删除标签',
            content: '该标签已被使用，不可删除。',
          })
        }
      }
    })
  }
  //关闭新增/查看/编辑 标签弹框
  const closeTagModal = () => {
    addTagState.open = false
    addTagState.isView = false
    addTagState.modalTitle = '新建标签'
    addTagState.rules = {
      categoryId: [
        { required: true, type: 'number', message: '请选择标签分类', trigger: 'change' },
      ],
      text: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
    }
    addTagState.data = {
      randomColor: true,
      categoryId: '',
      text: '',
      description: '',
      color: '',
      id: '',
    }
  }

  // 排序
  // function sortFn(data) {
  //   const { order, prop } = data
  //   const sortConditions = [
  //     {
  //       fieldName: prop,
  //       sort: order === 'descending' ? 'DESC' : 'ASC',
  //     },
  //   ]
  //   onSearch(false, sortConditions)
  // }
  function startSearch() {
    state.searchData.name = state.filterSearch.name
    state.searchData.status = state.filterSearch.status
    if (state.filterSearch.time && state.filterSearch.time[0] && state.filterSearch.time[1]) {
      state.searchData.time = [
        formartTime(state.filterSearch.time[0]),
        formartTime(state.filterSearch.time[1], true),
      ]
    } else {
      state.searchData.time = ['', '']
    }

    onSearch(true)
  }

  // 重置
  function resetFn() {
    state.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    state.filterSearch = {
      name: '',
      status: '',
      time: [],
    }
    state.searchData = {
      name: '',
      status: '',
      time: [],
    }
    classState.editData = {}
    onSearch(true)
  }

  function refreshData() {
    getTreeList()
    onSearch(false)
  }
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        refreshData()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true }, // { immediate: true } 表示立即执行一次
  )
  onMounted(() => {
    refreshData()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    :deep(.nancalui-splitter__bar) {
      .nancalui-splitter__collapse {
        display: none;
      }
    }
  }
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;

    .left {
      min-width: 280px;
      height: 100%;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      width: calc(100% - var(--aside-width));
      height: 100%;
      margin-left: 10px;
      background: #fff;
      border-radius: $cf-border-radius;
      .common-table {
        .status-box span {
          display: inline-block;
          padding: 0 4px;
          font-weight: 400;
          font-size: 12px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 20px;
          border-radius: 2px;
          &.wait_publish {
            color: #1e89ff;
            background: var(---, #ebf4ff);
            border: 1px solid #99c9ff;
          }
          &.approval {
            color: #fe8624;
            background: #fff4e6;
            border: 1px solid #ffba70;
          }
          &.publish {
            color: var(---Success-, #2ca340);
            background: var(---Success-, #ebfaee);
            border: 1px solid var(---Success-, #c0e3c6);
          }
          &.off {
            color: var(----, #1d2129);
            background: var(---, #f0f2f5);
            border: 1px solid var(---, #c9cdd4);
          }
        }
      }
    }
  }
  .template-list-title {
    width: 100%;
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .class-list {
    width: 100%;
    padding: 12px;
    .class-list-wrap {
      height: calc(100vh - 280px);
      overflow: auto;
    }
    .class-tree {
      height: calc(100vh - 280px);
      overflow: auto;
      .tree-tool {
        width: 60px;
        margin-left: auto;
      }
      .tree-icon-tool {
        display: none;
        margin-left: 4px;
        font-size: 16px;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
      &:hover .tree-icon {
        display: inline-block;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content--value-wrapper) {
      display: flex;
      width: 100%;
      > svg {
        flex-shrink: 0;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:hover .tree-icon-tool) {
      display: inline-block;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .class-list-item {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      line-height: 20px;
      cursor: pointer;
      .class-con {
        display: flex;
        align-items: center;
        width: 230px;
        .class-label {
          flex: 1;
        }
      }
      .class-tools {
        display: none;
        margin-left: 14px;
      }
      &.is-active {
        background: #ebf4ff;
      }
      &:hover {
        background: #ebf4ff;
        border-radius: 2px;
        .class-tools {
          display: block;
        }
      }
    }
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
</style>
<style lang="scss">
  .nancalui-modal {
    &.add-tag {
      .view-span {
        display: inline-block;
        color: #1d2129;
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 28px;
      }
      .custom {
        padding: 6px;

        &-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: rgba(0, 0, 0, 0.9);
          font-weight: bolder;
          font-size: 14px;

          .icon {
            color: #8091b7;
            font-size: 16px;
            cursor: pointer;
          }
        }

        &-name {
          margin-top: 8px;
        }

        &-color {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;

          .nancalui-switch {
            margin-left: 6px;
          }

          &-label {
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border: 1px solid #fff;
            border-radius: 4px;

            .icon {
              display: none;
              color: #fff;
              font-size: 12px;
            }

            &.checked {
              box-shadow: 0 0 0 1px #447dfd;

              .icon {
                display: block;
              }
            }
          }
          &.second {
            margin-top: 8px;
          }
        }

        &-footer {
          margin-top: 14px;
          text-align: right;

          .nancalui-button {
            min-width: 40px;
            height: 24px;
            padding: 0 8px;
            font-size: 12px;
            line-height: 24px;
          }
        }
      }
    }
  }
</style>
