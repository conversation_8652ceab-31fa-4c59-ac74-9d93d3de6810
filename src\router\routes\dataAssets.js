import Layout from '@/layout'

// 功能模块-数据资产
export default [
  {
    path: '/dataAssets',
    component: Layout,
    redirect: '/dataAssets/assetsRegister',
    name: 'dataAssets',
    code: 'asset_panorama',
    meta: {
      title: '数据资产',
      icon: 'icon-panorama',
      parentRouterName: 'governanceManage',
      code: 'asset_panorama',
      unShowProject: true,
    },
    children: [
      // 资源分类
      {
        path: 'resourceClassify',
        name: 'resourceClassify',
        code: 'resourceClassify',
        redirect: '/dataAssets/resourceClassify/resourceClassifyList',
        component: () => import('@/views/data-assets/resourceClassify/index.vue'),
        meta: {
          title: '资源分类',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
          code: 'asset_panorama',
        },
        children: [
          // 资源分类
          {
            path: 'resourceClassifyList',
            name: 'resourceClassifyList',
            component: () => import('@/views/data-assets/resourceClassify/list'),
            hidden: true,
            meta: {
              title: '资源分类',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'resourceClassify',
            },
          },
        ],
      },
      // 资源目录
      {
        path: 'resourceCatalog',
        name: 'resourceCatalog',
        code: 'resourceCatalog',
        redirect: '/dataAssets/resourceCatalog/resourceCatalogList',
        component: () => import('@/views/data-assets/resourceCatalog/index.vue'),
        meta: {
          title: '资源目录',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
          code: 'asset_panorama',
        },
        children: [
          // 资源目录
          {
            path: 'resourceCatalogList',
            name: 'resourceCatalogList',
            component: () => import('@/views/data-assets/resourceCatalog/list'),
            hidden: true,
            meta: {
              title: '资源目录',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'resourceCatalog',
            },
          },
          {
            path: 'resourceCatalogDetail',
            name: 'resourceCatalogDetail',
            component: () => import('@/views/data-assets/resourceCatalog/detail'),
            hidden: true,
            meta: {
              title: '资源目录详情',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
            },
          },
        ],
      },
      // 资产分类
      {
        path: 'assetsClassify',
        name: 'assetsClassify',
        code: 'assetsClassify',
        redirect: '/dataAssets/assetsClassify/assetsClassifyList',
        component: () => import('@/views/data-assets/assetsClassify/index'),
        meta: {
          title: '资产分类',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'asset_panorama',
          unShowProject: true,
          code: 'asset_panorama',
        },
        children: [
          // 资产分类
          {
            path: 'assetsClassifyList',
            name: 'assetsClassifyList',
            component: () => import('@/views/data-assets/assetsClassify/list'),
            hidden: true,
            meta: {
              title: '资产分类',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'assetsClassify',
            },
          },
          // 授权页面
          {
            path: 'assetsClassifyAuth',
            name: 'assetsClassifyAuth',
            component: () => import('@/views/data-assets/assetsClassify/auth'),
            hidden: true,
            meta: {
              title: '资产分类',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 资产目录
      {
        path: 'assetsCatalog',
        name: 'assetsCatalog',
        code: 'assetsCatalog',
        redirect: '/dataAssets/assetsCatalog/assetsCatalogList',
        component: () => import('@/views/data-assets/assetsCatalog/index'),
        meta: {
          title: '资产目录',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
          code: 'asset_panorama',
        },
        children: [
          // 资产目录
          {
            path: 'assetsCatalogList',
            name: 'assetsCatalogList',
            component: () => import('@/views/data-assets/assetsCatalog/list'),
            hidden: true,
            meta: {
              title: '资产检索',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'assetsCatalog',
            },
          },

          // 资产查询
          {
            path: 'assetsCatalogQuery',
            name: 'assetsCatalogQuery',
            component: () => import('@/views/data-assets/assetsCatalog/query'),
            hidden: true,
            meta: {
              title: '资产目录',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          // api查询
          {
            path: 'assetsCatalogApiQuery',
            name: 'assetsCatalogApiQuery',
            component: () => import('@/views/data-assets/assetsCatalog/api-query'),
            hidden: true,
            meta: {
              title: 'API接口',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 资产注册
      {
        path: 'assetsRegister',
        name: 'assetsRegister',
        code: 'assetsRegister',
        redirect: '/dataAssets/assetsRegister/assetsRegList',
        component: () => import('@/views/data-assets/assetsRegister/index'),
        meta: {
          title: '资产注册',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
          code: 'assetsRegister',
        },
        children: [
          {
            path: 'assetsRegList',
            name: 'assetsRegList',
            component: () => import('@/views/data-assets/assetsRegister/list'),
            hidden: true,
            meta: {
              title: '资产注册',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'assetsRegister',
            },
          },
          {
            path: 'assetsRegDocView',
            name: 'assetsRegDocView',
            component: () => import('@/views/data-assets/assetsRegister/document-preview'),
            hidden: true,
            meta: {
              title: '资产注册-文件预览',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'assetsRegAssetDetail',
            name: 'assetsRegAssetDetail',
            component: () => import('@/views/data-assets/assetsRegister/assets-menu-detail'),
            hidden: true,
            meta: {
              title: '资产注册-资产目录详情',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'assetsRegDocRegister',
            name: 'assetsRegDocRegister',
            component: () => import('@/views/data-assets/assetsRegister/document-register'),
            hidden: true,
            meta: {
              title: '资产注册-文件注册',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'assetsRegSee',
            name: 'assetsRegSee',
            component: () => import('@/views/data-assets/assetsRegister/see'),
            hidden: true,
            meta: {
              title: '资产注册详情',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'register',
            name: 'assetsRegRegister',
            component: () => import('@/views/data-assets/assetsRegister/register'),
            hidden: true,
            meta: {
              title: '资产注册申请',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 合并资产目录
      {
        path: 'mergeAsset',
        name: 'mergeAsset',
        code: 'mergeAsset',
        component: () => import('@/views/data-assets/assetPanorama/merge'),
        meta: {
          title: '资产目录',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
        },
      },
      // 资产总览
      {
        path: 'assetOverview',
        name: 'assetOverview',
        code: 'dataOverview',
        component: () => import('@/views/data-assets/assetPanorama/assetOverview/list'),
        meta: {
          title: '资产总览',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          code: 'dataOverview',
          unShowProject: true,
        },
      },
      // 资产目录
      {
        path: 'assetLibrary',
        name: 'assetLibrary',
        code: 'assetLibrary',
        redirect: '/dataAssets/assetLibrary/assetLibraryList',
        component: () => import('@/views/data-assets/assetPanorama/assetLibrary/index'),
        meta: {
          title: '资产目录',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'assetLibraryList',
            name: 'assetLibraryList',
            component: () => import('@/views/data-assets/assetPanorama/assetLibrary/list'),
            hidden: true,
            meta: {
              title: '资产目录',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              code: 'assetLibrary',
            },
          },
          {
            path: 'assetLibraryDetail',
            name: 'assetLibraryDetail',
            component: () => import('@/views/data-assets/assetPanorama/assetLibrary/detail'),
            hidden: true,
            meta: {
              title: '资产目录查看',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // API管理
      {
        path: 'apiManage',
        name: 'apiManage',
        code: 'apiManage',
        redirect: '/dataAssets/apiManage/apiList',
        component: () => import('@/views/data-assets/api-manage/index'),
        meta: {
          title: 'API管理',
          activeMenu: '/dataAssets/apiManage',
          parentRouterName: 'asset_panorama',
          code: 'apiMonitor',
          unShowProject: false,
        },
        children: [
          {
            path: 'apiList',
            name: 'apiList',
            component: () => import('@/views/data-assets/api-manage/list'),
            hidden: true,
            meta: {
              title: 'API管理',
              activeMenu: '/dataAssets/apiManage',
              parentRouterName: 'asset_panorama',
              keepAlive: true,
              code: 'apiManage',
            },
          },
          {
            path: 'apiAdd',
            name: 'apiAdd',
            component: () => import('@/views/data-assets/api-manage/edit'),
            hidden: true,
            meta: {
              title: 'API管理-新增',
              activeMenu: '/dataAssets/apiManage',
              parentRouterName: 'asset_panorama',
            },
          },
          {
            path: 'apiEdit',
            name: 'apiEdit',
            component: () => import('@/views/data-assets/api-manage/edit'),
            hidden: true,
            meta: {
              title: 'API管理-编辑',
              activeMenu: '/dataAssets/apiManage',
              parentRouterName: 'asset_panorama',
            },
          },
          {
            path: 'apiDetail',
            name: 'apiDetail',
            component: () => import('@/views/data-assets/api-manage/detail'),
            hidden: true,
            meta: {
              title: 'API管理-详情',
              activeMenu: '/dataAssets/apiManage',
              parentRouterName: 'asset_panorama',
            },
          },
          {
            path: 'apiTest',
            name: 'apiTest',
            component: () => import('@/views/data-assets/api-manage/test'),
            hidden: true,
            meta: {
              title: 'API管理-测试',
              activeMenu: '/dataAssets/apiManage',
              parentRouterName: 'asset_panorama',
            },
          },
        ],
      },
      // 指标资产目录
      {
        path: 'targetLibrary',
        name: 'targetLibrary',
        code: 'targetCatalog',
        redirect: '/dataAssets/targetLibrary/targetLibraryList',
        component: () => import('@/views/data-assets/assetPanorama/targetLibrary/index'),
        meta: {
          title: '指标资产目录',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'targetLibraryList',
            name: 'targetLibraryList',
            component: () => import('@/views/data-assets/assetPanorama/targetLibrary/list'),
            hidden: true,
            meta: {
              title: '指标资产目录',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              code: 'targetCatalog',
            },
          },
          {
            path: 'targetLibraryDetail',
            name: 'targetLibraryDetail',
            component: () => import('@/views/data-assets/assetPanorama/targetLibrary/detail'),
            hidden: true,
            meta: {
              title: '指标资产目录-查看',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 血缘关系
      {
        path: 'dataMap',
        name: 'dataMap',
        code: 'bloodRelation',
        component: () => import('@/views/data-assets/dataMap/detail'),
        meta: {
          title: '血缘关系',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          code: 'bloodRelation',
          unShowProject: true,
        },
      },
      // 全局搜索
      {
        path: 'globalSearch',
        name: 'globalSearch',
        code: 'allSearch',
        redirect: '/dataAssets/globalSearch/globalSearchList',
        component: () => import('@/views/data-assets/globalSearch/index'),
        meta: {
          title: '全局搜索',
          activeMenu: '/dataAssets/assetPanorama',
          parentRouterName: 'assetsManage',
          code: 'allSearch',
          unShowProject: true,
        },
        children: [
          // 全局搜索
          {
            path: 'globalSearchList',
            name: 'globalSearchList',
            hidden: true,
            component: () => import('@/views/data-assets/globalSearch/list'),
            meta: {
              title: '全局搜索',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              code: 'allSearch',
            },
          },
          {
            path: 'globalSearchDetail',
            name: 'globalSearchDetail',
            hidden: true,
            component: () => import('@/views/data-assets/globalSearch/detail'),
            meta: {
              title: '查看',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // API监控
      {
        path: 'apiMonitor',
        name: 'apiMonitor',
        code: 'apiMonitor',
        redirect: '/dataAssets/apiMonitor/apiMonitorList',
        component: () => import('@/views/data-assets/api-monitor/index'),
        meta: {
          title: 'API监控',
          activeMenu: '/dataAssets/apiMonitor',
          parentRouterName: 'asset_panorama',
          code: 'apiMonitor',
          unShowProject: false,
        },
        children: [
          {
            path: 'apiMonitorList',
            name: 'apiMonitorList',
            hidden: true,
            component: () => import('@/views/data-assets/api-monitor/list'),

            meta: {
              title: 'API监控',
              activeMenu: '/dataAssets/apiMonitor',
              parentRouterName: 'asset_panorama',
              keepAlive: true,
              code: 'apiMonitor',
            },
          },
          {
            path: 'apiMonitorDetail',
            name: 'apiMonitorDetail',
            component: () => import('@/views/data-assets/api-monitor/detail'),
            hidden: true,
            meta: {
              title: 'API实例',
              activeMenu: '/dataAssets/apiMonitor',
              parentRouterName: 'asset_panorama',
            },
          },
        ],
      },
      // 服务统计
      {
        path: 'serviceCount',
        name: 'serviceCount',
        code: 'serviceCount',
        redirect: '/dataAssets/serviceCount/serviceCountList',
        component: () => import('@/views/data-assets/service-count/index'),
        meta: {
          title: '服务统计',
          activeMenu: '/dataAssets/serviceCount',
          parentRouterName: 'asset_panorama',
          code: 'serviceCount',
          unShowProject: false,
        },
        children: [
          {
            path: 'serviceCountList',
            name: 'serviceCountList',
            component: () => import('@/views/data-assets/service-count/list'),
            hidden: true,
            meta: {
              title: '服务统计',
              activeMenu: '/dataAssets/serviceCount',
              parentRouterName: 'asset_panorama',
              code: 'serviceCount',
            },
          },
        ],
      },
      //  新资产目录
      {
        path: 'assetsDirectory',
        name: 'assetsDirectory',
        code: 'assetsDirectory',
        redirect: '/dataAssets/assetsDirectory/list',
        component: () => import('@/views/data-assets/assetsDirectory/index'),
        meta: {
          title: '资产目录',
          parentRouterName: 'assetsManage',
          unShowProject: true,
          code: 'assetsDirectory',
        },
        children: [
          // 资产目录
          {
            path: 'list',
            name: 'assetsDirectoryList',
            component: () => import('@/views/data-assets/assetsDirectory/list'),
            hidden: true,
            meta: {
              title: '资产目录',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              keepAlive: true,
              code: 'assetsDirectory',
            },
          },
          {
            path: 'detail',
            name: 'assetsDirectoryDetail',
            component: () => import('@/views/data-assets/assetsDirectory/see'),
            hidden: true,
            meta: {
              title: '表详情',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'docView',
            name: 'assetsDirectoryDocView',
            component: () => import('@/views/data-assets/assetsDirectory/document-preview'),
            hidden: true,
            meta: {
              title: '资产目录-文件预览',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'assetsMenu',
            name: 'assetsDirectoryAssetsMenu',
            component: () => import('@/views/data-assets/assetsDirectory/assets-menu-detail'),
            hidden: true,
            meta: {
              title: '资产目录-资源目录',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 外部链接-资产全景
      {
        path: 'newAssetPanorama',
        name: 'newAssetPanorama',
        code: 'newAssetPanorama',
        component: () => import('@/views/data-assets/assetPanorama/newAssetPanorama'),
        meta: {
          title: '资产全景',
          parentRouterName: 'assetsManage',
          code: 'newAssetPanorama',
          unShowProject: true,
        },
      },
      //   资产全景
      {
        path: 'assetsPanoramaNew',
        name: 'assetsPanoramaNew',
        code: 'assetsPanoramaNew',
        redirect: '/dataAssets/assetsPanoramaNew/config',
        component: () => import('@/views/data-assets/assetPanoramaNew/index'),
        meta: {
          title: '资产全景',
          activeMenu: '/dataAssets/assetsPanoramaNew',
          parentRouterName: 'assetsPanoramaNew',
          unShowProject: true,
          code: 'assetsPanoramaNew',
        },
        children: [
          // 资产全景展示页
          {
            path: 'show',
            name: 'assetsPanoramaNewShow',
            component: () => import('@/views/data-assets/assetPanoramaNew/show.vue'),
            hidden: true,
            code: 'assetsPanoramaNewShow',
            meta: {
              title: '资产全景展示',
              activeMenu: '/dataAssets/assetPanorama',
              parentRouterName: 'assetsPanoramaNew',
              unShowProject: true,
              code: 'assetsPanoramaNewShow',
            },
          },
          // 资产全景配置页
          {
            path: 'config',
            name: 'assetsPanoramaNewConfig',
            component: () => import('@/views/data-assets/assetPanoramaNew/configPage/index.vue'),
            hidden: true,
            code: 'assetsPanoramaNewConfig',
            meta: {
              title: '资产全景配置',
              activeMenu: '/dataAssets/assetsPanoramaNew',
              parentRouterName: 'assetsPanoramaNew',
              unShowProject: true,
              keepAlive: true,
              code: 'assetsPanoramaNewConfig',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/metadataMap',
    component: Layout,
    redirect: '/metadataMap/list',
    name: 'metadataMap',
    code: 'metadataMap',
    meta: {
      title: '元数据地图',
      icon: 'menu-metadataMap',
      parentRouterName: 'metadataMap',
      code: 'metadataMap',
      unShowProject: true,
    },
    children: [
      // 元数据地图
      {
        path: 'list',
        name: 'MetadataMapList',
        component: () => import('@/views/data-assets/metadataMap/list'),
        meta: {
          title: '元数据地图',
          activeMenu: '/metadataMap/list',
          parentRouterName: 'metadataMap',
          unShowProject: true,
          code: 'metadataMap',
        },
      },
      // 元数据地图详情
      {
        path: 'detail',
        name: 'metadataMapDetail',
        component: () => import('@/views/data-assets/metadataMap/detail'),
        meta: {
          title: '查看',
          activeMenu: '/metadataMap/list',
          parentRouterName: 'metadataMap',
          unShowProject: true,
        },
      },
      // 元数据地图查看
      {
        path: 'see',
        name: 'metadataMapSee',
        component: () => import('@/views/data-assets/metadataMap/see'),
        meta: {
          title: '查看',
          activeMenu: '/metadataMap/list',
          parentRouterName: 'metadataMap',
          unShowProject: true,
        },
      },
      // 元数据地图
      {
        path: 'metaList',
        name: 'metadataMapMeatList',
        code: 'metadataMapMeatList',
        component: () => import('@/views/data-assets/metadataMap/meta-list'),
        meta: {
          title: '元数据地图',
          activeMenu: '/metadataMap/list',
          parentRouterName: 'metadataMap',
          code: 'metadataMapMeatList',
          unShowProject: true,
        },
      },
    ],
  },
  {
    path: '/recordsCenter',
    name: 'recordsCenter',
    code: 'recordsCenter',
    component: Layout,
    redirect: '/recordsCenter/recordsCenterLibraryList',
    meta: {
      title: '数据中心',
      icon: 'icon-monitoring',
      parentRouterName: 'assetsManage',
      code: 'recordsCenter',
      unShowProject: true,
    },
    children: [
      {
        path: 'recordsCenterLibraryList',
        name: 'recordsCenterLibraryList',
        code: 'recordsCenterLibraryList',
        hidden: true,
        redirect: '/recordsCenter/recordsCenterLibraryList/index',
        component: () => import('@/views/data-assets/recordsCenter/resource-library/index.vue'),
        meta: {
          title: '资源目录',
          icon: 'el-icon-camera',
          activeMenu: '/recordsCenter/recordsCenterLibraryList/index',
          parentRouterName: 'assetsManage',
          code: 'recordsCenterLibraryList',
          unShowProject: true,
        },
        children: [
          {
            path: 'index',
            name: 'recordsCenterLibraryListIndex',
            hidden: true,
            component: () => import('@/views/data-assets/recordsCenter/resource-library/list.vue'),
            meta: {
              title: '资源目录',
              icon: 'el-icon-camera',
              activeMenu: '/recordsCenter/recordsCenterLibraryList',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              code: 'recordsCenterLibraryList',
            },
          },
          {
            path: 'recordsCenterLibraryDetail',
            name: 'recordsCenterLibraryDetail',
            hidden: true,
            component: () =>
              import('@/views/data-assets/recordsCenter/resource-library/detail.vue'),
            meta: {
              title: '结构化数据',
              icon: 'el-icon-camera',
              activeMenu: '/recordsCenter/recordsCenterLibraryList',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
    ],
  },
  {
    path: '/assetsSupervise',
    component: Layout,
    redirect: '/assetsSupervise/tagManagementList',
    name: 'assetsSupervise',
    code: 'asset_manage',
    meta: {
      title: '资产管理',
      icon: 'icon-assets',
      parentRouterName: 'assetsManage',
      code: 'asset_manage',
      unShowProject: true,
    },
    children: [
      {
        path: 'tagManagementList',
        name: 'tagManagementList',
        code: 'tagManage',
        component: () => import('@/views/data-assets/tagManagement/list'),
        meta: {
          title: '标签管理',
          activeMenu: '/assetsSupervise/tagManagement',
          parentRouterName: 'assetsManage',
          code: 'tagManage',
          unShowProject: true,
        },
      },
      // 数据打标
      {
        path: 'tagManagement',
        name: 'tagManagement',
        code: 'markingTask',
        redirect: '/assetsSupervise/tagManagement/markingTagTask',
        component: () => import('@/views/data-assets/tagManagement/index'),
        meta: {
          title: '数据打标',
          icon: 'icon-assets-detail',
          parentRouterName: 'assetsManage',
          code: 'markingTask',
          unShowProject: true,
        },
        children: [
          {
            path: 'markingTagTask',
            name: 'markingTagTask',
            hidden: true,
            component: () => import('@/views/data-assets/tagManagement/markingTagTask'),
            meta: {
              title: '数据打标',
              activeMenu: '/assetsSupervise/tagManagement',
              parentRouterName: 'assetsManage',
              unShowProject: true,
              code: 'markingTask',
            },
          },
          {
            path: 'addMarkingTask',
            name: 'addMarkingTask',
            hidden: true,
            component: () => import('@/views/data-assets/tagManagement/addTask'),
            meta: {
              title: '数据打标',
              activeMenu: '/assetsSupervise/tagManagement',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'checkMarkingTask',
            name: 'checkMarkingTask',
            hidden: true,
            component: () => import('@/views/data-assets/tagManagement/addTask'),
            meta: {
              title: '查看',
              activeMenu: '/assetsSupervise/tagManagement',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
          {
            path: 'editMarkingTask',
            name: 'editMarkingTask',
            hidden: true,
            component: () => import('@/views/data-assets/tagManagement/addTask'),
            meta: {
              title: '编辑打标任务',
              activeMenu: '/assetsSupervise/tagManagement',
              parentRouterName: 'assetsManage',
              unShowProject: true,
            },
          },
        ],
      },
    ],
  },
]
