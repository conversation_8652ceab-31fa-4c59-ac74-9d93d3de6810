<template>
  <div class="container">
    <div class="team-content">
      <div class="btn-group">
        <!-- 新增任务 -->
        <n-button class="add-team-btn" color="primary" variant="solid" @click="addTeam">
          新增团队
        </n-button>
      </div>
      <div class="team-list">
        <CfTable
          actionWidth="120"
          ref="tableNoRef"
          :tableConfig="{
            data: state.dataSource,
            rowKey: 'id',
          }"
          :table-head-titles="tableHead"
          :paginationConfig="{
            total: state.total,
            pageSize: state.searchData.pageSize,
            currentPage: state.searchData.pageNum,
            onCurrentChange: (v) => {
              state.searchData.pageNum = v
              onSearch(false)
            },
            onSizeChange: (v) => {
              state.searchData.pageSize = v
              onSearch()
            },
          }"
        >
          <template #editor="{ row }">
            <!-- 编辑 -->
            <n-button class="edit-btn" variant="text" color="primary" @click="editTeam(row)">
              编辑
            </n-button>
            <!-- 删除 -->
            <n-button class="delete-btn" variant="text" color="primary" @click="deleteTeam(row)">
              删除
            </n-button>
          </template>
        </CfTable>
      </div>
    </div>
    <el-drawer
      v-model="state.teamDrawerVisible"
      title=""
      :size="680"
      append-to-body
      :close-on-click-overlay="true"
      :before-close="closeDrawer"
      class="team-drawer_68768798"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">团队配置</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawer" />
        </div>
        <div class="n-drawer-body-content">
          <n-form :data="state.formData" :label-width="100" ref="formRef">
            <n-form-item label="团队名称：" field="teamInfo.name" :rules="rules.teamInfo.name">
              <n-input v-model="state.formData.teamInfo.name" />
            </n-form-item>
            <n-form-item
              label="团长："
              field="teamInfo.userIdLeader"
              :rules="rules.teamInfo.userIdLeader"
            >
              <el-select
                v-model="state.formData.teamInfo.userIdLeader"
                filterable
                clearable
                remote
                reserve-keyword
                style="width: 100%"
                placeholder="请输入工号或姓名"
                :remote-method="remoteMethod"
                :loading="state.searchLoading"
              >
                <el-option
                  v-for="item in [].concat(state.extraUserList, state.userList)"
                  :key="item.id"
                  :label="item.name + '-' + item.username"
                  :value="item.id"
                />
              </el-select>
            </n-form-item>
            <n-form-item label="团员：" field="teamUserList" :rules="rules.teamUserList">
              <n-button color="primary" variant="solid" @click="addMember"> 新增 </n-button>
            </n-form-item>
            <!-- 团员选择 -->
            <n-form-item label="&nbsp;">
              <div class="member-list">
                <div
                  class="member-item"
                  v-for="(item, index) in state.formData.teamUserList"
                  :key="index"
                >
                  <el-select
                    v-model="item.userId"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    style="width: 100%"
                    placeholder="请输入工号或姓名"
                    :remote-method="remoteMethod"
                    :loading="state.searchLoading"
                  >
                    <el-option
                      v-for="item in [].concat(state.extraUserList, state.userList)"
                      :key="item.id"
                      :label="item.name + '-' + item.username"
                      :value="item.id"
                    />
                  </el-select>
                  <SvgIcon
                    icon="icon-clear-sign"
                    @click="
                      state?.formData?.teamUserList?.length > 1 &&
                        state.formData.teamUserList.splice(index, 1)
                    "
                  />
                </div>
              </div>
            </n-form-item>
          </n-form>
        </div>
        <div class="n-drawer-body-footer">
          <!-- 确定 -->
          <n-button color="primary" variant="solid" @click.prevent="handleTeamSubmit">
            确定
          </n-button>
          <!-- 取消 -->
          <n-button color="primary" @click.prevent="state.teamDrawerVisible = false">
            取消
          </n-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup>
  import api from '@/api'
  const formRef = ref(null) // 表单ref
  const state = reactive({
    formData: {
      // 表单数据
      teamInfo: {
        name: '',
        userIdLeader: null,
      },
      teamUserList: [{}],
    },
    teamDrawerVisible: false, // 团队弹窗
    dataSource: [], // 表格数据
    searchData: {
      // 分页
      condition: {},
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
    },
    total: 0, // 总条数
    userList: [
      // 用户列表
    ],
    // 额外的用户列表
    extraUserList: [
      // 用户列表
    ],
  })
  const rules = reactive({
    // 表单校验规则
    teamInfo: {
      name: [{ required: true, message: '请输入团队名称', trigger: 'blur' }],
      userIdLeader: [{ required: true, message: '请选择团长', trigger: 'blur', type: 'number' }],
    },
    teamUserList: [{ required: true, message: '请选择团员', trigger: 'blur', type: 'array' }],
  })
  const tableHead = computed(() => {
    // 表格表头
    return [
      { name: '团队名称', prop: 'name', width: 900 },
      { name: '团长', prop: 'userIdLeaderName' },
      { name: '团队人数', prop: 'teamUserNumber' },
      { name: '创建时间', prop: 'createTime' },
    ]
  })
  // 获取用户列表
  const getUserList = () => {
    // 获取用户列表
    console.log('获取用户列表')
    api.system
      .userList({
        condition: {},
        pageNum: 1,
        pageSize: 100,
      })
      .then((res) => {
        if (res.success) {
          state.userList = res.data.list
        }
      })
      .catch(() => {})
  }
  const remoteMethod = (query) => {
    state.searchLoading = true
    api.system
      .userList({
        condition: { name: query },
        pageNum: 1,
        pageSize: 100,
      })
      .then((res) => {
        state.searchLoading = false
        if (res.success) {
          state.userList = res.data.list
        }
      })
      .catch(() => {
        state.searchLoading = false
      })
  }
  const addTeam = () => {
    // 新增团队
    console.log('新增团队')
    state.formData = {
      // 表单数据
      teamInfo: {
        name: '',
        userIdLeader: null,
      },
      teamUserList: [{}],
    }
    state.teamDrawerVisible = true
  }
  const closeDrawer = () => {
    // 关闭弹窗
    state.teamDrawerVisible = false
  }
  const addMember = () => {
    // 新增团员
    state.formData.teamUserList.push({})
    console.log('新增团员')
  }
  const handleTeamSubmit = () => {
    // 提交
    console.log('提交', state.formData)
    formRef.value.validate((val) => {
      if (val) {
        state.teamDrawerVisible = false
        const { id } = state.formData.teamInfo || {}
        api.dataGovernance[id ? 'editTeamManagement' : 'addTeamManagement'](state.formData).then(
          (res) => {
            if (res.success) {
              ElMessage.success('提交成功')
              onSearch()
            }
          },
        )
      }
    })
  }
  const onSearch = (reset = true) => {
    // 搜索
    console.log('搜索')
    api.dataGovernance
      .getTeamManagement(state.searchData)
      .then((res) => {
        if (res.success) {
          state.dataSource = res.data.list
          state.total = res.data.total
        }
      })
      .catch(() => {})
  }

  // 编辑
  const editTeam = (row) => {
    // 编辑
    console.log('编辑', row)
    api.dataGovernance
      .getTeamManagementDetail(row.id)
      .then((res) => {
        if (res.success) {
          const { userIdLeader } = res.data.teamInfo || {}
          const { teamUserList } = res.data || []
          api.dataGovernance
            .getUserList({
              ids: (teamUserList || []).concat([{ userId: userIdLeader }])?.map((_) => _.userId),
            })
            .then((res) => {
              if (res.success) {
                const userList = res.data || []
                state.extraUserList = userList
              }
            })
          state.formData = res.data
          state.teamDrawerVisible = true
        }
      })
      .finally(() => {
        state.teamDrawerVisible = true
      })
  }
  // 删除
  const deleteTeam = (row) => {
    // 删除
    console.log('删除', row)
    ElMessageBox.confirm('确定删除该团队吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      api.dataGovernance.deleteTeamManagement(row.id).then((res) => {
        if (res.success) {
          ElMessage.success('删除成功')
          onSearch()
        }
      })
    })
  }
  onSearch()
  getUserList()
</script>
<style lang="scss" scoped>
  .container {
    padding: 16px;
    border-radius: 0;

    .team-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 0;

      .btn-group {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 16px;
      }

      .team-list {
        width: 100%;
        height: calc(100% - 64px);
      }
    }
  }
</style>
<style lang="scss">
  .team-drawer_68768798 {
    .member-list {
      margin-top: 10px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .el-drawer__header {
      display: none;
    }

    .n-drawer-body {
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 52px;
        padding: 0 16px;
        border-bottom: 1px solid #e5e6eb;

        &-name {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: calc(100% - 30px);
          color: rgba(0, 0, 0, 0.9);
          font-weight: bold;
          font-size: 18px;

          .icon {
            margin-right: 8px;

            font-size: 16px;
          }

          .title {
            width: calc(100% - 24px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }

        .close {
          color: #8091b7;
          font-size: 24px;
          cursor: pointer;
        }
      }

      &-content {
        box-sizing: border-box;
        height: calc(100vh - 116px);
        padding: 16px 16px 0 16px;
        overflow-y: auto;

        &.has-top-padding {
          padding: 16px 16px 0 16px;
        }

        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 8px;

          &:hover {
            background-color: #b1bcd6;
          }
        }

        .nancalui-form {
          .nancalui-form__item--horizontal {
            margin-bottom: 8px;

            &:has(.error-message) {
              margin-bottom: 20px;
            }
          }
        }
      }

      &-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 64px;
        padding: 0 16px;
      }
    }
  }

  .team-drawer_68768798 .member-list > .member-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
  }
</style>
