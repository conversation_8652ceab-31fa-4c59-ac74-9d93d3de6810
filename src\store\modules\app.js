import Cookies from 'js-cookie'
import api from '@/api/index'

const state = {
  sidebar: {
    // opened: Cookies.get('sidebarStatus') ? !!Number(Cookies.get('sidebarStatus')) : true,
    opened: false,
    withoutAnimation: false,
  },
  device: 'desktop',
  showRightProject: false, // 顶部右侧下拉导航是否展开控制
  menuHidden: false, // 左边导航栏是否隐藏，true收缩
  bodyClick: 1,
  mapList: localStorage.getItem('mapList') || [], //映射实体
  dataTypeList: localStorage.getItem('dataTypeList') || {}, //数据类型
}

const mutations = {
  TOGGLE_SIDEBAR: (state) => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  CLOSE_PROJECT: (state, flag) => {
    state.showRightProject = flag
  },
  TOGGLE_LEFT_MENU: (state, flag) => {
    state.menuHidden = flag
  },
  BODY_CLICK: (state, flag) => {
    state.bodyClick += 1
  },
  SET_MAP_LIST: (state, data) => {
    state.mapList = data
    localStorage.setItem('mapList', data)
  },
  SET_DATA_TYPE_LIST: (state, data) => {
    state.dataTypeList = data
    localStorage.setItem('dataTypeList', data)
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setMapList({ commit }, data) {
    commit('SET_MAP_LIST', data)
  },
  setDataTypeList({ commit }, kind) {
    const dataTypeList = state.dataTypeList
    if (dataTypeList?.kind === kind) {
      return false
    } else {
      api.dataManagement.erFieldtype({ dbtype: kind }).then((res) => {
        commit('SET_DATA_TYPE_LIST', { kind: kind, data: res.data })
      })
    }
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
