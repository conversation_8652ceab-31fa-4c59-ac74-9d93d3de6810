<template>
  <section class="attrbute">
    <div class="operation">
      <n-input
        class="attrbute-input"
        :disabled="disabled"
        v-model="filterText"
        placeholder="输入要搜索的属性的名称，然后按Enter"
        clearable
        @clear="clearFilter"
        @keyup.enter="fitlerAttr"
      />
      <n-button :disabled="disabled" plain @click="addFiled">添加</n-button>
      <n-button :disabled="disabled" plain @click="deleteHandle">删除</n-button>
      <n-button :disabled="disabled" plain @click="handleUp">上移</n-button>
      <n-button :disabled="disabled" plain @click="handleDown">下移</n-button>
      <n-button :disabled="disabled" plain @click="addDiction">元数据引用</n-button>
    </div>
    <n-table-v
      ref="multipleTable"
      :cache="5"
      :columns="columns"
      :data="data"
      :width="1000"
      :height="300"
      fixed
      style="width: 100%"
      :row-event-handlers="{ onClick: clickAttr }"
    >
      <template #empty>
        <section class="containerBox">
          <img src="@img/no-page-content.png" />
          <p class="empty-word">无数据，请录入查询对象</p>
        </section>
      </template>
    </n-table-v>
    <div class="other">
      <div class="other-left">
        <div class="table">
          <h3>其它</h3>
          <div class="table-box">
            <div class="table-node">同义词</div>
            <div class="table-node">
              <n-input :disabled="disabled" v-model="form.synonymous" type="text" />
            </div>

            <div class="table-node">核心</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.core" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">本质标识符</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.essential" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">业务主键</div>
            <div class="table-node">
              <n-select
                :disabled="disabled"
                v-model="form.candidate"
                @change="onCandidateChange"
                placeholder="请选择"
              >
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">标准化</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.standard" placeholder="请选择">
                <n-option name="继承" value="1" />
                <n-option name="对象" value="2" />
                <n-option name="非对象" value="3" />
              </n-select>
            </div>

            <div class="table-node">默认值</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.defVal" type="text"
            /></div>
          </div>

          <h3>信息安全</h3>
          <div class="table-box">
            <div class="table-node">是否保护信息</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.protect" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">隐私分类</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.privacyClassify" type="text"
            /></div>

            <div class="table-node">信息安全等级</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.infoSecurityLevel" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">是否加密</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.encryptFlag" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">加密方法</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.encryption" type="text"
            /></div>

            <div class="table-node">数据所有者</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.dataOwner" type="text"
            /></div>
          </div>
        </div>
      </div>

      <div class="other-right">
        <n-tabs v-model="activeName" type="wrapped">
          <n-tab title="定义" id="first">
            <n-input
              :disabled="disabled"
              v-model="form.definition"
              type="textarea"
              placeholder="请输入内容"
            />
          </n-tab>
        </n-tabs>
      </div>
    </div>
  </section>
</template>

<script lang="jsx">
  import { cloneDeep } from 'lodash'
  import { dataTypeName } from '../../utils/config.js'
  import { notSetLen, notSetScale } from '../../utils/index.js'

  export default {
    props: {
      cell: {
        type: Object,
        default: () => null,
      },
    },
    data() {
      return {
        filterText: '', // 搜索条件
        activeName: 'first',
        columns: [
          {
            key: 'checked',
            dataKey: 'checked',
            width: 50,
            cellRenderer: ({ rowData }) => {
              const onChange = (value) => (rowData.checked = value)
              return <n-checkbox v-model={rowData.checked} onChange={onChange} />
            },
            headerCellRenderer: () => {
              const _data = this.data
              const onChange = (value) => {
                this.data = _data.map((row) => {
                  row.checked = value
                  return row
                })
              }
              let allSelected = _data.every((row) => row.checked)
              const containsChecked = _data.some((row) => row.checked)
              return (
                <n-checkbox
                  v-model={allSelected}
                  half-checked={containsChecked && !allSelected}
                  onChange={onChange}
                />
              )
            },
          },
          {
            key: 'index',
            dataKey: 'index',
            title: '序号',
            width: 50,
            cellRenderer: ({ rowIndex }) => {
              return rowIndex + 1
            },
          },
          {
            key: 'mappingDictionId',
            dataKey: 'mappingDictionId',
            title: '是否标准',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return rowData.mappingDictionId ? <n-tag>是</n-tag> : '否'
            },
          },
          {
            key: 'name',
            dataKey: 'name',
            title: '属性名',
            width: 200,
            cellRenderer: ({ rowData }) => {
              return (
                <n-input
                  data-type='name'
                  disabled={this.disabled}
                  v-model={rowData.name}
                  type='text'
                  onBlur={(e) => this.onMatchCol(e, rowData)}
                />
              )
            },
          },
          {
            key: 'standardCol',
            dataKey: 'standardCol',
            title: '标准列名',
            width: 200,
            cellRenderer: ({ rowData }) => {
              return <n-input v-model={rowData.standardCol} type='text' readonly disabled />
            },
          },
          {
            key: 'col',
            dataKey: 'col',
            title: '实际列名',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return (
                <n-input
                  data-type='col'
                  disabled={this.disabled}
                  v-model={rowData.col}
                  type='text'
                  onBlur={(e) => this.onMatchCol(e, rowData)}
                />
              )
            },
          },
          {
            key: 'substantiveKey',
            dataKey: 'substantiveKey',
            title: '实质键',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return <n-checkbox disabled={this.disabled} v-model={rowData.substantiveKey} />
            },
          },
          {
            key: 'notNull',
            dataKey: 'notNull',
            title: 'NotNull',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return (
                <n-checkbox
                  disabled={
                    this.disabled ||
                    rowData.substantiveKey === true ||
                    rowData.notNullDisabled === true
                  }
                  v-model={rowData.notNull}
                />
              )
            },
          },
          {
            key: 'type',
            dataKey: 'type',
            title: '类型',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return (
                <n-select disabled={this.disabled} v-model={rowData.type} placeholder='请选择'>
                  <n-option name='Derived' value='Derived' />
                  <n-option name='Pseudo' value='Pseudo' />
                  <n-option name='Additional' value='Additional' />
                  <n-option name='Drop' value='Drop' />
                  <n-option name='System' value='System' />
                </n-select>
              )
            },
          },
          {
            key: 'dataType',
            dataKey: 'dataType',
            title: '数据类型',
            width: 150,
            cellRenderer: ({ rowData, rowIndex }) => {
              return (
                <n-select disabled={this.disabled} v-model={rowData.dataType} placeholder='请选择'>
                  {this.dataTypeOpt?.map((item) => {
                    return <n-option key={item[0]} name={item[0]} value={item[0]} />
                  })}
                </n-select>
              )
            },
          },
          {
            key: 'length',
            dataKey: 'length',
            title: '长度',
            width: 150,
            cellRenderer: ({ rowData, rowIndex }) => {
              return (
                <n-input
                  disabled={this.disabled || notSetLen(rowData.dataType, 'Zn')}
                  v-model={rowData.length}
                  type='number'
                />
              )
            },
          },
          {
            key: 'scale',
            dataKey: 'scale',
            title: '小数点',
            width: 150,
            cellRenderer: ({ rowData, rowIndex }) => {
              return (
                <n-input
                  disabled={this.disabled || notSetScale(rowData.dataType, 'Zn')}
                  v-model={rowData.scale}
                  type='number'
                />
              )
            },
          },
          {
            key: 'fk',
            dataKey: 'fk',
            title: 'FK',
            width: 50,
          },
          {
            key: 'uk',
            dataKey: 'uk',
            title: 'UK',
            width: 50,
          },
          {
            key: 'remarks',
            dataKey: 'remarks',
            title: '备注',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return <n-input disabled={this.disabled} v-model={rowData.remarks} type='text' />
            },
          },
        ],
        data: [],
        form: {
          // 其它
          alternateName: '', // 候补名
          synonymous: '', // 同义词
          core: '', // 核心
          essential: '', // 本质标识符
          candidate: '', // 业务主键
          sync: '', // 同步
          nonInheritance: '', // 非继承
          Inclusive: '', // Inclusive
          standard: '1', // 标准化
          defVal: '', // 默认值
          field: '', // 域
          reverse: '', // 逆向信息

          // 信息安全
          protect: '', // 是否保护信息
          privacyClassify: '', // 隐私分类
          infoSecurityLevel: '', // 信息安全等级
          encryptFlag: '', // 是否加密
          encryption: '', // 加密方法
          dataOwner: '', // 数据所有者

          definition: '', // 定义
        },
        entityId: '',
        entityGuid: '',
        oldData: [], // 老数据
        selectedData: [],
      }
    },
    computed: {
      disabled() {
        return this.cell.data.type === 'BLOOD' || this.cell.data.disableMove
      },
      // 数据类型
      dataTypeOpt() {
        const { kind } = this.cell?.data
        return dataTypeName[kind]
      },
      // 数据库类型
      kind() {
        const { kind } = this.cell?.data
        return kind
      },
      // 选中属性
      multipleSelection() {
        return this.data.filter((d) => d.checked)
      },
    },
    watch: {
      filterText(n, o) {
        if (n === '') {
          this.clearFilter()
        }
      },
      cell(val) {
        this.initData()
      },
    },
    async created() {
      await this.initData()
    },

    methods: {
      async initData() {
        try {
          let data = { ...this.cell.data }
          this.entityId = data?.entity?.id
          this.entityGuid = data?.entity?.guid
          if (data.attr[0]) {
            data.attr[0].entityId = this.entityId
            data.attr[0].entityGuid = this.entityGuid
          }
          this.data = cloneDeep(data.attr || []).map((i) => {
            return { ...i, checked: false }
          })
          this.form = data.attr[0] || this.form
          this.oldData = this.data
        } catch (e) {
          console.log(e)
        }
      },

      // 点击一行属性
      clickAttr({ rowData }) {
        this.form = rowData
      },
      // 过滤属性
      fitlerAttr() {
        this.data = this.data.filter((item) => {
          return item.name.indexOf(this.filterText) > -1
        })
      },
      // 清除过滤条件
      clearFilter() {
        this.data = this.oldData
      },
    },
  }
</script>

<style lang="scss" scoped>
  .attrbute {
    padding: 10px 16px;

    input {
      width: 100%;
      padding: 0 11px;
      line-height: 28px;
      border: none;
      outline: none;
    }
  }

  .operation {
    position: relative;
    display: flex;
    margin-bottom: 10px;
  }
  .attrbute-input {
    width: 558px;
    margin-right: 14px;
  }
  .multipleTable {
    margin-top: 10px;
  }

  .other {
    display: flex;
    margin-top: 14px;

    &-left {
      width: 290px;
      height: 175px;
      margin-right: 10px;
      overflow: auto;
      border: 1px solid #cfcfcf;
    }
    &-right {
      width: calc(100% - 300px);
    }
  }

  :deep(.nancalui-table-v__row) {
    &:hover {
      background: rgba(37, 123, 255, 0.08) !important;
      cursor: pointer;
    }
  }
  :deep(.nancalui-select) {
    height: unset;
  }
  .containerBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    transform: translateY(30%);
    img {
      width: 216px;
    }
  }
</style>
