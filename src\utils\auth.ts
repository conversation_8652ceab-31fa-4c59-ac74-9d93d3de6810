// import Cookies from 'js-cookie'
// import api from '@/api/user'
// import store from '@/store'
// import router from '@/router'
const TokenKey = window.location.hostname + 'nc_admin_token_new'
const TokenPrefix = 'Bearer '
const storeStorage = window.location.hostname + 'store' // 设置项目的store的localStorage的key值
const isLogin = () => {
  // return !!Cookies.get(TokenKey)
  return !!localStorage.getItem(TokenKey)
}
// let status = true
// const getSystemInfo = () => {
//   if (status) {
//     status = false
//     api.getInfoByCookies().then(async (response: any) => {
//       if (response.success) {
//         const { data } = response
//         const menuTreeArr = data?.apps?.filter((val) => val.appCode === 'data')
//         if (menuTreeArr.length > 0) {
//           // 设置token
//           data.data = Cookies.get(TokenKey)
//           sessionStorage.setItem('isNormalLogin', '1')
//           await store.commit('user/SET_TOKEN', data.data)
//           setToken(data.data)
//           store.commit('user/SET_ROLE', data.roles)
//           store.commit('user/SET_ID', data.userId)
//           store.commit('user/SET_NAME', data.username)
//           store.commit('user/SET_ROLE_CODE', data.threeManagerCode || null)
//           store.commit('user/SET_CON_STATUS', data.enabledPt)
//           // 获取按钮权限
//           const menuTree = menuTreeArr[0]?.menuTree
//           let btnList = flatten(menuTree).filter((val) => val.resourceType === 'BUTTON')
//           btnList = btnList.map((val) => val.code)
//           store.commit('user/SET_BUTTON_AUTH_LIST', btnList)
//           if (menuTree[0]?.code === 'all') {
//             store.commit('user/SET_MENU_TREE', menuTree[0]?.children || [])
//           } else {
//             store.commit('user/SET_MENU_TREE', menuTree || [])
//           }
//           // 设置菜单列表
//           store.commit(
//             'user/SET_MENU',
//             flatten(menuTree).filter((val) => val.resourceType === 'MENU'),
//           )
//           store.commit('user/SET_ACTIVE_SYS', false)
//           store.commit(
//             'user/SET_AVATAR',
//             'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
//           )
//           router.push({
//             name: 'ConvergencePage',
//           })
//         }
//         return
//       }
//     })
//   }
// }

//扁平化菜单，用做按钮权限判断的数组
const flatten = (arr) => {
  return arr.reduce((pre, cur) => {
    pre.push(cur)
    if (cur.children) {
      return pre.concat(flatten(cur.children))
    } else {
      return pre
    }
  }, [])
}

const getToken = () => {
  let str = location.search ? location.search : location.hash
  let token = str?.split('tk=')[1]?.split('&')[0]
  let currentToken = localStorage.getItem(TokenKey)
  if (token) {
    currentToken = token
  }
  if (sessionStorage.getItem('isNormalLogin') === '1' && localStorage.getItem(TokenKey)) {
    currentToken = localStorage.getItem(TokenKey)
  }

  return currentToken
  // const CookiesToken = Cookies.get(TokenKey)
  // if (CookiesToken) {
  //   if (sessionStorage.getItem('isNormalLogin') === '1') {
  //     return CookiesToken
  //   } else {
  //     getSystemInfo()
  //     return 'cross-domain-token'
  //   }
  // } else {
  //   return false
  // }
}
const setToken = (token: string) => {
  localStorage.setItem(TokenKey, token)
  // Cookies.set(TokenKey, token, {
  //   domain: location.hostname,
  // })
}
const removeToken = () => {
  // Cookies.set(TokenKey, '', {
  //   domain: location.hostname,
  // })
  localStorage.removeItem(TokenKey)

  sessionStorage.setItem('isNormalLogin', '0')
  // status = true
}
export { TokenPrefix, isLogin, getToken, setToken, removeToken,storeStorage,TokenKey }
