<template>
  <div class="user-dropdown">
    <div class="user-dropdown-avatar">
      <SvgIcon class="icon-user" icon="icon-user-solid" />
      <div class="name">
        {{ state.name }}
      </div>
      <SvgIcon class="icon-arrow" icon="icon-arrow-bottom" />
    </div>
    <div :class="{ 'user-dropdown-content': true, [props.algin]: true }">
      <div class="user-dropdown-content-box" :style="'width:' + props.contentWidth + 'px'">
        <div class="dropdown-menu-item">
          <div class="userBox">
            <div class="circle"
              ><div class="name">{{ state.name?.slice(0, 1)?.toUpperCase() }}</div
              ><img class="bg" src="@img/home/<USER>"
            /></div>
            <div class="nickname">{{ state.name }}</div>
          </div>
        </div>
        <div
          v-if="props.systemMenuStatus"
          class="dropdown-menu-item"
          @click.prevent="settingFn('systemManagement')"
        >
          <div class="dropdown-menu-item-label"
            ><SvgIcon class="icon-nav" icon="icon-nav-setting" title="系统设置" />系统设置</div
          >
        </div>
        <!-- <div class="dropdown-menu-item" @click.prevent="logout">
          <div class="dropdown-menu-item-label"
            ><SvgIcon class="icon-nav" icon="icon-nav-out" title="退出登录" />退出登录</div
          >
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, computed, defineProps } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { TokenKey } from '@/utils/auth'
  import { ElNotification } from 'element-plus'
  const router = useRouter()
  const store = useStore()

  const props = defineProps({
    contentWidth: {
      type: Number,
      default: 120,
    },
    algin: {
      type: String,
      default: 'left',
    },
    systemMenuStatus: {
      type: Boolean,
      default: false,
    },
  })

  const state = reactive({
    name: '',
    menuTreeList: [],
  })

  state.name = computed(() => {
    return store.state.user.name
  })

  state.menuTreeList = computed(() => {
    return store.state.user.menuTreeList
  })

  const settingFn = (code, routeName = null) => {
    let activeInfo = { activeMenu: '', activeName: '', code: '' }
    state.menuTreeList.forEach((val) => {
      if (val.code === code) {
        activeInfo = getActiveInfoFn(val.children, activeInfo, 1)
      }
    })
    if (routeName) {
      if (!activeInfo.activeMenu) {
        ElNotification({
          title: '提示',
          message: '请联系管理员分配场景权限',
          type: 'warning',
        })
        return false
      }
    }
    store.commit('user/SET_FIRST_MENU', code)
    router.push({
      name: routeName ? routeName : activeInfo.activeMenu,
    })
  }

  const getActiveInfoFn = (list, activeInfo, zIndex) => {
    list.forEach((val) => {
      if (val.children && val.children[0] && val.children[0].resourceType === 'MENU') {
        getActiveInfoFn(val.children, activeInfo, zIndex + 1)
      } else if (val.routerName && !activeInfo.activeMenu) {
        activeInfo.activeMenu = val.routerName
        activeInfo.activeName = val.name
        activeInfo.code = zIndex > 1 ? val.parentCode : val.code
      }
    })
    return activeInfo
  }

  const logout = async () => {
    localStorage.removeItem(TokenKey)
    await store.dispatch('user/logout')
    router.push(`/`)
  }
</script>

<style lang="scss" scoped>
  .user-dropdown {
    position: relative;
    margin: 0 auto;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    &:hover {
      .user-dropdown-content {
        display: flex;
      }
    }
    &-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      .icon-user {
        width: 18px;
        margin-right: 4px;
        color: #fff;
        font-size: 18px;
      }
      .name {
        position: relative;
        z-index: 2;
        max-width: 100px;
        overflow: hidden;
        color: #fff;
        font-size: 18px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .icon-arrow {
        width: 8px;
        margin-left: 4px;
        color: #fff;
        font-size: 8px;
      }
    }
    &-content {
      position: absolute;
      top: 30px;
      display: none;
      box-sizing: border-box;
      width: 100%;
      &:after {
        position: absolute;
        top: -30px;
        width: 100%;
        height: 30px;
        content: '';
      }
      &.left {
        left: 0;
        justify-content: flex-start;
        &:after {
          left: 0;
        }
      }
      &.right {
        right: 0;
        justify-content: flex-end;
        &:after {
          right: 0;
        }
      }
      &-box {
        flex-shrink: 0;
        padding: 0 4px;
        background-color: #ffffff;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(37, 43, 58, 0.2);
        .userBox {
          padding: 10px 0;
        }

        .circle {
          position: relative;
          width: 60px;
          height: 60px;
          margin: 0 auto;
          overflow: hidden;
          color: #ffffff;
          font-weight: bold;
          font-size: 14px;
          line-height: 60px;
          white-space: nowrap;
          text-align: center;
          text-overflow: ellipsis;
          border-radius: 50%;
          .bg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
          .name {
            position: relative;
            z-index: 2;
            font-size: 32px;
          }
        }

        .nickname {
          overflow: hidden;
          color: #000000;
          font-weight: bold;
          font-size: 14px;
          white-space: nowrap;
          text-align: center;
          text-overflow: ellipsis;
        }

        .dropdown-menu-item {
          padding: 4px 0;
          border-top: 1px solid #eeeeee;
          &-label {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 10px;
            color: #333333;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            .icon-nav {
              width: 16px;
              height: 16px;
              margin-right: 6px;
              color: #333333;
            }
            &:hover {
              color: #000000;
              font-weight: bold;
              background: #f7f8fa;
              .icon-nav {
                color: #000000;
              }
            }
          }
        }
      }
    }
  }
</style>
