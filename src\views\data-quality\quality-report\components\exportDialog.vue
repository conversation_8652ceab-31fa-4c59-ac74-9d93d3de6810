<template>
  <n-modal
    v-model="state.dialogVisible"
    title="导出预览"
    bodyClass="commonDialog"
    :close-on-click-overlay="false"
    :append-to-body="true"
    :draggable="false"
    width="960px"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-form">
        <n-form label-width="42px">
          <n-form-item label="格式：">
            <n-radio-group v-model="state.exportType" direction="row">
              <n-radio value="png">PNG图片</n-radio>
              <n-radio value="pdf">PDF文件</n-radio>
            </n-radio-group>
          </n-form-item>
        </n-form>
      </div>
      <!-- 预览 -->
      <div class="content-preview">
        <Preview :formInline="formInline" :multiple="multiple" />
      </div>
    </div>
    <template #footer>
      <n-modal-footer class="dialog-footer">
        <n-button size="sm" @click.prevent="closeDialog">取 消</n-button>
        <n-button size="sm" color="primary" variant="solid" @click.prevent="download">
          下载</n-button
        >
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue'
  import workerUrl from 'modern-screenshot/worker?url'
  import { createContext, destroyContext, domToPng } from 'modern-screenshot'
  import { jsPDF } from 'jspdf'
  // 引用预览组件
  import Preview from './Preview.vue'
  const props = defineProps({
    formInline: {
      type: Object,
      default() {
        return {}
      },
    },
    multiple: {
      type: Number,
    },
  })

  const previewUrl = ref('')
  const base64ToBlob = (code) => {
    const parts = code.split(';base64,')
    const contentType = parts[0].split(':')[1]
    const raw = window.atob(parts[1])
    const rawLength = raw.length
    const uInt8Array = new Uint8Array(rawLength)
    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i)
    }
    return new Blob([uInt8Array], { type: contentType })
  }
  async function screenshotsPerSecond() {
    const dom = document.querySelector('.content-preview')
    const context = await createContext(dom, {
      workerUrl,
      workerNumber: 1,
      scale: 6,
    })

    return new Promise((resolve) =>
      domToPng(context).then((dataUrl) => {
        previewUrl.value = dataUrl
        destroyContext(context)
        resolve()
      }),
    )
  }
  // 下载
  const download = () => {
    screenshotsPerSecond().then(() => {
      if (state.exportType === 'png') {
        const blob = base64ToBlob(previewUrl.value)
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        // 事件序列生成图片
        link.download = new Date().getTime() + '.png'

        //此写法兼容可火狐浏览器
        document.body.appendChild(link)
        const evt = document.createEvent('MouseEvents')
        evt.initEvent('click', false, false)
        link.dispatchEvent(evt)
        document.body.removeChild(link)
      } else {
        const pdf = new jsPDF()
        pdf.addImage(previewUrl.value, 'png', 0, 0, 210, 137.46)
        pdf.save(`${new Date().getTime() + '.png'}.pdf`)
      }
    })
  }

  const state = reactive({
    dialogVisible: false,
    exportType: 'png',
  })

  const closeDialog = () => {
    state.dialogVisible = false
  }

  watch(
    () => props.dialogVisible,
    (val) => {
      state.dialogVisible = val
    },
  )

  watch(
    () => props.log,
    (val) => {
      state.log = val
    },
  )
  defineExpose({
    showDialog() {
      state.dialogVisible = true
    },
  })
</script>

<style lang="scss" scoped>
  .content {
    &-form {
    }
    &-preview {
      .preview-content {
        width: 100%;
      }
    }
  }
</style>
