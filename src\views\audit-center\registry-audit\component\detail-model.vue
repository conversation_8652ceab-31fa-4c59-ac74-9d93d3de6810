<template>
  <div class="detail-model">
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="基本信息" id="base">
        <el-form
          ref="ruleForm"
          :model="state.ruleForm"
          label-width="100px"
          label-position="left"
          class="demo-ruleForm"
        >
          <el-form-item label="所属场景：">
            <el-input
              v-model="state.ruleForm.projectName"
              maxlength="30"
              placeholder="所属场景"
              disabled
            />
          </el-form-item>
          <el-form-item label="模型层：">
            <el-input v-model="state.ruleForm.layerName" placeholder="模型层" disabled />
          </el-form-item>

          <el-form-item label="描述信息：">
            <el-input
              v-model="state.ruleForm.description"
              type="textarea"
              show-word-limit
              maxlength="200"
              :autosize="{ minRows: 3 }"
              disabled
            />
          </el-form-item>

          <el-form-item label="创建人:">
            <el-input v-model="state.ruleForm.createByName" placeholder="" disabled />
          </el-form-item>
          <el-form-item label="创建时间:">
            <el-input v-model="state.ruleForm.createTime" placeholder="" disabled />
          </el-form-item>
          <el-form-item label="更新时间:">
            <el-input v-model="state.ruleForm.updateTime" placeholder="" disabled />
          </el-form-item>
        </el-form>
      </n-tab>
      <n-tab title="字段信息" id="metaData">
        <div class="table">
          <PublicTable
            ref="publicTable"
            :needOtherActionBar="state.needOtherActionBar"
            :table-head-titles="state.tableHeadTitles"
            :showPagination="false"
            :exceptHeight="300"
          />
        </div>
      </n-tab>
      <n-tab title="数据预览" id="dataPreview">
        <PublicTable
          ref="publicTablePreview"
          :showPagination="true"
          :table-head-titles="state.tableHeadTitlesPreview"
          :pagination="state.pagination"
          :exceptHeight="345"
          @tablePageChange="tablePageChange"
      /></n-tab>
    </n-tabs>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import insertCss from 'insert-css'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      const router = useRouter()
      const publicTable = ref()
      const publicTablePreview = ref()
      const state = reactive({
        activeName: 'base',
        disabled: true,
        detailId: null,

        ruleForm: {
          projectCode: '',
          projectName: '',
          createByName: '',
          layerId: '',
          layerName: '',
          createTime: '',
          updateByName: '',
          updateTime: '',
          description: '',
          paramColumns: [], // 自定义参数
        },
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '字段中文名称' },
          { prop: 'name', name: '字段英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
        ],

        tableHeadTitlesPreview: [],
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
      })
      const methods = {
        // 获取模型详情
        getDetail(id) {
          state.loading = true
          api.model
            .getModalDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data[key]
                })
                state.ruleForm = {
                  ...state.ruleForm,
                }
              }
            })
            .catch(() => {})
        },
        getMetaDataByModel(id) {
          state.loading = true
          api.model
            .getModeData({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                res.data.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                publicTable.value.initTableData({ list: res.data })
              }
            })
            .catch(() => {
              publicTable.value.initFailed()
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.getDataPreview(false)
        },
        getDataPreview(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              projectCode: toRefs(store.state.user.currentProject, 'projectCode').value,
              modelName: router.currentRoute.value.query.modelName,
              // modelName: 'ci_tun',
              // projectCode: 'xaadybcdwebcxadfe',
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          api.model
            .getDataWithProject(data)
            .then((res) => {
              let { success, data } = res
              if (success) {
                if (data.list && data.list.length > 0) {
                  state.tableHeadTitlesPreview = [{ prop: 'number', name: '序号', width: 80 }]
                  Object.keys(data.list[0]).forEach((key) => {
                    state.tableHeadTitlesPreview.push({
                      prop: key,
                      name: key,
                    })
                  })
                }
                data.list.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                publicTablePreview.value.initTableData(data)
              }
            })
            .catch(() => {
              publicTablePreview.value.initFailed()
            })
        },

        //切换
        handleClick(id) {
          if (id === 'metaData') {
            methods.getMetaDataByModel(state.detailId)
          }
          switch (id) {
            case 'metaData':
              methods.getMetaDataByModel(state.detailId)
              break
            case 'dataPreview':
              methods.getDataPreview(state.detailId)
              break
            default:
              methods.getDetail(state.detailId)
          }
        },
      }
      onMounted(() => {
        nextTick(() => {
          state.detailId = router.currentRoute.value.query.id
          methods.getDetail(state.detailId)
        })
      })

      return {
        state,
        publicTable,
        publicTablePreview,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $labelWidth: 90px;
  .detail-model {
    position: relative;
    height: 100%;
    color: #333;
    .base-tabs {
      height: 100%;
    }
    .mapModel {
      display: flex;
      width: 100%;
      div {
        box-sizing: border-box;
        width: 25%;
        height: 40px;
        font-size: 12px;
        line-height: 40px;
        text-align: center;
        background-color: rgba(105, 122, 154, 0.24);
        border-right: 2px solid #fff;
      }
    }
    :deep(.x6-graph-scroller) {
      width: 100% !important;
      height: calc(100% - 70px) !important;
      &::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
      }
      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 2px;
      }
    }
    :deep(.base-tabs) {
      .el-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 600px;
        margin: 45px auto;

        .el-form-item {
          position: relative;
          display: flex;
          width: 100%;
          margin-bottom: 20px;
          .el-form-item__label {
            position: absolute;
            top: 0;
            right: 600px;
            width: max-content !important;
          }
          &.operate-box {
            display: inline-block;
            text-align: center;
            transform: translateX($labelWidth);
          }
          .el-select {
            width: 100%;
          }
          .el-form-item__content {
            // 要想在sass的calc中使用变量，必须对这个变量使用sass的插值方法（#{$labelWidth}）
            width: calc(100% - #{$labelWidth});
            margin-left: 0 !important;
          }
        }
      }
    }
  }
</style>
