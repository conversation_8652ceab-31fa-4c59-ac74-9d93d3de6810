<template>
  <div class="box">
    <h3>{{ str }}</h3>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { removeToken, storeStorage } from '@/utils/auth.ts'
  export default {
    data() {
      return {
        str: '',
        redirect: undefined,
      }
    },

    async created() {
      try {
        removeToken()
        localStorage.removeItem('nc_admin_token')
        let auth = this.$route.query.auth

        if (auth) {
          await this.$store.dispatch('user/singlelogin', auth)

          sessionStorage.setItem('isNormalLogin', '1')
          localStorage.removeItem(storeStorage)
          // // 获取WebSocket
          this.getWebSocketUrlFn()
          // // 获取项目列表
          this.getCheckProjectFn()
        } else {
          this.str = '! 跳转失败'
        }
      } catch (e) {
        this.str = '! 跳转失败'
        sessionStorage.setItem('isNormalLogin', '0')
      }
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect
        },
        immediate: true,
      },
    },
    computed: {
      ...mapState({
        isCon: (state) => state['user'].enabledPt,
        menuTreeList: (state) => state['user'].menuTreeList,
      }),
      variables() {
        return variables
      },
    },
    methods: {
      // 获取默认的场景
      getCheckProjectFn() {
        this.$api.project.getMyProjectList({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.projectList = res.data
            if (this.projectList.length > 0) {
              let flag = true
              this.projectList.forEach((val) => {
                if (val.using && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                } else if (val.preferred && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                }
              })
              if (flag) {
                this.$store.commit('user/SET_PROJECT', this.projectList[0])
              }
            }
            // 对本地路由适配
            let menuTreeList = [...this.menuTreeList]
            let newMenuTreeList = this.menuTreeMateFn(
              menuTreeList,
              this.$router.options.routes,
              1,
            ).filter((val) => val.code !== 'home')
            this.$store.commit('user/SET_MENU_TREE', newMenuTreeList)
            localStorage.setItem(storeStorage, JSON.stringify(this.$store.state))
            // this.checkPro('dataIntegration')
            // 跳转对应路由
            this.$router.push({
              name: 'ConvergencePage',
            })
          }
        })
      },
      // 选中的模块切换路由地址以及设置tag等
      checkPro(code) {
        let activeInfo = { activeMenu: 'ConvergencePage', activeName: '', code: '' }
        this.menuTreeList.forEach((val) => {
          if (val.code === code) {
            activeInfo = this.getActiveInfoFn(val.children, activeInfo, 1)
          }
        })
        sessionStorage.setItem('checkedTagName', activeInfo.activeMenu)
        this.$store.commit('user/SET_FIRST_MENU', code)
        this.$store.commit('user/SET_ACTIVE_MENU', activeInfo.activeMenu)
        this.$store.commit('user/SET_ACTIVE_MENU_CODE', activeInfo.code)
        let tagList = sessionStorage.getItem('TAG_LIST_CF')
        if (tagList) {
          tagList = JSON.parse(tagList)
        } else {
          tagList = []
        }
        tagList = tagList.filter((val) => val.name !== activeInfo.activeMenu)
        tagList.unshift({
          title: activeInfo.activeName,
          name: activeInfo.activeMenu,
          icon: 'menu-' + activeInfo.code + '-tag',
          query: {},
          params: {},
        })

        sessionStorage.setItem('TAG_LIST_CF', JSON.stringify(tagList))
        this.$router.push({
          name: activeInfo.activeMenu,
        })
      },
      // 路由树匹配code和name对应
      menuTreeMateFn(menuTreeList, routerList, zIndex) {
        menuTreeList.forEach((item) => {
          if (item.resourceType === 'MENU') {
            routerList.forEach((val) => {
              if (val.code === item.code) {
                item.routerName = val.name
                item.icon = val?.meta?.icon || null
              }
              if (val.children) {
                if (zIndex < 3) {
                  this.menuTreeMateFn(menuTreeList, val.children, zIndex + 1)
                }
              }
            })
            if (item.children && item.children[0]?.resourceType === 'MENU') {
              this.menuTreeMateFn(item.children, routerList, zIndex + 1)
            }
          }
        })
        return menuTreeList
      },
      // 获取选择模块的菜单
      getActiveInfoFn(list, activeInfo, zIndex) {
        list.forEach((val) => {
          if (val.children && val.children[0] && val.children[0].resourceType === 'MENU') {
            this.getActiveInfoFn(val.children, activeInfo, zIndex + 1)
          } else if (val.routerName && !activeInfo.activeMenu) {
            activeInfo.activeMenu = val.routerName
            activeInfo.activeName = val.name
            activeInfo.code = zIndex > 1 ? val.parentCode : val.code
          }
        })
        return activeInfo
      },
      // 获取webSocket地址
      getWebSocketUrlFn() {
        this.$api.base.getEnvironmentConfig({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.$store.commit('user/SET_WEBSOCKET', res.data.ws)
            this.$store.commit(
              'user/SET_OTHER_MRS',
              res.data.mrs ? res.data.mrs : { enabled: false },
            )
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .box {
    width: 100%;
    height: calc(100%);
    display: flex;
    justify-content: center;
    align-items: center;
    color: crimson;
  }
</style>
