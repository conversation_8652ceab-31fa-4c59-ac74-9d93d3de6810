<template>
  <!-- 模型构建-映射方式 -->
  <div class="data-collection-link-mapping">
    <div class="box-bottom">
      <n-form
        ref="ruleForm"
        :data="state.ruleForm"
        :rules="state.rules"
        label-width="80px"
        class="demo-ruleForm"
        label-position="left"
        :pop-position="['right']"
      >
        <n-form-item class="first-line" label="数据源类型：" field="dataSourceType">
          <n-select
            v-model="state.ruleForm.dataSourceType"
            placeholder="请选择"
            filter
            allow-clear
            :disabled="state.disabled"
            @value-change="datasourceTypeChange"
          >
            <n-option
              v-for="item in state.datasourceTypeOptions"
              :key="item.value"
              :name="item.label"
              :value="item.value"
            />
          </n-select>
        </n-form-item>
        <div class="one-line">
          <div class="left-item">
            <n-form-item label="选择数据源：" field="dataSourceFeign">
              <n-select
                v-model="state.ruleForm.dataSourceFeign"
                placeholder="请选择"
                filter
                allow-clear
                :disabled="state.disabled"
                @value-change="dataSourceFeignChange"
              >
                <n-option
                  v-for="item in state.dataSourceOptions"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>

            <n-form-item field="dataSourceTable">
              <n-select
                v-model="state.ruleForm.dataSourceTable"
                placeholder="请选择"
                filter
                allow-clear
                :disabled="state.disabled"
                @value-change="dataSourceTableChange"
              >
                <n-option
                  v-for="item in state.dataSourceTableOptions"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </div>

          <div class="right-item">
            <n-form-item label="选择数据模型：" field="modeFeign">
              <div class="default-name">{{ state.ruleForm.modeFeignName }}</div>
            </n-form-item>
            <n-form-item field="modeTable">
              <n-select
                v-model="state.ruleForm.modeTable"
                placeholder="请选择"
                filter
                allow-clear
                :disabled="state.disabled"
                @value-change="modeTableChange"
              >
                <n-option
                  v-for="item in state.modeTableOptions"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </div>
        </div>
      </n-form>
      <div class="link-mapping-box">
        <SvgIcon
          v-if="state.leftData.length && state.rightData.length"
          class="maximize"
          icon="maximize-icon"
          title="最大化"
          @click.prevent="maximizeLinkMapping"
        />
        <sceneTableLinkDetail v-if="state.disabled" ref="sceneTableLink" :oneline="true" />
        <sceneTableLink v-else ref="sceneTableLink" :oneline="true" />
      </div>
    </div>

    <n-modal
      v-model="state.dialogVisible"
      class="dialog link-mapping-fullPage-box"
      title=""
      width="86%"
      :draggable="false"
      :close-on-click-overlay="false"
      :append-to-body="false"
    >
      <SvgIcon
        class="minimize"
        icon="minimize-icon"
        @click.prevent="minimizeLinkMapping"
        title="最小化"
      />

      <sceneTableLinkDetail
        v-if="state.disabled"
        ref="sceneTableLinkFullPage"
        containerId="containerEr2"
      />
      <sceneTableLink v-else ref="sceneTableLinkFullPage" containerId="containerEr2" />
      <div class="bottom"></div>
    </n-modal>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import sceneTableLink from '../scene-table-link'
  import sceneTableLinkDetail from '../scene-table-link/detail'

  export default {
    name: '',
    components: { sceneTableLink, sceneTableLinkDetail },
    props: {},
    setup() {
      const state = reactive({
        disabled: false, // 创建状态才能编辑模型
        dialogVisible: false,
        ruleForm: {
          dataSourceName: '',
          dataSourceType: '',
          dataSourceFeign: '',
          dataSourceTable: '',
          modeFeign: '',
          modeFeignName: '原始数据层',
          modeTable: '',
          modeTableName: '',
        },
        rules: {
          dataSourceType: { required: true, message: '请选择', trigger: 'change' },
          dataSourceFeign: [
            { type: 'number', required: true, message: '请选择', trigger: 'change' },
          ],
          dataSourceTable: { required: true, message: '请选择', trigger: 'change' },
          modeFeign: [{ type: 'number', required: true, message: '请选择', trigger: 'change' }],
          modeTable: [{ type: 'number', required: true, message: '请选择', trigger: 'change' }],
        },
        sourceTableData: [],
        authorizedTableData: [],
        allData: {
          name: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [1, 1],
          authorizedPage: [1],
        },
        queryData: '',
        editId: null, // 查看模式id
        datasourceTypeOptions: [],
        dataSourceOptions: [],
        dataSourceTableOptions: [],
        modeFeignOptions: [],
        modeTableOptions: [],
        oldModeTableOptions: [], //保留原始数据

        leftData: [], // 左侧数据
        rightData: [], // 右侧数据
        totalData: {}, // 所有数据
        activeModeTableItem: [], // 选中的数据模型table
      })
      const sceneTableLink = ref()
      const sceneTableLinkFullPage = ref()
      const ruleForm = ref()
      const router = useRouter()

      const methods = {
        filterMethod(value) {
          state.modeTableOptions = state.oldModeTableOptions.filter(
            // (item) => item.label.includes(value) || item.cnName.includes(value),
            (item) => item.label.toUpperCase().includes(value.toUpperCase()),
          )
        },
        // 最大化
        maximizeLinkMapping() {
          state.dialogVisible = true
          let result = sceneTableLink.value.getGraphData()
          // 连线数据
          let _columnMapping = result.cells.filter((item) => {
            return item.shape === 'edge'
          })
          nextTick(() => {
            sceneTableLinkFullPage.value.changTable(
              {
                leftData: state.leftData,
                rightData: state.rightData,
                dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
                modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
              },
              function (that) {
                let { data, methods } = that
                // 连线
                if (_columnMapping.length) {
                  _columnMapping.forEach((item) => {
                    data.graph.addEdge(item)
                    if (!state.disabled) {
                      methods.setLineIdArr(item)
                    }
                  })
                }
                data.graph.scrollToPoint(600, 250) // 将 x 和 y 指定的点（相对于画布）滚动到视口中心，如果只指定了其中一个方向，则只滚动对应的方向
              },
            )
          })
        },
        // 最小化
        minimizeLinkMapping() {
          let result = sceneTableLinkFullPage.value.getGraphData()
          // 连线数据
          let _columnMapping = result.cells.filter((item) => {
            return item.shape === 'edge'
          })
          let _state = state
          nextTick(() => {
            sceneTableLink.value.changTable(
              {
                leftData: state.leftData,
                rightData: state.rightData,
                dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
                modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
              },
              function (that) {
                // 连线
                let { data, methods } = that
                if (_columnMapping.length) {
                  methods.clearEdge()
                  _columnMapping.forEach((item) => {
                    data.graph.addEdge(item)
                    if (!state.disabled) {
                      methods.setLineIdArr(item)
                    }
                  })
                }
                _state.dialogVisible = false
              },
            )
          })
        },
        // 获取数据源列表
        getOfflineDatasourceList() {
          api.dataManagement.getOfflineDatasourceList().then((res) => {
            let { data } = res
            let _data = []
            data.forEach((item) =>
              _data.push({
                label: item,
                value: item,
              }),
            )
            state.datasourceTypeOptions = _data
          })
        },
        //画布重新渲染
        canvasUpdate() {
          sceneTableLink.value.clearEdge() //清空连线
          sceneTableLink.value.changTable({
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
          })
        },
        // 数据源类型下拉-change
        datasourceTypeChange() {
          state.ruleForm.dataSourceFeign = ''
          state.ruleForm.dataSourceTable = ''
          state.dataSourceOptions = []
          state.dataSourceTableOptions = []

          if (state.ruleForm.dataSourceType) {
            methods.getDatasourceFeignList()
          } else {
            state.leftData = []
            methods.canvasUpdate()
          }
        },

        // 获取数据源下拉层-列表
        getDatasourceFeignList() {
          api.dataManagement
            .getDatasourceFeignList({ datasourceType: state.ruleForm.dataSourceType })
            .then((res) => {
              let { data } = res
              data.forEach((item) => {
                item.label = item.name
                item.value = item.id
              })
              state.dataSourceOptions = data
              state.leftData = []
              methods.canvasUpdate()
            })
        },
        //  数据源下拉层-change
        dataSourceFeignChange(data) {
          state.dataSourceTableOptions = []
          state.ruleForm.dataSourceTable = ''
          if (data) {
            let _data = state.dataSourceOptions.filter((item) => {
              return item.id === data.value
            })
            state.ruleForm.dataSourceName = _data[0].name
            methods.getSourceTables({ dataSourceId: state.ruleForm.dataSourceFeign })
          } else {
            state.leftData = []
            methods.canvasUpdate()
          }
        },
        // 根据数据源下拉层获取表数据
        getSourceTables(data) {
          api.dataManagement.getSourceTables(data).then((res) => {
            let { data } = res
            let _dataSourceTableOptions = []
            data.forEach((item) => {
              _dataSourceTableOptions.push({
                label: item,
                value: item,
              })
            })
            state.dataSourceTableOptions = _dataSourceTableOptions
            state.leftData = []
            methods.canvasUpdate()
          })
        },
        // 数据源下拉层change获取表数据
        dataSourceTableChange(data) {
          if (data) {
            let { dataSourceFeign, dataSourceTable } = state.ruleForm
            if (!(dataSourceFeign && dataSourceTable)) return
            let _data = {
              id: dataSourceFeign,
              tableName: dataSourceTable,
            }
            api.dataManagement.getSourceStructure(_data).then((res) => {
              // 渲染左边表格
              state.leftData = res.data
              if (!res.data.length) return
              methods.canvasUpdate()
            })
          } else {
            state.leftData = []
            methods.canvasUpdate()
          }
        },
        // 模型分层下拉列表
        getDataModelTree() {
          api.model.getDataModelTree().then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.name === '原始数据层') {
                state.ruleForm.modeFeign = item.id
              }
            })
            state.modeFeignOptions = data
            methods.modeFeignChange({ value: state.ruleForm.modeFeign })
          })
        },
        // 模型分层change
        modeFeignChange(data) {
          let _data = state.modeFeignOptions.filter((item) => {
            return item.id === data.value
          })
          state.ruleForm.modeFeignName = _data[0].label
          let params = { layerId: state.ruleForm.modeFeign, jobType: 'COLLECT_JOB' }
          if (state.editId) {
            params.jobId = state.editId
          }
          api.model.getModelListWithLayerId(params).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.cnName = item.cnName
              item.value = item.id
              item.label = item.name
            })
            // state.ruleForm.modeTable = ''
            // state.ruleForm.modeTableName = ''

            state.oldModeTableOptions = data
            state.modeTableOptions = data

            // state.rightData = []
            // methods.canvasUpdate()
          })
        },
        // 模型列表change
        modeTableChange(data) {
          if (data) {
            state.activeModeTableItem = state.modeTableOptions.filter((item) => {
              return item.id === data.value
            })
            state.ruleForm.modeTableName = state.activeModeTableItem[0].label

            api.model.getModeData({ id: state.ruleForm.modeTable }).then((res) => {
              // 渲染右边表格
              state.rightData = res.data
              if (!res.data.length) return
              methods.canvasUpdate()
            })
          } else {
            state.rightData = []
            methods.canvasUpdate()
          }
        },

        // 初始化数据
        init(data) {
          state.totalData = data
          if (state.editId) {
            let {
              dataSourceId,
              sourceTableName,
              sinkModelName,
              modelName,
              dataSourceType,
              tableData,
              dataSourceName,
              modelId,
            } = data

            state.ruleForm.dataSourceFeign = dataSourceId // 数据源id
            state.ruleForm.dataSourceTable = sourceTableName //// 数据源下表名
            state.ruleForm.name = sinkModelName //模型英文名
            state.ruleForm.cnName = modelName //模型中文名
            state.ruleForm.dataSourceType = dataSourceType // 数据库类型
            state.ruleForm.dataSourceName = dataSourceName
            state.ruleForm.modeTable = modelId //模型ID
            state.leftData = tableData

            methods.editInit(data)
          } else {
            let { name, description } = data

            state.allData.name = name
            state.allData.description = description
          }

          nextTick(() => {
            methods.initTable()
          })
        },
        //编辑初始化下拉框数据
        editInit(data) {
          //获取数据源列表
          api.dataManagement
            .getDatasourceFeignList({ datasourceType: state.ruleForm.dataSourceType })
            .then((res) => {
              let { data } = res
              data.forEach((item) => {
                item.label = item.name
                item.value = item.id
              })
              state.dataSourceOptions = data
            })

          api.dataManagement
            .getSourceTables({ dataSourceId: state.ruleForm.dataSourceFeign })
            .then((res) => {
              let { data } = res
              let _dataSourceTableOptions = []
              data.forEach((item) => {
                _dataSourceTableOptions.push({
                  label: item,
                  value: item,
                })
              })
              state.dataSourceTableOptions = _dataSourceTableOptions
            })
          methods.editInitCanvas(data)
        },

        // 编辑初始化数据
        async editInitCanvas(data) {
          if (data) {
            let { name, sourceTableName, sinkModelName, layerName } = data
            state.dataSourceNames = [name, sourceTableName]
            state.modelNames = [layerName, sinkModelName]

            await api.dataManagement
              .getSourceStructure({
                id: data.dataSourceId,
                tableName: data.sourceTableName,
              })
              .then((res1) => {
                // 渲染左边表格
                state.leftData = res1.data
              })

            await api.model.getModeData({ id: data.modelId }).then((res2) => {
              // 渲染右边表格
              state.rightData = res2.data
            })
            state.loading = false

            data.graphData = {
              leftData: state.leftData,
              rightData: state.rightData,
              dataSourceNames: state.dataSourceNames,
              modelNames: state.modelNames,
            }
            data.allEdge = data.columnMapping
            nextTick(() => {
              sceneTableLink.value.changTable(data.graphData, function (that) {
                let { methods } = that
                // 连线
                if (state.totalData.allEdge) {
                  methods.addEdge({ data: state.totalData.allEdge, initLater: true })
                }
                // data.graph.scrollToPoint(600, 150)
              })
            })
          }
        },

        // 表格初始化
        initTable() {
          methods.getDataModelTree()
        },

        // 回传数据
        async getAllData() {
          let passedLink = false
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          if (!_result.passed) {
            return _result
          }
          state.totalData.dataSourceId = state.ruleForm.dataSourceFeign // 数据源id
          state.totalData.sourceTableName = state.ruleForm.dataSourceTable
          if (state.activeModeTableItem.length) {
            state.totalData.sinkModelName = state.activeModeTableItem[0].name
            state.totalData.modelName = state.activeModeTableItem[0].cnName
          }

          state.totalData.modelId = state.ruleForm.modeTable // 模型id
          state.totalData.dataSourceType = state.ruleForm.dataSourceType // 数据库类型

          let result = sceneTableLink.value.getGraphData()
          // 连线数据
          let _columnMapping = result.cells.filter((item) => {
            return item.shape === 'edge'
          })
          let _data = result.cells.filter((cell) => {
            return cell.shape === 'er-rect'
          })

          // 所有右侧数据
          // let _sourceDate = _data.filter((cell) => {
          //   return cell.attrs.text.text === '目的表'
          // })
          let alldata = []
          _data.forEach((item) => {
            alldata.push(...item.ports.items)
          })
          let needColumnMapping = []
          if (_columnMapping.length) {
            // if (_columnMapping.length !== _sourceDate[0].ports.items.length - 1) {
            //   passedLink = false
            //   ElNotification({
            //     title: '提示',
            //     message: '右侧表必须全部建立映射关系！',
            //     type: 'warning',
            //   })
            // } else {
            passedLink = true
            _columnMapping.forEach((item, index) => {
              needColumnMapping[index] = {}
              alldata.forEach((cell) => {
                if (item.source.port === cell.id) {
                  needColumnMapping[index].sourceColumnName = cell.attrs.colName.text
                  needColumnMapping[index].sourceColumnType = cell.attrs.dataType.text
                  needColumnMapping[index].sourceColumnLength = cell.attrs.dataLength.text
                }
                if (item.target.port === cell.id) {
                  needColumnMapping[index].sinkMetaCode = cell.attrs.colName.text
                  needColumnMapping[index].sinkMetaType = cell.attrs.dataType.text
                  needColumnMapping[index].sinkMetaLength = cell.attrs.dataLength.text
                  needColumnMapping[index].sinkSortNum = cell.attrs.sortNum.text
                }
              })
            })
            // }
          } else {
            passedLink = false
            ElNotification({
              title: '提示',
              message: '请建立映射关系',
              type: 'warning',
            })
          }
          state.totalData.columnMapping = needColumnMapping

          state.totalData.graphData = {
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.name],
          }
          state.totalData.allEdge = _columnMapping

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid && passedLink, data: state.totalData })
            })
          })
          return _result2
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.editId = state.queryData.editId
        state.disabled = state.queryData.status && state.queryData.status !== 'NEW' ? true : false
        methods.getOfflineDatasourceList()
      })

      return {
        state,
        sceneTableLink,
        sceneTableLinkFullPage,
        ruleForm,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f4f4f4;
  $outBg: #eeeeee;

  .data-collection-link-mapping {
    background: #eeeeee;
    height: 100%;
    background-color: $outBg;
    .box-top {
      padding: 16px 20px;
      background-color: $contentBoxBg;
      border-radius: 4px;
      .project-name {
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: $font;
        line-height: 24px;
        display: flex;
      }
      .project-user {
        padding-bottom: 10px;

        .project-user-content {
          padding: 8px 0 0;
        }
        span {
          display: inline-block;
          height: 22px;
          padding: 0 14px;
          margin-right: 12px;
          background: $spanBg;
          border-radius: 2px;
        }
      }
      .project-describe {
        padding: 10px 0 16px 0;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $font;
        line-height: 20px;
        border-top: 1px solid $border;
        max-height: 86px;
        min-height: 47px;
      }
    }
    .box-bottom {
      height: 100%;
      background-color: $contentBoxBg;
      border-radius: 0 0 4px;
      .nancalui-form {
        :deep(.nancalui-select--disabled) {
          .nancalui-select__arrow {
            display: none;
          }
        }
        .first-line {
          .nancalui-select {
            width: 160px;
          }
        }
        .one-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .left-item {
          display: flex;
          .nancalui-form__item--horizontal {
            width: auto;
            margin-right: 10px;
          }
        }
        .right-item {
          display: flex;
          .nancalui-form__item--horizontal {
            width: 100%;
            margin-right: 10px;

            :deep(.nancalui-form__label) {
              flex: 0 0 96px !important;
            }
            .default-name {
              color: #666;
              font-size: 12px;
            }
            &:last-of-type {
              margin-right: 0;
            }
          }
        }
      }
      .link-mapping-box {
        position: relative;
        height: calc(100% - 80px);
        .maximize {
          position: absolute;
          top: 54px;
          right: 12px;
          cursor: pointer;
          z-index: 99;
        }
      }
    }
    :deep(.link-mapping-fullPage-box) {
      .btn-close {
        display: none;
      }
      .nancalui-modal__body {
        height: 80vh;
        padding: 20px 30px 67px;

        .minimize {
          float: right;
          cursor: pointer;
        }
      }
    }
  }
</style>
