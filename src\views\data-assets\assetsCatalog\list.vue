<template>
  <section class="container">
    <section class="container-box">
      <section class="cf-tools">
        <div class="row">
          <div class="col text-label">
            名称：
            <n-input v-model="condition.keyword" placeholder="资产名称/标签" />
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <section class="cf-tree">
          <div class="page-title"> 资产目录 </div>
          <n-input
            class="table-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入关键词"
            suffix="search"
            @change="(val) => treeRef.treeRef.filter(val)"
          />
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :filter-node-method="filterNode"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeList"
            @node-click="
              (node) => {
                state.selectedKey = node.id
                state.filterSearch.condition.classificationCode = node.code
                if (node.code === 'DORIS_DB') {
                  state.filterSearch.condition.type = undefined
                }
                node.code === 'DORIS_DB'
                  ? getList(
                      Object.assign(
                        {},
                        {
                          ...state.filterSearch,
                          condition: {
                            classificationCode: '',
                            keyword: condition.keyword,
                            type: 'TABLE',
                          },
                        },
                      ),
                    )
                  : onSearch()
              }
            "
          />
        </section>
        <section v-loading="state.loading" class="talble-container">
          <section class="tabs-tools">
            <div class="tabs">
              <div class="tab">资产类型</div>
              <!--              <div-->
              <!--                class="tab"-->
              <!--                :class="{-->
              <!--                  active: state.filterSearch.condition.type === item.code,-->
              <!--                }"-->
              <!--                v-for="(item, index) in state.typeList"-->
              <!--                :key="index"-->
              <!--                @click="-->
              <!--                  () => {-->
              <!--                    state.filterSearch.condition.type = item.code-->
              <!--                    onSearch()-->
              <!--                  }-->
              <!--                "-->
              <!--              >-->
              <!--                {{ item.text }}-->
              <!--              </div>-->
              <div
                class="tab"
                :class="{
                  active: state.filterSearch.condition.type === undefined,
                }"
                @click="
                  () => {
                    state.filterSearch.condition.type = undefined
                    onSearch()
                  }
                "
                >全部</div
              >
              <div
                class="tab"
                :class="{
                  active: state.filterSearch.condition.type === 'TABLE_OR_RESOURCE_DIRECTORY',
                }"
                @click="
                  () => {
                    state.filterSearch.condition.type = 'TABLE_OR_RESOURCE_DIRECTORY'
                    onSearch()
                  }
                "
                >结构化资源目录</div
              >
              <div
                v-if="state.filterSearch.condition.classificationCode !== 'DORIS_DB'"
                class="tab"
                :class="{
                  active: state.filterSearch.condition.type === 'FILE',
                }"
                @click="
                  () => {
                    state.filterSearch.condition.type = 'FILE'
                    onSearch()
                  }
                "
                >非结构化资源目录</div
              >
            </div>
            <!--            <div class="tabs">-->
            <!--              <div class="tab">数据来源</div>-->
            <!--              <div-->
            <!--                class="tab"-->
            <!--                :class="{-->
            <!--                  active: state.filterSearch.condition.sourceCode === item.code,-->
            <!--                }"-->
            <!--                v-for="(item, index) in state.sourceList"-->
            <!--                :key="index"-->
            <!--                @click="-->
            <!--                  () => {-->
            <!--                    state.filterSearch.condition.sourceCode = item.code-->
            <!--                    onSearch()-->
            <!--                  }-->
            <!--                "-->
            <!--              >-->
            <!--                {{ item.name }}-->
            <!--              </div>-->
            <!--            </div>-->
          </section>
          <div class="header"
            >系统已为您找到相关结果
            <span>{{ state.total }}</span>
            个</div
          >
          <div class="card-container">
            <!-- 卡片 -->
            <div class="card-item" v-for="item in state.tableList" :key="item">
              <div class="card-item-top">
                <div
                  v-html="
                    {
                      FILE: `<svg width='40' height='41' viewBox='0 0 40 41' fill='none' xmlns='http://www.w3.org/2000/svg'> <rect y='0.5' width='40' height='40' rx='6' fill='#447DFD'/> <path d='M13.25 11.25C13.25 10.8358 13.5858 10.5 14 10.5H26C26.4142 10.5 26.75 10.8358 26.75 11.25C26.75 11.6642 26.4142 12 26 12H14C13.5858 12 13.25 11.6642 13.25 11.25Z' fill='white' fill-opacity='0.46'/> <path d='M13.25 13.5C12.4216 13.5 11.75 14.1716 11.75 15V23.25C11.75 24.0784 12.4216 24.75 13.25 24.75H20.4361C23 24.75 25.732 24.75 28.25 24.75C28.25 24.75 28.25 22.5 28.25 21.4361V15C28.25 14.1716 27.5784 13.5 26.75 13.5H13.25Z' fill='white' fill-opacity='0.65'/> <path d='M22.0313 31.5C20.7856 30.4006 20 28.792 20 27H14.75C14.3358 27 14 26.6642 14 26.25C14 25.8358 14.3358 25.5 14.75 25.5H20.189C20.4929 24.3193 21.1472 23.2793 22.0343 22.4973C22.0045 22.4991 21.9745 22.5 21.9443 22.5H18.0557C17.6231 22.5 17.2116 22.3133 16.9268 21.9878L15.1982 20.0122C14.9134 19.6867 14.5019 19.5 14.0693 19.5H11C10.1716 19.5 9.5 20.1716 9.5 21V30C9.5 30.8284 10.1716 31.5 11 31.5H22.0313Z' fill='white'/> <path d='M17.9687 31.5C19.2144 30.4006 20 28.792 20 27H25.25C25.6642 27 26 26.6642 26 26.25C26 25.8358 25.6642 25.5 25.25 25.5H19.811C19.5071 24.3193 18.8528 23.2793 17.9657 22.4973C17.9955 22.4991 18.0255 22.5 18.0557 22.5H21.9443C22.3769 22.5 22.7884 22.3133 23.0732 21.9878L24.8018 20.0122C25.0866 19.6867 25.4981 19.5 25.9307 19.5H29C29.8284 19.5 30.5 20.1716 30.5 21V30C30.5 30.8284 29.8284 31.5 29 31.5H17.9687Z' fill='white'/> </svg>`,
                      TABLE: `<svg width=&quot;40&quot; height=&quot;41&quot; viewBox=&quot;0 0 40 41&quot; fill=&quot;none&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><rect y=&quot;0.5&quot; width=&quot;40&quot; height=&quot;40&quot; rx=&quot;6&quot; fill=&quot;#04C495&quot;/><path d=&quot;M26 17.5H29C29.8284 17.5 30.5 18.1716 30.5 19V26.5C30.5 28.9853 28.4853 31 26 31V17.5Z&quot; fill=&quot;#75EBC2&quot;/><path d=&quot;M9.5 11.5C9.5 10.6716 10.1716 10 11 10H24.5C25.3284 10 26 10.6716 26 11.5V31H11C10.1716 31 9.5 30.3284 9.5 29.5V11.5Z&quot; fill=&quot;white&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M16.25 16.75C16.25 16.3358 16.5858 16 17 16H22.25C22.6642 16 23 16.3358 23 16.75C23 17.1642 22.6642 17.5 22.25 17.5H17C16.5858 17.5 16.25 17.1642 16.25 16.75Z&quot; fill=&quot;#04C495&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M12.5 16.75C12.5 16.3358 12.8358 16 13.25 16H14C14.4142 16 14.75 16.3358 14.75 16.75C14.75 17.1642 14.4142 17.5 14 17.5H13.25C12.8358 17.5 12.5 17.1642 12.5 16.75Z&quot; fill=&quot;#04C495&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M16.25 20.5C16.25 20.0858 16.5858 19.75 17 19.75H22.25C22.6642 19.75 23 20.0858 23 20.5C23 20.9142 22.6642 21.25 22.25 21.25H17C16.5858 21.25 16.25 20.9142 16.25 20.5Z&quot; fill=&quot;#04C495&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M12.5 20.5C12.5 20.0858 12.8358 19.75 13.25 19.75H14C14.4142 19.75 14.75 20.0858 14.75 20.5C14.75 20.9142 14.4142 21.25 14 21.25H13.25C12.8358 21.25 12.5 20.9142 12.5 20.5Z&quot; fill=&quot;#04C495&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M16.25 24.25C16.25 23.8358 16.5858 23.5 17 23.5H22.25C22.6642 23.5 23 23.8358 23 24.25C23 24.6642 22.6642 25 22.25 25H17C16.5858 25 16.25 24.6642 16.25 24.25Z&quot; fill=&quot;#04C495&quot;/><path fill-rule=&quot;evenodd&quot; clip-rule=&quot;evenodd&quot; d=&quot;M12.5 24.25C12.5 23.8358 12.8358 23.5 13.25 23.5H14C14.4142 23.5 14.75 23.8358 14.75 24.25C14.75 24.6642 14.4142 25 14 25H13.25C12.8358 25 12.5 24.6642 12.5 24.25Z&quot; fill=&quot;#04C495&quot;/></svg>`,
                      RESOURCE_DIRECTORY: `<svg width='40' height='41' viewBox='0 0 40 41' fill='none' xmlns='http://www.w3.org/2000/svg'><rect y='0.5' width='40' height='40' rx='6' fill='#7E6CF3'/><path d='M9.5 14.875C9.5 12.1826 11.6826 10 14.375 10C17.0674 10 19.25 12.1826 19.25 14.875V19.75H14.375C11.6826 19.75 9.5 17.5674 9.5 14.875Z' fill='white'/><path d='M9.5 26.125C9.5 28.8174 11.6826 31 14.375 31C17.0674 31 19.25 28.8174 19.25 26.125V21.25H14.375C11.6826 21.25 9.5 23.4326 9.5 26.125Z' fill='white' fill-opacity='0.65'/><path d='M30.5 26.125C30.5 28.8174 28.3174 31 25.625 31C22.9326 31 20.75 28.8174 20.75 26.125V21.25H25.625C28.3174 21.25 30.5 23.4326 30.5 26.125Z' fill='white'/><path fill-rule='evenodd' clip-rule='evenodd' d='M30.5 14.875C30.5 15.2892 30.1642 15.625 29.75 15.625L21.5 15.625C21.0858 15.625 20.75 15.2892 20.75 14.875C20.75 14.4608 21.0858 14.125 21.5 14.125L29.75 14.125C30.1642 14.125 30.5 14.4608 30.5 14.875Z' fill='white' fill-opacity='0.65'/><path fill-rule='evenodd' clip-rule='evenodd' d='M29 18.25C29 18.6642 28.6642 19 28.25 19L21.5 19C21.0858 19 20.75 18.6642 20.75 18.25C20.75 17.8358 21.0858 17.5 21.5 17.5L28.25 17.5C28.6642 17.5 29 17.8358 29 18.25Z' fill='white' fill-opacity='0.65'/><path fill-rule='evenodd' clip-rule='evenodd' d='M29.75 11.5C29.75 11.9142 29.4142 12.25 29 12.25L22.25 12.25C21.8358 12.25 21.5 11.9142 21.5 11.5C21.5 11.0858 21.8358 10.75 22.25 10.75L29 10.75C29.4142 10.75 29.75 11.0858 29.75 11.5Z' fill='white' fill-opacity='0.65'/></svg>`,
                    }[item.type]
                  "
                >
                </div>
                <div class="card-title">
                  <div class="title" @click="detailDialogRef.open(item)">{{ item.name }} </div>
                  <div class="content">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="17"
                      viewBox="0 0 16 17"
                      fill="none"
                    >
                      <g clip-path="url(#clip0_1302_196555)">
                        <path
                          d="M8 3.5C3.73932 3.5 1.63529 6.62559 1.05233 8.33683C1.01627 8.44268 1.01627 8.55732 1.05233 8.66317C1.63529 10.3744 3.73932 13.5 8 13.5C12.2607 13.5 14.3647 10.3744 14.9477 8.66317C14.9837 8.55732 14.9837 8.44268 14.9477 8.33683C14.3647 6.62559 12.2607 3.5 8 3.5Z"
                          stroke="#8091B7"
                          stroke-width="1.14286"
                        />
                        <circle cx="8" cy="8.5" r="2" stroke="#8091B7" stroke-width="1.14286" />
                      </g>
                      <defs>
                        <clipPath id="clip0_1302_196555">
                          <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                        </clipPath>
                      </defs>
                    </svg>
                    {{ item.viewCount || '0' }}
                  </div>
                </div>

                <div class="bnts">
                  <template v-if="['RESOURCE_DIRECTORY', 'TABLE'].includes(item.type)">
                    <div
                      v-if="['TABLE'].includes(item.type)"
                      class="btn"
                      @click="standardSpecificationRef.open(item)"
                      >标准规范</div
                    >
                    <div class="btn" @click="getAssetsTableDataFn(item)">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="17"
                        viewBox="0 0 16 17"
                        fill="none"
                      >
                        <path
                          d="M1.33203 2.83398H14.6654"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.33203 8.16602H4.9987"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.33203 13.5H4.9987"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M10.5013 11.8327C12.0661 11.8327 13.3346 10.5641 13.3346 8.99935C13.3346 7.43455 12.0661 6.16602 10.5013 6.16602C8.9365 6.16602 7.66797 7.43455 7.66797 8.99935C7.66797 10.5641 8.9365 11.8327 10.5013 11.8327Z"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M12.332 11.166L14.6654 13.5162"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                      查询
                    </div>
                    <n-button
                      v-if="['TABLE'].includes(item.type)"
                      class="btn"
                      :disabled="!item.hasApi"
                      @click="ApiInterfaceFn(item)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="17"
                        viewBox="0 0 16 17"
                        fill="none"
                      >
                        <path
                          d="M6.94336 4.61328L8.71113 2.84551C10.078 1.47868 12.294 1.47868 13.6609 2.84551V2.84551C15.0277 4.21235 15.0277 6.42843 13.6609 7.79526L11.8931 9.56303L6.94336 4.61328Z"
                          stroke="currentColor"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M9.06445 12.3926L7.29669 14.1603C5.92985 15.5272 3.71377 15.5272 2.34694 14.1603V14.1603C0.980104 12.7935 0.980104 10.5774 2.34694 9.2106L4.1147 7.44283L9.06445 12.3926Z"
                          stroke="currentColor"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15.0742 1.43164L13.66 2.84585"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <path
                          d="M6.94336 7.44336L5.8827 8.50402"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <path
                          d="M9.06445 9.56445L8.00379 10.6251"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <path
                          d="M2.34766 14.1602L0.933443 15.5744"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                      </svg>
                      API接口
                    </n-button>
                  </template>
                  <template v-if="item.type === 'FILE'">
                    <div
                      class="btn"
                      @click="
                        $router.push({
                          name: 'assetsRegDocView',
                          query: { id: item.registerFrom },
                        })
                      "
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="17"
                        viewBox="0 0 16 17"
                        fill="none"
                      >
                        <path
                          d="M1.33203 2.83398H14.6654"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.33203 8.16602H4.9987"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.33203 13.5H4.9987"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M10.5013 11.8327C12.0661 11.8327 13.3346 10.5641 13.3346 8.99935C13.3346 7.43455 12.0661 6.16602 10.5013 6.16602C8.9365 6.16602 7.66797 7.43455 7.66797 8.99935C7.66797 10.5641 8.9365 11.8327 10.5013 11.8327Z"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M12.332 11.166L14.6654 13.5162"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                      查询
                    </div>
                    <div class="btn" @click="onDownLoad(item.registerFrom)">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="17"
                        viewBox="0 0 16 17"
                        fill="none"
                      >
                        <path
                          d="M2 9V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V9"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <line
                          x1="0.5"
                          y1="-0.5"
                          x2="5.15685"
                          y2="-0.5"
                          transform="matrix(-0.707107 0.707107 0.707107 0.707107 12 7.5)"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <line
                          x1="4.70711"
                          y1="7.5"
                          x2="8"
                          y2="10.7929"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                        <line
                          x1="8"
                          y1="10"
                          x2="8"
                          y2="2"
                          stroke="currentColor"
                          stroke-linecap="round"
                        />
                      </svg>
                      下载
                    </div>
                  </template>
                </div>
              </div>
              <div class="card-item-bottom">
                <div class="type">
                  <div class="type-title">资产类型：</div>
                  <div
                    class="type-content"
                    :title="
                      {
                        TABLE: '结构化资源目录',
                        FILE: '非结构化资源目录',
                        RESOURCE_DIRECTORY: '结构化资源目录',
                      }[item.type] || '--'
                    "
                  >
                    {{
                      {
                        TABLE: '结构化资源目录',
                        FILE: '非结构化资源目录',
                        RESOURCE_DIRECTORY: '结构化资源目录',
                      }[item.type] || '--'
                    }}
                  </div>
                </div>
                <div class="type">
                  <div class="type-title">来源系统：</div>
                  <div class="type-content" :title="item.sourceName || '--'">{{
                    item.sourceName || '--'
                  }}</div>
                </div>
                <div class="type">
                  <div class="type-title">资产分类：</div>
                  <div class="type-content" :title="item.classificationName || '--'">{{
                    item.classificationName || '--'
                  }}</div>
                </div>
              </div>

              <div class="card-item-bottom">
                <div class="type">
                  <div class="type-title lang">资产持有部门：</div>
                  <div class="type-content" :title="item.ownerDept || '--'">{{
                    item.ownerDept || '--'
                  }}</div>
                </div>
                <div class="type">
                  <div class="type-title">资产估值：</div>
                  <div class="type-content" :title="item.valuation || '--'">{{
                    item.valuation || '--'
                  }}</div>
                </div>
                <div class="type">
                  <div class="type-title">标签：</div>
                  <div class="type-tag">
                    <cfTag :tagArr="item?.tag || '[]'" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="height: 60px; width: 100%">
            <CfTable
              :paginationConfig="{
                total: state.total,
                pageSize: state.filterSearch.pageSize,
                currentPage: state.filterSearch.pageNum,
                onCurrentChange: (v) => {
                  state.filterSearch.pageNum = v
                  onSearch()
                },
                onSizeChange: (v) => {
                  state.filterSearch.pageSize = v
                  state.filterSearch.pageNum = 1
                  onSearch()
                },
              }"
            />
          </div>
        </section>
      </section>
      <DetailDialog ref="detailDialogRef" @success="onSearch" />
      <ConfigDrawer ref="configDrawerRef" @success="onSearch" />
      <standardSpecification ref="standardSpecificationRef" />
    </section>
  </section>
</template>
<script setup>
  import { nextTick } from 'vue'
  import CfTtee from '@/components/cfTtee'
  import cfTag from '@/components/cfTag'
  import DetailDialog from './components/detailDialog'
  import standardSpecification from './components/standardSpecification'
  import ConfigDrawer from './components/configDrawer'
  import api from '@/api/index'
  import {
    getAssetsList,
    getAssetsSourceList,
    getAssetsClassifyTreeList,
    getAssetsTableData,
  } from '@/api/sceneManage'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const treeRef = ref(null)
  const detailDialogRef = ref(null)
  const standardSpecificationRef = ref(null)

  const state = reactive({
    open: false,
    loading: false,
    treeSearchText: '',
    sourceList: [],
    typeList: [],
    treeList: [],
    tableList: [],
    // 资产类型
    type: '',
    // 数据来源
    filterSearch: {
      condition: {
        classificationCode: '',
        keyword: '',
        sourceCode: undefined,
        type: undefined,
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    total: 0,
    tableHeadTitles: [
      {
        name: '创建时间',
        prop: 'createTime',
      },
    ],
    expandedKeys: [],
    selectedKey: null,
  })
  const condition = ref({})

  // 表数据查询
  const getAssetsTableDataFn = (item) => {
    state.loading = true
    const params = {
      condition: {
        executeCount: true,
        columns: [],
        filterCondition: [
          {
            colName: '',
            operator: '',
            value: '',
          },
        ],
        id: item.id,
        orderBy: [
          {
            field: '',
            order: '',
          },
        ],
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    }
    getAssetsTableData(params)
      .then((res) => {
        state.loading = false

        router.push({
          name: 'assetsCatalogQuery',
          query: {
            id: item.id,
            registerFrom: item.registerFrom,
            type: item.type,
            name: item.name,
          },
        })
      })
      .catch((err) => {
        state.loading = false
      })
  }

  const ApiInterfaceFn = (item) => {
    if (item.hasApi) {
      router.push({
        name: 'assetsCatalogApiQuery',
        query: {
          name: item.name,
          enName: item.enName,
          id: item.id,
        },
      })
    }
  }

  const filterNode = (value, data) => {
    if (!value) return true
    return data.name?.includes(value)
  }
  // 获取列表
  const getList = (params) => {
    state.loading = true
    let data = JSON.parse(JSON.stringify(params || state.filterSearch))
    if (
      data?.condition?.classificationCode === 'DORIS_DB' ||
      (!data?.condition?.classificationCode && data?.condition?.type === 'RESOURCE_DIRECTORY')
    ) {
      if (data?.condition?.classificationCode === 'DORIS_DB') {
        data.condition.type = 'TABLE'
      } else {
        data.condition.type = 'TABLE_OR_RESOURCE_DIRECTORY'
      }
      data.condition.classificationCode = ''
    }
    getAssetsList(data)
      .then((res) => {
        state.loading = false
        state.tableList = res.data?.list || []
        state.total = res.data?.total || 0
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 搜索
  const onSearch = (init) => {
    const { condition: filterC } = state.filterSearch
    init &&
      Object.assign(state.filterSearch, {
        pageNum: 1,
        condition: { ...filterC, ...condition.value },
      })
    getList()
  }
  // 非结构化数据下载
  async function onDownLoad(id) {
    try {
      const res = await api.documentManage.outsideGet({ id: id })
      const { docUrl, name } = res.data
      let lafix = docUrl.substring(docUrl.lastIndexOf('.'))
      const fileName = name + lafix
      getBlob(docUrl).then((blob) => {
        saveAs(blob, fileName)
      })
    } finally {
    }
  }
  // 获取文件流
  function getBlob(url) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(xhr.response)
        }
      }
      xhr.send()
    })
  }
  // 下载文件
  function saveAs(blob, filename) {
    var link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = filename
    link.click()
  }

  // 数据准备

  // 获取资产来源列表
  const _getAssetsSourceList = () => {
    getAssetsSourceList().then((res) => {
      state.sourceList = [{ name: '全部' }].concat(res?.data || [])
    })
  }

  // 获取资产类型列表
  // 获取资产类型列表
  const _getAssetsTypeList = () => {
    state.typeList = [{ text: '全部' }].concat(
      [
        // {
        //   "code": "TABLE",
        //   "text": "表"
        // },

        {
          code: 'TABLE_OR_RESOURCE_DIRECTORY',
          text: '结构化资源目录',
        },
        {
          code: 'FILE',
          text: '非结构化资源目录',
        },
      ] || [],
    )
  }
  // 获取树形结构
  const getTreeList = () => {
    getAssetsClassifyTreeList({ keyword: '' }).then((res) => {
      res.data && (res.data.name = '资产目录')
      state.treeList = (res.data && [res.data]) || []
      state.treeList[0].id ??= 'root'
      nextTick(() => {
        state.expandedKeys = [state.treeList?.[0]?.id || 1]
        state.selectedKey = state.treeList?.[0]?.id || 1
      })
    })
  }
  const resetFn = (() => {
    const data = JSON.parse(JSON.stringify(state.filterSearch))
    return () => {
      state.filterSearch = JSON.parse(JSON.stringify(data))
      condition.value = {}
      onSearch()
    }
  })()
  _getAssetsSourceList()
  _getAssetsTypeList()
  getTreeList()
  onSearch()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      .tabs-tools {
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        .tabs {
          display: flex;
          gap: 8px;
          align-items: center;
          align-self: stretch;
          height: 48px;
          line-height: 48px;
          & + .tabs {
            border-bottom: 1px solid var(---, #dcdfe6);
          }
          > .tab {
            display: flex;
            gap: 4px;
            align-items: center;
            padding: 5px 12px;

            color: #a1a1aa;
            font-weight: 400;
            font-size: 14px;
            font-family: 'PingFang HK';
            font-style: normal;
            line-height: 22px;
            &:nth-child(n + 2) {
              color: #333;
              cursor: pointer;

              &:hover {
                color: #1e89ff;
              }
            }
            &.active {
              color: #1e89ff;
              background: #ecf3ff;
              border-radius: 2px;
            }
          }
        }
      }
      &-table {
        display: flex;
        flex: 1;
        gap: 10px;
        height: calc(100% - 106px);

        .cf-tree {
          .page-title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            width: 100%;
            min-height: 36px;
            margin-left: -16px;
            padding: 0 16px;
            color: #1d2129;
            font-weight: bolder;
            font-size: 16px;
            &[sub-label]::after {
              display: inline-block;
              margin-left: 12px;
              padding: 0px 4px;
              color: #1aa4ee;
              font-weight: 400;
              font-size: 12px;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              line-height: 22px;
              line-height: 20px;

              background: rgba(26, 164, 238, 0.08);
              border: 1px solid rgba(26, 164, 238, 0.4);
              border-radius: 2px;
              content: attr(sub-label);
            }
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .talble-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          width: 100%;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0px 0px 2px 2px;
          .header {
            display: flex;
            gap: 2px;
            align-items: flex-start;
            align-self: stretch;
            padding: 10px 8px 10px 16px;

            color: var(---, #909399);
            font-weight: 400;
            font-size: 14px;
            font-style: normal;
            line-height: 22px; /* 157.143% */
            > span {
              color: var(---, #1e89ff);
            }
          }
          .card-container {
            display: flex;
            flex: 1 0 0;
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
            align-self: stretch;
            padding: 0 16px;
            overflow-y: auto;
            .card-item {
              display: flex;
              flex-direction: column;
              gap: 16px;
              align-items: flex-start;
              width: 100%;
              padding: 16px;
              border: 1px solid var(---, #e5e6eb);
              border-radius: 4px;
              &-top {
                display: flex;
                gap: 8px;
                align-items: center;
                align-self: stretch;
                width: 100%;
                .card-title {
                  display: flex;
                  flex: 1 0 0;
                  flex-direction: column;
                  gap: 4px;
                  align-items: flex-start;
                  .title {
                    color: var(---, #1e89ff);
                    font-weight: 500;
                    font-size: 18px;
                    font-family: 'Source Han Sans CN';
                    font-style: normal;
                    line-height: 26px; /* 144.444% */
                    cursor: pointer;
                  }
                  .content {
                    display: flex;
                    gap: 4px;
                    align-items: center;
                    align-self: stretch;
                    padding: 0px 4px;

                    overflow: hidden;
                    color: var(----, #8091b7);
                    font-weight: 400;
                    font-size: 14px;
                    font-style: normal;
                    line-height: 22px;

                    text-overflow: ellipsis;
                  }
                }

                .bnts {
                  display: flex;
                  gap: 8px;
                  align-items: center;
                }
              }
              &-bottom {
                display: flex;
                flex-wrap: nowrap;
                gap: 100px;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                width: 100%;
                padding: 0 48px;
                .type {
                  display: flex;
                  flex: 1;
                  overflow: hidden;
                  color: var(----, #606266);
                  font-weight: 400;
                  font-size: 14px;
                  font-style: normal;
                  line-height: 22px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  &-title {
                    //width: 100px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-align: right;
                    text-overflow: ellipsis;
                  }
                  &-content {
                    width: calc(100% - 100px);
                    overflow: hidden;
                    color: var(----, rgba(0, 0, 0, 0.9));
                    font-weight: 500;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  &-tag {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    align-items: center;
                    width: calc(100% - 100px);
                    &-item {
                      display: flex;
                      align-items: center;
                      padding: 0px 4px;
                      border: 1px solid;
                      border-radius: 2px;
                    }
                  }
                }
              }

              .btn {
                display: flex;
                gap: 4px;
                align-items: center;
                padding: 4px 16px;
                color: #1e89ff;
                font-size: 14px;
                border: 1px solid var(---, #1e89ff);
                border-radius: 2px;

                &.active,
                &:hover {
                  color: #fff;
                  background: #1e89ff;
                }
              }
            }
          }
        }
      }
    }
    :deep(.el-loading-mask) {
      .el-loading-spinner {
        .circular {
          width: 48px !important;
          height: 48px !important;
        }
      }
    }
  }
</style>
