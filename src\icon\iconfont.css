@font-face {
  font-family: "iconfont"; /* Project id 3440248 */
  src: url('iconfont.woff2?t=1655280892027') format('woff2'),
       url('iconfont.woff?t=1655280892027') format('woff'),
       url('iconfont.ttf?t=1655280892027') format('truetype'),
       url('iconfont.svg?t=1655280892027#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-fenzuguanli:before {
  content: "\ec23";
}

.icon-gupiao-shouye:before {
  content: "\e680";
}

.icon-cubelifangti:before {
  content: "\e70a";
}

.icon-fangdajing1:before {
  content: "\e610";
}

.icon-order1:before {
  content: "\e701";
}

.icon-fuwudiqiu:before {
  content: "\ec0f";
}

.icon-jibenshuju:before {
  content: "\e7b8";
}

.icon-shenhe_gaizhang:before {
  content: "\e703";
}

.icon-shuju4:before {
  content: "\e6f9";
}

.icon-mima1:before {
  content: "\e8b2";
}

.icon-zhcc_yonghuming:before {
  content: "\e716";
}

.icon-tishi1:before {
  content: "\e8ba";
}

.icon-arrow-right-bold:before {
  content: "\e6ec";
}

.icon-select-bold:before {
  content: "\e6ed";
}

.icon-arrow-up-filling:before {
  content: "\e6ee";
}

.icon-arrow-down-filling:before {
  content: "\e6ef";
}

.icon-arrow-left-filling:before {
  content: "\e6f0";
}

.icon-arrow-right-filling:before {
  content: "\e6f1";
}

.icon-caps-unlock-filling:before {
  content: "\e6f2";
}

.icon-comment-filling:before {
  content: "\e6f3";
}

.icon-check-item-filling:before {
  content: "\e6f4";
}

.icon-clock-filling:before {
  content: "\e6f5";
}

.icon-delete-filling:before {
  content: "\e6f6";
}

.icon-decline-filling:before {
  content: "\e6f7";
}

.icon-dynamic-filling:before {
  content: "\e6f8";
}

.icon-favorite-filling:before {
  content: "\e6fa";
}

.icon-layout-filling:before {
  content: "\e6fb";
}

.icon-help-filling:before {
  content: "\e6fc";
}

.icon-history-filling:before {
  content: "\e6fd";
}

.icon-filter-filling:before {
  content: "\e6fe";
}

.icon-file-common-filling:before {
  content: "\e6ff";
}

.icon-news-filling:before {
  content: "\e700";
}

.icon-fullscreen-expand-filling:before {
  content: "\e702";
}

.icon-rise-filling:before {
  content: "\e704";
}

.icon-picture-filling:before {
  content: "\e705";
}

.icon-notification-filling:before {
  content: "\e706";
}

.icon-user-filling:before {
  content: "\e707";
}

.icon-switch-filling:before {
  content: "\e709";
}

.icon-task-filling:before {
  content: "\e70b";
}

.icon-success-filling:before {
  content: "\e70c";
}

.icon-warning-filling:before {
  content: "\e70d";
}

.icon-folder-filling:before {
  content: "\e70e";
}

.icon-top-filling:before {
  content: "\e713";
}

.icon-home-filling:before {
  content: "\e714";
}

.icon-sorting:before {
  content: "\e715";
}

.icon-3column:before {
  content: "\e663";
}

.icon-column-4:before {
  content: "\e664";
}

.icon-add:before {
  content: "\e665";
}

.icon-adjust:before {
  content: "\e666";
}

.icon-arrow-up-circle:before {
  content: "\e667";
}

.icon-arrow-right-circle:before {
  content: "\e668";
}

.icon-arrow-down:before {
  content: "\e669";
}

.icon-ashbin:before {
  content: "\e66a";
}

.icon-arrow-right:before {
  content: "\e66b";
}

.icon-eye-open:before {
  content: "\e66c";
}

.icon-bottom:before {
  content: "\e66d";
}

.icon-back:before {
  content: "\e66e";
}

.icon-bad:before {
  content: "\e66f";
}

.icon-arrow-double-left:before {
  content: "\e670";
}

.icon-arrow-left-circle:before {
  content: "\e671";
}

.icon-arrow-double-right:before {
  content: "\e672";
}

.icon-caps-lock:before {
  content: "\e673";
}

.icon-camera:before {
  content: "\e674";
}

.icon-chart-bar:before {
  content: "\e675";
}

.icon-attachment:before {
  content: "\e676";
}

.icon-code:before {
  content: "\e677";
}

.icon-close:before {
  content: "\e678";
}

.icon-check-item:before {
  content: "\e679";
}

.icon-calendar:before {
  content: "\e67a";
}

.icon-comment:before {
  content: "\e67b";
}

.icon-column-vertical:before {
  content: "\e67c";
}

.icon-column-horizontal:before {
  content: "\e67d";
}

.icon-complete:before {
  content: "\e67e";
}

.icon-chart-pie:before {
  content: "\e67f";
}

.icon-customer-service:before {
  content: "\e681";
}

.icon-delete:before {
  content: "\e682";
}

.icon-direction-down:before {
  content: "\e683";
}

.icon-copy:before {
  content: "\e684";
}

.icon-cut:before {
  content: "\e685";
}

.icon-data-view:before {
  content: "\e686";
}

.icon-direction-down-circle:before {
  content: "\e687";
}

.icon-direction-right:before {
  content: "\e688";
}

.icon-direction-up:before {
  content: "\e689";
}

.icon-discount:before {
  content: "\e68a";
}

.icon-direction-left:before {
  content: "\e68b";
}

.icon-download:before {
  content: "\e68c";
}

.icon-electronics:before {
  content: "\e68d";
}

.icon-drag:before {
  content: "\e68e";
}

.icon-elipsis:before {
  content: "\e68f";
}

.icon-export:before {
  content: "\e690";
}

.icon-explain:before {
  content: "\e691";
}

.icon-edit:before {
  content: "\e692";
}

.icon-eye-close:before {
  content: "\e693";
}

.icon-email:before {
  content: "\e694";
}

.icon-error:before {
  content: "\e695";
}

.icon-favorite:before {
  content: "\e696";
}

.icon-file-common:before {
  content: "\e697";
}

.icon-file-delete:before {
  content: "\e698";
}

.icon-file-add:before {
  content: "\e699";
}

.icon-film:before {
  content: "\e69a";
}

.icon-fabulous:before {
  content: "\e69b";
}

.icon-file:before {
  content: "\e69c";
}

.icon-folder-close:before {
  content: "\e69d";
}

.icon-filter:before {
  content: "\e69e";
}

.icon-good:before {
  content: "\e69f";
}

.icon-hide:before {
  content: "\e6a0";
}

.icon-home:before {
  content: "\e6a1";
}

.icon-history:before {
  content: "\e6a2";
}

.icon-file-open:before {
  content: "\e6a3";
}

.icon-forward:before {
  content: "\e6a4";
}

.icon-import:before {
  content: "\e6a5";
}

.icon-image-text:before {
  content: "\e6a6";
}

.icon-keyboard-26:before {
  content: "\e6a7";
}

.icon-keyboard-9:before {
  content: "\e6a8";
}

.icon-link:before {
  content: "\e6a9";
}

.icon-layout:before {
  content: "\e6aa";
}

.icon-fullscreen-shrink:before {
  content: "\e6ab";
}

.icon-layers:before {
  content: "\e6ac";
}

.icon-lock:before {
  content: "\e6ad";
}

.icon-fullscreen-expand:before {
  content: "\e6ae";
}

.icon-map:before {
  content: "\e6af";
}

.icon-meh:before {
  content: "\e6b0";
}

.icon-menu:before {
  content: "\e6b1";
}

.icon-loading:before {
  content: "\e6b2";
}

.icon-help:before {
  content: "\e6b3";
}

.icon-minus-circle:before {
  content: "\e6b4";
}

.icon-modular:before {
  content: "\e6b5";
}

.icon-notification:before {
  content: "\e6b6";
}

.icon-mic:before {
  content: "\e6b7";
}

.icon-more:before {
  content: "\e6b8";
}

.icon-pad:before {
  content: "\e6b9";
}

.icon-operation:before {
  content: "\e6ba";
}

.icon-play:before {
  content: "\e6bb";
}

.icon-print:before {
  content: "\e6bc";
}

.icon-mobile-phone:before {
  content: "\e6bd";
}

.icon-minus:before {
  content: "\e6be";
}

.icon-navigation:before {
  content: "\e6bf";
}

.icon-pdf:before {
  content: "\e6c0";
}

.icon-prompt:before {
  content: "\e6c1";
}

.icon-move:before {
  content: "\e6c2";
}

.icon-refresh:before {
  content: "\e6c3";
}

.icon-run-up:before {
  content: "\e6c4";
}

.icon-picture:before {
  content: "\e6c5";
}

.icon-run-in:before {
  content: "\e6c6";
}

.icon-pin:before {
  content: "\e6c7";
}

.icon-save:before {
  content: "\e6c8";
}

.icon-search:before {
  content: "\e6c9";
}

.icon-share:before {
  content: "\e6ca";
}

.icon-scanning:before {
  content: "\e6cb";
}

.icon-security:before {
  content: "\e6cc";
}

.icon-sign-out:before {
  content: "\e6cd";
}

.icon-select:before {
  content: "\e6ce";
}

.icon-stop:before {
  content: "\e6cf";
}

.icon-success:before {
  content: "\e6d0";
}

.icon-smile:before {
  content: "\e6d1";
}

.icon-switch:before {
  content: "\e6d2";
}

.icon-setting:before {
  content: "\e6d3";
}

.icon-survey:before {
  content: "\e6d4";
}

.icon-task:before {
  content: "\e6d5";
}

.icon-skip:before {
  content: "\e6d6";
}

.icon-text:before {
  content: "\e6d7";
}

.icon-time:before {
  content: "\e6d8";
}

.icon-telephone-out:before {
  content: "\e6d9";
}

.icon-toggle-left:before {
  content: "\e6da";
}

.icon-toggle-right:before {
  content: "\e6db";
}

.icon-telephone:before {
  content: "\e6dc";
}

.icon-top:before {
  content: "\e6dd";
}

.icon-unlock:before {
  content: "\e6de";
}

.icon-user:before {
  content: "\e6df";
}

.icon-upload:before {
  content: "\e6e0";
}

.icon-work:before {
  content: "\e6e1";
}

.icon-training:before {
  content: "\e6e2";
}

.icon-warning:before {
  content: "\e6e3";
}

.icon-zoom-in:before {
  content: "\e6e4";
}

.icon-zoom-out:before {
  content: "\e6e5";
}

.icon-add-bold:before {
  content: "\e6e6";
}

.icon-arrow-left-bold:before {
  content: "\e6e7";
}

.icon-arrow-up-bold:before {
  content: "\e6e8";
}

.icon-close-bold:before {
  content: "\e6e9";
}

.icon-arrow-down-bold:before {
  content: "\e6ea";
}

.icon-minus-bold:before {
  content: "\e6eb";
}

