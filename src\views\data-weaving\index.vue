<template>
  <div class="container nc-flex">
    <div class="left nc-p-10">
      <div class="list-title">
        <div>参考数据</div>
      </div>
      <div class="nc-m-t-20">
        <n-input
          class="data-collection-page-tree-ipt"
          v-model="state.keyword"
          placeholder="表中文名或英文名检索"
          suffix="search"
          @input="onSearchTable"
        />
        <div class="nc-m-t-8 table-list">
          <div
            class="table-item"
            v-for="item in state.tableList"
            :key="item.id"
            @mousedown="startDrag($event, item)"
            ><SvgIcon class="nc-m-r-4" icon="icon-title-table" title="表对象" />{{ item.zName }}-{{
              item.eName
            }}</div
          >
        </div>
      </div>
    </div>
    <div class="right nc-m-l-10">
      <div class="tools nc-flex nc-p-10">
        <n-button @click="onExecute">执行</n-button>
        <n-button @click="state.suspend = true">暂停</n-button>
        <template v-if="state.activeTab === 1">
          <n-button>保存</n-button>
          <n-button @click="state.addTableShow = true">添加表</n-button>
          <n-button @click="onUndo">撤销</n-button>
          <n-button @click="onRedo">重做</n-button>
          <n-button @click="onAlign('top')">上对齐</n-button>
          <n-button @click="onAlign('bottom')">下对齐</n-button>
          <n-button @click="onAlign('left')">左对齐</n-button>
          <n-button @click="onAlign('right')">右对齐</n-button>
        </template>
      </div>
      <div class="graph-tabs">
        <n-tabs v-model="state.activeTab" @active-tab-change="tabChange" type="options">
          <n-tab :id="item.id" :key="item.id" :title="item.name" v-for="item in state.tabs" />
        </n-tabs>
        <div class="summary" v-show="state.activeTab === 2">
          <div class="summary-item">
            <SvgIcon icon="have-border-ok" />
            <span>成功：{{ state.summary.success }}</span>
          </div>
          <div class="summary-item">
            <SvgIcon icon="icon-close-solid" />
            <span>失败：{{ state.summary.failed }}</span>
          </div>
          <div class="summary-item">
            <SvgIcon icon="icon_three_rotate" />
            <span>运行中：{{ state.summary.running }}</span>
          </div>
          <div class="summary-item">
            <SvgIcon icon="history-icon" />
            <span>待运行：{{ state.summary.waiting }}</span>
          </div>
        </div>
      </div>
      <div class="graph-container" id="graphBox">
        <div id="antv"></div>
        <div class="minimapBox">
          <h3 @click="state.showMinMap = !state.showMinMap">
            <SvgIcon icon="icon-arrow-up" v-if="!state.showMinMap" />
            <SvgIcon icon="icon-arrow-drop" v-else />

            缩略图
          </h3>
          <div
            id="minimapContainer"
            :class="{ showMinMap: state.showMinMap, hiddenMinMap: !state.showMinMap }"
          ></div>
        </div>
      </div>
    </div>
    <ul id="nodeContextMenu" v-show="state.nodeContextMenuShow" class="graph-contextMenu">
      <li @click="onNodeInfoShow('1')">数据视图</li>
      <li @click="onNodeInfoShow('2')">数据加工</li>
      <li @click="onNodeInfoShow('3')">数据预览</li>
      <li @click="onNodeInfoShow('4')">基本信息</li>
      <li @click="onNodeDelete">删除</li>
    </ul>
    <addTable v-model="state.addTableShow" @sure="onAddTable" />
    <nodeInfo v-model="state.nodeInfoShow" :activeId="state.activeId" @sure="onEditTable" />
    <tableData v-model="state.tableDataShow" />
  </div>
</template>
<script setup>
  import Graph from './graph'
  import addTable from './components/add-table.vue'
  import nodeInfo from './components/node-info.vue'
  import tableData from './components/table-data.vue'

  const state = reactive({
    keyword: '',
    activeTab: 1,
    tabs: [
      { id: 1, name: '编织视图', json: [] },
      { id: 2, name: '运行视图', json: [] },
    ],
    summary: { success: 0, failed: 0, running: 0, waiting: 0 },
    tableList: [
      {
        id: '1',
        zName: '用户信息表',
        eName: 'userinfo',
        cols: [
          { col: 'userid', name: '工号' },
          { col: 'username', name: '姓名' },
        ],
      },
      {
        id: '2',
        zName: '订单信息表',
        eName: 'orderinfo',
        cols: [
          { col: 'orderid', name: '订单编号' },
          { col: 'ordervalue', name: '订单金额' },
          { col: 'orderyear', name: '签订时间' },
          { col: 'userid', name: '工号' },
          { col: 'username', name: '销售人员' },
        ],
      },
    ],
    graph: null,
    showMinMap: true,
    addTableShow: false,
    nodeContextMenuShow: false,
    curNode: null,
    nodeInfoShow: false,
    activeId: '',
    nodeIds: [],
    time: 1,
    suspend: false, //是否暂停
    tableDataShow: false,
  })
  function onNodeInfoShow(id) {
    state.nodeInfoShow = true
    state.activeId = id
  }
  function onSearchTable() {}
  function onEditTable(data) {}

  function tabChange() {
    state.graph.cleanSelection()
    state.nodeContextMenuShow = false
    state.tableDataShow = false
    if (state.activeTab === 1) {
      state.tabs[0].json.forEach((item) => {
        delete item?.data?.status
      })
      state.graph.fromJSON(state.tabs[0].json)
    } else {
      const json = state.graph?.toJSON()?.cells || []
      state.tabs[0].json = json
      const statusJson = []
      json.forEach((item) => {
        if (item.shape === 'combining-rect') {
          statusJson.push({
            id: item.id,
            width: 300,
            height: 32,
            shape: 'status-node',
            position: item.position,
            data: {
              ...item.data,
              status: 'waiting',
            },
          })
        } else {
          if (
            !statusJson.find(
              (s) =>
                s.shape === 'edge' &&
                s.source?.cell === item.source?.cell &&
                s.target?.cell === item.target?.cell,
            )
          ) {
            statusJson.push({
              id: item.id,
              shape: 'edge',
              source: {
                cell: item.source.cell,
                port: 'right',
              },
              target: {
                cell: item.target.cell,
                port: 'left',
              },
            })
          }
        }
      })
      state.graph.fromJSON(statusJson)
    }
  }
  function setNodeIds() {
    state.nodeIds = []
    const rootNodes = state.graph.getRootNodes()
    rootNodes.forEach((item) => {
      const nodes = state.graph.getSuccessors(item)
      const ids = [item.id].concat(nodes.map((n) => n.id))
      state.nodeIds = [...state.nodeIds, ...ids]
    })
  }
  // 执行
  function onExecute() {
    state.suspend = false
    if (state.activeTab === 1) {
      state.activeTab = 2
      tabChange()
    }
    state.time = 1
    setNodeIds()
    setNodeStatus(true)
  }
  // 设置节点状态
  function setNodeStatus(isExecute = false) {
    if (state.suspend || state.activeTab === 1) {
      return false
    }
    const nodes = state.graph?.toJSON()?.cells.filter((c) => c.shape === 'status-node')
    const isFinish = nodes.every((c) => ['success', 'failed'].includes(c.data.status))
    if (isFinish && isExecute) {
      nodes.forEach((c) => {
        const node = state.graph.getCellById(c.id)
        node.setData({
          status: 'waiting',
        })
      })
    }
    const status = ['waiting', 'running', 'success', 'failed']
    state.nodeIds.forEach((id, index) => {
      if (index < state.time) {
        const node = state.graph.getCellById(id)
        const index = status.findIndex((s) => s === node.data.status)
        if (index < 2) {
          node.setData({
            status: status[index + 1],
          })
        }
      }
    })
    summaryCount()
    setTimeout(() => {
      state.time++
      setNodeStatus()
    }, 3000)
  }
  function summaryCount() {
    const json = state.graph?.toJSON()?.cells || []
    state.summary.success = json.filter(
      (item) => item.shape === 'status-node' && item.data.status === 'success',
    ).length
    state.summary.running = json.filter(
      (item) => item.shape === 'status-node' && item.data.status === 'running',
    ).length
    state.summary.waiting = json.filter(
      (item) => item.shape === 'status-node' && item.data.status === 'waiting',
    ).length
    state.summary.failed = json.filter(
      (item) => item.shape === 'status-node' && item.data.status === 'failed',
    ).length
  }
  function onNodeDelete() {
    state.graph.removeNode(state.curNode)
    state.nodeContextMenuShow = false
  }
  function onAddTable() {
    if (state.tableList.length === 2) {
      state.tableList.push({
        id: 3,
        zName: '人员销售统计',
        eName: 'sales_statistics',
        cols: [
          { col: 'userid', name: '工号' },
          { col: 'username', name: '姓名' },
          { col: 'salescount', name: '销售金额' },
          { col: 'year', name: '统计时间' },
        ],
      })
    } else if (state.tableList.length === 3) {
      state.tableList.push({
        id: 4,
        zName: '人员部门信息表',
        eName: 'userparty',
        cols: [
          { col: 'userid', name: '工号' },
          { col: 'username', name: '姓名' },
          { col: 'tparty', name: '所属部门' },
          { col: 'tarea', name: '所属区域' },
        ],
      })
    } else if (state.tableList.length === 4) {
      state.tableList.push({
        id: 5,
        zName: '部门销售统计',
        eName: 'partysales',
        cols: [
          { col: 'tparty', name: '部门' },
          { col: 'tarea', name: '区域' },
          { col: 'salescount', name: '销售金额' },
          { col: 'year', name: '统计时间' },
        ],
      })
    } else if (state.tableList.length === 5) {
      state.tableList.push({
        id: 6,
        zName: '区域销售统计',
        eName: 'regionalsales',
        cols: [
          { col: 'tarea', name: '区域' },
          { col: 'salescount', name: '销售金额' },
          { col: 'year', name: '统计时间' },
        ],
      })
    }
  }
  function startDrag(e, item) {
    if (state.activeTab === 2) {
      return false
    }
    const { dnd } = Graph
    const ports = item.cols.map((col, index) => {
      return {
        id: item.id + '-' + col.col,
        group: 'list',
        zIndex: -1,
        attrs: {
          sortNum: { text: index + 1 },
          portDisc: {
            text: `${col.name} -${col.col}`,
            textWrap: {
              text: `${col.name} -${col.col}`,
              width: 280, // 宽度减少 10px
              height: '100%', // 高度为参照元素高度的一半
              ellipsis: true, // 文本超出显示范围时，自动添加省略号
              breakWord: true, // 是否截断单词
            },
          },
          portRect: {
            fill: '#8091B7',
          },
        },
      }
    })
    const node = state.graph.createNode({
      shape: 'combining-rect',
      width: 300,
      height: 32,
      id: item.id,
      data: {
        name: `${item.zName}-${item.eName}`,
        icon: 'icon-business-obj',
      },
      position: {
        x: 16,
        y: 0,
      },
      ports,
    })
    dnd.start(node, e)
  }
  const getContainerSize = () => {
    return {
      width: document.getElementById('graphBox').offsetWidth,
      height: document.getElementById('graphBox').offsetHeight,
    }
  }
  function resizeFn() {
    const { width, height } = getContainerSize()
    state.graph.resize(width, height)
  }
  function initGraph() {
    if (state.graph) {
      Graph.destroy()
    }
    const { graph } = Graph.init('antv')
    state.graph = graph
    resizeFn()
    window.addEventListener('resize', resizeFn)
    state.graph.on('blank:click', ({ e, x, y }) => {
      state.nodeContextMenuShow = false
      state.tableDataShow = false
    })
    state.graph.on('node:click', ({ e, x, y, node }) => {
      console.log('node:click', node.toJSON())
      if (node.toJSON().shape === 'status-node') {
        state.tableDataShow = true
      } else {
        state.nodeContextMenuShow = true
        state.curNode = node
        const { clientX, clientY } = e
        const { x: nodex, y: nodey } = node.getPosition()
        const { width: w, height: h } = node.size()
        const target = document.getElementById('nodeContextMenu')
        const height =
          window.innerHeight - clientY - (y - nodey) + h < 200
            ? clientY - (y - nodey) + h - 150
            : clientY - (y - nodey) + h - 50

        target.style.setProperty('left', `${clientX - (x - nodex) + w + 10}px`)
        target.style.setProperty('top', `${height}px`)
      }
    })
    state.graph.on('node:change:data', ({ node }) => {
      const edges = graph.getIncomingEdges(node)
      const { status } = node.getData()
      edges?.forEach((edge) => {
        if (status === 'running') {
          edge.attr('line/strokeDasharray', 5)
          edge.attr('line/style/animation', 'running-line 30s infinite linear')
        } else {
          edge.attr('line/strokeDasharray', '')
          edge.attr('line/style/animation', '')
        }
      })
    })
  }
  // 重做 redo
  function onRedo() {
    const { history } = state.graph
    history.redo()
  }
  // 撤回 Undo
  function onUndo() {
    const { history } = state.graph
    history.undo()
  }
  // 对齐
  function onAlign(type) {
    const { graph, selectdNode } = Graph
    const nodePosition = {}
    if (type === 'center') return graph.centerContent()
    selectdNode?.forEach((item, index) => {
      if (index === 0) {
        nodePosition.minX = item.getPosition().x
        nodePosition.maxX = item.getPosition().x
        nodePosition.minY = item.getPosition().y
        nodePosition.maxY = item.getPosition().y
      } else {
        let { x, y } = item.getPosition()
        if (x > nodePosition.maxX) nodePosition.maxX = x
        else if (x < nodePosition.minX) nodePosition.minX = x
        if (y > nodePosition.maxY) nodePosition.maxY = y
        else if (y < nodePosition.minY) nodePosition.minY = y
      }
    })
    switch (type) {
      case 'left':
        selectdNode?.forEach((item) => {
          item.setPosition(nodePosition.minX, item.getPosition().y)
        })
        break
      case 'right':
        selectdNode?.forEach((item) => {
          item.setPosition(nodePosition.maxX, item.getPosition().y)
        })
        break
      case 'top':
        selectdNode?.forEach((item) => {
          item.setPosition(item.getPosition().x, nodePosition.minY)
        })
        break
      case 'bottom':
        selectdNode?.forEach((item) => {
          item.setPosition(item.getPosition().x, nodePosition.maxY)
        })
        break
    }
  }
  onMounted(() => {
    nextTick(() => {
      initGraph()
    })
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    height: 100vh;
  }
  .left {
    width: 280px;
    background: #fff;
    .list-title {
      width: 100%;
      color: #1d2129;
      font-weight: 500;
      font-size: 16px;
      line-height: 18px;
      text-indent: 8px;
      border-left: 4px solid $cf-color-primary;
    }
    .table-list {
      height: calc(100vh - 200px);
      overflow: auto;
      .table-item {
        display: flex;
        align-items: center;
        height: 40px;
        margin-bottom: 8px;
        padding: 0 8px;
        line-height: 40px;
        border: 1px solid var(---, #bfd9ff);
        border-radius: 4px;
        cursor: pointer;
        &:hover {
          color: #447dfd;
          background: #f0f1f5;
        }
      }
    }
  }
  .right {
    flex: 1;
    width: calc(100% - 290px);
    height: 100%;
    background: #fff;
    .tools {
      border-bottom: 1px solid #e4e7ed;
    }
    .graph-tabs {
      position: fixed;
      top: 80px;
      left: 315px;
      z-index: 2;
      background: #ffffff;
    }
    .graph-container {
      position: relative;
      width: 100%;
      height: calc(100% - 60px);
      #antv {
        width: 100%;
        height: 100%;
      }
      .minimapBox {
        position: fixed;
        right: 18px;
        bottom: 18px;
        h3 {
          height: 28px;
          margin: 0;
          padding: 0 8px;
          color: #4a4a4a;
          font-weight: normal;
          font-size: 12px;
          line-height: 28px;
          text-align: center;
          background: #e9ecf1;
          cursor: pointer;
        }
        #minimapContainer {
          background: #fff;
        }
        .showMinMap {
          display: block;
        }
        .hiddenMinMap {
          display: none;
        }
      }
    }
  }

  .graph-contextMenu {
    position: fixed;
    max-height: 240px;
    padding: 12px;
    overflow: auto;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
    li {
      padding: 4px 16px;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        color: #447dfd;
        background: #f0f1f5;
      }
    }
  }
  .summary {
    position: fixed;
    top: 80px;
    right: 100px;
    display: flex;
    .summary-item {
      display: flex;
      align-items: center;
      padding: 8px;
      background: #f0f1f5;
      border-radius: 4px;
      svg {
        margin-right: 4px;
      }
      + .summary-item {
        margin-left: 8px;
      }
    }
  }
  :deep(.my-selecting) {
    .x6-widget-selection-inner,
    .x6-widget-selection-box {
      border: 2px dashed #447dfd !important;
    }
  }
</style>
