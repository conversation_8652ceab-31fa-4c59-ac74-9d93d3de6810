<template>
  <div class="page-row">
    <div>
      <slot name="left"></slot>
    </div>
    <div>
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="less" scoped>
  .page-row {
    padding: 15px 16px;
    display: flex;
    justify-content: space-between;

    > div {
      display: flex;
      align-items: center;
      > :deep(.yy-icon) {
        margin-right: 8px;
        color: #8091b7;
      }
    }
  }
</style>
