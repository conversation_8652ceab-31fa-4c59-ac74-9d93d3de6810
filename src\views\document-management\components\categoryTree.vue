<template>
  <div class="class-list nc-m-t-10">
    <n-input
      class="data-collection-page-tree-ipt"
      v-model="state.treeSearchText"
      placeholder="请输入"
      suffix="search"
      @input="searchClassFn"
    />
    <div class="nc-m-t-8 class-tree">
      <n-tree :data="state.treeData" ref="treeref" @node-click="clickFn">
        <template #content="{ nodeData }">
          <SvgIcon class="tree-icon" v-if="nodeData.children?.length" icon="open-file" />
          <SvgIcon class="tree-icon" v-else icon="icon-file-tree" />
          <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
        </template>
        <template #icon="{ nodeData, toggleNode }">
          <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
          <span
            v-else
            @click="
              (event) => {
                event.stopPropagation()
                toggleNode(nodeData)
              }
            "
          >
            <SvgIcon
              v-if="nodeData.expanded"
              class="nancalui-tree-switch"
              icon="tree-contract-new"
            />
            <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
          </span>
        </template>
      </n-tree>
    </div>
  </div>
</template>
<script setup>
  import api from '@/api/index'
  const state = reactive({
    treeSearchText: '',
    treeData: [],
  })
  const emit = defineEmits(['node-click'])
  function clickFn(node) {
    emit('node-click', node.id)
  }
  const treeref = ref(null)
  async function searchClassFn() {
    treeref.value.treeFactory.searchTree(state.treeSearchText, {
      isFilter: true,
      matchKey: 'name',
    })
  }
  async function getClassifyTreeList() {
    const res = await api.documentManage.getClassifyTreeList()
    state.treeData = [
      {
        id: 'all',
        name: '全部',
        selected: true,
        expanded: true,
        children: res.data,
      },
    ]
  }
  onMounted(async () => {
    await getClassifyTreeList()
  })
</script>
<style lang="scss" scoped>
  .class-list {
    padding: 12px;
    .class-tree {
      height: calc(100vh - 280px);
      overflow: auto;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
