<template>
  <section class="container">
    <section class="container-box">
      <section class="cf-tools">
        <div class="row">
          <div class="col text-label">
            全局质量：
            <n-select
              v-model="state.formInline.tableId"
              placeholder="全局质量"
              allow-clear
              filter
              :options="state.monitorList"
              @value-change="monitorChangeFn"
            />
          </div>
          <div class="col text-label">
            评分体系：
            <n-select
              v-model="state.formInline.ratingSystem"
              placeholder="评分体系"
              :options="state.scoreOptions"
              @value-change="ratingFn"
            />
          </div>
          <div class="col text-label">
            时间选择：
            <n-range-date-picker-pro
              v-model="state.formInline.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              allow-clear
              @confirmEvent="getDataFn"
            />
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="getDataFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <div class="content-box-top">
          <div class="add-box-top">
            <div class="top-left">
              <n-button
                variant="solid"
                color="primary"
                @click.prevent="exportFn"
                v-if="
                  state.buttonAuthList.includes(
                    'governanceManage_qualityManage_qualityReport_refresh_edit',
                  )
                "
              >
                <SvgIcon icon="new-definition-export" class="icon" title="导出" />导出
              </n-button>
              <n-button
                color="primary"
                @click.prevent="getDataFn"
                v-if="
                  state.buttonAuthList.includes(
                    'governanceManage_qualityManage_qualityReport_export_edit',
                  )
                "
              >
                <SvgIcon icon="icon-new-refresh" class="icon" title="添加" />刷新
              </n-button>
              <n-button
                color="primary"
                :disabled="!state.formInline.tableId"
                @click.prevent="downloadFile"
              >
                <SvgIcon icon="icon-download" class="icon" title="下载" />下载质量报告
              </n-button>
            </div>
            <div class="top-right1"> </div>
          </div>
        </div>

        <div class="content-box-row">
          <UseFullscreen class="col" v-slot="{ toggle, isFullscreen }">
            <div :class="{ 'is-fullscreen': isFullscreen }">
              <div class="title">
                质量评分
                <n-tooltip
                  class="tree-btn"
                  content="展示规则评分所属评分等级下的规则数量及占比；规则评分计算方式：规则评分*（评分体系/100）。"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon class="illustrate" icon="icon-illustrate" />
                </n-tooltip>
                <span>({{ state.formInline.startTime }} — {{ state.formInline.endTime }})</span>
                <!-- <div class="fullscreen icons">
                  <svgIcon
                    icon="icon-fullscreen"
                    @click="
                      () => {
                        toggle()
                      }
                    "
                  />
                </div> -->
              </div>
              <div v-loading="state.qualityLoading" class="canvas" id="qualityScore"> </div>
              <div class="label-box">
                <div v-for="(item, index) in state.pieLabel" class="label" :key="index"
                  ><div class="circle" :style="'background-color:' + state.colorList[index]"></div
                  >{{ item.name }}({{ item.min * state.multiple }}-{{
                    item.max * state.multiple
                  }}分)</div
                >
              </div>
            </div>
          </UseFullscreen>
          <UseFullscreen class="col" v-slot="{ toggle, isFullscreen }">
            <div :class="{ 'is-fullscreen': isFullscreen }">
              <div class="title">
                六性评分
                <n-tooltip
                  class="tree-btn"
                  content="展示六性维度下的规则评分；六性评分计算方式：∑(某一六性维度关联的所有规则评分*规则权重)/∑规则权重*（评分体系/100）。"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon class="illustrate" icon="icon-illustrate" />
                </n-tooltip>
                <span>({{ state.formInline.startTime }} — {{ state.formInline.endTime }})</span>
                <!-- <div class="fullscreen icons">
                  <svgIcon
                    icon="icon-fullscreen"
                    @click="
                      () => {
                        toggle()
                      }
                    "
                  /> </div
              > -->
              </div>
              <div v-loading="state.sixLoading" class="canvas" id="sixScore"> </div>
              <div class="label-box">
                <div class="label"><div class="rect"></div>评分</div>
              </div>
            </div>
          </UseFullscreen>
        </div>
        <div class="content-box-row">
          <UseFullscreen class="col" v-slot="{ toggle, isFullscreen }">
            <div :class="{ 'is-fullscreen': isFullscreen }">
              <div class="title"
                >质量评分历史趋势
                <n-tooltip
                  class="tree-btn"
                  content="展示某一时间段内每天的质量平均分；质量平均分计算方式：∑(报告范围内所有规则评分*规则权重)/∑规则权重*（评分体系/100）。"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon class="illustrate" icon="icon-illustrate" />
                </n-tooltip>
                <span>({{ state.formInline.startTime }} — {{ state.formInline.endTime }})</span>
                <!-- <div class="fullscreen icons">
                  <svgIcon
                    icon="icon-fullscreen"
                    @click="
                      () => {
                        toggle()
                      }
                    "
                  /> </div
              > -->
              </div>
              <div
                v-loading="state.historyLoading"
                :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
                id="historyTrend"
              ></div>
            </div>
          </UseFullscreen>
          <UseFullscreen class="col" v-slot="{ toggle, isFullscreen }">
            <div :class="{ 'is-fullscreen': isFullscreen }">
              <div class="title"
                >质量报告
                <n-tooltip
                  class="tree-btn"
                  content="展示表评分及表关联的规则评分；表评分计算方式：∑(表关联的所有规则评分*规则权重)/∑规则权重*（评分体系/100）；规则评分计算方式：规则评分*（评分体系/100）。"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon class="illustrate" icon="icon-illustrate" />
                </n-tooltip>
                <span>({{ state.formInline.startTime }} — {{ state.formInline.endTime }})</span>
                <!-- <div class="fullscreen icons">
                  <svgIcon
                    icon="icon-fullscreen"
                    @click="
                      () => {
                        toggle()
                      }
                    "
                  /> </div
              > -->
              </div>
              <div
                v-loading="state.tableLoading"
                :class="['canvas', 'table', 'scroll-bar-style', state.isLzos ? 'isLzos' : '']"
              >
                <n-table
                  :key="state.tableData"
                  ref="tableRef"
                  :data="state.tableData"
                  row-key="name"
                  :expand-row-keys="state.expandKey"
                  @expand-change="expandChange"
                >
                  <n-column resizeable type="expand">
                    <template #default="rowData">
                      <div class="table-expand-child">
                        <div class="table-expand-child-row header">
                          <div class="table-expand-child-row-col first"></div>
                          <div class="table-expand-child-row-col">规则名称</div>
                          <div class="table-expand-child-row-col">规则评分</div>
                        </div>
                        <template v-if="rowData.row.child">
                          <div v-for="item in rowData.row.child" class="table-expand-child-row">
                            <div class="table-expand-child-row-col first"></div>
                            <div class="table-expand-child-row-col" :title="item.ruleName">{{
                              item.ruleName
                            }}</div>
                            <div class="table-expand-child-row-col">{{ item.ruleScore }}</div>
                          </div>
                        </template>
                      </div>
                    </template>
                  </n-column>
                  <n-column resizeable field="name" header="表名（监控对象）" />
                  <n-column resizeable field="score" header="表评分" />
                </n-table>
              </div>
            </div>
          </UseFullscreen>
        </div>
      </section>
    </section>
    <ExportDialog ref="exportDialogRef" :formInline="state.formInline" :multiple="state.multiple" />
  </section>
</template>
<script setup>
  import * as echarts from 'echarts'
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { formartTimeDate } from '@/utils/index'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  // 导出弹框组件
  import ExportDialog from './components/exportDialog.vue'
  import { UseFullscreen } from '@vueuse/components'

  const store = useStore()
  const router = useRouter()
  const exportDialogRef = ref(null)
  // 导出事件
  const exportFn = () => {
    exportDialogRef.value.showDialog()
  }

  // 下载
  const downloadFile = () => {
    api.dataQuality.downWord({ tableId: state.formInline.tableId }).then((res) => {
      if (res.type === 'application/json') {
        // 说明是普通对象数据，读取信息
        const fileReader = new FileReader()
        fileReader.readAsText(res)
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result)
          // 后台信息
          ElNotification({
            title: '提示',
            message: jsonData.message,
            type: 'error',
          })
        }
      } else {
        try {
          // 下载文件
          const blob = new Blob([res], {
            type: 'text/plain',
          })
          const link = document.createElement('a')
          let fileName = state.monitorName + '的质量报告.docx'
          link.download = fileName
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        } catch (e) {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        }
      }
    })
  }

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    buttonAuthList: [],
    formInline: {
      tableId: null,
      ratingSystem: 'PRECENTAGE_SCORE',
      time: '',
      startTime: null,
      endTime: null,
    },
    multiple: 20,
    scoreOptions: [
      { name: '五分制', value: 'FIVE_POINT_SCORE', multiple: 1 },
      { name: '十分制', value: 'TENTH_SCORE', multiple: 2 },
      { name: '百分制', value: 'PRECENTAGE_SCORE', multiple: 20 },
    ],
    monitorList: [],
    colorList: ['#447DFD', '#6E9EFF', '#04C495', '#FF7D00', '#F63838', '#2F5CD6'],
    pieLabel: [
      { name: '优秀', min: 4, max: 5 },
      { name: '良好', min: 3, max: 4 },
      { name: '中等', min: 2, max: 3 },
      { name: '及格', min: 1, max: 2 },
      { name: '不及格', min: 0, max: 1 },
    ],
    sixLabel: [
      { name: '准确性', value: 'ACCURACY' },
      { name: '有效性', value: 'VALIDITY' },
      { name: '完整性', value: 'COMPLETENESS' },
      { name: '唯一性', value: 'UNIQUENESS' },
      { name: '一致性', value: 'CONSISTENCY' },
      { name: '及时性', value: 'TIMELINESS' },
    ],
    tableData: [],
    expandKey: [],
    timerFlag: null,
    qualityChart: null,
    qualityLoading: false,
    sixChart: null,
    sixLoading: false,
    historyChart: null,
    historyLoading: false,
    tableLoading: false,
    monitorName: '',
  })
  // 全局质量表切换
  const monitorChangeFn = (item) => {
    if (item) {
      state.monitorName = item.name
    } else {
      state.monitorName = ''
    }
    getDataFn()
  }
  // 评分切换
  const ratingFn = (item) => {
    state.multiple = item.multiple
    getDataFn()
  }
  // 切换
  const getDataFn = () => {
    state.expandKey = []
    if (state.formInline.time && state.formInline.time[0]) {
      if (Object.prototype.toString.call(state.formInline.time[0]) === '[object Date]') {
        state.formInline.startTime = formartTimeDate(state.formInline.time[0], '-', true)
        state.formInline.endTime = formartTimeDate(state.formInline.time[1], '-', true)
      } else {
        state.formInline.startTime = state.formInline.time[0]
        state.formInline.endTime = state.formInline.time[1]
      }
    } else {
      state.formInline.startTime = null
      state.formInline.endTime = null
    }
    if (state.timerFlag) {
      clearTimeout(state.timerFlag)
      state.timerFlag = null
    }
    state.qualityChart?.dispose()
    state.sixChart?.dispose()
    state.historyChart?.dispose()
    state.timerFlag = setTimeout(() => {
      getMapData()
    }, 300)
  }
  // 获取画布数据
  const getMapData = () => {
    // 获取质量评分
    state.qualityLoading = true
    api.dataQuality
      .qualityReportScope({
        tableId: state.formInline.tableId,
        ratingSystem: state.formInline.ratingSystem,
        startTime: state.formInline.startTime,
        endTime: state.formInline.endTime,
      })
      .then((res) => {
        state.qualityLoading = false
        let { success, data } = res
        if (success) {
          data.forEach((val) => {
            val.value = val.num
          })
          qualityFn(data)
        }
      })

    // 获取六性评分
    state.sixLoading = true
    api.dataQuality
      .qualityReportSixScope({
        tableId: state.formInline.tableId,
        ratingSystem: state.formInline.ratingSystem,
        startTime: state.formInline.startTime,
        endTime: state.formInline.endTime,
      })
      .then((res) => {
        state.sixLoading = false
        let { success, data } = res
        if (success) {
          let arr = []
          data.forEach((val) => {
            val.name = state.sixLabel.filter((item) => item.value === val.name)[0].name
            val.value = val.socre
            arr.push(val.socre || 0)
          })
          sixFn(arr)
        }
      })

    // 获取历史评分趋势
    state.historyLoading = true
    api.dataQuality
      .qualityReportHistoryScope({
        tableId: state.formInline.tableId,
        ratingSystem: state.formInline.ratingSystem,
        startTime: state.formInline.startTime,
        endTime: state.formInline.endTime,
      })
      .then((res) => {
        state.historyLoading = false
        let { success, data } = res
        if (success) {
          let xData = []
          let yData = []
          state.formInline.startTime = data[0].date + ' 00:00:00'
          state.formInline.endTime = data[data.length - 1].date + ' 23:59:59'
          state.formInline.time = [data[0].date, data[data.length - 1].date]
          data.forEach((val) => {
            xData.push(val.date.slice(5, 10))
            yData.push(val.score || 0)
          })
          trendFn(xData, yData)
        }
      })

    // 获取质量报告表
    state.tableLoading = true
    api.dataQuality
      .qualityReportTable({
        tableId: state.formInline.tableId,
        ratingSystem: state.formInline.ratingSystem,
        startTime: state.formInline.startTime,
        endTime: state.formInline.endTime,
      })
      .then((res) => {
        state.tableLoading = false
        let { success, data } = res
        if (success) {
          data.forEach((val) => {
            val.expand = false
          })
          state.tableData = data
        }
      })
  }
  // 质量报告展开
  const expandChange = (item) => {
    if (!item.expand) {
      state.expandKey.push(item.name)
    } else {
      state.expandKey = state.expandKey.filter((val) => val !== item.name)
    }
    item.expand = !item.expand
    if (!item.child) {
      api.dataQuality
        .qualityReportTableRule({
          tableId: item.tableId,
          ratingSystem: state.formInline.ratingSystem,
          startTime: state.formInline.startTime,
          endTime: state.formInline.endTime,
        })
        .then((res) => {
          let { success, data } = res
          if (success) {
            state.tableData.forEach((val) => {
              if (item.name === val.name) {
                val.child = [...data]
              }
            })
          }
        })
    }
  }
  // 重置
  const resetFn = () => {
    state.formInline = {
      tableId: null,
      ratingSystem: null,
      time: '',
      startTime: null,
      endTime: null,
    }
    getDataFn()
  }

  // 质量评分
  const qualityFn = (data) => {
    let chartDom = document.getElementById('qualityScore')
    state.qualityChart = echarts.init(chartDom)
    let option = {
      color: state.colorList,
      tooltip: {
        trigger: 'item',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',
        className: 'quality-tooltip',
        padding: 0,
        formatter: function (item) {
          return `<div class="quality-tooltip-box">
<div class="quality-tooltip-box-title">质量评分占比</div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">${item.name}</div><div class="num">${item.value}条</div></div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">占比</div><div class="num">${item.percent}%</div></div>
</div>`
        },
      },
      series: [
        {
          name: '质量评分',
          type: 'pie',
          radius: ['30%', '50%'],
          center: ['50%', '42%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          padAngle: 5,
          label: {
            orient: 'vertical',
            left: 'left',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 40,
              formatter: (item) => {
                return ['{a|' + item.name + '}', '{b|' + item.value + '}'].join('\n')
              },
              rich: {
                a: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  height: 30,
                },
              },
            },
          },
          data: data,
        },
      ],
    }
    state.qualityChart.setOption(option)
  }
  // 六性评分
  const sixFn = (data) => {
    let chartDom = document.getElementById('sixScore')
    state.sixChart = echarts.init(chartDom)
    let option = {
      color: state.colorList,
      tooltip: {
        trigger: 'item',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',

        className: 'quality-tooltip',
        padding: 0,
        formatter: function (item) {
          return `<div class="quality-tooltip-box">
<div class="quality-tooltip-box-title">六性评分</div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">准确性</div><div class="num">${data[0]}分</div></div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">有效性</div><div class="num">${data[1]}分</div></div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">完整性</div><div class="num">${data[2]}分</div></div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">唯一性</div><div class="num">${data[3]}分</div></div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">一致性</div><div class="num">${data[4]}分</div></div>
<div class="quality-tooltip-box-item"><div class="circle"></div><div class="text">及时性</div><div class="num">${data[5]}分</div></div>
</div>`
        },
      },
      radar: {
        shape: 'polygon', //雷达图绘制类型，支持 'polygon'(多边形) 和 'circle'(圆)。[ default: 'polygon' ]
        splitNumber: 4, // 雷达图圈数设置
        name: {
          textStyle: {
            color: 'rgba(0, 0, 0, 0.4)',
          },
        },
        center: ['50%', '50%'], // 图表的位置
        radius: '50%',
        // 设置雷达图中间射线的颜色
        axisLine: {
          //坐标轴轴线相关设置
          lineStyle: {
            color: '#E1E1E1',
          },
        },
        splitLine: {
          // 坐标轴在 grid 区域中的分隔线。
          show: true,
          lineStyle: {
            width: 1,
            color: '#E1E1E1', // 设置网格的颜色
          },
        },
        //雷达图背景的颜色，在这儿随便设置了一个颜色，完全不透明度为0，就实现了透明背景
        splitArea: {
          show: false,
          areaStyle: {
            color: 'rgba(255,0,0,0)', // 图表背景的颜色
          },
        },

        indicator: [
          { name: '准确性', value: 'ACCURACY', max: 5 * state.multiple },
          { name: '有效性', value: 'VALIDITY', max: 5 * state.multiple },
          { name: '完整性', value: 'COMPLETENESS', max: 5 * state.multiple },
          { name: '唯一性', value: 'UNIQUENESS', max: 5 * state.multiple },
          { name: '一致性', value: 'CONSISTENCY', max: 5 * state.multiple },
          { name: '及时性', value: 'TIMELINESS', max: 5 * state.multiple },
        ],
      },
      series: [
        {
          name: '六性评分',
          type: 'radar',
          avoidLabelOverlap: false,
          data: [
            {
              value: data,
              name: '六性评分',
              // 设置区域边框和区域的颜色
              symbol: 'none',
              itemStyle: {
                // 单个拐点标志的样式设置。
                width: 8,
                normal: {
                  color: '#fff',
                  borderColor: '#18A0FBFF',
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 1,
                  opacity: 0.9,
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                },
                emphasis: {
                  color: '#fff',
                  borderColor: '#18A0FBFF',

                  borderWidth: 1,
                  opacity: 1,
                },
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: '#447DFD',
                  opacity: 0.5,
                },
                emphasis: {
                  color: '#447DFD',
                  // 高亮时的样式
                  opacity: 1,
                },
              },
              areaStyle: {
                color: 'rgba(24, 160, 251, .25)',
              },
            },
          ],
        },
      ],
    }
    state.sixChart.setOption(option)
  }
  // 质量评分趋势
  const trendFn = (xData, yData) => {
    let chartDom = document.getElementById('historyTrend')
    let historyChart = echarts.init(chartDom)
    let option = {
      color: '#447DFD',
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',
        className: 'quality-tooltip',
        padding: 0,
        formatter: function (value) {
          let item = value[0]
          return `<div class="quality-tooltip-box">
<div class="quality-tooltip-box-title">质量平均分</div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">日期</div><div class="num">${item.name}</div></div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">评分</div><div class="num">${item.value}分</div></div>
</div>`
        },
      },
      xAxis: {
        type: 'category',
        data: xData,
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        name: '质量平均分',
      },
      series: [
        {
          name: '平均分',
          data: yData,
          type: 'line',
          showSymbol: false,
        },
      ],
    }
    historyChart.setOption(option)
    state.historyChart = historyChart
  }

  onBeforeUnmount(() => {
    state.qualityChart?.dispose()
    state.sixChart?.dispose()
    state.historyChart?.dispose()
    window.onresize = null
  })
  onMounted(() => {
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList
    // 获取监控对象
    api.dataQuality.getConfiguredRuleList({}).then((res) => {
      let { success, data } = res
      if (success) {
        state.monitorList = data?.map((item) => ({
          value: item.tableId,
          name: item.tableName,
        }))
      }
    })
    getDataFn()
    nextTick(() => {
      window.onresize = () => {
        getDataFn()
      }
    })
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .content-box {
    &-top {
      background-color: #fff;
      border-radius: 8px;
      .add-box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 8px;
        border-bottom: 1px solid #dcdfe6;
        svg {
          font-size: 16px;
          cursor: pointer;
        }
        .top-left {
          display: flex;
          gap: 4px;
          .nancalui-button {
            margin-left: 8px;
            .button-content {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
                font-size: 16px;
              }
            }
          }
        }
      }
      .top-bottom {
        display: flex;
        justify-content: space-between;
        padding: 0 10px 0 16px;
        .search-box-left {
          display: flex;
          flex-wrap: wrap;
          height: 42px;
          overflow: hidden;
          font-weight: normal;
          &.expend {
            height: auto;
            padding-bottom: 10px;
          }
          .item-box {
            margin-top: 10px;
          }
          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 16px;
            font-weight: normal;
          }
        }
        .search-box-right {
          display: flex;
          padding: 10px 0;

          :deep(.nancalui-button) {
            &.nancalui-button--text {
              padding: 0 8px;
              &:hover {
                background-color: #e3ecff;
              }
            }

            .button-content {
              display: flex;
              align-items: center;
              svg.expend {
                transform: rotate(180deg);
              }
            }
          }

          svg {
            margin-left: 4px;
            font-size: 16px;
          }
        }
      }
    }
    &-row {
      height: calc(50% - 48px);
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 0 16px;
      gap: 16px;
      .col {
        position: relative;
        box-sizing: border-box;
        flex: 1;
        height: 100%;
        border-radius: 2px;
        border: 1px solid var(---, #dcdfe6);
        background: #fff;
        > div {
          height: 100%;
        }
        .title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 52px;
          padding: 0 16px;
          color: #1d2129;
          font-family: 'PingFang TC';
          font-size: 18px;
          font-style: normal;
          font-weight: bold;
          line-height: 26px; /* 144.444% */
          border-bottom: 1px solid var(---, #f0f2f5);
          .illustrate {
            margin-left: 8px;
            color: #8091b7;
            &:hover {
              color: $themeBlue;
            }
          }

          &:before {
            position: absolute;
            top: 17px;
            left: 0;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
          span {
            margin-left: 12px;
            color: #909399;
            font-family: 'PingFang TC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
        .canvas {
          box-sizing: border-box;
          height: calc(100% - 52px);
          &.table,
          &.trend {
            // top: 0;
            // height: 320px;
            &.isLzos {
              height: 394px;
            }
          }
          &.table {
            box-sizing: border-box;
            padding: 16px;
            overflow-y: auto;
            :deep(.nancalui-table) {
              height: 100%;
              tr {
                &.expanded {
                  .icon-expand-row {
                    transform: rotate(0deg);
                  }
                }
                .icon-expand-row {
                  width: 16px;
                  height: 16px;
                  background-image: url('/src/assets/img/quality/arrow-bottom.png');
                  background-repeat: no-repeat;
                  background-position: center;
                  background-size: cover;
                  transform: rotate(-90deg);
                  i {
                    display: none;
                  }
                }
              }
              tbody > tr > td:has(.table-expand-child) {
                padding: 0;
              }
              .nancalui-table__container {
                &::-webkit-scrollbar-thumb {
                  background-color: #b1bcd6;
                  border-radius: 8px;
                  &:hover {
                    background-color: #b1bcd6;
                  }
                }
              }
              .table-expand-child {
                width: 100%;
                &-row {
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  &.header {
                    .table-expand-child-row-col {
                      color: rgba(0, 0, 0, 0.9);
                      font-weight: bolder;
                      background-color: #e3ecff;
                      border-top: none;
                      &.first {
                        background-color: #f6f7fb;
                      }
                    }
                  }
                  &:not(.header):hover {
                    background-color: #e3ecff;
                  }
                  &-col {
                    box-sizing: border-box;
                    width: calc(50% - 30px);
                    height: 40px;
                    padding-left: 20px;
                    overflow: hidden;
                    color: rgba(0, 0, 0, 0.75);
                    font-size: 14px;
                    line-height: 40px;
                    white-space: nowrap;
                    text-align: left;
                    text-overflow: ellipsis;
                    border-top: 1px solid #c5d0ea;
                    &.first {
                      width: 60px;
                      background-color: #f6f7fb;
                      border-top: none;
                    }
                  }
                }
              }
              .nancalui-table__empty {
                display: flex;
                align-items: center;
                justify-content: center;
                height: calc(100% - 42px);
                .nancalui-empty__image {
                  width: 48px;
                  height: 48px;
                  background-image: url('/src/assets/table-no-content-small.png');
                  background-repeat: no-repeat;
                  background-position: center;
                  background-size: cover;
                  svg {
                    display: none;
                  }
                }
                .nancalui-empty__description {
                  margin-top: 4px;
                }
              }
            }
          }
        }
        .label-box {
          position: absolute;
          bottom: 24px;
          z-index: 2;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          .label {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 8px;
            color: rgba(0, 0, 0, 0.75);
            font-size: 12px;
            .circle {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 50%;
            }
            .rect {
              width: 12px;
              height: 4px;
              margin-right: 4px;
              background-color: $themeBlue;
            }
          }
        }
      }
    }
  }
  .icons {
    position: relative;
    & + .icons {
      margin-left: 8px;
    }
    &:only-of-type {
      margin-left: auto;
    }
    .yy-icon {
      position: relative;
      cursor: pointer;
      color: #8091b7;
      z-index: 2;
      &:hover {
        color: $themeBlue;
      }
    }
    &:hover {
      &::before {
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #e3ecff;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 5px;
      }
    }
  }

  .is-fullscreen {
    height: 100%;
    .col,
    .canvas {
      height: calc(100% - 52px) !important;
    }
  }
</style>
<style lang="scss">
  @import '@/styles/variables.scss';
  .quality-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: center;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 42px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
</style>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 16px;
      display: flex;
      flex-direction: column;
      &-table {
        flex: 1;
        height: calc(100% - 60px);
        display: flex;
        flex-direction: column;
        gap: 16px;
        border-radius: 0px 0px 2px 2px;
        background: var(--100, #fff);
      }
    }
  }
</style>
