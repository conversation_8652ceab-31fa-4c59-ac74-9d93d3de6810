<template>
  <div class="scene">
    <iframe
      v-if="props.info.sceneDisplayLink"
      class="iframe"
      :src="props.info.sceneDisplayLink"
    ></iframe>
    <div v-else class="empty">
      <img class="empty-img" src="@/assets/table-no-content.png" />
      <p class="empty-text">暂无场景</p>
    </div>
    <img
      class="screen"
      src="@/assets/img/management/full-screen.png"
      @click.prevent.stop="goTargetFn"
    />
  </div>
</template>

<script setup>
  import { defineProps } from 'vue'
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const goTargetFn = () => {
    window.open(props.info.sceneDisplayLink, '_blank')
  }
</script>

<style lang="scss" scoped>
  .scene {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    .iframe {
      width: 100%;
      height: calc(100% - 6px);
      border: none;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
      flex-shrink: 0;
      position: relative;
    }
    .empty {
      width: 100%;
      height: calc(100% - 50px);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      &-img {
        display: block;
        width: 140px;
        height: auto;
        margin: 0 auto;
      }

      &-text {
        margin-top: 20px;
        color: #999999;
        font-size: 12px;
        text-align: center;
      }
    }
    .screen {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 30px;
      height: 30px;
      z-index: 9;
      cursor: pointer;
    }
  }
</style>
