<script setup>
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import LocalTree from './components/tree.vue'
  import offlineWork from './components/offlineWork'
  import DirDialog from './components/dirDialog.vue'

  // 业务流程弹窗组件
  import OperationFlowDialog from './components/operationFlowDialog.vue'
  const { proxy } = getCurrentInstance()
  const treeRef = ref(null)
  const currentComponent = ref(offlineWork)
  const dirDialogRef = ref(null)
  const operationFlowDialogRef = ref(null)
  const allComRef = ref(null)

  const state = reactive({
    showCom: true,
    treeSearchText: '',
    currentWorkFlowId: null, //当前treeId
    taskId: null, //当前任务ID
    processId: null,
    checkTreeId: null, // 当前标签选中的树id
    tagArr: [],
    treeList: [
      {
        id: -1,
        name: '全部',
        type: 'DIRECTORY',
        isTemplateDirectory: false,
        children: [],
      },
    ],
    // 当前节点
    currentNode: null,
    // 展示目录弹窗
    showDirDialog: false,
    pos: {
      x: 160,
      y: 530,
    },
  })

  const visible = ref(true)
  const currentIndex = ref(null)

  const handleContextMenu = (e, index) => {
    e.preventDefault()
    state.pos = {
      x: e.pageX,
      y: e.pageY + 20,
    }
    currentIndex.value = index
    visible.value = true
  }

  const handleCommand = (command) => {
    visible.value = false
    switch (command) {
      case 'closeAll':
        state.tagArr = []
        break
      case 'closeLeft':
        state.tagArr = state.tagArr.slice(currentIndex.value)
        break
      case 'closeRight':
        state.tagArr = state.tagArr.slice(0, currentIndex.value + 1)
        break
      case 'closeOther':
        state.tagArr = [state.tagArr[currentIndex.value]]
        break
      case 'closeCurrent':
        state.tagArr.splice(currentIndex.value, 1)
        break
    }
    if (state.tagArr.length > 0) {
      state.showCom = false
      const tag = state.tagArr[state.tagArr.length - 1]
      state.checkTreeId = tag.treeId
      state.currentWorkFlowId = tag.treeId
      state.processId = tag.processId || null
      state.taskId = tag.taskId || null
      state.showCom = true

      currentComponent.value = tag.com
    }
  }

  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }

  // 双击画布打开标签
  const openLabelFn = (item) => {
    methods.showLabelFn(item)
  }

  onMounted(() => {})

  const methods = {
    // 单机目录树
    nodeClick(val) {
      state.currentNode = val

      methods.showLabelFn(val)
    },
    // 判断新增或打开标签页
    showLabelFn(val) {
      let hasLabel = false // 判断是否已有该标签
      state.tagArr.forEach((v) => {
        if (v.treeId === val.id) {
          hasLabel = true
          v.name = val.name
          currentComponent.value = v.com
        }
      })

      switch (val.type) {
        case 'JOB':
          state.checkTreeId = val.id
          state.taskId = val.taskId
          break
        default:
          return
          break
      }

      let data = {
        name: val.name,
        treeId: val.id,
        processId: val.processId || null,
        taskId: val.taskId || null,
        com: offlineWork,
      }
      if (!hasLabel) {
        if (val.type === 'JOB') {
          data.img = '非结构化数据分析'
          data.com = offlineWork
        }
        currentComponent.value = data.com
        state.tagArr.push(data)
      }
    },
    // 获取目录树
    getTreeList() {
      api.realTimeDevelop.getRealTimeDevelopTree().then((res) => {
        state.treeList[0].children = res?.data?.children || []
        // state.currentWorkFlowId = state.treeList[0].children[0]?.id
        // state.checkTreeId = state.treeList[0].children[0]?.id
      })
    },
    // 创建目录弹窗
    createDirDialog() {
      // dirDialogRef.value.open(state.currentNode)
      dirDialogRef.value.open()
    },
    // 创建业务流程
    createOperationFlow() {
      // operationFlowDialogRef.value.open(state.currentNode)
      operationFlowDialogRef.value.open()
    },
  }

  // 获取图片地址
  const getImageSrc = (url) => {
    const path = new URL(`../../../../src/assets/img/offlineDev/${url}.png`, import.meta.url)
    return path.href
  }
  methods.getTreeList()

  // 改变tag
  const changeTag = (item) => {
    // proxy.$MessageBoxService.open({
    //   title: '是否确认切换菜单？',
    //   content: '切换后，数据如果没有保存将不可恢复',
    //   save: () => {
    state.checkTreeId = item.treeId
    state.currentWorkFlowId = item.treeId
    state.processId = item.processId || null
    state.taskId = item.taskId || null
    currentComponent.value = item.com
    //   },
    // })
  }
  // 删除tag
  const delTag = (index) => {
    // proxy.$MessageBoxService.open({
    //   title: '是否确认关闭菜单？',
    //   content: '关闭后，数据如果没有保存将不可恢复',
    //   save: () => {
    state.tagArr.splice(index, 1)
    if (state.tagArr.length > 0) {
      state.showCom = false
      const tag = state.tagArr[state.tagArr.length - 1]
      state.checkTreeId = tag.treeId
      state.currentWorkFlowId = tag.treeId
      state.processId = tag.processId || null
      state.taskId = tag.taskId || null
      state.showCom = true

      currentComponent.value = tag.com
    }
    //   },
    // })
  }
</script>
<template>
  <section class="container">
    <DirDialog ref="dirDialogRef" :treeList="state.treeList" @success="methods.getTreeList" />
    <OperationFlowDialog
      ref="operationFlowDialogRef"
      :treeList="state.treeList"
      @success="methods.getTreeList"
    />
    <section class="container-box">
      <section class="container-box-table">
        <section class="cf-tree">
          <div class="row">
            <div class="title">实时作业 </div>
            <i class="cf-icon-add" @click="methods.createDirDialog"> </i>
          </div>
          <div class="cf-tree-container">
            <n-input
              class="table-tree-ipt"
              v-model="state.treeSearchText"
              placeholder="请输入关键词"
              suffix="search"
              @change="(val) => treeRef.treeRef.filter(val)"
            />
            <LocalTree
              ref="treeRef"
              :check-on-click-node="true"
              :default-expanded-keys="[]"
              :filter-node-method="filterNode"
              :props="{
                children: 'children',
                label: 'name',
              }"
              :showBtns="true"
              node-key="id"
              @node-dblclick="methods.nodeClick"
              @node-click="methods.nodeClick"
              :data="state.treeList"
              @change="methods.getTreeList"
            />
          </div>
        </section>

        <section class="talble-container">
          <!-- 标签 -->
          <div v-if="state.tagArr.length > 0" class="tag">
            <div class="tag-box">
              <div
                :class="['tag-node', state.checkTreeId === item.treeId ? 'active' : '']"
                v-for="(item, index) in state.tagArr"
                :key="item.name"
                @click="changeTag(item)"
                @contextmenu.prevent="handleContextMenu($event, index)"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_3643_483676)">
                  <path d="M12.8399 13.6C12.8399 13.6 12.7599 14.12 12.9199 14.36L13.5999 14.84L13.5599 15V15.24L13.7199 15.28L13.9599 15.24L13.9999 15.32L14.1199 15.48H14.2799L14.4399 15.12V14.96L14.5999 14.92C14.5999 14.92 15.6799 14.56 15.4399 14.16L15.3599 14.12C15.3599 14.12 14.8799 14.32 14.6799 14.28C14.6799 14.28 13.7599 14.32 13.3199 14L12.8399 13.6Z" fill="#D77083"/>
                  <path d="M13.08 13.28C13.08 13.28 12.64 13.28 12.92 13.72C12.92 13.72 13.36 14.2 14.2 14.32C14.2 14.32 15.44 14.32 15.52 13.92C15.52 13.92 15.48 13.44 15.12 13.48C14.76 13.6 13.08 13.28 13.08 13.28Z" fill="#733D48"/>
                  <path d="M15 12C15 12 15.56 12.6 15.32 13.52C15.32 13.52 15.28 13.92 14.96 14.04C14.96 14.04 14.36 14.16 14.2 14.08C14.2 14.08 13.44 14.04 13.2 13.64C13.2 13.64 12.88 13.32 13.16 12.96C13.16 12.96 13.72 12.72 13.84 12.36C13.84 12.36 14.04 12.2 14.28 12.24C14.52 12.28 14.72 12.4 14.72 12.4C14.72 12.4 15.08 12.12 15 12Z" fill="#D59D52"/>
                  <path d="M13.52 12.48C13.52 12.48 13.8 13.08 14.48 13.12C14.48 13.12 15.12 13.12 15.4 12.72L15.16 12.12L14.72 12C14.72 12 13.56 11.64 13.52 12.48Z" fill="#DBAE62"/>
                  <path d="M15 12C15 12 15.28 12.36 14.92 12.44C14.92 12.44 14.56 12.52 14.36 12.44C14.36 12.44 13.12 12.8 13.44 13.84C13.44 13.84 13.2 13.84 13.08 13.24L13.2 12C13.2 12 14.88 11.68 15 12Z" fill="#B88F4D"/>
                  <path d="M14.84 12.6C14.84 12.6 15 12.92 14.88 13.08C14.88 13.08 14.84 13.16 15.04 13.2C15.04 13.2 15.28 12.92 15 12.48C15 12.48 14.84 12.44 14.84 12.6Z" fill="white"/>
                  <path d="M14.84 12.56C14.84 12.5812 14.8484 12.6015 14.8634 12.6165C14.8784 12.6316 14.8987 12.64 14.92 12.64C14.9412 12.64 14.9615 12.6316 14.9765 12.6165C14.9915 12.6015 15 12.5812 15 12.56C15 12.5388 14.9915 12.5184 14.9765 12.5034C14.9615 12.4884 14.9412 12.48 14.92 12.48C14.8987 12.48 14.8784 12.4884 14.8634 12.5034C14.8484 12.5184 14.84 12.5388 14.84 12.56Z" fill="white"/>
                  <path d="M14.88 13.12C14.88 13.1413 14.8884 13.1616 14.9034 13.1766C14.9184 13.1916 14.9388 13.2 14.96 13.2C14.9812 13.2 15.0016 13.1916 15.0166 13.1766C15.0316 13.1616 15.04 13.1413 15.04 13.12C15.04 13.0988 15.0316 13.0785 15.0166 13.0635C15.0016 13.0485 14.9812 13.04 14.96 13.04C14.9388 13.04 14.9184 13.0485 14.9034 13.0635C14.8884 13.0785 14.88 13.0988 14.88 13.12Z" fill="white"/>
                  <path d="M15.44 13.6C15.44 13.6 15.8 12.68 15.12 11.96H15V12.16C15 12.16 15.4 12.6 15.28 13.12C15.28 13.12 15.24 13.56 15.16 13.76C15.16 13.76 14.88 14.04 14.56 14.04C14.56 14.04 13.88 14.04 13.32 13.72C13.32 13.72 13.04 13.4 13.16 13.04H13.08C13.08 13 13.04 12.96 12.88 13.04C12.88 13.04 12.96 13.16 12.8 13.2C12.8 13.2 12.48 13.36 12.56 14C12.6 14.56 13.32 14.92 13.32 14.92C13.32 14.92 13.36 14.96 13.36 15.04C13.36 15.12 13.32 15.32 13.56 15.36C13.56 15.36 13.72 15.4 13.8 15.32C13.8 15.32 13.92 15.68 14.16 15.6C14.4 15.52 14.48 15.28 14.36 15.08C14.36 15.08 14.92 15 15.28 14.56C15.36 14.56 15.84 14.04 15.44 13.6ZM15.28 14.36C15.2 14.48 15.04 14.84 14.48 14.96C14.48 14.96 14.16 14.96 14.04 14.92C14.04 14.92 13.72 15.04 14.08 15C14.08 15 14.36 15 14.32 15.2C14.32 15.2 14.28 15.4 14.2 15.4C14.2 15.4 14.12 15.48 14 15.24C14 15.24 14.04 15.16 13.88 15.16C13.88 15.16 13.6 15.28 13.6 15C13.6 15 13.68 14.84 13.76 14.8C13.76 14.8 12.96 14.56 12.84 13.88C12.84 13.88 12.76 13.72 12.88 13.76C13.44 14.28 14.12 14.36 14.12 14.36C14.72 14.44 15.24 14.2 15.24 14.2C15.28 14.24 15.32 14.28 15.28 14.36ZM15.4 14C15.4 14 15.36 14.08 15.32 14.12C15.28 14.12 15.28 14.16 15.24 14.16C14.52 14.48 13.64 14.16 13.64 14.16C12.96 13.8 12.92 13.56 12.92 13.52C12.92 13.48 12.92 13.44 12.96 13.44C12.96 13.44 13.04 13.28 13.12 13.36C13.12 13.48 13.2 13.64 13.28 13.72C13.52 13.92 13.84 14.08 14.36 14.12C14.88 14.16 15.16 14 15.28 13.76C15.36 13.8 15.44 13.92 15.4 14Z" fill="black"/>
                  <path d="M0.560059 8.8C0.560059 8.8 0.800059 10.76 2.32006 12.04L4.12006 10.48L6.80006 10.4L10.2401 8.44C10.2401 8.44 12.3201 7.12 12.2401 5.64C12.2401 5.64 9.96006 6.68 9.44006 6.36L8.56006 6.8L7.60006 7.48L6.20006 7.6L6.00006 7.84L3.40006 8.64C3.40006 8.64 3.04006 7.52 4.24006 5.64L4.64006 5L2.92006 5.8L2.88006 5.4L2.68006 5.44L2.36006 6.44L2.00006 6.56L1.00006 8.28L0.880059 8H0.720059V8.8H0.560059Z" fill="#947BB0"/>
                  <path d="M11.84 7.19994L12.2 7.07994L12.64 6.43994H12.8L13.08 6.47994L13.2 6.71994L13.44 6.95994L13.88 7.07994L14.28 7.23994L14.64 7.55994L14.8 7.79994L15 8.15994L15.16 8.71994L15.28 9.11994L15.36 9.39994L15.44 9.63994L15.4 9.99994L15.28 10.2799L15.12 10.4799L14.72 10.5999L14.6 10.7199L14.08 10.7999H13.72L13.48 10.9599L13.52 11.1599H13.76L14.2 11.0399L14.64 11.0799L14.92 11.3199L15.04 11.4399L15.08 11.7199V12.1199L14.8 12.3599L14.52 12.3199L14.32 12.2399L13.96 12.2799L13.84 12.4399L13.6 12.6799L13.16 12.9199L12.76 12.9999L12.16 13.0799L12.12 13.2399V13.3999L12.04 13.7199L11.8 14.0399L11.56 14.3999L12.04 14.4399L12.48 14.5199L12.68 14.7599L12.76 14.9999L12.68 15.1999L12.4 15.4399L11.76 15.4799H6.00003C6.00003 15.4799 2.80003 15.4799 1.48003 13.1599C0.160029 10.8399 0.640029 9.35994 0.640029 9.35994L0.720029 8.99994C0.720029 8.99994 1.24003 10.8399 2.12003 11.7599L2.36003 12.0399C2.36003 12.0399 2.52003 11.9999 2.64003 11.3599C2.64003 11.3599 2.92003 10.0399 6.20003 10.0399L7.56003 9.31994L8.92003 8.91994L9.92003 8.67994L10.72 8.27994L11.84 7.19994Z" fill="#E75270"/>
                  <path d="M13.6 8.68004C13.6 8.68004 13.84 8.32004 14.32 8.56004C14.32 8.56004 14.8 8.84004 14.48 9.48004C14.48 9.48004 14.28 9.44004 14.12 9.48004C14.08 9.48004 13.16 9.52004 13.6 8.68004Z" fill="black"/>
                  <path d="M13.76 9.08005C13.76 9.08005 13.64 8.96005 13.72 8.80005L13.6 8.68005C13.6 8.68005 13.4 8.92005 13.76 9.20005C13.76 9.20005 13.92 9.32005 14.28 9.32005C14.28 9.32005 14.4 9.36005 14.4 9.40005V9.08005C14.4 9.08005 14.32 9.28005 14.2 9.28005C14.2 9.28005 13.92 9.24005 13.76 9.08005Z" fill="white"/>
                  <path d="M13.84 8.88001C13.84 8.94367 13.8653 9.00471 13.9103 9.04972C13.9553 9.09473 14.0163 9.12001 14.08 9.12001C14.1436 9.12001 14.2047 9.09473 14.2497 9.04972C14.2947 9.00471 14.32 8.94367 14.32 8.88001C14.32 8.81636 14.2947 8.75532 14.2497 8.71031C14.2047 8.6653 14.1436 8.64001 14.08 8.64001C14.0163 8.64001 13.9553 8.6653 13.9103 8.71031C13.8653 8.75532 13.84 8.81636 13.84 8.88001Z" fill="#D19B52"/>
                  <path d="M13.76 8.71998C13.76 8.7518 13.7727 8.78232 13.7952 8.80483C13.8177 8.82733 13.8482 8.83998 13.88 8.83998C13.9118 8.83998 13.9424 8.82733 13.9649 8.80483C13.9874 8.78232 14 8.7518 14 8.71998C14 8.68815 13.9874 8.65763 13.9649 8.63512C13.9424 8.61262 13.9118 8.59998 13.88 8.59998C13.8482 8.59998 13.8177 8.61262 13.7952 8.63512C13.7727 8.65763 13.76 8.68815 13.76 8.71998Z" fill="white"/>
                  <path d="M4.87995 2.39994L4.91995 1.99994L5.95995 1.23994L6.43995 0.999941C6.43995 0.999941 7.07995 0.439941 7.99995 0.439941L9.11995 0.559941L9.91995 0.839941L10.52 1.19994L11 1.43994L11.6 1.83994L11.48 2.55994L11.36 3.07994L11.52 3.79994L11.28 4.15994C11.28 4.15994 11.16 4.47994 10.52 4.99994L10.16 5.67994L9.51995 6.47994L8.39995 7.19994L7.43995 7.47994L6.67995 7.55994L3.91995 8.39994L3.39995 8.63994C3.39995 8.63994 3.07995 6.91994 4.23995 5.63994L4.95995 4.99994C4.95995 4.99994 5.43995 4.59994 5.51995 3.95994L5.95995 4.71994L6.27995 4.03994C6.27995 3.99994 6.31995 2.27994 4.87995 2.39994Z" fill="#E7DFAB"/>
                  <path d="M11.96 1.56006L11.6 2.00006L11.48 2.40006L11.4 3.32006L11.28 4.16006C11.28 4.16006 10.8 4.92006 10.6 4.84006L10.44 5.20006L10.36 5.48006L11 4.92006L11.84 4.56006L12.32 4.36006L12.52 3.96006L12.32 3.20006L12.28 2.72006L12.04 2.64006C12.04 2.64006 11.92 2.00006 12.12 1.76006L11.96 1.56006Z" fill="#D19456"/>
                  <path d="M10.04 5.84C10.04 5.84 10.84 4.88 11.84 4.44V4.68L12.84 4.12H13.08L13 4.64L12.72 5L12.4 5.52L11.88 5.88L10.8 6.28L9.76001 6.52L10.04 5.84Z" fill="#D77083"/>
                  <path d="M6.19995 2.72006C6.19995 2.72006 7.51995 2.36006 7.95995 2.72006C8.39995 3.08006 9.59995 2.64006 9.59995 2.64006L10.68 3.28006L10.6 4.80006C10.6 4.80006 10.48 3.80006 9.31995 3.68006C8.15995 3.56006 8.19995 3.36006 7.47995 3.00006C6.75995 2.64006 6.91995 3.04006 6.19995 2.72006Z" fill="#D89E52"/>
                  <path d="M6.19995 2.71997C6.19995 2.71997 6.35995 3.19997 7.15995 3.23997C7.95995 3.27997 10.52 4.59997 9.51995 5.67997C9.51995 5.67997 9.35995 6.87997 8.35995 7.15997C8.35995 7.15997 9.15995 6.11997 8.63995 4.99997C8.63995 4.99997 8.51995 4.07997 7.39995 3.79997C7.43995 3.79997 6.71995 3.47997 6.19995 2.71997Z" fill="#D89E52"/>
                  <path d="M6.95995 4.28003C6.95995 4.28003 7.35995 5.52003 8.03995 5.72003C8.03995 5.72003 8.83995 6.44003 8.11995 6.96003L6.67995 7.56003L4.19995 8.20003L3.31995 8.52003C3.31995 8.52003 4.31995 7.24003 4.99995 6.92003C4.99995 6.92003 6.19995 6.60003 6.71995 5.32003L6.95995 4.28003Z" fill="#D89E52"/>
                  <path d="M5.79998 4.28003C5.79998 4.28003 5.51998 5.48003 4.31998 6.64003C3.71998 7.20003 3.59998 7.92003 3.47998 8.44003C3.47998 8.44003 3.79998 7.32003 4.67998 6.60003C5.19998 6.16003 5.75998 5.48003 5.79998 4.28003Z" fill="#814879"/>
                  <path d="M6.83997 7.75998C6.83997 7.75998 7.59997 7.79998 7.87997 8.03998C7.87997 8.03998 7.63997 7.83998 7.43997 7.75998C7.43997 7.75998 8.63997 7.91998 9.11997 8.11998C9.11997 8.11998 9.67997 8.23998 9.99997 8.11998C9.99997 8.11998 9.11997 8.07998 8.75997 7.83998C8.75997 7.83998 8.03997 7.55998 7.59997 7.59998L6.83997 7.75998Z" fill="#785085"/>
                  <path d="M9.40001 6.84001C9.40001 6.84001 9.76001 6.76001 10.24 6.84001C10.24 6.84001 11 7.24001 11.88 6.64001C11.88 6.64001 11.2 7.48001 10.64 7.36001C10.64 7.36001 9.08001 7.08001 8.76001 7.20001C8.80001 7.24001 9.28001 7.08001 9.40001 6.84001Z" fill="#E1CAC9"/>
                  <path d="M5.92004 2.44006C5.92004 2.44006 6.48004 1.96006 6.72004 1.88006C6.72004 1.88006 7.80004 1.60006 7.36004 1.88006L7.16004 2.12006C7.16004 2.12006 7.56004 1.92006 8.00004 1.96006L7.80004 2.12006C7.80004 2.12006 8.48004 2.00006 8.56004 2.12006C8.56004 2.12006 8.84004 2.44006 9.12004 2.28006C9.12004 2.28006 9.00004 2.60006 8.32004 2.24006C8.32004 2.24006 7.84004 2.24006 8.32004 2.44006C8.28004 2.44006 7.00004 2.12006 5.92004 2.44006Z" fill="white"/>
                  <path d="M5.39997 11.08C5.39997 11.08 4.83997 11.16 4.83997 12C4.83997 12 4.95997 12.68 5.47997 12.88C5.47997 12.88 5.23997 11.84 5.59997 11.12L5.39997 11.08Z" fill="#C74360"/>
                  <path d="M10.56 11.3199C10.56 11.3199 10.48 12.7199 11.04 13.3199C11.04 13.3199 11.52 13.7999 12.16 13.7199C12.16 13.7199 12.52 13.1199 11.8 13.0799C11.8 13.0799 11.08 12.9199 10.88 12.1999C10.84 12.2399 10.76 11.3999 10.56 11.3199Z" fill="#C04B5D"/>
                  <path d="M11.1599 10.0401C11.1599 10.0401 10.8799 11.1601 12.0799 11.4801C12.0799 11.4801 13.0399 11.6001 13.3199 11.0801C13.3199 11.0801 12.3199 11.2801 11.7999 10.7601C11.7599 10.7601 11.1999 10.3601 11.1599 10.0401ZM12.8799 6.68005C12.8799 6.68005 12.1199 7.16005 12.6799 7.68005C12.6799 7.68005 12.7999 7.12005 12.9199 6.96005C13.0799 6.80005 12.8799 6.68005 12.8799 6.68005Z" fill="#AD3652"/>
                  <path d="M12.8 7.03994C12.8 7.03994 12.6 7.47994 12.68 7.63994C12.68 7.63994 12.8 7.91994 12.88 7.87994C12.88 7.87994 12.68 7.47994 12.92 7.11994C12.92 7.11994 13.12 6.83994 12.92 6.43994C12.92 6.47994 13 6.75994 12.8 7.03994ZM13.2 7.83994C13.2 7.83994 13.68 7.55994 13.36 7.11994C13.36 7.11994 13.44 7.51994 13.2 7.83994ZM11.16 9.99994C11.32 11.1599 12.56 11.0799 12.56 11.0799C11.32 10.8399 11.16 10.0399 11.16 9.99994Z" fill="black"/>
                  <path d="M15.4 9.24004C15.4 9.24004 15.24 8.68004 15.24 8.40004C15.24 8.40004 14.96 6.92004 13.44 6.84004C13.44 6.84004 13.28 6.36004 12.8 6.36004C12.8 6.36004 12.52 6.40004 12.08 6.96004C12 7.04004 12 6.92004 12 6.92004C12.08 6.84004 12.08 6.76004 12.08 6.76004C12.36 6.44004 12.52 5.76004 12.6 5.48004C12.76 5.36004 12.88 5.20004 13 5.04004L13.16 4.52004C13.16 4.52004 13.08 4.52004 13 4.64004C12.92 4.76004 13.28 4.00004 13.16 3.80004C13.16 3.80004 12.68 4.04004 12.64 4.16004C12.64 4.16004 12.52 3.44004 12.36 3.08004C12.36 3.08004 12.36 2.56004 12.52 2.48004C12.52 2.48004 12.36 2.28004 12.12 2.64004C12.12 2.64004 11.84 2.36004 12.24 1.60004C12.24 1.60004 12.32 1.44004 11.92 1.56004C11.92 1.56004 11.6 1.88004 11.56 1.96004C11.56 1.96004 11.4 1.76004 11.32 1.52004C11.32 1.52004 11.28 1.48004 11.24 1.52004C11.24 1.52004 11.16 1.64004 11.12 1.40004C11.12 1.40004 10.96 1.12004 10.88 1.12004C10.88 1.12004 10.92 1.32004 10.88 1.32004C10.88 1.32004 10.84 1.32004 10.72 1.24004C10.72 1.24004 10.12 0.840041 9.20002 0.640041C9.20002 0.640041 9.00002 0.560041 8.92002 0.520041C8.92002 0.520041 8.88002 0.520041 8.84002 0.560041C8.84002 0.560041 8.68003 0.560041 8.56003 0.520041C8.12003 0.320041 7.92002 0.520041 7.92002 0.520041C5.64002 0.520041 4.92002 2.00004 4.92002 2.00004C4.68002 2.24004 4.88002 2.40004 4.88002 2.40004H5.24002C6.76002 3.36004 5.80002 4.28004 5.80002 4.28004L5.64002 3.76004C5.28002 3.40004 5.40002 3.76004 5.40002 3.76004C5.52002 4.32004 5.12002 4.68004 5.12002 4.68004C3.92002 5.04004 3.04002 5.56004 3.04002 5.56004C2.80002 5.52004 3.16002 5.16004 2.88002 5.20004C2.60002 5.24004 2.44002 5.96004 2.44002 5.96004C1.68002 6.52004 1.04002 7.60004 0.800024 8.00004C0.800024 7.92004 0.760024 7.40004 0.760024 7.12004C0.760024 7.12004 0.480024 7.80004 0.640024 8.52004C0.640024 8.52004 0.520024 8.84004 0.440024 9.24004C0.440024 9.24004 0.440024 9.32004 0.400024 9.40004V9.92004C0.400024 10.56 0.440024 11.52 0.840024 12.32C0.840024 12.32 1.44002 14.2 3.92002 15.24C3.92002 15.24 4.92002 15.6 5.80002 15.6H11.8C11.8 15.6 12.64 15.6 12.76 15.16C12.88 14.68 12.6 14.2 11.8 14.32C11.8 14.32 11.56 14.36 11.88 14.12C11.88 14.12 12.16 13.88 12.16 13.36C12.16 13.36 12.04 13.16 12.44 13.2C12.44 13.2 12.72 13.2 12.88 13.16C12.88 13.16 13.64 13 13.92 12.36C13.92 12.36 14.2 12.24 14.36 12.36C14.36 12.36 14.68 12.52 14.76 12.4C14.76 12.4 14.8 12.36 15 12.24C15 12.24 15.08 12.28 15.16 11.68C15.16 11.68 15.16 11.44 15 11.32C15 11.32 14.92 11.2 14.88 11.12C14.88 11.12 14.68 10.84 14 10.96C14 10.96 13.64 11.2 13.56 11.08C13.48 10.96 13.68 10.96 13.68 10.96C13.68 10.96 14.48 10.92 14.8 10.68C14.8 10.68 15.32 10.64 15.36 10.08C15.4 10 15.72 9.72004 15.4 9.24004ZM11.96 6.60004C11.24 8.08004 9.84002 8.52004 9.84002 8.52004C9.12002 8.52004 8.96002 8.56004 8.96002 8.56004C9.48002 8.64004 9.36002 8.72004 9.36002 8.72004C8.24002 8.84004 7.84002 9.04004 7.84002 9.04004C6.36002 8.80004 5.56002 8.96004 5.56002 8.96004C3.76002 9.28004 3.00002 10.12 3.00002 10.12C4.60002 9.20004 5.68002 9.12004 5.68002 9.12004C7.44002 9.00004 7.56002 9.12004 7.56002 9.12004C7.08002 9.20004 6.48002 9.64004 6.48002 9.64004C6.20002 9.84004 4.96002 10.04 4.96002 10.04C4.52002 10.08 3.72002 10.32 3.72002 10.32C2.48002 10.72 2.48002 11.84 2.48002 11.84C2.08002 11.4 2.12002 10.64 2.12002 10.64L2.08002 10.6C2.12002 10.44 2.20002 10.28 2.36002 9.92004C2.72002 9.08004 2.96002 9.04004 3.60002 8.64004C4.24002 8.24004 6.60002 7.80004 6.60002 7.80004C8.92002 7.52004 9.56003 6.68004 9.56003 6.68004C9.60003 6.64004 9.68003 6.56004 9.80002 6.48004H9.84002C9.84002 6.48004 11.2 6.44004 12.28 5.72004C12.24 6.08004 11.96 6.60004 11.96 6.60004ZM12.96 4.20004C12.96 4.20004 12.88 4.88004 12.36 5.32004C12.36 5.32004 11.6 6.08004 9.96002 6.36004L9.88002 6.40004C10 6.24004 10.16 6.12004 10.16 6.12004C11.48 5.92004 12.28 4.96004 12.28 4.96004C11.12 5.80004 10.24 5.96004 10.24 5.96004C10.36 5.80004 10.44 5.60004 10.56 5.40004C10.72 5.28004 11.24 4.84004 11.68 4.72004C11.68 4.72004 11.48 5.08004 11.24 5.24004C11.24 5.24004 11.6 5.20004 12.2 4.64004C12.16 4.56004 12.64 4.20004 12.96 4.20004ZM11.52 2.72004C11.6 2.04004 11.96 1.72004 11.96 1.72004C12.12 1.84004 11.96 1.96004 11.96 1.96004C11.84 2.36004 11.92 2.88004 11.92 2.88004L11.52 3.80004C11.52 3.76004 11.44 3.40004 11.52 2.72004ZM5.56002 4.16004C6.08002 4.84004 5.24002 5.96004 5.24002 5.96004C6.68002 4.84004 6.36002 3.80004 6.36002 3.80004C6.36002 2.76004 5.52002 2.44004 5.52002 2.44004C5.08002 2.36004 5.16002 2.20004 5.16002 2.20004C6.36002 0.840041 7.48002 1.08004 7.48002 1.08004C7.48002 0.880041 6.64002 1.00004 6.64002 1.00004C6.92002 0.600041 7.88002 0.680041 7.88002 0.680041C8.04002 0.720041 8.40002 0.880041 8.40002 0.880041L8.24002 0.560041C8.36002 0.600041 8.44003 0.640041 8.52003 0.680041C8.60003 0.720041 8.68003 0.720041 8.68003 0.720041C8.80002 0.720041 8.96002 0.760041 9.20002 0.760041C9.20002 0.760041 9.44003 0.800041 9.64003 1.20004C9.64003 1.20004 9.60003 0.960041 9.56003 0.880041C9.56003 0.880041 9.84002 1.04004 9.96002 1.24004C9.96002 1.24004 10 1.20004 9.96002 1.04004C9.96002 1.04004 10.48 1.28004 10.68 1.60004C10.68 1.60004 10.72 1.64004 10.72 1.52004C10.72 1.52004 10.76 1.28004 10.84 1.52004C10.84 1.52004 10.8 1.76004 10.88 1.72004C10.88 1.72004 10.96 1.64004 10.96 1.52004C10.96 1.52004 11.04 1.48004 11.04 1.60004C11.04 1.60004 11.12 1.68004 11.2 1.60004C11.2 1.60004 11.28 1.68004 11.32 1.84004C11.36 2.00004 11.44 2.12004 11.44 2.12004C11.44 2.12004 11.28 2.52004 11.32 2.96004C11.32 2.96004 11.32 4.08004 11.16 4.28004C11.16 4.28004 11.64 4.08004 11.84 3.24004C11.84 3.24004 12.12 2.52004 12.16 2.96004C12.16 2.96004 12.08 3.16004 12.16 3.40004C12.16 3.40004 12.04 3.88004 11.92 3.96004C11.92 3.96004 12.16 3.80004 12.28 3.60004C12.28 3.60004 12.4 4.00004 12.2 4.12004C12.2 4.12004 11.64 4.48004 11.12 4.60004C11.12 4.60004 10.76 4.80004 10.64 5.04004L10.56 5.16004C10.76 4.68004 10.88 4.20004 10.88 4.20004C11.08 4.04004 11.16 3.72004 11.16 3.72004C11.08 3.72004 10.92 3.92004 10.92 3.92004C10.76 4.08004 10.84 3.64004 10.84 3.64004C10.92 3.52004 11 3.32004 11 3.32004C11 3.16004 10.84 3.36004 10.84 3.36004C10.68 3.44004 10.76 3.16004 10.76 3.16004C10.88 2.92004 10.8 2.84004 10.8 2.84004C10.72 2.84004 10.6 3.20004 10.6 3.20004C10.56 3.44004 10.36 2.96004 10.36 2.96004C10.16 2.80004 10.2 2.40004 10.2 2.40004C10.16 2.16004 10.12 2.40004 10.12 2.40004C10.08 2.68004 10.04 2.52004 10.04 2.52004C10.04 2.32004 10 2.16004 10 2.16004V2.48004C9.92002 2.60004 9.84002 2.32004 9.84002 2.32004C9.80002 2.04004 9.72003 2.08004 9.72003 2.08004C9.68003 2.24004 9.76003 2.32004 9.72003 2.36004C9.68003 2.40004 9.56003 2.24004 9.56003 2.08004C9.56003 1.92004 9.44002 2.00004 9.44002 2.00004C9.44002 2.28004 9.36002 2.16004 9.36002 2.16004C7.28002 1.24004 6.40002 1.96004 6.40002 1.96004C8.32002 1.16004 10.04 3.04004 10.04 3.04004C9.76003 2.92004 9.28002 3.00004 9.28002 3.00004C8.72002 3.24004 8.40002 3.08004 8.40002 3.08004C8.60002 3.32004 9.40002 3.20004 9.40002 3.20004C9.72002 3.08004 10 3.24004 10 3.24004C10.36 3.56004 9.92002 3.56004 9.92002 3.56004C10.32 3.72004 10.44 4.32004 10.44 4.32004C10.32 5.96004 8.60003 6.80004 8.60003 6.80004C9.44003 5.52004 8.92002 4.76004 8.92002 4.76004C8.64003 4.12004 7.48002 3.84004 7.48002 3.84004C8.64002 4.36004 8.72003 5.04004 8.72003 5.04004C8.84002 6.24004 8.16002 6.96004 8.16002 6.96004L7.76002 7.12004C8.04002 6.76004 8.00002 6.12004 8.00002 6.12004C7.80002 6.96004 7.00002 7.36004 7.00002 7.36004C7.28002 6.92004 7.24002 6.52004 7.24002 6.52004C7.16002 6.92004 6.52002 7.40004 6.52002 7.40004C6.24002 7.76004 4.40002 8.20004 4.24002 8.20004C4.16002 8.20004 3.76002 8.32004 3.52002 8.44004C3.08002 7.16004 4.80002 5.32004 4.80002 5.32004C5.40002 4.92004 5.56002 4.16004 5.56002 4.16004ZM2.12002 6.68004C1.92002 8.96004 2.68002 8.92004 2.68002 8.92004C1.80002 8.32004 2.60002 5.80004 2.60002 5.80004C2.84002 5.20004 2.88002 5.80004 2.88002 5.80004C2.52002 6.48004 2.64002 7.12004 2.64002 7.12004C2.84002 5.52004 4.32002 5.32004 4.32002 5.32004C3.24002 6.88004 3.32002 8.52004 3.32002 8.52004H3.36002C3.24002 8.52004 3.20002 8.56004 3.20002 8.56004C2.88002 7.68004 3.20002 6.52004 3.20002 6.52004C2.64002 7.12004 2.96002 8.96004 2.96002 8.96004C2.92002 9.08004 2.76002 9.00004 2.76002 9.00004C2.60002 9.00004 2.32002 9.60004 2.32002 9.60004C2.24002 9.60004 2.12002 9.32004 2.12002 9.32004C2.04002 9.52004 2.12002 9.60004 2.12002 9.60004C2.20002 10.08 2.16002 10 2.16002 10C2.08002 10.04 2.00002 9.76004 2.00002 9.76004C1.88002 10 2.04002 10.28 2.04002 10.28L2.00002 10.52C2.00002 10.52 1.28002 9.52004 0.960024 8.48004C1.28002 7.60004 2.12002 6.68004 2.12002 6.68004ZM0.760024 8.12004C0.760024 8.12004 0.680024 8.64004 2.00002 10.76C2.00002 11 2.00002 11.24 2.04002 11.48C2.04002 11.48 1.08002 10.6 0.840024 9.00004C0.840024 9.00004 0.720024 8.24004 0.760024 8.12004ZM15.36 9.92004C15.36 9.92004 15.08 9.60004 15 9.76004L15.28 10C15.28 10 15.28 10.48 14.72 10.52C14.72 10.52 14.52 10.8 13.88 10.8C13.88 10.8 13.48 10.84 13.36 10.96C13.36 10.96 13.2 11.08 12.64 11.04C12.64 11.04 12.8 11.12 13.12 11.12C13.12 11.12 13.32 11.08 13.36 11.16C13.36 11.16 13.68 11.28 13.68 11.32C13.68 11.36 14.52 10.84 14.84 11.32C14.84 11.32 14.92 11.48 15 11.48C15 11.48 15.04 11.68 15 11.72C14.96 11.76 14.84 11.6 14.84 11.72C14.84 11.72 15.12 11.76 15 12.08C15 12.08 14.76 12.44 14.44 12.16C14.44 12.16 14 12.24 13.88 12.16C13.88 12.16 13.52 13.32 11.72 13C11.72 13 11 12.84 10.68 11.8C10.68 11.8 10.24 9.92004 8.48002 10.4C8.48002 10.4 9.60003 10.32 10.12 11.04C10.12 11.04 10.52 11.36 10.76 12.32C10.76 12.32 11.04 13 11.76 13.12C11.76 13.12 12.28 13.16 11.92 13.84C11.92 13.84 11.72 14.16 11.28 14.4C11.28 14.4 11.12 14.48 11.16 14.6C11.16 14.6 11.12 14.84 11.36 14.64C11.36 14.64 12.28 14.12 12.56 14.84C12.56 14.84 12.64 15 12.56 15.04C12.56 15.04 12.48 15.04 12.44 14.96C12.44 14.96 12.36 14.76 12.24 14.8C12.24 14.8 12.48 14.96 12.4 15.28C12.4 15.28 12.2 15.52 12.12 15.28C12.12 15.28 12.2 14.84 11.88 14.92C11.88 14.92 12.2 15.16 11.96 15.4C11.96 15.4 11.84 15.48 11.6 15.44C11.6 15.44 11.48 15.28 10.88 15.44H5.68002C5.68002 15.44 2.96002 15.4 1.36002 13C1.36002 13 0.640024 11.68 0.600024 10.8L1.20002 11.6L0.680024 10.32V9.44004C0.680024 9.28004 0.720024 9.12004 0.720024 9.04004C0.760024 8.88004 0.920024 10.28 2.12002 11.8C2.28002 12.44 2.60002 12.84 2.60002 12.84C2.28002 12.84 1.84002 12.4 1.84002 12.4C2.32002 13.16 3.04002 13.36 3.04002 13.36C4.00002 14.16 4.96002 14.36 4.96002 14.36C4.28002 14.2 3.52002 13.56 3.52002 13.56C2.80002 13 2.44002 12.08 2.44002 12.08C2.44002 11.76 2.80002 11.2 2.80002 11.2C3.28002 10.48 4.52002 10.28 4.52002 10.28C5.24002 10.24 6.12002 10.08 6.12002 10.08C5.92002 10.16 5.64002 10.76 5.64002 10.76C5.60002 10.84 5.28002 10.88 5.28002 10.88C3.88002 11.04 3.60002 11.6 3.60002 11.6C3.84002 11.4 4.28002 11.24 4.28002 11.24C4.52002 11.12 5.40002 11.08 5.40002 11.08C5.04002 12.44 5.56002 13.52 5.56002 13.52C5.08002 12.04 5.76002 10.96 5.76002 10.96C6.64002 9.64004 8.08002 9.36004 8.08002 9.36004C8.60002 9.20004 9.88002 8.88004 9.88002 8.88004C10.76 8.56004 11.2 7.96004 11.2 7.96004C11.4 7.64004 11.84 7.44004 12 7.36004C12 7.48004 12.08 7.60004 12.2 7.68004C12.2 7.68004 12.36 7.72004 12.28 7.44004C12.28 7.44004 12.12 7.28004 12.36 6.92004C12.36 6.92004 12.64 6.60004 12.84 6.56004C12.84 6.56004 13.2 6.84004 13.24 7.12004C13.24 7.12004 14.76 7.36004 14.96 8.68004C14.96 8.68004 15 8.92004 15 9.00004C15 9.00004 15 9.16004 15.16 9.40004C15.24 9.32004 15.48 9.72004 15.36 9.92004Z" fill="black"/>
                  </g>
                  <defs>
                  <clipPath id="clip0_3643_483676">
                  <rect width="16" height="16" fill="white"/>
                  </clipPath>
                  </defs>
                </svg>
                <span class="tag-name">{{ item.name }}</span>
                <SvgIcon class="icon" icon="icon-offline-close" @click.stop="delTag(index)" />
              </div>
              <div
                class="tagPop"
                :style="'left:' + state.pos.x + 'px;top:' + state.pos.y + 'px;'"
                v-show="visible"
              >
                <div class="tagMenu">
                  <div class="node" @click="handleCommand('closeAll')">关闭全部</div>
                  <div class="node" @click="handleCommand('closeLeft')">关闭左侧</div>
                  <div class="node" @click="handleCommand('closeRight')">关闭右侧</div>
                  <div class="node" @click="handleCommand('closeOther')">关闭其他</div>
                  <div class="node" @click="handleCommand('closeCurrent')">关闭当前</div>
                </div>
              </div>
            </div>
          </div>

          <transition v-if="state.tagArr.length > 0 && state.showCom" name="fade">
            <component
              ref="allComRef"
              :currentId="state.currentWorkFlowId"
              :processId="state.processId"
              :taskId="state.taskId"
              :checkTreeId="state.checkTreeId"
              :is="currentComponent"
              :key="state.checkTreeId"
              :treeList="state.treeList"
              @openLabelFn="openLabelFn"
              @changeTree="methods.getTreeList"
            />
          </transition>
          <div v-else class="cf-empty" tip="请在左侧选择作业"> </div>
        </section>
      </section>
    </section>
  </section>
</template>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 10px;
      display: flex;
      flex-direction: column;
      .cf-tree {
        padding: 8px 0;
        .row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          align-self: stretch;
        }
        .title {
          display: flex;
          padding: 6px 12px 6px 0px;
          justify-content: space-between;
          align-items: center;
          align-self: stretch;

          color: #1d2129;
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          font-style: normal;
          font-weight: bolder;
          line-height: 24px;
          &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #1e89ff;
            margin-right: 8px;
          }
        }
        .cf-tree-container {
          width: 100%;
          padding: 0 12px;
          height: calc(100% - 36px);
          .tree-box {
            height: calc(100% - 40px);
            margin-top: 8px;
          }
          :deep(.nancalui-input__inner) {
            border-right: 1px solid #e5e6eb;
          }
          .tree-box {
            :deep(.nancalui-input__inner) {
              border-right: none;
            }
          }
          :deep(.icon-search)::before {
            content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNyIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE3IDE2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTYuNzc5MyAwSDAuMjc1MzkxVjE2SDE2Ljc3OTNWMFoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTEyLjEzNjcgMTEuNzkzNUwxNS4yMzEyIDE0Ljc5MzUiIHN0cm9rZT0iIzkwOTM5OSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgPHBhdGggZD0iTTcuNDk1NjIgMTMuMDAxQzEwLjkxMzcgMTMuMDAxIDEzLjY4NDYgMTAuMzE0NyAxMy42ODQ2IDcuMDAwOThDMTMuNjg0NiAzLjY4NzI3IDEwLjkxMzcgMS4wMDA5OCA3LjQ5NTYyIDEuMDAwOThDNC4wNzc1NCAxLjAwMDk4IDEuMzA2NjQgMy42ODcyNyAxLjMwNjY0IDcuMDAwOThDMS4zMDY2NCAxMC4zMTQ3IDQuMDc3NTQgMTMuMDAxIDcuNDk1NjIgMTMuMDAxWiIgc3Ryb2tlPSIjOTA5Mzk5Ii8+Cjwvc3ZnPg==');
            display: block;
            width: 16.504px;
            height: 16px;
            cursor: pointer;
          }
        }
        .cf-icon-add {
          display: block;
          width: 16px;
          height: 16px;
          margin-right: 12px;
          cursor: pointer;
          &::before {
            content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfNzkyXzE1MTg5NykiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzYwNjI2NiIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjNjA2MjY2IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF83OTJfMTUxODk3Ij4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
            display: block;
            width: 16px;
            height: 16px;
          }
          &:hover {
            &::before {
              content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfODM2XzE1NjIwMCkiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzAwNkRFQSIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjMDA2REVBIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzAwNkRFQSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF84MzZfMTU2MjAwIj4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
            }
          }
        }
      }
      &-table {
        flex: 1;
        height: calc(100% - 60px);
        display: flex;
        gap: 10px;
        .talble-container {
          position: relative;
          height: calc(100%);
          border-radius: 0px 0px 2px 2px;
          background: var(--100, #fff);
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          flex: 1 0 0;
          align-self: stretch;
          overflow: hidden;

          .tag {
            display: flex;
            padding: 8px;
            box-sizing: border-box;
            align-items: flex-start;
            flex-shrink: 0;
            align-self: stretch;
            border-bottom: 1px solid var(---, #dcdfe6);
            background: #fff;
            width: 100%;
            overflow-x: auto;

            &-box {
              white-space: nowrap;
              width: 100%;
              display: flex
            }

            &-name {
              min-width: 40px;
              max-width: 180px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin: 0 4px;
              vertical-align: middle;
            }

            &-node {
              display: flex;
              align-items: center;
              height: 30px;
              border-radius: 2px;
              border: 1px solid #dcdfe6;
              padding: 1px 6px 1px 10px;
              align-items: center;
              gap: 4px;
              align-self: stretch;
              cursor: pointer;
              margin-right: 6px;

              &:hover,
              &.active {
                color: rgba(30, 137, 255, 1);
                border-color: rgba(194, 222, 255, 1);
                background: #ebf4ff;

                .yy-icon {
                  color: rgba(30, 137, 255, 1);
                }
              }
              img {
                width: 16px;
                vertical-align: middle;
              }
              .yy-icon {
                color: #dcdfe6;
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    .tagPop {
      position: fixed;
      top: 160px;
      left: 530px;
      z-index: 9;
      border-radius: 2px;
      background: #fff;
      box-shadow: 0 4px 8px 0 rgba(37, 43, 58, 0.2);

      .node {
        padding: 0 10px;
        line-height: 28px;
        text-align: center;
        cursor: pointer;
        font-size: 12px;

        &:hover {
          color: #1e89ff;
          background: #ebf4ff;
        }
      }
    }
  }
</style>
<style lang="scss">
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    border-radius: 4px;
    background: var(--100, #fff);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    &-item {
      width: 132px;
      display: flex;
      padding: 5px 12px;
      align-items: center;
      gap: 8px;
      flex: 1 0 0;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
