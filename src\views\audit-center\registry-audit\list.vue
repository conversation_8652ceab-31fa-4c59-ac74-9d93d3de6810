<template>
  <section class="container" ref="tableRef">
    <section class="tools">
      <div class="row">
        <div class="col">
          资产名称：
          <n-input
            v-model="state.formInline.name"
            placeholder="请输入资产名称"
            size="small"
            clearable
            @clear="onSearch(true)"
          />
          申请人：
          <n-tree-select
            v-model="state.formInline.person"
            filter
            allowClear
            placeholder="请选择"
            :leafOnly="true"
            :key="state.personOpt"
            :treeData="state.personOpt"
            :prop="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
          >
            <template #icon="{ nodeData, toggleNode }">
              <span
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '14.5px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="12"
                  height="12"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="#8a8e99"
                  />
                </svg>
              </span>
            </template>
            <template #default="{ item }">
              {{ item.name }}
            </template>
          </n-tree-select>

          申请时间：
          <n-range-date-picker-pro
            class="createTime"
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="tabs-box">
        <div class="tabs">
          <div :class="['tab', state.tabActive ? 'active' : '']" @click="tabChange(true)"
            >待审批</div
          >
          <div :class="['tab', !state.tabActive ? 'active' : '']" @click="tabChange(false)"
            >已审批</div
          >
        </div>

        <n-button v-if="state.tabActive" variant="text" color="primary" @click="allApprove('all')"
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            style="margin-right: 4px"
          >
            <path
              d="M2 12.6249L2.00173 9.64089C2.00207 9.4339 2.16992 9.26612 2.37694 9.2659C3.62715 9.2659 4.87739 9.2659 6.12762 9.2659C6.47318 9.2659 6.47135 8.95938 6.47135 8.22963C6.47135 7.49989 4.63326 6.88523 4.63326 4.31977C4.63326 1.75431 6.53742 1 8.11994 1C9.70247 1 11.4261 1.75431 11.4261 4.31977C11.4261 6.88523 9.59769 7.29312 9.59769 8.22963C9.59769 9.16611 9.59769 9.2659 9.89041 9.2659C11.1353 9.2659 12.3801 9.2659 13.625 9.2659C13.8321 9.2659 14 9.43382 14 9.64089V12.6249H2Z"
              stroke="currentColor"
              stroke-linejoin="round"
            />
            <path
              d="M2 15H14"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          一键审批</n-button
        >
      </div>

      <div class="table">
        <template v-if="state.tabActive">
          <CfTable
            actionWidth="180"
            :key="state.dataSource"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.applyTableHead"
            isNeedSelection
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #projectName="{ row }">
              {{ JSON.parse(row.objectContent).name }}
            </template>
            <template #projectCode="{ row }">
              {{ JSON.parse(row.objectContent).code }}
            </template>

            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="detail(row)">查看</n-button>
              <n-button variant="text" color="primary" @click="allApprove(row)">审批</n-button>
              <n-button variant="text" color="primary" @click="state.progressId=row.id;state.progressShow=true">进度</n-button>
            </template>
          </CfTable>
        </template>

        <template v-else>
          <CfTable
            :key="state.dataSource"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.auditTableHead"
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #projectName="{ row }">
              {{ JSON.parse(row.objectContent).name }}
            </template>
            <template #projectCode="{ row }">
              {{ JSON.parse(row.objectContent).code }}
            </template>
            <template #auditStatusName="{ row }">
              <i
                :style="{
                  background: row.auditStatusName === '审核通过' ? '#2CA340' : '#F63838',
                  width: '6px',
                  height: '6px',
                  display: 'inline-block',
                  'margin-right': '6px',
                  'border-radius': '20px',
                }"
                class="status"
              ></i>
              {{ row.auditStatusName === '审核通过' ? '通过' : '驳回' }}
            </template>
          </CfTable>
        </template>
      </div>
    </section>
  </section>

  <Progress v-if="state.progressShow" :id="state.progressId" @close="state.progressShow = false" />

  <comPop
    v-if="state.auditPopShow"
    title="资产注册审批"
    width="540"
    @onClose="onClosePop"
    @onConfirm="onConfirmAudit"
  >
    <n-form
      :data="state.auditForm"
      ref="auditformRef"
      labelSuffix="："
      label-width="100px"
      message-type="text"
      style="width: 100%"
    >
      <n-form-item field="radio" label="审批结果">
        <n-radio-group direction="row" v-model="state.auditForm.radio">
          <n-radio value="0">通过</n-radio>
          <n-radio value="1">驳回</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item field="description" label="审批备注">
        <n-textarea v-model="state.auditForm.description" rows="5" placeholder="请输入描述信息" />
      </n-form-item>
    </n-form>
  </comPop>

  <comPop
    v-if="state.auditDetailShow"
    title="资产注册详情"
    width="940"
    @onClose="state.auditDetailShow = false"
    @onConfirm="state.auditDetailShow = false"
  >
    <div class="detail-box">
      <div class="tabs-box" style="padding: 0">
        <div class="tabs">
          <div :class="['tab', state.isPreview ? 'active' : '']" @click="state.isPreview = true"
            >表结构</div
          >
          <div
            :class="['tab', !state.isPreview ? 'active' : '']"
            @click="
              () => {
                state.isPreview = false
                getDataPreview()
              }
            "
            >数据预览</div
          >
        </div>
      </div>
    </div>
    <n-form
      v-if="state.isPreview"
      :data="state.detailForm"
      labelSuffix="："
      label-width="120px"
      message-type="text"
      style="width: 100%"
    >
      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="注册申请单号">
            {{ state.detailForm.assetCode }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="注册人">
            {{ state.detailForm.registrant }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="注册部门">
            {{ state.detailForm.registrationDept }}
          </n-form-item>
        </n-col>
      </n-row>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="资产编码">
            {{ state.detailForm.assetCode }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="注册时间">
            {{ state.detailForm.registrationTime }}
          </n-form-item>
        </n-col>
      </n-row>

      <div class="detailTit"><span class="name">资产信息</span></div>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="资产名称">
            {{ state.detailForm.name }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="英文名称">
            <div class="showName" :title="state.detailForm.enName">{{
              state.detailForm.enName
            }}</div>
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="资产分类">
            {{ state.detailForm.classificationCode }}
          </n-form-item>
        </n-col>
      </n-row>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="所属业务">
            {{ state.detailForm.bizOwnership }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="资产提供方">
            {{ state.detailForm.provider }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="来源系统">
            {{ state.detailForm.sourceName }}
          </n-form-item>
        </n-col>
      </n-row>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="资产重要程度">
            {{ state.importanceLevelOpt[state.detailForm.importanceLevel] }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="发布时间">
            {{ state.detailForm.publishTime }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="资产持有部门">
            {{ state.deptOpt[state.detailForm.ownerDeptId] }}
          </n-form-item>
        </n-col>
      </n-row>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="资产管理部门">
            {{ state.deptOpt[state.detailForm.manageDeptId.toString()] }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="资产密级">
            {{ state.securityLevelOpt[state.detailForm.securityLevel] }}
          </n-form-item>
        </n-col>
        <n-col :span="8">
          <n-form-item field="radio" label="资产有效时间">
            {{ state.detailForm.thruDate }}
          </n-form-item>
        </n-col>
      </n-row>

      <n-row>
        <n-col :span="8">
          <n-form-item field="radio" label="资产估值">
            {{ state.detailForm.valuation }}
          </n-form-item>
        </n-col>
        <n-col :span="16">
          <n-form-item field="radio" label="资产附件">
            <div class="showFile">
              <p v-for="item in state.detailForm.attachments.split(',')" :key="item">{{ item }}</p>
            </div>
          </n-form-item>
        </n-col>
      </n-row>
    </n-form>
    <div style="height: 312px" v-else>
      <CfTable
        ref="tableNoRef"
        :tableConfig="{
          data: state.listPreview,
          rowKey: 'id',
        }"
        :table-head-titles="state.dataFiled"
      />
    </div>
  </comPop>
</template>

<script lang="ts" setup>
  import { assetsMenuPreview } from '@/api/assets'
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import { formartTime } from '@/utils/index'
  import comPop from '@comp/comPop/index.vue'
  import Progress from '../components/Progress.vue'
  import {
    assetsRegDetail,
    getAdhocQueryLeftListAssetsDetail,
    collectTableDetail,
  } from '@/api/dataManage.js'
  import { auditRegisterList, workFlowApproval } from '@/api/auditCenter.js'
  import api from '@/api/index.js'

  /**
   * 数据部分
   */
  const tableRef = ref()
  const tableNoRef = ref()
  const auditformRef = ref()
  const state = reactive<any>({
    dataFiled: [],
    listPreview: [],
    progressShow: false,
    progressId:'',
    isPreview: true,
    auditDetailShow: false,
    auditPopShow: false,
    tabActive: true,
    tableHeight: 500,
    formInline: {
      name: '',
      person: null,
      time: '',
    },
    applyTableHead: [
      { prop: 'projectName', name: '资产名称', slot: 'projectName' },
      { prop: 'projectCode', name: '资产编码', slot: 'projectCode' },
      { prop: 'applyTypeName', name: '审批类型' },
      { prop: 'applyByName', name: '申请人' },
      { prop: 'applyTime', name: '申请时间' },
    ],
    auditTableHead: [
      { prop: 'projectName', name: '资产名称', slot: 'projectName' },
      { prop: 'projectCode', name: '资产编码', slot: 'projectCode' },
      { prop: 'applyTypeName', name: '审批类型' },
      { prop: 'applyByName', name: '申请人' },
      { prop: 'applyTime', name: '申请时间' },
      { prop: 'auditByName', name: '审批人' },
      { prop: 'auditTime', name: '审批时间' },
      { prop: 'auditStatusName', name: '审批结果', slot: 'auditStatusName' },
    ],
    auditForm: {
      radio: '0',
      description: '',
    },
    detailForm: {},
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    personOpt: [],
    auditArr: [],
    // 资产级别
    levelOpt: {
      LEVEL_1: '一级',
      LEVEL_2: '二级',
      LEVEL_3: '三级',
    },
    // 重要程度
    importanceLevelOpt: {
      GENERAL: '普通',
      IMPORTANT: '核心',
      VITAL: '必要',
    },
    // 密级
    securityLevelOpt: {
      PUBLIC: '公开',
      INTERIOR: '内部',
      CONTROLLED: '受控',
      SECRET: '秘密',
      CONFIDENTIAL: '机密',
      CORE: '核心',
    },
    // 提供方式
    supplyModeOpt: { QUERY: '查询', DOWNLOAD: '下载', API: 'API' },
    deptOpt: [],
    personObj: {},
  })
  onMounted(() => {
    state.tableHeight = tableRef.value.clientHeight - 206
    onSearch()
    getDeptPresonFn()
    getDeptFn()
  })

  const onSearch = async () => {
    let { time, name, person } = state.formInline

    let startTime = ''
    let endTime = ''
    if (time) {
      if (time[0]) {
        startTime = formartTime(time[0])
      }
      if (time[1]) {
        endTime = formartTime(time[1], true)
      }
    }

    const params = {
      condition: {
        name,
        registrantId: person,
        endTime: endTime === null ? '' : endTime,
        startTime: startTime === null ? '' : startTime,
        status: state.tabActive ? 'WAIT' : 'DONE',
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    }

    const res = await auditRegisterList(params)
    if (res.success && res.data) {
      state.dataSource = res.data.list
      state.page.total = res.data.total
    }
  }

  const getDataPreview = () => {
    state.listPreview = []
    // state.dataFiled = []
    const { type, registerFrom } = state.detailForm
    if (isNaN(registerFrom as never)) return
    let data = {
      condition: {
        id: registerFrom,
      },
      pageNum: 1,
      pageSize: 20,
    }
    const apiFun = {
      TABLE: api.model.getDataWithProject,
      RESOURCE_DIRECTORY: assetsMenuPreview,
    }[type]

    apiFun(data)
      .then((res: any) => {
        let { success, data } = res
        if (success) {
          if (data.list && data.list.length > 0) {
            // state.dataFiled = Object.keys(data.list[0]).map((_) => ({
            //   prop: _,
            //   name: _,
            // })) as never[]
            state.listPreview = data.list
          }
          state.page.total = data?.total || 0
        }
      })
      .catch(() => {
        state.loadingPreview = false
      })
  }

  const tabChange = (stuats) => {
    state.tabActive = stuats
    onSearch()
  }

  // 审批接口
  const workFlowApprovalFn = async (data) => {
    let arr = []

    if (state.auditArr === 'all') {
      tableNoRef.value.getSelectionRows().forEach((item) => {
        arr.push({
          auditComment: state.auditForm.description,
          auditStatus: data.status,
          bizType: 'ASSETS',
          id: item.id,
        })
      })
    } else {
      arr.push({
        auditComment: state.auditForm.description,
        auditStatus: data.status,
        bizType: 'ASSETS',
        id: state.auditArr.id,
      })
    }

    const params = arr
    const res = await workFlowApproval(params)

    if (res.success) {
      data.status === 'PASS' ? ElMessage.success('审批通过') : ElMessage.success('审批驳回')
      onSearch()
    }
  }

  // 获取部门人员
  const getDeptPresonFn = async () => {
    const res = await api.project.getThreeDepartment({})
    let { success, data } = res
    if (success && data !== null) {
      state.personOpt = data

      flattenTree(data).forEach((item) => {
        state.personObj[item.id] = item.name
      })
    }
  }

  function flattenTree(tree) {
    let flattened = []
    for (let item of tree) {
      if (!item.isDepartment) {
        flattened.push(item)
      }
      if (item.children) {
        flattened = flattened.concat(flattenTree(item.children))
      }
    }
    return flattened
  }

  // 获取部门
  const getDeptFn = async () => {
    const res = await api.base.getDepartmentTrees({})
    if (res.code === 'SUCCESS') {
      state.deptOpt = {}
      res.data.children.forEach((item) => {
        const { id, label } = item
        state.deptOpt[id] = label
      })
    }
  }

  const resetFn = () => {
    state.formInline = {
      name: '',
      person: '',
      time: '',
    }
    onSearch(true)
  }

  // 资产注册详情
  const detail = async ({ objectId, objectContent }) => {
    const res: any = await assetsRegDetail({ id: objectId })
    if (res.success && res.data) {
      state.auditDetailShow = true
      state.isPreview = true
      state.detailForm = res.data

      res.data.type === 'RESOURCE_DIRECTORY'
        ? collectTableDetailFn(res.data.registerFrom)
        : getAdhocQueryLeftListAssetsDetailFn(res.data.registerFrom)
    }
  }

  const getAdhocQueryLeftListAssetsDetailFn = async (id) => {
    state.dataFiled = []
    const res = await getAdhocQueryLeftListAssetsDetail({ id })
    state.dataFiled = res.data.map((_) => ({
      prop: _.name,
      name: _.cnName,
    })) as never[]
  }

  const collectTableDetailFn = async (id) => {
    state.dataFiled = []
    const res = await collectTableDetail({ id })
    state.dataFiled = res.data.tableColumnMeta.map((_) => ({
      prop: _.colName,
      name: _.comment,
    })) as never[]
  }

  const allApprove = (type) => {
    if (tableNoRef.value.getSelectionRows().length === 0 && type === 'all') {
      ElMessage.error('请选择要审批的数据')
      return false
    }
    state.auditPopShow = true
    state.auditArr = type
  }
  const onClosePop = () => {
    state.auditPopShow = false
  }
  const onConfirmAudit = () => {
    state.auditPopShow = false

    if (state.auditForm.radio === '0') {
      workFlowApprovalFn({ status: 'PASS' })
    } else {
      workFlowApprovalFn({ status: 'REJECT' })
    }
  }

  const pageChange = (pageNum) => {
    state.page.pageNum = pageNum === 0 ? 1 : pageNum
    state.page.pageNum = pageNum
    onSearch()
  }
  const pageSizeChange = (pageSize) => {
    state.page.pageNum = 1
    state.page.pageSize = pageSize
    onSearch()
  }
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .tools {
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 10px 8px 10px 16px;
      .createTime {
        width: 260px;
        margin-right: 32px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          background: #1e89ff;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
        // border-bottom: 1px solid #c5d0ea;
      }
    }
  }

  .content {
    position: relative;
    height: calc(100% - 62px);
    margin-top: 10px;
    background: #fff;
    border-radius: 2px;
  }
  .table {
    height: calc(100% - 48px);
  }

  .tabs-box {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
    height: 48px;
    padding: 8px;

    .tabs {
      display: flex;
      align-items: center;
      align-self: stretch;
      justify-content: flex-start;
    }

    .tab {
      display: flex;
      gap: 4px;
      align-items: center;
      align-self: stretch;
      justify-content: center;
      box-sizing: content-box;
      width: 60px;
      height: 22px;
      padding: 5px 16px;
      color: #1d2129;
      font-size: 14px;
      background: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px 0px 0px 2px;
      cursor: pointer;

      &.active {
        color: #fff;
        background: #1e89ff;
        border-color: #1e89ff;
      }
    }
  }

  .detailTit {
    display: flex;
    gap: 10px;
    align-items: center;
    align-self: stretch;
    padding: 4px 0px;
    background: #f2f6fc;

    .name {
      color: var(----, rgba(0, 0, 0, 0.9));

      font-weight: 500;
      font-size: 14px;

      font-family: 'Source Han Sans CN';
      font-style: normal;
      line-height: 24px; /* 150% */

      &::before {
        display: inline-block;
        width: 3px;
        height: 16px;
        margin-right: 12px;
        vertical-align: middle;
        background: var(---, #1e89ff);
        content: '';
      }
    }
  }

  .showName {
    width: 175px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .showFile {
    width: 500px;
    white-space: pre-wrap;
    word-wrap: break-word;

    P {
      margin: 0;
    }
  }
  :deep(.nancalui-table__empty) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  :deep(.nancalui-table .nancalui-table__thead .header-container .title) {
    font-weight: 300;
  }
</style>
