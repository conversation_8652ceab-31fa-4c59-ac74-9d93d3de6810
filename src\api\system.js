import request from '@/utils/request'
import URL from '@/const/urlAddress'

// ---------------------------- 用户管理开始 -------------------------------------

// 用户列表
function userList(data) {
  return request({
    url: `${URL.AUTH}/user/list`,
    method: 'post',
    data: data,
  })
}

// 添加用户
function addUser(data) {
  return request({
    url: `${URL.AUTH}/user/add`,
    method: 'post',
    data: data,
  })
}

// 修改用户
function updateUser(data) {
  return request({
    url: `${URL.AUTH}/user/update`,
    method: 'post',
    data: data,
  })
}

// 用户详情
function userDetail(data) {
  return request({
    url: `${URL.AUTH}/user/${data.id}`,
    method: 'get',
    data: data,
  })
}

// 查询当前用户菜单权限
function functions(data) {
  return request({
    url: `${URL.AUTH}/user/functions`,
    method: 'get',
    data: data,
  })
}

// 删除用户
function deleteUser(data) {
  return request({
    url: `${URL.AUTH}/user/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}
// 用户名称验重
function checkUserName(data) {
  return request({
    url: `${URL.AUTH}/user/checkUserName`,
    method: 'post',
    data: data,
  })
}

// 用户同步
function userCenterPull(data) {
  return request({
    url: `${URL.AUTH}/user/userCenter/pull`,
    method: 'get',
  })
}
// ---------------------------- 用户管理结束 -------------------------------------

// ---------------------------- 角色管理开始 -------------------------------------

// 角色列表
function roleList(data) {
  return request({
    url: `${URL.AUTH}/role/list`,
    method: 'post',
    data: data,
  })
}

// 角色下拉列表
function roleAll(data) {
  return request({
    url: `${URL.AUTH}/role/listAll`,
    method: 'get',
    params: data,
  })
}

// 角色下拉列表
function menuTree(data) {
  return request({
    url: `${URL.AUTH}/menu/tree`,
    method: 'get',
    params: data,
  })
}
// 角色下拉列表（含按钮）
function menuBtnTree(data) {
  return request({
    url: `${URL.AUTH}/menu/tree/search`,
    method: 'post',
    data: data,
  })
}

// 添加角色
function addRole(data) {
  return request({
    url: `${URL.AUTH}/role/add`,
    method: 'post',
    data: data,
  })
}

// 修改角色
function updateRole(data) {
  return request({
    url: `${URL.AUTH}/role/update`,
    method: 'post',
    data: data,
  })
}

// 查询角色详情
function roleDetail(data) {
  return request({
    url: `${URL.AUTH}/role/${data.id}`,
    method: 'get',
    data: data,
  })
}

// 删除角色
function deleteRole(data) {
  return request({
    url: `${URL.AUTH}/role/${data.id}`,
    method: 'post',
    data: data,
  })
}
// 角色名称验重
function checkRoleName(data) {
  return request({
    url: `${URL.AUTH}/role/checkRoleName`,
    method: 'post',
    data: data,
  })
}
// 查询角色用户列表分页
function queryRoleUserListPage(data) {
  return request({
    url: `${URL.AUTH}/role/queryRoleUserListPage`,
    method: 'post',
    data: data,
  })
}
// ---------------------------- 角色管理结束 -------------------------------------
// 获取底座引擎
function getConfigEngine(data) {
  return request({
    url: `${URL.AUTH}/system/config/engine/get`,
    method: 'get',
    params: data,
  })
}

// ---------------------------- 外部链接开始 -------------------------------------
// 外部链接默认图标
function externalLinkDefaultIcon(data) {
  return request({
    url: `${URL.AUTH}/external-link/default/icons`,
    method: 'post',
    data: data,
  })
}

// 外部链接默认图标
function externalLinkDefaultIconNew(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/default/icon`,
    method: 'get',
    params: data,
  })
}

// 着陆页配置项-获取链接类型
function externalLinkDefaultType(data) {
  return request({
    url: `${URL.AUTH}/landing/page/type`,
    method: 'get',
    params: data,
  })
}

// 外部链接列表
function externalLinkSearchList(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/search`,
    method: 'post',
    data: data,
  })
}

// 外部链接列表
function externalLinkList(data) {
  return request({
    url: `${URL.AUTH}/external-link/list`,
    method: 'post',
    data: data,
  })
}

// 外部链接保存
function externalLinkSave(data) {
  return request({
    url: `${URL.AUTH}/external-link/save`,
    method: 'post',
    data: data,
  })
}
function externalLinkAdd(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/add`,
    method: 'post',
    data: data,
  })
}

// 外部链接修改
function externalLinkUpdate(data) {
  return request({
    url: `${URL.AUTH}/external-link/update`,
    method: 'post',
    data: data,
  })
}
function externalLinkUpdateNew(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/update`,
    method: 'post',
    data: data,
  })
}

// 外部链接删除
function externalLinkDelete(data) {
  return request({
    url: `${URL.AUTH}/external-link/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}
function externalLinkDeleteNew(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/remove/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 链接详情
function externalLinkDetail(data) {
  return request({
    url: `${URL.AUTH}/landing/page/config/detail/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 统一登录页列表
function loginMenuList(data) {
  return request({
    url: `${URL.AUTH}/landing/page`,
    method: 'get',
    params: data,
  })
}

// 着陆页待办事项
export const landingWait = (data) => {
  return request({
    url: `${URL.AUTH}/landing/page/wait/process`,
    method: 'post',
    data,
  })
}


// ---------------------------- 外部链接结束 -------------------------------------

// ---------------------------- 流程管理开始 -------------------------------------

// 流程管理列表
export const processManageList = (data) => {
  return request({
    url: `${URL.AUTH}/bpm/def/page`,
    method: 'post',
    data,
  })
}

// 流程管理详情
export const processManageDetail=(data)=>{
  return request({
    url: `${URL.AUTH}/bpm/def/detail`,
    method: 'get',
    params:data,
  })
}

// 流程管理获取nodecode
export const processManageGetNodeCode=(data)=>{
  return request({
    url: `${URL.AUTH}/bpm/def/generator-node-code`,
    method: 'get',
    params:data,
  })
}

// 流程管理更新
export const processManageUpdate= (data) => {
  return request({
    url: `${URL.AUTH}/bpm/def/update`,
    method: 'post',
    data,
  })
}

// 流程管理获取进度
export const processManageGetProgress=(data)=>{
  return request({
    url: `${URL.AUTH}/audit/bpm-detail`,
    method: 'get',
    params:data,
  })
}
// ---------------------------- 流程管理结束 -------------------------------------

export default {
  userList,
  addUser,
  updateUser,
  userDetail,
  functions,
  deleteUser,
  checkUserName,
  userCenterPull,

  roleList,
  roleAll,
  menuTree,
  menuBtnTree,
  addRole,
  updateRole,
  roleDetail,
  deleteRole,
  checkRoleName,
  queryRoleUserListPage,
  getConfigEngine,
  externalLinkDefaultIcon,
  externalLinkDefaultIconNew,
  externalLinkDefaultType,
  externalLinkSearchList,
  externalLinkList,
  externalLinkSave,
  externalLinkAdd,
  externalLinkUpdate,
  externalLinkUpdateNew,
  externalLinkDelete,
  externalLinkDeleteNew,
  externalLinkDetail,
  loginMenuList,
  landingWait,
  processManageList,
  processManageDetail,
  processManageGetNodeCode,
  processManageUpdate,
  processManageGetProgress
}
