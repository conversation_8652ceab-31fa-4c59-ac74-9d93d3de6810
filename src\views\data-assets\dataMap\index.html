<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <script>
      const data = {
        nodes: [],
        edges: [],
      }

      let nodes = []
      let edges = []

      for (let i = 0; i < 5000; i++) {
        nodes.push({
          x: i % 2 === 0 ? 660 : 150,
          y: i % 2 === 0 ? 150 * (i - 1) : 150 * i,
          description: '描述内容',
          label: '标题' + (i + 1),
          color: '#2196f3',
          attrs: [
            {
              key: 'id',
              type: 'number(6)',
            },
            {
              key: 'key',
              type: 'varchar(255)',
            },
            {
              key: 'gender',
              type: 'enum(M, F)',
            },
            {
              key: 'birthday',
              type: 'date',
            },
            {
              key: 'hometown',
              type: 'varchar(255)',
            },
            {
              key: 'country',
              type: 'varchar(255)',
            },
            {
              key: 'nation',
              type: 'varchar(255)',
            },
            {
              key: 'jobId',
              type: 'number(3)',
            },
            {
              key: 'phone',
              type: 'varchar(255)',
            },
            {
              key: 'deptId',
              type: 'number(6)',
            },
            {
              key: 'startTime',
              type: 'date',
            },
            {
              key: 'leaveTime',
              type: 'date',
            },
          ],

          id: 'node' + i,
          type: 'dice-er-box',
        })

        if (i % 2 === 0) {
          edges.push({ source: 'node' + (i - 1), target: 'node' + i })
        }
      }

      data.nodes = nodes
      data.edges = edges
    </script>
  </body>
</html>
