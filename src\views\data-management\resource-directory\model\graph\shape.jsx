import { Graph } from '@antv/x6'
import '@antv/x6-vue-shape'

import Entity from './shape/Entity.vue'
import Tables from './shape/Table.vue'
import Diagram from './shape/Diagram.vue'
import Inputcom from './shape/Inputcom.vue'
import Textareacom from './shape/Textareacom.vue'
import Blood from './shape/Blood.vue'

const ports = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
  },
  items: [],
}
function caclPorts() {
  let num = 1
  let items = []
  for (let i = 1; i <= num; i++) {
    const sub = i - 1 === 0 ? '' : `-${i - 1}`
    items.push({
      id: 'top' + sub,
      group: 'top',
    })
    items.push({
      id: 'right' + sub,
      group: 'right',
    })
    items.push({
      id: 'bottom' + sub,
      group: 'bottom',
    })
    items.push({
      id: 'left' + sub,
      group: 'left',
    })
  }
  return items
}
ports.items = caclPorts()

Graph.unregisterNode('nc-entity')
Graph.unregisterNode('nc-table')
Graph.unregisterNode('nc-diagram')
Graph.unregisterNode('nc-input-component')
Graph.unregisterNode('nc-pop-input')
Graph.unregisterNode('nc-blood')

export const NcEntity = Graph.registerNode('nc-entity', {
  inherit: 'vue-shape',
  x: 200,
  y: 150,
  width: 95,
  height: 60,
  component: {
    render: () => <Entity />,
  },
  ports: { ...ports },
})

export const NcTables = Graph.registerNode('nc-table', {
  inherit: 'vue-shape',
  x: 200,
  y: 150,
  width: 95,
  height: 60,
  component: {
    render: () => <Tables />,
  },
  ports: { ...ports },
})

export const NcTableChart = Graph.registerNode('nc-diagram', {
  inherit: 'vue-shape',
  x: 200,
  y: 150,
  width: 95,
  height: 60,
  component: {
    render: () => <Diagram />,
  },
})

export const NcInput = Graph.registerNode('nc-input-component', {
  inherit: 'vue-shape',
  width: 100,
  height: 40,
  component: {
    render: () => <Inputcom />,
  },
  data: {
    type: 'input',
  },
})

export const NcPopInput = Graph.registerNode('nc-pop-input', {
  inherit: 'vue-shape',
  width: 150,
  height: 80,
  component: {
    render: () => <Textareacom />,
  },
  data: {
    type: 'textArea',
  },
})

export const NcBlood = Graph.registerNode('nc-blood', {
  inherit: 'vue-shape',
  width: 200,
  height: 80,
  component: {
    render: () => <Blood />,
  },
  data: {
    label: '',
    content: '',
  },
})
