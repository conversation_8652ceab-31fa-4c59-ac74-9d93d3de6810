<template>
  <!-- 数据服务 -->
  <div class="container">
    <div class="container-main">
      <div class="entranceBox">
        <div class="entranceNode" @click="goJump('api')">
          <img class="icon" src="@img/serve/icon-api.png" alt="" />
          <button>API管理</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { useRouter } from 'vue-router'
  export default {
    name: '',
    setup() {
      const router = useRouter()
      const goJump = (name, url) => {
        router.push({ name })
      }
      return {
        goJump,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    &-main {
      position: relative;
      width: 100%;
      height: 100%;
      background: url(@img/total-bg.png) no-repeat;
      background-size: auto 100%;
      background-color: #fff;
      padding: 50px 0 50px 87px;
      box-sizing: border-box;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
      }
      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: rgb(177, 176, 176);
        border-radius: 2px;
      }
    }

    .entranceNode {
      position: relative;
      display: inline-block;
      width: 163px;
      text-align: center;
      vertical-align: middle;
      cursor: pointer;

      .icon {
        position: relative;
        transform: translateY(0);
        width: 100%;
        margin-bottom: 12px;
        transition: all 1s;
      }
      button {
        width: 127px;
        height: 47px;
        background: url(@img/total-btn.png) no-repeat;
        background-size: 100%;
        border: none;
        color: #474d66;
        font-size: 16px;
        transition: all 0.5s;
      }

      &:hover {
        .icon {
          transform: translateY(-20px);
        }
        button {
          background: url(@img/total-btn-active.png) no-repeat;
          background-size: 100%;
          color: #2c68ff;
        }
      }
    }
    .icon-arrow {
      width: 134px;
      height: 20px;
    }

    .entranceBox {
      margin-top: 43px;
      font-size: 0;
    }

    .dotLine {
      position: absolute;
      top: 169px;
      left: 158px;
      width: 72px;
    }
    .nodePosition {
      left: 136px;
    }
  }
</style>
