import { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './config/vite/plugins'
import { resolve } from 'path'
import proxy from './config/vite/proxy'
import { VITE_DROP_CONSOLE, VITE_PORT } from './config/constant'

// import { VITE_PORT } from './config/constant'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  const isBuild = command === 'build'
  return {
    base: './',
    resolve: {
      extensions: ['.vue', '.js', '.ts', '.json', '.mjs'],
      alias: [
        {
          find: '@vue-office/excel',
          replacement: '@vue-office/excel/lib/v3',
        },
        {
          find: 'vue',
          replacement: 'vue/dist/vue.esm-bundler.js',
        },
        {
          find: '@antv/g6',
          replacement: '@antv/g6/dist/g6.min.js',
        },
        {
          find: '@antv/x6',
          replacement: '@antv/x6/dist/x6.js',
        },
        {
          find: '@antv/layout',
          replacement: '@antv/layout/dist/layout.min.js',
        },
        {
          find: '@antv/x6-vue-shape',
          replacement: '@antv/x6-vue-shape/lib',
        },

        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // @/xxxx => src/xxxx
        {
          find: /\@\//,
          replacement: pathResolve('src') + '/',
        },
        // #/xxxx => types/xxxx
        {
          find: /\#\//,
          replacement: pathResolve('types') + '/',
        },
        // #/xxxx => types/xxxx
        {
          find: /\@img\//,
          replacement: pathResolve('src/assets/img') + '/',
        },
        {
          find: /\@comp\//,
          replacement: pathResolve('src/components') + '/',
        },
        {
          find: /\@icons\//,
          replacement: pathResolve('src/assets/icons') + '/',
        },
      ],
    },
    assetsInclude:['**/*.bcmap'],
    // plugins
    plugins: createVitePlugins(isBuild),

    // css
    css: {},

    // server
    server: {
      hmr: { overlay: false }, // 禁用或配置 HMR 连接 设置 server.hmr.overlay 为 false 可以禁用服务器错误遮罩层
      // 服务配置
      port: VITE_PORT, // 类型： number 指定服务器端口;
      open: true, // 类型： boolean | string在服务器启动时自动在浏览器中打开应用程序；
      cors: false, // 类型： boolean | CorsOptions 为开发服务器配置 CORS。默认启用并允许任何源
      host: '0.0.0.0', // IP配置，支持从IP启动
      proxy,
    },

    // build
    build: {
      minify: 'terser',
      target: 'chrome83',
      cssTarget: 'chrome83',
      terserOptions: {
        compress: {
          keep_infinity: true,
          drop_console: VITE_DROP_CONSOLE,
        },
      },
      rollupOptions: {
        // 确保外部化处理那些你不想打包进库的依赖
        external: [],
        // external: ['3d-force-graph'],
        input: ['./index.html', './collaborativeLink.html'],
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/css/[name]-[hash].[ext]',
        },

        // https://rollupjs.org/guide/en/#big-list-of-options
      },
      // watch: {
      //   // https://rollupjs.org/guide/en/#watch-options
      // },
      // Turning off brotliSize display can slightly reduce packaging time
      brotliSize: false,
      chunkSizeWarningLimit: 2000,
      sourcemap: true,
    },
  }
}
