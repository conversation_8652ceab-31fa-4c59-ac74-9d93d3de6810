<template>
  <div class="container">
    <div class="cf-page-title">
      {{ router.currentRoute.value.query.title }}
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>
    <div class="white-box nc-m-t-10">
      <div class="row nc-p-10">
        <n-button @click="onCreate($event)" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          添加人员
        </n-button>
      </div>
      <div class="table" v-loading="state.isLoading">
        <CfTable
          :actionWidth="120"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              onSearch(false)
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              onSearch()
            },
          }"
        >
          <template #secretLevel="{ row }">
            <span :class="['level-tag', row.secretLevel]">{{ row.secretLevelName || '--' }}</span>
          </template>
          <template #editor="{ data: { row } }">
            <div class="edit-box">
              <n-button variant="text" color="primary" @click="onDel(row)">删除</n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
    <n-modal
      v-model="state.isShow"
      title="人员授权"
      class="largeDialog authDialog"
      width="560px"
      :close-on-click-overlay="false"
      @close="
        () => {
          state.isShow = false
          state.treeSearchText = ''
        }
      "
    >
      <section class="user-body">
        <n-input
          class="asideTree-search-input"
          v-model="state.treeSearchText"
          placeholder="请输入姓名查询"
          @input="searchTreeFn"
        >
          <template #append>
            <n-button class="search-btn" icon="search" />
          </template>
        </n-input>
        <section>
          <n-tree
            ref="treeRef"
            :data="state.treeData"
            check
            :prop="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
          >
            <template #icon="{ nodeData, toggleNode }">
              <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
              <span
                v-else
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <SvgIcon
                  v-if="nodeData.expanded"
                  class="nancalui-tree-switch"
                  icon="tree-contract-new"
                />
                <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
              </span>
            </template>
          </n-tree>
        </section>
      </section>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button
            @click.prevent="
              () => {
                state.isShow = false
                state.treeSearchText = ''
              }
            "
            >取 消</n-button
          >
          <n-button v-loading="state.editLoading" variant="solid" @click.prevent="saveTreeFn"
            >确 定</n-button
          >
        </div>
      </template>
    </n-modal>
  </div>
</template>
<script setup>
  import { reactive, onMounted, getCurrentInstance } from 'vue'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const treeRef = ref()
  const state = reactive({
    classId: null,
    tableHeadTitles: [
      { prop: 'staffCode', name: '工号' },
      { prop: 'staffName', name: '姓名' },
      { prop: 'deptName', name: '部门' },
      { prop: 'secretLevel', name: '密级', slot: 'secretLevel' },
    ],
    formInline: {},
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    isLoading: false,
    tableData: [],
    isShow: false,
    editLoading: false,
    treeSearchText: '',
    treeData: [],
    selectTreeData: [], //已授权的人员列表
    treeKey: 0,
    timer: null,
  })
  function getPersonTree() {
    return api.documentManage.getPersonTree({
      categoryId: state.classId,
    })
  }

  // 删除
  async function onDel(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条授权',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.deleteAuth({ id: row.id }).then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
            onSearch(false)
          }
        })
      },
    })
  }
  // 添加人员
  function onCreate(el, search = '') {
    let params = {
      pageNum: 1,
      pageSize: 100,
      condition: {
        departmentFullId: '1',
        departmentFullName: '全部',
      },
    }
    if (search) {
      params.condition = {
        name: search,
      }
    }
    api.system.userList(params).then((res) => {
      let { success, data } = res
      if (success) {
        const tableDataIds = new Set(state.tableData.map((item) => item.staffId))
        let newList = []
        data.list.forEach((item) => {
          if (!tableDataIds.has(item.id)) {
            newList.push({ ...item, name: item.name + '-' + item.username })
          }
        })
        state.treeData = [
          {
            departmentFullName: null,
            fullId: '1',
            id: 1,
            isDepartment: true,
            job: null,
            label: null,
            name: '公司',
            username: null,
            expanded: true,
            children: newList || [],
          },
        ]

        state.isShow = true
      }
    })
  }
  // 树搜索
  function searchTreeFn() {
    //防抖
    clearTimeout(state.timer)
    state.timer = setTimeout(() => {
      onCreate('', state.treeSearchText)
    }, 300)
    // state.treeKey++
  }
  function filterTreeData(treeData, text) {
    // 使用map复制一下节点，避免修改到原树
    return treeData
      .map((node) => ({ ...node, expanded: true }))
      .filter((node) => {
        node.children = node.children && filterTreeData(node.children, text)
        return (
          String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
          String(node.username).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
          (node.children && node.children.length)
        )
      })
  }
  function updateTree(arr) {
    arr.forEach((val, index) => {
      if (val.isDepartment) {
        val.expanded = true
      } else {
        val.expanded = false
      }
      if (val.children) {
        updateTree(val.children)
      }
    })
    return arr
  }
  //确认
  function saveTreeFn() {
    const staffIdList = treeRef.value.treeFactory
      .getCheckedNodes()
      ?.filter((node) => !node.isDepartment)
      ?.map((node) => node.id)
    console.log('submit', staffIdList)
    let params = {
      categoryId: state.classId,
      staffIdList,
    }
    if (staffIdList.length > 0) {
      state.editLoading = true
      api.documentManage
        .addAuth(params)
        .then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '保存成功！',
              type: 'success',
            })
            state.isShow = false
            onSearch()
          }
        })
        .finally(() => {
          state.editLoading = false
        })
    } else {
      ElNotification({
        title: '提示',
        message: '授权人员不能为空',
        type: 'warning',
      })
    }
  }
  // 取消
  function goBack() {
    router.push({
      name: 'docClassificationList',
      query: { pagination: router.currentRoute.value.query.pagination },
    })
  }
  // 查询
  function onSearch(init = true, sortConditions) {
    if (init) {
      state.pagination.currentPage = 1
    }
    state.isLoading = true
    api.documentManage
      .getAuthList({
        pageSize: state.pagination.pageSize,
        pageNum: state.pagination.currentPage,
        condition: state.classId,
      })
      .then((res) => {
        if (res.success) {
          state.pagination.total = res.data.total
          state.tableData = res.data.list
          state.selectTreeData = []
        }
      })
      .catch(() => {
        state.tableData = []
      })
      .finally(() => {
        state.isLoading = false
      })
  }
  onMounted(() => {
    state.classId = router.currentRoute.value.query.id
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .table {
    height: calc(100vh - 236px);
    .level-tag {
      width: 40px;
      height: 20px;
      font-weight: 400;
      font-size: 12px;
      font-style: normal;
      line-height: 20px;
      text-align: center;
      border: 1px solid;
      border-radius: 2px;
      &.NONCLASSIFIED {
        color: #1aa4ee;
        background: rgba(26, 164, 238, 0.08);
        border-color: rgba(26, 164, 238, 0.4);
      }
      &.GENERAL {
        color: #fe8624;
        background: rgba(255, 244, 230, 1);
        border-color: rgba(255, 186, 112, 1);
      }
      &.IMPORTANT {
        color: #d40000;
        background: rgba(255, 237, 237, 1);
        border-color: rgba(239, 119, 119, 1);
      }
      &.CORE {
        color: #224ecd;
        background: rgba(34, 78, 205, 0.08);
        border-color: rgba(34, 78, 205, 0.4);
      }
    }
    .edit-box {
      .button-content {
        padding: 0;
        color: #1e89ff;
      }
    }
  }
  .user-body {
    position: relative;
    width: 100%;
    height: 480px;
    padding: 16px;
    overflow: hidden;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    .asideTree-search-input {
      margin-bottom: 8px;
      :deep(.nancalui-input__wrapper) {
        border: 1px solid #e5e6eb !important;
        border-radius: 2px !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }
      .search-btn {
        width: 32px;
      }
      .nancalui-input-slot__append {
        border-color: #e5e6eb;
      }
    }
    :deep(.nancalui-tree__node-vline),
    :deep(.nancalui-tree__node-hline) {
      display: none;
    }
    > section {
      width: 100%;
      height: calc(100% - 38px);
      overflow: scroll;
    }
    :deep(.nancalui-tree__node-content) {
      align-items: center;
      border-radius: 2px;

      &:hover {
        background-color: #ebf4ff;
      }

      > span {
        width: 24px;
        height: 20px;
        color: #8091b7;
        .nancalui-tree-switch {
          font-size: 16px;
        }
      }

      .nancalui-tree__node-content--value-wrapper {
        position: relative;
        width: 100%;
      }

      &.active {
        background-color: #ecf7ff;

        .nancalui-tree__node-content--value-wrapper {
          .tree-label,
          .tree-icon {
            color: #1d2129;
          }
        }

        & > span {
          svg {
            color: #1d2129;
          }
        }
      }

      .tree-icon {
        min-width: 16px;
        margin-right: 4px;
        color: #1d2129;
        font-size: 16px;
      }

      .tree-label {
        max-width: calc(100% - 20px);
        overflow: hidden;
        color: #1d2129;
        font-size: 14px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .tree-btn {
        display: none;
        width: 80px;

        .icon {
          margin-left: 4px;
          color: $themeBlue;
          font-size: 12px;
        }
      }
    }
  }
</style>
