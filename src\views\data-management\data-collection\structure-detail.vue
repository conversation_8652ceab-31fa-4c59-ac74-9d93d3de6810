<template>
  <!-- 结构化数据采集查看  -->
  <div class="data-collection-detail container">
    <div class="add-box">
      <div class="page-title">
        查看
        <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
      </div>

      <div class="box-content scroll-bar-style">
        <div class="box-content-inside">
          <div class="basic-box inside-box">
            <n-form
              ref="ruleForm"
              class="base-form disabled-form"
              :data="state.ruleForm"
              label-width="100px"
              label-align="end"
              disabled
            >
              <div class="content-box first">
                <div class="content-title">
                  <span>基础信息</span>
                </div>
                <div v-show="state.basicTitle" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="check-style" label="采集类型：" field="collectType">
                      <div class="form-detail-text">{{
                        state.ruleForm.collectType === 'STRUCTURE' ? '结构化数据' : '非结构化数据'
                      }}</div>
                    </n-form-item>
                    <n-form-item label="描述信息：">
                      <div class="form-detail-text">{{ state.ruleForm.description || '无' }}</div>
                    </n-form-item>
                  </div>

                  <div class="inline-box">
                    <n-form-item label="最小内存：">
                      <div class="form-detail-text">{{ state.ruleForm.executorMinMemory }}</div>
                    </n-form-item>
                    <n-form-item label="最大内存：">
                      <div class="form-detail-text">{{ state.ruleForm.executorMaxMemory }}</div>
                    </n-form-item>
                  </div>
                  <div class="inline-box">
                    <n-form-item label="采集任务名称：">
                      <div class="form-detail-text">{{ state.ruleForm.name }}</div>
                    </n-form-item>
                  </div>
                </div>
                <div class="content-title">
                  <span>选择表</span>
                </div>
                <div v-show="state.selectFile" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="second" field="dataSourceFeign" label="数据源名称：">
                      <div class="form-detail-text">{{ state.ruleForm.dataSourceName }}</div>
                    </n-form-item>
                    <n-form-item class="third" field="dataSourceTable" label="数据源表：">
                      <div class="form-detail-text">{{ state.ruleForm.dataSourceTable }}</div>
                      <!--                      <n-select v-model="state.ruleForm.dataSourceTable" placeholder=" ">-->
                      <!--                        <n-option-->
                      <!--                          v-for="item in state.dataSourceTableOptions"-->
                      <!--                          :key="item.value"-->
                      <!--                          :name="item.label"-->
                      <!--                          :value="item.value"-->
                      <!--                        />-->
                      <!--                      </n-select>-->
                    </n-form-item>
                  </div>
                  <div class="inline-box">
                    <n-form-item class="second" field="filterSql" label="数据过滤：">
                      <div class="form-detail-text">{{ state.ruleForm.filterSql || '--' }}</div>
                    </n-form-item>
                  </div>
                </div>
                <div class="content-title">
                  <span>表信息</span>
                </div>
                <div class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="second label-box" label="表密级：">
                      <div
                        :class="[
                          'form-detail-text',
                          'datasourceSecret',
                          secretTransition(state.ruleForm.confidentialityLevel).iconClassName,
                        ]"
                        >{{ secretTransition(state.ruleForm.confidentialityLevel).name }}</div
                      >
                    </n-form-item>

                    <n-form-item class="third label-box" field="fileName" label="标签：">
                      <div class="form-detail-text">
                        <template v-if="state.ruleForm?.tagList?.length > 0">
                          <div
                            v-for="(item, index) in state.ruleForm.tagList"
                            :key="index"
                            class="label"
                            :style="
                              'color:' +
                              item.color.split('_')[0] +
                              ';background-color:' +
                              item.color.split('_')[1] +
                              ';border-color:' +
                              (item.color.split('_').length > 2
                                ? item.color.split('_')[2]
                                : item.color.split('_')[0])
                            "
                            >{{ item.text }}</div
                          >
                        </template>
                        <span v-else>无</span>
                      </div>
                    </n-form-item>
                  </div>
                </div>
              </div>
              <!--              <div class="content-box" v-if="state.ruleForm.dataSourceTable">-->
              <!--                <div class="content-title">-->
              <!--                  <span>预览表</span>-->
              <!--                </div>-->
              <!--                <div class="bottom-line">-->
              <!--                  <n-public-table-->
              <!--                    :key="state.key"-->
              <!--                    :isDisplayAction="false"-->
              <!--                    :table-head-titles="state.tableHeadPreviewTitles"-->
              <!--                    :showPagination="false"-->
              <!--                    :tableHeight="240"-->
              <!--                    :tableData="state.tablePreviewData"-->
              <!--                  />-->
              <!--                </div>-->
              <!--              </div>-->
              <div class="content-box">
                <div class="content-title">
                  <span>设置目标表</span>
                </div>
                <div v-show="state.selectFile" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="second" field="cnName" label="模型中文名称：">
                      <div class="form-detail-text" :title="state.ruleForm.modelcName">{{
                        state.ruleForm.modelcName
                      }}</div>
                    </n-form-item>
                    <n-form-item class="third" field="name" label="模型英文名称：">
                      <div class="form-detail-text" :title="state.ruleForm.modelName">{{
                        state.ruleForm.modelName
                      }}</div>
                    </n-form-item>
                    <n-form-item label="表类型：" field="overlayOrNot">
                      <div class="overlayOrNot-box">
                        {{ state.ruleForm.overlayOrNot ? '非分区表' : '分区表' }}
                        <n-tooltip
                          class="tree-btn"
                          content="默认创建hive非分区表，取消勾选则创建分区表，不支持指定分区，系统将自动分配分区。"
                          position="top"
                          :enterable="false"
                        >
                          <SvgIcon class="illustrate" icon="icon-illustrate" />
                        </n-tooltip>
                      </div>
                    </n-form-item>
                  </div>
                </div>
                <div class="content-title">
                  <span>字段映射</span>
                </div>
                <div v-if="state.haveLinkData" class="bottom-line" v-loading="state.loading">
                  <!-- 表格逆向 -->
                  <div
                    v-show="state.ruleForm.sinkModelCreateMethod !== 'NEW' && !state.disabled"
                    class="model-reverse-box"
                    id="model-reverse-box"
                  >
                    <div class="reverse-box-left child">
                      <div
                        class="table-title"
                        :title="
                          state.ruleForm.dataSourceName +
                          (state.ruleForm.dataSourceTable
                            ? ' - ' + state.ruleForm.dataSourceTable
                            : '')
                        "
                        >数据源：{{
                          state.ruleForm.dataSourceName +
                          (state.ruleForm.dataSourceTable
                            ? ' - ' + state.ruleForm.dataSourceTable
                            : '')
                        }}</div
                      >
                      <n-table-v
                        ref="leftTableDom"
                        :columns="state.tableHeadPreviewTitles"
                        :data="state.leftData"
                        :width="state.tableWidth"
                        :header-height="40"
                        :row-height="40"
                        :height="state.tableHeight"
                        fixed
                        :key="state.key"
                        style="width: 100%"
                      >
                      </n-table-v>
                    </div>
                    <div class="reverse-box-right child">
                      <div class="table-title" :title="state.ruleForm.name"
                        >目的表：{{ state.ruleForm.name }}</div
                      >
                      <n-table-v
                        ref="leftTableDom"
                        :columns="state.tableHeadPreviewTitles1"
                        :data="state.rightData"
                        :width="state.tableWidth"
                        :header-height="40"
                        :row-height="40"
                        :height="state.tableHeight"
                        fixed
                        :key="state.key"
                        style="width: 100%"
                      >
                      </n-table-v>
                    </div>
                  </div>
                  <!-- 连线映射 -->
                  <!--                  <div class="link-mapping-box">-->
                  <!--                    <sceneTableLinkDetail ref="sceneTableLinkDetail" :oneline="true" />-->
                  <!--                  </div>-->
                </div>
                <div v-else class="table-no-content-box">
                  <div class="table-no-content">
                    <img
                      class="pic-no-conyent"
                      src="@/assets/table-no-content.png"
                      alt="暂无内容"
                    />
                    <div>暂无内容</div>
                  </div>
                </div>
                <div class="ddl">
                  <div v-if="state.ddl" class="ddl-content">
                    <n-textarea
                      v-model="state.ddl"
                      placeholder="请输入描述信息"
                      :autosize="{ minRows: 3 }"
                      readonly
                    />
                  </div>
                </div>
              </div>
            </n-form>
          </div>
          <!-- 配置信息 -->
          <div class="content-box">
            <div class="content-title">
              <span>调度信息</span>
            </div>
            <div class="content-box-view">
              <configureSchedulingRulesDetail
                v-show="state.dispatch"
                ref="configureSchedulingRulesDom"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import * as sqlFormatter from 'sql-formatter'
  import { collectJobDetail, collectJobDDL } from '@/api/dataManage'
  import configureSchedulingRulesDetail from './components/configure-scheduling-rules-detail'
  import sceneTableLinkDetail from './scene-table-link/detail'

  export default {
    name: '',
    components: {
      configureSchedulingRulesDetail,
      sceneTableLinkDetail,
    },
    props: {},
    setup() {
      const router = useRouter()
      const configureSchedulingRulesDom = ref()
      const ruleForm = ref()

      const sceneTableLinkDetail = ref()
      const state = reactive({
        basicTitle: true,
        selectFile: true,
        dispatch: true,
        ddl: '',
        ruleForm: {
          name: '',
          overlayOrNot: true,
          filterSql: '', // 过滤
          description: '',
          collectType: 'STRUCTURE',
          dataSourceFeign: '', //绑定的数据源id
          dataSourceTable: '', //数据源id下表名
          sinkModelCreateMethod: 'NEW',
          modeTable: '', //原始数据层下模型id
          modeFeign: '', //原始数据层id
          modeFeignName: '', //原始数据层name
          modelcName: '', // 模型中文名
          modelName: '', // 模型英文名
          dataSourceName: '',
          datasourceType: '',
          confidentialityLevel: '',
          tagList: [], // 标签
        },
        tableHeadPreviewTitles: [
          { prop: 'number', dataKey: 'number', title: '序号', name: '序号', width: 80 },
          {
            prop: 'comment',
            dataKey: 'comment',
            title: '字段中文名',
            name: '字段中文名',
            width: 100,
          },
          {
            prop: 'colName',
            dataKey: 'colName',
            title: '字段英文名',
            name: '字段英文名',
            width: 100,
          },
          {
            prop: 'convertFileType',
            dataKey: 'convertFileType',
            title: '字段类型',
            name: '字段类型',
            width: 100,
          },
          { prop: 'length', dataKey: 'length', title: '字段长度', name: '字段长度', width: 100 },
        ],
        tableHeadPreviewTitles1: [
          { prop: 'sortNum', dataKey: 'sortNum', title: '序号', name: '序号', width: 80 },
          {
            prop: 'cnName',
            dataKey: 'cnName',
            title: '字段中文名',
            name: '字段中文名',
            width: 100,
          },
          { prop: 'name', dataKey: 'name', title: '字段英文名', name: '字段英文名', width: 100 },
          {
            prop: 'fieldType',
            dataKey: 'fieldType',
            title: '字段类型',
            name: '字段类型',
            width: 100,
          },
          {
            prop: 'fieldLength',
            dataKey: 'fieldLength',
            title: '字段长度',
            name: '字段长度',
            width: 100,
          },
        ],
        tablePreviewData: { list: [] },
        dataSourceTableOptions: [],
        leftData: [],
        rightData: [],
        dataSourceNames: [],
        modelNames: [],
        totalData: {},
        loading: false,
        allData: {},
        editId: null,
        haveLinkData: true, //有映射内容
        key: 1,
        tableWidth: 500,
        tableHeight: 300,
      })

      const methods = {
        // 获取元素信息
        getTableInfo() {
          state.tableWidth = (document.body.offsetWidth - 312) / 2
        },
        expandTitleFn(name) {
          state[name] = !state[name]
        },
        closeFn() {
          router.push({
            name: 'dataCollectionIndex',
            query: {},
          })
        },
        // 获取ddl
        ddlFn() {
          let columnMapping = state.allData.columnMapping.map((val) => {
            return {
              sourceCode: val.sourceColumnName,
              sourceDataLength: val.sourceColumnLength,
              sourceDataType: val.sourceColumnType,
              sourceName: val.sourceName || null,
              sourceOrderNum: val.sinkSortNum,
              targetCode: val.sinkMetaCode,
              targetDataLength: val.sinkMetaLength,
              targetDataType: val.sinkMetaType,
              targetName: val.sourceName,
              targetOrderNum: val.sinkSortNum,
            }
          })
          let ddlBO = {
            name: state.ruleForm.modelName,
            overlayOrNot: state.ruleForm.overlayOrNot,
            mappingList: columnMapping,
          }
          collectJobDDL(ddlBO).then((res) => {
            if (res.success) {
              // state.ddl = sqlFormatter.format(res.data, { language: 'hive' })
              state.ddl = res.data
            }
          })
        },
        //匹配密级
        secretTransition(status) {
          let name = '公开'
          let iconClassName = 'blue1'

          switch (status) {
            case 'INTERIOR':
              name = '内部'
              iconClassName = 'yellow'
              break
            case 'CONTROLLED':
              name = '受控'
              iconClassName = 'blue2'
              break
            case 'SECRET':
              name = '秘密'
              iconClassName = 'red2'
              break
            case 'CONFIDENTIAL':
              name = '机密'
              iconClassName = 'red1'
              break
            case 'CORE':
              name = '核心'
              iconClassName = 'blue3'
              break
            default:
              break
          }

          return { name, iconClassName }
        },
        //数据结构化采集编辑初始化
        editGetData() {
          collectJobDetail({ id: state.editId })
            .then((res) => {
              let { data, success } = res
              if (success) {
                let { name, description, mappingList } = data
                state.ruleForm.overlayOrNot = data.overrides || false
                state.ruleForm.filterSql = data.filterSql || ''
                state.ruleForm.name = name || '无'
                state.ruleForm.description = description || '无'
                let newData = {
                  tagList:
                    data?.dataTag?.map((val) => {
                      return { name: val.text, color: val.color, value: val.color }
                    }) || [],
                  description: data.description || null,
                  sinkModelEname: data.destinationEntry || null,
                  sinkModelCname: data.destinationEntryName || null,
                  id: data.id,
                  columnMapping: mappingList.map((val) => {
                    return {
                      sourceColumnName: val.sourceCode,
                      sourceColumnLength: val.sourceDataLength,
                      sourceColumnType: val.sourceDataType,
                      sourceName: val.targetName || null,
                      sinkSortNum: val.sourceOrderNum,
                      sinkMetaCode: val.targetCode,
                      sinkMetaLength: val.targetDataLength,
                      sinkMetaType: val.targetDataType,
                    }
                  }),
                  name: data.name || null,
                  overlayOrNot: data.overrides,
                  filterSql: data.filterSql || '',
                  collectWay: data.collectWay || 'FULL',
                  collectRule: data.schedulingConfigInfo?.collectWay,
                  retryCount: data.schedulingConfigInfo?.reRunTimes,
                  retryTime: data.schedulingConfigInfo?.reRunInterval,
                  collectType: 'STRUCTURE',
                  schedule: {
                    schedulingStrategy: data.schedulingConfigInfo?.schedulingStrategy,
                    periodicDeclarationMode: data.schedulingConfigInfo?.periodicDeclarationMode,
                    cron:
                      data.schedulingConfigInfo?.periodicDeclarationMode === 'CRON_EXPRESSION'
                        ? data.schedulingConfigInfo?.cron
                        : null,
                    fromDateTime: data.schedulingConfigInfo?.effectiveFromTime || null,
                    thruDateTime: data.schedulingConfigInfo?.effectiveThruTime || null,
                    period: data.schedulingConfigInfo?.timeUnit || 'HOUR',
                    extent: data.schedulingConfigInfo?.timeInterval || null,
                    rateTime: data.schedulingConfigInfo?.timePoint || null,
                  },
                  confidentialityLevel: data.securityLevel || null,
                  sourceTableName: data.sourceEntry || null,
                  dataSourceId: data.sourceId || null,
                  sinkModelCreateMethod: 'REVERSE',
                }
                state.allData = newData
                state.ruleForm.dataSourceFeign = data.sourceId
                state.ruleForm.confidentialityLevel = data.securityLevel
                state.ruleForm.modelcName = data.destinationEntryName || '无'
                state.ruleForm.modelName = data.destinationEntry || '无'
                state.ruleForm.executorMinMemory = data.executorMinMemory || '2G'
                state.ruleForm.executorMaxMemory = data.executorMaxMemory || '2G'
                // state.ruleForm.modeTable = sinkModelId
                state.ruleForm.sinkModelCreateMethod = 'REVERSE'
                state.ruleForm.tagList = data.dataTag
                state.ruleForm.dataSourceTable = data.sourceEntry
                // methods.getSourceTables(data.sourceId, function () {
                //   state.ruleForm.dataSourceTable = data.sourceEntry
                // })

                let _tableData = []
                mappingList?.forEach((item, index) => {
                  _tableData.push({
                    number: index + 1,
                    cnName: item.sourceName,
                    name: item.targetCode,
                    fieldType: item.targetDataType,
                    fieldLength: item.targetDataLength,
                    isPass: true,
                    sourceColumnName: item.sourceCode,
                    sourceColumnType: item.sourceDataType,
                    sourceColumnLength: item.sourceDataLength,
                  })
                })

                state.allData.tableData = _tableData
                state.loading = false
                methods.ddlFn()
                methods.getDatasourceFeignList(data.sourceId) //获取数据源相关数据
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 编辑初始化数据-映射
        async detaileInitCanvas(data) {
          if (data) {
            state.totalData = data
            let { dataSourceName, sourceTableName, sinkModelEname, layerName, columnMapping } = data

            state.dataSourceNames = [dataSourceName, sourceTableName]
            state.modelNames = [layerName || '原始数据层', sinkModelEname]

            await api.dataManagement
              .getSourceStructure({
                id: data.dataSourceId,
                tableName: data.sourceTableName,
              })
              .then((res1) => {
                // 渲染左边表格
                // res1.data.forEach((val) => {
                //   val.dataType = val.convertFileType
                // })
                if (res1.data.length > 0) {
                  // let tableHeadPreviewTitles = []
                  // Object.keys(res1.data[0]).forEach((key) => {
                  //   tableHeadPreviewTitles.push({
                  //     prop: key,
                  //     dataKey: key,
                  //     name: key,
                  //     title: key,
                  //   })
                  // })
                  // state.tableHeadPreviewTitles = tableHeadPreviewTitles
                  state.tablePreviewData.list = res1.data
                  state.key++
                }
                res1.data.forEach((val, ind) => {
                  val.number = ind
                })
                state.leftData = res1.data
              })

            // await api.model.getModeData({ id: data.destinationId }).then((res2) => {
            //   // 渲染右边表格
            //   state.rightData = res2.data
            // })
            let rightData = []
            columnMapping.forEach((val) => {
              rightData.push({
                sortNum: val.sinkSortNum,
                name: val.sinkMetaCode,
                fieldType: val.sinkMetaType,
                fieldLength: val.sinkMetaLength,
                cnName: val.sourceName,
              })
            })
            state.rightData = rightData
            state.loading = false

            data.graphData = {
              leftData: state.leftData,
              rightData: state.rightData,
              dataSourceNames: state.dataSourceNames,
              modelNames: state.modelNames,
            }
            data.allEdge = data.columnMapping

            if (state.leftData?.length || state.rightData?.length) {
              // nextTick(() => {
              //   sceneTableLinkDetail.value?.changTable(data.graphData, function (that) {
              //     let { methods } = that
              //     // 连线
              //     if (state.totalData.allEdge) {
              //       methods.addEdge({ data: state.totalData.allEdge, initLater: true })
              //     }
              //   })
              // })
            } else {
              state.haveLinkData = false
            }
          }
        },
        // 获取数据源-列表
        getDatasourceFeignList(id) {
          api.dataManagement.getDatasourceFeignList({ includeNonInvertible: false }).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.id === id) {
                state.ruleForm.dataSourceName = item.name
                state.ruleForm.datasourceType = item.datasourceType
              }
            })
            state.dataSourceOptions = data
            state.tableData = {}
            state.leftData = []
            state.allData.dataSourceName = state.ruleForm.dataSourceName
            state.allData.dataSourceType = state.ruleForm.datasourceType
            methods.detaileInitCanvas(state.allData)

            configureSchedulingRulesDom.value.init(state.allData)
          })
        },
        // 根据数据源下拉层获取表数据
        getSourceTables(data, callBack) {
          api.dataManagement.getSourceTables({ dataSourceId: data }).then((res) => {
            let { data, success } = res
            if (success) {
              let _dataSourceTableOptions = []
              data.forEach((item) => {
                _dataSourceTableOptions.push({
                  label: item,
                  value: item,
                })
              })
              state.dataSourceTableOptions = _dataSourceTableOptions
              callBack()
            }
          })
        },

        // 获取模型层下拉列表
        async getDataModelTree() {
          let result = await api.model.getDataModelTree().then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.name === '原始数据层') {
                state.ruleForm.modeFeign = item.id
              }
            })
            state.modeFeignOptions = data
            let a = methods.modeFeignChange({ value: state.ruleForm.modeFeign })
            return a
          })
          return result
        },
        // 模型层-change
        async modeFeignChange(data) {
          let _data = state.modeFeignOptions.filter((item) => {
            return item.id === data.value
          })
          state.ruleForm.modeFeignName = _data[0].label
          let params = { layerId: state.ruleForm.modeFeign, jobType: 'COLLECT_JOB' }
          if (state.editId) {
            params.jobId = state.editId
          }
          let result = await api.model.getModelListWithLayerId(params).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.cnName = item.cnName
              item.value = item.id
              item.label = item.name
            })
            state.oldModeTableOptions = data
            state.modeTableOptions = data
            return { modeTableOptions: state.modeTableOptions }
          })
          return result
        },

        // 取消
        cancel() {
          router.go(-1)
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId
        if (state.editId) {
          // methods.getDataModelTree() //获取原始数据层相关数据
          methods.editGetData() //获取采集任务详情
        }
        nextTick(() => {
          methods.getTableInfo()
        })
        window.addEventListener('resize', () => {
          methods.getTableInfo()
        })
      })
      onBeforeUnmount(() => {
        window.removeEventListener('resize', () => {
          methods.getTableInfo()
        })
      })

      return {
        state,
        ruleForm,

        configureSchedulingRulesDom,
        sceneTableLinkDetail,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-detail {
    height: 100%;
    padding: 12px;
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 16px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 60px);
        margin-top: 8px;
        overflow: auto;
        .content-box {
          margin-top: 10px;
          padding: 16px 0;
          background-color: #fff;
          border-radius: 2px;
          &.first {
            margin-top: 0;
          }
          &-view {
            padding: 0 16px;
          }
        }
        .content-title {
          position: relative;
          height: 30px;
          margin-bottom: 16px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;
          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
        .inside-box {
          .box-title {
            height: 48px;
            color: #000;
            font-weight: bolder;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            line-height: 48px;
            border-bottom: 1px solid #ebedf0;

            span {
              position: relative;
              width: max-content;
            }

            i {
              position: absolute;
              right: 0;
              bottom: -2px;
              left: 0;
              height: 6px;
              background: #008ae7;
              opacity: 0.2;
            }
          }
        }
        .bottom-line {
          margin-top: 12px;
          .model-reverse-box {
            display: flex;
            height: 370px;
            padding: 16px 16px 0 16px;
            //padding: 16px 20px;
            //border: 1px solid #ebedf0;
            border-radius: 8px;

            .child:not(:last-child) {
              padding-right: 20px;
            }
            .child:last-child {
              padding-left: 20px;
            }
            .child {
              width: 50%;

              .table-title {
                margin-bottom: 8px;
                overflow: hidden;
                color: #000000;
                font-weight: bolder;
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC;
                line-height: 20px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
            :deep(.nancalui-table-v) {
              .nancalui-table-v-header__header {
                .nancalui-table-v__header-cell {
                  flex: 1 !important;
                  flex-shrink: 0;
                  width: auto;
                  &:first-of-type {
                    flex: none !important;
                    flex-shrink: 0 !important;
                    width: 80px !important;
                  }
                }
              }
              .nancalui-table-v__body {
                .nancalui-table-v__row {
                  .nancalui-table-v__row-cell {
                    flex: 1 !important;
                    width: auto;
                    &:first-of-type {
                      flex: none !important;
                      flex-shrink: 0 !important;
                      width: 80px !important;
                    }
                  }
                  .table-v-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    &-col {
                      flex: 1;
                      flex-shrink: 0;
                      box-sizing: border-box;
                      padding: 0 8px;
                      &:first-of-type {
                        flex: none;
                        width: 80px !important;
                      }
                    }
                  }
                }
              }
            }
          }
          .reverse-box-left {
            :deep(.nancalui-table) {
              .nancalui-table__thead tr th.is-left:first-child .header-container {
                width: max-content;
                padding-left: 16px !important;
              }
            }
          }
          .link-mapping-box {
            height: 388px;
          }
        }
        .base-form {
          .form-detail-text {
            width: calc(100% - 100px);
            margin-right: 8px;
            overflow: hidden;
            color: #1a1a1a;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
            .label {
              box-sizing: border-box;
              height: 20px;
              margin-right: 4px;
              padding: 0 4px;
              color: #1f84e1;
              font-size: 12px;
              line-height: 18px;
              background-color: #edf5fd;
              border: 1px solid #1f84e1;
              border-radius: 2px;
            }
            &.datasourceSecret {
              box-sizing: border-box;
              width: 40px;
              height: 20px;
              margin-top: 6px;
              color: rgba(0, 0, 0, 0.75);
              font-size: 12px;
              line-height: 18px;
              text-align: center;
              background-color: #f4f4f5;
              border: 1px solid rgba(177, 179, 184, 0.53);
              border-radius: 2px;
            }
            &.yellow {
              color: #ffba70;
              background-color: #fff4e6;
              border: 1px solid #ffba70;
            }
            &.blue1 {
              color: rgba(26, 164, 238, 0.4);
              background-color: rgba(26, 164, 238, 0.08);
              border: 1px solid rgba(26, 164, 238, 0.4);
            }
            &.blue2 {
              color: #99c9ff;
              background-color: #ebf4ff;
              border: 1px solid #99c9ff;
            }
            &.blue3 {
              color: rgba(34, 78, 205, 0.4);
              background-color: rgba(34, 78, 205, 0.08);
              border: 1px solid rgba(34, 78, 205, 0.4);
            }
            &.red1 {
              color: rgba(122, 0, 0, 0.4);
              background-color: rgba(122, 0, 0, 0.08);
              border: 1px solid rgba(122, 0, 0, 0.4);
            }
            &.red2 {
              color: #ef7777;
              background-color: #ffeded;
              border: 1px solid #ef7777;
            }
          }
          .nancalui-textarea__div {
            width: 910px !important;
          }
          .inline-box {
            display: flex;
            flex-wrap: wrap;
            .nancalui-form__item--horizontal {
              width: 40%;
              &.label-box {
                align-items: flex-start;
                .form-detail-text:not(.datasourceSecret) {
                  padding-top: 6px;
                  .label {
                    display: inline-block;
                    margin-right: 6px;
                    margin-bottom: 6px;
                  }
                }
              }
            }
            .overlayOrNot-box {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              .illustrate {
                margin-left: 4px;
                color: #909399;
              }
            }
          }
          .check-style {
            align-items: flex-start;
            .form-detail-text {
              height: 32px;
              line-height: 32px;
            }
            .inline {
              display: flex;
              align-items: baseline;
              div {
                width: max-content;
              }
            }
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
        .ddl {
          &-content {
            min-height: 200px;
            margin: 0 16px;
            padding: 8px;
            border: 1px solid #a3b4db;
            border-radius: 6px;
            :deep(.nancalui-textarea__div) {
              .nancalui-textarea {
                padding: 0;
                color: rgba(0, 0, 0, 0.75);
                background-color: transparent;
                border: 1px solid #fff;
                cursor: default;
                &:hover {
                  border: 1px solid #fff !important;
                  box-shadow: none !important;
                }
              }
            }
          }
        }
      }
    }
  }
  .table-no-content-box {
    height: 300px;
    margin: 11px 0 11px 0;
    padding: 16px 20px;
    text-align: center;
    border: 1px solid #ebedf0;
    border-radius: 8px;
    .table-no-content {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      .pic-no-conyent {
        width: 140px;
      }
      div {
        margin-top: 15px;
        color: #999999;
        font-size: 12px;
        line-height: normal;
      }
    }
  }
  :deep(.nancalui-table-v) {
    .nancalui-table-v__main {
      background-color: #fff;
    }
    .nancalui-table-v-header__header {
      background-color: #ebf4ff;
      .nancalui-table-v__header-cell {
        background-color: #ebf4ff;
      }
    }
    .nancalui-table-v__header-cell-text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
