<template>
  <n-drawer
    v-model="state.visiable"
    class="node-info-drawer"
    :z-index="1000"
    size="1000"
    title=""
    @close="onClose(false)"
  >
    <div class="nc-p-16">
      <n-tabs v-model="state.activeTab" @active-tab-change="tabChange">
        <n-tab :id="item.id" :key="item.id" :title="item.name" v-for="item in state.tabs" />
      </n-tabs>
      <div style="overflow: auto; height: calc(100vh - 130px)">
        <div v-if="state.activeTab === '1'">
          <div class="col-content-row nc-p-10">
            <div class="col-content-row-item">
              <div class="col-content-row-item-content"> 2024-12-17 </div>
              <div class="col-content-row-item-title" unit-text=""> 更新时间 </div>
              <SvgIcon icon="new-service-table-icon" class="service-count-icon" />
            </div>
            <div class="col-content-row-item">
              <div class="col-content-row-item-content"> 89276 </div>
              <div class="col-content-row-item-title" unit-text=""> 数据条数 </div>
              <SvgIcon icon="new-service-count-icon" class="service-count-icon" />
            </div>
            <div class="col-content-row-item">
              <div class="col-content-row-item-content"> 3 </div>
              <div class="col-content-row-item-title" unit-text=""> 影响指标 </div>
              <SvgIcon icon="new-service-use-icon" class="service-count-icon" />
            </div>
            <div class="col-content-row-item">
              <div class="col-content-row-item-content" unit-text="(条)"> 5 </div>
              <div class="col-content-row-item-title"> 服务接口</div>
              <SvgIcon icon="new-service-datacount-icon" class="service-count-icon" />
            </div>
          </div>
          <div class="content-box-row col-1">
            <div class="col">
              <div class="title"> 质量变化趋势 </div>
              <div class="col-content">
                <div id="qualityChangeTrend"></div>
              </div>
            </div>
          </div>
          <div class="content-box-row col-1 nc-m-t-16">
            <div class="col">
              <div class="title"> 数据量变化趋势 </div>
              <div class="col-content">
                <div id="dataChangeTrend"></div>
              </div>
            </div>
          </div>
        </div>
        <n-form :data="state.formData" class="nc-p-16" v-else-if="state.activeTab === '2'">
          <div class="nc-m-b-16 nc-flex" style="font-size: 22px">
            <svgIcon icon="icon-card-rerun" class="nc-m-r-8" />
            <svgIcon icon="icon-ddl" />
            <div class="nc-right">
              <n-select
                v-model="state.formData.version"
                :options="state.versions"
                placeholder="请选择版本"
              />
            </div>
          </div>
          <n-form-item field="code">
            <n-textarea
              v-model="state.formData.code"
              resize="none"
              class="script-textarea scroll-bar-style"
              color="primary"
              placeholder="请输入SQL语句"
            />
          </n-form-item>
        </n-form>
        <div v-else-if="state.activeTab === '3'" class="nc-p-10">
          <CfTable
            actionWidth="0"
            :table-head-titles="state.tableHeadTitles"
            :tableConfig="{
              data: state.tableData,
              rowKey: 'id',
            }"
          />
        </div>
        <div v-else-if="state.activeTab === '4'">
          <n-form label-width="120px" label-align="start" message-type="text" labelSuffix="：">
            <n-form-item label="表英文名">
              <span>USERINFO</span>
            </n-form-item>
            <n-form-item label="表中文名">
              <span>用户信息表</span>
            </n-form-item>
            <n-form-item label="密级">
              <span>内部 </span>
            </n-form-item>
            <n-form-item label="标签">
              <div class="nc-flex">
                <level-tag bgColor="#F4FFEB" borderColor="#A8ED83" color="#00C700">用户</level-tag>
                <level-tag class="nc-m-l-8" bgColor="#FFFBE4" borderColor="#FFE481" color="#FFA900"
                  >基础</level-tag
                >
                <level-tag class="nc-m-l-8" bgColor="#E2F8FF" borderColor="#7BD7FF" color="#0093FF"
                  >公共</level-tag
                >
              </div>
            </n-form-item>
            <n-form-item label="数据条数">
              <span>20000 </span>
            </n-form-item>
            <n-form-item label="更新时间">
              <span>2024-12-17 14:32:12 </span>
            </n-form-item>
          </n-form>
        </div>
      </div>
    </div>
    <div class="container-footer">
      <div class="my-appliction-right">
        <template v-if="state.activeTab === '2'">
          <n-button style="margin-left: 8px" plain @click="onClose()">取消</n-button>
          <n-button :loading="loading" variant="solid" @click="onClose(true)">提交</n-button>
        </template>
        <n-button v-else :loading="loading" variant="solid" @click="onClose()">关闭</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import * as echarts from 'echarts'
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    activeId: { type: String, default: '' },
  })
  const emit = defineEmits(['update:modelValue', 'sure'])
  const state = reactive({
    tabs: [
      { id: '1', name: '数据视图' },
      { id: '2', name: '数据加工' },
      { id: '3', name: '数据预览' },
      { id: '4', name: '基本信息' },
    ],
    activeTab: '',
    visiable: false,
    formData: {
      code: `SELECT p.product_id,

       p.product_name,

            SUM(s.quantity * s.unit_price) AS total_sales

      FROM products p

      JOIN sales s ON p.product_id = s.product_id

      GROUP BY p.product_id, p.product_name;`,
      version: 'v3',
    },
    versions: ['v1', 'v2', 'v3'],
    tableHeadTitles: [
      { prop: 'id', name: 'ID(序号)' },
      { prop: 'name', name: 'NAME(名称)' },
      { prop: 'age', name: 'AGE(年龄)' },
    ],
    tableData: [
      {
        id: 1,
        name: '张三',
        age: 18,
      },
      {
        id: 2,
        name: '李四',
        age: 20,
      },
      {
        id: 3,
        name: '王五',
        age: 22,
      },
    ],
  })
  function onClose(sure = false) {
    emit('update:modelValue', false)
    if (sure) {
      emit('sure')
    }
  }
  //获取质量变化趋势
  function getQualityTrends() {
    nextTick(() => {
      const chartDom = document.getElementById('qualityChangeTrend')
      console.log('获取质量变化趋势', chartDom)
      const myChart = echarts.init(chartDom)
      const option = {
        xAxis: {
          type: 'category',
          data: [
            '01-01',
            '01-02',
            '01-03',
            '01-04',
            '01-05',
            '01-06',
            '01-07',
            '01-08',
            '01-09',
            '01-10',
            '01-11',
            '01-12',
          ],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260, 30, 40, 50, 60, 70],
            type: 'line',
          },
        ],
      }
      myChart.setOption(option)
    })
  }
  //获取数据量变化趋势
  function getDataTrends() {
    nextTick(() => {
      const chartDom = document.getElementById('dataChangeTrend')
      const myChart = echarts.init(chartDom)
      const option = {
        xAxis: {
          type: 'category',
          data: [
            '01-01',
            '01-02',
            '01-03',
            '01-04',
            '01-05',
            '01-06',
            '01-07',
            '01-08',
            '01-09',
            '01-10',
            '01-11',
            '01-12',
          ],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [150, 2300, 224, 2180, 135, 1470, 2600, 30, 40, 500, 60, 70],
            type: 'line',
          },
        ],
      }
      myChart.setOption(option)
    })
  }
  function tabChange() {
    if (state.activeTab === '1') {
      getQualityTrends()
      getDataTrends()
    }
  }
  watch(
    () => props.modelValue,
    (val) => {
      state.visiable = val
      state.activeTab = props.activeId
      tabChange()
    },
  )
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .col-content-row {
    display: flex;
    flex-wrap: wrap;
    &-item {
      position: relative;
      display: flex;
      flex: 1 0 0;
      flex-direction: column-reverse;
      align-items: left;
      justify-content: center;
      height: 96px;
      padding: 16px 24px;
      border-radius: 6px;
      + .col-content-row-item {
        margin-left: 4px;
      }
      &::before {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        display: block;
        width: 32px;
        height: 32px;
        margin: auto;
      }
      &:nth-child(1) {
        background: var(---, #2a99ff);
      }
      &:nth-child(2) {
        background: var(---, #09bfa1);
      }
      &:nth-child(3) {
        background: var(---, #ff9e42);
      }
      &:nth-child(4) {
        background: var(---, #909399);
      }
      &:nth-child(5) {
        background: var(---, #31b046);
      }
      &-title {
        color: #fff;
        font-weight: 500;
        font-size: 16px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 26px;
      }
      &-content {
        color: #fff;
        font-weight: 500;
        font-size: 30px;
        font-family: 'DIN Alternate';
        font-style: normal;
        line-height: 40px;
        &[unit-text]::after {
          color: #fff;
          font-weight: 400;
          font-size: 16px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 24px;
          content: attr(unit-text);
        }
      }
    }
    .service-count-icon {
      position: absolute;
      right: 16px;
      bottom: 12px;
      font-size: 32px;
      cursor: default;
    }
  }
  .col {
    box-sizing: border-box;
    overflow: hidden;
    background-color: #fff;
    border-radius: 4px;
    .title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 52px;
      padding: 0 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bolder;
      font-size: 18px;
      border-bottom: 1px solid #dcdfe6;

      &:before {
        position: absolute;
        top: 17px;
        left: 0;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
      span {
        margin-left: 8px;
        color: rgba(0, 0, 0, 0.55);
        font-weight: normal;
        font-size: 14px;
      }
    }
    &-content {
      height: 300px;
      #qualityChangeTrend,
      #dataChangeTrend {
        width: 100%;
        height: 100%;
      }
    }
  }
  :deep(.nancalui-form__item--horizontal) {
    &.description-textarea {
      width: 1060px;
    }
    .script-textarea {
      height: calc(100vh - 200px);
      &::placeholder {
        line-height: 363px;
        text-align: center;
      }
    }
  }
</style>
