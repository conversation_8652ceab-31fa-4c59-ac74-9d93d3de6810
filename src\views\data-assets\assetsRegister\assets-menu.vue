<template>
  <section class="template-con-flex" ref="tableRef">
    <div class="left nc-p-t-10 asideTree">
      <div class="template-list-title nc-flex">
        <div>资源目录</div>
      </div>
      <div class="class-list nc-m-t-10">
        <n-input
          class="class-list-tree-ipt"
          v-model="tableState.treeSearchText"
          placeholder="请输入关键词"
          suffix="search"
          @input="(val) => treeRef.treeRef.filter(val)"
        />
        <div class="nc-m-t-8 class-tree">
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :filter-node-method="filterNode"
            :default-expanded-keys="classState.expandedKeys"
            :current-node-key="classState.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="classState.data"
            @node-click="clickFn"
          />
        </div>
      </div>
    </div>
    <div class="right">
      <CfTable
        v-show="tableState.tabActive === 'menu'"
        actionWidth="180"
        v-loading="tableState.isLoad"
        :key="tableState.tableList"
        ref="tableNoRef"
        :tableConfig="{
          data: tableState.tableList,
          rowKey: 'id',
        }"
        :table-head-titles="tableState.formTableHead"
        :paginationConfig="{
          total: tableState.pagination.total,
          pageSize: tableState.pagination.pageSize,
          currentPage: tableState.pagination.currentPage,
          onCurrentChange: (v) => {
            tableState.pagination.currentPage = v
            onSearch()
          },
          onSizeChange: (v) => {
            tableState.pagination.pageSize = v
            onSearch()
          },
        }"
      >
        <template #tableType="{ row }">
          {{ row.tableType === 'TABLE' ? '表' : '视图' }}
        </template>
        <template #assetsRegistered="{ row }">
          <i
            :style="{
              background: registerStatusColor(row.registerStatus),
              width: '6px',
              height: '6px',
              display: 'inline-block',
              'margin-right': '6px',
              'border-radius': '20px',
            }"
            class="status"
          ></i
          >{{ registerStatus(row.registerStatus) }}
        </template>

        <template #editor="{ data: { row } }">
          <n-button variant="text" color="primary" @click="goJump('assetsRegAssetDetail', row)"
            >查看</n-button
          >
          <n-button
            v-if="row.registerStatus == 'UNREGISTERED' || row.registerStatus == 'INVALID'"
            variant="text"
            color="primary"
            @click="goJump('assetsRegRegister', row)"
            >注册资产</n-button
          >
        </template>
      </CfTable>

      <formTab v-show="tableState.tabActive === 'form'" ref="formTabRef" />
    </div>
  </section>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import { getCurrentInstance, reactive, nextTick } from 'vue'
  import { tagList } from '@/views/document-management/config/tag'
  import { assetsMenuTree, assetsMenuList, dorisCategory } from '@/api/assets'
  import formTab from './form.vue'
  import CfTtee from '@/components/cfTtee'

  const router = useRouter()
  const emit = defineEmits(['updateTab'])
  const { proxy } = getCurrentInstance()
  const classState = reactive({
    data: [],
    expandedKeys: [],
    selectedKey: null,
  })
  const tableRef = ref()
  const formTabRef = ref()

  const props = defineProps({
    formInline: {
      type: String,
      default: 'true',
    },
  })
  const tableState = reactive({
    tabActive: 'form',
    tableHeight: 500,
    filterSearch: { sourceCategoryId: '' },
    tableList: [],
    isLoad: false,
    formTableHead: [
      { prop: 'cnName', name: '中文名称' },
      { prop: 'name', name: '英文名称' },
      { prop: 'tableType', name: '表类型', slot: 'tableType' },
      { prop: 'assetsRegistered', name: '注册状态', slot: 'assetsRegistered' },
    ],
    pagination: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
  })
  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }

  // 注册状态转换
  const registerStatus = (status) => {
    const obj = {
      UNREGISTERED: '未注册',
      WAIT_AUDIT: '待审批',
      REGISTERED: '注册成功',
      INVALID: '已失效',
      PUBLISH_AUDITING: '注册审核中',
      DOWN_AUDITING: '注销审核中',
    }
    return obj[status]
  }

  const registerStatusColor = (status) => {
    const obj = {
      UNREGISTERED: '#A8ABB2',
      WAIT_AUDIT: '#1e89ff',
      REGISTERED: '#2CA340',
      INVALID: '#f54446',
      PUBLISH_AUDITING: '#1e89ff',
      DOWN_AUDITING: '#1e89ff',
    }

    return obj[status]
  }

  // 注册
  function goJump(name, row) {
    sessionStorage.setItem('assetsReg', JSON.stringify(row))
    router.push({ name, query: { tabs: 'menu' } })
  }
  // 点击树节点
  function clickFn(node) {
    classState.selectedKey = node.id
    if (node?.nancalId) {
      tableState.tabActive = 'form'
      emit('updateTab', tableState.tabActive)
      return false
    }
    tableState.tabActive = 'menu'
    emit('updateTab', tableState.tabActive)
    tableState.pagination.currentPage = 1
    tableState.filterSearch.sourceCategoryId = isNaN(node.id) ? null : node.id
    onSearch()
  }
  // 搜索
  const treeRef = ref(null)
  async function searchClassFn() {
    treeRef.value.treeFactory.searchTree(tableState.treeSearchText, {
      isFilter: true,
      matchKey: 'name',
    })
  }
  // 获取分类树
  async function getClassifyTreeList() {
    const res = await assetsMenuTree()
    dorisCategory().then((resp) => {
      if (resp.success) {
        let allData = res.data
        allData.children = searchTreeWithCode(allData.children, resp.data.dorisCategory)
        classState.data = [{ ...allData, expanded: true }]
        nextTick(() => {
          classState.expandedKeys = [classState.data?.[0]?.id || 1]
          classState.selectedKey = classState.data?.[0]?.id || 1
        })
      }
    })
  }
  // 查找树结构中是否存在特定doris父节点
  const searchTreeWithCode = (nodesArr, id) => {
    if (!nodesArr || !id) {
      return nodesArr
    }
    for (let index = 0; index < nodesArr.length; index++) {
      if (nodesArr[index].id === id) {
        nodesArr[index].children.unshift({
          id: 9999999999,
          name: 'DORIS库',
          nancalId: '能科',
          selected: true,
        })
        nodesArr[index].expanded = true
        return nodesArr
      } else {
        if (nodesArr[index].children && nodesArr[index].children.length) {
          let res = searchTreeWithCode(nodesArr[index].children, id)
          if (res) {
            return nodesArr
          }
        }
      }
    }
  }
  // 查询
  function onSearch(objPram) {
    let { assetsRegistered, name } = props.formInline
    if (objPram) {
      tableState.pagination.currentPage = 1
      assetsRegistered = objPram.assetsRegistered
      name = objPram.name
    }

    tableState.isLoad = true
    assetsMenuList({
      pageNum: tableState.pagination.currentPage,
      pageSize: tableState.pagination.pageSize,
      condition: {
        registerStatus: assetsRegistered,
        name: name || '',
        ...tableState.filterSearch,
      },
    })
      .then((res) => {
        tableState.tableList = res.data.list
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }

  const getFormList = (formData) => {
    formTabRef.value.searchClickFn(formData)
  }
  onMounted(() => {
    tableState.tableHeight = tableRef.value.clientHeight - 107
    getClassifyTreeList()
    onSearch()
  })

  const pageChange = (pageNum) => {
    tableState.pagination.currentPage = pageNum === 0 ? 1 : pageNum
    onSearch()
  }
  const pageSizeChange = (pageSize) => {
    tableState.pagination.currentPage = 1
    tableState.pagination.pageSize = pageSize
    onSearch()
  }

  // 使用toRefs解构
  defineExpose({
    onSearch,
    getFormList,
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/cf.scss';
  @import '@/styles/variables.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: calc(100vh - 222px);
    border-top: 1px solid #dcdfe6;
    .left {
      width: 300px;
      background: #fff;
      // border-right: 1px solid #dcdfe6;
      border-radius: $cf-border-radius;
    }
    .right {
      width: calc(100% - var(--aside-width));
      height: calc(100% - 7px);
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .class-list {
    padding: 12px;
    .class-tree {
      height: calc(100vh - 334px);
      overflow: auto;
      :deep(.tree-box) {
        height: calc(100% - 46px);
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .tableData {
    height: calc(100% - 15px);
  }
  :deep(.page-footer) {
    z-index: 2;
  }
  .registered::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .in-review::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .un-registered::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #a8abb2;
    border-radius: 50%;
    content: '';
  }
  .nancalui-pagination {
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: flex-end;
    width: 100%;
    padding: 14px 16px;
    border-top: 1px solid #dcdfe6;
  }
  :deep(.el-loading-mask) {
    .el-loading-spinner {
      .circular {
        width: 48px !important;
        height: 48px !important;
      }
    }
  }
</style>
