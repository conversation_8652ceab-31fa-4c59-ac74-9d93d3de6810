<template>
  <div class="overview-box">
    <div class="overview">
      <div class="overview-left">
        <div class="table center">
          <div class="tr">
            <div class="td onlyOne">项目成员</div>
          </div>
          <div class="tr header">
            <div class="td">姓名</div>
            <div class="td">工号</div>
            <div class="td">角色</div>
          </div>
          <div v-for="(item, index) in state.memberList" :key="index" class="tr">
            <div class="td">{{ item.uname }}</div>
            <div class="td">{{ item.userId }}</div>
            <div class="td">{{ item.role }}</div>
          </div>
          <div v-if="state.memberList.length === 0" class="tr">
            <div class="td">暂无相关成员</div>
          </div>
        </div>
      </div>
      <div class="overview-right">
        <div class="table">
          <div class="tr leftHeader">
            <div class="td first">项目名称</div>
            <div class="td">{{ props.info.projectName }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据集名称</div>
            <div class="td">{{ props.info.datasetName }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据类型</div>
            <div class="td">{{ props.info.dataType }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据量</div>
            <div class="td">{{ props.info.taskNum }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">计价策略</div>
            <div class="td">{{ props.info.rewardDesc }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">来源流程</div>
            <div class="td">{{ props.info.sourceProcess }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">应用流程</div>
            <div class="td">{{ props.info.applicationProcess }}</div>
          </div>
          <!--          <div class="tr leftHeader">-->
          <!--            <div class="td first">项目发起部门</div>-->
          <!--            <div class="td">{{ props.info.dataInitiateDepartment }}</div>-->
          <!--          </div>-->
          <div class="tr header">
            <div class="td">背景及问题</div>
          </div>
          <div class="tr">
            <div class="td more">{{ props.info.backgroundProblems }}</div>
          </div>
          <div class="tr header">
            <div class="td">标注目标</div>
          </div>
          <div class="tr">
            <div class="td more">{{ props.info.markTarget }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { getTeamManagementDetail } from '@/api/dataGovernance.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    key: 1,
    loading: false,
    memberList: [],
  })

  onMounted(() => {
    state.loading = true
    getTeamManagementDetail(props.info.teamId)
      .then((res) => {
        if (res.success) {
          let list = []
          list.push({
            uname: res.data?.teamInfo?.userIdLeaderNamecn,
            userId: res.data?.teamInfo?.userIdLeaderName,
            role: '组长',
          })
          res.data?.teamUserList?.forEach((val) => {
            list.push({
              uname: val.uname,
              userId: val.userName,
              role: '组员',
            })
          })
          state.memberList = list
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  })
</script>

<style lang="scss" scoped>
  .overview-box {
    .switch {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;

      &-label {
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #c5d0ea;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        border-radius: 2px;

        &:first-of-type {
          border-right: none;

          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
        }

        &:last-of-type {
          border-left: none;

          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
        }
      }
    }

    .overview {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding-bottom: 16px;

      &-left {
        width: 330px;
        .pic {
          display: block;
          width: 100%;
          height: auto;
          max-height: 240px;
          margin-bottom: 16px;
        }
      }
      &-right {
        width: calc(100% - 346px);
      }
      .table {
        box-sizing: border-box;
        width: 100%;
        border-top: 1px solid #ebebeb;
        border-left: 1px solid #ebebeb;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        &.center {
          .tr {
            .td {
              text-align: center;
            }
          }
        }

        .tr {
          display: flex;
          // justify-content: space-between;
          // align-items: center;
          min-height: 36px;
          &.header {
            background-color: rgb(235, 244, 255);
            .td {
              color: #1d2129;
            }
          }
          &.leftHeader {
            .td {
              &.first {
                width: 160px;
                flex: none;
                background-color: rgb(235, 244, 255);
                color: #1d2129;
              }
            }
          }
          .td {
            flex-shrink: 0;
            font-size: 14px;
            color: #606266;
            // line-height: 36px;
            text-align: left;
            border-right: 1px solid #ebebeb;
            border-bottom: 1px solid #ebebeb;
            padding: 0 12px;
             padding: 12px 15px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      word-break: break-word;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            word-wrap: break-all;
            &.onlyOne {
              color: #1d2129;
              font-size: 16px;
              font-weight: bolder;
            }
            &.more {
              min-height: 36px;
              height: auto;
              line-height: 22px;
              white-space: normal;
              padding: 6px 12px;
            }
          }
        }
      }
    }

    .table-box {
      height: 478px;
    }
  }
</style>
