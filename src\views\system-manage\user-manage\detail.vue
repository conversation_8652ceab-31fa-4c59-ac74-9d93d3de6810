<template>
  <section class="container">
    <section class="container-box">
      <div class="page_header_common_style">
        <span class="need_smallcube__title">{{ title }}</span>
      </div>
      <div class="form">
        <n-form
          ref="form"
          :data="form"
          label-width="82px"
          label-align="end"
          class="disable-hide-border disabled-form"
        >
          <n-row>
            <n-col :span="24">
              <n-form-item label="用户名：">
                <n-input
                  v-model="form.username"
                  :disabled="true"
                  noborder
                  placeholder=" "
                  size="small"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="电子邮箱：">
                <n-input
                  v-model="form.email"
                  :disabled="true"
                  noborder
                  placeholder=" "
                  size="small"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="联系电话：">
                <n-input
                  v-model="form.phone"
                  :disabled="true"
                  noborder
                  type="tel"
                  maxlength="11"
                  placeholder=" "
                  size="small"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>
          <!--  隐藏密码 -->
          <n-row v-if="0">
            <n-col :span="24">
              <n-form-item label="登录密码：">
                <n-input
                  v-model="form.password"
                  noborder
                  :disabled="true"
                  placeholder=" "
                  maxlength="30"
                  type="password"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row v-if="0">
            <n-col :span="24">
              <n-form-item label="密码二次确认：">
                <n-input
                  v-model="form.confirmPwd"
                  :disabled="true"
                  placeholder=" "
                  maxlength="30"
                  type="password"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="角色：">
                <n-input
                  v-model="roleName"
                  :disabled="true"
                  noborder
                  placeholder=" "
                  maxlength="30"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="密级：">
                <n-input
                  v-model="form.confidentialityLevelName"
                  :disabled="true"
                  noborder
                  placeholder=" "
                  maxlength="30"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="部门：">
                <n-input
                  v-model="form.departmentFullName"
                  :disabled="true"
                  noborder
                  placeholder=" "
                  maxlength="30"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="用户描述：">
                <n-textarea
                  v-model="form.description"
                  :disabled="true"
                  placeholder=" "
                  maxlength="200"
                  :autosize="{ minRows: 3 }"
                  type="textarea"
                  size="small"
                />
              </n-form-item>
            </n-col>
          </n-row>
        </n-form>
      </div>
    </section>
    <div class="container-footer">
      <div class="my-appliction">
        <n-button color="primary" size="sm" variant="solid" @click.prevent="goBack">返回</n-button>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    name: 'DataScriptDetail',
    data() {
      return {
        title: '用户详情',
        treeData: [],
        form: {
          username: '',
          email: '',
          phone: '',
          password: '',
          confirmPwd: '',
          roleId: '',
          confidentialityLevelName: '',
        },
        passwordType: 'password',
        roleAllArr: '', // 角色下拉列表
        roleName: '',
      }
    },
    mounted() {
      this.roleAll()
    },
    methods: {
      // 用户数据详情查询
      userDetail() {
        let params = {
          id: this.$route.query.id,
        }

        this.$api.system.userDetail(params).then((res) => {
          this.form = res.data
          this.form.roleId = String(res.data.roleId)
          this.form.confirmPwd = res.data.password

          let arr = this.roleAllArr.filter((item) => {
            return item.key === String(res.data.roleId)
          })
          this.roleName = arr.length === 0 ? '未分配' : arr[0].value
        })
      },
      // 用户数据详情查询
      roleAll() {
        let params = {}

        this.$api.system.roleAll(params).then((res) => {
          this.roleAllArr = res.data
          this.userDetail()
        })
      },
      goBack() {
        this.$router.back()
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    // padding-bottom: 70px;
    &-box {
      height: calc(100% - 60px);
      border-radius: 4px;
    }

    &-footer {
      // width: 100%;
      height: 60px;
      padding: 17px 30px;
      text-align: center;
      background: #ffffff;
      margin-top: 10px;
      margin-left: -10px;
      margin-right: -10px;
      border-radius: 8px 8px 0px 0px;
    }
  }

  .form {
    background: #fff;
    padding: 33px 58px;
    color: #333;
    .nancalui-form {
      width: 700px;
      margin: 0 auto;
    }
    h2 {
      font-size: 14px;
      padding-left: 5px;
      border-left: 4px solid $themeBlue;
      margin: 0 0 20px;
    }
  }
</style>
