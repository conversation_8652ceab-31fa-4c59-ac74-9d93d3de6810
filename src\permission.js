import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { ElMessage } from 'element-plus'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/adminlogin', '/singlesignon', '/dataWeaving', '/dataLabel'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // console.log('to', to)
  // 开始进度条
  NProgress.start()

  // 设置页面title
  document.title = getPageTitle(to.meta.title)

  // 确定用户是否已登录
  const hasToken = getToken()
  if (hasToken) {
    const userCon = store.getters.userCon
    if (to.path === '/adminlogin') {
      next()
      NProgress.done()
    } else if (from.path === '/' && to.path !== '/convergencePage') {
      if (userCon) {
        //如果有中间平台，登陆状态下都跳到中间平台
        if (to.name !== 'ConvergencePage') {
          router.push({ name: 'ConvergencePage' })
          next(false)
        } else {
          next()
        }
      } else {
        // 如果已登录，则重定向到主页
        next()
      }
      NProgress.done()
    } else {
      // console.log('to.path', to.path)
      const hasGetUserInfo = store.getters.name
      if (hasGetUserInfo) {
        next()
      } else {
        try {
          // 获取对方信息
          await store.dispatch('user/getInfo')
          next()
        } catch (error) {
          // 删除 token 进入登录页面重新登录
          await store.dispatch('user/resetToken')
          ElMessage.error(error || 'Has Error')
          if (to.name !== 'ConvergencePage') {
            router.push({ name: 'ConvergencePage' })
            next(false)
          } else {
            next()
          }
          NProgress.done()
        }
      }
    }
  } else {
    // 没有 token
    if (whiteList.indexOf(to.path) !== -1) {
      // 白名单免登录
      next()
    } else {
      // 没有访问权限的其他页面被重定向到登录页面。
      // 删除 token 进入登录页面重新登录
      if (to.name !== 'ConvergencePage') {
        router.push({ name: 'ConvergencePage' })
        next(false)
      } else {
        next()
      }
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
