<template>
  <div class="tree-header">
    <div class="tree-header-search">
      <n-input v-model="filterText" clearable placeholder="搜索" @input="treeFilter">
        <template #suffix>
          <el-icon class="el-input__icon" @click="treeFilter"><search /></el-icon>
        </template>
      </n-input>
    </div>
    <n-tree
      class="modeTree"
      :data="treeData"
      ref="treeRef"
      :prop="defaultProps"
      @node-click="handleNodeClick"
    >
      <template #icon="{ nodeData, toggleNode }">
        <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
        <span
          v-else
          @click="
            (event) => {
              event.stopPropagation()
              toggleNode(nodeData)
            }
          "
        >
          <SvgIcon v-if="nodeData.expanded" class="nancalui-tree-switch" icon="tree-contract-new" />
          <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
        </span>
      </template>
      <template #content="{ nodeData }">
        <div class="custom-tree-node">
          <template v-if="nodeData.type === 1">
            <img style="width: 20px; height: 20px" src="@img/data_model/ic_wenjian.png" alt="" />
          </template>
          <template v-else-if="nodeData.type === 2">
            <div class="point"></div>
            <img style="width: 20px; height: 20px" src="@img/data_model/ic_zhuti.png" alt="" />
          </template>
          <template v-else-if="nodeData.type === 3">
            <div class="point"></div>
            <img style="width: 20px; height: 20px" src="@img/data_model/ic_shiti.png" alt="" />
          </template>
          <template v-else>
            <div class="point"></div>
            <img style="width: 20px; height: 20px" src="@img/data_model/ic_guanxi.png" alt="" />
          </template>
          <span :title="nodeData.name">{{ nodeData.name }}</span>
        </div>
      </template>
    </n-tree>
  </div>
</template>

<script>
  export default {
    props: {
      treeData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        // 模型树数据结构
        defaultProps: {
          children: 'children',
          label: 'name',
        },
        filterText: '',
      }
    },
    methods: {
      treeFilter(value) {
        this.$refs.treeRef.treeFactory.searchTree(value, {
          isFilter: true,
          matchKey: 'name',
        })
      },
      clearMenu() {
        this.$parent.clearMenu()
      },
      handleNodeClick(data) {
        this.clearMenu()
        if (data.kind === 2) {
          this.$emit('nodeClick', data)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .tree-header {
    height: calc(100% - 32px);
    .tree-header-search {
      padding: 10px;
      background: #ffffff;
    }
  }
  .modeTree {
    height: calc(100% - 52px);
    padding: 0 10px;
    overflow: auto;
    background: #ffffff;
  }
  :deep(.nancalui-tree__node-content--value-wrapper) {
    flex: 1;
    width: 30px;
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 8px;
    line-height: 30px;
    img {
      object-fit: cover;
      image-rendering: pixelated;
    }
    span {
      display: inline-block;
      margin-left: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
    .setting,
    .tools {
      display: none;
      margin-left: auto;
    }
    &:hover {
      .setting,
      .tools {
        display: block;
        margin-left: auto;
        i {
          display: inline-block;
          width: 20px;
          height: 20px;
          vertical-align: middle;
        }
      }
    }
    .setting {
      .icon {
        width: 20px;
        height: 20px;
        background: url(@img/data_framework/icon-more.png) no-repeat;
        background-size: 100% 100%;
        &:hover {
          background: url(@img/data_framework/icon-more-hover.png) no-repeat;
          background-size: 100% 100%;
        }
      }
    }
    // 树工具 删除、编辑
    .i-edit {
      margin-right: 8px;
      background: url(@img/data_framework/icon-edit.png) no-repeat;
      background-size: 100% 100%;
      &:hover {
        background: url(@img/data_framework/icon-edit-hover.png) no-repeat;
        background-size: 100% 100%;
      }
    }
    .i-delete {
      background: url(@img/data_framework/icon-delete.png) no-repeat;
      background-size: 100% 100%;
      &:hover {
        background: url(@img/data_framework/icon-delete-hover.png) no-repeat;
        background-size: 100% 100%;
      }
    }
  }
</style>
