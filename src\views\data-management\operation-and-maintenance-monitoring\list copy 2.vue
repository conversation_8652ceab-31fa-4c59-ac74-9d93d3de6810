<template>
  <!-- 运维监控 -->
  <div class="merge-overview">
    <overview />
    <div class="operation-index-page container">
      <div class="operation-page-echarts-box">
        <div class="operation-page-echarts-box-left echarts-box">
          <div class="top-title-box">
            <moduleName :info="{ name: '近7天采集任务趋势' }" />
          </div>
          <div id="echarts-collect-task-dom"></div>
        </div>
        <div class="operation-page-echarts-box-right echarts-box">
          <div class="top-title-box">
            <moduleName :info="{ name: '近7天采集任务状态分布' }" />
          </div>
          <div id="echarts-collect-status-dom"></div>
        </div>
      </div>
      <div class="list-box" v-loading="state.loading">
        <n-public-table
          :tableHeadTitles="state.tableHeadTitles"
          :tableData="state.tableData"
          :tableHeight="state.tableHeight"
          @tablePageChange="tablePageChange"
        >
          <template #pageTop>
            <div class="box-add">
              <div class="commonForm-search">
                <div class="search-left">
                  <slot name="searchLeft"></slot>
                </div>
                <div class="commonForm">
                  <n-form
                    :inline="true"
                    :data="state.formInline"
                    :key="state.key"
                    class="demo-form-inline search-right commonForm"
                  >
                    <n-form-item label="运行状态：">
                      <n-select
                        v-model="state.formInline.state"
                        placeholder="请选择"
                        allow-clear
                        filter
                        @value-change="initTable('filter')"
                      >
                        <n-option
                          v-for="item in state.dataStructureTypeOption"
                          :key="item.label"
                          :name="item.label"
                          :value="item.value"
                        />
                      </n-select>
                    </n-form-item>
                    <n-form-item label="时间范围：">
                      <n-range-date-picker-pro
                        v-model="state.formInline.time"
                        :placeholder="['开始日期', '结束日期']"
                        format="YYYY-MM-DD"
                        allow-clear
                        @confirmEvent="initTable('filter')"
                      />
                    </n-form-item>
                    <n-form-item label="">
                      <n-input
                        v-model="state.formInline.keyword"
                        size="small"
                        placeholder="请输入完整采集任务名称"
                        clearable
                        @clear="initTable('filter')"
                      >
                        <template #append>
                          <n-button @click.prevent="initTable('filter')">
                            <n-popover
                              class="item"
                              content="搜索"
                              trigger="hover"
                              :position="['bottom']"
                            >
                              <SvgIcon class="icon_search" icon="icon_search" />
                            </n-popover>
                          </n-button>
                        </template>
                      </n-input>
                    </n-form-item> </n-form
                ></div>
              </div>
            </div>
          </template>

          <template #state="{ editor }">
            <div v-if="editor.row.state">
              <span
                :class="{
                  dot: true,
                  red: editor.row.state === 'FAILURE',
                  blue: editor.row.state === 'RUNNING_EXECUTION',
                }"
              ></span>
              <!-- {{ editor.row.state + ' | ' + dataSourceTypeChinese(editor.row.state) }} -->
              {{ dataSourceStateChinese(editor.row.state) }}
            </div>
          </template>
        </n-public-table>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs, onBeforeUnmount } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { formartTime, formartTimeDate } from '@/utils/index'
  import * as echarts from 'echarts'
  import overview from '../data-collection-overview/index'
  export default {
    title: 'List',
    components: { overview },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        key: 1,
        loading: false,
        tableData: {},
        tableHeight: 436,
        formInline: {
          sourceType: '',
          state: false,
          sourceType2: '',
          time: [],
          keyword: '',
        },
        dataStructureTypeOption: [
          {
            value: 'RUNNING_EXECUTION',
            label: '运行中',
          },
          {
            value: 'SUCCESS',
            label: '成功',
          },
          {
            value: 'FAILURE',
            label: '失败',
          },
        ],
        org_dataSourceType: [],
        org_dataSourceType2: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'processName', name: '采集任务名称' },
          { prop: 'dataStructureTypeName', name: '采集数据类型' },
          { prop: 'during', name: '任务运行时长' },
          { prop: 'state', name: '任务状态', slot: 'state' },
          { prop: 'startTime', name: '任务执行时间' },
          { prop: 'dispatch', name: '调度频率' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
      })
      // 环形饼图基础配置
      const monitoringEchartsOption = {
        title: {
          text: '',
          left: 'center',
          textStyle: {
            color: '#666666',
            fontSize: 14,
            fontWeight: '500',
            lineHeight: 20,
          },
        },

        legend: {
          type: 'plain',
          //  orient: 'vertical', //竖直展示，如果横着的话，这个可以直接去掉
          top: '89%',
          bottom: 'center',
          align: 'left',
          itemGap: 10,
          itemWidth: 10, // 设置宽度
          itemHeight: 17, // 设置高度
          icon: 'circle',
          symbolKeepAspect: false,
        },
        tooltip: {
          trigger: 'item', //数据项图形触发
          // formatter: '{a} <br/>{b}: {c}条  {d}%', // 展示格式
          formatter: function (params) {
            var showHtm = ''
            let { seriesName, color, name, value, percent } = params

            showHtm += `
                  <div style='color:rgba(0,0,0,0.45)'>${seriesName}</div>
                  `

            showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-top:4px;border-radius:10px;width:10px;height:10px;background-color:${color};'></span>
                    <span style='font-weight:400;margin-right:2px'>${name}</span>
                  </div>
                  <span style='font-weight:500;margin-right:4px'>${value}条</span>
                </div>
                `
            showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-top:4px;border-radius:10px;width:10px;height:10px;background-color:${color};'></span>
                    <span style='font-weight:400;margin-right:2px'>占比</span>
                  </div>

                  <span style='font-weight:500;'>${percent}%</span>
                </div>
                `
            return showHtm
          },
          textStyle: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: '近7天采集任务状态占比',
            type: 'pie',
            selectedMode: 'single',
            center: ['50%', '40%'], // 将0改成50%，修改圆的内径
            radius: ['45%', '65%'], // 将0改成50%，修改圆的内径
            clockwise: false,
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: function (parames) {
                  return parames.percent + '%'
                },
              },
            },
            data: [
              { value: 65.5, name: '正常', itemStyle: { color: '#73DEB3' } },
              { value: 15.2, name: '重跑', itemStyle: { color: '#73A0FA' } },
              { value: 15.2, name: '失败', itemStyle: { color: '#F75656' } },
            ],
          },
        ],
      }
      // 折线图图基础配置
      const lineChartOption = {
        Animation: true,
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: function (params) {
            var showHtm = ''
            for (var i = 0; i < params.length; i++) {
              //日期
              var time = params[i].name
              //名称
              var name = params[i].seriesName
              //值
              var value = params[i].value
              var color = params[i].color
              if (i == 0) {
                showHtm += `
                  <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                  `
              }
              showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                    <span style='font-weight:400'>${name}</span>
                  </div>
                  <span style='font-weight:500;'>${value}</span>
                </div>
                `
            }
            return showHtm
          },
          textStyle: {
            fontSize: 12,
            color: '#333',
          },
        },
        color: ['#6E9EFF'], // 设置tooltips展示图标颜色
        legend: {
          itemWidth: 10, //粗细
          itemHeight: 1, //粗细
          data: [
            // {
            //   name: '任务数',
            //   icon: 'rect',
            //   itemStyle: {
            //     color: '#6E9EFF',
            //   },
            // },
          ],
          // data: ['成功任务', '失败任务'],
          // icon: 'circle', //  这个字段控制形状  类型包括 circle 圆形，triangle 三角形，diamond 四边形，arrow 变异三角形，none 无

          itemGap: 50, // 设置间距，
          textStyle: {
            // 图例文字的样式
            color: '#666666',
            fontSize: 12,
          },
          x: 'center', //左（left）、右（right）、居中（center）
          y: 'bottom', //上（top）、下（bottom）、居中（center）
        },
        toolbox: {
          // feature: {
          //   saveAsImage: {},
          // },
        },
        grid: {
          left: 20,
          right: 40,
          bottom: 30,
          top: 40,
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: [],
          },
        ],
        yAxis: [
          {
            type: 'value',
            offset: 10,
          },
        ],
        series: [
          {
            name: '任务数',
            type: 'line',
            // stack: 'Total', //是否堆叠面积
            lineStyle: {
              color: '#6E9EFF',
            },
            // emphasis: {
            //   focus: 'series',
            // },
            smooth: true,
            symbol: 'circle',
            symbolSize: 7,
            data: [],
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 1,
                  color: '#fff', // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: '#F0F7FFFF', // 100% 处的颜色
                },
              ]), //背景渐变色
            },
          },
        ],
      }
      let mylineChart = null
      let myCircularPieChart = null
      const methods = {
        dataSourceStateChinese(type) {
          let result = state.dataStructureTypeOption.filter((item) => item.value === type)
          return result[0]['label']
        },

        initData() {
          window.onresize = () => {
            methods.echartsResize()
          }
          //一进来默认展示7天的数据
          state.formInline.time = [
            new Date(methods.getSomeDayAgoTime(6)),
            new Date(methods.getSomeDayAgoTime(0)),
          ]
          methods.getMonitorStatistic()
          methods.initTable()
        },
        //统计接口
        getMonitorStatistic() {
          if (!mylineChart) {
            let chartDom = document.getElementById('echarts-collect-task-dom')
            chartDom.setAttribute('_echarts_instance_', '')
            mylineChart = echarts.init(chartDom)
          }

          if (!myCircularPieChart) {
            let chartDom = document.getElementById('echarts-collect-status-dom')
            chartDom.setAttribute('_echarts_instance_', '')
            myCircularPieChart = echarts.init(chartDom)
          }
          mylineChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })
          myCircularPieChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })

          api.dataManagement
            .getMonitorStatistic({
              startDate: methods.getSomeDayAgoTime(6),
              endDate: methods.getSomeDayAgoTime(0),
              projectCode: state.formInline.projectCode,
            })
            .then((res) => {
              let { data, success } = res
              mylineChart?.hideLoading()
              myCircularPieChart?.hideLoading()
              if (success) {
                // 任务趋势-折线图
                let { trend, total, success, repeat, fail } = data
                let _taskData = []
                let _taskXData = []
                trend.forEach((item) => {
                  _taskXData.push(item.title)
                  _taskData.push(item.value)
                })

                lineChartOption.xAxis[0].data = _taskXData || []
                lineChartOption.series[0].data = _taskData || []
                document
                  .getElementById('echarts-collect-task-dom')
                  .setAttribute('_echarts_instance_', '')
                lineChartOption && mylineChart.setOption(lineChartOption)
                // 任务状态-折线图

                total = total === 0 ? 1 : total
                monitoringEchartsOption.series[0].data[0].value = success
                monitoringEchartsOption.series[0].data[1].value = repeat
                monitoringEchartsOption.series[0].data[2].value = fail
                monitoringEchartsOption && myCircularPieChart.setOption(monitoringEchartsOption)
              }
            })
            .catch(() => {
              mylineChart?.hideLoading()
              myCircularPieChart?.hideLoading()
            })
        },
        //获取指定几天前的日期
        getSomeDayAgoTime(someDay = 1, split = '-') {
          let times = new Date().getTime() - someDay * 24 * 60 * 60 * 1000
          let _someDayAgoTime = new Date(times)

          let year = _someDayAgoTime.getFullYear()
          let month =
            _someDayAgoTime.getMonth() + 1 < 10
              ? '0' + (_someDayAgoTime.getMonth() + 1)
              : _someDayAgoTime.getMonth() + 1
          let day =
            _someDayAgoTime.getDate() < 10
              ? '0' + _someDayAgoTime.getDate()
              : _someDayAgoTime.getDate()

          return year + split + month + split + day
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 152 - 50 - 301
        },
        // 初始化form
        initTable(str) {
          state.key++
          if (str === 'filter') {
            state.pagination.currentPage = 1
          }
          let data = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              state: state.formInline.state || null,
              name: state.formInline.keyword || null,
              projectCode: state.formInline.projectCode,
              startDate: state.formInline.time?.[0]
                ? formartTimeDate(state.formInline.time?.[0], '-')
                : null,
              endDate: state.formInline.time?.[1]
                ? formartTimeDate(state.formInline.time?.[1], '-')
                : null,
            },
          }
          if (state.formInline.projectCode) {
            state.loading = true
            api.dataManagement
              .monitorInstanceList(data)
              .then((res) => {
                state.loading = false
                // 新增序号属性
                res.data.list.map((item, index) => {
                  let period = ''
                  let rateTime = item.rateTime ? item.rateTime : ''
                  switch (item.period) {
                    case 'hour':
                      period = '间隔小时' + item.extent
                      break
                    case 'day':
                      period = '每天'
                      break
                    case 'week':
                      period = '每周' + item.extent
                      break
                    case 'month':
                      period = '每月' + item.extent + '日'
                      break
                    default:
                      period = item.cron || null
                  }
                  if (item.period === 'hour') {
                    return Object.assign(item, {
                      number: index + 1,
                      dispatch: period ? period + ' | ' + item.startTime.slice(11) : '',
                    })
                  } else {
                    return Object.assign(item, {
                      number: index + 1,
                      dispatch: item.cron ? period : period ? period + ' | ' + rateTime : '',
                      // dispatch: period ? period + ' | ' + rateTime : '',
                    })
                  }
                })
                state.tableData = res.data
              })
              .catch(() => {
                state.loading = false
              })
          } else {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
            state.tableData = {}
          }
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看
        seeDetails(editor) {
          let { row } = editor
          router.push({ name: 'instanceCheck', query: { id: row.id } })
        },
        // 屏幕onresize echarts重新绘制
        echartsResize() {
          //echarts重新绘制
          if (mylineChart) {
            mylineChart.resize()
          }
          if (myCircularPieChart) {
            myCircularPieChart.resize()
          }
        },
      }

      methods.setTableHeight()
      onBeforeUnmount(() => {
        window.onresize = null

        if (mylineChart) {
          mylineChart.dispose() //销毁
        }
        if (myCircularPieChart) {
          myCircularPieChart.dispose() //销毁
        }
      })

      onMounted(() => {
        const { projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        methods.initData()
      })

      return {
        state,
        buttonAuthList,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .merge-overview {
    height: calc(100vh - $navbarHeight);
    overflow-y: auto;
  }
  .operation-index-page {
    padding: 10px 20px;
    .operation-page-echarts-box {
      height: 291px;
      background-color: #fff;
      border-radius: 4px;
      margin-bottom: 10px;
      display: flex;
      .echarts-box {
        padding: 16px 5px 16px 20px;
        #echarts-collect-task-dom,
        #echarts-collect-status-dom {
          height: 250px;
          width: 100%;
          div {
            height: 100%;
            width: 100%;
          }
        }
      }
      &-left {
        flex: 1;
      }
      &-right {
        width: 420px;
        border-left: 1px solid #ebedf0;
      }
    }
    .list-box {
      height: calc(100% - 301px);
      padding: 0 16px;
      border-radius: 4px;
      background-color: #fff;

      .seeDetails {
        color: $themeBlue;
        padding: 0;
      }

      .commonForm-search {
        display: flex;
        justify-content: space-between;
        padding: 16px 0;
      }
      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #04c495;
        border-radius: 50px;
        margin-right: 6px;
        &.red {
          background: #f54446;
        }
        &.blue {
          background: #04c495;
        }
      }
    }
  }
</style>
