<template>
  <div
    :class="{
      box: true,
      isSee: nodeInfo.isSee,
      checked: nodeInfo.checked,
    }"
  >
    <div
      :class="{
        nodeBoxBorder: nodeInfo.isSee ? false : true,
        nodeBgCss: true,
        green: nodeInfo.nodeRunstatus === 'SUCCESS',
        red: nodeInfo.nodeRunstatus === 'FAILURE' || nodeInfo.nodeRunstatus === 'STOP',
        gray: nodeInfo.nodeRunstatus === 'WAITING_TO_RUN',
        orange: nodeInfo.nodeRunstatus === 'RUNNING_EXECUTION',
      }"
    ></div>
    <div class="top">
      <div class="case"
        ><img v-if="nodeInfo.taskType" class="pic" :src="getAssetsImages(nodeInfo.taskType)"
      /></div>
      <div class="text" :title="nodeInfo.name">{{ nodeInfo.name }}</div>
      <div class="case more">
        <el-popover
          v-if="!nodeInfo.publishStatus"
          placement="bottom-start"
          :width="52"
          trigger="hover"
          :show-arrow="false"
          popper-class="popover-dag"
        >
          <template #default>
            <div class="popover-dag-box">
              <div v-if="!nodeInfo.isSee" class="popover-dag-box-label run"
                >重跑<SvgIcon class="icon" icon="icon-arrow-right" />
                <div class="popover-dag-box-label-child">
                  <div
                    :class="{
                      'popover-dag-box-label-child-label': true,
                      disabled: nodeInfo.nodeRunstatus !== 'FAILURE' ? false : true,
                    }"
                    @click.stop.prevent="
                      setFn(
                        'CURRENT_NODE',
                        nodeInfo.nodeRunstatus !== 'FAILURE' ? false : true,
                        true,
                      )
                    "
                    >重跑当前节点</div
                  >
                  <div
                    :class="{
                      'popover-dag-box-label-child-label': true,
                      disabled: nodeInfo.nodeRunstatus !== 'FAILURE' ? false : true,
                    }"
                    @click.stop.prevent="
                      setFn(
                        'CUSTOMED_NODE',
                        nodeInfo.nodeRunstatus !== 'FAILURE' ? false : true,
                        true,
                      )
                    "
                    >重跑当前节点及下游节点</div
                  >
                  <div
                    :class="{
                      'popover-dag-box-label-child-label': true,
                      disabled: nodeInfo.collectJobIds ? false : true,
                    }"
                    @click.stop.prevent="
                      setFn('WORKFLOW_COLLECT', nodeInfo.collectJobIds ? false : true, true)
                    "
                    >重跑整个工作流（含采集任务）</div
                  >
                  <div
                    :class="{
                      'popover-dag-box-label-child-label': true,
                      disabled: !nodeInfo.collectJobIds ? false : true,
                    }"
                    @click.stop.prevent="
                      setFn('WORKFLOW_NO_COLLECT', !nodeInfo.collectJobIds ? false : true, true)
                    "
                    >重跑整个工作流（不含采集任务）</div
                  >
                  <div
                    :class="{
                      'popover-dag-box-label-child-label': true,
                      disabled: nodeInfo.hasFailure ? false : true,
                    }"
                    @click.stop.prevent="
                      setFn('WORKFLOW_FAIL_NODE', nodeInfo.hasFailure ? false : true, true)
                    "
                    >重跑整个工作流失败节点</div
                  >
                </div>
              </div>
              <div
                v-if="!nodeInfo.isSee"
                :class="{
                  'popover-dag-box-label': true,
                  disabled: nodeInfo.dsProcessCode ? false : true,
                }"
                @click.stop.prevent="seeLogFn(nodeInfo.dsProcessCode ? true : false)"
                >查看日志
              </div>
              <div
                v-if="!nodeInfo.isSee"
                :class="{
                  'popover-dag-box-label': true,
                  disabled: nodeInfo.nodeRunstatus === 'FAILURE' ? false : true,
                }"
                @click.prevent.stop="
                  setFn('FORCED_SUCCESS', nodeInfo.nodeRunstatus === 'FAILURE' ? false : true)
                "
                >置成功</div
              >
              <div
                v-if="!nodeInfo.isSee"
                :class="{
                  'popover-dag-box-label': true,
                  disabled:
                    nodeInfo.nodeFrozen || (nodeInfo.scheduleType === 'DEFAULT' ? false : true),
                }"
                @click.prevent.stop="
                  setFn(
                    'FROZEN',
                    nodeInfo.nodeFrozen || (nodeInfo.scheduleType === 'DEFAULT' ? false : true),
                  )
                "
                >冻结（暂停调度）</div
              >
              <div
                v-if="!nodeInfo.isSee"
                :class="{
                  'popover-dag-box-label': true,
                  disabled: nodeInfo.nodeFrozen ? false : true,
                }"
                @click.prevent.stop="setFn('RECOVERY', nodeInfo.nodeFrozen ? false : true)"
                >恢复（正常调度）</div
              >
              <div
                v-if="nodeInfo.needGrad"
                :class="{
                  'popover-dag-box-label': true,
                }"
                @click.prevent.stop="showFn('showPost')"
                >展开上级</div
              >
              <div
                v-if="nodeInfo.needGrad"
                :class="{
                  'popover-dag-box-label': true,
                }"
                @click.prevent.stop="showFn('showPre')"
                >展开下级</div
              >
            </div>
          </template>
          <template #reference>
            <SvgIcon class="icon" icon="icon-dag-more" />
          </template>
        </el-popover>
        <div v-else style="width: 16px"></div>
      </div>
    </div>
    <!--    <div class="bottom">{{ getNodeByType(nodeInfo.taskType) }}</div>-->
  </div>
  <logPop v-if="showDirDialog" :nodeInfo="nodeInfo" :isCanvas="true" @seeLogFn="seeLogFn" />
</template>

<script>
  import { reactive, toRefs, inject, watch } from 'vue'
  import logPop from './logPop.vue'
  import { instanceDag } from '@/api/dataManage'
  import { ElNotification } from 'element-plus'
  export default {
    components: { logPop },
    setup() {
      const state = reactive({
        node: null,
        nodeInfo: {
          needGrad: false,
          checked: false,
          name: '',
          nodeRunstatus: '',
          taskType: '',
        },
        showDirDialog: false,
      })
      state.node = inject('getNode')()
      onMounted(() => {
        nextTick(() => {
          state.nodeInfo = state.node.getData() || {
            checked: false,
            name: '',
            nodeRunstatus: '',
            taskType: '',
          }
          // 监听数据改变事件
          state.node.on('change:data', ({ current }) => {
            state.nodeInfo = current
          })
        })
      })

      // 离线作业类型枚举
      const offlineJobTypeList = {
        OW_PY_SPARK: 'PySpark',
        OW_HIVE_SQL: 'HiveSQL',
        OW_SPARK_SQL: 'SparkSQL',
        OW_PYTHON: 'Python',
        OW_SHELL: 'Shell',
        OW_HIVE_DDL: 'HiveDDL',
        OW_DATA_ASYNC: '同步至资源库',
        OW_DATA_UPLOAD: 'upload',
        OW_DORIS_DDL: 'DorisDDL',
        OW_DORIS_SQL: 'DorisSQL',
      }

      const getAssetsImages = (name) => {
        if (name === 'OW_PY_SPARK') {
          return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIaSURBVHgB7ZQxb9NAFMffndNwkZA4RhbkfgLSjQGJFMUSA6DkE+Bs7RRnYu4EW5oFKpbSjY2UgoSEUNJPkI4w1UztVmdo0zj2Xd8p71qnaqUqydChf+knn+/evffuPZ8B7kSKXwj/pHwvuDzPYE6Ky6IDTBfTEVsqdE9DXZLyeOHYzcEcpZVT4wuqE3uFaAQDNw88nDnAoCxKjmYVBaov/py0Y0/IGJK9PHO+pRyqMItMWYae2B9hkIl5r9AbeMI3Y05zFWQf6SAu3FTMaTDs4sEQ3uJb0U5rrbe5HvuxTTbOu0gf+YLsXePSbIqIrExi28iDf8/ybFHw59hwmf89XLI9CJESskzjJmXkUsA1cmKzNO9tpIdsIRJ5YtYf3+dVleiIASuaL8lmYDYekRMf2cxkfERzPXLk0omDjJ2x0ZkEzsVpkzlyiwxMPXdp3a5VqAQRnVCS7S6NJe2vXw7gIF+Rd3BRX4G8RB4i76kEh7T5KfKIbIfIL7I38zXkA/If+WsD5Cg7Hy7qbY7foKzMpi7ZhjDuUzdzipCSWqPnMlxRpqxcGDdvbpr6X9T6tGMa29cMelrn8D6M6sHKm8b6xo/NYOVVzdpxmFYMZH31tYsXzQWlJGZaaX78HnBQE2Yz/YvWN3Y6eG0l8DhSmm9xzpupThbnFkDrpBasVkMzbn7+2YY0LTXo3Wr6EoFqTThTia/GX+GdbpnOALxaskMiHanHAAAAAElFTkSuQmCC'
        } else {
          return new URL(
            `/src/assets/img/offlineDev/${offlineJobTypeList[name]}.png`,
            import.meta.url,
          ).href
        }
      }

      // 通过作业类型判断节点
      const getNodeByType = (jobType) => {
        if (jobType === 'OW_DATA_ASYNC') {
          return 'dataAsync'
        } else if (jobType === 'OW_PY_SPARK') {
          return 'PySpark'
        } else if (jobType === 'OW_HIVE_SQL') {
          return 'HiveSQL'
        } else if (jobType === 'OW_SPARK_SQL') {
          return 'SparkSQL'
        } else if (jobType === 'OW_PYTHON') {
          return 'Python'
        } else if (jobType === 'OW_SHELL') {
          return 'Shell'
        } else if (jobType === 'OW_HIVE_DDL') {
          return 'HiveDDL'
        } else if (jobType === 'DATA_UPLOAD') {
          return '非结构化数据分析'
        } else if (jobType === 'DATA_ASYNC') {
          return '同步至资源库'
        }
      }
      const seeLogFn = (flag) => {
        if (!state.nodeInfo.dsTaskId) {
          ElNotification({
            title: '提示',
            message: '当前业务流程暂未执行第一轮调度，不可执行该操作。',
            type: 'warning',
          })
          return false
        }
        state.showDirDialog = flag
      }

      // 节点操作
      const setFn = (type, notClick = true, isRun = false) => {
        if (!state.nodeInfo.dsTaskId) {
          ElNotification({
            title: '提示',
            message: '当前业务流程暂未执行第一轮调度，不可执行该操作。',
            type: 'warning',
          })
          return false
        }
        if (!notClick) {
          let data = {
            dsTaskId: state.nodeInfo.dsTaskId || null,
            processId: state.nodeInfo.processId || null,
            taskCode: state.nodeInfo.nodeCode,
          }
          if (isRun) {
            data.instanceOperationType = 'RUN_AGAIN'
            data.runAgainType = type
          } else {
            data.instanceOperationType = type
          }
          instanceDag(data).then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '操作成功',
                type: 'success',
              })
              sessionStorage.setItem('checkedNodeGradeIsChangeCode', 'refresh')
            }
          })
        }
      }

      // 展开上下级
      const showFn = (key) => {
        let checkedNodeGrade = JSON.parse(sessionStorage.getItem('checkedNodeGrade'))
        let isNewNode = true
        checkedNodeGrade.forEach((val) => {
          if (val.nodeCode === state.nodeInfo.nodeCode) {
            val[key] = true
            isNewNode = false
          }
        })
        if (isNewNode) {
          let item = { nodeCode: state.nodeInfo.nodeCode, showPre: false, showPost: false }
          item[key] = true
          checkedNodeGrade.push(item)
        }
        sessionStorage.setItem('checkedNodeGradeIsChangeCode', state.nodeInfo.nodeCode)
        sessionStorage.setItem('checkedNodeGrade', JSON.stringify(checkedNodeGrade))
      }

      const params = toRefs(state)

      return {
        ...params,
        getNodeByType,
        offlineJobTypeList,
        getAssetsImages,
        seeLogFn,
        setFn,
        showFn,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .box {
    position: relative;
    box-sizing: border-box;
    width: auto;
    height: 38px;
    text-align: center;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
    &.isSee {
      border: 1px solid #99c9ff;
      &.checked {
        border: 2px solid #99c9ff;
      }
    }
    &:hover {
      background: #ebf4ff;
    }
    .nodeBoxBorder {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 4px;
      &.green {
        border: 1px solid #2ca340;
      }
      &.red {
        border: 1px solid #f63838;
      }
      &.gray {
        border: 1px solid #8091b7;
      }
      &.orange {
        background: linear-gradient(90deg, #ff7d00 50%, transparent 0) repeat-x,
          linear-gradient(90deg, #ff7d00 50%, transparent 0) repeat-x,
          linear-gradient(0deg, #ff7d00 50%, transparent 0) repeat-y,
          linear-gradient(0deg, #ff7d00 50%, transparent 0) repeat-y;
        animation: linearGradientMove 0.3s infinite linear;
      }
      &.nodeBgCss {
        background-size: 8px 2px, 8px 2px, 2px 8px, 2px 8px;
        background-position: 0 0, 0 100%, 0 0, 100% 0;
      }
    }
    @keyframes linearGradientMove {
      100% {
        background-position: 8px 0, -8px 100%, 0 -8px, 100% 8px;
      }
    }
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 4px;
      overflow: hidden;
      border-radius: 4px;
      position: relative;
      .text {
        box-sizing: border-box;
        width: calc(100% - 48px);
        max-width: 300px;
        padding: 0 10px;
        overflow: hidden;
        color: #1d2129;
        font-weight: 400;
        font-size: 14px;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        font-family: Source Han Sans CN;
      }
      .case {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        position: relative;
        border-radius: 4px;
        .pic {
          width: 24px;
          height: 24px;
        }
        &.more {
          background-color: transparent;
          .icon {
            color: #606266;
          }
          &:hover {
            cursor: pointer;
            .icon {
              color: #1e89ff;
            }
          }
        }
        .icon {
          width: 16px;
          height: 16px;
          color: #fff;
          font-size: 16px;
        }
      }
    }
  }
</style>
