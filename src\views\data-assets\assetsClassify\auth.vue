<template>
  <section class="container">
    <section class="container-box">
      <div class="page-title">
        {{ route.query.name }}
        <div class="detail-back-box" @click.prevent="$router.go(-1)"> 返回 </div>
      </div>
      <section class="container-box-table auth">
        <CfTable
          ref="multipleTableRef"
          :key="state.tableList"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
          }"
          :actionWidth="66"
          :paginationConfig="{
            total: state.total,
            pageSize: state.filterSearch.pageSize,
            currentPage: state.filterSearch.pageNum,
            onCurrentChange: (v) => {
              state.filterSearch.pageNum = v
              onSearch()
            },
            onSizeChange: (v) => {
              state.filterSearch.pageSize = v
              state.filterSearch.pageNum = 1
              onSearch()
            },
          }"
          :table-head-titles="state.tableHeadTitles"
        >
          <template #pageTop>
            <div class="table-top">
              <div class="btn active" @click.prevent="createDialogRef.open($route.query.code)">
                <SvgIcon class="icon" icon="new-add" />
                新建授权
              </div>
            </div>
          </template>
          <template #editor="{ data: { row } }">
            <el-button type="primary" link @click.prevent="delFn(row)"> 删除 </el-button>
          </template>
        </CfTable>
      </section>
    </section>
    <CreateAuthDialog ref="createDialogRef" @success="onSearch" />
  </section>
</template>
<script setup>
  import { ElMessage } from 'element-plus'
  import { getAssetsClassifyRoleList, deleteAssetsClassifyRole } from '@/api/sceneManage'
  import { useRoute } from 'vue-router'
  import CreateAuthDialog from './components/createAuthDialog.vue'
  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const state = reactive({
    open: false,
    tableList: [],
    treeSearchText: '',
    filterSearch: {
      condition: {
        classificationCode: route.query.code,
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    total: 0,
    tableHeadTitles: [
      {
        name: '角色名称',
        prop: 'roleName',
      },
      {
        name: '角色ID',
        prop: 'roleId',
      },
      {
        name: '授权人',
        prop: 'createByName',
      },
      {
        name: '授权时间',
        prop: 'createTime',
      },
    ],
  })
  const createDialogRef = ref(null)

  // 获取资产分类列表
  const getAssetsClassifyList = async () => {
    getAssetsClassifyRoleList(state.filterSearch).then(({ data }) => {
      state.tableList = data?.list || []
      state.total = data?.total || 0
    })
  }
  const onSearch = () => {
    getAssetsClassifyList()
  }
  // 删除
  const delFn = (data) => {
    proxy.$dialogPopup({
      title: '是否确认删除？',
      message: '删除后，授权不再展示',
      save: () => {
        deleteAssetsClassifyRole(data).then(({ success }) => {
          if (!success) return
          onSearch()
          ElMessage.success('删除成功')
        })
      },
    })
  }
  getAssetsClassifyList()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 10px;
      display: flex;
      flex-direction: column;
      &-table {
        flex: 1;
        height: calc(100% - 60px);
        display: flex;
        gap: 10px;
        border-radius: 0px 0px 2px 2px;
        background: var(--100, #fff);
      }
    }
  }
  .cf-tools {
  }
  .page-title {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    height: 46px;
    padding: 0 16px;
    color: #1d2129;
    font-weight: bolder;
    font-size: 16px;
    background-color: #fff;
    border-radius: 2px;
    &[sub-label]::after {
      margin-left: 12px;
      content: attr(sub-label);
      display: inline-block;
      padding: 0px 4px;
      border-radius: 2px;
      line-height: 22px;
      border: 1px solid rgba(26, 164, 238, 0.4);
      background: rgba(26, 164, 238, 0.08);

      color: #1aa4ee;

      font-family: 'Source Han Sans CN';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }

    .detail-back-box {
      position: absolute;
      top: 0;
      right: 16px;
      bottom: 0;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 30px;
      margin: auto;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      color: #1d2129;
      font-weight: normal;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        color: #479dff;
        border: 1px solid #479dff;
      }
    }
  }
  :deep(.page-top) {
    display: flex;
    padding: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    .btn {
      display: flex;
      padding: 4px 16px;
      align-items: center;
      gap: 4px;
      border-radius: 2px;
      border: 1px solid var(---, #1e89ff);
      color: #1e89ff;
      font-size: 14px;

      &.active,
      &:hover {
        color: #fff;
        background: #1e89ff;
      }
    }
  }
</style>
