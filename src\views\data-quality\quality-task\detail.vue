<template>
  <div class="config container">
    <section class="form-content">
      <div class="form-content-name">
        <moduleName :info="{ name: '质量任务调度信息' }" />
        <section class="form-content-config">
          <!-- <h4>调度配置</h4> -->
          <div class="form-bg white">
            <div class="row">
              <div class="check-title">执行时间</div>
              <n-input :disabled="true" class="check-date" v-model="ruleForm.startDate" />
              <n-input
                :disabled="true"
                class="check-date-time"
                v-model="ruleForm.time"
                placeholder="00:00:00"
              />
            </div>
            <div class="row">
              <div class="check-title">作业频次</div>
              <n-input
                v-if="ruleForm.period === 'day'"
                :disabled="true"
                class="check-date"
                value="日"
              />
              <n-input
                v-else-if="ruleForm.period === 'week'"
                :disabled="true"
                class="check-date"
                value="周"
              />
              <n-input v-else :disabled="true" class="check-date" value="月" />
              <n-input
                v-model="ruleForm.rateTime"
                :disabled="true"
                class="check-date-time"
                placeholder="00:00:00"
              />
            </div>
          </div>
        </section>
      </div>
      <div class="form-content-name">
        <moduleName :info="{ name: '质量规则配置信息' }" />
        <div class="form-content-name-desc">{{ modelName ? modelName : '暂无描述' }} </div>
      </div>
      <section class="form-content-box">
        <n-form
          ref="ruleForm"
          :data="ruleForm"
          :rules="rules"
          label-position="end"
          class="ruleForm stepTwo"
        >
          <div class="form-step-field">
            <n-public-table
              :isNeedSelection="false"
              :isDisplayAction="false"
              :table-head-titles="tableHeadTitles"
              :pagination="pagination"
              :tableData="tableData"
              :tableHeight="tableHeight"
              :actionWidth="180"
              @tablePageChange="checkPageChange($event, 'pagination')"
            >
              <template #ruleBox="{ editor }">
                <div class="rule-box">
                  <template v-for="(item, ind) in editor.row.rules">
                    <div
                      v-if="ind < 4"
                      :key="item.id"
                      :class="
                        item.ruleType === 'STANDARD' ? 'rule-box-label checked' : 'rule-box-label'
                      "
                      @click="showRuleFn(editor.row)"
                      ><div class="rule-box-label-name"
                        >{{ item.name }}<text class="weight">{{ item.weight }}</text></div
                      >
                      <n-popover
                        v-if="!isSee && item.ruleType !== 'STANDARD'"
                        class="item"
                        content="删除"
                        trigger="hover"
                        :position="['bottom']"
                      >
                        <SvgIcon
                          class="icon-close"
                          icon="icon-close-gray"
                          @click.prevent.stop="delRuleFn(editor.row, item.id)"
                        />
                      </n-popover>
                    </div>
                  </template>
                  <n-popover
                    v-if="editor.row.rules.length > 4"
                    class="item"
                    content="更多"
                    trigger="hover"
                    :position="['bottom']"
                  >
                    <SvgIcon
                      class="rule-box-more"
                      icon="icon-spot"
                      @click="showRuleFn(editor.row)"
                    />
                  </n-popover>
                </div>
              </template>
            </n-public-table>
          </div>
        </n-form>
      </section>
    </section>
    <footer class="footer">
      <n-button variant="solid" @click.prevent="cancelFn">返回</n-button>
    </footer>
    <!--校验规则弹窗-->
    <n-modal
      v-model="showRule"
      class="rule-dialog commonDialog"
      :close-on-click-overlay="false"
      width="580px"
      @close="showRule = false"
    >
      <template #header>
        <n-modal-header>
          <span slot="title"><moduleName :info="{ name: '校验规则' }" /></span>
        </n-modal-header>
      </template>
      <div class="rule-content scroll-bar-style">
        <span
          v-for="(item, index) in showRuleList"
          :key="index"
          :class="
            item.ruleType === 'STANDARD' ? 'rule-content-label checked' : 'rule-content-label'
          "
          >{{ item.name }}<text class="weight">{{ item.weight }}</text></span
        >
      </div>
      <template #footer>
        <n-modal-footer>
          <n-button variant="solid" @click.prevent="showRule = false">关闭</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import moduleName from '@/components/ModuleName'
  import dataTransfer from '@/components/DataTransfer'
  // import { checkCName } from '@/utils/validate'

  export default {
    name: 'DataQualityTaskConfig',
    components: { moduleName, dataTransfer },
    data() {
      return {
        id: '',
        hasClick: false, // 防止多次点击
        stepsData: [{ label: '配置检验规则' }, { label: '配置调度策略' }],
        stepIndex: 0,
        ruleForm: {
          taskType: true,
          name: '',
          description: '',
          extent: '',
          startDate: '',
          time: '',
          rateTime: '',
          scheduleId: '',
          period: 'day',
        },
        rules: {
          // name: [{ required: true, validator: checkCName, trigger: 'blur' }],
          period: [{ required: true, message: '请选择周期', trigger: 'blur' }],
          startDate: [{ required: true, message: '请选择任务执行时间', trigger: 'blur' }],
          rateTime: [{ required: true, message: '请选择周期检测时间', trigger: 'blur' }],
        },
        weekList: [
          { label: '周一', value: 1 },
          { label: '周二', value: 2 },
          { label: '周三', value: 3 },
          { label: '周四', value: 4 },
          { label: '周五', value: 5 },
          { label: '周六', value: 6 },
          { label: '周日', value: 7 },
        ],
        monthList: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
          { prop: 'rule', name: '校验规则', width: '500px', slot: 'ruleBox' },
        ],
        exceptHeight: 0,
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        tableData: { list: [] },
        tableHeight: 525,
        checkedRule: null,
        loading: false,
        // 引用规则弹窗
        addRuleDialog: false,
        addForm: {
          id: '',
          cnName: '',
        },
        addRules: {},
        ruleIds: null,
        ruleList: [],
        delList: [], // 批量移除选中列表
        // 校检规则弹窗
        showRule: false,
        showRuleList: [],
        timeoutFlag: null,
        isSee: false,
        modelName: '',
        modelDescription: '',
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    mounted() {
      this.tableHeight = document.body.offsetHeight - 484
      this.modelId = this.$route.query.modelId
      this.getRulesListFn('')
      // this.checkedRule = JSON.parse(sessionStorage.getItem('ruleData'))
      if (this.$route.query.isSee) {
        this.isSee = true
      }
      this.getModelDetailFn(this.$route.query.modelId)
      // 设置月
      let monthList = []
      for (let i = 1; i <= 31; i++) {
        monthList.push({
          label: i + '号',
          value: i,
        })
      }
      this.monthList = monthList
      // 获取调度配置
      if (this.modelId) {
        this.id = this.$route.query.modelId
        this.$api.dataQuality.getTaskDetail({ id: this.id }).then((res) => {
          if (res.code === 'SUCCESS') {
            let startDate = '',
              time = '00:00:00'
            if (res.data.schedule) {
              if (res.data.schedule.startTime) {
                startDate = res.data.schedule.startTime.slice(0, 10).replace(/-/g, '/')
                time = res.data.schedule.startTime.slice(11, 20)
              }
              if (res.data.schedule.extent) {
                res.data.schedule.extent = Number(res.data.schedule.extent)
              }
            }
            this.ruleForm = {
              taskType: true,
              name: '',
              description: '',
              startDate: startDate,
              time: time,
              extent: res.data.schedule ? res.data.schedule.extent : '',
              rateTime: res.data.schedule ? res.data.schedule.rateTime : '00:00:00',
              period: res.data.schedule ? res.data.schedule.period : '',
              scheduleId: res.data.schedule ? res.data.schedule.id : '',
            }
            this.$nextTick(() => {
              let checkedRule = []
              res.data.mapping.forEach((val) => {
                checkedRule.push({
                  id: val.columnId,
                  ruleData: val.rules || [],
                })
              })
              this.checkedRule = checkedRule
              this.onSearch(res.data.mapping)
            })
          }
        })
      } else {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    },
    beforeUnmount() {},
    methods: {
      disabledDate(time) {
        return time.getTime() < Date.now() - 8.64e7
      },
      // 通过id获取模型详情
      getModelDetailFn(modeId) {
        this.$api.model.getModalDetail({ id: modeId }).then((res) => {
          if (res.code === 'SUCCESS') {
            this.modelName = res.data.cnName
            this.modelDescription = res.data.description
          }
        })
      },
      // 规则搜索输入
      ruleMethod(name) {
        if (this.timeoutFlag) {
          clearTimeout(this.timeoutFlag)
          this.timeoutFlag = null
        }
        this.timeoutFlag = setTimeout(() => {
          this.getRulesListFn(name)
        }, 1000)
      },
      // 下拉框聚焦事件
      focusFn() {
        this.getRulesListFn('')
      },
      // 获取规则列表
      getRulesListFn(name) {
        this.loading = true
        let params = {
          name: name,
        }
        this.$api.dataQuality
          .getCommonRuleList(params)
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.loading = false
              this.ruleList = res.data
            }
          })
          .catch(() => {
            this.loading = false
          })
      },
      // 周期变化事件
      radioChangeFn() {
        this.ruleForm.extent = ''
      },
      // 点击取消
      cancelFn() {
        this.$router.go(-1)
      },
      // 表格页码
      checkPageChange(item, pagination) {
        this[pagination].currentPage = item.currentPage
        this[pagination].pageSize = item.pageSize
        let list = this.tableData.list.slice(
          (item.currentPage - 1) * item.pageSize,
          item.currentPage * item.pageSize,
        )
        if (list.length === 0 && item.currentPage > 1) {
          list = this.tableData.list.slice(
            (item.currentPage - 2) * item.pageSize,
            (item.currentPage - 1) * item.pageSize,
          )
        }
        // 重新绑定序号
        list.map((item, index) => {
          return Object.assign(item, { number: index + 1 })
        })
        this.tableData = {
          pageNum: item.currentPage,
          pageSize: item.pageSize,
          list: list,
          total: list.length,
        }
      },
      // 检测部分表单数据
      checkPartFormFn(arr, formName) {
        let isTrue = new Promise((resolve) => {
          this.$refs[formName].validateField(arr, (val) => {
            resolve(val)
          })
        })
        return isTrue
      },
      // 保存规则
      saveRuleFn(sessionRule) {
        this.tableData.list.forEach((val) => {
          sessionRule.forEach((rule) => {
            if (val.id === rule.id) {
              //替换对应行ruleIds
              val.rules = rule.ruleData
            }
          })
        })
      },
      // 保存引入规则
      saveForm() {
        const ruleDataList = this.$refs.dataTransferFn.getCheckedRuleData()
        if (ruleDataList) {
          const ruleData = {
            ruleData: ruleDataList,
            id: this.addForm.id,
          }
          if (this.checkedRule) {
            const isHave = this.checkedRule.some((item, index) => {
              if (item.id === ruleData.id) {
                this.checkedRule[index] = ruleData
              }
              return item.id === ruleData.id
            })
            if (!isHave) {
              this.checkedRule.push(ruleData)
            }
          } else {
            this.checkedRule = [{ ...ruleData }]
          }
          this.ruleIds = null
          this.saveRuleFn(this.checkedRule)
          this.addRuleDialog = false
        }
      },
      // 点击引入规则
      addRuleFn(item) {
        this.addRuleDialog = true
        this.addForm = {
          id: item.id,
          cnName: item.cnName,
        }
        this.ruleIds = JSON.stringify(item.rules)
      },
      // 查看已经引入的规则
      showRuleFn(item) {
        this.showRuleList = item.rules
        this.showRule = true
      },
      // 移除规则
      delRuleFn(item, ruleId) {
        item.rules = item.rules.filter((val) => {
          return val.id !== ruleId
        })
        this.checkedRule.map((rlueItem) => {
          if (item.id === rlueItem.id) {
            rlueItem.ruleData = item.rules
          }
        })
      },
      //单个移除
      onlyDelFn(list) {
        if (this.tableData?.list.length > 1) {
          this.$dialogPopup({
            title: '是否确认删除元数据？',
            message: '删除后，元数据将不会被校检',
            save: () => {
              this.delFn(list)
            },
          })
        } else {
          this.$notify({
            title: '提示',
            message: '至少保留一个元数据',
            type: 'warning',
          })
        }
      },
      // 移除
      delFn(delArr) {
        let flag = null
        let newArr = []
        this.tableData.list.forEach((val) => {
          flag = true
          delArr.forEach((v) => {
            if (val.id === v.id) {
              flag = false
            }
          })
          if (flag) {
            newArr.push(val)
          }
        })
        this.tableData.list = newArr
        this.delList = []
        this.checkPageChange(
          { currentPage: this.pagination.currentPage, pageSize: this.pagination.pageSize },
          'pagination',
        )
      },
      onSearch(mapping = []) {
        let params = {
          id: this.modelId,
        }
        this.$api.model.getModeData(params).then((res) => {
          if (res.code === 'SUCCESS') {
            let list = [...res.data]
            list.forEach((item) => {
              mapping.forEach((val) => {
                if (val.columnId === item.id) {
                  item.rules = val.rules
                }
              })
            })
            // 动态添加编号
            list.map((item, index) => {
              return Object.assign(item, { number: index + 1, rules: item.rules || [] })
            })

            if (this.checkedRule) {
              this.saveRuleFn(this.checkedRule)
            }
            let data = {
              pageNum: this.pagination.currentPage,
              pageSize: this.pagination.pageSize,
              list: list,
              total: list.length,
            }
            this.tableData = data
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .config {
    position: relative;
    // padding: 0 10px 76px 10px;
    .form-content {
      // margin-top: 10px;
      height: calc(100vh - 125px);
      background-color: #fff;
      border-radius: 4px;
      overflow: hidden;
      // 滚动条的宽度
      &::-webkit-scrollbar {
        width: 4px; // 横向滚动条
        height: 4px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 2px;
      }

      &-name {
        padding: 14px 20px;
        background-color: #fff;

        &-project {
          display: inline-block;
          padding: 2px 14px;
          background-color: #f4f4f4;
          border-radius: 2px;
          color: #666666;
          font-size: 14px;
          margin-top: 6px;
        }

        &-desc {
          margin-top: 10px;
          color: #333333;
          font-size: 12px;
          padding-left: 10px;
          padding-top: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &-box {
        box-sizing: border-box;
        position: relative;
        .ruleForm {
          margin: 0 auto;
          position: relative;
          z-index: 2;

          :deep(.el-form-item__label) {
            padding: 0;
          }

          .form-item-title {
            margin-bottom: 12px;

            :deep(.el-form-item__content) {
              line-height: normal;

              .small-title {
                color: #333333;
                font-size: 14px;
                margin-right: 12px;
                font-weight: bold;
                vertical-align: middle;
              }
            }
            .check-radio {
              display: inline-block;
              vertical-align: middle;
              line-height: 32px;
              height: 32px;
              padding-right: 60px;
              border: 1px solid #dcdfe6;
              box-sizing: border-box;
              margin-right: 10px;
              border-radius: 2px;
              background-color: #fff;
              &-name {
                display: inline-block;
                color: #666666;
                height: 18px;
                line-height: 18px;
                font-size: 12px;
                vertical-align: top;
                padding: 0 10px;
                margin: 7px 60px 7px 0;
                border-right: 1px solid #ebedf0;
              }
            }

            :deep(.check-time) {
              width: 220px;
              box-sizing: border-box;

              .el-input__wrapper {
                width: 100%;
              }
            }

            .check-time-box {
              display: inline-block;
              vertical-align: middle;
              width: 242px;
              height: 32px;
              line-height: 32px;
              box-sizing: border-box;
              overflow: hidden;
              border-radius: 2px;
              background-color: #fff;
              border: 1px solid #dcdfe6;

              :deep(.el-select) {
                width: 120px;
                position: relative;
                vertical-align: 0;

                .el-input.is-focus {
                  .el-input__wrapper {
                    box-shadow: none !important;
                  }
                }

                .el-input__wrapper {
                  box-shadow: none;

                  &.is-focus {
                    box-shadow: none !important;
                  }

                  &:before {
                    content: '';
                    width: 1px;
                    height: 22px;
                    position: absolute;
                    right: 0;
                    top: 5px;
                    background-color: #dcdfe6;
                  }
                }
              }

              :deep(.check-time) {
                width: 120px;
                vertical-align: 0;

                .el-input__wrapper {
                  box-shadow: none;
                  height: 32px;
                }

                &.lang {
                  width: 100%;
                }
              }
            }

            .check-period {
              width: 315px;
              margin-right: 5px;
            }
          }

          .form-step-field {
            padding: 20px;

            &-box {
              margin-bottom: 20px;
            }
          }

          .form-step-second {
            padding-top: 40px;
            width: 638px;
            margin: 0 auto;
          }

          .rule-box {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 10px 0;

            &-label {
              width: 100px;
              padding: 0 10px;
              line-height: 28px;
              background-color: #f2f3f6;
              border-radius: 14px;
              margin-right: 10px;
              cursor: pointer;
              position: relative;
              &.checked {
                background-color: #f0f7ff;
              }
              &-name {
                width: 100%;
                color: #333333;
                text-align: center;
                font-size: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding-right: 20px;
                position: relative;
                .weight {
                  position: absolute;
                  top: 0;
                  bottom: 0;
                  right: 0;
                  margin: auto;
                  padding-left: 6px;
                  text-align: center;
                  height: 14px;
                  line-height: 14px;
                  border-left: 1px solid #c8c9cc;
                }
              }
              .icon-close {
                display: none;
                position: absolute;
                width: 18px;
                height: 18px;
                top: -6px;
                right: -6px;
              }
              &:hover {
                .icon-close {
                  display: block;
                }
              }
            }

            &-more {
              color: #333333;
              cursor: pointer;

              &:hover {
                color: $themeBlue;
              }
            }
          }
        }

        &-bg {
          position: absolute;
          z-index: 1;
          width: 252px;
          height: 232px;
          right: 0;
          bottom: 0;
        }
      }
      &-config {
        border-radius: 8px;
        // border: 1px solid #eeeeee;
        height: 124px;
        margin-top: 20px;
        display: flex;
        align-items: center;
        h4 {
          font-size: 12px;
          margin: -53px 20px 0 60px;
        }
        .form-bg {
          width: 100%;
          margin-left: 10px;
          .row {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 20px;
            .check-date-time {
              width: 292px;
              margin-left: 10px;
            }
          }

          .check-title {
            display: inline-block;
            color: #333;
            font-size: 12px;
            margin-right: 20px;
          }

          .check-date,
          .check-time {
            width: 292px;
          }

          .check-period {
            width: 196px;
            margin-left: 6px;
          }

          .input-number-box {
            display: inline-block;
          }
          .check-time,
          .check-time-time {
            margin-top: 20px;
            width: 292px;
            margin-left: 10px;
            &.is-disabled {
              width: 292px;
            }
            .el-input__wrapper {
              width: 100%;
            }
            :deep(.el-input-group__prepend) {
              width: 44px;
              text-align: center;
              height: 32px;
              line-height: 32px;
              color: #666;
              font-size: 12px;
              border-right: none;
              position: relative;
              &:before {
                content: '';
                width: 1px;
                height: 18px;
                background-color: #ebedf0;
                position: absolute;
                top: 0;
                bottom: 0;
                right: 0;
                margin: auto;
              }
            }
            :deep(.el-input-group__append) {
              width: 32px;
              text-align: center;
              height: 32px;
              line-height: 32px;
              color: #646566;
              font-size: 12px;
              background-color: #f7f8fa;
              border-radius: 0 1px 1px 0;
              border-left: none;
            }
            :deep(.el-input__wrapper) {
              height: 32px;
              line-height: 32px;
              border: 1px solid #dcdfe6;
              border-left: none;
              box-shadow: none;
            }
            .el-input-number {
              width: 100%;
              box-sizing: border-box;
              &.is-disabled {
                :deep(.el-input__wrapper) {
                  border-left: 1px solid #dcdfe6;
                }
              }
              :deep(.el-input__inner) {
                text-align: left;
              }
            }
          }
        }
      }
    }
    .inlineBlock {
      width: calc(100% - 80px);
      margin-left: 80px;
      :deep(.el-transfer) {
        --el-transfer-panel-width: 320px;
        .el-transfer__buttons {
          padding: 0 69px;
        }
      }
    }

    .publicFormCss {
      .el-input .el-input__inner,
      .el-textarea .el-textarea__inner {
        background: #f7f8fa;
        border-radius: 2px;
        border: 1px solid #e1e1e1;

        ::-webkit-input-placeholder {
          color: #666666;
        }
      }
      :deep(.el-input .el-input__inner) {
        height: 32px;
        color: #333333;
      }
    }
    // 查看下样式
    .only-detail {
      .el-form {
        :deep(.el-input .el-input__inner),
        :deep(.el-textarea .el-textarea__inner) {
          background: #f7f8fa;
        }
        :deep(.password-form-item) {
          .el-form-item__content {
            position: relative;
            .show-password {
              width: 30px;
              text-align: center;
              position: absolute;
              top: 0;
              right: 0;
              cursor: pointer;
            }
          }
        }
        :deep(.is-disabled) {
          .el-input__suffix {
            display: none;
          }
        }
      }
    }
    .footer {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 60px;
      line-height: 60px;
      background: #fff;
      border-top: 1px solid rgba(200, 200, 200, 0.35);
      text-align: center;
    }
  }

  .addForm {
    margin-top: 16px;
    .ruleSelect {
      width: 100%;
    }
  }

  .rule-dialog {
    .nancalui-modal__body {
      .rule-content {
        max-height: 184px;
        overflow-y: auto;

        &-label {
          display: inline-block;
          /*width: 76px;*/
          padding: 0 14px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 10px;
          margin-bottom: 14px;
          background-color: #f2f3f6;
          border-radius: 12px;
          color: #333333;
          font-size: 12px;
          &.checked {
            background-color: #f0f7ff;
          }
          .weight {
            margin-left: 6px;
            padding-left: 6px;
            text-align: center;
            height: 14px;
            line-height: 14px;
            border-left: 1px solid #c8c9cc;
          }
        }
      }
    }
  }
  :deep(.demo-ruleForm) {
    width: 100%;
    margin: 0 auto;
  }
</style>
