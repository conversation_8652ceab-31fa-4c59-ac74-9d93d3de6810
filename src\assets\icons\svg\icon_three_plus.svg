<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>新建@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数字孪生-三维项目管理-三维场景编辑器" transform="translate(-20.000000, -726.000000)" fill-rule="nonzero">
            <g id="场景" transform="translate(0.000000, 488.000000)">
                <g id="树状图" transform="translate(6.000000, 37.000000)">
                    <g id="测试库备份" transform="translate(0.000000, 190.000000)">
                        <g id="新建" transform="translate(14.000000, 11.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="currentColor" x="0" y="0" width="16" height="16"></rect>
                            <path d="M8.02397577,2.01666665 C8.35032557,2.01709498 8.62018648,2.25795709 8.66620149,2.57145724 L8.67312325,2.66751863 L8.66666667,7.34966609 L13.3333333,7.35 C13.6923184,7.35 13.9833333,7.64101491 13.9833333,8 C13.9833333,8.32635008 13.7428251,8.59652646 13.4293856,8.64295233 L13.3333333,8.65 L8.66466667,8.64966609 L8.6591423,13.3341853 C8.65867177,13.6931701 8.36727567,13.9838033 8.00829089,13.9833333 C7.68194109,13.982905 7.41208018,13.7420429 7.36606518,13.4285428 L7.35914342,13.3324814 L7.36466667,8.64966609 L2.66666667,8.65 C2.30768158,8.65 2.01666667,8.35898509 2.01666667,8 C2.01666667,7.67364992 2.25717486,7.40347354 2.57061443,7.35704767 L2.66666667,7.35 L7.36666667,7.34966609 L7.37312437,2.6658147 C7.37359489,2.30682992 7.66499099,2.0161967 8.02397577,2.01666665 Z" id="形状结合" fill="currentColor"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>