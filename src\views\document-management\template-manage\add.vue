<template>
  <div class="container">
    <div class="cf-page-title">
      模板配置
      <div class="detail-back-box" @click.prevent="goBack(false)"> 返回 </div>
    </div>
    <div class="white-box nc-p-16" style="height: calc(100% - 128px); overflow: auto">
      <n-form
        :data="state.formData"
        :rules="state.rules"
        ref="formRef"
        label-width="150px"
        label-suffix="："
      >
        <n-row>
          <n-col span="11">
            <n-form-item field="classId" label="模板分类">
              <n-select v-model="state.formData.classId" placeholder="" :allow-clear="true">
                <n-option
                  v-for="item in state.classList"
                  :key="item.id"
                  :name="item.name"
                  :value="item.id"
                />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col span="11" push="2">
            <n-form-item field="name" label="模板名称">
              <n-input v-model="state.formData.name" placeholder="" maxLength="200" />
            </n-form-item>
          </n-col>
        </n-row>
        <n-form-item field="templateType" label="模板类型">
          <div class="file-type">
            <n-radio-group
              v-model="state.formData.templateType"
              direction="row"
              @change="onTypeChange"
              :disabled="state.formData.id"
            >
              <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
                item.name
              }}</n-radio>
            </n-radio-group>
            <span v-if="state.formData.templateType === 'word'" class="word-tips"
              >提交表单前，请先保存word非结构化数据。</span
            >
          </div>
        </n-form-item>
        <n-form-item field="templateData" label="模板内容">
          <div class="docEditorCon" v-if="state.formData.templateType === 'word'">
            <DocumentEditor
              v-if="state.documentServerUrl"
              id="docEditor"
              :documentServerUrl="state.documentServerUrl"
              :config="state.officeConfig"
              :events_onDocumentReady="onDocumentReady"
              :events_onError="onDocumentError"
              :onLoadComponentError="onLoadComponentError"
            />
          </div>
          <div
            id="luckysheet"
            class="luckysheet"
            v-else-if="state.formData.templateType === 'excel'"
          ></div>
        </n-form-item>
      </n-form>
    </div>
    <div class="white-box nc-m-t-10 nc-p-16">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { DocumentEditor } from '@onlyoffice/document-editor-vue'
  import { randomGuid } from '@/utils/index'
  import { onBeforeUnmount, reactive, nextTick, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { exportExcel } from '@/views/data-management/resource-library/export'
  import { showtoolbarConfig } from '../config/sheet'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS,
    formData: { classId: '', name: '', templateType: 'word', templateData: '' },
    rules: {
      classId: [{ required: true, message: '请选择模板分类', trigger: 'change', type: 'number' }],
      name: [{ required: true, message: '请输入模板名称', trigger: 'burl' }],
      templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
      templateData: [{ required: true, message: '请输入模板内容', trigger: 'burl' }],
    },
    classList: [],
    typeList: [
      { name: 'word', id: 'word' },
      { name: 'excel', id: 'excel' },
    ],
    documentServerUrl: '',
    officeConfig: {},
    fileId: '',
  })
  // 模板类型切换
  async function onTypeChange() {
    try {
      if (state.formData.templateType === 'word') {
        if (state.formData.id) {
          const res = await api.documentManage.getOnlineEditor({
            source: 'template',
            fileId: state.formData.id,
          })
          const { documentServerUrl, config } = res.data
          state.officeConfig = config
          state.documentServerUrl = documentServerUrl + '/'
        } else {
          const res = await api.documentManage.getWordNew({
            source: 'template',
          })
          const { documentServerUrl, config, fileId } = res.data
          state.officeConfig = config
          state.documentServerUrl = documentServerUrl + '/'
          state.fileId = fileId
        }
      } else {
        window.luckysheet.destroy()
        nextTick(() => {
          window.luckysheet.create({
            container: 'luckysheet', //luckysheet是容器id
            showinfobar: false,
            lang: 'zh', // 设定表格语言
            gridKey: state.formData.id ? state.formData.id : randomGuid(),
            title: state.formData.name,
            showtoolbarConfig: showtoolbarConfig,
            loadUrl: state.formData.id
              ? location.origin + '/api/govern-document/template/config/loadUrl'
              : '',
          })
        })
      }
    } catch (err) {
      console.log(err)
    }
  }
  // word报错
  function onDocumentError(err) {
    console.log('onDocumentError', err)
  }
  // word加载成功
  function onDocumentReady() {
    console.log('Document is loaded', window.DocEditor.instances.docEditor)
  }
  // word加载失败
  function onLoadComponentError(errorCode, errorDescription) {
    console.log('onLoadComponentError', errorCode, errorDescription)
  }
  // 确定
  const formRef = ref(null)
  function onConfirm() {
    const data = {
      id: state.formData.id || null,
      classId: state.formData.classId,
      name: state.formData.name,
      templateType: state.formData.templateType,
    }
    if (state.formData.templateType === 'word') {
      data.docFileId = state.formData.id || state.fileId
      state.formData.templateData = state.documentServerUrl
    } else {
      // 退出编辑模式，防止获取不到正在编辑的单元格的值
      window.luckysheet.exitEditMode()
      const sheetData = window.luckysheet.getAllSheets()
      data.templateData = JSON.stringify(sheetData)
      state.formData.templateData = data.templateData
    }
    formRef.value.validate().then((valid) => {
      if (valid) {
        let apiType = state.formData.id ? 'templateEdit' : 'templateSave'
        api.documentManage[apiType](data).then((res) => {
          if (res.success) {
            proxy.$message.success('保存成功')
            if (state.formData.templateType === 'excel') {
              exportExcel(
                window.luckysheet.getAllSheets(),
                state.formData.name,
                res.data || state.formData.id,
                'template',
              )
            }
            goBack(true)
          }
        })
      }
    })
  }
  // 取消
  function goBack(refresh = false) {
    if (refresh) {
      router.push({ name: 'templateManageList', query: { refresh: 'true' } })
    } else {
      router.go(-1)
    }
  }
  async function getDetail(id) {
    const res = await api.documentManage.templateDetail({ id })
    state.formData = res.data
  }
  function getClassList() {
    api.documentManage.templateClassList({}).then((res) => {
      state.classList = res.data
      if (!state.formData.id && router.currentRoute.value.query?.classId) {
        state.formData.classId = Number(router.currentRoute.value.query.classId)
      } else {
        state.formData.classId = state.classList[0]?.id
      }
    })
  }
  onMounted(async () => {
    getClassList()
    if (router.currentRoute.value.query.id) {
      await getDetail(router.currentRoute.value.query.id)
    }
    onTypeChange()
  })
  onBeforeUnmount(() => {
    window.luckysheet.destroy()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .docEditorCon {
    width: 100%;
    height: calc(100vh - 370px);
    min-height: 400px;
  }
  .luckysheet {
    position: relative;
    width: 100%;
    height: calc(100vh - 370px);
    overflow: hidden;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
  .file-type {
    display: flex;
    align-items: center;
    .word-tips {
      &::before {
        margin-right: 4px;
        color: #f52f3e;
        font-size: 16px;
        content: '*';
      }
      margin-left: 10px;
      color: #909399;
      font-weight: 400;
      font-size: 12px;
    }
  }
</style>
