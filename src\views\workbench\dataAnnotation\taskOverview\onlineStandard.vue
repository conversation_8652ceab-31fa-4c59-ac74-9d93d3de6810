<template>
  <div class="container" @mouseMove="mouseMove">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <n-button variant="solid" @click="state.tagPopup = true">标注结果</n-button>
      <!-- 自动标注 -->
      <n-button variant="solid" @click="autoTag">自动标注</n-button>
      <n-button variant="solid" @click="saveData">保存</n-button>
      <n-button style="margin-left: 8px" plain @click="closeFn">取消</n-button>
    </div>
    <!-- 预览 -->
    <div
      class="preview-content"
      id="preview-pint"
      ref="previewPintRef"
      v-loading="state.loading"
      element-loading-text="Loading..."
    >
      <FilePreview
        ref="filePreviewRef"
        :option="state.option"
        :type="state.type"
        @pageChange="pageChange"
        @loadComplete="getPreviewData(props.info.id)"
        :data="state.annotationsPage"
        @imageMarkUpdate="imageMarkUpdate"
        @click-polygon="(id,data ,scale, imgOffset = { x: 0, y: 0 })  => {
          tagRefs[id].showTag()
          const maxX = Math.max(...data.map(item => item.x * scale + imgOffset.x))
          const maxY = Math.min(...data.map(item => item.y * scale + imgOffset.y))
          Object.assign(state.annotationsPage[state.currentPage] [id], {x: maxX, y: maxY})
        }"
      />
    </div>
    <template v-for="(item, key) in tagList" :key="key">
      <Tag
        :ref="(ref) => (tagRefs[item.id] = ref)"
        :tagOptions="state.tagOptions"
        :x="item.x"
        :y="item.y"
        @markDelete="markDelete(item)"
        :initialTags="item.tags"
        @tagUpdate="(_) => tagUpdate(_, item)"
        @confirm="filePreviewRef.enable()"
      />
    </template>

    <!-- 标注结果 -->
    <div class="tagPopup" v-show="state.tagPopup">
      <div class="tagPopup-title">
        <span>标注结果</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          @click="state.tagPopup = false"
        >
          <path
            d="M4.5 19.4998L19.4995 4.50023"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4.5 4.50024L19.4995 19.4998"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="tagPopup-btn">
        <!-- 增加段落 -->
        <n-button color="primary" @click="addParagraph">增加段落</n-button>
        <!-- 导出结果 -->
        <n-button color="primary" @click="exportResult">导出结果</n-button>
      </div>
      <div class="tagPopup-content">
        <!-- 自动标注段落 -->
        <div :class="['tagPopup-paragraph']">
          <!-- 标注段落的内容 -->
          <!-- 标题 -->
          <div class="tagPopup-paragraph-title"> 段落1： </div>
          <!-- 内容 -->
          <div class="tagPopup-paragraph-content">
            <div
              class="tagPopup-content-item"
              v-for="(item, key) in state.autoTagResult"
              :key="key"
            >
              <div class="tag-content">
                <h2>内容：</h2>
                <div class="tag-content-text"> {{ item.selectedText }} </div>
              </div>
              <div class="tag-result">
                <h2>标签：</h2>
                <div
                  class="tag"
                  :style="getTagStyle(tag.color)"
                  v-for="(tag, tagKey) in item.tags"
                  :key="tagKey"
                >
                  {{ tag.text }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 标注段落 -->
        <div
          v-for="(item, i) in state.annotationsParagraph"
          :class="['tagPopup-paragraph', item.active && 'active']"
          @click="activeParagraph(item.id)"
          :key="i"
        >
          <!-- 标注段落的内容 -->
          <!-- 标题 -->
          <div class="tagPopup-paragraph-title"> 段落{{ i + 2 }}： </div>
          <!-- 内容 -->
          <div class="tagPopup-paragraph-content">
            <div
              class="tagPopup-content-item"
              v-for="(item, key) in paragraphLinks(item.links)"
              :key="key"
            >
              <div class="tag-content">
                <h2>内容：</h2>
                <div class="tag-content-text"> {{ item.selectedText }} </div>
              </div>
              <div class="tag-result">
                <h2>标签：</h2>
                <div
                  class="tag"
                  :style="getTagStyle(tag.color)"
                  v-for="(tag, tagKey) in item.tags"
                  :key="tagKey"
                >
                  {{ tag.text }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="tagPopup-content-item" v-for="(item, key) in state.tagPopupData" :key="key">
          <div class="tag-content">
            <h2>内容：</h2>
            <div class="tag-content-text"> {{ item.selectedText }} </div>
          </div>
          <div class="tag-result">
            <h2>标签：</h2>
            <div
              class="tag"
              :style="getTagStyle(tag.color)"
              v-for="(tag, tagKey) in item.tags"
              :key="tagKey"
            >
              {{ tag.text }}
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script setup>
  // 导入必要的工具库和组件
  import { throttle, isEmpty } from 'lodash-es' // 用于函数节流和空值判断
  import { ElNotification } from 'element-plus' // Element UI的通知组件
  import { useRouter } from 'vue-router' // Vue路由
  import Tag from '@/views/document-management/document-annotation/tag.vue' // 标签组件
  import FilePreview from '@/views/document-management/document-annotation/components/FilePreview.vue' // 文件预览组件
  import * as d3 from 'd3' // D3.js数据可视化库
  import oldApi from '@/api/index' // API接口
  import { uuid } from '@/utils/index' // 工具函数

  // 初始化路由
  const router = useRouter()
  // 创建预览区域的ref引用
  const previewPintRef = ref(null)
  // 存储标签引用的对象
  const tagRefs = {}
  const filePreviewRef = ref(null)
  let api = { ...oldApi }

  // 定义组件props
  const props = defineProps({
    info: {
      // 文档信息
      type: Object,
      default: () => ({}),
    },
    apis: {
      // 接口配置
      type: Object,
    },
    isPolling: { // 是否轮询
      type: Boolean,
      default: false
    }
  })
  if (props.apis) {
  api = {...props.apis}
  }

  // 定义组件emits事件
  const emit = defineEmits(['close'])

  // 响应式状态管理
  const state = reactive({
    name: '文档预览', // 文档名称
    option: {}, // 预览选项
    type: '', // 文档类型
    loading: false, // 加载状态
    tagOptions: [], // 标签选项列表
    annotations: {}, // 标注数据
    annotationsPage: {}, // 分页标注数据
    currentPage: 1, // 当前页码
    tagPopup: false, // 是否显示标签弹窗
    tagPopupData: [], // 标签弹窗数据
    tagPopCount: 0,
    annotationsParagraph: [], // 标注段落
    autoTagResult: [], // 自动标注结果
  })

  // 页码变化处理函数
  const pageChange = (page) => {
    setTimeout(() => {
      state.currentPage = page // 更新当前页码
      renderWaveLine() // 重新渲染波浪线
    })
  }

  // 获取文档URL
  function getDocUrl(id) {
    return api.documentManage.outsideGet({ id: id }).then((res) => {
      const { name, type, docUrl } = res.data
      state.name = name // 更新文档名称
      state.type = type // 更新文档类型
      state.option = {
        // 更新预览选项
        value: docUrl, // 文档URL
        name: name, // 文档名称
      }
    })
  }

  // 关闭函数
  function closeFn() {
    emit('close') // 触发关闭事件
  }

  // 组件挂载时执行
  onMounted(() => {
    // 获取文档ID并加载文档URL
    const { id } = props.info
    getDocUrl(id).finally(() => {
      switch (true) {
        case ['pdf', 'word'].includes(state.type):
          document.addEventListener('selectionchange', handleTextSelection)
          break
        case state.type.includes('image'):
          break
      }
    })
  })

  // 获取标签列表
  const getTargetList = () => {
    api.dataGovernance.getTaskLabelDetail(props.info.taskId).then((res) => {
      if (res.success) {
        const { data } = res
        state.tagOptions = JSON.parse(data?.labels || '[]')
      }
    })
  }
  getTargetList()
  const handleTextSelection = throttle((e) => {
    // 不是body元素则不处理
    const selection = window.getSelection()
    // 获取选中文字
    const selectedText = selection.toString()

    if (!selection.rangeCount) return
    const range = selection.getRangeAt(0)
    const selectedElement = range.commonAncestorContainer
    // 检查选区是否在目标容器内（例如 class="allowed-container"）
    console.log('selectedElement', selectedElement)
    const box = document.getElementById('select-text-layer_232')
    if (!box.contains(selectedElement)) {
      console.log('选中的内容不在允许范围内')
      return
    }

    // 选择data-temp为true的div
    // 清除之前的波浪线
    document.querySelectorAll('[data-temp="true"]').forEach((el) => el.remove())

    const waveContainer = document.getElementById('wave-container')
    // 获取waveContainer的位置
    const { top, left, width, height } = waveContainer.getBoundingClientRect()

    const rects = Array.from(range.getClientRects())
    // 过滤掉top位置相近的矩形
    // 重新编排行组
    const sortedRects = []
    state.annotations = {}

    // 获取选中区域
    const boundingClientRect = range.getBoundingClientRect()
    rects.map((rect, index) => {
      rect = JSON.parse(JSON.stringify(rect))
      // 检查是否在四个选中区域内
      if (
        !(
          boundingClientRect.top <= rect.top &&
          boundingClientRect.bottom >= rect.bottom &&
          boundingClientRect.left <= rect.left &&
          boundingClientRect.right >= rect.right
        )
      )
        return
      // 宽度过小的不处理
      if (rect.width < 4 || 100 > rect.height > 4) return
      if (sortedRects.length === 0) {
        sortedRects.push(rect)
        return rect
      }
      // 检查sortedRects与当前rect的y值是否相差大于4px
      const isPush = sortedRects.find((item) => {
        const diffY = Math.abs(item.top - rect.top)
        if (diffY < 8) {
          item.width = Math.max(item.width, rect.right - item.left)
          item.left = Math.min(item.left, rect.left)
          return true
        } else {
          return false
        }
      })
        ? false
        : true
      if (isPush) sortedRects.push(rect)
      console.log('sortedRects', sortedRects)
      return
    })
    if (!sortedRects.length) return
    const annotationId = `wave-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    sortedRects.forEach((rect) =>
      addWaveLine({
        offsetY: rect.height,
        width: rect.width,
        top: rect.top - top,
        left: rect.left - left,
        annotationId,
      }),
    )
    state.annotations[annotationId] = {
      selectedText,
      id: annotationId,
      sortedRects,
      y: boundingClientRect.top - top,
      x: boundingClientRect.right - left,
      width: boundingClientRect.width,
      height: boundingClientRect.height,
      boundingRect: { top, left, width, height },
    }
  }, 100)
  const tagList = computed(() => {
    // state.annotations为空时展示当前页的标注
    if (isEmpty(state.annotations || {})) {
      return state.annotationsPage[state.currentPage] || {}
    }
    const newAnnotations = {
      ...(state.annotationsPage[state.currentPage] || {}),
      ...(state.annotations || {}),
    }
    return newAnnotations
  })
  function markDelete(item) {
    const { id } = item
    // 从state.annotations中删除
    delete state.annotations[id]
    // 从state.annotationsPage[state.currentPage]中删除
    delete state.annotationsPage[state.currentPage][id]
    // 从tagList中删除
    delete tagList.value[id]
    // 从tagRefs中删除
    delete tagRefs[id]
    // 从tagPopupData中删除
    state.tagPopupData = state.tagPopupData.filter((_item) => _item.id !== id)
    // 删除标记完成的波浪线
    document.querySelectorAll(`[data-annotation-id="${id}"]`).forEach((el) => el.remove())
    // 手动触发更新
    state.annotationsPage = { ...state.annotationsPage }
    // 从段落中删除link的id
    state.annotationsParagraph?.forEach((paragraph) => {
      paragraph.links = paragraph.links.filter((_id) => _id !== id)
    })
    filePreviewRef.value.enable()
  }
  function tagUpdate(tags, item) {
    // 获取激活的标注段落项
    const activeParagraph = state.annotationsParagraph?.find((paragraph) => paragraph.active)
    if (activeParagraph) {
      // activeParagraph.links去重
      activeParagraph.links.push(item.id)
      activeParagraph.links = [...new Set(activeParagraph.links)]
    }
    item.tags = tags
    // state.annotationsPage[state.currentPage] = { ...state.annotationsPage[state.currentPage], [annotationId]: { id: annotationId, sortedRects, y: boundingClientRect.top - top, x: boundingClientRect.left - left, width: boundingClientRect.width, height: boundingClientRect.height } }
    state.annotationsPage[state.currentPage] = {
      ...state.annotationsPage[state.currentPage],
      [item.id]: item,
    }
    state.tagPopupData.push(item)
    // 渲染标记完成的波浪线
    Object.values(state.annotationsPage[state.currentPage] || {}).forEach((item) => {
      // 查找data-annotation-id是否为item.id的div，删除
      document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach((el) => el.remove())
      renderWaveLine()
    })
  }
  // 计算属性：通过id获取段落关联的标注
  const paragraphLinks = computed(() => (links) => {
    const tagPopupData = []
    Object.values(state.annotationsPage || {})
      .flatMap((item) => Object.values(item || {}))
      .map((item) => {
        links.find((link) => {
          if (item.id === link) {
            tagPopupData.push(item)
          }
        })
      })
    return tagPopupData
  })

  // 图片标记更新
function imageMarkUpdate(data, scale, imgOffset = { x: 0, y: 0 }) {
    const annotationId = `wave-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    // 遍历data最大值
  const maxX = Math.max(...data.map(item => item.x * scale + imgOffset.x))
  const maxY = Math.min(...data.map(item => item.y * scale + imgOffset.y))
    state.annotations[annotationId] = { id: annotationId, data, type: 'image', x: maxX, y: maxY }
    state.annotationsPage[state.currentPage] = {
      ...state.annotationsPage[state.currentPage],
      [annotationId]: state.annotations[annotationId],
    }
    filePreviewRef.value.disable()
  }
  // 渲染本页的波浪线
  function renderWaveLine() {
    // 清除之前的data-temp为true的div
    document.querySelectorAll('[data-temp="true"]').forEach((el) => el.remove())
    // 重置计数器
    state.tagPopCount = 0
    // 查询
    // 渲染标记完成的波浪线
    Object.values(state.annotationsPage[state.currentPage] || {}).forEach((item) => {
      if (item.sortedRects?.length) {
        const { top, left, width, height } = item.boundingRect

        // 清除重复的
        document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach((el) => el.remove())
        item.sortedRects.forEach((rect) => {
          addWaveLine({
            dataTemp: false,
            offsetY: rect.height,
            width: rect.width,
            top: rect.top - top,
            left: rect.left - left,
            annotationId: item.id,
          })
        })
      }
    })
  }

  // 添加波浪线
  function addWaveLine(
    {
      dataTemp = true,
      offsetY = 0,
      top = 0,
      left = 0,
      width = 2000,
      height = 10,
      wavelength = 20,
      lineWidth = 1,
      annotationId,
    } = {
      dataTemp: true,
      offsetY: 0,
      top: 0,
      left: 0,
      width: 2000,
      height: 10,
      wavelength: 50,
      lineWidth: 1,
      annotationId,
    },
  ) {
    const amplitude = height * 0.5 // 振幅
    const centerY = height / 2 // 中心Y坐标
    const tagData = state.tagPopupData.find((item) => item.id === annotationId)
    let tagColor = '#f00'
    if (tagData) {
      tagColor = getTagStyle(tagData.tags[0].color).color
    }

    const svg = d3
      .select('#wave-container')
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr(
        'style',
        `position: absolute;top: ${
          top + offsetY
        }px;left:${left}px;color: ${tagColor};z-index: 100000;`,
      )
      .attr('data-annotation-id', annotationId)
      .attr('data-temp', dataTemp)

    // 添加点击事件
    svg.on('click', function (event) {
      tagRefs[annotationId]?.showTag()
    })

    const waveData = []
    for (let x = 0; x < width; x++) {
      const y = (amplitude - lineWidth * 2) * Math.sin((x / wavelength) * Math.PI * 2) + centerY
      waveData.push([x, y])
    }
    svg
      .append('path')
      .datum(waveData)
      .attr(
        'd',
        d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1])
          .curve(d3.curveBasis),
      )
      .attr('stroke', 'currentColor')
      .attr('fill', 'none')
  }

  function autoTag() {
    // 传到后端，标注完成后重新渲染
    api.dataGovernance.autoAnnotation({ taskId: props.info.taskId }).then((res) => {
      if (res.success) {
        // 成功
        ElMessage.success('标注完成') // 成功提示
        getAutoTag(props.info.taskId)
      }
    })
  }

  // 获取自动标注结果
  function getAutoTag(taskId) {
    api.dataGovernance.autoAnnotationResult({ taskId: props.info.taskId }).then((res) => {
      if (res.success) {
        const tableHead = res?.data?.excelDataList?.shift?.()?.rowData
        state.autoTagResult = (res?.data?.excelDataList || []).map((item) => {
          const rowData = item.rowData
          const data = {
            selectedText: rowData[0].value,
          }
          // 根据tag名称查询标签
          const tags =
            state.tagOptions?.filter((tag) => tableHead.find((row) => row.value === tag.text)) || []
          data.tags = tags.map((tag) => {
            return { text: tag.text, color: tag.color }
          })
          return data
        })
        state.annotationsParagraph.push({ id: uuid(), text: '', active: length === 0, links: [] })
      }
    })
  }
  getAutoTag(props.info.taskId)

  onUnmounted(() => {
    document.removeEventListener('selectionchange', handleTextSelection)
  })

  function refineSelection() {
    const selection = window.getSelection()
    if (selection.rangeCount === 0) return null

    const originalRange = selection.getRangeAt(0)
    const startContainer = originalRange.startContainer
    const startOffset = originalRange.startOffset
    const endContainer = originalRange.endContainer
    const endOffset = originalRange.endOffset

    // 1. 定位首尾有效内容
    const startNode = findFirstNonEmptyNode(startContainer, startOffset, true)
    const endNode = findLastNonEmptyNode(endContainer, endOffset, true)

    if (!startNode || !endNode) return null

    // 2. 创建新 Range
    const newRange = document.createRange()
    newRange.setStart(startNode.node, startNode.offset)
    newRange.setEnd(endNode.node, endNode.offset)

    // 3. 过滤中间空白节点
    const walker = document.createTreeWalker(
      newRange.commonAncestorContainer,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // 保留非空白文本节点
          return node.textContent.trim() !== ''
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_REJECT
        },
      },
    )

    // 4. 精准调整选区边界
    let firstValidNode = null
    let lastValidNode = null
    while (walker.nextNode()) {
      const node = walker.currentNode
      if (!firstValidNode) firstValidNode = node
      lastValidNode = node
    }

    if (firstValidNode && lastValidNode) {
      newRange.setStart(firstValidNode, 0)
      newRange.setEnd(lastValidNode, lastValidNode.length)
    }

    // 5. 更新选区
    selection.removeAllRanges()
    selection.addRange(newRange)
    return newRange
  }

  // /​**
  //   * 查找起点后的第一个有效节点
  //   * @param { Node } node 起始节点
  //     * @param { number } offset 起始偏移量
  //       * @param { boolean } forward 是否向前查找
  //         */
  function findFirstNonEmptyNode(node, offset, forward) {
    let currentNode = node
    let currentOffset = offset

    while (currentNode) {
      if (currentNode.nodeType === Node.TEXT_NODE) {
        const text = currentNode.textContent.slice(currentOffset)
        if (text.trim() !== '') {
          return { node: currentNode, offset: currentOffset }
        }
      }

      // 遍历下一个节点
      currentNode = forward ? nextNode(currentNode) : previousNode(currentNode)
      currentOffset = 0
    }
    return null
  }

  // /​**​
  //  * 查找终点前的最后一个有效节点
  //  * @param {Node} node 结束节点
  //  * @param {number} offset 结束偏移量
  //  */
  function findLastNonEmptyNode(node, offset) {
    let currentNode = node
    let currentOffset = offset

    while (currentNode) {
      if (currentNode.nodeType === Node.TEXT_NODE) {
        const text = currentNode.textContent.slice(0, currentOffset)
        if (text.trim() !== '') {
          return { node: currentNode, offset: currentOffset }
        }
      }

      // 向前遍历节点
      currentNode = previousNode(currentNode)
      currentOffset = currentNode?.textContent?.length || 0
    }
    return null
  }

  // 辅助函数：获取下一个节点
  function nextNode(node) {
    return node.nextSibling || node.parentNode?.nextSibling
  }

  // 辅助函数：获取上一个节点
  function previousNode(node) {
    return node.previousSibling || node.parentNode?.previousSibling
  }

  // 使用示例：监听鼠标抬起事件
  // document.addEventListener('mouseup', () => {
  //   const cleanedText = refineSelection();
  //   console.log('Refined Selection:', cleanedText);
  // });
  function saveData() {
    api.documentManage
      .saveDocumentAnnotation({
        documentId: props.info.id,
        mark: JSON.stringify(state.annotationsPage),
        paragraph: JSON.stringify(state.annotationsParagraph),
        contents: Object.values(state.annotationsPage || {})
          .flatMap((item) => Object.values(item || {}))
          .map((item) => ({
            markId: item.id,
            content: item.selectedText,
            type: state.type,
            position: JSON.stringify(item?.sortedRects || item?.data || []),
            remarks: JSON.stringify(item?.tags || []),
          })),
      })
      .then((res) => {
        if (res.success) {
                    state.originalData.mark =  JSON.stringify(state.annotationsPage)
            state.originalData.paragraph =  JSON.stringify(state.annotationsParagraph)
          ElNotification({
            title: '提示',
            message: '操作成功！',
            type: 'success',
          })
          api.dataManagement.workbenchAnnotationAuditTag({ id: props.info.taskId }).then((res) => {
            closeFn()
          })
        }
      })
  }
  // 获取预览数据
  function getPreviewData(id) {
    api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
      if (res.success) {
        try {
          const { mark, paragraph } = res.data
          // 保存原始数据
          state.originalData = res.data
          state.annotationsPage = JSON.parse(mark || '{}')
          state.tagPopupData = Object.values(state.annotationsPage[1] || {})
          state.annotationsParagraph = JSON.parse(paragraph || '[]')
          if (state.annotationsParagraph?.length === 0) addParagraph()
          switch (true) {
            case ['pdf', 'word'].includes(state.type):
              renderWaveLine()
              break
            case state.type.includes('image'):
              break
          }
        } catch (error) {
          state.annotationsPage = {}
        }
      }
    })
  }
  const intervalKey =setInterval(() => {
  polling()
}, 1*1000)
  onUnmounted(() => {
    clearInterval(intervalKey) 
  })
  // 轮询获取最新的标注数据
  function polling() {
    if (!props.isPolling || state.isOperating) return
      api.documentManage.getDocumentAnnotationPreview(props.info.id).then((res) => {
        if (res.success) {
          const { mark, paragraph } = res.data  
          // 对比mark和paragraph是否有变化，如果有变化则更新state.annotationsPage和state.annotationsParagraph
          if (mark !== state.originalData?.mark || paragraph !== state.originalData?.paragraph) {
            // 对比差异数据后合并
            const { mark: oldMark, paragraph: oldParagraph } = state.originalData || {}
            // 旧的标注数据
            const oldMarkData = JSON.parse(oldMark || '{}')
            const oldParagraphData = JSON.parse(oldParagraph || '[]')
            // 新的标注数据
            const newMark = JSON.parse(mark || '{}')
            const newParagraph = JSON.parse(paragraph || '[]')
            // 当前的标注数据
            const currentMark = JSON.parse(JSON.stringify(state.annotationsPage)) || {}
            const currentParagraph = JSON.parse(JSON.stringify(state.annotationsParagraph)) || []
            /* 合并 */
            // 获取最大页码
            const maxPage = Math.max(
              ...Object.keys(oldMarkData).map((key) => parseInt(key)), 
              ...Object.keys(newMark).map((key) => parseInt(key)),
              ...Object.keys(currentMark).map((key) => parseInt(key)), 
            )
            // 合并后的数据
            const mergedData = {}
            let mergedParagraph = []
            // 根据页面号合并标注数据
            for (let i = 1; i <= maxPage; i++) {
              mergedData[i] = {}
              // 旧的标注数据
              const oldMarkDataPage = oldMarkData[i] || {}
              const oldParagraphDataPage = oldParagraphData[i] || []  
              // 新的标注数据
              const newMarkDataPage = newMark[i] || {}
              const newParagraphDataPage = newParagraph[i] || []
              // 当前的标注数据
              const currentMarkDataPage = currentMark[i] || {}
              const currentParagraphDataPage = currentParagraph[i] || []
              // 遍历新的标注数据，合并到旧的标注数据中
              console.log('oldMarkDataPage', Object.keys(Object.assign({},oldMarkDataPage,newMarkDataPage,currentMarkDataPage)))
              Object.keys(Object.assign({},oldMarkDataPage,newMarkDataPage,currentMarkDataPage)).forEach((key) => {
                const newMarkData = newMarkDataPage[key]  || {}
                const oldMarkData = oldMarkDataPage[key] || {}
                const currentMarkData = currentMarkDataPage[key] || {} 
                // 若最新的标注为空，则删除
                if (isEmpty(newMarkData)) {
                  delete mergedData[i][key]
                  return 
                }
                // 合并
                mergedData[i][key] = {
                  ...oldMarkData,
                  ...currentMarkData,
                  ...newMarkData, 
                }
              })
            }
            const mergedDataString = JSON.stringify(mergedData)
          state.originalData.mark = mergedDataString
state.annotationsPage = mergedData

            // 合并段落数据
            mergedParagraph = [...new Set([...oldParagraphData, ...newParagraph, ...currentParagraph].map((item) =>item.id))]
            .map((id) => {
              const newParagraphData = newParagraph.find((item) => item.id === id) || {}
              const oldParagraphData2 = oldParagraphData.find((item) => item.id === id) || {}
              const currentParagraphData = currentParagraph.find((item) => item.id === id) || {} 
              // 合并
              return {
               ...oldParagraphData2,
               ...currentParagraphData,
               ...newParagraphData,
              }
            })

            const mergedParagraphDataString = JSON.stringify(mergedParagraph)
            state.annotationsParagraph = mergedParagraph
            state.originalData.paragraph = mergedParagraphDataString

          }
        }
      })
  }

  // 获取标签样式
  function getTagStyle(tag) {
    const color = tag.split('_')
    return {
      color: color[0],
      background: color[1],
      borderColor: color[2],
    }
  }

  // 激活段落
  function activeParagraph(id) {
    state.annotationsParagraph.forEach((t) => {
      t.active = t.id === id
    })
  }
  function addParagraph() {
    // 数组长度
    const length = state.annotationsParagraph.length
    // 关联标注
    state.annotationsParagraph.push({ id: uuid(), text: '', active: length === 0, links: [] })
  }
  // 导出
  function exportResult() {
    // 通过文件流下载文件
    api.documentManage.exportDocumentAnnotationExcel(props.info.id).then((blob) => {
      // 下载文件
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '标注结果.xlsx'
      link.click()
      URL.revokeObjectURL(link.href)
    })
  }
  // 操作事件
  function mouseMove(type) {
//标记是否在操作
    state.isOperating = true;    
    // 五秒后恢复
    setTimeout(() => {
      state.isOperating = false; 
    }, 5000);
  }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .container {
    width: 100%;
    height: 100%;
    min-width: 1100px;
    position: relative;
    // overflow-y: auto;
    padding-bottom: 0;
  }

  .preview-content {
    padding: 10px;
    background: #fff;
  }

  :deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;

    .img-Preview {
      width: 100% !important;
      height: max-content;
      min-height: 100%;
      overflow-x: unset;
      overflow-y: unset;
    }
  }

  .title {
    width: calc(100% - 80px);
  }

  .tagPopup {
    position: absolute;
    top: 0;
    right: 0;
    width: 400px;
    height: calc(100% - 159px);
    background: #fff;
    z-index: 100000;
    border: 1px solid #ccc;

    &-title {
      position: relative;
      font-size: 16px;
      font-weight: bold;
      line-height: 52px;
      text-align: center;
      border-bottom: 1px solid #ccc;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;

      svg {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }

      ::before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }

    &-btn {
      padding: 8px 16px;
    }
    &-paragraph {
      padding: 10px;
      min-height: 300px;
      border: 3px dashed #ccc;
      border-radius: 4px;
      margin-bottom: 10px;
      padding: 10px;
      cursor: pointer;
      // 激活状态
      &.active {
        border-color: #1e89ff;
        // 动画
        animation: pulse 1s infinite;
        @keyframes pulse {
          0% {
            border-color: #1e89ff;
          }
          50% {
            border-color: #ccc;
          }
          100% {
            border-color: #1e89ff;
          }
        }
      }
    }

    &-content {
      height: calc(100% - 53px - 48px);
      padding: 10px;
      overflow-y: auto;

      &-item {
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;
        background: #f5f5f5;

        h2 {
          font-size: 14px;
          font-weight: bold;
          line-height: 32px;
          margin: 0;
          border: none;
        }

        .tag-content-text {
          font-size: 14px;
          line-height: 32px;
          color: #333;
        }

        .tag-result .tag {
          font-size: 14px;
          line-height: 32px;
          color: #333;
          display: inline-block;
          padding: 0 14px;
          border: 1px solid transparent;
          border-radius: 4px;
          margin-right: 10px;
        }
      }
    }
  }
</style>
<style>
  #wave-container,
  #tag-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100000;
    width: 0;
    height: 0;
  }
</style>
