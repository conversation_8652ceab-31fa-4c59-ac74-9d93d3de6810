import G6 from '@antv/g6'
// 注册node
G6.registerNode('meta-node', {
  draw: (cfg, group) => {
    const shape = group.addShape('dom', {
      attrs: {
        width: cfg.data.size,
        height: cfg.data.size,
        html: `<div id=${cfg.data.id} class="metadata-map-node ${
          cfg.data.checked ? 'checked' : ''
        } ${cfg.data.colorClass} ${cfg.data.isSee ? 'isSee' : ''} ${
          cfg.data.isMainBody ? 'isMainBody' : ''
        }" title=${cfg.data.name}><div class="text">${cfg.data.name}</div></div>`,
      },
      draggable: false,
      name: 'meta-shape',
    })
    return shape
  },
})
