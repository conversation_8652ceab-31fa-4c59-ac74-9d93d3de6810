<template>
  <div :class="{ container: true, isLzos: state.isLzos }">
    <div class="tools">
      <div class="row">
        <div class="add-box-top-title">
          <div class="common-section-header">
            <div class="title">文字识别</div>
            <div class="detail-back-box">
              <div class="detail-back-box-btn" @click.prevent="cancelFn"
                ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
              </div>
              <div class="detail-back-box-btn checked" @click.prevent="closeFn"
                ><SvgIcon class="icon" icon="icon-close" />关闭
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-box">
        <div class="add-box-top-title">
          <div class="common-section-header has-bottom-border">
            <div class="title">文件预览</div>
          </div>
        </div>
        <div class="content-box-body scroll-bar-style">
          <component :is="Component" :option="state.option" />
        </div>
      </div>
      <div class="content-box">
        <div class="add-box-top-title">
          <div class="common-section-header has-bottom-border">
            <div class="title">识别内容</div>
          </div>
        </div>
        <div class="content-box-body scroll-bar-style">
          <pre class="imgBox" v-loading="state.loading" element-loading-text="正在识别中..."
            >{{ state.ocrContent }}
          </pre>
          <div v-if="state.ocrContent === ''" class="errHint">
            <img src="@/assets/img/close-circle-fill.png" alt="" />
            <p>识别失败</p>
          </div>
          <div class="footer">
            <n-button class="save" :loading="state.loading" size="sm">复制文本</n-button>
            <n-button
              size="sm"
              variant="solid"
              color="primary"
              :loading="state.loading"
              @click.prevent="resetCheckFn"
              >{{ state.ocrContent === '请点击文字识别' ? '文字识别' : '重新识别' }}</n-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import Clipboard from 'clipboard'
  import axios from 'axios'
  import PDF from './components/PDFJS.vue'
  import Code from './components/Code.vue'
  import Img from './components/Img.vue'
  import Txt from './components/Txt.vue'
  import Excel from './components/Excel.vue'
  import { useRouter, useRoute } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { getApiHost } from '@/api/dataManage.js'
  const router = useRouter()
  const route = useRoute()
  const componentMap = {
    XML: Code,
    LOG: Txt,
    TXT: Txt,
    JSON: Code,
    CSV: Excel,
    PDF: PDF,
    PNG: Img,
    JPEG: Img,
    JPG: Img,
    DOCX: PDF,
    DOC: PDF,
    EXCEL: Excel,
    XLSX: Excel,
    XLS: Excel,
  }
  const type = ref()
  const Component = ref(componentMap[type.value])
  const clipboard = ref(null)

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    loading: false,
    info: {},
    id: '',
    option: {
      name: '',
    },
    ocrContent: '请点击文字识别',
    versionList: [],
  })

  onMounted(() => {
    state.id = String(route.query.id)
    api.assets.getFilDetail({ id: state.id }).then((res) => {
      if (res.success) {
        state.info = res.data
        getVersion()
      }
    })

    clipboard.value = new Clipboard('.save', {
      text: function () {
        return state.ocrContent
      },
    })

    clipboard.value.on('success', copyText)
  })
  onBeforeUnmount(() => {
    clipboard.value.off('success', copyText)
    clipboard.value.destroy()
  })

  const copyText = (e) => {
    // 打印动作信息（copy 或者 cut）
    // console.info('Action:', e.action)
    // 打印复制的文本
    console.info('Text:', e.text)
    ElNotification({
      title: '提示',
      message: '复制文本成功',
      type: 'success',
    })
  }

  // 获取文件数据
  const seeFile = (data) => {
    let { sourceFileFormat, ossUrl, sourceFileName, id, fileEncoding } = data
    const sourceFileType = sourceFileName?.split('.')?.pop()?.toLocaleUpperCase()
    state.option.name = sourceFileName
    state.option.value = ossUrl
    state.option.type = sourceFileType
    type.value = sourceFileType
    state.option = {
      name: sourceFileName,
      value: ossUrl,
      type: sourceFileType,
      fileEncoding,
      ...{
        DOCX: {
          value: `./api/govern-collect/collect/file/word/preview/byid?id=${id}`,
        },
        DOC: {
          value: `./api/govern-collect/collect/file/word/preview/byid?id=${id}`,
        },
        CSV: {
          value: `./api/govern-collect/collect/file/word/preview/byid?id=${id}`,
        },
        XLS: {
          value: `./api/govern-collect/collect/file/word/preview/byid?id=${id}`,
        },
      }[sourceFileType],
    }
    Component.value = componentMap[type.value]
  }
  // 获取版本数据
  const getVersion = () => {
    api.dataManagement.versionListAll({ jobFileId: state.id }).then((res) => {
      if (res.success) {
        state.versionList = res.data
        if (state.versionList.length > 0) {
          if (route.query.version) {
            state.version = route.query.version
          } else {
            state.version = state.versionList[0].id
          }
        }
        let checkVersion = state.versionList.filter((val) => val.id === state.version)
        if (checkVersion.length > 0) {
          seeFile({ ...state.info, ossUrl: checkVersion[0].versionFileUrl })
        } else {
          seeFile({ ...state.info })
        }
      }
    })
  }

  // 重新识别
  const resetCheckFn = async () => {
    state.loading = true
    const arr = ['PNG', 'JPEG', 'JPG']

    if (state.option.type === 'PDF') {
      const canvas = document.getElementById('the-canvas1')
      const dataUrl = canvas.toDataURL('image/jpeg') // 可根据需要修改图片格式
      // 将 dataUrl 转换为 Blob 对象
      const blob = await (await fetch(dataUrl)).blob()
      const formData = new FormData()
      formData.append('file', blob)

      try {
        getApiHost().then((res) => {
          textCheckFn({
            formData,
            data: res.data,
          })
        })
      } catch (error) {
        state.loading = false
        console.log(error)
      }
    } else if (arr.includes(state.option.type)) {
      try {
        // 发送请求获取图片数据
        const response = await axios.get(state.option.value, { responseType: 'blob' })
        const formData = new FormData()
        formData.append('file', response.data)

        getApiHost().then((res) => {
          textCheckFn({
            formData,
            data: res.data,
          })
        })
        // const res = await axios.post('http://*************:9029/ocr/image/recognition', formData)
        // state.ocrContent = res.data.data.length > 0 ? res.data.data : ''
        // state.loading = false
      } catch (error) {
        state.loading = false
        // 处理上传失败的逻辑
        console.log(error)
      }
    }
  }

  const textCheckFn = async (data) => {
    const url = `http://${data.data.host}:${data.data.port}/ocr/image/recognition`
    const res = await axios.post(url, data.formData)
    state.ocrContent = res.data.data.length > 0 ? res.data.data : ''
    state.loading = false
  }

  // 取消
  const cancelFn = () => {
    router.go(-1)
  }
  // 关闭
  const closeFn = () => {
    router.push({ name: 'resourceLibraryList' })
  }
  onMounted(() => {})
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    padding: 12px;
    .tools {
      background-color: #fff;
      border-radius: 8px;
      .row {
        position: relative;
        .detail-back-box-btn {
          font-weight: normal;
        }
      }
    }
    .content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: calc(100% - 60px);
      margin-top: 8px;
      border-radius: 8px;
      &-box {
        width: 67%;
        height: 100%;
        background-color: #fff;
        border-radius: 8px;

        &:nth-child(2) {
          width: calc(33% - 8px);
        }
        &-body {
          position: relative;
          height: calc(100% - 52px);
          padding: 16px;
          overflow-y: auto;
          &-title {
            display: flex;
            align-items: center;
            height: 48px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 16px;

            svg {
              margin-right: 4px;
              color: #8091b7;
              font-size: 16px;
              cursor: pointer;

              &.noExpand {
                transform: rotate(180deg);
              }
            }

            i {
              display: block;
              flex: 1;
              height: 1px;
              margin-left: 8px;
              border-bottom: 1px solid #c5d0ea;
            }
          }
          .font-box {
            min-height: 200px;
            .flex-space-between {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .slider-wrapper {
                width: calc(100% - 100px);
                :deep(.nancalui-slider) {
                  .nancalui-slider__runway {
                    background-color: #e3ecff;
                  }
                }
              }
              .snapshot-value {
                width: 100px;
                margin-left: 8px;
                :deep(.nancalui-input__wrapper) {
                  padding-right: 0;
                }
              }
            }
          }
          .textarea-box {
            width: 100%;
            .tip {
              margin-top: 4px;
              color: rgba(0, 0, 0, 0.55);
              font-size: 14px;
            }
          }
          .custom-color {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: rgba(0, 0, 0, 0.55);
            font-size: 14px;

            .nancalui-switch {
              margin-left: 6px;
            }

            &-label {
              display: flex;
              align-items: center;
              justify-content: center;
              box-sizing: border-box;
              width: 20px;
              height: 20px;
              margin-right: 8px;
              border: 1px solid #fff;
              border-radius: 4px;

              .icon {
                display: none;
                color: #fff;
                font-size: 12px;
              }

              &.checked {
                box-shadow: 0 0 0 1px #447dfd;

                .icon {
                  display: block;
                }
              }
            }
          }

          .errHint {
            position: absolute;
            top: 50%;
            left: 50%;
            text-align: center;
            transform: translate(-50%, -50%);

            p {
              margin: 0;
            }
          }
          .imgBox {
            position: relative;
            width: 100%;
            height: calc(100% - 64px);
            overflow: auto;
            font-family: 思源黑体;
            border-radius: 6px;
            &.gray {
              background-color: #b8b8b8;
            }

            .mark {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              justify-content: center;
              width: 302px;
              height: 224px;
              color: #cfcfcf;
              font-size: 36px;
              text-align: center;
              transform: rotate(-30deg);
            }
          }
          .footer {
            position: absolute;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            box-sizing: border-box;
            width: 100%;
            height: 64px;
            padding: 0 16px;
          }
        }
      }
    }
    .add-box-top-title {
      margin-bottom: 0;
      .title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .confidentiality-level-label {
          display: inline-block;
          height: 24px;
          margin-left: 8px;
          padding: 0 8px;
          color: rgba(0, 0, 0, 0.75);
          font-weight: normal;
          font-size: 14px;
          line-height: 22px;
          text-align: center;
          background-color: #e3ecff;
          border: 1px solid #6e9eff;
          border-radius: 6px;
        }
      }
    }

    .nancalui-button--outline--secondary {
      color: #447dfd;
    }
  }
</style>
