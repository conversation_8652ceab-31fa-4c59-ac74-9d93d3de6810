<template>
  <div class="container">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">非结构化数据编号：</span>
          <n-input v-model="tableState.filterSearch.ruleCode" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">非结构化数据名称：</span>
          <n-input v-model="tableState.filterSearch.name" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">密级：</span>
          <n-select v-model="tableState.filterSearch.confidentialityLevel" :allow-clear="true">
            <n-option
              v-for="item in tableState.secretList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>非结构化数据分类</div>
        </div>
        <categoryTree @node-click="clickFn" />
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onAdd" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          新建非结构化数据</n-button
        >
        <div class="table" v-loading="tableState.isLoad">
          <CfTable
            actionWidth="180"
            :tableHeadTitles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #editor="{ data: { row } }">
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                @click="onView(row)"
                >预览</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onPush(row)"
                >发布</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-else-if="row.status === 'PUBLISH'"
                @click="onUnder(row)"
                >下架</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onEdit(row)"
                >编辑</n-button
              >
              <n-popover trigger="hover">
                <SvgIcon class="nc-m-l-14" icon="icon-btn-more" />
                <template #content>
                  <div>
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        @click="onRelation(row)"
                        >关联非结构化数据</n-button
                      >
                    </div>
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        @click="onVersion(row)"
                        >版本管理</n-button
                      >
                    </div>
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        v-if="row.status === 'WAITING_PUBLISH'"
                        @click="onDelete(row)"
                        >删除</n-button
                      >
                    </div>
                  </div>
                </template>
              </n-popover>
            </template>
            <template #tagList="{ row }">
              <level-tag :bgColor="row.bgColor" :borderColor="row.borderColor" :color="row.color">{{
                row.confidentialityLevelName
              }}</level-tag>
            </template>
            <template #versionHeader="{ column }">
              <n-tooltip content="MAIN：最新版本 V?：历史版本" position="tl">
                <template #content>
                  <span>MAIN：最新版本<br />V?：历史版本</span>
                </template>
                版本 <n-icon name="icon-helping"></n-icon>
              </n-tooltip>
            </template>
            <template #version="{ row }">
              <span class="version">{{ row.version }}</span>
            </template>
            <template #status="{ row }">
              <span class="w-publish" v-if="row.status === 'WAITING_PUBLISH'">未发布</span>
              <span class="publish" v-else-if="row.status === 'PUBLISH'">已发布</span>
            </template>
          </CfTable>
        </div>
      </div>
    </section>
    <relationDocs v-model="tableState.relationDocsVisible" :id="tableState.relationDocsId" />
  </div>
</template>
<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'
  import { tagList } from '../config/tag.js'
  import relationDocs from '../components/relation-docs.vue'
  import categoryTree from '../components/categoryTree.vue'
  import api from '@/api/index'
  import { getDocumentAuthType } from '@/api/documentManage.js'
  const router = useRouter()
  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const tableState = reactive({
    typeIds: [],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    tableList: [],
    filterSearch: {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
      categoryId: null,
    },
    searchData: {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    },
    tableHeadTitles: [
      { prop: 'ruleCode', name: '非结构化数据编号', width: '200' },
      { prop: 'name', name: '非结构化数据名称', width: '200' },
      { prop: 'categoryName', name: '非结构化数据分类' },
      { prop: 'type', name: '非结构化数据类型' },
      { prop: 'confidentialityLevelName', name: '密级', slot: 'tagList' },
      { prop: 'version', name: '版本', slot: 'version', headerSlot: 'versionHeader' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', width: '160' },
      { prop: 'status', name: '状态', slot: 'status' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    relationDocsVisible: false,
    relationDocsId: '',
    isLoad: true,
  })
  function clickFn(id) {
    tableState.filterSearch.categoryId = id
    onSearch()
  }
  function startSearch() {
    Object.keys(tableState.filterSearch).forEach((key) => {
      tableState.searchData[key] = tableState.filterSearch[key]
    })
    onSearch()
  }

  const getCurrentUserTypeAuth = async () => {
    const res = await getDocumentAuthType()
    tableState.typeIds = []
    tableState.typeIds = res.data.map((item) => item.categoryId)
  }
  // 查询
  function onSearch(init = true) {
    if (init) {
      tableState.pagination.currentPage = 1
    }
    tableState.isLoad = true
    api.documentManage
      .documentationList({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...tableState.searchData,
          categoryId:
            tableState.filterSearch.categoryId === 'all'
              ? null
              : tableState.filterSearch.categoryId,
        },
      })
      .then((res) => {
        tableState.tableList = res.data.list.map((i) => {
          const { color, bgColor, borderColor } = tagList[i.confidentialityLevelName]
          return {
            ...i,
            bgColor,
            color,
            borderColor,
          }
        })
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  // 重置
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    tableState.searchData = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    onSearch()
  }
  // 新增非结构化数据
  function onAdd() {
    router.push({
      name: 'documentPreparationAdd',
      query: { categoryId: tableState.filterSearch.categoryId },
    })
  }
  // 编辑非结构化数据
  function onEdit(row) {
    router.push({ name: 'documentPreparationAdd', query: { id: row.id } })
  }
  // 发布非结构化数据
  function onPush(row) {
    api.documentManage.documentationPublish({ id: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }
  // 下架非结构化数据
  function onUnder(row) {
    api.documentManage.documentationOffline({ id: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }
  // 关联非结构化数据
  function onRelation(row) {
    tableState.relationDocsId = row.id
    tableState.relationDocsVisible = true
  }
  // 版本管理
  function onVersion(row) {
    router.push({ name: 'documentPreparationVersion', query: { id: row.id, name: row.name } })
  }
  // 预览非结构化数据
  function onView(row) {
    router.push({
      name: 'documentPreparationPreview',
      query: { id: row.id },
    })
  }
  // 删除非结构化数据
  function onDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条非结构化数据',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.documentationDelete({ id: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
          }
        })
      },
    })
  }
  function refreshData() {
    onSearch(false)
    // const { currentPage, pageSize } = router.currentRoute.value.query
    // if (currentPage && pageSize) {
    //   tableState.pagination.currentPage = Number(currentPage)
    //   tableState.pagination.pageSize = Number(pageSize)
    //   onSearch(false)
    // } else {
    //   onSearch()
    // }
  }
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        refreshData()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true },
  )
  onMounted(() => {
    refreshData()
  })

  defineExpose({ getCurrentUserTypeAuth })
</script>
<!-- 定义导航守卫 -->
<script>
  import { defineComponent } from 'vue'
  export default defineComponent({
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getCurrentUserTypeAuth() // 调用已暴露的方法
      })
    },
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .popover-item {
    width: 100px;
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
  .publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .w-publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
</style>
