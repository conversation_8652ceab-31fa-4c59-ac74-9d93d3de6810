:root {
  // 输入框部分样式
  .el-input--small {
    box-sizing: border-box;
    line-height: $themeInputHeight;

    .el-input__wrapper {
      height: $themeInputHeight;
      padding: $themeInputPadding;
    }
  }
  .el-input__wrapper {
    border-radius: 2px !important;
  }
  // 下拉框，日期选择器，输入框focus边框颜色
  .el-range-editor.is-active,
  .el-select .el-input.is-focus .el-input__wrapper,
  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px $themeBlue inset !important;
  }

  .el-input__inner,
  .el-textarea__inner {
    font-size: $themeFont;

    &::-webkit-input-placeholder {
      color: #999;
      font-size: $themeFont;
    }
  }
  .el-textarea__inner {
    padding-bottom: 15px;
  }

  .el-input.is-disabled .el-input__inner,
  .el-textarea.is-disabled .el-textarea__inner,
  .el-input__inner,
  .el-textarea__inner {
    color: #1d2129;
  }
  .el-input.is-disabled .el-input__inner {
    -webkit-text-fill-color: #1d2129;
  }

  .el-range-editor--small {
    height: $themeInputHeight;

    &.el-input__wrapper {
      height: $themeInputHeight;
    }
  }

  // 日期部分样式
  .el-date-editor--date,
  .el-date-editor--time {
    .el-input__prefix {
      display: none;
    }

    .el-input__wrapper {
      background-image: url('/src/assets/img/icon-date-w.png');
      background-repeat: no-repeat;
      background-position: 96% 50%;
      background-size: 18px;

      &.is-active,
      &:hover {
        background-image: none;
      }
    }
  }

  .el-date-editor--time {
    .el-input__wrapper {
      background-image: url('/src/assets/img/icon-time-w.png');
    }
  }

  // 下拉框部分样式
  .el-select--small {
    height: $themeInputHeight;
  }

  .el-select {
    width: 220px;
    .el-select__wrapper {
      box-shadow: 0 0 0 1px #e5e6eb inset;
      &:not(.is-focused):hover {
        box-shadow: 0 0 0 1px #447dfd inset;
      }
      &.is-focused {
        box-shadow: 0 0 0 1px #447dfd inset;
      }
    }
  }

  .el-select-dropdown__list {
    margin: 0 !important;
    padding: 4px !important;

    .el-select-dropdown__item {
      max-width: 420px;
      height: 38px;
      padding: 0 10px;
      color: #333333;
      font-size: 12px;
      line-height: 38px;
      border-radius: 4px;

      &.selected {
        color: var(--themeBlue);
        background-color: #fff;
      }

      &.is-disabled {
        color: #a8abb2;
      }

      &.hover {
        background-color: #fff;
      }

      &:hover {
        font-weight: bold;
        background-color: #f7f8fa;
      }
    }
  }

  // 分页器下拉框
  .el-pagination__sizes {
    margin-left: 16px;

    .el-select {
      width: auto;
    }
  }

  // 抽屉部分样式
  .el-drawer__body {
    padding: 0;
  }

  // dialog部分公共样式
  .el-overlay-dialog {
    overflow: hidden;
  }
  .el-dialog {
    border-radius: 8px;
    .nancalui-table__thead tr th {
      line-height: 40px; //处理publicTable在el-dialog里面表头高度丢失问题
    }

    &.commonDialog {
      .el-dialog__body {
        padding: 0 30px 30px;
      }
    }

    &.largeDialog {
      .el-dialog__body {
        padding: 0 60px 50px;
      }
    }

    &.middleDialog {
      .el-dialog__body {
        padding: 0 30px 50px;
      }
    }
    .el-dialog__header {
      position: relative;
      margin-right: 0;
      padding: 20px 30px 38px 30px !important;
      // border-bottom: 1px solid #ebebeb;
      border-bottom: 0 !important;

      .el-dialog__title {
        display: block;
        height: 16px;
        padding-left: 6px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bolder;
        font-size: 15px;
        line-height: 16px;
        border-left: 4px solid var(--themeBlue);
      }

      .el-dialog__headerbtn {
        top: -16px;
        right: 18px;
        bottom: 0;
        width: 32px;
        height: 32px;
        margin: auto;
      }
    }

    .el-dialog__footer {
      padding: 13px 20px;
      text-align: center;
      border-top: 1px solid $themeBorderColor;
    }
  }
  .el-popover {
    min-width: 60px;
    padding: 8px 12px;
    .el-popover-tree-name {
      color: #262626;
      cursor: pointer;
      //&:hover {
      //  color: $themeBlue;
      //}
    }
  }
  // 弹出框部分公共样式
  .el-popover.el-popper.tag-box-more-popover,
  .el-popover.el-popper.tag-box-clear-popover {
    padding: 4px;
  }

  .el-popover.el-popper.tree-node-control {
    padding: 4px;
  }
  .el-popover.el-popper.total-menu-popover {
    padding: 8px;
    background: var(--sideBarBackColor);
    .total-menu-box {
      .label {
        height: 40px;
        margin-bottom: 4px;
        padding-left: 28px;
        color: var(--sideBarMenuColor);
        font-size: 14px;
        line-height: 40px;
        border-radius: 4px;
        cursor: pointer;
        &.checked {
          color: var(--sideBarMenuSelectColor);
        }
        &:last-of-type {
          margin-bottom: 0;
        }
        &:hover {
          background-color: var(--sideBarMenuBackColor);
        }
      }
    }
  }
  .el-popover.el-popper.btn-more-popover {
    min-width: 100px;
    padding: 4px;
    background-color: #fff;
    .seeDetails {
      height: 32px;
      margin-left: 0;
      color: var(--themeBlue);
      font-size: 12px;
      line-height: 32px;
      text-align: center;
      &:hover {
        background-color: #f7f8fa !important;
      }
      &:disabled {
        color: #c9c9c9;
      }
    }
  }
  .el-popover {
    &.popover-target {
      padding: 10px 12px;
      background: rgba(0, 0, 0, 0.65);
      border: none;
      border-radius: 4px;
      box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
      transform: translateY(-20px);
      &:before {
        position: absolute;
        top: -6px;
        right: 0;
        left: 0;
        width: 0;
        height: 0;
        margin: auto;
        border-right: 6px solid transparent;
        border-bottom: 6px solid rgba(0, 0, 0, 0.65);
        border-left: 6px solid transparent;
        content: '';
      }
      .popover-target-name {
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
      }
    }
    &.popover-board-from {
      width: 52px;
      min-width: 52px;
      height: 30px;
      padding: 0;
      line-height: 30px;
      text-align: center;
      background: rgba(0, 0, 0, 0.65);
      border: none;
      border-radius: 4px;
      transform: translateY(1px);
      &:before {
        position: absolute;
        right: 0;
        bottom: -6px;
        left: 0;
        width: 0;
        height: 0;
        margin: auto;
        border-top: 6px solid rgba(0, 0, 0, 0.65);
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
        content: '';
      }
      .popover-board-from-name {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        text-align: center;
      }
    }
    &.popover-dag {
      padding: 4px;
      .popover-dag-box {
        &-label {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          padding: 0 6px 0 12px;
          color: #1d2129;
          cursor: pointer;
          &:not(.disabled):hover {
            color: #447dfd;
            background-color: #ebf4ff;
          }
          &.disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
          &-child {
            position: absolute;
            top: -4px;
            left: 140px;
            display: none;
            box-sizing: border-box;
            width: 230px;
            height: 168px;
            padding: 4px 0;
            background: #fff;
            border-left: 1px solid #dcdfe6;
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
            &-label {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 32px;
              padding: 0 6px 0 12px;
              color: #1d2129;
              cursor: pointer;
              &:not(.disabled):hover {
                color: #447dfd;
                background-color: #ebf4ff;
              }
              &.disabled {
                color: #c0c4cc;
                cursor: not-allowed;
              }
            }
          }
          &.run:hover {
            .popover-dag-box-label-child {
              display: block;
            }
          }
        }
      }
    }
  }

  // 表单部分公共样式
  .el-form {
    &.disable-hide-border {
      //表单查看样式
      .el-form-item__label {
        padding: 0 0 0 0;
      }
      .el-form-item__content {
        .is-disabled {
          cursor: default;
          .el-input__inner,
          .el-textarea__inner {
            background-color: transparent !important;
            box-shadow: none;
            cursor: default;
            resize: none; //隐藏拉伸按钮
          }
          .el-input__wrapper {
            padding: 1px 2px;
            background-color: transparent;
            box-shadow: none;
            .el-input__inner {
              background: transparent !important;
            }
          }
        }
      }
    }
    .el-form-item__label {
      position: relative;
      color: #666;
      font-weight: normal;
      font-size: 12px;
    }

    .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
      margin-right: 4px;
      color: var(--el-color-danger);
      content: '*';
    }
    &.el-form--inline .el-form-item {
      margin-right: 16px;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  // checkbox样式
  .el-checkbox__input.is-checked {
    .el-checkbox__inner {
      background-color: var(--themeBlue);
      border-color: var(--themeBlue);
    }
  }
  .el-checkbox__label {
    font-size: 12px;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--themeBlue);
  }

  // radio样式
  .el-radio__input.is-checked {
    .el-radio__inner {
      background-color: var(--themeBlue);
      border-color: var(--themeBlue);
    }
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: var(--themeBlue);
  }

  // switch样式
  .el-switch.is-checked {
    .el-switch__core {
      background-color: var(--themeBlue);
      border-color: var(--themeBlue);
    }
  }

  // 面包屑样式
  .el-breadcrumb {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        a {
          color: #999999;
          font-weight: normal;

          &:hover {
            color: $themeFontColor;
          }
        }

        .no-redirect {
          color: #333333;
        }
      }
    }
  }

  // 分页样式
  .el-pagination.is-background {
    .el-pager li,
    .btn-next,
    .btn-prev {
      background-color: #fff;
      border: 1px solid #dcdcdc;
    }

    .el-pager li:not(.is-disabled).is-active {
      background-color: var(--buttonPrimaryColor);
      border: 1px solid var(--buttonPrimaryColor);
    }
  }
  .el-radio.el-radio--small .el-radio__inner {
    width: 16px;
    height: 16px;
  }
}
td.el-table__cell .nancalui-button--text {
  padding: 0;
}
.el-loading-spinner {
  top: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin: auto !important;
  .circular {
    width: 16px !important;
    height: 16px !important;
  }
}

.el-tree-node__expand-icon.is-leaf {
  visibility: hidden;
}
