<template>
  <div class="container overflow">
    <div class="cf-page-title">
      API编制
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>
    <div class="white-box nc-p-16" v-loading="state.loading">
      <n-form
        ref="formRef"
        :data="state.formData"
        :rules="state.rules"
        label-width="120px"
        label-align="start"
        message-type="text"
        labelSuffix="："
      >
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="API名称" field="apiName">
              <n-input
                v-model="state.formData.apiName"
                maxlength="50"
                placeholder="请输入API名称"
              />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="categoryId" label="分类">
              <n-tree-select
                v-model="state.formData.categoryId"
                :treeData="state.categoryTree"
                style="width: 100%"
                :allowClear="true"
                node-key="id"
                :prop="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求方式" field="requestMethod">
              <n-select
                size="sm"
                v-model="state.formData.requestMethod"
                placeholder="请选择请求方式"
              >
                <n-option
                  v-for="item in state.requestMethodOps"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="密级" field="confidentialityLevel">
              <n-select
                v-model="state.formData.confidentialityLevel"
                placeholder="选择数据表后自动获取"
                filter
                allow-clear
                :disabled="state.formData.definitionType === 'APPOINT_TABLE'"
                :options="state.securityLevelOpt"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="构建方式" field="definitionType">
              <n-select
                v-model="state.formData.definitionType"
                placeholder="请选择构建方式"
                style="width: 100%"
                @value-change="definitionFn"
              >
                <n-option name="表" value="APPOINT_TABLE" />
                <n-option name="SQL" value="SQL_SCRIPT" />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item
              label="数据表"
              field="modelTableId"
              v-if="state.formData.definitionType === 'APPOINT_TABLE'"
            >
              <div class="nc-flex" style="width: 100%">
                <n-select
                  size="sm"
                  style="width: 200px"
                  v-model="state.formData.datasourceId"
                  placeholder="请选择数据源"
                  @value-change="getTables"
                >
                  <n-option
                    v-for="item in state.dataSourceOps"
                    :key="item.id"
                    :name="item.name"
                    :value="item.id"
                  />
                </n-select>

                <el-select
                  placeholder="请选择数据数据表"
                  style="width: calc(100% - 210px); margin-left: 10px"
                  v-model="state.formData.modelTableId"
                  filterable
                  remote
                  reserve-keyword
                  remote-show-suffix
                  :remote-method="getTableDataFn"
                  @change="getParamsUrl"
                  :loading="state.tableLoading"
                >
                  <el-option
                    v-for="item in state.systemList"
                    :key="item.modelTableId"
                    :value="item.modelTableId"
                    :label="item.systemName"
                    :securityLevel="item.securityLevel"
                  />
                </el-select>
              </div>
            </n-form-item>
            <n-form-item label="数据源" field="datasourceId" v-else>
              <n-select
                size="sm"
                v-model="state.formData.datasourceId"
                placeholder="请选择数据源"
                @value-change="getTables"
              >
                <n-option
                  v-for="item in state.dataSourceOps"
                  :key="item.id"
                  :name="item.name"
                  :value="item.id"
                />
              </n-select>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40" v-if="state.formData.definitionType === 'SQL_SCRIPT'">
          <n-col :span="24">
            <n-form-item label="SQL脚本" field="sqlScript">
              <CfCodemirror v-model="state.formData.sqlScript" />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row
          :gutter="40"
          v-if="state.formData.definitionType === 'SQL_SCRIPT'"
          style="margin-bottom: 8px"
        >
          <!-- 解析参数 -->
          <n-col :span="24">
            <i style="display: inline-block; margin-left: 120px"></i>
            <n-button
              type="primary"
              @click="parseParams(true)"
              style="margin-right: 4px"
              :disabled="state.formData.definitionType === 'APPOINT_TABLE'"
            >
              解析参数
            </n-button>
            <!-- 提示 -->
            <n-tooltip
              class="tree-btn"
              content="如需根据请求参数获取数据，请使用${name:string}做为变量:select from tab1 where name = ${name:string}  变量中数据类型，只支持string和number"
              position="top"
              :enterable="false"
            >
              <SvgIcon class="illustrate" icon="icon-illustrate" />
            </n-tooltip>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <div class="param-box">
              <div class="param-box-search">
                <n-search
                  v-model="state.leftKeyword"
                  size="sm"
                  clearable
                  :auto-focus="false"
                  style="width: 200px"
                  @clear="onSearchLeft"
                  @search="onSearchLeft"
                />
              </div>

              <n-form-item class="inline" label="请求参数" field="requestParamList">
                <div class="border-box">
                  <n-public-table
                    ref="publicTable"
                    v-if="state.formData.definitionType === 'APPOINT_TABLE'"
                    :tableData="state.leftTable"
                    :configData="state.leftConfigData"
                    :isDisplayAction="false"
                    :isNeedSelection="true"
                    :editDisabled="true"
                    :rowKey="state.formData.definitionType === 'SQL_SCRIPT' ? 'column' : 'fieldId'"
                    :key="state.tableKey"
                    :table-head-titles="leftTableFormTitles"
                    :showPagination="false"
                    :tableHeight="320"
                    @handle-selection-change="reqSelectChange"
                  >
                    <template #dataType="{ editor }">
                      <n-select
                        v-model="editor.row.dataType"
                        :disabled="
                          editor.row.column === 'pageNumber' ||
                          editor.row.column === 'pageSize' ||
                          state.formData.definitionType === 'APPOINT_TABLE'
                        "
                        placeholder="请选择字段类型"
                        :class="
                          editor.row.column !== 'pageNumber' &&
                          editor.row.column !== 'pageSize' &&
                          editor.row.isRequired
                            ? 'isRequired'
                            : ''
                        "
                        style="width: 100%"
                        @value-change="
                          () => {
                            editor.row.isRequired = false
                          }
                        "
                      >
                        <n-option
                          v-for="item in tableDataTypeList"
                          :name="item.name"
                          :value="item.value"
                        />
                      </n-select>
                    </template>
                    <template #required="{ editor }">
                      <n-switch
                        :disabled="
                          editor.row.column === 'pageNumber' || editor.row.column === 'pageSize'
                        "
                        v-model="editor.row.required"
                      >
                        <template #checkedContent>是</template>
                        <template #uncheckedContent>否</template>
                      </n-switch>
                    </template>
                    <template #columnName="{ editor }">
                      {{ editor.row.columnName ? editor.row.columnName : editor.row.column }}
                    </template>
                  </n-public-table>

                  <n-public-table
                    ref="publicTable"
                    v-else
                    :tableData="state.leftTable"
                    :configData="state.leftConfigData"
                    :isDisplayAction="false"
                    :editDisabled="true"
                    :rowKey="state.formData.definitionType === 'SQL_SCRIPT' ? 'column' : 'fieldId'"
                    :key="state.tableKey"
                    :table-head-titles="leftTableTitles"
                    :showPagination="false"
                    :tableHeight="320"
                    @handle-selection-change="reqSelectChange"
                  >
                    <template #dataType="{ editor }">
                      <n-select
                        v-model="editor.row.dataType"
                        :disabled="
                          editor.row.column === 'pageNumber' ||
                          editor.row.column === 'pageSize' ||
                          state.formData.definitionType === 'APPOINT_TABLE'
                        "
                        placeholder="请选择字段类型"
                        style="width: 100%"
                      >
                        <n-option
                          v-for="item in state.dataTypeList"
                          :name="item.name"
                          :value="item.value"
                        />
                      </n-select>
                    </template>
                    <template #required="{ editor }">
                      <n-switch
                        :disabled="
                          editor.row.column === 'pageNumber' || editor.row.column === 'pageSize'
                        "
                        v-model="editor.row.required"
                      >
                        <template #checkedContent>是</template>
                        <template #uncheckedContent>否</template>
                      </n-switch>
                    </template>
                  </n-public-table>
                </div>
              </n-form-item>
              <div v-if="state.showMessage" class="msg">请选择请求参数</div>
            </div>
          </n-col>
          <n-col :span="12">
            <div class="param-box">
              <div class="param-box-search">
                <n-search
                  v-model="state.rightKeyword"
                  size="sm"
                  clearable
                  style="width: 200px"
                  @clear="onSearchRight"
                  @search="onSearchRight"
                />
              </div>
              <n-form-item label="响应参数" field="responseParamList">
                <div class="border-box">
                  <n-public-table
                    :isDisplayAction="false"
                    :isNeedSelection="true"
                    :editDisabled="false"
                    :tableData="state.rightTable"
                    :configData="state.rightConfigData"
                    :key="state.formData.definitionType"
                    :table-head-titles="rightTableTitles"
                    :showPagination="false"
                    :tableHeight="320"
                    :rowKey="state.formData.definitionType === 'SQL_SCRIPT' ? 'column' : 'fieldId'"
                    @handle-selection-change="resSelectChange"
                  >
                    <template #columnName="{ editor }">
                      {{ editor.row.columnName ? editor.row.columnName : editor.row.column }}
                    </template>
                  </n-public-table>
                </div>
              </n-form-item>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="数据格式" field="responseFormat">
              <n-radio-group direction="row" v-model="state.formData.responseFormat">
                <n-radio key="JSON" value="JSON">JSON</n-radio>
                <n-radio key="CSV" value="CSV">CSV</n-radio>
              </n-radio-group>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="鉴权方式" field="authKind">
              <n-checkbox-group
                v-model="state.formData.authKind"
                direction="row"
                @change="onAuthKindChange"
                class="nc-flex"
                style="align-items: center"
              >
                <n-checkbox label="TOKEN" value="1" />
                <n-checkbox label="白名单" value="2" disabled style="margin-right: 4px" />
                <SvgIcon
                  style="color: #1e89ff"
                  icon="icon-edit3"
                  v-if="state.formData.authKind?.includes('2')"
                  @click="state.whiteVisiable = true"
                />
              </n-checkbox-group>
            </n-form-item>
          </n-col>
          <n-col :span="12" v-if="state.formData.authKind?.includes('1')">
            <n-form-item label="token到期时间" field="expireDate">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.expireDate"
                format="YYYY-MM-DD"
                @confirmEvent="startDateChange"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="接口地址" field="url">
              <n-input v-model="url" disabled />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求间隔" field="callInterval">
              <n-input v-model="state.formData.callInterval" placeholder="请输入">
                <template #append>秒</template>
              </n-input>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="请求超时" field="timeout">
              <n-input v-model="state.formData.timeout" placeholder="请输入">
                <template #append>秒</template>
              </n-input>
            </n-form-item></n-col
          >
        </n-row>
        <n-row :gutter="40" v-if="state.formData.definitionType === 'APPOINT_TABLE'">
          <n-col :span="12">
            <n-form-item label="数据过滤条件" field="whereCondition">
              <n-input
                v-model="state.formData.whereCondition"
                maxlength="1000"
                placeholder="定义数据范围，如：department='研发部'"
              />
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>
    <div class="nc-m-t-10 nc-p-16" style="background-color: #fff">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack">取消</n-button>
        <n-button variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
    <whiteList
      v-model="state.whiteVisiable"
      :ips="state.formData.ipWhiteList"
      @confirm="confirmWhiteList"
    />
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import whiteList from './whiteList.vue'
  import { ElMessage } from 'element-plus'
  import moment from 'moment'
  import api from '@/api/index'
  import { getCurrentInstance, reactive, watch } from 'vue'
  import { confidentialityLevelOptions } from '../assetsRegister/components/utlis.js'
  import CfCodemirror from '@/components/cfCodemirror'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const state = reactive({
    // 密级
    securityLevelOpt: confidentialityLevelOptions,
    loading: false,
    showMessage: false,
    tableLoading: false,
    tableKey: 1,
    isScript: false,
    formData: {
      apiName: '',
      categoryId: null,
      requestMethod: 'GET',
      datasourceId: '',
      modelTableId: null,
      modelTable: '',
      whereCondition: '',
      responseFormat: 'JSON',
      authKind: ['2'],
      expireDate: '',
      responseParamList: [],
      requestParamList: [],
      callInterval: '1',
      timeout: '3',
      ipWhiteList: [],
      pathCode: '',
      token: '',
      confidentialityLevel: '',
      definitionType: 'APPOINT_TABLE',
      sqlScript: '',
    },
    prefix: '',
    rules: {
      apiName: [{ required: true, message: '请输入API名称', trigger: 'blur' }],
      categoryId: [{ required: true, message: '请选择分类', trigger: 'change', type: 'number' }],
      requestMethod: [{ required: true, message: '请选择请求方式', trigger: 'change' }],
      expireDate: [{ required: true, message: '请选择过期时间', trigger: 'change' }],
      modelTableId: [{ required: true, message: '请选择数据表', trigger: 'blur', type: 'string' }],
      responseFormat: [
        { required: true, message: '请选择数据格式', trigger: 'blur', type: 'string' },
      ],
      responseParamList: [
        { required: true, message: '请选择返回参数', trigger: 'blur', type: 'array' },
      ],
      // requestParamList: [
      //   { required: true, message: '请选择请求参数', trigger: 'blur', type: 'array' },
      // ],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
      definitionType: [{ required: true, message: '请选择构建方式', trigger: 'change' }],
      sqlScript: [{ required: true, message: '请输入SQL脚本', trigger: 'blur' }],
      datasourceId: [
        { required: true, message: '请选择数据源', trigger: 'change', type: 'number' },
      ],
    },
    categoryTree: [],
    leftKeyword: '',
    leftTable: {},
    leftConfigData: {},
    rightKeyword: '',
    rightTable: {},
    rightConfigData: {},
    leftTableData: [],
    rightTableData: [],
    whiteVisiable: false,
    requestMethodOps: [
      {
        label: 'GET',
        value: 'GET',
      },
      {
        label: 'POST',
        value: 'POST',
      },
    ],
    dataSourceOps: [],
    systemList: [], // 添加 systemList 初始化
    dataTypeList: [
      { name: '数字(number)', value: 'number' },
      { name: '整数(long)', value: 'long' },
      { name: '小数(decimal)', value: 'decimal' },
      { name: '布尔(boolean)', value: 'boolean' },
      { name: '字符串(string)', value: 'string' },
      { name: '日期(date)', value: 'date' },
      { name: '日期时间(datetime)', value: 'datetime' },
    ],
  })
  // 表类型的dataTypeList 计算属性
  const tableDataTypeList = computed(() => {
    return state.leftTable.list
      .map((item) => {
        return { value: item.dataType, name: item.dataType }
      })
      .concat([
        { value: 'BOOLEAN', name: '布尔值(BOOLEAN)' },
        { value: 'TINYINT', name: '短整型(TINYINT)' },
        { value: 'SMALLINT', name: '整型(SMALLINT)' },
        { value: 'BIGINT', name: '长整型(BIGINT)' },
        { value: 'DATETIME', name: '日期时间(DATETIME)' },
        { value: 'FLOAT', name: '浮点数(FLOAT)' },
        { value: 'DOUBLE', name: '浮点数(DOUBLE)' },
        { value: 'VARCHAR', name: '字符串(VARCHAR)' },
        { value: 'DATE', name: '日期(DATE)' },
        { value: 'INT', name: '整型(INT)' },
        { value: 'CHAR', name: '字符串(CHAR)' },
        { value: 'DECIMAL', name: '高精度定点数(DECIMAL)' },
      ])
      .concat([
        { value: 'VARCHAR2', name: '字符串(VARCHAR2)' },
        { value: 'NUMBER', name: '数字(NUMBER)' },
        { value: 'INTEGER', name: '整数(INTEGER)' },
        { value: 'INT', name: '整数(INT)' },
        { value: 'SMALLINT', name: '整数(SMALLINT)' },
        { value: 'TIMESTAMP', name: '时间戳(TIMESTAMP)' },
        { value: 'DATE', name: '日期(DATE)' },
      ])
      .concat(state.dataTypeList)
  })

  // 设置默认请求参数
  const setDefaultFn = (list) => {
    let defaultList = []
    if (list.filter((val) => val.column === 'pageNumber').length === 0) {
      defaultList.push({
        fieldId: 'pageNumber',
        column: 'pageNumber',
        columnName: '页码',
        dataType: 'long',
        disabledThisRow: true,
        required: true,
      })
    }
    if (list.filter((val) => val.column === 'pageSize').length === 0) {
      defaultList.push({
        fieldId: 'pageSize',
        column: 'pageSize',
        columnName: '每页条数',
        dataType: 'long',
        disabledThisRow: true,
        required: true,
      })
    }
    state.leftTable.list = defaultList.concat(list)
  }

  watch(
    () => state.leftTable,
    (newVal) => {
      setDefaultFn(newVal?.list || [])
    },
  )

  const url = computed(
    () =>
      state.prefix +
      state.formData.pathCode +
      (state.formData.token ? `?token=${state.formData.token}` : ''),
  )
  const leftTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名' },
    { prop: 'dataType', name: '字段类型', slot: 'dataType', width: 180 },
    { prop: 'required', name: '是否必填', slot: 'required' },
  ]
  const leftTableFormTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名', slot: 'columnName' },
    { prop: 'dataType', name: '字段类型', slot: 'dataType', width: 180 },
    { prop: 'required', name: '是否必填', slot: 'required' },
  ]
  const rightTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名', slot: 'columnName'  },
  ]
  function confirmWhiteList(data) {
    state.formData.ipWhiteList = data
  }

  // 切换构建方式
  const definitionFn = (e) => {
    state.isScript = false
    state.formData.modelTableId = ''
    state.formData.datasourceId = ''
    state.leftTable = []
    state.rightTable = []
    state.showMessage = false
    state.tableKey++
  }

  // token到期时间change
  const startDateChange = (e) => {
    state.formData.expireDate = formartTime(e)
  }
  const formartTime = (time, Symbol = '-') => {
    var year = time.getFullYear()
    var mon = time.getMonth() + 1
    var day = time.getDate()
    var submitTime = ''
    submitTime += year + Symbol
    if (mon >= 10) {
      submitTime += mon + Symbol
    } else {
      submitTime += '0' + mon + Symbol
    }
    if (day >= 10) {
      submitTime += day
    } else {
      submitTime += '0' + day
    }
    return submitTime
  }

  // 保存
  const formRef = ref(null)
  const publicTable = ref(null)
  function onConfirm() {
    if (state.formData.definitionType === 'SQL_SCRIPT' && !state.isScript) {
      proxy.$message.error('请解析参数后再保存API')
      return false
    }

    formRef.value.validate((f) => {
      if (f) {
        state.formData.requestParamList =
          state.formData.definitionType === 'APPOINT_TABLE'
            ? publicTable.value.getCheckedRows()
            : state.leftTable.list
        state.formData.requestParamList = state.formData.requestParamList.filter(
          (val) => val.column !== 'pageNumber' && val.column !== 'pageSize',
        )
        if (state.formData.definitionType === 'APPOINT_TABLE') {
          const filterModelTable = state.systemList.find(
            (item) => item.modelTableId === state.formData.modelTableId,
          )
          state.formData.modelTable = filterModelTable?.name || filterModelTable?.modelTableId
        }
        const params = {
          ...state.formData,
          expireDate: state.formData.expireDate
            ? moment(state.formData.expireDate).format('YYYY-MM-DD')
            : null,
          callInterval: Number(state.formData.callInterval),
          timeout: Number(state.formData.timeout),
          url:
            state.formData.pathCode +
            (state.formData.token ? `?token=${state.formData.token}` : ''),
          modelTableId: isNaN(state.formData.modelTableId) ? 0 : state.formData.modelTableId,
          datasourceType: (
            state.dataSourceOps?.find(({ id }) => id === state.formData.datasourceId)
              ?.datasourceType || 'doris'
          ).toLocaleLowerCase(),
        }

        state.showMessage = false
        state.leftTable.list.forEach((val) => {
          val.isRequired = false
        })
        if (params.datasourceId === -1) {
          params.datasourceId = null
        }
        if (state.formData.definitionType === 'APPOINT_TABLE') {
          if (params.requestParamList.length > 0) {
            state.leftTable.list.forEach((val) => {
              params.requestParamList.forEach((item) => {
                if (item.column === val.column) {
                  if (!tableDataTypeList.value.map((val) => val.value).includes(val.dataType)) {
                    state.showMessage = true
                    val.isRequired = true
                  }
                }
              })
            })
          }
        }
        state.tableKey++
        if (state.showMessage) {
          return false
        }
        const apiUrl = state.formData.id ? 'apiEdit' : 'apiAdd'
        api.documentManage[apiUrl](params).then((res) => {
          if (res.success) {
            const hint = !state.formData.id ? '新建' : '编辑'
            proxy.$message.success(hint + '成功!')
            router.push({ name: 'apiManage', query: { refresh: true } })
          }
        })
      }
    })
  }
  // 查询请求参数表格
  function onSearchLeft() {
    if (state.leftKeyword) {
      let data = state.leftTableData.filter(
        (item) =>
          item.column.includes(state.leftKeyword) || item?.columnName?.includes(state.leftKeyword),
      )
      state.leftTable = { list: data }
    } else {
      state.leftTable = { list: state.leftTableData }
    }
  }
  // 查询返回参数表格
  function onSearchRight() {
    if (state.rightKeyword) {
      let data = state.rightTableData.filter(
        (item) =>
          item.column.includes(state.rightKeyword) || item.columnName.includes(state.rightKeyword),
      )
      state.rightTable = { list: data }
    } else {
      state.rightTable = { list: state.rightTableData }
    }
  }
  // 获取路径和参数
  async function getParamsUrl(data, kind = 'all') {
    state.formData.whereCondition = ''
    state.formData.sqlScript = ''
    if (!state.formData.modelTableId) {
      return
    }
    if (data?.name) {
      state.formData.modelTable = data.name?.split('(')[0]
    }
    //根据数据表设置密级
    let activeItem = state.systemList.filter(
      (item) => item.modelTableId === state.formData.modelTableId,
    )[0]
    activeItem && (state.formData.confidentialityLevel = activeItem.securityLevel)
    let res = null
    if (state.formData.datasourceId && state.formData.datasourceId !== -1) {
      res = await api.documentManage.getTablesStructure({
        datasourceId: state.formData.datasourceId,
        datasourceName: state.formData.modelTableId,
        kind: kind,
      })
    } else {
      res = await api.documentManage.apiParamsList({
        modelTableId: state.formData.modelTableId,
        kind: kind,
        apiId: router.currentRoute.value.query?.id,
        optType: router.currentRoute.value.query?.id ? 'edit' : 'add',
      })
    }
    const { list, pathCode } = res?.data
    state.rightTableData = list.map((l) => {
      delete l.dataLength
      return l
    })
    state.leftTableData = list.map((l) => {
      const cur = state.formData.requestParamList.find((r) => r.fieldId === l.fieldId)
      delete l.dataLength
      return {
        ...l,
        required: cur?.required || false,
      }
    })
    state.leftTable = {
      list: state.leftTableData,
    }
    state.rightTable = {
      list: list,
    }
    // 切换数据表时，返回参数默认全选
    if (data) {
      state.rightConfigData = {
        selectRow: list,
      }
      state.formData.responseParamList = list
    }
    if (kind === 'all') {
      state.formData.pathCode = pathCode
    }
    formRef.value.validateFields(['modelTableId'])
  }
  // 请求token
  function onAuthKindChange() {
    if (!state.formData.pathCode) {
      proxy.$message.warning('请先选择数据表！')
      state.formData.authKind = state.formData.authKind.filter((item) => item !== '1')
    }
    if (state.formData.authKind.includes('1')) {
      api.documentManage.apiToken({ url: state.formData.pathCode }).then((res) => {
        state.formData.token = res.data
      })
    } else {
      state.formData.token = ''
    }
  }
  // 请求参数变化
  function reqSelectChange(val) {
    state.formData.requestParamList = val
    state.leftConfigData.selectRow = val
    // formRef.value.validateFields(['requestParamList'])
  }
  // 返回参数变化
  function resSelectChange(val) {
    state.formData.responseParamList = val
    formRef.value.validateFields(['responseParamList'])
  }
  // 详情
  async function getDetail(id) {
    try {
      const res = await api.documentManage.apiDetail({ apiId: id })
      state.formData = {
        ...res.data,
        pathCode: res.data.url?.split('?token=')[0] || '',
      }

      const isSql = res.data.definitionType === 'SQL_SCRIPT'
      state.formData.datasourceId =
        res.data.datasourceType !== 'doris' && res.data.datasourceId ? res.data.datasourceId : -1

      if (res.data.definitionType === 'APPOINT_TABLE' && state.formData.datasourceId > -1) {
        getTableDataFn(state.formData.modelTable)
      } else {
        await getTables()
      }

      let defaultList = [
        {
          fieldId: 'pageNumber',
          column: 'pageNumber',
          columnName: '页码',
          dataType: 'long',
          disabledThisRow: true,
          required: true,
        },
        {
          fieldId: 'pageSize',
          column: 'pageSize',
          columnName: '每页条数',
          dataType: 'long',
          disabledThisRow: true,
          required: true,
        },
      ]
      state.leftConfigData = {
        selectRow: defaultList.concat(state.formData.requestParamList),
      }
      state.rightConfigData = {
        selectRow: state.formData.responseParamList,
      }
      // 如果
      if (!state.systemList.find((item) => item.modelTableId === state.formData.modelTableId)) {
        const resItem = await api.documentManage.apiModelTables({
          tableName: state.formData.modelTable,
        })
        state.systemList = state.systemList.concat(
          resItem.data.map((item) => {
            return {
              ...item,
              modelTableId: String(item.modelTableId),
              systemName: `${item.name}(${item.cnName || ''})`,
            }
          }),
        )
      }

      state.formData.modelTableId =
        state.formData.datasourceId > -1 ? res.data.modelTable : String(res.data.modelTableId)
      isSql ? parseParams(true) : getParamsUrl(null, 'all', 'param')
    } finally {
    }
  }
  // 请求分类
  async function getClassifyTreeList() {
    const res = await api.documentManage.apiCategoryTree()
    state.categoryTree = res.data
  }
  async function getDatasource() {
    const res = await api.documentManage.getDatasource({
      datasourceType: 'ORACLE',
    })

    state.dataSourceOps = (res.data || []).map((item) => {
      return {
        ...item,
        id: !item.id ? -1 : item.id, // 数据源id为-1时，为默认数据源
      }
    })
  }
  // SQL脚本解析
  function onSqlScriptChange(status) {
    if (state.formData.sqlScript) {
      api.documentManage
        .sqlParse({
          sql: state.formData.sqlScript,
          datasourceType: state.formData.datasourceId >= 0 ? 'oracle' : 'doris',
          datasourceId: state.formData.datasourceId,
        })
        .then((res) => {
          if (status) {
            state.isScript = true
          }
          const { pathCode, requestParams, responseParams } = res.data
          state.formData.pathCode = pathCode
          state.leftTableData = requestParams?.map((name) => {
            // const item = state.formData.requestParamList.find((item) => item.column === name.name)

            return (
              // item ||
              {
                column: name.name,
                columnName: name.name,
                dataType: name.type,
                required: false,
              }
            )
          })

          state.rightTableData = responseParams?.map((name) => {
            const item = state.formData.responseParamList.find((item) => item.column === name)
            return (
              item || {
                column: name,
                columnName: name,
                required: false,
              }
            )
          })
          state.formData.sqlScript = res.data.sql
          state.leftTable = { list: state.leftTableData }
          state.rightTable = { list: state.rightTableData }
        })
    }
  }
  function parseParams(status) {
    onSqlScriptChange(status)
  }
  // 请求表下拉
  async function getTables() {
    try {
      state.tableLoading = true
      state.leftTable = []
      state.rightTable = []
      state.formData.modelTableId = ''
      if (state.formData.datasourceId && state.formData.datasourceId !== -1) {
        const res = await api.documentManage.getTablesV2({
          datasourceId: state.formData.datasourceId,
        })
        const securityLevel = state.dataSourceOps.find(
          (item) => item.id === state.formData.datasourceId,
        )?.confidentialityLevel
        if (res.data?.length !== state.systemList?.length) {
          state.systemList = res.data.map((item) => {
            return {
              modelTableId: item.name,
              systemName: `${item.name}(${item.comment || ''})`,
              securityLevel: securityLevel,
            }
          })
        }
      } else {
        const res = await api.documentManage.apiModelTables({ tableName: '' })
        if (res.data?.length !== state.systemList?.length) {
          state.systemList = res.data.map((item) => {
            return {
              ...item,
              modelTableId: String(item.modelTableId),
              systemName: `${item.name}(${item.cnName || ''})`,
            }
          })
        }
      }
    } finally {
      state.tableLoading = false
    }
  }

  const getTableDataFn = async (tableName) => {
    if (state.formData.datasourceId && state.formData.datasourceId !== -1) {
      const res = await api.dataManagement.getSourceTablesPageV2({
        condition: { dataSourceId: state.formData.datasourceId, name: tableName },
        pageNum: 1,
        pageSize: 300,
      })
      const securityLevel = state.dataSourceOps.find(
        (item) => item.id === state.formData.datasourceId,
      )?.confidentialityLevel
      if (res.data?.list.length !== state.systemList?.length) {
        state.systemList = res.data.list.map((item) => {
          return {
            modelTableId: item.name,
            systemName: `${item.name}(${item.comment || ''})`,
            securityLevel: securityLevel,
          }
        })
      }
    } else {
      const res = await api.documentManage.apiModelTables({ tableName })
      if (res.data?.length !== state.systemList?.length) {
        state.systemList = res.data.map((item) => {
          return {
            ...item,
            modelTableId: String(item.modelTableId),
            systemName: `${item.name}(${item.cnName || ''})`,
          }
        })
      }
    }
  }
  // 请求路径前缀
  async function getPrefix() {
    const res = await api.documentManage.apiPrefix()
    state.prefix = res.data
  }
  // 返回
  function goBack() {
    router.go(-1)
  }
  onMounted(async () => {
    try {
      state.loading = true
      const { id, categoryId } = router.currentRoute.value.query
      await getClassifyTreeList()
      await getPrefix()
      await getDatasource()

      state.formData.categoryId = categoryId ? Number(categoryId) : null
      if (id) {
        await getDetail(id)
      } else {
        let defaultList = [
          {
            fieldId: 'pageNumber',
            column: 'pageNumber',
            columnName: '页码',
            dataType: 'long',
            disabledThisRow: true,
            required: true,
          },
          {
            fieldId: 'pageSize',
            column: 'pageSize',
            columnName: '每页条数',
            dataType: 'long',
            disabledThisRow: true,
            required: true,
          },
        ]
        state.leftConfigData = {
          selectRow: defaultList.concat(state.formData.requestParamList),
        }
        setDefaultFn([])
      }
    } finally {
      state.loading = false
    }
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';

  :deep(.nancalui-checkbox__group > *) {
    margin-top: 0;
  }
  .container {
    padding-bottom: 0;
  }
  .white-box {
    height: calc(100% - 130px);
  }

  .param-box {
    position: relative;
    &-search {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    .border-box {
      padding: 50px 10px 10px 10px;
      border: 1px solid #e5e6eb;
    }
    .msg {
      position: absolute;
      left: 120px;
      bottom: -16px;
      color: #f63838;
      font-size: 12px;
    }
    :deep(.isRequired) {
      .nancalui-select__selection {
        border: 1px solid #f63838 !important;
      }
    }
  }

  :deep(.nancalui-input-slot__append) {
    background: #fafafa;
  }
</style>
