<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">状态：</span>
          <n-select
            v-model="state.originalFormInline.sourceType"
            placeholder="状态"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.org_dataSourceType"
              :key="item.value"
              :name="item.label"
              :value="item.value"
            />
          </n-select>
          <span class="label">采集任务名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="请输入采集任务名称"
            size="small"
            clearable
          />
          <span class="label">数据源名称：</span>
          <n-input
            v-model="state.originalFormInline.datasourceName"
            placeholder="请输入数据源名称"
            size="small"
            clearable
          />
          <span class="label">数据表：</span>
          <n-input
            v-model="state.originalFormInline.sourceEntry"
            placeholder="请输入数据表"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: state.showType === 'table',
        }"
      >
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">数据分类</div>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          />
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="row">
              <div class="switch">
                <div class="switch-label blue" @click.prevent="goJumpAdd('collectionAdd')">
                  <SvgIcon class="icon-switch" icon="new-add" />新增任务
                </div>
                <!-- <div class="switch-label blue more" @click.prevent="goJumpAdd('collectionMoreAdd')">
                  <SvgIcon class="icon-switch" icon="new-add" />批量新建
                </div> -->
              </div>
              <div
                v-if="state.showType === 'table'"
                class="switch-label"
                @click.prevent="changeStyle('card')"
              >
                <SvgIcon class="icon-switch" icon="icon-card-switch" />卡片
              </div>
              <div v-else class="switch-label" @click.prevent="changeStyle('table')">
                <SvgIcon class="icon-switch" icon="icon-table-switch" />列表
              </div>
            </div>
            <div
              v-if="state.showType === 'card'"
              v-loading="state.cardLoading"
              :class="state.cardData.length > 0 ? 'table-list' : 'table-list empty-list'"
            >
              <div v-show="state.cardData.length > 0" class="card-list">
                <div class="card" v-for="item in state.cardData" :key="item">
                  <div class="card-top">
                    <div class="top-left">
                      <img
                        v-if="item.publishStatus === 'WAITING_PUBLISH'"
                        class="card-right-img"
                        src="@/assets/img/collect/created.png"
                      />
                      <img
                        v-else-if="item.publishStatus === 'PUBLISHED'"
                        class="card-right-img"
                        src="@/assets/img/collect/publish.png"
                      />
                      <img v-else class="card-right-img" src="@/assets/img/collect/offline.png" />
                    </div>
                    <div class="top-right">
                      <div class="title">
                        <div class="title-name" :title="item.name">{{ item.name }}</div>
                        <div class="title-operate">
                          <n-popover
                            :class="{
                              'table-list-card-popover': true,
                              'card ': true,
                            }"
                            :position="['bottom-end']"
                            align="end"
                            trigger="hover"
                          >
                            <template #content>
                              <div class="table-list-card-header-popover">
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataCollection_publish_edit',
                                    ) && item.publishStatus !== 'PUBLISHED'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  code="dataManagement_dataCollection_publish_edit"
                                  @click.prevent="cardSendFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-card-send-new" />发布
                                </n-button>
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataCollection_off_edit',
                                    ) && item.publishStatus === 'PUBLISHED'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  code="dataManagement_dataCollection_off_edit"
                                  @click.prevent="cardOffFn(item)"
                                  ><SvgIcon class="icon" icon="icon-card-off-new" />下架</n-button
                                >
                                <n-button
                                  class="table-list-card-header-popover-label"
                                  v-if="
                                    buttonAuthList.includes('dataManagement_dataCollection_edit') &&
                                    item.publishStatus !== 'PUBLISHED' &&
                                    item.collectType !== 'UNSTRUCTURE'
                                  "
                                  variant="text"
                                  code="dataManagement_dataCollection_edit"
                                  @click.prevent="cardEditFn(item)"
                                  ><SvgIcon class="icon" icon="icon-new-edit" />编辑</n-button
                                >
                                <n-button
                                  class="table-list-card-header-popover-label"
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataCollection_delete',
                                    ) && item.publishStatus !== 'PUBLISHED'
                                  "
                                  variant="text"
                                  code="dataManagement_dataCollection_delete"
                                  @click.prevent="cardDelFn(item)"
                                  ><SvgIcon class="icon" icon="icon-new-delete" />删除</n-button
                                >
                                <n-button
                                  class="table-list-card-header-popover-label"
                                  v-if="
                                    buttonAuthList.includes('dataManagement_dataCollection_view')
                                  "
                                  code="dataManagement_dataCollection_view"
                                  variant="text"
                                  @click.prevent="cardSeeFn(item)"
                                  ><SvgIcon class="icon" icon="icon-new-see" />查看</n-button
                                >
                                <n-button
                                  class="table-list-card-header-popover-label"
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataCollection_run_edit',
                                    ) && item.publishStatus === 'PUBLISHED'
                                  "
                                  variant="text"
                                  @click.prevent="cardRunFn(item)"
                                  ><SvgIcon class="icon" icon="icon-card-rerun" />立即执行</n-button
                                >
                              </div>
                            </template>
                            <SvgIcon
                              class="icon-more"
                              icon="icon-more"
                              @click.prevent.stop=""
                            /> </n-popover
                        ></div>
                      </div>
                      <div
                        class="message"
                        :title="
                          (item.schedulingConfigInfo?.collectWay === 'FULL'
                            ? '全量采集'
                            : '增量采集') +
                          ' | ' +
                          (item.schedulingConfigInfo?.schedulingStrategy === 'AUTO'
                            ? '自动化调度'
                            : '手动调度') +
                          ' | ' +
                          '创建人 ' +
                          item.createByName
                        "
                        >{{
                          (item.schedulingConfigInfo?.collectWay === 'FULL'
                            ? '全量采集'
                            : '增量采集') +
                          ' | ' +
                          (item.schedulingConfigInfo?.schedulingStrategy === 'AUTO'
                            ? '自动化调度'
                            : '手动调度') +
                          ' | ' +
                          '创建人 ' +
                          item.createByName
                        }}</div
                      >
                    </div>
                  </div>
                  <div class="card-mid">
                    <div class="card-mid-item">数据源类型：{{ item.sourceType }}</div>
                    <div class="card-mid-item">数据源名称：{{ item.sourceName || '--' }}</div>
                    <div class="description" :title="item.description">{{
                      item.description || '暂无描述'
                    }}</div>
                  </div>
                  <div class="card-bottom">
                    <div class="card-bottom-col">
                      <div
                        :class="[
                          'card-bottom-col-label',
                          stateTransition(item.publishStatus, 'true'),
                        ]"
                        >{{ stateTransition(item.publishStatus, 'cName') }}</div
                      >
                      <div
                        :class="[
                          'card-bottom-col-label',
                          secretTransition(item.securityLevel).iconClassName,
                        ]"
                        >{{ secretTransition(item.securityLevel).name }}</div
                      >
                    </div>
                    <div class="card-bottom-col">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
              <div v-if="state.cardData.length === 0" class="empty">
                <img class="empty-img" src="@/assets/table-no-content.png" />
                <p class="empty-text">暂无采集任务，请先新增</p>
              </div>
            </div>
            <div v-else class="table-list dif" v-loading="state.loading">
              <CfTable
                :key="state.key"
                saveWidth
                :isDisplayAction="true"
                :table-head-titles="state.tableHeadTitles"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    init()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    init()
                  },
                }"
                actionWidth="200"
              >
                <template #name="{ row }">
                  <div class="taskName" :title="row.name">
                    <!--                  <SvgIcon class="icon-status-svg" icon="collect-name-icon" title="名称" />-->
                    {{ row.name }}
                  </div>
                </template>
                <template #datasourceType="{ row }">
                  <div class="datasourceType">
                    <img class="datasourceType-img" :src="getAssetsImages(row.sourceType)" />
                    {{ row.sourceName }}
                  </div>
                </template>
                <template #datasourceSecret="{ row }">
                  <div
                    :class="['datasourceSecret', secretTransition(row.securityLevel).iconClassName]"
                    >{{ secretTransition(row.securityLevel).name }}</div
                  >
                </template>
                <template #rateTime="{ row }">
                  <div class="rateTime-box" :title="row.rateTime">
                    {{ row.rateTime }}
                  </div>
                </template>
                <template #envType="{ row }">
                  <div class="envType">
                    <div :class="{ 'envType-name': true, test: row.envType === 'TEST' }">{{
                      row.envType === 'TEST'
                        ? '开发环境'
                        : row.envType === 'OFFICIAL'
                        ? '正式环境'
                        : '--'
                    }}</div>
                  </div>
                </template>

                <template #taskStatus="{ row }">
                  <div class="taskStatus">
                    <div :class="['circle', stateTransition(row.publishStatus, 'iconName')]"></div>
                    {{ stateTransition(row.publishStatus, 'cName') }}
                  </div>
                </template>
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      v-if="
                        buttonAuthList.includes('dataManagement_dataCollection_publish_edit') &&
                        row.publishStatus !== 'PUBLISHED'
                      "
                      class="has-right-border"
                      variant="text"
                      code="dataManagement_dataCollection_publish_edit"
                      @click.prevent="cardSendFn(row)"
                    >
                      发布
                    </n-button>
                    <n-button
                      v-if="
                        buttonAuthList.includes('dataManagement_dataCollection_off_edit') &&
                        row.publishStatus === 'PUBLISHED'
                      "
                      class="has-right-border"
                      variant="text"
                      code="dataManagement_dataCollection_off_edit"
                      @click.prevent="cardOffFn(row)"
                      >下架</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_dataCollection_edit') &&
                        row.publishStatus !== 'PUBLISHED'
                      "
                      variant="text"
                      code="dataManagement_dataCollection_edit"
                      @click.prevent="cardEditFn(row)"
                      >编辑</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_dataCollection_delete') &&
                        row.publishStatus !== 'PUBLISHED'
                      "
                      variant="text"
                      code="dataManagement_dataCollection_delete"
                      @click.prevent="cardDelFn(row)"
                      >删除</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="buttonAuthList.includes('dataManagement_dataCollection_view')"
                      code="dataManagement_dataCollection_view"
                      variant="text"
                      @click.prevent="cardSeeFn(row)"
                      >查看</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_dataCollection_run_edit') &&
                        row.publishStatus === 'PUBLISHED'
                      "
                      variant="text"
                      @click.prevent="cardRunFn(row)"
                      >立即执行</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
            <div
              v-if="state.showType === 'card'"
              class="data-source-page-content-pagination nancalui-table-page"
            >
              <n-pagination
                :total="state.pageInfo.total"
                v-model:pageSize="state.pageInfo.pageSize"
                v-model:pageIndex="state.pageInfo.currentPage"
                :can-change-page-size="true"
                canViewTotal
                :page-size-options="[10, 20, 50, 100]"
                @page-index-change="cardCurrentChange"
                @page-size-change="cardSizeChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick, watch } from 'vue'
  import { useStore } from 'vuex'
  import { useRoute } from 'vue-router'
  import {
    queryDataTypeTree,
    collectJobList,
    collectJobDel,
    collectJobExecute,
    collectJobOffline,
    collectJobPublish,
  } from '@/api/dataManage'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import cardList from '@/components/CardList'
  import CfTtee from '@/components/cfTtee'
  export default {
    name: '',
    components: { cardList },
    props: {},
    setup() {
      const store = useStore()
      const route = useRoute()
      watch(
        () => route.params,
        () => {
          let refreshListKey = sessionStorage.getItem('refreshListKey')
          if (refreshListKey === 'dataCollectionRefresh') {
            methods.init()
          }
        },
      )
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: {},
        tableHeight: 436,
        key: 1,
        showType: 'card',
        cardLoading: true,
        loading: false,
        currentOperation: 1, // 当前操作  1：发布 2：删除 3：编辑 4：下架 5：查看
        originalFormInline: {
          sourceType: null,
          keyword: null,
          datasourceName: null,
          sourceEntry: null,
        },
        formInline: {
          sourceType: null,
          keyword: null,
          datasourceName: null,
          sourceEntry: null,
        },
        org_dataSourceType: [
          { label: '待发布', value: 'WAITING_PUBLISH' },
          { label: '已发布', value: 'PUBLISHED' },
          { label: '已下架', value: 'OFFLINE' },
        ], //状态集合
        optionItemData: {}, // 操作的任务数据
        cardData: [],
        pageInfo: {
          total: 0,
          pageSize: 12,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'name', name: '任务名称', slot: 'name' },
          { prop: 'sourceEntry', name: '英文名' },
          { prop: 'dataSourceName', name: '数据源', slot: 'datasourceType' },
          { prop: 'dataSourceName', name: '数据源密级', slot: 'datasourceSecret' },
          { prop: 'status', name: '状态', slot: 'taskStatus' },
          { prop: 'period', name: '调度信息', width: 180 },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'createByName', name: '创建人' },
        ],
        categoryId: null,
        treeSearchText: '',
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        expandedKeys: [], 
        selectedKey: null,
      })

      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const methods = {
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        // 获取图片
        getAssetsImages(name) {
          return new URL(`/src/assets/img/dev/card-source/${name}.png`, import.meta.url).href //本地文件路径
        },
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 196
          } else {
            state.tableHeight = document.body.offsetHeight - 298
          }
        },

        //匹配状态设置中文名称和图标
        stateTransition(status, needCn = false) {
          let _status = 2
          let _statusCn = '待发布'
          let iconClassName = 'blue'
          switch (status) {
            case 'WAITING_PUBLISH':
              _status = 2
              _statusCn = '待发布'
              iconClassName = 'blue'
              break
            case 'PUBLISHED':
              _status = 1
              _statusCn = '已发布'
              iconClassName = 'green'
              break
            case 'OFFLINE':
              _status = 0
              _statusCn = '已下架'
              iconClassName = 'gray'
              break
            default:
              break
          }

          return needCn ? (needCn === 'cName' ? _statusCn : iconClassName) : _status
        },

        //匹配密级
        secretTransition(status) {
          let name = '公开'
          let iconClassName = 'blue1'

          switch (status) {
            case 'INTERIOR':
              name = '内部'
              iconClassName = 'yellow'
              break
            case 'CONTROLLED':
              name = '受控'
              iconClassName = 'blue2'
              break
            case 'SECRET':
              name = '秘密'
              iconClassName = 'red2'
              break
            case 'CONFIDENTIAL':
              name = '机密'
              iconClassName = 'red1'
              break
            case 'CORE':
              name = '核心'
              iconClassName = 'blue3'
              break
            default:
              break
          }

          return { name, iconClassName }
        },

        // 发布
        cardSendFn(item) {
          state.optionItemData = item
          state.currentOperation = 1
          proxy.$MessageBoxService.open({
            title: '是否确认发布任务',
            content: '发布后，采集作业将会开始运行',
            save: () => {
              methods.submitOtherDialog()
            },
          })
          // collectJobPublish({ id: state.optionItemData.id }).then((res) => {
          //   let { success, data } = res
          //   if (success) {
          //     if (data.success) {
          //       //可发布
          //       state.currentOperation = 1
          //       proxy.$MessageBoxService.open({
          //         title: '是否确认发布任务',
          //         content: '发布后，数据作业将会开始运行',
          //         save: () => {
          //           methods.submitOtherDialog()
          //         },
          //       })
          //     } else {
          //       //不可发布，提示是否新增
          //       proxy.$MessageBoxService.open({
          //         title: '是否确认发布任务',
          //         content: data.message,
          //         save: () => {
          //           //前往新增页面
          //           router.push({ name: 'collectionAdd' })
          //         },
          //       })
          //     }
          //   }
          // })
        },
        // 删除
        cardDelFn(item) {
          state.optionItemData = item
          state.currentOperation = 2
          proxy.$MessageBoxService.open({
            title: '是否确认删除任务',
            content: '删除后，数据作业将会被停止运行',
            save: () => {
              methods.submitOtherDialog()
            },
          })
        },

        // 下架
        cardOffFn(item) {
          state.optionItemData = item
          state.currentOperation = 4
          proxy.$MessageBoxService.open({
            title: '是否确认下架任务',
            content: '下架后，数据作业将会被暂停运行',
            save: () => {
              methods.submitOtherDialog()
            },
          })
        },
        // 确认执行其他弹框
        submitOtherDialog() {
          switch (state.currentOperation) {
            case 1: // 发布
              state.loading = true
              collectJobPublish({ id: state.optionItemData.id })
                .then((res) => {
                  state.loading = false
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '发布成功',
                      type: 'success',
                    })
                  }

                  if (state.showType === 'card') {
                    methods.init(true)
                  } else {
                    methods.init(false)
                  }
                })
                .catch(() => {
                  state.loading = false
                })
              break
            case 2: // 删除
              state.loading = true
              collectJobDel({ id: state.optionItemData.id })
                .then((res) => {
                  state.loading = false
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除成功',
                      type: 'success',
                    })
                    let initState = false
                    if (state.showType === 'card') {
                      initState = state.cardData.length === 1 ? true : false
                    } else {
                      initState = state.tableData.length === 1 ? true : false
                    }
                    if (state.showType === 'card') {
                      methods.init(true)
                    } else {
                      methods.init(initState)
                    }
                  }
                })
                .catch(() => {
                  state.loading = false
                })
              break
            case 4: // 下架
              state.loading = true
              collectJobOffline({ id: state.optionItemData.id })
                .then((res) => {
                  let { success } = res
                  state.loading = false

                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '下架成功',
                      type: 'success',
                    })
                    if (state.showType === 'card') {
                      methods.init(true)
                    } else {
                      methods.init(false)
                    }
                  }
                })
                .catch(() => {
                  state.loading = false
                })

              break
            default:
              break
          }
        },
        //新增数据采集
        goJumpAdd(name) {
          router.push({ name: name, query: { sourceCategoryId: state.categoryId } })
        },
        // 编辑
        cardEditFn(item) {
          router.push({
            name: 'collectionEdit',
            query: {
              editId: item.id,
              status: item.publishStatus,
              sourceCategoryId: item.sourceCategoryId,
            },
          })
          // let { collectType, status } = item
          // state.optionItemData = item
          //
          // api.dataManagement.checkDataSource({ id: state.optionItemData.id }).then((res) => {
          //   let { success, data } = res
          //   if (success) {
          //     if (data.success) {
          //       //可编辑
          //       if (collectType === 'STRUCTURE') {
          //         router.push({
          //           name: 'collectionEdit',
          //           query: { editId: item.id, status: item.publishStatus },
          //         })
          //       }
          //     } else {
          //       //判断状态--创建可编辑-提示重新选择数据源  -- 下架提示新增
          //       if (status === 'CREATED' && collectType === 'STRUCTURE') {
          //         proxy.$MessageBoxService.open({
          //           title: '提示',
          //           content: '此任务内数据源已被下架/解绑导致采集任务不可用，请更换数据源',
          //           save: () => {
          //             //前往编辑页面-更换数据源
          //             router.push({
          //               name: 'collectionEdit',
          //               query: { editId: item.id, status: item.publishStatus, disabledDataSource: true },
          //             })
          //           },
          //         })
          //       }
          //       if (status === 'OFFLINE' && collectType === 'STRUCTURE') {
          //         proxy.$MessageBoxService.open({
          //           title: '提示',
          //           content: '此任务内数据源已被下架/解绑导致采集任务不可用，是否新增采集任务？？',
          //           save: () => {
          //             //前往新增页面
          //             router.push({ name: 'collectionAdd' })
          //           },
          //         })
          //       }
          //     }
          //   }
          // })
        },
        // 查看
        cardSeeFn(item) {
          // let { collectType } = item
          state.optionItemData = item
          router.push({
            name: 'collectionDetail',
            query: { editId: item.id },
          })
          // // 数据化数据采集
          // if (collectType === 'STRUCTURE') {
          //   router.push({
          //     name: 'collectionDetail',
          //     query: { editId: item.id },
          //   })
          // } else {
          //   router.push({
          //     name: 'collectionUnstructureDetail',
          //     query: { editId: item.id },
          //   })
          // }
        },

        // 立即执行
        cardRunFn(item) {
          let { id } = item
          collectJobExecute({ id }).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '开始执行',
                type: 'success',
              })
            }
            if (state.showType === 'card') {
              methods.init(true)
            } else {
              methods.init(false)
            }
          })
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            sourceType: null,
            keyword: null,
            datasourceName: null,
            sourceEntry: null,
          }
          methods.searchClickFn()
        },
        //搜索
        onSearch() {
          state.key++
          methods.init(true)
        },
        // 获取树列表
        getTreeListFn() {
          queryDataTypeTree({}).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              if (res.data?.children.length > 0) {
                treeData = [res.data]
                treeData[0].selected = true
                state.categoryId = treeData[0].enName === 'data_cate_all' ? null : treeData[0].id
              } else {
                treeData.push({
                  children: [],
                  id: null,
                  level: 0,
                  label: '全部',
                  type: 'ROOT',
                })
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]

              nextTick(() => {
                state.expandedKeys = [state.treeData?.[0]?.id || 1]
                state.selectedKey = state.treeData?.[0]?.id || 1
              })
              methods.onSearch(true)
            }
          })
        },
        // 树搜索
        searchTreeFn() {
          state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.id) {
            state.categoryId = node.enName === 'data_cate_all' ? null : node.id
            methods.onSearch(true)
          }
        },
        // 初始化
        init(init = false) {
          if (state.showType === 'card') {
            methods.initCard(init)
          } else {
            methods.initTable(init)
          }
        },
        // 初始化表格（卡片）
        initCard(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              publishStatus: state.formInline.sourceType || null,
              name: state.formInline.keyword || null,
              datasourceName: state.formInline.datasourceName || null,
              sourceEntry: state.formInline.sourceEntry || null,
              sourceCategoryId: state.categoryId,
            },
          }
          if (init) {
            state.cardLoading = true
          }
          collectJobList(data)
            .then((res) => {
              state.cardLoading = false
              res.data.list.forEach((item) => {
                let {
                  name,
                  createTime,
                  createByName,
                  dataSourceName,
                  dataSourceType,
                  publishStatus,
                  collectType,
                  description,
                } = item
                item.cardTitle = name
                item.cardDate = createTime
                item.cardUser = createByName
                item.cardStatus = methods.stateTransition(publishStatus) // 7待发布 1已发布 0已下架
                item.cardContentTitle = '调度策略'

                let _cardOperation = []
                switch (
                  item.cardStatus // 卡片更多操作 a：发布 b：删除 c：编辑 d：下架 e：查看 f:立即执行 g:撤回 h:测试 i:解绑 j:开发 k:申请
                ) {
                  case 1: // 发布状态
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_off_edit')) {
                      _cardOperation.push('d')
                    }
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_view')) {
                      _cardOperation.push('e')
                    }
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_run_edit')) {
                      _cardOperation.push('f')
                    }

                    item.cardOperation = _cardOperation.join('')
                    break
                  default: // 下架状态
                    if (
                      buttonAuthList.value.includes('dataManagement_dataCollection_publish_edit')
                    ) {
                      _cardOperation.push('a')
                    }
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_delete')) {
                      _cardOperation.push('b')
                    }
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_edit')) {
                      if (collectType === 'STRUCTURE') {
                        //结构化采集才有编辑目前
                        _cardOperation.push('c')
                      }
                    }
                    if (buttonAuthList.value.includes('dataManagement_dataCollection_view')) {
                      _cardOperation.push('e')
                    }
                    item.cardOperation = _cardOperation.join('')
                    break
                }

                item.cardMappingTitle = '采集信息'
                item.cardMappingLabel = [
                  { name: '数据源类型', value: dataSourceType, type: 1 },
                  { name: '数据源名称', value: dataSourceName, type: 1 },
                  // { name: '数据环境', value: envTypeName, type: 1, width: '72px' },
                ]
                item.cardDescriptionTitle = '描述信息'
                item.cardDescription = { value: description }
              })
              if (init) {
                state.cardData = []
              }
              state.cardData = res.data.list
              state.pageInfo.total = res.data.total
              state.pageInfo.pages = res.data.pages
            })
            .catch(() => {
              state.cardLoading = false
            })
        },
        // 初始化表格（列表）
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage

          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              publishStatus: state.formInline.sourceType || null,
              name: state.formInline.keyword || null,
              datasourceName: state.formInline.datasourceName || null,
              sourceEntry: state.formInline.sourceEntry || null,
              sourceCategoryId: state.categoryId,
            },
          }
          state.loading = true
          collectJobList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.number = index + 1
                  if (item.schedulingConfigInfo) {
                    item.period = methods.changeChinese(item.schedulingConfigInfo)
                    item.rateTime = methods.changeChinese(item.schedulingConfigInfo, true)
                  } else {
                    item.period = '未配置'
                    item.rateTime = '未配置'
                  }
                })
                state.tableData = res.data
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        changeChinese(data, rateTime = false) {
          let words = ''
          let rateTime_words = data.effectiveFromTime
            ? data.effectiveFromTime + ' - ' + data.effectiveThruTime
            : '--'
          if (data?.schedulingConfigInfo?.periodicDeclarationMode === 'CRON_EXPRESSION') {
            words = data.cron
          } else {
            switch (data.timeUnit) {
              case 'HOUR':
                words = `间隔${data.timeInterval}小时`
                break
              case 'DAY':
                words = `每日 | ${data?.timePoint}`
                break
              case 'WEEK':
                let arr = data?.timeInterval?.split(',') || []
                arr = arr.map((v) => {
                  v = methods.weekForChinese(Number(v))
                  return v
                })
                words = `每周${arr.toString()} | ${data?.timePoint}`
                break
              case 'MONTH':
                words = `每月${data.timeInterval}号 | ${data?.timePoint}`
                break
            }
          }
          return rateTime ? rateTime_words : words
        },
        weekForChinese(num) {
          let word = '一'
          switch (num) {
            case 1:
              word = `一`
              break
            case 2:
              word = `二`
              break
            case 3:
              word = `三`
              break
            case 4:
              word = `四`
              break
            case 5:
              word = `五`
              break
            case 6:
              word = `六`
              break
            case 7:
              word = `日`
              break
          }
          return word
        },
        //改变展示形式
        changeStyle(type) {
          if (state.showType === type) return
          state.showType = type
          if (type === 'table') {
            state.pageInfo.pageSize = 10
          } else {
            state.pageInfo.pageSize = 12
          }
          localStorage.setItem('dataCollectionShowType', state.showType)
          nextTick(() => {
            methods.init(true)
          })
        },
        // 切换分页数量
        cardSizeChange(val) {
          state.pageInfo.pageSize = val
          methods.init(true)
        },
        // 切换分页
        cardCurrentChange(val) {
          state.pageInfo.currentPage = val
          methods.init()
        },

        // 列表（表格）操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.init()
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        if (localStorage.getItem('dataCollectionShowType')) {
          state.showType = localStorage.getItem('dataCollectionShowType')
          if (state.showType === 'table') {
            state.pageInfo.pageSize = 10
          } else {
            state.pageInfo.pageSize = 12
          }
        }
        const { name, projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        state.formInline.projectName = name.value
        methods.getTreeListFn()
        setTimeout(() => {
          sessionStorage.setItem('refreshListKey', '')
        })
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    &.isLzos {
      height: 100%;
      padding: 0;
    }
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          //&:before {
          //  position: absolute;
          //  top: 17px;
          //  left: 0;
          //  width: 4px;
          //  height: 18px;
          //  background: var(
          //    --Radial,
          //    radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
          //  );
          //  border-radius: 0 4px 4px 0;
          //  content: '';
          //}
          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;
            background-color: #1e89ff;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    box-sizing: border-box;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 0;
    padding: 0;
    .data-collection-page-tree {
      width: 286px;
      height: 100%;
      padding: 8px 0;
      background-color: #fff;
      border-radius: 2px;
      box-sizing: border-box;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 36px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }
      }
      &-ipt {
        padding: 0 12px;
        margin-bottom: 8px;

        :deep(.nancalui-input-slot__suffix) {
          opacity: 0.5;

          .icon-search {
            font-weight: normal;
            transform: scale(1.4);
          }
        }
      }

      :deep(.tree-box) {
        padding: 0 12px;
        height: calc(100% - 76px);
      }
    }
    .data-collection-page-content {
      width: calc(100% - 10px - var(--aside-width));
      height: 100%;
      border-radius: 2px;
      background-color: #fff;
      overflow: hidden;
      .out-box {
        height: 100%;
        .row {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #dcdfe6;
          box-sizing: border-box;
          padding: 0 8px;
          .switch {
            display: flex;
            align-items: center;
          }
          .switch-label {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #1e89ff;
            font-size: 14px;
            padding: 5px 14px;
            cursor: pointer;
            border-radius: 4px;
            .icon-switch {
              font-size: 16px;
              margin-right: 4px;
            }
            &.more {
              margin-left: 16px;
            }
            &:hover {
              color: #479dff;
              background-color: #ecf7ff;
            }
            &.blue {
              background-color: #1e89ff;
              border-radius: 2px;
              color: #fff;
              &:hover {
                background-color: #6e9eff;
              }
            }
          }
        }
      }

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 106px);
        padding: 16px 16px 0 16px;
        &.dif {
          padding: 0;
          height: calc(100% - 46px);
        }
        .card {
          position: relative;
          display: inline-block;
          width: calc((100% - 32px) / 2);
          min-width: 280px;
          margin: 0 16px 16px 0;

          padding: 16px 20px;
          overflow: hidden;
          vertical-align: top;
          border: 1px solid #c5d0ea;
          border-radius: 8px;
          // background: #ecf0f4;
          &:hover {
            border: 1px solid #fff;
            box-shadow: 0 4px 16px -2px rgba(0, 0, 0, 0.1);
          }
          .card-top {
            display: flex;
            .top-left {
              margin-right: 8px;
              img {
                width: 40px;
                height: 40px;
                border-radius: 6px;
              }
            }
            .top-right {
              width: calc(100% - 48px);
              color: rgba(0, 0, 0, 0.48);
              font-size: 12px;
              .title {
                display: flex;
                justify-content: space-between;
                color: rgba(0, 0, 0, 0.85);
                font-weight: bolder;
                font-size: 16px;
                line-height: 24px;
                .title-name {
                  width: calc(100% - 24px);
                  overflow: hidden;
                  color: rgba(0, 0, 0, 0.9);
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
                .title-operate {
                  display: flex;
                  align-items: center;
                  width: 24px;
                  height: 24px;
                  padding: 4px;
                  color: $themeBlue;
                  border-radius: 6px;
                  &:hover {
                    background-color: #ecf7ff;
                  }
                }
              }
              .message {
                overflow: hidden;
                color: rgba(0, 0, 0, 0.55);
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
          .card-mid {
            margin: 8px 0 12px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 400;
            font-size: 14px;
            .card-mid-first {
              display: flex;
              margin-bottom: 8px;
              line-height: 22px;
            }
            &-item {
              margin-bottom: 8px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.75);
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .description {
              width: 100%;
              height: 22px;
              margin-bottom: 12px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.75);
              line-height: 22px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .card-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 24px;
            &-col {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: rgba(0, 0, 0, 0.55);
              font-weight: 400;
              font-size: 12px;
              font-family: 'PingFang SC';
              font-style: normal;
              line-height: 24px;
              font-feature-settings: 'clig' off, 'liga' off;
              &-label {
                box-sizing: border-box;
                height: 24px;
                margin-right: 4px;
                padding: 0 8px;
                color: rgba(0, 0, 0, 0.75);
                font-size: 14px;
                line-height: 24px;
                background-color: #f4f4f5;
                border: 1px solid rgba(177, 179, 184, 0.53);
                border-radius: 2px;
                &.yellow {
                  color: #ffba70;
                  background-color: #fff4e6;
                  border: 1px solid #ffba70;
                }
                &.green {
                  color: #31b046;
                  background-color: #ebfaed;
                  border: 1px solid #31b046;
                }
                &.blue {
                  color: #1e89ff;
                  background-color: #ebf4ff;
                  border: 1px solid #99c9ff;
                }
                &.blue1 {
                  color: rgba(26, 164, 238, 0.4);
                  background-color: rgba(26, 164, 238, 0.08);
                  border: 1px solid rgba(26, 164, 238, 0.4);
                }
                &.blue2 {
                  color: #1e89ff;
                  background-color: #ebf4ff;
                  border: 1px solid #99c9ff;
                }
                &.blue3 {
                  color: rgba(34, 78, 205, 0.4);
                  background-color: rgba(34, 78, 205, 0.08);
                  border: 1px solid rgba(34, 78, 205, 0.4);
                }
                &.red1 {
                  color: rgba(122, 0, 0, 0.4);
                  background-color: rgba(122, 0, 0, 0.08);
                  border: 1px solid rgba(122, 0, 0, 0.4);
                }
                &.red2 {
                  color: #ef7777;
                  background-color: #ffeded;
                  border: 1px solid #ef7777;
                }
              }
            }
          }
        }
        :deep(.el-table__body) {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }
          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;
              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }
          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }
          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;
              &.PUBLISH {
                background-color: #04c495;
              }
              &.CREATED {
                background-color: #447dfd;
              }
              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }
        :deep(.nancalui-table-page) {
          padding: 16px;
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        justify-content: center;
        flex-direction: column;
        height: 60px;
        padding: 0 16px;
        border-top: 1px solid #dcdfe6;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }

      .card-list {
        height: 100%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }
        &::-webkit-scrollbar-thumb {
          background-color: #cfcfcf;
          border-radius: 2px;
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.yellow {
          color: #fe8624;
          background-color: #fff4e6;
          border: 1px solid #ffba70;
        }
        &.blue1 {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #99c9ff;
        }
        &.blue2 {
          color: #1aa4ee;
          border: 1px solid rgba(26, 164, 238, 0.4);
          background: rgba(26, 164, 238, 0.08);
        }
        &.blue3 {
          color: #224ecd;
          border: 1px solid rgba(34, 78, 205, 0.4);
          background: rgba(34, 78, 205, 0.08);
        }
        &.red1 {
          color: #7a0000;
          border: 1px solid rgba(122, 0, 0, 0.4);
          background: rgba(122, 0, 0, 0.08);
        }
        &.red2 {
          color: #d40000;
          border: 1px solid #ef7777;
          background: #ffeded;
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  /*屏幕宽度大于1220时展示3个卡片*/
  @media only screen and (min-width: 1220px) {
    .data-collection-page {
      .data-collection-page-content {
        .card-list {
          .card {
            width: calc((100% - 32px) / 3);
            &:nth-of-type(3n) {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  /*屏幕宽度大于1630时展示4个卡片*/
  @media only screen and (min-width: 1630px) {
    .data-collection-page {
      .data-collection-page-content {
        .card-list {
          .card {
            width: calc((100% - 48px) / 4);
            &:nth-of-type(3n) {
              margin-right: 16px;
            }
            &:nth-of-type(4n) {
              margin-right: 0;
            }
          }
        }
      }
    }
  }
</style>
