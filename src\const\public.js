/*
 * 时间戳转化为日期
 * @param timestamp 时间戳
 * @param time 是否返回时间
 */
export const timestampToTime = (timestamp, time = false) => {
  let date = new Date(timestamp) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear()
  let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  let h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  if (time) {
    if(time === 'HOUR'){
      return Y + '-' + M + '-' + D + ' ' + h
    }else if(time === 'MINUTE'){
      return Y + '-' + M + '-' + D + ' ' + h + ':' + m
    }else {
      return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
    }
  } else {
    return Y + '-' + M + '-' + D
  }
}
