<template>
  <section class="container">
    <section class="container-box">
      <section class="cf-tools">
        <div class="row">
          <div class="col text-label">
            <n-select
              v-model="condition.envType"
              placeholder="请选择"
              :options="envTypeOptions"
              clearable
            />
          </div>
          <div class="col text-label">
            名称：
            <n-input v-model="condition.taskName" placeholder="实时作业名称" />
          </div>
          <div class="col text-label">
            任务状态：
            <n-select
              v-model="condition.status"
              placeholder="请选择"
              :options="statusOptions"
              clearable
            />
          </div>
          <div class="col text-label">
            任务触发时间：
            <n-range-date-picker-pro
              class="createTime"
              v-model="state.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              allow-clear
            />
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <CfTable
          saveWidth
          class="multipleTableRef"
          ref="multipleTableRef"
          :key="state.tableList"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.total,
            pageSize: state.filterSearch.pageSize,
            currentPage: state.filterSearch.pageNum,
            onCurrentChange: (v) => {
              state.filterSearch.pageNum = v
              onSearch()
            },
            onSizeChange: (v) => {
              state.filterSearch.pageSize = v
              state.filterSearch.pageNum = 1
              onSearch()
            },
          }"
          :table-head-titles="state.tableHeadTitles"
        >
          <template #pageTop>
            <div class="table-top">
              <div class="title">实时监控</div>
            </div>
          </template>
          <template #status="{ row }">
            <div v-if="row.status" class="row">
              <SvgIcon v-if="row.status === 'RUNNING'" icon="icon-rt-run" title="运行中" />
              <SvgIcon v-else-if="row.status === 'STOP'" icon="icon-rt-stop" title="停止" />
              <SvgIcon v-else icon="icon-rt-stop" title="失败" />

              {{ { RUNNING: '运行中', STOP: '停止', FAILED: '失败' }[row.status] || '--' }}
            </div>
            <div v-else> -- </div>
          </template>
          <template #editor="{ data: { row } }">
            <el-button v-if="row.status === 'RUNNING'" type="primary" link @click="stopFn(row.id)">
              停止
            </el-button>
            <el-button v-else-if="row.status === 'STOP'" type="primary" link @click="runFn(row)">
              启动</el-button
            >
            <el-button type="primary" link @click.prevent="sqlFn(row)"> 查看作业 </el-button>
            <el-button type="primary" link @click.prevent="logFn(row)">查看日志 </el-button>
          </template>
        </CfTable>
      </section>
    </section>
    <LogDialog ref="logDialog" @success="onSearch" />
    <SqlDialog ref="sqlDialogRef" @success="onSearch" />
  </section>
</template>
<script setup>
  import { ElMessage } from 'element-plus'
  import { formartTime } from '@/utils/index'
  import api from '@/api/index'
  import LogDialog from './components/logDialog.vue'
  import SqlDialog from './components/sqlDialog.vue'
  const { proxy } = getCurrentInstance()
  const state = reactive({
    tableList: [],
    treeSearchText: '',
    filterSearch: {
      condition: {
        envType: '',
        status: null,
        taskName: '',
        endTime: null,
        startTime: null,
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    total: 0,
    tableHeadTitles: [
      {
        name: '实时作业名称',
        prop: 'taskName',
      },
      {
        name: '描述',
        prop: 'description',
      },
      {
        name: '责任人',
        prop: 'personInChargeName',
      },
      {
        name: '任务触发时间',
        prop: 'startTime',
      },
      {
        name: '运行状态',
        prop: 'status',
        slot: 'status',
      },
    ],
    time: [new Date(), new Date()],
  })
  const logDialog = ref(null)
  const sqlDialogRef = ref(null)
  const condition = ref({ envType: 'OFFICIAL' })

  const statusOptions = [
    {
      name: '运行中',
      value: 'RUNNING',
    },
    {
      name: '停止',
      value: 'STOP',
    },
    {
      name: '失败',
      value: 'FAILED',
    },
  ]
  const envTypeOptions = [
    { name: '生产环境', value: 'OFFICIAL' },
    { name: '开发环境', value: 'TEST' },
  ]

  // 获取实时作业运行列表
  const getRealTimeDevelopList = async () => {
    api.realTimeDevelop.getRealTimeDevelopList(state.filterSearch).then(({ data }) => {
      state.tableList = data?.list || []
      state.total = data?.total || 0
    })
  }
  const onSearch = (init) => {
    init &&
      Object.assign(state.filterSearch, {
        pageNum: 1,
        condition: {
          ...condition.value,
          startTime: state.time?.[0] && formartTime(state.time[0]),
          endTime: state.time?.[1] && formartTime(state.time[1],true),
        },
      })
    getRealTimeDevelopList()
  }
  // 运行
  const runFn = (data) => {
    api.realTimeDevelop.startRealTimeDevelop(data.id).then((res) => {
      if (!res?.success) return
      // 运行成功
      ElMessage.success('运行成功！')
      onSearch()
    })
  }

  // 停止运行
  const stopFn = (id) => {
    api.realTimeDevelop.stopRealTimeDevelop(id).then((res) => {
      if (!res?.success) return
      ElMessage.success('停止成功！')
      onSearch()
    })
  }

  // 查看日志
  const logFn = (data) => {
    logDialog.value.open(data)
  }

  const resetFn = (() => {
    const data = JSON.parse(JSON.stringify(state.filterSearch))
    return () => {
      state.filterSearch = JSON.parse(JSON.stringify(data))
      condition.value = {}
      state.time = []
      onSearch()
    }
  })()
  const sqlFn = (data) => {
    api.realTimeDevelop.getRealTimeDevelopRunContent(data.id).then((res) => {
      if (!res?.success) return
      sqlDialogRef.value.open(res.data)
    })
  }
  onSearch(true)
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 10px;
      display: flex;
      flex-direction: column;
      &-table {
        flex: 1;
        height: calc(100% - 60px);
        display: flex;
        gap: 10px;
        border-radius: 0px 0px 2px 2px;
        background: var(--100, #fff);
        .row {
          display: flex;
          align-items: center;
          gap: 6px;
          flex: 1 0 0;
        }
      }
    }
  }
</style>
<style lang="scss">
  .multipleTableRef .page-top {
    display: flex;
    padding: 5px 0;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    .title {
      display: flex;
      padding: 6px 12px 6px 0px;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;

      color: #1d2129;
      font-family: 'Source Han Sans CN';
      font-size: 16px;
      font-style: normal;
      font-weight: bolder;
      line-height: 24px;
      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: #1e89ff;
        margin-right: 8px;
      }
    }
  }
</style>
