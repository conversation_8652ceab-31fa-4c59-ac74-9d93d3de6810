<template>
  <section class="container">
    <div class="page-title">
      流程配置
      <div class="detail-back-box" @click.prevent="cancelFn"> 返回 </div>
    </div>
    <div class="container-box" id="setFlowContainerBox">
      <div class="container-box-canvas" id="setFlowContainer"></div>
    </div>
    <div class="footer">
      <n-button
        variant="solid"
        color="primary"
        :loading="state.loading"
        @click.stop.prevent="saveFn"
        >保存
      </n-button>
      <n-button @click.stop.prevent="cancelFn">取消</n-button>
    </div>
  </section>
</template>

<script setup>
  import { onMounted, reactive, nextTick, onBeforeUnmount } from 'vue'
  import setFlowGraph from './index.js'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'

  const router = useRouter()
  const state = reactive({
    nodeConfigShow: false,
    loading: false,
    id: null,
    graph: null,
    flowData: {},
    name: '',
  })
  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  const cancelFn = () => {
    router.go(-1)
  }

  const saveFn = () => {
    //获取画布所有数据
    let json = state.graph.toJSON()
    let proNodes = json.cells.filter((val) => val.shape !== 'process-edge')
    let proEdges = json.cells.filter((val) => val.shape === 'process-edge')
    let nodes = []
    let edges = []

    proNodes.forEach((item) => {
      nodes.push({
        assignee: item.data.assignee ? Number(item.data.assignee) : null,
        locationX: item.position.x,
        locationY: item.position.y,
        name: item.data.nodeType !== 'TASK' ? item.data.name : String(item.data.assignee),
        nodeCode: item.data.nodeCode ? item.data.nodeCode : '',
        nodeType: item.data.nodeType,
        optType: item.data.nodeType === 'TASK' ? 'APPROVAL_RESULT_OPINION' : '',
      })
    })

    proEdges.forEach((item) => {
      edges.push({
        sourceNodeCode: item.source.cell,
        targetNodeCode: item.target.cell,
      })
    })

    const params = {
      desc: '',
      name: state.name,
      nodes,
      processCode: state.id,
      relations: edges,
    }

    api.system
      .processManageUpdate(params)
      .then((res) => {
        if (res.success) {
          ElNotification({
            title: '提示',
            message: '保存成功',
            type: 'success',
          })
          router.go(-1)
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  }

  const processManageGetNodeCodeFn = () => {
    return api.system
      .processManageGetNodeCode({})
      .then((res) => {
        if (res.success) {
          return res.data.nodeCode
        }
        return null
      })
      .catch((error) => {
        console.error('获取节点编码失败:', error)
        return null
      })
  }

  // 初始化创建画布
  const initFn = async (detailData) => {
    const nodes = []
    const edges = []
    let isStart = false
    let isEnd = false

    // if(!detailData.nodes){
    //   isStart = true
    //   isEnd = true
    // }

    if (detailData.relations && detailData) {
      detailData.nodes.forEach((item) => {
        if (item.nodeType === 'START') {
          isStart = true
          item.isSide = true
        }
        nodes.push(item)
      })
      detailData.nodes.forEach((item) => {
        if (item.nodeType === 'END') {
          isEnd = true
          item.isSide = true
          item.isEnd = true
        }
      })

      detailData.relations.forEach((item) => {
        edges.push({
          source: item.sourceNodeCode,
          target: item.targetNodeCode,
        })
      })
    }

    if (!isStart) {
      const nodeCode = await processManageGetNodeCodeFn()
      nodes.push({
        assignee: null,
        assigneeName: null,
        name: '开始',
        nodeCode,
        nodeType: 'START',
        isSide: true,
      })
    }
    if (!isEnd) {
      const nodeCode = await processManageGetNodeCodeFn()
      nodes.push({
        assignee: null,
        assigneeName: null,
        name: '结束',
        nodeCode,
        nodeType: 'END',
        isSide: true,
        isEnd: true,
      })
    }

    if (!isStart && !isEnd) {
      edges.push({
        source: nodes[0].nodeCode,
        target: nodes[1].nodeCode,
      })
    }

    state.flowData = {
      nodes,
      edges,
    }

    state.graph = setFlowGraph.init(state.flowData)

    state.resizeFn = () => {
      nextTick(() => {
        resizeFn()
      })
    }
    state.resizeFn()
    window.addEventListener('resize', state.resizeFn)
  }
  // 计算尺寸
  const resizeFn = () => {
    const { width, height } = getContainerSize()
    state.graph.resize(width, height)
    state.graph.centerContent()
  }
  // 获取画布容器大小
  const getContainerSize = () => {
    return {
      width: document.getElementById('setFlowContainerBox').offsetWidth,
      height: document.getElementById('setFlowContainerBox').offsetHeight,
    }
  }

  // 获取详情
  const getDetailFn = () => {
    state.loading = true
    api.system
      .processManageDetail({ processCode: state.id })
      .then(async (res) => {
        if (res.success) {
          state.name = res.data?.name
          await initFn(res.data)
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  }

  onMounted(() => {
    state.id = router.currentRoute.value.query.id || null
    getDetailFn()
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', state.resizeFn)
    setFlowGraph.destroy()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    position: relative;
    .page-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 46px;
      padding: 0 8px 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;
      background-color: #fff;
      margin-bottom: 10px;
      border-radius: 2px 2px 0 0;

      &:before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }

      .detail-back-box {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        z-index: 9;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 30px;
        margin: auto;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #479dff;
          border: 1px solid #479dff;
        }
      }
    }

    &-box {
      position: relative;
      box-sizing: border-box;
      min-width: 0;
      height: calc(100% - 100px);
      background: #fff;
      border-radius: 0 0 2px 2px;
      &-canvas {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    }
    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      width: calc(100% - 32px);
      height: 60px;
      padding: 0 16px;
      line-height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      background: #fff;
    }
  }
</style>
