import { createRouter, createWebHashHistory, onBeforeRouteLeave } from 'vue-router'
import _CONSTANTS_ROUTERS from './base'
import store from '@/store'
import { TokenKey } from '@/utils/auth'

const routes = []
const modules = import.meta.glob('../views/test/*.vue')
for (const path in modules) {
  const name = path.split('/').pop().split('.').shift()
  routes.push({
    path: `/${name}`,
    component: modules[path],
  })
}

const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior: () => ({ y: 0 }),
  routes: [...routes, ..._CONSTANTS_ROUTERS],
})

//扁平化菜单，用做按钮权限判断的数组
const flatten = (arr) => {
  return arr.reduce((pre, cur) => {
    pre.push(cur)
    if (cur.children) {
      return pre.concat(flatten(cur.children))
    } else {
      return pre
    }
  }, [])
}

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
router.beforeEach(async (to, from, next) => {
  if (to.meta.isIframe) {
    sessionStorage.setItem('pageIsIframe', 'Y')
  } else {
    sessionStorage.setItem('pageIsIframe', 'N')
  }
  if (to.meta.code) {
    if (store.state.user.menuCodeList.includes(to.meta.code)) {
      next()
    } else {
      localStorage.removeItem(TokenKey)
      await store.dispatch('user/logout')
      router.push(`/`)
    }
    return
  }
  next()
})

export default router
