import { generateGuid } from '@/utils/tools'
// 对象===>数组
export function objToArray(obj) {
  const dataSource = [
    {
      ...obj.root,
      properties: undefined,
      expanded: true,
      children: [],
    },
  ]
  if (obj.root?.properties) {
    deepToArray(dataSource[0].children, obj.root.properties)
  }
  return dataSource
}
function deepToArray(data, properties) {
  let index = 0
  for (const key of Object.keys(properties)) {
    const value = properties[key]
    console.log(key, value)
    data.push({
      ...value,
      properties: undefined,
      children: [],
    })
    if (value.properties) {
      deepToArray(data[index].children, value.properties)
    }
    index++
  }
  return data
}
// 数组==>对象
export function arryToObj(arr) {
  const { filedName } = arr[0]
  const obj = {}
  obj[filedName] = { ...arr[0], properties: {}, children: undefined }
  if (arr[0].children) {
    deepToObj(obj[filedName].properties, arr[0].children)
  }
  return obj
}
// websives请求参数封装
export function arryToParams(arr) {
  const params = {}
  arr.forEach((i, index) => {
    if (i.filedName) {
      params[i.filedName] = {
        ...i,
        type: 'text',
        sort: index,
        required: false,
      }
    }
  })
  return params
}
// websives请求参数==>数组
export function paramsToArry(obj) {
  const res = []
  console.log('Object.keys(obj)', Object.keys(obj))
  if (Object.keys(obj).length) {
    for (const key of Object.keys(obj)) {
      console.log('paramsToArry', key)
      const value = obj[key]
      res.push(value)
    }
  } else {
    res.push({ filedName: '', description: '', kid: generateGuid() })
  }
  return res
}
function deepToObj(data, children) {
  for (const item of children) {
    data[item.filedName] = {
      ...item,
      children: undefined,
      properties: {},
    }
    if (item.children.length) {
      deepToObj(data[item.filedName].properties, item.children)
    }
  }
}
