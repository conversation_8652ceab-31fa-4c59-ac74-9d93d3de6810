<template>
  <div id="bloodMap"></div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import G6 from '@antv/g6'

  const props = defineProps({
    dataView: {
      type: Object,
      default: null,
    },
  })

  const data = ref({
    nodes: [],
    edges: [],
  })

  let graph = null

  watch(
    () => props.dataView,
    (newVal) => {
      if (newVal) {
        graph.destroy()
        if (newVal.vertices) {
          getAllMap()
        }
      }
    },
  )

  onMounted(() => {
    getAllMap()
  })
  G6.registerEdge(
    'can-running',
    {
      afterDraw(cfg, group) {
        // get the first shape in the group, it is the edge's path here=
        const shape = group.get('children')[0]
        // the start position of the edge's path
        const startPoint = shape.getPoint(0)

        // add red circle shape
        const circle = group.addShape('circle', {
          attrs: {
            x: startPoint.x,
            y: startPoint.y,
            fill: '#1890ff',
            r: 3,
          },
          // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
          name: 'circle-shape',
        })

        // animation for the red circle
        circle.animate(
          (ratio) => {
            // the operations in each frame. Ratio ranges from 0 to 1 indicating the prograss of the animation. Returns the modified configurations
            // get the position on the edge according to the ratio
            const tmpPoint = shape.getPoint(ratio)
            // returns the modified configurations here, x and y here
            return {
              x: tmpPoint.x,
              y: tmpPoint.y,
            }
          },
          {
            repeat: true, // Whether executes the animation repeatly
            duration: 3000, // the duration for executing once
          },
        )
      },
      setState(name, value, item) {
        const shape = item.get('keyShape')
        if (name === 'running') {
          if (value) {
            shape.attr('stroke', '#FFD194')
          } else {
            shape.attr('stroke', '#CCD0D8')
            // shape.stopAnimate()
            shape.attr('lineDash', null)
          }
        }
      },
    },
    'quadratic',
  )

  /**
   * format the string
   * @param {string} str The origin string
   * @param {number} maxWidth max width
   * @param {number} fontSize font size
   * @return {string} the processed result
   */
  const fittingString = (str, maxWidth, fontSize) => {
    const ellipsis = '...'
    const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
    let currentWidth = 0
    let res = str
    const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese charactors and letters
    str.split('').forEach((letter, i) => {
      if (currentWidth > maxWidth - ellipsisLength) return
      if (pattern.test(letter)) {
        // Chinese charactors
        currentWidth += fontSize
      } else {
        // get the width of single letter according to the fontSize
        currentWidth += G6.Util.getLetterWidth(letter, fontSize)
      }
      if (currentWidth > maxWidth - ellipsisLength) {
        res = `${str.substr(0, i)}${ellipsis}`
      }
    })
    return res
  }

  const tooltip = new G6.Tooltip({
    offsetX: 10,
    offsetY: 10,
    fixToNode: [1, 0.5],
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node', 'edge'],
    // 自定义 tooltip 内容
    getContent: (e) => {
      const outDiv = document.createElement('div')
      outDiv.style.width = 'fit-content'
      outDiv.style.height = 'fit-content'
      const model = e.item.getModel()

      if (e.item.getType() === 'node') {
        let liEl = ''
        Object.keys(model.data).forEach((ele, index) => {
          liEl += `<li>${Object.keys(model.data)[index]}：${Object.values(model.data)[index]}</li>`
        })

        outDiv.innerHTML = `
          <ul>
            <li>顶点类型: ${model.name}</li>
            <li>顶点ID: ${model.id}</li>
            ${liEl}
          </ul>`
      } else {
        const source = e.item.getSource()
        const target = e.item.getTarget()
        outDiv.innerHTML = `来源：${source.getModel().id}<br/>去向：${target.getModel().id}`
      }
      return outDiv
    },
  })

  // 获取所有血缘数据
  const getAllMap = async () => {
    const { vertices, edges } = props.dataView

    data.value = {
      nodes: [],
      edges: [],
    }
    vertices?.forEach((node) => {
      data.value.nodes.push({
        id: node.id.toString(),
        name: node.label,
        label: fittingString(node.id, 59, 12),
        data: node.properties,
      })
    })
    edges?.forEach((edge) => {
      data.value.edges.push({
        id: edge.id.toString(),
        source: edge.outV.toString(),
        target: edge.inV.toString(),
        label: edge.label,
        type: 'can-running',
      })
    })

    initData(data.value)
  }

  const highlightStyle = {
    // 节点高亮样式
    node: {
      highlight: {
        fill: '#FFD194',
        shadowColor: '#ED6807',
        stroke: '#ED6807',
        cursor: 'pointer',
      },
    },
    // 线高亮样式
    edge: {
      highlight: {
        stroke: '#FFD194',
        lineWidth: 1,
        labelCfg: {
          style: {
            fill: '#1D2129',
            autoRotate: true,
            fontSize: 12, // 设置边文字的大小
          },
        },
      },
    },
  }

  const initData = (data) => {
    const container = document.getElementById('bloodMap')
    const width = container.scrollWidth
    const height = container.scrollHeight || 500
    graph = new G6.Graph({
      container: 'bloodMap',
      width,
      height,
      layout: {
        type: 'force',
        preventOverlap: true,
        nodeSize: 120,
      },
      nodeStateStyles: highlightStyle.node,
      edgeStateStyles: highlightStyle.edge,
      plugins: [tooltip],
      modes: {
        default: [
          { type: 'drag-canvas', enableOptimize: true },
          {
            type: 'activate-relations',
            activeState: 'active',
            inactiveState: 'inactive',
          },
          {
            type: 'zoom-canvas',
            fixSelectedItems: {
              fixAll: true,
              fixState: 'yourStateName', // 'selected' by default
            },
          },
        ],
      },
      defaultEdge: {
        type: 'polyline',
        style: {
          stroke: '#CCD0D8',
          lineWidth: 1, // 设置边的宽度为 2 像素
          // endArrow: {
          // 	fill: '#CCD0D8',
          // 	path: G6.Arrow.triangle(5, 7),
          // },
        },
        labelCfg: {
          style: {
            fill: '#1D2129',
            autoRotate: true,
            fontSize: 12, // 设置边文字的大小
          },
        },
      },
      defaultNode: {
        type: 'circle',
        size: [59],
        style: {
          fill: '#99C9FF',
          stroke: '#1E89FF',
          lineWidth: 1,
        },
        labelCfg: {
          /* label's position, options: center, top, bottom, left, right */
          position: 'center',
          style: {
            fontSize: 12,
            fill: '#1D2129',
            fontWeight: 'normal',
          },
        },
      },
    })

    graph.data(data)
    graph.render()

    function refreshDragedNodePosition(e) {
      const model = e.item.get('model')
      model.fx = e.x
      model.fy = e.y
    }
    graph.on('node:dragstart', (e) => {
      graph.layout()
      refreshDragedNodePosition(e)
    })
    graph.on('node:drag', (e) => {
      refreshDragedNodePosition(e)
    })

    graph.on('node:mouseenter', (ev) => {
      const nodeCurrent = ev.item
      nodeCurrent._cfg.styles.highlight.fill = '#FF9E42'
      nodeCurrent._cfg.styles.highlight.lineWidth = 1

      graph.setItemState(nodeCurrent, 'highlight', true)

      // 遍历边，获取相关联的节点
      const relatedNodes = []
      const edges = nodeCurrent.getEdges()
      edges.forEach((edge) => {
        relatedNodes.push(edge.getSource())
        relatedNodes.push(edge.getTarget())

        graph.setItemState(edge, 'running', true)
        edge._cfg.model.labelCfg.style.fill = '#FFD194'
        edge.refresh()
      })

      relatedNodes.forEach((node) => {
        node._cfg.styles.highlight.fill = '#FFD194'
        node._cfg.styles.highlight.lineWidth = 1
        graph.setItemState(node, 'highlight', true)
      })
    })

    graph.on('node:mouseleave', () => {
      graph.setAutoPaint(false)
      graph.getNodes().forEach((node) => {
        graph.clearItemStates(node)
      })
      graph.getEdges().forEach((edge) => {
        edge._cfg.model.labelCfg.style.fill = '#1D2129'
        edge.refresh()
        graph.clearItemStates(edge)
      })
      graph.paint()
      graph.setAutoPaint(true)
    })
  }
</script>

<style scoped>
  #bloodMap {
    width: 100%;
    height: 100%;
  }
</style>
