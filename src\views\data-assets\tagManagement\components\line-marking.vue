<template>
  <!-- 数据打标-行打标 -->
  <div class="link-meta-data">
    <div class="link-meta-data-content">
      <div
        v-if="state.isNeedSelection"
        :class="{ 'filter-type-box': true, all: state.batchMarkData.filterType === 'allData' }"
      >
        <n-form ref="form" :data="state.batchMarkData" :rules="state.formRules" label-width="92px">
          <div class="nancalui-form-item-top">
            <div class="top-left">
              <n-form-item label="数据搜索方式：" field="filterType">
                <n-select
                  v-model="state.batchMarkData.filterType"
                  placeholder="请选择搜索方式"
                  @value-change="filterTypeChange"
                >
                  <n-option
                    v-for="item in state.batchMarkData.allFilterOptions"
                    :key="item.value"
                    :name="item.label"
                    :value="item.value"
                  />
                </n-select>
              </n-form-item>
              <n-form-item label="" field="tagged">
                <n-select
                  v-model="state.batchMarkData.tagged"
                  placeholder="请选择打标状态"
                  allow-clear
                  @value-change="taggedStatusChange"
                >
                  <n-option
                    v-for="item in state.batchMarkData.taggedOptions"
                    :key="item.value"
                    :name="item.label"
                    :value="item.value"
                  />
                </n-select>
              </n-form-item>
              <n-button
                color="primary"
                v-show="state.batchMarkData.filterType === 'allData'"
                @click.prevent="startFilterCondition"
                >筛选</n-button
              >
              <n-button
                v-show="state.batchMarkData.filterType === 'allData'"
                :disabled="state.disableBatchMark"
                @click.prevent="filterDataMarking"
                >筛选数据打标</n-button
              >
            </div>
            <div class="top-right">
              <n-button
                color="primary"
                v-show="state.batchMarkData.filterType !== 'allData'"
                @click.prevent="startFilterCondition"
                >筛选</n-button
              >
              <n-button
                v-show="state.batchMarkData.filterType !== 'allData'"
                :disabled="state.disableBatchMark"
                @click.prevent="filterDataMarking"
                >筛选数据打标</n-button
              >
              <n-button
                v-show="state.batchMarkData.filterType !== 'allData'"
                @click.prevent="addFilterCondition"
                >添加条件</n-button
              >
              <n-button
                v-show="state.batchMarkData.filterType !== 'allData'"
                @click.prevent="resetFilterCondition"
                >重置</n-button
              >
              <n-button
                v-show="state.batchMarkData.filterType === 'allData'"
                color="primary"
                variant="solid"
                :disabled="!state.activeAssetsList.length"
                @click.prevent="save"
                >打标</n-button
              >
            </div>
          </div>
          <div
            v-show="state.batchMarkData.filterType !== 'allData'"
            class="nancalui-form-item-bottom"
          >
            <div
              class="filter-box"
              v-for="(item, index) in state.batchMarkData.filterCondition"
              :key="item"
            >
              <div class="filter-item">
                <n-form-item
                  label=""
                  class="meta-select"
                  :field="'filterCondition.' + index + '.colName'"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                  }"
                >
                  <n-select
                    v-model="item.colName"
                    placeholder="请选择"
                    filterable
                    clearable
                    @value-change="paramColumnsChange(item)"
                  >
                    <n-option
                      v-for="option in state.allParamColumns"
                      :key="option.prop"
                      :name="option.prop"
                      :value="option.prop"
                    />
                  </n-select>
                </n-form-item>
                <n-form-item
                  class="filter-select"
                  label=""
                  :field="'filterCondition.' + index + '.operator'"
                  :rules="{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                  }"
                >
                  <n-select v-model="item.operator" placeholder="请选择">
                    <n-option
                      v-for="option in state.filterOperator"
                      :key="option.value"
                      :name="option.label"
                      :value="option.value"
                    />
                  </n-select>
                </n-form-item>
                <n-form-item
                  class="value-input"
                  label=""
                  :field="'filterCondition.' + index + '.value'"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: 'blur',
                  }"
                >
                  <n-input v-model="item.value" size="small" placeholder="请输入" />
                </n-form-item>
              </div>

              <div class="handle-box" @click="removeFilterCondition(index)">
                <SvgIcon icon="filter-cut" class="reduce" title="删除"
              /></div>
            </div>
          </div>
        </n-form>
      </div>
      <div class="add-tag-box" v-show="state.batchMarkData.filterType !== 'allData'">
        <n-button
          color="primary"
          variant="solid"
          :disabled="!state.activeAssetsList.length"
          @click.prevent="save"
          >打标</n-button
        >
      </div>
      <div class="public-table-box" v-loading="state.loading">
        <n-public-table
          :key="state.key"
          rowKey="assetsIndex"
          :isDisplayAction="false"
          :showPagination="true"
          :isNeedSelection="state.isNeedSelection"
          :table-head-titles="state.tableHeadTitles"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          @tablePageChange="tablePageChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #tagInfos="{ editor }">
            <div class="tag-infos-box">
              <div
                class="tag-item-box"
                v-for="item in editor.row.tagInfos"
                :key="item.id"
                :title="item.name"
              >
                <div class="tag-item"
                  >{{ item.name }}
                  <SvgIcon
                    v-if="state.isNeedSelection"
                    icon="mark-del"
                    class="reduce"
                    title="删除"
                    @click="delMarking(editor.row, item.id)"
                  />
                </div>
              </div>

              <div class="more-box">
                <SvgIcon
                  v-if="editor.row.tagInfos?.length > 3"
                  class="more-icon"
                  icon="more-icon"
                  title="更多"
                  @click="seeMoreTags(editor)"
                />
              </div>
            </div>
          </template>
        </n-public-table>
      </div>
    </div>

    <!-- 查看具体标签弹框 -->

    <n-modal
      title="添加标签"
      bodyClass="allTagsDialog commonDialog"
      v-model="state.tagsDialogVisible"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="false"
      width="780px"
      :before-close="closeTagsDialog"
    >
      <div class="tags-dialog-content">
        <div
          class="tag-item"
          v-for="item in state.checkRowTags"
          :key="item.id"
          :title="item.name"
          >{{ item.name }}</div
        >
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button color="primary" size="sm" variant="solid" @click.prevent="closeTagsDialog"
            >关 闭</n-button
          ></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { reactive, ref, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  export default {
    name: '',
    components: {},
    props: {},

    setup(props, { emit }) {
      const form = ref()
      const router = useRouter()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        tableData: {},
        tableHeight: 400,

        disableBatchMark: true, //禁用筛选数据打标按钮
        treeData: [],
        treeAttrData: {
          showCheckbox: false,
          showControl: true,
          showLeftIcon: true,
          parentControl: '',
          childControl: '',
        },
        allTags: [],
        activeTags: [],
        dialogVisible: false,
        tagsDialogVisible: false,
        checkRowTags: [], //查看元数据的所有标签
        submiting: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'number2', name: '字段一' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        modelId: '',
        batchMarkData: {
          filterType: 'allData',
          allFilterOptions: [
            {
              label: '全部数据',
              value: 'allData',
            },
            {
              label: '全部条件搜索',
              value: 'ALL',
            },
            {
              label: '任意条件搜索',
              value: 'OR',
            },
          ],
          filterCondition: [
            // {
            //   colName: '',
            //   operator: '',
            //   value: '',
            //   colType: '',
            // },
          ],
          tagged: null,
          taggedOptions: [
            {
              label: '已打标',
              value: 'true',
            },
            {
              label: '未打标',
              value: 'false',
            },
          ],
        },
        allParamColumns: [], //数据过滤元数据下拉数据
        filterOperator: [],
        formRules: {
          filterType: [{ required: true, message: '请选择搜索方式', trigger: 'change' }],
        },

        key: 1,
        modelName: '',
        groupId: null, //标签组分页
        activeAssetsList: [], //批量选中的标签列表
        projectCode: '',
        isNeedSelection: true, //是否需要勾选框 查看不需要
        showTagsIds: [], //当前标签分类下展示的标签合计
      })
      const methods = {
        setTableHeight(height = 400) {
          state.tableHeight = document.body.offsetHeight - height
          state.key++
        },
        //筛选数据打标
        filterDataMarking() {
          emit('showTagDialog', {
            filterDataMarkingData: {
              filterCondition: state.batchMarkData.filterCondition,
              filterMode:
                state.batchMarkData.filterType === 'allData'
                  ? null
                  : state.batchMarkData.filterType,
              modelName: state.modelName,
              modelId: state.modelId,
              tagged: state.batchMarkData.tagged || null,
              projectCode: state.projectCode,
            },
          })
        },
        //单个删除标签
        delMarking(row, id) {
          proxy.$MessageBoxService.open({
            title: '是否确认删除该标签',
            content: '删除后该标签将不可使用',
            save: () => {
              let _tagInfos = row.tagInfos.filter((item) => {
                return item.id !== id
              })
              row.newTagInfos = _tagInfos.map((list) => {
                return list.id
              })

              emit('delSingerMarking', [row])
            },
          })
        },
        //资产打标
        save() {
          emit('showTagDialog', { activeAssetsList: state.activeAssetsList })
        },

        //查看更多标签
        seeMoreTags(editor) {
          state.checkRowTags = editor.row.tagInfos
          state.tagsDialogVisible = true
        },
        // 关闭查看标签弹框
        closeTagsDialog() {
          state.tagsDialogVisible = false
        },

        //新增筛选数据条件
        addFilterCondition() {
          state.batchMarkData.filterCondition.push({
            colName: '',
            operator: '',
            value: '',
            colType: '',
          })
          state.disableBatchMark = true //禁用筛选数据打标按钮
          methods.tableHeightChange()
        },
        //重置筛选数据条件
        resetFilterCondition() {
          state.disableBatchMark = true //禁用筛选数据打标按钮
          state.batchMarkData.filterCondition = []
          methods.tableHeightChange()
          // methods.filterConditionValidate()
        },
        //条件筛选
        startFilterCondition() {
          methods.filterConditionValidate()
        },
        //过滤条件验证
        filterConditionValidate() {
          form.value.validate((valid) => {
            if (valid) {
              methods.initTable()
            } else {
              return false
            }
          })
        },

        //过滤方式change
        filterTypeChange() {
          // state.key += '1'
          state.disableBatchMark = true //禁用筛选数据打标按钮
          state.batchMarkData.filterCondition = []
          methods.tableHeightChange()
          // methods.filterConditionValidate()
        },
        //打标状态change
        taggedStatusChange() {
          state.disableBatchMark = true //禁用筛选数据打标按钮
        },
        //表格高度变化
        tableHeightChange() {
          if (state.batchMarkData.filterType === 'allData') {
            methods.setTableHeight(400)
          } else {
            let nowLength =
              state.batchMarkData.filterCondition.length - 1 < 0
                ? 0
                : state.batchMarkData.filterCondition.length
            nowLength = nowLength > 8 ? 8 : nowLength
            let _tableHeight = 400 + 116 + Math.ceil(nowLength / 3) * 42
            methods.setTableHeight(_tableHeight)
          }
          state.key++
        },

        //批量打标下拉 元数据下拉框change
        paramColumnsChange(data) {
          let _item = state.allParamColumns.filter((item) => item.name === data.colName)
          if (_item && _item.length) {
            data.colType = _item[0].colType
          } else {
            data.colType = 'VARCHAR'
          }
        },

        // 获取批量打标过滤的元数据列表
        getMetadataList() {
          api.model
            .getMetadataList({
              id: state.modelId,
            })
            .then((res) => {
              // 新增序号属性
              let _allParamColumns = []
              if (res.data && res.data.length) {
                res.data.forEach((item) => {
                  _allParamColumns.push({
                    prop: item.name,
                    name: item.name,
                    colType: item.fieldType,
                  })
                })
                state.allParamColumns = _allParamColumns
              }
            })
        },

        //获取过滤条件
        getFilterOperator() {
          api.assets.getFilterOperator().then((res) => {
            let { success, data } = res
            if (success) {
              state.filterOperator = data
            }
          })
        },
        //移除筛选数据条件
        removeFilterCondition(index) {
          state.batchMarkData.filterCondition.splice(index, 1)
        },

        // 初始化模型资产列表
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage

          let data = {
            condition: {
              filterCondition: state.batchMarkData.filterCondition,
              filterMode:
                state.batchMarkData.filterType === 'allData'
                  ? null
                  : state.batchMarkData.filterType,
              modelName: state.modelName,
              modelId: state.modelId,
              projectCode: state.projectCode,
              tagged: state.batchMarkData.tagged || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          // if (filter) {
          //   data.condition.filterCondition = []
          // }
          state.loading = true
          state.activeAssetsList = [] //清空勾选项
          api.assets
            .getTaggingHistoryList(data)
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                state.tableHeadTitles = [{ prop: 'number', name: '序号', width: 80 }]
                if (data && data.list[0]) {
                  let allData = { list: [] }
                  let allTitleProps = []

                  data.list.map((item) => {
                    return Object.assign(item, item.assetsContent)
                  })

                  let noShowParames = ['assetsContent', 'tagInfos', 'assetsIndex', 'ds']
                  // Object.keys(data.list[0]).forEach((key) => {
                  //   if (noShowParames.includes(key)) return
                  //   state.tableHeadTitles.push({
                  //     prop: key,
                  //     name: key,
                  //   })
                  // })
                  //该id完整元数据

                  state.allParamColumns.forEach((item) => {
                    if (noShowParames.includes(item.prop)) return
                    state.tableHeadTitles.push({
                      prop: item.prop,
                      name: item.prop,
                    })
                    allTitleProps.push(item.prop)
                  })
                  state.tableHeadTitles.push({
                    prop: 'tagInfos',
                    name: '标签',
                    slot: 'tagInfos',
                    width: 288,
                  })
                  allTitleProps.push('tagInfos')
                  allTitleProps.push('assetsContent')
                  allTitleProps.push('assetsIndex')

                  //处理大小写问题
                  data.list.forEach((item, index) => {
                    let _itemKeys = []
                    Object.keys(item).forEach((_item) => {
                      _itemKeys.push(_item.toLowerCase())
                    })
                    let _object = {}

                    allTitleProps.forEach((key) => {
                      if (_itemKeys.includes(key.toLowerCase())) {
                        if (
                          key === 'tagInfos' ||
                          key === 'assetsContent' ||
                          key === 'assetsIndex'
                        ) {
                          _object[key] = item[key]
                        } else {
                          _object[key] = item[key.toLowerCase()]
                        }
                      } else {
                        _object[key] = null
                      }
                    })
                    _object['number'] = index + 1
                    allData.list.push(_object)
                  })

                  // data.list.map((item, index) => {
                  //   return Object.assign(item, {
                  //     number: index + 1,
                  //   })
                  // })
                  // state.allParamColumns = JSON.parse(JSON.stringify(state.tableHeadTitles))
                  // state.allParamColumns.splice(0, 1)
                  data.list = allData.list

                  state.tableData = data
                  state.key++
                } else {
                  state.tableHeadTitles = [
                    { prop: 'number', name: '序号', width: 80 },
                    { prop: 'number2', name: '字段一' },
                  ]
                  state.tableData = {}
                  state.key++
                }

                if (data.list?.length) {
                  state.disableBatchMark = false
                }
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        //获取勾选数据
        handleSelectionChange(val) {
          state.activeAssetsList = val
        },

        //搜索
        onSearch() {
          methods.initTable()
        },
        init(data) {
          if (data === 'taggedInit') {
            state.batchMarkData.tagged = null
          }
          methods.initTable()
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        state.modelId = Number(router.currentRoute.value.query.modelId) // 获取路由传参
        state.modelName = router.currentRoute.value.query.modelName // 获取路由传参
        state.projectCode = router.currentRoute.value.query.projectCode // 获取路由传参
        state.isNeedSelection = router.currentRoute.value.query.type ? false : true
        if (router.currentRoute.value.query.type) {
          //查看
          methods.setTableHeight(345)
        }
        methods.getMetadataList()
        methods.getFilterOperator()
        methods.initTable()
      })

      return {
        state,
        form,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .link-meta-data {
    margin-top: 20px;
    &-content {
      background-color: #fff;
      border-radius: 5px;
      // padding: 0 20px;
      height: calc(100% - 60px);
      overflow: hidden;
    }
    .options-box-bg {
      // position: absolute;
      // bottom: 10px;
      // left: 0;
      // right: 0;
      // height: 66px;
      height: 66px;
      margin-top: 10px;
      margin-left: -10px;
      margin-right: -10px;

      .content {
        width: 100%;
        height: 100%;
        padding: 0 20px;
        text-align: right;
        background-color: #fff;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
    .top-title {
      .need_smallcube__title {
        margin: 0;
      }
    }

    .filter-type-box {
      display: flex;
      margin-bottom: 20px;
      max-height: 206px;
      background: #f7f8fa;
      border-radius: 8px;
      border: 1px solid #ebedf0;
      padding: 20px 0px 4px 20px;
      &.all {
        padding: 0;
        background: #fff;
        border: none;
        justify-content: space-between;
        margin-bottom: 0;
      }
      :deep(.nancalui-form) {
        display: flex;
        flex-direction: column;
        width: 100%;
        // flex: 1;

        .nancalui-form-item-top {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding-right: 20px;
          .top-left {
            display: flex;
            & > div {
              margin-right: 10px;
            }
          }
          .button-box {
            padding-left: 105px;
          }
          .nancalui-button--outline {
            // border: 1px solid $themeBlue;
            // color: $themeBlue;
            &.dif {
              color: #333333;
              border: 1px solid #cfcfcf;
            }
            &:hover {
              color: $themeBlue;
              background: #f0f7ff;
            }
          }
        }
        .nancalui-form-item-bottom {
          padding-right: 4px;
          flex: 1;
          overflow-y: auto;

          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: flex-start;
          height: 100%;
          overflow: scroll;
          .filter-box {
            width: 33.33%;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
          }

          .handle-box {
            width: 26px;
            height: 32px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 0 10px;
            &:hover {
              svg {
                color: $themeBlue;
              }
            }
          }
          .filter-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: calc(100% - 26px - 20px);
            .nancalui-form__item--horizontal {
              margin-bottom: 0 !important;

              &.meta-select {
                // width: 120px;
                width: 120px;
                width: 32%;
              }
              &.filter-select {
                // width: 140px;
                width: 38%;
              }
              &.value-input {
                // width: 100px;

                width: 26%;
              }
            }
          }
        }
      }
      .content-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .model-mark {
        padding: 12px 0 0 10px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 380;
        color: #999999;
        line-height: 20px;
        // border-top: 1px solid $border;
        max-height: 86px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    :deep(.middleDialog) {
      .dialog-content-box {
        display: flex;
        height: calc(70vh - 120px);
        justify-content: center;
        .tree {
          border-radius: 4px 0px 0px 4px;
          border: 1px solid #ebeef5;
          border-right: 0;
        }
        .tree-search {
          padding: 10px 10px 0;
        }
      }
    }
    :deep(.allTagsDialog) {
      .tags-dialog-content {
        display: flex;
        flex-wrap: wrap;
        max-height: 50vh;
        overflow-y: scroll;
        .tag-item {
          // width: 68px;
          height: 28px;
          line-height: 28px;
          background: #f2f3f6;
          font-size: 12px;
          color: #333333;
          border-radius: 14px;
          // white-space: nowrap;
          // text-overflow: ellipsis;
          // overflow: hidden;
          padding: 0 10px;
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: default;
          text-align: center;
        }
      }
    }
    .add-tag-box {
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
    .public-table-box {
    }

    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: flex-start;
    }

    .common-table {
      :deep(.nancalui-table) {
        .nancalui-table__cell {
          position: relative;
        }
        .tag-infos-box {
          display: flex;
          align-items: center;
          .tag-item-box {
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
            &:hover {
              .yy-icon {
                display: block;
                cursor: pointer;
              }
            }
            &:nth-of-type(3) {
              margin-right: 50px;
            }
            .yy-icon {
              width: 18px;
              height: 18px;
              position: absolute;
              top: 0px;
              right: 5px;
              display: none;
            }
          }
          .tag-item {
            flex-shrink: 0; //不压缩
            width: 68px;
            height: 28px;
            background: #f2f3f6;
            border-radius: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: 28px;
            text-align: center;
            margin-right: 10px;
            padding: 0 10px;
            cursor: default;
          }
          .more-box {
            position: absolute;
            right: -10px;
            top: 0;
            bottom: 0;
            padding: 0 10px;
            display: flex;
            align-items: center;
            // background-color: #fff;
          }
          .more-icon {
            color: #333;

            width: 22px;
            height: 18px;
            cursor: pointer;
            &:hover {
              color: #4d4e4eff;
              background-color: #eff1f5ff;
            }
          }
        }
      }
    }

    .footerBtn {
      position: fixed;
      right: 20px;
      bottom: 30px;
      z-index: 9;
    }
  }
</style>
