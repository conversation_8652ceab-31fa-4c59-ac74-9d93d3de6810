import request from '@/utils/request'
import URL from '@/const/urlAddress'

// ------------实时作业 开始-----------------
function getWorkData() {
  return request({
    url: `${URL.OPERATION}/operation/overview/statistics`,
    method: 'get',
  })
}
// 实时数据作业列表
function newRealWorkList(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/page`,
    method: 'post',
    data: data,
  })
}

// 作业开发
function addRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/save`,
    method: 'post',
    data: data,
  })
}

// 新增实时作业
function addRealWorkRun(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/saveAndRun`,
    method: 'post',
    data: data,
  })
}

// 获取实时作业画布
function newRealDataWorkDetail(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/dag/${data.id}`,
    method: 'get',
  })
}

// 实时数据作业画布保存
function newRealSaveDataWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/dag/save`,
    method: 'post',
    data: data,
  })
}

// 实时数据作业画布运行
function runRealTask(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/execute/test/${data.id}`,
    method: 'get',
  })
}

// 实时数据作业画布终止运行
function stopRealTask(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/execute/stop/${data.id}`,
    method: 'get',
  })
}

// 实时数据作业画布运行日志
function runRealTaskLog(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/log/${data.id}`,
    method: 'get',
  })
}

// 运行日志
function getExcLog(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/log/${data.id}`,
    method: 'get',
  })
}

// 实时作业下架
function newOffRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/offline/${data.jobId}`,
    method: 'get',
  })
}

// 实时作业撤回审批
function newCancelRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/cancel/apply/${data.jobId}`,
    method: 'get',
  })
}

// 实时作业上架
function newOnRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/online/${data.jobId}`,
    method: 'get',
    isNoPop: true, // 报错也不弹窗
  })
}

// 实时作业删除
function newDeleteRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/delete/${data.jobId}`,
    method: 'get',
  })
}

// 实时作业修改
function newEditRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/update`,
    method: 'post',
    data: data,
  })
}

// 实时作业详情
function getDetailRealWork(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/${data.jobId}`,
    method: 'get',
  })
}

// 实时作业计算引擎
function getEnginesList() {
  return request({
    url: `${URL.REALTIME}/stream/job/enums/engines`,
    method: 'get',
  })
}

// 作业计算引擎枚举
function getFieldTypeList() {
  return request({
    url: `${URL.REALTIME}/stream/job/enums/fieldTypes`,
    method: 'get',
  })
}

// 实时算子上传文件包
function realWorkUpload(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/upload`,
    method: 'post',
    data: data,
  })
}

// 实时作业获取结构数据源
function getDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/type/realtime`,
    method: 'get',
    params: data,
  })
}
// 实时作业Flink SQL 解析
function flinkSQLAnalysis(data) {
  return request({
    url: `${URL.REALTIME}/stream/job/flink/sql-parser`,
    method: 'post',
    data: data,
  })
}
// 实时作业一键复制
function newCopyRealTimeWork(data) {
  return request({
    url: `${URL.OPERATION}/stream/job/copy`,
    method: 'post',
    data,
  })
}

// ------------实时作业 结束-----------------

// 新增数据作业
function addDataWork(data) {
  return request({
    url: `${URL.DEV}/taskflow/add`,
    method: 'post',
    data: data,
  })
}

// 作业名称校检
function validDataWorkName(data) {
  return request({
    url: `${URL.DEV}/operation/isJobNameExist`,
    method: 'post',
    data: data,
  })
}
// 实时作业名称校检
function validRealDataWorkName(data) {
  return request({
    url: `${URL.DEV}/stream/job/checkJobName`,
    method: 'post',
    data: data,
  })
}

// -----------------------------------算法中心 开始-----------------------------------
// 算法中心列表
function listAlgorithm(data) {
  return request({
    url: `${URL.OPERATION}/algorithm/center/search`,
    method: 'post',
    data: data,
  })
}

// 编辑/新增
function addAlgorithm(data) {
  return request({
    url: `${URL.OPERATION}/algorithm/center/save`,
    method: 'post',
    data: data,
  })
}

// 删除算法
function deleteAlgorithm(data) {
  return request({
    url: `${URL.OPERATION}/algorithm/center/delete/${data.id}`,
    method: 'post',
  })
}

// ------------------------------------算法中心 结束-----------------------------------

// ------------------------------------离线作业 开始-----------------------------------
// 新数据作业列表
function newDataWorkList(data) {
  return request({
    url: `${URL.OPERATION}/operation/search`,
    method: 'post',
    data: data,
  })
}

// 数据作业创建
function newAddDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/create`,
    method: 'post',
    data: data,
  })
}

// 数据作业修改
function newEditDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/update`,
    method: 'post',
    data: data,
  })
}

// 数据作业删除
function newDeleteDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/delete`,
    method: 'get',
    params: data,
  })
}

// 数据作业下架
function newOffDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/audit/offline`,
    method: 'get',
    params: data,
  })
}

// 数据作业下架撤回
function newOffDataWorkRollback(data) {
  return request({
    url: `${URL.OPERATION}/operation/audit/offline/rollback`,
    method: 'get',
    params: data,
  })
}

// 数据作业上架
function newOnDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/audit/online`,
    method: 'get',
    params: data,
    isNoPop: true, // 报错也不弹窗
  })
}

// 数据作业上架撤回
function newOnDataWorkRollback(data) {
  return request({
    url: `${URL.OPERATION}/operation/audit/online/rollback`,
    method: 'get',
    params: data,
  })
}

// 数据作业立即启动
function newExecuteDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/execute`,
    method: 'get',
    params: data,
  })
}

// 数据作业一键复制
function newCopyDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/copy`,
    method: 'get',
    params: data,
  })
}

// 数据作业画布测试运行
function runTaskDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/task/run`,
    method: 'post',
    data: data,
  })
}

// 数据作业画布测试运行日志和结果
function runTaskLogDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/task/run/log/${data.type}/${data.isSelect}/${data.processCode}`,
    method: 'post',
    data: data,
  })
}

// 数据作业画布运行
function runTask(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/test/run`,
    method: 'post',
    data: data,
  })
}

// 数据作业画布运行日志和结果
function runTaskResultAndLog(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/run/log`,
    method: 'get',
    params: data,
  })
}

// 数据作业画布终止运行
function stopRunTask(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/test/stop`,
    method: 'get',
    params: data,
  })
}

// 离线作业详情
function getDetailOffline(data) {
  return request({
    url: `${URL.OPERATION}/operation/detail`,
    method: 'get',
    params: data,
  })
}

// 数据处理SQL节点校验
function newSaveSqlWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/sql`,
    method: 'post',
    data: data,
  })
}

// hiveSql解析出写入的字段列表
function parseSqlWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/sql/parse`,
    method: 'post',
    data: data,
  })
}

// 数据作业画布保存前检测
function newCheckDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/config/check`,
    method: 'post',
    data: data,
  })
}
// 数据作业画布保存
function newSaveDataWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/save`,
    method: 'post',
    data: data,
  })
}

// 离线数据作业画布详情
function newDataWorkDetail(data) {
  return request({
    url: `${URL.OPERATION}/operation/dag/get`,
    method: 'get',
    params: data,
  })
}
// 离线数据作业详情
function getDetailOffWork(data) {
  return request({
    url: `${URL.OPERATION}/operation/detail`,
    method: 'get',
    params: data,
  })
}

// 固化炉设备编号列表
function getOvenList() {
  return request({
    url: `${URL.OPERATION}/operation/curingOven/ovenIdList`,
    method: 'get',
  })
}

// 固化炉工艺列表
function getCraftList(data) {
  return request({
    url: `${URL.OPERATION}/operation/curingOven/ncNameList`,
    method: 'get',
    params: data,
  })
}
// 作业算法详情
function algoFieldList(data) {
  return request({
    url: `${URL.OPERATION}/algo/studio/detail`,
    method: 'get',
    params: data,
  })
}
// python测试
function sendJson(data) {
  return request({
    url: `${URL.PYTHON}/sendjson`,
    method: 'post',
    data: data,
  })
}

// ------------------------------------离线作业 结束-----------------------------------

// 数据查询树列表新
function getNewSearchTree(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/list`,
    method: 'post',
    data: data,
  })
}
// 数据查询组 脚本，目录验重
function checkScriptName(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/checkScriptName`,
    method: 'post',
    data,
  })
}

// 数据查询组-添加/修改
function newAddSearchTree(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/group/save`,
    method: 'post',
    data: data,
  })
}

// 数据查询组-添加/修改脚本
function newAddScriptTree(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/save`,
    method: 'post',
    data: data,
  })
}

// 数据查询组-删除查询组
function newDelSearchTree(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/group/delete/${data.id}`,
    method: 'post',
    data: {},
  })
}

// 数据查询组-删除脚本
function newDelScriptTree(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/delete/${data.id}`,
    method: 'post',
    data: {},
  })
}

// 数据查询组-脚本试运行
function newScriptRun(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/run`,
    method: 'post',
    data: data,
  })
}

// 数据查询组-脚本运行
function newCompletelyScriptRun(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/run-completely`,
    method: 'post',
    data: data,
  })
}

// 数据查询组-脚本运行终止
function newScriptStop(data = {}) {
  return request({
    url: `${URL.OPERATION}/data/search/script/run/stop/${data.processCode}`,
    method: 'post',
    data: {},
  })
}

// 数据查询组-脚本日志
function newScriptLog(data) {
  return request({
    url: `${URL.OPERATION}/data/search/script/run/log`,
    method: 'post',
    data: data,
  })
}

// 数据作业详情
function dataWorkDetail(data) {
  return request({
    url: `${URL.DEV}/taskflow/selectflow`,
    method: 'get',
    params: data,
  })
}

// 数据查询树导出
function exportSearchTree(data = {}) {
  return request({
    url: `${URL.DEV}/datasearch/script/export`,
    method: 'get',
    params: data,
    responseType: 'blob',
  })
}

// 根据id发布Api
function publish(data) {
  return request({
    url: `${URL.DEV}/taskflow/taskflow/publish/${data.id}?version=${data.version}`,
    method: 'post',
    data: data,
  })
}

// 数据脚本-测试运行
function execSql(data) {
  return request({
    url: `${URL.DEV}/taskscript/execSql`,
    method: 'post',
    data: data,
  })
}

// 数据查询运行日志 execLog
function execLog(data) {
  return request({
    url: `${URL.DEV}/taskscript/queryLog`,
    method: 'post',
    data: data,
  })
}

// 场景目录列表
function sceneTree(data) {
  return request({
    url: `${URL.DEV}/scenegroup/trees`,
    method: 'get',
    params: data,
  })
}

// 场景目录新增
function sceneTreeAdd(data) {
  return request({
    url: `${URL.DEV}/scenegroup/insert`,
    method: 'post',
    data: data,
  })
}

// 场景目录编辑
function sceneTreeEdit(data) {
  return request({
    url: `${URL.DEV}/scenegroup/update`,
    method: 'post',
    data: data,
  })
}

// 场景目录删除检测
function sceneTreeDeleteCheck(data) {
  return request({
    url: `${URL.DEV}/scenegroup/checkdel/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 场景目录删除
function sceneTreeDelete(data) {
  return request({
    url: `${URL.DEV}/scenegroup/${data.id}`,
    method: 'delete',
    data: data,
  })
}

// 数据作业列表
function dataWorkList(data) {
  return request({
    url: `${URL.DEV}/dataoperation/list`,
    method: 'get',
    params: data,
  })
}

// 根据场景id查看授权的列表
function getAuthTable(data) {
  return request({
    url: `${URL.DEV}/scene/getAuthTable`,
    method: 'get',
    params: data,
  })
}

// 根据TableId查看授权表的详情
function getAuthTableDetail(data) {
  return request({
    url: `${URL.DEV}/scene/getAuthTableDetail`,
    method: 'get',
    params: data,
  })
}

// 数据作业输入输出血缘表
function datalineageInputOutput(data) {
  return request({
    url: `${URL.DEV}/datalineage/source/list`,
    method: 'get',
    params: data,
  })
}

// 数据作业输入输出血缘表
function hiveCopy(data) {
  return request({
    url: `${URL.DEV}/dataoperation/hive/table/copy`,
    method: 'post',
    data: data,
  })
}

// -------------------------------- 数据开发-场景开发接口 结束  ------------------------------------------------

// -------------------------------- 数据开发-运维监控接口 开始  ------------------------------------------------

// 运维监控任务实例分页查询
function monitorTaskDetailQuery(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/instance/page`,
    method: 'post',
    data: data,
  })
}

// 实时作业日志
function monitorTaskDetailRealLog(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/realtime/log/${data.id}`,
    method: 'get',
  })
}

// 离线作业日志
function monitorTaskDetailLog(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/instance/${data.id}/log`,
    method: 'get',
  })
}

// 重跑
function monitorRepeat(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/instance/${data.id}/repeat/running`,
    method: 'get',
  })
}

// 获取运行实例的节点运行情况
function monitorTaskInstance(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/instance/view/${data.id}`,
    method: 'get',
  })
}

// 运维监控任务分页查询
function monitorTaskQuery(data) {
  return request({
    url: `${URL.OPERATION}/monitor/task/page`,
    method: 'post',
    data: data,
  })
}

// -------------------------------- 数据开发-运维监控接口 结束  ------------------------------------------------
// ------------------------------------算法中心 开始-----------------------------------
// 算法中心-标签列表
function algorithmTagList() {
  return request({
    url: `${URL.OPERATION}/algo/tag/list`,
    method: 'get',
  })
}
// 算法中心-算法调试
function algorithmDebugTest(data) {
  return request({
    url: `${URL.OPERATION}/algo/debug`,
    method: 'post',
    data: data,
  })
}
// 算法中心-算法调试同步
function algorithmDebugTestSync(data, cancelToken) {
  return request({
    url: `${URL.OPERATION}/algo/debug/sync`,
    method: 'post',
    cancelToken,
    data: data,
  })
}
// 算法中心-获取示例代码
function algorithmExampleCode() {
  return request({
    url: `${URL.OPERATION}/algo/code/example`,
    method: 'get',
  })
}
// 算法中心-获取算法调试日志
function algorithmDebugLog(data) {
  return request({
    url: `${URL.OPERATION}/algo/debug/log`,
    method: 'post',
    data: data,
  })
}
// 算法中心-算法调试中止
function algorithmDebugStop(data) {
  return request({
    url: `${URL.OPERATION}/algo/debug/stop`,
    method: 'post',
    data: data,
  })
}
// 算法中心-算法新增
function algorithmCreate(data) {
  return request({
    url: `${URL.OPERATION}/algo/create`,
    method: 'post',
    data: data,
  })
}
// 算法中心-算法列表
function algorithmSearch(data) {
  return request({
    url: `${URL.OPERATION}/algo/search`,
    method: 'post',
    data: data,
  })
}
// 算法中心-作业用的算法列表
function algorithmSearchForWork(data) {
  return request({
    url: `${URL.OPERATION}/algo/studio/search`,
    method: 'post',
    data: data,
  })
}
// 算法中心-下载代码模板
function algorithmDownload() {
  return request({
    url: `${URL.OPERATION}/algo/code/template/download`,
    responseType: 'blob',
    method: 'get',
  })
}
// 算法中心-文件上传解析
function algorithmParsePython(data) {
  return request({
    url: `${URL.OPERATION}/algo/code/parse/python`,
    method: 'post',
    data: data,
  })
}
// 算法中心-算法详情查看
function algorithmDetail(data) {
  return request({
    url: `${URL.OPERATION}/algo/detail`,
    method: 'get',
    params: data,
  })
}
// 算法中心-算法删除
function algorithmDelete(data) {
  return request({
    url: `${URL.OPERATION}/algo/delete`,
    method: 'post',
    data: data,
  })
}
// 算法中心-发布到公共
function algorithmPublicPublish(data) {
  return request({
    url: `${URL.OPERATION}/algo/public/publish`,
    method: 'post',
    data: data,
  })
}
// 算法中心-获取最后一次调试结果
function algorithmDebugLogLast(data) {
  return request({
    url: `${URL.OPERATION}/algo/debug/log/last`,
    method: 'get',
    params: data,
  })
}
// 算法中心-算法修改
function algorithmUpdate(data) {
  return request({
    url: `${URL.OPERATION}/algo/update`,
    method: 'post',
    data: data,
  })
}

// 算法中心-快照上传
// function algorithmImageUpload(data) {
//   return request({
//     url: `${URL.OPERATION}/algo/image/upload`,
//     method: 'post',
//     data,
//   })
// }

// 算法中心-快照上传
function algorithmImageUpload(data) {
  return request({
    url: `${URL.OPERATION}/algo/snapshot`,
    method: 'post',
    data,
  })
}

// 派发任务列表
function indicatorTaskList(data) {
  return request({
    url: `${URL.OPERATION}/indicator/task/search`,
    method: 'post',
    data,
  })
}
// 派发任务确认
function indicatorTaskConfirm(data) {
  return request({
    url: `${URL.OPERATION}/indicator/task/confirm`,
    method: 'post',
    data,
  })
}
// 算法重名校验
function checkAlgoName(data) {
  return request({
    url: `${URL.OPERATION}/algo/checkAlgoName`,
    method: 'post',
    data,
  })
}
// 算法发布
function algorithmPublish(data) {
  return request({
    url: `${URL.OPERATION}/algo/audit/publish`,
    method: 'post',
    data,
  })
}
// 算法撤销
function algorithmRollback(data) {
  return request({
    url: `${URL.OPERATION}/algo/audit/rollback`,
    method: 'post',
    data,
  })
}
// 算法下架
function algorithmOffline(data) {
  return request({
    url: `${URL.OPERATION}/algo/audit/offline`,
    method: 'post',
    data,
  })
}

// ------------------------------------算法中心 结束-----------------------------------

// 表权限管理-树获取
function tablePermissionTree(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/tree`,
    method: 'post',
    data,
  })
}
// 表权限管理-分页查询
function tablePermissionPage(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/search`,
    method: 'post',
    data,
  })
}
// 表权限管理-取消授权
function tablePermissionCancel(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/cancel`,
    method: 'post',
    data,
  })
}

// 表权限管理-查询当前有权限的用户ID
function tablePermissionUserList(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/users`,
    method: 'post',
    data,
  })
}

// 表权限管理-增加授权人员
function tablePermissionAddUser(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/add`,
    method: 'post',
    data,
  })
}

// 表权限管理-增加授权人员
function tablePermissionAddUserList(data) {
  return request({
    url: `${URL.OPERATION}/table/permission/manager/add`,
    method: 'post',
    data,
  })
}

export default {
  getWorkData,
  // 实时作业
  newCopyRealTimeWork,
  newRealWorkList,
  newOffRealWork,
  newCancelRealWork,
  newDeleteRealWork,
  newEditRealWork,
  newOnRealWork,
  addRealWork,
  addRealWorkRun,
  newRealDataWorkDetail,
  newRealSaveDataWork,
  runRealTask,
  stopRealTask,
  runRealTaskLog,
  getDetailRealWork,
  getDetailOffline,
  monitorTaskDetailRealLog,
  monitorRepeat,
  monitorTaskInstance,
  getExcLog,
  getEnginesList,
  getFieldTypeList,
  realWorkUpload,
  getDatasource,
  flinkSQLAnalysis,
  validRealDataWorkName,
  // 离线作业
  addDataWork,
  validDataWorkName,
  newDataWorkList,
  newAddDataWork,
  newEditDataWork,
  newDeleteDataWork,
  newOffDataWork,
  newOffDataWorkRollback,
  newOnDataWork,
  newOnDataWorkRollback,
  newExecuteDataWork,
  newCopyDataWork,
  runTaskDataWork,
  runTaskLogDataWork,
  runTask,
  runTaskResultAndLog,
  stopRunTask,
  newSaveSqlWork,
  parseSqlWork,
  newCheckDataWork,
  newSaveDataWork,
  newDataWorkDetail,
  getDetailOffWork,
  getOvenList,
  getCraftList,
  algoFieldList,
  sendJson,
  getNewSearchTree,
  checkScriptName,
  newAddSearchTree,
  newAddScriptTree,
  newDelSearchTree,
  newDelScriptTree,
  newScriptRun,
  newCompletelyScriptRun,
  newScriptStop,
  newScriptLog,
  dataWorkDetail,
  publish,
  execSql,
  execLog,
  monitorTaskDetailLog,

  exportSearchTree,
  // 算法中心
  listAlgorithm,
  addAlgorithm,
  deleteAlgorithm,
  checkAlgoName,
  algorithmPublish,
  algorithmRollback,
  algorithmOffline,

  // 场景接口
  sceneTree,
  sceneTreeAdd,
  sceneTreeEdit,
  sceneTreeDeleteCheck,
  sceneTreeDelete,

  dataWorkList,

  getAuthTable,
  getAuthTableDetail,
  datalineageInputOutput,
  hiveCopy,

  monitorTaskQuery,
  monitorTaskDetailQuery,
  //算法中心
  algorithmTagList,
  algorithmDebugTest,
  algorithmDebugTestSync,
  algorithmExampleCode,
  algorithmDebugLog,
  algorithmDebugStop,
  algorithmCreate,
  algorithmSearch,
  algorithmSearchForWork,
  algorithmDownload,
  algorithmParsePython,
  algorithmDetail,
  algorithmDelete,
  algorithmPublicPublish,
  algorithmDebugLogLast,
  algorithmUpdate,
  algorithmImageUpload,
  indicatorTaskList,
  indicatorTaskConfirm,
  // 标管理
  tablePermissionTree,
  tablePermissionPage,
  tablePermissionCancel,
  tablePermissionUserList,
  tablePermissionAddUser,
  tablePermissionAddUserList,
}
