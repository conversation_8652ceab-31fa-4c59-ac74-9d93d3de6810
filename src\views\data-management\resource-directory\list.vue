<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">表名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="请输入表名称"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">数据分类</div>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />

          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          />
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="row">
              <n-radio-group
                direction="row"
                v-model="state.collectType"
                size="sm"
                @change="radioFn"
              >
                <n-radio-button value="ALL">全部</n-radio-button>
                <n-radio-button value="NO">待采集</n-radio-button>
                <n-radio-button value="YES">已采集</n-radio-button>
              </n-radio-group>
              <div class="switch-label">
                <div
                  class="tabs-btn"
                  code="dataManagement_dataCollection_add"
                  v-if="
                    buttonAuthList.includes('dataManagement_resourceDirectory_apply_edit') &&
                    state.collectType === 'ALL'
                  "
                  @click.prevent="applyFn(state.selectionList)"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 14.5H2.5C1.94772 14.5 1.5 14.0523 1.5 13.5V2.5C1.5 1.94772 1.94772 1.5 2.5 1.5H2.875H3.5625H4.25H10.4375H11.125H11.5C12.0523 1.5 12.5 1.94772 12.5 2.5V8V8.5"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                    <path
                      d="M4 4.5H10"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M4 8H10"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M4 11.5H7"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M12.4999 10.5L14.5 12.4999"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                    <path
                      d="M12.4999 14.5L14.5 12.5001"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                    <path d="M14 12.5L9 12.5" stroke="currentColor" stroke-linecap="round" />
                  </svg>

                  申请采集
                </div>
                <div v-if="state.isRefreshWait" class="tabs-btn"
                  >同步进度：{{ state.refreshInfo.synchronizedTables }}/{{
                    state.refreshInfo.totalTables
                  }}</div
                >
                <div
                  v-else
                  v-loading="state.loading"
                  :class="state.databaseId ? 'tabs-btn' : 'tabs-btn disabled'"
                  @click.prevent="getRefreshCountFn(true)"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_1839_123665)">
                      <path
                        d="M6 1.41589C7.43769 0.874404 9.00889 0.861306 10.4546 1.37876C11.9004 1.89621 13.1344 2.91334 13.9533 4.26248C14.7722 5.61162 15.1271 7.21226 14.9594 8.80057C14.8768 9.58351 14.6696 10.3404 14.3514 11.0421M14.3514 11.0421C14.024 11.7638 13.579 12.4271 13.0311 13L13.0794 11.0421L14.3514 11.0421Z"
                        stroke="currentColor"
                        stroke-linecap="round"
                      />
                      <path
                        d="M10 14.5841C8.56231 15.1256 6.99111 15.1387 5.54535 14.6212C4.09959 14.1038 2.86558 13.0867 2.0467 11.7375C1.22782 10.3884 0.872948 8.78774 1.04058 7.19943C1.12321 6.41649 1.33038 5.65958 1.64864 4.95795M1.64864 4.95795C1.97603 4.2362 2.42098 3.57294 2.96887 3L2.9206 4.95795L1.64864 4.95795Z"
                        stroke="currentColor"
                        stroke-linecap="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_1839_123665">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  刷新</div
                >
              </div>
            </div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                saveWidth
                :isDisplayAction="state.isDisplayAction"
                :isNeedSelection="state.isNeedSelection"
                :table-head-titles="state.tableHeadTitles"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="140"
                :selectable="(row) => !row.collectApplicantName"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    onSearch()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    onSearch()
                  },
                }"
                @handle-selection-change="handleSelectionChange"
              >
                <template #name="{ row }">
                  <div class="taskName">
                    {{ row.sourceName }}
                  </div>
                </template>
                <template #isCollected="{ row }">
                  <div>{{ row.isCollected ? '是' : '否' }}</div>
                </template>

                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      v-if="buttonAuthList.includes('dataManagement_resourceDirectory_view')"
                      code="dataManagement_resourceDirectory_view"
                      variant="text"
                      @click.prevent="cardSeeFn(row)"
                      >查看</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_resourceDirectory_apply_edit') &&
                        !row.collectApplicantName && !(row.isCollected)
                      "
                      code="dataManagement_resourceDirectory_apply_edit"
                      variant="text"
                      @click.prevent="applyFn([row.id])"
                      >申请采集</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="row.collectApplicantName && !row.isCollected"
                      :disabled="true"
                      variant="text"
                      ><n-tooltip
                        class="tree-btn"
                        :content="`${row.collectApplicantName} ${row.collectApplicationTime}申请`"
                        position="top"
                        :enterable="false"
                        :disabled="!row.collectApplicantName"
                      >
                        <span>已申请</span>
                      </n-tooltip></n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import {
    dataCategoryTreeDatasource,
    collectRecordList,
    collectTableApplyMore,
    assetsMenuDatasourceRefresh,
    assetsMenuDatasourceRefreshCount,
  } from '@/api/dataManage'
  import { ElNotification } from 'element-plus'
  import cardList from '@/components/CardList'
  import CfTtee from '@/components/cfTtee'
  export default {
    name: '',
    components: { cardList, CfTtee },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: {},
        tableHeight: 436,
        key: 1,
        collectType: 'ALL',
        loading: false,
        isDisplayAction: true,
        isNeedSelection: true,
        isRefreshWait: false,
        refreshInfo: { totalTables: 0, synchronizedTables: 0 },
        databaseId: null,
        selectionList: [],
        originalFormInline: {
          keyword: null,
        },
        formInline: {
          keyword: null,
        },
        optionItemData: {}, // 操作的任务数据
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'number', name: '编号', width: 80 },
          { prop: 'sourceName', name: '数据源名称', slot: 'name' },
          { prop: 'tableName', name: '表名称' },
          { prop: 'tableComment', name: '表描述' },
          { prop: 'isCollected', name: '是否采集', slot: 'isCollected' },
          { prop: 'updateTime', name: '更新时间' },
        ],
        timeFlag: null,
        categoryId: null,
        treeSearchText: '',
        treeData: [{ id: '0_category', name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: '0_category', name: '全部', expanded: true, children: [] }],
        expandedKeys: [], 
        selectedKey: null,
      })
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const methods = {
        // 获取图片
        getAssetsImages(name) {
          return new URL(`/src/assets/img/dev/card-source/${name}.png`, import.meta.url).href //本地文件路径
        },
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 196
          } else {
            state.tableHeight = document.body.offsetHeight - 298
          }
        },

        //新增数据采集
        applyFn(ids = []) {
          if (ids.length > 0) {
            collectTableApplyMore(ids).then((res) => {
              if (res.success) {
                ElNotification({
                  title: '提示',
                  message: '申请成功',
                  type: 'success',
                })
                methods.onSearch(true)
              }
            })
          } else {
            ElNotification({
              title: '提示',
              message: '请先勾选资源任务',
              type: 'warm',
            })
          }
        },
        // 查看
        cardSeeFn(item) {
          router.push({
            name: 'resourceDirectoryCollectDetail',
            query: { id: item.id },
          })
        },
        radioFn(type) {
          state.isDisplayAction = false
          state.isNeedSelection = false
          if (type === 'YES') {
            state.tableHeadTitles = [
              { prop: 'sourceName', name: '数据源名称', slot: 'name' },
              { prop: 'tableName', name: '表名称' },
              { prop: 'tableComment', name: '表描述' },
              { prop: 'collectJobName', name: '关联任务' },
              { prop: 'collectDestTableName', name: 'ODS表英文名称' },
              { prop: 'updateTime', name: '更新时间' },
            ]
          } else if (type === 'NO') {
            state.tableHeadTitles = [
              { prop: 'sourceName', name: '数据源名称', slot: 'name' },
              { prop: 'tableName', name: '表名称' },
              { prop: 'tableComment', name: '表描述' },
              { prop: 'collectApplicantName', name: '申请人' },
              { prop: 'collectApplicationTime', name: '申请时间' },
              { prop: 'updateTime', name: '更新时间' },
            ]
          } else {
            state.tableHeadTitles = [
              { prop: 'number', name: '编号', width: 80 },
              { prop: 'sourceName', name: '数据源名称', slot: 'name' },
              { prop: 'tableName', name: '表名称' },
              { prop: 'tableComment', name: '表描述' },
              { prop: 'isCollected', name: '是否采集', slot: 'isCollected' },
              { prop: 'updateTime', name: '更新时间' },
            ]

            state.isDisplayAction = true
            state.isNeedSelection = true
          }
          state.tableData = {}
          methods.onSearch(true)
          state.key++
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            keyword: null,
          }
          methods.searchClickFn()
        },
        // 刷新
        refreshFn() {
          let ids = []
          if (state.databaseId) {
            ids.push(state.databaseId)
          } else {
            return false
          }
          state.loading = true
          assetsMenuDatasourceRefresh({ ids })
            .then((res) => {
              state.loading = false
              if (res.success) {
                ElNotification({
                  title: '提示',
                  message: '刷新成功',
                  type: 'success',
                })
                methods.onSearch(true)
                methods.getRefreshCountFn()
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 获取刷新进度
        getRefreshCountFn(flag = false) {
          if (!state.databaseId) {
            return false
          }
          assetsMenuDatasourceRefreshCount({ datasourceId: state.databaseId }).then((res) => {
            if (res) {
              if (state.timeFlag) {
                clearTimeout(state.timeFlag)
                state.timeFlag = null
              }
              state.refreshInfo = res.data
              if (
                state.refreshInfo.totalTables > 0 &&
                state.refreshInfo.totalTables !== state.refreshInfo.synchronizedTables
              ) {
                state.isRefreshWait = true
                state.timeFlag = setTimeout(() => {
                  methods.getRefreshCountFn()
                }, 60 * 1000)
              } else {
                state.isRefreshWait = false
              }
              if (flag) {
                methods.refreshFn()
              }
            }
          })
        },
        // 获取树列表
        getTreeListFn() {
          dataCategoryTreeDatasource({}).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              if (res.data?.children.length > 0) {
                treeData = [res.data]
                treeData[0].selected = true
                state.categoryId = treeData[0].id + '_category'
              } else {
                treeData.push({
                  children: [],
                  id: '0_category',
                  level: 0,
                  label: '全部',
                  type: 'ROOT',
                })
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }

              treeData = methods.changeTreeNameFn(treeData)
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]

              nextTick(() => {
                state.expandedKeys = [state.treeData?.[0]?.id || 1]
                state.selectedKey = state.treeData?.[0]?.id || 1
              })
              methods.onSearch(true)
            }
          })
        },
        // 转化数据源名称
        changeTreeNameFn(treeData) {
          return treeData.map((node) => {
            if (node.datasource) {
              node.id = node.databaseId + '_datasource'
              node.name = node.databaseName
            } else {
              node.id = node.id + '_category'
            }
            node.children = node.children && methods.changeTreeNameFn(node.children)
            return node
          })
        },
        // 树搜索
        searchTreeFn() {
          state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.id) {
            state.categoryId = node.id
            if (node.databaseId) {
              state.databaseId = node.databaseId
              methods.getRefreshCountFn()
            } else {
              state.databaseId = null
            }
            methods.onSearch(true)
          }
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        // 初始化表格（列表）
        onSearch(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let _status = null
          if (state.collectType === 'ALL') {
            _status = null
          } else if (state.collectType === 'YES') {
            _status = true
          } else {
            _status = false
          }
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              collected: _status,
              sourceName: state.formInline.keyword || null,
            },
          }
          if (String(state.categoryId).indexOf('category') !== -1) {
            data.condition.sourceCategoryId = state.categoryId.split('_')[0]
          } else {
            data.condition.datasourceId = state.categoryId.split('_')[0]
          }
          state.loading = true
          collectRecordList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.number = index + 1
                  if (item.collectApplicantName) {
                    item.disabledThisRow = true
                  }
                })
                state.tableData = res.data
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 列表选择
        handleSelectionChange(list) {
          state.selectionList = list
            ?.filter((val) => !val.collectApplicantName)
            .map((val) => val.id)
        },
        // 列表（表格）操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.onSearch()
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        methods.getTreeListFn()
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    &.isLzos {
      height: 100%;
      padding: 0;
    }
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }
          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-color: #1e89ff;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    height: 100%;
    padding: 0;
    border-radius: 0;
    .data-collection-page-tree {
      box-sizing: border-box;
      width: 286px;
      height: 100%;
      padding: 8px 0;
      background-color: #fff;
      border-radius: 2px;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 36px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
      &-ipt {
        margin-bottom: 8px;
        padding: 0 12px;

        :deep(.nancalui-input-slot__suffix) {
          opacity: 0.5;

          .icon-search {
            font-weight: normal;
            transform: scale(1.4);
          }
        }
      }

      :deep(.tree-box) {
        padding: 0 12px;
        height: calc(100% - 72px);
      }
    }
    .data-collection-page-content {
      width: calc(100% - 10px - var(--aside-width));
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .out-box {
        height: 100%;
        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 46px;
          padding: 0 8px;
          .switch-label {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1e89ff;
            font-size: 14px;
            cursor: pointer;
            .icon-switch {
              margin-right: 4px;
              font-size: 16px;
            }
            &:hover {
              color: #479dff;
            }
          }
        }

        .tabs-btn {
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 0 16px;
          &.disabled {
            color: #c8c9cc;
            cursor: not-allowed;
            svg {
              cursor: not-allowed;
            }
          }
        }
      }

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;
        .nancalui-table {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }
          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;
              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }
          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }
          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;
              &.PUBLISH {
                background-color: #04c495;
              }
              &.CREATED {
                background-color: #447dfd;
              }
              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }
      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }
        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }
</style>
