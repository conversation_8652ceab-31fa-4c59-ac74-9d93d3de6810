<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>ic/代码@2x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="算法管理-查看算法" transform="translate(-96.000000, -402.000000)">
            <g id="算法脚本" transform="translate(80.000000, 384.000000)">
                <g id="ic/代码" transform="translate(16.000000, 18.000000)">
                    <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                    <g id="默认标题" transform="translate(2.000000, 1.000000)" stroke="#262626" stroke-linecap="round" stroke-linejoin="round">
                        <rect id="矩形" x="0" y="0" width="12" height="14" rx="2"></rect>
                        <polyline id="路径" points="7 5 9 7 7 9"></polyline>
                        <polyline id="路径" points="5 5 3 7 5 9"></polyline>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>