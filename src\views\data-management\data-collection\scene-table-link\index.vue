<template>
  <div class="scene-table-link">
    <!-- <div :class="{ 'page-top': true, dif: oneline }">
      <div class="title">关联映射关系</div>
      <div class="options">
        <n-button
          color="primary"
          :class="{
            'auto-connect-button': true,
            disabled: !(state.leftModelData.length && state.rightModelData.length),
          }"
          :disabled="!(state.leftModelData.length && state.rightModelData.length)"
          @click.prevent="autoConnect"
          >自动匹配</n-button
        >
        <n-button
          color="primary"
          :class="{
            'clear-edge-button': true,
            disabled: state.haveLineIdArr.length === 0,
          }"
          :disabled="state.haveLineIdArr.length === 0"
          @click.prevent="clearEdge"
          >全部清除</n-button
        >
        <n-button
          v-show="secondPresentation"
          :class="{ 'view-button': true, disabled: !allExcelData.length }"
          :disabled="!allExcelData.length"
          color="primary"
          @click.prevent="dataView"
          >数据预览</n-button
        >
      </div>
    </div> -->
    <div id="svgBox" :class="{ ' page-mid ': true, 'chart-box': true, dif: oneline }">
      <div :id="containerId" class="trend-chart"></div>
    </div>
    <dataViewDialog ref="dataViewDialog" />
  </div>
</template>
<script>
  import { reactive, onBeforeUnmount, toRefs, ref } from 'vue'
  import { Graph, Shape } from '@antv/x6'
  import dataViewDialog from '@/views/data-management/data-collection/components/data-view-dialog'
  import { ElNotification } from 'element-plus'
  export default {
    name: '',
    components: { dataViewDialog },
    props: {
      containerId: {
        type: String,
        default: 'containerEr', // 盒子id
      },
      secondPresentation: {
        //展现数据预览
        type: Boolean,
        default: false,
      },
      allExcelData: {
        type: Array,
        default: () => [], // excel文件内容
      },
      oneline: {
        type: Boolean,
        default: false,
      },
    },
    setup(props) {
      const dataViewDialog = ref()
      const { containerId, allExcelData } = toRefs(props)
      const state = reactive({
        graph: null,
        data: [],
        leftModelData: [],
        rightModelData: [],
        haveLineIdArr: [], // 已经连线的id集合
        hasInit: false,
        resizeFn: null, // 屏幕变化执行函数
      })
      const methods = {
        // 数据预览
        //数据预览
        dataView() {
          if (allExcelData.value && allExcelData.value.length) {
            dataViewDialog.value.init(allExcelData.value)
          } else {
            ElNotification({
              title: '提示',
              message: '请选择数据源',
              type: 'warning',
            })
          }
        },
        setLineIdArr(item) {
          state.haveLineIdArr.push(item.source.port, item.target.port)
        },
        // 清空连线
        clearEdge() {
          if (state.graph) {
            let allEdges = state.graph.getEdges()
            if (allEdges.length) {
              allEdges.forEach((item) => {
                state.graph.removeEdge(item)
              })
              state.haveLineIdArr = []
            }
          }
        },
        // 自动匹配
        autoConnect() {
          if (state.leftModelData?.length && state.rightModelData?.length) {
            methods.clearEdge()
            let leftAllData = state.data.filter((item) => {
              // return item.label === '数据源'
              return item.label.includes('数据源')
            })
            let rightAllData = state.data.filter((item) => {
              // return item.label === '目的表'
              return item.label.includes('目的表')
            })

            let needColumnMapping = []

            rightAllData[0].ports.forEach((right, index) => {
              leftAllData[0].ports.forEach((left) => {
                if (
                  right.attrs.colName.text?.toUpperCase() ===
                    left.attrs.colName.text?.toUpperCase() &&
                  right.attrs.colName.text !== '字段英文名'
                ) {
                  needColumnMapping.push({
                    shape: 'edge',
                    zIndex: index + 1,
                    source: {
                      port: left.id,
                      cell: '1',
                    },
                    attrs: {
                      line: {
                        strokeWidth: 1,
                      },
                    },
                    target: {
                      port: right.id,
                      cell: '2',
                    },
                  })
                }
              })
            })
            state.haveLineIdArr = []
            needColumnMapping.forEach((item) => {
              state.graph.addEdge(item)
              state.haveLineIdArr.push(item.source.port, item.target.port)
            })
          }
        },

        // 改变下拉框数据渲染x6画布
        changTable(data, callBack = null) {
          state.warningTip = false

          let { leftData, rightData, dataSourceNames, modelNames } = data
          state.leftModelData = leftData
          state.rightModelData = rightData
          let dataSourceLable = ''

          if (dataSourceNames[1]) {
            dataSourceLable = '数据源:' + dataSourceNames[0] + ' - ' + dataSourceNames[1]
          } else {
            dataSourceLable = '数据源表:' + dataSourceNames[0]
          }

          let modelLable = '目的表:' + modelNames[0] + ' - ' + modelNames[1]
          let _leftData = [
            {
              id: '1-0',
              group: 'listLast',
              zIndex: -1,
              attrs: {
                serialNumber: {
                  text: '序号',
                },
                colName: {
                  text: '字段英文名',
                },
                comment: {
                  text: '字段中文名',
                },
                dataType: {
                  text: '字段类型',
                },
                dataLength: {
                  text: '字段长度',
                },
              },
            },
          ]
          let _rightData = [
            {
              id: '2-0',
              group: 'listLast',
              zIndex: -1,
              attrs: {
                serialNumber: {
                  text: '序号',
                },
                colName: {
                  text: '字段英文名',
                },
                comment: {
                  text: '字段中文名',
                },
                dataType: {
                  text: '字段类型',
                },
                dataLength: {
                  text: '字段长度',
                },
              },
            },
          ]
          if (state.leftModelData?.length) {
            state.data[0] = {
              id: '1',
              shape: 'er-rect',
              // label: '数据源',
              label: dataSourceLable,
              width: 520,
              height: 40,
              position: {
                x: 20,
                y: 0,
              },

              ports: [],
            }

            state.leftModelData.forEach((item, index) => {
              _leftData.push({
                id: '1-' + (index + 1),
                group: 'list',
                zIndex: -1,
                attrs: {
                  serialNumber: {
                    text: index + 1,
                  },
                  colName: {
                    text: item.colName,
                    textWrap: {
                      text: item.colName,
                      width: -420, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  dataType: {
                    text: item.dataType,
                    textWrap: {
                      text: item.dataType,
                      width: -470, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  dataLength: {
                    text: item.length,
                  },
                  comment: {
                    text: item.comment,
                    textWrap: {
                      text: item.comment,
                      width: -480, // 总NODE_WIDTH = 520 -420 =100
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                },
              })
            })
            state.data[0].ports = _leftData
          } else {
            state.data[0] = []
          }

          if (state.rightModelData?.length) {
            state.data[1] = {
              id: '2',
              shape: 'er-rect',
              // label: '目的表',
              label: modelLable,
              width: 520,
              height: 40,
              position: {
                x: 800,
                y: 0,
              },
              ports: [],
            }

            state.rightModelData.forEach((item, index) => {
              _rightData.push({
                id: '2-' + (index + 1),
                group: 'list',
                zIndex: -1,
                attrs: {
                  sortNum: {
                    text: item.sortNum,
                  },
                  serialNumber: {
                    text: index + 1,
                  },
                  colName: {
                    text: item.name,
                    textWrap: {
                      text: item.name,
                      width: -420, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  dataType: {
                    text: item.fieldType,
                    textWrap: {
                      text: item.fieldType,
                      width: -470, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  dataLength: {
                    text: item.fieldLength,
                  },
                  comment: {
                    text: item.description || item.cnName,
                    textWrap: {
                      text: item.description || item.cnName,
                      width: -480, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                },
              })
            })
            state.data[1].ports = _rightData
          } else {
            state.data[1] = []
          }
          if (state.graph) {
            state.graph.clearCells()
          }
          methods.init()
          if (typeof callBack === 'function') {
            callBack({ data: state, methods })
          }
        },

        init() {
          state.hasInit = true
          const LINE_HEIGHT = 42
          const NODE_WIDTH = 570
          Graph.registerPortLayout(
            'erPortPosition',
            (portsPositionArgs) => {
              return portsPositionArgs.map((_, index) => {
                let y = (index + 2) * LINE_HEIGHT

                return {
                  position: {
                    x: 0,
                    y,
                  },
                  angle: 0,
                }
              })
            },
            true,
          )
          Graph.registerPortLayout(
            'erPortPositionDif',
            (portsPositionArgs) => {
              return portsPositionArgs.map((_, index) => {
                let y = (index + 1) * LINE_HEIGHT
                return {
                  position: {
                    x: 0,
                    y,
                  },
                  angle: 0,
                }
              })
            },
            true,
          )

          Graph.registerNode(
            'er-rect',
            {
              inherit: 'rect',
              markup: [
                {
                  tagName: 'rect',
                  selector: 'body',
                },
                {
                  tagName: 'text',
                  selector: 'label',
                },
              ],
              attrs: {
                body: {
                  strokeWidth: 1,
                  stroke: 'none',
                  fill: '#fff',
                  textAnchor: 'left',
                  refX: 0,
                },
                label: {
                  fontWeight: 'bold',
                  fill: '#000',
                  fontSize: 12,
                  textAnchor: 'left',
                  refX: 0,
                },
              },
              ports: {
                groups: {
                  // 内容
                  list: {
                    markup: [
                      {
                        tagName: 'rect',
                        selector: 'portBody',
                      },
                      {
                        tagName: 'text',
                        selector: 'formTitle',
                      },
                      {
                        tagName: 'text',
                        selector: 'sortNum',
                      },

                      {
                        tagName: 'text',
                        selector: 'serialNumber',
                      },
                      {
                        tagName: 'text',
                        selector: 'colName',
                      },
                      {
                        tagName: 'text',
                        selector: 'comment',
                      },
                      {
                        tagName: 'text',
                        selector: 'dataType',
                      },
                      {
                        tagName: 'text',
                        selector: 'dataLength',
                      },
                    ],
                    attrs: {
                      portBody: {
                        width: NODE_WIDTH,
                        height: LINE_HEIGHT,
                        strokeWidth: 1,
                        stroke: '#697A9A2E',
                        fill: '#fff', //内容颜色和边框
                        magnet: true,
                      },

                      sortNum: {
                        ref: 'portBody',
                        refX: 0,
                        refY: 0,
                        fontSize: 0,
                        magnet: false,
                      },
                      serialNumber: {
                        ref: 'portBody',
                        refX: 16,
                        refY: 15,
                        fontSize: 12,
                        magnet: true,
                      },
                      colName: {
                        ref: 'portBody',
                        refX: 114,
                        refY: 15,
                        fontSize: 12,
                        magnet: false,
                      },
                      comment: {
                        ref: 'portBody',
                        refX: 268,
                        refY: 15,
                        fontSize: 12,
                        magnet: false,
                      },
                      dataType: {
                        ref: 'portBody',
                        refX: 372,
                        refY: 15,
                        fontSize: 12,
                        magnet: false,
                      },
                      dataLength: {
                        ref: 'portBody',
                        refX: 476,
                        refRx: 0.1,
                        refY: 15,
                        fontSize: 12,
                        width: 50,
                        magnet: true,
                      },
                    },
                    position: 'erPortPosition',
                  },
                  // 表头
                  listLast: {
                    markup: [
                      {
                        tagName: 'rect',
                        selector: 'portBody',
                      },
                      {
                        tagName: 'text',
                        selector: 'formTitle',
                      },
                      {
                        tagName: 'text',
                        selector: 'serialNumber',
                      },
                      {
                        tagName: 'text',
                        selector: 'colName',
                      },
                      {
                        tagName: 'text',
                        selector: 'comment',
                      },
                      {
                        tagName: 'text',
                        selector: 'dataType',
                      },
                      {
                        tagName: 'text',
                        selector: 'dataLength',
                      },
                    ],
                    attrs: {
                      portBody: {
                        width: NODE_WIDTH,
                        height: LINE_HEIGHT,
                        strokeWidth: 1,
                        stroke: '#E3ECFF',
                        fill: '#E3ECFF', //表头颜色和边框
                        magnet: false,
                      },
                      serialNumber: {
                        ref: 'portBody',
                        refX: 16,
                        refY: 15,
                        fontSize: 12,
                        fontWeight: 'bold',
                        magnet: false,
                      },
                      colName: {
                        ref: 'portBody',
                        refX: 114,
                        refY: 15,
                        fontSize: 12,
                        fontWeight: 'bold',
                        magnet: false,
                      },
                      comment: {
                        ref: 'portBody',
                        refX: 268,
                        refY: 15,
                        fontSize: 12,
                        fontWeight: 'bold',
                        magnet: false,
                      },
                      dataType: {
                        ref: 'portBody',
                        refX: 372,
                        refY: 15,
                        fontSize: 12,
                        fontWeight: 'bold',
                        magnet: false,
                      },
                      dataLength: {
                        ref: 'portBody',
                        refX: 476,
                        refY: 15,
                        refRx: 0.2,
                        fontSize: 12,
                        fontWeight: 'bold',
                        magnet: true,
                      },
                    },
                    position: 'erPortPositionDif',
                  },
                },
              },
            },
            true,
          )

          Shape.Rect.config({
            attrs: {
              body: {
                fill: '#f5f5f5',
                stroke: '#d9d9d9',
                strokeWidth: 1,
              },
            },
            ports: {
              groups: {
                in: {
                  position: { name: 'top' },
                },
                out: {
                  position: { name: 'bottom' },
                },
              },
            },
            portMarkup: [
              {
                tagName: 'circle',
                selector: 'portBody',
                attrs: {
                  r: 50,
                  magnet: true,
                  stroke: '#31d0c6',
                  fill: '#fff',
                  strokeWidth: 2,
                },
              },
            ],
          })

          // const magnetAvailabilityHighlighter = {
          //   name: 'stroke',
          //   args: {
          //     padding: 3,
          //     attrs: {
          //       strokeWidth: 3,
          //       stroke: '#52c41a',
          //     },
          //   },
          // }
          let that = state
          let height = document.getElementById(containerId.value).offsetHeight
          let width = document.getElementById(containerId.value).offsetWidth

          if (!state.graph) {
            state.graph = new Graph({
              container: document.getElementById(containerId.value),
              width,
              height,
              snapline: {
                enabled: true,
                clean: false,
              },

              scroller: {
                enabled: true,
                pannable: false,
                pageVisible: true,
                pageBreak: false,
              },
              mousewheel: {
                enabled: true,
                modifiers: ['ctrl', 'meta'],
              },

              keyboard: true,
              clipboard: true,
              selecting: {
                enabled: true,
                rubberband: true,
                showNodeSelectionBox: true,
              },
              // scroller: true, // 是否添加滚动条
              interacting: {
                nodeMovable: false, // 禁止节点移动
              },
              // highlighting: {
              //   magnetAvailable: magnetAvailabilityHighlighter,
              // },
              connecting: {
                snap: true, // 当 snap 设置为 true 时连线的过程中距离节点或者连接桩 50px 时会触发自动吸附
                allowBlank: false, // 是否允许连接到画布空白位置的点，默认为 true。
                allowMulti: true, // 是否链接多个
                allowLoop: false, // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点，默认为 true
                allowNode: false, // 是否允许边链接到节点（非节点上的链接桩），默认为 true。
                allowEdge: false, // 是否允许边链接到另一个边，默认为 true
                allowPort: true, // 是否允许边链接到链接桩
                highlight: true,
                router: {
                  name: 'er',
                  args: {
                    offset: 25,
                    direction: 'H',
                  },
                },
                createEdge() {
                  return new Shape.Edge({
                    attrs: {
                      line: {
                        stroke: '#333',
                        strokeWidth: 1,
                      },
                    },
                  })
                },
                // 数据源侧  只输出不能输入
                validateMagnet({ cell }) {
                  return cell.label.includes('数据源')
                },
                // 连线规则设置
                validateConnection(data) {
                  let status = false
                  let { sourcePort, targetPort } = data
                  if (
                    state.haveLineIdArr.includes(sourcePort) ||
                    state.haveLineIdArr.includes(targetPort)
                  ) {
                    // 已经连接过
                    status = false
                  } else {
                    status = true
                  }
                  return status
                },
              },
            })
            let _state = state
            const resizeFn = () => {
              const { width, height } = methods.getContainerSize()
              _state.graph.resize(width, height)
            }
            resizeFn()
            window.addEventListener('resize', resizeFn)
            state.resizeFn = resizeFn
          }
          state.graph.bindKey('backspace', () => {
            const cells = state.graph.getSelectedCells()
            if (cells.length) {
              state.graph.removeCells(cells)
            }
          })
          state.graph.bindKey('delete', () => {
            const cells = state.graph.getSelectedCells()
            if (cells.length) {
              state.graph.removeCells(cells)
            }
          })
          const cells = []
          state.data.forEach((item) => {
            if (item.shape === 'edge') {
              cells.push(state.graph.createEdge(item))
            } else {
              cells.push(state.graph.createNode(item))
            }
          })
          state.graph.resetCells(cells)
          // state.graph.zoomToFit({ padding: 10, maxScale: 1 })
          state.graph.on('edge:mouseenter', ({ cell }) => {
            cell.addTools([
              {
                name: 'source-arrowhead',
              },
              {
                name: 'target-arrowhead',
                args: {
                  attrs: {
                    fill: 'red',
                  },
                },
              },
              {
                name: 'button-remove',
                args: { distance: -40 },
              },
            ])
          })

          // 连线后执行
          state.graph.on('edge:connected', (data) => {
            let allEdges = that.graph.getEdges()
            // let a = []
            allEdges.forEach((item) => {
              state.haveLineIdArr.push(item.source.port, item.target.port)
            })
          })

          // 取消连线
          state.graph.on('edge:removed', (data) => {
            let sourceId = data.edge.source.port
            let targetId = data.edge.target.port
            // 貌似 toolId 存在时候是点击删除按钮删除，不存在时候为不满足条件时候自动触发
            if (data.options.toolId) {
              let new_haveLineIdArr = state.haveLineIdArr.map((item) => {
                let a = item
                if (item === sourceId || item === targetId) {
                  a = ''
                }
                return a
              })
              state.haveLineIdArr = new_haveLineIdArr.filter((val) => val)
            }
          })

          state.graph.on('edge:mouseleave', ({ cell }) => {
            cell.removeTools()
          })
          // state.graph.zoomTo(1) // 默认方法倍数
        },
        getGraphData() {
          return state.graph.toJSON()
        },
        // 获取画布容器大小
        getContainerSize() {
          return {
            width: document.getElementById('svgBox').offsetWidth,
            height: document.getElementById('svgBox').offsetHeight,
          }
        },
        // 渲染连线
        addEdge({ data, initLater }) {
          if (initLater) {
            let leftAllData = state.data.filter((item) => {
              return item.label.includes('数据源')
            })
            let rightAllData = state.data.filter((item) => {
              return item.label.includes('目的表')
            })

            let needColumnMapping = []

            if (data.length && leftAllData.length && rightAllData.length) {
              data.forEach((item, index) => {
                needColumnMapping[index] = {}
                leftAllData[0].ports.forEach((cell) => {
                  if (
                    item.sourceColumnName?.toUpperCase() === cell.attrs.colName.text?.toUpperCase()
                  ) {
                    needColumnMapping[index].shape = 'edge'
                    needColumnMapping[index].zIndex = index + 1
                    needColumnMapping[index].source = {
                      port: cell.id,
                      cell: '1',
                    }
                    needColumnMapping[index].attrs = {
                      line: {
                        // stroke: '#722ed1',
                        strokeWidth: 1,
                      },
                    }
                  }
                })
                rightAllData[0].ports.forEach((cell) => {
                  if (item.sinkMetaCode?.toUpperCase() === cell.attrs.colName.text?.toUpperCase()) {
                    needColumnMapping[index].target = {
                      port: cell.id,
                      cell: '2',
                    }
                  }
                })
                if (!needColumnMapping[index].target) {
                  //没有输出端时候,去除该连线
                  needColumnMapping[index] = 0
                }
              })
              if (needColumnMapping.length) {
                needColumnMapping.forEach((item) => {
                  if (item !== 0) {
                    state.graph.addEdge(item)
                  }
                })
              }
            }
          } else {
            if (data.length) {
              data.forEach((item) => {
                state.graph.addEdge(item)
              })
            }
          }
        },
      }
      onBeforeUnmount(() => {
        window.removeEventListener('resize', state.resizeFn)
      })
      return {
        state,
        dataViewDialog,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .scene-table-link {
    height: 100%;
    font-weight: 500;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;

    .page-top {
      margin-bottom: 12px;
      &.dif {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          margin-bottom: 0;
        }
      }
      .clear-edge-button {
        &.disabled {
          color: #c8c9cc;
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          cursor: no-drop;
        }
      }
      .auto-connect-button {
        &.disabled {
          color: #c8c9cc;
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          cursor: no-drop;
        }
      }
      .view-button {
        color: $themeBlue;
        background: #f0f7ff;
        border: 1px solid $themeBlue;

        &.disabled {
          color: #c8c9cc;
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          cursor: no-drop;
        }
      }
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .page-mid {
      // height: calc(100% - 84px);
      height: 100%;
      // background-color: #f7f8fa;
      border-radius: 8px;
      //border: 1px solid #ebedf0;
      &.dif {
        // height: calc(100% - 64px);
      }

      // margin-top: 10px;
      :deep(.x6-graph-scroller) {
        width: 100% !important;
        height: 100% !important;
        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #ddd;
          border-radius: 2px;
        }
      }
      :deep(.x6-graph-scroller.x6-graph-scroller-paged .x6-graph) {
        box-shadow: none;
      }
      #containerEr {
        width: 100%;
        height: calc(100vh - 620px);
      }
      #containerEr2 {
        width: 100%;
      }
    }
  }
</style>
