<template>
  <section class="container">
    <section class="container-box">
      <section class="header">
        <!-- <n-button-group size="sm" class="map-info">
          <n-button
            :class="{ 'res-btn': true, active: state.buttonActive == 0 }"
            @click.stop.prevent="getMapParams(0)"
            >3D血缘</n-button
          >
          <n-button
            :class="{
              'col-btn': true,
              active: state.buttonActive == 1,
            }"
            @click.stop.prevent="getMapParams(1)"
            >2D血缘</n-button
          >
        </n-button-group>
        <el-form :inline="true" :model="state.filterSearch" class="commonForm">
          <el-form-item label="">
            <el-input
              v-model="state.filterSearch.keyword"
              size="small"
              placeholder="请输入查询关键字"
              clearable
              @change="getLineageByPro"
            >
              <template #append>
                <n-button @click.stop.prevent="getLineageByPro">
                  <SvgIcon class="icon_search" icon="icon_search" />
                </n-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form> -->
      </section>
      <div
        id="flow-box"
        :class="{ 'map-box': btnScreen == '全屏', 'full-map': btnScreen == '退出' }"
        ><div class="btn-3d" v-if="btnScreen == '退出'" @click.stop.prevent="isFullScreenFn">
          <img :src="getAssetsImages('back_3d')" alt="" />
        </div>
        <section :class="{ tools: true, 'tools-2d': state.buttonActive == 1 }">
          <div v-if="state.buttonActive == 0" @click.stop.prevent="puaseClick">
            <img :src="getAssetsImages(puaseText)" alt="" />
            <span>{{ btnTit }}</span>
          </div>
          <div @click.stop.prevent="isFullScreenFn">
            <img :src="getAssetsImages(screenText)" alt="" />
            <span>{{ btnScreen }}</span>
          </div>
        </section>
        <section :class="{ displayPanel: true, 'displayPanel-other': state.buttonActive == 1 }">
          <div><span class="point point3"></span>文件</div>
          <div><span class="point point4"></span>数据模型</div>
          <div><span class="point point5"></span>API</div>
          <div><span class="point point6"></span>数据源</div>
          <!-- <div><span class="point point8"></span>其他</div> -->
        </section>
        <section
          v-show="state.buttonActive == 0"
          ref="flow"
          class="flow"
          @mousewheel="scrollWheel"
        ></section>
      </div>
    </section>
    <!-- 文件 -->
    <div :class="{ 'n-drawer': true, active: state.showFilePop, 'sm-drawer': btnScreen == '全屏' }">
      <div
        :class="{
          'n-drawer-content': true,
        }"
      >
        <div class="drawer-content-name">
          <moduleName :info="{ name: state.fileForm.sourceFileName }" />
          <img
            class="close"
            src="@/assets/img/dev/icon-false-gray.png"
            @click="closeModle('showFilePop')"
          />
        </div>
        <n-tabs v-model="state.activeName" @active-tab-change="handleClick">
          <n-tab title="基本信息" id="first" />
        </n-tabs>
        <n-form
          class="disable-hide-border disabled-form"
          v-show="state.activeName === 'first'"
          :data="state.fileForm"
          label-width="100px"
          label-position="left"
          label-align="end"
        >
          <n-form-item label="文件名称：">
            <n-input
              v-model="state.fileForm.sourceFileName"
              maxlength="30"
              placeholder=" "
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="文件类型：">
            <n-input v-model="state.fileForm.sourceFileType" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="文件格式：">
            <n-input v-model="state.fileForm.sourceFileFormat" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="文件大小：">
            <n-input v-model="state.fileForm.sourceFileSize" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="创建时间：">
            <n-input v-model="state.fileForm.createTime" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="创建人：">
            <n-input v-model="state.fileForm.createByName" placeholder=" " disabled noborder />
          </n-form-item>
        </n-form>
      </div>
    </div>
    <!-- 数据源 -->
    <div :class="{ 'n-drawer': true, active: state.showPop, 'sm-drawer': btnScreen == '全屏' }">
      <div
        :class="{
          'n-drawer-content': true,
        }"
      >
        <div class="drawer-content-name">
          <moduleName
            :info="{
              name: `${state.form.name}`,
            }"
          />
          <img
            class="close"
            src="@/assets/img/dev/icon-false-gray.png"
            @click="closeModle('showPop')"
          />
        </div>
        <n-tabs v-model="state.activeName" @active-tab-change="handleClick">
          <n-tab v-if="!state.isFileList" title="基础信息" id="first" />
          <!-- <n-tab v-if="state.isDataList && !state.isFileList" title="数据结构" id="second" /> -->
          <n-tab v-if="state.isFileList" title="文件预览" id="third" />
        </n-tabs>
        <n-form
          class="disable-hide-border disabled-form"
          v-show="state.activeName === 'first'"
          :data="state.form"
          label-width="100px"
          label-align="end"
        >
          <n-form-item label="数据源名称：">
            <n-input disabled noborder v-model="state.form.name" placeholder=" " />
          </n-form-item>
          <n-form-item label="数据结构类型：">
            <n-input disabled noborder v-model="state.form.dataStructureTypeName" placeholder=" " />
          </n-form-item>
          <n-form-item label="数据库类型：">
            <n-input disabled noborder v-model="state.form.datasourceType" placeholder=" " />
          </n-form-item>

          <n-form-item label="描述信息：">
            <n-textarea
              v-model="state.form.description"
              :rows="5"
              placeholder=" "
              maxlength="200"
              disabled
              noborder
            />
          </n-form-item>
        </n-form>
        <section v-show="state.activeName === 'second'">
          <n-form ref="formRef" :data="state.form" label-width="84px" label-align="end">
            <n-form-item
              prop="email"
              label="选择数据表："
              :rules="[
                {
                  required: true,
                  message: '请选择数据表',
                  trigger: 'blur',
                },
              ]"
            >
              <n-select
                v-model="state.tableId"
                placeholder="请选择数据表"
                @value-change="getSourceStructure"
                @clear="getSourceStructure"
              >
                <n-option
                  v-for="item in state.tableOpts"
                  :key="item.key"
                  :name="item.key"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </n-form>
          <h2 class="source-h2">数据结构信息</h2>

          <n-public-table
            ref="publicTable"
            :table-head-titles="state.tableHeadTitles"
            :tableData="state.tableData"
            :showPagination="false"
            :tableHeight="state.apiTableHeight"
          />
        </section>
        <n-public-table
          v-show="state.activeName === 'third'"
          ref="publicTableFile"
          :table-head-titles="state.tableHeadTitlesFile"
          :tableData="state.fileTableData"
          :showPagination="false"
          :tableHeight="state.tableHeight"
        />
      </div>
    </div>
    <!-- 模型 -->
    <div
      :class="{ 'n-drawer': true, active: state.showModlePop, 'sm-drawer': btnScreen == '全屏' }"
    >
      <div
        :class="{
          'n-drawer-content': true,
        }"
      >
        <div class="drawer-content-name">
          <moduleName
            :info="{
              name: `${state.modelForm?.cnName} ${state.modelForm.name ? '｜' : ''} ${
                state.modelForm.name
              }`,
            }"
          />
          <img
            class="close"
            src="@/assets/img/dev/icon-false-gray.png"
            @click="closeModle('showModlePop')"
          />
        </div>
        <n-tabs v-model="state.activeName" @active-tab-change="handleClick">
          <n-tab title="基础信息" id="first" />
          <n-tab title="字段信息" id="second" />
          <n-tab title="数据预览" id="third" />
        </n-tabs>
        <n-form
          class="disable-hide-border disabled-form"
          v-show="state.activeName === 'first'"
          :data="state.modelForm"
          label-width="100px"
          label-align="end"
        >
          <n-form-item label="所属场景：">
            <n-input disabled noborder v-model="state.modelForm.projectName" placeholder=" " />
          </n-form-item>
          <n-form-item label="模型层：">
            <n-input disabled noborder v-model="state.modelForm.layerName" placeholder=" " />
          </n-form-item>
          <n-form-item label="描述信息：">
            <n-textarea
              v-model="state.form.description"
              :rows="1"
              placeholder=" "
              maxlength="200"
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="创建人：">
            <n-input disabled noborder v-model="state.modelForm.createByName" placeholder=" " />
          </n-form-item>
          <n-form-item label="创建时间：">
            <n-input disabled noborder v-model="state.modelForm.createTime" placeholder=" " />
          </n-form-item>
          <n-form-item label="更新时间：">
            <n-input disabled noborder v-model="state.modelForm.updateTime" placeholder=" " />
          </n-form-item>
        </n-form>

        <n-public-table
          v-show="state.activeName === 'second'"
          ref="publicTableModel"
          :table-head-titles="state.modelTableHeadTitles"
          :tableData="state.modelTableData"
          :showPagination="false"
          :tableHeight="state.tableHeight"
        />
        <n-public-table
          v-show="state.activeName === 'third'"
          ref="publicTablePreview"
          :loading="state.loadingPreview"
          :key="state.keyCunt"
          :table-head-titles="state.tableHeadTitlesPreview"
          :tableData="state.previewTableData"
          :showPagination="true"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          @tablePageChange="tablePageChange"
        />
      </div>
    </div>
    <!-- api -->
    <div :class="{ 'n-drawer': true, active: state.showApiPop, 'sm-drawer': btnScreen == '全屏' }">
      <div
        :class="{
          'n-drawer-content': true,
        }"
      >
        <div class="drawer-content-name">
          <moduleName :info="{ name: state.apiForm.apiName }" />
          <img
            class="close"
            src="@/assets/img/dev/icon-false-gray.png"
            @click="closeModle('showApiPop')"
          />
        </div>
        <n-tabs v-model="state.activeName" @active-tab-change="handleClick">
          <n-tab title="基本信息" id="first" />
          <n-tab title="参数信息" id="second" />
          <n-tab title="返回示例" id="backData" />
        </n-tabs>
        <n-form
          class="disable-hide-border disabled-form"
          v-show="state.activeName === 'first'"
          :data="state.apiForm"
          label-width="100px"
          label-position="left"
          label-align="end"
        >
          <n-form-item label="API名称：">
            <n-input
              v-model="state.apiForm.apiName"
              maxlength="30"
              placeholder=" "
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="参数协议：">
            <n-input v-model="state.apiForm.protocol" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="请求方式：">
            <n-input v-model="state.apiForm.requestMethod" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="请求Path:">
            <n-input v-model="state.apiForm.showUrl" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="描述信息：">
            <n-textarea
              v-model="state.form.apiStory"
              :rows="3"
              :autosize="{ minRows: 3 }"
              placeholder=" "
              maxlength="200"
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="Token：">
            <n-input v-model="state.apiForm.token" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="创建人：">
            <n-input v-model="state.apiForm.createByName" placeholder=" " disabled noborder />
          </n-form-item>
          <n-form-item label="创建时间：">
            <n-input v-model="state.apiForm.createTime" placeholder=" " disabled noborder />
          </n-form-item>
        </n-form>
        <section v-show="state.activeName === 'second'">
          <div class="params-info">
            <n-button
              :class="{ active: state.firstButtonActive }"
              link
              @click.stop.prevent="getRequestParams"
              >请求参数</n-button
            >
            <n-button
              :class="{ active: !state.firstButtonActive }"
              link
              @click.stop.prevent="getReturnParams"
              >返回参数</n-button
            >
          </div>
          <div v-show="state.paramsType === 'request'" class="params-content">
            <n-public-table
              ref="requestPublicTable"
              :table-head-titles="state.requestTableHeadTitles"
              :tableData="state.requestTableData"
              :showPagination="false"
              :tableHeight="state.apiTableHeight"
            >
              <!-- 参数 value -->
              <template #parameterValue="{ editor }">
                <div>{{ editor.row.value }}</div>
              </template>
              <!-- 是否必填 -->
              <template #isRequired="{ editor }">
                <div v-if="editor.row.isRequired" class="isRequired">
                  <span class="required">*</span>{{ editor.row.isRequired }}
                </div>
                <div v-else>{{ editor.row.isRequired }}</div>
              </template>
            </n-public-table>
          </div>
          <div v-show="state.paramsType === 'return'" class="params-content">
            <n-public-table
              ref="returnPublicTable"
              :table-head-titles="state.returnTableHeadTitles"
              :tableData="state.returnTableData"
              :showPagination="false"
              :tableHeight="state.apiTableHeight"
            />
          </div>
        </section>
        <div
          v-show="state.activeName === 'backData'"
          class="test-api-box-bottom"
          v-loading="state.backDataLoading"
        >
          <div class="title">请求返回</div>
          <div class="back-content">
            <div class="content-left">
              <div class="code">Code</div>
              <div class="code-number">{{ state.statusCode }}</div>
            </div>
            <div class="content-right">
              <div class="back-title">响应内容</div>
              <div class="back-data-box">
                <div class="back-data">
                  <div v-show="state.showJsonViewer">
                    <json-viewer :value="state.treeData" :expand-depth="5" copyable boxed sort />
                    <!-- <JsonViewer :value="state.treeData" :expand-depth="5" copyable boxed sort /> -->
                  </div>

                  <div v-show="!state.showJsonViewer">
                    {{ state.errorMessage }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
  import { onMounted, onUnmounted, reactive, ref, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { ElNotification } from 'element-plus'
  import ForceGraph3D from '3d-force-graph'
  import { Model } from '@antv/x6'
  import insertCss from 'insert-css'
  import { DagreLayout, ForceLayout, GridLayout } from '@antv/layout'
  import FlowGraph from './flow-2D/index'
  import * as THREE from 'three'
  import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js'
  import { CSS2DObject, CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer'
  import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer'
  import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
  import api, { dataApplication } from '@/api/index'
  import blocks from './blocks.json'
  import moduleName from '@/components/ModuleName'

  const store = useStore()
  const publicTable = ref()
  const publicTableModel = ref()
  const publicTablePreview = ref()
  const publicTableFile = ref()
  const requestPublicTable = ref()
  const returnPublicTable = ref()
  const flow = ref(null)
  const btnTit = ref('暂停')
  const puaseText = ref('stopBtn')
  const btnScreen = ref('退出')
  const screenText = ref('screen_out')
  let Graph3D
  let Graph2D

  let isRotationActive = true //是否默认旋转
  let isRotationHoverActive = true //是否默认旋转
  let isScreenActive = true //是否是全屏
  let distance = 1000
  let scal = 1 //放大系数
  const data = {
    graphData: {},
    timer: null,
  }
  // const scene = new THREE.Scene()
  const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
  const state = reactive({
    buttonAuthList: [],
    buttonActive: 0,
    showPop: false,
    isDataList: true,
    isFileList: false,
    showModlePop: false,
    showApiPop: false,
    showFilePop: false,
    activeName: 'first',
    filterSearch: {
      keyword: '',
    },
    mapData: {
      nodes: [],
      relationship: [],
    },
    isLoading: false,
    firstCont: true,
    resizeFn: null,
    graph: {},
    chooseNode: {},
    tableId: '',
    id: '',
    tableOpts: [],
    form: {
      database: '',
      name: '',
      dataStructureType: '',
      dataStructureTypeName: '',
      datasourceType: '',
      host: '',
      port: '',
      userName: '',
      password: '',
      description: '',
    },
    modelForm: {
      projectName: '',
      layerName: '',
      description: '',
      createByName: '',
      createTime: '',
      updateTime: '',
    },
    apiForm: {
      projectCode: '',
      apiName: '',
      protocol: '',
      requestMethod: '',
      example: '',
      apiUrl: '',
      showUrl: '',
      apiStory: '',
      token: '',
      createByName: '',
      createTime: '',
      updateByName: '',
      updateTime: '',
      auditComments: '',
      paramColumns: [], // 自定义参数
    },
    apiId: '',

    firstButtonActive: true,
    paramsType: 'request',
    returnTableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'name', name: '元数据' },
      { prop: 'fieldTypeName', name: '类型' },
      { prop: 'fieldLength', name: '长度' },
      { prop: 'description', name: '描述' },
    ],
    returnTableData: {},
    requestTableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'name', name: '参数名称' },
      { prop: 'value', name: '参数值', slot: 'parameterValue' },
      { prop: 'fieldType', name: '参数类型' },
      { prop: 'isRequired', name: '是否必填', slot: 'isRequired' },
      { prop: 'description', name: '参数说明' },
    ],
    requestTableData: {},
    apiTableHeight: 400,

    allParamColumns: [],
    backDataLoading: false,
    treeData: {},
    showJsonViewer: false,
    statusCode: '',
    errorMessage: '',
    queryData: '',
    dataTableList: [],
    metaList: [],
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'number', name: '序号' },
      { prop: 'colName', name: '字段' },
      { prop: 'dataType', name: '类型' },
      { prop: 'length', name: '字段长度' },
      { prop: 'comment', name: '描述信息' },
    ],
    modelTableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'number', name: '序号' },
      { prop: 'cnName', name: '字段中文名' },
      { prop: 'name', name: '字段英文名' },
      { prop: 'fieldTypeName', name: '字段类型' },
    ],
    showModle: '',
    showModleId: null,
    tableHeadTitlesPreview: [],
    modelTableData: {},
    projectCode: null,
    loadingPreview: false,
    keyCunt: 0,
    tableHeight: 400,
    tableHeadTitlesFile: [],
    pagination: {
      pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
      currentPage: 1,
      pageSize: 10,
    },
    paginationFile: {
      pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
      currentPage: 1,
      pageSize: 10,
    },
    needOtherActionBar: {
      label: '设为默认',
      show: false,
    },

    fileForm: {
      sourceFileName: '',
      sourceFileType: '',
      sourceFileFormat: '',
      sourceFileSize: '',
      createTime: '',
      createByName: '',
    },
  })

  onMounted(() => {
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList
    nextTick(() => {
      getLineageByPro()
    })
  })

  onUnmounted(() => {
    clearInterval(data.timer)
    data.graphData = blocks
    FlowGraph.destroy()
    location.reload()
  })
  const setTableHeight = () => {
    state.tableHeight =
      btnScreen.value == '全屏'
        ? document.body.offsetHeight - 341
        : document.body.offsetHeight - 185
  }
  const setApiTableHeight = () => {
    state.tableHeight =
      btnScreen.value == '全屏'
        ? (state.apiTableHeight = document.body.offsetHeight - 341)
        : (state.apiTableHeight = document.body.offsetHeight - 185)
  }
  const isFullScreenFn = () => {
    isScreenActive = !isScreenActive
    btnScreen.value = `${isScreenActive ? '退出' : '全屏'}`
    screenText.value = `${isScreenActive ? 'screen_out' : 'screen'}`
  }
  const getMapParams = (num) => {
    state.buttonActive = num
    clearInterval(data.timer)
    data.graphData = blocks
    FlowGraph.destroy()
    screenText.value = `${state.buttonActive == 0 ? 'screen' : 'screen_black'}`
    state.filterSearch.keyword = ''
    getLineageByPro()
  }

  const getLineageByPro = () => {
    let params = { keyword: state.filterSearch.keyword }
    // 获取数据血缘关系
    api.assets.getLineageByPro(params).then((res) => {
      if (res.code === 'SUCCESS' && res.data.nodes.length > 0) {
        let links = []
        let nodes = []
        res.data.relationship.forEach((item) => {
          links.push({
            source: item.to,
            target: item.from,
            value: 3,
          })
        })
        res.data.nodes.map((item, index) => {
          nodes.push({
            id: item.uk,
            nId: item.id,
            nEnName: item.name,
            nName: item.cnName,
            nProjectCode: item.projectCode,
            nType: item.dbType,
            dataLineageType: item.type,
          })
        })
        data.graphData = {
          nodes,
          links,
        }
        init()
      }
    })
  }
  const init = () => {
    clearInterval(data.timer)
    // 遍历关联节点
    data.graphData.links.forEach((link) => {
      if (!link.source.neighbors) {
        const a = data.graphData.nodes.filter((item, index) => {
          link.source === item.id
        })
        const b = data.graphData.nodes.filter((item, index) => {
          link.target === item.id
        })
        if (a && b) {
          a.forEach((itemA) => {
            b.forEach((itemB) => {
              !itemA.neighbors && (itemB.neighbors = [])
              !itemB.neighbors && (itemB.neighbors = [])
              itemA.neighbors.push(itemB)
              itemB.neighbors.push(itemA)

              !itemA.links && (itemA.links = [])
              !itemB.links && (itemB.links = [])
              itemA.links.push(link)
              itemB.links.push(link)
            })
          })
        }
      }
    })
    data.graphData = data.graphData
    const highlightNodes = new Set() //高亮节点
    const highlightLinks = new Set() //高亮链接
    let hoverNode = null //选中节点
    Graph3D = ForceGraph3D({ controlType: 'orbit' })(flow.value)
      .width('100vh')
      .height('100vh')
      .backgroundColor('rgba(8, 30, 68, 0.2)')
      .showNavInfo(false) //隐藏页脚
      .graphData(data.graphData) //导入数据
      // .nodeThreeObjectExtend((node) => {
      //   return highlightNodes.has(node) ? (node === hoverNode ? false : true) : true
      // })
      .nodeThreeObjectExtend(true)
      .nodeThreeObject((node) => {
        const imgTexture = new THREE.TextureLoader().load(getAssetsImages('border-circle'))
        const material = new THREE.SpriteMaterial({ map: imgTexture })
        const sprite = new THREE.Sprite(material)
        sprite.scale.set(16, 16)
        // sprite.layers.set(1)
        // return sprite
        return highlightNodes.has(node) ? (node === hoverNode ? sprite : false) : false
      })
      // .nodeAutoColorBy('user')
      .nodeRelSize(3)
      //自定义颜色
      .nodeColor((node) => {
        let color = '#79BBFF'
        switch (node.dataLineageType) {
          // switch (node.user) {
          case 'DATA_SOURCE_FILE':
            color = '#18ba72'
            break
          case 'API':
            color = '#8D7AF8'
            break
          case 'DATA_SOURCE':
            color = '#F5A623'
            //不区分输入输出
            // if (node.workType === 'REAL_TIME' && node.nodeType === 'SOURCE') {
            //   //实时输入
            //   color = '#18BA72'
            // } else if (node.workType === 'REAL_TIME' && node.nodeType === 'SINK') {
            //   //实时输出
            //   color = '#F5A623'
            // } else if (node.workType === 'OFFLINE' && node.nodeType === 'SOURCE') {
            //   //离线输入
            //   if (node.nodeDataType === 'STRUCTURED') {
            //     //结构化输入
            //     // color = '#8CD9F8'
            //     color = '#F3689A'
            //   } else if (node.nodeDataType === 'UNSTRUCTURED') {
            //     //非结构化
            //     color = '#FFFFFF'
            //   }
            // }
            break
          case 'MODEL':
            // color = '#FFFFFF'
            color = '#36AEFF'
            break
          case 'ADS':
            color = '#F59823'
            break
          default:
            color = '#A6DD82'
            break
        }
        return highlightNodes.has(node) ? '#FFD555' : color
      })
      .nodeLabel(
        // (node) => {
        //   const labelDiv = document.createElement('div')
        //   labelDiv.className = 'hover-label'
        //   labelDiv.innerHTML = `<span>${node.nName}</span><span>${node.nEnName}</span>`
        //   return labelDiv
        // },
        // <div class="hover-label">
        (node) =>
          `
                  <div style="display:flex;flex-direction: column;text-align: center;align-items: center;justify-content: center;padding:6px 12px;background: #454550; border-radius:4px;font-size:12px">
                    <span>${node.nName}</span>
                    <span >${node.dataLineageType === 'MODEL' ? node.nEnName : ''}</span>
                  </div>
                `,
      ) //节点名
      // .nodeOpacity(1)
      .linkOpacity(0.3) // 线透明度
      .linkWidth((link) => (highlightLinks.has(link) ? 4 : 1)) //线宽度
      .linkDirectionalParticles((link) => (highlightLinks.has(link) ? 4 : 0))
      .linkDirectionalParticleWidth(4)
      .onNodeHover((node) => {
        // if (
        //   (!node && !highlightNodes.size) || (node && hoverNode === node && node.nodeType !== hoverNode.nodeType && node.workType !== hoverNode.workType)
        // )
        if ((!node && !highlightNodes.size) || (node && hoverNode === node)) return
        highlightNodes.clear()
        highlightLinks.clear()
        if (node) {
          highlightNodes.add(node)
          node.neighbors?.forEach((neighbor) => highlightNodes.add(neighbor))
          node.links?.forEach((link) => highlightLinks.add(link))
        }

        hoverNode = node || null
        puaseHover()

        updateHighlight()
      })
      .onLinkHover((link) => {
        highlightNodes.clear()
        highlightLinks.clear()

        if (link) {
          highlightLinks.add(link)
          highlightNodes.add(link.source)
          highlightNodes.add(link.target)
        }

        updateHighlight()
      })
      // .linkDirectionalParticles('value') //粒子个数

      // .graphData(state.graphData) //导入数据
      // .nodeAutoColorBy('nProjectCode') //节点自动获取颜色
      // .nodeLabel((node) => `${node.nName}`) //节点名
      // .linkAutoColorBy((d) => d.projectCode) //线自动获取颜色
      // .linkOpacity(0.4) // 线透明度
      // .linkDirectionalParticles('value') //粒子个数
      // .linkDirectionalParticleSpeed((d) => d.value * 0.003) //粒子运动速度
      .onNodeClick((node) => {
        if (state.buttonAuthList?.includes('assetsManage_dataAssets_dataMap_view')) {
          const distance = 40
          const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z)
          const newPos =
            node.x || node.y || node.z
              ? { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio }
              : { x: 0, y: 0, z: distance } // special case if node is in (0,0,0)
          let timeOutNum = 1000 // 显示弹窗过渡时间
          if (state.chooseNode.id === node.id) {
            // 如果点击同一个节点，则直接弹窗
            timeOutNum = 0
          }
          state.chooseNode = node
          const nodePosition = new THREE.Vector3(node.x, node.y, node.z)
          Graph3D.cameraPosition(
            newPos, // new position
            node,
            // { x: 100, y: 0, z: 0 },
            // {
            //   x: nodePosition.x + 10,
            //   y: nodePosition.y,
            //   z: nodePosition.z,
            // },
            // lookAt ({ x, y, z })
            1000, // ms transition duration
          )
          setTimeout(() => {
            state.activeName = 'first'
            if (node.dataLineageType === 'DATA_SOURCE') {
              state.showPop = true
              state.dataTableList = []
              if (node.nType === 'KAFKA') {
                state.isDataList = false
                state.isFileList = false
                getSourceDetailFn(parseInt(node.nId))
              } else {
                state.isDataList = true
                state.isFileList = false
                getSourceDetailFn(parseInt(node.nId))
                // getSourceSelect(parseInt(node.nId))
              }
            } else if (node.dataLineageType === 'MODEL') {
              state.showModlePop = true
              state.showModle = node.nEnName
              state.showModleId = node.nId
              state.projectCode = node.nProjectCode
              setTableHeight()
              getModleDetail(parseInt(node.nId))
              getModeData(parseInt(node.nId))
            } else if (node.dataLineageType === 'API') {
              state.showApiPop = true
              state.apiId = parseInt(node.nId)
              setApiTableHeight()
              getDetail(parseInt(node.nId))
            } else if (node.dataLineageType === 'DATA_SOURCE_FILE') {
              state.showFilePop = true
              getFileDetail(parseInt(node.nId))
            }
          }, timeOutNum)
        }
      })

    Graph3D.controls().autoRotate = true
    Graph3D.controls().autoRotateSpeed = 0.5
    set3DBg()

    const renderScene = new RenderPass(scene, Graph3D.camera())
    const bloomPass = new UnrealBloomPass()
    bloomPass.strength = 9 //表示泛光的强度，值越大明亮的区域越亮，较暗区域变亮的范围越广
    bloomPass.radius = 1 //表示泛光散发的半径
    bloomPass.threshold = 0.1 //表示产生泛光的光照强度阈值，如果照在物体上的光照强度大于该值就会产生泛光
    const composer = Graph3D.postProcessingComposer()
    // const composer = new EffectComposer(Graph3D.renderer())
    // composer.addPass(renderScene)
    composer.addPass(bloomPass)
    // Graph3D.renderer().autoClear = false
    // Graph3D.camera().layers.set(1)
    // composer.renderer.render()

    // Graph3D.camera().layers.set(2)
    // Graph3D.renderer().render(scene, Graph3D.camera())
  }
  //设置3d背景
  const set3DBg = () => {
    // let path = '/src/assets/img/assets/temp_sky.png'
    let path = getAssetsImages('temp_sky_s')
    let pathT = ''
    let urls = [pathT, pathT, pathT, path, pathT, pathT]
    let materials = []
    for (let i = 0; i < urls.length; ++i) {
      let loader = new THREE.TextureLoader()
      let texture = loader.load(urls[i])
      materials.push(
        new THREE.MeshBasicMaterial({
          map: texture,
          side: THREE.BackSide,
          // wireframe: true,
          reflectivity: 1,
        }),
      )
    }
    let cube = new THREE.Mesh(new THREE.BoxGeometry(22000, 5000, 22000), materials)
    // scene.background = new THREE.CubeTextureLoader()
    //   .setPath('/src/assets/img/assets/')
    //   .load([
    //     'temp_sky.png',
    //     'temp_sky.png',
    //     'temp_sky.png',
    //     'temp_sky.png',
    //     'temp_sky.png',
    //     'temp_sky.png',
    //   ])
    // cube.layers.set(2)
    scene.add(cube)
  }
  const getAssetsImages = (name) => {
    return new URL(`/src/assets/img/assets/${name}.png`, import.meta.url).href //本地文件路径
  }
  const updateHighlight = () => {
    // trigger update of highlighted objects in scene
    Graph3D.nodeThreeObject(Graph3D.nodeThreeObject())
      .nodeColor(Graph3D.nodeColor())
      .linkWidth(Graph3D.linkWidth())
      .linkDirectionalParticles(Graph3D.linkDirectionalParticles())
  }

  //------弹窗内容

  const getSourceFileFn = (dataSourceId) => {
    api.assets
      .getFile({ id: dataSourceId, type: 'FILE' })
      .then(async (res) => {
        let { data, success } = res
        if (data && success) {
          let { file } = res.data
          await api.project.getDatasourceExcelPreview({ url: file.resourceUrl }).then((res) => {
            let { data, success } = res
            if (success) {
              if (data.length > 0) {
                state.tableHeadTitlesFile = [{ prop: 'number', name: '序号', width: 80 }]
                Object.keys(data[0]).forEach((key) => {
                  state.tableHeadTitlesFile.push({
                    prop: key,
                    name: key,
                  })
                })
              }
              data.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.fileTableData = { list: data }
            }
          })
        }
      })
      .catch(() => {
        state.isFileList = false
      })
  }
  const tablePageChangeFile = (data) => {
    state.paginationFile.currentPage = data.currentPage
    state.paginationFile.pageSize = data.pageSize
    getSourceFileFn()
  }
  // 获取数据源详情
  const getSourceDetailFn = (dataSourceId) => {
    api.project
      .getDatasourceDetail({ id: dataSourceId })
      .then((res) => {
        if (res.success) {
          res.data
            ? (state.form = res.data)
            : Object.keys(state.form).forEach((key) => (state.form[key] = ''))
        }
      })
      .catch(() => (state.isFileList = false))
  }

  // 获取数据表下拉列表
  const getSourceSelect = (dataSourceId) => {
    state.id = dataSourceId
    api.dataManagement.getSourceSelect({ dataSourceId }).then((res) => {
      if (res.code === 'SUCCESS') {
        if (res.data.length > 0) {
          state.tableOpts = []
          state.tableId = ''
          res.data.forEach((item) => {
            state.tableOpts.push({ key: item, value: item })
          })
          state.tableId = res.data[0]
          getSourceStructure(state.tableId)
        }
      }
    })
  }

  // 获取数据表下拉列表
  const getSourceStructure = (val) => {
    let data = {
      id: state.id,
      tableName: val,
    }
    api.dataManagement
      .getSourceStructure(data)
      .then((res) => {
        if (res.code === 'SUCCESS' && res.data.length > 0) {
          state.dataTableList = res.data.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          state.tableData = { list: state.dataTableList }
        }
      })
      .catch(() => {})
  }

  // 查询模型详情
  const getModleDetail = (id) => {
    api.model
      .getModalDetail({ id })
      .then((res) => {
        if (res.success) {
          state.modelForm = res.data
        }
      })
      .catch(() => (state.showModlePop = false))
  }
  //查询模型数据预览
  const getDataPreview = () => {
    let data = {
      condition: {
        id: parseInt(state.showModleId),
        // projectCode: state.projectCode,
        // modelName: state.showModle,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    state.loadingPreview = true

    state.previewTableData = []
    state.tableHeadTitlesPreview = []
    api.model
      .getDataWithProject(data)
      .then((res) => {
        let { success, data } = res
        state.loadingPreview = false
        if (success) {
          state.tableHeadTitlesPreview = [{ prop: 'number', name: '序号', width: 80 }].concat(
            state.allParamColumns,
          )
          if (data.list && data.list.length > 0) {
            let allData = { list: [] }

            data.list.map((item, index) => {
              let _itemKeys = []
              Object.keys(item).forEach((_item) => {
                _itemKeys.push(_item.toLowerCase())
              })
              let _object = {}
              state.tableHeadTitlesPreview.forEach((key) => {
                if (_itemKeys.includes(key.prop.toLowerCase())) {
                  _object[key.prop] = item[key.prop.toLowerCase()]
                } else {
                  _object[key.prop] = null
                }
              })
              _object['number'] = index + 1
              allData.list.push(_object)
            })

            data.list = allData.list
          }
          state.keyCunt++
          state.previewTableData = data
        }
      })
      .catch(() => {
        state.loadingPreview = false
      })
  }
  // 表格操作变化
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    getDataPreview()
  }
  // 查询某个模型的元数据列表
  const getModeData = (id) => {
    api.model
      .getModeData({ id })
      .then((res) => {
        if (res.success) {
          state.metaList = res.data
          res.data.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          state.modelTableData = { list: res.data }
          let _allParamColumns = []
          if (res.data && res.data.length > 0) {
            res.data.forEach((item) => {
              _allParamColumns.push({
                prop: item.name,
                name: item.name,
              })
            })
            state.allParamColumns = _allParamColumns
            state.tableHeadTitlesPreview = [{ prop: 'number', name: '序号', width: 80 }].concat(
              state.allParamColumns,
            )
            getDataPreview()
          }
        }
      })
      .catch(() => {})
  }
  // 获取API详情
  const getDetail = (id) => {
    api.dataService
      .getApiDetail({ id })
      .then((res) => {
        let { data, success } = res
        if (success) {
          Object.keys(state.apiForm).forEach((key) => {
            state.apiForm[key] = data[key]
          })
          // state.apiForm['showUrl'] =
          //   data.protocol.toLowerCase() + '://' + data.serverUrl + data.apiUrl // 全路径
          state.apiForm['showUrl'] = data.serverUrl + data.apiUrl // 全路径

          // 返回参数
          state.returnTableData = { list: data.paramColumns }
          //请求参数
          state.allParamColumns = JSON.parse(JSON.stringify(data.paramColumns)) //深拷贝返回参数

          state.allParamColumns.map((item) => {
            return Object.assign(item, { value: '', isRequired: false })
          })
          state.allParamColumns = [
            {
              name: 'pageNum',
              value: 1,
              fieldType: 'Number',
              isRequired: true,
              description: '当前页',
              type: 'number',
            },
            {
              name: 'pageSize',
              value: 10,
              fieldType: 'Number',
              isRequired: true,
              description: '每页多少条,示例值(10)',
              type: 'number',
            },
            ...state.allParamColumns,
          ]
          state.requestTableData = { list: state.allParamColumns }
        }
      })
      .catch(() => {
        state.showApiPop = false
      })
  }
  // 获取文件详情
  // 文件大小转换
  const getfilesize = (size) => {
    if (!size) return ''
    var num = 1024.0 //byte
    if (size < num) return size + 'B'
    if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + 'K' //kb
    if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + 'M' //M
    if (size < Math.pow(num, 4)) return (size / Math.pow(num, 3)).toFixed(2) + 'G' //G
    return (size / Math.pow(num, 4)).toFixed(2) + 'T' //T
  }
  const getFileDetail = (id) => {
    api.assets
      .getFilDetail({ id })
      .then((res) => {
        let { data, success } = res
        if (success) {
          Object.keys(state.fileForm).forEach((key) => {
            state.fileForm[key] = data[key]
            if (key === 'sourceFileSize') {
              state.fileForm[key] = getfilesize(data[key])
            }
          })
        }
      })
      .catch(() => {
        state.showFilePop = false
      })
  }
  // 查看 返回/请求 参数
  const getRequestParams = () => {
    state.paramsType = 'request'
    state.firstButtonActive = true
  }
  const getReturnParams = () => {
    state.paramsType = 'return'
    state.firstButtonActive = false
  }
  //切换
  const handleClick = (id) => {
    state.activeName = id
    if (id === 'backData') {
      testApi(state.apiId)
    }
  }

  //api示例
  const testApi = (id) => {
    // 验证参数
    let passed = true
    let data = {}
    state.allParamColumns.forEach((item) => {
      data[item.name] = item.value ? item.value : null
      if (item.isRequired && !item.value) {
        passed = false
      }
    })
    if (passed) {
      state.backDataLoading = true
      let _data = []

      state.allParamColumns.forEach((item) => {
        if (item.id) {
          _data.push({
            metadataId: item.id,
            name: item.name,
            textValue: item.value,
          })
        } else {
          _data.push({
            name: item.name,
            textValue: item.value,
          })
        }
      })
      let _object = { apiParamForms: _data, id, token: state.apiForm.token }
      api.dataService
        .apiTest(_object)
        .then((res) => {
          let { success } = res
          state.backDataLoading = false
          if (success) {
            state.statusCode = 200
            state.showJsonViewer = true
            state.treeData = res
            ElNotification({
              title: '提示',
              message: '测试成功',
              type: 'success',
            })
          }
        })
        .catch((err) => {
          state.backDataLoading = false
          state.statusCode = '失败'
          state.showJsonViewer = false
          state.treeData = {}
          state.errorMessage = err.message
        })
    } else {
      ElNotification({
        title: '提示',
        message: '参数信息-请求参数-必填字段丢失',
        type: 'error',
      })
    }
  }

  const closeModle = (modelName) => {
    state[modelName] = false
    isRotationHoverActive = true
  }
  const puaseClick = (e) => {
    isRotationActive = !isRotationActive
    btnTit.value = `${isRotationActive ? '暂停' : '旋转'}`
    puaseText.value = `${isRotationActive ? 'stopBtn' : 'spinBtn'}`
    Graph3D.controls().autoRotate = isRotationActive
  }
  const puaseHover = () => {
    if (isRotationActive) {
      isRotationHoverActive = !isRotationHoverActive
      Graph3D.controls().autoRotate = isRotationHoverActive
    }
  }

  const scrollWheel = (e) => {
    let ev = e || window.event
    Graph3D.controls().autoRotate = false
    if (isRotationActive) {
      isRotationActive = false
      btnTit.value = `旋转`
      puaseText.value = `spinBtn`
    }
    Graph3D.controls().maxDistance = 4000
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    &-box {
      position: relative;
      padding: 20px 10px 10px;
      overflow: hidden;
      border-radius: 4px;
      .map-box {
        position: relative;
        width: 100%;
        height: calc(100% - 20px);
      }
      .full-map {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1002;
        width: 100%;
        height: 100%;
        background-color: #fff;
      }
    }

    .header {
      position: relative;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 0 10px;
      h2 {
        display: inline-block;
        margin: 0;
        color: #697a9a;
        font-size: 14px;

        img {
          width: 14px;
          margin-right: 4px;
          vertical-align: middle;
        }
      }
      .el-form {
        display: inline-block;
        float: right;
      }
    }
    .btn-3d {
      position: absolute;
      top: 20px;
      left: 20px;
      z-index: 5;
      img {
        width: 34px;
        height: 34px;
      }
    }
    .tools {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 5;
      display: flex;
      > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 47px;
        margin-left: 10px;
        color: #fff;
        font-size: 12px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.4);
        border-radius: 4px;
        img {
          width: 20px;
        }
      }
      > div:hover {
        border: 1px solid rgba(255, 255, 255, 0.7);
        cursor: pointer;
      }
    }
    .tools-2d {
      > div {
        color: #666666;
        background: #f2f3f6;
        border: 1px solid #e1e1e1;
      }
      > div:hover {
        border: 1px solid #c8c9cc;
        cursor: pointer;
      }
    }
    .displayPanel {
      position: absolute;
      right: 20px;
      bottom: 20px;
      z-index: 5;

      div {
        margin-top: 10px;
        color: #fff;
        font-size: 12px;
      }
      .point {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #f3689a;
        border-radius: 100px;
        border-radius: 100px;
      }
      .point2 {
        background: #ffffff;
      }
      .point-other {
        background: #b8c8e5;
      }
      .point3 {
        background: #18ba72;
      }
      .point4 {
        background: #36aeff;
      }
      .point5 {
        background: #8d7af8;
      }
      .point6 {
        background: #f5a623;
      }
      .point7 {
        background: #18ba72;
      }
      .point8 {
        background: red;
      }
    }
    .displayPanel-other {
      > div {
        color: #666666;
      }
    }
    .flow {
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: url('/src/assets/img/assets/sky.jpg') no-repeat;
      background-size: 100%;
      border-radius: 4px;
    }
    .flow-2d {
      width: 100%;
      height: 100%;
      overflow: hidden;
      border-radius: 4px;
    }
    .loading-box {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
      text-align: center;
      background-color: rgba(0, 0, 0, 0.85);
      :deep(.nancalui-icon__container) {
        position: absolute;
        top: calc(50% - 16px);
        display: inline-block;
        width: 50px;
        height: 50px;
        .icon-refresh:before {
          font-size: 24px;
        }
      }
    }

    .noData {
      text-align: center;
      img {
        width: 213px;
        margin: 153px 0 20px;
      }
      h3 {
        color: #999;
        font-weight: 300;
        font-size: 12px;
      }
    }
    .params-info {
      display: flex;
      padding: 0 0 10px 0;
      :deep(.nancalui-button) {
        width: 88px;
        height: 32px;
        margin-left: 0;
        border: 1px solid #e1e1e1;
        border-radius: 0;
        &.active {
          color: $themeBlue;
          background: #f0f7ff;
          border: 1px solid $themeBlue;
        }
        > span {
          font-size: 13px;
        }
      }
      div {
        padding-right: 20px;
        cursor: pointer;
      }
    }
    .params-content {
      .common-table {
        font-size: 200px;
        :deep(.el-table) {
          .el-table__cell {
            .required {
              color: #f54446;
            }
            .required-input .el-input__wrapper {
              border: 1px solid #f54446;
              &.is-focus {
                box-shadow: none;
              }
            }
          }
        }
      }
    }
    .test-api-box-bottom {
      height: 100%;
      font-size: 12px;
      .title {
        height: 57px;
        padding: 10px 0;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
      }
      .back-content {
        display: flex;
        height: calc(100% - 80px);
        font-weight: 400;
        .content-left {
          width: 260px;
          .code {
            padding-bottom: 5px;
            line-height: 20px;
            border-bottom: 1px solid #e1e1e1;
          }
          .code-number {
            padding-top: 20px;
          }
        }
        .content-right {
          flex: 1;
          .back-title {
            padding-bottom: 5px;
            line-height: 20px;
            border-bottom: 1px solid #e1e1e1;
          }
          .back-data-box {
            height: calc(100% - 350px);
            padding: 10px 0;
            .back-data {
              height: 100%;
              overflow: auto;

              & > div {
                height: 100%;
              }
              :deep(.jv-container.jv-light) {
                height: calc(100% - 20px);
                padding: 10px 0;
                background-color: #f4f4f4;
                .jv-code {
                  overflow: auto !important;
                  &.boxed {
                    height: calc(100% - 100px);
                    max-height: 600px;
                  }
                }
              }
            }
          }
        }
      }
    }
    .source-h2 {
      margin: 20px 0 0;
      color: #333;
      font-size: 13px;
    }

    .map-info {
      display: flex;
      padding: 1px 0 10px 0;
      :deep(.nancalui-button) {
        width: 88px;
        height: 32px;

        > span {
          font-size: 13px;
        }
      }
      .nancalui-button:nth-child(1) {
        border-right: 0;
      }
      .active {
        color: $themeBlue;
        background: #f0f7ff;
        border: 1px solid $themeBlue !important;
      }
      .res-btn.active ~ .nancalui-button {
        border-left: 0;
      }
      .col-btn.active ~ .nancalui-button {
        border-right: 0;
      }

      .nancalui-button + .nancalui-button {
        margin-left: 0;
      }
    }
    :deep(.nancalui-tabs) {
      margin-bottom: 20px;
    }
  }
  .hover-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    color: red;
    font-size: 12px;
    text-align: center;
    background: #454550;
    border-radius: 4px;
  }
  .n-drawer {
    position: fixed;
    top: 0;
    right: 0;
    z-index: -99;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 0;
    transition: all ease 0.1s;
    &-content {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 100%;
      transition: all linear 0.2s;
    }
    &.active {
      z-index: 1005;
      opacity: 1;
      .n-drawer-content {
        width: 680px;
        padding: 0 20px;
        background-color: #fff;
        .drawer-content-name {
          position: relative;
          left: -20px;
          width: calc(100% + 40px);
          height: 38px;
          padding: 0 20px;
          line-height: 38px;
          background-color: #f7f8fa;
          .module-name {
            position: relative;
            padding-left: 10px;
          }
          .close {
            position: absolute;
            top: 0;
            right: 20px;
            bottom: 0;
            width: 16px;
            height: 16px;
            margin: auto;
            cursor: pointer;
          }
        }
        &.checked {
          width: 980px;
        }
      }
    }
  }
  .sm-drawer {
    top: 90px;
    right: 20px;
    height: calc(100% - 120px);
  }
  @keyframes ant-line {
    to {
      stroke-dashoffset: -1000;
    }
  }
</style>
