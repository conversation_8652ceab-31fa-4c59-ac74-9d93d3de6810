<template>
    <div class="content">
        <onlineStandard :info="state.onlineStandardInfo" :apis="apis" isPolling @close="closeOnlineStandardFn" />
    </div>
</template>
<script setup>
import * as api from './api.js';
import onlineStandard from '@/views/workbench/dataAnnotation/taskOverview/onlineStandard.vue';
// 通过原生方式获取url参数
const query = location.search.substr(1); // 获取url中"?"符后的字串
const vars = query.split("&"); // 用"&"分隔字符串，获得类似name=xiaoli这样的元素数组
const obj = {};
for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split("="); // 用"="分隔字符串，获得类似name和xiaoli这样的元素
    if (typeof (obj[pair[0]]) == "undefined") {
        obj[pair[0]] = pair[1]; // 赋值
    } else if (typeof (obj[pair[0]]) == "string") {
        let arr = [obj[pair[0]], pair[1]]; // 如果属性名已经存在了，那么把值转换为数组
        obj[pair[0]] = arr; // 赋值
    } else {
        obj[pair[0]].push(pair[1]); // 如果属性名已经存在了，那么直接添加值
    }
}
obj.authorization = obj.authorization.replace(/%20/g, ' ')
const apis = {};
for (const key in api) {
    const value = api[key];
    for (const key2 in value) {
        const fn = value[key2];
        value[key2] = (data) => {
            return fn(data, obj.authorization)
        }
    }
    apis[key] = value;
}
const state = reactive({
    onlineStandardInfo: {
        id: obj.id,
        taskId: obj.taskId,
    },
})
onMounted(() => {
})
const closeOnlineStandardFn = () => {
    router.push({ path: '/workbench/dataAnnotation/taskOverview' });
}
</script>
<style lang="scss" scoped>
.content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
}
</style>