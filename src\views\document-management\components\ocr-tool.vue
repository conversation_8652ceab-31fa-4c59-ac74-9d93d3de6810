<template>
  <section class="ocr-box">
    <div class="ocr-btn">
      <n-button :loading="state.loading" @click="screenshot" color="primary" variant="solid">
        <SvgIcon icon="icon-scan" class="nc-m-r-4" />
        OCR识别</n-button
      >
      <span>点击识别后，框选非结构化数据中需要识别的区域</span>
    </div>
    <div class="ocr-res">
      <section id="res-content" class="ocr-res-content">{{ state.response }}</section>
      <n-button @click="onCopy" color="primary">
        <SvgIcon icon="icon-new-copy" class="nc-m-r-4" />
        复制</n-button
      >
    </div>
  </section>
</template>
<script setup>
  import ScreenShot from 'js-web-screen-shot'
  import api from '@/api/index'
  import { getCurrentInstance, reactive } from 'vue'
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    loading: { type: Boolean, default: false },
    type: { type: String, default: 'excel' },
  })
  const state = reactive({
    response: '',
    loading: false,
    imgSrc: '',
  })
  function screenshot() {
    state.loading = true
    canvasToImg()
    const { width, height } = document.getElementById('preview-pint').getBoundingClientRect()
    const config = {
      enableWebRtc: false,
      level: 21,
      canvasWidth: width - 20,
      canvasHeight: height - 20,
      position: { left: 245, top: 180 },
      loadCrossImg: true,
      writeBase64: false,
      imgSrc: state.imgSrc,
      // imgAutoFit: true,
      hiddenToolIco: {
        square: true,
        round: true,
        rightTop: true,
        brush: true,
        mosaicPen: true,
        text: true,
        separateLine: true,
        undo: true,
        save: true,
      },
      cropBoxInfo: { x: 0, y: 20, w: 300, h: 100 },
      // screenShotDom: document.getElementById('preview-pint'),
      completeCallback: async ({ base64, cutInfo }) => {
        const file = base64ToFile(base64, 'image.png')
        let formData = new FormData() // 创建FormData对象
        formData.append('file', file)
        state.loading = true
        emit('changeLoad', true)
        const response = await api.documentManage.sendOcr(formData)

        if (response.success) {
          state.response = response.data
        }
        state.loading = false
        emit('changeLoad', false)
      },
      triggerCallback: ({ code, msg, displaySurface, displayLabel }) => {
        if (code === 0) {
          state.loading = false
        }
      },
    }
    nextTick(() => {
      setTimeout(async () => {
        new ScreenShot(config)
      }, 100)
    })
  }
  // 设置canvas的背景色,并且转为base64图片
  function canvasToImageWithBackground(canvas, backgroundColor) {
    // 创建一个新的canvas，以避免修改原始canvas
    let newCanvas = document.createElement('canvas')
    let ctx = newCanvas.getContext('2d')
    // pdf一整张滚动，需要计算可视范围画布
    if (props.type === 'pdf') {
      const { height } = document.getElementsByClassName('pdf-content')[0].getBoundingClientRect()
      const { height: totalH, y } = canvas.getBoundingClientRect()
      const sc = canvas.height / totalH
      // 设置新canvas的尺寸与原canvas相同
      newCanvas.width = canvas.width
      newCanvas.height = sc * (height - 20)
      // 绘制背景色
      ctx.fillStyle = backgroundColor
      ctx.fillRect(0, 0, newCanvas.width, newCanvas.height)
      // 将可视范围画布h重画
      ctx.drawImage(
        canvas,
        0,
        (186 - y) * sc,
        canvas.width,
        sc * (height - 20),
        0,
        0,
        canvas.width,
        sc * (height - 20),
      )
    } else {
      newCanvas.width = canvas.width
      newCanvas.height = canvas.height
      // 绘制背景色
      ctx.fillStyle = backgroundColor
      ctx.fillRect(0, 0, newCanvas.width, newCanvas.height)
      // 将原canvas的内容绘制在新canvas上
      ctx.drawImage(canvas, 0, 0)
    }
    // 将canvas转换为图片
    const image = newCanvas.toDataURL('image/png')
    return image
  }
  function cropImage(image, x, y, width, height) {
    let canvas = document.createElement('canvas')
    let ctx = canvas.getContext('2d')
    canvas.width = width
    canvas.height = height
    ctx.drawImage(image, x, y, width, height, 0, 0, width, height)
    return canvas.toDataURL() // 如果需要可以返回截取后的图像
  }
  function canvasToImg() {
    let imgUrl = ''
    if (props.type.includes('image')) {
      const img = document.getElementById('preview-pint')?.getElementsByTagName('img')[0]
      const { height: totalH } = img
      const { y } = document
        .getElementById('preview-pint')
        ?.getElementsByClassName('img-Preview')[0]
        .getBoundingClientRect()
      const { height } = document
        .getElementById('preview-pint')
        ?.getElementsByClassName('img-content')[0]
        .getBoundingClientRect()
      const sc = img.naturalHeight / totalH
      let croppedImage = cropImage(img, 0, (176 - y) * sc, img.naturalWidth, sc * height) // 参数：x, y, width, height
      imgUrl = croppedImage
    } else {
      const canvas = document.getElementById('preview-pint')?.getElementsByTagName('canvas')[0]
      if (canvas) {
        imgUrl = canvasToImageWithBackground(canvas, 'white')
      }
    }
    state.imgSrc = imgUrl
    state.loading = false
  }
  function onCopy() {
    const txtDom = document.getElementById('res-content')
    try {
      let selection = window.getSelection()
      if (selection.rangeCount > 0) selection.removeAllRanges()
      let range = document.createRange()
      range.selectNodeContents(txtDom)
      selection.addRange(range)
      document.execCommand('copy')
      selection.removeAllRanges()
      proxy.$message.success('复制成功')
      return Promise.resolve({ success: true, message: '复制成功' })
    } catch (error) {
      proxy.$message.error('复制失败')
      return Promise.resolve({ success: false, message: '复制失败' })
    }
  }
  function base64ToFile(base64, filename) {
    // 将base64的数据部分提取出来
    const parts = base64.split(';base64,')
    const contentType = parts[0].split(':')[1]
    const raw = window.atob(parts[1])

    // 将原始数据转换为Uint8Array
    const rawLength = raw.length
    const uInt8Array = new Uint8Array(rawLength)
    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i)
    }

    // 使用Blob创建File对象
    const blob = new Blob([uInt8Array], { type: contentType })
    return new File([blob], filename, { type: contentType })
  }
  const emit = defineEmits(['changeLoad'])
</script>
<style lang="scss" scoped>
  .ocr-box {
    position: relative;
    z-index: 99;
    box-sizing: border-box;
    height: 174px;
    margin-top: 10px;
    padding: 16px;
    background-color: #fff;
    .ocr-btn {
      span {
        margin-left: 12px;
        color: #a8abb2;
      }
    }
    .ocr-res {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 76px;
      margin-top: 24px;
      &-content {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        margin-right: 12px;
        padding: 8px;
        overflow-y: scroll;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }
      .nancalui-button {
        height: 100%;
      }
    }
  }
</style>
