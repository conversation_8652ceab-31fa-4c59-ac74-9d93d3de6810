<template>
  <n-modal
    v-model="showDirDialog"
    title="移动"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <!-- 选择目录 -->
        <n-form-item label="选择目录" field="directoryId">
          <DirSelect
            v-model:directoryId="state.syncForm.directoryId"
            :data="$attrs.treeList"
            :filter-id="filterId"
            :disable-checked="disableChecked"
            :key="filterId"
            ref="dirSelectRef"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="confirm">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import api from '@/api/index'
  import DirSelect from './dirSelect.vue'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const dirSelectRef = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({
    currentData: null,
    currentNode: null,
    syncForm: {
      directoryId: null,
    },
    syncRules: {
      directoryId: [
        {
          required: true,
          message: '请选择目录',
          trigger: 'blur',
          type: 'number',
        },
      ],
    },
  })
  // 递归校验父级属性,并返回对应id
  function containsValueInAncestors(obj, propName, value) {
    const { data } = obj
    // 检查当前对象是否包含该属性且属性值等于目标值
    if (data.hasOwnProperty(propName) && data[propName] === value) {
      return data.id
    }

    // 如果当前对象有父级对象，继续递归检查
    if (obj.hasOwnProperty('parent') && obj.parent) {
      return containsValueInAncestors(obj.parent, propName, value)
    }

    return
  }

  // 递归校验子级属性
  function containsValueInProperties(obj, propName, value) {
    // 辅助函数，用于检查属性值是否为对象或数组
    const isObjectOrArray = (val) => val && (typeof val === 'object' || typeof val === 'function')

    // 遍历对象的所有属性
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const prop = obj[key]
        // 检查当前属性是否是我们要找的属性，并且值是否包含目标值
        if (key === propName && prop === value) {
          return true // 找到目标值，返回 true
        }
        // 如果当前属性是对象或数组，递归检查
        if (isObjectOrArray(prop)) {
          const found = containsValueInProperties(prop, propName, value)
          if (found) {
            return true // 在子级中找到目标值，返回 true
          }
        }
      }
    }

    // 如果当前对象及其子级中都没有找到目标属性包含目标值，返回 false
    return false
  }
  const filterId = computed(() => {
    // debugger
    switch (state.currentData.type) {
      case 'JOB':
        return containsValueInAncestors(state.currentNode, 'type', 'WORKFLOW')
      case 'DIRECTORY':
        // 子级是否包含WORKFLOW、JOB
        const isContainsJOB = containsValueInProperties(state.currentData, 'type', 'JOB')
        if (isContainsJOB) {
          return containsValueInAncestors(state.currentNode, 'type', 'WORKFLOW')
        }
        return
      case 'WORKFLOW':
        return

      default:
        return
    }
  })

  const disableChecked = (node) => {
    return (
      (() => {
        switch (state.currentData.type) {
          case 'JOB':
            return
          case 'DIRECTORY':
            // 子级是否包含WORKFLOW、JOB
            const isContainsJOB = containsValueInProperties(state.currentData, 'type', 'JOB')
            const isContainsWORKFLOW = containsValueInProperties(
              state.currentData,
              'type',
              'WORKFLOW',
            )
            if (isContainsJOB || isContainsWORKFLOW) {
              return containsValueInAncestors(node, 'type', 'WORKFLOW')
            }
            return
          case 'WORKFLOW':
            return containsValueInAncestors(node, 'type', 'WORKFLOW')
          default:
            return
        }
      })() || containsValueInAncestors(node, 'id', state.currentData.id)
    )
  }
  // 取消
  const cancel = () => {
    showDirDialog.value = false
  }
  // 确认
  const confirm = () => {
    if (state.syncForm.directoryId === null) {
      createDir()
    } else {
      syncForm.value.validate((val) => {
        if (val) {
          createDir()
        }
      })
    }
  }
  const createDir = () => {
    const { type, id } = state.currentData || {}
    // if (!id) return ElMessage.error('请选择目录')
    api.offlineJob
      .moveDirectory({
        id,
        toDir: state.syncForm.directoryId,
      })
      .then((res) => {
        emit('success')
        showDirDialog.value = false
        ElMessage.success('移动成功')
      })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.currentData = null
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(data, node) {
      resetForm()
      state.syncForm.directoryId = data.pid
      state.currentData = data
      state.currentNode = node
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
  }
</style>
