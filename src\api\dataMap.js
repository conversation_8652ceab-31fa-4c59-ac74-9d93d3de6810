import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 项目获取
export const getProject = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/load`,
    method: 'post',
    data,
  })
}

// 项目保存
export const saveProject = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/save`,
    method: 'post',
    data,
  })
}

// 项目修改
export const updateProject = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/update`,
    method: 'post',
    data,
  })
}

// 项目关系获取
export const getProjectReleation = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/releation/page/search`,
    method: 'post',
    data,
  })
}

// 项目关系保存
export const saveProjectReleation = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/releation/save`,
    method: 'post',
    data,
  })
}

// 项目关系修改
export const updateProjectReleation = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/releation/update`,
    method: 'post',
    data,
  })
}

// 项目关系删除
export const deleteProjectReleation = (obj) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/releation/remove/${obj.id}`,
    method: 'post',
  })
}

// 获取数据列表
export const getConfList = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/panorama/conf/load`,
    method: 'post',
    data,
  })
}

export const saveConfTable = (data, isTableSave) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/panorama/conf${isTableSave ? '/table/save' : '/save'}`,
    method: 'post',
    data,
  })
}

// 获取血缘关系图
export const getBloodMap = (data) => {
  return request({
    url: `${URL.ASSETS}/ledata/assets/project/map/load`,
    method: 'post',
    data,
  })
}
