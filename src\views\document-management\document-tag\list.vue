<template>
  <div class="container">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">非结构化数据编号：</span>
          <n-input v-model="tableState.filterSearch.ruleCode" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">非结构化数据名称：</span>
          <n-input v-model="tableState.filterSearch.name" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">密级：</span>
          <n-select v-model="tableState.filterSearch.confidentialityLevel" :allow-clear="true">
            <n-option
              v-for="item in tableState.secretList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>非结构化数据分类</div>
        </div>
        <categoryTree @node-click="clickFn" />
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onMark" color="primary" variant="solid">
          <SvgIcon icon="icon-upload" class="nc-m-r-4" />
          批量打标</n-button
        >
        <div class="table" v-loading="tableState.isLoad">
          <CfTable
            ref="multipleTableRef"
            actionWidth="150"
            :isNeedSelection="true"
            :table-head-titles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch()
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch(true)
              },
            }"
          >
            <template #securityList="{ row }">
              <level-tag
                :bgColor="item.bgColor"
                :borderColor="item.borderColor"
                :color="item.color"
                v-for="item in tableState.securityList"
                v-show="row.confidentialityLevelName === item.name"
                :key="item.name"
                >{{ item.name }}</level-tag
              >
            </template>
            <template #versionHeader="{ column }">
              <n-tooltip content="MAIN：最新版本 V?：历史版本" position="tl">
                <template #content>
                  <span>MAIN：最新版本<br />V?：历史版本</span>
                </template>
                版本 <n-icon name="icon-helping"></n-icon>
              </n-tooltip>
            </template>
            <template #version="{ row }">
              <span class="version">{{ row.version }}</span>
            </template>
            <template #tags="{ row }">
              <cfTag :tagArr="JSON.stringify(row.tags || [])" />
            </template>
            <template #editor="{ data: { row } }">
              <n-button :disabled="!tableState.typeIds.includes(row.categoryId)" variant="text" color="primary" @click="onView(row)">预览</n-button>
              <n-button :disabled="!tableState.typeIds.includes(row.categoryId)" variant="text" color="primary" @click="onMark(row)">打标</n-button>
            </template>
          </CfTable>
        </div>
      </div>
    </section>
    <relationDocs
      v-model="tableState.relationDocsVisible"
      :id="tableState.relationDocsId"
      apiType="uploadAssoRelation"
      type="upload"
    />
    <markModal
      v-model="tableState.markVisible"
      :name="tableState.name"
      :id="tableState.markId"
      :ids="tableState.checkeIds"
      @needRefresh="onSearch"
    />
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'
  const router = useRouter()
  import relationDocs from '../components/relation-docs.vue'
  import categoryTree from '../components/categoryTree.vue'
  import api from '@/api/index'
  import markModal from './mark.vue'
  import cfTag from '@/components/cfTag'
  import { getDocumentAuthType } from '@/api/documentManage.js'
  const { proxy } = getCurrentInstance()

  const multipleTableRef = ref(null)
  const tableState = reactive({
    typeIds:[],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    tableList: [],
    filterSearch: {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
      categoryId: null,
    },
    searchData: {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    },
    securityList: [
      {
        name: '公开',
        color: '#1AA4EE',
        borderColor: 'rgba(26, 164, 238, 0.40)',
        bgColor: 'rgba(26, 164, 238, 0.08)',
      },
      {
        name: '内部',
        color: '#FE8624',
        borderColor: '#FFBA70',
        bgColor: '#FFF4E6',
      },
      {
        name: '受控',
        color: '#1E89FF',
        borderColor: '#99C9FF',
        bgColor: '#EBF4FF',
      },
      {
        name: '秘密',
        color: '#D40000',
        borderColor: '#EF7777',
        bgColor: '#FFEDED',
      },
      {
        name: '机密',
        color: '#7A0000',
        borderColor: 'rgba(122, 0, 0, 0.40)',
        bgColor: 'rgba(122, 0, 0, 0.08)',
      },
      {
        name: '核心',
        color: '#224ECD',
        borderColor: 'rgba(34, 78, 205, 0.40)',
        bgColor: 'rgba(34, 78, 205, 0.08)',
      },
    ],
    tableHeadTitles: [
      { prop: 'ruleCode', name: '非结构化数据编号', width: '200' },
      { prop: 'name', name: '非结构化数据名称', width: '240' },
      { prop: 'categoryName', name: '非结构化数据分类' },
      { prop: 'type', name: '非结构化数据类型' },
      { prop: 'confidentialityLevelName', name: '密级', slot: 'securityList' },
      { prop: 'version', name: '版本', slot: 'version', headerSlot: 'versionHeader' },
      { prop: 'tags', name: '标签', slot: 'tags', width: 300 },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    relationDocsVisible: false,
    relationDocsId: '',
    markVisible: false,
    markId: '',
    checkeIds: [],
    name: '',
    isLoad: true,
  })
  function clickFn(id) {
    tableState.filterSearch.categoryId = id
    onSearch()
  }

    const getCurrentUserTypeAuth = async () => {
    const res = await getDocumentAuthType()
    tableState.typeIds = []
    tableState.typeIds = res.data.map((item) => item.categoryId)
  }

  function getSelectionRows() {
    return multipleTableRef.value.getSelectionRows()
  }
  function clearSelection() {
    multipleTableRef.value.clearSelection()
  }
  function startSearch() {
    Object.keys(tableState.filterSearch).forEach((key) => {
      tableState.searchData[key] = tableState.filterSearch[key]
    })
    onSearch()
  }
  // 查询
  function onSearch(init = false) {
    if (init) {
      tableState.pagination.pageNum = 1
      tableState.pagination.currentPage = 1
      clearSelection()
    }

    tableState.isLoad = true
    api.documentManage
      .documentTagList({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...tableState.searchData,
          categoryId:
            tableState.filterSearch.categoryId === 'all'
              ? null
              : tableState.filterSearch.categoryId,
        },
      })
      .then((res) => {
        tableState.tableList = res.data.list
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  // 重置
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    tableState.searchData = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    onSearch(true)
  }
  // 新增非结构化数据
  function onMark(row) {
    if (row.id) {
      tableState.markId = row.id
      tableState.checkeIds = [row.id]
      tableState.name = row.name
    } else {
      tableState.markId = ''
      let rows = getSelectionRows()
      if (rows.length > 0) {
        tableState.checkeIds = rows.map((item) => item.id)
        let showNameArr = rows.filter((item, index) => index <= 1).map((item) => item.name)
        tableState.name =
          showNameArr.length === 2 ? `${showNameArr.join('、')}` : showNameArr.join('、')
      } else {
        proxy.$message.warning('请勾选数据')
        return
      }
    }
    tableState.markVisible = true
  }
  // 编辑非结构化数据
  function onEdit(row) {
    router.push({ name: 'documentUploadEdit', query: { id: row.id } })
  }
  // 发布非结构化数据
  function onPush(row) {
    api.documentManage.uploadPublish({ docId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }
  // 下架非结构化数据
  function onUnder(row) {
    api.documentManage.uploadOffline({ docId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }
  // 关联非结构化数据
  function onRelation(row) {
    tableState.relationDocsId = row.id
    tableState.relationDocsVisible = true
  }
  // 版本管理
  function onVersion(row) {
    router.push({
      name: 'documentUploadVersion',
      query: { id: row.id, name: row.name, type: 'upload' },
    })
  }
  // 预览非结构化数据
  function onView(row) {
    router.push({
      name: 'documentTagPreview',
      query: { id: row.id },
    })
  }
  // 删除非结构化数据
  function onDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条非结构化数据',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.uploadDelete({ docId: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
          }
        })
      },
    })
  }
  onMounted(() => {
    const { currentPage, pageSize } = router.currentRoute.value.query
    if (currentPage && pageSize) {
      tableState.pagination.currentPage = Number(currentPage)
      tableState.pagination.pageSize = Number(pageSize)
      onSearch(false)
    } else {
      onSearch()
    }
  })
  defineExpose({ getCurrentUserTypeAuth })
</script>
<!-- 定义导航守卫 -->
<script>
  import { defineComponent } from 'vue'
  export default defineComponent({
    beforeRouteEnter(to, from, next) {      
      next((vm) => {
        vm.getCurrentUserTypeAuth() // 调用已暴露的方法
      })
    },
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .popover-item {
    width: 100px;
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
  .publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .w-publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
