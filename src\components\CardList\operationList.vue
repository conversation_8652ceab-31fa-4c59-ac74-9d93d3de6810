<template>
  <div class="operation-control">
    <div
      v-if="state.cardOperation.indexOf('n') !== -1"
      :class="info.cardStatus === 1 ? 'operation-control-btn disabled' : 'operation-control-btn'"
      @click.prevent="operationFn('cardConfirmFn', info.cardStatus === 1)"
    >
      <span
        ><SvgIcon
          class="operation-control-btn-icon"
          icon="icon-card-distribute"
          title="确认"
        />确认</span
      >
    </div>
    <div
      v-if="state.cardOperation.indexOf('m') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardCopyFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-copy" />一键复制</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('l') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardSyncFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-sync" />同步至生产</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('f') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardRunFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-rerun" />立即执行</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('j') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardDevelopFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-dev" />开发</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('a') !== -1"
      :class="info.cardStatus === 3 ? 'operation-control-btn disabled' : 'operation-control-btn'"
      @click.prevent="operationFn('cardSendFn', info.cardStatus === 3)"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-send1" />发布</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('c') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardEditFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-edit" />编辑</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('b') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardDelFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-del" />删除</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('d') !== -1"
      :class="info.cardStatus === 5 ? 'operation-control-btn disabled' : 'operation-control-btn'"
      @click.prevent="operationFn('cardOffFn', info.cardStatus === 5)"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-off" />下架</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('e') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardSeeFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-see" />查看</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('g') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardCancelFn')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-cancel" />撤回</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('h') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardTestConnected')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-connected" />测试</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('i') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardUnbind')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-unbind" />解绑</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('k') !== -1"
      class="operation-control-btn"
      @click.prevent="operationFn('cardDataSourceApply')"
    >
      <span><SvgIcon class="operation-control-btn-icon" icon="icon-card-apply" />申请</span>
    </div>
  </div>
</template>

<script>
  import { reactive, computed } from 'vue'
  export default {
    props: {
      cardType: {
        type: Number,
        default: 1,
      },
      cardOperation: {
        type: String,
        default: '',
      },
      info: {
        type: Object,
        default: () => {},
      },
    },
    setup(props, { emit }) {
      const state = reactive({
        cardType: '',
        cardOperation: '',
        info: {},
      })
      state.cardType = computed(() => {
        return props.cardType || 1
      })
      state.cardOperation = computed(() => {
        return props.cardOperation || ''
      })
      state.info = computed(() => {
        return props.info || {}
      })
      const operationFn = (name, flag = false) => {
        if (!flag) {
          emit('childOperation', { name, info: state.info })
        }
      }
      return {
        state,
        operationFn,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .operation-control {
    height: 24px;
    line-height: 24px;
    overflow: hidden;
    position: relative;
    text-align: right;

    &-btn {
      display: inline-block;
      box-sizing: border-box;
      margin-left: 20px;
      font-size: 14px;
      color: #333333;
      cursor: pointer;
      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &:hover {
        color: $themeBlue;
      }

      &-icon {
        font-size: 18px;
        cursor: pointer;
        border-radius: 4px;
        margin-right: 4px;
        // color: $themeBlue;
      }
      &.disabled {
        color: #b8b8b8;
        cursor: not-allowed;
        .operation-control-btn-icon {
          color: #b8b8b8;
          cursor: not-allowed;
        }
      }
    }
  }
</style>
