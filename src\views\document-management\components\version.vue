<template>
  <div class="container">
    <div class="cf-page-title">
      版本管理
      <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
    </div>
    <div class="table white-box nc-m-t-10 nc-p-t-16">
      <CfTable
        actionWidth="160"
        :table-head-titles="tableState.tableHeadTitles"
        :tableConfig="{
          data: tableState.tableList,
          rowKey: 'id',
        }"
        :paginationConfig="{
          total: tableState.pagination.total,
          pageSize: tableState.pagination.pageSize,
          currentPage: tableState.pagination.currentPage,
          onCurrentChange: (v) => {
            tableState.pagination.currentPage = v
            onSearch()
          },
        }"
      >
        <template #editor="{ data: { row, $index } }">
          <n-button variant="text" color="primary" @click="onView(row)">查看</n-button>
          <n-button
            variant="text"
            color="primary"
            v-if="$index || tableState.pagination.currentPage > 1"
            @click="onBack(row)"
            >回退</n-button
          >
        </template>
      </CfTable>
    </div>
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { getCurrentInstance } from 'vue'
  const { proxy } = getCurrentInstance()

  const tableState = reactive({
    tableList: [],
    pagination: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'version', name: '版本' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间' },
    ],
    docId: '',
    listApi: 'documentationVersionList',
    rollbackApi: 'documentationRollback',
    previewRoute: 'documentPreparationPreview',
  })
  // 查询
  function onSearch() {
    api.documentManage[tableState.listApi]({
      pageNum: tableState.pagination.currentPage,
      pageSize: tableState.pagination.pageSize,
      docId: tableState.docId,
    }).then((res) => {
      tableState.tableList = res.data.list
      tableState.pagination.total = res.data.total
    })
  }
  // 查看
  function onView(row) {
    router.push({
      name: tableState.previewRoute,
      query: {
        docUrl: row.docUrl,
        id: tableState.docId,
        name: tableState.name,
        version: row.version,
      },
    })
  }
  // 回退
  function onBack(row) {
    proxy.$MessageBoxService.open({
      title: '回退确认',
      content: `确认回退到${row.version}版本吗？`,
      save: () => {
        api.documentManage[tableState.rollbackApi]({
          docId: tableState.docId,
          version: row.version,
        }).then((res) => {
          if (res.success) {
            proxy.$message.success('回退成功！')
            onSearch()
          }
        })
      },
    })
  }
  const router = useRouter()
  function closeFn() {
    router.go(-1)
  }
  onMounted(() => {
    const { id, name, type } = router.currentRoute.value.query
    tableState.docId = id
    tableState.name = name
    if (type === 'upload') {
      tableState.listApi = 'uploadVersionList'
      tableState.rollbackApi = 'uploadVersionRollback'
      tableState.previewRoute = 'documentUploadPreview'
    } else if (type === 'collaborate') {
      tableState.listApi = 'collaborateVersion'
      tableState.rollbackApi = 'collaborateVersionRollback'
      tableState.previewRoute = 'documentCollaborationPreview'
    }
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .table {
    height: calc(100vh - 184px);
  }
</style>
