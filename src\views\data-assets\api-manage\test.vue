<template>
  <div class="container" v-loading="state.loading">
    <div class="cf-page-title">
      测试API接口：{{ state.formData.apiName }}
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>

    <n-splitter orientation="vertical" splitBarSize="2px" style="height: calc(100% - 56px)">
      <template v-slot:NSplitterPane>
        <n-splitter-pane size="350px" minSize="100px" style="overflow: hidden">
          <div class="white-box table-content">
            <n-input
              v-model="state.url"
              disabled
              style="width: calc(100% - 20px); margin: 10px 10px 0 10px"
            >
              <template #prepend>{{ state.formData.requestMethod }}</template>
            </n-input>
            <div class="cf-page-title" style="margin-bottom: 0">请求参数</div>
            <div class="p-con table">
              <CfTable
                :actionWidth="242"
                :table-head-titles="state.tableTitles"
                :tableConfig="{
                  data: state.formData.requestParamList,
                  rowKey: 'id',
                }"
                :showPagination="false"
              >
                <template #required="{ row }">
                  <span>{{ row.required ? '是' : '否' }}</span>
                </template>
                <template #parameterValue="{ row }">
                  <n-input
                    v-model="row.value"
                    :class="row.required ? 'required-input' : ''"
                    placeholder=""
                    @focus="inputFocus($event)"
                  />
                </template>
              </CfTable>

              <div style="text-align: right">
                <n-button
                  @click="onTest"
                  color="primary"
                  variant="solid"
                  v-loading="state.testLoading"
                >
                  立即测试</n-button
                >
              </div>
            </div>
          </div>
        </n-splitter-pane>

        <n-splitter-pane style="overflow: hidden" :collapsible="true" collapseDirection="both" >
          <div class="white-box">
            <div class="cf-page-title">请求返回</div>
            <div class="p-con nc-flex">
              <div class="left">
                <div class="code">Code</div>
                <div class="code-number">{{ state.responseInfo.statusCode }}</div>
              </div>
              <div class="right">
                <div class="back-title">响应内容</div>
                <div class="back-data-box">
                  <div class="back-data">
                    <div v-show="state.responseInfo.showJsonViewer">
                      <json-viewer
                        :value="state.responseInfo.treeData"
                        :expand-depth="5"
                        copyable
                        boxed
                        sort
                      />
                    </div>
                    <div v-show="!state.responseInfo.showJsonViewer">
                      {{ state.responseInfo.errorMessage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </n-splitter-pane>
      </template>
    </n-splitter>
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { getCurrentInstance, reactive } from 'vue'
  import axios from 'axios'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const state = reactive({
    tableTitles: [
      { prop: 'column', name: '参数名称' },
      { prop: 'value', name: '参数值', slot: 'parameterValue', width: 300 },
      { prop: 'dataType', name: '参数类型' },
      { prop: 'required', name: '是否必填', slot: 'required' },
      { prop: 'comment', name: '参数说明' },
    ],
    formData: {
      apiName: '',
      requestMethod: '',
      url: '',
      responseFormat: '',
      requestParamList: [],
    },
    url: '',
    prefix: '',
    responseInfo: {
      statusCode: null,
      showJsonViewer: true,
      treeData: {},
      errorMessage: '',
    },
    loading: false,
    testLoading: false,
  })
  // 点击全选input内容
  function inputFocus(e) {
    e.target.select()
  }
  function addQueryString(params) {
    var str = ''
    for (var Key in params) {
      str += Key + '=' + params[Key] + '&'
    }
    return (state.formData.token ? '&' : '?') + str.substr(0, str.length - 1)
  }
  // 测试
  function onTest() {
    state.responseInfo = {
      statusCode: null,
      showJsonViewer: true,
      treeData: {},
      errorMessage: '',
    }
    const hasErr = state.formData.requestParamList?.some((item) => {
      return !item.value && item.required
    })
    if (hasErr) {
      proxy.$message.error('请填写必填参数')
      return
    }
    state.testLoading = true
    // 1.xhr
    let apiParam = {}
    state.formData.requestParamList?.map((item) => {
      if (item.value) {
        apiParam[item.column] = item.value
      }
    })
    const xhr = new XMLHttpRequest()
    let url = state.url
    if (state.formData.requestMethod === 'GET') {
      url += addQueryString(apiParam)
    }
    xhr.open(state.formData.requestMethod, url, true)
    if (state.formData.requestMethod === 'POST') {
      xhr.setRequestHeader('Content-Type', 'application/json')
    }
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        state.responseInfo.statusCode = xhr.status
        if (xhr.status === 200) {
          if (state.formData.responseFormat === 'CSV') {
            try {
              state.responseInfo.showJsonViewer = false
              state.responseInfo.errorMessage = JSON.parse(xhr.responseText)
              state.testLoading = false
            } catch (e) {
              // 下载文件
              const blob = new Blob([xhr.responseText], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
              })
              const link = document.createElement('a')
              const fileName = state.formData.apiName + new Date().getTime() + '.csv'
              link.download = fileName
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click()
              URL.revokeObjectURL(link.href)
              document.body.removeChild(link)
              state.testLoading = false
            }
          } else {
            state.responseInfo.showJsonViewer = true
            state.responseInfo.treeData = JSON.parse(xhr.responseText)
          }
        } else {
          state.responseInfo.showJsonViewer = false
          state.responseInfo.errorMessage = JSON.parse(xhr.responseText)
        }
        state.testLoading = false
      }
    }
    if (state.formData.requestMethod === 'GET') {
      xhr.send()
    } else {
      xhr.send(JSON.stringify(apiParam))
    }
  }
  function goBack() {
    router.go(-1)
  }
  // 请求详情
  async function getDetail(id) {
    try {
      state.loading = true
      const res = await api.documentManage.apiTestDetail({ apiId: id })

      //设置pageNumber、pageSize默认值
      res.data.requestParamList?.forEach((item) => {
        if (item.column === 'pageNumber') {
          item.value = 1
        }
        if (item.column === 'pageSize') {
          item.value = 10
        }
      })
      state.formData = res.data
    } finally {
      state.loading = false
    }
  }
  // 请求路径前缀
  async function getPrefix() {
    const res = await api.documentManage.apiPrefix()
    state.prefix = res.data
    state.url =
      state.prefix +
      state.formData.url +
      (state.formData.token && !state.formData.url.includes('?token=')
        ? `?token=${state.formData.token}`
        : '')
  }
  onMounted(async () => {
    const { id } = router.currentRoute.value.query
    await getDetail(id)
    await getPrefix()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .white-box {
    height: 100%;
  }

  .table-content {
    .table {
      height: calc(100% - 350px);
      min-height: 200px;
    }
  }
  .p-con {
    padding: 0 10px 10px 10px;
  }
  .left {
    width: 260px;
    .code {
      padding: 0 10px 10px 10px;
      border-bottom: 1px solid #e1e1e1;
    }
    .code-number {
      padding: 20px 0 0 10px;
    }
  }
  .right {
    flex: 1;
    .back-title {
      padding: 0 10px 10px 10px;
      border-bottom: 1px solid #e1e1e1;
    }
    .back-data-box {
      padding-top: 10px;
      .back-data {
        height: 245px;
        overflow: auto;
        background: #f4f4f4;
        border-radius: 2px;
        :deep(.jv-container.jv-light) {
          padding: 10px 0;
          background-color: #f4f4f4;
          :deep(.jv-code) {
            padding: 20px;
            overflow: auto;
          }
        }
      }
    }
  }
</style>
