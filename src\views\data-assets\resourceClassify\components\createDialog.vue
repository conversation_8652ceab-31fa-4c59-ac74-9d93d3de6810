<template>
  <n-modal
    v-model="showDirDialog"
    :title="state.pageType === 'add' ? '新建分类' : '编辑分类'"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item label="分类名称" field="name">
          <n-input v-model="state.syncForm.name" placeholder="请输入名称" />
        </n-form-item>
        <n-form-item label="父级分类">
          <TreeSelect
            v-model="state.syncForm.parentCode"
            :data="state.treeData?.children"
            :props="{
              label: 'name',
              value: 'code',
              children: 'children',
            }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择父级分类"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="数据过滤">
          <div style="width: 100%">
            <template v-for="(item, indx) in state.dataFilterArr" :key="item">
              <n-divider v-show="indx !== 0" content-position="center">or</n-divider>
              <div class="dataFilterBox">
                <el-input v-model="item.clause" placeholder="输入表名匹配规则，如：like “%123%”" />
                <n-button @click="addDataFilterFn" v-if="indx === 0" variant="text" color="primary"
                  >添加</n-button
                >
                <n-button @click="delDataFilterFn(indx)" v-else variant="text" color="primary"
                  >删除</n-button
                >
              </div>
            </template>
          </div>
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button
          variant="solid"
          @click.prevent="state.pageType === 'add' ? createDir() : updateDir()"
          >确 定</n-button
        >
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import {
    addResourceClassify,
    getResourceClassifyTree,
    updateResourceClassify,
  } from '@/api/resourceClassify'
  import { checkCName } from '@/utils/validate'
  import TreeSelect from './treeSelect'

  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({
    treeData: null,
    syncForm: { name: null, parentCode: null },
    pageType: 'add',
    id: null,
    dataFilterArr: [
      {
        andOr: 'or',
        clause: '',
      },
    ],
    syncRules: {
      name: [{ required: true, message: '请输入数据分类名称', trigger: 'blur' }],
      // 父级分类
      pid: [
        {
          required: true,
          message: '请选择父级分类',
          trigger: 'change',
        },
      ],
      // 分类编号
      code: [
        {
          required: true,
          message: '请输入分类编号',
          trigger: 'blur',
        },
      ],
    },
  })
  const cancel = () => {
    showDirDialog.value = false
  }

  // 新建目录
  const createDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        const params = { ...state.syncForm, screeningSqlClause: state.dataFilterArr }
        addResourceClassify(params).then(({ success }) => {
          if (!success) return
          emit('success')
          showDirDialog.value = false
          ElMessage.success('创建成功')
        })
      }
    })
  }

  // 更新目录
  const updateDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        const params = { ...state.syncForm, id: state.id, screeningSqlClause: state.dataFilterArr }
        updateResourceClassify(params).then(({ success }) => {
          if (!success) return
          emit('success')
          showDirDialog.value = false
          ElMessage.success('编辑成功')
        })
      }
    })
  }

  const _getResourceClassifyTree = () => {
    getResourceClassifyTree({
      keyword: '',
    }).then(({ data }) => {
      assignLevels(data)
      state.treeData = data
    })
  }
  function assignLevels(node, level = 0) {
    node.level = level
    level > 9 && (node.disabled = true)
    node.children?.forEach((child) => {
      assignLevels(child, level + 1)
    })
  }

  // 添加数据过滤
  const addDataFilterFn = () => {
    state.dataFilterArr.push({ code: '' })
  }

  // 删除数据过滤
  const delDataFilterFn = (indx) => {
    state.dataFilterArr.splice(indx, 1)
  }

  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.dataFilterArr = [{ andOr: 'or', clause: '' }]
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(row) {
      resetForm()
      state.pageType = 'add'
      if (row) {
        state.pageType = 'edit'
        state.id = row.id
        state.syncForm = { name: row.name, parentCode: row.parentCode }
        state.dataFilterArr = row.screeningSqlClauseList || [{ andOr: 'or', clause: '' }] // TODO 待后端返回数据过滤
      }
      showDirDialog.value = true
      _getResourceClassifyTree()
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
  }

  .dataFilterBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .nancalui-divider__text.nancalui-divider__text.is-center {
      left: 50% !important;
    }
  }
  :deep(.nancalui-divider--horizontal) {
    margin: 12px 0;
  }
</style>
