<template>
  <div class="container">
    <div class="content-box">
      <div class="content-box-title"
        >{{ state.info.projectName }}
        <div
          :class="[
            'datasourceSecret',
            secretTransition(state.info.confidentialityLevel).iconClassName,
          ]"
          >{{ secretTransition(state.info.confidentialityLevel).name }}</div
        >
        <!--        <div class="detail-back-box" @click.prevent="joinFn"> 我要加入 </div>-->
      </div>
      <div class="content-box-row">
        <div class="flex-box">
          <div class="flex-box-item">项目组长：{{ state.info.projectLeader }}</div>
          <div class="flex-box-item">牵头单位：{{ state.info.leadingUnit }}</div>
          <div class="flex-box-item">项目状态：
            <span :class="['project-status',state.info.projectStatus ===item?'active':'' ]" v-for="(item,index) in ['已立项','标注中','已结束']">{{item}}</span>
          </div>
          <div class="flex-box-item">募集资金：{{ state.info.raiseFunds }}</div>
        </div>
        <!--        <div class="flex-box">-->
        <!--          <div class="flex-box-item onlyOne">项目简介：{{ state.info.projectDesc	 }}</div>-->
        <!--        </div>-->
        <!--        <div class="flex-box">-->
        <!--          <div class="flex-box-item onlyOne"-->
        <!--            >实施路径：{{ state.info.implementApproach }}</div-->
        <!--          >-->
        <!--        </div>-->
        <div class="tabs-box">
          <div class="tabs">
            <div
              :class="state.checkedTabs === 0 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(0)"
              >项目概览</div
            >
            <div
              :class="state.checkedTabs === 1 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(1)"
              >标注规则</div
            >
            <div
              :class="state.checkedTabs === 2 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(2)"
              >标注任务</div
            >
            <div
              :class="state.checkedTabs === 3 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(3)"
              >我的任务</div
            >
            <div
              :class="state.checkedTabs === 4 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(4)"
              >我的审核</div
            >
            <div
              :class="state.checkedTabs === 5 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(5)"
              >结果展示</div
            >
          </div>
          <div v-loading="state.loading" class="tabs-content">
            <overview v-if="state.checkedTabs === 0 && state.info.id" :info="state.info" />
            <markRule v-if="state.checkedTabs === 1 && state.info.id" :info="state.info" />
            <task v-if="state.checkedTabs === 2 && state.info.id" :info="state.info" />
            <annotationTask
              v-if="state.checkedTabs === 3 && state.info.id && !state.onlineStandardInfo"
              :info="state.info"
              @onlineStandard="openOnlineStandardFn"
            />
            <template v-if="state.checkedTabs === 4">
              
              <annotationList  v-if="state.previewStandardInfo" :info="state.previewStandardInfo"
              @back="closePreviewStandardFn" />
              <previewStandard
              v-else-if="state.detailStandardInfo"
              :info="state.detailStandardInfo"
              @close="closeDetailStandardFn"
               @detail="openPreviewStandardFn"
              />
              <auditTask
              v-else-if="state.info.id && !state.previewStandardInfo"
              :info="state.info"
              @previewStandard="openDetailStandardFn"
              />
            </template>
            <result v-if="state.checkedTabs === 5 && state.info.id" :info="state.info" />
            <onlineStandard isPolling
              v-if="state.checkedTabs === 3 && state.onlineStandardInfo"
              :info="state.onlineStandardInfo"
              @close="closeOnlineStandardFn"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import overview from './components/overview'
  // 标注规则
  import markRule from './components/markRule'
  // 标注任务
  import task from '../taskManagement/list.vue'
  // 我的任务
  import annotationTask from '../annotationTask/index.vue'
  // 标注列表
  import annotationList from './annotationList.vue'
  // 我的审核
  import auditTask from '../auditTask/index.vue'
  // 在线标注
  import onlineStandard from './onlineStandard.vue'
  // 我的审核-预览标注结果
  import previewStandard from './previewStandard.vue'
  // 结果展示
  import result from './components/result'

  import { workbenchDefinitionList, workbenchAnnotationProjectDetail } from '@/api/dataManage.js'

  const store = useStore()
  const router = useRouter()

  const state = reactive({
    loading: false,
    id: null,
    checkedTabs: 0,
    info: {},
    onlineStandardInfo: null,
    previewStandardInfo: null,
  })

  const checkTabsFn = (index) => {
    state.onlineStandardInfo = null
    state.previewStandardInfo = null
    state.checkedTabs = index
  }

  const openOnlineStandardFn = (row) => {
    state.checkedTabs = 3
    state.onlineStandardInfo = row
  }
  const closeOnlineStandardFn = () => {
    state.checkedTabs = 3
    state.onlineStandardInfo = null
  }

  // 我的审核-打开预览标注结果
  const openPreviewStandardFn = (row) => {
    state.checkedTabs = 4
    state.previewStandardInfo = row
  }
  // 我的审核-打开标注详情
  const openDetailStandardFn = (row) => {
    state.checkedTabs = 4
    state.detailStandardInfo = row  
  }
  // 我的审核-关闭预览标注结果
  const closePreviewStandardFn = () => {
    state.checkedTabs = 4
    state.previewStandardInfo = null
  }
  // 我的审核-关闭标注详情
  const closeDetailStandardFn = () => {
    state.checkedTabs = 4
    state.detailStandardInfo = null  
  }

  //匹配密级
  const secretTransition = (status) => {
    let name = '公开'
    let iconClassName = 'blue1'

    switch (status) {
      case 'INTERIOR':
        name = '内部'
        iconClassName = 'yellow'
        break
      case 'CONTROLLED':
        name = '受控'
        iconClassName = 'blue2'
        break
      case 'SECRET':
        name = '秘密'
        iconClassName = 'red2'
        break
      case 'CONFIDENTIAL':
        name = '机密'
        iconClassName = 'red1'
        break
      case 'CORE':
        name = '核心'
        iconClassName = 'blue3'
        break
      default:
        break
    }

    return { name, iconClassName }
  }

  // 获取详情
  const getDetailFn = () => {
    state.loading = true
    workbenchAnnotationProjectDetail({ id: state.id })
      .then((res) => {
        if (res.success) {
          state.info = res.data
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
    state.loading = false
  }

  const joinFn = () => {}

  onMounted(() => {
    state.id = router.currentRoute.value.query.id || null
    getDetailFn()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    width: 100%;
    height: calc(100vh - 96px);
    padding: 16px;

    .content-box {
      height: 100%;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 10px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 18px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
        .datasourceSecret {
          box-sizing: border-box;
          width: 40px;
          height: 20px;
          color: rgba(0, 0, 0, 0.75);
          font-size: 12px;
          font-weight: normal;
          line-height: 18px;
          text-align: center;
          background-color: #f4f4f5;
          border: 1px solid rgba(177, 179, 184, 0.53);
          border-radius: 2px;
          margin-left: 8px;
          &.yellow {
            color: #fe8624;
            background-color: #fff4e6;
            border: 1px solid #ffba70;
          }
          &.blue1 {
            color: #1e89ff;
            background-color: #ebf4ff;
            border: 1px solid #99c9ff;
          }
          &.blue2 {
            color: #1aa4ee;
            border: 1px solid rgba(26, 164, 238, 0.4);
            background: rgba(26, 164, 238, 0.08);
          }
          &.blue3 {
            color: #224ecd;
            border: 1px solid rgba(34, 78, 205, 0.4);
            background: rgba(34, 78, 205, 0.08);
          }
          &.red1 {
            color: #7a0000;
            border: 1px solid rgba(122, 0, 0, 0.4);
            background: rgba(122, 0, 0, 0.08);
          }
          &.red2 {
            color: #d40000;
            border: 1px solid #ef7777;
            background: #ffeded;
          }
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 86px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;

          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }

      &-row {
        height: calc(100% - 62px);
        border-radius: 2px;
        background-color: #ffffff;
        padding: 12px 12px 0 12px;

        .flex-box {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 16px;

          &-item {
            width: 25%;
            flex-shrink: 0;
            overflow: hidden;
            color: #606266;
            font-weight: normal;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            .project-status{
              &+span{
                margin-left: 8px;
              }
              &.active{
                color: #fff;
                // background-color: #1e89ff;
                // color: #1e89ff; 
                border-radius: 2px; 
                // 发光效果
                box-shadow: 0 0 10px rgba(30, 137, 255, 0.5);
                // 文字发光效果
                text-shadow: 0 0 10px rgba(30, 137, 255, 0.5);
                // 彩色发光效果
                background: linear-gradient(45deg, #1e89ff, #479dff);
              }

            }

            &.onlyOne {
              width: 100%;
            }
          }
        }

        .tabs-box {
          position: relative;
          width: 100%;
          height: calc(100% - 36px);
          padding-left: 90px;
          padding-top: 16px;
          box-sizing: border-box;
          border-top: 1px solid #c5d0ea;

          .tabs {
            position: absolute;
            width: 90px;
            left: 0;
            top: 16px;

            &-item {
              color: #1d2129;
              font-weight: 500;
              font-size: 16px;
              height: 36px;
              line-height: 36px;
              position: relative;
              padding: 0 12px;
              cursor: pointer;

              &.checked {
                color: #1e89ff;
              }

              &:before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 4px;
                height: 18px;
                margin: auto;
                background: #1e89ff;
                content: '';
              }
            }

            &-content {
              width: 100%;
              height: 100%;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }
</style>
