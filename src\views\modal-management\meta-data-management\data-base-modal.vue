<template>
  <div class="container">
    <div class="container-box">
      <div class="form">
        <h2>{{ $route.params.title }}</h2>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="数据源" prop="data_source_id">
            <el-select
              v-model="ruleForm.data_source_id"
              size="small"
              placeholder="请选择"
              @change="changOne"
            >
              <el-option
                v-for="item in sourceOptions"
                :key="item.id"
                :label="item.dataBaseName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据表" prop="optionsTwo">
            <div class="box0">
              <div class="box">
                <!-- <el-select v-model="ruleForm.optionsOne" size="small" placeholder="请选择数据源" @change="changOne">
                  <el-option v-for="item in sourceOptions" :key="item.id" :label="item.dataBaseName" :value="item.id">
                  </el-option>
                </el-select> -->
                <el-select
                  v-model="ruleForm.optionsTwo"
                  size="small"
                  placeholder="请选择数据表"
                  @change="changTwo"
                >
                  <el-option
                    v-for="item in sourceOptionsTwo"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="">
            <div class="box1">
              <delivery-table
                :deliveryTableData="deliveryTableData"
                @getReverseData="getReverseDataHandle"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="footer">
        <n-button
          size="sm"
          variant="solid"
          color="primary"
          @click.stop.prevent="reverseMeta('ruleForm')"
          >确定</n-button
        >
        <n-button size="sm" @click.stop.prevent="cancel">取消</n-button>
      </div>
    </div>
  </div>
</template>

<script>
  import deliveryTable from '@/components/shuttleModal'
  export default {
    name: 'DataBaseModal',
    components: {
      'delivery-table': deliveryTable,
    },
    data() {
      return {
        ruleForm: {
          data_source_id: '',
          optionsOne: [],
          optionsTwo: '',
        },
        surface: '',
        rules: {
          data_source_id: [{ required: true, message: '请选中数据源', trigger: 'change' }],
          schema: [{ required: true, message: '请选择', trigger: 'change' }],
          optionsTwo: [{ required: true, message: '请选择', trigger: 'change' }],
        },

        // 数据源选择
        sourceOptions: [],
        sourceOptionsTwo: [],
        optionsOne: '',
        optionsTwo: '',
        deliveryTableData: [],
        getReverseData: [],
      }
    },
    computed: {},
    mounted() {
      this.getSourceOptions()
    },
    methods: {
      // 获取数据源选项
      getSourceOptions() {
        this.$api.dataManagement.getSourceOptions().then((res) => {
          this.sourceOptions = res.data
        })
      },
      // debug
      changOne() {
        let params = {
          externalDataSourceId: this.ruleForm.data_source_id,
        }
        this.$api.dataManagement.getTableOptions(params).then((res) => {
          this.sourceOptionsTwo = res.data
        })
      },
      // debug
      changTwo() {
        let params = {
          dataSourceId: this.ruleForm.data_source_id,
          tableName: this.ruleForm.optionsTwo,
        }
        this.$api.dataManagement.getLeftModel(params).then((res) => {
          this.deliveryTableData = res.data
        })
      },
      getReverseDataHandle(data) {
        this.getReverseData = data
      },
      // 提交逆向元数据
      reverseMeta(form) {
        this.$refs[form].validate((valid) => {
          if (valid) {
            let columnList = this.getReverseData.map((item, index) => {
              return {
                description: item.metaDesc,
                field: item.fieldKey,
                fieldIndex: index + 1,
                length: item.length,
                name: '',
                type: item.type,
              }
            })
            let params = {
              columnList: columnList,
              dataSourceId: this.ruleForm.data_source_id,
              tableName: this.ruleForm.optionsTwo,
              type: 1,
            }
            this.$api.model.reverseMeta(params).then((res) => {
              this.$notify({
                title: '成功',
                message: '逆向元数据添加成功',
                type: 'success',
              })
              this.$router.back()
            })
          }
        })
      },
      goBack() {
        this.$router.go(-1)
      },
      cancel() {
        this.$router.go(-1)
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    padding-bottom: 100px;
    .form {
      background: #fff;
      padding: 33px 58px 20px 58px;
      color: #333;
      height: 510px;
      overflow-y: auto;
      h2 {
        font-size: 14px;
        padding-left: 5px;
        border-left: 4px solid $themeBlue;
      }
      .el-form {
        margin-left: 8px;
        margin-top: 20px;
        &-item__label {
          color: #333;
          font-weight: 500;
        }
        .box0 {
          display: flex;
          justify-content: flex-start;
        }
        .box {
          display: flex;
          margin-right: 45px;
          .el-select {
            margin-right: 20px;
          }
        }
        .box1 {
          margin: 0 auto;
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 60px;
      line-height: 60px;
      padding-right: 20px;
      background: #fff;
      border-top: 1px solid rgba(200, 200, 200, 0.35);
      text-align: right;
    }
  }
</style>
