<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>添加@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数字孪生-三维项目管理-三维场景编辑器" transform="translate(-1722.000000, -753.000000)" fill-rule="nonzero">
            <g id="配置信息" transform="translate(1640.000000, 131.000000)">
                <g id="模型动作数据" transform="translate(0.000000, 528.000000)">
                    <g id="编组-32备份-5" transform="translate(20.000000, 94.000000)">
                        <g id="添加" transform="translate(62.000000, 0.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="currentColor" x="0" y="0" width="18" height="18"></rect>
                            <path d="M9,0.85 C13.5011226,0.85 17.15,4.49887741 17.15,9 C17.15,13.5011226 13.5011226,17.15 9,17.15 C4.49887741,17.15 0.85,13.5011226 0.85,9 C0.85,4.49887741 4.49887741,0.85 9,0.85 Z M9,2.15 C5.21684759,2.15 2.15,5.21684759 2.15,9 C2.15,12.7831524 5.21684759,15.85 9,15.85 C12.7831524,15.85 15.85,12.7831524 15.85,9 C15.85,5.21684759 12.7831524,2.15 9,2.15 Z M9,5.35 C9.32635008,5.35 9.59652646,5.59050819 9.64295233,5.90394776 L9.65,6 L9.65,8.35 L12,8.35 C12.3589851,8.35 12.65,8.64101491 12.65,9 C12.65,9.32635008 12.4094918,9.59652646 12.0960522,9.64295233 L12,9.65 L9.65,9.65 L9.65,12 C9.65,12.3589851 9.35898509,12.65 9,12.65 C8.67364992,12.65 8.40347354,12.4094918 8.35704767,12.0960522 L8.35,12 L8.35,9.65 L6,9.65 C5.64101491,9.65 5.35,9.35898509 5.35,9 C5.35,8.67364992 5.59050819,8.40347354 5.90394776,8.35704767 L6,8.35 L8.35,8.35 L8.35,6 C8.35,5.64101491 8.64101491,5.35 9,5.35 Z" id="形状结合" fill="currentColor"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>