<template>
  <div class="task-list-item">
    <div class="task-list-item-header">
      <div class="task-list-item-icon">
        <SvgIcon
          :icon="
            item.status === 'ONLINE'
              ? 'quality-task-list-item-icon-on'
              : 'quality-task-list-item-icon-off'
          "
          class="icon"
        />
      </div>
      <div class="task-list-item-title">
        <div class="task-list-item-title-text"
          ><span class="text" :title="item.name">{{ item.name }}</span>
          <!-- 操作按钮 -->
          <div class="task-list-item-title-action">
            <n-popover
              class="table-list-card-popover"
              :position="['bottom-end']"
              align="end"
              trigger="hover"
            >
              <div class="icon"> </div>
              <template #content>
                <div class="table-list-card-header-popover">
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_view',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_view"
                    variant="text"
                    class="table-list-card-header-popover-label"
                    @click.prevent="viewFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-see" />查看</n-button
                  >
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_edit',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_edit"
                    :disabled="item.status === 'ONLINE'"
                    class="table-list-card-header-popover-label"
                    variant="text"
                    @click.prevent="editFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-edit" />编辑</n-button
                  >
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_run',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_run"
                    :disabled="item.status === 'OFFLINE'"
                    class="table-list-card-header-popover-label"
                    variant="text"
                    @click.prevent="runFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-card-rerun" />立即运行</n-button
                  >
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_log',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_log"
                    class="table-list-card-header-popover-label"
                    variant="text"
                    @click.prevent="runRecordFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-log" />运行记录</n-button
                  >
                  <n-button
                    variant="text"
                    v-if="
                      item.status === 'ONLINE' &&
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_off_edit',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_off  _edit"
                    class="table-list-card-header-popover-label"
                    @click.prevent="statusFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-offline" />下架</n-button
                  >
                  <n-button
                    v-if="
                      item.status !== 'ONLINE' &&
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_push_edit',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_push_edit"
                    variant="text"
                    class="table-list-card-header-popover-label"
                    @click.prevent="statusFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-online" />发布</n-button
                  >

                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityTask_delete',
                      )
                    "
                    code="governanceManage_qualityManage_qualityTask_delete"
                    :disabled="item.status === 'ONLINE'"
                    variant="text"
                    class="table-list-card-header-popover-label"
                    @click.prevent="delFn(item)"
                  >
                    <SvgIcon class="icon" icon="icon-new-delete" />删除</n-button
                  >
                </div>
              </template>
            </n-popover>
          </div>
        </div>
        <!-- 其他信息 -->
        <div class="task-list-item-title-info">
          <span class="text" :title="'控对象 ' + item.tableName"
            >监控对象 {{ item.tableName }}</span
          >
          |
          <span class="text" :title="'创建人 ' + item.createByName"
            >创建人 {{ item.createByName }}</span
          >
        </div>
      </div>
    </div>
    <div class="task-list-item-content">
      <!-- 调度方式   -->
      <div class="task-list-item-content-dispatch">
        <span class="text">调度方式：</span>
        <span class="text">{{ item.scheduleModeName }}</span>
      </div>
      <!-- 规则数量 -->
      <div class="task-list-item-content-rule">
        <span class="text">规则数量：</span>
        <span class="text">{{ item.configRuleCount }}/{{ item.totalRuleCount }}</span>
      </div>
      <!-- 备注 -->
      <div class="task-list-item-content-remark">
        <span class="text">{{ item.description || '暂无描述' }}</span>
      </div>
      <!-- 创建时间 -->
      <div class="task-list-item-content-create-time">
        <div v-if="item.status === 'ONLINE'" class="task-list-item-content-create-time-status green">已发布</div>
        <div v-else class="task-list-item-content-create-time-status">已下架</div>
        <div>{{ item.createTime }}</div></div>
    </div>
  </div>
</template>

<script>
  import { reactive, toRefs, ref, nextTick, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  export default {
    title: '',
    components: {},
    props: {
      item: {
        type: Object,
        default() {
          return {}
        },
      },
    },
    emits: [
      // 删除事件
      'delete',
      // 修改事件
      'edit',
      // 运行事件
      'run',
      // 停止事件
      'stop',
      'view',
      'change',
    ],
    setup(props, { emit }) {
      const { proxy } = getCurrentInstance()
      const { item } = toRefs(props)
      const router = useRouter()
      const store = useStore()
      const state = reactive({ ...item?.value, buttonAuthList: [] })
      const methods = {
        // 点击删除
        delFn(item) {
          proxy.$dialogPopup({
            title: '是否确认删除质量任务？',
            message: '删除后，质量任务将会被停止运行',
            save: () => {
              // 如果新增指标后，需要更新指标体系数据
              if (sessionStorage.getItem('targetThemeData')) {
                sessionStorage.removeItem('targetThemeData')
              }
              api.dataQuality.deleteTaskItem({ id: item?.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除成功',
                    type: 'success',
                  })
                  // 如果新增指标后，需要更新指标体系数据
                  if (sessionStorage.getItem('targetThemeData')) {
                    sessionStorage.removeItem('targetThemeData')
                  }
                  emit('delete', item)
                }
              })
            },
          })
        },
        // 点击运行
        runFn(item) {
          api.dataQuality.taskExecute({ id: item?.id }).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '运行成功',
                type: 'success',
              })
              emit('run', item)
            }
          })
        },
        // 点击跳转运行记录
        runRecordFn(item) {
          router.push({
            name: 'operatingLog',
            query: {
              id: item?.id,
              name: item?.name,
            },
          })
        },
        // 点击上线
        statusFn(item) {
          const isOnline = item?.status === 'ONLINE'
          api.dataQuality[isOnline ? 'qualityTaskOffline' : 'qualityTaskOnline']({
            id: item?.id,
          }).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: isOnline ? '下架成功' : '发布成功',
                type: 'success',
              })
              emit('change', item)
            }
          })
          // taskOffline
          emit('online', item)
        },
        // 点击编辑
        editFn(item) {
          emit('edit', item)
        },
        // 点击查看
        viewFn(item) {
          emit('view', item)
        },
      }
      const { buttonAuthList } = toRefs(store.state.user)
      state.buttonAuthList = buttonAuthList
      return {
        state,
        props,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .task-list-item {
    width: 100%;
    height: 192px;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid var(---, #DCDFE6);
    background: #fff;
    &:hover {
      border: 1px solid #fff;
      box-shadow: 0 4px 16px -2px #0000001a;
    }

    &-header {
      display: flex;

      .task-list-item-icon {
        width: 40px;
        height: 40px;
        margin-right: 8px;

        .icon {
          width: 100%;
          height: 100%;
        }
      }

      .task-list-item-title {
        width: calc(100% - 48px);

        &-text {
          display: flex;
          justify-content: space-between;

          .text {
            color: #1D2129;
            /* 常用/r500/h8 */
            font-family: 'Source Han Sans CN';
            font-size: 16px;
            font-style: normal;
            font-weight: bold;
            line-height: 24px; /* 150% */
            // 超出省略
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &-action {
          min-width: 24px;
          width: 24px;
          height: 24px;

          .icon {
            width: 100%;
            height: 100%;
            background: url('@/assets/img/quality/more.png') no-repeat center;
            background-size: 100%;
            cursor: pointer;

            &:hover {
              background: url('@/assets/img/quality/more-hover.png') no-repeat center;
            }
          }
        }

        &-info {
          overflow: hidden;
          color: #909399;
          font-feature-settings: 'clig' off, 'liga' off;
          text-overflow: ellipsis;
          white-space: nowrap;

          /* 说明/12_R400 */
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
          & > span {
            display: inline-block;
            vertical-align: top;
            overflow: hidden;
            max-width: 50%;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    &-content {
      & > div {
        margin-top: 8px;
        height: 20px;
        overflow: hidden;
        color: #1D2129;
        text-overflow: ellipsis;
        white-space: nowrap;

        /* 常用/r400/h9 */
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }

      .task-list-item-content-create-time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #A8ABB2;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        margin-top: 12px;
        line-height: 20px; /* 166.667% */
        &-status{
          box-sizing: border-box;
          height: 20px;
          padding: 0 4px;
          color: #1D2129;
          font-size: 12px;
          line-height: 18px;
          background-color: #f4f4f5;
          border: 1px solid #C0C4CC;
          border-radius: 2px;
          &.green {
            color: #31b046;
            background-color: #ebfaed;
            border: 1px solid #31b046;
          }
        }
      }
    }
  }
</style>
