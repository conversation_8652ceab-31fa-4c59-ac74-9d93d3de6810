<template>
  <textarea
    v-model="data.value"
    placeholder="请输入"
    style="border: none; outline: none; width: 100%; height: 100%; resize: none"
    @input="changeValue"
  ></textarea>
</template>

<script>
  export default {
    name: 'Inputcom',
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {
          value: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.data = current
      })
    },
    methods: {
      changeValue() {
        this.getNode().setData(this.data)
      },
    },
  }
</script>

<style scoped lang="scss"></style>
