<template>
  <n-modal
    v-model="showDirDialog"
    title="新建离线作业"
    width="560px"
    :close-on-click-overlay="false"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item label="作业名称" field="name">
          <n-input v-model="state.syncForm.name" maxlength="500" placeholder="请输入" />
        </n-form-item>
        <n-form-item label="业务流程" field="workflowDirectoryId">
          <n-select
            v-model="state.syncForm.workflowDirectoryId"
            placeholder="请选择"
            filter
            @value-change="getOfflineJobTaskTypeList"
          >
            <n-option
              v-for="item in state.offlineJobList"
              :key="item.id"
              :value="item.id"
              :name="item.name"
            />
          </n-select>
        </n-form-item>
        <!-- 作业类型 -->
        <n-form-item label="作业类型" field="type">
          <n-select v-model="state.syncForm.type" placeholder="请选择">
            <n-option
              v-for="item in state.offlineJobTaskTypeList"
              :key="item.nodeCode"
              :value="item.nodeCode"
              :name="item.name"
            />
          </n-select>
        </n-form-item>
        <!-- 选择目录 -->
        <n-form-item label="选择目录" field="directoryId">
          <DirSelect
            ref="treeRef"
            :disabled="!isNaN(state.currentNode?.data?.id || state.currentNode?.id)"
            v-model:directoryId="state.syncForm.directoryId"
            :filter-node-method="filterNode"
            :disable-checked="
              (node) =>
                containsValueInAncestors(node, 'type', 'DIRECTORY') &&
                !containsValueInAncestors(node, 'type', 'WORKFLOW')
            "
            :data="$attrs.treeList"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" v-loading="state.hasClickSave" @click.prevent="createDir"
          >确 定</n-button
        >
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import api from '@/api/index'
  import DirSelect from './dirSelect.vue'
  import { checkCName500 } from '@/utils/validate'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const treeRef = ref(null)
  const state = reactive({
    hasClickSave: false,
    currentNode: null,
    offlineJobList: [],
    workFlow: null,
    syncForm: {
      name: null,
      type: null,
      workflowDirectoryId: null,
      directoryId: null,
    },
    offlineJobTaskTypeList: [
      { name: 'PySpark', nodeCode: 'OW_PY_SPARK' },
      { name: 'HiveSQL', nodeCode: 'OW_HIVE_SQL' },
      { name: 'DorisSQL', nodeCode: 'OW_DORIS_SQL' },
      { name: 'SparkSQL', nodeCode: 'OW_SPARK_SQL' },
      { name: 'Python', nodeCode: 'OW_PYTHON' },
      { name: 'Shell', nodeCode: 'OW_SHELL' },
      { name: 'HiveDDL', nodeCode: 'OW_HIVE_DDL' },
      { name: 'DorisDDL', nodeCode: 'OW_DORIS_DDL' },
      { name: '同步至资源库', nodeCode: 'OW_DATA_ASYNC' },
      { name: '非结构化数据分析', nodeCode: 'OW_DATA_UPLOAD' },
    ],
    syncRules: {
      name: [
        {
          required: true,
          validator: (...args) =>
            checkCName500(...args, null, null, {
              nameType: 'CN',
              name: state.syncForm.name,
              id: null,
            }),
          trigger: 'blur',
        },
      ],
      directoryId: [
        {
          required: true,
          message: '请选择目录',
          trigger: 'blur',
          type: 'number',
        },
      ],
      workflowDirectoryId: [
        {
          required: true,
          message: '请选择业务流程',
          trigger: 'blur',
          type: 'number',
        },
      ],
      type: [
        {
          required: true,
          message: '请选择作业类型',
          trigger: 'blur',
        },
      ],
    },
  })

  // 递归校验父级属性,并返回对应id
  function containsValueInAncestorsProcessId(obj, propName, value) {
    const { data } = obj
    // 检查当前对象是否包含该属性且属性值等于目标值
    if (data.hasOwnProperty(propName) && data[propName] === value) {
      return data.processId
    }

    // 如果当前对象有父级对象，继续递归检查
    if (obj.hasOwnProperty('parent') && obj.parent) {
      return containsValueInAncestorsProcessId(obj.parent, propName, value)
    }

    return
  }

  const filterNode = (value, data) => {
    if (!value) return true
    return data?.id === value
  }

  // 递归校验父级属性
  function containsValueInAncestors(obj, propName, value) {
    const { data } = obj
    // 检查当前对象是否包含该属性且属性值等于目标值
    if (data.hasOwnProperty(propName) && data[propName] === value) {
      return true
    }

    // 如果当前对象有父级对象，继续递归检查
    if (obj.hasOwnProperty('parent') && obj.parent) {
      return containsValueInAncestors(obj.parent, propName, value)
    }

    // 如果没有找到，返回false
    return false
  }
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        if (state.hasClickSave) {
          return false
        }
        state.hasClickSave = true
        const { type, id } = state.currentNode || {}
        // if (!id) return ElMessage.error('请选择目录')
        api.offlineJob
          .createJob(state.syncForm)
          .then(({ success, data }) => {
            state.hasClickSave = false
            if (!success) return
            emit('success', data)
            showDirDialog.value = false
            ElMessage.success('创建成功')
          })
          .catch(() => {
            state.hasClickSave = false
          })
      }
    })
  }
  const getOfflineJobList = () => {
    api.offlineJob.getOfflineJobList().then((res) => {
      state.offlineJobList = res.data || []
      if (state.workFlow) {
        state.syncForm.workflowDirectoryId = workFlow.workFlowId
        state.syncForm.type = workFlow.node.nodeCode
      }
    })
  }
  // 离线作业任务类型列表
  const getOfflineJobTaskTypeList = () => {
    api.offlineJob
      .getOfflineJobTaskTypeList({
        taskId: state.syncForm.workflowDirectoryId,
      })
      .then((res) => {
        state.offlineJobTaskTypeList = res.data || []
      })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.currentNode = null
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  getOfflineJobList()
  defineExpose({
    open(node, workFlow) {
      resetForm()

      if (workFlow) {
        state.workFlow = workFlow
        state.syncForm.workflowDirectoryId = workFlow.workFlowId
        state.syncForm.type = workFlow.node.nodeCode
      } else {
        state.syncForm.workflowDirectoryId = containsValueInAncestorsProcessId(
          node,
          'type',
          'WORKFLOW',
        )
      }
      state.currentNode = node
      state.syncForm.directoryId = node.data?.id || node.id
      showDirDialog.value = true
      !isNaN(state.syncForm.directoryId) &&
        nextTick(() => {
          treeRef.value?.treeRef?.filter(state.syncForm.directoryId)
        })
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
  }
</style>
