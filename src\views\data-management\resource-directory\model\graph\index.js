import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'
import './shape.jsx'
import { setEdgeVertices } from '@/utils/graph'
import { reName } from '../utils/index'
const { Dnd } = Addon

export default class FlowGraph {
  static init(obj) {
    this.selectdNode = []
    let gridConfig = null
    if (obj.type === 'detail') {
      gridConfig = {
        background: {
          image:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAWCAYAAABOm/V6AAAAAXNSR0IArs4c6QAAAElJREFUSEtjfP32w3+GAQaMIEeICgswEuMOWqkddQQs9EdDYjQk0HPiaJoYfGmCmNKSlmpG08TgSxOjtSgDA8NowhxNmIOyAgMAUq/3pZMfqAAAAAAASUVORK5CYII=',
          repeat: 'repeat',
        },
        interacting: {
          nodeMovable: false,
          edgeMovable: false,
        },
        resizing: { enabled: false },
        selecting: { enabled: false },
      }
    } else {
      gridConfig = {
        interacting: {
          nodeMovable: true,
        },
        background: {
          image:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAWCAYAAABOm/V6AAAAAXNSR0IArs4c6QAAAElJREFUSEtjfP32w3+GAQaMIEeICgswEuMOWqkddQQs9EdDYjQk0HPiaJoYfGmCmNKSlmpG08TgSxOjtSgDA8NowhxNmIOyAgMAUq/3pZMfqAAAAAAASUVORK5CYII=',
          repeat: 'repeat',
        },
        grid: {
          size: 1,
          visible: false,
          // type: 'doubleMesh',
          // args: [
          //   {
          //     color: '#eee', // 主网格线颜色
          //     thickness: 1, // 主网格线宽度
          //   },
          //   {
          //     color: '#ddd', // 次网格线颜色
          //     thickness: 1, // 次网格线宽度
          //     factor: 4, // 主次网格线间隔
          //   },
          // ],
        },
      }
    }

    this.id = obj.id
    this.graph = new Graph({
      container: document.getElementById(`${this.id}`),
      width: obj.width || 1000,
      height: obj.height || 800,
      async: true,
      // 禁用边移动
      interacting: function (cellView) {
        return { edgeMovable: false, ...gridConfig.interacting }
      },
      resizing: {
        enabled: true,
        minHeight: 60,
      }, // 改变节点大小
      // 画布调整
      scroller: {
        enabled: true,
        pageVisible: false, // 是否分页，默认为 false
        pageBreak: true, // 是否显示分页符，默认为 false。
        pannable: true, // 是否开启画布平移功能
      },
      minimap: obj.notNeedMini
        ? null
        : {
            width: 244,
            height: 180,
            padding: 30,
            enabled: true,
            container: document.getElementById('minimapContainer'),
          },
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
        minScale: 0.5,
        maxScale: 2,
      },
      // 画布调整
      selecting: {
        enabled: true,
        multiple: true,
        rubberband: true,
        movable: true,
        modifiers: 'ctrl',
      },
      history: {
        enabled: true,
        ignoreAdd: false,
        ignoreRemove: false,
        ignoreChange: false,
        beforeAddCommand(event, args) {
          let val = true
          // 添加工具
          if (args.key === 'tools') {
            val = false
          }
          // 手动设置的路径点
          if (args.key === 'vertices' && args?.previous?.length === 0) {
            val = false
          }
          return val
        },
      },
      connecting: {
        allowNode: false,
        allowLoop: true,
        allowMulti: true,
        anchor: 'center',
        allowBlank: false,
        // highlight: true,
        snap: {
          radius: 150,
        },
        attrs: {
          line: {
            fill: 'none',
            stroke: '#3c4260',
            strokeWidth: 2,
          },
        },
        router: 'normal',
        connector: {
          name: 'rounded',
          args: {
            radius: 8,
          },
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                fill: 'none',
                stroke: '#3c4260',
                strokeWidth: 2,
              },
            },
          })
        },
        validateConnection({
          edge,
          edgeView,
          sourceView,
          targetView,
          sourcePort,
          targetPort,
          sourceMagnet,
          targetMagnet,
          sourceCell,
          targetCell,
          type,
        }) {
          // 不允许同一个节点的同一个方向的链接桩相连
          if (
            sourceCell.id === targetCell.id &&
            sourcePort.split('-')[0] === targetPort.split('-')[0]
          ) {
            return false
          } else {
            return true
          }
        },
        // validateMagnet({ e, magnet, view, cell }) {
        //   console.log( magnet, view, cell);
        //   return false
        // },
      },
      // highlighting: {
      //   // 连线过程中，链接桩可以被链接时被使用。
      //   magnetAvailable: {
      //     name: 'stroke',
      //     args: {
      //       attrs: {
      //         stroke: '#5F95FF',
      //       },
      //     },
      //   },
      // },
      snapline: true,
      clipboard: {
        enabled: true,
      },
      keyboard: {
        enabled: true,
      },
      ...gridConfig,
    })

    this.initStencil(obj)

    if (obj.type !== 'detail') {
      this.initEvent()
    }

    return { graph: this.graph, selectdNode: this.selectdNode }
  }

  static initStencil(obj) {
    this.dnd = new Dnd({
      target: this.graph,
      scaled: false,
      animation: true,
      validateNode(droppingNode, options) {
        if (droppingNode.shape === 'nc-entity') {
          let params = droppingNode.getData()
          params.entity.guid = droppingNode.id
          params.guid = droppingNode.id
          droppingNode.setData(params, { overwrite: true })
          obj.that.currentNode = droppingNode
          obj.that.EntityPopShow = true

          // let params = droppingNode.getData()
          // params.entity.guid = droppingNode.id
          // params.guid = droppingNode.id

          // Vue.$api.dataModel.addEntity(params.entity).then((res) => {
          //   params.id = res.data.id
          //   params.entity.id = res.data.id
          //   droppingNode.setData(params, { overwrite: true })
          //   obj.that.saveGraph()
          // })
        } else if (droppingNode.shape === 'nc-table') {
          let params = droppingNode.getData()
          params.table.guid = droppingNode.id
          params.guid = droppingNode.id
          droppingNode.setData(params, { overwrite: true })
          obj.that.currentNode = droppingNode
          obj.that.TablesPopShow = true
          // let params = droppingNode.getData()
          // params.table.guid = droppingNode.id
          // params.guid = droppingNode.id

          // Vue.$api.dataModel.addTab(params.table).then((res) => {
          //   params.id = res.data.id
          //   params.table.id = res.data.id
          //   droppingNode.setData(params, { overwrite: true })
          //   obj.that.saveGraph()
          // })
        } else {
          // obj.that.saveGraph()
        }
      },
    })
  }

  static showPorts(ports, show) {
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden'
    }
  }

  static initEvent() {
    const { graph } = this
    const container = document.getElementById(this.id)

    graph.on('node:selected', ({ cell, node }) => {
      this.selectdNode.push(node)
    })
    graph.on('node:unselected', ({ cell, node }) => {
      let index = this.selectdNode.findIndex((item) => item.id === node.id)
      this.selectdNode.splice(index, 1)
    })
    graph.on(
      'node:mouseenter',
      FunctionExt.debounce(({ cell }) => {
        const ports = container.querySelectorAll('.x6-port-body')
        this.showPorts(ports, true)
      }),
      500,
    )
    graph.on('node:mouseleave', () => {
      const ports = container.querySelectorAll('.x6-port-body')
      this.showPorts(ports, false)
    })
    const lineChange = (edges) => {
      edges.forEach((edge) => {
        setEdgeVertices(edge)
      })
    }
    // 动态设置路径点
    graph.on('node:mousemove', ({ node }) => {
      let edges = graph.getEdges().filter((item) => {
        return item.source.cell === node.id || item.target.cell === node.id
      })
      lineChange(edges)
    })
    // 动态设置路径点
    graph.on('node:resizing', ({ node }) => {
      let edges = graph.getEdges().filter((item) => {
        return item.source.cell === node.id || item.target.cell === node.id
      })
      lineChange(edges)
    })
    // #region 快捷键与事件
    // copy cut paste
    graph.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.copy(cells)
      }
      return false
    })
    graph.bindKey(['meta+x', 'ctrl+x'], () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.cut(cells)
      }
      return false
    })
    graph.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.isClipboardEmpty()) {
        let allNodes = this.graph.getNodes()

        let list = allNodes.map((item) => item.data.table || item.data.entity)
        // console.log(list)
        const cells = this.graph.paste({ offset: 32 })
        cells.forEach((item) => {
          let attr = 'entity'
          let entityName = 'name'
          if (item.shape === 'nc-table') {
            attr = 'table'
            entityName = 'tabName'
          }
          let data = item.getData()
          let name = reName(list, entityName, data[attr][entityName])
          item.data[attr][entityName] = name
          item.data.guid = item.id
        })
        graph.cleanSelection()
        // console.log(cells)
        graph.select(cells)
      }
      return false
    })

    // undo redo
    graph.bindKey(['meta+z', 'ctrl+z'], () => {
      if (graph.history.canUndo()) {
        graph.history.undo()
      }
      return false
    })
    graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
      if (graph.history.canRedo()) {
        graph.history.redo()
      }
      return false
    })

    // select all
    graph.bindKey(['meta+a', 'ctrl+a'], () => {
      const nodes = graph.getNodes()
      if (nodes) {
        graph.select(nodes)
      }
    })

    // zoom
    graph.bindKey(['alt++'], () => {
      const zoom = graph.zoom()
      if (zoom < 1.5) {
        graph.zoom(0.1)
      }
    })
    graph.bindKey(['alt+-'], () => {
      const zoom = graph.zoom()
      if (zoom > 0.5) {
        graph.zoom(-0.1)
      }
    })

    graph.bindKey(['alt+0'], () => {
      graph.zoomTo(1)
    })
  }

  // 销毁
  static destroy() {
    this.graph.dispose()
  }
}
