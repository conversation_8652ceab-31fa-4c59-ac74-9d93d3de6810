<template>
  <section class="flow" id="flow">
    <div id="flowContainer" class="graph"></div>
    <!-- <el-drawer
      v-model="drawer"
      direction="rtl"
      modal-class="drawer-modal-bg"
      :close-on-click-modal="false"
      :modal="false"
      :with-header="false"
      :size="680"
    >
      <modelDrawer v-if="nodeType === 'node'" @changeStatus="changeStatus" :info="modelInfo" />
      <relationDrawer
        v-if="nodeType === 'edg'"
        @changeStatus="changeStatus"
        :mapData="mapData"
        :info="relationInfo"
      />
    </el-drawer> -->
  </section>
</template>

<script>
  import { reactive, toRefs, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import FlowGraph from './graph'
  import { Model } from '@antv/x6'
  import { DagreLayout } from '@antv/layout'
  import { ElNotification, ElLoading } from 'element-plus'
  import api from '@/api/index'
  // import modelDrawer from './components/model'
  // import relationDrawer from './components/relation'

  export default {
    // components: { modelDrawer, relationDrawer },
    props: {
      checkedItem: Object,
    },
    setup(props) {
      const store = useStore()
      const state = reactive({
        graph: {},
        resizeFn: null,
        cell: {},
        modelInfo: {}, // 模型详情
        relationInfo: {}, // 关系详情
        mapData: {},
        dataModelId: '',
        drawer: false,
        nodeType: '', // node:节点，edg:边
        currentProject: {},
        projectCode: '',
        modelData: {},
      })
      watch(
        () => props.checkedItem,
        (newVal) => {
          state.dataModelId = newVal.id ? newVal.id : null
          state.projectCode = newVal.projectCode ? newVal.projectCode : null
          if (state.dataModelId) {
            methods.assetsLineage(state.dataModelId)
            methods.getModelData()
          }
        },
      )
      const methods = {
        getModelData() {
          let projectCode = state.projectCode
          api.model.getDataModelTree({ projectCode }).then((res) => {
            if (res.success) {
              state.modelData = {}
              res.data.forEach((item) => {
                state.modelData[item.id] = item.name
              })
            }
          })
        },
        // 初始化创建画布
        initFn() {
          state.graph = FlowGraph.init()
          // methods.boundEvent()
          state.resizeFn = () => {
            nextTick(() => {
              methods.resizeFn()
            })
          }
          state.resizeFn()
          window.addEventListener('resize', state.resizeFn)
        },
        // 计算尺寸
        resizeFn() {
          const { width, height } = methods.getContainerSize()
          state.graph.resize(width, height)
          state.graph.centerContent()
        },
        // 获取画布容器大小
        getContainerSize() {
          return {
            width: document.getElementById('flow').offsetWidth,
            height: document.getElementById('flow').offsetHeight,
          }
        },
        // 根据数据表追溯关系
        assetsLineage(dataModelId) {
          let params = {
            // 测试数据
            dataModelId,
          }
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          api.assets.assetsLineage(params).then((res) => {
            if (res.data) {
              state.mapData = res.data
              methods.setMapData()
            } else {
              state.graph.clearCells()
              ElNotification({
                title: '提示',
                message: '暂无数据',
                type: 'warning',
              })
            }
            loading.close()
          })
        },
        // 切换画布数据
        setMapData() {
          const data = (Model.FromJSONData = {
            nodes: [],
            edges: [],
          })
          state.mapData.nodes.forEach((item, index) => {
            data.nodes.push({
              id: item.nId,
              data: item,
              shape: 'audit-box',
              size: {
                width: 160,
                height: 52,
              },
              attrs: {
                body: {
                  refWidth: '100%',
                  refHeight: '100%',
                  strokeWidth: 1,
                  fill: '#ECF7FF',
                  stroke: '#18A0FB',
                  rx: 4,
                  ry: 4,
                },
              },
              ports: {
                groups: {
                  left: {
                    id: 'left',
                    position: {
                      name: 'absolute',
                      args: { x: 0, y: 0 },
                    },
                    attrs: {
                      circle: {
                        x: 50,
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                  right: {
                    id: 'right',
                    position: {
                      name: 'absolute',
                      args: { x: 0, y: 0 },
                    },
                    attrs: {
                      circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                },
                items: [
                  {
                    id: 'left',
                    group: 'left',
                    args: {
                      x: 0,
                      y: 26,
                    },
                  },
                  {
                    id: 'right',
                    group: 'right',
                    args: {
                      x: 160,
                      y: 26,
                    },
                  },
                ],
              },
              zIndex: 1,
            })
          })
          // debugger
          state.mapData.relationship.forEach((item, index) => {
            data.edges.push({
              source: { cell: item.nTo, port: 'left' },
              target: { cell: item.nFrom, port: 'right' },
              interacting: true,
              connector: 'rounded',
              router: { name: 'manhattan' },
              data: item,
              attrs: {
                line: {
                  stroke: '#666666',
                  strokeWidth: 1,
                  strokeDasharray: 0,
                  targetMarker: { name: 'classic', size: 8 },
                },
              },
              zIndex: 0,
              // tools: [
              //   {
              //     name: 'button',
              //     args: {
              //       markup: [
              //         {
              //           tagName: 'rect',
              //           selector: 'button',
              //           attrs: {
              //             x: -44,
              //             y: -14,
              //             width: 88,
              //             height: 22,
              //             fill: '#ffffff',
              //             stroke: '#9A979D',
              //             'stroke-width': 1,
              //             cursor: 'pointer',
              //             zIndex: 2,
              //           },
              //         },
              //         {
              //           tagName: 'text',
              //           textContent: item.relType,
              //           selector: 'icon',
              //           attrs: {
              //             fill: '#333333',
              //             fontSize: 10,
              //             textAnchor: 'middle',
              //             zIndex: 3,
              //           },
              //         },
              //       ],
              //       distance: -150,
              //       onClick({ view }) {
              //         state.nodeType = 'edg'
              //         state.drawer = true
              //         state.relationInfo = view.cell.getData()
              //       },
              //     },
              //   },
              // ],
            })
          })

          const dagreLayout = new DagreLayout({
            type: 'dagre',
            rankdir: 'TB',
            align: 'UL',
            ranksep: 100,
            nodesep: 55,
            controlPoints: true,
          })
          const model = dagreLayout.layout(data)
          //统计每个层下的个数，以计算y轴坐标
          let odsCount = 0
          let dwdCount = 0
          let dwsCount = 0
          let adsCount = 0
          model.nodes.forEach((node) => {
            //要根据模型层确定x轴，同一模型层x轴坐标相同
            //ntpye：83-ods;84-dwd;85-dws;86-ads
            const clientWidth = document.getElementById('flow').clientWidth
            let nodey = node.size.height + 20
            const nType = parseInt(node.data.nType)
            switch (state.modelData[nType]) {
              case 'ODS层':
                node.x = clientWidth / 8 - 80
                odsCount++
                nodey *= odsCount
                break
              case 'DWD层':
                node.attrs.body.fill = '#F0FAF2'
                node.attrs.body.stroke = '#18BA72'
                node.x = clientWidth / 4 + 80
                dwdCount++
                nodey *= dwdCount
                break
              case 'DWS层':
                node.attrs.body.fill = '#F5F7FF'
                node.attrs.body.stroke = '#8D7AF8'
                node.x = clientWidth / 2 + 80
                dwsCount++
                nodey *= dwsCount
                break
              case 'ADS层':
                node.attrs.body.fill = '#FFF5ED'
                node.attrs.body.stroke = '#F5A623'
                node.x = (clientWidth / 4) * 3 + 80
                adsCount++
                nodey *= adsCount
                break
              default:
                node.x = clientWidth / 8 - 80
                node.y = 20
            }
            node.y = 20 + nodey
          })
          state.graph.fromJSON(model)
          // state.graph.centerContent()
        },
        // 改变状态
        changeStatus(item) {
          state.nodeType = ''
          state.drawer = false
          if (item) {
            methods.assetsLineage(state.dataModelId)
          }
        },
        // 点击node
        boundEvent() {
          state.graph.on('node:click', ({ cell }) => {
            state.cell = cell
            state.nodeType = 'node'
            state.drawer = true
            api.model.getModalDetail({ id: cell.id }).then((res) => {
              if (res.code === 'SUCCESS') {
                state.modelInfo = res.data
              } else {
                ElNotification({
                  title: '提示',
                  message: '暂无数据',
                  type: 'warning',
                })
              }
            })
          })
        },
      }
      onMounted(() => {
        // const { currentProject } = toRefs(store.state.user)
        // state.currentProject = currentProject.value
        // methods.getModelData()
        methods.initFn()
      })
      onBeforeUnmount(() => {
        window.removeEventListener('resize', state.resizeFn)
        FlowGraph.destroy()
      })
      const params = toRefs(state)
      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .flow {
    width: 100%;
    height: 100%;

    :deep(.x6-graph-scroller) {
      &::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: rgb(177, 176, 176);
        border-radius: 2px;
      }
    }

    :deep {
      .drawer-modal-bg {
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }
</style>
