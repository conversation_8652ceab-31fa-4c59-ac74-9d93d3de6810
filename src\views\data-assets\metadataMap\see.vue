<template>
  <section class="container">
    <div class="container-box">
      <moduleName :info="{ name: '查看' }" />
      <div class="title">基础信息<span class="bor"></span></div>
      <div v-if="state.type === 'FILE'">
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">文件名称：</div>
              <div class="value">{{ state.info.name }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">文件类型：</div>
              <div class="value">{{ state.info.type }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">文件路径：</div>
              <div class="value">{{ state.info.location }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">文件权限：</div>
              <div class="value">{{ state.info.privileges }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">创建时间：</div>
              <div class="value">{{ state.info.createAt || '--' }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">文件大小：</div>
              <div class="value">{{ state.info.size || '--' }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">修改时间：</div>
              <div class="value more-line">{{ state.info.updateAt }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">所有者：</div>
              <div class="value more-line">{{ state.info.fileOwner }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="16">
            <div class="label">
              <div class="name">所属数据源：</div>
              <div class="value more-line">{{ state.info.datasourceName || '--' }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="16">
            <div class="label">
              <div class="name">描述信息：</div>
              <div class="value more-line">{{ state.info.description || '--' }}</div>
            </div>
          </n-col>
        </n-row>
      </div>
      <div v-else>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">API名称：</div>
              <div class="value">{{ state.info.name }}</div>
            </div>
          </n-col>
          <n-col :span="16">
            <div class="label">
              <div class="name">API路径：</div>
              <div class="value">{{ state.info.url }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">API方法：</div>
              <div class="value">{{ state.info?.extConf?.methodType || '--' }}</div>
            </div>
          </n-col>
          <n-col :span="16">
            <div class="label">
              <div class="name">API权限：</div>
              <div class="value">--</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">创建时间：</div>
              <div class="value">{{ state.info.createTime || '--' }}</div>
            </div>
          </n-col>
          <n-col :span="16">
            <div class="label">
              <div class="name">所有者：</div>
              <div class="value">{{ state.info.ownName || '--' }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="16">
            <div class="label">
              <div class="name">描述信息：</div>
              <div class="value more-line">{{ state.info.description || '--' }}</div>
            </div>
          </n-col>
        </n-row>
        <div class="title">配置信息<span class="bor"></span></div>
        <div class="table">
          <div class="table-label">
            <div class="table-label-title">请求参数：<span></span></div>
            <div class="table-label-content">
              <n-public-table
                :table-head-titles="state.tableHeadTitles"
                :tableData="state.tableData"
                :tableHeight="state.tableHeight"
                :showPagination="false"
                emptyText="暂无相关信息"
              />
            </div>
          </div>
          <div class="table-label">
            <div class="table-label-title">响应结果：<span></span></div>
            <div class="table-label-content">
              <n-public-table
                :table-head-titles="state.tableHeadTitlesResult"
                :tableData="state.tableDataResult"
                :tableHeight="state.tableHeight"
                :showPagination="false"
                emptyText="暂无相关信息"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <n-button variant="solid" class="footer-btn" @click.prevent="methods.cancelFn">返回</n-button>
    </div>
  </section>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  const store = useStore()
  const router = useRouter()
  // 获取当前组件实例
  const state = reactive({
    loading: false,
    info: {},
    id: '',
    type: '',
    tableHeadTitles: [
      { prop: 'num', name: '序号', width: '100px' },
      { prop: 'name', name: '参数名' },
      { prop: 'type', name: '类型' },
      { prop: 'code', name: '是否必须', width: '30px' },
      { prop: 'parentName', name: '描述信息' },
    ],
    tableHeadTitlesResult: [
      { prop: 'num', name: '序号', width: '100px' },
      { prop: 'name', name: '参数名' },
      { prop: 'type', name: '类型' },
    ],
    tableHeight: 100,
    tableData: {
      list: [],
    },
    tableDataResult: { list: [] },
  })

  const methods = {
    // 返回
    cancelFn() {
      router.go(-1)
    },
  }

  onMounted(() => {
    state.tableHeight = document.body.offsetHeight - 490
    state.id = router.currentRoute.value.query.id
    state.type = router.currentRoute.value.query.type
    state.loading = true
    api.assets.metadataMapDetail({ uid: state.id, type: state.type }).then((res) => {
      if (res.success) {
        if (state.type === 'FILE') {
          state.info = res.data.file
        } else {
          if (res.data.datasource.extConf) {
            res.data.datasource.extConf = JSON.parse(res.data.datasource.extConf)
            let params = res.data.datasource.extConf.params
            let result = res.data.datasource.extConf.mapping
            let num = 1
            result.forEach((val, ind) => {
              state.tableDataResult.list.push({
                num: ind + 1,
                name: val,
              })
            })
            if (typeof params === 'string') {
              try {
                params = JSON.parse(params)
              } catch (e) {
                console.log(e)
              }
            }
            if (typeof params === 'object') {
              for (let i in params) {
                state.tableData.list.push({
                  num: num,
                  name: i,
                })
                num++
              }
            } else {
              state.tableData.list = [{ num: 1, name: 'params' }]
            }
          }
          state.info = res.data.datasource
        }
      }
    })
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    position: relative;
    &-box {
      padding: 16px 20px 0 20px;
      box-sizing: border-box;
      height: calc(100% - 60px);
      .title {
        color: #000000;
        font-size: 14px;
        font-weight: bolder;
        padding: 12px 0;
        border-bottom: 1px solid #ebedf0;
        margin-bottom: 24px;
        margin-top: 10px;
        position: relative;
        .bor {
          position: absolute;
          width: 56px;
          height: 6px;
          background-color: #008ae7;
          opacity: 0.2;
          left: 0;
          bottom: 12px;
        }
      }
      .nancalui-row {
        margin-bottom: 12px;
        .nancalui-col {
          padding: 0 !important;
        }
      }
      .label {
        display: flex;
        justify-content: flex-start;
        line-height: 20px;
        // align-items: center;
        .name {
          width: 100px;
          color: #333;
          font-weight: bolder;
          font-size: 14px;
          text-align: right;
        }
        .value {
          width: calc(100% - 100px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #666666;
          font-size: 14px;
          .space-span {
            margin-right: 5px;
          }
          &.more-line {
            overflow: auto;
            white-space: wrap;
          }
        }
      }
    }
    .table {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-label {
        width: calc(50% - 8px);
        &-title {
          color: #333333;
          font-size: 14px;
          font-weight: bolder;
          margin-bottom: 8px;
          span {
            color: #666666;
            font-weight: normal;
          }
        }
      }
    }
    .footer {
      width: calc(100% + 20px);
      margin-top: 10px;
      margin-left: -10px;
      height: 60px;
      line-height: 60px;
      background-color: #fff;
      overflow: hidden;
      border-radius: 8px 8px 0 0;
      text-align: center;
    }
  }
</style>
