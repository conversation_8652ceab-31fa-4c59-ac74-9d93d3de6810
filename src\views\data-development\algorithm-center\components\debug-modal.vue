<template>
  <!-- 算法中心 -调试页面 -->
  <div class="debug-modal-page">
    <n-modal
      v-model="state.dialogVisible"
      bodyClass="debug-modal-page-dialog commonDialog"
      :draggable="false"
      title="算法调试"
      width="1000px"
      :close-on-click-overlay="false"
      :append-to-body="false"
      :before-close="handleClose"
    >
      <div class="modal-content">
        <div class="data-title">示例数据</div>
        <n-public-table
          rowKey="number"
          :isDisplayAction="true"
          :table-head-titles="state.tableHeadTitles"
          :showPagination="false"
          :tableHeight="246"
          :actionWidth="120"
          :tableData="state.tableData"
          :isNeedSelection="false"
        >
          <!-- 参数名 -->

          <template v-for="(item, index) in state.paramsArray" :key="index" #[item]="{ editor }">
            <n-input
              :key="state.tableKey[editor.rowIndex][index]"
              v-model="editor.row[item]"
              placeholder=" "
              :class="editor.row[item] ? '' : 'required-input'"
              @blur="eNameBlur(editor, index)"
            />
          </template>

          <template #editor="{ editor }">
            <div class="edit-box">
              <n-button class="has-right-border" variant="text" @click.prevent="addRow(editor)"
                >新增</n-button
              >
              <n-button
                class="has-right-border"
                variant="text"
                :disabled="editor.row.number === 1"
                @click.prevent="reduceRow(editor.row)"
                >移除</n-button
              >
            </div></template
          >
        </n-public-table>

        <div class="run-data-box">
          <n-tabs v-model="state.activeName" class="tabs">
            <n-tab title="运行日志" id="log" />
            <n-tab title="运行结果" id="result" />
          </n-tabs>
          <div class="view-box" v-show="state.activeName === 'log'" v-loading="state.loading">
            <div v-if="state.logs" class="view-box-content scoll scroll-bar-style">
              {{ state.logs }}</div
            >
            <div v-else class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
              <div class="empty-text">暂无运行日志</div>
            </div>
          </div>
          <div class="view-box" v-show="state.activeName === 'result'" v-loading="state.loading">
            <div v-if="state.result" class="view-box-content">
              <json-viewer :value="state.result" :expand-depth="5" copyable boxed sort />
            </div>
            <div v-else class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
              <div class="empty-text">暂无运行结果</div>
            </div>
          </div>
          <div class="button-box">
            <n-button
              v-if="!state.debuging"
              size="sm"
              variant="solid"
              color="primary"
              @click.prevent="startDebug"
              >开始调试</n-button
            >
            <n-button v-else size="sm" variant="solid" color="primary" @click.prevent="stopDebug"
              >中止调试</n-button
            >
          </div>
        </div>
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button size="sm" variant="solid" color="primary" @click.prevent="handleClose"
            >确 定</n-button
          >
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { ref, reactive } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import axios from 'axios'
  export default {
    title: 'List',
    components: {},
    props: {},
    emits: ['deBugDataChange'],
    setup(props, { emit }) {
      const router = useRouter()
      const allDataSourceTable = ref()
      const state = reactive({
        tableData: { list: [] },
        activeName: 'log',
        key: 1,
        loading: false,
        dialogVisible: false,
        tableHeadTitles: [],
        logs: '',
        result: '',
        debuging: false, //是否调试中
        parentData: {}, //父级数据
        tableKey: [[]],
        params: {},
        paramsArray: [],
        editId: '',
        source: null, //接口请求的唯一标识
      })
      const methods = {
        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
          state.tableData.list = []
          state.params = {}
          state.paramsArray = []
          state.logs = ''
          state.result = ''
        },
        eNameBlur(editor, index) {
          state.tableKey[editor.rowIndex][index] = state.tableKey[editor.rowIndex][index] + 1
        },
        //校验入参是否填写完毕
        validateParames() {
          let pass = true
          state.tableData.list.forEach((item) => {
            Object.keys(item).forEach((key) => {
              if (item[key] === '') {
                pass = false
              }
            })
          })
          return pass
        },
        //开始调试
        startDebug() {
          let _pass = methods.validateParames()
          if (_pass) {
            state.source = axios.CancelToken.source()
            let parames = {
              ...state.parentData.ruleForm,
              parameterList: state.parentData.tableData?.list || [],
              testData: JSON.stringify(state.tableData.list),
            }
            if (state.parentData.ruleForm.createMethod === 'EXTERNAL_API') {
              //外部调用
              delete parames.code
            } else {
              delete parames.apiUrl
            }
            if (state.editId) {
              parames.id = state.editId
            }
            delete parames.logBO
            state.debuging = true
            state.loading = true
            state.parentData.testData = state.tableData

            api.dataDev
              .algorithmDebugTestSync(parames, state.source.token)
              .then((res) => {
                let { success, data } = res
                if (success) {
                  let { log, result, state: status } = data
                  state.parentData.ruleForm.state = status || null //记录状态
                  state.parentData.data = data
                  emit('deBugDataChange', state.parentData)
                  if (status === 'SUCCESS' || status === 'FAILURE') {
                    ////未调用中止接口、并且状态为非成功和失败就一直请求

                    if (status === 'SUCCESS') {
                      ElNotification({
                        title: '提示',
                        message: '调试成功',
                        type: 'success',
                      })
                    }
                    if (status === 'FAILURE') {
                      ElNotification({
                        title: '提示',
                        message: '调试失败!',
                        type: 'error',
                      })
                    }
                    state.debuging = false
                    state.logs = log
                    state.result = result || null
                    state.loading = false
                  }
                } else {
                  state.debuging = false
                  state.loading = false
                  state.parentData.ruleForm.state = null
                  emit('deBugDataChange', state.parentData)
                }
              })
              .catch((err) => {
                if (axios.isCancel(err)) {
                  console.log('请求取消')
                }
                state.debuging = false
                state.loading = false
                state.parentData.ruleForm.state = null
                emit('deBugDataChange', state.parentData)
              })
          }
        },
        //中止调试
        stopDebug() {
          state.source && state.source.cancel('请求取消')
          // api.dataDev
          //   .algorithmDebugStop({ id: state.parentData.ruleForm.processCode })
          //   .then((res) => {
          //     let { success } = res
          //     if (success) {
          //       state.debuging = false
          //       state.loading = false
          //       state.logs = ''
          //       state.result = ''
          //     }
          //   })
        },

        show(data) {
          state.dialogVisible = true
          state.parentData = data
          let { tableData } = data

          //动态 传入参数 表头
          let _tableData = tableData?.list?.filter((item) => {
            return item.parameterType === 'INPUT'
          })

          let box_width = 940
          state.tableHeadTitles = []
          state.tableKey[0] = []
          _tableData?.forEach((item) => {
            state.tableHeadTitles.push({
              prop: item.parameterName,
              name: item.parameterName,
              slot: item.parameterName,
              width: box_width / 6,
            })
            state.params[item.parameterName] = ''
            state.tableKey[0].push(1)

            state.paramsArray.push(item.parameterName)
          })
          state.params.number = 1
          state.tableData.list.push({ ...state.params })
          // state.tableKey++
        },
        //新增行
        addRow(editor) {
          state.tableKey.push([])
          state.tableHeadTitles.forEach(() => {
            state.tableKey[editor.rowIndex + 1].push(1)
          })
          state.tableData.list.push({ ...state.params })
          state.tableData.list?.map((val, index) => {
            return Object.assign(val, { number: index + 1 })
          })
        },
        //移除行
        reduceRow(row) {
          state.tableData.list?.splice(row.number - 1, 1)
          state.tableData.list?.map((val, index) => {
            return Object.assign(val, { number: index + 1 })
          })
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId
      })
      return {
        allDataSourceTable,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .debug-modal-page {
    font-family: PingFangSC-Regular, PingFang SC;
    :deep(.nancalui-modal) {
      .nancalui-modal__body {
        max-height: 670px;
        padding: 4px 30px 0;
      }
    }
    :deep(.nancalui-table) {
      .nancalui-table__cell {
        padding-top: 4px;
      }

      .nancalui-table__empty {
        padding: 0;
      }
      .nancalui-input.required-input {
        border: 1px solid red;
        border-radius: 4px;
        .nancalui-input__wrapper {
          border: none;
        }
      }
    }
    .data-title {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: 20px;
      margin-bottom: 8px;
      &:before {
        content: '*';
        color: #f52f3e;
        display: inline-block;
        vertical-align: middle;
        margin-right: 2px;
        font-size: 12px;
      }
    }
    .run-data-box {
      position: relative;
      margin-top: 25px;
      .view-box {
        height: 200px;
        .view-box-content {
          &.scoll {
            overflow-y: auto;
            white-space: pre;
            padding: 20px 20px 20px 0;
          }
          height: 100%;
          padding: 0;
        }
        :deep(.jv-container.jv-light) {
          background-color: #f7f8fa;
          padding: 10px 0 30px 0;
          height: 100%;
          overflow-y: auto;
          .jv-code {
            height: 100%;
            overflow: auto;
            &::-webkit-scrollbar {
              width: 6px; // 横向滚动条
              height: 6px; // 纵向滚动条 必写
            }

            // 滚动条的滑块
            &::-webkit-scrollbar-thumb {
              background-color: #e1e1e1;
              border-radius: 3px;
            }
          }
        }
        :deep(.jv-container.boxed) {
          border: none;
          &:hover {
            box-shadow: none;
          }
        }
      }
      .button-box {
        position: absolute;
        right: 0;
        top: 0;
      }
    }

    .table-no-content {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      img {
        display: block;
        margin: 0 auto;
        width: 140px;
        height: auto;
      }
      .empty-text {
        color: #999999;
        font-size: 12px;
        text-align: center;
        margin-top: 20px;
      }
    }
  }
</style>
