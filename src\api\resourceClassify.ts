import request from '@/utils/request';
const ASSETS = '/api/govern-management';

//资源分类列表
export const queryResourceClassify = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/list`,
    method: 'post',
    data,
  })

//资源分类新增
export const addResourceClassify = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/create`,
    method: 'post',
    data,
  })

//资源分类更新
export const updateResourceClassify = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/update`,
    method: 'post',
    data,
  })

//资源分类删除
export const deleteResourceClassify = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/delete/${data.id}`,
    method: 'post',
    data,
  })

  //资源子级分类
export const getResourceClassifyChildren = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/children/${data.code}`,
    method: 'post',
  })

  //资源分类列表树
export const getResourceClassifyTree = (data: any) =>
  request({
    url: `${ASSETS}/data-resource-category/tree-list`,
    method: 'post',
    data,
  })


  //资源目录列表
export const getResourceDirectoryList = (data: any) =>
  request({
    url: `${ASSETS}/data-resource/list`,
    method: 'post',
    data,
  })