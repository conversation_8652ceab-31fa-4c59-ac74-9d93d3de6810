<template>
  <section class="algorithm-center-add container">
    <section class="algorithm-center-add-box scroll-bar-style">
      <div class="page-title">
        <moduleName :info="{ name: (state.editId ? '编辑' : '新增') + '算法' }" />
      </div>
      <!-- <n-button @click.prevent="domToPng">取消</n-button>
      <img :src="state.file" alt="" /> -->
      <n-form
        ref="ruleForm"
        :data="state.ruleForm"
        :rules="state.rules"
        label-align="start"
        label-width="80px"
        class="scroll-bar-style"
      >
        <n-form-item label="新增方式：" field="createMethod">
          <div class="form-item-inline">
            <n-radio-group
              direction="row"
              v-model="state.ruleForm.createMethod"
              :disabled="state.editId"
            >
              <n-radio value="NEW">新增</n-radio>
              <n-radio value="IMPORT">文件导入</n-radio>

              <n-radio value="EXTERNAL_API">API集成</n-radio>
              <!--              <n-radio value="aa" disabled>SDK工具包导入</n-radio>-->
            </n-radio-group>
            <n-upload
              v-if="state.ruleForm.createMethod === 'IMPORT' && !state.editId"
              :class="{
                'upload-demo': true,
              }"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :accept="state.fileAccept"
              :limit="1"
              droppable
              @click.prevent=""
            >
              <div class="upload-content">
                <n-button class="up-load-box">
                  <SvgIcon class="icon-upload" icon="icon-upload" />上传文件</n-button
                >
              </div>
            </n-upload>
            <div
              class="upload-demo-box"
              v-if="state.ruleForm.createMethod === 'IMPORT' && !state.editId"
            >
              <div v-if="state.ruleForm.fileList?.length" class="file-data">
                <div>{{ state.fileData.name }}</div>
                <SvgIcon
                  class="icon-add-svg"
                  icon="close-icon"
                  title="关闭"
                  @click.stop.prevent="onRemove"
              /></div>
              <div class="nancal-upload__tip">
                <span @click.prevent.stop="downTemplate">下载模板</span>
                <SvgIcon class="template-icon-svg" icon="template-icon" /> </div
            ></div>
          </div>
        </n-form-item>
        <div class="box-title">
          <span>基础信息<i></i></span>
        </div>
        <div class="form-item-inline">
          <n-form-item label="中文名称：" field="cnName">
            <n-input
              v-model="state.ruleForm.cnName"
              maxlength="30"
              clearable
              placeholder="请输入"
              :disabled="state.editId"
            />
          </n-form-item>
          <n-form-item label="英文名称：" field="name">
            <n-input
              v-model="state.ruleForm.name"
              maxlength="80"
              clearable
              placeholder="请输入"
              :disabled="state.editId"
            />
          </n-form-item>
        </div>
        <div class="form-item-inline">
          <n-form-item label="算法标签：" field="tagList">
            <el-tree-select
              ref="selectTree"
              v-model="state.ruleForm.tagList"
              :data="state.tagOptions"
              :disabled="state.editId"
              node-key="key"
              :props="{
                label: 'name',
                value: 'key',
                children: 'children',
              }"
              multiple
              show-checkbox
              :render-after-expand="false"
              filterable
              clearable
            />
          </n-form-item>
          <!--          <n-form-item-->
          <!--            v-if="state.ruleForm.createMethod === 'EXTERNAL_API'"-->
          <!--            label="集成URL："-->
          <!--            field="apiUrl"-->
          <!--          >-->
          <!--            <n-input-->
          <!--              v-model="state.ruleForm.apiUrl"-->
          <!--              maxlength="80"-->
          <!--              clearable-->
          <!--              placeholder="请输入"-->
          <!--            />-->
          <!--          </n-form-item>-->
        </div>

        <n-form-item label="描述信息：" class="description-textarea label_align_required">
          <n-textarea
            v-model="state.ruleForm.description"
            :autosize="{ minRows: 3 }"
            show-count
            maxlength="200"
            placeholder="请输入"
          />
        </n-form-item>
        <div class="box-title" v-show="state.ruleForm.createMethod !== 'EXTERNAL_API'">
          <span>算法脚本<i></i></span><span class="tips">（当前Python环境仅支持3.6.8版本）</span>
        </div>
        <div v-show="state.ruleForm.createMethod !== 'EXTERNAL_API'">
          <n-form-item field="code">
            <n-textarea
              v-model="state.ruleForm.code"
              :disabled="state.editId && state.ruleForm.createMethod === 'IMPORT'"
              resize="none"
              class="script-textarea scroll-bar-style"
              color="primary"
              placeholder="当前仅支持Python、R、Jupyter、Java、C++语言......."
            />
          </n-form-item>
        </div>

        <div
          class="script-dom script-img"
          ref="scriptDom"
          :style="{ 'background-image': state.scriptDomImage }"
        >
          <div
            :style="{
              whiteSpace: 'pre',
              'font-size': '24px',
              color: '#fff',
              overflow: 'hidden',
              'line-height': '1.5',
              padding: '20px',
              'padding-top': '60px',
            }"
          >
            {{ state.ruleForm.code }}
          </div>
        </div>

        <div class="box-title">
          <span>参数设置<i></i></span>
        </div>
        <div class="table-box">
          <n-public-table
            rowKey="number"
            :isDisplayAction="true"
            :table-head-titles="state.tableHeadTitles"
            :showPagination="false"
            :tableHeight="state.tableHeight"
            :tableData="state.tableData"
            :isNeedSelection="false"
          >
            <!-- 参数类型 -->
            <template #parameterType="{ editor }">
              <n-select
                v-model="editor.row.parameterType"
                placeholder="请选择"
                :disabled="state.disabled"
                :class="editor.row.parameterTypePass ? '' : 'required-input'"
                :options="state.parameterTypeOptions"
                @value-change="parameterTypeChange(editor)"
              />
            </template>
            <!-- 参数名 -->
            <template #parameterName="{ editor }">
              <n-input
                :disabled="state.disabled"
                v-model="editor.row.parameterName"
                placeholder=""
                maxlength="80"
                :class="editor.row.parameterNamePass ? '' : 'required-input'"
                :key="state.tableKey[editor.rowIndex]"
                @blur="eNameBlur(editor)"
              />
            </template>

            <!-- 取值类型 -->
            <template #valueType="{ editor }">
              <n-select
                v-model="editor.row.valueType"
                placeholder="请选择"
                :class="editor.row.valueTypePass ? '' : 'required-input'"
                :disabled="state.disabled"
                @value-change="valueTypeChange(editor.row)"
              >
                <n-option
                  v-for="item in state.valueTypeOptions"
                  :key="item.name"
                  :name="item.cnName + '(' + item.name + ')'"
                  :value="item.name"
                />
              </n-select>
            </template>
            <!-- 字段长度 -->
            <template #description="{ editor }">
              <n-input
                :disabled="state.disabled"
                v-model="editor.row.description"
                placeholder=" "
                maxlength="200"
              />
            </template>
            <template #editor="{ editor }">
              <div class="edit-box">
                <n-button class="has-right-border" variant="text" @click.prevent="addRow(editor)"
                  >新增</n-button
                >
                <n-button
                  class="has-right-border"
                  variant="text"
                  :disabled="editor.row.number === 1"
                  @click.prevent="reduceRow(editor.row)"
                  >移除</n-button
                >
              </div></template
            >
          </n-public-table>
        </div>
      </n-form>
    </section>

    <div class="container-footer">
      <div class="my-appliction">
        <n-button variant="solid" color="primary" @click.prevent="debug">算法调试</n-button>
        <n-button
          variant="solid"
          color="primary"
          :loading="state.loading"
          @click.prevent="save('CREATED')"
          :disabled="!(state.ruleForm.state === 'SUCCESS')"
          >保存</n-button
        >
        <n-button
          v-if="!state.saveMethod || state.saveMethod === 'TEMPORARY_SAVE'"
          variant="solid"
          color="primary"
          :loading="state.loading"
          @click.prevent="save('TEMPORARY_SAVE')"
          :disabled="!(state.ruleForm.state === 'SUCCESS')"
          >暂存</n-button
        >
        <n-button @click.prevent="goBack">取消</n-button>
      </div>
    </div>
    <debugModal ref="debugModalDom" @deBugDataChange="deBugDataChange" />
  </section>
</template>

<script>
  import { checkCName, checkName } from '@/utils/validate'
  import { onMounted, ref, reactive, watch } from 'vue'
  import api from '@/api/index'
  import debugModal from './components/debug-modal'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import domtoimage from 'dom-to-image'
  export default {
    name: 'AlgorithmCenterAdd',
    components: { debugModal },
    setup() {
      const router = useRouter()
      const state = reactive({
        loading: false,
        editId: false,
        disabled: false,
        tableKey: [1],
        ruleForm: {
          createMethod: 'NEW',
          cnName: '',
          name: '',
          code: '', //脚本
          fileList: [],
          tagList: [], //选中的标签item
          processCode: null, //调试code
          state: null, //调试状态
          description: '',
          apiUrl: '',
        },

        rules: {
          createMethod: [{ required: true, message: '请选择', trigger: 'change' }],
          // cnName: [{ required: true, validator: checkCName, trigger: 'blur' }],
          cnName: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'dataDev', 'checkAlgoName', {
                  cnName: state.ruleForm.cnName || null,
                  id: state.editId || null,
                }),
              trigger: 'blur',
            },
          ],
          // name: [{ required: true, validator: checkName, trigger: 'blur' }],
          name: [
            {
              required: true,
              validator: (...args) =>
                checkName(...args, 'dataDev', 'checkAlgoName', {
                  name: state.ruleForm.name || null,
                  id: state.editId || null,
                }),
              trigger: 'blur',
            },
          ],
          // tag: [{ type: 'number', required: true, message: '请选择', trigger: 'change' }],
          code: [{ required: true, message: '请输入或者上传脚本', trigger: 'blur' }],
          apiUrl: [{ required: true, message: '请输入集成URL', trigger: 'blur' }],
        },
        tagOptions: [],
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'parameterType', name: '参数类型', slot: 'parameterType' },
          { prop: 'parameterName', name: '参数名', slot: 'parameterName' },
          { prop: 'valueType', name: '取值类型', slot: 'valueType' },
          { prop: 'description', name: '描述信息', slot: 'description' },
        ],
        tableHeight: 325,
        tableData: {
          list: [
            {
              number: 1,
              parameterType: '',
              parameterName: '',
              valueType: '',
              description: '',
              sort: 1,
              parameterTypePass: false,
              parameterNamePass: false,
              valueTypePass: false,
            },
          ],
        },
        parameterTypeOptions: [
          {
            name: '入参',
            value: 'INPUT',
          },
          {
            name: '出参',
            value: 'OUTPUT',
          },
        ],
        valueTypeOptions: [],
        fileAccept: '.py',
        fileData: '',
        testData: [],
        haveInAndOutParameter: false, //已设置出参和入参
        file: '',
        scrollTop: 0,
        scriptDomImage: '',
        // scriptDomImage: `url('../../../../src/assets/img/dev/algorithm-num-${
        //   Math.floor(Math.random() * 8) + 1
        // }.png')`,
        saveMethod: null, //保存还是暂存
      })
      const ruleForm = ref()
      const debugModalDom = ref()
      const scriptDom = ref()
      const selectTree = ref()
      const methods = {
        getAssetsImages() {
          let num = Math.floor(Math.random() * 8) + 1
          return new URL(`/src/assets/img/dev/algorithm-num-${num}.png`, import.meta.url).href //本地文件路径
        },
        async toPng(dom) {
          let _scrollTop = document.querySelectorAll('.script-textarea')[0].scrollTop
          state.scrollTop = '-' + _scrollTop + 'px'
          const bolb = await domtoimage?.toBlob(dom, { height: 373 })?.catch((error) => {
            console.error('oops, something went wrong!', error)
          })
          return bolb
        },
        async domToPng() {
          const blob = await methods.toPng(scriptDom.value)
          state.file = blob
          const file = new File([blob], `snapshot${new Date().getTime()}.png`)
          const params = new FormData()
          params.append('file', file)
        },
        //调试成功后 参数脚本和新增方式改变 需要重新调试
        clearDeBugData() {
          state.ruleForm.processCode = null
          state.ruleForm.state = null
          state.testData = []
        },
        //调试数据返回
        deBugDataChange(data) {
          state.ruleForm.processCode = data.ruleForm?.processCode
          state.ruleForm.state = data.ruleForm?.state
          state.ruleForm.logBO = data?.data || {}

          state.testData = data.testData?.list
        },
        //参数名校验
        eNameBlur(editor, data = state.tableData?.list || []) {
          let { row, rowIndex } = editor
          let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
          let res = regex.test(row.parameterName)
          row.parameterNamePass = false
          if (res && row.parameterName?.length > 1 && row.parameterName?.length < 81) {
            row.parameterNamePass = true
            data.forEach((item) => {
              //遍历对比所有参数名是否相同 相同不通过 （入参和入参比较  出参和出参）
              if (row.parameterType === item.parameterType) {
                if (row.number !== item.number && item.parameterName === row.parameterName) {
                  row.parameterNamePass = false
                }
              }
              if (!row.parameterType) {
                //参数类型还没选 -跟所有的比
                if (row.number !== item.number && item.parameterName === row.parameterName) {
                  row.parameterNamePass = false
                }
              }
            })
          }

          state.tableKey[rowIndex] = state.tableKey[rowIndex] + 1
          return row.parameterNamePass
        },
        //参数类型change
        parameterTypeChange(editor) {
          let { row } = editor

          if (row.parameterType) {
            row.parameterTypePass = true
            methods.eNameBlur(editor)
          }
        },
        //参数类型change
        valueTypeChange(row) {
          if (row.valueType) {
            row.valueTypePass = true
          }
        },
        //获取示例代码
        getAlgorithmTemplateCode() {
          api.dataDev.algorithmExampleCode().then((res) => {
            let { success, data } = res
            if (success) {
              state.ruleForm.code = data
            }
          })
        },
        //获取标签列表
        getAlgorithmTagList() {
          api.documentManage.getTagLibraryClassListHasTag().then((res) => {
            let { success, data } = res
            if (success) {
              state.tagOptions = data
            }
          })
        },
        //文件上传
        beforeUpload(_data) {
          let renameFile = _data[0].file
          const name = renameFile.name.replace(/\s+/g, '')
          const UploadRawFile = new File([renameFile], name, { type: renameFile.type })
          state.fileData = UploadRawFile
          state.ruleForm.fileList = [UploadRawFile]
          const formData = new FormData()
          formData.append('file', UploadRawFile)
          api.dataDev.algorithmParsePython(formData).then((res) => {
            let { success, data } = res
            if (success) {
              state.ruleForm.code = data
            }
          })

          return false
        },
        // 模板下载
        downTemplate() {
          api.dataDev.algorithmDownload().then((res) => {
            // 下载文件
            const blob = new Blob([res], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
            })
            const link = document.createElement('a')
            link.download = '算法模板.py'
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          })
        },
        //移除文件
        onRemove() {
          state.ruleForm.code = ''
          state.ruleForm.fileList = []
          state.fileData = ''
        },
        //新增行
        addRow() {
          state.tableData.list.push({
            number: 1,
            parameterType: '',
            parameterTypePass: false,
            parameterName: '',
            parameterNamePass: false,
            valueType: '',
            valueTypePass: false,
            description: '',
            sort: 1,
          })
          state.tableKey.push(1)
          state.tableData.list?.map((val, index) => {
            return Object.assign(val, { number: index + 1 })
          })
        },
        //移除行
        reduceRow(row) {
          state.tableData.list?.splice(row.number - 1, 1)
          state.tableData.list?.map((val, index) => {
            return Object.assign(val, { number: index + 1 })
          })
        },
        // 获取字段类型
        getFieldTypeList() {
          api.model.getFieldType({}).then((res) => {
            state.valueTypeOptions = res.data
          })
        },
        //校验参数 是否通过
        checkParameter() {
          let pass = true
          state.tableData.list.forEach((item) => {
            //参数类型 参数名 取值类型 任何一个不通过即为不通过
            if (!item.parameterTypePass || !item.parameterNamePass || !item.valueTypePass) {
              pass = false
            }
          })
          return pass
        },
        //调试
        debug() {
          ruleForm.value.validate((valid) => {
            if (valid) {
              if (methods.checkParameter()) {
                let input_array =
                  state.tableData.list.filter((item) => item.parameterType === 'INPUT') || []

                let output_array =
                  state.tableData.list.filter((item) => item.parameterType === 'OUTPUT') || []

                if (input_array.length && output_array.length) {
                  //必须有入参才能调试
                  let data = {
                    ruleForm: { ...state.ruleForm },
                    tableData: state.tableData,
                  }
                  let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
                  data.ruleForm.tagList =
                    activeOptions?.map((val) => {
                      return { text: val.name, color: val.color, id: val.id || null }
                    }) || []
                  debugModalDom.value?.show(data)
                } else {
                  ElNotification({
                    title: '提示',
                    message: '出参和入参是必须的!',
                    type: 'warning',
                  })
                }
              } else {
                ElNotification({
                  title: '提示',
                  message: '请正确补全参数必填项',
                  type: 'warning',
                })
              }
            } else {
              return false
            }
          })
        },
        //出参 入参sort分别排序
        parameterTypeSort() {
          let input_array = []
          let output_array = []
          state.tableData.list.forEach((item) => {
            if (item.parameterType === 'INPUT') {
              input_array.push(item)
            }
            if (item.parameterType === 'OUTPUT') {
              output_array.push(item)
            }
          })
          input_array?.map((val, index) => {
            return Object.assign(val, { sort: index + 1 })
          })
          output_array?.map((val, index) => {
            return Object.assign(val, { sort: index + 1 })
          })

          state.haveInAndOutParameter = input_array.length && output_array.length

          return [...input_array, ...output_array]
        },
        //保存算法
        save(saveMethod) {
          ruleForm.value.validate(async (valid) => {
            if (valid) {
              if (methods.checkParameter()) {
                let tableDataSort = methods.parameterTypeSort()
                if (!state.haveInAndOutParameter) {
                  ElNotification({
                    title: '提示',
                    message: '出参和入参是必须的',
                    type: 'warning',
                  })
                  return
                }

                let interFaceName = 'algorithmCreate'
                let _message = '新增算法成功'
                let _parames = {
                  ...state.ruleForm,
                  parameterList: tableDataSort,
                  testData: JSON.stringify(state.testData),
                }
                let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
                _parames.tagList =
                  activeOptions?.map((val) => {
                    return { text: val.name, color: val.color, id: val.id || null }
                  }) || []
                if (state.ruleForm.createMethod === 'EXTERNAL_API') {
                  //外部调用
                  // delete _parames.code
                } else {
                  delete _parames.apiUrl
                }
                if (saveMethod === 'TEMPORARY_SAVE') {
                  _message = '暂存算法成功'
                }
                if (state.editId) {
                  interFaceName = 'algorithmUpdate'
                  _parames.id = state.editId
                  _message = '算法更新成功'
                }

                _parames.saveMethod = saveMethod //暂存还是保存

                //上传快照

                const blob = await methods.toPng(scriptDom.value)
                state.file = new File([blob], `snapshot${new Date().getTime()}.png`)
                const _file = new FormData()
                _file.append('file', state.file)
                api.dataDev.algorithmImageUpload(_file).then((reslut) => {
                  let { success, data } = reslut
                  if (success) {
                    _parames.coverUrl = data

                    state.loading = true
                    api.dataDev[interFaceName](_parames)
                      .then((res) => {
                        state.loading = false
                        let { success } = res
                        if (success) {
                          ElNotification({
                            title: '提示',
                            message: _message,
                            type: 'success',
                          })
                          router.push({
                            name: 'algorithmCenter',
                          })
                        }
                      })
                      .catch(() => {
                        state.loading = false
                      })
                  }
                })
              } else {
                ElNotification({
                  title: '提示',
                  message: '请补全参数必填项',
                  type: 'warning',
                })
              }
            } else {
              return false
            }
          })
        },
        goBack() {
          router.go(-1)
        },
        //算法详情
        getAlgorithmDetail() {
          api.dataDev.algorithmDetail({ id: state.editId }).then((res) => {
            let { success, data } = res
            if (success) {
              data.parameterList?.map((item, index) => {
                let _parameterTypePass = false
                let _valueTypePass = false
                if (item.parameterType) {
                  _parameterTypePass = true
                }
                if (item.valueType) {
                  _valueTypePass = true
                }

                return Object.assign(item, {
                  number: index + 1,
                  parameterTypePass: _parameterTypePass,
                  valueTypePass: _valueTypePass,
                  parameterNamePass: methods.eNameBlur(
                    { row: item, rowIndex: index },
                    data.parameterList,
                  ),
                })
              })
              state.tableData = { list: data.parameterList }
              Object.keys(state.ruleForm).forEach((key) => {
                state.ruleForm[key] = data[key] || ''
              })
              state.ruleForm.tagList = data?.tagList?.map((item) => 'tag_' + item.id) || []
              state.ruleForm.fileList = []
            }
          })
        },
      }

      // 参数脚本和新增方式改变 需要重新调试
      watch(
        [() => state.ruleForm.createMethod, () => state.ruleForm.code, () => state.tableData],
        () => {
          methods.clearDeBugData()
        },
        {
          deep: true,
        },
      )
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId
        state.saveMethod = router.currentRoute.value.query.displayStatus || null
        const imgSrc = methods.getAssetsImages()
        state.scriptDomImage = `url(${imgSrc})`
        methods.getFieldTypeList()
        methods.getAlgorithmTagList()

        if (state.editId) {
          methods.getAlgorithmDetail()
        } else {
          methods.getAlgorithmTemplateCode()
        }
      })

      return {
        state,
        ruleForm,
        debugModalDom,
        scriptDom,
        selectTree,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .algorithm-center-add {
    &-box {
      height: calc(100% - 60px);
      padding: 12px 20px 20px;
      overflow-y: auto;
      background-color: #fff;

      border-radius: 4px;

      .nancalui-form {
        margin-top: 14px;
        .nancalui-form__item--horizontal {
          .el-select,
          .nancalui-input,
          .nancalui-select {
            width: 430px;
          }
        }
        .form-item-inline {
          display: flex;
          align-items: center;
          .nancalui-form__item--horizontal {
            margin-right: 40px;
          }
          .upload-demo {
            .upload-content {
              display: flex;
              align-items: center;
              :deep(.up-load-box) {
                margin-left: 12px;
                .button-content {
                  color: #000;
                }
              }
            }
          }
          .upload-demo-box {
            display: flex;
            align-items: center;

            .file-data {
              display: flex;
              align-items: center;
              margin-left: 6px;
              div {
                margin-right: 8px;
                color: #666666;
                font-weight: 400;
                font-size: 12px;
                line-height: 20px;
              }
              svg {
                font-size: 16px;
                cursor: pointer;
              }
            }
            .nancal-upload__tip {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 92px;
              height: 32px;
              margin-left: 12px;
              color: $themeBlue;
              font-size: 14px;
              border-radius: 4px;
              cursor: pointer;
              &:hover {
                background: #f0f7ff;
              }
              span {
                margin-right: 4px;
              }
              svg {
                font-size: 16px;
              }
            }
          }
        }

        :deep(.nancalui-form__item--horizontal) {
          &.description-textarea {
            width: 1060px;
          }

          .script-textarea {
            height: 373px;

            &::placeholder {
              line-height: 363px;
              text-align: center;
            }
          }
        }
        .table-box {
          min-height: 325px;
          :deep(.nancalui-table) {
            .nancalui-table__cell {
              padding-top: 4px;
            }

            .nancalui-input,
            .nancalui-select {
              &.required-input {
                border: 1px solid red;
                border-radius: 4px;
                .nancalui-select__selection {
                  border: none;
                }
                .nancalui-input__wrapper {
                  background-color: transparent;
                  border: none;
                }
              }
            }
          }
        }
        .script-dom {
          width: 746px;
          height: 0;
          overflow: hidden;
          white-space: pre;
          .dom-box {
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        .script-img {
          background: url('../../../../src/assets/img/dev/algorithm-num-1.png') no-repeat;
          background-size: 100% 100%;
          border-radius: 20px;
          // width: 746px;
          // overflow: hidden;
        }

        .box-title {
          height: 48px;
          margin-bottom: 20px;
          color: #000;
          font-weight: bolder;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          line-height: 48px;
          border-bottom: 1px solid #ebedf0;

          span {
            position: relative;
            width: max-content;
            &:before {
              display: inline-block;
              margin-right: 2px;
              color: #f52f3e;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              vertical-align: middle;
              content: '*';
            }
          }

          i {
            position: absolute;
            right: 0;
            bottom: -2px;
            left: 0;
            height: 6px;
            background: #008ae7;
            opacity: 0.2;
          }
          .tips {
            margin-bottom: 10px;
            color: #f54446;
            font-size: 12px;
          }
        }
      }
    }
    .container-footer {
      position: absolute;
      right: 0;
      bottom: 0;
      box-sizing: border-box;
      width: 100%;
      height: 60px;
      padding-right: 20px;
      line-height: 60px;
      text-align: center;
      background: #fff;
      border-top: 1px solid rgba(200, 200, 200, 0.35);
    }
  }
</style>
