<template>
  <div class="scene-table-link">
    <div id="svgBox" :class="{ 'page-mid': true, 'chart-box': true, isEmpty: isEmpty }">
      <div :id="containerId" class="trend-chart"></div>
    </div>
  </div>
</template>
<script>
  import { reactive, onBeforeUnmount, toRefs, provide } from 'vue'
  import { Graph, Shape } from '@antv/x6'
  // import api from '@/api'
  export default {
    name: '',
    components: {},
    props: {
      containerId: {
        type: String,
        default: 'containerEr', // 盒子id
      },
      isEmpty: {
        type: Boolean,
        default: false,
      },
      sqlType: {
        type: Number,
        default: 1,
      },
      // allExcelData: {
      //   type: Array,
      //   default: () => [], // excel文件内容
      // },
    },
    setup(props) {
      const { containerId, sqlType } = toRefs(props)
      provide('sqlType', sqlType.value)
      const state = reactive({
        currentEdge: null,
        graph: null,
        data: [],
        leftModelData: [],
        rightModelData: [],
        haveLineIdArr: [], // 已经连线的id集合
        hasInit: false,
        resizeFn: null, // 屏幕变化执行函数
        placeHolder: '',
      })
      const methods = {
        setLineIdArr(item) {
          state.haveLineIdArr.push(item.source.port, item.target.port)
        },
        // 清空连线
        clearEdge() {
          if (state.graph) {
            let allEdges = state.graph.getEdges()
            if (allEdges.length) {
              allEdges.forEach((item) => {
                state.graph.removeEdge(item)
              })
              state.haveLineIdArr = []
            }
          }
        },
        // 改变下拉框生成
        changTable(data, callBack = null) {
          state.data = []
          state.haveLineIdArr = []
          state.warningTip = false
          let { leftData, rightData, relationDataMap } = data
          state.leftModelData = leftData
          state.rightModelData = rightData
          let _leftData = []
          let _rightData = []
          if (state.leftModelData?.length) {
            state.data[0] = {
              id: '1',
              shape: 'er-rect',
              // label: '数据源',
              label: '源端数据',
              width: 320,
              height: 42,
              position: {
                x: 50,
                y: 0,
              },
              ports: [],
            }

            state.leftModelData.forEach((item, index) => {
              if (!item.level) {
                item.level = 0
              }
              let attrs = {
                filedNameEn: {
                  text: item.filedNameEn,
                  transform: `translate(${10 * item.level} 0)`,
                  textWrap: {
                    text: item.comment,
                    width: -100, // 总NODE_WIDTH = 520 -420 =100
                    height: '50%', // 高度为参照元素高度的一半
                    ellipsis: true, // 文本超出显示范围时，自动添加省略号
                    breakWord: true, // 是否截断单词
                  },
                },
                filedNameZh: {
                  text: item.filedNameZh,
                  transform: `translate(${10 * item.level} 0)`,
                  textWrap: {
                    text: item.name,
                    width: 120, // 宽度减少 10px
                    height: '50%', // 高度为参照元素高度的一半
                    ellipsis: true, // 文本超出显示范围时，自动添加省略号
                    breakWord: true, // 是否截断单词
                  },
                },
                filedType: {
                  text: item.filedType,
                  transform: `translate(${10 * item.level} 0)`,
                  textWrap: {
                    text: item.fieldType,
                    width: -150, // 宽度减少 10px
                    height: '50%', // 高度为参照元素高度的一半
                    ellipsis: true, // 文本超出显示范围时，自动添加省略号
                    breakWord: true, // 是否截断单词
                  },
                },
                openSvg: {
                  display: item.hasChild ? 'block' : 'none',
                  transform: `translate(${10 * item.level} 0)`,
                },
              }
              _leftData.push({
                id: '1-' + index,
                guid: item.id,
                group: 'list',
                zIndex: -1,
                attrs: attrs,
              })
            })
            state.data[0].ports = _leftData
          } else {
            state.data[0] = []
          }

          if (state.rightModelData?.length) {
            state.data[1] = {
              id: '2',
              shape: 'er-rect',
              // label: '目的表',
              label: '非结构化数据管理',
              width: 320,
              height: 42,
              position: {
                x: 800,
                y: 0,
              },
              ports: [],
              // attrs: {
              //   body: {
              //     fill: '#E6F8EB',
              //   },
              // },
            }

            state.rightModelData.forEach((item, index) => {
              _rightData.push({
                id: '2-' + index,
                guid: item.id,
                group: 'list',
                zIndex: -1,
                attrs: {
                  filedNameEn: {
                    text: item.filedNameEn,
                    textWrap: {
                      text: item.comment,
                      width: -100, // 总NODE_WIDTH = 520 -420 =100
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  filedNameZh: {
                    text: item.filedNameZh,
                    textWrap: {
                      text: item.name,
                      width: -120, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                  filedType: {
                    text: item.filedType,
                    textWrap: {
                      text: item.fieldType,
                      width: -150, // 宽度减少 10px
                      height: '50%', // 高度为参照元素高度的一半
                      ellipsis: true, // 文本超出显示范围时，自动添加省略号
                      breakWord: true, // 是否截断单词
                    },
                  },
                },
              })
            })
            state.data[1].ports = _rightData
          } else {
            state.data[1] = []
          }
          if (relationDataMap?.length) {
            let needColumnMapping = []
            relationDataMap.forEach((item, index) => {
              let newObj = {}
              let leftIndex = _leftData.findIndex((cell) => item.sourceFiledId === cell.guid)
              if (leftIndex > -1) {
                newObj.shape = 'edge'
                newObj.zIndex = index + 1
                newObj.source = {
                  port: '1-' + leftIndex,
                  cell: '1',
                }
                newObj.data = {
                  sqlType: typeof item.sqlType !== 'object' ? parseInt(item.sqlType) : '',
                  ruleSql: item.ruleSql || '',
                  rulesDataMap: item.rulesDataMap || [],
                }
                newObj.attrs = {
                  line: {
                    // stroke: '#722ed1',
                    strokeWidth: 1,
                  },
                }
              }
              let rightIndex = _rightData.findIndex(
                (cell) => String(item.targetFiledId) === String(cell.guid),
              )
              if (rightIndex > -1) {
                newObj.target = {
                  port: '2-' + rightIndex,
                  cell: '2',
                }
              }
              needColumnMapping[index] = newObj
            })
            needColumnMapping.forEach((item) => {
              if (item.source?.port && item.target?.port) {
                state.data.push(item)
                state.haveLineIdArr.push(item.source.port, item.target.port)
              }
            })
          }
          if (state.graph) {
            state.graph.clearCells()
          }
          methods.clearEdge()
          methods.init()
          if (typeof callBack === 'function') {
            state.graph.on('render:done', () => {
              callBack({ data: state, methods })
            })
          }
        },

        init() {
          state.hasInit = true
          const LINE_HEIGHT = 42
          const NODE_WIDTH = 320
          if (!state.graph) {
            Graph.registerPortLayout(
              'erPortPosition',
              (portsPositionArgs) => {
                return portsPositionArgs.map((_, index) => {
                  let y = (index + 1) * LINE_HEIGHT
                  return {
                    position: {
                      x: 0,
                      y,
                    },
                    angle: 0,
                  }
                })
              },
              true,
            )
            Graph.registerNode(
              'er-rect',
              {
                inherit: 'rect',
                markup: [
                  {
                    tagName: 'rect',
                    selector: 'body',
                  },
                  {
                    tagName: 'text',
                    selector: 'label',
                  },
                ],
                attrs: {
                  body: {
                    strokeWidth: 1,
                    stroke: '#EBEDF0',
                    fill: '#e5e5e5',
                    // fill: '#fff',
                    textAnchor: 'center',
                  },
                  label: {
                    fontWeight: 'bold',
                    fill: '#000',
                    fontSize: 12,
                  },
                },
                ports: {
                  groups: {
                    list: {
                      markup: [
                        {
                          tagName: 'rect',
                          selector: 'portBody',
                        },
                        {
                          tagName: 'image',
                          selector: 'openSvg',
                        },
                        {
                          tagName: 'text',
                          selector: 'filedNameEn',
                        },

                        {
                          tagName: 'text',
                          selector: 'filedNameZh',
                        },
                        {
                          tagName: 'text',
                          selector: 'filedType',
                        },
                      ],
                      attrs: {
                        portBody: {
                          width: NODE_WIDTH,
                          height: LINE_HEIGHT,
                          strokeWidth: 1,
                          stroke: '#697A9A2E',
                          fill: '#fff', //内容颜色和边框
                          magnet: true,
                        },
                        openSvg: {
                          href: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAKCAYAAAC9vt6cAAAAAXNSR0IArs4c6QAAAPtJREFUKFOd0E9Kw1AQBvD5EolkkY3gBdz0AJnERgikC69QEdd6Bz2A3kHX0uoVXBgISJu87O3GCwhusggGk5EntfRPWrSze8x8P2YemPlVRDq0RQGYgJmHInKyRZ4APCAIAruqqmciOvwnMrYsqwcdCsNwvyzLkYgc/AUB8GbbdjdJkvcfQBcz6394EZG9TQiADyI6UkpN9NwM0A/f98O6rp+IaHcN8mma5nGapslvfwGYbnIqIvfLOBEJgDOl1GAeXwF00/O8y6ZprucHDcO4yrLsZnmzVkAPua57S0Tn08BdnucXbWetBaIo2imK4lGHHMfpx3H81QZ8A9flTHcjywF/AAAAAElFTkSuQmCC',
                          width: 8,
                          height: 8,
                          refX: 4,
                          refY: 17,
                          display: 'none',
                        },
                        filedNameEn: {
                          ref: 'portBody',
                          refX: 20,
                          refY: 15,
                          fontSize: 12,
                        },
                        filedNameZh: {
                          ref: 'portBody',
                          refX: 200,
                          refY: 15,
                          fontSize: 12,
                        },
                        filedType: {
                          ref: 'portBody',
                          refX: 350,
                          refY: 15,
                          fontSize: 12,
                        },
                      },
                      position: 'erPortPosition',
                    },
                    // 内容

                    // // 表头
                    // listLast: {
                    //   position: 'erPortPositionDif',
                    // },
                  },
                },
              },
              true,
            )
            let height = document.getElementById(containerId.value).offsetHeight
            let width = document.getElementById(containerId.value).offsetWidth
            state.graph = new Graph({
              container: document.getElementById(containerId.value),
              width,
              height,
              snapline: {
                enabled: true,
                clean: false,
              },

              scroller: {
                enabled: true,
                pannable: false,
                pageVisible: true,
                pageBreak: false,
              },
              mousewheel: {
                enabled: true,
                modifiers: ['ctrl', 'meta'],
              },

              keyboard: true,
              clipboard: true,
              selecting: {
                enabled: true,
                rubberband: true,
                showNodeSelectionBox: true,
              },
              // scroller: true, // 是否添加滚动条
              interacting: {
                nodeMovable: false, // 禁止节点移动
              },
              // highlighting: {
              //   magnetAvailable: magnetAvailabilityHighlighter,
              // },
              connecting: {
                snap: false, // 当 snap 设置为 true 时连线的过程中距离节点或者连接桩 50px 时会触发自动吸附
                allowBlank: false, // 是否允许连接到画布空白位置的点，默认为 true。
                allowMulti: true, // 是否链接多个
                allowLoop: false, // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点，默认为 true
                allowNode: false, // 是否允许边链接到节点（非节点上的链接桩），默认为 true。
                allowEdge: false, // 是否允许边链接到另一个边，默认为 true
                allowPort: true, // 是否允许边链接到链接桩
                highlight: true,

                router: {
                  name: 'er',
                  args: {
                    offset: 25,
                    direction: 'H',
                  },
                },
                createEdge() {
                  return new Shape.Edge({
                    attrs: {
                      line: {
                        stroke: '#333',
                        strokeWidth: 1,
                      },
                    },
                  })
                },
                // 数据源侧  只输出不能输入
                validateMagnet({ cell }) {
                  return cell.label.includes('源端数据')
                },
                //
                validateConnection(data) {
                  let status = false
                  let { sourcePort, targetPort } = data

                  if (
                    state.haveLineIdArr.includes(sourcePort) ||
                    state.haveLineIdArr.includes(targetPort)
                  ) {
                    // 已经连接过
                    status = false
                  } else {
                    status = true
                  }
                  return status
                },
              },
            })
            let _state = state
            const resizeFn = () => {
              const { width, height } = methods.getContainerSize()
              _state.graph.resize(width, height)
            }
            resizeFn()
            window.addEventListener('resize', resizeFn)
            state.resizeFn = resizeFn
            // state.graph.bindKey('backspace', () => {
            //   const cells = state.graph.getSelectedCells()
            //   if (cells.length) {
            //     state.graph.removeCells(cells)
            //   }
            // })
            // state.graph.bindKey('delete', () => {
            //   const cells = state.graph.getSelectedCells()
            //   if (cells.length) {
            //     state.graph.removeCells(cells)
            //   }
            // })

            // state.graph.zoomToFit({ padding: 10, maxScale: 1 })
            state.graph.on('edge:mouseenter', ({ e, cell }) => {
              cell.addTools([
                {
                  name: 'source-arrowhead',
                },
                {
                  name: 'target-arrowhead',
                  args: {
                    attrs: {
                      fill: 'red',
                    },
                  },
                },
                {
                  name: 'button-remove',
                  args: { distance: 50 },
                },
              ])
            })
            // 连线后执行
            state.graph.on('edge:connected', (data) => {
              let allEdges = state.graph.getEdges()
              let a = []
              allEdges.forEach((item) => {
                a.push(item.source.port, item.target.port)
              })
              state.haveLineIdArr = a
            })
            // 取消连线
            state.graph.on('edge:removed', (data) => {
              let sourceId = data.edge.source.port
              let targetId = data.edge.target.port
              // 貌似 toolId 存在时候是点击删除按钮删除，不存在时候为不满足条件时候自动触发
              if (data.options.toolId) {
                let new_haveLineIdArr = state.haveLineIdArr.filter(
                  (item) => item !== sourceId && item !== targetId,
                )
                state.haveLineIdArr = new_haveLineIdArr.filter((val) => val)
              }
            })
            state.graph.on('edge:mouseleave', ({ e, cell }) => {
              cell.removeTool('source-arrowhead')
              cell.removeTool('target-arrowhead')
              cell.removeTool('button-remove')
            })
          }
          const cells = []
          state.data.forEach((item) => {
            if (item.shape === 'edge') {
              cells.push(state.graph.createEdge(item))
            } else {
              cells.push(state.graph.createNode(item))
            }
          })
          state.graph.resetCells(cells)
          // state.graph.zoomTo(1) // 默认方法倍数
        },
        getGraphData() {
          return state.graph.toJSON()
        },
        // 获取画布容器大小
        getContainerSize() {
          return {
            width: document.getElementById('svgBox')?.offsetWidth,
            height: document.getElementById('svgBox')?.offsetHeight,
          }
        },
        // 渲染连线
        addEdge({ data, initLater }) {
          if (initLater) {
            let leftAllData = state.data.filter((item) => {
              return item.label === '源端数据'
            })
            let rightAllData = state.data.filter((item) => {
              return item.label === '非结构化数据管理'
            })
            let needColumnMapping = []
            if (data.length) {
              data.forEach((item, index) => {
                needColumnMapping[index] = {}
                let leftRow = leftAllData[0].ports.find(
                  (cell) =>
                    item.sourceFiledNameEn.toUpperCase() ===
                    cell.attrs.filedNameEn.text.toUpperCase(),
                )
                if (leftRow) {
                  needColumnMapping[index].shape = 'edge'
                  needColumnMapping[index].zIndex = index + 1
                  needColumnMapping[index].source = {
                    port: leftRow.id,
                    cell: '1',
                  }
                  needColumnMapping[index].attrs = {
                    line: {
                      // stroke: '#722ed1',
                      strokeWidth: 1,
                    },
                  }
                }
                let rightRow = rightAllData[0].ports.find(
                  (cell) =>
                    item.targetFiledNameEn.toUpperCase() ===
                    cell.attrs.filedNameEn.text.toUpperCase(),
                )
                if (rightRow) {
                  needColumnMapping[index].target = {
                    port: rightRow.id,
                    cell: '2',
                  }
                }
              })
              needColumnMapping.forEach((item) => {
                state.graph.addEdge(item)
              })
            }
          } else {
            if (data.length) {
              data.forEach((item) => {
                state.graph.addEdge(item)
              })
            }
          }
        },
      }
      onBeforeUnmount(() => {
        window.removeEventListener('resize', state.resizeFn)
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .scene-table-link {
    height: 100%;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;

    .page-top {
      margin-bottom: 12px;
      &.dif {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          margin-bottom: 0;
        }
      }
      .clear-edge-button {
        &.disabled {
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          color: #c8c9cc;
          cursor: no-drop;
        }
      }
      .auto-connect-button {
        &.disabled {
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          color: #c8c9cc;
          cursor: no-drop;
        }
      }
      .view-button {
        background: #f0f7ff;
        border: 1px solid $themeBlue;
        color: $themeBlue;

        &.disabled {
          background: #f7f8fa;
          border: 1px solid #e1e1e1;
          color: #c8c9cc;
          cursor: no-drop;
        }
      }
      .title {
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }
    }
    .page-mid {
      // height: calc(100% - 84px);
      height: 100%;
      // background-color: #f7f8fa;
      border-radius: 8px;
      // border: 1px solid #ebedf0;
      &.dif {
        // height: calc(100% - 64px);
      }
      &.isEmpty {
        border: 1px solid red;
      }

      // margin-top: 10px;
      :deep(.x6-graph-scroller) {
        height: 100% !important;
        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #ddd;
          border-radius: 2px;
        }
      }
      :deep(.x6-graph-scroller.x6-graph-scroller-paged .x6-graph) {
        box-shadow: none;
      }
      #containerEr {
        width: 100%;
        height: calc(100vh - 700px);
      }
      #containerEr2 {
        width: 100%;
      }
    }
  }
</style>
