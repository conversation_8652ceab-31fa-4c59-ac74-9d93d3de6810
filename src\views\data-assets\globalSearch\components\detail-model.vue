<template>
  <div class="detail-model">
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="信息" id="metaData" />
      <n-tab title="血缘" id="dataMap" />
    </n-tabs>
    <div v-if="state.activeName === 'metaData'" class="detail-content">
      <n-button-group size="sm" class="params-info">
        <n-button
          :class="{ 'res-btn': true, active: state.buttonActive == 0 }"
          @click.stop.prevent="getMetaDataByModel"
          >表结构</n-button
        >

        <n-button
          :class="{ 'row-btn': true, active: state.buttonActive == 1 }"
          @click.stop.prevent="getDataPreview"
          >数据预览</n-button
        >
      </n-button-group>
      <div class="table" v-loading="state.loadingPreview">
        <n-public-table
          v-if="state.buttonActive == 0"
          ref="publicTable"
          :table-head-titles="state.tableHeadTitles"
          :tableData="state.tableData"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          @tablePageChange="tablePageChange"
        />
        <n-public-table
          v-if="state.buttonActive == 1"
          class="preciew-table"
          ref="publicTablePreview"
          :key="state.keyCunt"
          :table-head-titles="state.tableHeadTitlesPreview"
          :tableData="state.previewTableData"
          :pagination="state.paginationP"
          :tableHeight="state.tableHeight"
          @tablePageChange="tablePageChangeP"
        />
      </div>
    </div>
    <div
      v-if="state.activeName === 'dataMap'"
      v-loading="state.bloodLoading"
      class="detail-content"
    >
      <div class="select-row">
        <n-button-group size="sm" class="params-info">
          <n-button
            :class="{ 'res-btn': true, active: state.bloodType === 'MODEL' }"
            @click.stop.prevent="getBloodMapData('MODEL')"
            >表血缘</n-button
          >
          <n-button
            :class="{ 'row-btn': true, active: state.bloodType === 'COLUMN' }"
            @click.stop.prevent="getBloodMapData('COLUMN')"
            >字段血缘</n-button
          >
        </n-button-group>
        <div v-if="state.bloodType === 'COLUMN'" class="select-row-right">
          <div class="node"> <span class="circle"></span>主节点</div>
          <n-select
            class=""
            v-model="state.columnId"
            :options="
              state.columnIdOptions.map((val) => {
                return { ...val, name: val.name, value: val.id }
              })
            "
            @value-change="columnFn"
          />
        </div>
      </div>
      <flow v-if="state.hasData" :checkedItem="state.checkedItem" :detailName="seeName" />
      <div v-else class="empty">
        <img class="empty-img" src="@/assets/table-no-content.png" alt="暂无数据" />
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import insertCss from 'insert-css'
  import flow from './flow'
  export default {
    name: '',
    components: { flow },
    props: {
      isShow: { type: Boolean, default: false },
      seeName: { type: String, default: 'globalSearchDetail' },
      mapType: { type: String, default: 'MODEL' },
    },
    setup(props) {
      const store = useStore()
      const router = useRouter()
      const publicTable = ref()
      const publicTablePreview = ref()
      const colPublicTable = ref()
      const rowPublicTable = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        buttonActive: 0,
        activeName: 'metaData',
        bloodType: 'MODEL',
        columnId: '',
        columnIdOptions: [],
        disabled: true,
        detailId: null,
        ruleForm: {
          projectCode: '',
          projectName: '',
          createByName: '',
          layerId: '',
          layerName: '',
          createTime: '',
          updateByName: '',
          updateTime: '',
          description: '',
          paramColumns: [], // 自定义参数
        },
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        changeType: 'sm',
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
        ],
        tableData: {},
        tableHeight: 400,
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
          total: 0,
        },
        keyCunt: 0,
        checkedItem: {},
        tableHeadTitlesPreview: [],
        loadingPreview: false,
        bloodLoading: false,
        hasData: false,
        previewTableData: {},
        allParamColumns: [],
        allTableData: [],
        paginationP: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
      })
      const methods = {
        columnFn() {
          methods.assetsLineage(state.detailId)
        },
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 305 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 305
          }
        },
        // 获取模型详情
        getDetail(id) {
          state.loading = true
          api.model
            .getModalDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                state.ruleForm = data
              }
            })
            .catch(() => {})
        },
        getMetaDataByModel(init = true) {
          state.buttonActive = 0
          methods.setTableHeight()
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let params = {
            condition: {
              id: state.detailId,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loadingPreview = true
          api.model
            .getModelDataList(params)
            .then((res) => {
              state.loadingPreview = false
              let { data, success } = res
              if (success) {
                res.data.list.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })

                state.tableData = res.data
              }
            })
            .catch(() => {
              state.loadingPreview = false
            })
        },
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.getMetaDataByModel(false)
          state.keyCunt++
        },
        // 模拟分页
        startPagination(data) {
          let { currentPage, pageSize } = state.pagination
          let starPos = (currentPage - 1) * pageSize
          let endPos = currentPage * pageSize
          // 更新表格
          let filterData = data.slice(starPos, endPos)
          // 新增序号属性
          filterData.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          return {
            list: filterData,
            pageNum: currentPage,
            pageSize,
            total: state.allTableData.length,
          }
        },
        // 表格操作变化
        tablePageChangeP(data) {
          state.paginationP.currentPage = data.currentPage
          state.paginationP.pageSize = data.pageSize
          methods.getDataPreview(false)
        },
        // 获取批量打标过滤的元数据列表
        getMetadataList() {
          api.model
            .getModeData({
              id: state.detailId,
            })
            .then((res) => {
              // 新增序号属性
              let _allParamColumns = []
              state.columnIdOptions = res.data
              if (res.data && res.data.length) {
                res.data.forEach((item, index) => {
                  if (index === 0) {
                    state.columnId = item.id
                  }
                  _allParamColumns.push({
                    prop: item.name,
                    name: item.name,
                    width: 150,
                  })
                })
                state.allParamColumns = _allParamColumns
                state.tableHeadTitlesPreview = [{ prop: 'number', name: '序号', width: 80 }].concat(
                  state.allParamColumns,
                )
              }
            })
        },
        getDataPreview(init = true) {
          state.buttonActive = 1
          methods.setTableHeight()
          state.paginationP.currentPage = init ? 1 : state.paginationP.currentPage
          let data = {
            condition: {
              // projectCode: toRef(store.state.user.currentProject, 'projectCode').value,
              projectCode: state.ruleForm.projectCode,
              modelName: router.currentRoute.value.query.modelName,
              // modelName: 'ci_tun',
              // projectCode: 'xaadybcdwebcxadfe',
            },
            pageNum: state.paginationP.currentPage,
            pageSize: state.paginationP.pageSize,
          }
          state.loadingPreview = true
          let allData = { list: [] }
          api.model
            .getDataWithProject(data)
            .then((res) => {
              state.loadingPreview = false
              let { success, data } = res
              if (success) {
                if (data.list && data.list.length > 0) {
                  state.tableHeadTitlesPreview = [
                    { prop: 'number', name: '序号', width: 80 },
                  ].concat(state.allParamColumns)
                  data.list.map((item, index) => {
                    let _itemKeys = []
                    Object.keys(item).forEach((_item) => {
                      _itemKeys.push(_item.toLowerCase())
                    })
                    let _object = {}
                    state.tableHeadTitlesPreview.forEach((key) => {
                      if (_itemKeys.includes(key.prop.toLowerCase())) {
                        _object[key.prop] = item[key.prop.toLowerCase()]
                      } else {
                        _object[key.prop] = null
                      }
                    })
                    _object['number'] = index + 1
                    allData.list.push(_object)
                  })
                  data.list = allData.list
                }
                state.keyCunt++
                state.previewTableData = data
              }
            })
            .catch(() => {
              state.loadingPreview = false
            })
        },
        chooseChange(val) {
          state.changeType = val
          switch (val) {
            case 'sm':
              methods.getMetaDataByModel()
              break
            case 'lg':
              methods.getDataPreview()
              break
            default:
          }
        },
        // 根据数据表追溯血缘关系
        assetsLineage(id) {
          let initData = {
            nodes: [],
            relationship: [],
          }
          state.bloodLoading = true
          let data = {
            id: parseInt(id),
            type: state.bloodType,
          }
          let url = 'modelTableLineage'
          if (state.bloodType === 'COLUMN') {
            url = 'modelColumnLineage'
            data.columnId = state.columnId
            data.modelId = id
            delete data.id
          }
          api.assets[url](data)
            .then((res) => {
              state.bloodLoading = false
              state.checkedItem = {
                id: parseInt(id),
                projectCode: state.ruleForm.projectCode,
                bloodType: state.bloodType,
              }
              if (res.data?.nodes.length > 0) {
                state.hasData = true
                res.data.nodes.forEach((item, index) => {
                  //当前版本只有model类型下可以跳转
                  let isMainNode = false
                  if (state.bloodType === 'MODEL') {
                    if (item.id === id) {
                      isMainNode = true
                    }
                  } else {
                    if (item.id === String(state.columnId)) {
                      isMainNode = true
                    }
                  }
                  initData.nodes.push({
                    id: item.uk,
                    nId: item.id,
                    nEnName: (item.type === 'DATA_SOURCE_VIRTUAL_TABLE' ? 'tmp_' : '') + item.name,
                    nCnName: item.cnName,
                    tableName: item.tableName,
                    nProjectCode: item.projectCode,
                    type: item.type,
                    isClick: item.type === 'MODEL',
                    isMainNode: isMainNode,
                  })
                })
                res.data.relationship.forEach((item) => {
                  initData.relationship.push({
                    nTo: item.to,
                    nFrom: item.from,
                  })
                })
              } else {
                state.hasData = false
              }
              state.checkedItem.assetsData = JSON.stringify(initData)
            })
            .catch(() => {
              state.bloodLoading = false
            })
        },
        //切换
        handleClick(id) {
          state.activeName = id
          switch (id) {
            case 'metaData':
              methods.getMetaDataByModel()
              break
            case 'dataMap':
              methods.assetsLineage(state.detailId)
              break
            default:
          }
        },
        // 获取血缘数据
        getBloodMapData(type) {
          if (state.bloodType !== type) {
            state.bloodType = type
            methods.assetsLineage(state.detailId)
          }
        },
      }
      onMounted(() => {
        nextTick(() => {
          state.detailId = router.currentRoute.value.query.id
          methods.getMetadataList()
          methods.getDetail(state.detailId)
          methods.getMetaDataByModel()
        })
      })

      return {
        state,
        publicTable,
        publicTablePreview,
        colPublicTable,
        rowPublicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .detail-model {
    position: relative;
    height: 100%;
    color: #333;

    .detail-content {
      height: calc(100% - 33px);
      margin-top: 8px;
      .select-row {
        display: flex;
        height: 48px;
        align-items: center;
        justify-content: space-between;

        &-right {
          position: relative;
          top: 71px;
          left: -17px;
          z-index: 2;
          width: 240px;
          padding: 16px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: #666666;
          font-size: 12px;
          border-radius: 6px;
          background: #fff;
          /* 小-二级菜单 */
          box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);

          .node {
            display: flex;
            width: 72px;
            height: 32px;
            padding: 5px 6px 5px 12px;
            align-items: center;
            gap: 4px;
            border-radius: 6px 0px 0px 6px;
            border-top: 1px solid var(---, #a3b4db);
            border-bottom: 1px solid var(---, #a3b4db);
            border-left: 1px solid var(---, #a3b4db);
            background: var(---, #f6f7fb);
          }
          :deep(.nancalui-select) {
            width: 168px;
            display: flex;
            padding-left: 10px;
            align-items: center;
            gap: 6px;
            flex: 1 0 0;
            border-radius: 0px 6px 6px 0px;
            border: 1px solid var(---, #a3b4db);
            background: var(--100, #fff);
          }
          :deep(.nancalui-select__selection:hover) {
            border: 1px solid #2f5cd6;
            box-shadow: none;
          }

          .circle {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #ff9f19;
            border-radius: 50%;
          }
          :deep(.nancalui-select) {
            width: 120px;
            .nancalui-select__selection {
              border: none;
              .nancalui-select__input {
                padding-left: 0;
              }
            }
          }
        }
      }
    }
    .table {
      padding: 0 16px;
      .preciew-table {
        :deep(.nancalui-table) {
          .nancalui-table__fix-header {
            overflow-x: scroll !important;
          }
          .nancalui-table__scroll-view {
            overflow: unset;
          }
        }
      }
    }
    :deep(.x6-graph-scroller) {
      width: 100% !important;
      height: 100% !important;
      &::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
        background-color: #fff;
      }
      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 2px;
      }
    }
    :deep(.detail-content) {
      .nancalui-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 600px;
        margin: 0 75px;
        .nancalui-form__item--horizontal {
          position: relative;
          display: flex;
          width: 100%;
          margin-bottom: 20px;
          .nancalui-form__label {
            position: absolute;
            top: 0;
            right: 600px;
            width: max-content !important;
            color: #666;
            font-size: 14px;
          }
          &.operate-box {
            display: inline-block;
            text-align: center;
            transform: translateX($labelWidth);
          }
          .nancalui-form__control {
            color: #333;
            // 要想
            width: calc(100% - #{$labelWidth});
            margin-left: 0 !important;
            color: #333;
            color: #333;
          }
        }
        // .nancalui-input__wrapper.nancalui-input--disabled,
        // .nancalui-textarea.nancalui-textarea--disabled {
        //   background-color: transparent;
        //   border: none;
        //   box-shadow: none;
        // }
        // .el-input.is-disabled .el-input__inner,
        // .el-textarea.is-disabled .el-textarea__inner {
        //   font-size: 14px !important;
        // }
      }
      .params-info {
        display: flex;
        padding: 1px 16px 10px;
        :deep(.nancalui-button) {
          width: 88px;
          height: 32px;

          > span {
            font-size: 13px;
          }
        }
        .nancalui-button:nth-child(1) {
          border-right: 0;
          // border-left: 0
        }
        .nancalui-button:nth-child(3) {
          // border-right: 0;
          border-left: 0;
        }
        .active {
          color: $themeBlue;
          background: #f0f7ff;
          // border-radius: 2px 0px 0px 2px;
          border: 1px solid $themeBlue !important;
        }
        .res-btn.active ~ .nancalui-button {
          border-left: 0;
        }
        .res-btn {
          border-radius: 6px 0 0 6px;
        }
        .row-btn {
          border-radius: 0 6px 6px 0;
        }

        .nancalui-button + .nancalui-button {
          margin-left: 0;
        }
        div {
          padding-right: 20px;
          cursor: pointer;
        }
      }
      .params-content {
        .common-table {
          font-size: 200px;
          :deep(.el-table) {
            .el-table__cell {
              .required {
                color: #f54446;
              }
              .required-input .el-input__wrapper {
                border: 1px solid #f54446;
                &.is-focus {
                  box-shadow: none;
                }
              }
            }
          }
          .tag-infos-box {
            display: flex;
            align-items: center;
            .tag-item-box {
              position: relative;
              display: flex;
              align-items: center;
              height: 40px;
              &:hover {
                .yy-icon {
                  display: block;
                  cursor: pointer;
                }
              }
              &:nth-of-type(3) {
                margin-right: 50px;
              }
              .yy-icon {
                position: absolute;
                top: 0px;
                right: 5px;
                display: none;
                width: 18px;
                height: 18px;
              }
            }
            .tag-item {
              flex-shrink: 0; //不压缩
              width: 68px;
              height: 28px;
              margin-right: 10px;
              padding: 0 10px;
              overflow: hidden;
              line-height: 28px;
              white-space: nowrap;
              text-align: center;
              text-overflow: ellipsis;
              background: #f2f3f6;
              border-radius: 14px;
              cursor: default;
            }
            .more-box {
              position: absolute;
              top: 10px;
              right: 0;
              bottom: 0;
              padding: 0 10px;
              // background-color: #fff;
            }
            .more-icon {
              width: 22px;
              height: 18px;
              color: #333;
              cursor: pointer;
              &:hover {
                color: #4d4e4eff;
                background-color: #eff1f5ff;
              }
            }
          }
        }
        .registry-info,
        .tag-info {
          margin-top: 10px;
          padding-left: 10px;
          h4 {
            margin: 0;
            color: #000;
            font-size: 14px;
          }
          &-box {
            margin-top: 20px;
            span {
              display: inline-block;
              box-sizing: border-box;
              height: 28px;
              margin-right: 10px;
              margin-bottom: 10px;
              padding: 4px 10px;
              color: #333;
              font-size: 12px;
              line-height: 21px;
              text-align: center;
              background: #f2f3f6;
              border-radius: 14px;
            }
          }
        }
      }
    }

    :deep(.map-no-content) {
      display: table;
      width: 100%;
      height: 100%;
      > div {
        display: table-cell;
        text-align: center;
        vertical-align: middle;
      }
      img {
        width: 266px;
      }
      .text {
        margin-top: 20px;
        color: var(--el-text-color-secondary);
        font-size: 12px;
        line-height: normal;
      }
    }
    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 50px);
      text-align: center;
      &-img {
        display: block;
        width: 140px;
      }
      &-text {
        margin-top: 20px;
        color: var(--el-text-color-secondary);
        font-size: 12px;
        line-height: normal;
      }
    }

    :deep(.params-info .nancalui-button) {
      padding: 0 16px;
      width: auto;
    }
    :deep(.params-info .nancalui-button.active) {
      background: none !important;
    }

    :deep(.nancalui-tabs) {
      height: 53px;

      .nancalui-tabs-nav,
      .nancalui-tabs-nav-tab,
      .nancalui-tabs-nav-size-md.nancalui-tabs-nav-type-line .nancalui-tabs-tab {
        height: 100%;
      }
    }
  }
</style>
