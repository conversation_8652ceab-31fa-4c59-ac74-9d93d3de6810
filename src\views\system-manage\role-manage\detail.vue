<template>
  <section class="container-padding16">
    <section class="container-padding16-box" v-loading="state.loading">
      <div class="page_header_common_style">
        <span class="need_smallcube__title">{{ state.title }}</span>
      </div>
      <div class="form">
        <n-form
          ref="form"
          :data="state.form"
          :rules="state.rules"
          label-width="82px"
          label-align="right"
          :pop-position="['right']"
          label-suffix="："
        >
          <n-row justify="between" :gutter="24">
            <n-col :span="12">
              <n-form-item label="角色编码" field="code">
                <n-input
                  disabled
                  v-model="state.form.code"
                  placeholder="请输入角色编码"
                  size="small"
                  maxlength="50"
                  clearable
                />
              </n-form-item>
            </n-col>
            <n-col :span="12">
              <n-form-item label="角色名称" field="name">
                <n-input
                  disabled
                  v-model="state.form.name"
                  placeholder="请输入角色名称"
                  size="small"
                  maxlength="50"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row justify="between" :gutter="24">
            <n-col :span="24">
              <n-form-item label="角色描述" field="description">
                <n-textarea
                  v-model="state.form.description"
                  :disabled="true"
                  placeholder="请输入角色描述"
                  :autosize="{ minRows: 3 }"
                  maxlength="200"
                  show-count
                  resize="both"
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row justify="between" :gutter="24" class="tree-box-row">
            <n-col :span="24">
              <n-form-item class="tree-box" label="拥有权限">
                <n-tree
                  ref="tree"
                  :prop="state.defaultProps"
                  :data="state.dataTree"
                  check="downward"
                  :key="state.key"
                  @check-change="hanleCheck"
                />
              </n-form-item>
            </n-col>
          </n-row>
        </n-form>
      </div>
    </section>
    <div class="container-padding16-footer">
      <div class="my-appliction">
        <n-button color="primary" size="sm" variant="solid" @click.prevent="goBack">返回</n-button>
      </div>
    </div>
  </section>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  export default {
    name: 'DataScriptDetail',
    setup() {
      const router = useRouter()
      const tree = ref()
      const state = reactive({
        title: '角色详情',
        treeData: [],
        form: {
          name: '',
          description: '',
        },
        roleAllArr: '', // 角色下拉列表
        rules: {
          name: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
        },
        dataTree: [],
        defaultProps: {
          children: 'children',
          label: 'name',
          key: 'id',
          group: true,
        },
        query: {},
        key: 1,
        loading: false,
      })

      const methods = {
        // 角色数据详情查询
        roleDetail() {
          let params = {
            id: state.query.id,
          }
          api.system
            .roleDetail(params)
            .then((res) => {
              state.loading = false
              if (res.code === 'SUCCESS') {
                state.form = res.data
                if (res.data.menuIds) {
                  let menuIds = res.data.menuIds

                  methods.editCheckNodes(state.dataTree, menuIds)

                  state.key++
                }
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        //编辑回显选中
        editCheckNodes(data, ids = []) {
          data.map((item, key = 'id') => {
            if (item.children && item.children.length != 0) {
              this.editCheckNodes(item.children, ids)
            }
            if (ids.includes(item.id)) {
              item.checked = true
            }
          })
        },
        menuTree() {
          let params = {}
          state.loading = true
          api.system
            .menuTree(params)
            .then((res) => {
              if (res.data) {
                res.data.forEach((item) => {
                  item.children = item.children[0]?.children
                  if (item.code === 'data') {
                    item.children = item.children.filter((val) => val.code !== 'data_application')
                  }
                })
                state.dataTree = methods.setTreeRecursion(res.data)

                methods.roleDetail()
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        setTreeRecursion(data) {
          data.map((item, index) => {
            item.expanded = true
            item.disableCheck = true
            if (item.children && item.children.length !== 0) {
              this.setTreeRecursion(item.children)
            }
          })
          return data
        },

        // 设置已选择菜单
        setCheckedKeys(ids) {
          tree.value.setCheckedKeys(ids)
        },
        goBack() {
          router.back()
        },
      }
      onMounted(() => {
        state.query = router.currentRoute.value.query
        methods.menuTree()
      })
      return {
        state,
        tree,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container-padding16 {
    // padding-bottom: 70px;
    &-box {
      height: calc(100% - 74px);
      border-radius: 4px;
      overflow: hidden;
    }

    &-footer {
      height: 64px;
      padding: 16px;
      text-align: right;
      background: #ffffff;
      margin-top: 10px;
      border-radius: 0px 0px 2px 2px;
    }

    :deep(.el-input) {
      width: 320px;
    }
  }

  .form {
    height: calc(100% - 52px);
    background: #fff;
    padding: 16px 58px;
    color: #333;

    .nancalui-form {
      width: 880px;
      :deep(.nancalui-input__inner) {
        color: #000;
      }
    }

    h2 {
      font-size: 14px;
      padding-left: 5px;
      border-left: 4px solid $themeBlue;
      margin: 0 0 20px;
    }

    .nancalui-form {
      height: 100%;
      margin: 0 auto;
      transform: translateX(-41px);
      height: 100%;
      .tree-box-row {
        height: calc(100% - 120px);
      }
      :deep(.tree-box) {
        width: 100%;
        height: 100%;
        .nancalui-form__control,
        .nancalui-form__control-container {
          width: 100%;
          height: 100%;
        }
        > .nancalui-tree-list {
          align-items: flex-start;
        }
        .nancalui-tree {
          width: 100%;
          max-height: calc(100vh - 400px);
          overflow-y: auto;
          display: flex;
          justify-content: space-between;
          padding: 10px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          .nancalui-checkbox.disabled .nancalui-checkbox__material {
            border-color: #dcdfe6;
            background-color: #f2f6fc !important;
          }
          .nancalui-checkbox.disabled.active svg polygon {
            fill: #a8abb2;
          }
          .nancalui-tree__node {
            height: 26px;
          }
          .nancalui-tree__node-vline,
          .nancalui-tree__node-hline {
            display: none;
          }
          .nancalui-tree__node-title {
            font-size: 12px;
          }

          .el-tree-node__content {
            .el-tree-node__label {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  :deep(.el-input, .el-select) {
    width: 100% !important;
  }
</style>
