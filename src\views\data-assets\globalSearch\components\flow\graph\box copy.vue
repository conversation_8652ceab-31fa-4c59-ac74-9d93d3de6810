<template>
  <div v-if="!nodeInfo.isSource && !nodeInfo.isTarget" class="box">
    <el-tooltip class="box-item" effect="dark" placement="top-start">
      <template #content>{{ nodeInfo.nName }} </template>
      <div class="name">{{ nodeInfo.nName }}</div>
    </el-tooltip>
    <el-tooltip class="box-item" effect="dark" placement="top-start">
      <template #content>{{ nodeInfo.nEnName }} </template>
      <div class="eng">{{ nodeInfo.nEnName }}</div>
    </el-tooltip>
  </div>
  <div
    v-else-if="nodeInfo.isSource || nodeInfo.isTarget"
    :class="[
      'icon-box',
      nodeInfo.workType === 'REAL_TIME' ? 'online' : 'offline',
      {
        source: nodeInfo.isSource && !nodeInfo.isTarget,
        target: nodeInfo.isTarget && !nodeInfo.isSource,
        structured: nodeInfo.nodeDataType === 'STRUCTURED',
      },
    ]"
  >
    <div class="img-box">
      <!-- <img class="icon" src="~@img/assets/assetSearch/<EMAIL>" /> -->
      <img class="icon" :src="nodeInfo.iconUrl" />
    </div>
    <el-tooltip class="box-item" effect="dark" placement="top-start">
      <template #content>{{ nodeInfo.nName }} </template>
      <div class="node-name"
        ><span>{{ nodeInfo.dataLineageType }}</span> {{ nodeInfo.nName }}</div
      >
    </el-tooltip>
  </div>
</template>

<script>
  import { reactive, toRefs, inject } from 'vue'

  export default {
    inject: ['getGraph', 'getNode'],
    setup() {
      const state = reactive({
        node: null,
        iconUrl: '',
        nodeInfo: {
          nName: '',
          nEnName: '',
        },
      })
      state.node = inject('getNode')()
      onMounted(() => {
        nextTick(() => {
          state.nodeInfo = state.node.getData() || { name: '', iconUrl: '', runInfo: { state: '' } }
          state.nodeInfo.iconUrl = new URL(
            `/src/assets/img/assets/assetSearch/${state.nodeInfo.iconName}.png`,
            import.meta.url,
          ).href
          // 监听数据改变事件
          state.node.on('change:data', ({ current }) => {
            state.nodeInfo = current
          })
        })
      })
      const params = toRefs(state)

      return {
        ...params,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .box {
    width: 160px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;
    box-sizing: border-box;
    border-radius: 6px;
    color: #333;
    border: 1px solid #697a9a;
    position: relative;

    .name,
    .eng {
      width: calc(100% - 5px);
      font-size: 12px;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }
    .eng {
      font-weight: 400;
      font-size: 12px;
      margin-top: 6px;
    }
  }
  .icon-box {
    width: 160px;
    height: 32px;
    padding: 0;
    // padding-right: 20px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    box-sizing: border-box;
    border-radius: 6px;
    color: #333;
    background-color: #ffffff;
    border: 1px solid #18ba72;
    position: relative;

    .img-box {
      // position: absolute;
      // top: -1px;
      // bottom: 0;
      // left: -1px;
      width: 30px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background-color: #18ba72;
      border-radius: 6px 0 0 6px;
      .icon {
        width: 18px;
        height: 18px;
      }
    }
    .node-name {
      font-size: 12px;
      font-weight: 500;
      width: 130px;
      padding: 2px 5px;
      box-sizing: border-box;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        font-weight: 400px;
      }
    }
  }

  .offline.source {
    border: 1px solid #447dfd;
    .img-box {
      background-color: #447dfd;
    }
  }
  .offline.source.structured {
    border: 1px solid #0e94cd;
    .img-box {
      background-color: #0e94cd;
    }
  }
  .offline.target {
    border: 1px solid #8d7af8;
    .img-box {
      background-color: #8d7af8;
    }
  }
  .online.source {
    border: 1px solid #18ba72;
    .img-box {
      background-color: #18ba72;
    }
  }
  .online.target {
    border: 1px solid #f5a623;
    .img-box {
      background-color: #f5a623;
    }
  }
</style>
