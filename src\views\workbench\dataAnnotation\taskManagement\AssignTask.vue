<template>
  <!-- 分派任务 -->
  <el-drawer
    v-model="state.assignTaskModalVisible"
    title=""
    :size="680"
    append-to-body
    :close-on-click-overlay="true"
    :before-close="closeDrawer"
    class="team-drawer_68768798"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">分派任务</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawer" />
      </div>
      <div class="n-drawer-body-content">
        <n-form
          :data="state.assignFormData"
          :rules="state.assignRules"
          :label-width="130"
          ref="assignFormRef"
        >
          <n-form-item label="分派用户：" field="receiveTaskUserId">
            <n-select
              v-model="state.assignFormData.receiveTaskUserId"
              placeholder="请选择项目状态"
              filter
              allow-clear
              :options="state.memberList"
            />
            <!--            <el-select-->
            <!--              v-model="state.assignFormData.receiveTaskUserId"-->
            <!--              filterable-->
            <!--              clearable-->
            <!--              remote-->
            <!--              reserve-keyword-->
            <!--              style="width: 100%"-->
            <!--              placeholder="请输入工号或姓名"-->
            <!--              :remote-method="remoteMethod"-->
            <!--              :loading="state.searchLoading"-->
            <!--            >-->
            <!--              <el-option-->
            <!--                v-for="item in state.userList"-->
            <!--                :key="item.id"-->
            <!--                :label="item.name + '-' + item.username"-->
            <!--                :value="item.id"-->
            <!--              />-->
            <!--            </el-select>-->
          </n-form-item>
        </n-form>
      </div>
      <div class="n-drawer-body-footer">
        <!-- 确定 -->
        <n-button color="primary" variant="solid" @click.prevent="handleAssignTaskSubmit">
          确定
        </n-button>
        <!-- 取消 -->
        <n-button color="primary" @click.prevent="state.assignTaskModalVisible = false">
          取消
        </n-button>
      </div>
    </div>
  </el-drawer>
</template>
<script setup>
  import api from '@/api'
  import { reactive, ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { getTeamManagementDetail } from '@/api/dataGovernance.js'
  const emit = defineEmits(['success'])
  const state = reactive({
    assignTaskModalVisible: false, // 领取任务弹窗
    assignFormData: {
      // 领取任务表单数据
      projectCode: null,
      projectId: null,
      receiveTaskUserId: null,
      taskId: null,
    },
    assignRules: {
      // 领取任务表单规则
      receiveTaskUserId: [
        // 团队id
        { required: true, message: '请选择团队', trigger: 'blur', type: 'number' },
      ],
    },
    // 用户列表
    userList: [],
    // 团长团员列表
    memberList: [],
  })
  const assignFormRef = ref(null) // 领取任务表单ref
  const remoteMethod = (query) => {
    state.searchLoading = true
    api.system
      .userList({
        condition: { name: query },
        pageNum: 1,
        pageSize: 100,
      })
      .then((res) => {
        state.searchLoading = false
        if (res.success) {
          state.userList = res.data.list
        }
      })
      .catch(() => {
        state.searchLoading = false
      })
  }
  const handleAssignTaskSubmit = () => {
    // 领取任务提交
    assignFormRef.value.validate((val) => {
      // 表单验证
      if (val) {
        // 验证通过
        api.dataGovernance.receiveTask(state.assignFormData).then((res) => {
          // 领取任务
          if (res.success) {
            // 领取任务成功
            ElMessage.success('领取任务成功') // 提示
            state.assignTaskModalVisible = false // 关闭弹窗
            emit('success') // 刷新列表
          }
        })
      }
    })
  }

  // 获取用户列表
  const getUserList = () => {
    // 获取用户列表
    console.log('获取用户列表')
    api.system
      .userList({
        condition: {},
        pageNum: 1,
        pageSize: 100,
      })
      .then((res) => {
        if (res.success) {
          state.userList = res.data.list
        }
      })
      .catch(() => {})
  }
  const closeDrawer = () => {
    // 关闭弹窗
    state.assignTaskModalVisible = false // 关闭弹窗
  }
  getUserList()
  defineExpose({
    // 暴露方法
    openDrawer: (row, projectId, type) => {
      // 打开弹窗
      state.assignFormData = {}
      state.assignFormData.taskId = row.id // 任务id
      state.assignFormData.projectId = projectId // 项目id
      state.assignFormData.documentCategoryId = row.documentCategoryId // 项目id
      if (type) {
        state.assignTaskModalVisible = true // 打开弹窗
        state.assignFormData.projectCode = row.projectCode
        getTeamManagementDetail(row.teamId).then((res) => {
          if (res.success) {
            let list = []
            list.push({
              name:
                res.data?.teamInfo?.userIdLeaderNamecn + '-' + res.data?.teamInfo?.userIdLeaderName,
              value: res.data?.teamInfo?.userIdLeader,
              role: '组长',
            })
            res.data?.teamUserList?.forEach((val) => {
              list.push({
                name: val.uname + '-' + val.userName,
                value: val.userId,
                role: '组员',
              })
            })
            state.memberList = list
          }
        })
      } else {
        api.dataGovernance.receiveTask(state.assignFormData).then((res) => {
          // 领取任务
          if (res.success) {
            // 领取任务成功
            ElMessage.success('领取任务成功') // 提示
            state.assignTaskModalVisible = false // 关闭弹窗
            emit('success') // 刷新列表
          }
        })
      }
    },
  })
</script>
