<template>
  <!-- 数据打标-列打标 -->
  <div class="link-meta-data">
    <div class="link-meta-data-content">
      <div :class="{ 'filter-type-box': true }">
        <div>
          <n-button
            v-if="state.isNeedSelection"
            color="primary"
            variant="solid"
            :disabled="!state.activeAssetsList.length"
            @click.prevent="save"
            >打标</n-button
          >
        </div>
        <div class="commonForm">
          <n-form :inline="true" :data="state.filterSearch" class="demo-form-inline search-right">
            <n-form-item label="">
              <n-input
                v-model="state.filterSearch.keyword"
                size="small"
                placeholder="中/英名称搜索"
                clearable
                @clear="onSearch"
              >
                <template #append>
                  <n-button @click.prevent="onSearch">
                    <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                      <SvgIcon class="icon_search" icon="icon_search" />
                    </n-popover>
                  </n-button>
                </template>
              </n-input>
            </n-form-item>
          </n-form>
        </div>
      </div>

      <div class="public-table-box" v-loading="state.loading">
        <n-public-table
          ref="publicTable"
          :key="state.key"
          :isDisplayAction="false"
          :isNeedSelection="state.isNeedSelection"
          :table-head-titles="state.tableHeadTitles"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          @tablePageChange="tablePageChange"
          @handleSelectionChange="handleSelectionChange"
        >
          <template #tagInfos="{ editor }">
            <div class="tag-infos-box">
              <div
                class="tag-item-box"
                v-for="item in editor.row.tagInfos"
                :key="item.id"
                :title="item.name"
              >
                <div class="tag-item"
                  >{{ item.name }}
                  <SvgIcon
                    v-if="state.isNeedSelection"
                    icon="mark-del"
                    class="reduce"
                    title="删除"
                    @click="delMarking(editor.row, item.id)"
                  />
                </div>
              </div>

              <div class="more-box">
                <SvgIcon
                  v-if="editor.row.tagInfos?.length > 3"
                  class="more-icon"
                  icon="more-icon"
                  title="更多"
                  @click="seeMoreTags(editor)"
                />
              </div>
            </div>
          </template>
        </n-public-table>
      </div>
    </div>

    <!-- 查看具体标签弹框 -->

    <n-modal
      title="添加标签"
      bodyClass="allTagsDialog commonDialog"
      v-model="state.tagsDialogVisible"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="false"
      width="780px"
      :before-close="closeTagsDialog"
    >
      <div class="tags-dialog-content">
        <div
          class="tag-item"
          v-for="item in state.checkRowTags"
          :key="item.id"
          :title="item.name"
          >{{ item.name }}</div
        >
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button color="primary" size="sm" variant="solid" @click.prevent="closeTagsDialog"
            >关 闭</n-button
          ></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { reactive, ref, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  export default {
    name: '',
    components: {},
    props: {},

    setup(props, { emit }) {
      const form = ref()
      const router = useRouter()
      const publicTable = ref()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        tableData: {},
        tableHeight: 736,
        loading: false,
        filterSearch: {
          keyword: '',
        },

        treeData: [],
        treeAttrData: {
          showCheckbox: false,
          showControl: true,
          showLeftIcon: true,
          parentControl: '',
          childControl: '',
        },
        allTags: [],
        activeTags: [],
        dialogVisible: false,
        tagsDialogVisible: false,
        checkRowTags: [], //查看元数据的所有标签
        submiting: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名字' },
          { prop: 'name', name: '英文名字' },
          { prop: 'fieldTypeName', name: '字段类型' },
          { prop: 'fieldLength', name: '字段长度' },
          { prop: 'description', name: '描述信息' },
          { prop: 'tagInfos', name: '标签', slot: 'tagInfos', width: 288 },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        modelId: '',
        allParamColumns: [], //数据过滤元数据下拉数据
        filterOperator: [],

        key: '1',
        modelName: '',
        groupId: null, //标签组分页
        activeAssetsList: [], //批量选中的标签列表

        isNeedSelection: true, //是否需要勾选框 查看不需要
        showTagsIds: [], //当前标签分类下展示的标签合计
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 310 - 50
        },
        //单个删除标签
        delMarking(row, id) {
          proxy.$MessageBoxService.open({
            title: '是否确认删除该标签',
            content: '删除后该标签将不可使用',
            save: () => {
              let _tagInfos = row.tagInfos.filter((item) => {
                return item.id !== id
              })

              row.newTagInfos = _tagInfos.map((list) => {
                return list.id
              })

              emit('delSingerMarking', [row])
            },
          })
        },
        //资产打标
        save() {
          emit('showTagDialog', { activeAssetsList: state.activeAssetsList })
        },
        goBack() {
          router.go(-1)
        },

        //查看更多标签
        seeMoreTags(editor) {
          state.checkRowTags = editor.row.tagInfos
          state.tagsDialogVisible = true
        },
        // 关闭查看标签弹框
        closeTagsDialog() {
          state.tagsDialogVisible = false
        },

        // 初始化模型资产列表
        initTable(filter = false) {
          let data = {
            condition: {
              modelId: state.modelId,
              modelName: state.modelName,
              metadataName: state.filterSearch.keyword,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          if (filter) {
            data.condition.filterCondition = []
          }
          state.loading = true
          api.assets
            .getColTagList(data)
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                if (data) {
                  let allData = { list: [] }

                  data.list.forEach((item, index) => {
                    let _item = Object.assign(item.metadataDTO, {
                      tagInfos: item.tagInfos,
                      number: index + 1,
                    })
                    allData.list.push(_item)
                  })

                  data.list = allData.list
                  state.tableData = data
                }
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        //获取勾选数据
        handleSelectionChange(val) {
          state.activeAssetsList = val
        },

        //搜索
        onSearch() {
          state.pagination.currentPage = 1
          // 清空选中
          publicTable.value.clearSelection()
          methods.initTable()
        },

        init() {
          methods.initTable()
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        state.modelId = Number(router.currentRoute.value.query.modelId) // 获取路由传参
        state.modelName = router.currentRoute.value.query.modelName // 获取路由传参

        state.isNeedSelection = router.currentRoute.value.query.type ? false : true

        methods.initTable()
      })

      return {
        state,
        form,
        publicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .link-meta-data {
    &-content {
      background-color: #fff;
      border-radius: 5px;
      height: calc(100% - 60px);
      overflow: hidden;
    }
    .options-box-bg {
      height: 66px;
      margin-top: 10px;
      margin-left: -10px;
      margin-right: -10px;

      .content {
        width: 100%;
        height: 100%;
        padding: 0 20px;
        text-align: right;
        background-color: #fff;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
    .top-title {
      .need_smallcube__title {
        margin: 0;
      }
    }

    .filter-type-box {
      display: flex;

      padding: 20px 0 20px;
      justify-content: space-between;

      .content-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .model-mark {
        padding: 12px 0 0 10px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 380;
        color: #999999;
        line-height: 20px;
        // border-top: 1px solid $border;
        max-height: 86px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    :deep(.middleDialog) {
      .dialog-content-box {
        display: flex;
        height: calc(70vh - 120px);
        justify-content: center;
        .tree {
          border-radius: 4px 0px 0px 4px;
          border: 1px solid #ebeef5;
          border-right: 0;
        }
        .tree-search {
          padding: 10px 10px 0;
        }
      }
    }
    :deep(.allTagsDialog) {
      .tags-dialog-content {
        display: flex;
        flex-wrap: wrap;
        max-height: 50vh;
        overflow-y: scroll;
        .tag-item {
          height: 28px;
          line-height: 28px;
          background: #f2f3f6;
          font-size: 12px;
          color: #333333;
          border-radius: 14px;
          padding: 0 10px;
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: default;
          text-align: center;
        }
      }
    }
    .add-tag-box {
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }

    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: flex-start;
    }

    .common-table {
      :deep(.nancalui-table) {
        .nancalui-table__cell {
          position: relative;
        }
        .tag-infos-box {
          display: flex;
          align-items: center;
          .tag-item-box {
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
            &:hover {
              .yy-icon {
                display: block;
                cursor: pointer;
              }
            }
            &:nth-of-type(3) {
              margin-right: 50px;
            }
            .yy-icon {
              width: 18px;
              height: 18px;
              position: absolute;
              top: 0px;
              right: 5px;
              display: none;
            }
          }

          .tag-item {
            flex-shrink: 0; //不压缩
            width: 68px;
            height: 28px;
            background: #f2f3f6;
            border-radius: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: 28px;
            text-align: center;
            margin-right: 10px;
            padding: 0 10px;
            cursor: default;
          }
          .more-box {
            position: absolute;
            right: -10px;
            top: 0;
            bottom: 0;
            padding: 0 10px;
            display: flex;
            align-items: center;
            // background-color: #fff;
            &:hover {
              // background-color: #f5f7fa;
            }
          }
          .more-icon {
            color: #333;

            width: 22px;
            height: 18px;
            cursor: pointer;
            &:hover {
              color: #4d4e4eff;
              background-color: #eff1f5ff;
            }
          }
        }
      }
    }

    .footerBtn {
      position: fixed;
      right: 20px;
      bottom: 30px;
      z-index: 9;
    }
  }
</style>
