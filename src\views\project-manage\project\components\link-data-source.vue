<template>
  <!-- 关联数据源 -->
  <div class="project-add-data-source">
    <n-public-table
      ref="publicTable"
      :isDisplayAction="true"
      :isNeedSelection="true"
      :table-head-titles="state.tableHeadTitles"
      :tableData="state.tableData"
      :pagination="state.pagination"
      :tableHeight="state.tableHeight"
      :actionWidth="140"
      @tablePageChange="tablePageChange"
      @handle-selection-change="handleSelectionChange"
    >
      <template #pageTop>
        <div class="box-add">
          <div class="top-left">
            <n-button color="primary" @click.prevent="addDataSource">添加数据源</n-button>
            <n-button
              v-if="state.editId"
              color="primary"
              size="default"
              @click.prevent="batchUnbind"
              >批量删除</n-button
            >
            <n-button v-else color="primary" size="default" @click.prevent="locBatchDeletion"
              >批量删除</n-button
            >
          </div>
        </div>
      </template>
      <!-- <template #dataStructureType="{ editor }">
        <div>{{
          editor.row.dataStructureType === 'ALL_STRUCTURE' ? '结构化数据源' : '非结构化数据源'
        }}</div>
      </template> -->
      <template #editor="{ editor }">
        <div class="edit-box">
          <n-button
            v-if="state.editId"
            class="has-right-border"
            variant="text"
            @click.prevent="unbindDataSource(editor)"
            >解绑</n-button
          >
          <n-button
            v-else
            class="has-right-border"
            variant="text"
            @click.prevent="locDeleteDataSource(editor)"
            >移除</n-button
          >
        </div>
      </template>
      <template #empty>
        <slot name="empty">
          <div class="table-no-content">
            <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
            <div>暂无数据源，请添加数据源</div>
          </div>
        </slot>
      </template>
    </n-public-table>
    <allDataSource ref="allDataSource" @getSelectData="getSelectData" />
  </div>
</template>
<script>
  import { reactive, onMounted, ref, getCurrentInstance } from 'vue'
  import allDataSource from './all-data-source'

  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  export default {
    name: '',
    components: { allDataSource },
    setup() {
      const allDataSource = ref()
      const publicTable = ref()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const router = useRouter()
      const state = reactive({
        tableData: {},
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'name', name: '数据源名称' },
          // { prop: 'database', name: '实例名称' },
          { prop: 'dataStructureTypeName', name: '数据结构类型' },
          { prop: 'datasourceType', name: '数据库类型' },
          { prop: 'description', name: '描述' },
        ],
        tableHeight: 400,
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          dataStructure: null,
          databaseType: null,
          keyword: null,
        },
        allTableData: [], //本页面所有数据
        selectTableData: [], // 本页选中的数据
        currentPageData: [], // 本页展示的数据
        editId: null, //编辑id
        editProjectCode: '',
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 347 - 14
        },
        // 编辑时候回显
        editInit(data) {
          if (state.allTableData.length) return
          state.editProjectCode = data.projectCode
          state.allTableData = data.datasourceList

          let _data = methods.startPagination(data.datasourceList) //前端分页第一页数据

          methods.updatedTable(_data)
        },
        //弹窗点击保存  --从勾选数据中获取没有的id放进所有数据里
        getSelectData(data) {
          let allIds = state.allTableData.map((item) => item.id)
          data?.forEach((item) => {
            if (!allIds.includes(item.id)) {
              // state.allTableData.push(item)
              delete item.disabledThisRow
              state.allTableData.unshift(item)
            } else {
              delete item.disabledThisRow
            }
          })

          // state.allTableData = data
          state.currentPageData = methods.startPagination(state.allTableData)
          // 新增序号属性
          state.currentPageData.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          methods.updatedTable(state.currentPageData)
        },

        // 新增数据源
        addDataSource() {
          allDataSource.value.init(JSON.parse(JSON.stringify(state.allTableData)))
        },

        // 更新表格
        updatedTable(data) {
          // 序号
          data.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          let _data = {
            total: state.allTableData.length,
            list: data,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.tableData = _data
          // publicTable.value.initTableData(_data)
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          let starPos = (data.currentPage - 1) * data.pageSize
          let endPos = data.currentPage * data.pageSize
          state.currentPageData = state.allTableData.slice(starPos, endPos)
          methods.updatedTable(state.currentPageData)
        },

        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
        },
        // 编辑单条解绑数据源
        unbindDataSource(data) {
          if (!data) return
          proxy.$MessageBoxService.open({
            title: '是否确认解绑该数据源',
            content: '解绑后该数据源将无法使用',
            save: () => {
              api.project
                .removeDatasource({ projectCode: state.editProjectCode, data: [data.row.id] })
                .then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '解绑数据源成功',
                      type: 'success',
                    })
                    methods.deleteDataSource(data)
                  }
                })
            },
          })
        },
        // 编辑批量解绑数据源
        batchUnbind() {
          if (!state.selectTableData.length) return
          let datasourceIds = state.selectTableData.map((item) => {
            return item.id
          })
          proxy.$MessageBoxService.open({
            title: '是否确认批量解绑数据源',
            content: '解绑后该数据源将无法使用',
            save: () => {
              api.project
                .removeDatasource({ projectCode: state.editProjectCode, data: datasourceIds })
                .then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '移除数据源成功',
                      type: 'success',
                    })
                    methods.batchDeletion()
                  }
                })
            },
          })
        },
        // 本地移除
        locDeleteDataSource(data) {
          if (!data) return
          proxy.$MessageBoxService.open({
            title: '是否确认移除该数据源',
            content: '移除后该数据源将无法使用',
            save: () => {
              ElNotification({
                title: '提示',
                message: '移除数据源成功',
                type: 'success',
              })
              methods.deleteDataSource(data)
            },
          })
        },
        // 本地批量移除
        locBatchDeletion() {
          if (!state.selectTableData.length) return
          proxy.$MessageBoxService.open({
            title: '是否批量移除该数据源',
            content: '移除后该数据源将无法使用',
            save: () => {
              methods.batchDeletion()
              ElNotification({
                title: '提示',
                message: '移除数据源成功',
                type: 'success',
              })
            },
          })
        },
        // 单个-移除数据源
        deleteDataSource(data) {
          state.allTableData = state.allTableData.filter((item) => {
            return item.id !== data.row.id
          })
          if (state.currentPageData.length === 1) {
            if (state.pagination.currentPage-- <= 1) state.pagination.currentPage = 1
          }

          state.currentPageData = methods.startPagination(state.allTableData)
          methods.updatedTable(state.currentPageData)
        },

        // 批量删除
        batchDeletion() {
          state.selectTableData.forEach((select) => {
            state.allTableData = state.allTableData.filter((item) => {
              return item.id !== select.id
            })
            if (state.currentPageData.length === 1) {
              if (state.pagination.currentPage-- <= 1) state.pagination.currentPage = 1
            }
            state.currentPageData = methods.startPagination(state.allTableData)
          })
          methods.updatedTable(state.currentPageData)
          // 清空选中
          publicTable.value.clearSelection()
        },

        // 模拟分页
        startPagination(data) {
          let { currentPage, pageSize } = state.pagination
          let starPos = (currentPage - 1) * pageSize
          let endPos = currentPage * pageSize
          // 更新表格
          let filterData = data.slice(starPos, endPos)
          return filterData
        },
        //获取所有数据
        getAllData() {
          return state.allTableData
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        state.editId = router.currentRoute.value.query.id // 获取路由传参

        methods.updatedTable([])
      })

      return {
        state,
        allDataSource,
        publicTable,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .project-add-data-source {
    background-color: #fff;
    padding: 0 16px 0 16px;
    height: 100%;
    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
    }
  }
</style>
