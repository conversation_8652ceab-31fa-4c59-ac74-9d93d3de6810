<template>
  <div class="container" v-loading="loading" element-loading-text="保存中...">
    <el-button type="primary" class="save" @click="save">保存</el-button>
    <DocumentEditor
      id="docEditor"
      documentServerUrl="http://************:10086/"
      :config="config"
      :events_onDocumentReady="onDocumentReady"
      :onLoadComponentError="onLoadComponentError"
    />
  </div>
</template>

<script setup>
  // import { useRouter, useRoute } from 'vue-router'
  import api from '@/api/index'
  import { DocumentEditor } from '@onlyoffice/document-editor-vue'
  const route = useRoute()
  const urlMap = JSON.parse(localStorage.getItem('urlMap') || '{}')
  const { ossUrl, sourceFileName, id } = route.query
  urlMap[id] ??= ossUrl
  const loading = ref(false)
  const config = ref({
    document: {
      fileType: 'docx',
      key: Math.random().toString(32).substr(2, 16),
      title: sourceFileName,
      url: urlMap[id],
      permissions: {
        chat: false,
        comment: false,
        copy: true,
        deleteCommentAuthorOnly: false,
        download: true,
        edit: true,
        editCommentAuthorOnly: false,
        fillForms: true,
        modifyContentControl: false,
        print: true,
        protect: false,
        review: false,
        reviewGroups: ['Group1', 'Group2', ''],
        userInfoGroups: ['Group1', ''],
      },
    },
    documentType: 'word',
    events: {
      onDownloadAs: onDownloadAs,
    },
    editorConfig: {
      lang: 'zh-CN',
      customization: {
        anonymous: {
          request: true,
          label: 'Guest',
        },
        autosave: true,
        comments: false,
        compactHeader: true,
        compactToolbar: false,
        compatibleFeatures: true,
        forcesave: false,
        help: false,
        hideRightMenu: false,
        hideRulers: false,
        integrationMode: 'embed',
        logo: {
          image:
            'data:image/svg+xml;base64,' +
            window.btoa(
              `<svg xmlns="http://www.w3.org/2000/svg" width="106" height="14" viewBox="0 0 106 14" fill="none"><path d="M100.465 12V3.15601H105.685V4.33201H101.857V6.79201H105.097V7.96801H101.857V10.812H105.817V12H100.465Z" fill="#444444"/><path d="M96.1508 12.168C93.9308 12.168 92.2148 10.5 92.2148 7.596C92.2148 4.728 93.9788 3 96.2228 3C97.3148 3 98.1788 3.528 98.7188 4.116L97.9748 5.004C97.5068 4.548 96.9548 4.212 96.2468 4.212C94.7228 4.212 93.6548 5.484 93.6548 7.56C93.6548 9.648 94.6508 10.944 96.2108 10.944C97.0148 10.944 97.6268 10.572 98.1548 9.996L98.9109 10.884C98.1909 11.712 97.3028 12.168 96.1508 12.168Z" fill="#444444"/><path d="M89.0039 12V3.15601H90.3959V12H89.0039Z" fill="#444444"/><path d="M82.207 12V3.15601H87.439V4.33201H83.599V7.03201H86.875V8.19601H83.599V12H82.207Z" fill="#444444"/><path d="M75.4102 12V3.15601H80.6422V4.33201H76.8022V7.03201H80.0782V8.19601H76.8022V12H75.4102Z" fill="#444444"/><path d="M69.7231 12.168C67.4551 12.168 65.8711 10.392 65.8711 7.548C65.8711 4.692 67.4551 3 69.7231 3C72.0031 3 73.5751 4.704 73.5751 7.548C73.5751 10.392 72.0031 12.168 69.7231 12.168ZM69.7231 10.944C71.1871 10.944 72.1471 9.612 72.1471 7.548C72.1471 5.484 71.1871 4.212 69.7231 4.212C68.2591 4.212 67.3111 5.484 67.3111 7.548C67.3111 9.612 68.2591 10.944 69.7231 10.944Z" fill="#444444"/><path d="M60.4766 12V3.15601H61.8686V10.812H65.6126V12H60.4766Z" fill="#444444"/><path d="M51.8477 12L54.7757 3.15601H56.3837L59.3117 12H57.8357L57.0917 9.49201H54.0197L53.2757 12H51.8477ZM54.3557 8.38801H56.7557L56.4077 7.20001C56.1197 6.24001 55.8557 5.25601 55.5797 4.26001H55.5317C55.2677 5.26801 54.9917 6.24001 54.7037 7.20001L54.3557 8.38801Z" fill="#444444"/><path d="M48.6977 12.168C46.4777 12.168 44.7617 10.5 44.7617 7.596C44.7617 4.728 46.5257 3 48.7697 3C49.8617 3 50.7257 3.528 51.2657 4.116L50.5217 5.004C50.0537 4.548 49.5017 4.212 48.7937 4.212C47.2697 4.212 46.2017 5.484 46.2017 7.56C46.2017 9.648 47.1977 10.944 48.7577 10.944C49.5617 10.944 50.1737 10.572 50.7017 9.996L51.4577 10.884C50.7377 11.712 49.8497 12.168 48.6977 12.168Z" fill="#444444"/><path d="M36.4414 12V3.15601H37.8694L40.7974 8.35201L41.7214 10.212H41.7814C41.7094 9.31201 41.5894 8.24401 41.5894 7.28401V3.15601H42.9214V12H41.4934L38.5654 6.79201L37.6414 4.94401H37.5934C37.6534 5.85601 37.7614 6.87601 37.7614 7.84801V12H36.4414Z" fill="#444444"/><path d="M27.8125 12L30.7405 3.15601H32.3485L35.2765 12H33.8005L33.0565 9.49201H29.9845L29.2405 12H27.8125ZM30.3205 8.38801H32.7205L32.3725 7.20001C32.0845 6.24001 31.8205 5.25601 31.5445 4.26001H31.4965C31.2325 5.26801 30.9565 6.24001 30.6685 7.20001L30.3205 8.38801Z" fill="#444444"/><path d="M20.1641 12V3.15601H21.5921L24.5201 8.35201L25.4441 10.212H25.5041C25.4321 9.31201 25.3121 8.24401 25.3121 7.28401V3.15601H26.6441V12H25.2161L22.2881 6.79201L21.3641 4.94401H21.3161C21.3761 5.85601 21.4841 6.87601 21.4841 7.84801V12H20.1641Z" fill="#444444"/><path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M1.60452 9.50808L6.56252 11.8261C7.06552 12.0551 7.91052 12.0551 8.43652 11.8261L13.3725 9.51908L14.5605 10.0761C15.0855 10.3281 15.0855 10.7171 14.5605 10.9461L8.39152 13.8291C7.91152 14.0581 7.06652 14.0581 6.56352 13.8291L0.393523 10.9451C-0.132477 10.6931 -0.132477 10.3041 0.393523 10.0751L1.60352 9.50708L1.60452 9.50808Z" fill="#444444"/><path opacity="0.75" fill-rule="evenodd" clip-rule="evenodd" d="M1.55452 5.98511L6.56252 8.32611C7.06552 8.55511 7.91052 8.55511 8.43652 8.32611L13.4445 5.98511L14.6055 6.52911C15.1315 6.78111 15.1315 7.17011 14.6055 7.39911L8.43652 10.2821C7.91052 10.5341 7.06552 10.5341 6.56252 10.2821L0.393523 7.39811C-0.132477 7.14611 -0.132477 6.75711 0.393523 6.52811L1.55452 5.98511Z" fill="#444444"/><path fill-rule="evenodd" clip-rule="evenodd" d="M6.56252 6.825L0.393523 3.942C-0.132477 3.69 -0.132477 3.301 0.393523 3.072L6.56252 0.189C7.08752 -0.063 7.93352 -0.063 8.43652 0.189L14.6055 3.072C15.1305 3.324 15.1305 3.713 14.6055 3.942L8.43652 6.825C7.91052 7.054 7.06552 7.054 6.56252 6.825Z" fill="#444444"/></svg>`,
            ),
          // imageDark:
          //   'https://api.onlyoffice.com/zh/content/img/docbuilder/examples/user-profile.png',
          // url: 'https://example.com',
        },
        macros: false,
        mentionShare: false,
        mobileForceView: true,
        plugins: false,
        toolbarHideFileName: false,
        toolbarNoTabs: false,
        unit: '厘米',
        zoom: 100,
      },
      //   callbackUrl: 'http://*************/url-to-callback.ashx',
    },
  })
  // 获取文件流
  const getFileStream = async (url) => {
    return new Promise((resolve, reject) => {
      fetch(url)
        .then((response) => response.blob())
        .then((blob) => {
          const file = new File([blob], sourceFileName, {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          })
          resolve(file)
        })
    })
  }
  // 上传到doc服务器
  const uploadFile = async () => {
    const file = await getFileStream(ossUrl)
    const formData = new FormData()
    formData.append('uploadedFile', file)
    fetch('http://*************/example/upload', {
      body: formData,
      method: 'POST',
      mode: 'cors',
    })
      .then((response) => response.json())
      .then((data) => {
        console.log('成功上传', data)
      })
  }
  const save = async () => {
    loading.value = true
    window.DocEditor.instances.docEditor.downloadAs('docx')
  }
  // uploadFile()
  async function onDownloadAs({ data, target }) {
    const { fileType, url } = data
    api.dataManagement
      .onlyOfficeUpload({
        httpUrl: url,
      })
      .then((res) => {
        urlMap[id] = res?.data?.webUrl || urlMap[id]
        localStorage.setItem('urlMap', JSON.stringify(urlMap))
        loading.value = false
      })
  }
  const onDocumentReady = () => {
    console.log('Document is loaded')
  }
  const onLoadComponentError = (errorCode, errorDescription) => {
    switch (errorCode) {
      case -1: // Unknown error loading component
        console.log(errorDescription)
        break

      case -2: // Error load DocsAPI from http://documentserver/
        console.log(errorDescription)
        break

      case -3: // DocsAPI is not defined
        console.log(errorDescription)
        break
    }
  }
</script>
<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    position: relative;
  }
  #docEditor {
    width: 100%;
    height: 100%;
  }
  .save {
    position: absolute;
    right: 60px;
    top: 10px;
    z-index: 1000;
  }
</style>
