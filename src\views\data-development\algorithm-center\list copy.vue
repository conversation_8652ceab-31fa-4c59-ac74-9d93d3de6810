<template>
  <section class="container">
    <section class="container-box">
      <section class="container-right">
        <section class="tools">
          <section class="commonForm">
            <n-button
              v-if="buttonAuthList.includes('governanceManage_dataDev_algorithmCenter_add')"
              code="governanceManage_dataDev_algorithmCenter_add"
              variant="solid"
              @click.prevent="onCreate"
            >
              新增算法
            </n-button>
            <n-form :inline="true" :data="searchForm" class="demo-form-inline">
              <n-form-item label="时间范围：">
                <n-range-date-picker-pro
                  v-model="searchForm.time"
                  :placeholder="['开始日期', '结束日期']"
                  format="YYYY-MM-DD"
                  allow-clear
                  @confirmEvent="onSearch(true)"
                />
              </n-form-item>
              <n-form-item label="">
                <n-input
                  v-model="searchForm.keyWord"
                  size="small"
                  placeholder="关键字搜索"
                  clearable
                  @clear="onSearch(true)"
                >
                  <template #append>
                    <n-button @click.stop.prevent="onSearch(true)">
                      <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                        <SvgIcon class="icon_search" icon="icon_search" />
                      </n-popover>
                    </n-button>
                  </template>
                </n-input>
              </n-form-item>
            </n-form>
          </section>
        </section>

        <section class="table">
          <n-public-table
            ref="publicTable"
            :isDisplayAction="true"
            :needOtherActionBar="needOtherActionBar"
            :table-head-titles="tableHeadTitles"
            :pagination="pagination"
            :tableHeight="tableHeight"
            :tableData="tableData"
            :actionWidth="240"
            @tablePageChange="tablePageChange"
          >
            <template #editor="{ editor }">
              <div class="edit-box">
                <n-button
                  v-if="
                    buttonAuthList.includes('governanceManage_dataDev_algorithmCenter_online_edit')
                  "
                  code="governanceManage_dataDev_algorithmCenter_online_edit"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.prevent="onDefiend(editor)"
                  >在线编辑
                </n-button>
                <n-button
                  v-if="buttonAuthList.includes('governanceManage_dataDev_algorithmCenter_edit')"
                  code="governanceManage_dataDev_algorithmCenter_edit"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.prevent="onEdit(editor)"
                  >编辑
                </n-button>
                <n-button
                  v-if="buttonAuthList.includes('governanceManage_dataDev_algorithmCenter_delete')"
                  code="governanceManage_dataDev_algorithmCenter_delete"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.prevent="onDelete(editor)"
                  >删除
                </n-button>
              </div>
            </template>
          </n-public-table>
        </section>
      </section>
    </section>
  </section>
</template>
<script>
  import { formartTime } from '@/utils/index'
  import { mapState } from 'vuex'
  import ENUM from '@/const/enum'

  export default {
    data() {
      return {
        shortcuts: ENUM.SHORTCUTS,
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'name', name: '名称' },
          { prop: 'fileUrl', name: '文件链接' },
          { prop: 'description', name: '描述' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        list: [],
        tableData: {},
        tableHeight: 300,
        searchForm: {
          taskType: '1', // "1-数据脚本 2-数据作业"
          scriptType: 2,
          keyWord: '',
          time: '',
        },
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
        buttonAuthList: (state) => state['user'].buttonAuthList,
      }),
    },
    created() {},
    mounted() {
      this.tableHeight = document.body.offsetHeight - 252
      this.onSearch(true)
    },
    methods: {
      // 表格操作变化
      tablePageChange(data) {
        this.pagination.currentPage = data.currentPage
        this.pagination.pageSize = data.pageSize
        this.onSearch(false)
      },
      onSearch(init = true) {
        this.pagination.currentPage = init ? 1 : this.pagination.currentPage
        let { keyWord } = this.searchForm
        let endTime = ''
        let startTime = ''
        if (this.searchForm.time && this.searchForm.time[0]) {
          startTime = formartTime(this.searchForm.time[0])
          endTime = formartTime(this.searchForm.time[1], true)
        }
        let params = {
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          condition: {
            keyWord: keyWord || null,
          },
        }
        if (startTime) {
          params.condition.startTime = startTime
          params.condition.endTime = endTime
        }
        this.$api.dataDev.listAlgorithm(params).then((res) => {
          res.data.list.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          this.list = res.data.list
          this.tableData = res.data
        })
      },
      // 新增
      onCreate() {
        if (!this.currentProject.projectCode) {
          this.$notify({
            title: '提示',
            message: '请联系管理员分配场景权限',
            type: 'warning',
          })
          return
        }
        this.$router.push({
          name: 'algorithmCenterAdd',
        })
      },
      // 在线编辑
      onDefiend(editor) {
        let { row } = editor
        this.$router.push({
          name: 'algorithmUserDefiend',
          query: { url: row.fileUrl },
        })
      },
      // 编辑
      onEdit(editor) {
        let { row } = editor
        let params = {
          id: row.id,
          name: row.name,
          description: row.description,
        }
        this.$router.push({
          name: 'algorithmCenterEdit',
          query: params,
        })
      },

      onDelete(editor) {
        let { row } = editor
        let params = {
          id: row.id,
        }
        this.$dialogPopup({
          title: '提示',
          message: '此操作将永久删除该脚本, 是否继续？',
          cancel: () => {
            console.log('你点击了取消')
          },
          save: () => {
            this.$api.dataDev.deleteAlgorithm(params).then((res) => {
              this.onSearch()
              if (res.success) {
                this.onSearch(this.list.length > 1 ? false : true)
                this.$notify({
                  title: '成功',
                  message: '删除成功',
                  type: 'success',
                })
                // this.$router.go(-1)
              }
            })
          },
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    &-box {
      border-radius: 4px;
      display: flex;
    }

    &-right {
      width: 100%;
      border-left: 1px solid #ebebeb;
      padding: 24px 20px 72px 20px;
    }
  }

  .tab {
    position: relative;
    background: #fff;
    padding-left: 36px;

    span {
      display: inline-block;
      height: 60px;
      line-height: 60px;
      padding: 0 7px;
      margin-right: 38px;
      color: #000;
      border-bottom: 4px solid transparent;
      font-size: 16px;
      box-sizing: border-box;
      cursor: pointer;
    }

    .tab-active {
      color: $themeBlue;
      border-bottom: 4px solid $themeBlue;
    }
  }

  .tools {
    position: relative;
    padding-bottom: 20px;
    background: #fff;
  }

  .table {
    background: #fff;

    .operate-btn {
      color: $themeBlue;
      padding: 0 10px;
      height: 20px;
      line-height: 20px;
      border-right: 1px solid #cfcfcf;
      margin-left: 0;
      border-radius: 0;

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        border-right: 1px solid transparent;
        padding-right: 0;
      }
    }
  }
</style>
