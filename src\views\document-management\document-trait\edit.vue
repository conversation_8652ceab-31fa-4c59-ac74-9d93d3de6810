<template>
  <div class="container">
    <div class="cf-page-title">
      非结构化数据特征管理
      <div class="detail-back-box" @click.prevent="goBack(false)"> 返回 </div>
    </div>
    <div class="white-box nc-p-16" style="height: calc(100% - 128px); overflow: auto">
      <n-form
        :data="state.formData"
        ref="formRef"
        :rules="state.rules"
        label-width="150px"
        label-suffix="："
      >
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="ruleCode" label="非结构化数据编号">
              <n-input v-model="state.formData.ruleCode" disabled />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="name" label="非结构化数据名称">
              <n-input v-model="state.formData.name" disabled />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="24">
            <n-form-item field="description" label="描述">
              <n-textarea
                v-model="state.formData.description"
                placeholder="请输入描述信息"
                maxlength="300"
                :autosize="{ minRows: 5 }"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="24">
            <n-form-item field="tagIds" label="特征关键字">
              <div style="color: #1d2129">{{ state.formData.keywordList?.join('，') || '-' }} </div>
              <!-- <n-select
                v-model="state.formData.tagIds"
                :options="state.targetOptions"
                allow-clear
                multiple
                :filter="getTargetList"
                @value-change="selectChangeFn"
                placeholder="请选择"
              /> -->
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="24">
            <n-form-item field="tagIds" label="标签">
              <el-tree-select
                ref="selectTree"
                v-model="state.formData.tagIds"
                :data="state.targetOptions"
                style="width: 100%"
                node-key="key"
                :props="{
                  label: 'name',
                  value: 'key',
                  children: 'children',
                }"
                multiple
                show-checkbox
                :render-after-expand="false"
                filterable
                clearable
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="type" label="数据类型">
              <n-input v-model="state.formData.type" />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="formatName" label="数据格式">
              <n-input v-model="state.formData.formatName" />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="size" label="大小">
              <n-input v-model="state.formData.size" />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="wordCount" label="字数">
              <n-input v-model="state.formData.wordCount" />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="maxValue" label="数据最大值">
              <n-input v-model="state.formData.maxValue" />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="minValue" label="数据最小值">
              <n-input v-model="state.formData.minValue" />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="createByName" label="创建人">
              <n-input v-model="state.formData.createByName" />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="createTime" label="创建时间">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.createTime"
                placeholder="请选择创建时间"
                :showTime="true"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="updateTime" label="更新时间">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.updateTime"
                placeholder="请选择更新时间"
                :showTime="true"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="collectTime" label="采集时间">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.collectTime"
                placeholder="请选择采集时间"
                :showTime="true"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="collectSystem" label="采集系统">
              <n-input v-model="state.formData.collectSystem" />
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="collectRate" label="更新频率">
              <n-input v-model="state.formData.collectRate" />
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>
    <div class="white-box nc-m-t-10 nc-p-16">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { formartTimeDate, formartTime } from '@/utils/index'
  import moment from 'moment'
  import { useRouter } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'
  import api from '@/api/index'
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const selectTree = ref(null)
  const state = reactive({
    formData: {
      id: '',
      name: '',
      ruleCode: '',
      description: '',
      tagIds: [],
      type: '',
      format: '',
      formatName: '',
      size: '',
      wordCount: '',
      maxValue: '',
      minValue: '',
      createByName: '',
      createTime: '',
      updateTime: '',
      collectTime: '',
      collectSystem: '',
      collectRate: '',
      keywordList: null,
    },
    targetOptions: [],
    nameExist: {},
    rules: {},
    activeTagList: [],
  })

  function getDetail() {
    return api.documentManage.getTraitDetail({ id: state.formData.id })
  }
  //选中标签change 记录选中的标签列表
  const selectChangeFn = (val) => {
    state.activeTagList = val.map((val) => {
      return { color: val.color, id: val.id, name: val.text, text: val.text, value: val.id }
    })
  }
  // 获取标签列表
  function getTargetList(val) {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        state.targetOptions = data
      }
    })
  }
  // 取消
  function goBack(refresh = false) {
    if (refresh) {
      router.push({ name: 'documentTraitList', query: { refresh: 'true' } })
    } else {
      router.go(-1)
    }
  }
  // 确定
  const formRef = ref(null)
  function onConfirm() {
    window.luckysheet.exitEditMode()
    formRef.value.validate().then((valid) => {
      if (valid) {
        let params = {
          ...state.formData,
          collectTime: state.formData.collectTime
            ? moment(state.formData.collectTime).format('YYYY-MM-DD HH:mm:ss')
            : null,
          createTime: state.formData.createTime
            ? moment(state.formData.createTime).format('YYYY-MM-DD HH:mm:ss')
            : null,
          updateTime: state.formData.updateTime
            ? moment(state.formData.updateTime).format('YYYY-MM-DD HH:mm:ss')
            : null,
        }
        if (params.tagIds?.length) {
          params.tagIds = params.tagIds.map((item) => {
            return Number(item.split('_')?.[1])
          })
        }
        api.documentManage
          .updateTrait(params)
          .then((res) => {
            if (res.success) {
              proxy.$message.success('保存成功')
              goBack(true)
            }
          })
          .catch(() => {})
      }
    })
  }

  onMounted(async () => {
    state.formData.id = Number(router.currentRoute.value.query.id)

    const res = await getDetail()
    if (res.success) {
      state.formData = res.data
      if (res.data.tags) {
        state.activeTagList = res.data.tags //记录当前已经选中的标签

        state.formData.tagIds = res.data.tags.map((item) => {
          return 'tag_' + item.id
        })
      }
    }
    getTargetList()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .nancalui-form {
    height: calc(100vh - 282px);
    .nancalui-form__item--horizontal {
      margin-bottom: 16px;
    }
  }
</style>
