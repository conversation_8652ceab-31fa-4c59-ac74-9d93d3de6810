import { Graph } from '@antv/x6'
import '@antv/x6-vue-shape'
import Box from './box'

Graph.registerNode('my-box', {
  inherit: 'vue-shape',
  component: {
    template: `
          <Box/>`,
    components: {
      Box,
    },
  },
  ports: {
    groups: {
      left: {
        id: 'left',
        position: 'left',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#697A9A',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
      right: {
        id: 'right',
        position: 'right',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#697A9A',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
    },
    items: [
      {
        id: 'left',
        group: 'left',
      },
      {
        id: 'right',
        group: 'right',
      },
    ],
  },
})
