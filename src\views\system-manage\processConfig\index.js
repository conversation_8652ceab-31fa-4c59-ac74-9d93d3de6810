import { Graph, Dom, Shape } from '@antv/x6'
import '@antv/x6-vue-shape'
import dagre from 'dagre'
import insertCss from 'insert-css'
import Box from './box'
import api from '@/api/index'

// 定义样式
// 我们用 insert-css 演示引入自定义样式
// 推荐将样式添加到自己的样式文件中
// 若拷贝官方代码，别忘了 npm install insert-css
insertCss(`
  .x6-cell {
    cursor: default;
  }
  .x6-node .btn {
    cursor: pointer;
  }
`)

let graph = null

const processManageGetNodeCodeFn = async () => {
  const res = await api.system.processManageGetNodeCode({})
  if (res.success) {
    return res.data.nodeCode
  }
  return null
}

export default class setFlowGraph {
  static init(flowData) {
    // 自定义节点
    Graph.registerNode(
      'process-node',
      {
        inherit: 'vue-shape',
        width: 260,
        height: 88,
        component: {
          template: `<Box />`,
          components: {
            Box,
          },
        },
        ports: {
          groups: {
            top: {
              id: 'top',
              position: 'top',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#000000',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
            bottom: {
              id: 'bottom',
              position: 'bottom',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#000000',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
          },
          items: [
            { id: 'top', group: 'top' },
            { id: 'bottom', group: 'bottom' },
          ],
        },
      },
      true,
    )

    // 自定义边
    Graph.registerEdge(
      'process-edge',
      {
        zIndex: -1,
        attrs: {
          line: {
            stroke: '#000000',
            strokeWidth: 1,
            targetMarker: {
              name: 'classic',
              size: 8,
            },
          },
        },
        router: {
          name: 'manhattan',
        },
      },
      true,
    )

    // 布局方向
    const dir = 'TB' // LR RL TB BT

    // 创建画布
    graph = new Graph({
      container: document.getElementById('setFlowContainer'),
      interacting: false,
      grid: {
        size: 20,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#EBEDF0',
            thickness: 1,
          },
          {
            color: '#EBEDF0',
            thickness: 1,
            factor: 4,
          },
        ],
      },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      connecting: {
        allowNode: true,
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        highlight: true,
        snap: true,
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          if (!targetMagnet) {
            return false
          }
          return true
        },
      },
    })

    // 监听自定义事件
    function setup() {
      graph.on('node:click', ({ e, node }) => {
        if (e.target.nodeName === 'IMG') {
          e.stopPropagation()
          graph.freeze()

          // 添加节点
          if (e.target.className === 'add') {
            processManageGetNodeCodeFn().then((nodeCode) => {
              const newNode = createNode({
                assignee: null,
                assigneeName: null,
                name: null,
                nodeCode,
                // nodeCode: String(Math.random()),
                nodeType: 'TASK',
              })

              // 找到当前节点的所有出边（子节点）
              const outgoingEdges = graph.getOutgoingEdges(node)
              const childNodes = outgoingEdges
                ? outgoingEdges.map((edge) => edge.getTargetNode())
                : []

              // 如果当前节点有子节点，需要重新连接
              if (childNodes.length > 0) {
                // 删除原有的从父节点到子节点的边
                outgoingEdges.forEach((edge) => {
                  graph.removeCell(edge)
                })

                // 创建从父节点到新节点的边
                const parentToNewEdge = createEdge(node.id, newNode.id)

                // 创建从新节点到原子节点的边
                const newToChildEdges = childNodes.map((childNode) =>
                  createEdge(newNode.id, childNode.id),
                )

                // 添加新节点和所有边
                graph.addCell([newNode, parentToNewEdge, ...newToChildEdges])
              } else {
                // 如果没有子节点，直接连接
                graph.addCell([newNode, createEdge(node.id, newNode.id)])
              }

              layout()
            })
          }

          // 删除节点
          if (e.target.className === 'del') {
            e.stopPropagation()
            graph.freeze()

            // 获取当前节点的父节点和子节点
            const incomingEdges = graph.getIncomingEdges(node)
            const outgoingEdges = graph.getOutgoingEdges(node)

            const parentNodes = incomingEdges
              ? incomingEdges.map((edge) => edge.getSourceNode())
              : []
            const childNodes = outgoingEdges
              ? outgoingEdges.map((edge) => edge.getTargetNode())
              : []

            // 删除与当前节点相关的所有边
            const relatedEdges = [...(incomingEdges || []), ...(outgoingEdges || [])]
            relatedEdges.forEach((edge) => {
              graph.removeCell(edge)
            })

            // 删除当前节点
            graph.removeCell(node)

            // 重新连接父节点和子节点
            if (parentNodes.length > 0 && childNodes.length > 0) {
              const newEdges = []
              parentNodes.forEach((parentNode) => {
                childNodes.forEach((childNode) => {
                  newEdges.push(createEdge(parentNode.id, childNode.id))
                })
              })
              graph.addCell(newEdges)
            }

            layout()
          }
        }
      })
    }

    // 自动布局
    function layout() {
      const nodes = graph.getNodes()
      const edges = graph.getEdges()
      const g = new dagre.graphlib.Graph()
      g.setGraph({ rankdir: dir, nodesep: 50, ranksep: 100 })
      g.setDefaultEdgeLabel(() => ({}))

      const width = 260
      const height = 90
      nodes.forEach((node) => {
        g.setNode(node.id, { width, height })
      })

      edges.forEach((edge) => {
        const source = edge.getSource()
        const target = edge.getTarget()
        g.setEdge(source.cell, target.cell)
      })

      dagre.layout(g)

      graph.freeze()

      g.nodes().forEach((id) => {
        const node = graph.getCell(id)
        if (node) {
          const pos = g.node(id)
          node.position(pos.x, pos.y)
        }
      })

      edges.forEach((edge) => {
        const source = edge.getSourceNode()
        const target = edge.getTargetNode()
        const sourceBBox = source.getBBox()
        const targetBBox = target.getBBox()

        if ((dir === 'LR' || dir === 'RL') && sourceBBox.y !== targetBBox.y) {
          const gap =
            dir === 'LR'
              ? targetBBox.x - sourceBBox.x - sourceBBox.width
              : -sourceBBox.x + targetBBox.x + targetBBox.width
          const fix = dir === 'LR' ? sourceBBox.width : 0
          const x = sourceBBox.x + fix + gap / 2
          edge.setVertices([
            { x, y: sourceBBox.center.y },
            { x, y: targetBBox.center.y },
          ])
        } else if ((dir === 'TB' || dir === 'BT') && sourceBBox.x !== targetBBox.x) {
          const gap =
            dir === 'TB'
              ? targetBBox.y - sourceBBox.y - sourceBBox.height
              : -sourceBBox.y + targetBBox.y + targetBBox.height
          const fix = dir === 'TB' ? sourceBBox.height : 0
          const y = sourceBBox.y + fix + gap / 2
          edge.setVertices([
            { x: sourceBBox.center.x, y },
            { x: targetBBox.center.x, y },
          ])
        } else {
          edge.setVertices([])
        }
      })

      graph.unfreeze()
    }

    function createNode(data) {
      return graph.createNode({
        shape: 'process-node',
        id: data.nodeCode,
        data: { ...data, ...flowData },
        ports: {
          groups: {
            top: {
              id: 'top',
              position: 'top',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#000000',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
            bottom: {
              id: 'bottom',
              position: 'bottom',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#000000',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
          },
          items: [
            { id: 'top', group: 'top' },
            { id: 'bottom', group: 'bottom' },
          ],
        },
      })
    }

    function createEdge(source, target) {
      return graph.createEdge({
        shape: 'process-edge',
        source: { cell: String(source), port: 'bottom' },
        target: { cell: String(target), port: 'top' },
      })
    }


    const nodes = []
    flowData.nodes.forEach((item) => {
      nodes.push(createNode(item))
    })


    const edges = []
    flowData.edges.forEach((item) => {
      edges.push(createEdge(item.source, item.target))
    })

    graph.resetCells([...nodes, ...edges])
    layout()
    graph.zoomTo(0.8)
    graph.centerContent()
    setup()
    return graph
  }

  // 销毁
  static destroy() {
    graph.dispose()
  }
}
