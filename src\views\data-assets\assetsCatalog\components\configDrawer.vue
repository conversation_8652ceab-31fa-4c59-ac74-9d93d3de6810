<template>
  <el-drawer v-model="drawer" :direction="'rtl'">
    <template #header>
      <h4>set title by slot</h4>
    </template>
    <template #default>
      <div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">cancel</el-button>
        <el-button type="primary" @click="confirmClick">confirm</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { createAssetsClassify, getAssetsClassifyTree } from '@/api/sceneManage'
  import { checkCName } from '@/utils/validate'
  const drawer = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({})
  const cancel = () => {
    drawer.value = false
  }
  const createDir = () => {}
  // 还原表单
  const resetForm = (() => {
    // const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      // state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open() {
      resetForm()
      drawer.value = true
      _getAssetsClassifyTree()
    },
  })
</script>
<style></style>
<style lang="scss" scoped></style>
