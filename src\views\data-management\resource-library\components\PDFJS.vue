<template>
  <div class="pdf-content">
    <!-- 换页按钮 -->
    <div class="page-btn" id="page-btn">
      <div
        :class="{ 'page-btn-box': true, disabled: state.pagination.currentPage === 1 }"
        @click="prevPage"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.5 3L4.5 8L10.5 13"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div
        :class="{
          'page-btn-box': true,
          disabled: state.pagination.currentPage >= state.pagination.total,
        }"
        @click="nextPage"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            d="M5.5 13L11.5 8L5.5 3"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>

    <!-- 显示pdf的canvas -->
    <div class="pdf-canvas" v-loading="state.loading">
      <SignImg
        v-if="state.signBox"
        :total="state.pagination.total"
        :signInfo="state.signInfo"
        :historySign="props.historySign"
        ref="signImgRef"
        @delSign="state.signBox = false"
        @saveSign="saveSign"
      />
      <img v-if="isPage" :src="imgSrc" class="signImg" :style="dynamicStyles" alt="" />

      <div ref="pdfcom"></div>
      <watermark :watermarkInfo="props.watermarkInfo" />
    </div>
  </div>
  <div class="pagination-content" id="pagination-content">
    <n-pagination
      :total="state.pagination.total"
      v-model:pageSize="state.pagination.pageSize"
      v-model:pageIndex="state.pagination.currentPage"
      @page-index-change="pageUpdate"
    />
  </div>
</template>

<script setup>
  import * as pdf from 'pdfjs-dist'
  import pdfWorker from 'pdfjs-dist/legacy/build/pdf.worker.min.js'
  // import pdfWorker from 'pdfjs-dist/build/pdf.worker.js?url'
  import { TextLayerBuilder } from 'pdfjs-dist/web/pdf_viewer'
  import { getSignSet } from '@/api/dataManage'
  import SignImg from './SignImg'
  import watermark from './watermark'
  import 'pdfjs-dist/web/pdf_viewer.css'
  pdf.GlobalWorkerOptions.workerSrc = pdfWorker

  const route = useRoute()
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
    signInfo: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      required: true,
    },
    historySign: {
      type: Object,
      default() {
        return {}
      },
    },
    watermarkInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
  const imgSrc = ref('')
  const signImgRef = ref(null)
  const pdfcom = ref(null)
  const state = reactive({
    loading: false,
    signBox: false,
    signInfo: {},
    pageList: [],
    pagination: {
      currentPage: 1,
      pageSize: 1,
      total: 0,
    },
  })
  const dynamicStyles = ref({
    width: '0px',
    height: '0px',
    display: 'none',
  })

  const isPage = computed(() => {
    return state.pageList.includes(state.pagination.currentPage)
  })

  watch(
    () => props.signInfo,
    (newVal, prevValue) => {
      state.signBox = newVal.status
      state.signInfo = newVal
      nextTick(() => {
        signImgRef.value.initData('create', newVal.info)
      })
    },
    { deep: true },
  )
  watch(
    () => props.historySign,
    (newVal, prevValue) => {
      state.signBox = true
      nextTick(() => {
        signImgRef.value.initData('history', newVal)
      })
    },
    { deep: true },
  )

  // 嵌入签名
  const saveSign = (obj) => {
    const { w, h, x, y, src } = obj
    dynamicStyles.value = {
      width: w + 'px',
      height: h + 'px',
      left: x,
      top: y,
      display: 'block',
    }
    imgSrc.value = src
    state.signBox = false
  }

  const prevPage = () => {
    if (state.pagination.currentPage > 1) {
      state.pagination.currentPage = Math.max(state.pagination.currentPage - 1, 1)
      pageUpdate?.()
      // 上一页
    }
  }
  const nextPage = () => {
    if (state.pagination.currentPage < state.pagination.total) {
      state.pagination.currentPage = Math.min(
        state.pagination.currentPage + 1,
        state.pagination.total,
      )
      pageUpdate?.()
      // 下一页
    }
  }

  const loadPdf = async (contentDom, url, loadingDom) => {
    state.loading = true
    //得到请求的 pdf 文件
    const loadingTask = pdf.getDocument({
      url: url,
      // disableRange: true,
      rangeChunkSize: 65536 * 16,
    })
    //loading效果,下载pdf过程中展示loading
    loadingTask.onProgress = () => {}
    // 临时pdfDoc
    let provisionalPDFDoc
    loadingTask.promise.then((pdfDoc) => {
      //下载完成时，loading消失
      if (loadingDom) {
        loadingDom.style.display = 'none'
      }
      state.pagination.total = pdfDoc.numPages
      provisionalPDFDoc = pdfDoc
      getPDFPage(state.pagination.currentPage, pdfDoc)
      state.loading = false
    })

    const getPDFPage = async (num, pdfDoc) => {
      // 渲染pdf
      pdfDoc.getPage(num).then((page) => {
        const pageDiv = document.createElement('div')
        pageDiv.setAttribute('style', 'position: relative;')
        const canvas = document.createElement('canvas')
        canvas.setAttribute('id', `the-canvas${num}`)
        const ctx = canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const scaledViewport = page.getViewport({ scale: 3 })
        canvas.height = Math.floor(scaledViewport.height * dpr)
        canvas.width = Math.floor(scaledViewport.width * dpr)
        canvas.style.width = contentDom.offsetWidth + 'px'

        const transform = dpr !== 1 ? [dpr, 0, 0, dpr, 0, 0] : undefined

        const renderContext = {
          canvasContext: ctx,
          viewport: scaledViewport,
          transform: transform,
        }
        pageDiv.appendChild(canvas)
        contentDom.innerHTML = ''
        contentDom?.appendChild(pageDiv)
        page
          .render(renderContext)
          .promise.then(() => {
            return page.getTextContent()
          })
          .then((textContent) => {
            // 创建新的TextLayerBuilder实例
            const textLayer = new TextLayerBuilder({})
            textLayer.setTextContentSource(textContent)
            textLayer.render(scaledViewport)
            pageDiv.appendChild(textLayer.div)
          })
      })
    }
    return new Promise((resolve, reject) => {
      resolve(() => getPDFPage(state.pagination.currentPage, provisionalPDFDoc))
    })
  }

  let pageUpdate
  onMounted(() => {
    loadPdf(pdfcom.value, option.value.value).then((res) => {
      pageUpdate = res
    })

    getSignSet({ fileId: route.query.id })
      .then((res) => {
        if (res.data.waterFileOssUrl) {
          const signInfo = res.data

          imgSrc.value = signInfo.waterFileOssUrl
          state.pageList = res.data.pageList
          dynamicStyles.value = {
            width: (signInfo.imgWidth / 100) * 700 + 'px',
            height: (signInfo.imgHeight / 100) * 700 + 'px',
            display: 'block',
            left: (signInfo.x / 100) * 700 + 'px',
            top: (signInfo.y / 100) * 990 + 'px',
          }
        }
      })
      .catch((err) => {
        console.log(err)
      })
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .pdf-content {
    position: relative;
    display: flex;
    flex: 1 0 0;
    align-self: stretch;
    justify-content: center;
    height: calc(100% - 44px);
    padding: 8px;
    background: var(---, #f6f7fb);
    border-radius: 6px;
    user-select: text;

    .page-btn {
      width: 0;
      height: 0;
      &-box {
        position: absolute;
        top: 50%;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        color: $themeBlue;
        background-color: #fff;
        border-radius: 27px;
        box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
        transform: translateY(-50%);
        cursor: pointer;
        &.disabled {
          color: #b8b8b8;
          background-color: #e5e5e5;
          cursor: not-allowed;
          &:hover {
            color: #b8b8b8;
            background: #e5e5e5;
          }
        }
        &:hover {
          color: #fff;
          background: #6e9eff;
        }
        &:last-child {
          right: 16px;
        }
        &:first-child {
          left: 16px;
        }
      }
    }
    .pdf-canvas {
      position: relative;
      width: 1000px;
      overflow-x: hidden;
      overflow-y: auto;

      .signImg {
        position: absolute;
        z-index: 2;
      }
    }
  }
  .pagination-content {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: flex-end;
    padding: 16px 0 0 0;

    &-total {
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;

      span {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  :deep(.textLayer) {
    display: none;
  }
</style>
