import request from '@/utils/request'
import URL from '@/const/urlAddress'

// ---------------------------------------数据资产接口  ----------------------------------------------------
//质量报告统计数据
function getLatestData(data) {
  return request({
    url: `${URL.QUALITY}/global/quality/assessment/results/latest`,
    method: 'post',
    data: data,
  })
}

//数据资产-查询明细
function getAssetsList(data) {
  return request({
    url: `${URL.ASSETS}/assets/detail/search`,
    method: 'post',
    data: data,
  })
}

function getModalFields(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/strategy/model/fields/page`,
    method: 'post',
    data: data,
  })
}

function getAssetsTagList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/list`,
    method: 'get',
  })
}

// 数据资产-资产目录-查询
function getDetailList(data) {
  return request({
    url: `${URL.ASSETS}/assets/${data.projectCode}/${data.dataModelName}`,
    method: 'get',
    params: data,
  })
}

// 数据源资源详情
export const getDatasourceDetail = (data) => {
  return request({
    url: `${URL.AUTH}/datasource/collect-table/detail/${data.id}`,
    method: 'get',
  })
}

// 根据数据表追溯血缘关系
function assetsLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/all`,
    method: 'get',
    params: data,
  })
}
// 根据数据表正向追溯血缘关系
function assetsLineageForward(data) {
  let params = { dataModelId: data.dataModelId }
  if (data.label) {
    params.label = data.label
  }
  return request({
    url: `${URL.ASSETS}/lineage/forward`,
    method: 'get',
    params: params,
  })
}

// 通过场景code查询血缘关系
function getLineageByPro(params) {
  return request({
    url: `${URL.ASSETS}/lineage/view`,
    method: 'get',
    params,
  })
}

// 保存追溯血缘关系
function saveLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/save`,
    method: 'post',
    data: data,
  })
}

// 更新追溯血缘关系
function updateLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/relationship/update`,
    method: 'post',
    data: data,
  })
}
// 查询结构化
function getFile(data) {
  return request({
    url: `${URL.OPERATION}/operation/resource/info`,
    method: 'post',
    data: data,
  })
}

// 查询指标血缘关系
function indicatorLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/indicator`,
    method: 'get',
    params: data,
  })
}

// 查询模型表级血缘关系
function modelTableLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/table`,
    method: 'get',
    params: data,
  })
}

// 查询模型字段级血缘关系
function modelColumnLineage(data) {
  return request({
    url: `${URL.ASSETS}/lineage/column`,
    method: 'get',
    params: data,
  })
}

// ---------------------------------------资产库接口  ----------------------------------------------------

//统计数据
function getAssetsLibraryCount(data) {
  return request({
    url: `${URL.ASSETS}/assets/overview/count`,
    method: 'get',
    params: data,
  })
}
//总览直方图
function getBarChart() {
  return request({
    url: `${URL.ASSETS}/assets/overview/countModelByBizDomainId`,
    method: 'get',
  })
}
//总览增减时序图
function getLineChart(data) {
  return request({
    url: `${URL.ASSETS}/assets/overview/trend/${data.days}`,
    method: 'get',
  })
}

//元数据分页列表
function getAssetsLibraryMetadataList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/metadata/page/list`,
    method: 'post',
    data: data,
  })
}

//数据模型-分页列表
function getAssetsLibraryModelList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/model/page/list`,
    method: 'post',
    data: data,
  })
}
//全局搜索
function globalSearchList(data) {
  return request({
    url: `${URL.ASSETS}/global-search/page`,
    method: 'post',
    data: data,
  })
}

//资产注册
function registerAssets(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/register-assets`,
    method: 'post',
    data: data,
  })
}

// 资产注销
function registerCancellation(data) {
  return request({
    url: `${URL.ASSETS}/asset/cancellation`,
    method: 'post',
    data: data,
  })
}
//资产注销
function deregisterAssets(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/deregister-assets`,
    method: 'post',
    data: data,
  })
}

function getStatusList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/register-info/${data.id}`,
    method: 'get',
  })
}
//资产注销
function cancelAssets(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/cancelRegisterAssets`,
    method: 'post',
    data: data,
  })
}
//数据模型标签-分页列表
function getAssetsTagModelList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/tag-model/page/list`,
    method: 'post',
    data: data,
  })
}
//资产信息查询
function getTagModelList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/modelRecords`,
    method: 'post',
    data: data,
  })
}
//字段信息分页
function getColTagList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/metaRecords`,
    method: 'post',
    data: data,
  })
}

//标签-分页列表
function getAssetsLibraryTagList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/tag/page/list`,
    method: 'post',
    data: data,
  })
}

//服务数 - 分页列表
function getAssetsLibraryList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/api/page/list`,
    method: 'post',
    data: data,
  })
}

// 通过业务域查询模型列表
function getModelByBiz(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/model/${data.assetsType}/${data.bizId}`,
    method: 'get',
  })
}

// ---------------------------------------资源库接口  ----------------------------------------------------
//数据统计
function getResourceLibraryCount(data) {
  return request({
    url: `${URL.ASSETS}/resource/library/count`,
    method: 'get',
    params: data,
  })
}

//数据源 - 分页列表
function getResourceLibraryDatasourceList(data) {
  return request({
    url: `${URL.ASSETS}/resource/library/datasource/page`,
    method: 'post',
    data: data,
  })
}
//数据表 - 分页列表
function getResourceLibraryTableList(data) {
  return request({
    url: `${URL.ASSETS}/resource/library/table/page`,
    method: 'post',
    data: data,
  })
}
//全局数据表 - 分页列表
function getResourceLibraryGlobalTableList(data) {
  return request({
    url: `${URL.ASSETS}/resource/library/table/global/page`,
    method: 'post',
    data: data,
  })
}

// ---------------------------------------资产标签分类接口接口-----------------------------------------------
//获取标签树接口
function getTagsTree(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/group/tree/${data.type}`,
    method: 'get',
    params: data,
  })
}

//获取数据库/表树接口
function getTableTree(data) {
  return request({
    url: `${URL.ASSETS}/metadata/list`,
    method: 'get',
    params: data,
  })
}

//标签树分组删除
function deleteTagsTreeGroup(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/group/${data.id}`,
    method: 'get',
    params: data,
  })
}

//标签树分组创建
function createTagsTreeGroup(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/group/save`,
    method: 'post',
    data: data,
  })
}

//标签树分组更新
function updateTagsTreeGroup(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/group/update`,
    method: 'post',
    data: data,
  })
}
//标签树分组名称验重
function checkGroupName(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/group/checkGroupName`,
    method: 'post',
    data: data,
  })
}

// ---------------------------------------标签管理接口  ----------------------------------------------------
//标签管理统计
function getTagManagementCount(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/statistic`,
    method: 'get',
    params: data,
  })
}

//标签管理 - 分页列表
function getTagManagementList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/page`,
    method: 'post',
    data: data,
  })
}
//元数据目录 - 分页列表
function getMateDataList(data) {
  return request({
    url: `${URL.ASSETS}/metadata/table/data-preview`,
    method: 'post',
    data: data,
  })
}
//标签查看
function getTagDetail(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/${data.id}`,
    method: 'get',
  })
}
//标签打标信息
function getTagData(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/list-by-tag`,
    method: 'get',
    params: data,
  })
}
//标签数据预览-获取模型列表
function getTagModel(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/target-model/list`,
    method: 'get',
    params: data,
  })
}
//标签数据预览-列表
function getDataWithProject(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/tagged-record/list`,
    method: 'get',
    params: data,
  })
}
function getDataWithTagId(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/tagged-record/page`,
    method: 'get',
    params: data,
  })
}

//标签管理 - 标签创建
function createTagsWithGroupId(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/save`,
    method: 'post',
    data: data,
  })
}

//标签管理 - 标签更新
function updateTagsWithGroupId(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/update`,
    method: 'post',
    data: data,
  })
}
//标签管理 - 标签删除
function deleteTagsWithGroupId(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/delete`,
    method: 'get',
    params: data,
  })
}
//标签管理 - 标签名称验重
function checkTagName(data) {
  return request({
    url: `${URL.ASSETS}/assets/tag/checkTagName`,
    method: 'post',
    data: data,
  })
}

// ---------------------------------------资产打标任务接口  ----------------------------------------

//打标任务- 分页列表
function getTaggingList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/page`,
    method: 'post',
    data: data,
  })
}

// 打标资产保存
function addAssetsTags(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/assets/save`,
    method: 'post',
    data: data,
  })
}

// 批量保存打标
function addMoreAssetsTags(data) {
  return request({
    url: `${URL.ASSETS}/asset/tagging/assets/save`,
    method: 'post',
    data: data,
  })
}

//创建任务
function createTagging(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/save`,
    method: 'post',
    data: data,
  })
}

// 任务打标资产记录分页
function getTaggingHistoryList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/dataRecords`,
    method: 'post',
    data: data,
  })
}

//任务更新
function updateTagging(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/update`,
    method: 'post',
    data: data,
  })
}

//任务信息
function detailTagging(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/${data.id}`,
    method: 'get',
    params: data,
  })
}

//任务删除
function deleteTagging(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/delete/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 根据模型查询数据资产
function getAssetsDetail(data) {
  return request({
    url: `${URL.ASSETS}/assets/detail/tagging/search`,
    method: 'post',
    data: data,
  })
}

// 已打标资产的标签信息
function getRowAssetsTagsDetail(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/tagged`,
    method: 'get',
    params: data,
  })
}
//查询场景列表
function getProjectList(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/projects`,
    method: 'get',
    params: data,
  })
}
//查询场景层级列表
function getProjectLayers(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/layers/${data.projectCode}`,
    method: 'get',
    params: data,
  })
}
//查询场景层级模型列表
function getLayerModels(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/models/${data.projectCode}/${data.layerId}`,
    method: 'get',
    params: data,
  })
}

// 有条件的全量打标 -后端打标
function markTagsWithCondition(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/completeWithCondition`,
    method: 'post',
    data: data,
  })
}
//打标资产保存-批量
function markBatchSave(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/batchSave`,
    method: 'post',
    data: data,
  })
}
//打标资产保存-单个
function markSingleSave(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/assets/save`,
    method: 'post',
    data: data,
  })
}
//打标--批量打标数据过滤算符枚举
function getFilterOperator(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/batchTagging/filter/operator`,
    method: 'get',
    params: data,
  })
}
// 获取文件类型场景的文件列表-分页
function getThisFileTypeListSearch(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/search`,
    method: 'post',
    data: data,
  })
}
// 获取文件类型场景的全局文件列表-分页
function getThisFileTypeListGlobalSearch(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/global/search`,
    method: 'post',
    data: data,
  })
}
// 全局储存分组列表
function getGlobalTreeSearch(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/global/list`,
    method: 'get',
    params: data,
  })
}
// 获取文件类型场景的文件列表-分页
function fileDeleteBatch(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/batch/delete`,
    method: 'post',
    data: data,
  })
}
// 获取文件类型详情
function getFilDetail(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/get`,
    method: 'get',
    params: data,
  })
}
// 首页 --资源增幅
function getHomeRi(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/ri`,
    method: 'post',
    data: data,
  })
}
// 首页 --资源监控
function getHomeRm(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/rm`,
    method: 'post',
    data: data,
  })
}
// 首页 --资源占比
function getHomeRp(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/rp`,
    method: 'post',
    data: data,
  })
}
// 首页 --数据数据质量
function getHomeQi(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/qi`,
    method: 'post',
    data: data,
  })
}

// 首页 --数据数据质量
function getHomeAs(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/as`,
    method: 'post',
    data: data,
  })
}

// 首页 --数据治理统计
function getHomeDs(data) {
  return request({
    url: `${URL.ASSETS}/home/<USER>/ds`,
    method: 'post',
    data: data,
  })
}

// 数据打标-查看打标资产信息
function getModeRecords(data) {
  return request({
    url: `${URL.ASSETS}/assets/tagging/modelRecords`,
    method: 'post',
    data: data,
  })
}

// 通过业务域查询模型列表-不带分页
function getModeForBizId(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/model/${data.bizId}`,
    method: 'get',
    params: data,
  })
}
//指标资产
// 获取指标资产列表
function indicatorModelList(data) {
  return request({
    url: `${URL.ASSETS}/assets/library/indicator-model/page/list`,
    method: 'post',
    data,
  })
}

// 元数据地图
// 元数据地图查询全景
function metadataMapList(data) {
  return request({
    url: `${URL.ASSETS}/metadata/map/data`,
    method: 'post',
    data,
  })
}
// 元数据地图查询
function metadataMapSearch(data) {
  return request({
    url: `${URL.ASSETS}/metadata/map/search`,
    method: 'post',
    data,
  })
}
// 元数据查询
function metadataMapDetail(data) {
  return request({
    url: `${URL.ASSETS}/metadata/detail`,
    method: 'get',
    params: data,
  })
}
// ---------------------------------------数据安全接口  ----------------------------------------
// 密级管理-表信息-列表
function getAssetsTables(data) {
  return request({
    url: `${URL.ASSETS}/assets/manager/table/page/list`,
    method: 'post',
    data,
  })
}
// 密级管理-字段信息-列表
function getAssetsTableFields(data) {
  return request({
    url: `${URL.ASSETS}/assets/manager/field/page/list`,
    method: 'post',
    data,
  })
}
// 密级管理-字段密级保存
function assetsTableFieldsSave(data) {
  return request({
    url: `${URL.ASSETS}/assets/manager/field/save`,
    method: 'post',
    data,
  })
}

// 密级管理-字段密级保存新
function assetsTableSave(data) {
  return request({
    url: `${URL.ASSETS}/assets/manager/table/save`,
    method: 'post',
    data,
  })
}
// ---------------------------------------脱敏算法接口  ----------------------------------------
// 脱敏算法-新增
function desensitizationAlgoAdd(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/add`,
    method: 'post',
    data,
  })
}
// 脱敏算法-分页列表
function getDesensitizationAlgoList(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/list`,
    method: 'post',
    data,
  })
}
// 脱敏算法-测试
function desensitizationAlgoTest(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/test`,
    method: 'post',
    data,
  })
}
// 脱敏算法-编辑保存
function desensitizationAlgoUpdate(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/update`,
    method: 'post',
    data,
  })
}
// 脱敏算法-获取算法信息
function getDesensitizationAlgoMessage(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 脱敏算法-名称验重
function desensitizationAlgoTestName(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/name-is-duplicate`,
    method: 'post',
    data,
  })
}
// 脱敏算法-名称验重
function algoConfigureUpdate(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/configure`,
    method: 'post',
    data,
  })
}
// 脱敏算法-删除
function deletDesensitizationAlgo(data) {
  return request({
    url: `${URL.ASSETS}/assets/desensitization/algo/delete/${data.id}`,
    method: 'post',
    data,
  })
}

// 元数据统计
function metadataStatistics(data) {
  return request({
    url: `${URL.BASE}/metadata/statistics/load`,
    method: 'post',
    data,
  })
}

// 资产注册申请
export const assetsApplyForRegitster = (data) => {
  return request({
    url: `${URL.ASSETS}/asset/register`,
    method: 'post',
    data,
  })
}

// 资产注册申请信息
export const assetsApplyForRegitsterInfo = () => {
  return request({
    url: `${URL.ASSETS}/asset/register/application`,
    method: 'get',
  })
}

// 模型注册资产分页列表
export const assetsRegisterList = (data) => {
  return request({
    url: `${URL.MODEL}/model/reg/asset/list`,
    method: 'post',
    data,
  })
}

// 资产分类列表树
export const assetsRegisterType = (data) => {
  return request({
    url: `${URL.ASSETS}/asset/classification/tree-list`,
    method: 'post',
    data,
  })
}

// 可注册为资产的数据分类
export const assetsMenuTree = (data) => {
  return request({
    url: `${URL.BASE}/external-data/registrable/category/tree`,
    method: 'get',
  })
}

// 可注册的资产分页列表
export const assetsMenuList = (data) => {
  return request({
    url: `${URL.BASE}/external-data/registrable/list`,
    method: 'post',
    data,
  })
}

// 获取成飞数据目录
export const dorisCategory = (data) => {
  return request({
    url: `${URL.ASSETS}/properties/config/dorisCategory`,
    method: 'get',
  })
}

// 资产最新质量检查详情
export const assetQualityDetail = (data) => {
  return request({
    url: `${URL.ASSETS}/asset/quality/${data.id}`,
    method: 'get',
  })
}

// 可注册的数据源资源数据预览
export const assetsMenuPreview = (data) => {
  return request({
    url: `${URL.BASE}/external-data/registrable-table/data-preview`,
    method: 'post',
    data,
  })
}

// 可注册的数据源资源详情
export const assetsMenuDetail = (id) => {
  return request({
    url: `${URL.BASE}/external-data/registrable-table/detail/${id}`,
    method: 'get',
  })
}

// 表字段信息
export const assetsFormInfo = (params) => {
  return request({
    url: `${URL.BASE}/assets/table/column/list`,
    method: 'get',
    params,
  })
}

// 表字段信息修改
export const assetsFormInfoUpdate = (data) => {
  return request({
    url: `${URL.BASE}/assets/table/column/edit`,
    method: 'post',
    data,
  })
}

export default {
  getFile,
  indicatorLineage,
  modelTableLineage,
  modelColumnLineage,
  getLatestData,
  assetsLineage,
  assetsLineageForward,
  getLineageByPro,
  saveLineage,
  updateLineage,
  getDetailList,
  getAssetsTagList,
  getAssetsList,
  getAssetsLibraryCount,
  getBarChart,
  getLineChart,
  getAssetsLibraryMetadataList,
  getAssetsLibraryModelList,
  globalSearchList,
  registerAssets,
  registerCancellation,
  deregisterAssets,
  getStatusList,
  cancelAssets,
  getTagModelList,
  getColTagList,
  getAssetsTagModelList,
  getAssetsLibraryTagList,
  getAssetsLibraryList,
  getModelByBiz,
  getResourceLibraryCount,
  getResourceLibraryDatasourceList,
  getTagsTree,
  getTableTree,
  deleteTagsTreeGroup,
  createTagsTreeGroup,
  updateTagsTreeGroup,
  checkGroupName,
  getTagManagementCount,
  getTagManagementList,
  getMateDataList,
  getTagDetail,
  getTagData,
  getTagModel,
  getDataWithProject,
  getDataWithTagId,
  createTagsWithGroupId,
  getTaggingHistoryList,
  updateTagsWithGroupId,
  getTaggingList,
  addAssetsTags,
  addMoreAssetsTags,
  createTagging,
  updateTagging,
  detailTagging,
  deleteTagging,
  getAssetsDetail,
  getRowAssetsTagsDetail,
  getProjectList,
  getProjectLayers,
  getLayerModels,
  markBatchSave,
  markSingleSave,
  getFilterOperator,
  getResourceLibraryTableList,
  getResourceLibraryGlobalTableList,
  getThisFileTypeListSearch,
  getThisFileTypeListGlobalSearch,
  getGlobalTreeSearch,
  fileDeleteBatch,
  getFilDetail,
  getHomeRi,
  getHomeRm,
  getHomeRp,
  getHomeQi,
  getHomeAs,
  getHomeDs,
  deleteTagsWithGroupId,
  checkTagName,
  getModeRecords,
  getModeForBizId,
  markTagsWithCondition,
  indicatorModelList,
  metadataMapList,
  metadataMapSearch,
  metadataMapDetail,
  getAssetsTables,
  getAssetsTableFields,
  assetsTableFieldsSave,
  assetsTableSave,
  desensitizationAlgoAdd,
  getDesensitizationAlgoList,
  desensitizationAlgoTest,
  desensitizationAlgoUpdate,
  getDesensitizationAlgoMessage,
  getModalFields,
  desensitizationAlgoTestName,
  algoConfigureUpdate,
  deletDesensitizationAlgo,
  metadataStatistics,
}
