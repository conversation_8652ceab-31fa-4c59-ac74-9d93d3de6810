import request from '@/utils/request'
// import { ASSETS, MODEL } from '@/const/urlAddress'
const ASSETS = '/api/govern-data-assets'
const MODEL = '/api/govern-data-model'
const PROJECT = '/api/govern-management'

// 业务域查询
export const searchTreeList = (params: any) =>
  request({
    url: `${ASSETS}/biz/scenario/tree`,
    method: 'get',
    params,
  })
// 业务域下拉查询
export const searchSelectList = (params: any) =>
  request({
    url: `${ASSETS}/biz/scenario/domain/search`,
    method: 'get',
    params,
  })
// 业务域查询详情
export const searchDetail = (params: any) =>
  request({
    url: `${ASSETS}/biz/scenario/domain/details`,
    method: 'get',
    params,
  })
// 场景域查询
export const searchSceneList = (params: any) =>
  request({
    url: `${PROJECT}/project/all/list`,
    method: 'get',
    params,
  })
//分页查询
export const searchList = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/domain/page/search`,
    method: 'post',
    data,
  })

// 业务域新增/修改
export const treeUpdate = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/domain/save`,
    method: 'post',
    data,
  })

// 业务域删除
export const treeDelete = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/domain/delete/${data.id}`,
    method: 'post',
    data,
  })

// 物理模型-查询
export const searchPhysicalList = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/model/search`,
    method: 'post',
    data,
  })

// 物理模型-新增/编辑
export const updatePhysicalModel = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/model/save`,
    method: 'post',
    data,
  })

// 物理表-详情
export const searchPhysicalDetail = (params: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/table/get/${params.id}`,
    method: 'get',
    params,
  })

// 物理表-查询
export const searchPhysicalTableList = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/table/search`,
    method: 'post',
    data,
  })

// 物理表-新增/编辑
export const updatePhysicalTable = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/table/save`,
    method: 'post',
    data,
  })

// 物理表-删除
export const deletePhysicalTable = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/table/delete/${data.id}`,
    method: 'post',
    data,
  })

// 物理表-数据明细
export const physicalTableDataDetail = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/physical/table/view`,
    method: 'post',
    data,
  })

// 场景维护-删除
export const sceneMaintainDelete = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/scenario/maintenance/delete/${data.id}`,
    method: 'post',
    data,
  })

// 场景维护-查看
export const sceneMaintainDetail = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/scenario/maintenance/get/${data.id}`,
    method: 'post',
    data,
  })

// 场景维护-新增/修改
export const updateSceneMaintain = (data: any) =>
  request({
    url: `${ASSETS}/biz/scenario/scenario/maintenance/save`,
    method: 'post',
    data,
  })

// 场景维护-树
export const getTreeSceneMaintain = () =>
  request({
    url: `${MODEL}/layer/tree`,
    method: 'get',
  })

// 场景维护-获取元数据
export const getMetaData = (data: any) =>
  request({
    url: `${MODEL}/metadata/search/${data.modeId}`,
    method: 'get',
  })

// 脱敏策略列表
export const strategyList = (data: any) =>
  request({
    url: `${ASSETS}/assets/desensitization/strategy/list`,
    method: 'post',
    data,
  })

// 脱敏策略新增
export const strategyAdd = (data: any) =>
  request({
    url: `${ASSETS}/assets/desensitization/strategy/add`,
    method: 'post',
    data,
  })

// 脱敏策略更新
export const strategyUpdate = (data: any) =>
  request({
    url: `${ASSETS}/assets/desensitization/strategy/update`,
    method: 'post',
    data,
  })

// 脱敏策略删除
export const strategyDelete = (data: any) =>
  request({
    url: `${ASSETS}/assets/desensitization/strategy/delete`,
    method: 'post',
    data,
  })

// 脱敏策略详情
export const strategyDetail = (params: any) =>
  request({
    url: `${ASSETS}/assets/desensitization/strategy/detail`,
    method: 'get',
    params,
  })

// 资产分类列表
export const getAssetsClassify = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/list`,
    method: 'post',
    data,
  })

// 创建资产分类
export const createAssetsClassify = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/create`,
    method: 'post',
    data,
  })

// 资产分类列表树
export const getAssetsClassifyTree = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/tree-list`,
    method: 'POST',
    data,
  })

// 资产分类编码获取
export const getAssetsClassifyCode = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/code/gen`,
    method: 'POST',
    data,
  })

// 资产子级分类
export const getAssetsClassifyChildren = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/children/${data.code}`,
    method: 'POST',
  })

// 删除资产分类
export const deleteAssetsClassify = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/delete/${data.id}`,
    method: 'POST',
  })

// 修改资产分类
export const updateAssetsClassify = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/update`,
    method: 'POST',
    data,
  })

// 资产分类权限角色列表
export const getAssetsClassifyRoleList = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/authorization/list`,
    method: 'POST',
    data,
  })

// 添加资产分类授权角色
export const addAssetsClassifyRole = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/authorization/to-role`,
    method: 'POST',
    data,
  })
// 删除资产分类授权角色
export const deleteAssetsClassifyRole = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/authorization/delete/${data.id}`,
    method: 'POST',
  })

// 资产列表
export const getAssetsList = (data: any) =>
  request({
    url: `${ASSETS}/asset/list`,
    method: 'POST',
    data,
  })

// 资产来源列表
export const getAssetsSourceList = (data: any) =>
  request({
    url: `${ASSETS}/asset/source/all-list`,
    method: 'POST',
    data,
  })
// 资产类型列表
export const getAssetsTypeList = (data: any = 'enumName=AssetType') =>
  request({
    url: `${ASSETS}/asset/enum-value`,
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
  })

// 资产分类列表树
export const getAssetsClassifyTreeList = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/tree-list`,
    method: 'POST',
    data,
  })

// 资产表数据查询
export const getAssetsTableData = (data: any) =>
  request({
    url: `${ASSETS}/asset/table-data-query`,
    method: 'POST',
    data,
  })

// 资产分类权限角色全部列表
export const getAssetsClassifyRoleAllList = (data: any) =>
  request({
    url: `${ASSETS}/asset/classification/authorization/all-list`,
    method: 'POST',
    data,
  })
// 资产详情
export const getAssetsDetail = (data: any) =>
  request({
    url: `${ASSETS}/asset/${data.id}`,
    method: 'GET',
  })
// 可注册的数据源资源详情
export const getAssetsSourceDetail = (data: any) =>
  request({
    url: `${PROJECT}/external-data/registrable-table/detail/${data.id}`,
    method: 'GET',
  })
