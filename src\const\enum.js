const SHORTCUTS = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const checkName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入采集任务名称！'))
  }
  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-]){2,20}$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 21) {
    callback()
  } else {
    callback(new Error('支持汉字、英文、数字、中划线、下划线，2~20个字符'))
  }
}

export default {
  SHORTCUTS,
  checkName,
}
