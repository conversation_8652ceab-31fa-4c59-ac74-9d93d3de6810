import request from '@/utils/request'
import URL from '@/const/urlAddress'

// -----------------------------审核中心--------------------------------------

/*
获取数据表
 */
export function getAuditList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/api/search`,
    method: 'post',
    data: data,
  })
}
/*
获取资产数据表
 */
export function getAuditAssetsList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/assets/search`,
    method: 'post',
    data: data,
  })
}
/*
获取指标体系数据表
 */
export function getAuditTargetsysList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/index_model/search`,
    method: 'post',
    data: data,
  })
}
/*
获取指标体系数据表
 */
export function getAuditAttriList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/indexAtrr/search`,
    method: 'post',
    data: data,
  })
}
/*
获取指标数据表
 */
export function getAuditTargetList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/index/search`,
    method: 'post',
    data: data,
  })
}
/*
获取算法数据表
 */
export function getAuditAlgorithmList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/algorithm/search`,
    method: 'post',
    data: data,
  })
}
/*
获取作业数据表
 */
export function getAuditJobList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/job/search`,
    method: 'post',
    data: data,
  })
}
/*
获取审核详情
*/
export function getAuditDetail(data) {
  return request({
    url: `${URL.AUDIT}/audit/detail/${data.id}`,
    method: 'get',
  })
}
/*
获取审批意见
 */
export function getAuditDetailByType(data) {
  return request({
    url: `${URL.AUDIT}/audit/${data.handleType}/${data.id}`,
    method: 'get',
  })
}

/*
通过/拒绝
 */
export function approvalStatus(data) {
  return request({
    url: `${URL.AUDIT}/audit/commit`,
    method: 'post',
    data: data,
  })
}
// 撤回审核申请
export function cancelAudit(data) {
  return request({
    url: `${URL.AUDIT}/feign/audit/cancel/`,
    method: 'post',
    data: data,
  })
}
//----------标准审核-------
/*
获取标准数据表
 */
export function getAuditStandardList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/standard/search`,
    method: 'post',
    data: data,
  })
}

// 业务流审批列表
export function getAuditWorkFlowList(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/job/search`,
    method: 'post',
    data: data,
  })
}

// 业务流审批接口
export function workFlowApproval(data = {}) {
  return request({
    url: `${URL.AUDIT}/audit/commit`,
    method: 'post',
    data: data,
  })
}

// 资产审核数据分页
export const auditRegisterList = (data) => {
  return request({
    url: `${URL.AUDIT}/audit/assets/search`,
    method: 'post',
    data,
  })
}

// 业务流程任务指定版本
export const workFlowVersion = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/version`,
    method: 'get',
    params: data,
  })
}

// 标签审核数据分页
export const tagAuditList = (data) => {
  return request({
    url: `${URL.AUDIT}/audit/document/tag/search`,
    method: 'post',
    data,
  })
}

// 标签审核数据分页
export const tagAuditCommit = (data) => {
  return request({
    url: `${URL.AUDIT}/audit/commit`,
    method: 'post',
    data,
  })
}

// 文档标注审批分页
export const documentMarkAuditList = (data) => {
  return request({
    url: `${URL.AUDIT}/audit/document/mark/search`,
    method: 'post',
    data,
  }) 
}

export default {
  getAuditList,
  getAuditDetailByType,
  getAuditJobList,
  getAuditAssetsList,
  getAuditTargetsysList,
  getAuditAttriList,
  getAuditTargetList,
  getAuditAlgorithmList,
  getAuditDetail,
  approvalStatus,
  cancelAudit,
  getAuditStandardList,
  tagAuditList,
  tagAuditCommit,
  documentMarkAuditList,
}
