<template>
  <div :class="['operatingLog', state.isLzos ? 'isLzos' : '']">
    <div class="quality">
      <div class="task">
        <div class="title">质量任务</div>
        <div v-loading="state.taskLoading" class="box scroll-bar-style">
          <div
            v-for="(item, index) in state.taskList"
            :key="index"
            :class="item.id === state.checkTaskId ? 'list checked' : 'list'"
            @click.prevent.stop="checkTaskFn(item)"
          >
            <div class="top">
              <SvgIcon
                :icon="
                  item.status === 'ONLINE'
                    ? 'quality-task-list-item-icon-on'
                    : 'quality-task-list-item-icon-off'
                "
                class="pic"
              />
              <div class="info">
                <div class="name">
                  <div class="text">{{ item.name }}</div>
                </div>
                <div class="desc"
                  >监控对象 {{ item.tableName }}｜创建人 {{ item.createByName }}</div
                >
              </div>
            </div>
            <div class="bottom"
              >调度方式：
              <span v-if="item.scheduleMode === 'MANUAL_OPERATION'">手动调度</span>
              <span v-else-if="item.scheduleMode === 'AUTO'">自动调度</span>
              <span v-else-if="item.scheduleMode === 'ASSOCIATED_SCHEDUL'">关联调度</span>
            </div>
            <div class="bottom">规则数量：{{ item.ruleNum }}/{{ item.ruleTotalNum }}</div>
            <div v-if="item.description" class="bottom">{{ item.description }}</div>
            <!-- 创建时间 -->
            <div class="time">
              <div v-if="item.status === 'ONLINE'" class="time-status green">已发布</div>
              <div v-else class="time-status">已下架</div>
              <div>{{ item.createTime }}</div></div
            >
          </div>
          <div v-if="state.taskList.length === 0 && state.hasLoad" class="empty">
            <img class="pic" src="@/assets/table-no-content-small.png" />
            <div class="empty-word">暂无任务</div>
          </div>
        </div>
      </div>
      <div class="rule">
        <div class="title">质量规则</div>
        <div v-loading="state.ruleLoading" class="box scroll-bar-style">
          <div
            v-for="(item, index) in state.ruleList"
            :key="index"
            :class="item.ruleId === state.checkRuleId ? 'list checked' : 'list'"
            @click.prevent.stop="checkRuleFn(item)"
          >
            <div class="top">
              <img class="pic" src="~@img/quality/rule.png" />
              <div class="info">
                <div class="name">
                  <div class="text">{{ item.fullName }}</div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="bottom-label">
                <span class="bottom-label-name">校验状态：</span>
                <div v-if="item.taskResultStatus === 'NORMAL'" class="status green">正常</div>
                <div v-else-if="item.taskResultStatus === 'ABNORMAL'" class="status red">告警</div>
                <div v-else-if="item.taskResultStatus === 'FAIL'" class="status gray">失败</div>
                <div v-else-if="item.taskResultStatus === 'RUNNING'" class="status">校验中</div>
              </div>
              <div class="bottom-label"
                ><span class="bottom-label-name">类型：</span>
                {{ item.ruleType === 'FIELD' ? '字段级' : '表级' }}
              </div>
              <div class="bottom-label"
                ><span class="bottom-label-name">规则模版：</span>
                {{ item.templateName || '--' }}
              </div>
              <div class="bottom-label"
                ><span class="bottom-label-name">监控阀值：</span>{{ item.ruleThresholdListText }}
              </div>
            </div>
          </div>
          <div v-if="state.ruleList.length === 0 && state.hasLoad" class="empty">
            <img class="pic" src="@/assets/table-no-content-small.png" />
            <div class="empty-word">暂无规则</div>
          </div>
        </div>
      </div>
    </div>
    <div :class="['log', state.isLzos ? 'isLzos' : '']">
      <div class="title">运行记录</div>
      <div v-loading="state.recordLoading" class="table">
        <CfTable
          :isDisplayAction="true"
          :table-head-titles="state.tableHeadTitles"
          :pagination="state.pagination"
          :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
          }"
          actionWidth="200"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              onSearch()
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              onSearch()
            },
          }"
          @tablePageChange="tablePageChange"
        >
          <template #status="{ row }">
            <div
              :class="{
                'template-name-box': true,
                gray: row.taskResultStatus === 'FAIL',
                blue: row.taskResultStatus === 'RUNNING',
                red: row.taskResultStatus === 'ABNORMAL',
              }"
            >
              <span v-if="row.taskResultStatus === 'NORMAL'">正常</span>
              <span v-else-if="row.taskResultStatus === 'FAIL'">失败</span>
              <span v-else-if="row.taskResultStatus === 'RUNNING'">校验中</span>
              <span v-else-if="row.taskResultStatus === 'ABNORMAL'">告警</span>
            </div>
          </template>
          <template #relation="{ row }">
            <div class="status">
              <div
                :class="{
                  circle: true,
                  gray: row.resultNotifyStatus !== 'TRIGGERED',
                }"
              ></div>
              <span>{{ row.resultNotifyStatus === 'TRIGGERED' ? '已触发' : '未触发' }}</span>
            </div>
          </template>
          <template #inspectionResult="{ row }">
            <span :title="row.inspectionResult">{{ row.inspectionResult }}</span>
          </template>
          <template #editor="{ row , index }">
            <div class="edit-box">
              <n-button
                v-if="
                state.pagination.currentPage && index < 4 && row.runningStatus === 'SUCCESS' && state.buttonAuthList.includes('governanceManage_qualityManage_operatingResult_view')
                "
                class="seeDetails has-right-border"
                variant="text"
                @click.prevent="seeResultFn(row)"
              >
                查看结果
              </n-button>
              <n-button
                v-if="
                  state.buttonAuthList.includes('governanceManage_qualityManage_operatingLog_view')
                "
                class="seeDetails has-right-border"
                variant="text"
                @click.prevent="seeLogFn(row)"
              >
                查看日志
              </n-button>
              <n-button
                v-if="
                  state.buttonAuthList.includes(
                    'governanceManage_qualityManage_operatingLog_refresh_edit',
                  )
                "
                class="seeDetails has-right-border"
                variant="text"
                @click.prevent="refreshFn(row)"
              >
                刷新
              </n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
    <n-modal
      title="查看日志"
      class="log-dialog middleDialog has-top-padding"
      v-model="state.dialogVisible"
      width="800px"
      :close-on-click-overlay="false"
    >
      <div class="out-box">
        <div class="words scroll-bar-style"
          >{{ state.dialogLogContent }}
          <div v-if="!state.dialogLogContent" class="empty">
            <img class="pic" src="@/assets/data-empty.png" />
            <div class="empty-word">暂无日志</div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <span class="dialog-footer">
            <n-button variant="solid" @click.prevent="state.dialogVisible = false">确定</n-button>
          </span>
        </div>
      </template>
    </n-modal>
    <n-modal
      title="查看结果"
      class="log-dialog middleDialog has-top-padding"
      v-model="state.seeResultDialogVisible"
      width="800px"
      :close-on-click-overlay="false"
    >
        <div class="result-box">
          <div class="title">
            <el-form-item label="规则名称：">
              <div :title="state.fullName || '--'">
                {{ state.fullName || '--' }}
              </div>
            </el-form-item>
            <el-form-item :label="state.inspectionResult?.split('：')?.[0] +'：'">
              {{ state.inspectionResult?.split('：')?.[1] || '--' }}
            </el-form-item>
          </div>
          <div class="table">
            <div class="table-head">
              <div class="item">
            问题数据详情  
              </div>
              <div class="item">
                <n-button
                  v-loading="state.downLoading"
                  :disabled="0"
                  color="primary"
                  @click.prevent.stop="downFn"
                  >导出</n-button
                >
              </div>
            </div>
            <div class="table-body">
              <CfTable
                actionWidth="180"
                ref="tableNoRef"
                :tableConfig="{
                  data: state.dataSource,
                  rowKey: 'id',
                }"
                :table-head-titles="state.tableHead"
                :paginationConfig="{
                  total: state.page.total,
                  pageSize: state.page.pageSize,
                  currentPage: state.page.pageNum,
                  onCurrentChange: (v) => {
                    state.page.pageNum = v
                    dialogSearch(false)
                  },
                  onSizeChange: (v) => {
                    state.page.pageSize = v
                    dialogSearch()
                  },
                }"
              >
              </CfTable>
            </div>
          </div>
        </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <span class="dialog-footer">
            <n-button variant="solid" @click.prevent="state.seeResultDialogVisible = false">确定</n-button>
          </span>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import { onMounted, reactive, toRefs } from 'vue'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  const store = useStore()
  const router = useRouter()
  import api from '@/api/index'

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    buttonAuthList: [],
    hasLoad: false,
    seeResultDialogVisible: false,
    dialogVisible: false,
    dialogLogContent: '',
    page: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
    },
    tableHeadTitles: [
      { prop: 'startTime', name: '任务触发时间' },
      { prop: 'endTime', name: '任务结束时间' },
      { prop: 'execTime', name: '运行时长' },
      { prop: 'taskResultStatus', name: '校验状态', slot: 'status' },
      { prop: 'resultNotifyStatus', name: '通知状态', slot: 'relation' },
      { prop: 'inspectionResult', name: '校验结果', width: 380, slot: 'inspectionResult' },
      { prop: 'score', name: '规则评分(百分制)', width: 180 },
    ],
    tableData: { list: [] },
    tableHeight: 436,
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    taskLoading: false,
    ruleLoading: false,
    recordLoading: false,
    checkTaskId: null,
    taskList: [],
    checkRuleId: null,
    ruleList: [],
  })
  // 表格操作变化
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    onSearch()
  }
  // 获取质量任务
  const getTaskList = () => {
    state.taskLoading = true
    state.ruleLoading = true
    state.recordLoading = true
    api.dataQuality
      .qualityTaskRecord()
      .then((res) => {
        state.taskLoading = false
        if (res.code === 'SUCCESS') {
          state.taskList = res.data
          if (state.taskList.length > 0) {
            if (state.checkTaskId) {
              getRuleList()
            } else {
              checkTaskFn(state.taskList[0])
            }
          } else {
            state.ruleLoading = false
            state.recordLoading = false
            state.hasLoad = true
          }
        }
      })
      .catch(() => {
        state.taskLoading = false
      })
  }

  // 选中任务
  const checkTaskFn = (item) => {
    if (item.id !== state.checkTaskId) {
      state.checkTaskId = item.id
      getRuleList()
    }
  }

  // 质量规则列表
  const getRuleList = () => {
    state.ruleLoading = true
    state.recordLoading = true
    api.dataQuality
      .qualityRuleRecord({ id: state.checkTaskId })
      .then((res) => {
        state.ruleLoading = false
        if (res.code === 'SUCCESS') {
          res.data.forEach((val) => {
            val.ruleThresholdListText = ruleThresholdListFn(val)
          })
          state.ruleList = res.data
          state.checkRuleId = null
          if (state.ruleList.length > 0) {
            state.checkRuleId = state.ruleList[0].ruleId
            onSearch(true)
          } else {
            state.recordLoading = false
            state.hasLoad = true
          }
        }
      })
      .catch(() => {
        state.ruleLoading = false
      })
  }

  // 转化监控阈值
  const ruleThresholdListFn = (item) => {
    let str = ''
    if (item.ruleThresholdList) {
      item.ruleThresholdList.forEach((val) => {
        str +=
          val.type === 'SUCCESS'
            ? '正常阈值' + val.operation + val.operationValue + '；'
            : '告警阈值' + val.operation + val.operationValue
      })
    }
    return str
  }

  // 选中规则
  const checkRuleFn = (item) => {
    if (item.ruleId !== state.checkRuleId) {
      state.checkRuleId = item.ruleId
      onSearch(true)
    }
  }
  // 获取运行记录
  const onSearch = (init) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    let data = {
      condition: {
        ruleId: state.checkRuleId,
        taskId: state.checkTaskId,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    state.recordLoading = true
    api.dataQuality
      .qualityTaskRecordDetail(data)
      .then((res) => {
        state.recordLoading = false
        state.hasLoad = true
        if (res.code === 'SUCCESS') {
          res.data.list.forEach((val) => {
            if (val.score) {
              val.score = val.score * 100
            }
          })
          state.tableData = res.data
          state.pagination.total = res.data.total
        }
      })
      .catch(() => {
        state.recordLoading = false
      })
  }

  let dialogSearch
  // 质量任务-问题数据明细分页查询
  const getQualityTaskRecordDetail = ({id ,pageNum ,pageSize}) => {
    const params = {
      condition: {
        id,
      },
      pageNum,
      pageSize,
    }
    api.dataQuality.getQualityTaskRecordDetail(params).then((res) => {
      if (res.success) {
        state.tableHead = res.data?.fieldTitles?.map(_=>( { prop: _, name: _ })) || []
        state.dataSource = res.data?.resultData?.list || []
        state.page.total = res.data?.resultData?.total || 0
        state.fullName = res.data?.fullName || '--'
        state.inspectionResult = res.data?.inspectionResult || '实际值'
      }
    })
  }
  // 查看日志
  const seeLogFn = (item) => {
    api.dataQuality.qualityTaskRecordLog({ id: item.processInstanceId }).then((res) => {
      if (res.success) {
        state.dialogVisible = true
        state.dialogLogContent = res.data
      }
    })
  }

  // 导出
  let downFn
  // 查看结果
  const seeResultFn = (item) => {
    dialogSearch = (()=>(init) => {
      state.page.pageNum = init? 1 : state.page.pageNum
      const params = {
        id: item.id,
        pageNum: state.page.pageNum,
        pageSize: state.page.pageSize,
      }
      getQualityTaskRecordDetail(params);
    })()
    downFn = (()=>(() => {
      const params = {
        condition: {
          id: item.id,
        },
        pageNum: state.page.pageNum,
        pageSize: state.page.pageSize,
      }
      state.downLoading = true
      api.dataQuality.qualityTaskRecordExport(params).then((biob) => {
        const link = document.createElement('a')
        Object.assign(link, {
          download: `${state.fullName}_${new Date().getFullYear()}-${
            new Date().getMonth() + 1
          }-${new Date().getDate()}_${new Date().getHours()}-${new Date().getMinutes()}.xlsx`,
          href: window.URL.createObjectURL(biob),
          target: '_blank',
          style: 'display:none',
        })
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }).finally(() => {
        state.downLoading = false
      })
    }))()  
    dialogSearch(false)
    state.seeResultDialogVisible = true
  }
  // 刷新
  const refreshFn = (item) => {
    api.dataQuality.qualityTaskRecordRefresh({ id: item.id }).then((res) => {
      if (res.success) {
        state.tableData.list = state.tableData.list.map((val) => {
          if (val.id === item.id) {
            if (res.data.score) {
              res.data.score = res.data.score * 100
            }
            val = { ...res.data }
          }
          return val
        })
        ElNotification({
          title: '提示',
          message: '刷新成功',
          type: 'success',
        })
      }
    })
  }
  // 重跑
  const runFn = () => {
    api.dataQuality.taskExecute({ id: state.checkTaskId }).then((res) => {
      if (res.success) {
        ElNotification({
          title: '提示',
          message: '运行成功',
          type: 'success',
        })
      }
    })
  }

  onMounted(() => {
    state.tableHeight = document.body.offsetHeight - 674
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList
    if (router.currentRoute.value.query.id) {
      state.checkTaskId = Number(router.currentRoute.value.query.id)
    }
    getTaskList()
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .operatingLog {
    position: relative;
    padding: 16px;
    &.isLzos {
      padding: 0;
    }
    .title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 52px;
      padding: 0 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bolder;
      font-size: 18px;
      border-bottom: 1px solid #c5d0ea;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }
      &-btn {
        position: absolute;
        right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: $themeBlue;
        font-weight: 400;
        font-size: 16px;
        border-radius: 6px;
        cursor: pointer;

        &:hover {
          background-color: #e3ecff;
        }
      }
    }
    .quality {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .task {
        box-sizing: border-box;
        width: 440px;
        height: 410px;
        background-color: #fff;
        border-radius: 2px;
        .list {
          border: 1px solid #c5d0ea;
          &.checked {
            border: 1px solid #e3ecff;
          }
          &:hover {
            border: 1px solid #ffffff;
          }
        }
      }
      .rule {
        box-sizing: border-box;
        width: calc(100% - 448px);
        height: 410px;
        background-color: #fff;
        border-radius: 2px;
        .box {
          .list {
            border-bottom: 1px solid #c5d0ea;
            border-radius: 0;
            &:hover {
              border-bottom: 1px solid #fff;
              border-radius: 6px;
            }
            &.checked {
              border-bottom: 1px solid #e3ecff;
              border-radius: 6px;
            }
          }
        }
      }
      .box {
        height: calc(100% - 52px);
        padding: 16px;
        overflow-y: auto;
        .list {
          box-sizing: border-box;
          margin-bottom: 16px;
          padding: 16px;
          border-radius: 6px;
          cursor: pointer;
          &:hover {
            box-shadow: 0 4px 16px -2px rgba(30, 47, 85, 0.15);
          }
          &.checked {
            background-color: #e3ecff;
          }
          .top {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .pic {
              width: 40px;
              height: 40px;
            }
            .info {
              width: calc(100% - 48px);
              margin-left: 8px;
              .name {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                .text {
                  max-width: calc(100% - 68px);
                  overflow: hidden;
                  color: #1d2129;
                  font-weight: bolder;
                  font-size: 16px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
                .status {
                  box-sizing: border-box;
                  height: 24px;
                  margin-left: 8px;
                  padding: 0 7px;
                  color: $themeBlue;
                  font-size: 14px;
                  line-height: 24px;
                  background-color: #e3ecff;
                  border: 1px solid $themeBlue;
                  border-radius: 6px;
                  &.green {
                    color: #04c495;
                    background-color: #e6fff4;
                    border: 1px solid #04c495;
                  }
                  &.red {
                    color: #f63838;
                    background-color: #fff2f0;
                    border: 1px solid #f63838;
                  }
                  &.gray {
                    color: rgba(0, 0, 0, 0.75);
                    background-color: #e3ecff;
                    border: 1px solid #a3b4db;
                  }
                }
              }
              .desc {
                margin-top: 2px;
                color: #909399;
                font-size: 12px;
              }
            }
          }
          .bottom {
            display: flex;
            margin-top: 8px;
            overflow: hidden;
            color: #1d2129;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:first-of-type {
              margin-top: 10px;
            }
            &-label {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              margin-right: 90px;
              font-weight: bolder;
              &-name {
                color: #606266;
                font-weight: normal;
              }
              .status {
                box-sizing: border-box;
                height: 20px;
                padding: 0 4px;
                color: #1d2129;
                font-weight: normal;
                font-size: 12px;
                line-height: 18px;
                background-color: #f4f4f5;
                border: 1px solid #c0c4cc;
                border-radius: 2px;
                &.green {
                  color: #31b046;
                  background-color: #ebfaed;
                  border: 1px solid #31b046;
                }
                &.red {
                  color: #f63838;
                  background: #fff2f0;
                  border: 1px solid #fcd6d0;
                }
                &.gray {
                  color: #8091b7;
                  background: #f5f7fa;
                  border: 1px solid #cbd2e3;
                }
                &.blue {
                  color: #1e89ff;
                  background: #ebf4ff;
                  border: 1px solid #99c9ff;
                }
              }
            }
          }
          .time {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 12px;
            color: #a8abb2;
            font-weight: 400;
            font-size: 12px;
            font-family: 'PingFang SC';
            font-style: normal;
            line-height: 20px;
            &-status {
              box-sizing: border-box;
              height: 20px;
              padding: 0 4px;
              color: #1d2129;
              font-size: 12px;
              line-height: 18px;
              background-color: #f4f4f5;
              border: 1px solid #c0c4cc;
              border-radius: 2px;
              &.green {
                color: #31b046;
                background-color: #ebfaed;
                border: 1px solid #31b046;
              }
            }
          }
        }
      }
    }
    .log {
      height: calc(100vh - 546px);
      margin-top: 8px;
      background-color: #fff;
      border-radius: 2px;
      &.isLzos {
        height: calc(100vh - 418px);
      }
      .table {
        height: calc(100% - 52px);
        padding: 16px;
        .status {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .circle {
            width: 6px;
            height: 6px;
            margin-right: 8px;
            background-color: #04c495;
            border-radius: 50%;
            &.gray {
              background-color: #b8b8b8;
            }
            &.blue {
              background-color: $themeBlue;
            }
            &.red {
              background-color: #f63838;
            }
          }
        }
        .template-name-box {
          display: inline-block;
          align-content: center;
          width: 40px;
          height: 20px;
          color: #2ca340;
          font-size: 12px;
          line-height: 18px;
          text-align: center;
          background: #ebfaee;
          border: 1px solid #95d19f;
          border-radius: 2px;
          &.red {
            color: #f63838;
            background: #fff2f0;
            border: 1px solid #fcd6d0;
          }
          &.gray {
            color: #8091b7;
            background: #f5f7fa;
            border: 1px solid #cbd2e3;
          }
          &.blue {
            width: 52px;
            color: #1e89ff;
            background: #ebf4ff;
            border: 1px solid #99c9ff;
          }
        }
        :deep(.nancalui-table__empty) {
          top: 60%;
          .table-no-content {
            box-sizing: border-box;
            height: 90px;
            padding-top: 48px;
            background-image: url('/src/assets/table-no-content-small.png');
            background-repeat: no-repeat;
            background-position: top;
            background-size: 48px;
            .text {
              margin-top: 0;
            }
          }
          .pic-no-conyent {
            display: none;
          }
        }
      }
    }
  }
  .out-box {
    box-sizing: border-box;
    height: 388px;
    padding: 8px;
    overflow-y: hidden;
    border: 1px solid #a3b4db;
    border-radius: 6px;
    .words {
      height: 100%;
      overflow-y: auto;
      color: rgba(0, 0, 0, 0.75);
      font-size: 12px;
      line-height: normal;
      white-space: pre-wrap;
    }
  }
  .result-box{
    box-sizing: border-box;
    padding: 8px;
    overflow-y: hidden;
    border: 1px solid #a3b4db;
    border-radius: 6px;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    gap: 8;
    .title{
      display: grid;
      grid-template-columns: calc(50% - 8px)  calc(50% - 8px);
      font-size: 14px;
      gap: 16px;
      :deep(.el-form-item){
        width: 100%;
        overflow: hidden;
      }
      :deep(.el-form-item__content){
        >div{
          white-space: nowrap; /*禁止文本换行*/
          overflow: hidden; /*文本溢出隐藏*/
          text-overflow: ellipsis; /*显示省略号*/
        }
      }
    }
    .table{
      display: flex;
      flex-direction: column;
      gap: 16px;
      .table-head{
        display: flex;
        line-height: 32px;
        .item:nth-child(1){
          flex: 1;
        }
        .item:nth-child(2){
        }
      }
      .table-body{
        height: 300px;
      }
    }
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 100px);
    margin-top: 60px;

    .pic {
      width: 88px;
      height: auto;
      margin-bottom: 10px;
    }

    &-word {
      color: rgba(0, 0, 0, 0.55);
      font-weight: 400;
      font-size: 14px;
      text-align: center;
    }
  }
</style>
