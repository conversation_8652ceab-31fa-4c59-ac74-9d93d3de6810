<template>
  <section class="relation">
    <n-divider :distance="50" content-position="left" borderColor="#e4e7ed">父实体关系</n-divider>
    <n-my-table
      :attrList="parentCloumns"
      :isAction="false"
      :isPage="false"
      :tableData="data.parent"
      maxHeight="300"
    />
    <n-divider :distance="50" content-position="left" borderColor="#e4e7ed">子实体关系</n-divider>
    <n-my-table
      :attrList="childrenCloumns"
      :isAction="false"
      :isPage="false"
      :tableData="data.children"
      max-height="300"
    />
  </section>
</template>

<script lang="jsx">
  import FlowGraph from '../../graph'
  export default {
    props: ['cell'],
    data() {
      return {
        parentCloumns: [
          { prop: 'name', name: '父实体', width: 180 },
          { prop: 'parentIdentity', name: '父识别符', width: 180 },
          { prop: 'subIdentity', name: '子识别符', width: 180 },
          { prop: 'relationName', name: '关系名', width: 180 },
          { prop: 'must', name: '必需', width: 180 },
          { prop: 'key', name: '键', width: 180 },
          { prop: 'discern', name: '区分', width: 180 },
        ],
        childrenCloumns: [
          { prop: 'name', name: '子实体', width: 180 },
          { prop: 'subIdentity', name: '子识别符', width: 180 },
          { prop: 'parentIdentity', name: '父识别符', width: 180 },
          { prop: 'relationName', name: '关系名', width: 180 },
          { prop: 'must', name: '必需', width: 180 },
          { prop: 'key', name: '键', width: 180 },
          { prop: 'discern', name: '区分', width: 180 },
        ],
        data: {
          parent: [],
          children: [],
        },
        id: '',
      }
    },
    watch: {
      cell(val) {
        this.relationSearch()
      },
    },
    methods: {
      relationSearch() {
        const { graph } = FlowGraph
        const cells = graph?.toJSON()?.cells || []
        const parentNodes = cells.filter(
          (cell) => this.cell.id === cell.data?.subEntityId && cell.shape === 'edge',
        )

        const subNodes = cells.filter(
          (cell) => this.cell.id === cell.data?.parentEntityId && cell.shape === 'edge',
        )
        this.data.parent = parentNodes.map((item) => {
          return {
            name: item.data.parentEntityName,
            parentIdentity: item.data.fatherChildKeys?.map((key) => key.fatherKey)?.join(','),
            subIdentity: item.data.fatherChildKeys?.map((key) => key.childKey)?.join(','),
            relationName: item.data.name,
            must: item.data.parentMust,
            key: item.data.keyUse,
            discern: '',
          }
        })
        this.data.children = subNodes.map((item) => {
          return {
            name: item.data.subEntityName,
            parentIdentity: item.data.fatherChildKeys?.map((key) => key.fatherKey)?.join(','),
            subIdentity: item.data.fatherChildKeys?.map((key) => key.childKey)?.join(','),
            relationName: item.data.name,
            must: item.data.subMust,
            key: item.data.keyUse,
            discern: '',
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .relation {
    padding: 14px;
  }

  :deep(.el-table th.el-table__cell) {
    background: rgba(37, 123, 255, 0.08) !important;
  }
</style>
