

export{}
declare module '@vue/runtime-core' {

  export interface GlobalComponents{
    NAccordion: typeof import('./types/vue-nancalui')['Accordion']
NAlert: typeof import('./types/vue-nancalui')['Alert']
NAnchor: typeof import('./types/vue-nancalui')['Anchor']
NAutoComplete: typeof import('./types/vue-nancalui')['AutoComplete']
NAvatar: typeof import('./types/vue-nancalui')['Avatar']
NBackTop: typeof import('./types/vue-nancalui')['BackTop']
NBadge: typeof import('./types/vue-nancalui')['Badge']
NBreadcrumb: typeof import('./types/vue-nancalui')['Breadcrumb']
NBreadcrumbItem: typeof import('./types/vue-nancalui')['BreadcrumbItem']
NButton: typeof import('./types/vue-nancalui')['Button']
NButtonGroup: typeof import('./types/vue-nancalui')['ButtonGroup']
NCard: typeof import('./types/vue-nancalui')['Card']
NCardList: typeof import('./types/vue-nancalui')['CardList']
NCarousel: typeof import('./types/vue-nancalui')['Carousel']
NCarouselItem: typeof import('./types/vue-nancalui')['CarouselItem']
NCheckbox: typeof import('./types/vue-nancalui')['Checkbox']
NCheckboxGroup: typeof import('./types/vue-nancalui')['CheckboxGroup']
NCheckboxButton: typeof import('./types/vue-nancalui')['CheckboxButton']
NCollapse: typeof import('./types/vue-nancalui')['Collapse']
NCollapseItem: typeof import('./types/vue-nancalui')['CollapseItem']
NColorPicker: typeof import('./types/vue-nancalui')['ColorPicker']
NComment: typeof import('./types/vue-nancalui')['Comment']
NCountdown: typeof import('./types/vue-nancalui')['Countdown']
NCustomizeSteps: typeof import('./types/vue-nancalui')['CustomizeSteps']
NDatePicker: typeof import('./types/vue-nancalui')['DatePicker']
NStickSlider: typeof import('./types/vue-nancalui')['StickSlider']
NDatePickerPro: typeof import('./types/vue-nancalui')['DatePickerPro']
NNRangeDatePickerPro: typeof import('./types/vue-nancalui')['NRangeDatePickerPro']
NDescriptions: typeof import('./types/vue-nancalui')['Descriptions']
NDivider: typeof import('./types/vue-nancalui')['Divider']
NDrawer: typeof import('./types/vue-nancalui')['Drawer']
NDropdown: typeof import('./types/vue-nancalui')['Dropdown']
NDropdownMenu: typeof import('./types/vue-nancalui')['DropdownMenu']
NDynamicWiden: typeof import('./types/vue-nancalui')['DynamicWiden']
NEditableSelect: typeof import('./types/vue-nancalui')['EditableSelect']
NEmpty: typeof import('./types/vue-nancalui')['Empty']
NForm: typeof import('./types/vue-nancalui')['Form']
NFormItem: typeof import('./types/vue-nancalui')['FormItem']
NFormOperation: typeof import('./types/vue-nancalui')['FormOperation']
NFullscreen: typeof import('./types/vue-nancalui')['Fullscreen']
NRow: typeof import('./types/vue-nancalui')['Row']
NCol: typeof import('./types/vue-nancalui')['Col']
NHandleWidth: typeof import('./types/vue-nancalui')['HandleWidth']
NIcon: typeof import('./types/vue-nancalui')['Icon']
NIconGroup: typeof import('./types/vue-nancalui')['IconGroup']
NImportErrModal: typeof import('./types/vue-nancalui')['ImportErrModal']
NInput: typeof import('./types/vue-nancalui')['Input']
NInputIcon: typeof import('./types/vue-nancalui')['InputIcon']
NInputNumber: typeof import('./types/vue-nancalui')['InputNumber']
NLayout: typeof import('./types/vue-nancalui')['Layout']
NContent: typeof import('./types/vue-nancalui')['Content']
NHeader: typeof import('./types/vue-nancalui')['Header']
NFooter: typeof import('./types/vue-nancalui')['Footer']
NAside: typeof import('./types/vue-nancalui')['Aside']
NLeftTree: typeof import('./types/vue-nancalui')['LeftTree']
NList: typeof import('./types/vue-nancalui')['List']
NListItem: typeof import('./types/vue-nancalui')['ListItem']
NMention: typeof import('./types/vue-nancalui')['Mention']
NMenu: typeof import('./types/vue-nancalui')['Menu']
NMenuItem: typeof import('./types/vue-nancalui')['MenuItem']
NSubMenu: typeof import('./types/vue-nancalui')['SubMenu']
NMessageBox: typeof import('./types/vue-nancalui')['MessageBox']
NModal: typeof import('./types/vue-nancalui')['Modal']
NModalHeader: typeof import('./types/vue-nancalui')['Modal']
NModalBody: typeof import('./types/vue-nancalui')['Modal']
NModalFooter: typeof import('./types/vue-nancalui')['Modal']
NModuleName: typeof import('./types/vue-nancalui')['ModuleName']
NMyTable: typeof import('./types/vue-nancalui')['MyTable']
NNotification: typeof import('./types/vue-nancalui')['Notification']
NFixedOverlay: typeof import('./types/vue-nancalui')['FixedOverlay']
NFlexibleOverlay: typeof import('./types/vue-nancalui')['FlexibleOverlay']
NPagination: typeof import('./types/vue-nancalui')['Pagination']
NPanel: typeof import('./types/vue-nancalui')['Panel']
NPanelHeader: typeof import('./types/vue-nancalui')['PanelHeader']
NPanelBody: typeof import('./types/vue-nancalui')['PanelBody']
NPanelFooter: typeof import('./types/vue-nancalui')['PanelFooter']
NPermissionButton: typeof import('./types/vue-nancalui')['PermissionButton']
NPopover: typeof import('./types/vue-nancalui')['Popover']
NProgress: typeof import('./types/vue-nancalui')['Progress']
NPublicTable: typeof import('./types/vue-nancalui')['PublicTable']
NRadio: typeof import('./types/vue-nancalui')['Radio']
NRadioGroup: typeof import('./types/vue-nancalui')['RadioGroup']
NRadioButton: typeof import('./types/vue-nancalui')['RadioButton']
NRate: typeof import('./types/vue-nancalui')['Rate']
NReadTip: typeof import('./types/vue-nancalui')['ReadTip']
NResult: typeof import('./types/vue-nancalui')['Result']
NNScrollbar: typeof import('./types/vue-nancalui')['NScrollbar']
NSearch: typeof import('./types/vue-nancalui')['Search']
NSelect: typeof import('./types/vue-nancalui')['Select']
NOption: typeof import('./types/vue-nancalui')['Option']
NOptionGroup: typeof import('./types/vue-nancalui')['OptionGroup']
NSkeleton: typeof import('./types/vue-nancalui')['Skeleton']
NSkeletonItem: typeof import('./types/vue-nancalui')['SkeletonItem']
NSlider: typeof import('./types/vue-nancalui')['Slider']
NSpace: typeof import('./types/vue-nancalui')['Space']
NSplitter: typeof import('./types/vue-nancalui')['Splitter']
NSplitterPane: typeof import('./types/vue-nancalui')['SplitterPane']
NStatistic: typeof import('./types/vue-nancalui')['Statistic']
NStatus: typeof import('./types/vue-nancalui')['Status']
NSteps: typeof import('./types/vue-nancalui')['Steps']
NStep: typeof import('./types/vue-nancalui')['Step']
NStepsGuide: typeof import('./types/vue-nancalui')['StepsGuide']
NSwitch: typeof import('./types/vue-nancalui')['Switch']
NTable: typeof import('./types/vue-nancalui')['Table']
NColumn: typeof import('./types/vue-nancalui')['Column']
NTableV2: typeof import('./types/vue-nancalui')['TableV2']
NAutoResizer: typeof import('./types/vue-nancalui')['AutoResizer']
NTabs: typeof import('./types/vue-nancalui')['Tabs']
NTab: typeof import('./types/vue-nancalui')['Tab']
NTag: typeof import('./types/vue-nancalui')['Tag']
NTagInput: typeof import('./types/vue-nancalui')['TagInput']
NTextarea: typeof import('./types/vue-nancalui')['Textarea']
NTimePicker: typeof import('./types/vue-nancalui')['TimePicker']
NTimeSelect: typeof import('./types/vue-nancalui')['TimeSelect']
NTimeline: typeof import('./types/vue-nancalui')['Timeline']
NTimelineItem: typeof import('./types/vue-nancalui')['TimelineItem']
NTooltip: typeof import('./types/vue-nancalui')['Tooltip']
NTransfer: typeof import('./types/vue-nancalui')['Transfer']
NTree: typeof import('./types/vue-nancalui')['Tree']
NUpload: typeof import('./types/vue-nancalui')['Upload']
NVirtualList: typeof import('./types/vue-nancalui')['VirtualList']
NWatermark: typeof import('./types/vue-nancalui')['Watermark']
  }


  export interface ComponentCustomProps {
    vDraggable?: typeof import('./types/vue-nancalui')['DraggableDirective']
vDroppable?: typeof import('./types/vue-nancalui')['DroppableDirective']
vSortable?: typeof import('./types/vue-nancalui')['SortableDirective']
vDImagePreview?: typeof import('./types/vue-nancalui')['ImagePreviewDirective']
vLoading?: typeof import('./types/vue-nancalui')['LoadingDirective']
vClickoutside?: typeof import('./types/vue-nancalui')['ClickoutsideDirective']
vRipple?: typeof import('./types/vue-nancalui')['RippleDirective']
vStepsGuide?: typeof import('./types/vue-nancalui')['StepsGuideDirective']
vfileDrop?: typeof import('./types/vue-nancalui')['FileDropDirective']
  }


  export interface ComponentCustomProperties{
    $drawerService?: typeof import('./types/vue-nancalui')['DrawerService']
$imagePreviewService?: typeof import('./types/vue-nancalui')['ImagePreviewService']
$loadingService?: typeof import('./types/vue-nancalui')['LoadingService']
$message?: typeof import('./types/vue-nancalui')['Message']
$MessageBoxService?: typeof import('./types/vue-nancalui')['MessageBoxService']
$notificationService?: typeof import('./types/vue-nancalui')['NotificationService']
  }


}
