<template>
  <div class="home-page scroll-bar-style">
    <div class="home-page-top">
      <section>
        <h4>数据模型</h4>
        <span
          >通过数据模型，构建平台数据逻辑结构，支撑数据采集落地、数据开发接入、数据治理落地等场景应用。</span
        >
      </section>
      <img src="~@img/home/<USER>" alt="" />
    </div>
    <div class="home-page-content">
      <section class="home-page-content-left">
        <h5><img class="count-img" src="@img/model/model_statistics.png" alt="" />数据统计</h5>
        <div class="left-content">
          <div class="content-box">
            <img src="@img/model/<EMAIL>" alt="" />
            <h3>{{ state.metaCount }}</h3>
            <div class="box-func">
              <span>元数据总个数（个）</span>
              <n-button
                size="sm"
                variant="solid"
                color="primary"
                @click.stop.prevent="goJump('metaDataList')"
                >查看</n-button
              >
            </div>
          </div>
          <div class="content-box">
            <img src="@img/model/<EMAIL>" alt="" />
            <h3 class="box-count">{{ state.modelCount }}</h3>
            <div class="box-func">
              <span>数据模型总个数（个）</span>
              <n-button
                size="sm"
                variant="solid"
                color="primary"
                @click.stop.prevent="goJump('dataModal')"
                >查看</n-button
              >
            </div>
          </div>
        </div>
      </section>
      <section class="home-page-content-right">
        <h5><img class="count-img" src="@img/model/model_add.png" alt="" />如何创建数据模型</h5>
        <div class="quick-entry">
          <div v-for="(item, index) in state.quickData" :key="index" class="quick-list">
            <div class="quick-item">
              <div class="item-left">
                <div class="quick-title">
                  <div class="circle"></div>
                  {{ item.title }}
                </div>
                <div class="quick-content" :title="item.content">
                  {{ item.content }}
                </div>
              </div>
              <img class="quick-icon" :src="item.icon" alt="" />
              <img class="swiper-default" src="~@img/home/<USER>/swiper-default.png" alt="" />
              <n-button
                v-if="state.buttonAuthList?.includes(item.codeName)"
                :code="item.codeName"
                size="sm"
                @click.stop.prevent="goJump(item.routerName, true)"
              >
                <SvgIcon :icon="item.btnIcon" class="icon-add-overview" :title="proItem.btnName" />
                {{ item.btnName }}
              </n-button>
            </div>
            <div class="blur-box"></div>
          </div>
        </div>
      </section>
    </div>
    <div class="home-page-bottom">
      <h5><img class="count-img" src="@img/model/model_trend.png" alt="" />模型新趋势</h5>
      <div id="lineChart" class="bottom-box-content"> </div>
    </div>
  </div>
</template>

<script>
  import { reactive, onMounted, toRefs } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import * as echarts from 'echarts'
  import 'swiper/css'
  import 'swiper/css/navigation'
  import { timestampToTime } from '@/const/public.js'

  export default {
    components: {},
    setup() {
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/model/${name}.png`, import.meta.url).href //本地文件路径
      }
      const state = reactive({
        whiteOrderNavbar: [],
        currentProject: {},
        buttonAuthList: [],
        metaCount: 0,
        modelCount: 0,
        quickData: [
          {
            icon: getAssetsImages('model-res-quick'),
            title: '逆向数据模型',
            content:
              '定义系统统一标准元数据信息，元数据是数据模型构建的基础，通过元数据管理确认数据模型设计唯一数据源。',
            btnName: '立即逆向',
            btnIcon: 'icon_rev',
            codeName: 'governanceManage_modal_modalOverview_goReverseModelIndex',
            routerName: {
              name: 'reverseModelList',
              goChildName: 'reverseModelList',
              type: 'reverseModelList',
              title: '逆向数据模型',
            },
          },
          {
            icon: getAssetsImages('meta-quick'),
            title: '创建元数据',
            content:
              '对接数据源，通过逆向生产数据库结构，自动还原创建数据模型，实现模型的快速构建。',
            btnName: '点击创建',
            btnIcon: 'icon_add_black',
            codeName: 'governanceManage_modal_modalOverview_goAddMetaData',
            routerName: {
              name: 'addMetaData',
              goChildName: 'addMetaData',
              type: 'ADD',
              title: '新增元数据',
            },
          },
          {
            icon: getAssetsImages('model-quick'),
            title: '新增数据模型',
            content: '基于业务实际情况，选择元数据正向构建数据模型。',
            btnName: '立即新增',
            btnIcon: 'icon_add_black',
            codeName: 'governanceManage_modal_modalOverview_goAddDimensionSteps',
            routerName: {
              name: 'addDimensionSteps',
              goChildName: 'addDimensionSteps',
              type: 'ADD',
              title: '新增数据模型',
            },
          },
        ],
      })
      const store = useStore()
      const router = useRouter()

      const methods = {
        goJump(item, needProject = false) {
          if (!needProject || state.currentProject.projectCode) {
            // const obj = state.whiteOrderNavbar.find((data) => {
            //   return data.name === item.name
            // })
            let _router = { name: item }
            if (item.title) {
              _router = { name: item.goChildName, query: { title: item.title, type: item.type } }
            }
            router.push(_router)
          } else {
            ElNotification({
              title: '提示',
              message: '请先新增场景',
              type: 'error',
            })
          }
        },
        geCount() {
          api.model.getModelCount().then((res) => {
            const { success, data } = res
            if (success) {
              state.modelCount = data
            }
          })
          api.model.getMetaCount().then((res) => {
            const { success, data } = res
            if (success) {
              state.metaCount = data
            }
          })
        },
        getLineChart() {
          if (lineChart !== 'undefined') {
            echarts.init(document.getElementById('lineChart')).dispose()
          }
          var lineChart = echarts.init(document.getElementById('lineChart'))
          let option = {
            grid: {
              left: '3%',
              right: '3%',
              top: '5%',
            },
            tooltip: {
              trigger: 'axis',
              textStyle: {
                fontSize: 12,
                color: '#333',
              },
              // formatter: '{b}<br>数据占比 {d}%',
            },
            // legend: {
            //   data: ['元数据', '数据模型'],
            // },
            color: ['#79BBFF', '#3DD986'],
            legend: {
              icon: 'circle',
              data: ['元数据', '数据模型'],
              bottom: '4%',
              right: '50%',
              itemWidth: 6,
              textStyle: {
                fontSize: 12,
                color: '#666',
              },
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                data: [0, 0, 0, 0, 0, 0, 0],
                name: '元数据',
                type: 'line',
                color: ['#79BBFF'],
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  borderColor: '#ffffff',
                  borderType: 'solid',
                  borderWidth: 2,
                },
                areaStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#79BBFF',
                        },
                        {
                          offset: 1,
                          color: '#FCFFFD',
                        },
                      ],
                    },
                  },
                  emphasis: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#79BBFF',
                        },
                        {
                          offset: 1,
                          color: '#FCFFFD',
                        },
                      ],
                    },
                  },
                },
              },
              {
                data: [0, 0, 0, 0, 0, 0, 0],
                name: '数据模型',
                type: 'line',
                color: ['#3DD986'],
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  borderColor: '#ffffff',
                  borderType: 'solid',
                  borderWidth: 2,
                },
                areaStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#E7FDF0',
                        },
                        {
                          offset: 1,
                          color: '#FCFFFD',
                        },
                      ],
                    },
                  },
                  emphasis: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#E7FDF0',
                        },
                        {
                          offset: 1,
                          color: '#FCFFFD',
                        },
                      ],
                    },
                  },
                },
              },
            ],
          }
          let timestamp = new Date().getTime() // 当前日期时间戳
          let endDate = timestampToTime(timestamp - 24 * 60 * 60 * 1000),
            startDate = timestampToTime(timestamp - 16 * 24 * 60 * 60 * 1000)
          const params = {
            endDate,
            startDate,
          }
          api.model.getMetaIncremental(params).then((res) => {
            const { success, data } = res
            if (success) {
              option.xAxis.data = Object.keys(data)
              option.series[0].data = Object.values(data)
              lineChart.setOption(option)
            }
          })
          api.model.getModelIncremental(params).then((res) => {
            const { success, data } = res
            if (success) {
              option.xAxis.data = Object.keys(data)
              option.series[1].data = Object.values(data)
              lineChart.setOption(option)
            }
          })
        },
      }
      onMounted(() => {
        const { whiteOrderNavbar, currentProject, buttonAuthList } = toRefs(store.state.user)
        state.whiteOrderNavbar = whiteOrderNavbar.value
        state.currentProject = currentProject.value
        state.buttonAuthList = buttonAuthList
        methods.getLineChart()
        methods.geCount()
      })

      return {
        state,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .home-page {
    width: 100%;
    height: calc(100vh - $navbarHeight);
    background-color: $themeContentBg;
    overflow: auto;
    position: relative;
    &-top {
      width: 100%;
      height: 260px;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      position: relative;
      section {
        position: absolute;
        top: 58px;
        left: 40px;
        h4 {
          font-size: 16px;
          font-weight: 500;
          margin: 0;
          margin-bottom: 20px;
        }
        span {
          font-size: 14px;
        }
      }
      & img {
        height: 100%;
        // width: 100%;
        margin: 0 auto;
      }
    }
    &-content {
      position: absolute;
      top: 164px;
      left: 0;
      width: 100%;
      height: 280px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      section {
        background-color: #fff;
        border-radius: 8px;
        padding: 30px;
        box-sizing: border-box;

        > div {
          width: 100%;
          height: calc(100% - 42px);
          display: flex;
          justify-content: space-between;
          .content-box {
            width: calc(50% - 10px);
            height: 100%;
            background: linear-gradient(
              180deg,
              rgba(180, 230, 249, 0.4) 0%,
              rgba(71, 179, 226, 0.4) 100%
            );
            border-radius: 8px;
            padding: 40px 34px;
            box-sizing: border-box;
            position: relative;
            img {
              height: 100px;
              width: 110px;
              position: absolute;
              right: 0;
              bottom: 0;
            }
            h3 {
              font-size: 34px;
              color: #333;
              margin: 0;
              margin-bottom: 16px;
            }
            .box-func {
              display: flex;
              justify-content: space-between;
              align-items: center;
              position: relative;
              z-index: 4;
              span {
                font-size: 12px;
              }
              :deep(.nancalui-button) {
                box-sizing: border-box;
                font-weight: 500;
                width: 64px;
                height: 28px;
                border-radius: 14px;
              }
            }
          }
          .content-box:last-child {
            background: linear-gradient(
              180deg,
              rgba(200, 216, 231, 0.4) 0%,
              rgba(107, 137, 169, 0.4) 100%
            );
          }
          .content-box:hover {
            background: linear-gradient(
              180deg,
              rgba(180, 230, 249, 0.6) 0%,
              rgba(71, 179, 226, 0.6) 100%
            );
          }
          .content-box:last-child:hover {
            background: linear-gradient(
              180deg,
              rgba(200, 216, 231, 0.6) 0%,
              rgba(107, 137, 169, 0.6) 100%
            );
          }
        }
      }
      &-left {
        width: calc((2 / 5) * 100% - 5px);
      }
      &-right {
        width: calc((3 / 5) * 100% - 5px);
        .quick-entry {
          display: flex;
          flex-wrap: nowrap;
          justify-content: space-between;
          flex-direction: row;
          .quick-list {
            position: relative;
            width: calc(33% - 10px);
            height: 100%;
            overflow: hidden;
            .blur-box {
              position: absolute;
              top: 0;
              right: 0;
              width: 76px;
              height: 54px;
            }
            &:hover {
              cursor: pointer;
              .blur-box {
                background: #79bbff;
                opacity: 0.19;
                filter: blur(20px);
              }

              .quick-item {
                .quick-title {
                  .circle {
                    background: $themeBlue;
                  }
                }
              }
            }
            .quick-item {
              position: relative;
              color: #333333;
              font-size: 14px;
              height: 100%;
              padding: 14px 17px 14px 20px;
              background: #f7f8fa;
              border-radius: 8px;
              border: 2px solid #ffffff;
              display: flex;
              .nancalui-button {
                position: absolute !important;
                right: 30px;
                bottom: 20px;
              }
              :deep(.nancalui-button) {
                width: 106px;
                height: 32px;
                background: linear-gradient(180deg, #f3f5f8 0%, #ffffff 100%);
                box-shadow: 2px 4px 6px 2px rgba(55, 99, 170, 0.06),
                  -2px -4px 6px 2px rgba(255, 255, 255, 0.5);
                border-radius: 8px;
                border: 2px solid #ffffff;
              }
              .item-left {
                flex: 1;
              }
              .swiper-default {
                position: absolute;
                right: 0;
                bottom: 0;
                height: 50%;
                opacity: 1;
              }
              .quick-icon {
                font-size: 20px;
                width: 54px;
                height: 69px;
                margin: 20px 10px 0 30px;
                opacity: 1;
              }

              .quick-icon {
                color: #fff;
              }

              .quick-title {
                position: relative;
                padding: 6px 0 14px 0;
                font-weight: bolder;
                display: flex;
                align-items: center;
                color: #333;

                .circle {
                  width: 8px;
                  height: 8px;
                  background: #e1e1e1;
                  border-radius: 8px;
                  margin-right: 8px;
                }
              }

              .quick-content {
                width: 100%;
                position: relative;
                font-weight: 400;
                color: #666;
                height: calc(100% - 60px);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 5;
                -webkit-box-orient: vertical;
                font-size: 12px;
              }
            }
            .icon-add-overview {
              font-size: 12px;
            }
          }
        }
      }
    }
    &-bottom {
      height: calc(100% - 468px);
      min-height: 390px;
      width: calc(100% - 40px);
      margin: 10px 20px;
      margin-top: 196px;
      background-color: #fff;
      border-radius: 8px;
      padding: 30px;
      box-sizing: border-box;
      .bottom-box-content {
        width: 100%;
        height: calc(100% - 30px);
        box-sizing: border-box;
      }
    }
    &-content,
    &-bottom {
      h5 {
        position: relative;
        height: 22px;
        line-height: 22px;
        margin: 0;
        padding-left: 20px;
        margin-bottom: 20px;
        // color: #697a9a;
        color: #333;
        font-size: 14px;
        font-weight: 600px;
      }
      .count-img {
        width: 14px;
        position: absolute;
        left: 0;
        top: 4px;
      }
    }
  }
</style>
