<template>
  <section class="container">
    <section class="container-box">
      <div class="page-title">
        资产查询-{{ name }}
        <div class="detail-back-box" @click.prevent="$router.go(-1)"> 返回 </div>
      </div>
      <div class="layout-box">
        <section class="container-box-table">
          <div class="page-top">
            <div class="btn active" @click.prevent="state.showFilter = true">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="17"
                viewBox="0 0 16 17"
                fill="none"
              >
                <path
                  d="M1.33203 2.83398H14.6654"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M1.33203 8.16602H4.9987"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M1.33203 13.5H4.9987"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10.5013 11.8327C12.0661 11.8327 13.3346 10.5641 13.3346 8.99935C13.3346 7.43455 12.0661 6.16602 10.5013 6.16602C8.9365 6.16602 7.66797 7.43455 7.66797 8.99935C7.66797 10.5641 8.9365 11.8327 10.5013 11.8327Z"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12.332 11.166L14.6654 13.5162"
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              高级查询
            </div>
            <div class="btns">
              <div class="line" @click.prevent="onSearch">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <g clip-path="url(#clip0_1410_90084)">
                    <path
                      d="M6 1.41589C7.43769 0.874404 9.00889 0.861306 10.4546 1.37876C11.9004 1.89621 13.1344 2.91334 13.9533 4.26248C14.7722 5.61162 15.1271 7.21226 14.9594 8.80057C14.8768 9.58351 14.6696 10.3404 14.3514 11.0421M14.3514 11.0421C14.024 11.7638 13.579 12.4271 13.0311 13L13.0794 11.0421L14.3514 11.0421Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                    <path
                      d="M10 14.5841C8.56231 15.1256 6.99111 15.1387 5.54535 14.6212C4.09959 14.1038 2.86558 13.0867 2.0467 11.7375C1.22782 10.3884 0.872948 8.78774 1.04058 7.19943C1.12321 6.41649 1.33038 5.65958 1.64864 4.95795M1.64864 4.95795C1.97603 4.2362 2.42098 3.57294 2.96887 3L2.9206 4.95795L1.64864 4.95795Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1410_90084">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                刷新
              </div>
              <div class="line" @click.prevent="colConfigDrawerRef.open()">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M2.01293 4.08894L7.51293 1.02163C7.81569 0.852786 8.18431 0.852786 8.48707 1.02163L13.9871 4.08894C14.3037 4.26554 14.5 4.59972 14.5 4.96231V8V11.0377C14.5 11.4003 14.3037 11.7345 13.9871 11.9111L8.48707 14.9784C8.18431 15.1472 7.81569 15.1472 7.51293 14.9784L2.01293 11.9111C1.69626 11.7345 1.5 11.4003 1.5 11.0377V4.96231C1.5 4.59972 1.69626 4.26554 2.01293 4.08894Z"
                    stroke="currentColor"
                  />
                  <circle cx="8" cy="8" r="3" stroke="currentColor" />
                </svg>
                列设置
              </div>
            </div>
          </div>
          <!-- 过滤条件 -->
          <div class="filter" v-if="state.showFilter">
            <VueDraggableNext
              class="filter"
              v-model="filterCondition"
              group="people"
              animation="300"
              @change="change"
            >
              <div class="filter-item" v-for="(item, index) in filterCondition" :key="index">
                <div class="select">
                  <el-select
                    style="width: 100%"
                    v-model="item.colName"
                    placeholder="请选择字段"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in state.tableColumns"
                      :key="item.name"
                      :label="item.cnName"
                      :value="item.name"
                    />
                  </el-select>
                </div>
                <div class="select">
                  <el-select
                    style="width: 100%"
                    v-model="item.operator"
                    placeholder="请选择过滤条件"
                    clearable
                  >
                    <el-option
                      v-for="item in state.ApiDataFilterOperatorEnum"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="select">
                  <el-input
                    style="width: 100%"
                    v-model="item.value"
                    placeholder="请输入值"
                    clearable
                  />
                </div>
                <div class="clear" @click="clearFilter(index)">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_1314_218707)">
                      <path
                        d="M2.5 4H13.5H2.5ZM14 14C14 14.8284 13.3284 15.5 12.5 15.5H3.5C2.67157 15.5 2 14.8284 2 14H3C3 14.2761 3.22386 14.5 3.5 14.5H12.5C12.7761 14.5 13 14.2761 13 14H14ZM3.5 15.5C2.67157 15.5 2 14.8284 2 14V4H3V14C3 14.2761 3.22386 14.5 3.5 14.5V15.5ZM14 4V14C14 14.8284 13.3284 15.5 12.5 15.5V14.5C12.7761 14.5 13 14.2761 13 14V4H14Z"
                        fill="#606266"
                      />
                      <path
                        d="M4 4H12H4ZM12.5 2C12.5 1.17157 11.8284 0.5 11 0.5H5C4.17157 0.5 3.5 1.17157 3.5 2H4.5C4.5 1.72386 4.72386 1.5 5 1.5H11C11.2761 1.5 11.5 1.72386 11.5 2H12.5ZM5 0.5C4.17157 0.5 3.5 1.17157 3.5 2V4H4.5V2C4.5 1.72386 4.72386 1.5 5 1.5V0.5ZM12.5 4V2C12.5 1.17157 11.8284 0.5 11 0.5V1.5C11.2761 1.5 11.5 1.72386 11.5 2V4H12.5Z"
                        fill="#606266"
                      />
                      <line
                        x1="1"
                        y1="3.5"
                        x2="15"
                        y2="3.5"
                        stroke="#606266"
                        stroke-linecap="round"
                      />
                      <line
                        x1="11.5"
                        y1="12.5"
                        x2="4.5"
                        y2="12.5"
                        stroke="#606266"
                        stroke-linecap="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_1314_218707">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            </VueDraggableNext>
            <div class="btns">
              <div
                class="btn active"
                @click.prevent="
                  filterCondition.push({
                    colName: '',
                    operator: '',
                    value: '',
                  })
                "
              >
                新增条件
              </div>
              <div class="btn active" @click.prevent="onSearch"> 查询 </div>
              <div
                class="btn"
                @click.prevent="
                  filterCondition = [
                    {
                      colName: '',
                      operator: '',
                      value: '',
                    },
                  ]
                "
              >
                清空
              </div>
              <div class="btn" @click.prevent="state.showFilter = false"> 关闭 </div>
            </div>
          </div>
          <div class="table-box" v-loading="loading">
            <CfTable
              ref="multipleTableRef"
              :key="colConfigDrawerRef?.key"
              :tableConfig="{
                data: state.tableList,
                rowKey: 'id',
                lazy: true,
                onSortChange: sortFn,
              }"
              indexSortable
              isNeedIndex
              :paginationConfig="{
                total: state.total,
                pageSize: state.filterSearch.pageSize,
                currentPage: state.filterSearch.pageNum,
                onCurrentChange: (v) => {
                  state.filterSearch.pageNum = v
                  onSearch()
                },
                onSizeChange: (v) => {
                  state.filterSearch.pageSize = v
                  state.filterSearch.pageNum = 1
                  onSearch()
                },
              }"
              :table-head-titles="tableHeadTitles"
            >
              <template #index="{ index }">
                {{ index + 1 + state.filterSearch.pageSize * (state.filterSearch.pageNum - 1) }}
              </template>
            </CfTable>
          </div>
        </section>
        <ColConfigDrawer
          v-if="state.tableColumns?.length"
          ref="colConfigDrawerRef"
          v-model:data="state.tableColumns"
          @success="onSearch"
          @update:data="updateData"
        />
      </div>
    </section>
  </section>
</template>
<script setup>
  import { VueDraggableNext } from 'vue-draggable-next'
  import { getAssetsTableData, getAssetsSourceDetail } from '@/api/sceneManage'
  import ColConfigDrawer from './components/colConfigDrawer'
  import api from '@/api/index'
  import { useRoute } from 'vue-router'
  const route = useRoute()
  const { registerFrom, id, type, name } = route.query
  const state = reactive({
    open: false,
    tableList: [],
    treeSearchText: '',
    filterSearch: {
      condition: {
        columns: [],
        filterCondition: [
          {
            colName: '',
            operator: '',
            value: '',
          },
        ],
        id,
        orderBy: [
          {
            field: '',
            order: '',
          },
        ],
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    total: 0,
    tableColumns: [],
    ApiDataFilterOperatorEnum: [
      {
        label: '等于(=)',
        value: 'EQUAL',
      },
      {
        label: '不等于(!=)',
        value: 'NOT_EQUAL',
      },
      {
        label: '包含(like)',
        value: 'LIKE',
      },
      {
        label: '不包含(not like)',
        value: 'NOT_LIKE',
      },
      {
        label: '在列表中(in)',
        value: 'IN',
      },
      {
        label: '不在列表中(not in)',
        value: 'NOT_IN',
      },
    ],
  })
  const { filterCondition, orderBy } = toRefs(state.filterSearch.condition)
  const loading = ref(false)
  const colConfigDrawerRef = ref(null)
  // 计算属性
  const tableHeadTitles = computed(() => {
    return state.tableColumns
      ?.filter((item) => !item.isUnChecked)
      ?.map(
        (item) =>
          ({
            name: item.cnName || item.name,
            prop: item.name,
            width: item.width,
            sortable: true,
          } || []),
      )
  })

  // 表数据查询
  const getAssetsTableDataFn = () => {
    getAssetsTableData(state.filterSearch)
      .then((res) => {
        loading.value = false
        state.tableList = res?.data?.list || []
        state.total = res?.data?.total || 0
      })
      .catch((err) => {
        loading.value = false
      })
  }
  const change = (event) => {
    const { moved, added } = event

    if (moved) console.log('moved', moved)
    if (added) console.log('added', added, added.element)
  }
  const onSearch = () => {
    loading.value = true
    getAssetsTableDataFn()
  }
  // 获取表头字段
  const getTableHeadTitles = () => {
    const url = type === 'RESOURCE_DIRECTORY' ? getAssetsSourceDetail : api.model.getModeData
    url({ id: registerFrom }).then((res) => {
      state.tableColumns =
        res.data?.tableColumnMeta?.map((_) => ({
          ..._,
          name: _?.colName,
          cnName: _?.comment,
          isUnChecked: _?.hidden,
        })) ||
        res?.data ||
        []

      if (Array.isArray(state.tableColumns)) {
        state.tableColumns = adjustColumnWidth(state.tableColumns)
        state.tableColumns.sort((a, b) => a.customSortNo - b.customSortNo)
      } else {
        state.tableColumns = []
      }
    })
  }

  const updateData = () => {
    state.tableColumns = adjustColumnWidthTwo(state.tableColumns)
  }

  function adjustColumnWidth(data) {
    // 深拷贝原始数据避免污染原数组
    const clonedData = data.map((item) => ({ ...item }))

    // Step 1: 设置默认宽度
    const withDefaultWidth = clonedData.map((item) => ({
      ...item,
      width: item.width || 200, // 保留已有width或设置默认值
      isUnChecked: item?.hidden,
    }))

    // Step 2: 递归查找最后一个可见列
    const findLastVisibleIndex = (arr, index = arr.length - 1) => {
      if (index < 0) return -1 // 边界条件
      return arr[index].hidden ? findLastVisibleIndex(arr, index - 1) : index
    }

    // Step 3: 应用宽度规则
    const targetIndex = findLastVisibleIndex(withDefaultWidth)
    if (targetIndex !== -1) {
      withDefaultWidth[targetIndex].width = 'auto'
    }

    return withDefaultWidth
  }

  function adjustColumnWidthTwo(data) {
    // 深拷贝原始数据避免污染原数组
    const clonedData = data.map((item) => ({ ...item }))

    // Step 1: 设置默认宽度
    const withDefaultWidth = clonedData.map((item) => ({
      ...item,
      width: item.width || 200, // 保留已有width或设置默认值
      isUnChecked: item?.isUnChecked,
    }))

    // Step 2: 递归查找最后一个可见列
    const findLastVisibleIndex = (arr, index = arr.length - 1) => {
      if (index < 0) return -1 // 边界条件
      return arr[index].isUnChecked ? findLastVisibleIndex(arr, index - 1) : index
    }

    // Step 3: 应用宽度规则
    const targetIndex = findLastVisibleIndex(withDefaultWidth)
    if (targetIndex !== -1) {
      withDefaultWidth[targetIndex].width = 'auto'
    }

    return withDefaultWidth
  }

  const clearFilter = (i) => {
    filterCondition.value.splice(i, 1)
  }
  const sortFn = (data) => {
    const { order, prop } = data
    Object.assign(orderBy.value, [
      {
        field: prop,
        order: order === 'descending' ? 'DESC' : 'ASC',
      },
    ])
    onSearch()
  }
  // 重置
  const resetFn = (() => {
    const data = JSON.parse(JSON.stringify(state.filterSearch))
    return () => {
      state.filterSearch = JSON.parse(JSON.stringify(data))
    }
  })()
  getTableHeadTitles()
  onSearch()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      .layout-box {
        display: flex;
        flex: 1;
        overflow: hidden;
      }
      &-table {
        gap: 10px;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        background: var(--100, #fff);
        border-radius: 0px 0px 2px 2px;
        .page-top {
          display: flex;
          align-items: center;
          align-self: stretch;
          justify-content: space-between;
          padding: 8px;
        }
        .btn {
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 4px 16px;
          color: #1e89ff;
          font-size: 14px;
          border: 1px solid #1e89ff;
          border-radius: 2px;

          &.active,
          &:hover {
            color: #fff;
            background: #1e89ff;
            box-shadow: none;
          }
        }
        .btns {
          display: flex;
          .line {
            display: flex;
            gap: 4px;
            align-items: center;
            padding: 4px 16px;
            color: #1e89ff;
            font-size: 14px;
            border-radius: 2px;
            cursor: pointer;
            &.active,
            &:hover {
              color: #006dea;
              box-shadow: none;
            }
          }
        }
        .table-box {
          height: calc(100% - 46px);
        }
      }
    }
  }

  .page-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    width: 100%;
    height: 46px;
    padding: 0 16px;
    color: #1d2129;
    font-weight: bolder;
    font-size: 16px;
    background-color: #fff;
    border-radius: 2px;
    &[sub-label]::after {
      display: inline-block;
      margin-left: 12px;
      padding: 0px 4px;
      color: #1aa4ee;
      font-weight: 400;
      font-size: 12px;
      font-family: 'Source Han Sans CN';
      font-style: normal;
      line-height: 22px;
      line-height: 20px;
      background: rgba(26, 164, 238, 0.08);
      border: 1px solid rgba(26, 164, 238, 0.4);
      border-radius: 2px;
      content: attr(sub-label);
    }
    &:before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 4px;
      height: 18px;
      margin: auto;
      background: #1e89ff;
      content: '';
    }

    .detail-back-box {
      position: absolute;
      top: 0;
      right: 16px;
      bottom: 0;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 30px;
      margin: auto;
      color: #1d2129;
      font-weight: normal;
      font-size: 14px;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      cursor: pointer;
      &:hover {
        color: #479dff;
        border: 1px solid #479dff;
      }
    }
  }

  .filter {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    width: 912px;
    padding: 8px;
    &-item {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 16px;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      width: 100%;
      height: 32px;
      > div {
        width: 100%;
      }
    }
    .btns {
      display: flex;
      gap: 8px;
      align-items: flex-start;
      padding: 8px 0px;
      .btn.active {
        box-shadow: none;
      }
    }
  }
</style>
<style lang="scss"></style>
