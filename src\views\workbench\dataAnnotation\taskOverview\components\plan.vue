<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="false"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
    >
    </CfTable>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchPlanList } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    tableData: { list: [{ name: '车间' }] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'name', name: '计划名称' },
      { prop: 'timeNode', name: '计划节点' },
      { prop: 'content', name: '计划内容描述', width: 'auto' },
      { prop: 'progress', name: '当前进展' },
      { prop: 'responsibleDepartment', name: '责任单位' },
      { prop: 'responsiblePerson', name: '责任人' },
    ],
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchPlanList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
