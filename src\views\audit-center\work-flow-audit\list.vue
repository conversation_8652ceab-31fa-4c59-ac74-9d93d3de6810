<template>
  <section class="container" ref="tableRef">
    <section class="tools">
      <div class="row">
        <div class="col">
          工作流名称：
          <n-input
            v-model="state.formInline.name"
            placeholder="请输入工作流名称"
            size="small"
            clearable
            @clear="onSearch(true)"
          />
          责任人：
          <n-tree-select
            v-model="state.formInline.personInCharge"
            filter
            allowClear
            placeholder="请选择"
            :leafOnly="true"
            :key="state.personOpt"
            :treeData="state.personOpt"
            :prop="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
          >
            <template #icon="{ nodeData, toggleNode }">
              <span
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '14.5px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="12"
                  height="12"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="#8a8e99"
                  />
                </svg>
              </span>
            </template>
            <template #default="{ item }">
              {{ item.name }}
            </template>
          </n-tree-select>
          提交人：
          <n-tree-select
            v-model="state.formInline.person"
            filter
            allowClear
            placeholder="请选择"
            :leafOnly="true"
            :key="state.personOpt"
            :treeData="state.personOpt"
            :prop="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
          >
            <template #default="{ item }">
              {{ item.name }}
            </template>
          </n-tree-select>
          提交时间：
          <n-range-date-picker-pro
            class="createTime"
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="tabs-box">
        <div class="tabs">
          <div :class="['tab', state.tabActive ? 'active' : '']" @click="tabChange(true)"
            >待审批</div
          >
          <div :class="['tab', !state.tabActive ? 'active' : '']" @click="tabChange(false)"
            >已审批</div
          >
        </div>

        <n-button v-if="state.tabActive" variant="text" color="primary" @click="allApprove('all')"
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            style="margin-right: 4px"
          >
            <path
              d="M2 12.6249L2.00173 9.64089C2.00207 9.4339 2.16992 9.26612 2.37694 9.2659C3.62715 9.2659 4.87739 9.2659 6.12762 9.2659C6.47318 9.2659 6.47135 8.95938 6.47135 8.22963C6.47135 7.49989 4.63326 6.88523 4.63326 4.31977C4.63326 1.75431 6.53742 1 8.11994 1C9.70247 1 11.4261 1.75431 11.4261 4.31977C11.4261 6.88523 9.59769 7.29312 9.59769 8.22963C9.59769 9.16611 9.59769 9.2659 9.89041 9.2659C11.1353 9.2659 12.3801 9.2659 13.625 9.2659C13.8321 9.2659 14 9.43382 14 9.64089V12.6249H2Z"
              stroke="currentColor"
              stroke-linejoin="round"
            />
            <path
              d="M2 15H14"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          一键审批</n-button
        >
      </div>

      <div v-loading="state.loading" class="table">
        <template v-if="state.tabActive">
          <CfTable
            :key="state.tableKey"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.tableHeadTitle"
            isNeedSelection
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #cnName="{ row }">
              {{ row.infoBO.name }}
            </template>
            <template #dispatchFreq="{ row }">
              <span :title="changeChinese(row.infoBO)">{{ changeChinese(row.infoBO) }}</span>
            </template>

            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="seeFn(row)">查看</n-button>
              <n-button variant="text" color="primary" @click="allApprove(row)">审批</n-button>
              <n-button variant="text" color="primary" @click="state.progressId=row.id;state.progressShow=true">进度</n-button>
            </template>
          </CfTable>
        </template>

        <template v-else>
          <CfTable
            :key="state.tableKey"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.auditTableHead"
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #cnName="{ row }">
              {{ row.infoBO.name }}
            </template>
            <template #name="{ row }">
              {{ row.infoBO.personInChargeName }}
            </template>
            <template #dispatchFreq="{ row }">
              <span :title="changeChinese(row.infoBO)">{{ changeChinese(row.infoBO) }}</span>
            </template>
            <template #auditStatusName="{ row }">
              <i
                :style="{
                  background: row.auditStatusName === '审核通过' ? '#2CA340' : '#F63838',
                  width: '6px',
                  height: '6px',
                  display: 'inline-block',
                  'margin-right': '6px',
                  'border-radius': '20px',
                }"
                class="status"
              ></i>
              {{ row.auditStatusName === '审核通过' ? '通过' : '驳回' }}
            </template>
          </CfTable>
        </template>
      </div>
    </section>
  </section>

    <Progress v-if="state.progressShow" :id="state.progressId" @close="state.progressShow = false" />


  <comPop
    v-if="state.auditPopShow"
    title="工作流审批"
    width="540"
    @onClose="onClosePop"
    @onConfirm="onConfirmAudit"
  >
    <n-form
      :data="state.auditForm"
      ref="auditformRef"
      labelSuffix="："
      label-width="100px"
      message-type="text"
      style="width: 100%"
    >
      <n-form-item field="radio" label="审批结果">
        <n-radio-group direction="row" v-model="state.auditForm.radio">
          <n-radio value="0">通过</n-radio>
          <n-radio value="1">驳回</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item field="description" label="审批备注">
        <n-textarea v-model="state.auditForm.description" rows="5" placeholder="请输入描述信息" />
      </n-form-item>
    </n-form>
  </comPop>

  <comPop
    v-if="state.auditDetailPopShow"
    title="详情"
    width="540"
    class="detailPop"
    @onClose="state.auditDetailPopShow = false"
    @onConfirm="state.auditDetailPopShow = false"
  >
    <n-row>
      <n-col class="title" :span="8"
        >版本：<span class="comName">V{{ state.detailForm.version }}</span></n-col
      >
      <n-col class="title" :span="8"
        >责任人：<span class="comName">{{ state.detailForm.personInChargeName }}</span></n-col
      >
      <n-col class="title" :span="8"
        >启动时间：<span class="comName">{{ state.detailForm.failRetryTimes }}</span></n-col
      >
    </n-row>

    <div class="sqlBox">
      <pre id="sql" class="sql">
        {{
          state.detailForm.dataAsyncTaskBO ||
          state.detailForm.dataUploadTaskBO ||
          state.detailForm.hiveDdlTaskBO ||
          state.detailForm.hiveSqlTaskBO ||
          state.detailForm.pySparkTaskBO ||
          state.detailForm.pythonTaskBO ||
          state.detailForm.shellTaskBO ||
          state.detailForm.sparkSqlTaskBO
        }}
      </pre>
      <n-button variant="text" color="primary" @click="copyFn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <g clip-path="url(#clip0_1511_75362)">
            <path
              d="M1 6C1 5.44772 1.44772 5 2 5H10C10.5523 5 11 5.44772 11 6V14C11 14.5523 10.5523 15 10 15H2C1.44772 15 1 14.5523 1 14V6Z"
              stroke="#1E89FF"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M4.5 2C4.5 1.17157 5.17157 0.5 6 0.5H14C14.8284 0.5 15.5 1.17157 15.5 2V10C15.5 10.8284 14.8284 11.5 14 11.5H12.5C12.2239 11.5 12 11.2761 12 11C12 10.7239 12.2239 10.5 12.5 10.5H14C14.2761 10.5 14.5 10.2761 14.5 10V2C14.5 1.72386 14.2761 1.5 14 1.5H6C5.72386 1.5 5.5 1.72386 5.5 2V3.5C5.5 3.77614 5.27614 4 5 4C4.72386 4 4.5 3.77614 4.5 3.5V2Z"
              fill="#1E89FF"
            />
          </g>
          <defs>
            <clipPath id="clip0_1511_75362">
              <rect width="16" height="16" fill="white" />
            </clipPath>
          </defs>
        </svg>
        复制</n-button
      >
    </div>
  </comPop>

  <comPop
    v-if="state.auditSeePopShow"
    title="业务流程提交详情"
    width="840"
    class="detailPop"
    @onClose="state.auditSeePopShow = false"
    @onConfirm="state.auditSeePopShow = false"
  >
    <CfTable
      :key="state.tableKey"
      ref="tableNoRef"
      :tableConfig="{
        data: state.auditSeeDatasource,
        rowKey: 'id',
      }"
      :table-head-titles="[
        { prop: 'name', name: '作业名称' },
        { prop: 'taskTypeName', name: '作业类型', slot: 'taskTypeName' },
        { prop: 'personInChargeName', name: '责任人', slot: 'personInChargeName' },
        { prop: 'operateType', name: '操作类型', slot: 'operateType' },
        // { prop: 'version', name: '提交版本', slot: 'version' },
      ]"
    >
      <template #taskTypeName="{ row }">
        {{ row?.taskTypeName || '--' }}
      </template>
      <template #personInChargeName="{ row }">
        {{ row?.personInChargeName || '--' }}
      </template>
      <template #operateType="{ row }">
        {{ operateType[row?.operateType] || '--' }}
      </template>
      <template #version="{ row }">
        <span class="version" @click="detail(row)">V{{ row?.version }}</span>
      </template>
    </CfTable>
  </comPop>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import { formartTime } from '@/utils/index'
  import comPop from '@comp/comPop/index.vue'
    import Progress from '../components/Progress.vue'
  import { getAuditWorkFlowList, workFlowApproval, workFlowVersion } from '@/api/auditCenter.js'
  import api from '@/api/index.js'

  /**
   * 数据部分
   */
  const tableRef = ref()
  const tableNoRef = ref()

  const operateType = {
    NEW: '新增',
    UPDATE: '变更',
    DELETED: '删除',
    NO_UPDATE: '未变更',
  }
  const state = reactive({
    loading: false,
    auditPopShow: false,
    auditDetailPopShow: false,
    auditSeePopShow: false,
    progressShow: false,
    progressId:'',
    tabActive: true,
    tableKey: 1,
    tableHeight: 500,
    formInline: {
      name: '',
      personInCharge: '',
      person: '',
      time: '',
    },
    tableHeadTitle: [
      { prop: 'cnName', name: '业务流程', slot: 'cnName' },
      { prop: 'dispatchFreq', name: '调度频率', slot: 'dispatchFreq' },
      { prop: 'applyByName', name: '提交人' },
      { prop: 'applyTime', name: '提交时间' },
      { prop: 'auditByName', name: '提交版本' },
    ],
    auditTableHead: [
      { prop: 'cnName', name: '业务流程', slot: 'cnName' },
      { prop: 'name', name: '责任人', slot: 'name' },
      { prop: 'dispatchFreq', name: '调度频率', slot: 'dispatchFreq' },
      { prop: 'applyByName', name: '提交人' },
      { prop: 'applyTime', name: '提交时间' },
      { prop: 'auditByName', name: '审批人' },
      { prop: 'auditTime', name: '审批时间' },
      { prop: 'auditStatusName', name: '审批结果', slot: 'auditStatusName' },
    ],
    auditSeeDatasource: [],
    auditForm: {
      radio: '0',
      description: '',
    },
    detailForm: {},
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    personOpt: [],
    auditArr: [],
  })
  onMounted(() => {
    state.tableHeight = tableRef.value.clientHeight - 206
    onSearch()
    getDeptPresonFn()
  })

  const onSearch = async () => {
    let { time, name, person, personInCharge } = state.formInline

    let startTime = ''
    let endTime = ''
    if (time) {
      if (time[0]) {
        startTime = formartTime(time[0])
      }
      if (time[1]) {
        endTime = formartTime(time[1], true)
      }
    }

    const params = {
      condition: {
        // applyType: state.tabActive ? 'PUBLISH' : 'DONE',
        endTime: endTime === null ? '' : endTime,
        jobType: 'OFFLINE',
        name,
        applyBy: person,
        personInCharge,
        startTime: startTime === null ? '' : startTime,
        status: state.tabActive ? 'WAIT' : 'DONE',
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    }
    state.loading = true
    const res = await getAuditWorkFlowList(params)
    if (res.success && res.data) {
      state.dataSource = res.data.list
      state.page.total = res.data.total
      state.tableKey++
    }
    state.loading = false
  }

  const resetFn = () => {
    state.formInline = {
      name: '',
      person: '',
      time: '',
      personInCharge: '',
    }
    onSearch(true)
  }

  const seeFn = (data) => {
    state.auditSeePopShow = true

    state.auditSeeDatasource = data.infoBO.taskList
  }

  const allApprove = (type) => {
    if (tableNoRef.value.getSelectionRows().length === 0 && type === 'all') {
      ElMessage.error('请选择要审批的数据')
      return false
    }
    state.auditPopShow = true
    state.auditArr = type
  }

  // 审批接口
  const workFlowApprovalFn = async (data) => {
    const arr = []
    if (state.auditArr === 'all') {
      tableNoRef.value.getSelectionRows().forEach((item) => {
        arr.push({
          auditComment: state.auditForm.description,
          auditStatus: data.status,
          bizType: 'OFFLINE',
          id: item.id,
        })
      })
    } else {
      arr.push({
        auditComment: state.auditForm.description,
        auditStatus: data.status,
        bizType: 'OFFLINE',
        id: state.auditArr.id,
      })
    }

    const params = arr
    const res = await workFlowApproval(params)
    if (res.success) {
      data.status === 'PASS' ? ElMessage.success('审批通过') : ElMessage.success('审批驳回')
      onSearch()
    }
  }

  const detail = async (data) => {
    const params = {
      taskId: data.id,
      version: data.version,
    }
    const res = await workFlowVersion(params)
    if (res.success) {
      state.auditDetailPopShow = true
      state.detailForm = res.data
    }
  }

  // 获取部门人员
  const getDeptPresonFn = async () => {
    const res = await api.project.getThreeDepartment({})
    let { success, data } = res
    if (success && data !== null) {
      state.personOpt = data
    }
  }

  const tabChange = (status) => {
    if (state.loading || state.tabActive === status) {
      return false
    }
    state.dataSource = []
    state.tabActive = status
    onSearch()
  }

  const copyFn = (e) => {
    const element = document.getElementById('sql')
    const textToCopy = element.textContent
    const tempTextArea = document.createElement('textarea')
    tempTextArea.value = textToCopy
    document.body.appendChild(tempTextArea)
    tempTextArea.select()
    document.execCommand('copy')
    document.body.removeChild(tempTextArea)
    ElMessage.success('文本复制成功！')
  }

  const onClosePop = () => {
    state.auditPopShow = false
  }
  const onConfirmAudit = async () => {
    if (state.auditForm.radio === '0') {
      await workFlowApprovalFn({ status: 'PASS' })
    } else {
      await workFlowApprovalFn({ status: 'REJECT' })
    }
    state.auditPopShow = false
  }

  // 调度频率转化
  const changeChinese = (data, rateTime = false) => {
    let words = ''

    if (data?.collectJobsInfo?.length > 0) {
      let arr = data?.collectJobsInfo.map((item) => item.name)
      return '前置任务 | ' + arr.join(',')
    }
    let rateTime_words = data?.schedule?.fromDateTime
      ? data?.schedule?.fromDateTime + ' - ' + data?.schedule?.thruDateTime
      : '--'

    if (data?.schedule?.isCron) {
      words = data?.schedule?.cron
    } else {
      switch (data?.schedule?.period) {
        case 'hour':
          words = `间隔 | ${data?.schedule?.extent}小时`
          break
        case 'day':
          words = `每日 | ${data?.schedule?.rateTime}`
          break
        case 'week':
          words = `每周${weekForChinese(Number(data?.schedule?.extent))} | ${
            data?.schedule?.rateTime
          }`
          break
        case 'month':
          words = `每月${data?.schedule?.extent}号 | ${data?.schedule?.rateTime}`
          break
      }
    }
    return rateTime ? rateTime_words : words
  }
  const weekForChinese = (num) => {
    let word = '一'
    switch (num) {
      case 1:
        word = `一`
        break
      case 2:
        word = `二`
        break
      case 3:
        word = `三`
        break
      case 4:
        word = `四`
        break
      case 5:
        word = `五`
        break
      case 6:
        word = `六`
        break
      case 7:
        word = `日`
        break
    }
    return word
  }

  const pageChange = (pageNum) => {
    state.page.pageNum = pageNum === 0 ? 1 : pageNum
    state.page.pageNum = pageNum
    onSearch()
  }
  const pageSizeChange = (pageSize) => {
    state.page.pageNum = 1
    state.page.pageSize = pageSize
    onSearch()
  }
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .tools {
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 10px 8px 10px 16px;
      .createTime {
        margin-right: 32px;
        width: 260px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          background: #1e89ff;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
        // border-bottom: 1px solid #c5d0ea;
      }
    }
  }

  .content {
    position: relative;
    height: calc(100% - 62px);
    background: #fff;
    margin-top: 10px;
    border-radius: 2px;
  }

  .tabs-box {
    display: flex;
    padding: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    height: 48px;

    .tabs {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      align-self: stretch;
    }

    .tab {
      display: flex;
      width: 60px;
      height: 22px;
      box-sizing: content-box;
      padding: 5px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 2px 0px 0px 2px;
      background: #fff;
      font-size: 14px;
      color: #1d2129;
      border: 1px solid #dcdfe6;
      cursor: pointer;

      &.active {
        color: #fff;
        border-color: #1e89ff;
        background: #1e89ff;
      }
    }
  }

  .table {
    height: calc(100% - 48px);
    .expand-table {
      position: relative;
      :deep(.nancalui-table__container) {
        position: relative;
        min-height: 100px;
        .nancalui-table__empty {
          top: 70px;
        }
      }
    }
  }

  .nancalui-table-page {
    border-top: 1px solid #dcdfe6;
  }

  .nancalui-pagination {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    justify-content: flex-end;
    padding: 14px 16px;
  }
  .nancalui-table-page {
    border-top: 1px solid #dcdfe6;
  }

  .detailPop {
    position: relative;

    .title {
      font-size: 14px;
      color: #606266;
    }
    .comName {
      color: #1d2129;
    }
    .sqlBox {
      display: flex;
      padding: 8px 6px 8px 10px;
      margin-top: 13px;
      flex-direction: column;
      align-items: flex-end;
      gap: 6px;
      flex: 1 0 0;
      border-radius: 2px;
      border: 1px solid var(---, #e5e6eb);
      background: var(--100, #fff);

      .sql {
        height: 271px;
        color: var(----, #1d2129);
        font-size: 14px;
        width: 100%;
        margin: 0;
        overflow: auto;
      }

      svg {
        margin-right: 4px;
      }
    }
  }

  :deep(.version) {
    font-size: 14px;
    color: #1e89ff;
    cursor: pointer;
  }
  :deep(.nancalui-table__empty) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  :deep(.nancalui-table .nancalui-table__thead .header-container .title) {
    font-weight: 400;
  }
</style>
