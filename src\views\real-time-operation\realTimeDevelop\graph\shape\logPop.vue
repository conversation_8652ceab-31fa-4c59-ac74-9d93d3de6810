<template>
  <teleport to="body">
    <section class="logPop" v-if="showDirDialog">
      <div class="box">
        <div class="title">日志</div>
        <pre class="log">{{ state.log }}</pre>
        <div class="footer">
          <button @click.prevent="showDirDialog = false">取 消</button>
          <button @click.prevent="showDirDialog = false">确 定</button>
        </div>
      </div>
    </section>
  </teleport>
</template>

<script setup>
  import { taskRunResult } from '@/api/dataManage.js'
  const showDirDialog = ref(false)
  const state = reactive({
    log: '',
  })

  const getLogFunc = async (dsProcessCode, nodeCode) => {
    const res = await taskRunResult({
      dsProcessCode,
      nodeCode,
    })
    if (res.code === 'SUCCESS') {
      state.log = res.data.log
    }
  }

  defineExpose({
    open(node) {
      getLogFunc(sessionStorage.getItem('dsProcessCode'), node.nodeCode)
      showDirDialog.value = true
    },
  })
</script>
<style lang="scss" scoped>
  .logPop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    .box {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 550px;
      background: #fff;
      border-radius: 2px;
    }

    .title {
      position: relative;
      padding: 13px 16px;
      color: rgba(0, 0, 0, 0.9);
      font-weight: 500;
      font-size: 16px;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }
    .log {
      overflow: auto;
      height: 300px;
      border: 1px solid #e5e6eb;
      border-radius: 2px;
      margin: 10px;
      word-wrap: break-word; /* 旧版本浏览器支持 */
      overflow-wrap: break-word; /* 标准属性 */
    }
    .footer {
      padding: 10px;
      text-align: right;
      button {
        width: 62px;
        height: 32px;
        border: 1px solid #e5e6eb;
        color: rgba(0, 0, 0, 0.9);

        &:nth-child(2) {
          background: #1e89ff;
          border-color: #1e89ff;
          color: #fff;
          margin-left: 10px;
        }
      }
    }
  }
</style>
