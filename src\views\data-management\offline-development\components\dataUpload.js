
export const confidentialityLevelOptions = [
    { name: '公开', value: 'PUBL<PERSON>' },
    { name: '内部', value: 'INTERIOR' },
    { name: '受控', value: 'CONTROLLED' },
    { name: '秘密', value: 'SECRET' },
    { name: '机密', value: 'CONFIDENTIAL' },
    { name: '核心', value: 'CORE' },
]
export const colorList = [
    { value: '#1F84E1', bgColor: '#EDF5FD', checked: true },
    { value: '#23BD4E', bgColor: '#EDFAF1', checked: false },
    { value: '#FFA800', bgColor: '#FFF6E5', checked: false },
    { value: '#5D5FEF', bgColor: '#F2F2FE', checked: false },
    { value: '#B7C1C7', bgColor: '#F9FAFB', checked: false },
]

export const cNameBlur = (data) => {
    let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,30}$/
    let res = regex.test(data.targetName)
    data.isPass2 = false
    if (res && data.targetName?.length > 1 && data.targetName?.length < 31) {
        data.isPass2 = true
    }
}
