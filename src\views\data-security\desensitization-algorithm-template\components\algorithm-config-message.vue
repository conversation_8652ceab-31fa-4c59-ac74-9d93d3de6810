<template>
  <!-- 脱敏算法配置信息 -->
  <div class="algorithm-config-message">
    <n-form
      ref="form"
      class="form__label_font14"
      :data="state.form"
      label-width="82px"
      :rules="state.rules"
      label-align="start"
      :pop-position="['right']"
    >
      <div v-if="state.desensitizer === 'HMAC_SHA256_HASH' || state.desensitizer === 'SHA256_HASH'">
        <div v-if="state.desensitizer === 'HMAC_SHA256_HASH'">
          <n-form-item label="秘钥：" field="secKey">
            <n-input v-model="state.form.secKey" maxlength="200" placeholder="请输入" clearable />
          </n-form-item>
          <div class="item-tips">算法使用前必须先配置密钥，此算法才能正常使用。</div>
        </div>
        <n-form-item label="盐值：" class="label_align_required">
          <n-input v-model="state.form.salt" maxlength="200" placeholder="请输入" clearable />
        </n-form-item>
        <div class="item-tips"
          >算法盐值由您自行配置，而非系统给出的安全随机数，哈希算法存在不可逆性，请关注相应使用风险。</div
        >
      </div>
      <div v-if="state.desensitizer === 'NUMERIC_TYPE_TRUNCATION'">
        <n-form-item label="X：" field="reserveX">
          <n-input-number
            v-model="state.form.reserveX"
            :min="1"
            :max="100"
            placeholder="请输入"
          ></n-input-number>
        </n-form-item>
        <div class="item-tips"
          >保留小数点前x位，将小数点前第1到x-1位、小数点后的位数全部截断并填补为0</div
        >
      </div>
      <div v-if="state.desensitizer === 'DATE_TYPE_TRUNCATION'">
        <n-form-item label="日期格式：" field="desensitizationDateType">
          <n-select
            v-model="state.form.desensitizationDateType"
            :options="state.dateFormatOptions"
            @value-change="desensitizationDateTypeChange"
          />
        </n-form-item>
        <n-form-item label="截断位置：" field="truncPosition">
          <n-select
            :key="state.key"
            v-model="state.form.truncPosition"
            :options="
              state.form.desensitizationDateType
                ? state.truncPositionOptions[state.form.desensitizationDateType]
                : []
            "
          />
        </n-form-item>
        <div class="item-tips">截断日期到指定位置</div>
      </div>
      <div
        v-if="
          state.desensitizer === 'FROM_X_TO_Y_MASK' ||
          state.desensitizer === 'RESERVE_FROM_X_TO_Y_MASK'
        "
      >
        <n-form-item label="X：" field="from">
          <n-input-number
            v-model="state.form.from"
            :min="1"
            :max="state.form.to ? state.form.to : Infinity"
            placeholder="请输入"
          ></n-input-number>
        </n-form-item>
        <n-form-item label="Y：" field="to">
          <n-input-number
            v-model="state.form.to"
            :min="state.form.from ? state.form.from : 1"
            placeholder="请输入"
          ></n-input-number>
        </n-form-item>
        <div v-if="state.desensitizer === 'FROM_X_TO_Y_MASK'" class="item-tips"
          >掩码字符串第X至第Y位字符</div
        >
        <div v-else class="item-tips">保留字符串第X至第Y位字符</div>
      </div>
      <div
        v-if="
          state.desensitizer === 'FIRST_M_AND_LAST_N_MASK' ||
          state.desensitizer === 'RESERVE_FIRST_M_AND_LAST_N_MASK'
        "
      >
        <n-form-item label="m：" field="mInTheFront">
          <n-input-number
            v-model="state.form.mInTheFront"
            :min="1"
            placeholder="请输入"
          ></n-input-number>
        </n-form-item>
        <n-form-item label="n：" field="nInTheBack">
          <n-input-number
            v-model="state.form.nInTheBack"
            :min="1"
            placeholder="请输入"
          ></n-input-number>
        </n-form-item>
        <div v-if="state.desensitizer === 'FIRST_M_AND_LAST_N_MASK'" class="item-tips"
          >掩码字符串前m后n位字符</div
        >
        <div v-else class="item-tips">保留字符串前m后n位字符</div>
      </div>
    </n-form>
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { checkCName } from '@/utils/validate'
  const router = useRouter()
  const form = ref()
  const props = defineProps({
    desensitizer: {
      type: String,
      default: '',
    },
  })
  const state = reactive({
    desensitizer: '',
    key: 1,
    form: {
      desensitizationDateType: null,
      truncPosition: null,
      salt: null,
      secKey: null,
      from: null,
      to: null,
      mInTheFront: null,
      nInTheBack: null,
      reserveX: null,
    },
    dateFormatOptions: [
      {
        name: '日期',
        value: 'DATE',
      },
      {
        name: '日期时间',
        value: 'DATETIME',
      },
    ],
    truncPositionOptions: {
      DATE: [
        {
          name: '年',
          value: 'TRUNCATE_TO_YEAR',
        },
        {
          name: '月',
          value: 'TRUNCATE_TO_MONTH',
        },
        {
          name: '日',
          value: 'TRUNCATE_TO_DAY',
        },
      ],
      DATETIME: [
        {
          name: '年',
          value: 'TRUNCATE_TO_YEAR',
        },
        {
          name: '月',
          value: 'TRUNCATE_TO_MONTH',
        },
        {
          name: '日',
          value: 'TRUNCATE_TO_DAY',
        },
        {
          name: '时',
          value: 'TRUNCATE_TO_HOUR',
        },
        {
          name: '分',
          value: 'TRUNCATE_TO_MINUTE',
        },
        {
          name: '秒',
          value: 'TRUNCATE_TO_SECONDS',
        },
      ],
    },

    rules: {
      desensitizationDateType: [{ required: true, message: '请选择日期类型', trigger: 'change' }],
      truncPosition: [{ required: true, message: '请选择', trigger: 'change' }],
      secKey: [{ required: true, message: '请输入秘钥', trigger: 'blur' }],
      from: [{ type: 'number', required: true, message: '请输入', trigger: 'blur' }],
      to: [{ type: 'number', required: true, message: '请输入', trigger: 'blur' }],
      mInTheFront: [{ type: 'number', required: true, message: '请输入', trigger: 'blur' }],
      nInTheBack: [{ type: 'number', required: true, message: '请输入', trigger: 'blur' }],
      reserveX: [{ type: 'number', required: true, message: '请输入', trigger: 'blur' }],
    },
  })
  //日期类型change
  const desensitizationDateTypeChange = () => {
    state.form.truncPosition = null
    state.key++
  }

  // 校验是否通过
  const getAllData = () => {
    let passed = new Promise((resolve) => {
      form.value.validate((valid) => {
        resolve({
          passed: valid,
          data: state.form,
        })
      })
    })

    return passed
  }
  // 编辑时候回显
  const editInit = (data) => {
    let _data = Object.assign(data, {
      ...data.dateTruncConf,
      ...data.hashConf,
      ...data.maskPositionConf,
      ...data.numericalValueTruncConf,
    })
    Object.keys(state.form).forEach((key) => {
      state.form[key] = _data[key]
    })
  }
  watch(
    () => props.desensitizer,
    (val) => {
      state.desensitizer = val
      Object.keys(state.form).forEach((key) => {
        state.form[key] = null
      })
    },
    {
      immediate: true,
      deep: true,
    },
  )

  defineExpose({
    getAllData,
    editInit,
  })
</script>
<style lang="scss" scoped>
  //统一表单输入框样式
  $labelWidth: 70px;

  .algorithm-config-message {
    height: 100%;

    .nancalui-form {
      :deep(.nancalui-form__label) {
        align-self: self-start;
        .nancalui-form__label-span {
          height: 100%;
        }
      }
      :deep(.nancalui-form__control .nancalui-form__control-info) {
        position: inherit;
      }

      .nancalui-form__item--horizontal {
        width: 100%;
        margin-bottom: 8px;
        &.hide-item {
          :deep(.nancalui-form__label) {
            opacity: 0;
          }
        }
        :deep(.nancalui-input-number) {
          .nancalui-input__wrapper .nancalui-input__inner {
            text-align: left;
          }
          .nancalui-input__wrapper {
            width: 100%;
          }
          .nancalui-input-slot__suffix {
            display: none;
          }
        }
      }

      .item-tips {
        width: calc(100% - 82px);

        margin-bottom: 8px;
        margin-left: 82px;
        color: rgba(0, 0, 0, 0.55);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;

        &.blue {
          height: 32px;
          padding-left: 10px;
          color: #447dfd;
          line-height: 30px;
          background: #f0f7ff;
          border: 1px solid #96bdff;
          border-radius: 6px;

          svg {
            color: #fff;
          }
        }
      }
    }
  }
</style>
