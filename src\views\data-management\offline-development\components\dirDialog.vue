<template>
  <n-modal
    v-model="showDirDialog"
    title="新建目录"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item label="目录名称" field="name">
          <n-input v-model="state.syncForm.name" maxlength="500" placeholder="请输入名称" />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" v-loading="state.hasClickSave" @click.prevent="createDir"
          >确 定</n-button
        >
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import api from '@/api/index'
  import { checkCName } from '@/utils/validate'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({
    currentNode: null,
    hasClickSave: false,
    syncForm: { name: null },
    syncRules: {
      name: [
        {
          required: true,
          message: '请输入目录名称',
          trigger: 'blur',
        },
      ],
    },
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        if (state.hasClickSave) {
          return false
        }
        state.hasClickSave = true
        const { type, id } = state.currentNode || {}
        // if (!id) return ElMessage.error('请选择目录')
        api.offlineJob
          .createDirectory({
            name: state.syncForm.name,
            pid: id,
          })
          .then(({ success }) => {
            state.hasClickSave = false
            if (!success) return
            emit('success')
            showDirDialog.value = false
            ElMessage.success('创建成功')
          })
          .catch(() => {
            state.hasClickSave = false
          })
      }
    })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.currentNode = null
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(node) {
      resetForm()
      state.currentNode = node
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
  }
</style>
