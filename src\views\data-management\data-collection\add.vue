<template>
  <!-- 结构化数据采集  -->
  <div class="data-collection">
    <div
      :class="{
        'data-collection-add': true,
        container: true,
        isLzos: state.isLzos,
      }"
    >
      <div class="add-box">
        <div class="page-title">
          {{ (state.editId ? '编辑' : '新增') + '采集任务' }}
          <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
        </div>

        <div class="box-content scroll-bar-style">
          <div class="box-content-inside">
            <div class="inside-box">
              <div class="inside-box-content">
                <div class="content-title">
                  <span>基础信息</span>
                </div>
                <n-form
                  v-if="state.basicTitle"
                  ref="ruleForm"
                  class="base-form"
                  :data="state.ruleForm"
                  :rules="state.rules"
                  label-width="150px"
                  label-align="start"
                >
                  <div class="inline">
                    <div class="inline-left">
                      <n-form-item label="采集任务名称：" field="name">
                        <n-input
                          v-model="state.ruleForm.name"
                          placeholder="请输入采集任务名称"
                          maxlength="500"
                          :disabled="state.disabled"
                          @clear="collectNameClear"
                        />
                      </n-form-item>
                    </div>
                    <n-form-item label="描述信息：">
                      <n-textarea
                        v-model="state.ruleForm.description"
                        placeholder="请输入描述信息"
                        maxlength="200"
                        :autosize="{ minRows: 3 }"
                        resize="both"
                        show-count
                      />
                    </n-form-item>
                  </div>
                  <div class="inline">
                    <div class="inline-left">
                      <n-form-item label="最小内存：" field="executorMinMemory">
                        <n-select
                          v-model="state.ruleForm.executorMinMemory"
                          :options="state.memoryOptions"
                        />
                      </n-form-item>
                    </div>
                    <n-form-item label="最大内存：" field="executorMaxMemory">
                      <n-select
                        v-model="state.ruleForm.executorMaxMemory"
                        :options="state.memoryOptions"
                      />
                    </n-form-item>
                  </div>
                </n-form>
              </div>

              <div class="inside-box-content">
                <div class="content-title">
                  <span>{{
                    state.ruleForm.collectType === 'STRUCTURE' ? '选择表' : '选择文件'
                  }}</span>
                </div>
                <template v-if="state.selectFile">
                  <structure
                    ref="structureDom"
                    v-if="state.ruleForm.collectType === 'STRUCTURE'"
                    :envType="state.ruleForm.envType"
                    :listenEnvTypeChange="state.listenEnvTypeChange"
                    :id="state.editId"
                    @dataSourceTypeChange="dataSourceTypeChange"
                  />
                  <unstructure
                    v-else
                    ref="structureDom"
                    :envType="state.ruleForm.envType"
                    :listenEnvTypeChange="state.listenEnvTypeChange"
                    @dataSourceTypeChange="dataSourceTypeChange"
                  />
                </template>
              </div>
            </div>
            <!-- 配置信息 -->
            <div class="config-box inside-box">
              <div class="inside-box-content footer">
                <div class="content-title">
                  <span>调度信息</span>
                </div>
                <configureSchedulingRules
                  v-if="state.dispatchInfo"
                  ref="configureSchedulingRulesDom"
                  :dataSourceType="state.ruleForm.dataSourceType"
                  :showTaskRule="state.showTaskRule"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{
        'box-operate': true,
        isLzos: state.isLzos,
      }"
    >
      <n-button size="sm" @click.prevent="cancel">取消</n-button>
      <n-button
        class="save"
        :loading="state.loading"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="save"
        >确定</n-button
      >
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { checkCName500 } from '@/utils/validate'
  import {
    collectJobAdd,
    collectJobUpdate,
    collectJobDetail,
    collectJobPublish,
  } from '@/api/dataManage'
  import structure from './components/structure'
  import unstructure from './components/unstructure'
  import configureSchedulingRules from './components/configure-scheduling-rules'

  export default {
    name: '',
    components: {
      configureSchedulingRules,
      structure,
      unstructure,
    },
    props: {},
    setup() {
      const router = useRouter()
      const configureSchedulingRulesDom = ref()
      const ruleForm = ref()
      const structureDom = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        basicTitle: true,
        selectFile: true,
        dispatchInfo: true,
        sourceCategoryId: null,
        ruleForm: {
          name: '',
          description: '',
          collectType: 'STRUCTURE',
          dataSourceType: '',
          envType: 'OFFICIAL',
          executorMinMemory: 1,
          executorMaxMemory: 2,
        },
        rules: {
          name: [
            {
              required: true,
              validator: (...args) =>
                // checkCName500(...args, 'dataManagement', 'collectTaskNameOnly', {
                checkCName500(...args, null, null, {
                  name: state.ruleForm.name,
                  id: state.editId || null,
                }),
              trigger: 'blur',
            },
          ],
          envType: [{ required: true, message: '请选择任务环境', trigger: 'change' }],
          collectType: [{ required: true, message: '请选择采集类型', trigger: 'change' }],
        },
        memoryOptions: [
          { name: '1G', value: 1 },
          { name: '2G', value: 2 },
          { name: '3G', value: 3 },
          { name: '4G', value: 4 },
          { name: '6G', value: 6 },
          { name: '8G', value: 8 },
        ],
        disabled: false, //非创建状态下不调用逆向模型接口
        loading: false,
        allData: {},
        editId: null,
        sinkModelCreateMethod: 'REVERSE', //建模方式
        collectTaskNameOnly: false, //重名校验是否通过
        showTaskRule: true,
        listenEnvTypeChange: true, //是否需要监听envTypeChange
        envTypeOptions: [
          {
            name: '正式环境',
            value: 'OFFICIAL',
          },
          {
            name: '开发环境',
            value: 'TEST',
          },
        ],
      })

      const methods = {
        expandTitleFn(name) {
          state[name] = !state[name]
        },
        closeFn() {
          router.push({
            name: 'dataCollectionIndex',
            query: {},
          })
        },
        collectTypeChange(data) {
          if (data === 'UNSTRUCTURE') {
            state.showTaskRule = false
          } else {
            state.showTaskRule = true
          }
        },
        envTypeChange() {
          state.listenEnvTypeChange = true
        },
        //获取数据库类型
        dataSourceTypeChange(data) {
          state.ruleForm.dataSourceType = data.dataSourceType
        },
        //采集任务同名校验
        collectNameCheck() {
          // 名称唯一
          return new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              if (valid) {
                let _data = {
                  name: state.ruleForm.name,
                  id: state.editId || null,
                }
                state.collectTaskNameOnly = false
                if (state.ruleForm.name) {
                  api.dataManagement.collectTaskNameOnly(_data).then((res) => {
                    let { success } = res
                    if (success) {
                      state.collectTaskNameOnly = success
                    }

                    resolve()
                  })
                }
              } else {
                return false
              }
            })
          })
        },
        //清空采集任务名
        collectNameClear() {
          state.collectTaskNameOnly = false
        },

        // 采集任务保存
        async save() {
          // let passed = false
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          if (state.ruleForm.executorMinMemory > state.ruleForm.executorMaxMemory) {
            ElNotification({
              title: '提示',
              message: '最大内存不能小于最小内存！',
              type: 'error',
            })
            _result.passed = false
          }
          if (!_result.passed) return
          let result = await structureDom.value.getAllData()
          let scheduling_result = await configureSchedulingRulesDom.value.getAllData()
          if (!(scheduling_result.passed && result.passed)) return
          let mergeAllData = { ...result.data, ...scheduling_result.data, ...state.ruleForm }
          state.loading = true

          if (mergeAllData.columnMapping) {
            mergeAllData.columnMapping.forEach((val) => {
              val.sinkMetaCode = val.sinkMetaCode.toLowerCase()
            })
          }

          if (mergeAllData.sinkModelCreateMethod === 'REVERSE') {
            //逆向模型
            let {
              dataSourceId,
              sourceTableName,
              sinkModelEname,
              sinkModelCname,
              sinkModelId,
              tableData,
              layerId,
              collectRule,
              dataSourceName,
            } = mergeAllData

            //逆向模型
            let _fieldList = []
            let allData = {}

            tableData.forEach((element, index) => {
              _fieldList.push({
                cnName: element.cnName,
                name: element.name.toLowerCase(),
                description: '',
                fieldLength: element.fieldLength,
                fieldType: element.fieldType,
                sortNum: index,
              })
            })

            allData.cnName = sinkModelCname
            allData.name = sinkModelEname
            allData.layerId = layerId
            allData.fieldList = _fieldList
            allData.useJobType = 'COLLECT_JOB'
            allData.collectionType = collectRule
            //8月新增参数
            allData.fromDatasourceId = dataSourceId
            allData.fromDatasourceName = dataSourceName
            allData.fromDatasourceTable = sourceTableName.split('.')[0] || ''
            allData.createMethod = 'REVERSE'
            allData.envType = state.ruleForm.envType

            if (sinkModelId) {
              allData.id = sinkModelId
            }

            if (!state.disabled) {
              //逆向模型
              // const reverseWithMetadata = await api.dataManagement.reverseWithMetadata(allData)
              // if (!reverseWithMetadata.success) {
              //   state.loading = false
              //   return
              // }
              // mergeAllData.sinkModelId = reverseWithMetadata.data.id
              mergeAllData.sinkModelId = null
            } else {
              //创建状态
              mergeAllData.sinkModelId = sinkModelId
            }

            delete mergeAllData.layerId

            delete mergeAllData.dataSourceName
            delete mergeAllData.tableData
          } else {
            //映射关系
            delete mergeAllData.graphData
            delete mergeAllData.allEdge
            delete mergeAllData.datasourceType
          }
          if (mergeAllData.schedule && mergeAllData.schedule.ruleForm) {
            delete mergeAllData.schedule.ruleForm
          }
          const dataTag = []
          mergeAllData?.tagList?.forEach((val) => {
            if (val.color != null) {
              dataTag.push(val)
            }
          })

          //创建任务
          let newData = {
            dataTag,
            description: mergeAllData.description || null,
            destinationEntry: mergeAllData.sinkModelEname || null,
            destinationEntryName: mergeAllData.sinkModelCname || null,
            id: state.editId || null,
            mappingList: mergeAllData.columnMapping.map((val) => {
              return {
                sourceCode: val.sourceColumnName,
                sourceDataLength: val.sourceColumnLength,
                sourceDataType: val.sourceColumnType,
                sourceName: val.sourceName || null,
                sourceOrderNum: val.sinkSortNum,
                targetCode: val.sinkMetaCode,
                targetDataLength: val.sinkMetaLength,
                targetDataType: val.sinkMetaType,
                targetName: val.sourceName,
                targetOrderNum: val.sinkSortNum,
              }
            }),
            name: mergeAllData.name || null,
            overrides: mergeAllData.overlayOrNot,
            filterSql: mergeAllData.filterSql,
            collectWay: mergeAllData.collectWay,
            incrementColumn: mergeAllData.incrementColumn,
            incrementColumnType: mergeAllData.incrementColumnType,
            schedulingConfigInfo: {
              collectWay: mergeAllData.collectRule,
              cron: mergeAllData.schedule?.cron || null,
              effectiveFromTime: mergeAllData.schedule?.fromDateTime || null,
              effectiveThruTime: mergeAllData.schedule?.thruDateTime || null,
              periodicDeclarationMode: mergeAllData.schedule?.cron
                ? 'CRON_EXPRESSION'
                : 'APPOINTED_TIME',
              reRun: mergeAllData.retryCount > 0 ? true : false,
              reRunInterval: mergeAllData.retryTime || null,
              reRunTimes: mergeAllData.retryCount || null,
              schedulingStrategy: mergeAllData.schedule ? 'AUTO' : 'MANUAL',
              timeUnit: mergeAllData.schedule?.period || null,
              timePoint: mergeAllData.schedule?.rateTime || null,
              appointedTime: mergeAllData.schedule?.period || null,
              timeInterval: mergeAllData.schedule?.extent || null,
            },
            securityLevel: mergeAllData.confidentialityLevel || null,
            sourceCategoryId: state.sourceCategoryId || null,
            sourceEntry: mergeAllData.sourceTableName || null,
            sourceId: mergeAllData.dataSourceId || null,
            executorMinMemory: mergeAllData.executorMinMemory || 1,
            executorMaxMemory: mergeAllData.executorMaxMemory || 2,
          }
          if (mergeAllData.schedule?.period == 'WEEK' || mergeAllData.schedule?.period == 'MONTH') {
            newData.schedulingConfigInfo.timeInterval =
              mergeAllData.schedule?.extent?.toString() || null
          }
          if (state.editId) {
            collectJobUpdate(newData)
              .then((res) => {
                let { success } = res
                if (success) {
                  sessionStorage.setItem('refreshListKey', 'dataCollectionRefresh')
                  ElNotification({
                    title: '提示',
                    message: '编辑成功',
                    type: 'success',
                  })
                  router
                    .push({ name: 'dataCollectionIndex' })
                    // collectJobPublish({ id: state.editId })
                    //   .then((resp) => {
                    //     state.loading = false
                    //     if (resp.success) {
                    //       ElNotification({
                    //         title: '提示',
                    //         message: '编辑成功',
                    //         type: 'success',
                    //       })
                    //       router.push({ name: 'dataCollectionIndex' })
                    //     }
                    //   })
                    .catch(() => {
                      state.loading = false
                    })
                }
              })
              .catch(() => {
                state.loading = false
              })
          } else {
            collectJobAdd(newData)
              .then((res) => {
                let { success } = res
                if (success) {
                  sessionStorage.setItem('refreshListKey', 'dataCollectionRefresh')
                  ElNotification({
                    title: '提示',
                    message: '新增成功',
                    type: 'success',
                  })
                  router.push({ name: 'dataCollectionIndex' })
                  // collectJobPublish({ id: res.data })
                  //   .then((resp) => {
                  //     state.loading = false
                  //     if (resp.success) {
                  //       ElNotification({
                  //         title: '提示',
                  //         message: '新增成功',
                  //         type: 'success',
                  //       })
                  //       router.push({ name: 'dataCollectionIndex' })
                  //     }
                  //   })
                  //   .catch(() => {
                  //     state.loading = false
                  //   })
                }
              })
              .catch(() => {
                state.loading = false
              })
          }
        },

        //数据结构化采集编辑初始化
        editGetData() {
          collectJobDetail({ id: state.editId })
            .then((res) => {
              let { data, success } = res
              if (success) {
                let { name, description, mappingList } = data
                state.ruleForm.name = name
                state.ruleForm.executorMinMemory = data.executorMinMemory || 1
                state.ruleForm.executorMaxMemory = data.executorMaxMemory || 2
                state.ruleForm.description = description
                let newData = {
                  tagList: data?.dataTag || [],
                  description: data.description || null,
                  sinkModelEname: data.destinationEntry || null,
                  sinkModelCname: data.destinationEntryName || null,
                  id: data.id,
                  columnMapping: mappingList.map((val) => {
                    return {
                      sourceColumnName: val.sourceCode,
                      sourceColumnLength: val.sourceDataLength,
                      sourceColumnType: val.sourceDataType,
                      sourceName: val.targetName || null,
                      sinkSortNum: val.sourceOrderNum,
                      sinkMetaCode: val.targetCode,
                      sinkMetaLength: val.targetDataLength,
                      sinkMetaType: val.targetDataType,
                    }
                  }),
                  name: data.name || null,
                  overlayOrNot: data.overrides,
                  filterSql: data.filterSql || '',
                  collectRule: data.schedulingConfigInfo?.collectWay,
                  collectWay: data.collectWay || 'FULL',
                  incrementColumn: data.incrementColumn || '',
                  incrementColumnType: data.incrementColumnType || '',
                  retryCount: data.schedulingConfigInfo?.reRunTimes,
                  retryTime: data.schedulingConfigInfo?.reRunInterval,
                  collectType: 'STRUCTURE',
                  schedule: {
                    schedulingStrategy: data.schedulingConfigInfo?.schedulingStrategy,
                    periodicDeclarationMode: data.schedulingConfigInfo?.periodicDeclarationMode,
                    cron:
                      data.schedulingConfigInfo?.periodicDeclarationMode === 'CRON_EXPRESSION'
                        ? data.schedulingConfigInfo?.cron
                        : null,
                    fromDateTime: data.schedulingConfigInfo?.effectiveFromTime || null,
                    thruDateTime: data.schedulingConfigInfo?.effectiveThruTime || null,
                    period: data.schedulingConfigInfo?.timeUnit || 'HOUR',
                    extent: data.schedulingConfigInfo?.timeInterval || null,
                    rateTime: data.schedulingConfigInfo?.timePoint || null,
                  },
                  confidentialityLevel: data.securityLevel || null,
                  sourceTableName: data.sourceEntry || null,
                  dataSourceId: data.sourceId || null,
                  dataSourceType: data.sourceType || null,
                  sinkModelCreateMethod: 'REVERSE',
                  executorMinMemory: data.executorMinMemory || 1,
                  executorMaxMemory: data.executorMaxMemory || 2,
                }
                if (newData.schedule?.period == 'WEEK' || newData.schedule?.period == 'MONTH') {
                  newData.schedule.extent = newData.schedule?.extent?.split(',') || []
                }
                state.allData = newData
                // state.ruleForm.envType = envType
                state.listenEnvTypeChange = false
                let _tableData = []
                mappingList?.forEach((item, index) => {
                  _tableData.push({
                    number: index + 1,
                    cnName: item.sourceName,
                    name: item.targetCode,
                    fieldType: item.targetDataType,
                    fieldLength: item.targetDataLength,
                    isPass: true,
                    sourceColumnName: item.sourceCode,
                    sourceColumnType: item.sourceDataType,
                    sourceColumnLength: item.sourceDataLength,
                  })
                })

                state.allData.tableData = _tableData
                state.loading = false
                structureDom.value.init(state.allData)
                configureSchedulingRulesDom.value.init(state.allData)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },

        //校验模型是否有效和被引用
        // modelsStudioValid(ids) {
        //   api.model.getModelsStudioValid({ ids: [ids] }).then((res) => {
        //     let { success, data } = res
        //     if (success) {
        //       if (!data.success) {
        //         ElNotification({
        //           title: '提示',
        //           message: data.reason,
        //           type: 'warning',
        //         })
        //       }
        //     }
        //   })
        // },

        // 取消
        cancel() {
          router.go(-1)
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId || null
        state.sourceCategoryId = router.currentRoute.value.query.sourceCategoryId || null
        state.disabled =
          router.currentRoute.value.query.status &&
          router.currentRoute.value.query.status !== 'WAITING_PUBLISH'
            ? true
            : false
        if (state.editId) {
          state.collectTaskNameOnly = true
          methods.editGetData()
        }
      })

      return {
        state,
        ruleForm,
        structureDom,
        configureSchedulingRulesDom,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection {
    height: 100%;
  }
  .data-collection-add {
    box-sizing: border-box;
    height: calc(100% - 64px);
    padding: 12px;
    &.isLzos {
      height: calc(100% - 52px);
      padding: 0;
    }
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 16px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 64px);
        overflow: auto;
        border-radius: 2px 2px 0 0;
        .inside-box {
          &-content {
            background-color: #fff;
            border-radius: 2px;
            &.footer {
              margin-top: 10px;
              border-radius: 2px 2px 0 0;
            }
            &:first-of-type {
              padding-top: 16px;
            }
          }
          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 16px;
            padding-left: 14px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .base-form {
          padding: 0 16px;
          .inline {
            display: flex;
            align-items: flex-start;
            &-left,
            .nancalui-form__item--horizontal {
              flex: 1;
            }
          }

          .nancalui-form__item--horizontal {
            margin-bottom: 16px;
          }
          .nancalui-input,
          .nancalui-select {
            max-width: 430px;
          }

          .nancalui-textarea__div {
            max-width: 434px !important;
          }
          .check-style {
            margin-top: -6px;
            margin-bottom: 10px;
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .box-operate {
    position: absolute;
    right: 12px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    width: calc(100% - 24px);
    height: 64px;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 0 0 2px 2px;
    :deep(.nancalui-button) {
      border-radius: 2px;
    }
    &.isLzos {
      right: 0;
      bottom: 0;
      width: 100%;
    }
  }
</style>
