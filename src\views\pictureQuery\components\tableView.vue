<template>
  <n-table :data="props.dataView.rows" style="width: 100%; height: 100%">
    <n-column resizeable field="id" header="id" />
    <n-column resizeable field="label" header="Llabel" />
    <n-column resizeable field="properties" header="properties">
      <template #default="scope">
        {{ JSON.stringify(scope.row.properties) }}
      </template>
    </n-column>
  </n-table>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs } from 'vue'

  const props = defineProps({
    dataView: {
      type: Object,
      default: null,
    },
  })

  
</script>
<style scoped lang="scss"></style>
