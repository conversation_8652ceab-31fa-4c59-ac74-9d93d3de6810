<!-- 结构化数据源新增 -->
<template>
  <div ref="tableRef" class="data-source-add publicFormCss container">
    <div class="data-source-add-box" v-loading="state.loading">
      <div class="page_header">
        <div>
          <span class="line"></span>
          <p v-if="state.type === 'ADD'" class="need_title">新增数据源</p>
          <p v-if="state.type === 'EDIT'" class="need_title">编辑数据源</p>
          <p v-if="state.type === 'DETAIL'" class="need_title">查看数据源</p>
        </div>
        <div class="detail-back-box" @click.prevent="goBack">返回</div>
      </div>

      <div
        :class="{
          'scroll-bar-style': true,
          'data-source-add-box-content': true,
          'only-detail': state.type === 'DETAIL',
        }"
      >
        <div class="content-title">
          <span class="line"></span>
          <span>基础信息</span>
        </div>
        <!-- 展示结构化数据 排除KAFKA -->
        <n-form
          v-if="state.type === 'DETAIL'"
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="150px"
          label-align="end"
          :pop-position="['right']"
          class="demo-ruleForm disable-hide-border disabled-form"
        >
          <div class="inline-box">
            <n-form-item label="数据源名称：" field="name">
              <div class="form-detail-text">{{ state.ruleForm.name }}</div>
            </n-form-item>
            <n-form-item label="端口号：" field="port">
              <div class="form-detail-text">{{ state.ruleForm.port }}</div>
            </n-form-item>
          </div>

          <div class="inline-box">
            <n-form-item label="数据分类：" field="11111">
              <div class="form-detail-text">{{ state.ruleForm.dataCategoryName }}</div>
            </n-form-item>
            <n-form-item label="数据库Database：" field="database">
              <div v-if="state.ruleForm.datasourceType === 'ORACLE'" class="form-detail-text">{{
                state.ruleForm.serviceName
              }}</div>
              <div v-else class="form-detail-text">{{ state.ruleForm.database }}</div>
            </n-form-item>
          </div>

          <div class="inline-box">
            <n-form-item label="数据源密级：" field="confidentialityLevelName">
              <div class="form-detail-text">{{ state.ruleForm.confidentialityLevelName }}</div>
            </n-form-item>
            <n-form-item label="用户名：" field="createByName">
              <div class="form-detail-text">{{ state.ruleForm.userName }}</div>
            </n-form-item>
          </div>

          <div class="inline-box">
            <n-form-item
              v-if="state.ruleForm.datasourceType !== 'SFTP'"
              label="数据源类型："
              field="datasourceType"
            >
              <div class="form-detail-text">{{ state.ruleForm.datasourceType }}</div>
            </n-form-item>
            <n-form-item label="创建人：" field="createByName">
              <div class="form-detail-text">{{ state.ruleForm.createByName }}</div>
            </n-form-item>
          </div>

          <div class="inline-box">
            <n-form-item label="IP地址Host：" field="host">
              <div class="form-detail-text">{{ state.ruleForm.host }}</div>
            </n-form-item>
            <n-form-item label="创建时间：" field="createTime">
              <div class="form-detail-text">{{ state.ruleForm.createTime }}</div>
            </n-form-item>
          </div>
          
          <div class="inline-box">
            <n-form-item label="描述信息：" field="description">
              <div class="form-detail-text" :title="state.ruleForm.description">{{
                state.ruleForm.description
              }}</div>
            </n-form-item>
          </div>
        </n-form>
      </div>

      <!-- 数据源表 -->
      <div class="datasourceForm">
        <div class="content-title">
          <span class="line"></span>
          <span>数据源表</span>
        </div>

        <n-input class="search-form" v-model="state.searchText" placeholder="请输入关键词">
          <template #append>
            <n-button class="search-btn" icon="search" />
          </template>
        </n-input>

        <n-table :data="filteredData" fix-header :table-height="state.tableHeight + 'px'">
          <n-column resizeable field="formName" header="表名" />
          <n-column resizeable header="操作" align="right">
            <template #default="scope">
              <span class="searchDetailText" @click="getFormDetail(scope.row.formName)"
                >查看表详情</span
              >
            </template>
          </n-column>
        </n-table>

        <n-pagination
          :total="filteredText?.length"
          v-model:pageSize="state.page.pageSize"
          v-model:pageIndex="state.page.pageIndex"
          :can-view-total="true"
          :can-change-page-size="true"
          :can-jump-page="true"
          :max-items="5"
          @page-index-change="pageChange"
          @page-size-change="pageSizeChange"
        />
      </div>
    </div>
  </div>

  <!--数据源表详情弹窗-->
  <n-modal
    v-model="state.showAddDrawer"
    width="540px"
    class="formDetailPop"
    bodyClass="formDetailBodyPop"
    :close-on-click-overlay="false"
    :draggable="false"
    :append-to-body="false"
    :before-close="
      () => {
        state.showAddDrawer = false
      }
    "
  >
    <template #header>
      <n-modal-header>
        <span class="line"></span>
        <span class="title">表详情</span>
      </n-modal-header>
    </template>
    <div class="name">表名称：{{ state.formName }}</div>
    <n-table :data="state.formDetailData" fix-header max-height="700px">
      <n-column resizeable field="colName" header="字段名" />
      <n-column resizeable field="dataType" header="字段类型" />
      <n-column resizeable field="comment" header="描述" />
    </n-table>
  </n-modal>
</template>
<script>
  import { ref, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const tableRef = ref()
      const router = useRouter()
      const ruleForm = ref()
      let passwordRequire = true

      if (router.currentRoute.value.query.databaseType === 'HIVE') {
        passwordRequire = false
      }
      const ENV_OPTS = {
        OFFICIAL: '生产环境',
        TEST: '开发环境',
      }
      const state = reactive({
        loading: false,
        showAddDrawer: false,
        searchText: '',
        formDetailData: [],
        formName: '',
        queryData: router.currentRoute.value.query || {}, // 获取路由传参
        disabled: false, // 表单不可用
        type: 'ADD', // 页面展示形式 ADD EDIT  DETAIL
        dataForm: [],
        ruleForm: {
          name: '',
          envType: '',
          datasourceType: '',
          host: '',
          port: '',
          dataCategoryName: '',
          database: '',
          userName: '',
          password: '',
          description: '',
          createByName: '',
          createTime: '',
          serviceName: '',
          defaultSchema: '',
          fileUrl: '',
          type: 'PROJECT', //类型(PROJECT-场景 PUBLIC-公共)
          fileType: [],
          showFilePath: '',
          filePath: [],
          confidentialityLevelName: '',
        },
        tableHeight: 500,
        page: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
        },
      })

      state.type = state.queryData.type ? state.queryData.type : 'ADD'
      state.ruleForm.datasourceType = state.queryData.databaseType
        ? state.queryData.databaseType.toUpperCase()
        : ''

      // 计算属性，过滤后的文本
      const filteredText = computed(() => {
        return state.dataForm?.filter((item) => {
          return item.formName.toLowerCase().includes(state.searchText.toLowerCase())
        })
      })

      // 计算属性，根据搜索文本过滤数据
      const filteredData = computed(() => {
        let arr = filteredText.value
        const startIndex = (state.page.pageIndex - 1) * state.page.pageSize
        const endIndex = startIndex + state.page.pageSize
        return arr.slice(startIndex, endIndex)
      })

      const methods = {
        // 获取数据源详情
        getDetail(id, interface_url = 'getDatasourceDetail') {
          state.loading = true
          api.project[interface_url]({ id })
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data[key]
                })
                if (data['filePath']) {
                  state.ruleForm['showFilePath'] = data['filePath'].join(',')
                }
                // methods.getTableList(data)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 获取数据库所有表
        getAlldatabaseForm(dataSourceId) {
          api.dataManagement.getSourceTables({ dataSourceId }).then((res) => {
            state.dataForm = []

            res.data?.forEach((item) => {
              state.dataForm.push({
                formName: item,
              })
            })
            state.page.total = state.dataForm.length
          })
        },
        // 获取表详情
        getFormDetail(tableName) {
          let data = {
            id: state.queryData.id,
            tableName: tableName,
          }
          state.loading = true
          api.dataManagement
            .getSourceStructure(data)
            .then((res) => {
              state.formName = tableName
              state.showAddDrawer = true
              state.formDetailData = res.data
              state.loading = false
            })
            .catch((err) => {
              state.loading = false
              console.log(err)
            })
        },
        // 返回
        goBack() {
          router.push({ name: 'dataSourceManagementIndex' })
        },
      }

      const pageChange = (pageNum) => {
        state.page.pageNum = pageNum === 0 ? 1 : pageNum
        state.page.pageNum = pageNum
      }
      const pageSizeChange = (pageSize) => {
        state.page.pageIndex = 1
        state.page.pageNum = 1
        state.page.pageSize = pageSize
      }

      onMounted(() => {
        state.tableHeight = tableRef.value.clientHeight - 395 - 136
        switch (state.type) {
          case 'DETAIL':
            methods.getDetail(state.queryData.id, 'getDatasourceAllDetail')
            methods.getAlldatabaseForm(state.queryData.id)
            state.rules = {}
            state.disabled = true
            break
          case 'EDIT':
            methods.getDetail(state.queryData.id, 'getDatasourceDetailByAes')
            break
        }
      })
      return {
        tableRef,
        ruleForm,
        state,
        ENV_OPTS,
        filteredData,
        filteredText,
        ...methods,
        pageChange,
        pageSizeChange,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 74px;
  .data-source-add {
    position: relative;
    padding: 16px;
    color: #333;

    .data-source-add-box {
      position: relative;
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;

      .page_header {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        height: 46px;
        margin-bottom: 10px;
        padding: 7px 8px 7px 0px;
        background: #fff;
        border-radius: 2px;

        p {
          display: inline-block;
          margin: 0;
          color: var(----, rgba(0, 0, 0, 0.9));
          font-size: 16px;
          vertical-align: middle;
        }
        .line {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 12px;
          vertical-align: middle;
          background: #1e89ff;
        }

        .detail-back-box {
          display: inline-block;
          float: right;
          width: 62px;
          height: 30px;
          color: #1d2129;
          font-size: 14px;
          line-height: 30px;
          text-align: center;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
        }
      }

      .data-source-add-box-content {
        box-sizing: border-box;
        width: 100%;
        padding: 16px 0;
        background: #fff;
        border-radius: 2px;

        .nancalui-form {
          width: 100%;
          margin: 0 auto;
          // transform: translatex(-70px);

          &.disable-hide-border {
            margin: 10px 16px !important;
          }
        }
      }

      // 查看下样式
      .only-detail {
        .nancalui-form {
          .inline-box {
            display: flex;
            flex-wrap: wrap;

            .nancalui-form__item--horizontal {
              width: 50%;
              height: 32px;
              margin-bottom: 8px;
            }
            :deep(.nancalui-form__label) {
              color: rgba(0, 0, 0, 0.85);
              font-weight: 500;
              font-size: 14px;
            }
            .form-detail-text {
              margin-right: 8px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.65);
              font-weight: 400;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .detail-text {
            .nancalui-form__item--horizontal {
              width: 78%;
            }
          }
        }
      }
    }

    .datasourceForm {
      height: calc(100% - 346px);
      margin-top: 10px;
      padding: 16px 0;
      background: #fff;
      border-radius: 2px;

      .search-form {
        width: 262px;
        margin: 12px 16px;

        :deep(.nancalui-input__wrapper) {
          border: 1px solid #e5e6eb;
          border-radius: 2px;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
        .search-btn {
          width: 32px;
        }
        .nancalui-input-slot__append {
          border-color: #e5e6eb;
        }
      }
      .searchDetailText {
        color: #1e89ff;
        font-size: 14px;
        cursor: pointer;
      }
    }

    .detail-title,
    .content-title {
      display: flex;
      gap: 10px;
      align-items: center;
      align-self: stretch;
      padding: 4px 0px !important;
      color: #2b71c2;
      font-weight: 400;
      font-size: 14px;
      font-family: 'Source Han Sans CN';
      font-style: normal;
      line-height: 22px;
      background: #f2f6fc;
      border: none !important;

      .line {
        display: inline-block;
        width: 4px;
        height: 18px;
        background: #1e89ff;
      }
      .icon-see {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }

  .formDetailPop {
    border-radius: 2px;

    .nancalui-modal__header {
      padding: 15px 16px 15px 0px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;

      .line {
        position: relative;
        top: 2px;
        display: inline-block;
        width: 4px;
        height: 18px;
        margin-right: 12px;
        background: #1e89ff;
      }
    }

    .name {
      margin: 8px 0;
      color: #606266;
      font-size: 14px;
    }
  }
  :deep(.formDetailBodyPop) {
    padding: 8px 16px;
  }
  :deep(.formDetailPop) {
    border-radius: 2px;
  }

  .nancalui-pagination {
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: flex-end;
    width: 100%;
    padding: 14px 16px;
    background: #fff;
    border-top: 1px solid #dcdfe6;
  }
</style>
