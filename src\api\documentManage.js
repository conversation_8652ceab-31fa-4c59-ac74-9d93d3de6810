import request from '@/utils/request'
import URL from '@/const/urlAddress'
// -------------------编码规则------------

// 时间格式初始化接口
export function getCodeTimeList() {
  return request({
    url: `${URL.DOCUMENT}/code/rule/timeFormatInit`,
    method: 'get',
    loading: true,
  })
}

// 编码规则分页集合
export function ruleListPage(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
// 编码规则分类集合
export function ruleListAll(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 编码规则详情
export function ruleDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 编码规则新建保存
export function ruleSave(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/save`,
    method: 'post',
    loading: true,
    data,
  })
}

// 编码规则编辑
export function ruleEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
// 删除编码规则校验
export function ruleDeleteCheck(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/deleteCheck`,
    method: 'get',
    loading: true,
    params: data,
  })
}

// 删除编码规则
export function ruleDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/code/rule/delete`,
    method: 'get',
    loading: true,
    params: data,
  })
}

//-------------------------------非结构化数据分类-------------------------------
// 新增分类
export function addClassifyTree(data) {
  return request({
    url: `${URL.DOCUMENT}/category/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 编辑分类
export function updateClassifyTree(data) {
  return request({
    url: `${URL.DOCUMENT}/category/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
// 删除分类
export function deleteClassify(data) {
  return request({
    url: `${URL.DOCUMENT}/category/${data.kind}/delete?id=${data.id}`,
    method: 'POST',
    loading: true,
  })
}

// 查询分类列表
export function getClassifyList(data) {
  const kind = data.kind
  delete data.kind
  return request({
    url: `${URL.DOCUMENT}/category/${kind}/listPageSearch`,
    method: 'post',
    loading: true,
    data: data,
  })
}

//获取编码规则列表
export function getRuleCodeList(data) {
  return request({
    url: `${URL.DOCUMENT}/category/select`,
    method: 'get',
    loading: true,
    params: data,
  })
}

//分类授权
export function getAuthList(data) {
  return request({
    url: `${URL.DOCUMENT}/category/auth/listPage`,
    method: 'post',
    loading: true,
    data: data,
  })
}
//授权人员
export function getPersonTree(data) {
  return request({
    url: `${URL.DOCUMENT}/category/dept/tree/person`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//授权删除
export function deleteAuth(data) {
  return request({
    url: `${URL.DOCUMENT}/category/auth/execute/delete?id=${data.id}`,
    method: 'post',
    loading: true,
    // data: data,
  })
}
//授权保存
export function addAuth(data) {
  return request({
    url: `${URL.DOCUMENT}/category/auth/save`,
    method: 'post',
    loading: true,
    data: data,
  })
}
//-------------------非结构化数据模板分类------------
// 非结构化数据模板分类集合
export function templateClassList(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/list`,
    method: 'post',
    loading: true,
    data,
  })
}

// 非结构化数据模板分类详情
export function templateClassDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据模板分类删除校验
export function templateClassDeleteCheck(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/deleteCheck`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据模板分类删除
export function templateClassDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/delete`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据模板分类新建保存
export function templateClassSave(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据模板分类新建保存
export function templateClassEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
//-------------------非结构化数据模板------------
// 非结构化数据模板列表
export function templateListPage(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据模板配置详情
export function templateDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//获取分类下拉树
export function getClassifyTreeList(data) {
  return request({
    url: `${URL.DOCUMENT}/category/tree/select`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据模板配置删除
export function templateDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/delete`,
    method: 'get',
    loading: true,
    params: data,
  })
}

// 非结构化数据模板配置新建保存
export function templateSave(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据模板配置编辑
export function templateEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/edit`,
    method: 'post',
    loading: true,
    data,
  })
}

// loadUrl
export function templateLoadUrl(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/loadUrl`,
    method: 'post',
    loading: true,
    data,
  })
}

// 非结构化数据模板配置上传
export function templateUpload(data) {
  return request({
    url: `${URL.DOCUMENT}/template/config/${data.bucket}/doc/upload`,
    method: 'post',
    loading: true,
    data: data.fn,
  })
}
// 模板分类平铺列表
export function templateSimpleTree(data) {
  return request({
    url: `${URL.DOCUMENT}/template/class/simpleTree?type=${data.type}`,
    method: 'post',
    loading: true,
  })
}
//-------------------非结构化数据编制------------
// 非结构化数据编制列表
export function documentationList(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// loadUrl
export function documentationLoadUrl(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/loadUrl`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 重名校验
export function documentationNameExist(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/nameExist`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据详情
export function documentationGet(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/get`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据新增
export function documentationSave(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据编辑
export function documentationUpdate(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/update`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据删除
export function documentationDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/delete/${data.id}`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据下架
export function documentationOffline(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/offline/${data.id}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据发布
export function documentationPublish(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/publish/${data.id}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据上传
export function documentationUpload(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/${data.bucket}/doc/upload`,
    method: 'post',
    loading: true,
    data: data.fn,
  })
}
// 版本列表
export function documentationVersionList(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/version/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 版本回退
export function documentationRollback(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/version/rollback`,
    method: 'post',
    loading: true,
    data,
  })
}
// 关联非结构化数据树-懒加载
export function associationTree(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/association/tree/v1`,
    method: 'post',
    loading: true,
    data,
  })
}
// 关联非结构化数据关系查看
export function associationRelation(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/association/relation/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//---------------------------非结构化数据上传---------------------------
// 关联非结构化数据关系查看
export function uploadAssoRelation(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/association/relation/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//关联非结构化数据树
export function uploadAssoTree(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/association/tree`,
    method: 'post',
    loading: true,
    data,
  })
}
//非结构化数据上传详情
export function uploadDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//非结构化数据上传新建
export function uploadSave(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/data-govern/save`,
    method: 'post',
    loading: true,
    data,
  })
}
//非结构化数据上传重新上传文件
export function uploadAgainSave(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/again/${data.bucket}/${data.docId}/file`,
    method: 'post',
    loading: true,
    data: data.fn,
  })
}
//非结构化数据上传编辑
export function uploadEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
//非结构化数据上传删除
export function uploadDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/execute/delete?docId=${data.docId}`,
    method: 'post',
    loading: true,
  })
}
//非结构化数据上传文件
export function uploadFile(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/${data.bucket}/${data.docId}/file`,
    method: 'post',
    loading: true,
    data: data.fn,
  })
}
//非结构化数据上传列表
export function uploadListPageSearch(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/listPageSearch`,
    method: 'post',
    loading: true,
    data,
  })
}
//loadUrl
export function uploadLoadUrl(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/loadUrl`,
    method: 'post',
    loading: true,
    data,
  })
}
// 重名校验
export function uploadNameExist(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/nameExist`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据上传下架
export function uploadOffline(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/offline/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据上传发布
export function uploadPublish(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/publish/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 版本列表
export function uploadVersionList(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/version/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 版本回退
export function uploadVersionRollback(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/version/rollback`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据上传文件下载
export function uploadFileDown(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/${data.bucket}/file/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
    responseType: 'blob',
  })
}

// 非结构化数据上传文件在线地址
export function uploadFilePreview(data) {
  return request({
    url: `${URL.DOCUMENT}/upload/online/file`,
    method: 'get',
    loading: true,
    params: data,
  })
}
export function sendOcr(data) {
  return request({
    url: `${URL.DOCUMENT}/tool/ocr/image/recognition`,
    method: 'post',
    data: data,
  })
}

//---------------------------非结构化数据采集---------------------------
//启动任务
export function startTask(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/start/${data.id}`,
    method: 'get',
    loading: true,
  })
}
//暂停任务
export function stopTask(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/stop/${data.id}`,
    method: 'get',
    loading: true,
  })
}
//运行任务
export function runTask(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/collect/${data.id}`,
    method: 'get',
    loading: true,
  })
}
//运行采集任务
export function runExecuteTask(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/execute/${data.id}`,
    method: 'get',
    loading: true,
  })
}
//运行api采集任务
export function runExecuteApiTask(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/execute/${data.id}`,
    method: 'post',
    loading: true,
    data,
  })
}
//删除任务
export function taskDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/delete?id=${data.id}`,
    method: 'get',
    loading: true,
    data,
  })
}
//删除api任务
export function taskApiDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/delete?id=${data.id}`,
    method: 'post',
    loading: true,
  })
}
// API SQL脚本解析
export function sqlParse(data) {
  return request({
    url: `${URL.DOCUMENT}/api/sql/parse`,
    method: 'post',
    loading: true,
    data,
  })
}
//获取详情接口信息
export function getApiDetailList(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/queryDataSourceApiList`,
    method: 'post',
    data,
  })
}
//获取文件信息
export function getFileList(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/sftp/getFileList`,
    method: 'post',
    loading: true,
    data,
  })
}
//saveLoad
export function saveLoad(data) {
  return request({
    url: `${URL.DOCUMENT}/documentation/saveLoad`,
    method: 'post',
    loading: true,
    data,
  })
}
//新增采集
export function collectAdd(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/create`,
    method: 'post',
    loading: true,
    data,
  })
}
//获取api采集详情
export function getCollectApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//新增api采集
export function collectAddApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/save`,
    method: 'post',
    loading: true,
    data,
  })
}
//编辑api采集
export function collectUpdateApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
//获取api采集映射
export function getSourceJson(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/jsonFlat/response-body?id=${data.id}`,
    method: 'get',
    loading: true,
  })
}
//任务列表
export function getDocTask(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/search`,
    method: 'post',
    loading: true,
    data,
  })
}
//任务列表-api
export function getDocApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/collect-api/search`,
    method: 'post',
    loading: true,
    data,
  })
}
//api源列表
export function getSourceApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/search`,
    method: 'post',
    loading: true,
    data,
  })
}
//api源详情
export function getSourceApDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/detail?id=${data.id}`,
    method: 'get',
  })
}
//新增api源
export function addDataSourceApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/save`,
    method: 'post',
    data,
  })
}
//编辑api源
export function updateDataSourceApi(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/edit`,
    method: 'post',
    data,
  })
}
//删除api源
export function sourceApiDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/document/api-datasource/delete?id=${data.id}`,
    method: 'post',
    data,
  })
}
// 非结构化数据采集接口下载
function collectDownload(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/download/doc`,
    method: 'get',
    responseType: 'blob',
    headers: {
      'X-Api-Url': data.url,
    },
    params: data,
  })
}
// 非结构化数据采集查询接口详情
function collectView(data) {
  return request({
    url: `${URL.DOCUMENT}/documentCollect/file/upload/doc`,
    method: 'get',
    headers: {
      'X-Api-Url': data.url,
    },
    params: data,
  })
}
//-------------------非结构化数据标签------------
// 非结构化数据标签列表
export function documentTagList(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/search`,
    method: 'post',
    loading: true,
    data,
  })
}
// 打标
export function documentTagging(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/batch/tagging`,
    method: 'post',
    loading: true,
    data,
  })
}
// 标签详情
export function tagDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//-------------------非结构化数据条目------------
// 非结构化数据条目查询非结构化数据列表
export function entryListPage(data) {
  return request({
    url: `${URL.DOCUMENT}/entry/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据推荐列表
export function entryRecordList(data) {
  return request({
    url: `${URL.DOCUMENT}/entry/record/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据预览记录保存
export function entryRecordSave(data) {
  return request({
    url: `${URL.DOCUMENT}/entry/record/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据条目接口下载
function apiDownload(data) {
  return request({
    url: `${URL.DOCUMENT}/entry/download/${data.type}/${data.id}`,
    method: 'get',
    responseType: 'blob',
    headers: {
      'X-Api-Url': data.url,
    },
  })
}
// 非结构化数据条目查询接口详情
function apiView(data) {
  return request({
    url: `${URL.DOCUMENT}/entry/apiDetail/${data.type}/${data.id}`,
    method: 'get',
    headers: {
      'X-Api-Url': data.url,
    },
  })
}
//-------------------非结构化数据服务外部调用------------
//非结构化数据分类查询非结构化数据列表
export function outsideListPage(data) {
  return request({
    url: `${URL.DOCUMENT}/outside/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
//分类下拉树
export function outsideTree(data) {
  return request({
    url: `${URL.DOCUMENT}/outside/tree/select`,
    method: 'get',
    loading: true,
    params: data,
  })
}
//非结构化数据详情
export function outsideGet(data) {
  return request({
    url: `${URL.DOCUMENT}/outside/get`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 资产注册接口
export function outsideAssetRegister(data) {
  return request({
    url: `${URL.DOCUMENT}/outside/asset/register`,
    method: 'post',
    loading: true,
    data,
  })
}

//-------------------非结构化数据协作管理------------
// 非结构化数据协作新建初始化表格
export function collaborateSheetInit(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/api/sheet/init?templateId=${data.templateId}`,
    method: 'post',
    loading: true,
  })
}
// 关联非结构化数据关系查看
export function collaborateRelation(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/association/relation/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 关联非结构化数据树
export function collaborateTree(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/association/tree`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作授权删除
export function collaborateAuthDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/auth/execute/delete?id=${data.id}`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作授权列表
export function collaborateAuthListPage(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/auth/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作授权保存
export function collaborateAuthSave(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/auth/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 部门人员树
export function collaborateDeptTree(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/dept/tree/person`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作详情
export function collaborateDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作新建保存
export function collaborateSave(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作编辑
export function collaborateEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/edit`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作删除
export function collaborateDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/execute/delete?docId=${data.docId}`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作列表
export function collaborateListPageSearch(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/listPageSearch`,
    method: 'post',
    loading: true,
    data,
  })
}
// 重名校验
export function collaborateNameExist(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/nameExist`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作下架
export function collaborateOffline(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/offline/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作发布
export function collaboratePublish(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/publish/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作文件在线地址
export function collaborateOnline(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/online/file`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作列表
export function collaborateVersion(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/version/list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 版本回退
export function collaborateVersionRollback(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/version/rollback`,
    method: 'post',
    loading: true,
    data,
  })
}
// 非结构化数据协作文件下载
export function collaboratefileDown(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/${data.bucket}/file/${data.docId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 非结构化数据协作文件上传
export function collaborateUploadFile(data) {
  return request({
    url: `${URL.DOCUMENT}/collaborate/${data.bucket}/${data.docId}/file`,
    method: 'post',
    loading: true,
    data: data.fn,
  })
}

//-------------------api管理------------
// 分类下拉树
export function apiCategoryTree(data) {
  return request({
    url: `${URL.DOCUMENT}/api/category/tree/select`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 分类详情
export function apiCategoryDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/api/category/detail`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 分类编辑
export function apiCategoryEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/api/category/edit`,
    method: 'post',
    loading: true,
    data,
  })
}

// 分类新建
export function apiCategorySave(data) {
  return request({
    url: `${URL.DOCUMENT}/api/category/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 分类删除
export function apiCategoryDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/api/category/${data.kind}/delete?id=${data.id}`,
    method: 'post',
    loading: true,
  })
}
// 添加编辑授权IP
export function apiAuthorizeAdd(data) {
  return request({
    url: `${URL.DOCUMENT}/api/authorize/add`,
    method: 'post',
    loading: true,
    data,
  })
}
// 根据API接口ID查询授权IP详情
export function apiAuthorizeGet(data) {
  return request({
    url: `${URL.DOCUMENT}/api/authorize/find/${data.apiId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 根据数据表ID查询请求参数列表
export function apiParamsList(data) {
  return request({
    url: `${URL.DOCUMENT}/api/list/params/${data.modelTableId}`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// API分页查询
export function apiListPageSearch(data) {
  return request({
    url: `${URL.DOCUMENT}/api/listPageSearch`,
    method: 'post',
    loading: true,
    data,
  })
}
// 检索API分页查询
export function apiCatalogListPageSearch(data) {
  return request({
    url: `${URL.DOCUMENT}/api/asset/listPage`,
    method: 'post',
    loading: true,
    data,
  })
}
// 添加API
export function apiAdd(data) {
  return request({
    url: `${URL.DOCUMENT}/api/add`,
    method: 'post',
    loading: true,
    data,
  })
}
// 编辑API
export function apiEdit(data) {
  return request({
    url: `${URL.DOCUMENT}/api/edit/${data.id}`,
    method: 'post',
    loading: true,
    data,
  })
}
// 删除API
export function apiDelete(data) {
  return request({
    url: `${URL.DOCUMENT}/api/delete/${data.apiId}`,
    method: 'post',
    loading: true,
    data,
  })
}
// 下架
export function apiOffline(data) {
  return request({
    url: `${URL.DOCUMENT}/api/offline/${data.apiId}`,
    method: 'post',
    loading: true,
    data,
  })
}

// 发布
export function apiPublish(data) {
  return request({
    url: `${URL.DOCUMENT}/api/publish/${data.apiId}`,
    method: 'post',
    loading: true,
    data,
  })
}
// API接口请求前缀
export function apiPrefix(data) {
  return request({
    url: `${URL.DOCUMENT}/api/prefix`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// API接口测试详情
export function apiTestDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/api/test/detail/${data.apiId}`,
    method: 'get',
    loading: true,
  })
}
// 获取token
export function apiToken(data) {
  return request({
    url: `${URL.DOCUMENT}/api/token/${data.url}`,
    method: 'get',
    loading: true,
  })
}
// 查询数据表集合
export function apiModelTables(data) {
  return request({
    url: `${URL.DOCUMENT}/api/model/tables`,
    method: 'get',
    loading: true,
    params: data,
  })
}
// 根据ID查询API详情
export function apiDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/api/detail/${data.apiId}`,
    method: 'get',
    loading: true,
  })
}
// 测试Api
export function testApi(data) {
  return request({
    url: `${URL.DOCUMENT}${data.url}`,
    method: data.method,
    [data.attr]: data.apiParam,
  })
}
//-----------------------非结构化数据特征--------------------------
export function getTraitList(data) {
  return request({
    url: `${URL.DOCUMENT}/document/features/search`,
    method: 'post',
    loading: true,
    data,
  })
}
export function getTraitDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/document/features/get?id=${data.id}`,
    method: 'post',
    loading: true,
  })
}
export function updateTrait(data) {
  return request({
    url: `${URL.DOCUMENT}/document/features/update`,
    method: 'post',
    loading: true,
    data,
  })
}
//<<----------------------api监控页面-------------->>>>
//api监控列表
export function getApiMonitoringList(data) {
  return request({
    url: `${URL.DOCUMENT}/api/monitoring/list`,
    method: 'post',
    data,
  })
}
//api监控实例列表
export function getApiMonitoringInstanceList(data) {
  return request({
    url: `${URL.DOCUMENT}/api/monitoring/instance/list`,
    method: 'post',
    data,
  })
}
//api监控实例详情
export function getApiMonitoringInstanceDetail(data) {
  return request({
    url: `${URL.DOCUMENT}/api/monitoring/instance/detail`,
    method: 'get',
    params: data,
  })
}
//服务统计-服务概况
export function getApiStatisticsSurvey(data) {
  return request({
    url: `${URL.DOCUMENT}/api/statistics/survey`,
    method: 'post',
    data,
  })
}
//服务统计-数据服务趋势
export function getApiStatisticsTrends(data) {
  return request({
    url: `${URL.DOCUMENT}/api/statistics/trends`,
    method: 'post',
    data,
  })
}
//服务统计-数据服务统计table内容
export function getApiStatisticsPageList(data) {
  return request({
    url: `${URL.DOCUMENT}/api/statistics/pageList`,
    method: 'post',
    data,
  })
}

//<<----------------------word在线编辑-------------->>>>
//在线编辑非结构化数据
export function getOnlineEditor(data) {
  return request({
    url: `${URL.DOCUMENT}/online/${data.source}/editor`,
    method: 'get',
    params: data,
  })
}
//新建非结构化数据
export function getWordNew(data) {
  return request({
    url: `${URL.DOCUMENT}/online/${data.source}/word/new`,
    method: 'get',
    params: data,
  })
}
//非结构化数据标签查询列表-带分页
export function getDocumentTags(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/tag/search`,
    method: 'post',
    data,
  })
}
//<<----------------------标签库管理-------------->>>>

//获取分类树
export function getTagLibraryClassTree(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/class/tree`,
    method: 'get',
    params,
  })
}
//新增分类
export function addTagLibraryClass(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/class/add`,
    method: 'post',
    data,
  })
}
//更新分类
export function updateTagLibraryClass(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/class/update`,
    method: 'post',
    data,
  })
}
//删除分类
export function deleteTagLibraryClass(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/class/${params.id}`,
    method: 'get',
    params,
  })
}

//新增标签
export function addTagLibraryTag(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/add`,
    method: 'post',
    data,
  })
}
//编辑标签
export function updateTagLibraryTag(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/update`,
    method: 'post',
    data,
  })
}
//删除标签
export function deleteTagLibraryTag(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/delete/${params.id}`,
    method: 'get',
    params,
  })
}
//查看标签
export function detailTagLibraryTag(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/get/${params.id}`,
    method: 'get',
    params,
  })
}
//标签分页查询
export function getTagLibraryTagList(data) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/listPage`,
    method: 'post',
    data,
  })
}
//校验标签是否可以删除或者下架
export function validTagLibraryTag(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/valid/${params.id}`,
    method: 'get',
    params,
  })
}
//标签发布
export function tagLibraryTagPublish(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/publish/${params.id}`,
    method: 'get',
    params,
  })
}
//标签下架
export function tagLibraryTagOff(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/manage/off/${params.id}`,
    method: 'get',
    params,
  })
}
//获取带标签的分类树
export function getTagLibraryClassListHasTag(params) {
  return request({
    url: `${URL.DOCUMENT}/documentTag/class/tagTree`,
    method: 'get',
    params,
  })
}
// 获取日志平台域名和端口
export function getLogPlatformUrl(params) {
  return request({
    url: `${URL.DOCUMENT}/dictionary/query/${params.groupCode}`,
    method: 'get',
    params,
  })
}
// 用户导入
export function userTemplateImport(data) {
  return request({
    url: `${URL.BASE}/user/template/import`,
    method: 'post',
    data: data,
    responseType: 'blob',
  })
}
// 获取已发布的oracle数据源
export function getDatasource(data) {
  return request({
    url: `${URL.DOCUMENT}/api/datasource/published/list`,
    method: 'post',
    data,
  })
}
// 获取oracle数据源对应的表
export function getTablesV2(data) {
  return request({
    url: `${URL.DOCUMENT}/api/datasource/tables/v2`,
    method: 'post',
    data,
  })
}
// 获取oracle数据源对应的表所属字段
export function getTablesStructure(data) {
  return request({
    url: `${URL.DOCUMENT}/api/datasource/table/structure`,
    method: 'post',
    data,
  })
}

// 文档标注-文档标注列表
export function getDocumentAnnotationList(data) {
  return request({
    url: `${URL.BASE}/document-mark/list`,
    method: 'post',
    loading: true,
    data,
  })
}

// 文档标注内容导出Excel
export function exportDocumentAnnotationExcel(documentId) {
  return request({
    url: `${URL.BASE}/document-mark/content/export/${documentId}`,
    method: 'get',
    loading: true,
    responseType: 'blob',
  })
}
// 文档标注-保存文档标注
export function saveDocumentAnnotation(data) {
  return request({
    url: `${URL.BASE}/document-mark/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 文档标注-文档标注预览
export function getDocumentAnnotationPreview(id) {
  return request({
    url: `${URL.BASE}/document-mark/view/${id}`,
    method: 'get',
    loading: true,
  })
}

export function getDocumentAuthType(data) {
  return request({
    url: `${URL.DOCUMENT}/category/user/auth/list`,
    method: 'post',
    loading: true,
    data,
  })
}

export default {
  getCodeTimeList,
  ruleListPage,
  ruleListAll,
  ruleDetail,
  ruleSave,
  ruleEdit,
  ruleDeleteCheck,
  ruleDelete,

  addClassifyTree,
  updateClassifyTree,
  deleteClassify,
  getClassifyList,
  getRuleCodeList,
  getClassifyTreeList,
  getAuthList,
  getPersonTree,
  deleteAuth,
  addAuth,

  templateClassList,
  templateClassDetail,
  templateClassDelete,
  templateClassDeleteCheck,
  templateClassSave,
  templateClassEdit,
  templateListPage,
  templateDetail,
  templateDelete,
  templateSave,
  templateEdit,
  templateUpload,
  templateLoadUrl,
  templateSimpleTree,
  documentationList,
  documentationLoadUrl,
  documentationNameExist,
  documentationGet,
  documentationSave,
  documentationUpdate,
  documentationDelete,
  documentationOffline,
  documentationPublish,
  documentationUpload,
  documentationVersionList,
  documentationRollback,
  uploadAssoRelation,
  uploadAssoTree,
  uploadDetail,
  uploadSave,
  uploadAgainSave,
  uploadEdit,
  uploadDelete,
  uploadFile,
  uploadListPageSearch,
  uploadLoadUrl,
  uploadNameExist,
  uploadOffline,
  uploadPublish,
  uploadVersionList,
  uploadVersionRollback,
  uploadFileDown,
  uploadFilePreview,
  sendOcr,
  associationTree,
  associationRelation,
  getDocTask,
  getDocApi,
  getSourceApi,
  getSourceApDetail,
  addDataSourceApi,
  updateDataSourceApi,
  sourceApiDelete,
  collectDownload,
  collectView,
  startTask,
  stopTask,
  runTask,
  runExecuteTask,
  runExecuteApiTask,
  taskDelete,
  taskApiDelete,
  getApiDetailList,
  getFileList,
  saveLoad,
  collectAdd,
  getCollectApi,
  collectAddApi,
  collectUpdateApi,
  getSourceJson,
  documentTagList,
  documentTagging,
  tagDetail,
  entryListPage,
  entryRecordList,
  entryRecordSave,
  apiDownload,
  apiView,

  outsideListPage,
  outsideTree,
  outsideGet,
  outsideAssetRegister,

  collaborateSheetInit,
  collaborateRelation,
  collaborateTree,
  collaborateAuthDelete,
  collaborateAuthListPage,
  collaborateAuthSave,
  collaborateDeptTree,
  collaborateDetail,
  collaborateSave,
  collaborateEdit,
  collaborateDelete,
  collaborateListPageSearch,
  collaborateNameExist,
  collaborateOffline,
  collaboratePublish,
  collaborateOnline,
  collaborateVersion,
  collaborateVersionRollback,
  collaboratefileDown,
  collaborateUploadFile,

  apiCategoryTree,
  apiCategoryDetail,
  apiCategorySave,
  apiCategoryEdit,
  apiCategoryDelete,
  apiAuthorizeAdd,
  apiAuthorizeGet,
  apiListPageSearch,
  apiCatalogListPageSearch,
  apiParamsList,
  apiAdd,
  apiEdit,
  apiDelete,
  apiOffline,
  apiPublish,
  apiDetail,
  apiPrefix,
  apiTestDetail,
  apiToken,
  apiModelTables,
  testApi,

  getTraitList,
  getTraitDetail,
  updateTrait,

  getApiMonitoringList,
  getApiMonitoringInstanceList,
  getApiMonitoringInstanceDetail,
  getApiStatisticsSurvey,
  getApiStatisticsTrends,
  getApiStatisticsPageList,
  getOnlineEditor,
  getWordNew,
  getDocumentTags,

  getTagLibraryClassTree,
  addTagLibraryClass,
  updateTagLibraryClass,
  deleteTagLibraryClass,
  addTagLibraryTag,
  updateTagLibraryTag,
  deleteTagLibraryTag,
  detailTagLibraryTag,
  getTagLibraryTagList,
  validTagLibraryTag,
  tagLibraryTagPublish,
  tagLibraryTagOff,
  getTagLibraryClassListHasTag,
  getLogPlatformUrl,
  userTemplateImport,
  getDatasource,
  getTablesV2,
  getTablesStructure,
  sqlParse,

  getDocumentAnnotationList,
  exportDocumentAnnotationExcel,
  saveDocumentAnnotation,
  getDocumentAnnotationPreview,
}
