<template>
  <div class="txt-content">
    <!-- 显示pdf的canvas -->
    <div class="txt-Preview" v-html="option.value" @click="input.click"> </div>
    <watermark :watermarkInfo="props.watermarkInfo" />
  </div>
</template>

<script setup>
  import mammoth from 'mammoth'
  import watermark from './watermark'
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
    watermarkInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
  var input = document.createElement('input')
  input.type = 'file'
  input.type = 'file'
  input.accept = '.doc,.docx'
  // 执行上传文件操作
  input.addEventListener('change', handleFileSelect, false)

  //获取上传文件base64数据
  function arrayBufferToBase64(arrayBuffer) {
    var binary = ''
    var bytes = new Uint8Array(arrayBuffer)
    var len = bytes.byteLength
    for (var i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }

  function handleFileSelect(event) {
    var file = event.target.files[0]
    //获取上传文件后缀，如果是docx格式，则使用mammoth来进行解析，
    //如果不是则访问后台，将文件传输流base64传递到后台
    //生成文件，然后用java解析doc，并返回到前台
    var extension = file.name.slice(((file.name.lastIndexOf('.') - 1) >>> 0) + 2)
    if (extension === 'docx') {
      readFileInputEventAsArrayBuffer(event, function (arrayBuffer) {
        var base64Data = arrayBufferToBase64(arrayBuffer)
        console.log(base64Data)
        mammoth.convertToHtml({ arrayBuffer: arrayBuffer }).then(displayResult, function (error) {
          console.error(error)
        })
      })
    } else if (extension === 'doc') {
      readFileInputEventAsArrayBuffer(event, function (arrayBuffer) {
        //base64文件流
        var base64Data = arrayBufferToBase64(arrayBuffer)
        var result = '后台请求'
        alert(result)
        console.log(base64Data)
      })
      //tinymce的set方法将内容添加到编辑器中
      tinymce.activeEditor.setContent(result)
    }
  }

  function displayResult(result) {
    //tinymce的set方法将内容添加到编辑器中
    option.value.value = result.value
  }

  function readFileInputEventAsArrayBuffer(event, callback) {
    var file = event.target.files[0]
    var reader = new FileReader()
    reader.onload = function (loadEvent) {
      var arrayBuffer = loadEvent.target.result
      callback(arrayBuffer)
    }
    reader.readAsArrayBuffer(file)
  }
</script>
<style lang="scss" scoped>
  .txt-content {
    position: relative;
    display: flex;
    flex: 1 0 0;
    align-self: stretch;
    justify-content: center;
    height: 100%;
    padding: 8px;
    border: 1px solid var(---, #a3b4db);
    border-radius: 6px;
    user-select: text;
    .txt-Preview {
      gap: 4px;
      overflow-y: auto;
    }
  }
</style>
