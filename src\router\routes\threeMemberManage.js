import Layout from '@/layout'

// 系统管理-日志管理
export default [
  {
    path: '/threeMember',
    component: Layout,
    redirect: '/threeMember/threeMemberManagement',
    name: 'threeMember',
    code: 'threeMember',
    meta: {
      title: '三员管理',
      icon: 'three-member-mark',
      parentRouterName: 'systemManage',
      code: 'threeMember',
      unShowProject: true,
    },
    children: [
      // 三员管理
      {
        path: 'threeMemberManagement',
        name: 'threeMemberManagement',
        code: 'threeMemberManagement',
        redirect: '/threeMember/threeMemberManagement/threeMemberManagementlist',
        component: () => import('@/views/three-member-manage/index'),
        meta: {
          title: '三员管理',
          parentRouterName: 'systemManage',
          code: 'threeMemberManagement',
          unShowProject: true,
        },
        children: [
          {
            path: 'threeMemberManagementlist',
            hidden: true,
            name: 'threeMemberManagementlist',
            component: () => import('@/views/three-member-manage/list'),
            meta: {
              title: '三员管理',
              unShowProject: true,
              activeMenu: '/threeMember/threeMemberManagement',
              parentRouterName: 'systemManage',
              code: 'threeMemberManagement',
            },
          },
        ],
      },
    ],
  },
]
