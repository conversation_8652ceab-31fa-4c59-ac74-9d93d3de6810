<template>
  <div class="addRule container">
    <section class="form-content">
      <div class="form-content-name">
        <moduleName :info="{ name: title }" />
      </div>
      <section class="form-content-box">
        <n-form
          ref="ruleForm"
          :data="ruleForm"
          :rules="rules"
          label-width="80px"
          label-align="end"
          class="ruleForm"
          :pop-position="['right']"
        >
          <n-form-item label="规则名称：" field="name">
            <n-input
              v-model="ruleForm.name"
              :disabled="isSee"
              minlength="2"
              maxlength="30"
              clearable
              placeholder="请输入数据规则名称"
            />
          </n-form-item>
          <n-form-item label="规则定义：" field="type">
            <div class="radio-box-label">
              <div v-if="isSee" class="radio-box disabled">
                <div class="radio-box-item">
                  <span class="radio-box-item-name">{{
                    ruleForm.type === 'built' ? '条件规则' : '正则表达式'
                  }}</span>
                </div>
              </div>
              <div v-else class="radio-box">
                <div
                  :class="ruleForm.type === 'built' ? 'radio-box-item checked' : 'radio-box-item'"
                  @click="radioFn('built')"
                >
                  <span class="radio-box-item-icon"></span>
                  <span class="radio-box-item-name">条件规则</span>
                </div>
                <div
                  :class="ruleForm.type === 'self' ? 'radio-box-item checked' : 'radio-box-item'"
                  @click="radioFn('self')"
                >
                  <span class="radio-box-item-icon"></span>
                  <span class="radio-box-item-name">正则表达式</span>
                </div>
              </div>
              <div v-if="ruleForm.builtList" class="rule-content">
                <div class="rule-content-name">
                  规则内容
                  <div v-if="!isSee" class="rule-content-name-btn" @click="clearFn">
                    清除
                    <SvgIcon icon="icon-clear" class="clear" title="清除" />
                  </div>
                </div>
                <div class="rule-content-info scroll-bar-style">
                  <template v-if="ruleForm.type === 'built'">
                    <div v-for="(item, index) in ruleForm.builtList" :key="index">
                      {{ index === 0 ? '' : condition }}字段值{{
                        item.type === 'LENGTH' ? '的长度' : ''
                      }}{{ item.condition }}{{ item.num }}
                    </div>
                  </template>
                  <div v-else>{{ ruleForm.builtList }}</div>
                </div>
              </div>
            </div>
          </n-form-item>
          <n-form-item label="描述信息：" field="description">
            <n-textarea
              v-model="ruleForm.description"
              resize="none"
              :disabled="isSee"
              :autosize="{ minRows: 3 }"
              class="textarea"
              maxlength="200"
              show-word-limit
              placeholder="请输入规则描述"
            />
          </n-form-item>
        </n-form>
      </section>
    </section>
    <footer class="footer">
      <n-button
        :loading="saveLoading"
        v-if="!isSee"
        class="footer-btn"
        variant="solid"
        @click.prevent="saveFn('all')"
        >保存
      </n-button>
      <n-button v-if="!isSee" class="footer-btn" @click.prevent="cancelFn">取消</n-button>
      <n-button v-if="isSee" class="footer-btn" variant="solid" @click.prevent="cancelFn"
        >返回</n-button
      >
    </footer>
    <!--条件规则-->
    <n-drawer
      title=""
      v-model="showBuiltDrawer"
      :close-on-click-overlay="false"
      :with-header="false"
      :size="680"
    >
      <div class="drawer-content">
        <!--头部-->
        <div class="drawer-content-name">
          <moduleName
            :info="{
              name: '条件规则',
              size: 16,
              color: '#333',
              width: '3px',
              height: '12px',
              left: '12px',
            }"
          />
          <svgIcon icon="icon-close" class="close" @click="showBuiltDrawer = false" />
        </div>
        <!--中间内容-->
        <div class="drawer-content-form">
          <div class="drawer-content-form-title">条件满足：</div>
          <n-select
            v-model="condition"
            class="condition"
            size="small"
            placeholder="请选择"
            style="margin-bottom: 20px"
          >
            <n-option name="满足全部（&&）" value="&&" />
            <n-option name="满足任意（||）" value="||" />
          </n-select>
          <div class="drawer-content-form-box">
            <div
              v-for="(item, index) in builtList"
              :key="index"
              class="drawer-content-form-box-list"
            >
              <div class="drawer-content-form-box-list-name">字段值</div>
              <n-select
                v-model="item.condition"
                class="drawer-content-form-box-list-select"
                size="small"
                placeholder="请选择"
                :options="
                  conditionList.map((val) => {
                    return { ...val, name: val.label, value: val.value }
                  })
                "
                @change="selectConditionFn($event, index, 'condition')"
              />
              <n-select
                v-model="item.type"
                class="drawer-content-form-box-list-input"
                size="small"
                placeholder="请选择"
                :options="
                  conditionType.map((val) => {
                    return { ...val, name: val.label, value: val.value }
                  })
                "
                @change="selectConditionFn($event, index, 'type')"
              />
              <n-input
                v-model="item.num"
                :showControl="false"
                size="small"
                placeholder="请输入"
                class="drawer-content-form-box-list-input"
                @keyup="onKeydownNumberFn($event, item.num, 'up')"
                @keydown="onKeydownNumberFn($event, item.num, 'down')"
                @blur="onKeydownNumberFn($event, item, 'blur')"
                @input="selectConditionFn($event, index, 'num')"
              />
              <div class="drawer-content-form-box-list-pic" @click="addConditionFn(true, index)">
                <SvgIcon icon="icon-circle-add" class="pic" title="添加" />
              </div>
              <div class="drawer-content-form-box-list-pic" @click="cutConditionFn(index)">
                <SvgIcon
                  icon="icon-circle-cut"
                  :class="builtList.length === 1 ? 'pic cut' : 'pic'"
                  title="删除"
                />
              </div>
            </div>
          </div>
          <div class="drawer-content-form-title">逻辑</div>
          <div class="result logic">
            <div v-for="(item, index) in builtList" :key="index">
              {{ index === 0 ? '' : condition }}字段值{{ item.type === 'LENGTH' ? '的长度' : ''
              }}{{ item.condition }}{{ item.num }}
            </div>
          </div>
        </div>
      </div>
      <!--底部-->
      <div class="drawer-content-footer">
        <n-button :loading="saveLoading" variant="solid" @click.prevent="saveFn('built')"
          >保存</n-button
        >
        <n-button @click.prevent="showBuiltDrawer = false">取消</n-button>
      </div>
    </n-drawer>
    <!--正则表达式-->
    <n-drawer
      title=""
      v-model="showSelfDrawer"
      :close-on-click-overlay="false"
      :with-header="false"
      :size="680"
    >
      <div class="drawer-content">
        <!--头部-->
        <div class="drawer-content-name">
          <moduleName
            :info="{
              name: '正则表达式',
              size: 14,
              color: '#000',
              width: '3px',
              height: '12px',
              left: '12px',
            }"
          />
          <svgIcon icon="icon-close" class="close" @click="showSelfDrawer = false" />
        </div>
        <!--中间内容-->
        <div class="drawer-content-form">
          <div class="drawer-content-form-title">正则表达式</div>
          <n-textarea
            v-model="selfRule"
            resize="none"
            :rows="18"
            :class="{
              textarea: true,
              'red-border': showSelfBorder && !selfRule,
            }"
            placeholder="请输入java正则表达式，如([1-9]\d*\.?\d*)|(0\.\d*[1-9])"
          />
          <div class="drawer-content-form-title btns">
            测试数据
            <n-button class="test" color="primary" @click.prevent="testFn">测试 </n-button>
          </div>
          <n-textarea
            v-model="selfTestValue"
            resize="none"
            :rows="9"
            :class="{
              textarea: true,
              'red-border': showSelfBorder && !selfTestValue,
            }"
            placeholder="请输入测试数据"
          />
          <div class="drawer-content-form-title">测试结果</div>
          <div class="result">{{ selfTestResult }}</div>
        </div>
      </div>
      <!--底部-->
      <div class="drawer-content-footer">
        <n-button :loading="saveLoading" variant="solid" @click.prevent="saveFn('self')"
          >保存</n-button
        >
        <n-button @click.prevent="showSelfDrawer = false">取消</n-button>
      </div>
    </n-drawer>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import moduleName from '@/components/ModuleName'
  import { checkCName, onKeydownNumber, onKeyupNumber } from '@/utils/validate'

  export default {
    components: { moduleName },
    data() {
      return {
        id: '',
        groupId: '',
        isSee: false,
        isEdit: false,
        title: '新增规则',
        ruleForm: {
          name: '',
          type: '', // built表示内置，self表示自定义
          description: '',
          builtList: null,
        },
        rules: {
          type: [{ required: true, message: '请选择规则类型', trigger: 'blur' }],
          name: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'project', 'validRule', {
                  name: this.ruleForm.name,
                  id: this.id || null,
                }),
              trigger: 'blur',
            },
          ],
        },
        // 条件规则
        showBuiltDrawer: false,
        showBuiltBorder: false,
        builtList: [],
        condition: '&&', // 判断满足全部还是任意 &&表示全部， ||表示任意
        conditionType: [
          { label: '数值', value: 'VALUE' },
          { label: '长度', value: 'LENGTH' },
        ],
        conditionList: [
          { label: '等于(==)', value: '==', operator: '==' },
          { label: '不等于(!=)', value: '!=', operator: '!=' },
          { label: '小于(<)', value: '<', operator: '<' },
          { label: '大于(>)', value: '>', operator: '>' },
          { label: '小于或者等于(<=)', value: '<=', operator: '<=' },
          { label: '大于或者等于(>=)', value: '>=', operator: '>=' },
        ],
        // 正则表达式
        showSelfDrawer: false,
        showSelfBorder: false,
        selfId: '',
        selfRule: '',
        selfTestValue: '',
        selfTestResult: '',
        saveLoading: false,
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    mounted() {
      this.groupId = this.$route.query.groupId
      if (this.$route.query.id) {
        this.id = this.$route.query.id
        this.title = '查看'
        this.isSee = true
        if (this.$route.query.isEdit) {
          this.title = '编辑规则'
          this.isSee = false
          this.isEdit = true
        }
        this.$api.dataQuality.getRuleDetail({ id: this.id }).then((res) => {
          if (res.code === 'SUCCESS') {
            this.condition = res.data.matchedCondition === 'ANY' ? '||' : '&&'
            let type = res.data.ruleDetailList[0].type === 'REGULAR' ? 'self' : 'built'
            let data = {
              name: res.data.name,
              type: type, // built表示内置，self表示自定义
              description: res.data.description,
              builtList: null,
            }
            if (type === 'self') {
              data.builtList = res.data.context
              this.selfRule = res.data.context
              this.selfId = res.data.ruleDetailList[0].id
            } else {
              res.data.ruleDetailList.map((val) => {
                val.condition = val.expression.replace(/[0-9|.}a]/gi, '')
                val.type = val.validType
                val.num = val.expression.replace(/[^0-9&^.]/gi, '')
                return val
              })
              data.builtList = res.data.ruleDetailList
              this.builtList = JSON.parse(JSON.stringify(data.builtList))
            }
            this.ruleForm = data
          }
        })
      }
    },
    methods: {
      onKeydownNumberFn(event, value, name) {
        if (name === 'down') {
          return onKeydownNumber(event, value)
        } else if (name === 'up') {
          return onKeyupNumber(event, value)
        } else if (name === 'blur') {
          value.num = value.num.replace(/\D/g, '')
        }
      },
      radioFn(name) {
        if (this.ruleForm.type !== '' && name !== this.ruleForm.type) {
          this.$dialogPopup({
            title: '是否确认切换规则类型',
            message: '切换后已配置的规则将被清空',
            save: () => {
              this.clearFn()
              this.openFn(name)
            },
          })
        } else {
          this.openFn(name)
        }
      },
      // 打开规则弹窗
      openFn(name) {
        this.ruleForm.type = name
        if (name === 'self') {
          this.showSelfDrawer = true
        } else {
          this.showBuiltDrawer = true
          if (this.builtList.length === 0) {
            this.addConditionFn(false)
          }
        }
      },
      // 清除规则
      clearFn() {
        this.ruleForm.builtList = null
        this.builtList = []
        this.selfRule = ''
        this.selfTestValue = ''
        this.selfTestResult = ''
      },
      // 增加条件
      addConditionFn(flag, index) {
        let item = {
          id: '',
          condition: '==',
          type: 'VALUE',
          num: 0,
        }
        if (flag) {
          this.builtList.splice(index + 1, 0, item)
        } else {
          this.builtList.push(item)
        }
      },
      // 减少条件
      cutConditionFn(index) {
        if (this.builtList.length > 1) {
          this.builtList.splice(index, 1)
        } else {
          this.$notify({
            title: '提示',
            message: '至少添加一条规则',
            type: 'warning',
          })
        }
      },
      // 选择条件类型
      selectConditionFn(event, index, name) {
        this.builtList[index][name] = event
      },
      // 点击取消
      cancelFn() {
        this.$router.go(-1)
      },
      // 点击测试
      testFn() {
        if (this.selfRule) {
          if (this.selfTestValue) {
            this.showSelfBorder = false
            let data = {
              pattern: this.selfRule,
              value: this.selfTestValue,
              ruleType: 'REGULAR',
              validType: 'VALUE',
            }
            this.$api.dataQuality.getCommonRuleTest(data).then((res) => {
              if (res.code === 'SUCCESS') {
                if (res.data) {
                  this.selfTestResult = '测试通过'
                } else {
                  this.selfTestResult = '测试未通过'
                }
              }
            })
          } else {
            this.$notify({
              title: '提示',
              message: '请输入测试数据',
              type: 'warning',
            })
            this.showSelfBorder = true
          }
        } else {
          this.$notify({
            title: '提示',
            message: '请输入规则表达式',
            type: 'warning',
          })
          this.showSelfBorder = true
        }
      },
      // 将字符串转化为脚本
      evalFn(fn) {
        let Fun = Function // 防止有些前端编译工具报错
        return new Fun('return ' + fn)()
      },
      // 保存
      saveFn(type) {
        this.saveLoading = true
        if (type === 'built') {
          // 条件规则
          if (this.builtList.length > 0) {
            this.ruleForm.builtList = JSON.parse(JSON.stringify(this.builtList))
            this.showBuiltDrawer = false
            this.saveLoading = false
          } else {
            this.$notify({
              title: '提示',
              message: '请先添加规则',
              type: 'warning',
            })
            this.saveLoading = false
          }
        } else if (type === 'self') {
          // 正则表达式
          if (this.selfRule) {
            if (this.selfTestValue && this.selfTestResult) {
              this.showSelfDrawer = false
              this.ruleForm.builtList = this.selfRule
              this.showSelfBorder = false
              this.saveLoading = false
            } else {
              this.$notify({
                title: '提示',
                message: '请填写测试数据并点击测试按钮进行校验',
                type: 'warning',
              })
              this.showSelfBorder = true
              this.saveLoading = false
            }
          } else {
            this.$notify({
              title: '提示',
              message: '请填写正则表达式',
              type: 'warning',
            })
            this.showSelfBorder = true
            this.saveLoading = false
          }
        } else if (type === 'all') {
          // 提交保存
          this.$refs.ruleForm.validate((val) => {
            if (val) {
              if (this.ruleForm.builtList) {
                let context = this.ruleForm.builtList
                let expressionList = []
                if (this.ruleForm.type === 'built') {
                  context = ''
                  this.ruleForm.builtList.forEach((val, ind) => {
                    if (ind === 0) {
                      context +=
                        '字段值' + (val.type === 'LENGTH' ? '的长度' : '') + val.condition + val.num
                    } else {
                      context +=
                        this.condition +
                        '字段值' +
                        (val.type === 'LENGTH' ? '的长度' : '') +
                        val.condition +
                        val.num
                    }
                    expressionList.push({
                      id: val.id,
                      expression: 'a' + val.condition + val.num,
                      type: 'AVIATOR',
                      validType: val.type,
                    })
                  })
                } else {
                  expressionList.push({
                    id: this.selfId,
                    expression: context,
                    type: 'REGULAR',
                    validType: 'VALUE',
                  })
                }
                let params = {
                  context: context,
                  description: this.ruleForm.description,
                  matchedCondition: this.condition === '&&' ? 'ALL' : 'ANY',
                  expressionList: expressionList,
                  name: this.ruleForm.name,
                  groupId: Number(this.groupId),
                }

                if (this.id) {
                  params.id = Number(this.id)
                  this.$api.dataQuality
                    .updateRule(params)
                    .then((res) => {
                      if (res.code === 'SUCCESS') {
                        this.saveLoading = false
                        this.$router.go(-1)
                      }
                    })
                    .catch((err) => (this.saveLoading = false))
                } else {
                  this.$api.dataQuality
                    .saveRule(params)
                    .then((res) => {
                      if (res.code === 'SUCCESS') {
                        this.saveLoading = false
                        this.$router.go(-1)
                      }
                    })
                    .catch((err) => (this.saveLoading = false))
                }
              } else {
                this.$notify({
                  title: '提示',
                  message: '请添加规则',
                  type: 'warning',
                })
              }
            } else {
              this.saveLoading = false
            }
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .addRule {
    position: relative;
    // padding: 0 10px;

    .form-content {
      position: relative;
      box-sizing: border-box;
      height: calc(100vh - 130px);
      padding: 2px;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 4px;

      &-bg {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 1;
        width: 252px;
        height: 232px;
      }

      // 滚动条的宽度
      &::-webkit-scrollbar {
        width: 4px; // 横向滚动条
        height: 4px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 2px;
      }

      &-name {
        padding: 8px 30px;
        //background-color: #f7f8fa;
        border-radius: 3px 3px 0 0;
      }

      &-box {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 38px);
        padding-top: 40px;
        border-radius: 4px;

        .ruleForm {
          position: relative;
          z-index: 2;
          width: 660px;
          margin: 0 auto;
          transform: translateX(-60px);
          .radio-box-label {
            width: 100%;
          }

          .radio-box {
            box-sizing: border-box;
            width: 236px;
            height: 32px;
            line-height: 32px;
            border-radius: 2px;

            &.disabled {
              width: 100%;
              padding-left: 10px;
              background-color: #f7f8fa;
              border: 1px solid #e1e1e1;
            }

            &-item {
              float: left;
              height: 32px;
              line-height: 32px;
              cursor: pointer;

              &:first-of-type {
                margin-right: 40px;
              }

              &.checked {
                .radio-box-item-icon {
                  border: 1px solid $themeBlue;

                  &:before {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    width: 8px;
                    height: 8px;
                    margin: auto;
                    background-color: $themeBlue;
                    border-radius: 50%;
                    content: '';
                  }
                }
              }

              &-icon {
                position: relative;
                display: inline-block;
                width: 14px;
                height: 14px;
                margin-right: 8px;
                vertical-align: middle;
                background-color: #fff;
                border: 1px solid #cfcfcf;
                border-radius: 50%;
              }

              &-name {
                color: #333333;
                font-size: 12px;
                line-height: 16px;
                vertical-align: middle;
              }
            }
          }

          .rule-content {
            box-sizing: border-box;
            width: 100%;
            margin-top: 10px;
            background-color: #f7f8fa;
            border: 1px solid #e1e1e1;

            &-name {
              padding: 6px 10px;
              color: #666666;
              font-size: 12px;
              line-height: normal;
              background-color: #fff;
              border-bottom: 1px solid #e1e1e1;

              &-btn {
                float: right;
                color: #333333;
                cursor: pointer;

                &:hover {
                  color: $themeBlue;

                  .clear {
                    color: $themeBlue;
                  }
                }

                .clear {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  color: #333333;
                  vertical-align: middle;
                }
              }
            }

            &-info {
              box-sizing: border-box;
              height: 128px;
              padding: 6px 10px;
              overflow-y: auto;
              color: #333333;
              font-size: 12px;
              line-height: 20px;
              background-color: #f7f8fa;
            }
          }
        }
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      height: 60px;
      margin-top: 10px;
      margin-right: -10px;
      margin-left: -10px;
      padding: 16px 30px;
      overflow: hidden;
      background: #ffffff;
      border-radius: 8px 8px 0 0;

      &-btn {
        margin: 0 10px;
      }
    }
  }

  .drawer-content {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 56px);
    padding: 38px 20px 20px 20px;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;
    // 滚动条的宽度
    &::-webkit-scrollbar {
      width: 4px; // 横向滚动条
      height: 4px; // 纵向滚动条 必写
    }

    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      background-color: #ddd;
      border-radius: 2px;
    }

    &-name {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 8px 20px;
      background-color: #f7f8fa;

      .close {
        position: absolute;
        top: 0;
        right: 20px;
        bottom: 0;
        width: 16px;
        height: 16px;
        margin: auto;
        color: #666;
        cursor: pointer;
      }
    }

    &-form {
      &-title {
        margin-top: 8px;
        margin-bottom: 3px;
        color: #000000;
        font-size: 12px;
        line-height: 30px;

        &.btns {
          margin-top: 20px;
          margin-bottom: 10px;
        }

        .test {
          float: right;
        }
      }

      .condition {
        width: 100%;
      }

      .add-btn {
        margin: 20px 0;
      }

      &-box {
        box-sizing: border-box;
        width: 100%;
        height: 350px;
        overflow-y: auto;
        border-bottom: 1px solid #e1e1e1;
        // 滚动条的宽度
        &::-webkit-scrollbar {
          width: 4px; // 横向滚动条
          height: 4px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #ddd;
          border-radius: 2px;
        }

        &-list {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          &-name {
            height: 32px;
            padding: 0 12px;
            color: #666666;
            font-size: 12px;
            line-height: 32px;
            background-color: #eeeeee;
            border-radius: 2px;
          }

          &-select {
            width: 180px;
          }

          &-input {
            width: 150px;
            color: #333;
            font-size: 12px;
          }

          &-pic {
            width: 20px;
            height: 20px;
            cursor: pointer;

            .pic {
              display: block;
              width: 20px;
              height: 20px;
              color: #666666;

              &.cut {
                color: #cfcfcf;
              }

              &:hover {
                color: $themeBlue;
              }
            }
          }
        }
      }

      :deep(.textarea) {
        color: #333;
        font-size: 12px;
        background-color: #fff;
        &.red-border {
          border: 1px solid #f66f6a;
        }
      }

      .result {
        box-sizing: border-box;
        height: 170px;
        padding: 12px;
        color: #333;
        font-size: 12px;
        background: #f7f8fa;
        border-radius: 4px;

        &.logic {
          height: 338px;
        }
      }
    }
  }

  .drawer-content-footer {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    padding: 12px 20px;
    text-align: center;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
  }
</style>
