<template>
  <div class="commonForm-search-dif">
    <div class="search-left">
      <n-form
        :inline="true"
        :data="state.formInline"
        class="demo-form-inline search-right"
        label-width="66px"
      >
        <!-- <n-form-item label="时间范围：">
          <n-range-date-picker-pro
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :shortcuts="state.shortcuts"
            allow-clear
            @confirmEvent="onSearch"
          />
        </n-form-item> -->
        <n-form-item label="API名称：">
          <n-input v-model="state.formInline.keyword" size="small" placeholder="请输入" clearable>
          </n-input>
        </n-form-item>
      </n-form>
    </div>
    <div class="form-box">
      <div class="search">
        <div class="search-btn" @click.prevent="startSearch">查询</div>
        <div class="search-btn reset" @click.prevent="resetFn">重置</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { reactive } from 'vue'
  import ENUM from '@/const/enum'
  export default {
    title: '',
    components: {},
    props: {},
    emits: ['handleSearch', 'reset'],
    setup(props, { emit }) {
      const state = reactive({
        shortcuts: ENUM.SHORTCUTS,

        formInline: {
          keyword: '',
        },
        searchData: {
          keyword: '',
        },
        org_apiStatus: [],
      })
      const methods = {
        startSearch() {
          Object.keys(state.formInline).forEach((key) => {
            state.searchData[key] = state.formInline[key]
          })
          methods.onSearch()
        },
        onSearch() {
          emit('handleSearch', state.searchData)
        },
        resetFn() {
          state.formInline.keyword = ''
          state.searchData.keyword = ''
          emit('reset')
        },
      }

      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .commonForm-search-dif {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 8px 10px 16px;

    .nancalui-form {
      display: flex;
      align-items: center;
      .nancalui-input {
        width: 240px;
      }
      .nancalui-form__item--horizontal {
        margin-bottom: 0;
        margin-right: 8px;
        .nancalui-form__label .nancalui-form__label-span {
          color: #333;
        }
      }
    }
    .form-box {
      display: flex;

      .search {
        display: flex;
        gap: 8px;
        .search-btn {
          padding: 0 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          border-radius: 2px;
          background-color: #1e89ff;
          cursor: pointer;
          height: 30px;
          line-height: 30px;
          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dcdfe6;
            color: #1d2129;
            background-color: transparent;
            border-radius: 2px;
          }
        }
      }
    }
  }
</style>
