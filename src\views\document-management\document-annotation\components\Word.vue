<template>
  <div class="pdf-content">
      <!-- 换页按钮 -->
      <div class="page-btn" id="page-btn">
          <div :class="{ 'page-btn-box': true, disabled: state.pagination.currentPage === 1 }" @click="prevPage">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.5 3L4.5 8L10.5 13" stroke="currentColor" stroke-linecap="round"
                      stroke-linejoin="round" />
              </svg>
          </div>
          <div :class="{
              'page-btn-box': true,
              disabled: state.pagination.currentPage >= state.pagination.total,
          }" @click="nextPage">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M5.5 13L11.5 8L5.5 3" stroke="currentColor" stroke-linecap="round"
                      stroke-linejoin="round" />
              </svg>
          </div>
      </div>

      <!-- 显示pdf的canvas -->
      <div class="pdf-canvas" v-loading="state.loading">
          <div id="wave-container"></div>
          <div id="tag-container"></div>
          <div id="select-text-layer_232" ref="pdfcom" :style="{ '--scale-factor': scaledFactor }"></div>
      </div>
  </div>
  <div class="pagination-content" id="pagination-content">
      <n-pagination :total="state.pagination.total" v-model:pageSize="state.pagination.pageSize"
          v-model:pageIndex="state.pagination.currentPage" @page-index-change="pageUpdate" />
  </div>
</template>

<script setup>
import { renderAsync } from 'docx-preview'
const emit = defineEmits(['page-change','load-complete'])
const route = useRoute()
const props = defineProps({
  option: {
      type: Object,
      default() {
          return {}
      },
  },
})
const { option } = toRefs(props)
const pdfcom = ref(null)
const state = reactive({
  loading: false,
  pagination: {
      currentPage: 1,
      pageSize: 1,
      total: 0,
  },
})

const prevPage = () => {
  if (state.pagination.currentPage > 1) {
      state.pagination.currentPage = Math.max(state.pagination.currentPage - 1, 1)
      pageUpdate?.()
      // 上一页
  }
}
const nextPage = () => {
  if (state.pagination.currentPage < state.pagination.total) {
      state.pagination.currentPage = Math.min(
          state.pagination.currentPage + 1,
          state.pagination.total,
      )
      pageUpdate?.()
      // 下一页
  }
}
function previewDocx(buffer) {
  let _options = {
      className: 'docx', // 默认和文档样式类的类名/前缀
      inWrapper: true, // 启用围绕文档内容渲染包装器
      ignoreWidth: false, // 禁止页面渲染宽度
      ignoreHeight: false, // 禁止页面渲染高度
      ignoreFonts: false, // 禁止字体渲染
      breakPages: true, // 在分页符上启用分页
      ignoreLastRenderedPageBreak: true, //禁用lastRenderedPageBreak元素的分页
      experimental: false, //启用实验性功能（制表符停止计算）
      trimXmlDeclaration: true, //如果为真，xml声明将在解析之前从xml文档中删除
      debug: false, // 启用额外的日志记录
  }

  renderAsync(buffer, document.getElementById('select-text-layer_232'), null, _options).then(
      (result) => {
          // 渲染完成后的回调函数
          emit('load-complete')
      } 
  )
}

let pageUpdate
onMounted(async () => {
  const blob = await fetch(option.value.value).then(res => res.blob())
  previewDocx(blob)
})

</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.pdf-content {
  position: relative;
  display: flex;
  flex: 1 0 0;
  align-self: stretch;
  justify-content: center;
  height: calc(100% - 44px);
  padding: 8px;
  background: var(---, #f6f7fb);
  border-radius: 6px;
  user-select: text;

  .page-btn {
      width: 0;
      height: 0;

      &-box {
          position: absolute;
          top: 50%;
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          color: $themeBlue;
          background-color: #fff;
          border-radius: 27px;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
          transform: translateY(-50%);
          cursor: pointer;

          &.disabled {
              color: #b8b8b8;
              background-color: #e5e5e5;
              cursor: not-allowed;

              &:hover {
                  color: #b8b8b8;
                  background: #e5e5e5;
              }
          }

          &:hover {
              color: #fff;
              background: #6e9eff;
          }

          &:last-child {
              right: 16px;
          }

          &:first-child {
              left: 16px;
          }
      }
  }

  .pdf-canvas {
      position: relative;
      width: 1000px;
      overflow-x: hidden;
      overflow-y: auto;

      #select-text-layer_232 {


          /* Internet Explorer/Edge */
          // :deep(*:empty) {
          //     height: 0;
          //     user-select: none;
          //     /* 标准语法 */
          //     -webkit-user-select: none;
          //     /* Safari */
          //     -moz-user-select: none;
          //     /* Firefox */
          //     -ms-user-select: none;
          //     // 禁止事件
          //     pointer-events: none;
          //     -webkit-pointer-events: none;
          //     -moz-pointer-events: none;
          // }

          // :deep(foreignObject) {
          //     user-select: none;
          //     /* 标准语法 */
          //     -webkit-user-select: none;
          //     /* Safari */
          //     -moz-user-select: none;
          //     /* Firefox */
          //     -ms-user-select: none;
          //     // 禁止事件
          //     pointer-events: none;
          //     -webkit-pointer-events: none;
          //     -moz-pointer-events: none;
          // }
      }

      .signImg {
          position: absolute;
          z-index: 2;
      }
  }
}

.pagination-content {
  display: flex;
  align-items: center;
  align-self: stretch;
  justify-content: flex-end;
  padding: 16px 0 0 0;

  &-total {
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;

      span {
          color: rgba(0, 0, 0, 0.85);
      }
  }
}

:deep(.textLayer) {
  // display: none;
}
</style>
