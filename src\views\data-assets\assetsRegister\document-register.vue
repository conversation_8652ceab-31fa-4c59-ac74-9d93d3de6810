<template>
  <section class="container">
    <div class="header">
      <div class="title">资产注册申请表</div>
      <n-button @click="router.back()">返回</n-button>
    </div>

    <n-form
      class="regInfoForm"
      :data="state.regInfoForm"
      labelSuffix="："
      label-width="135px"
      disabled
    >
      <n-row :gutter="48">
        <n-col :span="12">
          <n-form-item field="name" label="注册申请单号">
            <n-input v-model="state.regInfoForm.applicationCode" />
          </n-form-item>
        </n-col>
        <n-col :span="12">
          <n-form-item field="name" label="注册人">
            <n-input v-model="state.regInfoForm.registrant" />
          </n-form-item>
        </n-col>
      </n-row>

      <n-row :gutter="48">
        <n-col :span="12">
          <n-form-item field="name" label="注册部门">
            <n-input v-model="state.regInfoForm.registrationDept" />
          </n-form-item>
        </n-col>
        <n-col :span="12">
          <n-form-item field="name" label="资产编号">
            <n-input v-model="state.regInfoForm.assetCode" />
          </n-form-item>
        </n-col>
      </n-row>

      <n-row :gutter="48">
        <n-col :span="12">
          <n-form-item field="name" label="注册时间">
            <n-input v-model="state.regInfoForm.registrationTime" />
          </n-form-item>
        </n-col>
      </n-row>
    </n-form>

    <div class="assetsInfoForm">
      <div class="title">资产信息</div>

      <n-form ref="regInfoFormRef" :data="state.regInfoForm" labelSuffix="：" label-width="135px">
        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="name" label="资产名称">
              <n-input v-model="state.regInfoForm.name" disabled />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="name" label="非结构化数据编号">
              <n-input v-model="state.regInfoForm.enName" disabled />
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item
              field="classificationCode"
              label="资产分类"
              :rules="[
                { required: true, message: '请选择资产分类', trigger: 'change', type: 'string' },
              ]"
            >
              <n-tree-select
                v-model="state.regInfoForm.classificationCode"
                filter
                allowClear
                placeholder="请选择"
                :key="state.typeOpt"
                :treeData="state.typeOpt"
                :prop="{
                  label: 'name',
                  value: 'code',
                  children: 'children',
                }"
              >
                <template #default="{ item }">
                  {{ item.name }}
                </template>
              </n-tree-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="name" label="所属业务">
              <n-input v-model="state.regInfoForm.bizOwnership" />
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="name" label="资产提供方">
              <n-input v-model="state.regInfoForm.provider" />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item
              field="sourceName"
              label="来源系统"
              :rules="[{ required: true, message: '请输入来源系统', trigger: 'blur' }]"
            >
              <n-input v-model="state.regInfoForm.sourceName" />
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="name" label="资产重要程度">
              <n-select
                v-model="state.regInfoForm.importanceLevel"
                placeholder="请选择"
                filter
                allow-clear
                :options="state.importanceLevelOpt"
              />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="publishTime" label="发布时间">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.regInfoForm.publishTime"
                placeholder="请选择发布时间"
                :showTime="true"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="name" label="资产持有部门">
              <n-tree-select
                v-model="state.regInfoForm.ownerDeptId"
                filter
                allowClear
                placeholder="请选择"
                :key="state.deptOpt"
                :treeData="state.deptOpt"
                :prop="{
                  label: 'label',
                  value: 'id',
                  children: 'children',
                }"
              >
                <template #default="{ item }">
                  {{ item.label }}
                </template>
              </n-tree-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="name" label="资产管理部门">
              <n-tree-select
                v-model="state.regInfoForm.manageDeptId"
                filter
                allowClear
                placeholder="请选择"
                :key="state.deptOpt"
                :treeData="state.deptOpt"
                :prop="{
                  label: 'label',
                  value: 'id',
                  children: 'children',
                }"
              >
                <template #default="{ item }">
                  {{ item.label }}
                </template>
              </n-tree-select>
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="name" label="资产密级">
              <n-select
                v-model="state.regInfoForm.securityLevel"
                placeholder="请选择"
                filter
                allow-clear
                :options="state.securityLevelOpt"
              />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="effectiveTime" label="资产有效时间">
              <n-radio-group direction="row" v-model="state.regInfoForm.effectiveTime">
                <n-radio value="0">一年</n-radio>
                <n-radio value="1">二年</n-radio>
                <n-radio value="2">长期有效</n-radio>
              </n-radio-group>
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="48">
          <n-col :span="12">
            <n-form-item field="valuation" label="资产估值">
              <n-input-number v-model="state.regInfoForm.valuation" hideButton />
            </n-form-item>
          </n-col>
          <n-col :span="24">
            <n-form-item field="attachments" label="资产附件">
              <div class="upload">
                <div class="upload-box">
                  <n-upload
                    class="uploadBtn"
                    accept=".csv,.xls,.xlsx,.json,.txt,.log,.xml,.doc,.docx,.pdf,.jpg,.png,.jpeg"
                    droppable
                    :before-upload="beforeUpload"
                  >
                    <n-button variant="solid"
                      ><svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          id="&#231;&#186;&#191;&#230;&#128;&#167;/&#228;&#184;&#138;&#228;&#188;&#160;"
                        >
                          <path
                            id="Rectangle 2107"
                            d="M2 8.5V13.5C2 14.0523 2.44772 14.5 3 14.5H13C13.5523 14.5 14 14.0523 14 13.5V8.5"
                            stroke="white"
                            stroke-linecap="round"
                          />
                          <line
                            id="Line 19"
                            x1="11.2929"
                            y1="5"
                            x2="8"
                            y2="1.70711"
                            stroke="white"
                            stroke-linecap="round"
                          />
                          <line
                            id="Line 20"
                            x1="0.5"
                            y1="-0.5"
                            x2="5.15685"
                            y2="-0.5"
                            transform="matrix(0.707107 -0.707107 -0.707107 -0.707107 4 5)"
                            stroke="white"
                            stroke-linecap="round"
                          />
                          <line
                            id="Line 5"
                            x1="0.5"
                            y1="-0.5"
                            x2="8.5"
                            y2="-0.5"
                            transform="matrix(1.38419e-07 1 1 -1.38419e-07 8.5 2)"
                            stroke="white"
                            stroke-linecap="round"
                          />
                        </g>
                      </svg>
                      点击上传</n-button
                    >
                  </n-upload>

                  <div class="upload-box-icon">
                    <div class="hint" @click.stop="stopClick"
                      ><svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                      >
                        <g clip-path="url(#clip0_1280_482043)">
                          <path
                            d="M0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8C15.5 12.1421 12.1421 15.5 8 15.5C3.85786 15.5 0.5 12.1421 0.5 8ZM8.75 4.75C8.75 4.33579 8.41421 4 8 4C7.58579 4 7.25 4.33579 7.25 4.75C7.25 5.16421 7.58579 5.5 8 5.5C8.41421 5.5 8.75 5.16421 8.75 4.75ZM8.2889 6.59183C8.13836 6.48533 7.94143 6.47032 7.7765 6.55279L6.7765 7.05279C6.52951 7.17628 6.4294 7.47662 6.5529 7.72361C6.67639 7.9706 6.97673 8.07071 7.22372 7.94722L7.38809 7.86503L7.00397 10.938C6.98267 11.1084 7.05031 11.2778 7.18312 11.3867C7.31592 11.4955 7.49531 11.5286 7.65822 11.4743L9.15822 10.9743C9.42019 10.887 9.56178 10.6039 9.47445 10.3419C9.38713 10.0799 9.10397 9.93834 8.842 10.0257L8.09465 10.2748L8.49625 7.06202C8.51912 6.87905 8.43943 6.69834 8.2889 6.59183Z"
                            fill="#479DFF"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_1280_482043">
                            <rect width="16" height="16" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      可上传10个大小不超过10MB的任意类型文件</div
                    >
                    <div class="count" @click="state.uploadShow = !state.uploadShow"
                      >已添加<strong>{{ state.uploadList.length }}</strong
                      >附件
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        :style="{ transform: state.uploadShow ? 'rotate(0deg)' : 'rotate(180deg)' }"
                      >
                        <path d="M8 11L4.25 6H11.75L8 11Z" fill="#606266" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div v-show="state.uploadShow">
                  <div class="upload-list">
                    <div class="file" v-for="(file, indx) in state.uploadList" :key="file.name">
                      <span class="name">{{ file.name }}</span>
                      <span class="size">{{ formatSize(file.fileSize) }}</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        @click="delFile(indx)"
                      >
                        <path
                          d="M3.75781 4L12.2431 12.4853"
                          stroke="#909399"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M3.75781 12.4844L12.2431 3.99909"
                          stroke="#909399"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>

    <div class="footer">
      <n-button @click="router.back()">取消</n-button>
      <n-button variant="solid" @click="saveRegister">确定</n-button>
    </div>
  </section>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { confidentialityLevelOptions } from './components/utlis.js'
  import api from '@/api/index.js'
  import { assetsApplyForRegitsterInfo, assetsRegisterType } from '@/api/assets.js'
  /**
   * 路由对象
   */
  const route = useRoute()
  /**
   * 路由实例
   */
  const router = useRouter()
  //console.log('1-开始创建组件-setup')
  /**
   * 数据部分
   */
  const regInfoFormRef = ref()
  const state = reactive({
    uploadShow: true,
    uploadList: [],
    regInfoForm: {
      registerFrom: null,
      valuation: null,
      applicationCode: '',
      registrant: '',
      registrantId: null,
      registrationDept: '',
      registrationDeptId: null,
      registrationTime: '',
      assetCode: '',

      name: '',
      enName: '',
      classificationCode: '',
      bizOwnership: '',
      provider: '',
      sourceCode: '',
      sourceName: '',
      supplyMode: '',
      level: '',
      importanceLevel: '',
      publishTime: '',
      publishScope: '',
      ownerDept: '',
      ownerDeptId: null,
      manageDept: '',
      manageDeptId: null,
      administrator: '',
      administratorId: null,
      dataItemList: '',
      securityLevel: '',
      serviceApi: '',
      apiConfig: '',
      fromDate: '', //生效开始时间
      thruDate: '', //生效结束时间
      effectiveTime: '0', //有效时间
      attachments: '',
      type: 'FILE',
    },

    // 资产级别
    levelOpt: [
      { value: 'LEVEL_1', name: '一级' },
      { value: 'LEVEL_2', name: '二级' },
      { value: 'LEVEL_3', name: '三级' },
    ],
    // 重要程度
    importanceLevelOpt: [
      { value: 'GENERAL', name: '普通' },
      { value: 'IMPORTANT', name: '核心' },
      { value: 'VITAL', name: '必要' },
    ],
    // 密级
    securityLevelOpt: confidentialityLevelOptions,
    // 提供方式
    supplyModeOpt: [
      { value: 'QUERY', name: '查询' },
      { value: 'DOWNLOAD', name: '下载' },
      { value: 'API', name: 'API' },
    ],
    // 资产分类
    typeOpt: [
      { value: 'FILE', name: 'FILE' },
      { value: 'TABLE', name: 'TABLE' },
    ],
    // 部门
    deptOpt: [],
    // 部门人员
    deptPresonOpt: [],
  })

  onMounted(() => {
    getRegisterInfo()
    getDocInfo()
    getDeptFn()
    getDeptPresonFn()
    assetsRegisterTypeFn()
    state.regInfoForm.publishTime = formartTime(Date.now())
    state.regInfoForm.registrationTime = getCurrentTime()
  })

  // 保存资产注册申请
  const saveRegister = () => {
    if (state.regInfoForm.classificationCode === '') {
      ElMessage.error(`请选择资产分类`)
      return false
    }
    if (state.regInfoForm.sourceName === '') {
      ElMessage.error(`请输入来源系统`)
      return false
    }
    // regInfoFormRef.value.validate((isValid) => {
    //   if (isValid) {
    state.regInfoForm.fromDate = formartTime(Date.now())
    state.regInfoForm.thruDate = formartTime(Date.now() + 365 * 24 * 60 * 60 * 1000)
    switch (state.regInfoForm.effectiveTime) {
      case '1':
        state.regInfoForm.thruDate = formartTime(Date.now() + 365 * 24 * 60 * 60 * 1000 * 2)
        break
      case '2':
        state.regInfoForm.thruDate = formartTime(Date.now() + 365 * 24 * 60 * 60 * 1000 * 100)
        break
    }
    state.regInfoForm.ownerDeptId = Number(state.regInfoForm.ownerDeptId)
    state.regInfoForm.manageDeptId = Number(state.regInfoForm.manageDeptId)
    state.regInfoForm.attachments = state.uploadList.map((item) => item.url).join(',')
    const params = {
      ...state.regInfoForm,
      effectiveTime: undefined,
      publishTime: formartTime(Date.parse(state.regInfoForm.publishTime)),
    }
    api.documentManage.outsideAssetRegister(params).then((res) => {
      if (res.code === 'SUCCESS') {
        ElMessage.success('申请注册成功')
        router.back()
      }
    })
    //   }
    // })
  }
  // 文件上传
  const beforeUpload = (UploadRawFile) => {
    let size = UploadRawFile[0].file.size / 1048576 // 单位M
    let fileName = UploadRawFile[0].file.name.substring(
      UploadRawFile[0].file.name.lastIndexOf('/') + 1,
    )
    let suffix = fileName.split('.')[1]
    if (suffix === 'csv') {
      if (size > 5 * 1024) {
        ElNotification({
          title: '提示',
          message: 'csv文件不能超过5G！',
          type: 'warning',
        })
        return false
      }
    } else {
      if (size > 100) {
        ElNotification({
          title: '提示',
          message: '文件不能超过100M！',
          type: 'warning',
        })
        return false
      }
    }
    const formData = new FormData()
    formData.append('file', UploadRawFile[0].file)
    formData.append('bucket', 'data-govern')
    api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
      let { success, data } = res
      if (success) {
        state.uploadList.push(res.data)
      }
    })
    return false
  }

  const formatSize = (sizeInBytes) => {
    if (sizeInBytes < 1024) {
      return sizeInBytes + 'B'
    } else if (sizeInBytes < 1024 * 1024) {
      return (sizeInBytes / 1024).toFixed(2) + 'KB'
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return (sizeInBytes / (1024 * 1024)).toFixed(2) + 'MB'
    } else {
      return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
    }
  }

  const delFile = (index) => {
    state.uploadList.splice(index, 1)
  }

  const formartTime = (t) => {
    const now = typeof t === 'number' ? new Date(t) : t

    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  // 资产文件信息
  const getDocInfo = async () => {
    const res = await api.documentManage.outsideGet({ id: route.query.id })
    if (res.code === 'SUCCESS') {
      const { name, ruleCode } = res.data
      state.regInfoForm.enName = ruleCode
      state.regInfoForm.name = name
      state.regInfoForm.registerFrom = Number(route.query.id)
    }
  }
  // 资产分类
  const assetsRegisterTypeFn = async () => {
    const res = await assetsRegisterType({})
    if (res.code === 'SUCCESS') {
      state.typeOpt = res.data.children
    }
  }

  // 资产注册申请信息
  const getRegisterInfo = async () => {
    const res = await assetsApplyForRegitsterInfo()
    if (res.code === 'SUCCESS') {
      state.regInfoForm = { ...state.regInfoForm, ...res.data }
    }
  }

  // 获取部门
  const getDeptFn = async () => {
    const res = await api.base.getDepartmentTrees({})
    if (res.code === 'SUCCESS') {
      state.deptOpt = res.data.children
    }
  }

  // 获取部门人员
  const getDeptPresonFn = async () => {
    const res = await api.project.getThreeDepartment({})
    let { success, data } = res
    if (success && data !== null) {
      state.deptPresonOpt = data
    }
  }

  // 获取当前时间
  const getCurrentTime = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  const stopClick = () => {
    return false
  }
  // 使用toRefs解构
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .container {
    position: relative;

    .header {
      display: flex;
      align-items: center;
      align-self: stretch;
      justify-content: space-between;
      padding: 7px 8px 7px 0px;
      background: var(--100, #fff);
      border-radius: 2px;

      .title {
        color: #1d2129;
        font-size: 16px;

        &::before {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 12px;
          vertical-align: middle;
          background: var(---, #1e89ff);
          content: '';
        }
      }
    }

    .regInfoForm {
      margin: 10px 0;
      padding: 16px 0;
      background: #fff;
      border-radius: 2px;

      :deep(.nancalui-input .nancalui-input__wrapper.nancalui-input--disabled) {
        background: rgba(229, 230, 235, 1);
      }
      :deep(.nancalui-input--disabled .nancalui-input__inner) {
        background: none;
      }
    }

    .assetsInfoForm {
      height: calc(100% - 292px);
      padding: 16px 0;
      overflow: auto;
      background: #fff;
      border-radius: 2px;

      .title {
        display: flex;
        gap: 10px;
        align-items: center;
        align-self: stretch;
        margin: 0 24px 16px 24px;
        padding: 4px 0px;
        background: #f2f6fc;

        &::before {
          display: inline-block;
          width: 3px;
          height: 16px;
          vertical-align: middle;
          background: var(---, #1e89ff);
          content: '';
        }
      }
      :deep(.nancalui-input-number .nancalui-input__wrapper) {
        width: 100%;
        .nancalui-input__inner {
          text-align: left;
        }
      }
      .uploadBtn {
        display: inline-block;

        button {
          width: 124px;
          svg {
            margin-right: 4px;
          }
        }
      }

      .upload {
      }
      .upload-box {
        position: relative;
        display: flex;
        gap: 10px;

        &-icon {
          display: flex;
          gap: 10px;

          .hint {
            display: flex;
            gap: 4px;
            align-items: center;
            padding: 5px 16px;
            background: var(---, #ebf4ff);
            border: 1px solid var(---, #c2deff);
            border-radius: 2px;
          }
          .count {
            display: flex;
            align-items: center;
            padding: 5px 8px;
            font-size: 14px;
            cursor: pointer;

            strong {
              padding: 0 4px;
              color: #1e89ff;
              font-size: 16px;
            }
          }
        }
      }

      .upload-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        width: 100%;
        margin-top: 8px;

        .file {
          display: flex;
          gap: 16px;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0px 8px;
          background: var(---, #f5f7fa);
        }
      }
    }

    .footer {
      display: flex;
      align-items: center;
      align-self: stretch;
      justify-content: flex-end;
      margin-top: 10px;
      padding: 16px;
      background: #fff;
      border-radius: 2px;
    }
  }

  :deep(.nancalui-input--disabled .nancalui-input__inner) {
    background: rgb(229, 230, 235);
  }
  :deep(.nancalui-tree-select .nancalui-tree-select-input) {
    border-color: #cfcfcf;
  }
</style>
