<template>
  <section :class="['container role-manage-list', state.isLzos ? 'isLzos' : '']">
    <section class="tools">
      <div class="row">
        <div class="col">
          策略名称：
          <n-input
            v-model="state.originalFormInline.name"
            placeholder="关键字搜索"
            clearable
            class="search-form"
          />
          算法类型：
          <n-select v-model="state.originalFormInline.algoType" class="search-form" allow-clear>
            <n-option value="HASH" name="哈希" />
            <n-option value="TRUNCATION" name="截断" />
            <n-option value="MASK" name="掩码" />
          </n-select>
          状态：
          <n-select v-model="state.originalFormInline.status" class="search-form" allow-clear>
            <n-option value="ENABLE" name="正常" />
            <n-option value="DISABLE" name="禁用" />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <div class="container-user-header">
      <n-button
        v-if="buttonAuthList.includes('assetsManage_dataSecurity_desensitizationStrategy_add')"
        code="assetsManage_dataSecurity_desensitizationStrategy_add"
        color="primary"
        size="sm"
        variant="solid"
        @click.prevent="
          () => {
            state.showAdd = true
            state.editId = ''
          }
        "
      >
        <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
        新建脱敏策略
      </n-button>
    </div>
    <div class="list-box">
      <CfTable
        actionWidth="180"
        :key="state.tableData"
        ref="tableNoRef"
        :tableConfig="{
          data: state.tableData,
          rowKey: 'id',
        }"
        :table-head-titles="state.tableTitHead"
        :paginationConfig="{
          total: state.pagination.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,
          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            onSearch(false)
          },
          onSizeChange: (v) => {
            state.pagination.pageSize = v
            onSearch()
          },
        }"
      >
        <template #name="{ row }">
          {{ row.name || '--' }}
        </template>
        <template #algoType="{ row }">
          <SvgIcon
            :icon="state.algoTypeIcon[row.algoType]"
            style="width: 16px; height: 16px; position: relative; top: -1px"
          />
          {{ state.algoType[row.algoType] || '--' }}
        </template>
        <template #tableName="{ row }">
          <n-tooltip
            v-for="(item, index) in row.fieldList"
            :key="index"
            :content="item.tableName"
            position="top"
          >
            <span>
              {{ item.fieldName }}
              {{ index === row.fieldList.length - 1 ? '' : '、' }}
            </span>
          </n-tooltip>
        </template>
        <template #scene="{ row }">
          <span
            v-for="(item, index) in row.applicationScenarioList"
            :key="item + index"
            class="each-app"
          >
            {{ state.scene[item] }}
          </span>
        </template>
        <template #status="{ row }">
          <i
            class="status-icon"
            :style="{ background: row.status === 'ENABLE' ? '#04c495' : '#B8B8B8' }"
          ></i>
          {{ row.status === 'ENABLE' ? '正常' : '禁用' }}
        </template>

        <template #editor="{ data: { row } }">
          <view
            class="table-btn"
            v-if="buttonAuthList.includes('assetsManage_dataSecurity_desensitizationStrategy_edit')"
            code="assetsManage_dataSecurity_desensitizationStrategy_edit"
            @click="
              () => {
                state.showAdd = true
                state.editId = row.id
              }
            "
          >
            编辑
          </view>

          <view
            class="table-btn"
            v-if="
              buttonAuthList.includes('assetsManage_dataSecurity_desensitizationStrategy_delete')
            "
            code="assetsManage_dataSecurity_desensitizationStrategy_delete"
            @click="
              () => {
                deleteClick(row)
              }
            "
          >
            删除
          </view>
        </template>
      </CfTable>
    </div>
    <Add :isShow="state.showAdd" :id="state.editId" @close="closeAdd" />
  </section>
</template>

<script setup>
  import { reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { useStore } from 'vuex'
  import Add from './components/Add.vue'
  import { sceneManage } from '@/api'

  const store = useStore()
  //按钮权限
  const { buttonAuthList } = toRefs(store.state.user)

  const { proxy } = getCurrentInstance()
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    showAdd: false,
    showDetail: false,
    tableData: [],
    loading: false,
    total: 0,
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    tableTitHead: [
      { prop: 'name', name: '策略名称', slot: 'name' },
      { prop: 'algoType', name: '算法类型', slot: 'algoType' },
      { prop: 'tableName', name: '脱敏字段', slot: 'tableName' },
      { prop: 'scene', name: '应用场景', slot: 'scene' },
      { prop: 'status', name: '状态', slot: 'status' },
    ],
    originalFormInline: {
      name: '',
      status: null,
      algoType: null,
    },
    formInline: {
      name: '',
      status: null,
      algoType: null,
    },
    scene: {
      VIEW: '浏览',
      SERVICE: '服务',
    },
    algoType: {
      HASH: '哈希',
      TRUNCATION: '截断',
      MASK: '掩码',
    },
    algoTypeIcon: {
      HASH: 'icon-hash',
      TRUNCATION: 'icon-jieduan',
      MASK: 'icon-yanma',
    },
  })

  // 表格操作变化
  const tablePageSizeChange = (data) => {
    state.pagination.currentPage = 1
    state.pagination.pageSize = data
    initTable(false)
  }

  // 表格操作变化
  const tablePageIndexChange = (data) => {
    state.pagination.currentPage = data
    initTable(false)
  }
  const closeAdd = (refresh) => {
    state.showAdd = false
    if (refresh) {
      initTable()
    }
  }
  // 重置
  const resetFn = () => {
    state.originalFormInline = {
      name: '',
      status: null,
      algoType: null,
    }
    searchClickFn()
  }
  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.formInline[key] = state.originalFormInline[key]
    }
    initTable(true)
  }
  // 查询列表
  const initTable = (init = true) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage

    let params = {
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      condition: {
        ...state.formInline,
      },
    }
    state.loading = true
    sceneManage
      .strategyList(params)
      .then((res) => {
        state.loading = false
        if (res.code === 'SUCCESS') {
          res.data.list.forEach((item) => {
            item.functionNames = JSON.stringify(item.functionNames)
          })
          state.tableData = res.data?.list || []
          state.pagination.total = res.data?.total || 0
        }
      })
      .catch(() => {
        state.tableData = []
        state.loading = false
      })
  }
  // 删除
  const deleteClick = (row) => {
    proxy.$MessageBoxService.open({
      title: '提示',
      content: '此操作将永久删除该策略, 是否继续',
      save: () => {
        let params = {
          id: row.id,
        }
        sceneManage.strategyDelete(params).then((res) => {
          if (res.code === 'SUCCESS') {
            initTable(state.tableData.length > 1 ? false : true)
            ElNotification({
              title: '提示',
              message: '删除成功',
              type: 'success',
            })
          }
        })
      },
    })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style scoped lang="scss">
  @import '@/styles/variables.scss';

  .tools {
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 10px 16px;
      .createTime {
        width: 260px;
        margin-right: 32px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          background: #1e89ff;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
      }
    }
  }

  .role-manage-list {
    &.isLzos {
      padding: 0;
    }
    .list-box {
      position: relative;
      height: calc(100% - 102px);
      // padding: 16px;
      background-color: #fff;
      border-radius: 0px 0px 8px 8px;
      .table {
        height: calc(100% - 64px);
      }
      .left {
        .n-button {
          background: $themeBlue;
        }
      }
      .table-content-pagination {
        position: absolute;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        height: 60px;
        padding: 16px;
        :deep(.nancalui-pagination) {
          margin-right: 16px;
        }

        &-total {
          margin-right: 10px;
          color: rgba(0, 0, 0, 0.75);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          span {
            padding: 0 4px;
            color: var(--themeBlue);
          }
        }
      }
    }
    .search-form {
      float: left;
      margin-right: 16px;
      margin-left: 0;
    }
    :deep(.nancalui-range-date-picker-pro__range-picker) {
      height: 32px;
      .nancalui-input__wrapper {
        border: none !important;
      }
    }
    .container-user-header {
      position: relative;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      align-self: stretch;
      padding: 8px;
      font-weight: bolder;
      font-size: 18px;
      font-style: normal;
      background: var(--100, #fff);
      border-radius: 2px;
      &-border {
        position: absolute;
        top: 17px;
        left: 0;
        width: 4px;
        height: 18px;
      }
    }
    :deep(.nancalui-table__thead) {
      th {
        background-color: #e3ecff;
      }
    }
  }
  .table-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    color: $themeBlue;
  }
  .table-btn {
    margin-right: 12px;
    color: #1e89ff;
    cursor: pointer;
  }
  .search-form {
    float: left;
    width: 260px;
    margin-left: 8px;
  }
  .status-icon {
    position: relative;
    top: -2px;
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border-radius: 50%;
    &.status-running {
      background: $themeBlue;
    }
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 100px);
    margin-top: 60px;

    .pic {
      width: 72px;
      height: auto;
      margin-bottom: 10px;
    }

    &-word {
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      text-align: center;
    }
  }
  .each-app {
    display: inline-block;
    align-items: center;
    width: 46px;
    height: 26px;
    margin-right: 6px;
    padding: 1px 4px;
    color: var(----, rgba(0, 0, 0, 0.75));
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 26px;
    line-height: 22px;
    text-align: center;
    background: var(----, #e3ecff);
    border: 1px solid var(---, #a3b4db);
    border-radius: 6px;
  }

  .nancalui-table-page {
    border-top: 1px solid #dcdfe6;
  }
</style>
