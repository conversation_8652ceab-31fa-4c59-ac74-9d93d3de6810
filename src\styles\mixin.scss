@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin textOverflow {
  /*文本强制不换行*/
  white-space: nowrap;
  /*文本溢出显示省略号*/
  text-overflow: ellipsis;
  /*溢出的部分隐藏*/
  overflow: hidden;
}
