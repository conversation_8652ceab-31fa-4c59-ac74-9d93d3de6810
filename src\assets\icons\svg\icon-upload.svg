<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <defs>
        <rect id="path-1" x="0" y="0" width="16" height="16"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数据采集-结构化/新增文件结构化-选择文件（选择模型/文件导入暂无数据）" transform="translate(-613.000000, -229.000000)">
            <g id="内容" transform="translate(230.000000, 96.000000)">
                <g id="4.Input输入/14.Upload上传/基础" transform="translate(369.000000, 126.000000)">
                    <g id="icon" transform="translate(14.000000, 7.000000)">
                        <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="16" height="16"></rect>
                        <g id="Clipped">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <g id="矩形"></g>
                            <g id="编组" mask="url(#mask-2)" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2">
                                <g transform="translate(2.000000, 2.000000)" id="路径">
                                    <polyline points="0 6.00276667 0 12 12 12 12 6"></polyline>
                                    <polyline points="9 3 6 0 3 3"></polyline>
                                    <line x1="5.99723333" y1="8.66666667" x2="5.99723333" y2="0"></line>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>