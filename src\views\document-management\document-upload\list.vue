<template>
  <div class="container">
    <!-- 搜索工具栏 -->
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">非结构化数据编号：</span>
          <n-input v-model="tableState.filterSearch.ruleCode" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">非结构化数据名称：</span>
          <n-input v-model="tableState.filterSearch.name" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">密级：</span>
          <n-select v-model="tableState.filterSearch.confidentialityLevel" :allow-clear="true">
            <n-option
              v-for="item in tableState.secretList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <!-- 主体内容区域：左侧分类树 + 右侧数据列表 -->
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>非结构化数据分类</div>
        </div>
        <categoryTree @node-click="clickFn" />
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onUpload" color="primary" variant="solid">
          <SvgIcon icon="icon-upload" class="nc-m-r-4" />
          上传非结构化数据</n-button
        >
        <div class="table" v-loading="tableState.isLoad">
          <CfTable
            actionWidth="180"
            :table-head-titles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #editor="{ data: { row } }">
              <n-button
                :disabled="
                  !tableState.typeIds.includes(row.categoryId) ||
                  !tableState.fileTypeArr.some((ext) => row.type.endsWith(ext))
                "
                variant="text"
                color="primary"
                @click="onView(row)"
                >预览</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onPush(row)"
                >发布</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-else-if="row.status === 'PUBLISH'"
                @click="onUnder(row)"
                >下架</n-button
              >
              <n-button
                :disabled="!tableState.typeIds.includes(row.categoryId)"
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onEdit(row)"
                >编辑</n-button
              >
              <n-popover trigger="hover">
                <SvgIcon class="nc-m-l-14" icon="icon-btn-more" />
                <template #content>
                  <div>
                    <div class="popover-item" v-if="row.status === 'WAITING_PUBLISH'">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        @click="onUpload(row)"
                        >重新上传</n-button
                      >
                    </div>
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        @click="onRelation(row)"
                        >关联非结构化数据</n-button
                      ></div
                    >
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        @click="onVersion(row)"
                        >版本管理</n-button
                      >
                    </div>
                    <div class="popover-item">
                      <n-button
                        :disabled="!tableState.typeIds.includes(row.categoryId)"
                        variant="text"
                        color="primary"
                        v-if="row.status === 'WAITING_PUBLISH'"
                        @click="onDelete(row)"
                        >删除</n-button
                      >
                    </div>
                  </div>
                </template>
              </n-popover>
            </template>
            <template #tagList="{ row }">
              <level-tag :bgColor="row.bgColor" :borderColor="row.borderColor" :color="row.color">{{
                row.confidentialityLevelName
              }}</level-tag>
            </template>
            <template #versionHeader="{ column }">
              <n-tooltip content="MAIN：最新版本 V?：历史版本" position="tl">
                <template #content>
                  <span>MAIN：最新版本<br />V?：历史版本</span>
                </template>
                版本 <n-icon name="icon-helping"></n-icon>
              </n-tooltip>
            </template>
            <template #version="{ row }">
              <span class="version">{{ row.version }}</span>
            </template>
            <template #status="{ row }">
              <span class="w-publish" v-if="row.status === 'WAITING_PUBLISH'">未发布</span>
              <span class="publish" v-else-if="row.status === 'PUBLISH'">已发布</span>
            </template>
          </CfTable>
        </div>
      </div>
    </section>
    <!-- 关联文档弹窗组件 -->
    <relationDocs
      v-model="tableState.relationDocsVisible"
      :id="tableState.relationDocsId"
      apiType="uploadAssoRelation"
      type="upload"
    />
    <!-- 上传文档弹窗组件 -->
    <uploadModal
      v-model="tableState.uploadVisible"
      :categoryId="tableState.filterSearch.categoryId"
      :id="tableState.uploadId"
      @needRefresh="onSearch"
    />
  </div>
</template>

<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'

  // 导入相关组件和API
  import categoryTree from '../components/categoryTree.vue'
  import relationDocs from '../components/relation-docs.vue'
  import api from '@/api/index'
  import uploadModal from './upload.vue'
  import { tagList } from '../config/tag.js'
  import { getDocumentAuthType } from '@/api/documentManage.js'

  const router = useRouter()
  const route = useRoute()
  const { proxy } = getCurrentInstance()

  // 表格状态管理
  const tableState = reactive({
    typeIds: [], // 用户有权限的类型ID列表
    fileTypeArr: ['text_txt','excel', 'csv', 'word', 'pdf', 'image_jpg', 'image_png','image_jpeg'], // 支持的文件类型
    secretList: [
      // 密级选项列表
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    tableList: [], // 表格数据列表
    filterSearch: {
      // 搜索条件
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
      categoryId: null,
    },
    searchData: {
      // 实际搜索参数
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    },
    tableHeadTitles: [
      // 表格列配置
      { prop: 'ruleCode', name: '非结构化数据编号', width: '200' },
      { prop: 'name', name: '非结构化数据名称', width: '240' },
      { prop: 'categoryName', name: '非结构化数据分类' },
      { prop: 'type', name: '非结构化数据类型' },
      { prop: 'confidentialityLevelName', name: '密级', slot: 'tagList' },
      { prop: 'version', name: '版本', slot: 'version', headerSlot: 'versionHeader' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', width: '160' },
      { prop: 'status', name: '状态', slot: 'status' },
    ],
    pagination: {
      // 分页配置
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    relationDocsVisible: false, // 关联文档弹窗显示状态
    relationDocsId: '', // 当前操作的文档ID
    uploadVisible: false, // 上传弹窗显示状态
    uploadId: '', // 上传文档ID
    isLoad: true, // 加载状态
  })

  // 分类树点击事件处理
  function clickFn(id) {
    tableState.filterSearch.categoryId = id
    onSearch()
  }

  // 执行搜索
  function startSearch() {
    Object.keys(tableState.filterSearch).forEach((key) => {
      tableState.searchData[key] = tableState.filterSearch[key]
    })
    onSearch()
  }

  // 获取当前用户类型权限
  const getCurrentUserTypeAuth = async () => {
    const res = await getDocumentAuthType()
    tableState.typeIds = []
    tableState.typeIds = res.data.map((item) => item.categoryId)
  }

  // 查询数据
  function onSearch(init = true) {
    if (init) {
      tableState.pagination.currentPage = 1
    }
    tableState.isLoad = true
    api.documentManage
      .uploadListPageSearch({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...tableState.searchData,
          categoryId:
            tableState.filterSearch.categoryId === 'all'
              ? null
              : tableState.filterSearch.categoryId,
        },
      })
      .then((res) => {
        // 处理返回数据，添加样式信息
        tableState.tableList = res.data.list.map((i) => {
          const { color, bgColor, borderColor } = tagList[i.confidentialityLevelName]
          return {
            ...i,
            bgColor,
            color,
            borderColor,
          }
        })
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        // 更新路由参数
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }

  // 重置搜索条件
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    tableState.searchData = {
      ruleCode: '',
      name: '',
      confidentialityLevel: '',
    }
    onSearch(true)
  }

  // 上传文档
  function onUpload(row) {
    tableState.uploadVisible = true
    tableState.uploadId = row.id
  }

  // 编辑文档
  function onEdit(row) {
    router.push({ name: 'documentUploadEdit', query: { id: row.id } })
  }

  // 发布文档
  function onPush(row) {
    api.documentManage.uploadPublish({ docId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }

  // 下架文档
  function onUnder(row) {
    api.documentManage.uploadOffline({ docId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        ElNotification({
          title: '提示',
          message: '操作成功！',
          type: 'success',
        })
      }
    })
  }

  // 关联文档
  function onRelation(row) {
    tableState.relationDocsId = row.id
    tableState.relationDocsVisible = true
  }

  // 版本管理
  function onVersion(row) {
    router.push({
      name: 'documentUploadVersion',
      query: { id: row.id, name: row.name, type: 'upload' },
    })
  }

  // 预览文档
  function onView(row) {
    router.push({
      name: 'documentUploadPreview',
      query: { id: row.id },
    })
  }

  // 删除文档
  function onDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条非结构化数据',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.uploadDelete({ docId: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
          }
        })
      },
    })
  }

  // 刷新数据
  function refreshData() {
    onSearch(false)
  }

  // 监听路由参数变化
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        refreshData()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true },
  )

  // 组件挂载时执行
  onMounted(() => {
    refreshData()
  })

  // 暴露方法供外部使用
  defineExpose({ getCurrentUserTypeAuth })
</script>

<!-- 路由守卫配置 -->
<script>
  import { defineComponent } from 'vue'
  export default defineComponent({
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getCurrentUserTypeAuth() // 进入路由时获取用户权限
      })
    },
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .popover-item {
    width: 100px;
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
  .publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .w-publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
</style>
