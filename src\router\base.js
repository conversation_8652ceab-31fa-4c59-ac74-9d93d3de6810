import HOMEPAGE from './routes/homePage.js'
import DATAMANAGEMENT from './routes/dataManagement.js'
import DATADEV from './routes/dataDev.js'
import DATAQUALITY from './routes/dataQuality.js'
import DATAASSETS from './routes/dataAssets.js'
import DATASERVICE from './routes/dataService'
import MODELMA<PERSON><PERSON> from './routes/modelManage'
import PROJECTMANAGE from './routes/projectManage'
import AUDITCENTER from './routes/auditCenter'
import PERSONNELMANAGEMENT from './routes/personnelManagement'
import LOGMANAGEMENT from './routes/logManagement'
import THREEMEMBERMANAGE from './routes/threeMemberManage'
import DATASECURITY from './routes/dataSecurity'
import DOCUMENTmANAGE from './routes/documentManage'
import OFFLINEOPERATION from './routes/offlineOperation'
import PictureQuery from './routes/pictureQuery'
import RealTimeOperation from './routes/realTimeOperation'
// 数据治理
import dataGovernance from './routes/dataGovernance'

export default [
  ...HOMEPAGE,
  ...MODELMANAGE,
  ...DATAMANAGEMENT,
  ...DATADEV,
  ...DATAQUALITY,
  ...DATASERVICE,
  ...DATAASSETS,
  ...PROJECTMANAGE,
  ...AUDITCENTER,
  ...PERSONNELMANAGEMENT,
  ...LOGMANAGEMENT,
  ...THREEMEMBERMANAGE,
  ...DATASECURITY,
  ...DOCUMENTmANAGE,
  ...OFFLINEOPERATION,
  ...PictureQuery,
  ...RealTimeOperation,
  ...dataGovernance,
]
