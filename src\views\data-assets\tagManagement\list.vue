<template>
  <!-- 标签管理列表 -->
  <div class="tag-management-list container">
    <div class="tag-management-list-box">
      <div class="tag-left">
        <PublicLeftTree
          ref="publicTree"
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          :checkedNodes="state.allTableData"
          @treeCheckNode="treeCheckNode"
          @treeUpdateNode="treeUpdateNode"
          @treeDelNode="treeDelNode"
        />
      </div>
      <div class="tag-right">
        <div class="tag-box">
          <div
            class="statistics-list"
            v-for="(item, index) in state.statisticalDataType"
            :key="item.title"
          >
            <img :src="item.icon" alt="" />
            <div class="statistics-list-des">
              <div
                :class="{
                  'statistics-list-des-box': true,
                  active: state.activeStyle === index,
                  'can-click': item.viewable,
                }"
              >
                <div class="top"
                  >{{ item.title }} <SvgIcon v-if="item.viewable" icon="eye-open"
                /></div>
                <div>
                  <span class="number">{{ item.number }}</span>
                  <span class="unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-loading="state.loading">
          <n-public-table
            :isDisplayAction="true"
            :isNeedSelection="false"
            :table-head-titles="state.tableHeadTitles"
            :pagination="state.pagination"
            :tableHeight="state.tableHeight"
            :tableData="state.tableData"
            :actionWidth="180"
            @tablePageChange="tablePageChange"
          >
            <template #pageTop>
              <div class="box-add commonForm-search">
                <div class="top-left">
                  <n-button
                    v-if="buttonAuthList.includes('assetsSupervise_tagManagementList_add')"
                    code="assetsSupervise_tagManagementList_add"
                    color="primary"
                    variant="solid"
                    @click.prevent="createTag"
                    >自定义标签</n-button
                  >
                </div>
                <div class="commonForm">
                  <n-form
                    :inline="true"
                    :data="state.filterSearch"
                    class="demo-form-inline commonForm search-right"
                  >
                    <n-form-item label="时间范围：">
                      <n-range-date-picker-pro
                        v-model="state.filterSearch.time"
                        :placeholder="['开始日期', '结束日期']"
                        format="YYYY-MM-DD"
                        :shortcuts="state.shortcuts"
                        allow-clear
                        @confirmEvent="onSearch"
                      />
                    </n-form-item>
                    <n-form-item label="">
                      <n-input
                        v-model="state.filterSearch.keyword"
                        size="small"
                        placeholder="请输入标签名称"
                        clearable
                        @clear="onSearch"
                      >
                        <template #append>
                          <n-button @click.prevent="onSearch">
                            <n-popover
                              class="item"
                              content="搜索"
                              trigger="hover"
                              :position="['bottom']"
                            >
                              <SvgIcon class="icon_search" icon="icon_search" />
                            </n-popover>
                          </n-button>
                        </template>
                      </n-input>
                    </n-form-item>
                  </n-form>
                </div>
              </div>
            </template>
            <template #editor="{ editor }">
              <div class="edit-box">
                <n-button
                  v-if="buttonAuthList.includes('assetsSupervise_tagManagementList_view')"
                  code="assetsSupervise_tagManagementList_view"
                  class="has-right-border"
                  variant="text"
                  @click.prevent="handleTag(editor)"
                  >查看</n-button
                >
                <n-button
                  v-if="buttonAuthList.includes('assetsSupervise_tagManagementList_edit')"
                  code="assetsSupervise_tagManagementList_edit"
                  class="has-right-border"
                  variant="text"
                  @click.prevent="handleTag(editor, true)"
                  >编辑</n-button
                >
                <n-button
                  v-if="buttonAuthList.includes('assetsSupervise_tagManagementList_delete')"
                  code="assetsSupervise_tagManagementList_delete"
                  class="has-right-border"
                  variant="text"
                  @click.prevent="deleteTag(editor)"
                  >删除</n-button
                >
              </div>
            </template>
          </n-public-table>
        </div>
      </div>
    </div>
    <!--  新增编辑弹框 -->

    <n-modal
      bodyClass="largeDialog"
      :draggable="false"
      :title="state.dialogTitle"
      v-model="state.dialogVisible"
      :close-on-click-overlay="false"
      :append-to-body="false"
      width="560px"
      class="has-top-padding"
      :before-close="hideDialog"
    >
      <div class="content">
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="100px"
          label-align="start"
          :class="{ commonForm: true, 'disable-hide-border disabled-form': state.disabled }"
        >
          <n-form-item label="选择分组：" field="tagGroup">
            <n-cascader
              v-model="state.ruleForm.tagGroup"
              :options="state.options"
              ref="cascader"
              class="cascader-width"
              trigger="click"
              clearable
              filterable
              showPath
              pathMode
              noborder
              placeholder="请选择"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item label="标签名称：" field="tagName">
            <n-input
              v-model="state.ruleForm.tagName"
              maxlength="30"
              placeholder="请输入字段中文名称"
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item class="before-icon label_align_required" label="标签描述：">
            <n-textarea
              v-model="state.ruleForm.description"
              maxlength="200"
              :autosize="{ minRows: 3 }"
              clearable
              show-count
              :disabled="state.disabled"
            />
          </n-form-item>
        </n-form>
      </div>

      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            v-if="!state.disabled"
            color="primary"
            :loading="state.loading"
            size="sm"
            variant="solid"
            @click.prevent="saveNewTag"
            >保 存</n-button
          >
          <n-button size="sm" @click.prevent="hideDialog">取 消</n-button></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { reactive, onMounted, ref, getCurrentInstance, toRefs } from 'vue'
  import { formartTime } from '@/utils/index'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  // import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import ENUM from '@/const/enum'
  import { checkCName } from '@/utils/validate'
  export default {
    name: 'AuthorizedPersonnel',
    setup() {
      // 获取当前组件实例、
      // const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const { proxy } = getCurrentInstance()
      const ruleForm = ref()
      const publicTree = ref()
      const tagGroupProps = {
        //级联 指定绑定的字段
        checkStrictly: true,
        value: 'id',
        label: 'name',
      }
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/tagManagement/${name}.png`, import.meta.url).href //本地文件路径
      }
      const state = reactive({
        tableHeight: 400,
        tableData: {},
        loading: false,
        disabled: false,
        dialogTitle: '新增数据标签',
        dialogVisible: false,
        ruleForm: {
          tagGroup: '',
          tagName: '',
          description: '',
        },
        options: [], // 级联数据
        rules: {
          tagGroup: [{ type: 'array', required: true, message: '请选择分组', trigger: 'change' }],
          tagName: [
            {
              required: true,
              message: '请输入标签名称',
              trigger: 'blur',
            },
          ],
          // tagName: [
          //   {
          //     required: true,
          //     validator: (...args) =>
          //       checkCName(...args, 'assets', 'checkTagName', {
          //         name: state.ruleForm.tagName || null,
          //         id: state.isEdit ? state.editId : null,
          //       }),
          //     trigger: 'blur',
          //   },
          // ],
        },
        statisticalDataType: [
          {
            title: '标签总数',
            number: '--',
            viewable: false,
            icon: getAssetsImages('tag-icon1'),
            unit: '条',
          },
          {
            title: '被使用标签数',
            number: '--',
            viewable: false,
            icon: getAssetsImages('tag-icon2'),
            unit: '条',
          },
          {
            title: '未使用标签数',
            number: '--',
            viewable: false,
            icon: getAssetsImages('tag-icon3'),
            unit: '条',
          },
        ],
        shortcuts: ENUM.SHORTCUTS,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'name', name: '标签名称' },
          { prop: 'groupName', name: '标签类目' },
          { prop: 'description', name: '描述' },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'createByName', name: '创建人' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          keyword: '',
          time: [],
        },
        startTime: '',
        endTime: '',
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          showCheckbox: false,
          showControl: true,
          showLeftIcon: true,
          parentControl: '123',
          childControl: '123',
          validatorModuleName: 'assets',
          validatorApiFnName: 'checkGroupName',
          validatorApiDataKeys: ['name', 'id', 'pid', 'type'],
        },
        ancestorId: 1, //祖先id
        groupId: null, // 选中组id
        isEdit: false, // 是否是编辑
        editId: '',
        treeType: 'TAG', //左侧树Type
      })

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 242 - 86
        },
        //自定义新增标签
        createTag() {
          state.disabled = false
          state.isEdit = false
          state.dialogTitle = '新增数据标签'
          state.dialogVisible = true
        },
        //查看/编辑标签 //查看/编辑标签
        handleTag(editor, isEdit = false) {
          let { name, description, groupId, id } = editor.row
          state.disabled = true
          state.dialogTitle = '查看数据标签'
          state.dialogVisible = true
          state.isEdit = false
          if (isEdit) {
            state.disabled = false
            state.isEdit = true
            state.editId = id
            state.dialogTitle = '编辑数据标签'
          }
          let _tagGroup = methods.findTagGroup(state.options, groupId)
          state.ruleForm.tagGroup = _tagGroup
          state.ruleForm.tagName = name
          state.ruleForm.description = description
        },
        //删除标签
        deleteTag(editor) {
          let { id } = editor.row
          proxy.$MessageBoxService.open({
            title: '是否确认删除该标签',
            content: '删除后该标签将不可使用',
            save: () => {
              api.assets.deleteTagsWithGroupId({ id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除标签成功',
                    type: 'success',
                  })
                  if (state.tableData.list?.length === 1) {
                    methods.initTable(true)
                  } else {
                    methods.initTable()
                  }
                }
              })
            },
          })
        },

        //关闭弹框
        hideDialog() {
          state.dialogVisible = false
          ruleForm.value.resetFields() //重置校验状态
          Object.keys(state.ruleForm).forEach((key) => {
            state.ruleForm[key] = ''
          })
        },
        // 查找联级的id数组，用于查看回显
        findTagGroup(data, id) {
          var vArr = [id] //用来接收相关联父级id的集合
          function shellArr(obj, id) {
            if (obj.length) {
              obj.forEach((item) => {
                if (item.children && item.children.length) {
                  let _item = item.children.filter((row) => row.id === id)
                  if (_item.length) {
                    //这里利用some筛选子级有没有符合条件的，有就重新递归，没有就继续递归
                    vArr.unshift(_item[0].pid)
                    //重新递归
                    if (_item[0].pid !== state.ancestorId) {
                      shellArr(data, _item[0].pid)
                    } else {
                      return
                    }
                  } else {
                    //继续递归
                    shellArr(item.children, id)
                  }
                } else {
                  return
                }
              })
            }
          }
          shellArr(data, id) //调用，传入a为要筛选的数组，传入b为后台返回的id值

          return vArr
        },
        // 弹框保存
        saveNewTag() {
          let initState = false //是否重置页码

          ruleForm.value.validate((valid) => {
            if (valid) {
              let _interfaceName = 'createTagsWithGroupId'
              let _message = '新增成功'
              let _data = {}
              if (state.isEdit) {
                _interfaceName = 'updateTagsWithGroupId'
                _message = '更新成功'
                initState = false
                _data = {
                  description: state.ruleForm.description,
                  groupId: state.ruleForm.tagGroup[state.ruleForm.tagGroup.length - 1],
                  id: state.editId,
                  name: state.ruleForm.tagName,
                }
              } else {
                initState = true
                _data = {
                  description: state.ruleForm.description,
                  groupId: state.ruleForm.tagGroup[state.ruleForm.tagGroup.length - 1],
                  name: state.ruleForm.tagName,
                }
              }
              state.loading = true
              api.assets[_interfaceName](_data)
                .then((res) => {
                  state.loading = false
                  let { success } = res
                  if (success) {
                    state.dialogVisible = false
                    ElNotification({
                      title: '提示',
                      message: _message,
                      type: 'success',
                    })
                    // 重置数据
                    Object.keys(state.ruleForm).forEach((key) => {
                      state.ruleForm[key] = ''
                    })
                    methods.init(initState)
                  }
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        },

        // 初始化form
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              groupId: state.groupId, // 标签组id
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              name: state.filterSearch.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.assets
            .getTagManagementList(data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, {
                  number: index + 1,
                })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 获取左侧树数据
        getTreeData() {
          api.assets.getTagsTree({ type: state.treeType }).then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.options = methods.setTreeRecursion(data)
                // data.forEach((item) => {

                //   item.type = 'ROOT'
                // })
                state.treeData = data
              }
            }
          })
        },
        setTreeRecursion(data) {
          data.map((item) => {
            item.value = item.id
            if (item.children && item.children.length != 0) {
              this.setTreeRecursion(item.children)
            }
          })
          return data
        },
        // 树新增编辑
        treeUpdateNode(item) {
          let { ruleForm, checkItem, isEdit } = item
          let _message = '新增分类成功'
          let _interfaceName = 'createTagsTreeGroup'
          let _data = {}
          if (isEdit) {
            // 编辑
            _message = '编辑分类成功'
            _interfaceName = 'updateTagsTreeGroup'
            _data = {
              description: ruleForm.desc,
              id: checkItem.id,
              level: checkItem.level,
              name: ruleForm.name,
              pid: checkItem.pid,
              type: state.treeType,
            }
          } else {
            // 新增
            _data = {
              description: ruleForm.desc,
              level: checkItem.level + 1,
              name: ruleForm.name,
              pid: checkItem.id,
              type: state.treeType,
            }
          }
          api.assets[_interfaceName](_data)
            .then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: _message,
                  type: 'success',
                })
                publicTree.value.clearFn()
                methods.getTreeData()
              }
            })
            .catch(publicTree.value.clearLoading())
        },
        // 树删除
        treeDelNode(item) {
          if (item.children && item.children.length) {
            ElNotification({
              title: '提示',
              message: '该节点含有子节点不可删除',
              type: 'warning',
            })
          } else {
            proxy.$MessageBoxService.open({
              title: '是否确认删除该分类',
              content: '删除后该分类将不可恢复',
              save: () => {
                api.assets.deleteTagsTreeGroup({ id: item.id }).then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除分类成功',
                      type: 'success',
                    })
                    methods.getTreeData()
                  }
                })
              },
            })
          }
        },
        // 点击选中标签树行
        treeCheckNode(data) {
          if (!data) return
          let { checkItem } = data
          state.groupId = checkItem.id
          state.filterSearch.keyword = null
          state.filterSearch.time = null
          methods.onSearch()
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 搜索
        onSearch() {
          state.startTime = null
          state.endTime = null
          if (state.filterSearch.time) {
            if (state.filterSearch.time[0]) {
              state.startTime = formartTime(state.filterSearch.time[0])
            }
            if (state.filterSearch.time[1]) {
              state.endTime = formartTime(state.filterSearch.time[1], true)
            }
          }
          methods.initTable(true)
        },
        // 获取统计数据
        getStatistics() {
          api.assets.getTagManagementCount().then((res) => {
            let { success } = res
            if (success) {
              let { tagSum, tagUsedSum, tagUnusedSum } = res.data
              state.statisticalDataType[0].number = tagSum
              state.statisticalDataType[1].number = tagUsedSum
              state.statisticalDataType[2].number = tagUnusedSum
            }
          })
        },
        init(init = true) {
          methods.getStatistics()
          methods.getTreeData()
          methods.initTable(init)
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.init(true)
      })

      return {
        state,
        buttonAuthList,
        ruleForm,
        tagGroupProps,
        publicTree,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .tag-management-list {
    &-box {
      display: flex;
      flex-wrap: nowrap;
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
    }
    .tag-left {
      height: calc(100% + 15px);
    }
    .tag-right {
      flex: 1;
      min-width: 0; //解决左侧收起后 右侧数据不复原问题
      padding: 0 16px;
      overflow: hidden;
      .tag-box {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        justify-content: center;
        padding: 20px 20px 0 20px;
        .statistics-list {
          display: flex;
          align-content: center;
          width: 25%;

          img {
            width: 106px;
            height: 106px;
          }
          &-des {
            display: flex;
            align-items: center;
          }
          &-des-box {
            color: #333;
            font-size: 12px;
            &.can-click {
              cursor: pointer;
            }
            .top {
              display: flex;
              align-items: center;
              line-height: 20px;
            }
            .number {
              padding: 0 5px;
              color: #333333;
              font-weight: bolder;
              font-size: 24px;
            }
            .unit {
              color: #666;
            }
            .yy-icon {
              width: 20px;
              color: #c9c9cb;
            }
            &.active {
              .yy-icon {
                color: #0f94d0;
              }
            }
          }
        }
        span {
          color: #25bfff;
        }
      }
    }
    .box-add {
      display: flex;
      justify-content: space-between;
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
    }
    .largeDialog {
      .content {
        .commonForm {
          height: auto;
        }
      }
      .nancalui-form {
        :deep(.cascader-width) {
          width: 100% !important;
          .nancalui-input--disabled {
            .nancalui-input__inner {
              background: none;
            }
          }
        }
      }
    }
  }
</style>
