<template>
  <div class="detail-model">
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="基本信息" id="base">
        <n-form
          class="demo-ruleForm disable-hide-border disabled-form"
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="90px"
          label-position="left"
        >
          <n-form-item label="标签名称：">
            <n-input v-model="state.ruleForm.name" maxlength="30" disabled noborder />
          </n-form-item>
          <n-form-item label="标签分组：">
            <n-input v-model="state.ruleForm.groupName" disabled noborder />
          </n-form-item>

          <n-form-item label="标签描述：" class="input-textarea">
            <n-textarea
              v-model="state.ruleForm.description"
              :rows="3"
              :autosize="{ minRows: 3 }"
              placeholder=""
              maxlength="200"
              disabled
            />
          </n-form-item>
        </n-form>
      </n-tab>
      <n-tab title="数据预览" id="dataPreview">
        <div class="box-add">
          <div class="box-left commonForm">
            <n-form :inline="true" :data="state.filterSearch" label-align="end">
              <n-form-item label="目标模型：">
                <n-select
                  v-model="state.filterSearch.keyword"
                  placeholder="请选择"
                  :allow-clear="true"
                  width="220"
                  @value-change="changeModel"
                  @clear="changeModel"
                  :key="state.key"
                  filter
                >
                  <n-option
                    v-for="item in state.modelList"
                    :key="item.id"
                    :name="item.value"
                    :value="item.id"
                  />
                </n-select>
              </n-form-item>
            </n-form>
          </div>
          <div v-if="state.isShowModel" class="box-right"
            >模型表打标：<span>{{ state.modelTagName }}</span>
            <SvgIcon v-if="state.isTag" class="icon_true" icon="icon-true" />
            <SvgIcon v-else class="icon_true" icon="icon-false" />
          </div>
        </div>
        <div class="table-box">
          <div v-show="state.colH > 100" class="col-box">
            <h5>列打标</h5>
            <n-public-table
              ref="publicPreview"
              :table-head-titles="state.tableHeadTitlesRow"
              :tableData="state.colTableData"
              :showPagination="false"
              :tableHeight="state.colH"
            />
          </div>
          <!-- <div v-show="state.rowH > 100" class="row-box">
            <h5>行打标</h5>
            <n-public-table
              ref="publicTablePreview"
              :key="state.keyCunt"
              :table-head-titles="state.tableHeadTitlesPreview"
              :tableData="state.rowTableData"
              :showPagination="true"
              :pagination="state.paginationPreview"
              :tableHeight="state.rowH"
              @tablePageChange="tablePageChange"
            />
          </div> -->
          <div v-if="state.colH <= 100 && state.rowH <= 100" class="table-no-content">
            <img class="pic-no-conyent" src="@/assets/img/table-no-content.png" alt="暂无内容" />
            <div class="text">暂无打标数据</div>
          </div>
        </div>
      </n-tab>
    </n-tabs>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import insertCss from 'insert-css'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      const router = useRouter()
      const publicTable = ref()
      const publicPreview = ref()
      const publicTablePreview = ref()
      const state = reactive({
        activeName: 'base',
        modelTagName: '已进行模型表打标',
        isTag: true,
        disabled: true,
        detailId: null,
        ruleForm: {
          name: '',
          groupId: '',
          groupName: '',
          description: '',
        },
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        colH: 100,
        rowH: 100,
        isShowModel: false,
        allParamColumns: [],
        tableHeadTitlesPreview: [],
        tableHeadTitlesRow: [
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
          { prop: 'fieldLength', name: '字段长度' },
          { prop: 'description', name: '描述信息' },
        ],
        keyCunt: 0,
        colTableData: {},
        rowTableData: {},
        paginationPreview: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
        batchMarkData: {
          filterType: 'allData',
          filterCondition: [
            // {
            //   colName: '',
            //   operator: '',
            //   value: '',
            //   colType: '',
            // },
          ],
        },
        filterSearch: {
          keyword: null,
        },
        modelList: [],
        key: 0,
      })
      const currentProject = computed(() => store.state.user.currentProject)
      const methods = {
        // 获取模型详情
        getDetail(id) {
          state.loading = true
          api.assets
            .getTagDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data[key]
                })
                state.ruleForm = {
                  ...state.ruleForm,
                }
              }
            })
            .catch(() => {})
        },
        // 表格操作变化
        tablePageChange(data) {
          state.paginationPreview.currentPage = data.currentPage
          state.paginationPreview.pageSize = data.pageSize
          methods.getRowTableData(false)
        },

        getSelect() {
          let data = {
            tagId: parseInt(state.detailId),
            // tagId: 66,
          }
          api.assets
            .getTagModel(data)
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.modelList = data
                state.filterSearch.keyword = state.modelList[0]?.id ?? null
                if (state.modelList.length > 0) {
                  state.isShowModel = true
                  methods.getMetadataList()
                  methods.getDataPreview()
                } else {
                  state.isShowModel = false
                }
              }
            })
            .catch(() => {})
        },
        // 获取批量打标过滤的元数据列表
        getMetadataList() {
          api.model
            .getModeData({
              id: state.filterSearch.keyword,
            })
            .then((res) => {
              // 新增序号属性
              let _allParamColumns = []
              if (res.data && res.data.length) {
                res.data.forEach((item) => {
                  _allParamColumns.push({
                    prop: item.name,
                    name: item.name,
                  })
                })
                state.allParamColumns = _allParamColumns
                methods.getRowTableData()
              }
            })
        },
        changeModel(id) {
          methods.getMetadataList()
          methods.getDataPreview()
        },
        getDataPreview() {
          if (state.filterSearch.keyword && parseInt(state.detailId)) {
            //行打标
            let data = {
              modelId: state.filterSearch.keyword,
              tagId: parseInt(state.detailId),
              // tagId: 66,
            }
            api.assets
              .getDataWithProject(data)
              .then((res) => {
                let { success, data } = res
                if (success) {
                  state.isTag = data?.modelTagged ?? false
                  state.modelTagName = state.isTag ? '已进行模型表打标' : '未进行模型表打标'
                  if (data?.taggedModelColumns && data?.taggedModelColumns.length > 0) {
                    //列打标
                    data?.taggedModelColumns.map((item, index) => {
                      return Object.assign(item, { number: index + 1 })
                    })
                    state.colH = data?.taggedModelColumns.length * 40 + 100
                    state.colTableData = { list: data?.taggedModelColumns }
                  } else {
                    state.colH = 100
                    state.colTableData = { list: [] }
                  }
                }
              })
              .catch(() => {})
          }
          state.key++
        },
        getRowTableData(init = true) {
          state.paginationPreview.currentPage = init ? 1 : state.paginationPreview.currentPage
          if (state.filterSearch.keyword && parseInt(state.detailId)) {
            let data = {
              modelId: state.filterSearch.keyword,
              tagId: parseInt(state.detailId),
              pageNum: state.paginationPreview.currentPage,
              pageSize: state.paginationPreview.pageSize,
            }
            api.assets
              .getDataWithTagId(data)
              .then((res) => {
                let { data, success } = res
                if (success) {
                  if (data.list && data.list.length > 0) {
                    state.tableHeadTitlesPreview = [{ prop: 'number', name: '序号', width: 80 }]
                    if (data && data.list[0]) {
                      let allData = { list: [] }
                      let allTitleProps = []
                      data.list.map((item) => {
                        return Object.assign(item, item.assetsContent)
                      })
                      let noShowParames = ['assetsContent', 'tagInfos', 'assetsIndex', 'ds']
                      state.allParamColumns.forEach((item) => {
                        if (noShowParames.includes(item.prop)) return
                        state.tableHeadTitlesPreview.push({
                          prop: item.prop,
                          name: item.prop,
                        })
                        allTitleProps.push(item.prop)
                      })
                      allTitleProps.push('assetsContent')
                      allTitleProps.push('assetsIndex')

                      //处理大小写问题
                      data.list.forEach((item, index) => {
                        let _itemKeys = []
                        Object.keys(item).forEach((_item) => {
                          _itemKeys.push(_item.toLowerCase())
                        })
                        let _object = {}

                        allTitleProps.forEach((key) => {
                          if (_itemKeys.includes(key.toLowerCase())) {
                            if (key === 'assetsContent' || key === 'assetsIndex') {
                              _object[key] = item[key]
                            } else {
                              _object[key] = item[key.toLowerCase()]
                            }
                          } else {
                            _object[key] = null
                          }
                        })
                        _object['number'] = index + 1
                        allData.list.push(_object)
                      })
                      data.list = allData.list
                    } else {
                      state.tableHeadTitlesPreview = [
                        { prop: 'number', name: '序号', width: 80 },
                        { prop: 'number2', name: '字段一' },
                      ]
                    }
                  }
                  state.rowH = (data.list ? data.list.length : 0) * 40 + 100
                  state.keyCunt++
                  state.rowTableData = data
                } else {
                  tate.rowH = 100
                  state.rowTableData = null
                }
              })
              .catch(() => {})
          }
        },
        //切换
        handleClick(id) {
          switch (id) {
            case 'dataPreview':
              methods.getSelect()

              break
            default:
              methods.getDetail(state.detailId)
          }
        },
      }
      onMounted(() => {
        nextTick(() => {
          state.detailId = router.currentRoute.value.query.id
          methods.getDetail(state.detailId)
        })
      })

      return {
        state,
        publicTable,
        publicPreview,
        publicTablePreview,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $labelWidth: 90px;
  .detail-model {
    color: #333;
    height: 100%;
    position: relative;
    .base-tabs {
      height: 100%;
      :deep(.nancalui-tab__content) {
        height: calc(100% - 40px);

        > div {
          width: 100%;
          height: calc(100% - 30px);
        }
      }
      .box-add {
        height: 64px;
        padding: 16px 0;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
      }
      .box-left {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      .box-right {
        color: #000000;
        font-size: 14px;
        span {
          margin-right: 6px;
        }
      }
      .table-box {
        height: calc(100% - 70px);
        overflow-y: scroll;
        .col-box,
        .row-box {
          h5 {
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            margin: 0;
            margin-bottom: 20px;
          }
        }
        .col-box {
          margin-bottom: 20px;
          // :deep(.common-table) {
          //   max-height: 500px;
          // }
        }
      }
    }
    :deep(.base-tabs) {
      .nancalui-form {
        .nancalui-form__item--horizontal.input-textarea {
          align-items: start;
          .nancalui-form__label {
            align-self: start;
          }
        }
      }
    }
    :deep(.table-no-content) {
      text-align: center;
      margin-top: 100px;
      img {
        width: 266px;
      }
      .text {
        color: var(--el-text-color-secondary);
        font-size: 12px;
        margin-top: 20px;
        line-height: normal;
      }
    }
  }
</style>
