<template>
  <section :class="['container', state.isLzos ? 'isLzos' : '']">
    <section class="container-box">
      <section :class="state.open ? 'tools open' : 'tools'">
        <div class="row">
          <div class="col">
            规则名称：
            <n-input
              v-model="state.originalFormInline.name"
              placeholder="规则名称"
              size="small"
              clearable
            />
            类型：
            <n-select
              v-model="state.originalFormInline.ruleType"
              placeholder="全部类型"
              allow-clear
            >
              <n-option
                v-for="(item, index) in state.ruleTypeOptions"
                :key="index"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
            规则：
            <n-select
              v-model="state.originalFormInline.ruleSource"
              placeholder="规则来源"
              allow-clear
            >
              <n-option
                v-for="(item, index) in state.sourceOptions"
                :key="index"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
            时间范围：
            <n-range-date-picker-pro
              v-model="state.originalFormInline.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              :shortcuts="state.shortcuts"
              allow-clear
            />
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section :class="state.open ? 'table open' : 'table'">
        <div class="table-tree scroll-bar-style dataQuality-index-list-tree">
          <n-input
            class="table-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />
          <CfTtee
            ref="treeRef"
            isFloating
            :check-on-click-node="true"
            :default-expanded-keys="[state.selectedKey]"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          >
            <template #btns="{ node, data: nodeData }">
              <el-popover trigger="click" placement="right-start">
                <template #reference>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    @click="getNodeIdFn(node)"
                  >
                    <circle
                      cx="8"
                      cy="12.5"
                      r="0.5"
                      transform="rotate(-90 8 12.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="8"
                      r="0.5"
                      transform="rotate(-90 8 8)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="3.5"
                      r="0.5"
                      transform="rotate(-90 8 3.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                  </svg>
                </template>
                <ul class="cf-list">
                  <li
                    class="cf-list-item"
                    v-if="node.level < 4"
                    @click="addFn({ ...nodeData, level: node.level })"
                  >
                    <SvgIcon class="tree-icon-tool" icon="icon-tree-add2" />
                    创建分组</li
                  >
                  <li
                    class="cf-list-item"
                    v-if="node.level !== 0 && node.level !== 1"
                    @click="editFn({ ...nodeData, level: node.level }, 'tree')"
                  >
                    <SvgIcon class="tree-icon-tool" icon="icon-tree-edit2" />
                    编辑分组</li
                  >
                  <li
                    class="cf-list-item"
                    v-if="node.level !== 0 && node.level !== 1"
                    @click="delFn({ ...nodeData, level: node.level }, 'tree',node)"
                  >
                    <SvgIcon class="tree-icon-tool" @click="" icon="icon-tree-del" />
                    删除分组</li
                  >
                </ul>
              </el-popover>
            </template>
          </CfTtee>
        </div>
        <div class="table-content">
          <div class="table-content-addBtn">
            <n-button
              v-if="state.buttonAuthList.includes('governanceManage_qualityManage_qualityRule_add')"
              code="governanceManage_qualityManage_qualityRule_add"
              variant="solid"
            >
              <n-dropdown
                :visible="state.addStatus"
                trigger="hover"
                :position="['bottom']"
                align="start"
                :offset="10"
                @toggle="toggleFn"
              >
                <div class="add">
                  <SvgIcon icon="new-add" class="icon" title="新建规则" />新建规则
                  <SvgIcon v-if="state.addStatus" icon="icon-arrow-up" class="arrow" title="下拉" />
                  <SvgIcon v-else icon="icon-arrow-drop" class="arrow" title="上拉" />
                </div>
                <template #menu>
                  <div class="list-menu">
                    <div class="list-menu-item" @click.prevent.stop="showDrawerFn('TEMPLATE')"
                      ><SvgIcon icon="icon-template" class="icon" title="模板" />按模板创建</div
                    >
                    <div class="list-menu-item" @click.prevent.stop="showDrawerFn('CUSTOMED')"
                      ><SvgIcon icon="icon-custom" class="icon" title="自定义" />自定义创建</div
                    >
                  </div>
                </template>
              </n-dropdown>
            </n-button>
          </div>

          <div class="table-content-box">
            <CfTable
              actionWidth="260"
              ref="tableNoRef"
              :tableConfig="{
                data: state.tableData.list,
                rowKey: 'id',
              }"
              :table-head-titles="state.tableHeadTitles"
              :paginationConfig="{
                total: state.pagination.total,
                pageSize: state.pagination.pageSize,
                currentPage: state.pagination.currentPage,
                onCurrentChange: (v) => {
                  state.pagination.currentPage = v
                  onSearch()
                },
                onSizeChange: (v) => {
                  state.pagination.pageSize = v
                  onSearch(true)
                },
              }"
            >
              <template #tableType="{ row }">
                <div class="tableType">
                  <SvgIcon v-if="row.ruleType === 'TABLE'" icon="icon-quality-table" class="icon" />
                  <SvgIcon v-else icon="icon-quality-field" class="icon" />
                  {{ row.ruleType === 'TABLE' ? '表级' : '字段级' }}
                </div>
              </template>
              <template #status="{ row }">
                <div class="status">
                  <div :class="row.ruleDisabled ? 'circle gray' : 'circle'"></div>
                  {{ row.ruleDisabled ? '未启用' : '启用' }}
                </div>
              </template>
              <template #sexomorphicDimension="{ row }">
                <div v-if="row.sexomorphicDimension">
                  <span>{{ sexomorphicFnitemFn(row.sexomorphicDimension) }}</span>
                </div>
                <span v-else>--</span>
              </template>
              <template #relateStatus="{ row }">
                <div
                  :class="{
                    'template-name-box': true,
                    green: row.relateStatus === 'RELATED',
                  }"
                >
                  <span>{{ row.relateStatus === 'RELATED' ? '已关联' : '未关联' }}</span>
                </div>
              </template>
              <template #description="{ row }">
                <div class="description" :title="row.description">
                  {{ row.description }}
                </div>
              </template>

              <template #editor="{ data: { row } }">
                <div class="edit-box">
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityRule_edit',
                      )
                    "
                    code="governanceManage_qualityManage_qualityRule_edit"
                    class="seeDetails has-right-border"
                    variant="text"
                    @click.prevent="seeFn(row)"
                  >
                    查看
                  </n-button>
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityRule_edit',
                      )
                    "
                    code="governanceManage_qualityManage_qualityRule_edit"
                    class="seeDetails has-right-border"
                    :disabled="row.relateStatus === 'RELATED'"
                    variant="text"
                    @click.prevent="editFn(row, 'table')"
                  >
                    编辑
                  </n-button>
                  <n-button
                    v-if="
                      state.buttonAuthList.includes(
                        'governanceManage_qualityManage_qualityRule_delete',
                      )
                    "
                    code="governanceManage_qualityManage_qualityRule_delete"
                    class="seeDetails has-right-border"
                    :disabled="row.relateStatus === 'RELATED'"
                    variant="text"
                    @click.prevent="delFn(row, 'table')"
                  >
                    删除
                  </n-button>
                  <n-button
                    class="seeDetails has-right-border"
                    :disabled="row.relateStatus === 'UNCORRELATED'"
                    variant="text"
                  >
                    关注动态&nbsp;
                    <n-switch
                      size="sm"
                      :disabled="row.relateStatus === 'UNCORRELATED'"
                      v-model="row.attentionDynamic"
                      @change="attentionDynamicChange(row)"
                    />
                  </n-button>
                </div>
              </template>
            </CfTable>
          </div>
        </div>
      </section>
    </section>
    <!--新增分组-->
    <n-modal
      v-model="state.treeDialog"
      :title="(state.treeForm.id ? '编辑' : '创建') + '分组'"
      class="largeDialog has-top-padding"
      width="560px"
      :close-on-click-overlay="false"
      @close="state.treeDialog = false"
    >
      <n-form
        ref="treeFormRef"
        class="form__label_font14"
        :data="state.treeForm"
        :rules="state.treeFormRules"
        label-align="end"
        label-width="100px"
      >
        <n-form-item label="分组名称：" field="name">
          <n-input v-model="state.treeForm.name" :maxLength="30" placeholder="请输入分组名称" />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button @click.prevent="state.treeDialog = false">取 消</n-button>
          <n-button v-loading="state.editLoading" variant="solid" @click.prevent="saveTreeFn"
            >确 定</n-button
          >
        </div>
      </template>
    </n-modal>
    <!-- 配置规则 -->
    <n-drawer
      v-model="state.drawer"
      title=""
      :size="720"
      :esc-key-closeable="false"
      :close-on-click-overlay="false"
      class="template-config-drawer"
      :key="state.drawerKey"
    >
      <div class="n-drawer-body" v-loading="state.pageLoading">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">配置质量规则</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
        </div>
        <div class="n-drawer-body-content">
          <!-- 步骤条 -->
          <customizeSteps ref="customizeStepsRef" :stepsData="state.stepsData" />
          <div class="box-content">
            <!-- 配置信息 -->
            <div v-show="state.activeIndex === 0" class="list scroll-bar-style">
              <basic ref="basicRef" @changeRuleDefinition="changeRuleDefinitionFn" />
            </div>
            <!-- 生成规则 -->
            <div v-show="state.activeIndex === 1" class="list scroll-bar-style">
              <generativeRules ref="generativeRulesRef" />
            </div>
          </div>
        </div>
        <div class="box-operate">
          <n-button size="sm" color="secondary" @click.prevent="closeDrawerFn">取消</n-button>
          <n-button
            size="sm"
            v-if="state.activeIndex !== 0"
            variant="solid"
            color="primary"
            @click.prevent="prevFn"
            >上一步</n-button
          >
          <n-button
            v-if="
              state.activeIndex !== state.stepsData.length - 1 &&
              state.form.basicData.ruleDefinition !== 'SQL'
            "
            class="next"
            size="sm"
            variant="solid"
            color="primary"
            @click.prevent="nextFn"
            >下一步</n-button
          >

          <n-button
            v-if="
              state.activeIndex === state.stepsData.length - 1 ||
              state.form.basicData.ruleDefinition === 'SQL'
            "
            :loading="state.btnLoading"
            class="save"
            size="sm"
            variant="solid"
            color="primary"
            @click.prevent="saveFn"
            >生成规则</n-button
          >
        </div>
      </div>
    </n-drawer>
    <!-- 查看规则 -->
    <n-drawer
      v-model="state.seeRule"
      title=""
      :size="720"
      :esc-key-closeable="false"
      :close-on-click-overlay="false"
      class="template-config-drawer"
      :key="state.drawerKey"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">查看质量规则</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="state.seeRule = false" />
        </div>
        <div class="n-drawer-body-content">
          <div class="content-title">
            <span>基础信息</span>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">规则名称：</div>
            <div class="n-drawer-body-content-label-value" :title="state.seeInfo.fullName">{{
              state.seeInfo.fullName
            }}</div>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">描述：</div>
            <div class="n-drawer-body-content-label-value" :title="state.seeInfo.description">{{
              state.seeInfo.description || '无'
            }}</div>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">规则内容：</div>
            <div class="n-drawer-body-content-label-value" :title="state.seeInfo.ruleContentText">{{
              state.seeInfo.ruleContentText || '无'
            }}</div>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">规则来源：</div>
            <div class="n-drawer-body-content-label-value">{{
              state.seeInfo.ruleSourceText || '无'
            }}</div>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">规则模板：</div>
            <div class="n-drawer-body-content-label-value">{{
              state.seeInfo.templeName || '无'
            }}</div>
          </div>
          <div class="content-title">
            <span>配置信息</span>
          </div>
          <div class="n-drawer-body-content-label">
            <div class="n-drawer-body-content-label-name">阈值规则：</div>
            <div class="n-drawer-body-content-label-value">{{
              state.seeInfo.ruleThresholdListText || '--'
            }}</div>
          </div>
        </div>
        <div class="box-operate">
          <n-button size="sm" color="secondary" @click.prevent="state.seeRule = false"
            >关闭</n-button
          >
        </div>
      </div>
    </n-drawer>
    <!-- 避免样式污染 -->
    <div
      v-html="
        `<style>
  .el-popover.el-popper {
    padding: 6px 0;
    min-width: 120px;
  }
  .popover-tree-name {
    display: flex;
    padding: 5px 12px;
    align-items: center;
    gap: 8px;
    color: var(----, rgba(0, 0, 0, 0.75));
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
  }
  .popover-tree-name:hover {
    background: var(--unnamed, #e3ecff);
    color: var(---, #447dfd);
  }
  .popover-disabled {
    color: var(----, #b8b8b8);
  }
  .popover-disabled:hover {
    background: none;
    color: var(----, #b8b8b8);
  }
  .nancalui-switch.nancalui-switch--sm{
    width: 28px;
  }
    </style>`
      "
    >
    </div>
  </section>
</template>

<script setup>
  import { onMounted, reactive, ref, toRefs, getCurrentInstance } from 'vue'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import customizeSteps from '@/components/customizeSteps'
  import basic from '../rule-template/components/basic'
  import generativeRules from '../rule-template/components/generative-rules'
  import CfTtee from '@/components/cfTtee'
  const store = useStore()
  const router = useRouter()
  import api from '@/api/index'
  // 获取当前组件实例
  const { proxy } = getCurrentInstance()
  const treeRef = ref()
  const treeFormRef = ref()
  const customizeStepsRef = ref()
  const basicRef = ref()
  const generativeRulesRef = ref()
  let nextComponentName = null
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    drawerKey: 1,
    loading: false,
    pageLoading: false,
    drawer: false,
    stepsData: [
      {
        label: '配置信息',
      },
      {
        label: '生成规则',
      },
    ],
    activeIndex: 0, // 所处步数
    expend: false, //展开，收起
    buttonAuthList: [],
    addStatus: false,
    hasLoad: false,
    open: false,
    seeRule: false,
    shortcuts: ENUM.SHORTCUTS,
    originalFormInline: {
      name: '',
      time: [],
      ruleSource: null,
      ruleType: null,
    },
    searchForm: {
      name: '',
      time: [],
      ruleSource: null,
      ruleType: null,
      treeId: null,
    },
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'number', name: '序号', width: '70px' },
      { prop: 'fullName', name: '规则名称', width: '400px' },
      { prop: 'ruleType', name: '类型', slot: 'tableType', width: '110px' },
      {
        prop: 'sexomorphicDimension',
        name: '六性维度',
        slot: 'sexomorphicDimension',
        width: '110px',
      },
      { prop: 'ruleSourceText', name: '规则来源', width: '150px' },
      { prop: 'ruleDisabled', name: '启用状态', slot: 'status', width: '110px' },
      { prop: 'ruleContentText', name: '规则内容', width: '250px' },
      { prop: 'ruleTableFieldListText', name: '关联字段', width: '150px' },
      { prop: 'ruleThresholdListText', name: '监控阈值', width: '250px' },
      { prop: 'relateStatus', name: '关联任务状态', slot: 'relateStatus', width: '150px' },
      { prop: 'description', name: '描述', slot: 'description', width: '150px' },
      { prop: 'createByName', name: '创建人', width: '150px' },
      { prop: 'createTime', name: '创建时间', width: '160px' },
      { prop: 'updateTime', name: '最后修改时间', width: '160px' },
    ],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    sourceOptions: [
      { name: '规则模板', value: 'TEMPLATE' },
      { name: '自定义规则', value: 'CUSTOMED' },
    ],
    ruleTypeOptions: [
      { name: '表级', value: 'TABLE' },
      { name: '字段级', value: 'FIELD' },
    ],
    tableData: [],
    tableHeight: 436,
    treeDialog: false,
    treeSearchText: '',
    treeData: [],
    defaultTreeData: [],
    prop: {
      label: 'name',
      value: 'id',
      children: 'childrenList',
    },
    treeForm: {
      id: null,
      name: null,
      level: null,
      pid: null,
    },
    form: {
      basicData: {},
      ruleTableFieldList: [],
    },
    seeInfo: {},
    expandedKeys: [],
    selectedKey: null,
  })

  // 新增
  const showDrawerFn = (type) => {
    state.drawer = true
    state.drawerKey++
    state.activeIndex = 0
    nextTick(() => {
      state.form.basicData.ruleDefinition = 'CONDITIONAL'
      basicRef.value.editInit({ ruleSource: type, groupId: state.searchForm.treeId })
    })
  }

  // 切换规则定义
  const changeRuleDefinitionFn = (val) => {
    state.form.basicData.ruleDefinition = val
  }

  const closeDrawerFn = () => {
    state.drawer = false
    return false
  }
  // 上一步
  const prevFn = () => {
    state.activeIndex -= 1
    if (state.activeIndex <= 0) {
      state.activeIndex = 0
    }
    customizeStepsRef.value.updatedActive({ index: state.activeIndex })
  }
  // 下一步
  const nextFn = async () => {
    let passed = false
    let result = null
    // 分步校验
    switch (state.activeIndex) {
      case 0:
        // 校验是否通过
        result = await basicRef.value.getAllData()
        passed = result.passed
        state.form.basicData = result.data
        nextComponentName = generativeRulesRef.value
        break
      case 1:
        result = nextComponentName.getAllData()
        if (result.length) {
          passed = true
        } else {
          passed = false
          ElNotification({
            title: '提示',
            message: '数据为空',
            type: 'warning',
          })
        }
        state.form.ruleTableFieldList = result
        break
    }

    if (!passed) return
    if (state.activeIndex++ >= state.stepsData.length - 1) {
      state.activeIndex = state.stepsData.length - 1
    }
    if (state.activeIndex === 1) {
      nextComponentName.editInit(state.form.basicData, true)
    }
    customizeStepsRef.value.updatedActive({ index: state.activeIndex })
  }
  // 保存规则
  const saveFn = async () => {
    let result = await basicRef.value.getAllData()
    let data = { ...result.data }
    let ruleTableFieldList = generativeRulesRef.value.getAllData()
    data.ruleTableFieldList = ruleTableFieldList
    if (!result.passed) {
      return false
    }
    if (data.ruleTableFieldList.length === 0 && data.ruleDefinition !== 'SQL') {
      ElNotification({
        title: '提示',
        message: '请添加表！',
        type: 'warning',
      })
      return false
    }
    if (data.ruleDefinition === 'SQL') {
      data.ruleTableFieldList = null
    }
    let url = 'saveRuleV2'
    if (data.id) {
      url = 'updateRuleV2'
    }
    api.dataQuality[url](data).then((res) => {
      if (res.code === 'SUCCESS') {
        ElNotification({
          title: '提示',
          message: '保存成功',
          type: 'success',
        })
        state.drawer = false
        onSearch(true)
      }
    })
  }

  // 获取树列表
  const getTreeListFn = (status) => {
    api.dataQuality.getRuleTreeListV2({}).then((res) => {
      if (res.code === 'SUCCESS') {
        let treeData = []
        if (res.data.length > 0) {
          treeData = [...res.data]
          treeData[0].selected = true
          state.searchForm.treeId = treeData[0].id
        } else {
          treeData.push({
            children: [],
            id: null,
            level: 0,
            label: '全部',
            type: 'ROOT',
          })
        }
        if (treeData.length > 0) {
          treeData[0].expanded = true
        }
        state.treeData = [...treeData]
        state.defaultTreeData = [...treeData]

        if (!status) {
          nextTick(() => {
            state.expandedKeys = [state.treeData?.[0]?.id || 1]
            state.selectedKey = state.treeData?.[0]?.id || 1
          })
        }
        onSearch(true)
      }
    })
  }
  // 树搜索
  const searchTreeFn = () => {
    state.treeData = filterTreeData(state.defaultTreeData, state.treeSearchText)
  }
  // 循环过滤
  const filterTreeData = (treeData, text) => {
    // 使用map复制一下节点，避免修改到原树
    return treeData
      .map((node) => ({ ...node }))
      .filter((node) => {
        node.children = node.children && filterTreeData(node.children, text)
        return (
          String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
          (node.children && node.children.length)
        )
      })
  }

  const getNodeIdFn=(node)=>{
    state.selectedKey = node.data.id
  }

  // 树点击事件
  const clickFn = (node) => {
    state.selectedKey = node.id
    if (state.searchForm.treeId !== node.id) {
      state.searchForm.treeId = node.id
      onSearch(true)
    }
  }
  const onSearch = (init) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    let startTime = ''
    let endTime = ''
    if (state.searchForm.time && state.searchForm.time[0]) {
      startTime = formartTime(state.searchForm.time[0])
      endTime = formartTime(state.searchForm.time[1], true)
    }
    let data = {
      condition: {
        name: state.searchForm.name || null,
        treeId: state.searchForm.treeId || null,
        ruleSource: state.searchForm.ruleSource || null,
        ruleType: state.searchForm.ruleType || null,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    if (startTime) {
      data.condition.startTime = startTime
      data.condition.endTime = endTime
    }
    state.loading = true
    api.dataQuality
      .getRuleListV2(data)
      .then((res) => {
        state.loading = false
        if (res.code === 'SUCCESS') {
          // 新增序号属性
          res.data.list.map((item, index) => {
            item.ruleSourceText = item.ruleSource === 'CUSTOMED' ? '自定义规则' : '规则模板'
            item.ruleContentText = ruleContentFn(item)
            item.ruleThresholdListText = ruleThresholdListFn(item)
            item.ruleTableFieldListText = ruleTableFieldListFn(item)
            return Object.assign(item, { number: index + 1 })
          })
          state.tableData = res.data
          state.pagination.total = res.data.total
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 转化规则内容
  const ruleContentFn = (item) => {
    if (item.ruleSource === 'CUSTOMED') {
      if (item.ruleDefinition === 'CONDITIONAL') {
        let str = ''
        if (item.ruleConfigInfoList) {
          item.ruleConfigInfoList.forEach((val, ind) => {
            if (ind === 0) {
              str +=
                (ind === 0 ? '' : item.configInfoCondition) +
                '字段值' +
                (val.dataType === 'LENGTH' ? '的长度' : '') +
                val.operation +
                val.dataValue
            }
          })
        }
        return str
      } else if (item.ruleDefinition === 'SQL') {
        return item.sqlScript
      } else {
        return item.regularExpression
      }
    } else {
      return '--'
    }
  }

  // 转化监控阈值
  const ruleThresholdListFn = (item) => {
    let str = ''
    if (item.ruleThresholdList) {
      item.ruleThresholdList.forEach((val) => {
        str +=
          val.type === 'SUCCESS'
            ? '正常阈值' + val.operation + val.operationValue + '；'
            : '告警阈值' + val.operation + val.operationValue
      })
    }
    return str
  }

  // 转化关联字段
  const ruleTableFieldListFn = (item) => {
    if (item.ruleTableFieldList) {
      if (item.ruleType !== 'FIELD') {
        return item.ruleTableFieldList.map((val) => val.tableName).toString()
      } else {
        return item.ruleTableFieldList.length > 0
          ? item.ruleTableFieldList[0].tableName +
              '(' +
              item.ruleTableFieldList.map((val) => val.fieldName).toString() +
              ')'
          : '--'
      }
    } else {
      return '--'
    }
  }

  // 重置
  const resetFn = () => {
    state.originalFormInline = {
      name: '',
      time: [],
      ruleSource: null,
      ruleType: null,
    }
    searchClickFn()
  }
  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.searchForm[key] = state.originalFormInline[key]
    }
    onSearch(true)
  }

  const openFn = () => {
    state.open = !state.open
    setTableHeight()
  }

  const toggleFn = (flag) => {
    state.addStatus = flag
  }

  // 展开或收缩树
  const treeChange = () => {
    if (state.searchForm.treeId) {
      state.treeData = updateTree(state.treeData, state.searchForm.treeId)
    }
  }

  // 点击添加
  const addFn = (item) => {
    state.treeForm = {
      id: null,
      name: '',
      level: item.level,
      pid: item.id,
    }
    state.treeDialog = true
  }
  // 查看
  const seeFn = (item) => {
    console.log(item)
    state.seeInfo = { ...item }
    state.seeRule = true
  }
  // 点击编辑
  const editFn = (item, type) => {
    if (type === 'tree') {
      state.treeForm = {
        id: item.id,
        name: item.name,
        level: item.level - 1,
        pid: item.pid,
      }
      state.treeDialog = true
    } else {
      state.drawer = true
      state.drawerKey++
      state.activeIndex = 0
      nextTick(() => {
        api.dataQuality.getRuleDetailV2({ id: item.id }).then((res) => {
          if (res.success) {
            let data = { ...res.data }
            delete data.createTime
            delete data.createByName
            delete data.updateTime
            state.form.basicData.ruleDefinition = data.ruleDefinition
            if (data.enumConfig) {
              data.enumConfig.enumsList = data.enumConfig.enumsList.map((val) => {
                return { value: val }
              })
            }
            if (data.fieldRangeConfig) {
              if (data.fieldRangeConfig.rangeType === 'DATE_RANGE') {
                data.fieldRangeConfig.date = [
                  data.fieldRangeConfig.startDate.slice(0, 10),
                  data.fieldRangeConfig.endDate.slice(0, 10),
                ]
              } else {
                data.fieldRangeConfig.date = ''
              }
            }
            basicRef.value.editInit(data)
          }
        })
      })
    }
  }
  // 保存业务域
  const saveTreeFn = () => {
    treeFormRef.value.validate((val) => {
      if (val) {
        let data = { ...state.treeForm }
        let url = 'addRuleTreeV2'
        if (data.id) {
          url = 'updateRuleTreeV2'
        }
        api.dataQuality[url](data).then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '保存成功！',
              type: 'success',
            })
            state.treeDialog = false
            getTreeListFn('update')
          }
        })
      }
    })
  }
  // 点击删除
  const delFn = (item, type,node) => {
    if (type === 'tree') {
      proxy.$MessageBoxService.open({
        title: '删除分组',
        content: '删除将会导致该分组下的质量规则丢失，是否继续删除？',
        save: () => {
          api.dataQuality.delRuleTreeV2({ id: item.id }).then((res) => {
            if (res.code === 'SUCCESS') {
              ElNotification({
                title: '提示',
                message: '删除成功',
                type: 'success',
              })
              if(node){
                state.selectedKey=node.data.pid
              }
              getTreeListFn('delete')
            }
          })
        },
      })
    } else {
      proxy.$MessageBoxService.open({
        title: '删除质量规则',
        content: '删除后该质量规则将不可使用，是否继续删除？',
        save: () => {
          api.dataQuality.deleteRuleV2({ id: item.id }).then((res) => {
            if (res.code === 'SUCCESS') {
              ElNotification({
                title: '提示',
                message: '删除成功',
                type: 'success',
              })
              onSearch(true)
            }
          })
        },
      })
    }
  }

  // 循环修改
  const updateTree = (arr, id) => {
    arr.forEach((val) => {
      if (val.id === id) {
        val.selected = true
      } else {
        val.selected = false
      }
      if (val.children) {
        updateTree(val.children, id)
      }
    })
    return arr
  }

  // 表格操作变化
  const pageSizeChange = (val) => {
    state.pagination.pageSize = val
    onSearch(true)
  }
  const pageIndexChange = (val) => {
    state.pagination.currentPage = val
    onSearch()
  }

  // 六性转化
  const sexomorphicFnitemFn = (name) => {
    if (name == 'ACCURACY') {
      return '准确性'
    } else if (name == 'VALIDITY') {
      return '有效性'
    } else if (name == 'COMPLETENESS') {
      return '完整性'
    } else if (name == 'UNIQUENESS') {
      return '唯一性'
    } else if (name == 'CONSISTENCY') {
      return '一致性'
    } else if (name == 'TIMELINESS') {
      return '及时性'
    }
  }

  // 设置表格高度
  const setTableHeight = () => {
    if (state.open) {
      state.tableHeight = document.body.offsetHeight - 412
    } else {
      state.tableHeight = document.body.offsetHeight - 300
    }
  }
  const attentionDynamicChange = ({ attentionDynamic, id }) => {
    api.dataQuality[attentionDynamic ? 'dynamicAdd' : 'dynamicRemove']({ id }).then((res) => {
      if (res.code === 'SUCCESS') {
        // 关注成功
        ElNotification({
          title: '提示',
          message: attentionDynamic ? '关注成功' : '取关成功',
          type: 'success',
        })
      }
    })
  }
  onMounted(() => {
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList
    setTableHeight()
    getTreeListFn()
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    &.isLzos {
      padding: 0;
    }
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      .tools {
        background-color: #fff;
        border-radius: 2px;
        margin-bottom: 10px;
        &.open {
          height: 146px;
        }

        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 50px;
          padding: 10px 16px;
          .createTime {
            margin-right: 32px;
            width: 260px;
          }
          .col {
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }
          :deep(.button-content) {
            .add {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
              .arrow {
                margin-left: 4px;
                color: #fff;
                font-size: 16px;
              }
            }
          }

          .nancalui-input,
          .nancalui-select,
          .nancalui-tree-select {
            width: 260px;
            margin-right: 16px;
          }

          .search {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-btn {
              padding: 5px 16px;
              color: #fff;
              font-weight: 400;
              font-size: 14px;
              border: 1px solid #1e89ff;
              border-radius: 2px;
              background: #1e89ff;
              cursor: pointer;

              &.reset {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;
                color: #1d2129;
                background: #fff;
                border: 1px solid #dcdfe6;
                .icon {
                  margin-left: 4px;
                  font-size: 10px;
                }
              }
            }
          }

          &:first-of-type {
            box-sizing: border-box;
          }
        }
      }

      .table {
        height: calc(100% - 60px);
        margin-top: 8px;
        border-radius: 2px;
        &.open {
          height: calc(100% - 154px);
        }

        .title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 52px;
          padding: 0 16px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          border-bottom: 1px solid #c5d0ea;

          &:before {
            position: absolute;
            top: 17px;
            left: 0;
            width: 4px;
            height: 18px;
            background: var(
              --Radial,
              radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
            );
            border-radius: 0 4px 4px 0;
            content: '';
          }
          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }

        &-tree {
          display: inline-block;
          box-sizing: border-box;
          width: 286px;
          height: 100%;
          padding: 8px 12px;
          vertical-align: top;
          border-right: 1px solid #e5e5e5;
          margin-right: 10px;
          background-color: #fff;
          border-radius: 2px;
          :deep(.tree-box) {
            height: calc(100% - 8px);
            .btn-box {
              opacity: 1;
              background-color: #fff;
            }
          }
          &-ipt {
            margin-bottom: 8px;

            :deep(.nancalui-input-slot__suffix) {
              opacity: 0.5;

              .icon-search {
                font-weight: normal;
                transform: scale(1.4);
              }
            }
          }

          &-header {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 92px;
            height: 32px;
            margin-bottom: 8px;
            color: $themeBlue;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
          }

          :deep(.tree-box) {
            height: calc(100% - 40px);
          }
        }

        &-content {
          display: inline-block;
          box-sizing: border-box;
          width: calc(100% - 10px - var(--aside-width));
          height: 100%;
          vertical-align: top;
          background-color: #fff;
          border-radius: 2px;

          .cfTable {
            height: calc(100% - 48px);
          }

          &-addBtn {
            padding: 8px;
          }
          &-box {
            height: calc(100% - 48px);
          }

          &-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 48px;

            :deep(.nancalui-tabs) {
              width: 100%;

              .nancalui-tabs-nav-tab {
                padding: 0 16px;
              }
            }
          }

          :deep(.nancalui-table) {
            .header-container {
              min-width: 80px;
              .title {
                min-width: 60px;
              }
            }
            .nancalui-table__container {
              &::-webkit-scrollbar-thumb {
                background-color: #b1bcd6;
                border-radius: 6px;
                &:hover {
                  background-color: #b1bcd6;
                }
              }
            }
          }
          :deep(.nancalui-table__empty) {
            padding-top: 80px;
          }
          &-pagination {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            padding: 0 16px;
            background-color: #fff;

            &-total {
              color: rgba(0, 0, 0, 0.46);
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;

              span {
                color: rgba(0, 0, 0, 0.85);
              }
            }
          }
        }
      }
    }
  }
  .tableType {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }
  .description {
    max-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .status {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .circle {
      width: 6px;
      height: 6px;
      margin-right: 8px;
      background-color: #04c495;
      border-radius: 50%;
      &.gray {
        background-color: #b8b8b8;
      }
    }
  }
  .edit-box {
    :deep(.nancalui-button) {
      padding: 0;
      color: $themeBlue;
      &.nancalui-button--text--secondary:disabled {
        color: #c8c9cc;
      }
      .icon {
        font-size: 16px;
      }
      &:last-of-type {
        margin-right: 32px;
      }
    }
  }
  .template-name-box {
    min-width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    span {
      display: inline-block;
      align-content: center;
      width: 52px;
      height: 24px;
      margin-right: 6px;
      padding: 0 4px;
      color: var(----, rgba(0, 0, 0, 0.75));
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background: var(----, #e3ecff);
      border: 1px solid var(---, #a3b4db);
      border-radius: 6px;
    }
    &.green {
      span {
        color: #04c495;
        background: #e6fff4;
        border: 1px solid #04c495;
      }
    }
  }
  .list-menu {
    width: 118px;
    padding: 6px 0;
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 32px;
      padding: 0 12px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      cursor: pointer;
      .icon {
        margin-right: 8px;
        font-size: 16px;
      }
      &:hover {
        color: $themeBlue;
        background-color: #e3ecff;
      }
    }
  }
  .n-drawer-body {
    height: 100%;
    .box-steps {
      justify-content: center;
      border-bottom: none;
    }
    .n-drawer-body-content {
      &-label {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 32px;
        margin-bottom: 8px;
        &-name {
          width: 88px;
          color: #606266;
          font-size: 14px;
        }
        &-value {
          width: calc(100% - 88px);
          color: #1d2129;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .content-title {
        margin-bottom: 16px;
        height: 30px;
        line-height: 30px;
        color: #1d2129;
        font-size: 16px;
        font-weight: 500;
        padding-left: 14px;
        position: relative;
        background-color: #f2f6fc;
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }
      }
    }
    .box-operate {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px;
      padding: 0 16px;
    }
  }

  .nancalui-drawer .n-drawer-body-header {
    position: relative;

    &:before {
      position: absolute;
      top: 17px;
      left: 0;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }
  }

  :deep(
      .rule-template-basic
        .nancalui-form
        .nancalui-form__item--horizontal
        .nancalui-input
        .nancalui-input-slot__prepend
    ) {
    background: #fafafa;
    border: 1px solid #e5e6eb;
    border-right: none;
  }

  :deep(
      .nancalui-select.nancalui-select--disabled .nancalui-select__selection .nancalui-select__input
    ) {
    background: #f5f7fa;
  }
  :deep(
      .rule-template-basic
        .nancalui-form
        .config-box
        .nancalui-input-number
        .nancalui-input__wrapper.nancalui-input--disabled
    ) {
    background: #f5f7fa;
  }
  :deep(.rule-template-basic .nancalui-form .config-box .sql-box .nancalui-textarea) {
    background: #f5f7fa;
  }
  :deep(.rule-template-basic .nancalui-form .config-box .full-box.bg) {
    background: #f5f7fa;
  }

  :deep(.edit-box .nancalui-button:last-of-type) {
    margin-right: 0;
  }
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
