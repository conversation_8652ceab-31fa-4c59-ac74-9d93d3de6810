<template>
  <section class="model-content">
    <div class="projectName">
      <moduleName :info="{ name: '模型详情信息' }" />
      <img class="close" src="@/assets/img/dev/icon-false-gray.png" @click="closeHandle(false)" />
    </div>
    <section class="content">
      <el-form ref="form" :model="modelForm" label-position="top">
        <el-form-item label="所属场景：">
          <el-input
            v-model="projectName"
            disabled
            size="small"
            maxlength="30"
            placeholder="请输入所属场景"
          />
        </el-form-item>
        <!--        <el-form-item label="业务域：">-->
        <!--          <el-input-->
        <!--            v-model="modelForm.name"-->
        <!--            disabled-->
        <!--            size="small"-->
        <!--            maxlength="30"-->
        <!--            placeholder="请输入业务域"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item label="模型层：">
          <el-input
            v-model="modelForm.layerName"
            disabled
            size="small"
            maxlength="30"
            placeholder="请输入模型层"
          />
        </el-form-item>
        <el-form-item label="模型名称：">
          <el-input
            v-model="modelForm.cnName"
            disabled
            size="small"
            maxlength="30"
            placeholder="请输入模型名称"
          />
        </el-form-item>
        <el-form-item label="英文名称：">
          <el-input
            v-model="modelForm.name"
            disabled
            size="small"
            maxlength="30"
            placeholder="请输入英文名称"
          />
        </el-form-item>
        <el-form-item label="描述信息：">
          <el-input
            type="textarea"
            disabled
            :autosize="{ minRows: 3 }"
            size="small"
            maxlength="200"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
    </section>
    <div class="drawer__footer">
      <n-button size="sm" variant="solid" color="primary" @click.stop.prevent="closeHandle(false)"
        >关闭</n-button
      >
    </div>
  </section>
</template>

<script>
  import moduleName from '@/components/ModuleName'
  import { reactive, onMounted, toRef, toRefs, watch } from 'vue'
  import { useStore } from 'vuex'

  export default {
    components: { moduleName },
    props: {
      info: Object,
    },
    emits: ['changeStatus'],
    setup(props, { emit }) {
      const state = reactive({
        modelForm: {
          cnName: '',
          layerName: '',
          name: '',
        },
      })
      const store = useStore()
      const projectName = toRef(store.state.user.currentProject, 'name')
      watch(
        () => props.info,
        (newVal) => {
          state.modelForm = newVal
        },
      )
      const methods = {
        closeHandle(flag) {
          emit('changeStatus', flag)
        },
        saveFn() {},
      }
      onMounted(() => {})
      const params = toRefs(state)
      return {
        ...params,
        projectName,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .model-content {
    position: relative;
    height: 100vh;
    padding-bottom: 60px;
    box-sizing: border-box;

    .projectName {
      background-color: #f7f8fa;
      padding: 8px 20px;
      position: relative;

      .close {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 20px;
        width: 16px;
        height: 16px;
        margin: auto;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      padding: 10px 20px;

      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 20px;

          &__label {
            font-weight: normal;
            padding: 0;
            line-height: 20px;
          }

          &__content {
            .el-select {
              width: 100%;
            }

            .el-input {
              .el-input__inner {
                background-color: #fff;
              }
            }

            .is-disabled {
              .el-input__inner {
                background-color: #f7f8fa;
              }
            }
          }
        }

        .el-form-item:last-of-type {
          margin-bottom: 0;
        }

        .is-required {
          .el-form-item__label {
            //margin-left: 8px;
          }
        }
      }

      .table {
        margin-top: 20px;
      }
    }

    .drawer__footer {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      padding: 12px 20px;
      text-align: center;
      border-top: 1px solid rgba(0, 0, 0, 0.09);
    }
  }
</style>
