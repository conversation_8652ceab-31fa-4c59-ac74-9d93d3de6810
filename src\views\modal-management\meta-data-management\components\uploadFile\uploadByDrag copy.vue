<template>
  <div class="dialogFile">
    <n-modal
      class="largeDialog"
      v-model="isShow"
      width="600px"
      :close-on-click-overlay="false"
      :draggable="false"
      :before-close="cancel"
    >
      <template #header>
        <n-modal-header>
          <n-module-name>{{ dialogTitle }}导入</n-module-name>
        </n-modal-header>
      </template>
      <div class="main">
        <n-upload
          class="upload-drag-demo"
          ref="upload"
          :upload-options="uploadOptions"
          v-model="fileData"
          droppable
          accept=".xls,.xlsx"
          @delete-uploaded-file="handleRemove"
          :on-success="handleSuccess"
          :on-error="handleError"
          @on-change="changeFile"
          @file-over="changeFile"
          @file-drop="changeFile"
          @file-select="changeFile"
        >
          <div class="upload-trigger">
            <div><n-icon name="upload" size="24px" /></div>
            <div style="margin-top: 20px">
              将文件拖到此处，或
              <span class="link">点击上传</span>
            </div>
          </div>
        </n-upload>
        <div class="upload-tip">
          <span>注：仅限上传excel文件</span>
          <n-button size="xs" variant="text" color="primary" @click="handleDownload"
            >模板下载</n-button
          >
        </div>
        <ul class="table uploaded-files" v-if="fileData.length > 0">
          <li v-for="(uploadedFile, index) in fileData" :key="index" class="row">
            <span>{{ uploadedFile.name }}</span>
            <n-icon name="error-o" color="#F66F6A" @click="clearFile" />
          </li>
        </ul>
      </div>
      <template #footer>
        <n-modal-footer style="text-align: center; padding-right: 20px">
          <n-button
            variant="solid"
            size="sm"
            color="primary"
            @click="fileUploads"
            :loading="loadingUp"
            >保存</n-button
          >
          <n-button size="sm" @click="cancel()">取消</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { importMeta } from '@/api/model'
  import api from '@/api/index'
  export default {
    name: 'Index',
    props: {
      showUpload: { type: Boolean, default: false },
      dialogTitle: { type: String, default: '' },
      modelId: { type: Number },
      modelName: { type: String, default: '' },
    },
    data() {
      return {
        fileUrl: 'https://jsonplaceholder.typicode.com/posts/', // 接口地址
        uploadFile: {}, //  存放上传文件的内容
        uploadOptions: { url: 'https://jsonplaceholder.typicode.com/posts/' }, // 接口地址
        fileData: [], // 添加文件展示数据
        loadingUp: false,
      }
    },
    computed: {
      isShow() {
        return this.showUpload
      },
    },
    created() {},
    methods: {
      // 下载
      async handleDownload() {
        // 模板下载
        let objName = 'template/元数据导入模板.xlsx'
        const res = await api.model.fileDownload({
          bucket: 'data-govern',
          objName, //把转义过的地址转回来
        })

        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        } else {
          // 下载文件
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
          })
          const link = document.createElement('a')
          let _fileName = objName.split('/')
          link.download = _fileName[_fileName.length - 1]
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        }

        // const res = await this.$api.model.fileTemplate({ fileName: 'metadata-import-template' })
        // const blob = new Blob([res], {
        //   type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
        // })
        // const link = document.createElement('a')
        // link.download = this.dialogTitle + '导入模板.xlsx'
        // link.style.display = 'none'
        // // link.href = URL.createObjectURL(res)
        // link.href = URL.createObjectURL(blob)
        // document.body.appendChild(link)
        // link.click()
        // URL.revokeObjectURL(link.href)
        // document.body.removeChild(link)
      },
      cancel(init = false) {
        this.loadingUp = false
        this.clearFile()
        this.$emit('cancel', init)
      },
      handleRemove(file, fileList) {},
      handleSuccess(res, file) {
        this.$message.success(file.name + '上传成功')
      },
      handleError(err, file) {
        this.$message.error(file.name + '上传失败')
      },
      changeFile(file) {
        const isLt2M = file[0].size / 1024 / 1024 <= 10
        if (!isLt2M) {
          return this.$message({
            message: '上传文件大小不能超过 10MB!',
            type: 'warning',
          })
        }
        this.uploadFile = file[0]
        this.fileData = file
      },
      clearFile() {
        this.fileData = []
        this.uploadFile = {}
      },
      fileUploads() {
        const formData = new FormData()
        formData.append('file', this.uploadFile)
        // formData.append('name', this.uploadFile.name)
        // formData.append('modelId', this.modelId)
        this.loadingUp = true
        setTimeout(() => {
          this.loadingUp = false
        }, 1000)
        importMeta({ file: formData })
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.$message({
                message: '上传成功',
                type: 'success',
              })
              this.cancel(true)
            } else if (res.code === '705') {
              this.$message({
                message: '文件为空，请重新上传',
                type: 'warning',
              })
            }
          })
          .catch(() => {
            this.loadingUp = false
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .upload-drag-demo {
    width: 300px;
    margin: auto;
  }
  .upload-drag-demo .upload-trigger {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    /* box-sizing: border-box; */
    width: 300px;
    height: 150px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .upload-tip {
    margin: 0 auto;
    width: 300px;
    display: flex;
    justify-content: space-between;
  }
  .uploaded-files {
    margin: 0 auto;
    width: 300px;
    padding: 0;
    li {
      list-style: none;
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
    }
  }
  .upload-demo .upload-trigger .link {
    color: #447dfd;
  }
</style>
