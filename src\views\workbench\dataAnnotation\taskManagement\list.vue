<template>
  <previewStandard
    v-if="state.pageType === 'preview'"
    :info="state.previewStandardInfo"
    @close="state.pageType = 'detail'"
  />
  <div v-show="state.pageType !== 'preview'" class="task-content">
    <div class="btn-group">
      <!-- 新增任务 -->
      <n-button
        v-show="state.pageType === 'list'"
        class="add-task-btn"
        variant="solid"
        @click="addTask"
      >
        新增任务
      </n-button>

      <div v-show="state.pageType === 'detail'" class="btn-group-box">
        <n-button class="add-task-btn" variant="solid" @click="state.pageType = 'list'">
          返回
        </n-button>
        <span class="name">任务名称：{{ state.detailData.customedCategoryName }}</span>
      </div>
    </div>
    <!-- 任务列表 -->
    <div class="task-list">
      <CfTable
        v-show="state.pageType === 'list'"
        actionWidth="220"
        ref="tableNoRef"
        :tableConfig="{
          data: state.dataSource,
          rowKey: 'id',
        }"
        :table-head-titles="tableHead"
        :paginationConfig="{
          total: state.total,
          pageSize: state.searchData.pageSize,
          currentPage: state.searchData.pageNum,
          onCurrentChange: (v) => {
            state.searchData.pageNum = v
            onSearch(false)
          },
          onSizeChange: (v) => {
            state.searchData.pageSize = v
            onSearch()
          },
        }"
      >
        <template #taskNumber="{ row }">
          {{ row.taskAnnotationNum + ' / ' + row.taskTotalNum }}
        </template>
        <template #editor="{ row }">
          <n-button
            class="receive-btn"
            variant="text"
            color="primary"
            @click="goDetail(row, 'detail')"
          >
            文件详情
          </n-button>
          <!-- 分派 -->
          <n-button
            v-if="
              row.confidentialityLevel === 'SECRET' || row.confidentialityLevel === 'CONFIDENTIAL'
            "
            class="receive-btn"
            :disabled="row.submitByName || !row.dispatchTask"
            variant="text"
            color="primary"
            @click="
              receiveTask(
                { ...row, teamId: props.info.teamId, projectCode: props.info.projectCode },
                true,
              )
            "
          >
            分派
          </n-button>
          <n-button
            v-else
            class="receive-btn"
            :disabled="row.auditStatus !== '待领取'"
            variant="text"
            color="primary"
            @click="receiveTask(row, false)"
          >
            领取
          </n-button>
          <n-button
            class="delete-btn"
            v-if="row.auditStatus === '进行中'"
            :disabled="row.submitByName&&row.auditStatus !== '进行中'"
            variant="text"
            color="primary"
            @click="abandonTaskFn(row)"
          >
            放弃
          </n-button>
          <n-button
            class="delete-btn"
            v-if="row.auditStatus === '待领取'"
            :disabled="row.submitByName"
            variant="text"
            color="primary"
            @click="deleteTask(row)"
          >
            删除
          </n-button>
        </template>
      </CfTable>

      <CfTable
        v-show="state.pageType === 'detail'"
        actionWidth="220"
        ref="tableNoRef"
        :tableConfig="{
          data: state.dataSourceDetail,
          rowKey: 'id',
        }"
        :table-head-titles="tableDetailHead"
        :paginationConfig="{
          total: state.page.total,
          pageSize: state.page.pageSize,
          currentPage: state.page.pageNum,
          onCurrentChange: (v) => {
            state.page.pageNum = v
            getDetail(state.docCategoryId, false)
          },
          onSizeChange: (v) => {
            state.page.pageSize = v
            getDetail(state.docCategoryId)
          },
        }"
      >
        <template #editor="{ row }">
          <n-button
            class="receive-btn"
            variant="text"
            color="primary"
            @click="goDetail(row, 'preview')"
          >
            预览
          </n-button>
        </template>
      </CfTable>
    </div>
    <el-drawer
      v-model="state.taskDrawerVisible"
      title=""
      :size="680"
      append-to-body
      :close-on-click-overlay="true"
      :before-close="closeDrawer"
      class="team-drawer_68768798"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">{{ state.isEdit ? '编辑任务配置' : '新增任务配置' }}</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawer" />
        </div>
        <div class="n-drawer-body-content">
          <n-form :data="state.formData" :rules="rules" :label-width="130" ref="formRef">
            <n-form-item label="任务名称：" field="customedCategoryName">
              <n-input v-model="state.formData.customedCategoryName" placeholder="请输入任务名称" />
            </n-form-item>
            <!-- 任务类型 -->
            <n-form-item label="任务类型：" field="taskType">
              <n-select v-model="state.formData.taskType" placeholder="请选择">
                <n-option label="文本标注" value="文本标注" />
                <n-option label="图片标注" value="图片标注" />
                <n-option label="音频标注" value="音频标注" />
                <n-option label="视频标注" value="视频标注" />
              </n-select>
            </n-form-item>

            <!-- 预计完成时间 -->
            <n-form-item label="预计完成时间：" field="completionTime">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.completionTime"
                placeholder="请选择完成时间"
                format="YYYY-MM-DD"
              />
            </n-form-item>
            <!-- 选择文件分类 -->
            <n-form-item label="选择文件分类：" field="documentCategoryId">
              <TreeSelect
                v-model="state.formData.documentCategoryId"
                :data="state.treeData"
                :props="{
                  label: 'name',
                  children: 'children',
                  value: 'id',
                }"
                check-strictly
                :render-after-expand="false"
                placeholder="请选择"
                style="width: 100%"
                size="sm"
              />
            </n-form-item>
          </n-form>
        </div>
        <div class="n-drawer-body-footer">
          <!-- 确定 -->
          <n-button color="primary" variant="solid" @click.prevent="handleTaskSubmit">
            确定
          </n-button>
          <!-- 取消 -->
          <n-button color="primary" @click.prevent="state.taskDrawerVisible = false">
            取消
          </n-button>
        </div>
      </div>
    </el-drawer>
    <AssignTask ref="assignTaskRef" @success="onSearch" />
  </div>
</template>
<script setup>
  import { formartTime, formartTimeDate } from '@/utils/index'
  import { searchData } from '@/api/dataApplication'
  import api from '@/api/index'
  import TreeSelect from '@comp/cfTreeSelect/index.vue'
  import { useRouter } from 'vue-router'
  // 分派任务组件
  import AssignTask from './AssignTask.vue'
  // 预览标注结果
  import previewStandard from '../taskOverview/previewStandard.vue'
  const router = useRouter()
  const { id: projectId } = router.currentRoute.value.query // 任务id
  const formRef = ref(null),
    selectTree = ref(null)
  const assignTaskRef = ref(null)
  const state = reactive({
    pageType: 'list',
    detailData: {}, // 任务详情数据
    previewStandardInfo: { id: null }, // 预览标注结果数据  documentId
    // 文档目录数
    treeData: [],
    taskDrawerVisible: false, // 任务弹窗
    assignTaskModalVisible: false, // 分派任务弹窗
    docCategoryId: '',
    formData: {
      // 表单数据
      taskType: '', // 任务类型
      documentCategoryId: '', // 文件分类
      completionTime: '', // 预计完成时间
      projectId, // 项目id
      labels: '', // 标注标签
      customedCategoryName: '', //任务名称
    },
    dataSource: [], // 表格数据
    dataSourceDetail: [], // 表格数据详情
    searchData: {
      // 搜索数据
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
      condition: {
        projectId,
      },
    },
    total: 0, // 总条数
    page: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
  })
  const rules = {
    taskType: [
      // 任务类型
      { required: true, message: '请选择任务类型', trigger: 'change' },
    ],
    documentCategoryId: [
      // 文件分类
      { required: true, message: '请选择文件分类', trigger: 'change', type: 'number' },
    ],
    completionTime: [
      // 预计完成时间
      { required: true, message: '请输入预计完成时间', trigger: 'change', type: 'date' },
    ],
    customedCategoryName: [
      // 任务名称
      { required: true, message: '请输入任务名称', trigger: 'blur' },
    ],
  }

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const tableHead = computed(() => {
    // 表格表头
    return [
      { name: '任务名称', prop: 'customedCategoryName', width: 500 },
      { name: '任务类型', prop: 'taskType' },
      { name: '预计完成时间', prop: 'completionTime' },
      { name: '领取人', prop: 'submitByName' },
      { name: '已标注/总数', prop: 'taskNumber', slot: 'taskNumber' },
      { name: '状态', prop: 'auditStatus' },
    ]
  })

  const tableDetailHead = computed(() => {
    // 表格表头
    return [
      { name: '文件名称', prop: 'taskName', width: 500 },
      { name: '文件格式', prop: 'documentType' },
    ]
  })

  const goDetail = (row, page) => {
    state.pageType = page
    state.detailData = row
    if (page === 'detail') {
      state.docCategoryId = row.documentCategoryId
      getDetail(row.documentCategoryId)
      state.previewStandardInfo = { id: null }
    } else {
      state.previewStandardInfo.id = row.documentId
      state.previewStandardInfo.taskId = row.taskId
    }
  }
  const addTask = () => {
    state.formData = { projectId }
    state.isEdit = false
    state.taskDrawerVisible = true
  }

  const onSearch = (reset = true) => {
    // 搜索
    if (reset) {
      // 重置页码
      state.searchData.pageNum = 1
    }
    api.dataGovernance.getTaskPageNew(state.searchData).then((res) => {
      // 成功
      if (res.success) {
        state.dataSource = res.data?.list || [] // 表格数据
        state.total = res.data?.total || 0 // 总条数
      }
    })
  }

  // 获取任务详情
  const getDetail = (documentCategoryId, reset = true) => {
    if (reset) {
      // 重置页码
      state.page.pageNum = 1
    }
    api.dataGovernance
      .getTaskPageDetail({
        condition: {
          documentCategoryId,
          projectId,
        },
        pageNum: state.page.pageNum,
        pageSize: state.page.pageSize,
      })
      .then((res) => {
        // 成功
        if (res.success) {
          state.dataSourceDetail = res.data?.list || [] // 任务详情数据
          state.page.total = res.data?.total || 0 // 总条数
        }
      })
  }

  // 获取目录树
  const getTreeData = () => {
    api.documentManage.getClassifyTreeList().then((res) => {
      // 成功
      if (res.success) {
        state.treeData = res.data || [] // 目录树数据
      }
    })
  }
  const closeDrawer = () => {
    // 关闭弹窗
    state.taskDrawerVisible = false
  }
  const handleTaskSubmit = () => {
    // 提交
    formRef.value.validate((valid) => {
      console.log(valid)
      // 验证
      if (valid) {
        // 验证通过
        let data = { ...state.formData }
        data.completionTime = formartTimeDate(state.formData.completionTime, '-')
        api.dataGovernance[state.isEdit ? 'editTask' : 'addTask'](data).then((res) => {
          // 成功
          if (res.success) {
            // 成功
            state.taskDrawerVisible = false // 关闭弹窗
            ElMessage.success('添加成功') // 提示
            onSearch() // 搜索
          }
        })
      } else {
        // 验证不通过
        console.log('error submit!!')
        return false
      }
    })
  }
  // // 编辑任务
  // const editTask = (row) => {
  //   state.formData = {}
  //   state.taskDrawerVisible = true // 打开弹窗
  //   state.formData = {
  //     // 表单数据
  //     ...row,
  //     completionTime: new Date(row.completionTime), // 预计完成时间
  //   }
  //   // 记录是否是编辑任务
  //   state.isEdit = true
  // }
  // 分派
  const receiveTask = (row, type) => {
    assignTaskRef.value.openDrawer(row, projectId, type)
    state.assignFormData = {} // 表单数据
    state.assignFormData.taskId = row.id // 任务ID
    state.assignTaskModalVisible = true // 打开弹窗
  }

  // 放弃
  const abandonTaskFn = (row) => {
    ElMessageBox.confirm('确定放弃该任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      // 确定
      api.dataGovernance
        .abandonTask({
          documentCategoryId: row.documentCategoryId,
          projectId: row.projectId,
        })
        .then((res) => {
          // 成功
          if (res.success) {
            // 成功
            ElMessage.success('放弃成功') // 提示
            onSearch() // 搜索
          }
        })
    })
  }

  // 删除
  const deleteTask = (row) => {
    // 删除
    ElMessageBox.confirm('确定删除该任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      // 确定
      api.dataGovernance
        .deleteTaskMenu({
          documentCategoryId: row.documentCategoryId,
          projectId: row.projectId,
        })
        .then((res) => {
          // 成功
          if (res.success) {
            // 成功
            ElMessage.success('删除成功') // 提示
            onSearch() // 搜索
          }
        })
    })
  }
  onSearch()
  getTreeData()
</script>
<style lang="scss" scoped>
  .task-content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 0;

    .btn-group {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 16px;

      .name {
        margin-left: 10px;
      }
    }

    .task-list {
      width: 100%;
      height: calc(100% - 48px);
    }
  }
</style>
<style lang="scss">
  .team-drawer_68768798 {
    .member-list {
      margin-top: 10px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .el-drawer__header {
      display: none;
    }

    .n-drawer-body {
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 52px;
        padding: 0 16px;
        border-bottom: 1px solid #e5e6eb;

        &-name {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: calc(100% - 30px);
          color: rgba(0, 0, 0, 0.9);
          font-weight: bold;
          font-size: 18px;

          .icon {
            margin-right: 8px;

            font-size: 16px;
          }

          .title {
            width: calc(100% - 24px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }

        .close {
          color: #8091b7;
          font-size: 24px;
          cursor: pointer;
        }
      }

      &-content {
        box-sizing: border-box;
        height: calc(100vh - 116px);
        padding: 16px 16px 0 16px;
        overflow-y: auto;

        &.has-top-padding {
          padding: 16px 16px 0 16px;
        }

        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 8px;

          &:hover {
            background-color: #b1bcd6;
          }
        }

        .nancalui-form {
          .nancalui-form__item--horizontal {
            margin-bottom: 8px;

            &:has(.error-message) {
              margin-bottom: 20px;
            }
          }
        }
      }

      &-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 64px;
        padding: 0 16px;
      }
    }
  }

  .team-drawer_68768798 .member-list > .member-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
  }

  :root .nancalui-flexible-overlay {
    z-index: 3000 !important;
  }
</style>
