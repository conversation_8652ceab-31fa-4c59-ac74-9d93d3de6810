import { Graph, Addon, FunctionExt, Shape } from '@antv/x6'
import './shape'
import { ElNotification } from 'element-plus'
let timeFlag = null
const { Dnd } = Addon

export default class FlowGraph {
  // public static graph: Graph
  // private static stencil: Addon.Stencil
  static init() {
    const contaier = document.getElementById('flowContainer')
    this.graph = new Graph({
      container: contaier,
      width: contaier.offsetWidth - 2,
      height: contaier.offsetHeight,
      interacting: function (cellView) {
        if (
          !cellView.cell.getProp('interacting') &&
          cellView.cell.getProp('interacting') !== undefined
        ) {
          return false
        }
        return true
      },
      virtual: true,
      grid: {
        type: 'mesh',
        size: 20, // 网格大小 10px
        visible: true, // 渲染网格背景
        args: {
          color: '#F5F7FA', // 网格线/点颜色
          thickness: 2, // 网格线宽度/网格点大小
        },
      },
      // resizing:true,// 改变节点大小
      // 画布调整
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      // 滚轮
      mousewheel: {
        enabled: true,
      },
      minimap: {
        enabled: true,
        container: document.getElementById('minimapContainer'),
        width: 250,
        height: 140,
        padding: 15,
        minScale: 0.01,
      },
      // 画布调整
      selecting: {
        enabled: true,
        multiple: false,
        rubberband: true,
        movable: true,
        modifiers: ['ctrl', 'meta'],
        showNodeSelectionBox: false,
        // 修改节点选中样式为不显示任何特殊效果
        nodeStateStyles: {
          selected: {
            opacity: 1,
            attrs: {
              body: {
                fill: 'none',
                stroke: 'none',
              },
            },
          },
        },
      },
      connecting: {
        allowNode: true,
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        highlight: true,
        snap: true,
        allowPort: (args) => {
          const sourceCell = args.sourceCell
          const targetCell = args.targetCell
          if (sourceCell === targetCell) {
            return false
          }
          const edges = this.graph.getEdges()
          const sourceId = sourceCell.id
          const targetId = targetCell.id
          let isConnected = false
          edges.forEach((edge) => {
            if (
              (edge.getSourceCell()?.id === sourceId && edge.getTargetCell()?.id === targetId) ||
              (edge.getSourceCell()?.id === targetId && edge.getTargetCell()?.id === sourceId)
            ) {
              isConnected = true
              return false // 提前退出循环
            }
          })
          return !isConnected
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#8091B7',
                strokeWidth: 1,
                targetMarker: {
                  name: 'classic',
                  size: 8,
                },
              },
            },
            connector: 'normal',
            // router: {
            //   name: 'normal',
            // },
            zIndex: 0,
          })
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          if (!targetMagnet) {
            return false
          }
          return true
        },
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 4,
              stroke: 'rgba(223,234,255)',
            },
          },
        },
      },
      snapline: true,
      history: true,
      clipboard: {
        enabled: true,
      },
      keyboard: {
        enabled: true,
      },
      embedding: {
        enabled: true,
        findParent({ node }) {
          const bbox = node.getBBox()
          return this.getNodes().filter((node) => {
            // 只有 data.parent 为 true 的节点才是父节点
            const data = node.getData()
            if (data && data.parent) {
              const targetBBox = node.getBBox()
              return bbox.isIntersectWithRect(targetBBox)
            }
            return false
          })
        },
      },
    })
    this.initStencil()
    this.initEvent()
    return this.graph
  }

  static initStencil() {
    this.dnd = new Dnd({
      target: this.graph,
      scaled: false,
      animation: true,
      validateNode(droppingNode, options) {
        // console.log('拖拽', droppingNode, options)
      },
    })
  }

  static showPorts(ports, show) {
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden'
    }
  }

  static initEvent() {
    const { graph } = this
    const container = document.getElementById('flowContainer')

    graph.on('node:contextmenu', ({ cell, view }) => {
      const oldText = cell.attr('text/textWrap/text')
      const elem = view.container.querySelector('.x6-edit-text')
      if (elem === null) {
        return
      }
      cell.attr('text/style/display', 'none')
      if (elem) {
        elem.style.display = ''
        elem.contentEditable = 'true'
        elem.innerText = oldText
        elem.focus()
      }

      const onBlur = () => {
        cell.attr('text/textWrap/text', elem.innerText)
        cell.attr('text/style/display', '')
        elem.style.display = 'none'
        elem.contentEditable = 'false'
      }
      elem.addEventListener('blur', () => {
        onBlur()
        elem.removeEventListener('blur', onBlur)
      })
    })
    graph.on(
      'node:mouseenter',
      FunctionExt.debounce(({ cell }) => {
        const ports = container.querySelectorAll('.x6-port-body')
        this.showPorts(ports, true)
      }),
      500,
    )
    graph.on('node:mouseleave', ({ cell }) => {
      const ports = container.querySelectorAll('.x6-port-body')
      this.showPorts(ports, false)
    })

    graph.on('node:collapse', ({ node, e }) => {
      e.stopPropagation()
      node.toggleCollapse()
      const collapsed = node.isCollapsed()
      const cells = node.getDescendants()
      cells.forEach((n) => {
        if (collapsed) {
          n.hide()
        } else {
          n.show()
        }
      })
    })

    graph.on('edge:mouseenter', ({ cell }) => {
      cell.addTools([
        // {
        //   name: 'source-arrowhead',
        // },
        // {
        //   name: 'target-arrowhead',
        //   args: {
        //     attrs: {
        //       fill: 'red',
        //     },
        //   },
        // },
        { name: 'button-remove', args: { distance: -40 } },
      ])
    })

    graph.on('edge:mouseleave', ({ cell }) => {
      cell.removeTools()
    })

    graph.bindKey('backspace', () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.removeCells(cells)
      }
    })
    graph.bindKey('delete', () => {
      const cells = graph.getSelectedCells()
      console.log(cells)
      if (cells.length) {
        graph.removeCells(cells)
      }
    })
  }

  // 销毁
  static destroy() {
    this.graph.dispose()
  }
}
