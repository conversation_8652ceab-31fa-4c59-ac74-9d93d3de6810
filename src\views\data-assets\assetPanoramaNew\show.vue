<template>
  <div class="asset">
    <div class="asset-header">
      <div class="title">资产全景</div>
    </div>
    <div class="asset-content">
      <div class="asset-content-left">
        <div
          v-for="(item, index) in state.leftList"
          :key="index"
          :class="{
            box: true,
            isFirst: item.isFirst,
            isSecond: item.isSecond,
            threeLabel: item.labelList.length === 3,
            isFirstSecond: item.isFirstSecond,
          }"
        >
          <div class="box-title">
            <img class="img" :src="getAssetsImages('left_0' + (index + 1))" alt="" />{{ item.name }}
          </div>
          <div class="box-content">
            <div v-for="(val, ind) in item.labelList" :key="ind" class="label">
              <div class="num">
                {{ val.assetsValue }}<span class="unit">{{ val.unit }}</span>
              </div>
              <div class="name">{{ val.name }}</div>
            </div>
          </div>
          <img class="bg" :src="getAssetsImages('left_bg_0' + (index + 1))" alt="" />
        </div>
      </div>

      <div class="asset-content-center">
        <div class="asset-content-center-top">
          <div
            v-for="(item, index) in state.topList"
            :key="index"
            :class="{
              box: true,
              isFirst: item.isFirst,
              isSecond: item.isSecond,
              threeLabel: item.labelList.length === 3,
            }"
          >
            <div class="box-title">
              <img class="img" :src="getAssetsImages('top_0' + (index + 1))" alt="" />{{
                item.name
              }}
            </div>
            <div class="box-content">
              <template v-if="item.isTwoRow">
                <div class="row">
                  <div :key="-1" class="label">
                    <img class="pic" src="@/assets/img/assets/assetPanorama/resource.png" alt="" />
                    <div class="name">资源库</div>
                  </div>
                  <template v-for="(val, ind) in item.labelList" :key="ind">
                    <div v-if="ind < 5" class="label">
                      <div class="num">
                        {{ val.assetsValue }}<span class="unit">{{ val.unit }}</span>
                      </div>
                      <div class="name">{{ val.name }}</div>
                    </div>
                  </template>
                </div>
                <div class="row">
                  <div :key="-1" class="label">
                    <img class="pic" src="@/assets/img/assets/assetPanorama/asset.png" alt="" />
                    <div class="name">资产库</div>
                  </div>
                  <template v-for="(val, ind) in item.labelList" :key="ind">
                    <div v-if="ind > 4" class="label">
                      <div class="num">
                        {{ val.assetsValue }}<span class="unit">{{ val.unit }}</span>
                      </div>
                      <div class="name">{{ val.name }}</div>
                    </div>
                  </template>
                </div>
              </template>
              <template v-else>
                <div v-for="(val, ind) in item.labelList" :key="ind" class="label">
                  <div class="num">
                    {{ val.assetsValue }}<span class="unit">{{ val.unit }}</span>
                  </div>
                  <div class="name">{{ val.name }}</div>
                </div>
              </template>
            </div>
            <img class="bg" :src="getAssetsImages('top_bg_0' + (index + 1))" alt="" />
          </div>
        </div>
        <div class="asset-content-center-middle">
          <BloodMap />
        </div>
        <div class="asset-content-center-bottom">
          <div
            v-for="(item, index) in state.bottomList"
            :key="index"
            :class="{ box: true, hot: index === 2 }"
          >
            <div class="box-title">
              <img class="img" src="@/assets/img/assets/assetPanorama/bottom_01.png" alt="" />{{
                item.name
              }}
              <el-select
                v-if="item.code === 'DATA_INCREASE_TOP'"
                v-model="state.topResourceType"
                placeholder="请选择资源类型"
              >
                <el-option
                  v-for="val in state.resourceTypeList"
                  :key="val.value"
                  :label="val.label"
                  :value="val.value"
                />
              </el-select>
              <el-select
                v-if="item.code === 'DATA_INCREASE_NUM'"
                v-model="state.numResourceType"
                placeholder="请选择资源类型"
              >
                <el-option
                  v-for="val in state.resourceTypeList"
                  :key="val.value"
                  :label="val.label"
                  :value="val.value"
                />
              </el-select>
            </div>
            <div class="box-content">
              <div class="progress-box">
                <template
                  v-if="item.code === 'DATA_INCREASE_TOP' || item.code === 'DATA_INCREASE_NUM'"
                >
                  <div
                    v-for="(val, ind) in item.tables.filter(
                      (v) =>
                        v.resourceType ===
                        (item.code === 'DATA_INCREASE_TOP'
                          ? state.topResourceType
                          : state.numResourceType),
                    )[0]?.tableInfoDescs"
                    :key="ind"
                    class="progress"
                  >
                    <div class="progress-num">{{ ind + 1 }}</div>
                    <div class="progress-name" :title="val.departName">
                      {{ val.departName }}
                    </div>
                    <div class="progress-bar">
                      <div
                        class="progress-bar-percent"
                        :style="
                          'width: ' +
                          (val.value * 100) /
                            item.tables.filter(
                              (v) =>
                                v.resourceType ===
                                (item.code === 'DATA_INCREASE_TOP'
                                  ? state.topResourceType
                                  : state.numResourceType),
                            )[0]?.tableInfoDescs[0]?.value +
                          '%'
                        "
                      ></div>
                    </div>
                    <div class="progress-value" :title="val.value + val.unit">
                      {{ val.value }}{{ val.unit }}
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div
                    v-for="(val, ind) in item.tables[0].tableInfoDescs"
                    :key="ind"
                    class="progress"
                  >
                    <div class="progress-num">{{ ind + 1 }}</div>
                    <div class="progress-name" :title="val.value">
                      {{ val.value }}
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="asset-content-right">
        <div
          v-for="(item, index) in state.rightList"
          :key="index"
          :class="{
            box: true,
            isFirst: item.isFirst,
            isSecond: item.isSecond,
            threeLabel: item.labelList.length === 3,
            fourLabel: item.labelList.length === 4,
          }"
        >
          <div class="box-title">
            <img class="img" :src="getAssetsImages('right_0' + (index + 1))" alt="" />{{
              item.name
            }}
          </div>
          <div class="box-content">
            <div v-for="(val, ind) in item.labelList" :key="ind" class="label">
              <div class="num">
                {{ val.assetsValue }}<span class="unit">{{ val.unit }}</span>
              </div>
              <div class="name">{{ val.name }}</div>
            </div>
          </div>
          <img class="bg" :src="getAssetsImages('right_bg_0' + (index + 1))" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import BloodMap from './components/BloodMap.vue'
  import { getConfList } from '@/api/dataMap.js'

  const state = reactive({
    leftList: [
      {
        name: '数据源情况',
        code: 'DATASOURCE',
        labelList: [],
        isFirst: true,
        isFirstSecond: true,
      },
      {
        name: '主数据',
        code: 'MASTER_DATA',
        labelList: [],
        isSecond: true,
      },
      {
        name: '主题数据',
        code: 'THEME_DATA',
        labelList: [],
      },
      {
        name: '算法模型',
        code: 'ALGORITHM_MODEL',
        labelList: [],
      },
      {
        name: '标签引用数',
        code: 'NUMBER_OF_TAG_REFERENCES',
        labelList: [],
      },
      {
        name: '标签设计数',
        code: 'NUMBER_OF_LABEL_DESIGNS',
        labelList: [],
      },
      {
        name: '全局数据模型实体总数',
        code: 'GLOBAL_DATA_MODEL',
        labelList: [],
      },
    ],
    topList: [
      {
        name: '数据库情况',
        code: 'DATABASE',
        labelList: [],
        isFirst: true,
        isTwoRow: true,
      },
      {
        name: '工业设备',
        code: 'INDUSTRIAL_EQUIPMENT',
        labelList: [],
        isFirst: true,
      },
      {
        name: '管控中心指标',
        code: 'CONTROL_CENTER_INDICATORS',
        labelList: [],
        isFirst: true,
      },
    ],
    rightList: [
      {
        name: '非结构化数据',
        code: 'UNSTRUCTURED_DATA',
        labelList: [],
        isFirst: true,
      },
      {
        name: '元数据',
        code: 'METADATA',
        labelList: [],
        isSecond: true,
      },
      {
        name: '图谱场景主题',
        code: 'GRAPH_SCENE_THEME',
        labelList: [],
      },
      {
        name: '数据应用场景数',
        code: 'APPLICATION_SCENARIOS',
        labelList: [],
      },
      {
        name: '数据服务',
        code: 'DATA_SERVICE',
        labelList: [],
      },
      {
        name: '数据服务本月调用',
        code: 'DATA_SERVICE_CALLED_THIS_MONTH',
        labelList: [],
      },
      {
        name: '数据标准',
        code: 'DATA_STANDARDS',
        labelList: [],
      },
    ],
    bottomList: [
      {
        name: '数据日增量TOP5',
        code: 'DATA_INCREASE_TOP',
        tables: [],
      },
      {
        name: '数据日增条数TOP5',
        code: 'DATA_INCREASE_NUM',
        tables: [],
      },
      {
        name: '热门搜索TOP5',
        code: 'HOT_SEARCH',
        tables: [{ tableInfoDescs: [] }],
      },
    ],
    resourceTypeList: [
      { label: '资源库', value: '资源库' },
      { label: '资产库', value: '资产库' },
    ],
    topResourceType: '资源库', // 数据日增量类型
    numResourceType: '资产库', // 数据日增条数类型
  })

  // 获取图片
  const getAssetsImages = (name) => {
    return new URL(`/src/assets/img/assets/assetPanorama/${name}.png`, import.meta.url).href //本地文件路径
  }

  // 获取列表数据
  const getData = async () => {
    const res = await getConfList()
    const { success, data } = res
    if (success) {
      data.forEach((val) => {
        // 截取名称
        if (val.codes) {
          val.codes.forEach((item) => {
            if (item.name?.split('-')[1]) {
              item.name = item.name?.substring(item.name?.lastIndexOf('-') + 1)
            }
          })
        }
        state.topList.forEach((v) => {
          if (v.code === val.labelCode) {
            v.name = val.name
            v.labelList = val.codes
          }
        })
        state.leftList.forEach((v) => {
          if (v.code === val.labelCode) {
            v.name = val.name
            if (val?.codes?.length === 1) {
              val.codes[0].name = ''
            }
            v.labelList = val.codes
          }
        })
        state.rightList.forEach((v) => {
          if (v.code === val.labelCode) {
            v.name = val.name
            if (val?.codes?.length === 1) {
              val.codes[0].name = ''
            }
            v.labelList = val.codes
          }
        })
        state.bottomList.forEach((v) => {
          if (v.code === val.labelCode) {
            v.name = val.name
            v.tables = val.tables
          }
        })
      })
    }
  }

  onMounted(() => {
    getData()
  })
</script>

<style lang="scss" scoped>
  .asset {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 16px;
    overflow: auto;
    border-radius: 2px;
    box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.18);
    .asset-header {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 52px;
      background-color: #fff;
      border-bottom: 1px solid #dcdfe6;
      border-radius: 8px 8px 0 0;
      .title {
        position: relative;
        width: 100%;
        height: 24px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        text-align: left;
        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background-color: #1e89ff;
          content: '';
        }
      }
    }
    .asset-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      width: 100%;
      background-color: #fff;

      .asset-content-left,
      .asset-content-right {
        box-sizing: border-box;
        width: 338px;
        padding: 16px;
        .box {
          margin-bottom: 16px;
          &:last-of-type {
            margin-bottom: 0;
          }
        }
      }
      .asset-content-center {
        box-sizing: border-box;
        width: calc(100% - 676px);
        overflow: hidden;
        .asset-content-center-top {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          height: 214px;
          padding: 16px 0;
          .box {
            width: 28%;
            &:first-of-type {
              width: calc(44% - 32px);
              overflow: auto;
              &.isFirst {
                .box-content {
                  .label {
                    margin: 0 10px;
                  }
                }
              }
            }
          }
        }
        .asset-content-center-middle {
          position: relative;
          width: 100%;
          height: 680px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
        }
        .asset-content-center-bottom {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          box-sizing: border-box;
          padding: 16px 0;
          .box {
            width: calc(40% - 16px);
            height: 202px;
            .box-title {
              height: 30px;
            }
            &.hot {
              width: 20%;
              .box-content {
                .progress-box {
                  .progress {
                    .progress-name {
                      width: 140px;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .box {
        position: relative;
        box-sizing: border-box;
        width: 306px;
        height: 127.6px;
        padding: 16px;
        background: #f5f7f9;
        border-radius: 10px;
        &.isFirst,
        &.isSecond {
          height: 182px;
          .box-content {
            .label {
              margin: 0 18px;
              .num {
                font-size: 18px;
                .unit {
                  font-size: 14px;
                }
              }
            }
          }
        }
        &.isSecond {
          height: 180px;
          .box-content {
            .label {
              margin: 2px 8px;
            }
          }
        }
        &.threeLabel {
          .box-content {
            .label {
              margin: 2px 4px;
            }
          }
        }
        &.isFirstSecond {
          .box-content {
            .label {
              margin: 2px 12px;
            }
          }
        }
        &.fourLabel {
          .box-content {
            .label {
              width: 80px;
              text-align: center;
            }
          }
        }
        &:hover {
          background: linear-gradient(
            112deg,
            rgba(30, 137, 255, 0.06) 0%,
            rgba(235, 244, 255, 0.6) 97.81%
          );
        }

        .box-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;
          .img {
            width: 24px;
            height: 24px;
            margin-right: 10px;
          }
          .el-select {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 160px;
            margin: auto;
            :deep(.el-select__wrapper) {
              min-height: 28px;
              border-radius: 6px;
            }
          }
        }
        .box-content {
          position: relative;
          z-index: 2;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;
          height: calc(100% - 24px);
          .row {
            display: flex;
            align-items: center;
            //justify-content: center;
            width: 100%;
            .label:first-of-type {
              .pic {
                display: block;
                width: 20px;
                height: 20px;
                margin: 4px auto;
              }
              .name {
                color: #1d2129;
                font-weight: bolder;
              }
            }
          }
          .label {
            flex-shrink: 0;
            margin: 0 30px;
            text-align: center;
            .num {
              color: #1d2129;
              font-weight: bolder;
              font-size: 24px;
              .unit {
                font-size: 18px;
              }
            }
            .name {
              color: #606266;
              font-size: 12px;
            }
          }
          .progress-box {
            width: 100%;
            .progress {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 6px;
              &:last-of-type {
                margin-bottom: 0;
              }
              &:nth-of-type(2) {
                .progress-num {
                  background-color: #f0b100;
                }
                .progress-bar {
                  .progress-bar-percent {
                    background-color: #f0b100;
                  }
                }
              }
              &:nth-of-type(3) {
                .progress-num {
                  background-color: #31b046;
                }
                .progress-bar {
                  .progress-bar-percent {
                    background-color: #31b046;
                  }
                }
              }
              &:nth-of-type(4) {
                .progress-num {
                  background-color: #224ecd;
                }
                .progress-bar {
                  .progress-bar-percent {
                    background-color: #224ecd;
                  }
                }
              }
              &:nth-of-type(5) {
                .progress-num {
                  background-color: #1e89ff;
                }
                .progress-bar {
                  .progress-bar-percent {
                    background-color: #1e89ff;
                  }
                }
              }
              .progress-num {
                width: 20px;
                height: 16px;
                color: #fff;
                font-size: 12px;
                line-height: 16px;
                text-align: center;
                background: #fe8624;
                border-radius: 8px;
              }
              .progress-name {
                width: 60px;
                margin-left: 4px;
                overflow: hidden;
                color: #606266;
                font-size: 12px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .progress-bar {
                position: relative;
                width: 248px;
                height: 10px;
                margin-left: 8px;
                overflow: hidden;
                border-radius: 4px;
                .progress-bar-percent {
                  width: 0;
                  max-width: 100%;
                  height: 10px;
                  background-color: #fe8624;
                  border-radius: 4px;
                }
              }
              .progress-value {
                width: 50px;
                margin-left: 4px;
                overflow: hidden;
                color: #1d2129;
                font-size: 12px;
                white-space: nowrap;
                text-align: left;
                text-overflow: ellipsis;
              }
            }
          }
        }
        .bg {
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 1;
          width: 110px;
          height: auto;
        }
      }
    }
  }
</style>
