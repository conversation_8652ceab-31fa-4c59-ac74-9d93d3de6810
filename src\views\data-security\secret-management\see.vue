<template>
  <section :class="['container', state.isLzos ? 'isLzos' : '']" v-loading="state.loading">
    <div class="form-box-bottom">
      <div class="common-section-header">
        <div class="title">{{ state.tableName }}</div>
        <div :class="[state.tableConfidentialityLevel, 'tag']"
          >{{ matchingCname(state.tableConfidentialityLevel) }}
        </div>
        <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
      </div>
      <div class="table-box">
        <div class="table-box-content">
          <CfTable
            :isDisplayAction="false"
            :showPagination="false"
            :tableConfig="{
              data: state.tableData,
              rowKey: 'id',
            }"
            :table-head-titles="state.tableHeadTitles"
            smallImg
            :paginationConfig="{
              total: state.pagination.total,
              pageSize: state.pagination.pageSize,
              currentPage: state.pagination.currentPage,
              onCurrentChange: (v) => {
                state.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #wideFieldType="{ row }">
              <div>{{ matchingFieldTypeCname(row.wideFieldType) }} </div>
            </template>

            <template #confidentialityLevel="{ row }">
              <div v-if="state.editStatus">
                <n-select
                  v-model="row.confidentialityLevel"
                  :options="state.secretOptions"
                  size="sm"
                />
              </div>
              <div
                v-else-if="!state.editStatus && row.confidentialityLevel"
                :class="['tag', row.confidentialityLevel]"
              >
                {{ matchingCname(row.confidentialityLevel) }}
              </div>
            </template>
          </CfTable>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  const router = useRouter()
  const store = useStore()
  //按钮权限
  const { buttonAuthList } = toRefs(store.state.user)
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    loading: false,
    formInline: {
      confidentialityLevel: null,
      name: null,
    },

    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'number', name: '序号' },
      { prop: 'cnName', name: '中文名称' },
      { prop: 'name', name: '英文名称' },
      { prop: 'wideFieldType', name: '字段类型', slot: 'wideFieldType' },
      { prop: 'length', name: '字段长度' },
      // { prop: 'confidentialityLevel', name: '字段密级', slot: 'confidentialityLevel' },
    ],
    allTableData: [], //后端返回的全量数据
    activeTableData: [], //当前编辑的全量数据
    tableData: [],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      // pageSizes: [1, 2, 3],
    },
    secretOptions: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
    editStatus: false, //编辑状态
    seeId: null,
    tableName: null,
    tableConfidentialityLevel: null, //表密级
  })
  // 取消
  const cancelFn = () => {
    router.go(-1)
  }
  //匹配字段类型中文名
  const matchingFieldTypeCname = (wideFieldType) => {
    let cName = ''
    switch (wideFieldType) {
      case 'NUMBER':
        cName = '数字'
        break
      case 'TEXT':
        cName = '文本'
        break
      case 'DATETIME':
        cName = '时间'
        break
      default:
        cName = '--'
        break
    }
    return cName
  }
  //匹配中文
  const matchingCname = (confidentialityLevel) => {
    let cName = ''
    switch (confidentialityLevel) {
      case 'PUBLIC':
        cName = '公开'
        break
      case 'INTERIOR':
        cName = '内部'
        break
      case 'CONTROLLED':
        cName = '受控'
        break
      case 'SECRET':
        cName = '秘密'
        break
      case 'CONFIDENTIAL':
        cName = '机密'
        break
      case 'CORE':
        cName = '核心'
        break

      default:
        cName = '--'
        break
    }
    return cName
  }
  //搜索
  const onSearch = (clear = false) => {
    if (clear) {
      state.formInline.confidentialityLevel = null
      state.formInline.name = null
    }
    getTableData(true)
  }
  const setTableHeight = () => {
    state.tableHeight = document.body.offsetHeight - 200
  }
  // 表格操作变化
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    state.tableData = {
      list: startPagination(state.activeTableData),
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      total: state.activeTableData.length || 0,
    }
  }
  setTableHeight()
  //获取数据
  const getTableData = (init = false) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    state.loading = true
    api.assets
      .getAssetsTableFields({
        assetsId: state.seeId,
        confidentialityLevel: state.formInline.confidentialityLevel || null,
        name: state.formInline.name || null,
      })
      .then((res) => {
        let { success, data } = res
        state.loading = false
        if (success) {
          data.feildList.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          state.allTableData = JSON.parse(JSON.stringify(data.feildList)) //保存全量数据
          state.activeTableData = JSON.parse(JSON.stringify(data.feildList)) //保存编辑的全量数据

          console.log(data)
          state.tableData = data.feildList
          state.pagination.total = data.feildList.length || 0
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 前端分页
  const startPagination = (data) => {
    let { currentPage, pageSize } = state.pagination
    let starPos = (currentPage - 1) * pageSize
    let endPos = currentPage * pageSize
    // 更新表格
    let filterData = data.slice(starPos, endPos)
    return filterData
  }
  //编辑
  const edit = () => {
    state.editStatus = true
    state.tableData = {
      list: startPagination(state.activeTableData),
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      total: state.activeTableData.length || 0,
    }
  }
  //退出编辑
  const exitEdit = () => {
    state.editStatus = false
    //还原数据

    state.activeTableData = JSON.parse(JSON.stringify(state.allTableData))
    state.tableData = {
      list: startPagination(state.allTableData),
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      total: state.allTableData.length || 0,
    }
  }
  const closeFn = () => {
    router.push({
      name: 'secretManagementIndex',
      query: {},
    })
  }
  //保存
  const save = () => {
    //保存数据 然后替换后端缓存的初始数据
    api.assets
      .assetsTableFieldsSave({
        assetsId: state.seeId,
        fields: state.activeTableData,
      })
      .then((res) => {
        let { success } = res
        if (success) {
          state.editStatus = false
          getTableData(true)
        }
      })
  }
  onMounted(() => {
    state.seeId = router.currentRoute.value.query.id || null
    state.tableName = router.currentRoute.value.query.tableName || null
    state.tableConfidentialityLevel =
      router.currentRoute.value.query.tableConfidentialityLevel || null
    getTableData()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    position: relative;
    padding: 16px;
    &.isLzos {
      padding: 0;
    }
    .form-box-title {
      margin-bottom: 8px;
      background: #fff;
      border-radius: 8px;

      .search-box {
        display: flex;
        justify-content: space-between;
        padding: 0 16px 8px;
        .search-box-left {
          display: flex;
          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 16px;
          }
        }
        .search-box-right {
          display: flex;
          .btns {
            height: 32px;
            margin-left: 8px;
            padding: 0 8px;
            color: $themeBlue;
            line-height: 32px;
            border-radius: 6px;
            cursor: pointer;
            &:hover {
              color: $normallBtnTextHover;
              background-color: $normallBtnBgHover;
            }
            &.border {
              padding: 0 16px;
              border: 1px solid $themeBlue;
              &:hover {
                border: 1px solid $normallBtnBorderHover;
              }
            }
          }
        }
      }
    }
    .form-box-bottom {
      height: 100%;
      border-radius: 6px;
      .common-section-header {
        display: flex;
        height: 46px;
        background-color: #fff;
        align-items: center;
        border-bottom: none;
        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
        svg {
          color: #8091b7;
        }
      }
      .table-box {
        margin-top: 10px;
        padding-top: 16px;
        height: calc(100% - 56px);
        background-color: #fff;
        .edit-box {
          height: 48px;
          padding: 8px 16px;
          svg {
            margin-top: -3px;
            margin-right: 4px;
            font-size: 16px;
          }
        }
        .table-box-content {
          padding: 0 16px;
          height: 100%;
        }
      }
    }
    .tag {
      width: 44px;
      height: 24px;
      margin: 0 8px;
      padding: 0 4px;
      font-weight: normal;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      border: 1px solid;
      border-radius: 6px;
      &.PUBLIC {
        color: #04c495;
        background: #e6fff4;
        border-color: #04c495;
      }
      &.INTERIOR {
        color: rgba(0, 0, 0, 0.75);
        background: #e3ecff;
        border-color: #a3b4db;
      }
      &.CONTROLLED {
        color: #447dfd;
        background: #e3ecff;
        border-color: #447dfd;
      }
      &.SECRET {
        color: #ec3b9c;
        background: #fff0f6;
        border-color: #ec3b9c;
      }
      &.CONFIDENTIAL {
        color: #ff7d00;
        background: #fff6e6;
        border-color: #ff7d00;
      }

      &.CORE {
        color: #f63838;
        background: var(---Error-, #fff2f0);
        border-color: #f63838;
      }
    }
  }
</style>
