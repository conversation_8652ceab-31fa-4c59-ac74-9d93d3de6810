<template>
  <section class="container">
    <section class="container-box">
      <div class="page_header_common_style">
        <span class="need_smallcube__title">{{ title }}</span>
      </div>
      <div class="form">
        <n-form
          ref="form"
          :data="form"
          :rules="rules"
          label-width="126px"
          label-align="end"
          :pop-position="['right']"
          :key="key"
        >
          <n-row>
            <n-col :span="24">
              <n-form-item label="用户名：" field="username">
                <n-input
                  v-model="form.username"
                  maxlength="60"
                  placeholder="请输入用户名"
                  size="small"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="电子邮箱：" field="email">
                <n-input v-model="form.email" placeholder="请输入" size="small" clearable />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="联系电话：" field="phone">
                <n-input
                  v-model="form.phone"
                  type="tel"
                  maxlength="11"
                  placeholder="请输入"
                  size="small"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="登录密码：" field="password">
                <n-input
                  v-model="form.password"
                  placeholder="请输入"
                  maxlength="30"
                  show-password
                  autocomplete="new-password"
                  size="small"
                />
                <!-- <span class="show-pwd" @click="showPwd">
                  <SvgIcon :icon="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span> -->
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="密码二次确认：" field="confirmPwd">
                <n-input
                  v-model="form.confirmPwd"
                  placeholder="请输入"
                  maxlength="30"
                  show-password
                  size="small"
                />
                <!-- <span class="show-pwd" @click="showPwd">
                  <SvgIcon :icon="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span> -->
              </n-form-item>
            </n-col>
          </n-row>

          <n-row>
            <n-col :span="24">
              <n-form-item label="角色选择：" field="roleId">
                <n-select
                  v-model="form.roleId"
                  placeholder="请选择角色"
                  size="small"
                  filter
                  clearable
                  @value-change="
                    () => {
                      key++
                    }
                  "
                >
                  <n-option
                    v-for="item in roleAllArr"
                    :key="item.key"
                    :name="item.value"
                    :value="item.key"
                  />
                </n-select>
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="密级选择：" field="confidentialityLevel">
                <n-select
                  v-model="form.confidentialityLevel"
                  placeholder="请选择密级"
                  size="small"
                  filter
                  clearable
                  :options="confidentialityLevelList"
                  @value-change="
                    () => {
                      key++
                    }
                  "
                >
                </n-select>
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="所属部门：" field="departmentList">
                <n-cascader
                  ref="cascader"
                  v-model="form.departmentList"
                  class="cascader-width"
                  :options="departmentTrees"
                  valueKey="id"
                  trigger="click"
                  showPath
                  pathMode
                  clearable
                  filterable
                  placeholder="请选择"
                  @change="cascaderChange"
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row>
            <n-col :span="24">
              <n-form-item label="用户描述：">
                <n-textarea
                  v-model="form.description"
                  placeholder="请输入"
                  maxlength="200"
                  :autosize="{ minRows: 3 }"
                  show-count
                  resize="both"
                />
              </n-form-item>
            </n-col>
          </n-row>
        </n-form>
      </div>
    </section>
    <div class="container-footer">
      <div class="my-appliction">
        <n-button
          :loading="loading"
          color="primary"
          size="sm"
          variant="solid"
          @click.prevent="onConfirm"
          >确定</n-button
        >
        <n-button size="sm" plain @click.prevent="goBack">取消</n-button>
      </div>
    </div>
  </section>
</template>

<script>
  import { validPhone, validEmail } from '@/utils/validate'
  import { checkName } from '@/utils/validate'
  export default {
    name: 'DataScriptDetail',
    data() {
      const checkName = async (
        rule,
        value,
        callback,
        pars,
        obj,
        validatorType, //验重接口所属模块
        validatorApi, //验重接口名称
        validatorParams, //验证参数
      ) => {
        if (!value) {
          return callback(new Error('请输入名称'))
        }

        let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,60})$/
        let res = regex.test(value)
        if (res && value.length > 1 && value.length < 61) {
          if (validatorType && validatorApi) {
            //测试数据

            await this.$api[validatorType][validatorApi](validatorParams)
              .then((res) => {
                if (res.success) {
                  if (typeof res.data === 'object') {
                    if (res.data?.success) {
                      return callback('名称重复')
                    } else {
                      return callback()
                    }
                  } else {
                    if (res.data) {
                      return callback('名称重复')
                    } else {
                      return callback()
                    }
                  }
                } else {
                  return callback('名称校检失败')
                }
              })
              .catch(() => {
                return callback('名称校检失败')
              })
          } else {
            return callback()
          }
        } else {
          callback(new Error('支持英文、数字、下划线，只能以英文开头，2~60个字符'))
        }
      }

      const validateConfirmPwd = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入正确的密码'))
        } else if (this.form.password !== value) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }
      return {
        key: 1,
        title: '新增用户',
        treeData: [],
        form: {
          username: '',
          email: '',
          phone: '',
          password: '',
          confirmPwd: '',
          roleId: '',
          departmentList: [],
          departmentFullId: '',
          departmentFullName: '',
          confidentialityLevel: '',
          description: '',
        },

        departmentTrees: [], // 部门下拉列表
        passwordType: 'password',
        roleAllArr: '', // 角色下拉列表
        confidentialityLevelList: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ], // 密级下拉列表
        rules: {
          // username: [
          //   {
          //     required: true,
          //     message: '请输入用户名称',
          //     trigger: 'blur',
          //   },
          // ],
          username: [
            {
              required: true,
              validator: (...args) =>
                checkName(...args, 'system', 'checkUserName', {
                  username: this.form.username || null,
                  id: this.$route.query?.id || null,
                }),
              trigger: 'blur',
            },
          ],
          email: [{ required: true, trigger: 'blur', validator: validEmail }],
          phone: [{ required: true, trigger: 'blur', validator: validPhone }],
          password: [{ required: true, trigger: 'blur', message: '请输入正确的密码' }],
          confirmPwd: [{ required: true, trigger: 'blur', validator: validateConfirmPwd }],
          roleId: [{ required: true, message: '请选择角色', trigger: 'change' }],
          confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
          departmentList: [
            { type: 'array', required: true, message: '请选部门', trigger: 'change' },
          ],
        },
        loading: false,
      }
    },
    mounted() {
      this.roleAll()
      this.getDepartmentTrees()
    },
    methods: {
      // 级联选中节点变化时设置fullID、fullName参数
      cascaderChange(val) {
        let arr = val.join('/')
        this.form.departmentFullId = arr
        let vals = this.getCascaderObj(this.form.departmentList, this.departmentTrees)
        let obj = vals.map((item) => {
          return item.label
        })
        this.form.departmentFullName = obj.join('/')
        // this.$refs.cascader.dropDownVisible = false
      },
      // 处理数据
      getCascaderObj(val, opt) {
        return val.map((value) => {
          for (let itm of opt) {
            if (itm.id === value) {
              // eslint-disable-next-line no-param-reassign
              opt = itm.children
              return itm
            }
          }
          return null
        })
      },
      getDepartmentTrees() {
        this.$api.base.getDepartmentTrees({}).then((res) => {
          this.departmentTrees = []
          if (res.data !== null) {
            let a = this.setTreeRecursion([res.data])
            this.departmentTrees = a
          }

          this.$emit('getGroupData', res.data)
        })
      },
      setTreeRecursion(data) {
        data.map((item) => {
          item.value = item.id
          if (item.children && item.children.length != 0) {
            this.setTreeRecursion(item.children)
          }
        })
        return data
      },
      showPwd() {
        if (this.passwordType === 'password') {
          this.passwordType = ''
        } else {
          this.passwordType = 'password'
        }
      },
      // 用户数据详情查询
      userDetail() {
        let params = {
          id: this.$route.query.id,
        }

        this.$api.system.userDetail(params).then((res) => {
          this.form = res.data
          this.form.roleId = String(res.data.roleId)
          this.form.confirmPwd = res.data.password
          const departmentList = res.data.departmentFullId.split('/')
          this.form.departmentList = departmentList

          let arr = this.roleAllArr.filter((item) => {
            return item.key === String(res.data.roleId)
          })
          if (arr.length === 0) {
            this.roleAllArr.push({ key: res.data.roleId, value: '未分配' })
          }
        })
      },
      // 用户数据详情查询
      roleAll() {
        let params = {}

        this.$api.system.roleAll(params).then((res) => {
          this.roleAllArr = res.data
          if (this.$route.query.id) {
            this.title = '编辑用户'
            this.userDetail()
          }
        })
      },
      onConfirm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            if (this.$route.query.id) {
              this.updateUser()
            } else {
              this.addUser()
            }
          }
        })
      },
      // 添加用户
      addUser() {
        let params = {
          username: this.form.username,
          email: this.form.email,
          phone: this.form.phone,
          password: this.form.password,
          confirmPwd: this.form.confirmPwd,
          roleId: this.form.roleId,
          departmentFullId: this.form.departmentFullId,
          departmentFullName: this.form.departmentFullName,
          confidentialityLevel: this.form.confidentialityLevel,
          description: this.form.description,
        }
        this.$api.system
          .addUser(params)
          .then((res) => {
            this.loading = false
            if (res.code === 'SUCCESS') {
              this.$notify({
                title: '提示',
                message: '添加用户成功',
                type: 'success',
              })
              this.goBack()
            }
          })
          .catch(() => (this.loading = false))
      },
      // 编辑用户
      updateUser() {
        let params = {
          id: this.$route.query.id,
          username: this.form.username,
          email: this.form.email,
          phone: this.form.phone,
          password: this.form.password,
          confirmPwd: this.form.confirmPwd,
          roleId: this.form.roleId,
          departmentFullId: this.form.departmentFullId,
          departmentFullName: this.form.departmentFullName,
          confidentialityLevel: this.form.confidentialityLevel,
          description: this.form.description,
        }

        this.$api.system
          .updateUser(params)
          .then((res) => {
            this.loading = false

            if (res.code === 'SUCCESS') {
              this.$notify({
                title: '提示',
                message: '编辑用户成功',
                type: 'success',
              })

              this.goBack()
            }
          })
          .catch(() => (this.loading = false))
      },
      goBack() {
        this.$router.back()
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    // padding-bottom: 70px;
    &-box {
      height: calc(100% - 60px);
      border-radius: 4px;
    }

    &-footer {
      height: 60px;
      margin-top: 10px;
      margin-right: -10px;
      margin-left: -10px;
      padding: 17px 30px;
      text-align: center;
      background: #ffffff;
      border-radius: 8px 8px 0px 0px;
    }
  }

  .form {
    padding: 33px 58px;
    color: #333;
    background: #fff;
    .nancalui-form {
      width: 700px;
      margin: 0 auto;
    }
    h2 {
      margin: 0 0 20px;
      padding-left: 5px;
      font-size: 14px;
      border-left: 4px solid $themeBlue;
    }

    :deep(.cascader-width) {
      width: 100% !important;
    }
  }
</style>
