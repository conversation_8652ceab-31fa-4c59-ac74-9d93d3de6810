<template>
  <!-- 结构化数据采集  -->
  <div class="doc-collection">
    <div class="add-box">
      <div class="page-title">
        源端管理
        <div class="detail-back-box" @click.prevent="cancel"> 返回 </div>
      </div>
    </div>
    <div class="add-container">
      <section class="cf-tools">
        <div class="row">
          <div class="col">
            <n-button class="nc-m-8" @click="editApi({})" color="primary" variant="solid">
              <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
              新建</n-button
            >
          </div>
          <div class="col">
            <span class="label">接口名称：</span>
            <n-input
              class="asideTree-search-input"
              v-model="state.filterSearch.keyWord"
              clearable
              @clear="onSearch"
            >
              <template #append>
                <n-button class="search-btn" icon="search" @click.prevent="onSearch" />
              </template>
            </n-input>
          </div>
        </div>
      </section>
      <div class="table">
        <CfTable
          actionWidth="150"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              onSearch(false)
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              onSearch()
            },
          }"
        >
          <template #editor="{ data: { row } }">
            <n-button variant="text" color="primary" @click="editApi(row)">编辑</n-button>
            <n-button variant="text" color="primary" @click="onDelete(row)">删除</n-button>
          </template>
        </CfTable>
      </div>
    </div>
  </div>
  <AddModal :addModal="state.addShow" @onClose="onClose" :selectRow="state.selectRow" />
</template>
<script setup>
  import { ref, reactive } from 'vue'
  import AddModal from './add-source-api.vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'

  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const state = reactive({
    filterSearch: {
      keyWord: '',
    },
    loading: false,
    tableHeadTitles: [
      { prop: 'name', name: '数据源名称', width: '150' },
      { prop: 'method', name: '请求类型' },
      { prop: 'url', name: '请求地址' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', width: '170' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    tableList: [
      // {
      //   id:1,
      //   status:'START'
      // },{
      //   id:2,
      //   status:'STOP'
      // }
    ],
    isLoad: false,
    addShow: false,
    selectRow: {},
  })

  // 查询
  const onSearch = (init = true) => {
    if (init) {
      state.pagination.pageNum = 1
      state.pagination.currentPage = 1
    }
    state.isLoad = true

    let params = {
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    if (state.filterSearch.keyWord) {
      params.condition = state.filterSearch.keyWord
    }
    api.documentManage.getSourceApi(params)
      .then((res) => {
        state.tableList = res.data.list
        state.pagination.total = res.data.total
        state.isLoad = false
      })
      .catch(() => {
        state.isLoad = false
      })
  }
  const onDelete = (row) => {
    proxy.$MessageBoxService.open({
      title: `是否确认该条源端数据`,
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.sourceApiDelete({ id: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
          }
        })
      },
    })
  }
  const editApi = (row = {}) => {
    state.selectRow = row
    state.addShow = true
  }
  // 取消
  const cancel = () => {
    router.go(-1)
  }
  const onClose = (type) => {
    state.addShow = false
    state.selectRow = {}
    if (type) {
      onSearch()
    }
  }
  onMounted(async () => {
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .doc-collection {
    height: 100%;
    padding: 16px;
    .add-box {
      margin-bottom: 10px;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
    }
    .base-tabs {
      background-color: #fff;
      :deep(.nancalui-tabs-nav-tab) {
        padding-left: 16px;
      }
    }
    .add-container {
      height: calc(100% - 60px);
      background-color: #fff;
    }
    .asideTree-search-input {
      :deep(.nancalui-input__wrapper) {
        border: 1px solid #e5e6eb !important;
        border-radius: 2px !important;
        &:hover {
          z-index: 99;
        }
      }
      .search-btn {
        box-sizing: border-box;
        width: 30px;
      }
      :deep(.nancalui-input-slot__append) {
        box-sizing: border-box;
        min-height: 16px;
        padding-right: 0;
        border-color: #e5e6eb;
      }
    }
    .table {
      height: calc(100% - 56px);
      background-color: #fff;
      .level-tag {
        width: 40px;
        height: 20px;
        font-weight: 400;
        font-size: 12px;
        font-style: normal;
        line-height: 20px;
        text-align: center;
        border: 1px solid;
        border-radius: 2px;
        &.PUBLIC {
          color: #1aa4ee;
          background: rgba(26, 164, 238, 0.08);
          border-color: rgba(26, 164, 238, 0.4);
        }
        &.INTERIOR {
          color: #fe8624;
          background: rgba(255, 244, 230, 1);
          border-color: rgba(255, 186, 112, 1);
        }
        &.CONTROLLED {
          color: #1e89ff;
          background: rgba(153, 201, 255, 1);
          border-color: rgba(30, 137, 255, 1);
        }
        &.SECRET {
          color: #d40000;
          background: rgba(255, 237, 237, 1);
          border-color: rgba(239, 119, 119, 1);
        }
        &.CONFIDENTIAL {
          color: #7a0000;
          background: rgba(122, 0, 0, 0.08);
          border-color: rgba(122, 0, 0, 0.4);
        }
        &.CORE {
          color: #224ecd;
          background: rgba(34, 78, 205, 0.08);
          border-color: rgba(34, 78, 205, 0.4);
        }
      }
      :deep(.nancalui-switch__inner) {
        // font-size: 12px;
      }
    }
  }
</style>
