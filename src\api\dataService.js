import request from '@/utils/request'
import SEV from '@/const/urlAddress'

// --------------------------数据服务接口---------------------
// 新增Api
export function addApi(data) {
  return request({
    url: `${SEV.SEV}/api/create`,
    method: 'post',
    data: data,
  })
}
// 分页获取api列表
export function getApiList(data) {
  return request({
    url: `${SEV.SEV}/api/search`,
    method: 'post',
    data: data,
  })
}
// 获取api详情
export function getApiDetail(data) {
  return request({
    url: `${SEV.SEV}/api/detail/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 修改Api
export function updateApi(data) {
  return request({
    url: `${SEV.SEV}/api/update`,
    method: 'post',
    data: data,
  })
}

// 删除Api
export function deleteApi(data) {
  return request({
    url: `${SEV.SEV}/api/delete/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 发布 api
export function publishApi(data) {
  return request({
    url: `${SEV.SEV}/api/publish/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 下架api
export function downApi(data) {
  return request({
    url: `${SEV.SEV}/api/down/${data.id}`,
    method: 'post',
    data: data,
  })
}
// /api/path api路径验重 验证唯一性
export function onlyApiPath(data) {
  return request({
    url: `${SEV.SEV}/api/path`,
    method: 'get',
    params: data,
  })
}
// /api/path api名称验重
export function validApi(data) {
  return request({
    url: `${SEV.SEV}/checkApiName`,
    method: 'post',
    data: data,
  })
}
// Api调用源地址配置的读取
export function getServerUrl(data) {
  return request({
    url: `${SEV.SEV}/serverUrl`,
    method: 'get',
    params: data,
  })
}
// 获取协议列表
export function getProtocolList(data) {
  return request({
    url: `${SEV.SEV}/api/protocol`,
    method: 'get',
    params: data,
  })
}
// 获取请求方式列表
export function getMethodList(data) {
  return request({
    url: `${SEV.SEV}/api/method`,
    method: 'get',
    params: data,
  })
}
// api 状态枚举
export function getApiStatusEnums(data) {
  return request({
    url: `${SEV.SEV}/api/enums/status`,
    method: `get`,
    params: data,
  })
}

/*
测试api
 */

export function apiTest(data) {
  return request({
    url: `${SEV.SEV}/api/test`,
    method: 'post',
    data: data,
  })
}

// post模式测试
export function generate(object, data) {
  return request({
    url: `${SEV.SEV}/res${object.url}`,
    method: `${object.type}`,
    data: data,
  })
}
// get模式测试
export function getGenerate(object, data) {
  return request({
    url: `${SEV.SEV}/res${object.url}`,
    method: `${object.type}`,
    params: data,
  })
}
// Api数据过滤算符枚举
export function getFilterOperator() {
  return request({
    url: `${SEV.SEV}/api/filter/operator`,
    method: 'get',
  })
}
// 获取api授权信息
export function getApiAuthInformation(data) {
  return request({
    url: `${SEV.SEV}/${data.id}/grant`,
    method: 'get',
    params: data,
  })
}
///api授权
export function apiAuth(data) {
  return request({
    url: `${SEV.SEV}/grant`,
    method: 'post',
    data: data,
  })
}
///获取api调用次数
export function getApiCallTimes(data) {
  return request({
    url: `${SEV.SEV}/${data.id}/invoke`,
    method: 'get',
    params: data,
  })
}

// api下拉所有列表
export function getApiAll() {
  return request({
    url: `${SEV.SEV}/api/all`,
    method: 'get',
  })
}

//已授权api
export function apiWhiteList(data) {
  return request({
    url: `${SEV.SEV}/grant/apis`,
    method: 'post',
    data: data,
  })
}

//删除白名单
export function delWhite(data) {
  return request({
    url: `${SEV.SEV}/api/ips/delete/${data.id}`,
    method: 'get',
  })
}

//api日志监控列表
export function apiMonitorSearch(data) {
  return request({
    url: `${SEV.SEV}/api/monitor/api/search`,
    method: 'post',
    data: data,
  })
}

//api日志监控日志列表
export function apiMonitorLogSearch(data) {
  return request({
    url: `${SEV.SEV}/api/monitor/req/search`,
    method: 'post',
    data: data,
  })
}
//分页获取api及token列表
export function getApiTokenList(data) {
  return request({
    url: `${SEV.SEV}/api/token/search`,
    method: 'post',
    data: data,
  })
}
//设置Token有效期
export function setTokenExpire(data) {
  return request({
    url: `${SEV.SEV}/api/token/expire`,
    method: 'post',
    data: data,
  })
}

export default {
  addApi,
  getApiList,
  updateApi,
  deleteApi,
  getApiDetail,
  publishApi,
  generate,
  getGenerate,
  onlyApiPath,
  validApi,
  getServerUrl,
  downApi,
  getProtocolList,
  getMethodList,
  getApiStatusEnums,
  apiTest,
  getFilterOperator,
  getApiAuthInformation,
  apiAuth,
  getApiCallTimes,
  getApiAll,
  apiWhiteList,
  delWhite,
  apiMonitorSearch,
  apiMonitorLogSearch,
  getApiTokenList,
  setTokenExpire,
}
