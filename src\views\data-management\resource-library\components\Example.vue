<template>
  <div class="example">
    <div class="example-btn"
      ><n-button variant="solid" color="primary" @click.stop.prevent="runFn"
        ><SvgIcon class="icon" icon="icon-card-rerun" />立即执行</n-button
      ></div
    >
    <div class="example-content">
      <n-public-table
        :isDisplayAction="false"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        @tablePageChange="tablePageChange"
      >
        <template #status="{ editor }">
          <div v-if="editor.row.runState === 'RUNNING_EXECUTION'" class="status"
            ><div class="circle yellow"></div>执行中</div
          >
          <div v-else-if="editor.row.runState === 'SUCCESS'" class="status"
            ><div class="circle"></div>成功</div
          >
          <div v-else class="status"><div class="circle red"></div>失败</div>
        </template>
      </n-public-table>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  const store = useStore()
  const router = useRouter()
  import api from '@/api/index'
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    tableHeadTitles: [
      { prop: 'name', name: '任务名称' },
      { prop: 'startTime', name: '开始时间' },
      { prop: 'endTime', name: '完成时间' },
      { prop: 'elapse', name: '完成耗时' },
      { prop: 'runState', name: '运行状态', slot: 'status' },
    ],
    tableData: { list: [] },
    tableHeight: 0,
    pagination: {
      total: 3,
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
    },
  })
  // 切换分页
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    onSearch()
  }
  // 获取数据
  const onSearch = () => {
    let data = {
      condition: {
        jobId: props.info.jobId,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    api.dataManagement.runInstanceList(data).then((res) => {
      if (res.success) {
        res.data.list.forEach((val, ind) => {
          val.name =
            props.info.sourceFileName +
            '第' +
            (res.data.total -
              (ind + (state.pagination.currentPage - 1) * state.pagination.pageSize)) +
            '次采集'
        })
        state.tableData = res.data
        state.pagination.total = res.data.total
      }
    })
  }

  // 执行
  const runFn = () => {
    api.dataManagement.collectTaskExecute({ id: props.info.jobId }).then((res) => {
      let { success } = res
      if (success) {
        ElNotification({
          title: '提示',
          message: '执行成功',
          type: 'success',
        })
      }
      setTimeout(() => {
        state.pagination.currentPage = 1
        onSearch()
      }, 1000)
    })
  }

  // 设置表格高度
  const setTableHeight = () => {
    if (state.isLzos) {
      state.tableHeight = document.body.offsetHeight - 240
    } else {
      state.tableHeight = document.body.offsetHeight - 314
    }
  }
  onMounted(() => {
    setTableHeight()
    onSearch()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .example {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    &-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 0 10px;
      border-bottom: 1px solid #c5d0ea;
      :deep(.nancalui-button) {
        margin-left: 8px;
        padding: 0 8px;
        .button-content {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
            font-size: 16px;
          }
        }
      }
    }
    &-content {
      height: calc(100% - 52px);
      padding: 16px;
      .status {
        .circle {
          display: inline-block;
          width: 6px;
          height: 6px;
          margin-right: 4px;
          background-color: #04c495;
          border-radius: 50%;

          &.red {
            background-color: #f63838;
          }

          &.yellow {
            background-color: #ff7d00;
          }
        }
      }
    }
  }
</style>
