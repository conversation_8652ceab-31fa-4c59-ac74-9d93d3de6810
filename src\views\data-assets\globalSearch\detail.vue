<template>
  <div v-loading="state.loading" :class="['full-page container', state.isLzos ? 'isLzos' : '']">
    <div class="add-box-top-title">
      <div class="common-section-header">
        <div class="title">
          <span class="need_smallcube__title"
            >{{ state.modelTitle }}
            <span v-if="state.type === 'MODEL'" :class="'MODEL' + ' node-tag'">
              <img class="node-icon" :src="state.imgSrc" />
              模型
            </span></span
          >
        </div>
        <div class="detail-back-box">
          <div class="detail-back-box-btn" @click.prevent="cancelFn"
            ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
          </div>
          <div class="detail-back-box-btn checked" @click.prevent="closeFn"
            ><SvgIcon class="icon" icon="icon-close" />关闭
          </div>
        </div>
      </div>
    </div>
    <section>
      <div :class="{ 'global-search-detail': true, 'global-search-full': state.type !== 'MODEL' }">
        <div class="detail-api-content">
          <div class="content-box" v-if="state.type === 'API'">
            <detailApi ref="detailApi" />
          </div>
          <div class="content-box" v-if="state.type === 'MODEL'">
            <detailModel
              ref="detailModel"
              :isShow="isTagData"
              :seeName="seeName"
              :mapType="mapType"
            />
          </div>
          <div class="content-box" v-if="state.type === 'TAG'">
            <detailTag ref="detailTag" />
          </div>
          <div class="content-box" v-if="state.type === 'METADATA'">
            <detailMeta ref="detailMeta" />
          </div>
        </div>
      </div>
      <div v-if="state.type === 'MODEL'" class="detail-right">
        <n-module-name height="16" fontSize="16" color="#000">资产信息</n-module-name>
        <n-form
          class="disable-hide-border disabled-form"
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="85px"
          label-align="end"
        >
          <n-divider content-position="left" distance="0"
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M13.1005 3.00244L8.10052 9.00244L3.10052 3.00244"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.8994 6.99756L7.89935 12.9976L2.89935 6.99756"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span>基础信息</span>
          </n-divider>
          <n-form-item label="环境：">
            <div class="inputText">{{ state.ruleForm.envTypeName }}</div>
          </n-form-item>
          <n-form-item label="创建时间：">
            <div class="inputText">{{ state.ruleForm.createTime }}</div>
          </n-form-item>
          <n-form-item label="创建人：">
            <div class="inputText">{{ state.ruleForm.createByName }}</div>
          </n-form-item>
          <n-form-item label="表类型：">
            <div class="inputText">{{ state.ruleForm.modelType }}</div>
          </n-form-item>
          <n-form-item label="关联任务：">
            <div class="inputText" :title="state.ruleForm.referencedTasks">{{
              state.ruleForm.referencedTasks
            }}</div>
          </n-form-item>

          <n-divider content-position="left" distance="0"
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M13.1005 3.00244L8.10052 9.00244L3.10052 3.00244"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.8994 6.99756L7.89935 12.9976L2.89935 6.99756"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span>物理信息</span>
          </n-divider>
          <n-form-item label="存储类型：">
            <div class="inputText">{{ state.ruleForm.storageType }}</div>
          </n-form-item>
          <n-form-item label="存储数据量：">
            <div class="inputText">{{ state.ruleForm.storageSize }}</div>
          </n-form-item>

          <n-divider content-position="left" distance="0"
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M13.1005 3.00244L8.10052 9.00244L3.10052 3.00244"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.8994 6.99756L7.89935 12.9976L2.89935 6.99756"
                stroke="#8091B7"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span>使用信息</span>
          </n-divider>
          <n-form-item label="读取次数：">
            <div class="inputText">{{ state.ruleForm.apiReadCount || '--' }}</div>
          </n-form-item>
        </n-form>
      </div>
    </section>
    <!-- <div class="fixed-bottom">
      <n-button size="sm" variant="solid" color="primary" @click.stop.prevent="goBack"
        >返回</n-button
      >
    </div> -->
  </div>
</template>

<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import detailApi from './components/detail-api'
  import detailModel from './components/detail-model'
  import detailTag from './components/detail-tag'
  import detailMeta from './components/detail-meta'

  export default {
    title: '',
    components: { detailApi, detailModel, detailTag, detailMeta },
    props: {
      isModel: { type: Boolean, default: false },
      isTagData: { type: Boolean, default: true },
      seeName: { type: String, default: 'globalSearchDetail' },
      mapType: { type: String, default: 'MODEL' },
    },
    setup(props) {
      const router = useRouter()
      const detailApi = ref()
      const detailModel = ref()
      const detailTag = ref()
      const detailMeta = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        modelTitle: '',
        description: '',
        type: '',
        queryData: {},
        apiCallTimes: 0,
        isShow: false,
        loading: false,
        imgSrc: '',
        ruleForm: {
          projectCode: '',
          envTypeName: '',
          envType: '',
          createByName: '',
          layerId: '',
          modelType: '',
          referencedTasks: '',
          createTime: '',
          storageType: '',
          storageSize: '',
          description: '',
          apiReadCount: '',
          paramColumns: [], // 自定义参数
        },
      })
      const methods = {
        getAssetsImages(name) {
          return new URL(`/src/assets/img/assets/assetSearch/${name}.png`, import.meta.url).href //本地文件路径
        },
        // 返回
        goBack() {
          router.go(-1)
        },
        //获取api调用次数
        getApiCallTimes() {
          api.dataService.getApiCallTimes({ id: state.queryData.id }).then((res) => {
            let { data, success } = res
            if (success) {
              state.apiCallTimes = data
            }
          })
        },
        //获取模型基础信息
        getModelDetail(modelId) {
          state.loading = true
          api.model
            .getModalDetail({ id: modelId })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                state.ruleForm = data
                state.ruleForm.storageSize = Math.ceil(data.storageSize * 1024) + ' KB'
              }
            })
            .catch(() => {})
        },
        // 取消
        cancelFn() {
          router.go(-1)
        },
        // 关闭
        closeFn() {
          router.push({ name: 'mergeAsset' })
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query
        state.type = router.currentRoute.value.query.type
        if (props.isModel) {
          state.type = 'MODEL'
        }
        if (state.type === 'MODEL' && state.queryData.id) {
          state.imgSrc = methods.getAssetsImages('MODEL')
          methods.getModelDetail(state.queryData.id)
        }

        state.modelTitle = router.currentRoute.value.query.modelTitle
        if (state.type === 'API') {
          methods.getApiCallTimes()
        }
      })

      return {
        state,
        detailApi,
        detailModel,
        detailTag,
        detailMeta,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .full-page {
    padding: 12px;
    &.isLzos {
      height: 100vh;
      padding: 0;
    }
    .options-box-bg {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 66px;
      margin: 10px -10px 0 -10px;
      padding: 0 20px;
      background-color: #fff;

      .content {
        width: 100%;
        height: 100%;
        padding: 20px;
        text-align: right;
        background-color: #fff;
      }
    }
    .fixed-bottom {
      position: absolute;
      right: 0;
      bottom: 0px;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      padding: 0 30px;
      text-align: center;
      background-color: #fff;
      border-radius: 8px 8px 0px 0px;
    }
    section {
      display: flex;
      height: calc(100% - 60px);
    }

    .need_smallcube__title {
      position: relative;
      display: inline-block;
      margin: 0;
      border: none;
      padding: 0;

      .node-tag {
        position: relative;
        display: inline-block;
        box-sizing: border-box;
        height: 24px;
        margin-left: 8px;
        padding: 4px 12px 4px 24px;
        font-weight: 500;
        font-size: 12px;
        line-height: 14px;
        background: #f0f7ff;
        border-radius: 11px;
        img {
          position: absolute;
          top: 4px;
          left: 6px;
          width: 14px;
        }
      }
      .MODEL {
        color: #fff;
        background-color: $themeBlue;
        border-radius: 6px;
      }
      .TAG {
        color: #697a9a;
        background-color: rgba(105, 122, 154, 0.1);
      }
      .API {
        color: #31b2cc;
        background-color: rgba(49, 178, 204, 0.1);
      }
      .METADATA {
        color: #18ba72;
        background-color: #f0faf2;
      }
    }

    .global-search-detail {
      position: relative;
      width: calc(100% - 370px);
      height: 100%;
      margin-right: 8px;
      background-color: #fff;
      border-radius: 8px;

      .detail-api-content {
        height: 100%;
        overflow: hidden;
      }
      .content-box {
        height: calc(100% - 23px);
      }
      > p {
        padding-left: 10px;
        font-size: 12px;
      }
    }
    .global-search-full {
      width: 100%;
    }
    .detail-right {
      box-sizing: border-box;
      width: 362px;
      height: 100%;
      background-color: #fff;
      border-radius: 8px;

      :deep(.module-name-text) {
        width: 100%;
        line-height: 52px;
        font-weight: bolder;
        border-bottom: 1px solid var(---, #c5d0ea);
        padding-left: 16px;
        &::before {
          position: absolute;
          top: 17px;
          left: 0;
          width: 4px;
          height: 18px;
          background: var(
            --Radial,
            radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
          );
          border-radius: 0px 4px 4px 0px;
          content: '';
        }
      }
      :deep(.module-name-border) {
        height: 0;
        width: 0;
        margin-right: 0;
        background: none;
      }

      .disabled-form {
        padding: 0 16px;
      }

      span {
        position: relative;
        display: inline-block;
        width: 56px;
        line-height: 48px;
        color: #000000;
        font-weight: bolder;
        font-size: 14px;
      }
      svg {
        position: relative;
        top: 2px;
        margin-right: 4px;
      }
      :deep(.nancalui-divider__text) {
        padding: 0 8px 0 0;
      }
      :deep(.nancalui-divider--horizontal) {
        border-color: #c5d0ea;
      }
      .nancalui-form__item--horizontal {
        margin-bottom: 8px;
        :deep(.nancalui-form__label) {
          height: 22px;
          line-height: 22px;
        }
        :deep(.nancalui-form__label-span) {
          color: #333333;
          font-weight: bolder;
          font-size: 14px;
          line-height: 22px;
        }
        .inputText {
          display: -webkit-box;
          overflow: hidden;
          color: #666666;
          font-size: 14px;
          line-height: 22px;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
    }

    :deep(.nancalui-tabs .nancalui-tabs-nav-tab) {
      padding: 0 16px;
      border-bottom: 1px solid #c5d0ea;

      .nancalui-tabs-tab-title {
        padding: 0 10px;
      }
    }
    :deep(.nancalui-tabs-nav-type-line .nancalui-tabs-tab) {
      margin-left: 8px;
    }
  }
</style>
