<template>
  <div class="dialogFile">
    <el-dialog
      class="largeDialog"
      :model-value="showUpload"
      width="600px"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <template #header>
        <h2>{{ dialogTitle }}导入</h2>
      </template>
      <div class="main">
        <el-upload
          ref="upload"
          :show-file-list="false"
          drag
          :action="fileUrl"
          accept=".xls,.xlsx"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :on-error="handleError"
          :auto-upload="false"
          :on-change="changeFile"
        >
          <div class="el-upload__text"> <em>点击上传</em>/拖拽到此区域 </div>
          <template #tip>
            <div class="el-upload__tip"
              >注：仅限上传excel文件
              <n-button class="left" variant="text" @click.stop.prevent="handleDownload">
                <!-- <img src="~@img/data/icon-excel.png" alt="" /> -->
                模板下载
              </n-button>
            </div>
          </template>
        </el-upload>
        <div v-if="fileData.length > 0" class="showList">
          <ul>
            <div v-for="item in fileData" :key="item.uid">
              <li v-if="item.status === 'ready'">
                <!-- <svg-icon style="margin-right: 10px" icon-class="correct" /> -->
                <span style="margin-right: 20px">添加文件成功：{{ item.name }} &nbsp;请上传！</span>
                <SvgIcon class="delete" icon="icon_delete_n" @click="clearFile" title="清除" />
              </li>
              <li v-else-if="item.status === 'success'">
                <!-- <svg-icon style="margin-right: 10px" icon-class="correct" /> -->
                <span style="margin-right: 20px">数据导入成功：{{ item.name }} &nbsp;！</span>
                <SvgIcon class="delete" icon="icon_delete_n" @click="clearFile" title="清除" />
              </li>
              <li v-else-if="item.status === 'fail'">
                <!-- <svg-icon style="margin-right: 10px" icon-class="correct" /> -->
                <span style="margin-right: 20px"
                  >添加文件失败：{{ item.name }} &nbsp;请重新添加！</span
                >
                <SvgIcon class="delete" icon="icon_delete_n" @click="clearFile" title="清除" />
              </li>
              <li v-else-if="item.status === 'error'">
                <!-- <svg-icon style="margin-right: 10px" icon-class="error" /> -->
                <span style="margin-right: 20px"
                  >数据导入失败：{{ item.name }} &nbsp;请核对模板内容！</span
                >
                <SvgIcon class="delete" icon="icon_delete_n" @click="clearFile" title="清除" />
                <a href="" @click="clearFile">ddd</a>
              </li>
            </div>
          </ul>
        </div>
      </div>
      <template #footer>
        <n-button
          size="sm"
          variant="solid"
          color="primary"
          @click.stop.prevent="fileUploads"
          :loading="loadingUp"
          >保存</n-button
        >
        <n-button size="sm" @click.stop.prevent="cancel()">取消</n-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { importMeta } from '@/api/model'
  import api from '@/api/index'
  export default {
    name: 'Index',
    props: {
      showUpload: { type: Boolean, default: false },
      dialogTitle: { type: String, default: '' },
      modelId: { type: Number },
      modelName: { type: String, default: '' },
    },
    data() {
      return {
        fileUrl: 'https://jsonplaceholder.typicode.com/posts/', // 接口地址
        uploadFile: '', //  存放上传文件的内容
        fileData: [], // 添加文件展示数据
        loadingUp: false,
      }
    },
    computed: {},
    created() {},
    methods: {
      // 下载
      async handleDownload() {
        // 模板下载
        let objName = 'template/元数据导入模板.xlsx'
        const res = await api.model.fileDownload({
          bucket: 'data-govern',
          objName, //把转义过的地址转回来
        })
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        } else {
          // 下载文件
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
          })
          const link = document.createElement('a')
          let _fileName = objName.split('/')
          link.download = _fileName[_fileName.length - 1]
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        }
        // const res = await this.$api.model.fileTemplate({ fileName: 'metadata-import-template' })
        // const blob = new Blob([res], {
        //   type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
        // })
        // const link = document.createElement('a')
        // link.download = this.dialogTitle + '导入模板.xlsx'
        // link.style.display = 'none'
        // // link.href = URL.createObjectURL(res)
        // link.href = URL.createObjectURL(blob)
        // document.body.appendChild(link)
        // link.click()
        // URL.revokeObjectURL(link.href)
        // document.body.removeChild(link)
      },
      cancel(init = false) {
        this.loadingUp = false
        this.clearFile()
        this.$emit('cancel', init)
      },
      submitUpload() {
        if (this.fileData.length > 0) {
          this.$refs.upload.submit()
          this.clearFile()
        }
      },
      handleRemove(file, fileList) {},
      handleSuccess(res, file) {
        this.$message.success(file.name + '上传成功')
      },
      handleError(err, file) {
        this.$message.error(file.name + '上传失败')
      },
      changeFile(file, fileList) {
        if (fileList.length > 1) {
          fileList.shift()
        }
        const isLt2M = file.size / 1024 / 1024 <= 10
        if (!isLt2M) {
          return this.$message({
            message: '上传文件大小不能超过 10MB!',
            type: 'warning',
          })
        }
        this.uploadFile = file
        this.fileData = fileList
      },
      clearFile() {
        this.fileData = []
        this.uploadFile = ''
      },
      fileUploads() {
        const formData = new FormData()
        formData.append('file', this.uploadFile.raw)
        // formData.append('name', this.uploadFile.name)
        // formData.append('modelId', this.modelId)
        this.loadingUp = true
        setTimeout(() => {
          this.loadingUp = false
        }, 1000)
        importMeta({ file: formData })
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.$message({
                message: '上传成功',
                type: 'success',
              })
              this.cancel(true)
            } else if (res.code === '705') {
              this.$message({
                message: '文件为空，请重新上传',
                type: 'warning',
              })
            }
          })
          .catch(() => {
            this.loadingUp = false
          })
      },
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .el-dialog {
    h2 {
      height: 14px;
      line-height: 14px;
      font-size: 14px;
      margin: 0;
      padding-left: 10px;
      border-left: 4px solid $themeBlue;
      border-bottom: 0;
    }
    .main {
      margin: 0 100px;
      .showList {
        > ul {
          list-style: none;
          padding: 0;
          position: relative;
          left: 0;
          width: 100%;
          > div {
            > li {
              margin: 0;
              border-radius: 2px;
              span {
                display: inline-block;
                height: 18px;
                width: calc(100% - 15px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
              }
              &:hover {
                background-color: rgb(245, 247, 250);
                transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
              }
              &:hover .delete {
                // display: inline-block;
                z-index: 4;
                cursor: pointer;
              }
            }
            .delete {
              position: absolute;
              top: 3px;
              right: 0;
              z-index: -1;
            }
          }
        }
      }
    }
  }
  :deep(.el-upload__tip) {
    position: relative;
  }
  .left {
    font-size: 12px;
    // color: #278b53;
    color: $themeBlue;
    position: absolute !important;
    right: 0;
    top: -7px;
    img {
      width: 20px;
      height: 20px;
    }
  }
  :deep(.n-button--text:not(.is-disabled):focus) {
    // color: #278b53;
    color: $themeBlue;
  }
  :deep(.n-button--text:not(.is-disabled):hover) {
    // color: #278b53;
    color: $themeBlue;
  }
</style>
