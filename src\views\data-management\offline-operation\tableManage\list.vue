<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div v-if="state.formInline.tableType !== 'DORIS'" class="col">
          <span class="label">环境：</span>
          <n-select
            v-model="state.formInline.envType"
            placeholder="请选择环境"
            @value-change="getTreeListFn(true)"
          >
            <n-option
              v-for="item in state.envTypeList"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
        </div>
        <div v-else></div>
        <div class="search">
          <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">资源库管理</div>
          <n-tabs
            type="button"
            v-model="state.formInline.tableType"
            @active-tab-change="getTreeListFn"
          >
            <n-tab id="HIVE" title="Hive库" />
            <n-tab id="DORIS" title="资源库" />
          </n-tabs>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="仅展示前20条，请输入表名称搜索"
            suffix="search"
            @input="searchTreeFn"
          />
          <CfTtee
            ref="treeRef"
            isFloating
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          >
            <template #btns="{ node, data: nodeData }">
              <el-popover
                v-if="
                  node.level !== 0 &&
                  node.level !== 1 &&
                  buttonAuthList.includes('dataManagement_tableManage_tree_delete')
                "
                trigger="click"
                placement="right-start"
              >
                <template #reference>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      cx="8"
                      cy="12.5"
                      r="0.5"
                      transform="rotate(-90 8 12.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="8"
                      r="0.5"
                      transform="rotate(-90 8 8)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="3.5"
                      r="0.5"
                      transform="rotate(-90 8 3.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                  </svg>
                </template>
                <ul class="cf-list">
                  <li class="cf-list-item" @click="delNodeTree({ ...nodeData, level: node.level })">
                    <SvgIcon class="tree-icon-tool" @click="" icon="icon-tree-del" />
                    删除</li
                  >
                </ul>
              </el-popover>
            </template>
          </CfTtee>
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-tab">
              <n-radio-group direction="row" v-model="state.dataType" @change="radioFn">
                <n-radio-button value="table">表结构</n-radio-button>
                <n-radio-button value="data">数据预览</n-radio-button>
              </n-radio-group>
              <div v-if="state.dataType === 'data'" class="only">仅显示前20条数据</div>
            </div>
            <div class="table-list dif" v-loading="state.loading">
              <div
                :class="
                  state.tableData.list.length > 0 ? 'table-list-box' : 'table-list-box empty-table'
                "
                :style="'width:' + state.tableHeadTitles.length * 135 + 'px'"
              >
                <CfTable
                  :isDisplayAction="false"
                  :showPagination="false"
                  :table-head-titles="state.tableHeadTitles"
                  :tableConfig="{
                    data: state.tableData.list,
                    rowKey: 'id',
                  }"
                  actionWidth="180"
                >
                  <template #name="{ row }">
                    <div class="taskName">
                      {{ row.name }}
                    </div>
                  </template>
                  <template #state="{ row }">
                    <div class="taskName">
                      <span v-if="row.state === 1">运行中</span>
                      <span v-else-if="row.state === 6">失败</span>
                      <span v-else-if="row.state === 7">成功</span>
                      <span v-else>--</span>
                    </div>
                  </template>
                </CfTable>
              </div>
            </div>
            <!--            <div v-if="state.pageInfo.total>0" class="table-content-pagination">-->
            <!--              <div class="table-content-pagination-total">共<span>{{ state.pageInfo.total }}</span>条数据</div>-->
            <!--              <n-pagination-->
            <!--                  :total="state.pageInfo.total"-->
            <!--                  v-model:pageSize="state.pageInfo.pageSize"-->
            <!--                  v-model:pageIndex="state.pageInfo.currentPage"-->
            <!--                  :can-change-page-size="true"-->
            <!--                  @page-index-change="cardCurrentChange"-->
            <!--                  @page-size-change="cardSizeChange"-->
            <!--                  :page-size-options="[10, 20, 50, 100]"-->
            <!--              />-->
            <!--            </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { removeTable } from '@/api/model.js'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import { tableManagePage, collectMonitorList, collectResultRun } from '@/api/dataManage'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import cardList from '@/components/CardList'
  import CfTtee from '@/components/cfTtee'
  export default {
    name: '',
    components: { cardList },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: { list: [] },
        tableHeight: 436,
        key: 1,
        treeKey: 1,
        dataType: 'table',
        loading: false,
        showResult: false,
        isDisplayAction: true,
        formInline: {
          tableType: 'HIVE',
          envType: 'OFFICIAL',
          keyword: null,
          time: [],
        },
        optionItemData: {}, // 操作的任务数据
        envTypeList: [
          { name: '生产环境', value: 'OFFICIAL' },
          { name: '开发环境', value: 'TEST' },
        ],
        statusList: [
          { name: '运行中', value: '1' },
          { name: '运行成功', value: '7' },
          { name: '运行失败', value: '6' },
        ],
        shortcuts: ENUM.SHORTCUTS,
        pageInfo: {
          total: 0,
          pageSize: 20,
          currentPage: 1,
        },
        tableHeadTitles: [],
        timeFlag: null,
        categoryId: null,
        treeSearchText: '',
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        expandedKeys: [], 
        selectedKey: null,
      })
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const methods = {
        // 删除节点树
        delNodeTree(item) {
          proxy.$MessageBoxService.open({
            title: '删除节点树',
            content: `确认删除 ${item.name} 该表模型及对应物理表？`,
            cancel: () => {
              console.log('你点击了取消')
            },
            save: () => {
              removeTable({ id: item.id }).then((res) => {
                if (res.code === 'SUCCESS') {
                  ElMessage.success(`删除 ${item.name} 成功`)
                  methods.getTreeListFn()
                  methods.onSearch(true)
                }
              })
            },
          })
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 258
        },
        // 重置
        resetFn() {
          state.formInline = {
            tableType: 'HIVE',
            envType: 'OFFICIAL',
            keyword: null,
            time: [],
          }
          methods.init(true)
        },
        //搜索
        onSearch() {
          if (state.categoryId && state.categoryId !== -1) {
            methods.init(true)
          }
        },
        // 获取树列表
        getTreeListFn() {
          tableManagePage({
            pageNum: 1,
            pageSize: 20,
            condition: {
              tableType: state.formInline.tableType,
              envType:
                state.formInline.tableType === 'DORIS' ? 'OFFICIAL' : state.formInline.envType,
              name: state.treeSearchText,
            },
          }).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              treeData.push({
                children: [],
                id: -1,
                level: 0,
                name: '全部',
                type: 'ROOT',
              })
              if (res.data?.list?.length > 0) {
                res.data.list.forEach((val) => {
                  treeData[0].children.push({ ...val, children: [] })
                })
                treeData[0].children[0].selected = true
                state.categoryId = treeData[0].children[0].id
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]
              state.treeKey++

              nextTick(() => {
                state.expandedKeys = [state.treeData?.[0]?.id || 1]
                state.selectedKey = state.treeData?.[0]?.id || 1
              })
              methods.onSearch(true)
            }
          })
        },
        // 树搜索
        searchTreeFn() {
          if (state.timeFlag) {
            clearTimeout(state.timeFlag)
            state.timeFlag = null
          }
          state.timeFlag = setTimeout(() => {
            methods.getTreeListFn()
          }, 300)

          // state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.id) {
            state.categoryId = node.level === 1 ? null : node.id
            state.dataType = 'table'
            methods.radioFn()
            methods.onSearch(true)
          }
        },
        // 切换类型
        radioFn() {
          if (state.categoryId) {
            methods.init(true)
          }
        },
        // 初始化
        init(init = false) {
          if (state.dataType === 'data') {
            methods.getModalList(init)
          } else {
            methods.initTable(init)
          }
        },
        // 获取表数据
        getModalList(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              id: state.categoryId,
              envType: state.formInline.envType || null,
            },
          }
          api.model.getDataWithProject(data).then((res) => {
            if (res.success) {
              let tableHeadTitlesModal = []
              if (res.data.list.length > 0) {
                // 对比差异，合并字段
                const oldFields = new Set(state.tableData.list.map((item) => item.name))
                const newFields = new Set(res.data.list.flatMap((item) => Object.keys(item)))
                // const diffFields = [
                //   ...Array.from(newFields).filter((f) => !oldFields.has(f)),
                //   ...Array.from(oldFields).filter((f) => !newFields.has(f)),
                // ]
                // 解构后筛选oldFields中没有的元素
                let diffFields = [...newFields].filter(
                  (item) => ![...oldFields].some((ele) => ele === item),
                )
                state.tableData.list.forEach(({ cnName: name, name: prop }) => {
                  tableHeadTitlesModal.push({
                    prop,
                    name,
                    width: 135,
                  })
                })
                diffFields.forEach((field) => {
                  tableHeadTitlesModal.push({
                    prop: field,
                    name: field,
                    width: 135,
                  })
                })
                if (tableHeadTitlesModal.length < 5) {
                  tableHeadTitlesModal.forEach((val) => {
                    val.width = 688 / tableHeadTitlesModal.length
                  })
                }
                res.data.list.forEach((val) => {
                  tableHeadTitlesModal.forEach((v) => {
                    if (typeof val[v.prop] === 'boolean') {
                      val[v.prop] = String(val[v.prop])
                    }
                  })
                })
              }
              state.tableHeadTitles = tableHeadTitlesModal
              state.pageInfo.total = res.data.total
              state.tableData = res.data
              state.key++
            }
          })
        },
        // 获取表结构
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          // let data = {
          //   pageNum: state.pageInfo.currentPage,
          //   pageSize: state.pageInfo.pageSize,
          //   condition: {
          //     id: state.categoryId,
          //     envType: state.formInline.envType || null,
          //   },
          // }
          state.loading = true
          api.model
            .getModelFieldsList({ id: state.categoryId })
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.forEach((item, index) => {
                  item.number = index + 1
                })
                state.tableHeadTitles = [
                  { prop: 'cnName', name: '中文名称' },
                  { prop: 'name', name: '英文名称' },
                  { prop: 'fieldType', name: '字段类型' },
                  { prop: 'fieldLength', name: '字段长度' },
                ]
                state.tableData = { list: res.data }
                state.pageInfo.total = res.data.length
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 切换分页数量
        cardSizeChange(val) {
          state.pageInfo.pageSize = val
          methods.initTable(true)
        },
        // 切换分页
        cardCurrentChange(val) {
          state.pageInfo.currentPage = val
          methods.init()
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        const { name, projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        state.formInline.projectName = name.value
        methods.getTreeListFn()
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-color: #1e89ff;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    height: 100%;
    padding: 0;
    border-radius: 0;
    .data-collection-page-tree {
      box-sizing: border-box;
      width: 286px;
      height: 100%;
      padding: 8px 0;
      background-color: #fff;
      border-radius: 2px;
      :deep(.nancalui-tabs-nav-tab) {
        box-sizing: border-box;
        padding: 0 12px;
        border-bottom: none;
        .nancalui-tabs-nav-tab-list {
          width: 100%;
          .nancalui-tabs-tab {
            box-sizing: border-box;
            width: 50%;
            height: 32px;
            line-height: 32px;
            border: 1px solid #dcdfe6;
            &.nancalui-tabs-tab-active {
              border-color: #447dfd;
            }
          }
        }
      }
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 36px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
      &-ipt {
        margin-bottom: 8px;
        padding: 0 12px;
        :deep(.nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled)) {
          border: 1px solid #e5e6eb;
          .nancalui-input__inner {
            border-right: 1px solid #e5e6eb;
          }
        }

        :deep(.nancalui-input-slot__suffix) {
          opacity: 0.5;

          .icon-search {
            font-weight: normal;
            transform: scale(1.4);
          }
        }
      }
      :deep(.tree-box) {
        padding: 0 12px;
        height: calc(100% - 122px);
        .btn-box {
          opacity: 1;
          background-color: #fff;
        }
      }
    }
    .data-collection-page-content {
      width: calc(100% - 10px - var(--aside-width));
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .out-box {
        height: 100%;
        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
        &-tab {
          position: relative;
          padding: 8px;
          .only {
            position: absolute;
            top: 0;
            right: 8px;
            bottom: 0;
            height: 22px;
            margin: auto;
            color: #1d2129;
            font-size: 14px;
          }
        }
      }
      .table-list {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: calc(100% - 48px);
        padding: 0;
        overflow-x: auto;
        &-box {
          min-width: 100%;
          height: 100%;
          &.empty-table {
            :deep(.nancalui-table) {
              .nancalui-table__header-wrapper {
                display: none;
              }
            }

            :deep(.nancalui-table-v__header-wrapper) {
              display: none;
            }
          }
        }
        .nancalui-table {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }
          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;
              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }
          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }
          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;
              &.PUBLISH {
                background-color: #04c495;
              }
              &.CREATED {
                background-color: #447dfd;
              }
              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .table-content-pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        box-sizing: border-box;
        width: 100%;
        height: 60px;
        padding: 16px;
        border-top: 1px solid #dcdfe6;

        &-total {
          margin-right: 10px;
          color: rgba(0, 0, 0, 0.75);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          span {
            padding: 0 4px;
            color: var(--themeBlue);
          }
        }
        :deep(.nancalui-pagination) {
          .nancalui-pagination__size {
            margin: 0;
          }
        }
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }
        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }
  .modal-body {
    &-header {
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        .col {
          display: flex;
          flex: 1;
          flex-shrink: 0;
          align-items: center;
          justify-content: flex-start;
          height: 22px;
          color: #606266;
          font-size: 14px;
          line-height: 22px;
          .name {
            width: 100px;
          }
          .value {
            width: calc(100% - 100px);
            overflow: hidden;
            color: #1d2129;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
    &-table {
      width: 100%;
      overflow-x: auto;
      &-scroll {
        min-width: 100%;
      }
    }
  }

  .treeNodeName {
    display: inline-flex;
    align-items: center;
    width: 100%;
    height: 30px;
    overflow: hidden;
  }

  :deep(.el-table__header) {
    width: 100% !important;
  }
  :deep(.el-table__body) {
    width: 100% !important;
  }
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
