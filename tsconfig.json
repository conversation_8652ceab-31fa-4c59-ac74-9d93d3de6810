{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "noLib": false, "forceConsistentCasingInFileNames": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "lib": ["dom", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "types": ["vite/client"], "removeComments": true, "paths": {"@/*": ["src/*"], "#/*": ["types/*"], "@img/*": ["src/assets/img/*"], "@comp/*": ["src/components/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "src/store/modules/user/index.js"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}