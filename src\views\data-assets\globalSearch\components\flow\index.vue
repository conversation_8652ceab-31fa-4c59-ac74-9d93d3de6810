<template>
  <section class="flow" id="flow">
    <div id="flowContainer" class="graph"></div>
    <div v-if="bloodType === 'MODEL'" class="label-box">
      <div class="label"><span class="circle"></span>主节点</div>
    </div>
  </section>
</template>

<script>
  import { reactive, toRefs, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import FlowGraph from './graph'
  import { Model } from '@antv/x6'
  import { DagreLayout } from '@antv/layout'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'

  export default {
    props: {
      checkedItem: Object,
      detailName: String,
    },

    setup(props) {
      const router = useRouter()
      const store = useStore()
      const state = reactive({
        graph: {},
        resizeFn: null,
        cell: {},
        modelInfo: {}, // 模型详情
        relationInfo: {}, // 关系详情
        mapData: {},
        dataModelId: '',
        drawer: false,
        nodeType: '', // node:节点，edg:边
        currentProject: {},
        projectCode: '',
        bloodType: '',
        modelData: {},
      })
      watch(
        () => props.checkedItem,
        (newVal) => {
          state.dataModelId = newVal.id ? newVal.id : null
          state.projectCode = newVal.projectCode ? newVal.projectCode : null
          state.bloodType = newVal.bloodType ? newVal.bloodType : null
          state.mapData = newVal.assetsData ? JSON.parse(newVal.assetsData) : null

          if (state.mapData) {
            nextTick(() => {
              methods.setMapData()
            })
          }
        },
      )
      const methods = {
        getModelData() {
          let projectCode = state.projectCode
          api.model.getDataModelTree({ projectCode }).then((res) => {
            if (res.success) {
              state.modelData = {}
              res.data.forEach((item) => {
                state.modelData[item.id] = item.name
              })
            }
          })
        },
        // 初始化创建画布
        initFn() {
          state.graph = FlowGraph.init()
          methods.boundEvent()
          state.resizeFn = () => {
            nextTick(() => {
              methods.resizeFn()
            })
          }
          state.resizeFn()
          window.addEventListener('resize', state.resizeFn)
          state.dataModelId = props.checkedItem.id ? props.checkedItem.id : null
          state.projectCode = props.checkedItem.projectCode ? props.checkedItem.projectCode : null
          state.bloodType = props.checkedItem.bloodType ? props.checkedItem.bloodType : null
          state.mapData = props.checkedItem.assetsData
            ? JSON.parse(props.checkedItem.assetsData)
            : null
          if (state.mapData) {
            nextTick(() => {
              methods.setMapData()
            })
          }
        },
        // 计算尺寸
        resizeFn() {
          const { width, height } = methods.getContainerSize()
          state.graph.resize(width, height)
          state.graph.centerContent()
        },
        // 获取画布容器大小
        getContainerSize() {
          return {
            width: document.getElementById('flow').offsetWidth,
            height: document.getElementById('flow').offsetHeight,
          }
        },

        // 切换画布数据,默认实时
        setMapData() {
          let data = (Model.FromJSONData = {
            nodes: [],
            edges: [],
          })
          state.mapData?.nodes.forEach((item, index) => {
            data.nodes.push({
              id: item.id,
              nId: item.nId,
              data: { ...item },
              shape: 'my-box',
              size: { width: 160, height: 73 },
              ports: {
                groups: {
                  left: {
                    id: 'left',
                    position: 'left',
                    attrs: {
                      circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                  right: {
                    id: 'right',
                    position: 'right',
                    attrs: {
                      circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                },
                items: [
                  { id: 'left', group: 'left' },
                  { id: 'right', group: 'right' },
                ],
              },
              zIndex: 1,
            })
          })
          // debugger
          state.mapData?.relationship.forEach((item, index) => {
            data.edges.push({
              shape: 'edge',
              interacting: true,
              connector: 'rounded',
              router: { name: 'manhattan' },
              data: { ...item },
              attrs: {
                line: {
                  stroke: '#697A9A',
                  strokeWidth: 1,
                  targetMarker: { name: 'classic', size: 8 },
                },
              },
              zIndex: 0,
              source: { cell: item.nFrom, port: 'right' },
              target: { cell: item.nTo, port: 'left' },
            })
          })
          const dagreLayout = new DagreLayout({
            type: 'dagre',
            rankdir: 'LR',
            align: 'UL',
            ranksep: 50,
            nodesep: 20,
            controlPoints: true,
          })
          const model = dagreLayout.layout(data)
          state.graph.fromJSON(model)
          state.graph.centerContent()
        },

        // 点击node
        boundEvent() {
          state.graph.on('node:click', ({ cell }) => {
            if (cell.data.isClick && cell.data.type === 'MODEL') {
              if (String(cell.data.nId) !== String(state.dataModelId)) {
                router.push({
                  name: props.detailName,
                  query: {
                    id: cell.data.nId,
                    modelTitle: cell.data.nCnName || cell.data.nEnName,
                    modelName: cell.data.nEnName,
                    type: cell.data.type,
                  },
                })
              }
            }
          })
        },
      }

      onMounted(() => {
        methods.initFn()
      })
      onBeforeUnmount(() => {
        window.removeEventListener('resize', state.resizeFn)
        FlowGraph.destroy()
      })
      const params = toRefs(state)
      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .flow {
    width: 100%;
    border: 1px solid #c5d0ea;
    box-sizing: border-box;
    height: calc(100% - 48px);
    position: relative;
    .label-box {
      position: absolute;
      width: 80px;
      right: 0;
      top: 17px;
      .label {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 5px 0;
        color: #666666;
        font-size: 12px;
        .circle {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ff9f19;
          margin-right: 6px;
        }
      }
    }

    :deep(.x6-graph-scroller) {
      &::-webkit-scrollbar {
        width: 3px; // 横向滚动条
        height: 3px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: rgb(177, 176, 176);
        border-radius: 2px;
      }
    }

    :deep {
      .drawer-modal-bg {
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }
</style>
