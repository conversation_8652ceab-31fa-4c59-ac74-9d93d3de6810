<template>
  <section class="container" ref="tableRef">
    <div class="header">
      <div class="title">{{ state.info.name }}</div>
      <n-button @click="router.push({ name: 'assetsRegList', query: { tabs: 'menu' } })"
        >返回</n-button
      >
    </div>

    <div class="formInfo">
      <div class="tabs">
        <div :class="['tab', state.tabActive ? 'active' : '']" @click="tabChange(true)">表结构</div>
        <div :class="['tab', !state.tabActive ? 'active' : '']" @click="tabChange(false)"
          >数据预览</div
        >
        <div v-if="!state.tabActive" class="only">仅显示前20条数据</div>
      </div>
      <n-table
        :data="state.dataSource"
        fix-header
        :table-height="state.tableHeight + 'px'"
        style="width: 100%"
        v-if="state.tabActive"
      >
        <n-column resizeable field="comment" header="中文名称" />
        <n-column resizeable field="colName" header="英文名称" />
        <n-column resizeable field="dataType" header="字段类型" />
        <n-column resizeable field="length" header="字段长度" />
      </n-table>
      <n-table
        :data="state.listPreview"
        :table-height="state.tableHeight + 'px'"
        style="width: 100%"
        v-else
      >
        <n-column resizeable type="index" width="80" header="序号">
          <template #default="scope">
            {{
              `${
                state.page.pageNum === 1
                  ? scope.rowIndex + 1
                  : (state.page.pageNum - 1) * state.page.pageSize + scope.rowIndex + 1
              }`
            }}
          </template>
        </n-column>
        <n-column
          resizeable
          width="200"
          v-for="item in state.dataFiled"
          :key="item"
          :field="item"
          :header="state.headTit[item] ? state.headTit[item] : item"
        />
      </n-table>

      <!--      <n-pagination-->
      <!--        v-if="state.tabActive"-->
      <!--        :total="state.page.total"-->
      <!--        v-model:pageSize="state.page.pageSize"-->
      <!--        v-model:pageIndex="state.page.pageNum"-->
      <!--        :can-view-total="true"-->
      <!--        :can-change-page-size="true"-->
      <!--        :can-jump-page="true"-->
      <!--        :max-items="5"-->
      <!--        @page-index-change="pageChange"-->
      <!--        @page-size-change="pageSizeChange"-->
      <!--      />-->
    </div>
  </section>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { assetsMenuPreview, getDatasourceDetail } from '@/api/assets'
  /**
   * 路由实例
   */
  const router = useRouter()
  //console.log('1-开始创建组件-setup')
  /**
   * 数据部分
   */
  const tableRef = ref()
  const state = reactive({
    tabActive: true,
    info: {},
    dataSource: [],
    listPreview: [],
    dataFiled: [],
    tableHeight: 500,
    page: {
      pageNum: 1,
      pageSize: 20,
      total: 0,
    },
    headTit: {},
  })

  onMounted(() => {
    state.tableHeight = tableRef.value.clientHeight - 158
    const info = JSON.parse(sessionStorage.getItem('assetsReg'))
    state.info = info
    state.tabActive ? getMetaDataByModel() : getDataPreview()
  })

  // 获取数据列表
  const getMetaDataByModel = () => {
    getDatasourceDetail({ id: state.info.id })
      .then((res) => {
        state.loadingPreview = false
        let { data, success } = res
        if (success) {
          state.dataSource = res.data.tableColumnMeta.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })

          res.data.tableColumnMeta.forEach((item) => {
            state.headTit[item.colName] = item.comment
          })
          state.page.total = res.data.tableColumnMeta?.length || 0
        }
      })
      .catch(() => {
        state.loadingPreview = false
      })
  }

  const getDataPreview = () => {
    let data = {
      condition: {
        id: state.info.id,
      },
      pageNum: 1,
      pageSize: 20,
    }
    assetsMenuPreview(data)
      .then((res) => {
        let { success, data } = res
        if (success) {
          if (data.list && data.list.length > 0) {
            state.dataFiled = Object.keys(data.list[0])
            setTimeout(() => {
              state.listPreview = data.list
            }, 100)
          }
          state.page.total = data?.total || 0
        }
      })
      .catch(() => {
        state.loadingPreview = false
      })
  }

  const tabChange = (active) => {
    state.tabActive = active
    state.page.pageNum = 1
    state.tabActive ? getMetaDataByModel() : getDataPreview()
  }

  const pageChange = (pageNum) => {
    state.page.pageNum = pageNum === 0 ? 1 : pageNum
    state.page.pageNum = pageNum
    state.tabActive ? '' : getDataPreview()
  }
  const pageSizeChange = (pageSize) => {
    state.page.pageNum = 1
    state.page.pageSize = pageSize
    state.tabActive ? '' : getDataPreview()
  }

  // 使用toRefs解构
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .container {
    position: relative;

    .header {
      display: flex;
      align-items: center;
      align-self: stretch;
      justify-content: space-between;
      padding: 7px 8px 7px 0px;
      background: var(--100, #fff);
      border-radius: 2px;

      .title {
        color: #1d2129;
        font-size: 16px;

        &::before {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 12px;
          vertical-align: middle;
          background: var(---, #1e89ff);
          content: '';
        }
      }
    }

    .formInfo {
      position: relative;
      height: calc(100% - 56px);
      margin-top: 10px;
      background: #fff;

      .tabs {
        position: relative;
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: flex-start;
        padding: 8px;

        .tab {
          display: flex;
          gap: 4px;
          align-items: center;
          align-self: stretch;
          justify-content: center;
          box-sizing: content-box;
          width: 60px;
          height: 22px;
          padding: 5px 16px;
          color: #1d2129;
          font-size: 14px;
          background: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px 0px 0px 2px;
          cursor: pointer;

          &.active {
            color: #fff;
            background: #1e89ff;
            border-color: #1e89ff;
          }
        }
        .only {
          position: absolute;
          top: 0;
          right: 8px;
          bottom: 0;
          height: 22px;
          margin: auto;
          color: #1d2129;
          font-size: 14px;
        }
      }

      .nancalui-pagination {
        position: absolute;
        bottom: 0;
        left: 0;
        justify-content: flex-end;
        width: 100%;
        padding: 14px 16px;
        border-top: 1px solid #dcdfe6;
      }
      :deep(.nancalui-table__empty) {
        padding-top: 80px;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      align-self: stretch;
      justify-content: flex-end;
      margin-top: 10px;
      padding: 16px;
      background: #fff;
      border-radius: 2px;
    }
  }

  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
