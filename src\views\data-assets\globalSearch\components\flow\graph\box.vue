<template>
  <div class="box">
    <el-popover
      placement="bottom"
      :width="200"
      trigger="hover"
      popper-class="popover-target"
      :content="nodeInfo.type === 'COLUMN' ? nodeInfo.tableName : nodeInfo.nEnName"
      :show-arrow="false"
      :disabled="
        nodeInfo.type === 'COLUMN' ? nodeInfo.tableName.length < 22 : nodeInfo.nEnName.length < 22
      "
    >
      <template #default>
        <div class="popover-target-name">{{
          nodeInfo.type === 'COLUMN' ? nodeInfo.tableName : nodeInfo.nEnName
        }}</div>
      </template>
      <template #reference>
        <div
          :class="{
            eName: true,
            checked: nodeInfo.isMainNode,
            light:
              nodeInfo.type === 'INDICATOR' ||
              nodeInfo.type === 'COMPOSITE_INDICATOR' ||
              nodeInfo.type === 'COLUMN',
          }"
        >
          {{ nodeInfo.type === 'COLUMN' ? nodeInfo.tableName : nodeInfo.nEnName }}
        </div>
      </template>
    </el-popover>
    <el-popover
      placement="bottom"
      :width="200"
      trigger="hover"
      popper-class="popover-target"
      :content="nodeInfo.type === 'COLUMN' ? nodeInfo.nEnName : nodeInfo.nCnName"
      :show-arrow="false"
      :disabled="
        nodeInfo.type === 'COLUMN' ? nodeInfo.nEnName.length < 22 : nodeInfo.nCnName.length < 12
      "
    >
      <template #default>
        <div class="popover-target-name">{{
          nodeInfo.type === 'COLUMN' ? nodeInfo.nEnName : nodeInfo.nCnName
        }}</div>
      </template>
      <template #reference>
        <div :class="nodeInfo.type === 'MODEL' ? 'cName needHover' : 'cName'">{{
          nodeInfo.type === 'COLUMN' ? nodeInfo.nEnName : nodeInfo.nCnName
        }}</div>
      </template>
    </el-popover>
  </div>
</template>

<script>
  import { reactive, onMounted, toRefs, inject } from 'vue'
  import { useRouter } from 'vue-router'

  export default {
    inject: ['getGraph', 'getNode'],
    setup() {
      const router = useRouter()
      const state = reactive({
        node: null,
        nodeInfo: {
          nCnName: '',
          nEnName: '',
          type: '',
          tableName: '',
          isMainNode: false,
        },
      })
      state.node = inject('getNode')()
      onMounted(() => {
        nextTick(() => {
          state.nodeInfo = state.node.getData() || {
            nCnName: '',
            nEnName: '',
            type: '',
            tableName: '',
            isMainNode: false,
            runInfo: { state: '' },
          }

          // 监听数据改变事件
          state.node.on('change:data', ({ current }) => {
            state.nodeInfo = current
          })
        })
      })
      const methods = {
        // 查看
        checkDetails() {
          if (nodeInfo.isClick) {
            router.push({
              name: 'globalSearchDetail',
              query: {
                id: nodeInfo.nId,
                modelTitle: nodeInfo.nEnName || nodeInfo.nName,
                modelName: nodeInfo.nName,
                type: 'MODEL',
              },
            })
          }
        },
      }
      const params = toRefs(state)

      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .box {
    width: 160px;
    height: 73px;
    padding: 1px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #b8b8b8;
    background-color: #ffffff;
    text-align: center;
    position: relative;
    .eName {
      width: 100%;
      height: 32px;
      line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
      color: #fff;
      font-weight: bold;
      word-break: break-all;
      padding: 0 8px;
      box-sizing: border-box;
      background-color: #447dfd;
      border-radius: 3px 3px 0 0;
      &.green {
        background-color: #1db87b;
      }
      &.light {
        background-color: #8d7af8;
      }
      &.checked {
        background-color: #ff9f19;
      }
    }
    .cName {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
      color: #000000;
      font-weight: bold;
      padding: 10px 8px;
      box-sizing: border-box;
      word-break: break-all;
      &.needHover:hover {
        color: $themeBlue;
        cursor: pointer;
      }
    }
  }
</style>
