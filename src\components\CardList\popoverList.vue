<template>
  <div class="card-popover">
    <div
      v-if="state.cardOperation.indexOf('n') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardConfirmFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-distribute" title="确认" />确认</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('m') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardCopyFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-copy" />一键复制</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('l') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardSyncFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-sync" />同步至生产</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('f') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardRunFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-rerun" />立即执行</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('j') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardDevelopFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-dev" />开发</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('a') !== -1"
      :class="info.cardStatus === 3 ? 'card-popover-item disabled' : 'card-popover-item'"
      @click.prevent="operationFn('cardSendFn', info.cardStatus === 3)"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-send1" />发布</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('c') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardEditFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-edit" />编辑</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('b') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardDelFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-del" />删除</span>
    </div>

    <div
      v-if="state.cardOperation.indexOf('d') !== -1"
      :class="info.cardStatus === 5 ? 'card-popover-item disabled' : 'card-popover-item'"
      @click.prevent="operationFn('cardOffFn', info.cardStatus === 5)"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-off" />下架</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('e') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardSeeFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-see" />查看</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('g') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardCancelFn')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-cancel" />撤回</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('h') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardTestConnected')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-connected" />测试</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('i') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardUnbind')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-unbind" />解绑</span>
    </div>
    <div
      v-if="state.cardOperation.indexOf('k') !== -1"
      class="card-popover-item"
      @click.prevent="operationFn('cardDataSourceApply')"
    >
      <span><SvgIcon class="icon-card" icon="icon-card-apply" />申请</span>
    </div>
  </div>
</template>

<script>
  import { reactive, computed } from 'vue'
  export default {
    props: {
      cardType: {
        type: Number,
        default: 1,
      },
      cardOperation: {
        type: String,
        default: '',
      },
      info: {
        type: Object,
        default: () => {},
      },
    },
    setup(props, { emit }) {
      const state = reactive({
        cardType: '',
        cardOperation: '',
        info: {},
      })
      state.cardType = computed(() => {
        return props.cardType || 1
      })
      state.cardOperation = computed(() => {
        return props.cardOperation || ''
      })
      state.info = computed(() => {
        return props.info || {}
      })
      const operationFn = (name, flag = false) => {
        if (!flag) {
          emit('childOperation', { name, info: state.info })
        }
      }
      return {
        state,
        operationFn,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
</style>
