<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-100.000000, -120.000000)">
            <g id="逆向数据库btn" transform="translate(88.000000, 114.000000)">
                <g id="icon_nx_btn_s" transform="translate(12.000000, 6.000000)">
                    <rect id="矩形" fill-opacity="0" fill="currentColor" x="0" y="0" width="18" height="18"></rect>
                    <rect id="矩形" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" x="3.30909091" y="3" width="5.72727273" height="10"></rect>
                    <rect id="矩形" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" x="9.03636364" y="4.875" width="5.72727273" height="10"></rect>
                    <polyline id="路径" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" points="6.80909091 6.75 5.53636364 7.93421875 6.70304091 9.25"></polyline>
                    <polyline id="路径" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" points="11.2636364 8.625 12.5363636 9.80921875 11.3696864 11.125"></polyline>
                </g>
            </g>
        </g>
    </g>
</svg>