<template>
  <!-- 配置校验信息 -->
  <div class="data-collection-configure-scheduling">
    <div class="box-bottom">
      <div class="bottom-outBox scroll-bar-style">
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="150px"
          label-align="start"
          class="configure-ruleForm"
          :key="state.key"
        >
          <n-form-item v-if="showTaskRule" class="task-rule">
            <div class="task-rule-box">
              <div class="rule-type">
                <div class="label">数据库类型：</div>
                <div>{{ dataSourceType ? dataSourceType : '无数据' }}</div>
              </div>
              <!--              <n-form-item label="采集规则：" field="collectRule" class="collect-rule">-->
              <!--                <n-radio-group direction="row" v-model="state.ruleForm.collectRule">-->
              <!--                  <n-radio-->
              <!--                    v-for="item in state.ruleOptions"-->
              <!--                    :key="item.value"-->
              <!--                    :disabled="-->
              <!--                      state.disabled || !(dataSourceType === 'ORACLE' || dataSourceType === 'MYSQL')-->
              <!--                    "-->
              <!--                    :value="item.value"-->
              <!--                    >{{ item.label }}</n-radio-->
              <!--                  >-->
              <!--                </n-radio-group>-->
              <!--              </n-form-item>-->
            </div>
          </n-form-item>
          <n-form-item class="taskType" label="调度策略：">
            <n-radio-group direction="row" v-model="state.ruleForm.configureType">
              <n-radio value="auto">自动化调度</n-radio>
              <n-radio value="hander">手动调度</n-radio>
            </n-radio-group>
          </n-form-item>
          <div v-if="state.ruleForm.configureType === 'auto'">
            <n-form-item class="task-content" label="">
              <div class="configuration-content">
                <div class="configuration-content-col">
                  <!-- <div class="task-content-start-time">
                  <n-form-item label="任务首次执行时间：" field="executionData">
                    <div class="execution-time-box">
                      <n-col :span="11">
                        <n-date-picker-pro
                          v-model="state.ruleForm.executionData"
                          placeholder="年/月/日"
                          format="YYYY/MM/DD"
                          allow-clear
                          @confirmEvent="startDateChange"
                        />
                      </n-col>
                      <n-col :span="2" />
                      <n-col :span="11">
                        <n-form-item field="executionTime">
                          <n-time-picker
                            v-model="state.ruleForm.executionTime"
                            placeholder="00:00:00"
                            style="width: 100%"
                          />
                        </n-form-item>
                      </n-col>
                    </div>
                  </n-form-item>
                  <div class="tips">到达该配置时间，任务将自动运行</div>
                </div> -->

                  <n-form-item
                    label="采集频率："
                    class="task-content-collect-rate"
                    field="configureTimes"
                  >
                    <n-radio-group direction="row" v-model="state.ruleForm.configureTimes">
                      <n-radio value="time">配置时间</n-radio>
                      <n-radio value="cron">cron表达式</n-radio>
                    </n-radio-group>
                  </n-form-item>
                  <n-form-item
                    v-if="state.ruleForm.configureTimes === 'time'"
                    label=" "
                    class="task-content-collect-rate"
                  >
                    <div class="task-content-collect-rate-box">
                      <div class="collection-rate-box">
                        <n-form-item label="" field="period">
                          <n-radio-group direction="row" v-model="state.ruleForm.period">
                            <n-radio
                              v-for="item in state.periodOptions"
                              :key="item.value"
                              :value="item.value"
                              @change="periodChange"
                              >{{ item.label }}</n-radio
                            >
                          </n-radio-group>
                        </n-form-item>

                        <n-form-item
                          v-if="state.ruleForm.period === 'HOUR'"
                          class="time-box"
                          label=""
                          field="extent"
                        >
                          <!-- 开头不能为0，且不能输入小数 -->
                          <n-input
                            v-model="state.ruleForm.extent"
                            maxlength="30"
                            placeholder="请输入间隔小时"
                            @keydown="onKeydownPositiveInteger($event, state.ruleForm.extent)"
                            @keyup="onKeyupPositiveInteger($event, state.ruleForm.extent)"
                            @focus="inputFocus($event)"
                            @blur="fieldBlur('extent')"
                            :onpaste="
                              () => {
                                return false
                              }
                            "
                          />
                        </n-form-item>

                        <n-form-item
                          v-if="state.ruleForm.period == 'WEEK' || state.ruleForm.period == 'MONTH'"
                          class="time-box"
                          label=""
                          field="extent"
                        >
                          <n-select
                            v-if="state.ruleForm.period === 'WEEK'"
                            v-model="state.ruleForm.extent"
                            style="width: 100%"
                            placeholder="请选择周几"
                            multiple
                            :options="state.weekList"
                            :key="state.key"
                          />
                          <n-select
                            v-else-if="state.ruleForm.period === 'MONTH'"
                            v-model="state.ruleForm.extent"
                            style="width: 100%"
                            placeholder="请选择几号"
                            multiple
                            :options="state.monthList"
                            :key="state.key"
                          />
                        </n-form-item>
                        <n-form-item
                          v-if="state.ruleForm.period !== 'HOUR'"
                          class="time-box"
                          label=""
                          field="rateTime"
                        >
                          <n-time-picker
                            v-model="state.ruleForm.rateTime"
                            placeholder="00:00:00"
                            style="width: 100%"
                          />
                        </n-form-item>
                      </div>
                      <div class="tips">到达该配置时间，任务将自动运行</div>
                    </div>
                  </n-form-item>
                  <n-form-item v-else label=" " class="task-content-collect-cron" field="cron">
                    <n-input
                      v-model="state.ruleForm.cron"
                      size="small"
                      placeholder="请输入corn表达式"
                      maxlength="100"
                      clearable
                    />
                    <div class="tips">到达该配置时间，任务将自动运行</div>
                  </n-form-item>
                </div>
                <div class="configuration-content-col">
                  <n-form-item class="taskType dif" label="失败重试：">
                    <n-switch
                      v-model="state.ruleForm.rerun"
                      :disabled="!state.ruleForm.taskType"
                      color="#447DFD"
                      @change="rerunSwitchChange"
                    />
                  </n-form-item>
                  <n-form-item class="rerun-content">
                    <!-- 失败重试 -->
                    <div v-show="state.ruleForm.rerun" class="rerun-mechanism">
                      <n-form-item label="" field="retryCount">
                        <div class="rerun-mechanism-div">
                          <n-input
                            v-model="state.ruleForm.retryCount"
                            placeholder="0"
                            @keydown="onKeydownPositiveInteger($event, state.ruleForm.retryCount)"
                            @keyup="onKeyupPositiveInteger($event, state.ruleForm.retryCount)"
                            @focus="inputFocus($event)"
                            @blur="fieldBlur('retryCount')"
                            :onpaste="
                              () => {
                                return false
                              }
                            "
                          >
                            <template #append>次</template>
                          </n-input>
                          <div class="input-header">重试次数</div>
                        </div>
                      </n-form-item>
                      <n-form-item label="" field="retryTime"
                        ><div class="rerun-mechanism-div second">
                          <n-input
                            v-model="state.ruleForm.retryTime"
                            placeholder="0"
                            @keydown="onKeydownPositiveInteger($event, state.ruleForm.retryTime)"
                            @keyup="onKeyupPositiveInteger($event, state.ruleForm.retryTime)"
                            @focus="inputFocus($event)"
                            @blur="fieldBlur('retryTime')"
                            :onpaste="
                              () => {
                                return false
                              }
                            "
                          >
                            <template #append>分</template>
                          </n-input>
                          <div class="input-header">重试间隔</div>
                        </div>
                      </n-form-item>
                    </div>
                  </n-form-item>
                  <n-form-item
                    class="configure-effectiveDate"
                    label="生效时间："
                    style="margin-right: 0"
                    field="effectiveDate"
                  >
                    <n-range-date-picker-pro
                      v-model="state.ruleForm.effectiveDate"
                      :placeholder="['开始日期', '结束日期']"
                      format="YYYY-MM-DD HH:mm:ss"
                      allow-clear
                      showTime
                    >
                      <template #footer>
                        <slot name="footer">
                          <div class="date-picker-footer configure-scheduling">
                            <n-button variant="solid" color="primary" @click="setData">
                              长期有效
                            </n-button>
                          </div>
                        </slot>
                      </template>
                    </n-range-date-picker-pro>
                  </n-form-item>
                </div>
              </div>
            </n-form-item>
          </div>
        </n-form>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'
  import { formartTimeDate } from '@/utils/index'
  export default {
    name: '',
    components: {},
    props: {
      showTaskRule: {
        type: Boolean,
        default: true,
      },
      dataSourceType: {
        type: String,
        default: '',
      },
    },
    setup(props) {
      const checkRangeDatePickerPro = (rule, value, callback) => {
        if (!value || (!value[0] && !value[1])) {
          return callback(new Error('请选择生效时间'))
        } else if (!value[0]) {
          return callback(new Error('请选择开始日期'))
        } else if (!value[1]) {
          return callback(new Error('请选择结束日期'))
        } else {
          return callback()
        }
      }

      const state = reactive({
        key: 1,
        disabled: false,
        ruleForm: {
          dataSourceType: '',
          collectRule: 'FULL',
          taskType: true,
          configureType: 'auto',
          configureTimes: 'time',
          rerun: true,
          executionData: '',
          executionTime: '',
          period: 'HOUR',
          rateTime: '',
          extent: '',
          retryCount: 1,
          retryTime: 3,
          hour: '',
          scheduleId: null, //调度id
          effectiveDate: [], //有效时间区间
          cron: '',
        },
        rules: {
          cron: { required: true, message: '请输入cron表达式', trigger: 'blur' },
          configureTimes: { required: true, message: '请选择采集频率', trigger: 'change' },
          collectRule: { required: true, message: '请选择采集规则', trigger: 'change' },
          // executionData: {
          //   required: true,
          //   message: '请选择执行日期',
          //   trigger: 'change',
          // },
          // executionTime: {
          //   required: true,
          //   message: '请输入执行时间',
          //   trigger: 'change',
          // },
          period: { required: true, message: '请选择采集频率周期', trigger: 'change' },
          extent: { required: true, message: '请选择或者输入', trigger: 'blur' },

          rateTime: { required: true, message: '请选择采集频率时间', trigger: 'change' },
          hour: { required: true, message: '请输入间隔小时数', trigger: 'blur' },
          effectiveDate: {
            required: true,
            validator: checkRangeDatePickerPro,
            trigger: 'change',
            type: 'array',
          },

          // retryCount: { required: true, message: '请设置重试次数', trigger: 'blur' },
          // retryTime: { required: true, message: '请设置重试间隔', trigger: 'blur' },},
        },
        ruleOptions: [
          {
            label: '全量采集',
            value: 'FULL',
          },
          // {
          //   label: '增量采集',
          //   value: 'INCREMENT',
          // },
        ],
        sourceTableData: [],
        authorizedTableData: [],
        allData: {
          name: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [],
          authorizedPage: [],
        },
        queryData: '',
        editId: null, // 修改模式id
        periodOptions: [
          {
            label: '小时',
            value: 'HOUR',
          },
          {
            label: '日',
            value: 'DAY',
          },
          {
            label: '周',
            value: 'WEEK',
          },
          {
            label: '月',
            value: 'MONTH',
          },
        ],
        weekList: [
          { name: '周一', value: '1' },
          { name: '周二', value: '2' },
          { name: '周三', value: '3' },
          { name: '周四', value: '4' },
          { name: '周五', value: '5' },
          { name: '周六', value: '6' },
          { name: '周日', value: '7' },
        ],
        monthList: [],
        totalData: {},
      })
      const router = useRouter()
      const ruleForm = ref()
      const disabledDate = (time) => {
        return time.getTime() < Date.now() - 8.64e7
      }
      const methods = {
        setData() {
          state.ruleForm.effectiveDate[1] = new Date('2099-12-31 23:59:59')
        },
        fieldBlur(key) {
          state.ruleForm[key] = state.ruleForm[key].replace(/\D/g, '')
          if (key === 'extent') {
            if (state.ruleForm[key] > 23) {
              state.ruleForm[key] = 23
            } else if (state.ruleForm[key] < 1) {
              state.ruleForm[key] = 1
            }
          }
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },

        onKeydown(e) {
          let key = e.key
          let number = '1234567890'
          if (number.includes(key) || key === 'Backspace') {
            if (state.form.tokenExpireIncrementDays.toString().length === 1 && key === '0') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          } else {
            e.returnValue = false
          }
        },
        //调度开始日期change
        // startDateChange(e) {
        //   state.ruleForm.executionData = methods.formartTime(e)
        // },
        formartTime(time) {
          var year = time.getFullYear()
          var mon = time.getMonth() + 1
          var day = time.getDate()
          var submitTime = ''
          submitTime += year + '/'
          if (mon >= 10) {
            submitTime += mon + '/'
          } else {
            submitTime += '0' + mon + '/'
          }
          if (day >= 10) {
            submitTime += day
          } else {
            submitTime += '0' + day
          }

          return submitTime
        },
        //周月日调度频率change
        periodChange() {
          if (state.ruleForm.period == 'WEEK' || state.ruleForm.period == 'MONTH') {
            state.ruleForm.extent = []
            state.rules.extent = {
              required: true,
              type: 'array',
              message: '请选择或者输入',
              trigger: 'blur',
            }
          } else {
            state.ruleForm.extent = ''
            state.rules.extent = { required: true, message: '请选择或者输入', trigger: 'blur' }
          }
          state.key++
        },
        // 正整数值校验
        checkingValue(value) {
          let _value = String(state.ruleForm[value])
          if (_value.includes('-') || !_value) {
            state.ruleForm[value] = 0
          }
        },
        // 调度开关改变
        switchChange(on) {
          if (on) {
            methods.changeRulesStatus()
          } else {
            methods.changeRulesStatus(false)
            state.ruleForm.rerun = false
          }
        },
        // 重启机制开关
        rerunSwitchChange(on) {
          if (!on) {
            state.ruleForm.rerun = false
          }
        },
        // 初始化数据
        init(data) {
          let { name, description, dataSourceType, schedule, collectRule, retryTime, retryCount } =
            data
          state.totalData = data
          state.allData.name = name
          state.allData.description = description
          state.ruleForm.dataSourceType = dataSourceType
          if (schedule.period == 'WEEK' || schedule.period == 'MONTH') {
            state.rules.extent = {
              required: true,
              type: 'array',
              message: '请选择或者输入',
              trigger: 'blur',
            }
          } else {
            state.rules.extent = { required: true, message: '请选择或者输入', trigger: 'blur' }
          }
          if (state.editId) {
            if (schedule?.schedulingStrategy === 'AUTO') {
              // let time = schedule.startTime.replace(/\-/g, '/')
              // time = time.split(' ')
              // state.ruleForm.executionData = time[0]
              // state.ruleForm.executionTime = time[1]
              state.ruleForm.period = schedule.period
              state.ruleForm.extent = schedule.extent || null
              state.ruleForm.rateTime = schedule.rateTime
              state.ruleForm.retryCount = retryCount
              state.ruleForm.retryTime = retryTime
              state.ruleForm.scheduleId = schedule.id
              state.ruleForm.effectiveDate = [schedule.fromDateTime, schedule.thruDateTime]
              if (schedule?.period == 'WEEK' || schedule?.period == 'MONTH') {
                state.ruleForm.extent = schedule?.extent?.split(',') || []
              }
              if (schedule.periodicDeclarationMode === 'CRON_EXPRESSION') {
                state.ruleForm.configureTimes = 'cron'
                state.ruleForm.cron = schedule.cron
              } else {
                state.ruleForm.configureTimes = 'time'
              }
              if (state.ruleForm.retryCount || state.ruleForm.retryTime) {
                state.ruleForm.rerun = true
              } else {
                state.ruleForm.rerun = false
              }
            } else {
              state.ruleForm.configureType = 'hander'
              state.ruleForm.taskType = false
              state.ruleForm.rerun = false
            }
            setTimeout(() => {
              state.ruleForm.collectRule = collectRule
              state.key++
            }, 60)
          } else {
            state.key++
          }
        },

        // 回传数据
        async getAllData() {
          let passed = false
          // let time = ''

          if (state.ruleForm.configureType === 'auto') {
            let _result = await new Promise((resolve) => {
              ruleForm.value.validate((valid) => {
                resolve({ passed: valid })
              })
            })
            if (!_result.passed) {
              return _result
            }
            passed = true

            // time = state.ruleForm.executionData.slice(0, 10) + ' ' + state.ruleForm.executionTime
            // time = time.replace(/\//g, '-')

            //解决时间选择器返回的日期对象和编辑不更改是字符串的问题
            let _fromDateTime = null
            let _thruDateTime = null

            if (state.ruleForm.effectiveDate[0]) {
              if (
                Object.prototype.toString.call(state.ruleForm.effectiveDate[0]) === '[object Date]'
              ) {
                _fromDateTime = formartTimeDate(state.ruleForm.effectiveDate[0], '-', true)
              } else {
                _fromDateTime = state.ruleForm.effectiveDate[0]
              }
            }
            if (state.ruleForm.effectiveDate[1]) {
              if (
                Object.prototype.toString.call(state.ruleForm.effectiveDate[1]) === '[object Date]'
              ) {
                _thruDateTime = formartTimeDate(state.ruleForm.effectiveDate[1], '-', true)
              } else {
                _thruDateTime = state.ruleForm.effectiveDate[1]
              }
            }

            if (state.ruleForm.configureTimes === 'cron') {
              state.totalData.schedule = {
                ruleForm: state.ruleForm,
                period: '', // 周期  day, week, month, interval
                rateTime: '',
                cron: state.ruleForm.cron,
                extent: '',
                fromDateTime: _fromDateTime, //有效期开始时间
                thruDateTime: _thruDateTime, //有效期结束时间
              }
            } else {
              state.totalData.schedule = {
                ruleForm: state.ruleForm,
                period: state.ruleForm.period, // 周期  day, week, month, interval
                rateTime: state.ruleForm.rateTime || '',
                cron: '',
                extent: state.ruleForm.extent || '',
                fromDateTime: _fromDateTime, //有效期开始时间
                thruDateTime: _thruDateTime, //有效期结束时间
              }
            }

            if (state.editId) {
              state.totalData.schedule.id = state.ruleForm.scheduleId
            }
            state.totalData.dataSourceType = state.ruleForm.dataSourceType
            state.totalData.retryCount = state.ruleForm.rerun ? state.ruleForm.retryCount : 0
            state.totalData.retryTime = state.ruleForm.rerun ? state.ruleForm.retryTime : 0
          } else {
            passed = true
            state.totalData.schedule = null
            state.totalData.retryCount = 0
            state.totalData.retryTime = 0
          }
          state.totalData.collectRule = state.ruleForm.collectRule

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate(() => {
              resolve({ passed, data: state.totalData })
            })
          })
          return _result2
        },
        changeRulesStatus(required = true) {
          Object.keys(state.rules).forEach((key) => {
            if (key !== 'collectRule') {
              state.rules[key].required = required
            }
          })
        },
      }
      watch(
        () => props.dataSourceType,
        () => {
          // 普通的watch监听
          state.ruleForm.collectRule = 'FULL'
        },
      )
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.editId = state.queryData.editId

        state.disabled = state.queryData.status && state.queryData.status !== 'NEW' ? true : false
        if (state.editId) {
          // state.exceptHeight = 380
          // methods.changeRulesStatus()
        } else {
          state.ruleForm.effectiveDate[0] = new Date()
          state.ruleForm.effectiveDate[1] = new Date('2099-12-31 23:59:59')
        }
        let monthList = []
        for (let i = 1; i <= 31; i++) {
          monthList.push({
            name: i + '号',
            value: i.toString(),
          })
        }
        state.monthList = monthList
      })

      return {
        state,
        ruleForm,
        disabledDate,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f4f4f4;
  $outBg: #eeeeee;

  .data-collection-configure-scheduling {
    height: calc(100% - 10px);
    background: #eeeeee;
    background-color: $outBg;
    .nancalui-col__span--2 {
      flex: inherit;
      width: 10px !important;
    }
    :deep(.nancalui-radio-group) {
      .nancalui-radio__label {
        font-weight: 400;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }

    .box-bottom {
      height: 100%;
      padding: 0 16px;
      background-color: $contentBoxBg;
      .bottom-outBox {
        width: 100%;
        height: 100%;
        //overflow-y: auto;
      }
      :deep(.nancalui-form) {
        min-width: 658px;

        .task-rule {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 0px;
          .nancalui-form__label {
            align-self: flex-start;
          }
          .task-rule-box {
            display: flex;
            width: 100%;
            margin-bottom: 10px;
          }
          > .nancalui-form__label {
            flex: 0 0 32px !important;

            margin-bottom: 10px;
            font-weight: bolder;
            font-size: 14px;
            .nancalui-form__label-span {
              color: #333;
            }
          }
          .nancalui-form__control {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 0 !important;
            border-radius: 8px;

            .rule-type {
              display: flex;
              flex: 1;
              align-items: center;
              height: 32px;
              color: var(----, rgba(0, 0, 0, 0.85));
              font-weight: bolder;
              font-size: 14px;
              .label {
                width: 150px;
              }
              & div:last-child {
                margin-right: 16px;
                color: #000;
                font-weight: normal;
              }
              & div:not(:last-child) {
                &::before {
                  display: inline-block;
                  margin-right: 2px;
                  color: #f52f3e;
                  vertical-align: middle;
                  content: '*';
                }
              }
            }
            .collect-rule {
              display: flex;
              flex: 1;
              align-items: center;
              margin-bottom: 0;
              .nancalui-form__control {
                padding: 0;
              }
            }
          }
        }

        .taskType {
          position: relative;
          margin-bottom: 10px;
          .nancalui-form__label {
            font-weight: bolder;
            font-size: 14px;
            .nancalui-form__label-span {
              color: #333;
            }
          }
          &.dif {
            margin-bottom: 0;
            padding: 0 0 14px;
            .nancalui-form__label {
              flex: 0 0 150px !important;
            }
          }
        }
        .task-content {
          width: 100%;
          margin-bottom: 0;
          //overflow-y: auto;
          .nancalui-form__control {
            height: 100%;
          }
          .configuration-content {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
            min-height: 240px;
            &-col {
              flex: 1;
            }
            .task-content-start-time {
              .nancalui-form__item--horizontal {
                display: flex;
                margin-bottom: 0;
                .nancalui-form__label {
                  flex: 0 0 150px !important;
                }
              }
              .execution-time-box {
                display: flex;
                width: 100%;
                .nancalui-date-picker-pro {
                  width: 100%;
                }
                .nancalui-col {
                  height: 32px;
                }
              }
              .tips {
                margin-bottom: 16px;
                margin-left: 150px;
                padding-top: 14px;
                color: #999999;
                font-weight: 400;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                text-align: left;
              }
            }
            .task-content-collect-rate {
              display: flex;
              margin-bottom: 16px;
              .task-content-collect-rate-box {
                width: 100%;
              }
              .nancalui-form__label {
                flex: 0 0 150px !important;
              }
              .collection-rate-box {
                max-width: 430px;
                .nancalui-form__item--horizontal {
                  height: 30px;
                  .nancalui-radio-group {
                    align-items: center;
                    height: 30px;
                  }
                }
                .time-box {
                  margin-top: 16px;
                  height: auto;
                }
              }
              .tips {
                padding-top: 6px;
                color: #999999;
                font-weight: 400;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                text-align: left;
              }
            }
            .task-content-collect-cron {
              .nancalui-input {
                text-align: left;
                .nancalui-input__wrapper {
                  width: 92.9%;
                }
              }
              .nancalui-form__label {
                flex: 0 0 150px !important;
                opacity: 0;
              }
              .tips {
                padding-top: 6px;
                color: #999999;
                font-weight: 400;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                text-align: left;
              }
            }
          }
        }
        .rerun-content {
          margin-bottom: 16px;
          & > .nancalui-form__control {
            margin-left: 150px;
          }
          .nancalui-form__control {
            .nancalui-form__control-container {
              .rerun-mechanism {
                display: flex;
                width: 100%;
                overflow-y: auto;
                text-align: center;
                border-radius: 4px;
                .nancalui-form__item--horizontal {
                  margin-bottom: 0;
                }
              }
              .rerun-mechanism-div {
                position: relative;
                max-width: 207px;
                &.second {
                  margin-left: 16px;
                }
                .input-header {
                  position: absolute;
                  top: 6px;
                  bottom: 7px;
                  left: 0;
                  padding: 0 10px;
                  font-size: 12px;
                  line-height: 20px;
                  border-right: 1px solid #ebedf0;
                }
                .nancalui-input__wrapper {
                  border-radius: 6px 0 0 6px;
                }
                .nancalui-input__inner {
                  padding-left: 70px;
                }
              }
            }
            .nancalui-input-slot__append {
              color: #646566;
              font-size: 12px;
              background-color: #f5f7fa;
              border-color: #a3b4db;
            }
          }
        }
        .configure-effectiveDate {
          margin-bottom: 0;
          .nancalui-range-date-picker-pro .nancalui-range-date-picker-pro__range-picker {
            min-height: 32px;
          }
          .nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-time-width {
            width: 100%;
            max-width: 430px;
          }
          .nancalui-form__label {
            flex: 0 0 150px !important;
          }
          .nancalui-input__inner::placeholder {
            text-align: center;
          }
        }

        .no-config {
          min-height: 282px;
          margin: 0 auto;
          overflow-y: auto;
          text-align: center;
          border-radius: 4px;
          img {
            width: 170px;
            margin: 75px auto 10px;
          }
          p {
            color: #999999;
            font-weight: 400;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 20px;
          }
        }
      }
    }
  }
</style>
<style>
  .configure-scheduling.date-picker-footer {
    text-align: right;
  }
</style>
