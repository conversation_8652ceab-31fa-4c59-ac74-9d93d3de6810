<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <n-select
            v-model="state.originalFormInline.envType"
            placeholder="请选择环境"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.envTypeList"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
          <span class="label">作业类型：</span>
          <n-select
            v-model="state.originalFormInline.taskType"
            placeholder="请选择作业类型"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.workOptions"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
          <span class="label">名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="离线作业名称"
            size="small"
            clearable
          />
          <span class="label">调度类型：</span>
          <n-select
            v-model="state.originalFormInline.scheduleType"
            placeholder="请选择调度类型"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.scheduleOptions"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-title">离线监控</div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                saveWidth
                :key="state.key"
                :isDisplayAction="true"
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    initTable()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    initTable()
                  },
                }"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="320"
              >
                <template #name="{ row }">
                  <div class="taskName">
                    {{ row.name }}
                  </div>
                </template>
                <template #state="{ row }">
                  <div class="statusBox">
                    <div v-if="row.state === 'SUCCESS'" class="label"
                      ><SvgIcon class="icon" icon="icon-true" />成功</div
                    >
                    <div v-else-if="row.state === 'FAILURE'" class="label"
                      ><SvgIcon class="icon" icon="icon-false" />失败</div
                    >
                    <div v-else-if="row.state === 'STOP'" class="label"
                      ><SvgIcon class="icon" icon="icon-stop-new" />停止</div
                    >
                    <div v-else-if="row.state === 'RUNNING_EXECUTION'" class="label"
                      ><SvgIcon class="icon" icon="icon-run-new" />运行中</div
                    >
                    <div v-else-if="row.state === 'WAITING_TO_RUN'" class="label"
                      ><SvgIcon class="icon" icon="icon-wait-new" />等待运行</div
                    >
                    <span v-else>--</span>
                  </div>
                </template>
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      code="dataManagement_collectionMonitor_refresh_edit"
                      variant="text"
                      @click.prevent="goDAGFn(row)"
                      >DAG图</n-button
                    >
                    <n-button
                      class="has-right-border"
                      code="dataManagement_collectionMonitor_view"
                      variant="text"
                      @click.prevent="cardSeeFn(row)"
                      >查看实例</n-button
                    >
                    <n-button
                      class="has-right-border"
                      code="dataManagement_collectionMonitor_view"
                      variant="text"
                      @click.prevent="cardRunFn(row, 'CURRENT_NODE')"
                      >补当前作业</n-button
                    >
                    <n-button
                      class="has-right-border"
                      code="dataManagement_collectionMonitor_view"
                      variant="text"
                      @click.prevent="cardRunFn(row, 'CUSTOMED_NODE')"
                      >补当前及下游</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-modal
      v-model="state.showDag"
      title="DAG图"
      class="largeDialog has-top-padding"
      width="960px"
      :close-on-click-overlay="false"
      @close="closeDialog"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-flow"><flow :checkedItem="state.checkedItem" :isPopup="true" /></div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showDag = false">取消</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import ENUM from '@/const/enum'
  import flow from '../flow'
  import {
    offlineWorkList,
    operationWorkDetail,
    supplementDataRun,
    instanceDag,
  } from '@/api/dataManage'
  import { changeFrequencyChinese } from '@/utils/index.js'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  export default {
    name: '',
    components: { flow },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: {},
        tableHeight: 436,
        key: 1,
        collectType: 'ALL',
        loading: false,
        showDag: false,
        checkedItem: null,
        originalFormInline: {
          envType: 'OFFICIAL',
          taskType: null,
          scheduleType: null,
          keyword: null,
        },
        formInline: {
          envType: 'OFFICIAL',
          taskType: null,
          scheduleType: null,
          keyword: null,
        },
        optionItemData: {}, // 操作的任务数据
        envTypeList: [
          { name: '生产环境', value: 'OFFICIAL' },
          { name: '开发环境', value: 'TEST' },
        ],
        workOptions: [
          { name: 'PySpark', value: 'OW_PY_SPARK' },
          { name: 'HiveSQL', value: 'OW_HIVE_SQL' },
          { name: 'SparkSQL', value: 'OW_SPARK_SQL' },
          { name: 'Python', value: 'OW_PYTHON' },
          { name: 'Shell', value: 'OW_SHELL' },
          { name: '同步至资源库', value: 'OW_DATA_ASYNC' },
          { name: 'HiveDDL', value: 'OW_HIVE_DDL' },
        ],
        scheduleOptions: [
          { name: '正常', value: 'DEFAULT' },
          { name: '暂停', value: 'PAUSE' },
          { name: '空跑', value: 'DRY_RUN' },
        ],
        shortcuts: ENUM.SHORTCUTS,
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'name', name: '作业名称', slot: 'name' },
          { prop: 'processName', name: '业务流程' },
          { prop: 'taskTypeName', name: '作业类型' },
          { prop: 'scheduleTypeName', name: '调度状态' },
          { prop: 'scheduleText', name: '调度周期' },
          // { prop: 'state', name: '运行状态', slot: 'state' },
          // { prop: 'timeText', name: '运行时长' },
          { prop: 'personInChargeName', name: '责任人' },
          { prop: 'submitName', name: '提交人' },
          { prop: 'submitTime', name: '提交时间', width: 160 },
          { prop: 'currentVersionText', name: '当前版本' },
        ],
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 294
        },
        // 跳转DAG图
        goDAGFn(item) {
          state.showDag = true
          operationWorkDetail({ id: item.processId, nodeStatus: true }).then((res) => {
            if (res.success) {
              let hasFailure = false
              res.data.taskDef.forEach((val) => {
                if (!val.nodeFrozen) {
                  val.nodeFrozen = false
                }
                if (val.nodeRunstatus === 'FAILURE') {
                  hasFailure = true
                }
                val.nodeId = val.id
                val.checked = false
              })
              res.data.scheduleText = changeFrequencyChinese(res.data)
              state.checkedItem = {
                nodeCode: item.nodeCode,
                nodes: res.data.taskDef,
                relationship: res.data.relation,
                dsProcessCode: res.data.dsProcessCode,
                dsTaskId: res.data.dsTaskId,
                processId: res.data.id,
                collectJobIds: res.data.collectJobIds,
                hasFailure: hasFailure,
              }
            }
          })
        },
        closeDialog() {
          state.showDag = false
          return false
        },
        // 补数据
        cardRunFn(item, type) {
          supplementDataRun({ processId: item.processId, nodeCode: item.nodeCode }).then((res) => {
            if (res.success) {
              if (res.data) {
                instanceDag({
                  dsTaskId: res.data,
                  processId: item.processId,
                  taskCode: item.nodeCode,
                  instanceOperationType: 'RUN_AGAIN',
                  runAgainType: type,
                }).then((resp) => {
                  if (resp.success) {
                    ElNotification({
                      title: '提示',
                      message: '补数据成功',
                      type: 'success',
                    })
                    methods.initTable()
                  }
                })
              } else {
                ElNotification({
                  title: '提示',
                  message: '当前业务流程暂未执行第一轮调度，不可执行该操作。',
                  type: 'warning',
                })
              }
            }
          })
        },
        // 查看实例
        cardSeeFn(item) {
          router.push({
            name: 'offlineExample',
            query: {
              id: item.processId,
              nodeCode: item.nodeCode,
              envType: state.formInline.envType,
            },
          })
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            envType: 'OFFICIAL',
            taskType: null,
            scheduleType: null,
            keyword: null,
          }
          methods.searchClickFn()
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        //搜索
        onSearch() {
          state.key++
          methods.initTable(true)
        },
        // 初始化表格（列表）
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              name: state.formInline.keyword || null,
              envType: state.formInline.envType || null,
              taskType: state.formInline.taskType || null,
              scheduleType: state.formInline.scheduleType || null,
            },
          }
          state.loading = true
          offlineWorkList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.scheduleText = changeFrequencyChinese(item)
                  item.currentVersionText = item.currentVersion ? 'V' + item.currentVersion : '--'
                  item.timeText = methods.diffTime(item)
                  item.number = index + 1
                })
                state.tableData = res.data
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 时长转化
        diffTime(item) {
          if (item.triggerTime && item.finishedTime) {
            let startDate = new Date(item.triggerTime)
            let endDate = new Date(item.finishedTime)
            let diff = endDate.getTime() - startDate.getTime() //时间差的毫秒数

            //计算出相差天数
            let days = Math.floor(diff / (24 * 3600 * 1000))
            //计算出小时数
            let leave1 = diff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
            let hours = Math.floor(leave1 / (3600 * 1000))
            //计算相差分钟数
            let leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
            let minutes = Math.floor(leave2 / (60 * 1000))

            //计算相差秒数
            let leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
            let seconds = Math.round(leave3 / 1000)

            let returnStr = seconds < 10 ? '0' + seconds : seconds
            if (minutes > 0) {
              returnStr = (minutes < 10 ? '0' + minutes : minutes) + ':' + returnStr
            } else {
              returnStr = '00:' + returnStr
            }
            if (hours > 0) {
              returnStr = (hours < 10 ? '0' + hours : hours) + ':' + returnStr
            } else {
              returnStr = '00:' + returnStr
            }
            if (days > 0) {
              returnStr = days + '天' + returnStr
            }
            return returnStr
          } else {
            return '--'
          }
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        methods.initTable(true)
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;
            background-color: #1e89ff;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    box-sizing: border-box;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 0;
    padding: 0;
    .data-collection-page-content {
      width: 100%;
      height: 100%;
      border-radius: 2px;
      background-color: #fff;
      overflow: hidden;
      .out-box {
        height: 100%;
        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            margin: auto;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
        }
      }
      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;
        .taskName {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .icon-status-svg {
            margin-right: 4px;
            font-size: 18px;
          }
        }
        .statusBox {
          .label {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 5px 0;
            color: #606266;
            font-size: 14px;
            .icon {
              font-size: 16px;
              margin-right: 4px;
              color: #fff;
            }
          }
        }
        .envType {
          &-name {
            width: max-content;
            padding: 0 8px;
            color: #447dfd;
            font-size: 12px;
            line-height: 20px;
            background: #f0f7ff;
            border: 1px solid #bfd9ff;
            border-radius: 10px;
            &.test {
              color: #04c495;
              background: rgba(230, 255, 244, 0.7);
              border: 1px solid #75ebc2;
            }
          }
        }
        .taskStatus {
          .circle {
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 4px;
            background-color: $themeBlue;
            border-radius: 50%;

            &.green {
              background-color: #00ca5f;
            }

            &.gray {
              background-color: #b8b8b8;
            }
          }
        }
        .rateTime-box {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .status-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .status {
            width: 6px;
            height: 6px;
            margin-right: 4px;
            border-radius: 6px;
            &.PUBLISH {
              background-color: #04c495;
            }
            &.CREATED {
              background-color: #447dfd;
            }
            &.OFFLINE {
              background-color: #b8b8b8;
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }
      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }
        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }
  .modal-body {
    overflow: hidden;
    &-header {
      .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        .col {
          flex: 1;
          flex-shrink: 0;
          height: 22px;
          line-height: 22px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          color: #606266;
          font-size: 14px;
          .name {
            width: 100px;
          }
          .value {
            width: calc(100% - 100px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #1d2129;
          }
        }
      }
    }
    &-table {
      width: 100%;
      overflow-x: auto;
      &-scroll {
        min-width: 100%;
      }
    }
    &-flow {
      height: 524px;
      box-sizing: border-box;
    }
  }
</style>
