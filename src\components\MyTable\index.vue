<template>
  <div class="common-table">
    <div class="page-top">
      <slot name="pageTop"></slot>
    </div>
    <div class="page-mid">
      <el-table
        ref="table"
        :data="tableData"
        :highlight-current-row="highlight"
        style="width: 100%"
        :height="tableHeight"
        @selection-change="selectChange"
        @current-change="radioChange"
        @row-click="selectRow"
      >
        <el-table-column v-if="isSelection" type="selection" width="55" />
        <el-table-column v-if="isCustomeSelect" prop="" label="" align="center" width="50px">
          <template #header="scope">
            <slot :row="scope.row" :index="scope.$index" name="checkboxActionHeader"></slot>
          </template>
          <template #default="scope">
            <slot :row="scope.row" :index="scope.$index" name="checkboxAction"></slot>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in attrList"
          :key="index"
          :prop="item.prop"
          :label="item.name"
          :width="item.width"
          align="center"
          :show-overflow-tooltip="item.ellipsis"
          :fixed="item.fixed"
        >
          <template v-if="item.children && item.children.length">
            <el-table-column
              v-for="(col, key) in item.children"
              :key="key"
              :prop="col.prop"
              :label="col.name"
              :width="col.width"
            >
              <template v-if="col.renders" #default="scope">
                <dataContent :data="scope.row" :renders="col.renders" />
              </template>
            </el-table-column>
          </template>
          <template v-if="item.renders" #default="scope">
            <dataContent :data="scope.row" :index="scope.$index" :renders="item.renders" />
          </template>
          <template v-else-if="item.slotName" #default="scope">
            <slot :row="scope.row" :index="scope.$index" :name="item.slotName"></slot>
          </template>
          <template v-if="item.header" #header="scope">
            <dataContent :data="scope.row" :index="scope.$index" :renders="item.header" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="isAction"
          prop=""
          align="center"
          :width="actionWidth"
          label="操作"
          fixed="right"
        >
          <template #default="scope">
            <slot :row="scope.row" :index="scope.$index" name="action"></slot>
          </template>
        </el-table-column>
        <template #empty>
          <slot name="empty">
            <div class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/img/table-no-content.png" alt="暂无内容" />
              <div>{{ emptyText }}</div>
            </div>
          </slot>
        </template>
      </el-table>
      <el-pagination
        v-if="isPage && tableData.length"
        background
        :current-page="page"
        :page-sizes="[12, 24, 48]"
        :page-size="pageNum"
        :layout="layout"
        :total="total"
        :pager-count="4"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MyTable',
    components: {
      dataContent: {
        functional: true,
        props: {
          data: {
            type: Object,
            default: () => {},
          },
          index: {
            type: Number,
            default: () => {},
          },
          renders: {
            type: Function,
          },
        },
        render(ctx) {
          return ctx.renders(ctx.data, ctx.index, ctx)
        },
      },
    },
    props: {
      emptyText: {
        type: String,
        default: '暂无数据',
      },
      exceptHeight: {
        // 视口除去表格高度后的高度
        type: Number,
        default: 314,
      },
      attrList: {
        type: Array,
        default: () => [],
      },
      tableData: {
        type: Array,
        default: () => [],
      },
      isPage: {
        type: Boolean,
        default: true,
      },
      currentPage: {
        type: Number,
        default: 1,
      },
      pageSize: {
        type: Number,
        default: 10,
      },
      total: {
        type: Number,
        default: 0,
      },
      isAction: {
        type: Boolean,
        default: true,
      },
      actionWidth: {
        // 操作栏宽度
        type: Number,
        default: 130,
      },
      // 是否可以多选
      isSelection: {
        type: Boolean,
        default: false,
      },
      // 自定义多选，用于前端多数据翻页保存选择状态
      isCustomeSelect: {
        type: Boolean,
        default: false,
      },
      // 高亮单选行
      highlight: {
        type: Boolean,
        default: false,
      },
      // 页码工具
      layout: {
        type: String,
        default: 'total, prev, pager, next, sizes, jumper',
      },
    },
    data() {
      return {
        page: this.currentPage,
        pageNum: this.pageSize,
        tableHeight: this.exceptHeight,
      }
    },
    watch: {
      currentPage(val) {
        this.page = val
      },
      pageSize(val) {
        this.pageNum = val
      },
    },
    mounted() {
      this.tableHeight = document.body.offsetHeight - this.tableHeight - 14
    },
    methods: {
      selectRow(row) {
        this.$emit('selectRow', row)
      },
      handleSizeChange(pageSize) {
        this.$emit('sizeChange', pageSize)
      },
      handleCurrentChange(currentPage) {
        // 表格current-change和分页current-change的事件一样，防止同时触发
        if (typeof currentPage === 'number') {
          this.$emit('pageChange', currentPage)
        }
      },
      selectChange(data) {
        this.$emit('selectChange', data)
      },
      radioChange(data) {
        this.$emit('radioChange', data)
      },
      setCurrentRow(row) {
        this.$refs.table.setCurrentRow(row)
      },
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .common-table {
    height: 100%;
    :deep(.el-pagination) {
      justify-content: center;
      padding: 20px 0;

      button {
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      ul li {
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }
        .el-input__wrapper {
          height: $paginationChildHeight;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
    .page-mid {
      height: calc(100% - 120px);
      :deep(.el-table__fixed-right::before) {
        height: 0;
      }
      :deep(.table-no-content) {
        img {
          width: 213px;
        }
      }
      :deep(.el-scrollbar__view) {
        height: 100%;
      }
      :deep(.el-pagination.is-background) {
        &.btn-next,
        &.btn-prev,
        &.el-pager li {
          background-color: #fff;
          border: 1px solid #cfcfcf;
        }
      }

      :deep(.el-pager + button.btn-next[type='button']) {
        margin-right: 10px;
      }

      :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
        background-color: $themeBlue;
        color: #fff;
      }

      :deep(.el-table) {
        .el-table__inner-wrapper::before {
          // el-table 设置border边框 底部边线消失
          height: 0;
        }

        &:before {
          height: 0;
        }

        .el-checkbox__inner {
          width: 16px;
          height: 16px;

          &::after {
            left: 5px;
            top: 2px;
          }
        }

        thead {
          font-weight: 400;
          color: #333333;
          font-size: 14px;

          tr {
            height: 42px !important;
          }

          th {
            font-size: 12px;
            background-color: #f7f8fa !important;

            &.el-table__cell.is-leaf {
              border-bottom: none;
              border-right: none;
            }
          }

          th.el-table__cell {
            //表头边框
            // border: 1px solid #dcdcdc;
            // border-left: none;

            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333333;
            }
          }
        }

        tbody {
          td.el-table__cell {
            // border-bottom: 1px solid #dcdcdc;
            border-bottom: 1px solid #ebebeb;
          }

          tr {
            height: 50px !important;
          }

          tr td {
            //body边框
            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              // border-right: 1px solid #dcdcdc;

              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333;

              & > span {
                // 多行文本溢出隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }

              .edit-box {
                .has-right-border {
                  border-radius: initial;
                  height: 10px;
                  line-height: 9px;
                  padding: 0 0 0 10px;
                  border: 0;
                  border-left: 1px solid #cfcfcf !important;
                  color: var(--themeBlue);
                  font-size: 12px;
                  &.is-disabled {
                    color: #c9c9c9;
                  }
                }

                .btn-more {
                  font-size: 12px;
                  color: var(--themeBlue);
                  &:hover {
                    background-color: #fff;
                  }
                }
              }
            }
          }
        }

        //  解决操作栏坍塌的问题 解决固定列距表格的高度（注意：这里我写死了表头为一行文字时的高度）
        .el-table__fixed-body-wrapper {
          top: 42px !important;
        }

        // 解决多出来的横线(固定列右边)问题
        .el-table__fixed-right {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        // 若左右都有固定列时
        .el-table__fixed {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        /**改变table的滚动条样式*/
        // 滚动条的宽度
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 5px; // 横向滚动条
          height: 5px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          background-color: #e1e1e1;
          border-radius: 2px;
        }
      }
    }
  }
</style>
