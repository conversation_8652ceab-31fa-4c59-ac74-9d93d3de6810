<template>
  <div class="node">
    <div class="head" :title="data.label">{{ data.label }}</div>
    <div class="content" :title="data.content">
      {{ data.content }}
    </div>
  </div>
</template>

<script>
  export default {
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {
          label: '',
          content: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      node.on('change:data', ({ current }) => {
        this.data = node.getData()
      })
    },
    methods: {},
  }
</script>

<style scoped lang="scss">
  .node {
    cursor: pointer;
    display: inline-block;
    width: 200px;
    height: 80px;
    text-align: center;
    box-sizing: content-box;
    border: 1px solid #ebedf0;
    border-radius: 4px;
    margin: -1px;
    &:hover {
      border: 1px solid #6e9eff;
      .head {
        background: #ecf7ff;
      }
    }
    .head {
      height: 40px;
      line-height: 40px;
      color: #333333;
      font-size: 14px;
      background: #ebedf0;
      border-radius: 4px 4px 0 0;
      font-weight: 500;
      overflow: hidden;
      word-break: keep-all;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .content {
      height: 40px;
      line-height: 40px;
      color: #333333;
      font-size: 12px;
      background: #fff;
      overflow: hidden;
      border-radius: 0 0 4px 4px;
      word-break: keep-all;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    img {
      height: 14px;
      margin-right: 5px;
    }
    span {
      vertical-align: middle;
    }
  }
</style>
