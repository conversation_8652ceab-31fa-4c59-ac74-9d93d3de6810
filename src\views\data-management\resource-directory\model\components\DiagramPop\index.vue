<template>
  <n-modal draggable :title="title" :model-value="entityShow" :before-close="closepop">
    <section class="content-table">
      <n-tabs v-model="activeName" type="options">
        <n-tab title="单元格样式" id="first">
          <CellStyle
            :borderAttr="borderAttr"
            :diagramData="diagramData"
            :cellAttr="cellAttr"
            @diagramDataFn="diagramDataFn"
            @cellAttrFn="cellAttrFn"
          />
        </n-tab>
        <n-tab title="线样式" id="second">
          <LineStyle :borderAttr="borderAttr" @borderAttrFn="borderAttrFn" />
        </n-tab>
      </n-tabs>
    </section>
    <template #footer v-if="!cellData?.disableMove">
      <n-modal-footer>
        <n-button variant="solid" color="primary" @click="submitFn">确 定</n-button>
        <n-button @click="closePop">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script>
  import CellStyle from './Cell'
  import LineStyle from './Line'
  export default {
    components: {
      CellStyle,
      LineStyle,
    },
    props: {
      cell: {
        type: Object,
        default() {
          return null
        },
      },
    },
    data() {
      return {
        title: '表图编辑', // 弹窗标题
        entityShow: true,
        activeName: 'first',
        diagramData: [],
        borderAttr: {
          // 边框样式属性
          out: {
            color: '#000',
            style: 'solid',
            size: 1,
          },
          inside: {
            color: '#000',
            style: 'solid',
            size: 1,
          },
        },
        cellAttr: {
          // 单元格样式属性
          row: 6, // 表格行数
          col: 6, // 表格列数
          family: 'inherit',
          style: 'normal',
          weight: false,
          size: 12,
          textAlign: 'left',
          color: '#000',
          backColor: '#fff',
          delChecked: false,
          underlineChecked: false,
        },
      }
    },
    computed: {
      cellData() {
        return this.cell?.data
      },
    },
    created() {
      this.borderAttr = this.cell?.data?.borderAttr
      this.cellAttr = this.cell?.data?.cellAttr
      this.diagramData = this.cell?.data?.diagramData
    },
    methods: {
      // 提交数据
      submitFn() {
        let data = {
          diagramData: this.diagramData,
          borderAttr: this.borderAttr,
          cellAttr: this.cellAttr,
        }
        this.cell.setData(data, { overwrite: true })
        this.$emit('closepop')
      },
      // 关闭弹窗
      closepop(done) {
        done()
        this.$emit('closepop')
      },
      // 关闭弹窗
      closePop() {
        this.$emit('closepop')
      },
      // 操作边框属性修改
      borderAttrFn(info) {
        this.borderAttr = JSON.parse(JSON.stringify(info))
      },
      // 操作字体属性修改
      cellAttrFn(info) {
        this.cellAttr = JSON.parse(JSON.stringify(info))
      },
      // 操作行列变化
      diagramDataFn(arr) {
        this.diagramData = arr
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog) {
    width: 720px;
    :deep(.el-dialog__body) {
      padding: 20px 30px;
    }
  }
  h3 {
    color: #999;
    font-size: 14px;
  }
</style>
