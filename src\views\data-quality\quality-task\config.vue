<template>
  <div class="config container">
    <div class="step">
      <step ref="step" :stepsData="stepsData" />
    </div>
    <section class="form-content">
      <div class="form-content-name">
        <moduleName :info="{ name: modelCname }" />
        <div class="form-content-name-desc"
          >{{ modelDescription ? modelDescription : '暂无描述' }}
        </div>
      </div>
      <section class="form-content-box">
        <n-form
          ref="ruleForm"
          :data="ruleForm"
          :rules="rules"
          label-width="100px"
          label-align="end"
          class="ruleForm stepTwo"
        >
          <div v-show="stepIndex === 0" class="form-step-field">
            <div class="form-step-field-box">
              <n-button variant="solid" @click.prevent="checkMoreRuleFn">字段批量配置</n-button>
            </div>
            <n-public-table
              ref="publicTable"
              :class="{
                'red-rule': showRedBorder,
              }"
              :isNeedSelection="true"
              :isDisplayAction="true"
              :table-head-titles="tableHeadTitles"
              :pagination="pagination"
              :tableData="tableData"
              :tableHeight="tableHeight"
              :actionWidth="180"
              @tablePageChange="checkPageChange($event, 'pagination')"
              @handle-selection-change="checkSelectionChange"
              @headerIconClickFn="illustrateFn"
            >
              <template #ruleBox="{ editor }">
                <div class="rule-box">
                  <template v-for="(item, ind) in editor.row.rules">
                    <div
                      v-if="ind < 4"
                      :key="item.id"
                      :class="
                        item.ruleType === 'STANDARD' ? 'rule-box-label checked' : 'rule-box-label'
                      "
                      @click="showRuleFn(editor.row)"
                      ><div class="rule-box-label-name"
                        >{{ item.name }}<text class="weight">{{ item.weight }}</text></div
                      >

                      <n-popover
                        v-if="!isSee && item.ruleType !== 'STANDARD'"
                        class="item"
                        content="删除"
                        trigger="hover"
                        :position="['bottom']"
                      >
                        <SvgIcon
                          class="icon-close"
                          icon="icon-close-gray"
                          @click.prevent.stop="delRuleFn(editor.row, item.id)"
                        />
                      </n-popover>
                    </div>
                  </template>
                  <n-popover
                    v-if="editor.row.rules.length > 4"
                    class="item"
                    content="更多"
                    trigger="hover"
                    :position="['bottom']"
                  >
                    <SvgIcon
                      class="rule-box-more"
                      icon="icon-spot"
                      @click="showRuleFn(editor.row)"
                    />
                  </n-popover>
                </div>
              </template>
              <template #illustrate="{ editor }">
                <div>
                  {{ editor.header }}
                  <SvgIcon
                    class="icon-illustrate"
                    icon="icon-illustrate"
                    @click.prevent.stop="illustrateFn"
                  />
                </div>
              </template>
              <template #editor="{ editor }">
                <div class="edit-box">
                  <n-button
                    class="seeDetails has-right-border"
                    :disabled="isSee"
                    variant="text"
                    @click.stop.prevent="onlyDelFn([{ id: editor.row.id }])"
                    >移除
                  </n-button>
                  <n-button
                    class="seeDetails has-right-border"
                    :disabled="isSee"
                    variant="text"
                    @click.stop.prevent="addRuleFn(editor.row, 'one')"
                    >引用规则
                  </n-button>
                </div>
              </template>
            </n-public-table>
          </div>
          <div v-show="stepIndex === 1" class="form-step-second">
            <n-form-item class="form-item-title">
              <div>
                <span class="small-title">调度配置</span>
                <n-switch v-model="ruleForm.taskType" :disabled="isSee" color="#447DFD" size="sm" />
              </div>
            </n-form-item>
            <n-form-item class="form-item-title">
              <div v-if="ruleForm.taskType" class="form-bg">
                <div class="check-title">任务执行时间：</div>
                <n-date-picker-pro
                  v-model="ruleForm.startDate"
                  placeholder="请选择"
                  :key="key"
                  format="YYYY-MM-DD"
                  :class="{
                    'red-border': showDispatchRedBorder && !ruleForm.startDate,
                    'check-date': true,
                  }"
                  @confirmEvent="startDateChangeFn"
                />
                <n-time-picker
                  v-model="ruleForm.time"
                  :key="key"
                  :class="{
                    'red-border': showDispatchRedBorder && !ruleForm.time,
                    'check-date-time': true,
                  }"
                  placeholder="00:00:00"
                  format="HH:mm:ss"
                />
                <div class="check-explain">到达该配置时间，任务将自动运行</div>
                <div class="check-title top">作业频次:</div>
                <div
                  :class="{
                    'red-border': showDispatchRedBorder && !ruleForm.period,
                    'check-radio': true,
                  }"
                  :style="isSee ? 'background-color:#f5f7fa;' : ''"
                >
                  <div class="check-radio-name">周期</div>
                  <n-radio-group
                    v-model="ruleForm.period"
                    :disabled="isSee"
                    direction="row"
                    @change="radioChangeFn"
                  >
                    <n-radio value="day">日</n-radio>
                    <n-radio value="week">周</n-radio>
                    <n-radio value="month">月</n-radio>
                  </n-radio-group>
                </div>
                <div
                  :class="{
                    'red-border':
                      showDispatchRedBorder &&
                      ((ruleForm.period !== 'day' && !ruleForm.extent) || !ruleForm.rateTime),
                    'check-time-box': true,
                  }"
                >
                  <n-select
                    v-if="ruleForm.period === 'week'"
                    v-model="ruleForm.extent"
                    :disabled="isSee"
                    filter
                    :options="
                      weekList.map((val) => {
                        return { ...val, name: val.label, value: val.value }
                      })
                    "
                    size="small"
                    placeholder="请选择周几"
                  />
                  <n-select
                    v-else-if="ruleForm.period === 'month'"
                    v-model="ruleForm.extent"
                    :disabled="isSee"
                    size="small"
                    placeholder="请选择几号"
                    filter
                    :options="
                      monthList.map((val) => {
                        return { ...val, name: val.label, value: val.value }
                      })
                    "
                  />
                  <n-time-picker
                    v-model="ruleForm.rateTime"
                    :disabled="isSee"
                    :key="key"
                    :class="
                      ruleForm.period === 'day' || !ruleForm.period
                        ? 'check-time lang'
                        : 'check-time'
                    "
                    placeholder="00:00:00"
                    format="HH:mm:ss"
                  />
                </div>
                <div class="check-explain">任务开始后，将按照该频次进行周期性地数据质量检测</div>
              </div>
              <div v-else class="form-bg">
                <img class="form-bg-empty" src="/src/assets/img/dev/empty-task.png" />
                <p class="form-bg-text">暂无配置</p>
              </div>
            </n-form-item>
          </div>
        </n-form>
      </section>
    </section>
    <footer class="footer">
      <n-button v-if="stepIndex === 1" variant="solid" class="footer-btn" @click.prevent="prevFn"
        >上一步</n-button
      >
      <n-button v-if="stepIndex === 0" variant="solid" class="footer-btn" @click.prevent="nextFn"
        >下一步
      </n-button>
      <n-button
        v-if="stepIndex === 1 && !isSee"
        class="footer-btn"
        variant="solid"
        :loading="hasClick"
        @click.prevent="saveFn"
        >保存
      </n-button>
      <n-button v-if="isSee" color="primary" class="footer-btn" @click.prevent="cancelFn"
        >返回</n-button
      >
      <n-button v-else class="footer-btn" @click.prevent="cancelFn">取消</n-button>
    </footer>
    <!--规则评分说明弹窗-->
    <n-modal
      v-model="illustrateDialog"
      width="580px"
      class="commonDialog illustrateDialog"
      title="规则评分说明"
      :close-on-click-overlay="false"
      @close="illustrateDialog = false"
    >
      <div class="illustrate">
        <div class="illustrate-p">
          <span class="illustrate-p-circle"></span>
          字段规则由字段关联的标准值域规则和质量规则组成，根据各自的权重做加权平均计算字段评分，所有规则默认权重为1.公式如下：score=(满足数据标准值域的数据条数/字段总数据条数)*标准值域规则权重/(该字段下的规则权重总和)*100+(满足质量规则1的数据条数/字段总数据条数)*质量规则1权重/(该字段下的规则权重总和)*100+.......+(满足质量规则n的数据条数/字段总数据条数)*质量规则n权重/(该字段下的规则权重总和)*100
        </div>
        <div class="illustrate-p">
          <span class="illustrate-p-circle"></span>
          模型评分规则由标准字段占比和字段评分均值规则组成，根据各自的权重做加权平均计算模型评分，所有规则默认权重为1.公式如下：score=（该模型拥有或关联标准字段个数/模型字段总数)*(标准字段占比规则权重/模型评分权重总和)+(字段1评分+.....+字段n评分)/n*(字段评分均值规则权重/模型评分权重总和)
        </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer"
          ><n-button variant="solid" @click.prevent="illustrateDialog = false">关闭</n-button></div
        >
      </template>
    </n-modal>
    <!--引用规则弹窗-->
    <n-modal
      v-model="addRuleDialog"
      width="1020px"
      class="largeDialog"
      title="引用规则"
      :close-on-click-overlay="false"
      @close="addRuleDialog = false"
    >
      <n-form
        ref="addForm"
        :data="addForm"
        label-width="80px"
        label-position="left"
        class="demo-ruleForm"
      >
        <n-form-item v-if="addForm.cnName" label="字段名称：" prop="cnName">
          <div class="disabled">{{ addForm.cnName }} </div>
        </n-form-item>
        <div class="inlineBlock">
          <dataTransfer ref="dataTransferFn" :ruleList="ruleList" :ruleIds="ruleIds" />
        </div>
      </n-form>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="saveForm">保 存</n-button>
          <n-button @click.prevent="addRuleDialog = false">取 消</n-button>
        </div>
      </template>
    </n-modal>
    <!--校验规则弹窗-->
    <n-modal
      v-model="showRule"
      class="rule-dialog commonDialog"
      :close-on-click-overlay="false"
      width="580px"
      @close="showRule = false"
      title="校验规则"
    >
      <div class="rule-content scroll-bar-style">
        <span
          v-for="(item, index) in showRuleList"
          :key="index"
          :class="
            item.ruleType === 'STANDARD' ? 'rule-content-label checked' : 'rule-content-label'
          "
          >{{ item.name }}<text class="weight">{{ item.weight }}</text></span
        >
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="showRule = false">关闭</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import step from '@/components/customizeSteps'
  import moduleName from '@/components/ModuleName'
  import dataTransfer from '@/components/DataTransfer'
  import { formartTimeDate } from '@/utils/index'

  export default {
    name: 'DataQualityTaskConfig',
    components: { step, moduleName, dataTransfer },
    data() {
      return {
        id: '',
        key: 1,
        hasClick: false, // 防止多次点击
        illustrateDialog: false, // 评分规则
        stepsData: [{ label: '配置检验规则' }, { label: '配置调度策略' }],
        stepIndex: 0,
        ruleForm: {
          taskType: true,
          name: '',
          description: '',
          extent: '',
          startDate: '',
          time: '',
          rateTime: '',
          scheduleId: '',
          period: 'day',
        },
        rules: {
          period: [{ required: true, message: '请选择周期', trigger: 'blur' }],
          startDate: [{ required: true, message: '请选择任务执行时间', trigger: 'blur' }],
          rateTime: [{ required: true, message: '请选择周期检测时间', trigger: 'blur' }],
        },
        weekList: [
          { label: '周一', value: 1 },
          { label: '周二', value: 2 },
          { label: '周三', value: 3 },
          { label: '周四', value: 4 },
          { label: '周五', value: 5 },
          { label: '周六', value: 6 },
          { label: '周日', value: 7 },
        ],
        monthList: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
          {
            prop: 'rule',
            name: '校验规则',
            width: '500px',
            slot: 'ruleBox',
            headerSlot: 'illustrate',
          },
        ],
        exceptHeight: 0,
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        tableData: { list: [] },
        tableHeight: 445,
        checkedRule: null,
        loading: false,
        // 模型配置弹窗
        modelDialog: false,
        dataStandardWeight: 1,
        qualityRuleWeight: 1,
        // 引用规则弹窗
        addRuleDialog: false,
        addForm: {
          id: '',
          cnName: '',
        },
        addRules: {},
        ruleIds: null,
        ruleList: [],
        checkList: [], // 选中的数据
        delList: [], // 批量移除选中列表
        // 校检规则弹窗
        showRule: false,
        showRuleList: [],
        timeoutFlag: null,
        isSee: false,
        isFirst: true,
        projectCode: '',
        modelName: '',
        modelCname: '',
        modelDescription: '',
        showRedBorder: false,
        showDispatchRedBorder: false,
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    mounted() {
      this.tableHeight = document.body.offsetHeight - 424
      this.modelId = this.$route.query.modelId
      this.getRulesListFn('')
      if (this.$route.query.isSee) {
        this.isSee = true
      }
      this.getModelDetailFn(this.$route.query.modelId)
      // 设置月
      let monthList = []
      for (let i = 1; i <= 31; i++) {
        monthList.push({
          label: i + '号',
          value: i,
        })
      }
      this.monthList = monthList
      // 获取调度配置
      if (this.modelId) {
        this.id = this.$route.query.modelId
        this.$api.dataQuality.getTaskDetail({ id: this.id }).then((res) => {
          if (res.code === 'SUCCESS') {
            this.projectCode = res.data.projectCode
            let startDate = '',
              time = ''
            if (res.data.schedule) {
              if (res.data.schedule.startTime) {
                startDate = res.data.schedule.startTime.slice(0, 10)
                time = res.data.schedule.startTime.slice(11, 20)
              }
              if (res.data.schedule.extent) {
                res.data.schedule.extent = Number(res.data.schedule.extent)
              }
            }
            this.ruleForm = {
              taskType: true,
              name: '',
              description: '',
              startDate: startDate,
              time: time,
              extent: res.data.schedule ? res.data.schedule.extent : '',
              rateTime: res.data.schedule ? res.data.schedule.rateTime : '',
              period: res.data.schedule ? res.data.schedule.period : '',
              scheduleId: res.data.schedule ? res.data.schedule.id : '',
            }
            this.key++
            this.$nextTick(() => {
              let checkedRule = []
              res.data.mapping.forEach((val) => {
                checkedRule.push({
                  id: val.columnId,
                  ruleData: val.rules || [],
                })
              })
              this.checkedRule = checkedRule
              this.onSearch(res.data.mapping)
            })
          }
        })
      } else {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    },
    beforeUnmount() {},
    methods: {
      disabledDate(time) {
        return time.getTime() < Date.now() - 8.64e7
      },
      // 通过id获取模型详情
      getModelDetailFn(modeId) {
        this.$api.model.getModalDetail({ id: modeId }).then((res) => {
          if (res.code === 'SUCCESS') {
            this.modelName = res.data.name
            this.modelCname = res.data.cnName
            this.modelDescription = res.data.description
          }
        })
      },
      // 规则搜索输入
      ruleMethod(name) {
        if (this.timeoutFlag) {
          clearTimeout(this.timeoutFlag)
          this.timeoutFlag = null
        }
        this.timeoutFlag = setTimeout(() => {
          this.getRulesListFn(name)
        }, 1000)
      },
      // 下拉框聚焦事件
      focusFn() {
        this.getRulesListFn('')
      },
      // 获取规则列表
      getRulesListFn(name) {
        this.loading = true
        let params = {
          name: name,
        }
        this.$api.dataQuality
          .getCommonRuleList(params)
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.loading = false
              this.ruleList = res.data
            }
          })
          .catch(() => {
            this.loading = false
          })
      },
      // 选中数据
      checkSelectionChange(e) {
        this.checkList = e
      },
      // 批量配置规则
      checkMoreRuleFn() {
        if (this.checkList.length > 0) {
          this.addRuleFn(this.checkList, 'more')
        } else {
          this.$notify({
            title: '提示',
            message: '请先勾选数据',
            type: 'warning',
          })
        }
      },
      // 点击说明
      illustrateFn() {
        this.illustrateDialog = true
      },
      // 设置当前步骤
      setStepFn(index) {
        this.$refs.step.updatedActive({ index: index })
      },
      // 周期变化事件
      radioChangeFn() {
        this.ruleForm.extent = ''
      },
      // 点击取消
      cancelFn() {
        this.$router.go(-1)
      },
      // 表格页码
      checkPageChange(item, pagination) {
        this[pagination].currentPage = item.currentPage
        this[pagination].pageSize = item.pageSize
        let list = this.tableData.list.slice(
          (item.currentPage - 1) * item.pageSize,
          item.currentPage * item.pageSize,
        )
        if (list.length === 0 && item.currentPage > 1) {
          list = this.tableData.list.slice(
            (item.currentPage - 2) * item.pageSize,
            (item.currentPage - 1) * item.pageSize,
          )
        }
        // 重新绑定序号
        list.map((item, index) => {
          return Object.assign(item, { number: index + 1 })
        })
        this.tableData = {
          pageNum: item.currentPage,
          pageSize: item.pageSize,
          list: list,
          total: list.length,
        }
      },
      // 上一步
      prevFn() {
        this.stepIndex = 0
      },
      // 下一步
      nextFn() {
        let flag = false
        this.tableData.list.forEach((val) => {
          if (val.rules.length > 0) {
            flag = true
          }
        })
        if (flag || this.isSee) {
          this.stepIndex = 1
          this.setStepFn(this.stepIndex)
          this.showRedBorder = false
        } else {
          this.$notify({
            title: '提示',
            message: '请至少选择一条质量规则',
            type: 'warning',
          })
          this.showRedBorder = true
        }
      },
      // 保存规则
      saveRuleFn(sessionRule) {
        this.tableData.list.forEach((val) => {
          sessionRule.forEach((rule) => {
            if (val.id === rule.id) {
              let standardList = val.rules.filter((v) => v.ruleType === 'STANDARD')
              let newArr = standardList.concat(rule.ruleData)
              //替换对应行ruleIds
              val.rules = [...new Set(newArr)]
            }
          })
        })
        this.checkList = []
        if (this.$refs.publicTable) {
          this.$refs.publicTable.clearSelection()
        }
      },
      // 保存引入规则
      saveForm() {
        const ruleDataList = this.$refs.dataTransferFn.getCheckedRuleData()
        if (ruleDataList) {
          let ids = this.addForm.id.toString().split(',')
          ids.forEach((id) => {
            this.loopInsertRuleFn(id, ruleDataList)
          })
          this.saveRuleFn(this.checkedRule)
          this.ruleIds = null
          this.addRuleDialog = false
        }
      },
      // 循环插入规则
      loopInsertRuleFn(id, rules) {
        const ruleData = {
          ruleData: rules,
          id: Number(id),
        }
        if (this.checkedRule) {
          const isHave = this.checkedRule.some((item, index) => {
            if (item.id === ruleData.id) {
              this.checkedRule[index] = ruleData
            }
            return item.id === ruleData.id
          })
          if (!isHave) {
            this.checkedRule.push(ruleData)
          }
        } else {
          this.checkedRule = [{ ...ruleData }]
        }
      },
      // 点击引入规则
      addRuleFn(data, type) {
        this.addRuleDialog = true
        if (type === 'more') {
          let ids = data.map((val) => val.id).toString()
          this.addForm = {
            id: ids,
            cnName: '',
          }
          this.ruleIds = '[]'
        } else {
          this.addForm = {
            id: data.id,
            cnName: data.cnName,
          }
          this.ruleIds = JSON.stringify(data.rules)
        }
      },
      // 查看已经引入的规则
      showRuleFn(item) {
        this.showRuleList = item.rules
        this.showRule = true
      },
      // 移除规则
      delRuleFn(item, ruleId) {
        item.rules = item.rules.filter((val) => {
          return val.id !== ruleId
        })
        this.checkedRule.map((rlueItem) => {
          if (item.id === rlueItem.id) {
            rlueItem.ruleData = item.rules
          }
        })
      },
      //单个移除
      onlyDelFn(list) {
        if (this.tableData.list.length > 1) {
          this.$dialogPopup({
            title: '是否确认删除字段？',
            message: '删除后，字段将不会被校验',
            save: () => {
              this.delFn(list)
            },
          })
        } else {
          this.$notify({
            title: '提示',
            message: '至少保留一个字段',
            type: 'warning',
          })
        }
      },
      // 移除
      delFn(delArr) {
        let flag = null
        let newArr = []
        this.tableData.list.forEach((val) => {
          flag = true
          delArr.forEach((v) => {
            if (val.id === v.id) {
              flag = false
            }
          })
          if (flag) {
            newArr.push(val)
          }
        })
        this.tableData.list = newArr
        this.delList = []
        this.checkPageChange(
          { currentPage: this.pagination.currentPage, pageSize: this.pagination.pageSize },
          'pagination',
        )
      },
      onSearch(mapping = []) {
        let params = {
          id: this.modelId,
        }
        this.$api.model.getModeData(params).then((res) => {
          if (res.code === 'SUCCESS') {
            let list = [...res.data]
            list.forEach((item) => {
              mapping.forEach((val) => {
                if (val.columnId === item.id) {
                  item.rules = val.rules
                }
              })
            })
            // 动态添加编号
            list.map((item, index) => {
              return Object.assign(item, { number: index + 1, rules: item.rules || [] })
            })
            let data = {
              pageNum: this.pagination.currentPage,
              pageSize: this.pagination.pageSize,
              list: list,
              total: list.length,
            }
            this.tableData = data
            if (this.checkedRule) {
              this.saveRuleFn(this.checkedRule)
            }
          }
        })
      },
      // 日期格式转化
      startDateChangeFn(e) {
        this.ruleForm.startDate = formartTimeDate(e)
      },
      // 保存提交
      saveFn() {
        if (this.hasClick) {
          return
        } else {
          this.hasClick = true
        }
        this.showDispatchRedBorder = false
        let params = {
          mapping: [],
          modelId: this.modelId,
          modelName: this.modelName,
          projectCode: this.projectCode,
          dataStandardWeight: this.dataStandardWeight > 0 ? this.dataStandardWeight : 0,
          qualityRuleWeight: this.qualityRuleWeight > 0 ? this.qualityRuleWeight : 0,
        }
        if (this.ruleForm.taskType) {
          if (!this.ruleForm.startDate) {
            this.showDispatchRedBorder = true
          }
          let time = this.ruleForm.time
          if (!time || !this.ruleForm.rateTime) {
            this.showDispatchRedBorder = true
          }
          params.scheduleStatus = 'ENABLE'
          params.modelId = this.modelId
          params.schedule = {
            period: this.ruleForm.period,
          }
          params.schedule.startTime = this.ruleForm.startDate + ' ' + time
          params.schedule.rateTime = this.ruleForm.rateTime
          if (this.ruleForm.extent) {
            params.schedule.extent = this.ruleForm.extent
          }
          if (this.ruleForm.period !== 'day' && !this.ruleForm.extent) {
            this.showDispatchRedBorder = true
          }
          params.schedule.startTime = params.schedule.startTime.replace(/\//g, '-')
          if (this.ruleForm.scheduleId) {
            params.schedule.id = this.ruleForm.scheduleId
          }
        } else {
          params.scheduleStatus = 'DISABLE'
        }
        if (this.showDispatchRedBorder) {
          this.$notify({
            title: '提示',
            message: '请配置调度信息',
            type: 'warning',
          })
          this.hasClick = false
          return false
        }
        this.tableData.list.forEach((val) => {
          if (val.rules.length > 0) {
            val.rules.forEach((v) => {
              v.ruleType = v.ruleType || 'RULE'
            })
            params.mapping.push({
              columnCnName: val.cnName,
              columnId: val.id,
              columnName: val.name,
              columnType: val.fieldType,
              modelId: this.modelId,
              rules: val.rules,
            })
          }
        })
        this.$api.dataQuality
          .saveTask(params)
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.$notify({
                title: '提示',
                message: '保存成功',
                type: 'success',
              })
              this.$router.push({ name: 'DataQualityTaskList' })
            } else {
              this.hasClick = false
            }
          })
          .catch(() => {
            this.hasClick = false
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .config {
    position: relative;
    // padding: 0 10px 76px 10px;
    .step {
      padding: 0 20px;
      background-color: #fff;
      border-radius: 4px 4px 0 0;
    }
    .form-content {
      // margin-top: 10px;
      height: calc(100vh - 199px);
      overflow: hidden;
      // 滚动条的宽度
      &::-webkit-scrollbar {
        width: 4px; // 横向滚动条
        height: 4px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 2px;
      }

      &-name {
        padding: 14px 20px;
        background-color: #fff;

        &-project {
          display: inline-block;
          padding: 2px 14px;
          background-color: #f4f4f4;
          border-radius: 2px;
          color: #666666;
          font-size: 14px;
          margin-top: 6px;
        }

        &-desc {
          margin-top: 10px;
          color: #333333;
          font-size: 12px;
          padding-left: 10px;
          padding-top: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &-box {
        background-color: #fff;
        border-radius: 0 0 4px 4px;
        height: calc(100% - 50px);
        box-sizing: border-box;
        position: relative;

        .ruleForm {
          margin: 0 auto;
          position: relative;
          z-index: 2;

          .form-item-title {
            margin-bottom: 12px;

            .small-title {
              color: #333333;
              font-size: 14px;
              margin-right: 12px;
              font-weight: bold;
            }
            .nancalui-switch {
              vertical-align: -2px;
            }
            .check-radio {
              display: inline-block;
              vertical-align: middle;
              line-height: 36px;
              height: 32px;
              padding-right: 78px;
              border: 1px solid #dcdfe6;
              box-sizing: border-box;
              margin-right: 10px;
              border-radius: 2px;
              background-color: #fff;
              &.red-border {
                border: 1px solid #f66f6a;
              }
              &-name {
                display: inline-block;
                color: #666666;
                height: 18px;
                line-height: 18px;
                font-size: 12px;
                vertical-align: top;
                padding: 0 10px;
                margin: 7px 70px 7px 0;
                border-right: 1px solid #ebedf0;
              }
              :deep(.nancalui-radio) {
                .nancalui-radio__label {
                  color: #606266;
                }
                &.active {
                  .nancalui-radio__label {
                    color: $themeBlue;
                  }
                }
              }
            }

            :deep(.check-time) {
              width: 220px;
              box-sizing: border-box;
            }

            .check-time-box {
              display: inline-block;
              vertical-align: middle;
              width: 242px;
              height: 32px;
              line-height: 32px;
              box-sizing: border-box;
              overflow: hidden;
              border-radius: 2px;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &.red-border {
                border: 1px solid #f66f6a;
              }

              :deep(.nancalui-select) {
                display: inline-block;
                width: 120px;
                position: relative;
                vertical-align: 0;

                .nancalui-select__selection {
                  border: none;

                  &:before {
                    content: '';
                    width: 1px;
                    height: 22px;
                    position: absolute;
                    right: 0;
                    top: 5px;
                    background-color: #dcdfe6;
                  }
                }
              }

              :deep(.check-time) {
                display: inline-block;
                width: 118px;
                vertical-align: 0;

                .nancalui-input__wrapper {
                  border: none;
                  height: 32px;
                }

                &.lang {
                  width: 100%;
                }
              }
            }

            .check-period {
              width: 315px;
              margin-right: 5px;
            }
          }

          .form-step-field {
            padding: 20px;

            &-box {
              margin-bottom: 20px;
            }
          }

          .form-step-second {
            padding-top: 40px;
            width: 638px;
            margin: 0 auto;
          }

          .rule-box {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 10px 0;

            &-label {
              width: 100px;
              padding: 0 10px;
              line-height: 28px;
              background-color: #f2f3f6;
              border-radius: 14px;
              margin-right: 10px;
              cursor: pointer;
              position: relative;
              &.checked {
                background-color: #f0f7ff;
              }
              &-name {
                width: 100%;
                color: #333333;
                text-align: center;
                font-size: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding-right: 20px;
                position: relative;
                .weight {
                  position: absolute;
                  top: 0;
                  bottom: 0;
                  right: 0;
                  margin: auto;
                  padding-left: 6px;
                  text-align: center;
                  height: 14px;
                  line-height: 14px;
                  border-left: 1px solid #c8c9cc;
                }
              }
              .icon-close {
                display: none;
                position: absolute;
                width: 18px;
                height: 18px;
                top: -6px;
                right: -6px;
              }
              &:hover {
                .icon-close {
                  display: block;
                }
              }
            }

            &-more {
              color: #333333;
              cursor: pointer;

              &:hover {
                color: $themeBlue;
              }
            }
          }

          .icon-illustrate {
            font-size: 16px;
            margin-left: 4px;
            cursor: pointer;
          }

          .form-bg {
            background-color: #f7f8fa;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            box-sizing: border-box;

            &.white {
              background-color: #fff;
              padding-top: 10px;

              .check-title:before {
                display: none;
              }
            }

            .check-title {
              position: relative;
              line-height: 22px;
              margin-bottom: 6px;
              margin-left: 10px;
            }

            .check-title:before {
              content: '*';
              top: 3px;
              left: -8px;
              color: #f54446;
              font-size: 12px;
              position: absolute;
            }

            .check-title.top {
              margin-top: 16px;
            }

            .input-number-box {
              width: 348px;
              position: relative;

              &-label {
                position: absolute;
                width: 32px;
                text-align: center;
                height: 30px;
                line-height: 30px;
                right: 1px;
                top: 0;
                bottom: 0;
                margin: auto;
                color: #646566;
                font-size: 12px;
                background-color: #f7f8fa;
                border-radius: 0 1px 1px 0;
                border-left: 1px solid #dcdee0;
              }
            }

            :deep(.check-date) {
              width: 338px;
              &.red-border {
                border: 1px solid #f66f6a;
              }
            }

            :deep(.check-date-time) {
              display: inline-block;
              width: 242px;
              margin-left: 10px;
              vertical-align: top;
              &.red-border {
                border: 1px solid #f66f6a;
              }
            }

            :deep(.check-explain) {
              color: #999999;
              font-size: 12px;
              line-height: 20px;
              margin-top: 6px;
            }

            :deep(.check-time) {
              width: 180px;
              box-sizing: border-box;
            }

            .check-period {
              width: 315px;
              margin-right: 5px;
            }

            &-empty {
              display: block;
              margin: 10px auto;
              width: 209px;
              height: 120px;
            }

            &-text {
              color: #999999;
              font-size: 12px;
              text-align: center;
            }
          }
        }

        &-bg {
          position: absolute;
          z-index: 1;
          width: 252px;
          height: 232px;
          right: 0;
          bottom: 0;
        }
      }
    }
    .inlineBlock {
      width: calc(100% - 80px);
      margin-left: 80px;
    }

    // 查看下样式

    .footer {
      margin-top: 10px;
      margin-left: -10px;
      margin-right: -10px;
      height: 60px;
      overflow: hidden;
      padding: 16px 30px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      &-btn {
        margin: 0 10px;
      }
    }
  }

  .addForm {
    margin-top: 16px;
    .ruleSelect {
      width: 100%;
    }
  }

  .rule-dialog {
    .nancalui-modal__body {
      .rule-content {
        max-height: 184px;
        overflow-y: auto;

        &-label {
          display: inline-block;
          /*width: 76px;*/
          padding: 0 14px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 10px;
          margin-bottom: 14px;
          background-color: #f2f3f6;
          border-radius: 12px;
          color: #333333;
          font-size: 12px;
          &.checked {
            background-color: #f0f7ff;
          }
          .weight {
            margin-left: 6px;
            padding-left: 6px;
            text-align: center;
            height: 14px;
            line-height: 14px;
            border-left: 1px solid #c8c9cc;
          }
        }
      }
    }
  }
  .illustrateDialog {
    .el-dialog__body {
      .illustrate {
        &-p {
          margin-bottom: 20px;
          color: #333333;
          font-size: 12px;
          line-height: 20px;
          padding-left: 12px;
          position: relative;
          &-circle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #e1e1e1;
            margin-right: 6px;
            left: 0;
            top: 6px;
          }
        }
      }
    }
  }
  .disabled {
    width: 100%;
    line-height: 32px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    box-sizing: border-box;
    padding: 0 10px;
    color: #333;
    font-size: 12px;
    border-radius: 4px;
    cursor: not-allowed;
  }
  :deep(.modelForm) {
    .el-form-item__label {
      justify-content: flex-end;
    }
    .el-input-number {
      .el-input__inner {
        text-align: left;
      }
    }
    .text {
      color: #666666;
      font-size: 12px;
    }
  }
  .red-rule {
    :deep(.page-mid) {
      .nancalui-table {
        tr {
          th:nth-of-type(6) {
            span {
              position: relative;
              &:before {
                content: '';
                width: 100%;
                height: 2px;
                background-color: #f66f6a;
                position: absolute;
                left: 0;
                bottom: -4px;
              }
            }
          }
        }
      }
    }
  }
</style>
