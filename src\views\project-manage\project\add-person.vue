<template>
  <div v-loading="state.loading" class="project-add container-padding16">
    <div class="add-box">
      <div class="box-content">
        <!-- 授权人员 -->
        <authorizedPersonnel ref="authorizedPersonnel" :isFull="true" />
      </div>
      <div class="box-operate">
        <n-button size="sm" @click.prevent="cancel">取消</n-button>
        <n-button
          :loading="state.loading"
          class="save"
          size="sm"
          variant="solid"
          color="primary"
          @click.prevent="save"
          >确定</n-button
        >
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, watch, getCurrentInstance } from 'vue'
  import authorizedPersonnel from './components/authorized-personnel'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  export default {
    name: '',
    components: { authorizedPersonnel },
    props: {},

    setup() {
      const router = useRouter()

      const authorizedPersonnel = ref()
      const store = useStore()

      const state = reactive({
        loading: false,

        editId: '',
        editData: {},
      })
      const methods = {
        // 保存
        save() {
          let { name, description, id, datasourceList } = state.editData
          // let dataSourceList = datasourceList.map((item) => {
          //   return item.id
          // })
          const result = authorizedPersonnel.value.getAllData()
          let personList = result?.map((item) => {
            return item.id
          })
          let data = {
            id,
            name,
            description,
            // dataSourceList,
            personList,
          }
          state.loading = true
          api.project.projectUpdate(data).then((res) => {
            let { success } = res
            state.loading = false
            if (success) {
              ElNotification({
                title: '提示',
                message: '人员管理编辑成功',
                type: 'success',
              })
              store.commit('user/ADD_PROJECT', true) // 更新场景列表
              router.push({ name: 'projectList' })
            }
          })
        },
        // 取消
        cancel() {
          router.back()
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.id // 获取路由传参
        // 查看
        state.loading = true
        api.project
          .getProjectDetail({ id: state.editId })
          .then((res) => {
            let { success, data } = res
            if (success) {
              state.editData = data
              setTimeout(() => {
                authorizedPersonnel.value.editInit(state.editData)
                state.loading = false
              }, 1000)
            } else {
              state.loading = false
            }
          })
          .catch(() => {
            state.loading = false
          })
      })
      return {
        state,
        ...methods,
        authorizedPersonnel,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $content: #fff;
  $activeFont: #fff;
  .project-add {
    .add-box {
      height: 100%;
      // padding: 0 16px;
      border-radius: 4px;
      .out-box-steps {
        padding: 0 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;
      }
    }

    .box-content {
      position: relative;
      height: calc(100% - 74px);
      overflow: hidden;
      border-radius: 4px;
      // min-width: 1415px;
      .list {
        height: 100%;
        background: $activeFont;
      }
      .content-bg-img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 17.5%;
        min-width: 252px;
      }
    }
    .box-operate {
      height: 64px;
      margin-top: 10px;

      padding: 16px;
      text-align: right;
      background-color: $content;
      border-radius: 0px 0px 2px 2px;

      .nancalui-button.next,
      .nancalui-button.save {
        color: $activeFont;
        background-color: $themeBlue;
      }
    }
  }
</style>
