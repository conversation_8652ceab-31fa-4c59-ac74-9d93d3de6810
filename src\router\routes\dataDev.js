import Layout from '@/layout'

// 功能模块-数据开发
export default [
  {
    path: '/dataDev',
    component: Layout,
    redirect: '/dataDev/dataScript',
    name: 'dataDev',
    code: 'dataDev',
    meta: {
      title: '数据计算',
      icon: 'icon-dev',
      parentRouterName: 'governanceManage',
      code: 'dataDev',
    },
    children: [
      // 算法中心
      {
        path: 'algorithmCenter',
        name: 'algorithmCenter',
        code: 'algorithmCenter',
        redirect: '/dataDev/algorithmCenter/index',
        component: () => import('@/views/data-development/algorithm-center/index'),
        meta: { title: '算法中心', parentRouterName: 'governanceManage', code: 'algorithmCenter' },
        children: [
          {
            path: 'index',
            name: 'algorithmCenterIndex',
            hidden: true,
            component: () => import('@/views/data-development/algorithm-center/list'),
            meta: {
              title: '算法中心',
              activeMenu: '/dataDev/algorithmCenter',
              parentRouterName: 'governanceManage',
              code: 'algorithm<PERSON>enter',
            },
          },
          {
            path: 'add',
            name: 'algorithmCenterAdd',
            hidden: true,
            component: () => import('@/views/data-development/algorithm-center/add'),
            meta: {
              title: '新增算法中心',
              activeMenu: '/dataDev/algorithmCenter',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'edit',
            name: 'algorithmCenterEdit',
            hidden: true,
            component: () => import('@/views/data-development/algorithm-center/add'),
            meta: {
              title: '编辑算法中心',
              activeMenu: '/dataDev/algorithmCenter',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'algorithmCenterDetail',
            name: 'algorithmCenterDetail',
            hidden: true,
            component: () => import('@/views/data-development/algorithm-center/detail'),
            meta: {
              title: '查看算法中心',
              activeMenu: '/dataDev/algorithmCenter',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'userDefiend',
            name: 'algorithmUserDefiend',
            hidden: true,
            component: () => import('@/views/data-development/algorithm-center/user-defined'),
            meta: {
              title: '在线编辑',
              activeMenu: '/dataDev/algorithmCenter',
              parentRouterName: 'governanceManage',
            },
          },
        ],
      },
    ],
  },
]
