<template>
  <div class="detail">
    <div class="page-title"
      >{{ state.detailInfo.tableName }}
      <div class="detail-back-box" @click.prevent="closeFn">返回</div>
    </div>
    <div class="content">
      <div class="content-radio">
        <n-radio-group direction="row" v-model="state.contentType" size="sm" @change="radioFn">
          <n-radio-button value="STRUCTURE">表结构</n-radio-button>
          <n-radio-button value="MAP">E-R图</n-radio-button>
        </n-radio-group>
      </div>
      <div class="content-box">
        <div class="content-box-structure">
          <div class="label">
            <div class="name">数据源名称：</div>
            <div class="value">{{ state.detailInfo.sourceName }}</div>
          </div>
          <div class="label">
            <div class="name">表名称：</div>
            <div class="value">{{ state.detailInfo.tableName }}</div>
          </div>
          <div class="label">
            <div class="name">表描述：</div>
            <div class="value">{{ state.detailInfo.tableComment || '--' }}</div>
          </div>
        </div>
        <n-public-table
          :isDisplayAction="false"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :showPagination="false"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          v-if="state.contentType === 'STRUCTURE'"
        />
        <erMap v-else :tab="state.detailInfo.tableName" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { collectTableDetail } from '@/api/dataManage'
  import { ElNotification } from 'element-plus'
  import erMap from './model/index'
  const router = useRouter()
  const state = reactive({
    contentType: 'STRUCTURE',
    tableHeadTitles: [
      { prop: 'colName', name: '字段名称' },
      { prop: 'dataType', name: '字段类型' },
      { prop: 'comment', name: '描述' },
    ],
    id: null,
    tableHeight: 168,
    detailInfo: { sourceName: '', tableName: '', tableComment: '' },
    tableData: {
      list: [],
    },
  })

  const radioFn = (e) => {}

  const closeFn = () => {
    router.push({
      name: 'resourceDirectoryCollectIndex',
      query: {},
    })
  }

  const getDetailFn = () => {
    collectTableDetail({ id: state.id }).then((res) => {
      if (res.success) {
        state.detailInfo = res.data
        state.tableData.list = res.data.tableColumnMeta
      }
    })
  }

  onMounted(() => {
    state.id = router.currentRoute.value.query.id || null
    state.tableHeight = document.body.offsetHeight - 290
    getDetailFn()
  })
</script>

<style lang="scss" scoped>
  .detail {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    padding: 16px;
    .page-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 52px;
      margin-bottom: 16px;
      padding: 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;
      background-color: #fff;
      border-radius: 2px;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }

      .detail-back-box {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        z-index: 9;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 30px;
        margin: auto;
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          color: #479dff;
          border: 1px solid #479dff;
        }
      }
    }
    .content {
      height: calc(100% - 68px);
      background-color: #fff;
      border-radius: 2px;
      &-radio {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 46px;
        padding: 0 8px;
      }
      &-box {
        height: calc(100% - 46px);
        &-structure {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          padding: 0 24px;
          .label {
            display: flex;
            flex: 1;
            flex-shrink: 0;
            align-items: center;
            justify-content: flex-start;
            .name {
              color: #606266;
              font-size: 14px;
            }
            .value {
              width: calc(100% - 86px);
              overflow: hidden;
              color: #1a1a1a;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
</style>
