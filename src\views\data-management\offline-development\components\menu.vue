<template>
  <Teleport to="body" defer>
    <div
      class="wrapper"
      :style="{
        left: props.data?.position?.x + 'px',
        top: props.data?.position?.y + 'px',
      }"
    >
      <el-dropdown ref="dropdown">
        <i> </i>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="emit('changeClose', 'all')">关闭全部标签</el-dropdown-item>
            <el-dropdown-item>关闭其他标签</el-dropdown-item>
            <el-dropdown-item>关闭右侧标签</el-dropdown-item>
            <el-dropdown-item>关闭当前标签</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </Teleport>
</template>

<script setup>
  const emit = defineEmits(['changeClose'])
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({ position: { left: 0, top: 0 } }),
    },
  })
  const dropdown = ref(null)
  let a = false
  setInterval(() => {
    a && dropdown.value?.handleOpen()
    !a && dropdown.value?.handleClose()
    a = !a
  }, 2000)
</script>
<style lang="scss" scoped>
  .wrapper {
    position: absolute;
    z-index: 9999;
  }
</style>
