<template>
  <section class="container">
    <section class="container-box">
      <div class="form">
        <div class="page_header_common_style"
          ><span class="need_smallcube__title">{{ title }}</span>
        </div>
        <n-form ref="form" :data="form" :rules="rules" label-align="end" label-width="80px">
          <n-form-item label="作业名称：" field="name">
            <n-input v-model="form.name" maxlength="30" clearable placeholder="请输入作业名称" />
          </n-form-item>
          <n-form-item label="描述信息：">
            <n-textarea
              v-model="form.description"
              :rows="3"
              show-count
              maxlength="200"
              placeholder="请输入"
            />
          </n-form-item>
        </n-form>
      </div>
    </section>
    <div class="container-footer">
      <div class="my-appliction">
        <n-button variant="solid" :loading="loading" @click.prevent="addDataWork">保存 </n-button>
        <n-button @click.prevent="goBack">取消</n-button>
      </div>
    </div>
  </section>
</template>

<script>
  import { checkCName } from '@/utils/validate'

  export default {
    name: 'DataScriptDetail',
    data() {
      return {
        title: '新增算法中心',
        loading: false,
        form: {
          name: '',
          description: '',
        },
        rules: {
          name: [{ required: true, validator: checkCName, trigger: 'blur' }],
        },
      }
    },
    mounted() {
      if (this.$route.query.id) {
        this.title = '编辑算法中心'
        this.form = {
          name: this.$route.query.name,
          description: this.$route.query.description,
        }
      }
    },
    methods: {
      // 添加/编辑
      addDataWork() {
        let params = {
          description: this.form.description,
          name: this.form.name,
          id: this.$route.query.id || null,
        }
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            this.$api.dataDev
              .addAlgorithm(params)
              .then((res) => {
                this.loading = false
                if (res.success) {
                  this.$notify({
                    title: '成功',
                    message: '新增成功',
                    type: 'success',
                  })
                  this.$router.go(-1)
                }
              })
              .catch(() => (this.loading = false))
          }
        })
      },
      goBack() {
        this.$router.go(-1)
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    padding-bottom: 60px;

    &-box {
      height: calc(100% - 10px);
      position: relative;
      border-radius: 4px;

      > img {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 252px;
        height: 232px;
      }
    }

    &-footer {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 60px;
      line-height: 60px;
      padding-right: 20px;
      box-sizing: border-box;
      background: #fff;
      border-top: 1px solid rgba(200, 200, 200, 0.35);
      text-align: center;
    }
  }

  .form {
    background: #fff;
    padding: 2px;
    color: #333;

    &-title {
      height: 38px;
      line-height: 38px;
      background-color: #f7f8fa;
      border-radius: 3px;
      font-size: 14px;
      padding-left: 37px;
      box-sizing: border-box;
      position: relative;

      img {
        width: 3px;
        position: absolute;
        left: 30px;
        top: calc(50% - 6px);
      }
    }

    .nancalui-form {
      width: 700px;
      margin: 40px auto 0;
    }
  }
</style>
