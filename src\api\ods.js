import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 已授权用户列表
export function getAuthList(data) {
  return request({
    url: `${URL.ODS}/directory/authorizationUserList`,
    method: 'post',
    data,
  })
}
// 分页查询
function ODSSearch(data) {
  return request({
    url: `${URL.ODS}/directory/search`,
    method: 'post',
    loading: true,
    data,
  })
}

// 获取所有项目
function getAllProject() {
  return request({
    url: `${URL.ODS}/directory/project`,
    method: 'get',
    loading: true,
  })
}
// 详情
function getDetail(id) {
  return request({
    url: `${URL.ODS}/directory/detail/${id}`,
    method: 'get',
    loading: true,
  })
}
// 授权申请
function applyAuth(data) {
  return request({
    url: `${URL.ODS}/directory/authorization/application`,
    method: 'post',
    loading: true,
    data,
  })

}

// 授权
function authorization(data) {
  return request({
    url: `${URL.ODS}/directory/authorization/manager/model`,
    method: 'post',
    loading: true,
    data,
  })
}

// 采集数量
function getCollectNum(id) {
  return request({
    url: `${URL.ODS}/directory/count`,
    method: 'get',
    loading: true,
  })
}

// 授权管理保存
function saveAuthorization(data) {
  return request({
    url: `${URL.ODS}/directory/authorization/manager`,
    method: 'post',
    loading: true,
    data,
  })
}

// 授权申请查询
function getAuthorization(data) {
  return request({
    url: `${URL.ODS}/directory/authorization/application/detail`,
    method: 'post',
    loading: true,
    data,
  })
}

// 取消授权
function cancelAuthorization(data) {
  return request({
    url: `${URL.ODS}/directory/cancel/authorization`,
    method: 'post',
    loading: true,
    data,
  })
}

// 检查用户是否是三员管理员
function isThreeMember(params) {
  return request({
    url: `${URL.BASE}/threeManager/is-three-member`,
    method: 'get',
    loading: true,
    params
  })
}
export default {
  getAllProject,
  ODSSearch,
  getDetail,
  applyAuth,
  authorization,
  getCollectNum,
  saveAuthorization,
  getAuthorization,
  cancelAuthorization,
  isThreeMember,
  getAuthList,
}
