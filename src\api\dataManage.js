import request from '@/utils/request'
import URL from '@/const/urlAddress'

//----------盘点总览
//盘点统计
function getCollectCount(data) {
  return request({
    url: `${URL.COLLECT}/overview/dataStats`,
    method: 'get',
    params: data,
  })
}
//盘点echarts
function getCollectMonitor(data) {
  return request({
    url: `${URL.COLLECT}/overview/dataMonitor`,
    method: 'get',
    params: data,
  })
}
//采集总览
// 数据采集-统计正在运行的采集任务
function collectTaskCount(data) {
  return request({
    url: `${URL.COLLECT}/overview/job/running/count`,
    method: 'get',
  })
}

// 数据采集-统计正在运行的采集任务
function collectJobInstance(data) {
  return request({
    url: `${URL.COLLECT}/overview/job/instance/result/count`,
    method: 'get',
    params: data,
  })
}
// 《《《《------------- 数据采集接口部分 ----------- 》》》》
// 数据采集-sftp文件预览
function collectFilePreview(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/sftp/preview`,
    method: 'post',
    data: data,
  })
}

// 数据采集-sftp文件扫描
function collectFileScan(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/sftp/scan`,
    method: 'post',
    data: data,
  })
}

// 数据采集-获取数据采集列表
function getCollectList(data) {
  return request({
    url: `${URL.COLLECT}/collect/search`,
    method: 'post',
    data: data,
  })
}

// 数据采集-获取采集任务状态下拉列表
function getCollectStatusList(data) {
  return request({
    url: `${URL.COLLECT}/collect/status`,
    method: 'get',
    params: data,
  })
}

// 数据采集-采集任务上线
function collectTaskRestart(data) {
  return request({
    url: `${URL.COLLECT}/collect/publish`,
    method: 'get',
    params: data,
  })
}
// 数据采集-检查数据源状态能否上线
function checkDataSource(data) {
  return request({
    url: `${URL.COLLECT}/collect/checkDataSource`,
    method: 'get',
    params: data,
  })
}

// 数据采集-采集任务下线
function collectTaskStop(data) {
  return request({
    url: `${URL.COLLECT}/collect/down`,
    method: 'get',
    params: data,
  })
}
// 数据采集-获取采集任务详情
function getCollectTaskDetail(data) {
  return request({
    url: `${URL.COLLECT}/collect/detail`,
    method: 'get',
    params: data,
  })
}

// 数据采集-采集任务立即执行
function collectTaskExecute(data) {
  return request({
    url: `${URL.COLLECT}/collect/execute`,
    method: 'get',
    params: data,
  })
}

// 数据采集-新增采集任务校验采集任务名唯一  (true 已存在，false不存在)
function collectTaskNameOnly(data) {
  return request({
    url: `${URL.COLLECT}/collect/valid`,
    method: 'get',
    params: data,
  })
}
// 数据采集-新增采集任务
function addCollectTask(data) {
  return request({
    url: `${URL.COLLECT}/collect/add`,
    method: 'post',
    data: data,
  })
}
// 数据采集-修改采集任务
function updateCollectTask(data) {
  return request({
    url: `${URL.COLLECT}/collect/update`,
    method: 'post',
    data: data,
  })
}

// 数据采集-查询指定数据源数据库的表可用于增量采集的字段
function incUsableFields(data) {
  return request({
    url: `${URL.BASE}/datasource/table/fields/inc-usable`,
    method: 'post',
    data: data,
  })
}

// 数据采集-非结构化采集任务新增
function addUnstructuredCollectTask(data) {
  return request({
    url: `${URL.COLLECT}/collect/unstructured`,
    method: 'post',
    data: data,
  })
}

// 数据采集-删除采集任务
function deleteCollectTask(data) {
  return request({
    url: `${URL.COLLECT}/collect/delete`,
    method: 'get',
    params: data,
  })
}

// 离线采集数据源类型-默认结构化数据源枚举
function getOfflineDatasourceList(data) {
  return request({
    url: `${URL.BASE}/datasource/type/offline`,
    method: 'get',
    params: data,
  })
}

// 数据采集-新增数据源层下拉列表
function getDatasourceFeignList(data) {
  return request({
    url: `${URL.BASE}/datasource/list`,
    method: 'get',
    params: data,
  })
}

// 数据采集-查询source数据库的所有表
function getSourceTables(data) {
  return request({
    url: `${URL.BASE}/datasource/tables/${data.dataSourceId}`,
    method: 'get',
    params: data,
  })
}

// 数据采集-表是否已采集检查
function checkExists(data) {
  return request({
    url: `${URL.COLLECT}/collect-job/check-exists`,
    method: 'get',
    params: data,
  })
}

// 数据采集多选-表是否已采集检查
function checkMoreExists(data) {
  return request({
    url: `${URL.COLLECT}/collect-job/check-exists/batch`,
    method: 'get',
    params: data,
  })
}

//采集目标表是否已存在检查
function checkTargetExists(data) {
  return request({
    url: `${URL.COLLECT}/collect-job/check-destination-table-exists`,
    method: 'post',
    data,
  })
}

// 数据采集-查询source数据库的所有表带分页
function getSourceTablesPage(data) {
  return request({
    url: `${URL.BASE}/datasource/tables/page`,
    method: 'post',
    data: data,
  })
}

// 数据采集-查询source数据库的所有表带分页新
function getSourceTablesPageV2(data) {
  return request({
    url: `${URL.BASE}/datasource/tables/page/v2`,
    method: 'post',
    data: data,
  })
}

// 数据上传-查询source数据库的所有表
function fileDataPreview(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/data/preview`,
    method: 'post',
    data: data,
  })
}

// 数据采集-查询source数据库的表结构
function getSourceStructure(data) {
  return request({
    url: `${URL.BASE}/datasource/table/structure/${data.id}/${data.tableName}`,
    method: 'get',
    params: data,
  })
}
function getSourceStructureV2(data) {
  return request({
    url: `${URL.BASE}/datasource/table/structure/v2`,
    method: 'post',
    data: data,
  })
}

// 数据采集-修改
function editCollectTask(data) {
  return request({
    // url: `${URL.COLLECT}/collect/update/${data.id}`,
    url: `${URL.COLLECT}/collect/update`,
    method: 'post',
    data: data,
  })
}
// 数据采集-获取数据表下拉列表
function getSourceSelect(data) {
  return request({
    url: `${URL.BASE}/datasource/tables/${data.dataSourceId}`,
    method: 'get',
  })
}

// 数据采集-统计场景数据源数量
function getSourceCount(data) {
  return request({
    url: `${URL.BASE}/datasource/project/count`,
    method: 'get',
  })
}

// 数据采集-统计场景数据源数量
function getDataSourceCount(data) {
  return request({
    url: `${URL.BASE}/datasource/count`,
    method: 'get',
  })
}

// 《《《《------------- 模型管理使用的接口部分 ----------- 》》》》
// 数据盘点-获取数据源下拉列表,optionsOne & optionsThree
function getSourceOptions() {
  return request({
    url: `${URL.BASE}/external_data_sources/listAll`,
    method: 'get',
  })
}

//  数据盘点-根据数据源列表id获取数据表,optionsTwo
function getTableOptions(data) {
  return request({
    url: `${URL.BASE}/external_data_sources/${data.externalDataSourceId}/tables`,
    method: 'post',
    data: data,
  })
}

//  数据盘点-根据optionsTwo获取下表模型
function getLeftModel(data) {
  return request({
    url: `${URL.BASE}/external_data_sources/${data.dataSourceId}/tables/${data.tableName}/structure`,
    method: 'get',
    data: data,
  })
}
//  数据盘点--根据optionsFour获取下表模型
function getRightModel(data) {
  return request({
    url: `${URL.MODEL}/model/${data.id}/metas`,
    method: 'get',
    data: data,
  })
}

// 《《《《---- 运维监控接口部分-------- 》》》》
// 数据盘点-运维监控-列表
function monitorList(data) {
  return request({
    url: `${URL.COLLECT}/monitor/search`,
    method: 'post',
    data: data,
  })
}
//任务工作流实例列表
function monitorInstanceList(data) {
  return request({
    url: `${URL.COLLECT}/monitor/process/instance/search`,
    method: 'post',
    data: data,
  })
}

//监控统计接口
function getMonitorStatistic(data) {
  return request({
    url: `${URL.COLLECT}/monitor/process/instance/statistic`,
    method: 'post',
    data: data,
  })
}

// 数据盘点-运维监控-实例详情查询
function monitorTaskDetail(data) {
  return request({
    url: `${URL.COLLECT}/collect/${data.id}`,
    method: 'get',
    // params: data,
  })
}

// 数据盘点-运维监控-任务实例列表
function monitorTaskList(data) {
  return request({
    url: `${URL.COLLECT}/monitor/instance/search`,
    method: 'post',
    data: data,
  })
}
// 数据盘点-运维监控-重跑
function monitorTaskRerun(data) {
  return request({
    url: `${URL.COLLECT}/monitor/run/again/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 数据盘点-运维监控-查看日志
function getMonitorTaskLog(data) {
  return request({
    url: `${URL.COLLECT}/monitor/log/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 文件导入---单个
function fileUploadSingle(data) {
  return request({
    url: `${URL.COLLECT}/collect/${data.bucket}/obj/upload`,
    method: 'post',
    data: data.file,
  })
}
// 数据采集-数据本地上传
function fileUploadLocal(data) {
  return request({
    url: `${URL.OSS}/data-govern/obj/upload`,
    method: 'post',
    data: data,
  })
}

// 系统非结构化数据转换
function fileUpConvert(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/coordination/file/convert/${data.versionId}`,
    responseType: 'blob',
    method: 'post',
    data: data,
  })
}

// 数据采集-数据本地上传返回md5
function fileUploadLocalMd5(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/upload`,
    method: 'post',
    data: data,
  })
}

// 数据采集-数据本地上传标签列表
function fileUploadLocalTagList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/tag/list`,
    method: 'post',
    data: data,
  })
}

// 数据采集-新标签列表
function dataCollectionTagList(data) {
  return request({
    url: `${URL.BASE}/data/tag/list`,
    method: 'post',
    data: data,
  })
}

// 文件导入---多个
function fileUploadBatch(data) {
  return request({
    url: `${URL.COLLECT}/collect/${data.bucket}/obj/upload/batch`,
    method: 'post',
    data: data.file,
  })
}
// 文件下载
function fileDownload(data) {
  return request({
    url: `${URL.OSS}/${data.bucket}/obj/download`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}

// 预览文件下载
function fileWatermarkDownload(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/download`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}

// word转pdf下载
function wordToPdfDownload(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/download/topdf`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}

function fileSaveLoad(data) {
  return request({
    url: `${URL.COLLECT}/work/online/saveLoad`,
    method: 'post',
    data: data,
  })
}

// 新建文件上传
function fileUploadAdd(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/upload-record/add`,
    method: 'post',
    data: data,
  })
}

function websocketUrl(data) {
  return request({
    url: `${URL.COLLECT}/work/online/load/websocketUrl`,
    method: 'get',
    params: data,
  })
}
// 水印模板列表
function getWatermarkTemplateList(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/template/list`,
    method: 'get',
    params: data,
  })
}

// 获取水印设置
function getWatermarkInfo(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/config/get`,
    method: 'get',
    params: data,
  })
}

// 保存水印
function saveWatermark(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/config/save`,
    method: 'post',
    data: data,
  })
}

// 水印设置是否启用
function watermarkEnabled(data) {
  return request({
    url: `${URL.COLLECT}/file/watermark/config/enabled`,
    method: 'post',
    data: data,
  })
}
// 获取文件类型list
function getFileTypeList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/type`,
    method: 'get',
    params: data,
  })
}
// 获取文件类型场景的文件列表
function getThisFileTypeList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/list`,
    method: 'get',
    params: data,
  })
}
// 获取文件类型场景的文件列表-分页
function getThisFileTypeListSearch(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/search`,
    method: 'post',
    data: data,
  })
}
// 判断当前用户是否有权限查看文件
function permissionCheck(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/preview/permission/check`,
    method: 'get',
    params: data,
  })
}

// 判断文件是否重复
function fileUnique(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/data/unique`,
    method: 'post',
    data: data,
  })
}

// 文件删除---多个
function fileDeleteBatch(data) {
  return request({
    url: `${URL.OSS}/${data.bucket}/obj/delete/batch`,
    method: 'post',
    data: data.objNames,
  })
}
// 数据采集---逆向数据模型-元数据列表信息
function reverseWithMetadata(data) {
  return request({
    url: `${URL.MODEL}/model/save`,
    method: 'post',
    data: data,
  })
}
// --------------非结构化数据类型树改成目录树--------

// 资源目录 -- 分组删除
function collectJobTreeDel(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 资源目录 -- 分组创建
function collectJobTreeCreate(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/save`,
    method: 'post',
    data: data,
  })
}
// 资源目录 -- 分组树列表
function collectJobTreeList(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/tree`,
    method: 'get',
    params: data,
  })
}
// 资源目录 -- 分组更新
function collectJobTreeUpdate(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/update`,
    method: 'post',
    data: data,
  })
}
// 资源目录 -- 分组树重名校验
function collectJobTreeNameValid(data) {
  return request({
    url: `${URL.COLLECT}/collectJobTree/name/repeat/valid`,
    method: 'get',
    params: data,
  })
}

// 数据上传列表
function uploadRecordList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/upload-record/list`,
    method: 'post',
    data: data,
  })
}

// 执行实例列表
function runInstanceList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/run-instance-list`,
    method: 'post',
    data: data,
  })
}

// 版本列表
function versionList(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/list`,
    method: 'post',
    data: data,
  })
}

// 版本列表全部
function versionListAll(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/${data.jobFileId}/all-list`,
    method: 'post',
    data: data,
  })
}

// Excel协同保存
function coordinationFileSave(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/coordination/file/save`,
    method: 'post',
    data: data,
  })
}

// 创建版本
function addVersion(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/new`,
    method: 'post',
    data: data,
  })
}

// 删除版本
function deleteVersion(data) {
  return request({
    url: `${URL.COLLECT}/collect/file/version/delete/${data.jobFileId}`,
    method: 'post',
  })
}

//...............资源目录开始..................//
// 数据源资源列表
export const collectRecordList = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/collect/record/list`,
    method: 'post',
    data: data,
  })
}
// 数据源资源详情
export const collectTableDetail = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/collect-table/detail/${data.id}`,
    method: 'get',
  })
}
// 数据源资源详情ER图
export const erPhysics = (data) => {
  return request({
    url: `${URL.ER}/er/v1/physics/diagram`,
    method: 'get',
    params: data,
  })
}
// 数据源资源详情ER图--数据库列类型
export const erFieldtype = (data) => {
  return request({
    url: `${URL.ER}/er/v1//tab/fieldtype`,
    method: 'get',
    params: data,
  })
}
// 数据源资源申请采集
export const collectTableApply = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/apply/collect-table/${data.id}`,
    method: 'get',
  })
}
// 资产目录刷新
export const assetsMenuRefresh = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/resource-table/init`,
    method: 'get',
  })
}

// 资产目录数据源刷新
export const assetsMenuDatasourceRefresh = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/resource-table/refresh`,
    method: 'post',
    data: data,
  })
}

// 资产目录数据源刷新进度
export const assetsMenuDatasourceRefreshCount = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/resource-table/refresh/count`,
    method: 'get',
    params: data,
  })
}

// 数据源资源申请采集多个表
export const collectTableApplyMore = (data) => {
  return request({
    url: `${URL.STREAM}/datasource/apply/collect-multi-table`,
    method: 'post',
    data: data,
  })
}

//...............资源目录结束..................//

//...............新数据采集开始..................//
// 采集Job分页列表
export const collectJobList = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/list`,
    method: 'post',
    data: data,
  })
}

// 采集Job新建
export const collectJobAdd = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/create`,
    method: 'post',
    data: data,
  })
}

// 采集Job批量新建
export const collectJobMoreAdd = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/create-batch`,
    method: 'post',
    data: data,
  })
}

// 采集Job目标表DDL
export const collectJobDDL = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/target-table/ddl`,
    method: 'post',
    data: data,
  })
}

// 采集Job编辑
export const collectJobUpdate = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/update`,
    method: 'post',
    data: data,
  })
}
// 采集Job删除
export const collectJobDel = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/delete/${data.id}`,
    method: 'get',
  })
}
// 采集Job查看
export const collectJobDetail = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/detail/${data.id}`,
    method: 'get',
  })
}
// 采集Job执行
export const collectJobExecute = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/execute/${data.id}`,
    method: 'get',
  })
}
// 采集Job下架
export const collectJobOffline = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/offline/${data.id}`,
    method: 'get',
  })
}
// 采集Job发布
export const collectJobPublish = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job/publish/${data.id}`,
    method: 'get',
  })
}
//...............新数据采集结束..................//

//...............采集监控开始..................//
// 采集Job运行记录列表
export const collectMonitorList = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job-monitor/schedule/run-list`,
    method: 'post',
    data: data,
  })
}
// 采集结果数据查看
export const collectResultSee = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job-monitor/result-data-preview`,
    method: 'post',
    data: data,
  })
}

// 采集Job运行实例重跑
export const collectResultRun = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job-monitor/schedule/re-run/${data.id}`,
    method: 'post',
  })
}
// 采集Job运行实例日志
export const collectMonitorLog = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job-monitor/log/${data.instanceId}`,
    method: 'get',
    params: data,
  })
}
// 采集Job运行实例日志下载
export const collectMonitorLogDown = (data) => {
  return request({
    url: `${URL.COLLECT}/collect-job-monitor/log/download`,
    responseType: 'blob',
    method: 'get',
    params: data,
  })
}
//...............采集监控结束..................//

//...............离线作业开始..................//
// 查询当前登录人已授权项目ODS表
export const getODSModelList = (data) => {
  return request({
    url: `${URL.MODEL}/ods/directory/user/authorization`,
    method: 'get',
    params: data,
  })
}
// sql语法校验
export const checkSqlTable = (data) => {
  return request({
    url: `${URL.OPERATION}/parser/sql/check`,
    method: 'post',
    data: data,
  })
}
// sql解析-获取表
export const parserSqlTable = (data) => {
  return request({
    url: `${URL.OPERATION}/parser/sql/table`,
    method: 'post',
    data: data,
  })
}
// 离线作业任务保存
export const taskSave = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/save`,
    method: 'post',
    data: data,
  })
}

// 离线作业任务保存
export const taskCommit = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/commit`,
    method: 'post',
    data: data,
  })
}

// 离线作业任务运行
export const taskRun = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run`,
    method: 'post',
    data: data,
  })
}

// DorisDDL-HiveDDL生产提交
export const taskCommitRun = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/commit/run`,
    method: 'post',
    data: data,
  })
}
// 离线作业运行停止
export const taskStop = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/stop`,
    method: 'post',
    data: data,
  })
}

// 业务流程任务运行结果
export const taskRunState = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/run/state/v2`,
    method: 'post',
    data,
  })
}

// 离线作业任务运行结果
export const taskRunResult = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/log`,
    method: 'post',
    data: data,
  })
}

// 离线作业任务运行结果分页
export const taskRunResultPage = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/log/page`,
    method: 'post',
    data: data,
  })
}

// 获取结果的接口
export const taskResult = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/result/page`,
    method: 'post',
    data: data,
  })
}

// 获取结果的接口
export const taskResultV2 = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/result/page/v2`,
    method: 'post',
    data: data,
  })
}

// 清除当前运行节点的测试运行结果
export const taskClear = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/clear/test/result`,
    method: 'post',
    data: data,
  })
}

// 离线作业任务信息
export const taskDetail = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/detail`,
    method: 'get',
    params: data,
  })
}

// 离线作业任务列表
export const taskList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/list`,
    method: 'get',
    params: data,
  })
}

// 离线作业任务版本列表
export const taskVersionList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/version/list`,
    method: 'get',
    params: data,
  })
}

// 离线作业任务版本最新版（暂存）
export const getTaskDetailLatest = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/version/latest`,
    method: 'get',
    params: data,
  })
}

// 业务流程转为正式的信息
export const getOfficialInfo = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/official/info`,
    method: 'get',
    params: data,
  })
}
//...............离线作业结束..................//

//...............离线运维开始..................//
// 正式业务流程作业分页
export const operationWorkList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/search`,
    method: 'post',
    data: data,
  })
}
// 正式业务流程作业详情
export const operationWorkDetail = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/detail`,
    method: 'get',
    params: data,
  })
}

// 查看实例DAG图
export const taskMonitorDag = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/monitor/dag`,
    method: 'get',
    params: data,
  })
}

// 正式业务流程调度启动
export const processScheduleStart = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/schedule/start`,
    method: 'post',
    data: data,
  })
}

// 正式业务流程调度停止
export const processScheduleStop = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/schedule/stop`,
    method: 'post',
    data: data,
  })
}

// 离线作业分页
export const offlineWorkList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/search`,
    method: 'post',
    data: data,
  })
}

// DAG操作
export const instanceDag = (data) => {
  return request({
    url: `${URL.OPERATION}/instance/dag`,
    method: 'post',
    data: data,
  })
}

// 补数据重跑
export const supplementDataRun = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/node/dsInstanceId`,
    method: 'get',
    params: data,
  })
}

// 补数据列表
export const instanceRerunList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/instance/rerun/search`,
    method: 'post',
    data: data,
  })
}
// 离线实例分页
export const instanceList = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/instance/search`,
    method: 'post',
    data: data,
  })
}

// 表管理树
export const tableManageTree = (data) => {
  return request({
    url: `${URL.MODEL}/table/manage/tree`,
    method: 'get',
    params: data,
  })
}

// 表管理分页
export const tableManagePage = (data) => {
  return request({
    url: `${URL.MODEL}/table/manage/tree/page`,
    method: 'post',
    data: data,
  })
}

// 正式业务流程作业实例刷新运行状态
export const taskStateRefresh = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/instance/state/refresh`,
    method: 'get',
    params: data,
  })
}

// 正式业务流程作业实例日志
export const taskStateLog = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/instance/log`,
    method: 'get',
    params: data,
  })
}
// 实例日志下载
export const taskStateLogDownload = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/instance/log/download`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}

// 业务流程任务运行结果下载
export const processTaskLogDownload = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/result/download`,
    method: 'post',
    responseType: 'blob',
    data: data,
  })
}
//...............离线运维结束..................//

// 数据集成-运维监控-采集报表
// 采集报表
function collectReportDetails() {
  return request({
    url: `${URL.COLLECT}/overview/unstructured/collection/report`,
    method: 'get',
  })
}

// 获取签名设置
export const getSignSet = (data) => {
  return request({
    url: `${URL.COLLECT}/file/watermark/sign/get`,
    method: 'get',
    params: data,
  })
}

// 保存签名设置
export const saveSignSet = (data) => {
  return request({
    url: `${URL.COLLECT}/file/watermark/sign/save`,
    method: 'post',
    data: data,
  })
}

// 获取用户最近三次签名设置
export const getUserSignList = () => {
  return request({
    url: `${URL.COLLECT}/file/watermark/user/sign/list`,
    method: 'get',
  })
}

// 水印上传
export const waterUpload = (data) => {
  return request({
    url: `${URL.COLLECT}/collect/file/water/upload`,
    method: 'post',
    data: data,
  })
}

// demo
// 上传
function onlyOfficeUpload(data) {
  return request({
    url: `${URL.OSS}/test/word/demo/upload`,
    method: 'post',
    data: data,
  })
}

// 删除用户历史签名设置
export const delUserSign = (data) => {
  return request({
    url: `${URL.COLLECT}/file/watermark/user/sign/delete`,
    method: 'get',
    params: data,
  })
}

// 获取api地址和端口
export const getApiHost = () => {
  return request({
    url: `${URL.COLLECT}/text/rec/api/info`,
    method: 'get',
  })
}

// 新建数据分类
export const addDataTypeTree = (data) => {
  return request({
    url: `${URL.BASE}/dataCategory/add`,
    method: 'post',
    data: data,
  })
}
// 修改数据分类
export const editDataTypeTree = (data) => {
  return request({
    url: `${URL.BASE}/dataCategory/edit`,
    method: 'post',
    data: data,
  })
}
// 删除数据分类
export const delDataTypeTree = (data) => {
  return request({
    url: `${URL.BASE}/dataCategory/delete`,
    method: 'post',
    data: data,
  })
}
// 数据分类树
export const queryDataTypeTree = () => {
  return request({
    url: `${URL.BASE}/dataCategory/tree`,
    method: 'get',
  })
}
// 数据分类树带数据源
export const dataCategoryTreeDatasource = () => {
  return request({
    url: `${URL.BASE}/dataCategory/tree/datasource`,
    method: 'get',
  })
}

// 离线作业保存
export const offlineworkSave = (data) => {
  return request({
    url: `${URL.REALTIME}/offlinework/biz/process/save`,
    method: 'post',
    data: data,
  })
}

// 离线作业详情查询
export const offlineworkDetail = (params) => {
  return request({
    url: `${URL.REALTIME}/offlinework/biz/process/detail`,
    method: 'get',
    params: params,
  })
}
// 离线作业code生成
export const offlineworkGenerateCode = (params) => {
  return request({
    url: `${URL.REALTIME}/offlinework/biz/process/task/code/generate`,
    method: 'get',
    params: params,
  })
}

// 采集Job列表
export const colletTaskList = () => {
  return request({
    url: `${URL.COLLECT}/collect-job/all-list`,
    method: 'post',
  })
}

// 创建HIVE表DDL
export const createHiveDDLForm = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/hive-table/ddl`,
    method: 'post',
    data,
  })
}

// 创建Doris表DDL
export const createDorisDDLForm = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/doris-table/ddl`,
    method: 'post',
    data,
  })
}

// 业务流程运行
export const workFlowRun = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/run/v2`,
    method: 'post',
    data,
  })
}

// 业务流程停止
export const workFlowStop = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/stop`,
    method: 'post',
    data,
  })
}

// 业务流程提交
export const workFlowCommit = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/online/apply`,
    method: 'post',
    data,
  })
}

// 资产详情
export const assetsRegDetail = (params) => {
  return request({
    url: `${URL.ASSETS}/asset/${params.id}`,
    method: 'get',
    params: params,
  })
}

// 即席查询 左侧列表信息
export const getAdhocQueryLeftList = (data) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/list`,
    method: 'post',
    data,
  })
}
// 即席查询 左侧列表信息删除
export const getAdhocQueryLeftListDelete = (id) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/remove/${id}`,
    method: 'post',
  })
}

// 即席查询 新增
export const getAdhocQueryLeftListAdd = (data) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/add`,
    method: 'post',
    data,
  })
}

// 即席查询 修改接口
export const getAdhocQueryLeftListEdit = (data) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/update`,
    method: 'post',
    data,
  })
}

// 即席查询 ASSETS类型 表获取
export const getAdhocQueryLeftListAssets = () => {
  return request({
    url: `${URL.MODEL}/table/manage/list`,
    method: 'get',
    params: { tableType: 'DORIS', needDorisDatabase: true },
  })
}

// 即席查询 ASSETS类型 字段明细获取
export const getAdhocQueryLeftListAssetsDetail = (data) => {
  return request({
    url: `${URL.MODEL}/model/fields/list`,
    method: 'post',
    data,
  })
}

// 即席查询 详情信息
export const getAdhocQueryLeftListDetail = (id) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/detail/${id}`,
    method: 'post',
  })
}

// 即席查询 查询
export const getAdhocQueryLeftListQuery = (data) => {
  // return request({
  //   url: `${URL.OPERATION}/realtime/query`,
  //   method: 'post',
  //   data,
  // })
  // 即席查询批量接口 （兼容之前的接口）
  return request({
    url: `${URL.OPERATION}/realtime/query/batch`,
    method: 'post',
    data,
  })
}

// 数据分类树-获取已发布的数据源
export const getPublishDataSource = () => {
  return request({
    url: `${URL.BASE}/dataCategory/tree/database/v2`,
    method: 'get',
  })
}

// 实时开发 可注册为资产的数据分类
export const getRealtimeDataCategory = () => {
  return request({
    url: `${URL.BASE}/external-data/registrable/category/tree`,
    method: 'get',
  })
}

// 采集任务每日运行统计
export const getCollectTaskDailyStatistics = (data) => {
  return request({
    url: `${URL.COLLECT}/monitor/collect-job-run/daily/statistic`,
    method: 'post',
    data,
  })
}

// 采集任务每日运行耗时Top5
export const getCollectTaskDailyStatisticsTop5 = (data) => {
  return request({
    url: `${URL.COLLECT}/monitor/collect-job-run/time/top5`,
    method: 'post',
    data,
  })
}

// 即席查询-导出文件
export const getAdhocQueryLeftListExport = (data) => {
  return request({
    url: `${URL.OPERATION}/realtime/query/download`,
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 数据采集-新增数据源层下拉列表
export const getDatasourceFeignListNew = (data) => {
  return request({
    url: `${URL.BASE}/datasource/list`,
    method: 'get',
    params: data,
  })
}

// 运行日志
export const taskRunLog = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/run/log/extend/${data.runInstanceId}`,
    method: 'post',
    data,
  })
}

// 获取工作流前置任务-ODS信息查询
export const workFlowProcessTask = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/biz/process/task/preposition`,
    method: 'post',
    data,
  })
}

// Hive建表默认子句片段
export const hiveTableDefaultClause = (data) => {
  return request({
    url: `${URL.OPERATION}/offlinework/hive-ddl-fixed-cause`,
    method: 'post',
    data,
  })
}

// 数据治理-相关数据上传
export const workbenchProjectUpload = (data) => {
  const form = new FormData()
  form.append('file', data.file)
  return request({
    url: `${URL.BASE}/workbench/project/excel-import/${data.type}`,
    method: 'post',
    data: form,
    responseType: 'blob',
  })
}

// 个人数据治理-个人数据基本信息获取
export const workbenchUserInfo = (data) => {
  return request({
    url: `${URL.BASE}/workbench/user/info`,
    method: 'get',
    params: data,
  })
}

// 个人数据治理-任职记录信息获取
export const workbenchUserPosition = (data) => {
  return request({
    url: `${URL.BASE}/workbench/user/position`,
    method: 'post',
    data,
  })
}

// 数据治理项目列表
export const workbenchProjectList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/list`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-任务分页获取
function workbenchTaskSearch(data) {
  return request({
    url: `${URL.BASE}/workbench/annotation/task/search`,
    method: 'post',
    data,
  })
}

// 数据治理任务分页
function workbenchProjectTask(data) {
  return request({
    url: `${URL.BASE}/workbench/project/governance-task/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目收藏列表
export const workbenchProjectCollectList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/annotation/project/collection/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目字典列表
export const workbenchDicCode = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/dic-code/${data.type}`,
    method: 'post',
    data,
  })
}

// 数据治理项目详情
export const workbenchProjectDetail = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 数据治理项目详情
export const workbenchAnnotationProjectDetail = (data) => {
  return request({
    url: `${URL.BASE}/workbench/annotation/project/detail/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 数据治理项目主数据列表
export const workbenchMasterDataList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/master-data/list`,
    method: 'post',
    data: data,
  })
}

// 标注规则-分页
export const workbenchAnnotationRuleSearch = (data) => {
  return request({
    url: `${URL.BASE}/workbench/annotation/project/rule/search`,
    method: 'post',
    data: data,
  })
}

// 数据治理项目数据定义列表
export const workbenchDefinitionList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/data-definition/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目成员列表
export const workbenchMemberList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/member/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目主题模型列表
export const workbenchModelList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/topic-model/list`,
    method: 'post',
    data,
  })
}

export const workbenchBloodMap = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/bldp/drwng/arch`,
    method: 'post',
    data,
  })
}

// 数据治理项目质量规则列表
export const workbenchRuleList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/quality-rule/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目质量规则服务
export const workbenchRuleService = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/quality-rule/api-doc`,
    method: 'get',
    params: data,
  })
}

// 数据治理项目质量规则列表
export const workbenchPlanList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/plan/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目质量规则创建
export const workbenchPlanCreate = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/plan/create`,
    method: 'post',
    data,
  })
}

// 数据治理项目质量规则编辑
export const workbenchPlanEdit = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/plan/update`,
    method: 'post',
    data,
  })
}

// 数据治理项目质量规则删除
export const workbenchPlanDelete = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/plan/delete/${data.id}`,
    method: 'post',
    data,
  })
}

// 流程数据流图URL
export const workbenchFlowGraphUrl = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/flow-graph-url`,
    method: 'get',
    params: data,
  })
}

// 逻辑模型URL
export const workbenchLogicModelUrl = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/logic-model-url`,
    method: 'get',
    params: data,
  })
}

// 着陆页配置项-获取key
export const workbenchLoadKey = (data) => {
  return request({
    url: `${URL.BASE}/landing/page/load/key`,
    method: 'get',
    params: data,
  })
}

// 数据治理项目业务表单列表
export const workbenchBiztblList = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/biztbl/bll/list`,
    method: 'post',
    data,
  })
}

// 数据治理发起标注任务列表
export const initiateLabelTaskList = (data) => {
  const formData = new FormData()
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key])
    }
  }
  return request({
    url: `${URL.BASE}/workbench/project/problem/data-anno/data-list`,
    method: 'post',
    data: formData,
  })
}

// 数据治理发起标注任务保存
export const initiateLabelTaskSave = (data) => {
  return request({
    url: `${URL.BASE}/workbench/project/problem/data-anno/save`,
    method: 'post',
    data,
  })
}

// 数据治理项目数据标注说明列表
function workbenchExplainList(data) {
  return request({
    url: `${URL.BASE}/workbench/project/data-annotation-explain/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目数据标注列表
function workbenchAnnotationList(data) {
  return request({
    url: `${URL.BASE}/workbench/project/data-annotation/list`,
    method: 'post',
    data,
  })
}

// 数据治理项目问题数据列表
function workbenchProblemList(data) {
  return request({
    url: `${URL.BASE}/workbench/project/problem/list`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-审批驳回
function workbenchAuditReject(data) {
  return request({
    url: `${URL.BASE}/workbench/annotation/task/audit/reject/${data.id}`,
    method: 'post',
    data,
  })
}

// 数据标注-项目编辑
function workbenchAnnotationProjectUpdate(data) {
  return request({
    url: `${URL.BASE}/workbench/annotation/project/update`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-打标
function workbenchAnnotationAuditTag(data) {
  return request({
    url: `${URL.BASE}/workbench/annotation/task/audit/tag/${data.id}`,
    method: 'post',
    data,
  })
}

// 数据标注-任务分类分页获取-我参与的
function workbenchAnnotationTaskSearch(data) {
  return request({
    url: `${URL.BASE}/workbench/task/own/category/search`,
    method: 'post',
    data,
  })
}

export const reportDownload = (data) => {
  return request({
    url: `/api/govern-data-quality/governance/quality/report/word`,
    responseType: 'blob',
    method: 'get',
    params: data,
  })
}

// 业务对象搜索
export const businessObjectSearch = (data) => {
  return request({
    url: `/api/integration-global-model/arch/v1/businessobject/search`,
    method: 'get',
    params: data,
  })
}

// 业务数据流图
export const businessDataFlowGraph = (data) => {
  return request({
    url: `/api/integration-global-model/arch/v1/dataflow/search`,
    method: 'get',
    params: data,
  })
}

// 实体属性
export const entityProperty = (data) => {
  return request({
    url: `/api/integration-global-model/entity/v1/fieldList/search`,
    method: 'get',
    params: data,
  })
}

export default {
  getCollectCount,
  getCollectMonitor,
  getCollectList,
  getCollectStatusList,
  collectTaskRestart,
  collectTaskStop,
  getCollectTaskDetail,
  collectTaskExecute,
  collectTaskNameOnly,
  addCollectTask,
  deleteCollectTask,
  collectTaskCount,
  collectJobInstance,
  getDatasourceFeignList,
  checkExists,
  checkMoreExists,
  getSourceTables,
  getSourceTablesPage,
  getSourceTablesPageV2,
  fileDataPreview,
  getSourceStructure,
  getSourceStructureV2,
  editCollectTask,
  getSourceOptions,
  getTableOptions,
  getLeftModel,
  getRightModel,
  getSourceSelect,
  getSourceCount,
  monitorList,
  monitorTaskList,
  monitorTaskDetail,
  monitorTaskRerun,
  getMonitorTaskLog,
  getOfflineDatasourceList,
  addUnstructuredCollectTask,
  fileUpConvert,
  fileUploadLocal,
  fileUploadLocalMd5,
  fileUploadLocalTagList,
  dataCollectionTagList,
  fileUploadSingle,
  fileUploadBatch,
  fileDownload,
  fileWatermarkDownload,
  fileUploadAdd,
  fileSaveLoad,
  websocketUrl,
  getWatermarkTemplateList,
  getWatermarkInfo,
  saveWatermark,
  watermarkEnabled,
  getFileTypeList,
  getThisFileTypeList,
  fileDeleteBatch,
  getThisFileTypeListSearch,
  permissionCheck,
  fileUnique,
  reverseWithMetadata,
  collectJobTreeDel,
  collectJobTreeCreate,
  collectJobTreeList,
  collectJobTreeUpdate,
  updateCollectTask,
  incUsableFields,
  getMonitorStatistic,
  monitorInstanceList,
  collectFileScan,
  collectFilePreview,
  checkDataSource,
  collectJobTreeNameValid,
  uploadRecordList,
  runInstanceList,
  versionList,
  versionListAll,
  coordinationFileSave,
  addVersion,
  deleteVersion,
  collectReportDetails,
  onlyOfficeUpload,
  wordToPdfDownload,
  getDataSourceCount,
  getAdhocQueryLeftList,
  getAdhocQueryLeftListDelete,
  getPublishDataSource,
  getAdhocQueryLeftListAdd,
  getAdhocQueryLeftListDetail,
  getAdhocQueryLeftListEdit,
  getAdhocQueryLeftListAssets,
  getAdhocQueryLeftListAssetsDetail,
  getAdhocQueryLeftListQuery,
  getCollectTaskDailyStatistics,
  getCollectTaskDailyStatisticsTop5,
  getAdhocQueryLeftListExport,
  erPhysics,
  erFieldtype,
  checkTargetExists,
  hiveTableDefaultClause,
  workbenchExplainList,
  workbenchAnnotationList,
  workbenchProblemList,
  workbenchAuditReject,
  workbenchAnnotationProjectUpdate,
  workbenchAnnotationAuditTag,
  workbenchTaskSearch,
  workbenchProjectTask,
  workbenchProjectCollectList,
  initiateLabelTaskList,
  initiateLabelTaskSave,
  workbenchAnnotationTaskSearch,
  businessObjectSearch,
  businessDataFlowGraph,
  entityProperty,
}
