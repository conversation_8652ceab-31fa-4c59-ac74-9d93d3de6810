import URL from '@/const/urlAddress'
import request from './request'

export const dataGovernance = {
    // 数据标注-获取标签明细
    getTaskLabelDetail(data, Authorization) {
        return request({
            url: `/api/govern-management/workbench/task/load/detail/${data}`,
            method: 'post',
            headers: { 'X-Token': Authorization, Authorization },
        })
    },
    // 自动标注结果获取
    autoAnnotationResult(data, Authorization) {
        return request({
            url: `/api/govern-management/workbench/auto/annotation/result`,
            method: 'post',
            data,
            headers: { 'X-Token': Authorization, Authorization },
        })
    },
    // 自动标注
    autoAnnotation(data, Authorization) {
        return request({
            url: `/api/govern-management/workbench/auto/annotation/push`,
            method: 'post',
            data,
            headers: { 'X-Token': Authorization, Authorization },
        })
    }
}

// 文档标注
export const dataManagement = {
    // 文档标注-文档标注预览
    getDocumentAnnotationPreview(id, Authorization) {
        return request({
            url: `${URL.DOCUMENT}/document-mark/view/${id}`,
            method: 'get',
            loading: true,
            headers: { 'X-Token': Authorization, Authorization },
        })
    },



}

// 文档标注
export const documentManage = {
    // 文档标注-文档标注预览
    getDocumentAnnotationPreview(id, Authorization) {
        return request({
            url: `${URL.DOCUMENT}/document-mark/view/${id}`,
            method: 'get',
            loading: true,
            headers: { 'X-Token': Authorization, Authorization },
        })
    },
    //非结构化数据详情
    outsideGet(data, Authorization) {
        return request({
            url: `${URL.DOCUMENT}/outside/get`,
            method: 'get',
            loading: true,
            params: data,
            headers: { 'X-Token': Authorization, Authorization },
        })
    },
    // 文档标注-保存文档标注
    saveDocumentAnnotation(data, Authorization) {
        return request({
            url: `${URL.DOCUMENT}/document-mark/save`,
            method: 'post',
            loading: true,
            data,
            headers: { 'X-Token': Authorization, Authorization },
        })
    },
    // 文档标注内容导出Excel
    exportDocumentAnnotationExcel(documentId, Authorization) {
        return request({
            url: `${URL.DOCUMENT}/document-mark/content/export/${documentId}`,
            method: 'get',
            loading: true,
            responseType: 'blob',
            headers: { 'X-Token': Authorization, Authorization },
        })
    }
}