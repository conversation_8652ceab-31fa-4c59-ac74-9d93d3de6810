<template>
  <div class="content" @click="state.configPopupShow = false">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div
        v-if="state.isRun"
        :class="{ btn: true, disabled: state.isBtnDisble }"
        @click.prevent.stop="
          () => {
            state.isBtnDisble ? '' : stopFn(true)
          }
        "
      >
        <SvgIcon class="icon" icon="icon-offline-stop" />
        停止
      </div>
      <div
        v-else
        :class="{ btn: true, active: true, disabled: state.isBtnDisble }"
        @click.prevent.stop="
          () => {
            state.isBtnDisble ? '' : runFn()
          }
        "
      >
        <SvgIcon class="icon" icon="icon-offline-start" />
        运行
      </div>
      <div v-loading="state.loading" class="toolbar-box">
        <div
          :class="{ btn: true, disabled: state.isBtnDisble }"
          @click="
            () => {
              state.isBtnDisble ? '' : saveJob(false)
            }
          "
        >
          <SvgIcon class="icon" icon="icon-offline-save" />
          保存
        </div>
        <div
          :class="{ btn: true, disabled: state.isBtnDisble }"
          @click="
            () => {
              state.isBtnDisble ? '' : submitJob()
            }
          "
        >
          <SvgIcon class="icon" icon="icon-offline-submit" />
          提交
        </div>
      </div>
    </div>

    <div class="page-content">
      <div class="page-title"> 同步至资源库 </div>
      <n-form
        :data="dataAsyncTaskBO"
        :rules="rules"
        ref="formRef"
        label-width="110px"
        message-type="text"
      >
        <div class="data-async">
          <div class="content-title">
            <span>选择表</span>
          </div>
          <div class="form-row">
            <n-form-item label="来源数据源：">
              <div class="data-source"> Hive </div>
            </n-form-item>
            <n-form-item field="sourceTableId" label="数据源表：">
              <div class="source-box">
                <n-select
                  v-model="dataAsyncTaskBO.odsTable"
                  :options="[
                    { name: '结果表', value: false },
                    { name: 'ODS表', value: true },
                  ]"
                  @value-change="
                    () => {
                      dataAsyncTaskBO.sourceTableId = null
                      getOfflineModel('')
                      dataSourceTableChange(true)
                    }
                  "
                />
                <el-select
                  v-model="dataAsyncTaskBO.sourceTableId"
                  placeholder="请选择"
                  filterable
                  remote
                  :remote-method="getOfflineModel"
                  @change="dataSourceTableChange"
                >
                  <el-option
                    v-for="item in state.modelList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name + '(' + item.cnName + ')'"
                  />
                </el-select>
              </div>
            </n-form-item>
          </div>
          <div class="content-title">
            <span>预览表</span>
          </div>
          <CfTable
            :key="state.tableDataPreviewHeadTitles"
            :tableConfig="{
              data: state.tableDataPreview,
              rowKey: 'id',
            }"
            :table-head-titles="
              state.tableDataPreviewHeadTitles?.map(({ cnName, name }) => ({
                name: cnName || name,
                prop: name,
              }))
            "
          >
            <template #pageTop>
              <div style="text-align: right; margin-bottom: 8px"> 仅支持预览前10条数据</div>
            </template>
          </CfTable>
          <div class="content-title">
            <span>设置目标表</span>
          </div>
          <div class="form-row">
            <n-form-item field="sinkDatasourceId" label="目标数据源：">
              <n-select
                v-model="dataAsyncTaskBO.sinkDatasourceId"
                :options="state.tagerDataSourceList"
                placeholder="请选择数据源"
                @value-change="
                  (item) => {
                    dataSourceChange(item)
                  }
                "
              />
            </n-form-item>
            <n-form-item field="sinkTableEnName" label="表英文名称：">
              <n-input disabled v-model="dataAsyncTaskBO.sinkTableEnName" placeholder="请输入" />
            </n-form-item>
            <n-form-item field="clearOldData" label="覆盖模式：">
              <div class="coverMode">
                <n-checkbox
                  label="是否重建表"
                  :isShowTitle="false"
                  v-model="dataAsyncTaskBO.overwriteTable"
                />
                <n-checkbox
                  label="清空已有表数据"
                  :isShowTitle="false"
                  v-model="dataAsyncTaskBO.clearOldData"
                />
              </div>
            </n-form-item>
            <n-form-item field="sinkTableName" label="表中文名称：">
              <n-input v-model="dataAsyncTaskBO.sinkTableName" placeholder="请输入" />
            </n-form-item>
            <n-form-item label="前置SQL：">
              <n-textarea
                v-model="dataAsyncTaskBO.sinkPreSqlList[0]"
                :rows="5"
                :disabled="dataAsyncTaskBO.clearOldData"
                placeholder="请输入"
              />
            </n-form-item>
            <n-form-item label="后置SQL：">
              <n-textarea
                v-model="dataAsyncTaskBO.sinkPostSqlList[0]"
                :rows="5"
                :disabled="dataAsyncTaskBO.clearOldData"
                placeholder="请输入"
              />
            </n-form-item>
            <n-form-item field="executorMinMemory" label="最小内存：">
              <n-select
                v-model="dataAsyncTaskBO.executorMinMemory"
                :options="state.memoryOptions"
              />
            </n-form-item>
            <n-form-item field="executorMaxMemory" label="最大内存：">
              <n-select
                v-model="dataAsyncTaskBO.executorMaxMemory"
                :options="state.memoryOptions"
              />
            </n-form-item>
          </div>
          <div class="content-title">
            <span>字段映射</span>
          </div>
          <div class="table-row">
            <!-- <div>
              <div style="display: inline-block">
                <div class="btn" @click="selectionChange"> 智能字段生成 </div>
              </div>
            </div> -->
            <div class="form-table" style="margin-bottom: 0">
              <CfTable
                :key="state.metaDataModel"
                ref="sourceTableRef"
                class="table-row-list"
                :tableConfig="{
                  data: state.metaDataModel,
                  onSelectionChange: selectionChange,
                  rowKey: 'id',
                }"
                :table-head-titles="state.metaDataModelHead"
                isNeedSelection
                isNeedIndex
              >
                <template #pageTop>
                  <div class="table-top">
                    <div class="label"> 数据源： </div>
                    <div class="value">
                      {{ dataAsyncTaskBO.sourceTableName || '--' }}
                    </div>
                  </div>
                </template>
              </CfTable>
              <n-form
                :data="dataAsyncTaskBO.columnMapping"
                ref="columnMappingTableRef"
                message-type="text"
              >
                <CfTable
                  ref="targetTableRef"
                  :key="dataAsyncTaskBO.columnMapping"
                  :tableConfig="{
                    data: dataAsyncTaskBO.columnMapping,
                    'cell-style': {
                      border: 'none',
                      height: '40px',
                      padding: ' 0',
                    },
                    rowKey: 'id',
                  }"
                  isNeedIndex
                  :table-head-titles="[
                    { prop: 'sinkMetaCnName', name: '中文名', slot: 'sinkMetaCnName' },
                    { prop: 'sinkMetaCode', name: '英文名 ', slot: 'sinkMetaCode' },
                    { prop: 'sinkMetaType ', name: '字段类型', slot: 'sinkMetaType' },
                    {
                      prop: 'sinkMetaLength',
                      name: '字段长度',
                      slot: 'sinkMetaLength',
                      width: 180,
                    },
                    {
                      prop: 'sinkMetaPrecision',
                      name: '精度',
                      slot: 'sinkMetaPrecision',
                      width: 180,
                    },
                  ]"
                >
                  <template #pageTop>
                    <div class="table-top">
                      <div class="label"> 目标表： </div>
                      <div class="value" :title="dataAsyncTaskBO.sinkTableName || '--'">
                        {{ dataAsyncTaskBO.sinkTableName || '--' }}
                      </div>
                    </div>
                  </template>
                  <template #sinkMetaCnName="{ row, index }">
                    <n-form-item
                      :field="`[${index}].sinkMetaCnName`"
                      :rules="
                        index >= 0 && {
                          required: true,
                          validator: (...args) =>
                            checkCName500C(...args, null, null, {
                              nameType: 'CN',
                              name: row.sinkMetaCnName,
                              id: null,
                            }),
                          trigger: 'blur',
                        }
                      "
                      style="margin-top: 8px"
                    >
                      <n-input v-model="row.sinkMetaCnName" placeholder="" maxlength="30" />
                    </n-form-item>
                  </template>
                  <template #sinkMetaCode="{ row }">
                    <n-input
                      v-model="row.sinkMetaCode"
                      :class="!row.isPass ? 'required-input' : ''"
                      placeholder=""
                      :disabled="state.disabled"
                      maxlength="80"
                    />
                  </template>
                  <template #sinkMetaType="{ row }">
                    <n-select
                      v-model="row.sinkMetaType"
                      placeholder="请选择"
                      :disabled="state.disabled"
                      :key="state.tableRightKey"
                      @value-change="inputLengthWithFieldType(row)"
                    >
                      <n-option
                        v-for="item in state.fieldTypeOptions"
                        :key="item.name"
                        :name="item.cnName + '(' + item.name + ')'"
                        :value="item.name"
                      />
                    </n-select>
                  </template>
                  <template #sinkMetaLength="{ row, index }">
                    <n-input
                      v-model="row.sinkMetaLength"
                      :class="
                        row.sinkMetaLength &&
                        row.sinkMetaLength > 0 &&
                        row.sinkMetaLength.toString().indexOf('.') === -1
                          ? ''
                          : 'required-input'
                      "
                      type="number"
                      :key="state.lengthKey"
                      placeholder=""
                      :disabled="state.disabled || row.isRequiredFieldLength"
                      @blur="fieldBlur(row)"
                    />
                  </template>
                  <template #sinkMetaPrecision="{ row }">
                    <n-input
                      v-if="row.sinkMetaType === 'NUMBER'"
                      v-model="row.sinkMetaPrecision"
                      type="number"
                      :key="state.lengthKey"
                      placeholder=""
                    />

                    <span v-else>--</span>
                  </template>
                </CfTable>
              </n-form>
            </div>
            <div>
              <div style="display: inline-block">
                <div class="btn no-border" @click="createHiveDDL">
                  <SvgIcon class="icon" icon="code-icon" /> DDL
                </div>
              </div>
            </div>
          </div>
          <!-- 日志输出 -->
          <codemirror
            ref="myCm"
            v-if="state.hiveDDL?.length"
            v-model:value="state.hiveDDL"
            class="codemirror"
            :options="state.sqlOption"
            @ready="onCmReady"
            @focus="onCmFocus"
            @input="onCmCodeChange"
          />
        </div>
      </n-form>
    </div>
    <div class="config">
      <div class="config-btn" @click.prevent.stop="showConfigFn('attr', '属性配置')">属性配置</div>
      <div class="config-btn" @click.prevent.stop="showConfigFn('schedule', '调度配置')"
        >调度配置</div
      >
    </div>

    <!-- 属性配置弹窗 -->
    <section
      v-if="state.configPopupShow"
      :class="{
        'config-popup': true,
        attr: state.configPopupShowType === 'attr',
      }"
      @click.prevent.stop="preventFn"
    >
      <div class="config-popup-title">
        <span class="line"></span>
        <span class="name">{{ state.configPopupShowTypeTitle }}</span>
      </div>
      <div class="config-popup-content scroll-bar-style">
        <n-form :data="basicInfoBO" label-width="82px" message-type="text">
          <template v-if="state.configPopupShowType === 'attr'">
            <n-form-item field="name" label="作业名称：">
              <n-input v-model="basicInfoBO.name" placeholder="请输入作业名称" />
            </n-form-item>

            <n-form-item field="taskType" label="作业类型：">
              <n-select v-model="configInfoBO.taskType" :options="state.workOptions" disabled />
            </n-form-item>

            <n-form-item field="personInChargeName" label="责任人：">
              <n-input
                v-model="basicInfoBO.personInChargeName"
                disabled
                placeholder="请输入责任人"
              />
            </n-form-item>
            <n-form-item field="description" label="描述信息：">
              <n-textarea
                v-model="basicInfoBO.description"
                :autosize="{ minRows: 4, maxRows: 8 }"
                resize="both"
                placeholder="请输入描述信息"
              />
            </n-form-item>
          </template>
          <template v-if="state.configPopupShowType === 'schedule'">
            <n-form-item field="scheduleType" label="调度类型：">
              <n-select v-model="scheduleInfoBO.scheduleType" :options="state.scheduleOptions" />
            </n-form-item>
            <div class="content-title">调度依赖</div>
            <n-form-item field="scheduleRangeType" label="范围：">
              <n-select v-model="scheduleInfoBO.scheduleRangeType" :options="state.rangeOptions" />
            </n-form-item>
            <n-form-item field="prevTasks" label="上游作业：">
              <n-select
                v-model="scheduleInfoBO.prevTasks"
                :options="state.taskOptions"
                multiple
                @value-change="selectTaskFn"
              />
            </n-form-item>
            <div class="content-btn">
              <div class="btn active" @click.prevent.stop="initGraph">
                <SvgIcon class="icon" icon="icon-offline-visualization" />
                可视化配置
              </div>
            </div>
            <CfTable
              :tableConfig="{
                data: state.tableWorkData,
                rowKey: 'id',
              }"
              :table-head-titles="[
                { name: '上游作业', prop: 'name' },
                { name: '上游作业ID', prop: 'value' },
              ]"
            >
              <template #editor="{ row }">
                <div class="edit-box">
                  <n-button class="has-right-border" variant="text" @click.prevent="delWorkFn(row)"
                    >删除</n-button
                  >
                </div>
              </template>
            </CfTable>
          </template>
        </n-form>
      </div>
    </section>

    <n-modal
      v-model="state.showVisualization"
      title="可视化配置"
      class="largeDialog has-top-padding"
      width="720px"
      :close-on-click-overlay="false"
      @close="state.showVisualization = false"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-flow" id="flowContainer"> </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showVisualization = false">取消</n-button>
          <n-button @click.prevent="saveVisualizationFn">确定</n-button>
        </div>
      </template>
    </n-modal>

    <section
      v-loading="state.loading"
      v-show="state.logShow"
      element-loading-text="日志加载中..."
      :class="{ logPop: true, showRun: state.showRunLog }"
    >
      <div class="logPop-head">
        <div class="logTitle">运行日志</div>

        <div class="logPop-btn">
          <n-button
            color="primary"
            :disabled="!state.runLogText || state.loading"
            @click.prevent.stop="downLogFn"
            >下载日志</n-button
          >
          <SvgIcon
            :class="{ icon: true, show: state.showRunLog }"
            icon="icon-arrow-second"
            @click.prevent.stop="state.showRunLog = !state.showRunLog"
          />
        </div>
      </div>

      <div class="logText" v-if="state.showRunLog">
        <div v-if="state.runLogText !== ''" class="textarea" v-html="state.runLogText"></div>
        <div
          v-if="state.runLogText === '' || (state.runLogText === null && state.showRunLog)"
          class="empty"
        >
          <img class="empty-pic" src="@/assets/img/empty_gray.png" />
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { reactive, watch, defineEmits, onMounted, getCurrentInstance } from 'vue'
  import CfTable from '@/components/cfTable'
  import api from '@/api/index'
  import { Model } from '@antv/x6'
  import FlowGraph from './../graph'
  import { DagreLayout } from '@antv/layout'
  import { timestampToTime } from '@/const/public.js'
  import {
    checkCName500,
    checkCName500C,
    checkNameExcludeKeywords,
    onKeydownPositiveInteger,
    onKeyupPositiveInteger,
  } from '@/utils/validate'
  import { ElMessage, ElNotification } from 'element-plus'
  import { taskList, taskRun, taskStop, taskRunLog, taskRunResult } from '@/api/dataManage'
  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'
  const formRef = ref(null)
  const sourceTableRef = ref(null)
  const targetTableRef = ref(null)
  const columnMappingTableRef = ref(null)
  let runStatus = ref('waiting')
  const { proxy } = getCurrentInstance()

  const attrs = useAttrs()
  const state = reactive({
    lengthKey: 1,
    logShow: false,
    runLogText: '',
    showRunLog: false,
    isBtnDisble: false,
    nodeName: '',
    sourceFromCurrent: {},
    isEchoing: false, // 标志位：是否正在回显
    formData: {
      basicInfoBO: {
        description: '',
        id: 0,
        name: '',
        personInCharge: 0,
      },
      configInfoBO: {
        taskType: 'OW_DATA_ASYNC',
        dataAsyncTaskBO: {
          sinkDatasourceId: null,
          odsTable: false,
          overwriteTable: true,
          clearOldData: true,
          columnMapping: [],
          database: null,
          password: null,
          sinkDatabase: null,
          sinkHost: null,
          sinkJdbcDatabaseType: null,
          sinkPassword: null,
          sinkPort: null,
          sinkPostSqlList: [''],
          sinkPreSqlList: [''],
          sinkServiceName: null,
          sinkTableEnName: null,
          sinkTableName: null,
          sinkType: null,
          sinkUsername: null,
          sourceTableId: null,
          sourceTableName: null,
          sourceTableEnName: '',
          sourceType: null,
          username: null,
          executorMinMemory: 1,
          executorMaxMemory: 2,
        },
      },
      scheduleInfoBO: {
        id: attrs.taskId || null,
        scheduleType: null,
        prevTasks: [],
        scheduleRangeType: 'CURRENT_PROCESS',
      },
      id: attrs.taskId,
    },
    memoryOptions: [
      { name: '1G', value: 1 },
      { name: '2G', value: 2 },
      { name: '3G', value: 3 },
      { name: '4G', value: 4 },
      { name: '6G', value: 6 },
      { name: '8G', value: 8 },
    ],
    tableWorkData: [],
    processId: null,
    tableDataPreview: [],
    tableDataPreviewHeadTitles: [],
    modelList: [],
    // 数据源列表
    dataSourceList: [],
    metaDataModel: [],
    metaDataModelHead: [
      // 必须为name 否则渲染不出表头
      { prop: 'sinkMetaCnName', name: '字段中文名' },
      { prop: 'sourceColumnName', name: '字段英文名' },
      { prop: 'sourceColumnType', name: '字段类型' },
      { prop: 'sourceColumnLength', name: '字段长度', width: 180 },
    ],
    workOptions: [
      { name: 'PySpark', value: 'OW_PY_SPARK' },
      { name: 'HiveSQL', value: 'OW_HIVE_SQL' },
      { name: 'SparkSQL', value: 'OW_SPARK_SQL' },
      { name: 'Python', value: 'OW_PYTHON' },
      { name: 'Shell', value: 'OW_SHELL' },
      { name: '同步至资源库', value: 'OW_DATA_ASYNC' },
      { name: '非结构化数据分析', value: 'OW_DATA_UPLOAD' },
    ],
    scheduleOptions: [
      { name: '正常调度', value: 'DEFAULT' },
      { name: '暂停调度', value: 'PAUSE' },
      { name: '空跑', value: 'DRY_RUN' },
    ],
    rangeOptions: [
      { name: '当前工作流', value: 'CURRENT_PROCESS' },
      // { name: '当前项目', value: 'CURRENT_PROJECT' },
      // { name: '所有项目', value: 'TOTAL_PROJECT' },
    ],
    taskOptions: [],

    fieldTypeOptions: [],
    configPopupShowType: '',
    configPopupShowTypeTitle: '',
    configPopupShow: false,
    showVisualization: false,
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
  })

  const { dataAsyncTaskBO } = toRefs(state.formData.configInfoBO)
  const { basicInfoBO, configInfoBO, scheduleInfoBO } = toRefs(state.formData)

  const rules = {
    sinkTableEnName: [
      {
        required: true,
        validator: checkNameExcludeKeywords,
        trigger: 'blur',
      },
    ],
    sinkTableName: [
      {
        required: true,
        validator: (...args) =>
          checkCName500C(...args, null, null, {
            nameType: 'CN',
            name: dataAsyncTaskBO.value.sinkTableName,
            id: null,
          }),
        trigger: 'blur',
      },
    ],
    clearOldData: [
      {
        message: '请选择是否清空已有表数据',
        trigger: 'blur',
        type: 'bool',
      },
    ],
    sourceTableId: [
      {
        required: true,
        message: '请选择数据源表',
        trigger: 'blur',
        type: 'number',
      },
    ],
    sinkDatasourceId: [
      {
        required: true,
        message: '请选择目标数据源',
        trigger: 'blur',
        type: 'number',
      },
    ],
  }
  const emits = defineEmits(['changeLoadingStatus'])

  watch(
    runStatus,
    (newValue) => {
      emits('changeLoadingStatus', {
        taskId: attrs.taskId,
        runStatus: newValue,
      })
    },
    { deep: true },
  )

  // 下载日志
  const downLogFn = () => {
    if (state.runLogText) {
      state.runLogText = state.runLogText.replace(/<[^>]+>/g, '')
      const blob = new Blob([state.runLogText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        state.nodeName + '-' + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  //   获取离线作业生成的所有模型
  const getOfflineModel = (name) => {
    return api.offlineJob
      .getModelListV2({
        envType: 'TEST',
        name,
        odsTable: dataAsyncTaskBO.value.odsTable,
        checkUserAuthorize: true,
      })
      .then((res) => {
        state.modelList = res.data || []
        if (
          state.sourceFromCurrent?.id &&
          !state.modelList.find((item) => item.id === state.sourceFromCurrent?.id)
        ) {
          state.isBtnDisble = true
          state.modelList.push(state.sourceFromCurrent)
        }
        // !isNaN(dataAsyncTaskBO.value.sourceTableId) && dataSourceTableChange()
      })
  }

  // 数据同步使用的数据源列表
  const getAsyncDataSourceList = () => {
    api.offlineJob.getAsyncDataSourceList({}).then((res) => {
      state.tagerDataSourceList =
        res?.data?.map((_) => ({
          ..._,
          name: _?.name,
          value: _?.id,
        })) || []
    })
  }

  // 获取字段类型
  const getFieldTypeList = () => {
    state.fieldTypeOptions = {
      HIVE_DORIS: [
        { name: 'BOOLEAN', cnName: '布尔值' },
        { name: 'TINYINT', cnName: '短整型' },
        { name: 'SMALLINT', cnName: '整型' },
        { name: 'BIGINT', cnName: '长整型' },
        { name: 'DATETIME', cnName: '日期时间' },
        { name: 'FLOAT', cnName: '浮点数' },
        { name: 'DOUBLE', cnName: '浮点数' },
        { name: 'VARCHAR', cnName: '字符串' },
        { name: 'DATE', cnName: '日期' },
        { name: 'INT', cnName: '整型' },
        { name: 'CHAR', cnName: '字符串' },
        { name: 'DECIMAL', cnName: '高精度定点数' },
      ],
      HIVE_ORACLE: [
        { name: 'VARCHAR2', cnName: '字符串' },
        { name: 'NVARCHAR2', cnName: '字符串' },
        { name: 'CLOB', cnName: '字符串' },
        { name: 'NUMBER', cnName: '数字' },
        { name: 'INTEGER', cnName: '整数' },
        { name: 'INT', cnName: '整数' },
        { name: 'SMALLINT', cnName: '整数' },
        { name: 'TIMESTAMP', cnName: '时间戳' },
        { name: 'DATE', cnName: '日期' },
      ],
    }[dataSourceType.value]
  }
  const dataSourceTableChange = (init = true) => {
    init && (dataAsyncTaskBO.value.columnMapping = [])
    state.tableDataPreviewHeadTitles = []
    state.metaDataModel = []

    // 确保sourceTableId存在且modelList已加载
    if (!dataAsyncTaskBO.value.sourceTableId || !state.modelList?.length) {
      console.warn('数据源表ID或模型列表为空，无法进行数据源表变更')
      return
    }

    const { name, id, cnName } =
      state.modelList.find((_) => _.id === dataAsyncTaskBO.value.sourceTableId) || {}

    if (isNaN(id) || !id) {
      console.warn('未找到对应的模型数据，sourceTableId:', dataAsyncTaskBO.value.sourceTableId)
      return
    }

    // 获取模型的元数据和预览数据
    getMetaDataByModel(id)

    if (!init) return

    // 设置目标表相关信息
    dataAsyncTaskBO.value.sinkTableEnName = name
    dataAsyncTaskBO.value.sourceTableName = cnName
    dataAsyncTaskBO.value.sourceTableEnName = name
  }

  // 目标数据源切换
  const dataSourceChange = (item) => {
    dataSourceType.value = { DORIS: 'HIVE_DORIS', ORACLE: 'HIVE_ORACLE' }[item.datasourceType]
    dataAsyncTaskBO.value.columnMapping = []
    dataAsyncTaskBO.value.sinkJdbcDatabaseType = item.datasourceType
    dataAsyncTaskBO.value.sinkType = item.datasourceType
    sourceTableRef.value.clearSelection()
    getFieldTypeList()
  }
  //   查询数据源列表
  const getDataSourceList = () => {
    api.offlineJob
      .getDataSourceList({
        datasourceType: 'ORACLE',
      })
      .then((res) => {
        state.dataSourceList = res.data || []
      })
  }

  // 回显详情
  const getDetail = () => {
    return api.offlineJob.getOfflineJobTaskDetail({ id: state.formData.id }).then((res) => {
      if (res.success) {
        state.nodeName = res.data.name
        basicInfoBO.value = {
          description: res.data?.description,
          personInChargeName: res.data?.personInChargeName,
          name: res.data?.name,
          id: res.data?.id,
        }
        scheduleInfoBO.value = {
          id: attrs.taskId || null,
          scheduleType: res.data.scheduleType || null,
          prevTasks: res.data?.prevTasks || [],
          scheduleRangeType: 'CURRENT_PROCESS',
        }
        state.nodeCode = res.data?.nodeCode
        state.processId = res.data?.processId
        res.data?.versionBO?.dataAsyncTaskBO &&
          (dataAsyncTaskBO.value = res.data?.versionBO?.dataAsyncTaskBO)
        dataAsyncTaskBO.value.sinkPreSqlList ??= ['']
        dataAsyncTaskBO.value.sinkPostSqlList ??= ['']
        dataAsyncTaskBO.value.odsTable = res.data?.versionBO?.dataAsyncTaskBO?.odsTable || false
        state.sourceFromCurrent = {
          id: res.data?.versionBO?.dataAsyncTaskBO?.sourceTableId,
          name: res.data?.versionBO?.dataAsyncTaskBO?.sourceTableEnName,
          cnName: res.data?.versionBO?.dataAsyncTaskBO?.sourceTableName,
        }
        dataAsyncTaskBO.value.executorMinMemory =
          res.data?.versionBO?.dataAsyncTaskBO?.executorMinMemory || 1
        dataAsyncTaskBO.value.executorMaxMemory =
          res.data?.versionBO?.dataAsyncTaskBO?.executorMaxMemory || 2
        dataSourceType.value = {
          DORIS: 'HIVE_DORIS',
          ORACLE: 'HIVE_ORACLE',
        }[dataAsyncTaskBO.value?.sinkType]
        getFieldTypeList()
        // 确保在获取模型列表后再进行数据源表变更，以保证回显数据正确
        if (dataAsyncTaskBO.value.odsTable) {
          getOfflineModel('').finally(() => {
            // 确保有选中的数据源表ID时才调用dataSourceTableChange
            if (dataAsyncTaskBO.value.sourceTableId && state.modelList?.length > 0) {
              dataSourceTableChange(false)
            }
          })
        } else {
          // 对于非ODS表，也需要确保先获取模型列表
          getOfflineModel('').finally(() => {
            if (dataAsyncTaskBO.value.sourceTableId && state.modelList?.length > 0) {
              dataSourceTableChange(false)
            }
          })
        }
      }
    })
  }
  const getMetaDataByModel = (id) => {
    if (!id) {
      console.warn('getMetaDataByModel: 模型ID为空')
      return
    }

    // 获取模型元数据
    api.model
      .getModeData({ id })
      .then((res) => {
        let { data, success } = res
        if (success && data) {
          state.tableDataPreviewHeadTitles = data || []

          state.metaDataModel =
            data?.map((_) => {
              return {
                sourceColumnLength: _.fieldLength,
                sourceColumnName: _.name,
                sourceColumnType: _.fieldType,
                sinkMetaCnName: _.cnName,
                sourceSortNum: _.sortNum,
                sourceIndex: _.sortNum,
              }
            }) || []

          console.log('成功获取模型元数据，字段数量:', state.metaDataModel.length)

          // 如果是回显场景且有已保存的字段映射，恢复选中状态
          if (dataAsyncTaskBO.value.columnMapping?.length > 0) {
            nextTick(() => {
              // 设置回显标志位，防止触发selectionChange
              state.isEchoing = true

              let rawData = JSON.parse(JSON.stringify(dataAsyncTaskBO.value.columnMapping))
              rawData.forEach((item) => {
                let result = state.metaDataModel.find(
                  (row) => row.sourceColumnName === item.sourceColumnName,
                )
                if (result && sourceTableRef.value) {
                  sourceTableRef.value.toggleRowSelection(result, true)
                }
              })

              // 回显完成后，重置标志位
              nextTick(() => {
                state.isEchoing = false
              })
            })
          }
        } else {
          console.warn('获取模型元数据失败或数据为空')
        }
      })
      .catch((error) => {
        console.error('获取模型元数据出错:', error)
      })

    // 获取模型预览数据
    api.model
      .getDataWithProject({
        condition: {
          id,
          limitPreview: 10,
        },
        pageNum: 1,
        pageSize: 10,
      })
      .then((res) => {
        let { success, data } = res
        if (success && data?.list) {
          state.tableDataPreview = data.list
          console.log('成功获取预览数据，记录数量:', data.list.length)
        } else {
          console.warn('获取预览数据失败或数据为空')
          state.tableDataPreview = []
        }
      })
      .catch((error) => {
        console.error('获取预览数据出错:', error)
        state.tableDataPreview = []
      })
  }

  // 长度输入框验证是数字
  const fieldBlur = (item) => {
    item.sinkMetaLength = Number(item.sinkMetaLength.toString().replace(/\D/g, ''))
    state.lengthKey++
  }

  //根据字段类型设置长度精度
  const inputLengthWithFieldType = (item, flag = false) => {
    switch (item.sinkMetaType) {
      case 'DATETIME':
      case 'TIMESTAMP':
      case 'DATE':
        item.sinkMetaLength = null
        break
      case 'DOUBLE':
      case 'FLOAT':
      case 'BIGINT':
      case 'INT':
      case 'TINYINT':
      case 'SMALLINT':
      case 'NUMBER':
      case 'INTEGER':
      case 'DECIMAL':
        if (flag) {
          if (item.sourceColumnLength) {
            item.sinkMetaLength = item.sourceColumnLength
          } else {
            item.sinkMetaLength = 37
          }
        } else {
          item.sinkMetaLength = 37
        }
        break
      case 'CHAR':
      case 'VARCHAR':
      case 'VARCHAR2':
        if (flag) {
          if (item.sourceColumnLength) {
            item.sinkMetaLength = item.sourceColumnLength
          } else {
            item.sinkMetaLength = 255
          }
        } else {
          item.sinkMetaLength = 255
        }
        break
    }
  }

  // 提交作业
  const submitJob = async () => {
    const result = await saveJob(true)
    if (!result) {
      return false
    }
    Promise.all([formRef.value.validate(), columnMappingTableRef.value.validate()])
      .then((values) => {
        values.includes(true) &&
          api.offlineJob.commitJob({ id: attrs.taskId }).then((res) => {
            if (res.success) {
              ElMessage.success('提交成功')
            }
          })
      })
      .catch((error) => {})
  }
  // 停止运行
  const stopFn = (flag = true) => {
    taskStop({ id: state.formData?.id })
      .then((res) => {
        if (res.success) {
          if (flag) {
            ElNotification({
              title: '提示',
              message: '停止运行成功',
              type: 'success',
            })
          }
        }
        state.loading = false
        state.sqlOption.readOnly = false
        state.logLoading = false
        state.resultLoading = false
        state.isRun = false
      })
      .catch(() => {
        state.loading = false
        state.sqlOption.readOnly = false
        state.logLoading = false
        state.resultLoading = false
        state.isRun = false
      })
  }
  // 运行
  const runFn = async () => {
    // 1. 立即存 session，标记为等待保存
    sessionStorage.setItem(
      'dataAsyncPollingSession_' + state.formData.id,
      JSON.stringify({
        runStatus: 'WAITING_SAVE',
        runInstanceId: null,
      }),
    )

    state.logShow = true
    let data = {
      id: state.formData?.id || null,
      scheduleType: scheduleInfoBO.value?.scheduleType || null,
      nodeCode: state.nodeCode || null,
      nodeName: basicInfoBO.value.name || null,
      executeTime: timestampToTime(new Date().getTime(), true),
      ...configInfoBO.value,
    }

    const result = await saveJob(true)
    if (!result) {
      return false
    }

    api.offlineJob.getAsyncDataSyncRename(data).then((res) => {
      // 提示表名重复
      // res?.data?.tableRepeat && ElMessage.error('目标表表名重复')
      if (res?.data?.tableRepeat) {
        proxy.$MessageBoxService.open({
          title: '同步提示',
          content: `目标表已存在，确认同步至该表？`,
          save: () => {
            runningFn()
          },
          cancel: () => {
            runStatus.value = ''
            state.loading = false
            state.sqlOption.readOnly = false
            state.logLoading = false
            state.resultLoading = false
            state.isRun = false
            sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
          },
        })
      } else {
        runningFn()
      }
    })

    function runningFn() {
      state.loading = true
      state.isRun = true
      runStatus.value = 'running'
      taskRun(data)
        .then((res) => {
          if (res.success && state.isRun) {
            state.dsProcessCode = res.data.dsProcessCode || null
            state.runCode = res.data.runId || null
            state.runTime = res.data.runTime || null

            state.showRunLog = true
            sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
            res.data.submitAndRunResultState === 'SUCCESS'
              ? taskRunLogFn({
                  dsProcessCode: res.data.dsProcessCode,
                  runId: res.data.id,
                  nodeCode: res.data.nodeCode,
                  executeTime: res.data.executeTime,
                })
              : taskRunLogErrorFn(res.data.runInstanceId)
          } else {
            state.loading = false
            state.sqlOption.readOnly = false
            state.logLoading = false
            state.resultLoading = false
            state.isRun = false
            runStatus.value = 'running'
          }
        })
        .catch(() => {
          runStatus.value = 'running'
        })
        .finally(() => {
          state.loading = false
          state.sqlOption.readOnly = false
          state.logLoading = false
          state.resultLoading = false
          state.isRun = false
        })
    }
  }

  // 运行日志
  const taskRunLogFn = async (data) => {
    const res = await taskRunResult(data)
    state.loading = true

    if (res.code === 'SUCCESS') {
      state.runLogText = res.data.log

      switch (res.data.state) {
        case 'FAILURE':
          runStatus.value = 'fail'
          ElNotification({
            title: '提示',
            message: '运行失败',
            type: 'error',
          })
          state.loading = false
          sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
          break
        case 'RUNNING_EXECUTION':
          runStatus.value = 'running'
          // 存储轮询状态
          sessionStorage.setItem(
            'dataAsyncPollingSession_' + state.formData.id,
            JSON.stringify({
              runStatus: 'RUNNING_EXECUTION',
              runInstanceId: data.runId, // 这里是 taskRunLogFn 的参数
            }),
          )
          state.stateFlag = setTimeout(() => {
            taskRunLogFn(data)
          }, 3000)
          break
        case 'SUCCESS':
          runStatus.value = 'success'
          ElMessage.success('运行成功')
          state.loading = false
          sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
          break
        default:
          state.loading = false
          break
      }
    } else {
      runStatus.value = 'fail'
      state.loading = false
      state.runLogText += '<div class="red">运行失败</div>'
    }
  }

  // 运行日志
  const taskRunLogErrorFn = async (runInstanceId) => {
    const res = await taskRunLog({ runInstanceId })
    state.showRunLog = true
    state.loading = true

    if (res.code === 'SUCCESS') {
      state.runLogText = res.data.log

      switch (res.data.state) {
        case 'FAILURE':
          runStatus.value = 'fail'
          ElNotification({
            title: '提示',
            message: '运行失败',
            type: 'error',
          })
          state.loading = false
          sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
          break
        case 'RUNNING_EXECUTION':
          runStatus.value = 'running'
          // 存储轮询状态
          sessionStorage.setItem(
            'dataAsyncPollingSession_' + state.formData.id,
            JSON.stringify({
              runStatus: 'RUNNING_EXECUTION',
              runInstanceId, // 这里是 taskRunLogFn 的参数
            }),
          )
          state.stateFlag = setTimeout(() => {
            taskRunLogErrorFn(runInstanceId)
          }, 3000)
          break
        case 'SUCCESS':
          runStatus.value = 'success'
          ElMessage.success('运行成功')
          state.loading = false
          sessionStorage.removeItem('dataAsyncPollingSession_' + state.formData.id)
          break
        default:
          break
      }
    } else {
      runStatus.value = 'fail'
      state.loading = false
      state.runLogText += '<div class="red">运行失败</div>'
    }
  }

  // 保存作业
  const saveJob = async (isRun = false) => {
    if (state.formData.configInfoBO.dataAsyncTaskBO.columnMapping.length === 0) {
      ElNotification({
        title: '提示',
        message: '请配置同步字段',
        type: 'error',
      })
      return false
    }
    let flag = false
    let result = await Promise.all([
      formRef.value.validate(),
      columnMappingTableRef.value.validate(),
    ])
    if (dataAsyncTaskBO.value.executorMinMemory > dataAsyncTaskBO.value.executorMaxMemory) {
      ElNotification({
        title: '提示',
        message: '最大内存不能小于最小内存！',
        type: 'error',
      })
      result = false
    }
    if (result) {
      let data = { ...state.formData }
      data.configInfoBO.dataAsyncTaskBO.sinkJdbcDatabaseType =
        dataAsyncTaskBO.value.sinkJdbcDatabaseType
      data.configInfoBO.dataAsyncTaskBO.sinkType = dataAsyncTaskBO.value.sinkType
      if (data.scheduleInfoBO.prevTasks.length > 0) {
        data.scheduleInfoBO.prevTasksInfo = []
        state.tableWorkData.forEach((val) => {
          data.scheduleInfoBO.prevTasksInfo.push({
            nodeCode: val.value,
          })
        })
      }

      await api.offlineJob.saveJob(data).then((res) => {
        if (res.success) {
          if (isRun) {
            flag = true
          } else {
            ElMessage.success('保存成功')
          }
        }
      })
    }
    return flag
  }
  const dataSourceType = ref('HIVE_DORIS')
  const selectionChange = () => {
    // 如果正在回显，不触发selectionChange逻辑
    if (state.isEchoing) {
      return
    }

    const sortedData = sourceTableRef.value.getSelectionRows().map((_, i) => ({
      ..._,
      sinkMetaCode: _?.sourceColumnName?.length ? _.sourceColumnName : 'col_' + (i + 1),
      sinkMetaType: _?.sourceColumnType || 'STRING',
      sinkMetaLength: { HIVE_DORIS: 255, HIVE_ORACLE: '' }[dataSourceType.value],
    }))

    const list = sortedData.sort((a, b) => a.sourceSortNum - b.sourceSortNum)

    api.offlineJob
      .convertField({
        convertList: list?.map((_) => ({ fileType: _?.sinkMetaType })) || [],
        dataSourceType: dataSourceType.value,
      })
      .then((res) => {
        if (res.success) {
          dataAsyncTaskBO.value.columnMapping = list?.map((_, i) => {
            return {
              ..._,
              sinkMetaType: res?.data?.[i]?.convertFileType,
            }
          })
          dataAsyncTaskBO.value.columnMapping.forEach((val) => {
            inputLengthWithFieldType(val, true)
          })
        }
      })
  }

  // 创建HIVE表DDL
  const createHiveDDL = () => {
    const params = {
      sinkTableName: dataAsyncTaskBO.value.sinkTableName,
      sinkTableEnName: dataAsyncTaskBO.value.sinkTableEnName,
      columnMapping: dataAsyncTaskBO.value.columnMapping,
      sinkType: { HIVE_DORIS: 'DORIS', HIVE_ORACLE: 'ORACLE' }[dataSourceType.value],
    }
    api.offlineJob.syncToDDL(params).then((res) => {
      if (res.success) {
        state.hiveDDL = res.data
      }
    })
  }
  // 弹窗
  const showConfigFn = (type, title) => {
    if (state.configPopupShowType === type && state.configPopupShow) {
      state.configPopupShow = false
    } else {
      state.configPopupShowType = type
      state.configPopupShowTypeTitle = title
      state.configPopupShow = true
      getTaskListFn()
    }

    return false
  }
  // 获取上游作业列表
  const getTaskListFn = () => {
    taskList({ processId: state.processId, excludeDDL: true }).then((res) => {
      if (res.success) {
        state.taskOptions = res.data
          .filter((item) => item.nodeCode !== state.nodeCode)
          .map((val) => {
            return { ...val, value: val.nodeCode }
          })
        let list = state.taskOptions.filter((val) =>
          scheduleInfoBO.value.prevTasks.some((code) => code === val.value),
        )
        selectTaskFn(list)
      }
    })
  }
  // 选择上游作业
  const selectTaskFn = (e) => {
    state.tableWorkData = e
  }
  // 删除上游作业
  const delWorkFn = (item) => {
    scheduleInfoBO.value.prevTasks = scheduleInfoBO.value.prevTasks.filter(
      (val) => val !== item.value,
    )
    state.tableWorkData = state.tableWorkData.filter((val) => val.value !== item.value)
  }
  // 通过作业类型判断节点
  const getNodeByType = (jobType) => {
    if (jobType === 'OW_DATA_ASYNC') {
      return 'dataAsync'
    } else if (jobType === 'OW_PY_SPARK') {
      return 'PySpark'
    } else if (jobType === 'OW_HIVE_SQL') {
      return 'HiveSQL'
    } else if (jobType === 'OW_SPARK_SQL') {
      return 'SparkSQL'
    } else if (jobType === 'OW_PYTHON') {
      return 'Python'
    } else if (jobType === 'OW_SHELL') {
      return 'Shell'
    } else if (jobType === 'OW_HIVE_DDL') {
      return 'HiveDDL'
    }
  }
  // 初始化画布
  const initGraph = () => {
    state.showVisualization = true
    nextTick(() => {
      const graph = FlowGraph.init()
      let data = (Model.FromJSONData = {
        nodes: [],
        edges: [],
      })
      state.tableWorkData.forEach((item) => {
        data.edges.push({
          shape: 'edge',
          data: { ...item },
          source: { cell: String(item.value), port: 'bottom' },
          target: { cell: String(state.nodeCode), port: 'top' },
          attrs: {
            line: {
              stroke: '#8091B7',
              strokeWidth: 1,
              targetMarker: {
                name: 'classic',
                size: 8,
              },
            },
          },
          connector: 'normal',
          // router: {
          //   name: 'normal',
          // },
          zIndex: 0,
        })
      })
      state.tableWorkData.forEach((item) => {
        item.id = String(item.value)
        data.nodes.push({
          id: item.id,
          data: {
            ...item,
            isVisualization: true,
          },
          size: { width: 256, height: 44 },
          shape: getNodeByType(item.taskType),
          ports: {
            groups: {
              top: {
                id: 'top',
                position: 'top',
                attrs: {
                  circle: {
                    r: 3,
                    magnet: true,
                    stroke: '#8091B7',
                    strokeWidth: 1,
                    fill: '#fff',
                    style: {
                      visibility: 'hidden',
                    },
                  },
                },
              },
              bottom: {
                id: 'bottom',
                position: 'bottom',
                attrs: {
                  circle: {
                    r: 3,
                    magnet: true,
                    stroke: '#8091B7',
                    strokeWidth: 1,
                    fill: '#fff',
                    style: {
                      visibility: 'hidden',
                    },
                  },
                },
              },
            },
            items: [
              {
                id: 'top',
                group: 'top',
              },
              {
                id: 'bottom',
                group: 'bottom',
              },
            ],
          },
          zIndex: 1,
        })
      })
      data.nodes.push({
        id: String(state.nodeCode),
        data: {
          id: attrs.taskId,
          value: state.nodeCode,
          name: basicInfoBO.value.name,
          taskType: configInfoBO.value.taskType,
          processId: state.processId,
          isVisualization: true,
        },
        size: { width: 256, height: 44 },
        shape: getNodeByType(configInfoBO.value.taskType),
        ports: {
          groups: {
            top: {
              id: 'top',
              position: 'top',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#8091B7',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
            bottom: {
              id: 'bottom',
              position: 'bottom',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#8091B7',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
          },
          items: [
            {
              id: 'top',
              group: 'top',
            },
            {
              id: 'bottom',
              group: 'bottom',
            },
          ],
        },
        zIndex: 1,
      })
      const dagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: 'TB',
        align: undefined,
        ranksep: 20,
        nodesep: 20,
        controlPoints: true,
      })
      const model = dagreLayout.layout(data)
      graph.fromJSON(model)
      if (data.nodes.length > 3) {
        graph.scale(0.6)
      } else {
        graph.scale(0.8)
      }

      graph.centerContent()
    })
  }

  // 保存可视化
  const saveVisualizationFn = () => {
    const { graph } = FlowGraph
    let json = graph.toJSON()
    let nodes = json.cells.filter((val) => val.shape !== 'edge')
    let edges = json.cells.filter((val) => val.shape === 'edge')
    let list = nodes
      .filter((val) => edges.some((item) => item.source.cell === val.id))
      .map((v) => {
        return {
          ...v.data,
        }
      })
    scheduleInfoBO.value.prevTasks = list.map((val) => Number(val.id))
    selectTaskFn(list)
    state.showVisualization = false
  }
  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.hiveDDL = newCode
  }
  // SQL语句获取焦点时
  const onCmFocus = (cm) => {
    state.codemirror = cm
    state.codemirror.setOption('readOnly', true)
  }
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      state.codemirror.showHint(config)
    })
    state.codemirror.setValue(state.hiveDDL)
    state.codemirror.setOption('readOnly', true)
  }

  onMounted(() => {
    getAsyncDataSourceList()

    getDetail().finally(() => {
      // 这里移动 pollingSession 相关逻辑
      let pollingSession = sessionStorage.getItem('dataAsyncPollingSession_' + state.formData.id)
      if (pollingSession) {
        let sessionData = JSON.parse(pollingSession)
        if (sessionData.runStatus === 'WAITING_SAVE') {
          runFn()
        } else if (sessionData.runStatus === 'RUNNING_EXECUTION' && sessionData.runInstanceId) {
          // 自动恢复轮询
          taskRunLogFn(sessionData.runInstanceId)
        }
      }
      // 移除这里的getOfflineModel调用，因为在getDetail中已经处理了
      // getOfflineModel()
    })
    getDataSourceList()
    getFieldTypeList()
  })
</script>
<style lang="scss" scoped>
  .content {
    position: relative;
    width: 100%;
    height: 100%;
    padding-bottom: 58px;
    background-color: #fafafa;

    .toolbar {
      display: flex;
      flex-shrink: 0;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 8px;
      background-color: #fff;
      border-bottom: 1px solid #dcdfe6;

      &-box {
        display: flex;
        gap: 16px;
        align-items: center;
      }

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        box-sizing: border-box;
        padding: 4px 16px;
        color: #1e89ff;
        font-size: 14px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;
        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }
      }
    }

    .page-content {
      height: calc(100% - 92px);
      margin-right: 48px;
      overflow-y: auto;
      background-color: #fff;
      border-right: 1px solid var(---, #dcdfe6);

      .coverMode {
        display: flex;
        gap: 10px;
      }

      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 7px 7px 7px 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        line-height: 24px;
        background-color: #fff;

        &[sub-label]::after {
          display: inline-block;
          margin-left: 12px;
          padding: 0px 4px;
          color: #1aa4ee;
          font-weight: 400;
          font-size: 12px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 22px;
          background: rgba(26, 164, 238, 0.08);
          border: 1px solid rgba(26, 164, 238, 0.4);
          border-radius: 2px;
          content: attr(sub-label);
          /* 166.667% */
        }

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
    }

    .page-content {
      .data-async {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;

        .content-title {
          position: relative;
          width: 100%;
          height: 30px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }

        .data-source {
          display: flex;
          flex: 1 0 0;
          gap: 6px;
          align-items: center;
          width: 100%;
          height: 32px;
          padding: 5px 6px 5px 10px;
          overflow: hidden;
          color: var(----, #1d2129);
          font-weight: 400;
          font-size: 14px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 32px;
          white-space: nowrap;
          text-overflow: ellipsis;
          background: var(---, #f5f7fa);
          border: 1px solid var(---, #e5e6eb);
          border-radius: 2px;
          /* 157.143% */
        }
        .source-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          position: relative;
          .nancalui-select:first-of-type {
            flex-shrink: 0;
            width: 90px;
            :deep(.nancalui-select__selection) {
              border-right: none;
              border-radius: 0;
            }
            .el-select__wrapper {
              border-radius: 4px 0 0 4px;
            }
          }
          .el-select {
            width: calc(100% - 90px);
            .el-select__wrapper {
              border-radius: 0 4px 4px 0;
            }
          }
        }

        .form-row {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          width: 100%;
          margin-bottom: -8px;
          column-gap: 48px;
        }
      }

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 4px 16px;
        color: var(---, #1e89ff);
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 22px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;
        /* 157.143% */

        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }

        &.no-border {
          color: var(---, #1e89ff);
          background: transparent;
          border: none;
        }
      }

      .log {
        display: flex;
        flex: 1 0 0;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
        align-self: stretch;
        padding: 8px;
        white-space: pre-wrap;
        border: 1px solid var(---, #e5e6eb);
        border-radius: 2px;
      }

      .table-row {
        width: 100%;

        .form-table {
          display: flex;
          gap: 48px;

          .table-row-list,
          form {
            width: calc(50% - 24px);
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }

    .config {
      position: absolute;
      top: 92px;
      right: 0;
      z-index: 999;
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
      align-self: stretch;
      width: 48px;
      padding: 12px 8px;
      // border-left: 1px solid #dcdfe6;
      // background: #fafafa;

      &-btn {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        width: 32px;
        padding: 12px 4px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        text-align: center;
        background: #fff;
        border: 1px solid #c9cdd4;
        border-radius: 2px;
        cursor: pointer;
      }
    }

    .config-popup {
      position: absolute;
      top: 0;
      right: 48px;
      z-index: 999;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      align-items: center;
      width: 460px;
      height: 100%;
      background: #fff;
      box-shadow: -8px 0 24px -2px rgba(30, 47, 85, 0.1);

      &.attr {
        width: 400px;
      }

      &.version {
        width: 560px;
      }

      &-title {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        align-self: stretch;
        height: 48px;
        padding: 12px 20px 12px 0;
        border-bottom: 1px solid #f5f7fa;

        .line {
          width: 4px;
          height: 18px;
          margin-right: 12px;
          background: #1e89ff;
        }

        .name {
          color: rgba(0, 0, 0, 0.9);
          font-weight: 500;
          font-size: 16px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 24px;
        }
      }

      &-content {
        box-sizing: border-box;
        width: 100%;
        height: calc(100% - 48px);
        padding: 16px;
        overflow-y: auto;

        .nancalui-form {
          width: 100%;
        }

        .content-title {
          position: relative;
          height: 30px;
          margin-bottom: 16px;
          padding-left: 13px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 3px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }

        .content-btn {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .btn {
            display: flex;
            gap: 4px;
            align-items: center;
            box-sizing: border-box;
            padding: 4px 16px;
            color: #1e89ff;
            font-size: 14px;
            border: 1px solid var(---, #1e89ff);
            border-radius: 2px;

            .icon {
              font-size: 14px;
            }

            &.active,
            &:hover {
              color: #fff;
              background: #1e89ff;
            }
          }

          &-title {
            color: #1d2129;
            font-weight: 500;
            font-size: 14px;

            span {
              color: #909399;
              font-weight: normal;
            }
          }
        }

        .refer-table {
          box-sizing: border-box;
          height: 330px;
          margin-bottom: 10px;
          padding: 8px;
          overflow-y: auto;
          border: 1px solid var(---, #e5e6eb);
          border-radius: 2px;

          &-back {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64px;
            height: 32px;
            color: #6e9eff;
            border-radius: 2px;
            cursor: pointer;

            &:hover {
              color: #fff;
              background-color: #1e89ff;
            }

            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
          }

          &-name {
            padding: 0 16px;
            overflow: hidden;
            color: #1d2129;
            font-weight: 500;
            font-size: 14px;
            line-height: 32px;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }

          .label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            width: 100%;
            height: 32px;
            margin-bottom: 4px;
            padding: 0 8px;
            border-radius: 2px;
            cursor: pointer;

            &:hover {
              background-color: #ebf4ff;

              .icon {
                display: block;
              }

              .label-name {
                color: #1e89ff;
              }
            }

            &-name {
              width: calc(100% - 24px);
              overflow: hidden;
              color: #606266;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;

              &.field {
                color: rgba(0, 0, 0, 0.75);
              }
            }

            .icon {
              display: none;
              color: #1e89ff;
              font-size: 16px;

              &:hover {
                color: #1e89ff;
              }
            }
          }
        }
      }
    }
  }

  .modal-body {
    &-header {
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .col {
          display: flex;
          flex: 1;
          flex-shrink: 0;
          align-items: center;
          justify-content: flex-start;
          height: 22px;
          color: #606266;
          font-size: 14px;
          line-height: 22px;

          .name {
            max-width: 80px;
          }

          &:last-child {
            flex: none;
            width: 210px;
          }

          .value {
            width: calc(100% - 80px);
            overflow: hidden;
            color: #1d2129;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    &-content {
      box-sizing: border-box;
      height: 320px;
      padding: 8px 6px 8px 10px;
      overflow-y: auto;
      border: 1px solid var(---, #e5e6eb);
      border-radius: 2px;

      &-box {
        height: 270px;
        color: #1d2129;
        font-size: 14px;
        line-height: 22px;

        :deep(.nancalui-textarea__div) {
          .nancalui-textarea {
            padding: 0;
            color: rgba(0, 0, 0, 0.75);
            background-color: transparent;
            border: 1px solid #fff;

            &:hover {
              border: 1px solid #fff !important;
              box-shadow: none !important;
            }
          }
        }
      }

      &-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 30px;

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 30px;
          color: #1e89ff;
          font-size: 14px;
          text-align: center;
          border-radius: 2px;
          cursor: pointer;

          .icon {
            margin-right: 4px;
            font-size: 14px;
          }

          &:hover {
            color: #fff;
            background: #1e89ff;
          }
        }
      }
    }

    &-flow {
      box-sizing: border-box;
      height: 524px;
      padding: 16px;
    }
  }

  .codemirror {
    height: 400px !important;
    background-color: #fff;

    :deep(.CodeMirror) {
      height: 100% !important;
      overflow: hidden;
      box-shadow: none;
    }

    :deep(.CodeMirror-scroll) {
      box-sizing: border-box;
      height: 100%;
      margin-right: -6px;
      padding: 0;
      overflow-x: hidden !important;
      overflow-y: auto !important;

      .CodeMirror-sizer {
        border-right: none;

        .CodeMirror-lines {
          padding: 0;

          .CodeMirror-selected {
            height: 28px !important;
          }
        }

        .CodeMirror-cursors {
          top: 5px;
        }

        .CodeMirror-code > div {
          padding: 5px 0;
        }

        .CodeMirror-linenumber {
          padding: 0 6px;
          text-align: center;
        }

        .CodeMirror-line {
          padding: 0 10px;

          > span {
            padding-right: 10px !important;
            color: #046c5c;
            font-size: 14px;
            word-break: break-all;
          }
        }

        .CodeMirror-linebackground {
          background-color: #f0f2f5;
        }
      }
    }

    :deep(.CodeMirror-gutters) {
      width: 32px;
      min-height: 100%;
      background-color: #e6e8eb;
      border-right: none;
    }

    :deep(.CodeMirror-vscrollbar) {
      visibility: initial !important;

      &::-webkit-scrollbar-thumb {
        background-color: #b1bcd6;
        border-radius: 6px;

        &:hover {
          background-color: #b1bcd6;
        }
      }
    }
  }

  .logPop {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 200;
    box-sizing: border-box;
    width: 100%;
    height: 56px;
    background: #fff;

    &.showRun {
      height: 400px;
    }

    &-head {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      padding: 8px 16px 0;
      background: linear-gradient(180deg, #f3f3f5 0%, #fff 100%);

      .logTitle {
        width: 106px;
        height: 40px;
        color: #1e89ff;
        font-weight: bold;
        line-height: 40px;
        text-align: center;
        background: #fff;
        border: 1px solid #e5e6eb;
        border-bottom: none;
      }

      .logPop-btn {
        .icon {
          margin-left: 10px;
          font-size: 16px;

          &.show {
            transform: rotate(180deg);
          }
        }
      }
    }

    .logText {
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 56px);
      margin-top: 8px;
      padding: 16px;
      overflow-y: auto;
      word-break: break-all;
      background: linear-gradient(181deg, #f3f3f5 0.86%, #fff 11.07%);

      .textarea {
        min-height: 100%;
        padding: 16px;
        color: #000000bf;
        white-space: pre-wrap;
        word-break: break-all;
        background-color: transparent;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        &-pic {
          width: 48px;
          height: 48px;
        }
        &-text {
          margin-top: 16px;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
</style>
<style lang="scss">
  .page-content .page-top .table-top {
    display: flex;
    flex: 1 0 0;
    gap: 4px;
    align-items: center;

    margin-bottom: 8px;
    color: var(----, #606266);
    font-weight: 400;
    font-size: 14px;
    font-family: 'Source Han Sans CN';
    font-style: normal;
    line-height: 22px;

    .label {
      display: flex;
      gap: 4px;
      align-items: center;
      padding: 5px 8px;
    }

    .value {
      display: block;
      flex: 1 0 0;
      gap: 16px;
      align-items: center;
      overflow: hidden;
      color: var(----, #1d2129);
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
