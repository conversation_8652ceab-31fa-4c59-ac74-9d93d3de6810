<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>线框/基础指标@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0.5" fill="none" fill-rule="evenodd">
        <g id="指标管理-新建复合指标-编辑表达式" transform="translate(-747.000000, -220.000000)">
            <g id="编组-13备份-3" transform="translate(720.000000, 0.000000)">
                <g id="穿梭框（左）" transform="translate(16.000000, 68.000000)">
                    <g id="选项/默认备份" transform="translate(0.000000, 145.000000)">
                        <g id="线框/基础指标" transform="translate(12.000000, 8.000000)">
                        
                            <rect id="矩形" stroke="#447DFD" fill="#F0F7FF" fill-rule="nonzero" x="0" y="0" width="16" height="16" rx="4"></rect>
                            <g id="基" transform="translate(2.636000, 2.184000)" fill="#447DFD" fill-rule="nonzero">
                                <path d="M0.324,0.912 L0.324,1.644 L2.448,1.644 L2.448,5.616 L0.144,5.616 L0.144,6.384 L2.64,6.384 C2.064,7.092 1.188,7.668 0,8.124 L0.444,8.856 C1.836,8.208 2.844,7.38 3.468,6.384 L7.236,6.384 C7.86,7.38 8.916,8.196 10.404,8.82 L10.752,8.076 C9.552,7.668 8.652,7.104 8.076,6.384 L10.572,6.384 L10.572,5.616 L8.268,5.616 L8.268,1.644 L10.392,1.644 L10.392,0.912 L8.268,0.912 L8.268,0 L7.428,0 L7.428,0.912 L3.288,0.912 L3.288,0 L2.448,0 L2.448,0.912 L0.324,0.912 Z M3.288,5.616 L3.288,4.752 L7.428,4.752 L7.428,5.616 L3.288,5.616 Z M3.288,4.068 L3.288,3.216 L7.428,3.216 L7.428,4.068 L3.288,4.068 Z M3.288,2.532 L3.288,1.644 L7.428,1.644 L7.428,2.532 L3.288,2.532 Z M4.944,6.804 L4.944,7.764 L2.484,7.764 L2.484,8.532 L4.944,8.532 L4.944,9.804 L0.744,9.804 L0.744,10.608 L9.9,10.608 L9.9,9.804 L5.784,9.804 L5.784,8.532 L8.22,8.532 L8.22,7.764 L5.784,7.764 L5.784,6.804 L4.944,6.804 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>