import Layout from '@/layout'

// 功能模块-首页
export default [
  {
    path: '/pictureQuery',
    component: Layout,
    redirect: '/pictureQuery/home',
    name: 'pictureQuery',
    code: 'pictureQuery',
    meta: {
      title: '图查询',
      icon: 'icon-quality',
      parentRouterName: 'pictureQuery',
      code: 'pictureQuery',
    },
    children: [
      {
        path: 'home',
        name: 'pictureQueryHome',
        code: 'pictureQueryHome',
        hidden: true,
        meta: {
          title: '图查询',
          icon: 'icon-quality',
          parentRouterName: 'pictureQuery',
          code: 'pictureQuery',
        },
        component: () => import('@/views/pictureQuery/index'),
      },
    ],
  },
]
