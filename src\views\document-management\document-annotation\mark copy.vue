<template>
  <div class="container">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <n-button style="margin-left: 8px" plain @click="closeFn">取消</n-button>
      <n-button variant="solid" @click="saveData">保存</n-button>
    </div>
    <!-- 预览 -->
    <div class="preview-content" id="preview-pint" ref="previewPintRef" v-loading="state.loading"
      element-loading-text="Loading...">
      <FilePreview ref="filePreviewRef" :option="state.option" :type="state.type" @pageChange="pageChange"
        @loadComplete="getPreviewData(router.currentRoute.value.query.id)" :data="state.annotationsPage"
        @imageMarkUpdate="imageMarkUpdate" @click-polygon="(id) => tagRefs[id].showTag()" />
    </div>
    <template v-for="(item, key) in tagList" :key="key">
      <Tag :ref="(ref) => tagRefs[item.id] = ref" :tagOptions="state.tagOptions" :x="item.x" :y="item.y"
        @markDelete="markDelete(item)" :initialTags="item.tags" @tagUpdate="(_) => tagUpdate(_, item)"
        @confirm="filePreviewRef.enable()" />
    </template>
  </div>
</template>
<script setup>
import { throttle, isEmpty } from "lodash-es"
import { ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'
import Tag from './tag copy.vue'
import FilePreview from './components/FilePreview.vue'
import * as d3 from 'd3'
import api from '@/api/index'
const router = useRouter()
const previewPintRef = ref(null)
const tagRefs = {}
const filePreviewRef = ref(null)
const state = reactive({
  name: '文档预览',
  option: {},
  type: '',
  loading: false,
  tagOptions: [],
  annotations: {},
  // 分页标注data
  annotationsPage: {

  },
  currentPage: 1,

});
const pageChange = (page) => {
  setTimeout(() => {
    state.currentPage = page
    renderWaveLine()
  });
}
function getDocUrl(id) {
  return api.documentManage.outsideGet({ id: id }).then((res) => {
    const { name, type, docUrl } = res.data
    state.name = name
    state.type = type
    state.option = {
      value: docUrl,
      name: name,
    }
  })
}
function closeFn() {
  router.go(-1)
}
onMounted(() => {
  const { id } = router.currentRoute.value.query
  getDocUrl(id).finally(() => {
    switch (true) {
      case ['pdf', 'word'].includes(state.type):
        document.addEventListener('selectionchange', handleTextSelection);
        break;
      case state.type.includes('image'):
        break;
    }
  })
})

// 获取标签列表
const getTargetList = () => {
  api.documentManage.getTagLibraryClassListHasTag().then((res) => {
    let { success, data } = res
    if (success) {
      state.tagOptions = data
    }
  })
}
getTargetList()
const handleTextSelection = throttle((e) => {
  // 不是body元素则不处理
  const selection = window.getSelection();
  // 获取选中文字
  const selectedText = selection.toString();

  if (!selection.rangeCount) return;
  const range = selection.getRangeAt(0);
  const selectedElement = range.commonAncestorContainer;
  // 检查选区是否在目标容器内（例如 class="allowed-container"）
  console.log("selectedElement", selectedElement);
  const box = document.getElementById('select-text-layer_232')
  if (!box.contains(selectedElement)) {
    console.log("选中的内容不在允许范围内");
    return;
  }

  // 选择data-temp为true的div
  // 清除之前的波浪线
  document.querySelectorAll('[data-temp="true"]').forEach(el => el.remove());

  const waveContainer = document.getElementById('wave-container');
  // 获取waveContainer的位置
  const { top, left, width, height } = waveContainer.getBoundingClientRect();

  const rects = Array.from(range.getClientRects());
  // 过滤掉top位置相近的矩形
  // 重新编排行组
  const sortedRects = []
  state.annotations = {}

  // 获取选中区域
  const boundingClientRect = range.getBoundingClientRect()
  rects.map((rect, index) => {
    rect = JSON.parse(JSON.stringify(rect))
    // 检查是否在四个选中区域内
    if (!(boundingClientRect.top <= rect.top && boundingClientRect.bottom >= rect.bottom && boundingClientRect.left <= rect.left && boundingClientRect.right >= rect.right)) return
    // 宽度过小的不处理
    if (rect.width < 4 || 100 > rect.height > 4) return
    if (sortedRects.length === 0) {
      sortedRects.push(rect)
      return rect
    }
    // 检查sortedRects与当前rect的y值是否相差大于4px
    const isPush = sortedRects.find((item) => {
      const diffY = Math.abs(item.top - rect.top)
      if (diffY < 8) {
        item.width = Math.max(item.width, rect.right - item.left)
        item.left = Math.min(item.left, rect.left)
        return true
      } else {
        return false
      }
    }) ? false : true
    if (isPush) sortedRects.push(rect)
    console.log("sortedRects", sortedRects);
    return
  });
  if (!sortedRects.length) return
  const annotationId = `wave-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  sortedRects.forEach(rect => addWaveLine({ offsetY: rect.height, width: rect.width, top: rect.top - top, left: rect.left - left, annotationId }));
  state.annotations[annotationId] = { selectedText, id: annotationId, sortedRects, y: boundingClientRect.top - top, x: boundingClientRect.right - left, width: boundingClientRect.width, height: boundingClientRect.height, boundingRect: { top, left, width, height } }
}, 100)
const tagList = computed(() => {
  // state.annotations为空时展示当前页的标注
  if (isEmpty(state.annotations || {})) {
    return state.annotationsPage[state.currentPage] || {}
  }
  const newAnnotations = {
    ...(state.annotationsPage[state.currentPage] || {}),
    ...(state.annotations || {})
  }
  return newAnnotations
})
function markDelete(item) {
  const { id } = item
  // 从state.annotations中删除
  delete state.annotations[id]
  // 从state.annotationsPage[state.currentPage]中删除
  delete state.annotationsPage[state.currentPage][id]
  // 从tagList中删除
  delete tagList.value[id]
  // 从tagRefs中删除 
  delete tagRefs[id]
  // 删除标记完成的波浪线
  document.querySelectorAll(`[data-annotation-id="${id}"]`).forEach(el => el.remove());
  // 手动触发更新
  state.annotationsPage = { ...state.annotationsPage }
  filePreviewRef.value.enable()
}
function tagUpdate(tags, item) {
  item.tags = tags
  // state.annotationsPage[state.currentPage] = { ...state.annotationsPage[state.currentPage], [annotationId]: { id: annotationId, sortedRects, y: boundingClientRect.top - top, x: boundingClientRect.left - left, width: boundingClientRect.width, height: boundingClientRect.height } }
  state.annotationsPage[state.currentPage] = { ...state.annotationsPage[state.currentPage], [item.id]: item }
  // 渲染标记完成的波浪线
  Object.values(state.annotationsPage[state.currentPage] || {}).forEach(item => {
    // 查找data-annotation-id是否为item.id的div，删除
    document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach(el => el.remove());
    renderWaveLine()
  })
}
// 图片标记更新
function imageMarkUpdate(data, scale, imgOffset = { x: 0, y: 0 }) {
  const annotationId = `wave-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const maxX = Math.max(...data.map(item => item.x * scale + imgOffset.x))
  const maxY = Math.min(...data.map(item => item.y * scale + imgOffset.y))
  state.annotations[annotationId] = { id: annotationId, data, type: 'image', x: maxX, y: maxY, }
  state.annotationsPage[state.currentPage] = { ...state.annotationsPage[state.currentPage], [annotationId]: state.annotations[annotationId] }
  filePreviewRef.value.disable()
}
// 渲染本页的波浪线
function renderWaveLine() {
  // 清除之前的data-temp为true的div
  document.querySelectorAll('[data-temp="true"]').forEach(el => el.remove());
  // 查询
  // 渲染标记完成的波浪线
  Object.values(state.annotationsPage[state.currentPage] || {}).forEach(item => {
    if (item.sortedRects.length) {
      const { top, left, width, height } = item.boundingRect
      // 清除重复的
      document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach(el => el.remove());
      item.sortedRects.forEach(rect => addWaveLine({ dataTemp: false, offsetY: rect.height, width: rect.width, top: rect.top - top, left: rect.left - left, annotationId: item.id }));
    }
  })
}
// 添加波浪线
function addWaveLine({ dataTemp = true, offsetY = 0, top = 0, left = 0, width = 2000, height = 10, wavelength = 20, lineWidth = 1, annotationId } = { dataTemp: true, offsetY: 0, top: 0, left: 0, width: 2000, height: 10, wavelength: 50, lineWidth: 1, annotationId }) {
  const amplitude = height * .5;    // 振幅
  const centerY = height / 2;      // 中心Y坐标

  const svg = d3.select("#wave-container").append("svg")
    .attr("width", width)
    .attr("height", height)
    .attr("style", `position: absolute;top: ${top + offsetY}px;left:${left}px;color: #f00;z-index: 100000;`)
    .attr('data-annotation-id', annotationId)
    .attr('data-temp', dataTemp)

  // 添加点击事件
  svg.on("click", function (event) {
    tagRefs[annotationId]?.showTag()
  })

  const waveData = [];
  for (let x = 0; x < width; x++) {
    const y = (amplitude - lineWidth * 2) * Math.sin((x / wavelength) * Math.PI * 2) + centerY;
    waveData.push([x, y]);
  }
  svg.append("path")
    .datum(waveData)
    .attr("d", d3.line().x(d => d[0]).y(d => d[1]).curve(d3.curveBasis))
    .attr("stroke", "currentColor")
    .attr("fill", "none");
}
onUnmounted(() => {
  document.removeEventListener('selectionchange', handleTextSelection);
})

function refineSelection() {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) return null;

  const originalRange = selection.getRangeAt(0);
  const startContainer = originalRange.startContainer;
  const startOffset = originalRange.startOffset;
  const endContainer = originalRange.endContainer;
  const endOffset = originalRange.endOffset;

  // 1. 定位首尾有效内容
  const startNode = findFirstNonEmptyNode(startContainer, startOffset, true);
  const endNode = findLastNonEmptyNode(endContainer, endOffset, true);

  if (!startNode || !endNode) return null;

  // 2. 创建新 Range
  const newRange = document.createRange();
  newRange.setStart(startNode.node, startNode.offset);
  newRange.setEnd(endNode.node, endNode.offset);

  // 3. 过滤中间空白节点
  const walker = document.createTreeWalker(
    newRange.commonAncestorContainer,
    NodeFilter.SHOW_TEXT,
    {
      acceptNode: (node) => {
        // 保留非空白文本节点
        return node.textContent.trim() !== '' ?
          NodeFilter.FILTER_ACCEPT :
          NodeFilter.FILTER_REJECT;
      }
    }
  );

  // 4. 精准调整选区边界
  let firstValidNode = null;
  let lastValidNode = null;
  while (walker.nextNode()) {
    const node = walker.currentNode;
    if (!firstValidNode) firstValidNode = node;
    lastValidNode = node;
  }

  if (firstValidNode && lastValidNode) {
    newRange.setStart(firstValidNode, 0);
    newRange.setEnd(lastValidNode, lastValidNode.length);
  }

  // 5. 更新选区
  selection.removeAllRanges();
  selection.addRange(newRange);
  return newRange;
}

// /​**
//   * 查找起点后的第一个有效节点
//   * @param { Node } node 起始节点
//     * @param { number } offset 起始偏移量
//       * @param { boolean } forward 是否向前查找
//         */
function findFirstNonEmptyNode(node, offset, forward) {
  let currentNode = node;
  let currentOffset = offset;

  while (currentNode) {
    if (currentNode.nodeType === Node.TEXT_NODE) {
      const text = currentNode.textContent.slice(currentOffset);
      if (text.trim() !== '') {
        return { node: currentNode, offset: currentOffset };
      }
    }

    // 遍历下一个节点
    currentNode = forward ?
      nextNode(currentNode) :
      previousNode(currentNode);
    currentOffset = 0;
  }
  return null;
}

// /​**​
//  * 查找终点前的最后一个有效节点
//  * @param {Node} node 结束节点
//  * @param {number} offset 结束偏移量
//  */
function findLastNonEmptyNode(node, offset) {
  let currentNode = node;
  let currentOffset = offset;

  while (currentNode) {
    if (currentNode.nodeType === Node.TEXT_NODE) {
      const text = currentNode.textContent.slice(0, currentOffset);
      if (text.trim() !== '') {
        return { node: currentNode, offset: currentOffset };
      }
    }

    // 向前遍历节点
    currentNode = previousNode(currentNode);
    currentOffset = currentNode?.textContent?.length || 0;
  }
  return null;
}

// 辅助函数：获取下一个节点
function nextNode(node) {
  return node.nextSibling || node.parentNode?.nextSibling;
}

// 辅助函数：获取上一个节点
function previousNode(node) {
  return node.previousSibling || node.parentNode?.previousSibling;
}

// 使用示例：监听鼠标抬起事件
// document.addEventListener('mouseup', () => {
//   const cleanedText = refineSelection();
//   console.log('Refined Selection:', cleanedText);
// });
function saveData() {
  api.documentManage.saveDocumentAnnotation({
    documentId: router.currentRoute.value.query.id,
    mark: JSON.stringify(state.annotationsPage),
    contents: Object.values(state.annotationsPage || {}).flatMap(item => Object.values(item || {})).map(item => ({
      markId: item.id, content: item.selectedText,
      type: state.type,
      position: JSON.stringify(item?.sortedRects || item?.data || []),
      remarks: JSON.stringify(item?.tags || []),
    }))
  }).then((res) => {
    if (res.success) {
      ElNotification({
        title: '提示',
        message: '操作成功！',
        type: 'success',
      })
      closeFn()
    }
  });
}
// 获取预览数据
function getPreviewData(id) {
  api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
    if (res.success) {
      try {
        const { mark, paragraph } = res.data
        state.annotationsPage = JSON.parse(mark || '{}')
        switch (true) {
          case ['pdf', 'word'].includes(state.type):
            renderWaveLine()
            break
          case state.type.includes('image'):
            break
        }
      } catch (error) {
        state.annotationsPage = {}
      }
    }
  })
}
</script>
<style lang="scss" scoped>
@import '/src/styles/variables.scss';
@import '/src/styles/cf.scss';

.preview-content {
  height: calc(100vh - 175px);
  padding: 10px;
  background: #fff;
}

:deep(.img-content) {
  overflow-x: hidden;
  overflow-y: auto;

  .img-Preview {
    min-height: 100%;
    overflow-x: unset;
    overflow-y: unset;
  }
}

.title {
  width: calc(100% - 80px);
}
</style>
<style>
#wave-container,
#tag-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100000;
  width: 0;
  height: 0;
}
</style>
