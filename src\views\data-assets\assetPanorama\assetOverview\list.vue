<template>
  <div class="panorama scroll-bar-style">
    <!--全局数据模型实体总数-->
    <div class="content-total">
      <div class="data-total">
        <div class="data-title">
          <moduleName
            :info="{
              name: '数据统计',
              size: 16,
              color: '#333',
              width: '4px',
              height: '15px',
              left: '12px',
            }"
          />
          <!-- <h5><img class="count-img" src="@img/model/model_statistics.png" alt="" />数据统计</h5> -->
          <div class="nearDate">数据截止日期：{{ today }}</div>
        </div>
        <div class="data-total-box">
          <div
            class="content-total-box"
            v-for="(item, index) in totalList"
            :key="index"
            :style="'background-color:' + item.bgColor"
          >
            <img class="pic" :src="item.pic" />
            <div class="content-total-box-title">
              {{ item.name }}
              <!-- <el-tooltip v-if="item.tips" effect="dark" :content="item.tips" placement="top">
                <div class="tip" :style="'background-color:' + item.color">i</div>
              </el-tooltip> -->
            </div>
            <div class="content-total-box-num">
              <span>{{ item.num }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-echart">
      <div class="echart-title">
        <h5><img class="count-img" src="@img/assets/assets-bar.png" alt="" />资产增减时序图</h5>
        <n-form :inline="true" :data="formInline">
          <n-form-item label="" field="keyword">
            <n-select
              v-model="formInline.keyword"
              placeholder="选择周期"
              width="220"
              @value-change="getLineChart"
            >
              <n-option
                v-for="item in dateList"
                :key="item.id"
                :name="item.name"
                :value="item.id"
              />
            </n-select>
          </n-form-item>
        </n-form>
      </div>
      <div id="lineChart" class="content-echart-content"> </div>
    </div>
  </div>
</template>

<script>
  import moduleName from '@/components/ModuleName'
  import { reactive, onMounted, toRefs, nextTick } from 'vue'
  import api from '@/api/index'
  import { timestampToTime } from '@/const/public.js'
  import ForceGraph3D from '3d-force-graph'
  import blocks from './blocks.json'
  import * as echarts from 'echarts'

  export default {
    components: {},
    setup() {
      const state = reactive({
        totalList: [
          {
            name: '数据模型总数(个)',
            num: '--',
            bgColor: 'rgba(243, 245, 248, 1)',
          },
          { name: '数据资产总数(个)', num: '--', bgColor: 'rgba(237, 247, 240, 1)' },
          {
            name: '资产打标数(个)',
            num: '--',
            bgColor: 'rgba(244, 237, 250, 1)',
          },
          { name: '标签数总数(个)', num: '--', bgColor: 'rgba(237, 244, 247, 1)' },
          { name: '业务域总数(个)', num: '--', bgColor: 'rgba(247, 241, 237, 1)' },
        ],
        formInline: {
          keyword: 7,
        },
        dateList: [
          { id: 7, name: '过去七天' },
          { id: 31, name: '过去一个月' },
          // { id: 'MONTH', name: '月' },
        ],
        isFullScreen: false,
        loading: false,
        today: '',
        Graph: null,
        timer: null,
        graphData: {},
      })
      // 渲染图片
      state.totalList = state.totalList.map((val, ind) => {
        val.pic = new URL(`/src/assets/img/assets/assets-0${ind + 1}.png`, import.meta.url).href
        return val
      })

      const flow = ref(null)

      let isRotationActive = true

      const methods = {
        // 获取统计数据
        getTotalNum() {
          api.assets.getAssetsLibraryCount().then((res) => {
            if (res.success) {
              let { assetsNum, assetsTaggedNum, modelNum, tagNum, bizDomainNum } = res.data
              state.totalList[0].num = modelNum
              state.totalList[1].num = assetsNum
              state.totalList[2].num = assetsTaggedNum
              state.totalList[3].num = tagNum
              state.totalList[4].num = bizDomainNum
            }
          })
        },
        async getLineChart() {
          if (lineChart !== 'undefined') {
            echarts.init(document.getElementById('lineChart')).dispose()
          }
          var lineChart = echarts.init(document.getElementById('lineChart'))
          let option = {
            grid: {
              bottom: '10%',
              left: 40,
              right: 60,
              containLabel: true,
            },
            tooltip: {
              trigger: 'axis',
              textStyle: {
                fontSize: 12,
                color: '#333',
              },
              // formatter: '{b}<br>数据占比 {d}%',
            },
            legend: {
              icon: 'circle',
              data: ['ODS', 'DWD', 'DWS', 'ADS'],
              bottom: '0%',
              right: '40%',
              itemWidth: 8,
              textStyle: {
                fontSize: 12,
                color: '#666',
              },
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              axisLine: {
                lineStyle: {
                  color: '#ccc',
                },
              },
              axisLabel: {
                textStyle: {
                  color: '#303133',
                },
              },
            },
            yAxis: {
              type: 'value',
              splitLine: { show: true },
            },
            series: [
              {
                data: [0, 0, 0, 0, 0, 0, 0],
                name: '资产总数',
                type: 'line',
                color: ['rgba(54, 174, 255, 1)'],
                symbol: 'circle',
                symbolSize: 9,
                itemStyle: {
                  borderColor: '#ffffff',
                  borderType: 'solid',
                  borderWidth: 2,
                },
                areaStyle: {
                  normal: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54, 174, 255, 0.1)',
                        },
                        {
                          offset: 1,
                          color: '#FFFFFF',
                        },
                      ],
                    },
                  },
                  emphasis: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54, 174, 255, 0.1)',
                        },
                        {
                          offset: 1,
                          color: '#FFFFFF',
                        },
                      ],
                    },
                  },
                },
              },
            ],
          }
          await api.assets.getLineChart({ days: state.formInline.keyword }).then((res) => {
            const { success, data } = res
            if (success && data) {
              option.xAxis.data = data.dates
              option.series[0].data = data.dateSums
            }
          })
          lineChart.setOption(option)
        },
      }
      onMounted(() => {
        methods.getTotalNum()
        methods.getLineChart()
        state.loading = true
        // state.graphData = blocks
        state.today = timestampToTime(new Date().getTime(), true)
      })
      onBeforeUnmount(() => {})

      const params = toRefs(state)
      return {
        flow,
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .panorama {
    padding: 0 16px 16px 16px;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;

    .nearDate {
      color: #333333;
      font-size: 12px;
      // padding-top: 10px;
    }

    .content-box {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 14px;
      width: 100%;
      height: calc(100vh - 386px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 8px;
      &.full {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 9999;
        left: 0;
        top: 0;
        margin-top: 0;
        padding: 30px;
        box-sizing: border-box;
      }

      &-left,
      &-right {
        width: 70%;
        padding: 20px 30px 30px 30px;
        background-color: #fff;
        border-radius: 8px;
        position: relative;
      }

      &-left {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        &.all {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
        }

        .data-source {
          flex: 2;
          height: 284px;
          margin-right: 50px;
          padding-right: 16px;
          position: relative;

          &:before {
            content: '';
            width: 1px;
            height: 232px;
            background: #ebedf0;
            position: absolute;
            top: 0;
            right: 0;
          }
        }

        .data-equipment {
          flex: 3;
          padding-right: 28px;

          .data-table-box {
            text-align: center;
          }
        }

        .data-map {
          width: 100%;
          height: 100%;

          .canvas {
            width: 100%;
            height: calc(100% - 43px);
            overflow: hidden;
            margin-top: 20px;
            border-radius: 8px;
            position: relative;
            border: 1px solid #ebedf0;

            :deep(.x6-graph-scroller) {
              &::-webkit-scrollbar {
                width: 0; // 横向滚动条
                height: 0; // 纵向滚动条 必写
              }

              // 滚动条的滑块
              &::-webkit-scrollbar-thumb {
                background-color: #ddd;
                border-radius: 0;
              }
            }
            .tools {
              position: absolute;
              top: 5px;
              right: 5px;
              z-index: 5;
            }
          }
        }

        .data-ability {
          flex: 1;
          margin-right: 80px;
          padding-right: 16px;
          border-right: 1px solid #ebedf0;
        }

        .data-report {
          flex: 2;

          &-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            &-grade {
              width: 240px;

              .canvas {
                width: 100%;
                height: 180px;
              }
            }

            &-result {
              width: calc(100% - 240px);
            }
          }
        }
      }

      &-right {
        padding: 20px 20px 20px 30px;
        width: 29%;

        .data-base {
          .canvas {
            width: 100%;
            height: 260px;
          }
        }
      }

      .data-label {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 23px;

        &-name {
          color: #999999;
          font-size: 14px;
          width: 80px;
        }

        &-num {
          color: #000;
          font-size: 14px;
          font-weight: bold;

          span {
            font-size: 26px;
          }
        }
      }

      .data-table {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 30px;

        &-box {
          text-align: left;

          &-name {
            color: #999999;
            font-size: 14px;
          }

          &-num {
            color: #000000;
            font-weight: bold;
            font-size: 14px;
            margin-top: 12px;

            span {
              font-size: 26px;
            }
          }
        }
      }

      .bor {
        width: 100%;
        height: 1px;
        background-color: #ebedf0;
        margin-top: 33px;
      }

      .metadata {
        display: none;
        height: 470px;

        .data-table {
          margin-top: 20px;
        }

        .bor {
          margin-bottom: 30px;
        }
      }
    }

    .content-echart {
      padding: 30px;
      margin-top: 14px;
      width: 100%;
      height: calc(100vh - 386px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 8px;
      .echart-title {
        display: flex;
        justify-content: space-between;
      }
      &-content {
        margin-top: 20px;
        width: 100%;
        height: calc(100% - 42px);
        box-sizing: border-box;
      }
    }
    .content-total {
      margin-top: 14px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px 30px;
      background-color: #fff;
      border-radius: 8px;
      position: relative;

      .data-total {
        flex: 1;
        .data-title {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .setting {
            color: #0e94cd;
            font-size: 14px;

            .icon-img {
              width: 20px;
              height: 20px;
            }
          }
        }
        &-box {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-top: 30px;
        }
      }

      &.list {
        padding: 20px;

        .data-list {
          flex: 4;
          height: 512px;
          box-sizing: border-box;
          position: relative;
          padding: 10px;
          border-radius: 8px;
          margin-right: 40px;
          background-image: url('/src/assets/img/assets/assets-bg-02.png');
          background-position: left top;
          background-size: 100% 100%;
          background-repeat: no-repeat;

          .data-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;

            .setting {
              position: relative;
              top: 0;
              right: 0;
            }
          }

          .data-rank {
            position: relative;
            z-index: 2;
            background-color: #fff;
            width: 100%;
            padding: 20px 30px 20px 15px;
            height: 440px;
            box-sizing: border-box;
            border-radius: 12px;
            margin-top: 20px;

            &-list {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 25px;

              &:nth-of-type(1) {
                .data-rank-list-num {
                  background: linear-gradient(137deg, #ff6d51 0%, #f04930 100%);
                }
              }

              &:nth-of-type(2) {
                .data-rank-list-num {
                  background: linear-gradient(135deg, #fba433 0%, #ff770e 100%);
                }
              }

              &:nth-of-type(3) {
                .data-rank-list-num {
                  background: linear-gradient(135deg, #f8da60 0%, #fcb04d 100%);
                }
              }

              &-num {
                width: 16px;
                height: 16px;
                text-align: center;
                line-height: 16px;
                border-radius: 3px;
                background: #cbb4a2;
                margin-right: 16px;
                color: #fff;
                font-size: 12px;
              }

              &-progress {
                position: relative;
                width: calc(100% - 100px);
                height: 6px;
                background-color: #f2f3f6;
                border-radius: 3px;
                overflow: hidden;

                &-bar {
                  position: absolute;
                  background-color: #0e94cd;
                  width: 30%;
                  height: 6px;
                  left: 0;
                  top: 0;
                }
              }

              &-name {
                width: 56px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #333333;
                font-size: 12px;
                margin-left: 12px;
              }
            }
          }
        }

        .data-search {
          flex: 5;

          .canvas {
            width: 100%;
            height: 430px;
          }
        }
      }

      &-box {
        flex: 1;
        height: 200px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-right: 20px;
        padding: 0 10px;
        background: linear-gradient(180deg, #f3f6f8 0%, #ffffff 100%);
        border: 1px solid #ebedf0;
        box-sizing: border-box;
        .pic {
          width: 40px;
          height: 40px;
          margin-bottom: 16px;
        }
        &-title {
          color: #333;
          font-size: 14px;
          width: 120px;
          text-align: center;
          padding: 20px 0;
          border-top: 1px solid rgba(105, 122, 154, 0.1);
          box-sizing: border-box;

          .tip {
            margin-left: 8px;
            width: 18px;
            height: 18px;
            line-height: 18px;
            text-align: center;
            color: #fff;
            font-size: 12px;
            border-radius: 50%;
            font-style: italic;
          }
        }
        &-num {
          color: #000;
          font-size: 14px;
          span {
            font-size: 26px;
            font-weight: bold;
            margin-right: 2px;
          }
        }
      }
      &-box:nth-of-type(2) {
        background: linear-gradient(180deg, #edf7f0 0%, #ffffff 100%);
        border-color: #edf7f0;
      }
      &-box:nth-of-type(3) {
        background: linear-gradient(180deg, #f4edfa 0%, #ffffff 100%);
        border-color: #f4edfa;
      }
      &-box:nth-of-type(4) {
        background: linear-gradient(180deg, #edf4f7 0%, #ffffff 100%);
        border-color: #edf4f7;
      }
      &-box:last-of-type {
        background: linear-gradient(180deg, #f7f1ed 0%, #ffffff 100%);
        border-color: #f7f1ed;
        margin-right: 0;
      }
      &-box:hover {
        background: linear-gradient(180deg, #f3f5f8 0%, #ffffff 100%);
        box-shadow: 0px 6px 16px 2px rgba(55, 99, 170, 0.06);
        border: 2px solid #ffffff;
      }
    }
    h5 {
      position: relative;
      height: 22px;
      line-height: 22px;
      margin: 0;
      padding-left: 20px;
      color: #333;
      // color: #697a9a;
      font-size: 14px;
      font-weight: 600px;
    }
    .count-img {
      width: 14px;
      position: absolute;
      left: 0px;
      top: 4px;
    }
  }
</style>
