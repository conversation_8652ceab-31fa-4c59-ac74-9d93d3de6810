<template>
  <section class="container">
    <section class="container-box">
      <section class="cf-tools">
        <div class="row">
          <div class="col text-label">
            名称：
            <n-input v-model="condition.name" placeholder="请输入" />
          </div>
          <div class="col">
            表密级：
            <n-select
              v-model="condition.securityLevel"
              :options="state.securityLevelMap"
              placeholder="请选择"
              filterable
              clearable
            />
          </div>
          <div class="col">
            时间：
            <n-range-date-picker-pro
              v-model="state.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              allow-clear
            />
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="methods.onSearch(true)">查询</div>
            <div class="search-btn reset" @click.prevent="methods.resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <section class="cf-tree">
          <div class="row">
            <div class="title"> 数据分类 </div>
          </div>
          <n-input
            class="table-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入关键词"
            suffix="search"
            @change="(val) => treeRef.treeRef.filter(val)"
          />
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :filter-node-method="filterNode"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            @node-click="methods.nodeClick"
            :data="state.treeList"
          />
        </section>
        <section class="talble-container">
          <div class="select-box">
            <n-radio-group
              direction="row"
              v-model="state.filterSearch.condition.authorization"
              @change="
                () => {
                  state.filterSearch.pageNum = 1
                  methods.onSearch()
                  methods.clearSelection()
                }
              "
            >
              <n-radio-button :value="'ALL'">全部</n-radio-button>
              <n-radio-button :value="'UNAUTHORIZED'">未授权</n-radio-button>
              <n-radio-button :value="'PENDING_AUTHORIZATION'">待授权</n-radio-button>
              <n-radio-button :value="'AUTHORIZED'">已授权</n-radio-button>
            </n-radio-group>

            <div class="row">
              <div
                class="switch-label"
                v-if="
                  buttonAuthList.includes('dataManagement_odsDirectory_list_batch_auth_edit') &&
                  ['UNAUTHORIZED'].includes(state.filterSearch.condition.authorization)
                "
                @click.prevent="methods.batchApply"
              >
                <SvgIcon class="icon-switch" icon="apply-for" />批量申请
              </div>
              <div class="line" @click.prevent="methods.onSearch()">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <g clip-path="url(#clip0_1410_90084)">
                    <path
                      d="M6 1.41589C7.43769 0.874404 9.00889 0.861306 10.4546 1.37876C11.9004 1.89621 13.1344 2.91334 13.9533 4.26248C14.7722 5.61162 15.1271 7.21226 14.9594 8.80057C14.8768 9.58351 14.6696 10.3404 14.3514 11.0421M14.3514 11.0421C14.024 11.7638 13.579 12.4271 13.0311 13L13.0794 11.0421L14.3514 11.0421Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                    <path
                      d="M10 14.5841C8.56231 15.1256 6.99111 15.1387 5.54535 14.6212C4.09959 14.1038 2.86558 13.0867 2.0467 11.7375C1.22782 10.3884 0.872948 8.78774 1.04058 7.19943C1.12321 6.41649 1.33038 5.65958 1.64864 4.95795M1.64864 4.95795C1.97603 4.2362 2.42098 3.57294 2.96887 3L2.9206 4.95795L1.64864 4.95795Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1410_90084">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                刷新
              </div>
              <!-- <div
                class="switch-label"
                v-if="
                  buttonAuthList.includes('dataManagement_odsDirectory_list_batch_auth_edit') &&
                  [undefined].includes(state.filterSearch.condition.authorization)
                "
                @click.prevent="methods.batchApply"
              >
                <SvgIcon class="icon-switch" icon="apply-for" />批量申请
              </div>
              <div
                class="switch-label"
                v-if="
                  buttonAuthList.includes(
                    'dataManagement_odsDirectory_list_batch_auth_manage_edit',
                  ) && [undefined].includes(state.filterSearch.condition.authorization)
                "
                @click.prevent="methods.batchAuth"
              >
                <SvgIcon class="icon-switch" icon="volume-license-keys" />批量授权
              </div> -->
              <!-- <div
                class="switch-label"
                v-if="
                  buttonAuthList.includes('dataManagement_odsDirectory_list_one_click_auth_edit') &&
                  ['PENDING_AUTHORIZATION'].includes(state.filterSearch.condition.authorization)
                "
                @click.prevent="methods.auth(methods.getSelectionRows()?.map((_) => _.modelId))"
              >
                <SvgIcon class="icon-switch" icon="volume-license-keys" />一键授权
              </div> -->
            </div>
          </div>
          <CfTable
            saveWidth
            v-loading="state.loading"
            ref="multipleTableRef"
            :tableConfig="{
              data: state.tableList,
              rowKey: 'modelId',
            }"
            :reserveSelection="state.filterSearch.condition.authorization === 'UNAUTHORIZED'"
            :isNeedSelection="state.filterSearch.condition.authorization === 'UNAUTHORIZED'"
            :paginationConfig="{
              total: state.total,
              pageSize: state.filterSearch.pageSize,
              currentPage: state.filterSearch.pageNum,
              onCurrentChange: (v) => {
                state.filterSearch.pageNum = v
                methods.onSearch()
              },
              onSizeChange: (v) => {
                state.filterSearch.pageSize = v
                state.filterSearch.pageNum = 1
                methods.onSearch()
              },
            }"
            actionWidth="200"
            :table-head-titles="
              state.tableHeadTitles[
                state.filterSearch.condition.authorization !== 'ALL' ? 'part' : 'all'
              ]
            "
          >
            <template #tagList="{ row }">
              <cfTag :tagArr="row.label" />
            </template>

            <template #authorization="{ row }">
              <div
                :title="
                  { UNAUTHORIZED: '未授权', PENDING_AUTHORIZATION: '待授权', AUTHORIZED: '已授权' }[
                    row?.authorization
                  ] || '--'
                "
              >
                {{
                  { UNAUTHORIZED: '未授权', PENDING_AUTHORIZATION: '待授权', AUTHORIZED: '已授权' }[
                    row?.authorization
                  ] || '--'
                }}
              </div>
            </template>
            <template #editor="{ data: { row } }">
              <el-button
                type="primary"
                v-if="buttonAuthList.includes('dataManagement_odsDirectory_list_view')"
                link
                @click="
                  $router.push({
                    name: 'odsSee',
                    query: {
                      id: row?.modelId,
                      securityLevelDiplay: row?.securityLevelDiplay,
                      cnName: row?.cnName,
                    },
                  })
                "
              >
                查看
              </el-button>
              <el-button
                type="primary"
                v-if="
                  state.filterSearch.condition.threeManager &&
                  ['ALL'].includes(state.filterSearch.condition.authorization)
                "
                link
                @click="methods.authUser(row)"
              >
                授权用户
              </el-button>
              <el-button
                type="primary"
                v-if="
                  buttonAuthList.includes('dataManagement_odsDirectory_list_apply_auth_edit') &&
                  ['UNAUTHORIZED'].includes(state.filterSearch.condition.authorization)
                "
                link
                @click.prevent="methods.confirm([row?.modelId])"
              >
                申请授权
              </el-button>
            </template>
          </CfTable>
        </section>
      </section>
    </section>
    <logPop ref="logPopref" @seeLogFn="seeLogFn" />
  </section>
</template>

<script setup>
  import api from '@/api/index'
  import { useStore } from 'vuex'
  import { formartTime } from '@/utils/index'
  import { queryDataTypeTree } from '@/api/dataManage'
  import CfTtee from '@/components/cfTtee'
  import CfTable from '@/components/cfTable'
  import cfTag from '@/components/cfTag'
  import logPop from './components/logPop.vue'
  const multipleTableRef = ref(null)
  const store = useStore()
  const { buttonAuthList, name } = toRefs(store.state.user)
  const logPopref = ref(null)
  const state = reactive({
    // 是否显示授权管理弹窗
    treeList: null,
    tableList: [],
    treeSearchText: '',
    filterSearch: {
      condition: {
        authorization: 'ALL',
        categoryId: null,
        endTime: null,
        startTime: null,
        name: '',
        threeManager: null,
        securityLevel: null,
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    time: [],
    tableHeadTitles: {
      all: [
        { prop: 'cnName', name: '中文名称' },
        { prop: 'name', name: '英文名称' },
        { prop: 'categoryName', name: '数据分类' },
        { prop: 'dataNum', name: '数据条数' },
        { prop: 'label', name: '标签', slot: 'tagList', width: 300 },
        { prop: 'securityLevelDiplay', name: '密级' },
        { prop: 'authorizedNums', name: '已授权人数' },
        { prop: 'collectName', name: '关联采集任务' },
        { prop: 'createTime', name: '创建时间', width: 160 },
        { prop: 'createByName', name: '创建人' },
        { prop: 'updateTime', name: '最新同步时间', width: 160 },
      ],
      // 密级映射
      part: [
        { prop: 'cnName', name: '中文名称' },
        { prop: 'name', name: '英文名称' },
        { prop: 'categoryName', name: '数据分类' },
        { prop: 'dataNum', name: '数据条数' },
        { prop: 'authorization', name: '授权状态', slot: 'authorization' },
        { prop: 'label', name: '标签', slot: 'tagList', width: 300 },
        { prop: 'securityLevelDiplay', name: '密级' },
        { prop: 'collectName', name: '关联采集任务' },
        { prop: 'createTime', name: '创建时间', width: 160 },
        { prop: 'createByName', name: '创建人' },
        { prop: 'updateTime', name: '最新同步时间', width: 160 },
      ],
    },
    // 密级映射
    securityLevelMap: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
    expandedKeys: [],
    selectedKey: null,
  })
  const condition = ref({})
  const treeRef = ref(null)
  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }
  nextTick(() => {
    state.tableHeight = (document.querySelector('.talble-container')?.offsetHeight || 500) - 60 - 48
  })
  let threeManager = false
  const methods = {
    // 按钮部分
    ...{
      // 弹出授权用户窗口
      authUser(data) {
        logPopref.value?.open(data)
      },
      // 批量申请授权
      batchApply() {
        const rows = methods.getSelectionRows()
        if (rows?.length > 0) {
          methods.confirm(rows.map((_) => _?.modelId))
          return
        }
        ElMessage.warning('请选择数据')
      },
      // 取消授权
      cancelAuth(data) {
        api.ods
          .cancelAuthorization({ authorizationId: data.authorizationId, modelId: data.modelId })
          .then(() => {
            methods.onSearch()
          })
      },
      auth(ids) {
        const params = {
          modelId: ids,
        }
        api.ods
          .authorization(params)
          .then((res) => {
            methods.onSearch()
          })
          .finally(() => {})
      },
      // 确认申请
      confirm(ids) {
        const params = {
          modelIds: ids,
          userIds: [],
        }
        api.ods
          .applyAuth(params)
          .then((res) => {
            if (!res?.success) return
            methods.onSearch()
            ElMessage.success('申请成功')
          })
          .finally(() => {
            methods.clearSelection()
          })
      },
    },
    nodeClick({ id }) {
      state.selectedKey = id
      state.filterSearch.condition.categoryId = id
      methods.onSearch()
    },
    getSelectionRows() {
      return multipleTableRef.value.getSelectionRows()
    },
    clearSelection() {
      multipleTableRef.value.clearSelection()
    },
    // 检查用户是否是三员管理员
    checkAdmin() {
      api.ods.isThreeMember({ username: name.value }).then((res) => {
        if (res.code === 'SUCCESS') {
          state.filterSearch.condition.threeManager = res.data?.threeManager
          threeManager = res.data?.threeManager
        }
      })
    },

    // 获取树
    getTree() {
      queryDataTypeTree().then((res) => {
        if (res.code === 'SUCCESS') {
          let treeData = []
          if (res.data?.children.length > 0) {
            treeData = [res.data]
            treeData[0].selected = true
            state.categoryId = treeData[0].id
          } else {
            treeData.push({
              children: [],
              id: null,
              level: 0,
              label: '全部',
              type: 'ROOT',
            })
          }
          if (treeData.length > 0) {
            treeData[0].expanded = true
          }
          state.treeList = [...treeData]
          nextTick(() => {
            state.expandedKeys = [state.treeList?.[0]?.id]
            state.selectedKey = state.treeList?.[0]?.id
          })
          methods.onSearch(true)
        }
      })
    },
    onSearch(init) {
      state.loading = true
      const { filterSearch } = state
      init && (filterSearch.pageNum = 1)
      const params = Object.assign(filterSearch, {
        condition: init
          ? {
              ...filterSearch.condition,
              startTime: state.time?.[0] && formartTime(state.time[0]),
              endTime: state.time?.[0] && formartTime(state.time[1], true),
              ...condition.value,
            }
          : filterSearch.condition,
      })
      let data = JSON.parse(JSON.stringify(params))
      if (data.condition.authorization === 'ALL') {
        data.condition.authorization = null
      }
      api.ods
        .ODSSearch(data)
        .then((res) => {
          state.loading = false
          state.tableList = res?.data?.list || []
          state.total = res?.data?.total || 0
        })
        .finally(() => {
          state.loading = false
        })
    },
    resetFn: (() => {
      const resetData = JSON.parse(JSON.stringify(state.filterSearch))
      return function () {
        state.filterSearch = JSON.parse(JSON.stringify(resetData))
        state.time = []
        condition.value = {}
        state.filterSearch.condition.threeManager = threeManager
        methods.onSearch(true)
      }
    })(),
  }

  methods.getTree()
  methods.checkAdmin()
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      .row {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        .line {
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 4px 16px;
          color: #1e89ff;
          font-size: 14px;
          border-radius: 2px;
          cursor: pointer;
          &.active,
          &:hover {
            color: #006dea;
          }
        }
      }
      .title {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        padding: 6px 12px 6px 0px;

        color: #1d2129;
        font-weight: 500;
        font-size: 16px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 24px;
        &::before {
          width: 4px;
          height: 18px;
          margin-right: 8px;
          background: #1e89ff;
          content: '';
        }
      }
      &-table {
        display: flex;
        flex: 1;
        gap: 10px;
        height: calc(100% - 60px);
        .talble-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0px 0px 2px 2px;
          .select-box {
            display: flex;
            align-items: center;
            align-self: stretch;
            justify-content: space-between;
            padding: 8px;
            :deep(.el-radio-group) {
              margin-bottom: 0 !important;
            }
            .row {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              height: 46px;
              .switch-label {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 5px 14px;
                color: #1e89ff;
                font-size: 14px;
                cursor: pointer;
                .icon-switch {
                  margin-right: 4px;
                  font-size: 16px;
                }
                &:hover {
                  color: #479dff;
                }
              }
            }
          }
        }
      }
    }
  }
  .modal-container {
    :deep(.nancalui-button) + .nancalui-button {
      margin-left: 0;
    }
  }
  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>

<style>
  :root .nancalui-modal .nancalui-modal__header {
    height: auto;
    padding: 15px 16px 15px 0;
    overflow: hidden;
    color: var(----, rgba(0, 0, 0, 0.9));
    font-weight: 500;
    font-size: 16px;
    font-family: 'Source Han Sans CN';
    font-style: normal;
    background: var(--100, #fff);
    border-bottom: 1px solid #dcdfe6;
    border-radius: 2px 2px 0px 0px;
  }
  /* :root .nancalui-modal .nancalui-modal__header .nancalui-modal__title {
    display: flex;
    line-height: 24px;
    background: none;
    padding: 0;
    align-items: center;
  } */
  :root .nancalui-modal .nancalui-modal__header .nancalui-modal__title::before {
    display: inline-block;
    width: 4px;
    height: 18px;
    margin-right: 12px;
    background: #1e89ff;
    content: '';
  }
  :root .nancalui-modal > .btn-close .nancalui-modal__close > svg {
    display: none;
  }
  :root .nancalui-modal > .btn-close .nancalui-modal__close {
    background-image: none;
  }
  :root .nancalui-modal > .btn-close .nancalui-modal__close::before {
    display: inline-block;
    width: 24px;
    height: 24px;
    content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNNC41IDE5LjQ5OTVMMTkuNDk5NSA0LjQ5OTk4IiBzdHJva2U9IiM2MDYyNjYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgogIDxwYXRoIGQ9Ik00LjUgNC41MDA0OUwxOS40OTk1IDE5LjUiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
  }
  :root .nancalui-modal {
    border-radius: 2px;
  }
  .table-label {
    display: inline-block;
    padding: 0px 4px;
    color: var(--col1, #fa5924);
    font-weight: 400;
    font-size: 12px;
    font-family: 'Source Han Sans CN';
    font-style: normal;
    line-height: 20px;
    text-align: center;
    background: var(--col2, #fff2e8);
    border: 1px solid var(--col1, #fa5924);
    border-radius: 2px;
  }
</style>
