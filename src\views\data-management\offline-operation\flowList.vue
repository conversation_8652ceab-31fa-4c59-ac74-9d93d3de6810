<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">业务流程：</span>
          <n-input v-model="state.originalFormInline.keyword" placeholder="请输入业务流程名称" />
          <span class="label">责任人：</span>
          <el-select
              v-model="state.originalFormInline.personInCharge"
              filterable
              clearable
              remote
              reserve-keyword
              placeholder="请输入工号或姓名"
              :remote-method="remoteMethod"
              :loading="state.searchLoading"
          >
            <el-option
                v-for="item in state.personList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            />
          </el-select>
          <span class="label">调度状态：</span>
          <n-select
            v-model="state.originalFormInline.scheduleStatus"
            placeholder="请选择状态"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.statusList"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
          <span class="label">最近运行时间：</span>
          <n-range-date-picker-pro
            v-model="state.originalFormInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :shortcuts="state.shortcuts"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-title">业务流程监控</div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                saveWidth
                :key="state.key"
                :isDisplayAction="true"
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    initTable()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    initTable()
                  },
                }"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="180"
              >
                <template #name="{ row }">
                  <div class="taskName">
                    {{ row.name }}
                  </div>
                </template>
                <template #state="{ row }">
                  <div class="taskName">
                    <span v-if="row.state === 1">运行中</span>
                    <span v-else-if="row.state === 6">失败</span>
                    <span v-else-if="row.state === 7">成功</span>
                    <span v-else>--</span>
                  </div>
                </template>
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_view') &&
                        row.scheduleStatus === 'ONLINE'
                      "
                      code="dataManagement_collectionMonitor_view"
                      variant="text"
                      @click.prevent="cardRunFn(row)"
                      >停止调度</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_run_edit') &&
                        row.scheduleStatus !== 'ONLINE'
                      "
                      code="dataManagement_collectionMonitor_run_edit"
                      variant="text"
                      @click.prevent="cardRunFn(row)"
                      >恢复调度</n-button
                    >
                    <n-button
                      class="has-right-border"
                      code="dataManagement_collectionMonitor_refresh_edit"
                      variant="text"
                      @click.prevent="goDAGFn(row)"
                      >DAG图</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import ENUM from '@/const/enum'
  import { formartTimeDate, changeFrequencyChinese } from '@/utils/index'
  import { operationWorkList, processScheduleStart, processScheduleStop } from '@/api/dataManage'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import cardList from '@/components/CardList'
  export default {
    name: '',
    components: { cardList },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: {},
        tableHeight: 436,
        key: 1,
        collectType: 'ALL',
        loading: false,
        searchLoading: false,
        originalFormInline: {
          scheduleStatus: null,
          personInCharge: null,
          keyword: null,
          time: [new Date().setHours(0, 0, 0, 0), new Date().setHours(23, 59, 59, 999)],
        },
        formInline: {
          scheduleStatus: null,
          personInCharge: null,
          keyword: null,
          time: [new Date().setHours(0, 0, 0, 0), new Date().setHours(23, 59, 59, 999)],
        },
        optionItemData: {}, // 操作的任务数据
        statusList: [
          { name: '调度中', value: 'ONLINE' },
          { name: '已停止', value: 'OFFLINE' },
        ],
        personList: [],
        shortcuts: ENUM.SHORTCUTS,
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'name', name: '业务流程', slot: 'name' },
          { prop: 'scheduleStatusName', name: '调度状态' },
          { prop: 'personInChargeName', name: '责任人' },
          { prop: 'scheduleText', name: '调度频率' },
          { prop: 'lastPlanRunTime', name: '下次计划执行时间' },
          { prop: 'createByName', name: '最后修改人' },
          { prop: 'createTime', name: '最后修改时间' },
          { prop: 'lastProcessInstanceRunTime', name: '最近运行时间' },
        ],
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 294
        },
        remoteMethod(query){
          state.searchLoading = true
          api.system.userList({
            condition:{name: query},
            pageNum:1,
            pageSize:100,
          }).then((res) => {
            state.searchLoading = false
            if(res.success){
              state.personList = res.data.list
            }
          }).catch(()=>{
            state.searchLoading = false
          })
        },
        // 调度
        cardRunFn(item) {
          if (item.scheduleStatus === 'ONLINE') {
            processScheduleStop({ id: item.id }).then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: '停止调度成功',
                  type: 'success',
                })
                methods.initTable(false)
              }
            })
          } else {
            processScheduleStart({ id: item.id }).then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: '启动调度成功',
                  type: 'success',
                })
                methods.initTable(false)
              }
            })
          }
        },
        // 跳转DAG图
        goDAGFn(item) {
          router.push({ name: 'dagDetail', query: { id: item.id } })
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            scheduleStatus: null,
            personInCharge: null,
            keyword: null,
            time: [],
          }
          methods.searchClickFn()
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        //搜索
        onSearch() {
          state.key++
          setTimeout(() => {
            methods.initTable(true)
          }, 60)
        },
        // 初始化表格（列表）
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let startTime = ''
          let endTime = ''
          if (state.formInline.time && state.formInline.time[0]) {
            if (Object.prototype.toString.call(state.formInline.time[0]) === '[object Date]') {
              startTime = formartTimeDate(state.formInline.time[0], '-', true)
              endTime = formartTimeDate(state.formInline.time[1], '-', true)
            } else {
              startTime = state.formInline.time[0]
              endTime = state.formInline.time[1]
            }
          }
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              name: state.formInline.keyword || null,
              scheduleStatus: state.formInline.scheduleStatus || null,
              personInCharge: state.formInline.personInCharge || null,
            },
          }
          if (startTime) {
            data.condition.startTime = startTime
            data.condition.endTime = endTime
          }
          state.loading = true
          operationWorkList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.scheduleText = changeFrequencyChinese(item)
                  item.number = index + 1
                })
                state.tableData = res.data
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },

        // 列表（表格）操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.initTable()
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        methods.initTable(true)
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-tree-select,
        .el-select,
        .nancalui-select {
          width: 240px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;
            background-color: #1e89ff;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    box-sizing: border-box;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 0;
    padding: 0;
    .data-collection-page-content {
      width: 100%;
      height: 100%;
      border-radius: 2px;
      background-color: #fff;
      overflow: hidden;
      .out-box {
        height: 100%;
        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            margin: auto;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
        }
      }
      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;
        .nancalui-table {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }
          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;
              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }
          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }
          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;
              &.PUBLISH {
                background-color: #04c495;
              }
              &.CREATED {
                background-color: #447dfd;
              }
              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }
      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }
        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }
  .modal-body {
    &-header {
      .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        .col {
          flex: 1;
          flex-shrink: 0;
          height: 22px;
          line-height: 22px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          color: #606266;
          font-size: 14px;
          .name {
            width: 100px;
          }
          .value {
            width: calc(100% - 100px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #1d2129;
          }
        }
      }
    }
    &-table {
      width: 100%;
      overflow-x: auto;
      &-scroll {
        min-width: 100%;
      }
    }
  }
</style>
