<template>
  <section class="column">
    <div class="operation">
      <n-input
        class="attrbute-input"
        v-model="filterText"
        placeholder="输入要搜索的属性的名称，然后按Enter"
        clearable
        @clear="clearFilter"
        @keyup.enter="fitlerAttr"
      >
        <i slot="prefix" class="n-input__icon n-icon-search"></i>
      </n-input>
    </div>
    <n-table-v
      :cache="5"
      ref="multipleTable"
      :columns="columns"
      :data="data"
      :width="1000"
      :height="300"
      fixed
      style="width: 100%"
      :row-event-handlers="{ onClick: clickAttr }"
    >
      <template #empty>
        <section class="containerBox">
          <img src="@img/no-page-content.png" />
          <p class="empty-word">无数据，请录入查询对象</p>
        </section>
      </template>
    </n-table-v>
    <div class="other">
      <div class="other-left">
        <div class="table">
          <h3>其它</h3>
          <div class="table-box">
            <div class="table-node">默认值</div>
            <div class="table-node">
              <n-input :disabled="disabled" v-model="form.defVal" type="text" />
            </div>

            <div class="table-node">标准化</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.standard" placeholder="请选择">
                <n-option name="继承" value="1" />
                <n-option name="对象" value="2" />
                <n-option name="非对象" value="3" />
              </n-select>
            </div>

            <div class="table-node">CharUsed</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.charUsed" placeholder="请选择">
                <n-option name="继承" value="1" />
                <n-option name="对象" value="2" />
                <n-option name="非对象" value="3" />
              </n-select>
            </div>
          </div>

          <h3>信息安全</h3>
          <div class="table-box">
            <div class="table-node">是否保护信息</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.protect" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">隐私分类</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.privacyClassify" type="text"
            /></div>

            <div class="table-node">信息安全等级</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.infoSecurityLevel" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">是否加密</div>
            <div class="table-node">
              <n-select :disabled="disabled" v-model="form.encryptFlag" placeholder="请选择">
                <n-option name="True" value="true" />
                <n-option name="False" value="false" />
              </n-select>
            </div>

            <div class="table-node">加密方法</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.encryption" type="text"
            /></div>

            <div class="table-node">数据所有者</div>
            <div class="table-node"
              ><n-input :disabled="disabled" v-model="form.dataOwner" type="text"
            /></div>
          </div>
        </div>
      </div>

      <div class="other-right">
        <n-tabs v-model="activeName" type="wrapped">
          <n-tab title="定义" id="first">
            <n-input
              :disabled="disabled"
              v-model="form.definition"
              type="textarea"
              placeholder="请输入内容"
            />
          </n-tab>
        </n-tabs>
      </div>
    </div>
  </section>
</template>

<script lang="jsx">
  export default {
    props: {
      cell: {
        type: Object,
        dufault: {},
      },
    },
    data() {
      return {
        columns: [
          {
            key: 'checked',
            dataKey: 'checked',
            width: 50,
            cellRenderer: ({ rowData }) => {
              const onChange = (value) => (rowData.checked = value)
              return <n-checkbox v-model={rowData.checked} onChange={onChange} />
            },
            headerCellRenderer: () => {
              const _data = this.data
              const onChange = (value) => {
                this.data = _data.map((row) => {
                  row.checked = value
                  return row
                })
              }
              let allSelected = _data.every((row) => row.checked)
              const containsChecked = _data.some((row) => row.checked)
              return (
                <n-checkbox
                  v-model={allSelected}
                  indeterminate={containsChecked && !allSelected}
                  onChange={onChange}
                />
              )
            },
          },
          {
            key: 'index',
            dataKey: 'index',
            title: '序号',
            width: 50,
            cellRenderer: ({ rowIndex }) => {
              return rowIndex + 1
            },
          },
          {
            key: 'mappingDictionId',
            dataKey: 'mappingDictionId',
            title: '是否标准',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return rowData.mappingDictionId ? <n-tag>是</n-tag> : '否'
            },
          },
          {
            key: 'name',
            dataKey: 'name',
            title: '属性名',
            width: 200,
            cellRenderer: ({ rowData }) => {
              return <n-input disabled={this.disabled} v-model={rowData.name} type='text' />
            },
          },
          {
            key: 'col',
            dataKey: 'col',
            title: '列名',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return <n-input disabled={this.disabled} v-model={rowData.col} type='text' />
            },
          },
          {
            key: 'dataType',
            dataKey: 'dataType',
            title: '数据类型',
            width: 150,
            cellRenderer: ({ rowData, rowIndex }) => {
              return (
                <n-select disabled={this.disabled} v-model={rowData.dataType} placeholder='请选择'>
                  {this.dataTypeOpt?.map((item) => {
                    return <n-option key={item.name} name={item.name} value={item.name} />
                  })}
                </n-select>
              )
            },
          },
          {
            key: 'length',
            dataKey: 'length',
            title: '长度',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return (
                <n-input
                  disabled={this.disabled}
                  v-model={rowData.length}
                  type='number'
                  placeholder=''
                />
              )
            },
          },
          {
            key: 'scale',
            dataKey: 'scale',
            title: '小数点',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return <n-input disabled={this.disabled} v-model={rowData.scale} type='number' />
            },
          },
          {
            key: 'pk',
            dataKey: 'pk',
            title: 'PK',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return <n-checkbox disabled={this.disabled} v-model={rowData.pk} />
            },
          },
          {
            key: 'notNull',
            dataKey: 'notNull',
            title: 'NotNull',
            width: 150,
            cellRenderer: ({ rowData }) => {
              return (
                <n-checkbox
                  disabled={
                    this.disabled || rowData.pk === true || rowData.notNullDisabled === true
                  }
                  v-model={rowData.notNull}
                />
              )
            },
          },
          {
            key: 'fk',
            dataKey: 'fk',
            title: 'FK',
            width: 50,
          },
          {
            key: 'uk',
            dataKey: 'uk',
            title: 'UK',
            width: 50,
          },
        ],
        filterText: '', // 搜索条件
        activeName: 'first',
        data: [],
        form: {
          defVal: '', // 默认值
          standard: '1', // 标准化
          charUsed: '1', // CharUsed
          protect: 'false', // 是否保护信息
          privacyClassify: '', // 隐私分类
          infoSecurityLevel: '', // 信息安全等级
          encryptFlag: 'false', // 是否加密
          encryption: '', // 加密方法
          dataOwner: '', // 数据所有者
          definition: '', // 定义
        },

        tabId: '',
        tabGuid: '',
        oldData: [], // 老数据
        showDialog: false,
        selectedData: [],
      }
    },
    computed: {
      // 视图与血缘不可编辑
      disabled() {
        // const { type, disableMove } = this.cell.data
        // return type === 'BLOOD' || type === 'VIEW' || disableMove
        return true
      },
      // 数据库类型
      kind() {
        const { kind } = this.cell.data
        return kind
      },
      // 数据类型
      dataTypeOpt() {
        return this.$store.state.app.dataTypeList?.data
      },
      // 选中属性
      multipleSelection() {
        return this.data.filter((d) => d.checked)
      },
    },
    watch: {
      filterText(n, o) {
        if (n === '') {
          this.clearFilter()
        }
      },
      cell(val) {
        this.initData()
      },
    },
    async created() {
      this.initData()
    },
    methods: {
      // 初始化数据
      initData() {
        try {
          let data = this.cell.data
          this.tabId = data.id
          this.tabGuid = data.guid
          // 判断列是否是外键
          // const { graph } = FlowGraph
          // const cells = graph?.toJSON()?.cells || []
          // 目标为当前节点的线
          // const edges = cells.filter(
          //   (cell) => cell.shape === 'edge' && cell.target.cell === this.cell.id,
          // )
          if (data.col[0]) {
            data.col[0].tabId = this.tabId
            data.col[0].tabGuid = this.tabGuid
          }
          this.data = [...data.col]

          this.form = this.data[0] || this.form
          if (this.form.charUsed && typeof this.form.charUsed === 'object') {
            this.form.charUsed = `${this.form.charUsed.code}`
          }
          this.oldData = this.data
          console.log('initData', this.data)
        } catch (e) {
          console.log(e)
        }
      },
      // 点击一行属性
      clickAttr({ rowData }) {
        this.form = rowData
      },
      // 过滤属性
      fitlerAttr() {
        this.data = this.data.filter((item) => {
          return item.name.indexOf(this.filterText) > -1
        })
      },
      // 清除过滤条件
      clearFilter() {
        this.data = this.oldData
      },
    },
  }
</script>

<style lang="scss" scoped>
  .column {
    padding: 10px 16px;

    input {
      width: 100%;
      border: none;
      outline: none;
    }
  }

  .operation {
    position: relative;
    display: flex;
  }
  .attrbute-input {
    width: 558px;
    margin-right: 14px;
  }
  .multipleTable {
    margin-top: 10px;
  }

  .other {
    display: flex;
    margin-top: 14px;

    &-left {
      width: 290px;
      height: 175px;
      margin-right: 10px;
      overflow: auto;
      border: 1px solid #cfcfcf;
    }
    &-right {
      width: calc(100% - 300px);

      :deep(.n-tabs) {
        height: 100%;

        .n-tabs__content {
          height: calc(100% - 31px);

          > div {
            height: 100%;
          }
          .n-textarea {
            height: 100%;

            &__inner {
              height: 100%;
              background: #fff;
              border: none;
            }
          }
        }
      }
    }
  }
  :deep(.nancalui-select) {
    height: unset;
  }
  :deep(.n-table-v2__row:hover) {
    background: rgba(37, 123, 255, 0.08) !important;
    cursor: pointer;
  }
  .containerBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    transform: translateY(30%);
    img {
      width: 216px;
    }
  }
</style>
