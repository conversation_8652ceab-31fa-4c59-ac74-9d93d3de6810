<template>
  <!-- <n-tooltip v-if="title" :content="title" position="bottom"> -->
  <svg aria-hidden="true" class="yy-icon">
    <use :xlink:href="symbolId" :fill="color">
      <title v-if="title">{{ title }}</title>
    </use>
  </svg>
  <!-- </n-tooltip> -->
</template>

<script lang="ts" setup>
  const props = defineProps({
    prefix: {
      type: String,
      default: 'icon',
    },
    icon: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: 'currentColor',
    },
  })
  const symbolId = computed(() => `#${props.prefix}-${props.icon}`)
</script>
<style lang="scss" scoped>
  .yy-icon {
    width: 1em;
    height: 1em;
    fill: currentColor;
    vertical-align: middle;
    color: currentColor;
    outline: none;
  }
  .tooltip-icon {
    width: 20px;
    height: 20px;
  }
</style>
