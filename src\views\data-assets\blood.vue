<template>
  <section class="container">
    <section class="container-box">
      <div class="container-left">
        <PublicLeftTree
          ref="publicTree"
          :data="treeData"
          :treeAttrData="treeAttrData"
          @clickNode="clickNode"
          @treeUpdateNode="treeUpdateNode"
          @treeDelNode="treeDelNode"
        />
      </div>
      <div class="container-right">
        <div id="trend-chart" class="trend-chart"></div>
      </div>
    </section>
  </section>
</template>

<script>
  import { Graph, Model } from '@antv/x6'
  import insertCss from 'insert-css'
  import { DagreLayout } from '@antv/layout'
  import { mapState } from 'vuex'

  export default {
    data() {
      return {
        dataModelId: '',
        treeData: [],
        treeAttrData: {
          showControl: false,
          showLeftIcon: true,
          dialogTitle: '模型层',
        },
        graph: null,
        data: null,
        dir: 'TB',
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    watch: {
      currentProject(val) {
        if (val.id) {
          this.getTreeData()
        }
        if (this.$route.query.id) {
          this.assetsLineage(this.$route.query.id)
        }
      },
    },

    mounted() {
      this.getTreeData()
      if (this.$route.query.id) {
        this.assetsLineage(this.$route.query.id)
      }
      this.graph = new Graph({
        container: document.getElementById('trend-chart'),
        interacting: false,
        panning: {
          enabled: true,
        },
        background: {
          color: '#fff', // 设置画布背景颜色
        },
        grid: {
          size: 18,
          visible: false,
          type: 'doubleMesh',
          args: [
            {
              color: '#eee', // 主网格线颜色
              thickness: 1, // 主网格线宽度
            },
            {
              color: '#ddd', // 次网格线颜色
              thickness: 1, // 次网格线宽度
              factor: 4, // 主次网格线间隔
            },
          ],
        },
      })
    },
    methods: {
      init(cnName) {
        let h = 54
        let w = 180
        const data = (Model.FromJSONData = {
          nodes: [],
          edges: [],
        })
        this.data.nodes.forEach((item, index) => {
          let name = ''
          let rank = JSON.parse(item.nenName)
          if (item.nname !== 'NULL') {
            name = JSON.parse(item.nname)
          }
          if (rank.length / 2 >= name.length) {
            w = rank.length * 11 + 10
          } else {
            w = name.length * 15 + 10
          }
          data.nodes.push({
            id: JSON.parse(item.nid),
            // x: data.x,
            // y: data.y,
            width: w,
            // width: name ? name.length * 15 : 100,
            height: h,
            label: name,
            shape: 'text-block',
            // text: name,
            markup: [
              {
                tagName: 'rect',
                selector: 'body',
              },
              {
                tagName: 'text',
                selector: 'rank',
              },
              {
                tagName: 'text',
                selector: 'name',
              },
            ],
            attrs: {
              body: {
                refWidth: '100%',
                refHeight: '100%',
                strokeWidth: 1,
                fill: cnName === rank ? '#ECF3FF' : '#FFFFFF',
                stroke: cnName === rank ? '#447DFD' : '#697A9A',
                rx: 4,
                ry: 4,
              },
              name: {
                text: name,
                refX: 0,
                refY: -10,
                fontSize: 12,
                fill: '#0C0C0C',
                fontWeight: '800',
              },
              rank: {
                text: rank,
                refX: 0,
                refY: 10,
                fontSize: 12,
                fill: '#0C0C0C',
              },
            },
          })
        })
        // debugger
        this.data.relationship.forEach((item, index) => {
          data.edges.push({
            source: JSON.parse(item.nto),
            target: JSON.parse(item.nfrom),
            connector: 'rounded',
            router: {
              name: 'manhattan',
              args: {
                startDirections: ['bottom'],
                endDirections: ['top'],
              },
            },
            attrs: {
              line: {
                stroke: '#333333',
                strokeWidth: 1,
                strokeDasharray: 5,
                targetMarker: 'classic',
                style: {
                  animation: 'ant-line 30s infinite linear',
                },
              },
            },
          })
        })

        const dagreLayout = new DagreLayout({
          type: 'dagre',
          rankdir: 'LR',
          align: 'UL',
          ranksep: 100,
          nodesep: 55,
        })
        const model = dagreLayout.layout(data)

        this.graph.fromJSON(model)

        // this.layout()

        // function flash(cell) {
        //   const cellView = _this.graph.findViewByCell(cell)
        //   if (cellView) {
        //     cellView.highlight()
        //     setTimeout(() => cellView.unhighlight(), 300)
        //   }
        // }

        // this.graph.on('signal', (cell) => {
        //   if (cell.isEdge()) {
        //     const view = this.graph.findViewByCell(cell)
        //     if (view) {
        //       const token = Vector.create('circle', { r: 5, fill: '#1890ff' })
        //       const target = cell.getTargetCell()
        //       setTimeout(() => {
        //         view.sendToken(token.node, 2000, () => {
        //           if (target) {
        //             _this.graph.trigger('signal', target)
        //           }
        //         })
        //       }, 300)
        //     }
        //   } else {
        //     flash(cell)
        //     const edges = _this.graph.model.getConnectedEdges(cell, {
        //       outgoing: true,
        //     })
        //     edges.forEach((edge) => _this.graph.trigger('signal', edge))
        //   }
        // })

        // let manual = false

        // this.graph.on('node:mousedown', ({ cell }) => {
        //   // manual = true
        //   this.graph.trigger('signal', cell)
        // })

        // let Cell = this.graph.getNodes()
        // const trigger = () => {
        //   _this.graph.trigger('signal', Cell[0])
        //   // if (!manual) {
        //   setTimeout(trigger, 2000)
        //   // }
        // }

        // trigger()

        // 关系线动画
        insertCss(`
        @keyframes ant-line {
          to {
              stroke-dashoffset: -1000
          }
        }
      `)
      },
      // 数据模型tree数据获取
      getTreeData() {
        this.$api.model.getApproveModelTree().then((res) => {
          if (res.success) {
            let parentNode = {
              children: [],
              // fullId: "1",
              // fullName: "全部",
              id: null,
              label: '全部',
              parentGroupId: '0',
            }
            this.treeData = []
            parentNode.children = res.data
            this.treeData.push(parentNode)
            this.clickNode(parentNode)
          }
        })
      },
      // 点击树节点
      clickNode(data) {
        this.dataModelId = data.id && data.id !== 60 ? data.id : null
        if (this.dataModelId) {
          this.assetsLineage(this.dataModelId, data.name)
        }
      },
      // 添加或修改节点
      treeUpdateNode(item) {
        let params = {
          description: item.ruleForm.desc,
          name: item.ruleForm.name,
          projectCode: this.currentProject.projectCode,
        }
        if (item.isEdit) {
          params.id = item.checkItem.id
          this.$api.model
            .updateDataModelTree(params)
            .then((res) => {
              if (res.success) {
                this.getTreeData()
                this.$refs.publicTree.clearFn()
                this.$message({
                  type: 'success',
                  message: '修改成功!',
                })
              }
            })
            .catch(this.$refs.publicTree.clearLoading())
        } else {
          this.$api.model
            .addDataModelTree(params)
            .then((res) => {
              if (res.success) {
                this.getTreeData()
                this.$refs.publicTree.clearFn()
                this.$message({
                  type: 'success',
                  message: '新增成功!',
                })
              }
            })
            .catch(this.$refs.publicTree.clearLoading())
        }
      },
      // 删除节点
      treeDelNode(item) {
        let params = {
          id: item.id,
        }

        this.$confirm('此操作将永久删除该目录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.$api.dataDev.sceneTreeDeleteCheck(params).then((res) => {
            if (res.success) {
              if (res.data === 'can delete!') {
                this.$api.dataDev.sceneTreeDelete(params).then((resp) => {
                  this.getTreeData()
                  this.$message({
                    type: 'success',
                    message: '删除成功!',
                  })
                })
              } else {
                this.$message({
                  type: 'error',
                  message: res.data,
                })
              }
            }
          })
        })
      },
      // 根据数据表追溯血缘关系
      assetsLineage(dataModelId, cnName) {
        let params = {
          // 测试数据
          // projectCode: 'test',
          // dataModelId:4,
          projectCode: this.currentProject.projectCode,
          dataModelId,
        }
        this.$api.assets.assetsLineage(params).then((res) => {
          if (res.success) {
            this.data = res.data
            if (res.data) {
              this.init(cnName)
              // this.layout()
            } else {
              this.graph.clearCells()
              this.$message.error('暂无数据')
            }
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    position: relative;
    padding: 10px;

    &-box {
      border-radius: 4px;
      display: flex;
    }

    &-left {
      box-sizing: border-box;
    }

    &-right {
      width: 100%;
      padding: 16px;
      box-sizing: border-box;
      overflow: hidden;
    }

    .trend-chart {
      width: calc(100% + 190px);
      height: 100%;
    }
  }
</style>
