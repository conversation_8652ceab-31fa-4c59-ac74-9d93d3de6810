<template>
  <div class="generativeRules">
    <div class="content-title">
      <span>选择表</span>
    </div>
    <div class="content">
      <div v-if="state.ruleType === 'ACROSS_TABLE'" class="add-box">
        <div>
          主表字段：
          <n-button class="add" variant="solid" @click.prevent="showAdd('PRIMARY')"
            ><SvgIcon icon="new-add" class="icon" title="添加" />添加字段</n-button
          >
        </div>
      </div>
      <div v-else class="add-box">
        <n-button class="add" variant="solid" @click.prevent="showAdd('PRIMARY')"
          ><SvgIcon icon="new-add" class="icon" title="添加" />添加{{
            state.ruleType === 'FIELD' ? '字段' : '表'
          }}</n-button
        >
        <div v-if="state.tableData.list.length > 0" class="table-title"
          >共选择{{ state.tableData.list.length
          }}{{ state.ruleType === 'FIELD' ? '个字段' : '张表' }}，系统将生成{{
            state.tableData.list.length
          }}条规则，规则生成成功后请创建质量任务进行规则监控。</div
        >
      </div>
      <n-public-table
        v-if="state.tableData.list.length > 0"
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :showPagination="false"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="80"
        :key="state.tableKey"
      >
        <template #pCName="{ editor }">
          <div class="col-title" :title="editor.row.pCName">{{ editor.row.pCName || '--' }}</div>
        </template>
        <template #cnName="{ editor }">
          <div class="col-title" :title="editor.row.cnName">{{ editor.row.cnName }}</div>
        </template>
        <template #pName="{ editor }">
          <div class="col-title" :title="editor.row.pName">{{ editor.row.pName }}</div>
        </template>
        <template #name="{ editor }">
          <div class="col-title" :title="editor.row.name">{{ editor.row.name }}</div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="delFn(editor.row, 'tableData')"
            >
              <n-tooltip class="tree-btn" content="删除" position="top">
                <SvgIcon icon="icon-table-delete" class="icon" title="删除" />
              </n-tooltip>
            </n-button>
          </div>
        </template>
      </n-public-table>
      <div v-if="state.ruleType === 'ACROSS_TABLE'" class="add-box">
        <div>
          比对字段：
          <n-button class="add" variant="solid" @click.prevent="showAdd('VICE')"
            ><SvgIcon icon="new-add" class="icon" title="添加" />添加字段</n-button
          >
        </div>
      </div>
      <n-public-table
        v-if="state.ruleType === 'ACROSS_TABLE' && state.tableData2.list.length > 0"
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :showPagination="false"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData2"
        :actionWidth="80"
        :key="state.tableKey"
      >
        <template #pCName="{ editor }">
          <div class="col-title" :title="editor.row.pCName">{{ editor.row.pCName || '--' }}</div>
        </template>
        <template #cnName="{ editor }">
          <div class="col-title" :title="editor.row.cnName">{{ editor.row.cnName }}</div>
        </template>
        <template #pName="{ editor }">
          <div class="col-title" :title="editor.row.pName">{{ editor.row.pName }}</div>
        </template>
        <template #name="{ editor }">
          <div class="col-title" :title="editor.row.name">{{ editor.row.name }}</div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="delFn(editor.row, 'tableData2')"
            >
              <n-tooltip class="tree-btn" content="删除" position="top">
                <SvgIcon icon="icon-table-delete" class="icon" title="删除" />
              </n-tooltip>
            </n-button>
          </div>
        </template>
      </n-public-table>
      <div
        v-if="state.ruleType === 'ACROSS_TABLE' && state.selectedData?.selectedModel?.length > 1"
        class="table-title"
        style="margin-top: 12px"
        >共选择2张表，系统将生成1条规则，规则生成成功后请验证规则并创建质量任务进行 规则监控。</div
      >
    </div>
    <add-field
      :isShow="state.showAddDrawer"
      :ruleType="state.ruleType"
      :applicableDataType="state.applicableDataType"
      :templeId="state.templeId"
      :fieldCate="state.fieldCate"
      :wideFieldType="state.wideFieldType"
      :selectedTableId="state.selectedTableId"
      :selectedData="state.selectedData"
      @close="closeAdd"
    />
  </div>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import addField from './add-field'
  import api from '@/api/index'
  const state = reactive({
    expandTableStatus: true,
    showAddDrawer: false,
    selectedTableId: '',
    wideFieldType: 'NUMBER',
    fieldCate: 'PRIMARY',
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'pCName', name: '中文名称', slot: 'pCName' },
      { prop: 'pName', name: '英文名称', slot: 'pName' },
      { prop: 'name', name: '字段名', slot: 'name' },
      { prop: 'templateUse', name: '此模版是否配置', width: 140 },
      { prop: 'total', name: '规则数量(启用/总数)', width: 180 },
    ],
    tableKey: 1,
    tableHeight: 438,
    tableData: { list: [] },
    tableData2: { list: [] },
    selectedData: {},
    ruleSource: 'TEMPLATE',
    ruleType: 'FIELD',
    applicableDataType: 'ALL',
    templeId: null,
  })

  //展开收起table内容
  const expandTable = () => {
    state.expandTableStatus = !state.expandTableStatus
  }

  const showAdd = (type) => {
    state.fieldCate = type
    state.selectedData.treeId = null
    if (state.ruleType === 'ACROSS_TABLE') {
      let name = 'tableData'
      state.selectedTableId = state.tableData2.list.map((val) => val.pid)[0] || ''
      if (type === 'VICE') {
        name = 'tableData2'
        state.selectedTableId = state.tableData.list.map((val) => val.pid)[0] || ''
      }
      state.selectedData.selectedModel = state[name].list.map((val) => {
        return {
          bizDomainId: val.bizDomainId || null,
          id: val.pid,
          name: val.pName,
          cnName: val.pCName,
          fieldCate: val.fieldCate || 'PRIMARY',
        }
      })
      state.selectedData.nowFieldsList = state[name].list.map((val) => {
        return {
          bizDomainId: val.bizDomainId || null,
          id: val.id,
          pid: val.pid,
          name: val.name,
          fieldCate: val.fieldCate || 'PRIMARY',
        }
      })
    } else {
      state.selectedData.selectedModel = state.tableData.list.map((val) => {
        return {
          bizDomainId: val.bizDomainId || null,
          id: val.pid,
          name: val.pName,
          cnName: val.pCName,
          fieldCate: val.fieldCate || 'PRIMARY',
        }
      })
      let map = new Map()
      state.selectedData.selectedModel = state.selectedData.selectedModel.filter(
        (v) => !map.has(v.id) && map.set(v.id, v),
      )
      state.selectedData.nowFieldsList = state.tableData.list.map((val) => {
        return {
          bizDomainId: val.bizDomainId || null,
          id: val.id,
          pid: val.pid,
          name: val.name,
          fieldCate: val.fieldCate || 'PRIMARY',
        }
      })
    }
    if (state.selectedData?.selectedModel?.length > 0) {
      state.selectedData.treeId = state.selectedData.selectedModel[0].bizDomainId || null
    }
    console.log(state.selectedData)
    state.showAddDrawer = true
  }

  const delFn = (item, name) => {
    state[name].list = state[name].list.filter((val) => val.id !== item.id)
  }

  // 获取数据
  const getAllData = () => {
    let data = []
    state.tableData.list.forEach((val) => {
      data.push({
        fieldId: state.ruleType !== 'TABLE' ? val.id : null,
        fieldName: state.ruleType !== 'TABLE' ? val.name : null,
        tableId: state.ruleType !== 'TABLE' ? val.pid : val.id,
        tableName: state.ruleType !== 'TABLE' ? val.pName : val.name,
        bizDomainId: val.bizDomainId,
        fieldCate: val.fieldCate || 'PRIMARY',
      })
    })
    state.tableData2.list.forEach((val) => {
      data.push({
        fieldId: state.ruleType !== 'TABLE' ? val.id : null,
        fieldName: state.ruleType !== 'TABLE' ? val.name : null,
        tableId: state.ruleType !== 'TABLE' ? val.pid : val.id,
        tableName: state.ruleType !== 'TABLE' ? val.pName : val.name,
        bizDomainId: val.bizDomainId,
        fieldCate: val.fieldCate || 'PRIMARY',
      })
    })
    return data
  }

  // 编辑时回显数据
  const editInit = (item, isEdit = false) => {
    if (item.ruleType === 'ACROSS_TABLE') {
      state.tableHeight = 90
    }
    if (item?.fieldRangeConfig?.rangeType === 'DATE_RANGE') {
      state.wideFieldType = 'DATETIME'
    }
    state.ruleSource = item.ruleSource
    state.templeId = item.templeId
    state.ruleType = item.ruleType || 'FIELD'
    state.applicableDataType = item.applicableDataType || 'ALL'
    if (state.ruleType === 'TABLE') {
      state.tableHeadTitles = [
        // 必须为name 否则渲染不出表头
        { prop: 'cnName', name: '中文名称', slot: 'cnName' },
        { prop: 'name', name: '英文名称', slot: 'name' },
        { prop: 'templateUse', name: '此模版是否配置', width: 160 },
        { prop: 'total', name: '规则数量(启用/总数)', width: 160 },
      ]
    }
    state.tableKey++
    let data = item.ruleTableFieldList
    if (data.length > 0) {
      let editData = {
        treeId: data[0].bizDomainId,
        selectedModel: data.map((val) => {
          return {
            bizDomainId: val.bizDomainId,
            id: val.tableId,
            name: val.tableName,
            cnName: val.tableCnName,
            fieldCate: val.fieldCate || 'PRIMARY',
          }
        }),
        nowFieldsList: data.map((val) => {
          return {
            id: val.fieldId,
            name: val.fieldName,
            pid: val.tableId,
            fieldCate: val.fieldCate || 'PRIMARY',
          }
        }),
      }
      closeAdd(editData, isEdit)
    }
  }

  // 关闭添加字段/表弹窗回调`
  const closeAdd = (item, isEdit = false) => {
    if (item) {
      state.selectedData = { ...item }
      let data = {
        tableId: item.selectedModel.map((val) => val.id),
        templateId: state.templeId,
      }
      api.dataQuality.getRuleStatistics(data).then((res) => {
        if (res.code === 'SUCCESS') {
          let tableData = item.selectedModel.map((val) => {
            val.total = '0/0'
            val.bizDomainId = item.treeId
            res.data.forEach((item) => {
              if (item.tableId === val.id) {
                val.total = item.enableNum + '/' + item.total
                val.templateUse = item.templateUse ? '是' : '否'
              }
            })
            return val
          })
          let fieldData = item.nowFieldsList.map((val) => {
            item.selectedModel.forEach((v) => {
              if (val.pid === v.id) {
                val.pName = v.name
                val.pCName = v.cnName
                val.total = v.total
                val.templateUse = v.templateUse ? '是' : '否'
                val.bizDomainId = item.treeId
              }
            })
            return val
          })
          if (state.ruleType !== 'TABLE') {
            if (fieldData.length > 0) {
              // 如果是编辑
              if (isEdit) {
                state.tableData.list = fieldData.filter((val) => val.fieldCate === 'PRIMARY')
                state.tableData2.list = fieldData.filter((val) => val.fieldCate === 'VICE')
              } else {
                if (fieldData[0].fieldCate === 'PRIMARY') {
                  state.tableData.list = fieldData
                } else {
                  state.tableData2.list = fieldData
                }
              }
            }
          } else {
            state.tableData.list = tableData
          }
        }
      })
    } else {
      state.showAddDrawer = false
    }
  }

  defineExpose({
    getAllData,
    editInit,
  })
</script>

<style lang="scss" scoped>
  .generativeRules {
    position: relative;
    .content-title {
      position: relative;
      height: 30px;
      margin-bottom: 16px;
      padding-left: 14px;
      color: #1d2129;
      font-weight: 500;
      font-size: 16px;
      line-height: 30px;
      background-color: #f2f6fc;
      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }
    }
    .add-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      .add {
        width: 94px;
      }
      .icon {
        margin-right: 4px;
        font-size: 16px;
      }
    }
    .table-title {
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;
    }
  }
  :deep(.nancalui-table) {
    .header-container {
      padding: 0 0 0 20px !important;
      .title {
        max-width: 100%;
      }
    }
    .col-title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
