<template>
  <div class="container">
    <div class="content-box">
      <div class="content-box-title">
        <div class="search-box">
          <span class="label">项目名称：</span>
          <n-input v-model="state.searchData.condition.projectName" placeholder="请输入项目名称" />
          <span class="label">项目简介说明：</span>
          <n-input
            v-model="state.searchData.condition.projectDesc"
            placeholder="请输入项目简介说明"
          />
        </div>
        <div class="search-box">
          <div class="search-btn" @click="searchFn">
            <n-button color="primary" variant="solid">搜索</n-button>
          </div>
          <div class="add-btn" @click="addFn">
            <n-button color="primary" variant="solid">新增场景</n-button>
          </div>
        </div>
      </div>
      <div class="content-box-row">
        <div class="content-box-row-box">
          <div class="tabs">
            <div
              :class="['tabs-item', state.tabsActive === 'project' ? 'active' : '']"
              @click="tabsActiveFn('project')"
            >
              项目列表
            </div>
            <div
              :class="['tabs-item', state.tabsActive === 'collect' ? 'active' : '']"
              @click="tabsActiveFn('collect')"
            >
              我的收藏
            </div>
          </div>

          <div class="project-list" v-loading="state.loading">
            <div v-for="(item, i) in state.projectList" class="project-list-item">
              <div class="flex-box">
                <div
                  class="flex-box-item title"
                  @click="router.push({ name: 'taskDetail', query: { id: item.id } })"
                  ><div class="titleText" :title="item.projectName">{{ item.projectName }}</div>
                  <div
                    :class="[
                      'datasourceSecret',
                      secretTransition(item.confidentialityLevel).iconClassName,
                    ]"
                    >{{ secretTransition(item.confidentialityLevel).name }}</div
                  >
                </div>
                <div class="flex-box-item btnBox">
                  <!-- 项目投资 -->
                  <div v-if="state.tabsActive === 'project'">
                    <n-button
                      v-if="state.userId === item.createBy"
                      style="margin-right: 10px; margin-left: 0"
                      @click.stop.prevent="editFn(item)"
                    >
                      编辑
                    </n-button>
                    <n-button style="margin-left: 0" @click.stop.prevent="collectFn(item)">
                      收藏
                    </n-button>
                  </div>

                  <n-button
                    v-else
                    style="margin-left: 10px"
                    @click.stop.prevent="cancelCollectFn(item)"
                  >
                    取消收藏
                  </n-button>
                </div>
              </div>
              <div class="flex-box">
                <div class="flex-box-item first"
                  >项目组长：<span>{{ item.projectLeader }}</span></div
                >
                <div class="flex-box-item"
                  >创建时间：<span>{{ item.projectCreateDate }}</span></div
                >
                <div class="flex-box-item"
                  >更新时间：<span>{{ item.projectUpdateDate }}</span></div
                >
                <div class="flex-box-item end"
                  >募集资金：{{ item.raiseFunds || '--' }}元
                  <n-button
                    v-if="state.tabsActive === 'project'"
                    color="primary"
                    variant="solid"
                    style="margin-left: 10px"
                    @click.stop.prevent="investFn(item.id)"
                  >
                    项目投资
                  </n-button>
                </div>
              </div>
              <div class="flex-box flex-start">
                <div class="flex-box-item first"
                  >标注类型：<span>{{ item.annotationType }}</span></div
                >
                <div class="flex-box-item"
                  >数据量：<span>{{ item.taskNum }}</span></div
                >
              </div>
              <div class="flex-box">
                <div class="flex-box-item onlyOne">项目简介说明：{{ item.projectDesc }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="tabs-box">
          <div class="content-box-footer">
            <div class="title">我参与的</div>
            <div v-loading="state.loading" class="table-box">
              <CfTable
                :key="state.key"
                :isDisplayAction="false"
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.total,
                  pageSize: searchData.pageSize,
                  currentPage: searchData.pageNum,
                  onCurrentChange: (v) => {
                    searchData.pageNum = v
                    onSearch(false, state.tabs)
                  },
                  onSizeChange: (v) => {
                    searchData.pageSize = v
                    onSearch(true, state.tabs)
                  },
                }"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                  'header-cell-style': {
                    'font-weight': 'bolder',
                  },
                }"
              >
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-drawer
      v-model="state.showDrawer"
      title=""
      :size="560"
      :close-on-click-overlay="true"
      :before-close="closeDrawer"
      class="template-config-drawer"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">标注场景配置</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="state.showDrawer = false" />
        </div>
        <div class="n-drawer-body-content">
          <n-form
            ref="ruleForm"
            :data="state.ruleForm"
            :rules="state.rules"
            label-width="150px"
            label-align="start"
          >
            <n-form-item label="项目名称：" field="projectName">
              <n-input
                v-model="state.ruleForm.projectName"
                placeholder="请输入项目名称"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="审核团队:" field="teamId">
              <n-select
                v-model="state.ruleForm.teamId"
                placeholder="请选择团队"
                filter
                allow-clear
                :options="state.teamList"
              />
            </n-form-item>
            <n-form-item label="牵头单位：" field="leadingUnit">
              <n-input
                v-model="state.ruleForm.leadingUnit"
                placeholder="请输入牵头单位"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="项目状态：" field="projectStatus">
              <n-select
                v-model="state.ruleForm.projectStatus"
                placeholder="请选择项目状态"
                filter
                allow-clear
                :options="[
                  { name: '已立项', value: '已立项' },
                  { name: '标注中', value: '标注中' },
                  { name: '已结束', value: '已结束' },
                ]"
              />
            </n-form-item>
            <n-form-item label="密级：" field="confidentialityLevel">
              <n-select
                v-model="state.ruleForm.confidentialityLevel"
                placeholder="请选择项目状态"
                filter
                allow-clear
                :options="[
                  { name: '公开', value: 'PUBLIC' },
                  { name: '内部', value: 'INTERIOR' },
                  { name: '受控', value: 'CONTROLLED' },
                  { name: '秘密', value: 'SECRET' },
                  { name: '机密', value: 'CONFIDENTIAL' },
                ]"
              />
            </n-form-item>

            <!-- 标注标签 -->
            <n-form-item label="标注标签：" field="labels">
              <el-tree-select
                ref="selectTree"
                v-model="state.selectedTags"
                placeholder="请选择标签"
                :data="state.tagOptions"
                style="width: 100%"
                node-key="key"
                :props="{
                  label: 'name',
                  value: 'key',
                  children: 'children',
                }"
                @change="handleTagSelect"
                check-on-click-node
                collapse-tags
                collapse-tags-tooltip
                max-collapse-tags="10"
                multiple
                show-checkbox
                :render-after-expand="false"
                filterable
                clearable
              />
            </n-form-item>
            <!-- 募集资金 -->
            <n-form-item label="募集资金：" field="raiseFunds">
              <n-input
                v-model="state.ruleForm.raiseFunds"
                placeholder="请输入募集资金"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="项目简介：" field="projectDesc">
              <n-textarea
                v-model="state.ruleForm.projectDesc"
                placeholder="请输入项目简介"
                maxlength="500"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <!--            <n-form-item label="实施路径：" field="implementApproach">-->
            <!--              <n-textarea-->
            <!--                v-model="state.ruleForm.implementApproach"-->
            <!--                placeholder="请输入实施路径"-->
            <!--                maxlength="200"-->
            <!--                :autosize="{ minRows: 3 }"-->
            <!--                resize="both"-->
            <!--                show-count-->
            <!--              />-->
            <!--            </n-form-item>-->
            <n-form-item label="数据集名称：" field="datasetName">
              <n-input
                v-model="state.ruleForm.datasetName"
                placeholder="请输入数据集名称"
                maxlength="500"
              />
            </n-form-item>
            <!-- 数据类型 -->
            <n-form-item label="数据类型：" field="dataType">
              <n-select
                v-model="state.ruleForm.dataType"
                placeholder="请选择数据类型"
                filter
                allow-clear
                :options="[
                  { name: '图片', value: '图片' },
                  { name: '文档', value: '文档' },
                  { name: '音频', value: '音频' },
                  { name: '视频', value: '视频' },
                ]"
              />
            </n-form-item>
            <n-form-item label="标注类型：" field="annotationType">
              <n-select
                v-model="state.ruleForm.annotationType"
                placeholder="请选择标注类型"
                filter
                allow-clear
                :options="[
                  { name: '通识标注', value: '通识标注' },
                  { name: '专识标注', value: '专识标注' },
                ]"
              />
            </n-form-item>
            <!-- 计价策略 -->
            <n-form-item label="计价策略：" field="rewardDesc">
              <n-input
                v-model="state.ruleForm.rewardDesc"
                placeholder="请输入计价策略"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="来源流程：" field="sourceProcess">
              <n-input
                v-model="state.ruleForm.sourceProcess"
                placeholder="请输入来源流程"
                maxlength="200"
              />
            </n-form-item>
            <n-form-item label="应用流程：" field="applicationProcess">
              <n-input
                v-model="state.ruleForm.applicationProcess"
                placeholder="请输入应用流程"
                maxlength="200"
              />
            </n-form-item>
            <!--            <n-form-item label="项目发起部门：" field="dataInitiateDepartment">-->
            <!--              <n-input-->
            <!--                v-model="state.ruleForm.dataInitiateDepartment"-->
            <!--                placeholder="请输入项目发起部门"-->
            <!--                maxlength="500"-->
            <!--              />-->
            <!--            </n-form-item>-->
            <n-form-item label="背景及问题：" field="backgroundProblems">
              <n-textarea
                v-model="state.ruleForm.backgroundProblems"
                placeholder="请输入背景及问题"
                maxlength="500"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <!-- 标注目标 -->
            <n-form-item label="标注目标：" field="markTarget">
              <n-textarea
                v-model="state.ruleForm.markTarget"
                placeholder="请输入标注目标"
                maxlength="500"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <n-form-item label="结果展示：" field="resultPresentation">
              <n-input
                v-model="state.ruleForm.resultPresentation"
                placeholder="请输入展示结果URL"
              />
            </n-form-item>
          </n-form>
        </div>
        <div class="n-drawer-body-footer">
          <n-button @click.prevent="state.showDrawer = false">取 消</n-button>
          <n-button variant="solid" @click="submitFn">保 存</n-button>
        </div>
      </div>
    </n-drawer>
    <!-- 项目投资 -->
    <n-modal
      v-model="state.showInvestModal"
      title="项目投资"
      :width="600"
      :esc-key-closeable="false"
      :close-on-click-overlay="true"
      :before-close="
        () => {
          state.showInvestModal = false
        }
      "
    >
      <n-form
        :data="state.formData"
        label-width="100px"
        ref="formRef"
        style="height: 200px; display: flex; flex-direction: column; justify-content: center"
      >
        <!-- 投资金额 -->
        <n-form-item
          label="投资金额："
          field="raiseFunds"
          :rules="[{ required: true, message: '请输入投资金额', trigger: 'blur', type: 'number' }]"
        >
          <el-input-number
            v-model="state.formData.raiseFunds"
            :min="1"
            :step="1000"
            placeholder="请输入投资金额"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-modal-footer class="dialog-footer cenetr-footer">
          <!-- 确定 -->
          <n-button color="primary" variant="solid" @click.prevent="submitInvestFn">
            确定
          </n-button>
          <!-- 取消 -->
          <n-button color="primary" @click.prevent="state.showInvestModal = false"> 取消 </n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import api from '@/api'
  import { reactive, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import overview from './components/overview'
  import theme from './components/theme'
  import rule from './components/rule'
  import problem from './components/problem'
  import plan from './components/plan'
  import scene from './components/scene'
  import {
    workbenchDefinitionList,
    workbenchProjectDetail,
    workbenchAnnotationProjectDetail,
  } from '@/api/dataManage.js'

  const store = useStore()
  const router = useRouter()
  const ruleForm = ref(null)
  const formRef = ref(null)
  const selectTree = ref(null)
  const state = reactive({
    showDrawer: false,
    tabsActive: 'project',
    loading: false,
    id: null,
    selectedTags: [], // 已选择的标签
    tagOptions: [], // 标签树
    checkedTabs: 0,
    formData: {},
    ruleForm: {
      projectName: '',
      teamId: '',
      leadingUnit: '',
      projectStatus: '',
      confidentialityLevel: '',
      raiseFunds: '',
      projectDesc: '',
      implementApproach: '',
      datasetName: '',
      dataType: '',
      rewardDesc: '',
      sourceProcess: '',
      applicationProcess: '',
      resultPresentation: '',
      annotationType: '',
      dataInitiateDepartment: '',
      backgroundProblems: '',
      markTarget: '',
      labels: '', // 标注标签
    },
    rules: {
      projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
      teamId: [{ required: true, message: '请选择团队', trigger: 'blur', type: 'number' }],
      leadingUnit: [{ required: true, message: '请输入牵头单位', trigger: 'blur' }],
      projectStatus: [{ required: true, message: '请选择项目状态', trigger: 'blur' }],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'blur' }],
      labels: [{ required: true, message: '请选择标注标签', trigger: 'change' }],
      raiseFunds: [{ required: true, message: '请输入募集资金', trigger: 'blur' }],
      projectDesc: [{ required: true, message: '请输入项目简介', trigger: 'blur' }],
      implementApproach: [{ required: true, message: '请输入实施路径', trigger: 'blur' }],
      datasetName: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
      dataType: [{ required: true, message: '请选择数据类型', trigger: 'blur' }],
      annotationType: [{ required: true, message: '请选择标注类型', trigger: 'blur' }],
      rewardDesc: [{ required: true, message: '请输入计价策略', trigger: 'blur' }],
      sourceProcess: [{ required: true, message: '请输入来源流程', trigger: 'blur' }],
      applicationProcess: [{ required: true, message: '请输入应用流程', trigger: 'blur' }],
      dataInitiateDepartment: [{ required: true, message: '请输入项目发起部门', trigger: 'blur' }],
      backgroundProblems: [{ required: true, message: '请输入背景及问题', trigger: 'blur' }],
      markTarget: [{ required: true, message: '请输入标注目标', trigger: 'blur' }],
      resultPresentation: [{ required: true, message: '请输入结果展示', trigger: 'blur' }],
    },
    info: {
      groupLeader: '张三',
      createTime: '2024-03-19',
      updateTime: '2024-03-19',
      introduction: '这是一个项目简介',
    },
    searchData: {
      condition: {
        projectDesc: '',
        projectName: '',
      },
      pageNum: 1,
      pageSize: 10,
    },
    tableData: {
      list: [],
    },
    tableHeadTitles: [
      { prop: 'projectName', name: '项目名称', minWidth: '30%' },
      { prop: 'customedCategoryName', name: '任务名称', minWidth: '30%' },
      { prop: 'completionTime', name: '预计完成时间', minWidth: '20%' },
      { prop: 'auditStatus', name: '当前进展', minWidth: '20%' },
    ],
    pageInfo: {
      total: 1,
    },
    projectList: [],
    // 团队列表
    teamList: [],
  })

  state.userId = computed(() => {
    return store.state.user.id
  })

  // 获取团队列表
  const getTeamList = () => {
    api.dataGovernance.getTeamList({ type: 'ALL' }).then((res) => {
      // 成功
      if (res.success) {
        state.teamList = (res.data || []).map((item) => {
          return { name: item.name, value: item.id }
        })
      }
    })
  }
  const addFn = () => {
    state.ruleForm = {}
    state.showDrawer = true
  }

  // 标签选择事件
  const handleTagSelect = (values) => {
    let activeOptions = selectTree.value?.getCheckedNodes() || []
    state.ruleForm.labels = JSON.stringify(activeOptions.filter((val) => val.nodeType === 'tag'))
  }

  // 获取标签列表
  const getTargetList = () => {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        // 定义递归过滤函数
        const filterTree = (nodes) => {
          if (!Array.isArray(nodes)) {
            return []
          }
          return nodes.filter((node) => {
            if (node.nodeType === 'category' && node.children === null) {
              return false
            }
            if (node.children) {
              node.children = filterTree(node.children)
            }
            return true
          })
        }

        // 过滤数据
        const filteredData = filterTree(data)

        // 正则删除过滤后数据中的disabled属性和值
        const strTree = JSON.stringify(filteredData)
        state.tagOptions = JSON.parse(strTree.replace(/"disabled"/g, '"d"')) // 避免禁用
      }
    })
  }

  const searchData = ref({
    // 搜索数据
    pageNum: 1, // 当前页
    pageSize: 10, // 每页显示条数
    condition: {},
  })

  const onSearch = (reset = true) => {
    // 搜索
    if (reset) {
      // 重置页码
      searchData.value.pageNum = 1
    }
    api.dataManagement
      .workbenchAnnotationTaskSearch(searchData.value)
      .then((res) => {
        if (res.success) {
          state.tableData = res.data
          state.total = res.data.total

          state.governNum = res.data.total
          state.key++
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }
  const checkTabsFn = (index) => {
    state.checkedTabs = index
  }

  // 获取详情
  const getDetailFn = () => {
    workbenchProjectDetail({ id: state.id })
      .then((res) => {
        if (res.success) {
          state.info = res.data
        }
      })
      .catch(() => {})
  }

  const joinFn = () => {}
  const investFn = (id) => {
    state.showInvestModal = true
    state.formData = { id }
  }

  const closeDrawer = () => {
    return false
    // 关闭弹窗
    state.showDrawer = false
  }

  //匹配密级
  const secretTransition = (status) => {
    let name = '公开'
    let iconClassName = 'blue1'

    switch (status) {
      case 'INTERIOR':
        name = '内部'
        iconClassName = 'yellow'
        break
      case 'CONTROLLED':
        name = '受控'
        iconClassName = 'blue2'
        break
      case 'SECRET':
        name = '秘密'
        iconClassName = 'red2'
        break
      case 'CONFIDENTIAL':
        name = '机密'
        iconClassName = 'red1'
        break
      case 'CORE':
        name = '核心'
        iconClassName = 'blue3'
        break
      default:
        break
    }

    return { name, iconClassName }
  }

  // 获取项目列表
  const searchFn = () => {
    // if (!state.pageInfo.total || state.loading) {
    //   return
    // }
    state.loading = true
    if (state.tabsActive === 'collect') {
      api.dataGovernance
        .getProjectCollection({
          condition: {
            projectCode: '',
          },
          pageNum: 1,
          pageSize: 300,
        })
        .then((res) => {
          if (res.success) {
            state.projectList = res.data.list || []
            state.pageInfo.total = res.data.total
          }
        })
        .finally(() => {
          state.loading = false
        })
    } else {
      api.dataGovernance
        .getProjectAll(state.searchData.condition)
        .then((res) => {
          if (res.success) {
            state.projectList = res.data || []
            state.pageInfo.total = res.data.total
          }
        })
        .finally(() => {
          state.loading = false
        })
    }
  }

  const load = () => {
    state.searchData.pageNum++
    // searchFn()
  }
  const submitInvestFn = () => {
    formRef.value.validate((valid) => {
      if (!valid) {
        return false
      }
      state.showInvestModal = false
      // 提交投资
      api.dataGovernance.getRaisedFunds(state.formData).then((res) => {
        if (res.success) {
          ElMessage.success('投资成功')
          searchFn()
        }
      })
    })
  }

  const submitFn = () => {
    ruleForm.value.validate((valid) => {
      if (!valid) {
        return false
      }

      let data = { ...state.ruleForm }
      if (data.id) {
        api.dataManagement.workbenchAnnotationProjectUpdate(data).then((res) => {
          if (res.success) {
            state.showDrawer = false
            ElMessage.success('编辑成功')
            searchFn()
          }
        })
      } else {
        // 提交
        api.dataGovernance
          .addProject(state.ruleForm)
          .then((res) => {
            if (res.success) {
              state.showDrawer = false
              ElMessage.success('新增成功')
              searchFn()
            }
          })
          .catch(() => {})
      }
    })
  }

  // 切换tabs
  const tabsActiveFn = (type) => {
    state.tabsActive = type
    state.projectList = []
    searchFn()
  }

  // 编辑
  const editFn = (item) => {
    state.ruleForm = { ...item }
    state.showDrawer = true
    projectDetailFn(item)
  }

  // 项目详情
  const projectDetailFn = async (item) => {
    const res = await workbenchAnnotationProjectDetail({ id: item.id })
    if (res.success) {
      state.ruleForm.labels = res.data.labels
      const parsedLabels = JSON.parse(res.data.labels) || []
      state.selectedTags = parsedLabels.map((item) => item.key)
    }
  }

  // 收藏
  const collectFn = (item) => {
    api.dataGovernance.addProjectCollection({ projectCode: item.projectCode }).then((res) => {
      if (res.success) {
        ElMessage.success('收藏成功')
      }
    })
  }

  // 取消收藏
  const cancelCollectFn = (item) => {
    api.dataGovernance.deleteProjectCollection({ projectCode: item.projectCode }).then((res) => {
      if (res.success) {
        ElMessage.success('取消收藏成功')
        searchFn()
      }
    })
  }

  onMounted(() => {
    // state.id = router.currentRoute.value.query.id || null
    // getDetailFn
    searchFn()
    getTeamList()
    onSearch()
    getTargetList()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    width: 100%;
    height: calc(100vh - 96px);
    padding: 16px;

    .content-box {
      height: 100%;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 10px;
        padding: 0 16px;
        background-color: #ffffff;

        .search-box {
          display: flex;
          .label {
            color: #1d2129;
            font-size: 14px;
            line-height: 32px;
            font-weight: normal;
          }
          .nancalui-input {
            width: 260px;
            margin-right: 32px;
          }
          .search-btn {
            margin-right: 12px;
          }
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 86px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;

          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }

      &-row {
        height: calc(100% - 62px);
        &-box {
          background-color: #fff;
          border-radius: 2px;
        }
        .tabs {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 16px;
          border-bottom: 1px solid #c5d0ea;
          padding: 10px 16px;
          box-sizing: border-box;

          &-item {
            font-size: 16px;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            cursor: pointer;

            &:hover {
              color: #1e89ff;
              border-bottom: 2px solid #1e89ff;
            }

            &.active {
              color: #1e89ff;
              border-bottom: 2px solid #1e89ff;
            }
          }
        }

        .flex-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          &.flex-start {
            justify-content: flex-start;
          }

          > .title {
            width: 70%;
            font-size: 16px;
            color: #1d2129;
            cursor: pointer;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .titleText {
              //  下划线
              //text-decoration: underline;
              max-width: calc(100% - 80px);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
              font-weight: bolder;
              font-size: 20px;
              &:hover {
                color: #479dff;
              }
            }
            .datasourceSecret {
              box-sizing: border-box;
              width: 40px;
              height: 20px;
              color: rgba(0, 0, 0, 0.75);
              font-size: 12px;
              line-height: 18px;
              text-align: center;
              background-color: #f4f4f5;
              border: 1px solid rgba(177, 179, 184, 0.53);
              border-radius: 2px;
              margin-left: 8px;
              &.yellow {
                color: #fe8624;
                background-color: #fff4e6;
                border: 1px solid #ffba70;
              }
              &.blue1 {
                color: #1e89ff;
                background-color: #ebf4ff;
                border: 1px solid #99c9ff;
              }
              &.blue2 {
                color: #1aa4ee;
                border: 1px solid rgba(26, 164, 238, 0.4);
                background: rgba(26, 164, 238, 0.08);
              }
              &.blue3 {
                color: #224ecd;
                border: 1px solid rgba(34, 78, 205, 0.4);
                background: rgba(34, 78, 205, 0.08);
              }
              &.red1 {
                color: #7a0000;
                border: 1px solid rgba(122, 0, 0, 0.4);
                background: rgba(122, 0, 0, 0.08);
              }
              &.red2 {
                color: #d40000;
                border: 1px solid #ef7777;
                background: #ffeded;
              }
            }
          }

          &-item {
            width: 25%;
            flex-shrink: 0;
            overflow: hidden;
            color: #606266;
            font-weight: normal;
            font-size: 18px;
            white-space: nowrap;
            text-overflow: ellipsis;

            &.onlyOne {
              width: 100%;
            }
            &.btnBox {
              width: 30%;
              display: flex;
              justify-content: flex-end;
              align-items: center;
              padding-right: 10px;
              box-sizing: border-box;
            }
            &.first {
              width: 20%;
            }
            &.end {
              width: 30%;
            }
            span {
              font-weight: bolder;
              color: #1d2129;
            }
          }
        }

        .project-list {
          height: 370px;
          overflow-y: auto;
          padding: 10px 12px;
          box-sizing: border-box;
          &-item {
            border-bottom: 1px solid #c5d0ea;
            padding: 10px 12px;
            margin-bottom: 8px;
            box-sizing: border-box;
            &:hover {
              border-bottom: 1px solid #fff;
              border-radius: 6px;
              box-shadow: 0 4px 16px -2px rgba(30, 47, 85, 0.15);
            }
          }
        }

        .tabs-box {
          position: relative;
          width: 100%;
          height: calc(100% - 426px);
          padding: 0 12px;
          margin-top: 10px;
          background-color: #ffffff;
          border-radius: 2px;
          box-sizing: border-box;

          .content-box-footer {
            height: 100%;

            .title {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              height: 36px;
              position: relative;
              padding: 0 12px;
              color: #1d2129;
              font-weight: 500;
              font-size: 16px;
              &:before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 4px;
                height: 18px;
                margin: auto;
                background: #1e89ff;
                content: '';
              }
            }

            .table-box {
              height: calc(100% - 46px);
              margin-top: 10px;
            }
          }
        }
      }
    }
  }
</style>
