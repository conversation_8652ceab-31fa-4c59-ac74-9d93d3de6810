<template>
  <!-- 结构化数据采集  -->
  <div class="doc-collection">
    <div
      :class="{
        'doc-collection-add': true,
        container: true,
        isLzos: state.isLzos,
      }"
    >
      <div class="add-box">
        <div class="page-title">
          新增采集
          <div class="detail-back-box" @click.prevent="cancel"> 返回 </div>
        </div>

        <div class="box-content" style="overflow: auto">
          <div class="box-content-inside">
            <div class="inside-box inside-box-content">
              <div class="content-title">
                <span>数据源信息</span>
              </div>
              <n-form
                v-if="state.basicTitle"
                ref="sourceForm"
                class="base-form"
                :data="state.sourceForm"
                :rules="state.sourceRules"
                label-width="120px"
                label-align="start"
                label-suffix="："
              >
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="host" label="IP">
                      <n-input v-model="state.sourceForm.host" maxLength="50" />
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="port" label="端口">
                      <n-input v-model="state.sourceForm.port" maxLength="50" />
                    </n-form-item>
                  </n-col>
                </n-row>
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="username" label="用户名">
                      <n-input v-model="state.sourceForm.username" maxLength="50" />
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="password" label="密码">
                      <n-input v-model="state.sourceForm.password" show-password maxLength="50" />
                    </n-form-item>
                  </n-col>
                </n-row>
                <n-row :gutter="20">
                  <n-col span="10">
                    <n-form-item field="filepath" label="提取地址">
                      <n-input v-model="state.sourceForm.filepath" maxLength="100" />
                    </n-form-item>
                  </n-col>
                  <n-col style="margin-left: 8px" span="10">
                    <n-button @click.prevent="onSearch" color="primary"> 查询文件</n-button>
                  </n-col>
                </n-row>
              </n-form>
            </div>
            <div class="inside-box inside-box-content">
              <div class="content-title">
                <span>文件信息</span>
              </div>
              <div class="inline-table" v-loading="state.isLoad">
                <CfTable
                  :table-head-titles="state.tableHeadTitles"
                  :tableConfig="{
                    data: state.tableList,
                    rowKey: 'id',
                  }"
                  :paginationConfig="{
                    total: state.pagination.total,
                    pageSize: state.pagination.pageSize,
                    currentPage: state.pagination.currentPage,
                    onCurrentChange: (v) => {
                      state.pagination.currentPage = v
                      onSearch(false)
                    },
                    onSizeChange: (v) => {
                      state.pagination.pageSize = v
                      onSearch()
                    },
                  }"
                />
              </div>
            </div>
            <div class="inside-box inside-box-content">
              <div class="content-title">
                <span>采集处理</span>
              </div>
              <n-form
                v-if="state.basicTitle"
                ref="ruleForm"
                class="base-form"
                :data="state.ruleForm"
                :rules="state.rules"
                label-width="120px"
                label-align="start"
                label-suffix="："
              >
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="categoryId" label="非结构化数据分类">
                      <el-tree-select
                        style="width: 100%"
                        v-model="state.ruleForm.categoryId"
                        :data="state.classList"
                        node-key="id"
                        :props="{
                          label: 'name',
                          value: 'id',
                          children: 'children',
                        }"
                        filterable
                        clearable
                      />
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="confidentialityLevel" label="密级">
                      <n-select v-model="state.ruleForm.confidentialityLevel" :allow-clear="true">
                        <n-option
                          v-for="item in state.secretList"
                          :key="item.id"
                          :name="item.name"
                          :value="item.id"
                        />
                      </n-select>
                    </n-form-item>
                  </n-col>
                </n-row>
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="type" label="重复处理">
                      <n-radio-group v-model="state.ruleForm.duplicateLogic" direction="row">
                        <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
                          item.name
                        }}</n-radio>
                      </n-radio-group>
                    </n-form-item>
                  </n-col>
                </n-row>
              </n-form>
            </div>
            <!-- 配置信息 -->
            <div class="inside-box inside-box-content footer">
              <div class="content-title">
                <span>调度信息</span>
              </div>
              <configureSchedulingRules
                v-if="state.dispatchInfo"
                ref="configureSchedulingRulesDom"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{
        'box-operate': true,
        isLzos: state.isLzos,
      }"
    >
      <n-button size="sm" @click.prevent="cancel">取消</n-button>
      <n-button
        class="save"
        :loading="state.loading"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="save"
        >确定</n-button
      >
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { checkCIp, checkCPort, checkCName } from '@/utils/validate'
  import { collectJobAdd, collectJobUpdate, collectJobDetail } from '@/api/dataManage'
  import configureSchedulingRules from './components/configure-scheduling-rules'
  import { number } from 'echarts'

  export default {
    name: '',
    components: {
      configureSchedulingRules,
    },
    props: {},
    setup() {
      const router = useRouter()
      const configureSchedulingRulesDom = ref()
      const sourceForm = ref()
      const ruleForm = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        basicTitle: true,
        selectFile: true,
        dispatchInfo: true,
        sourceCategoryId: null,
        sourceForm: {
          host: '',
          port: '',
          username: '',
          password: '',
          filepath: '',
        },
        ruleForm: {
          categoryId: null,
          confidentialityLevel: 'INTERIOR',
          duplicateLogic: 'SKIP',
        },
        sourceRules: {
          host: [{ required: true, validator: checkCIp, trigger: 'blur' }],
          port: [{ required: true, validator: checkCPort, trigger: 'blur' }],
          username: [{ required: true, message: '请输入对应的用户名', trigger: 'blur' }],
          password: [{ required: true, message: '请输入对应的密码', trigger: 'blur' }],
          filepath: [{ required: true, message: '请输入文件提取地址', trigger: 'blur' }],
        },
        rules: {
          categoryId: [
            {
              required: true,
              message: '请选择非结构化数据分类',
              trigger: 'change',
              type: 'number',
            },
          ],
          confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
          duplicateLogic: [{ required: true, message: '请选择重复处理', trigger: 'blur' }],
        },

        disabled: false, //非创建状态下不调用逆向模型接口
        loading: false,
        allData: {},
        editId: null,

        tableHeadTitles: [
          { prop: 'number', name: '序号', width: '200' },
          { prop: 'fileName', name: '非结构化数据名称' },
        ],
        pagination: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        tableList: [],
        isLoad: false,
        classList: [],
        secretList: [
          { id: 'PUBLIC', name: '公开' },
          { id: 'INTERIOR', name: '内部' },
          { id: 'CONTROLLED', name: '受控' },
          { id: 'SECRET', name: '秘密' },
          { id: 'CONFIDENTIAL', name: '机密' },
          { id: 'CORE', name: '核心' },
        ],
        typeList: [
          { name: '跳过', id: 'SKIP' },
          { name: '覆盖现有文件', id: 'COVER' },
          { name: '重命名后保存', id: 'RENAME' },
        ],
      })

      const methods = {
        // 查询
        onSearch(init = true) {
          sourceForm.value.validate((valid) => {
            if (valid) {
              if (init) state.pagination.pageNum = 1
              state.isLoad = true
              let params = {
                pageNum: state.pagination.currentPage,
                pageSize: state.pagination.pageSize,
                condition: {
                  ...state.sourceForm,
                },
              }
              api.documentManage
                .getFileList(params)
                .then((res) => {
                  state.tableList = res.data.list.map((item, index) => {
                    return { ...item, number: index + 1 }
                  })
                  state.pagination.total = res.data.total
                  state.isLoad = false
                })
                .catch(() => {
                  state.isLoad = false
                })
            }
          })
        },
        async getClassifyTreeList() {
          const res = await api.documentManage.getClassifyTreeList()
          state.classList = res.data
          if (router.currentRoute.value?.query?.categoryId) {
            state.ruleForm.categoryId = Number(router.currentRoute.value?.query?.categoryId)
          }
        },
        // 采集任务保存
        async save() {
          let source_result = await new Promise((resolve) => {
            sourceForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          let rule_result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          if (!rule_result.passed || !source_result) return
          let scheduling_result = await configureSchedulingRulesDom.value.getAllData()
          if (!scheduling_result.passed) return

          let mergeAllData = {
            datasourceInfo: { ...state.sourceForm },
            ...scheduling_result.data,
            ...state.ruleForm,
          }
          console.log(scheduling_result.data)
          if (mergeAllData.schedule && mergeAllData.schedule.ruleForm) {
            delete mergeAllData.schedule.ruleForm
          }
          state.loading = true
          api.documentManage
            .collectAdd(mergeAllData)
            .then((res) => {
              let { success } = res
              state.loading = false
              if (success) {
                ElNotification({
                  title: '提示',
                  message: '新增成功',
                  type: 'success',
                })
                router.go(-1)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },

        // 取消
        cancel() {
          router.go(-1)
        },
      }
      onMounted(async () => {
        state.sourceCategoryId = router.currentRoute.value.query.categoryId || null
        await methods.getClassifyTreeList()
      })

      return {
        state,
        sourceForm,
        ruleForm,
        configureSchedulingRulesDom,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .doc-collection {
    height: 100%;
  }
  .doc-collection-add {
    box-sizing: border-box;
    height: calc(100% - 62px);
    padding: 16px;
    &.isLzos {
      height: calc(100% - 52px);
      padding: 0;
    }
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 64px);
        overflow: auto;
        border-radius: 2px 2px 0 0;
        .inside-box {
          margin-top: 10px;
          padding: 16px;
          &-content {
            background-color: #fff;
            border-radius: 2px;
            &.footer {
              margin-top: 10px;
              border-radius: 2px 2px 0 0;
            }
          }
          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 10px;
            padding-left: 14px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .base-form {
          .nancalui-row__justify--between,
          .nancalui-row__justify--start {
            .nancalui-col__span--10 {
              padding: 0 !important;
            }
          }
          .nancalui-form__item--horizontal {
            margin-bottom: 16px;
          }
          .nancalui-input,
          .nancalui-select,
          .el-select {
            max-width: 100%;
          }

          .check-style {
            margin-top: -6px;
            margin-bottom: 10px;
          }
        }
        .inline-table {
          :deep(.page-footer) {
            display: flex;
            justify-content: center;
          }
          :deep(.pic-no-conyent) {
            width: 200px;
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .box-operate {
    position: absolute;
    right: 16px;
    bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    width: calc(100% - 32px);
    height: 64px;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 2px;
    :deep(.nancalui-button) {
      border-radius: 2px;
    }
    &.isLzos {
      right: 0;
      bottom: 0;
      width: 100%;
    }
  }
</style>
