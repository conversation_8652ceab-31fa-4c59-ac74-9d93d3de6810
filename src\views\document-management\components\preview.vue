<template>
  <div class="container">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
    </div>
    <!-- 预览 -->
    <div
      :class="{ 'preview-content': true, 'not-need-ocr': !needOcr }"
      id="preview-pint"
      v-loading="state.loading"
      element-loading-text="Loading..."
    >
      <component :is="componentKey" :option="state.option" v-if="componentKey" />
      <div v-else>暂时不支持{{ state.type }}格式预览</div>
    </div>
    <OCRTOOL :loading="state.loading" @changeLoad="changeLoad" :type="state.type" v-if="needOcr" />
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import Excel from '@/views/data-management/resource-library/components/Excel.vue'
  import Img from '@/views/data-management/resource-library/components/Img.vue'
  import PDF from '@/views/data-management/resource-library/components/PDFJS.vue'
  import Txt from '@/views/data-management/resource-library/components/Txt.vue'
  import Word from './/word.vue'
  import OCRTOOL from './ocr-tool.vue'

  import api from '@/api/index'
  const router = useRouter()
  const state = reactive({
    name: '非结构化数据预览',
    option: {},
    type: '',
    loading: false,
    routeName: '',
  })
  const componentMap = {
    image_png: Img,
    image_jpg: Img,
    image_jpeg: Img,
    pdf: PDF,
    word: Word,
    excel: Excel,
    text_txt: Txt,
  }
  const needOcr = computed(() =>
    ['image_png', 'image_jpg', 'image_jpeg', 'excel', 'pdf'].includes(state.type),
  )
  const componentKey = computed(() => componentMap[state.type])
  function getDocUrl(id, docUrl, name, type, version) {
    if (type) {
      state.name = name
      state.type = type
      state.option = {
        value: docUrl,
        name: name,
      }
    } else {
      if (
        state.routeName === 'documentUploadPreview' ||
        state.routeName === 'documentTagPreview' ||
        state.routeName === 'documentEntryPreview'
      ) {
        api.documentManage.uploadDetail({ docId: id, version }).then(async (res) => {
          state.name = res.data.name || name
          state.type = res.data.type
          state.option = {
            value: `${
              location.origin
            }/api/govern-document/upload/online/file?bucket=data-govern&docId=${id}&version=${
              version ? version : ''
            }`,
            name: state.name,
          }
        })
      } else if (state.routeName === 'documentCollaborationPreview') {
        api.documentManage.collaborateDetail({ docId: id, version }).then(async (res) => {
          state.name = res.data.name || name
          state.type = res.data.type
          state.option = {
            value: `${
              location.origin
            }/api/govern-document/collaborate/online/file?bucket=data-govern&docId=${id}&version=${
              version ? version : ''
            }`,
            name: state.name,
          }
        })
      } else if (state.routeName === 'templatePreview') {
        api.documentManage.templateDetail({ id: id }).then((res) => {
          state.name = res.data.name
          state.type = res.data.templateType
          state.option = {
            value: res.data.templateFileUrl,
            name: res.data.name,
          }
        })
      } else if (state.routeName === 'documentPreparationPreview') {
        api.documentManage.documentationGet({ id: id }).then((res) => {
          state.name = res.data.name || name
          state.type = res.data.type
          state.option = {
            value: docUrl ? docUrl : res.data.docUrl,
            name: res.data.name || name,
          }
        })
      }
    }
  }
  // 非结构化数据预览记录保存
  function savePreviewRecord(id) {
    api.documentManage.entryRecordSave({
      documentId: id,
    })
  }
  function changeLoad(val) {
    state.loading = val
  }
  function closeFn() {
    router.go(-1)
  }
  onMounted(() => {
    const { id, docUrl, name, type, version } = router.currentRoute.value.query
    state.routeName = router.currentRoute.value.name
    getDocUrl(id, docUrl, name, type, version)
    if (id) {
      savePreviewRecord(id)
    }
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .preview-content {
    height: calc(100vh - 370px);
    padding: 10px;
    background: #fff;
    &.not-need-ocr {
      height: calc(100vh - 185px);
    }
  }
  :deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;
    .img-Preview {
      width: 100% !important;
      height: max-content;
      min-height: 100%;
      overflow-x: unset;
      overflow-y: unset;
    }
  }
  .title {
    width: calc(100% - 80px);
  }
</style>
