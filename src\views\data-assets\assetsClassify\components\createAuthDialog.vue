<template>
  <n-modal
    v-model="showDirDialog"
    title="角色授权"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <div class="container-box">
        <n-input
          v-model="state.treeSearchText"
          placeholder="请输入角色名称查询"
          suffix="search"
          @change="(val) => treeRef.filter(val)"
        />
        <RoleSelect
          ref="treeRef"
          v-model:roles="state.syncForm.roles"
          :allAssetsClassifyRoleAllList="state.allAssetsClassifyRoleAllList"
          :treeData="state.treeData"
          :data="state.roleAllArr"
          :key="filterId"
        />
      </div>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import { addAssetsClassifyRole, getAssetsClassifyRoleAllList } from '@/api/sceneManage'
  import api from '@/api/index'
  import RoleSelect from './roleSelect.vue'
  const showDirDialog = ref(false)
  const emit = defineEmits(['success'])
  const treeRef = ref(null)
  const state = reactive({
    treeData: null,
    syncForm: { classificationCode: '', roles: [] },
    roleAllArr: [],
    treeSearchText: '',
    allAssetsClassifyRoleAllList: [],
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    if (!state.syncForm.classificationCode || !state.syncForm.roles.length) {
      ElMessage.error('请选择角色')
      return
    }
    addAssetsClassifyRole(state.syncForm).then(({ success }) => {
      if (!success) return
      emit('success')
      showDirDialog.value = false
      ElMessage.success('创建成功')
    })
  }
  const _roleAll = () => {
    api.system.roleAll({}).then((res) => {
      state.roleAllArr = res.data
    })
  }

  // 资产分类权限角色全部列表-不分页
  const _getAssetsClassifyRoleAllList = (classificationCode) => {
    getAssetsClassifyRoleAllList({ classificationCode }).then(({ data }) => {
      state.allAssetsClassifyRoleAllList = data?.map((_) => _?.roleId) || []
    })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(code) {
      resetForm()
      state.syncForm.classificationCode = code
      showDirDialog.value = true
      _roleAll()
      _getAssetsClassifyRoleAllList(code)
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 16px;
    .container-box {
      padding: 16px;
      border-radius: 4px;
      border: 1px solid var(---, #e5e6eb);
      background: var(--100, #fff);
    }
  }
</style>
