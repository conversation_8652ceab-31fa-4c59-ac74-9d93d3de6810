div<template>
  <!-- 基础信息 -->
  <div class="rule-template-basic publicFormCss">
    <div class="add-basic-box">
      <n-form
        ref="form"
        class="form__label_font14"
        :data="state.form"
        label-width="100px"
        :rules="state.isView ? [] : state.rules"
        :pop-position="['right']"
        v-loading="state.loading"
      >
        <div class="content-title">
          <span>基础信息</span>
        </div>
        <div :class="['base-box', state.expandTableStatus[0] ? '' : 'hide']">
          <n-form-item label="任务名称：" field="name">
            <span v-if="state.isView" class="view-box">{{ state.form.name }}</span>
            <n-input
              v-else
              v-model="state.form.name"
              :disabled="state.form.id"
              maxlength="30"
              placeholder="请输入任务名称"
              clearable
            />
          </n-form-item>
          <n-form-item label="描述：">
            <span v-if="state.isView" class="view-box">{{ state.form.description }}</span>
            <n-textarea
              v-else
              v-model="state.form.description"
              placeholder="请输入描述信息"
              maxlength="200"
              :autosize="{ minRows: 3 }"
              resize="both"
              show-count
            />
          </n-form-item>
          <n-form-item label="监控对象：" field="tableId">
            <span v-if="state.isView" class="view-box">{{
              state.ruleList?.find?.((item) => item.value === state?.form?.tableId)?.name
            }}</span>
            <n-select
              v-else
              v-model="state.form.tableId"
              :disabled="state.form.id"
              placeholder="请选择"
              filter
              :options="state.ruleList"
              @value-change="changetableId"
            />
          </n-form-item>
        </div>
        <!-- 规则列表 -->
        <div class="content-title">
          <span>规则列表</span>
        </div>
        <div :class="[' config-box', state.expandTableStatus[3] ? '' : 'hide']">
          <TaskTable
            ref="publicTable"
            :isNeedSelection="!state.isView"
            :isDisplayAction="false"
            :table-head-titles="state.tableHeadTitles"
            :showPagination="false"
            :exceptHeight="640"
            :loading="false"
            @handleSelectionChange="handleSelectionChange"
          >
            <template #keySlot="{ editor }">
              <span v-if="state.isView" class="view-box">{{ editor.row.ruleWeight }}</span>
              <n-input-number
                v-else
                v-model="editor.row.ruleWeight"
                :min="0"
                :max="9"
                @keydown="onKeydownPositiveInteger($event, editor.row.ruleWeight)"
                @keyup="onKeyupPositiveInteger($event, editor.row.ruleWeight)"
                @focus="inputFocus($event)"
                @blur="fieldBlur('ruleWeight')"
                placeholder="请输入"
              />
            </template>
            <template #tableType="{ editor }">
              <div class="tableType">
                <SvgIcon
                  v-if="editor.row.ruleType === 'TABLE'"
                  icon="icon-quality-table"
                  class="icon"
                />
                <SvgIcon v-else icon="icon-quality-field" class="icon" />
                {{ editor.row.ruleType === 'TABLE' ? '表级' : '字段级' }}
              </div>
            </template>
            <template #status="{ editor }">
              <div class="status">
                <div :class="editor.row.ruleDisabled ? 'circle gray' : 'circle'"></div>
                {{ editor.row.ruleDisabled ? '未启用' : '启用' }}
              </div>
            </template>
            <template #sexomorphicDimension="{ editor }">
              <div v-if="editor.row.sexomorphicDimension" class="template-name-box">
                <span>{{ sexomorphicFnitemFn(editor.row.sexomorphicDimension) }}</span>
              </div>
              <span v-else>--</span>
            </template>
            <template #relation="{ editor }">
              <div
                :class="{
                  'template-name-box': true,
                  green: editor.row.relateStatus,
                }"
              >
                <span>{{ editor.row.relateStatus ? '已关联' : '未关联' }}</span>
              </div>
            </template>
            <template #default="{ editor }">
              {{ editor.row[item.prop] || '--' }}
            </template>
          </TaskTable>
        </div>

        <div class="content-title">
          <span>调度信息</span>
        </div>
        <div :class="[' config-box', state.expandTableStatus[1] ? '' : 'hide']">
          <!-- 调度方式配置 -->
          <n-form-item label="调度方式：" field="scheduleMode">
            <span v-if="state.isView" class="view-box">{{
              scheduleModeMap[state.form.scheduleMode] || '_ _'
            }}</span>
            <n-radio-group v-else v-model="state.form.scheduleMode" direction="row">
              <n-radio
                v-for="(value, key) in scheduleModeMap"
                :disabled="key === 'ASSOCIATED_SCHEDUL' && idDisabledScheduleMode"
                :value="key"
                :key="key"
                >{{ value }}
                <n-tooltip
                  class="tree-btn"
                  content="关联监控对象指定周期的生产调度任务，节点运行完成后，自动触发该质量任务"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon
                    v-if="key === 'ASSOCIATED_SCHEDUL'"
                    class="illustrate"
                    icon="icon-illustrate"
                  /> </n-tooltip
              ></n-radio> </n-radio-group
          ></n-form-item>
          <!-- 自动化调度配置 -->
          <template v-if="state.form.scheduleMode === 'AUTO'">
            <!-- 采集频率 -->
            <n-form-item label="采集频率：" field=" ">
              <span v-if="state.isView" class="view-box">{{
                state.form.schedule.scheduleRateType === 'CONFIG_TIME'
                  ? timeRuleExplainFn(state.form.schedule)
                  : 'cron表达式 ' + state.form.schedule.cron
              }}</span>
              <!-- 配置时间 or cron表达式 -->
              <n-radio-group v-else v-model="state.form.schedule.scheduleRateType" direction="row">
                <n-radio value="CONFIG_TIME" size="small">配置时间</n-radio>
                <n-radio value="CRON" size="small">cron表达式</n-radio>
              </n-radio-group>
            </n-form-item>
            <template v-if="!state.isView">
              <n-form-item label=" " v-if="state.form.schedule.scheduleRateType === 'CONFIG_TIME'">
                <div class="task-content-collect-rate-box">
                  <div
                    class="collection-rate-box"
                    :style="{
                      'grid-template-columns': ['week', 'month'].includes(
                        state.form.schedule.period,
                      )
                        ? '42% 1fr 1fr'
                        : 'repeat(2, 1fr)',
                    }"
                  >
                    <div class="has-border">
                      <n-form-item label="" field="schedule.period">
                        <div class="nancalui-input">
                          <n-radio-group
                            direction="row"
                            v-model="state.form.schedule.period"
                            style="width: 100%"
                          >
                            <div class="nancalui-input__wrapper">
                              <n-radio
                                v-for="item in state.periodOptions"
                                :key="item.value"
                                :value="item.value"
                                @change="periodChange"
                                >{{ item.label }}</n-radio
                              >
                            </div>
                          </n-radio-group>
                        </div>
                      </n-form-item>
                    </div>

                    <div v-if="state.form.schedule.period === 'hour'">
                      <n-form-item class="time-box" label="" field="schedule.extent">
                        <!-- 开头不能为0，且不能输入小数 -->
                        <n-input
                          v-model="state.form.schedule.extent"
                          maxlength="30"
                          placeholder="请输入间隔小时"
                          @keydown="onKeydownPositiveInteger($event, state.form.schedule.extent)"
                          @keyup="onKeyupPositiveInteger($event, state.form.schedule.extent)"
                          @focus="inputFocus($event)"
                          @blur="fieldBlur('extent')"
                          :onpaste="
                            () => {
                              return false
                            }
                          "
                        />
                      </n-form-item>
                    </div>
                    <div
                      v-if="
                        state.form.schedule.period == 'week' ||
                        state.form.schedule.period == 'month'
                      "
                    >
                      <n-form-item class="time-box" label="" field="extent">
                        <n-select
                          v-if="state.form.schedule.period === 'week'"
                          v-model="state.form.schedule.extent"
                          style="width: 100%"
                          placeholder="请选择周几"
                          filter
                          @value-change="
                            () => {
                              state.key++
                            }
                          "
                        >
                          <n-option
                            v-for="item in state.weekList"
                            :key="item.value"
                            :name="item.label"
                            :value="item.value"
                          />
                        </n-select>
                        <n-select
                          v-else-if="state.form.schedule.period === 'month'"
                          v-model="state.form.schedule.extent"
                          style="width: 100%"
                          placeholder="请选择几号"
                          filter
                          @value-change="
                            () => {
                              state.key++
                            }
                          "
                        >
                          <n-option
                            v-for="item in state.monthList"
                            :key="item.value"
                            :name="item.label"
                            :value="item.value"
                          />
                        </n-select>
                      </n-form-item>
                    </div>
                    <div v-if="state.form.schedule.period !== 'hour'">
                      <n-form-item class="time-box" label="" field="rateTime">
                        <n-time-picker
                          v-model="state.form.schedule.rateTime"
                          placeholder="00:00:00"
                          style="width: 100%"
                        />
                      </n-form-item>
                    </div>
                  </div>
                  <div class="tips">到达该配置时间，任务将自动运行</div>
                </div>
              </n-form-item>
              <n-form-item label=" " v-else>
                <n-input
                  v-model="state.form.schedule.cron"
                  size="small"
                  placeholder="请输入corn表达式"
                  maxlength="100"
                  clearable
                />
              </n-form-item>
            </template>

            <!-- 重跑机制 -->
            <n-form-item label="重跑机制：">
              <span v-if="state.isView" class="view-box"
                >重跑次数 {{ state.form.failRetryInterval }} 次 重跑间隔
                {{ state.form.failRetryTimes }} 分</span
              >
              <n-switch v-else v-model="state.rerun" color="#447DFD" @change="rerunSwitchChange" />
            </n-form-item>
            <n-form-item label=" " v-if="state.rerun && !state.isView" class="rerun-content">
              <div class="rerun-mechanism">
                <div class="has-border">
                  <n-form-item label="" field="failRetryInterval">
                    <div class="rerun-mechanism-div">
                      <div class="nancalui-input">
                        <div class="nancalui-input__wrapper"
                          ><n-input
                            v-model="state.form.failRetryInterval"
                            placeholder="0"
                            @keydown="
                              onKeydownPositiveInteger($event, state.form.failRetryInterval)
                            "
                            @keyup="onKeyupPositiveInteger($event, state.form.failRetryInterval)"
                            @focus="inputFocus($event)"
                            @blur="fieldBlur('failRetryInterval')"
                            size="sm"
                            :onpaste="
                              () => {
                                return false
                              }
                            "
                          />
                        </div>
                      </div>
                    </div>
                  </n-form-item>
                </div>
                <div>
                  <n-form-item label="" field="failRetryTimes"
                    ><div class="rerun-mechanism-div">
                      <div class="nancalui-input">
                        <div class="nancalui-input__wrapper failRetry-times">
                          <n-input
                            v-model="state.form.failRetryTimes"
                            placeholder="0"
                            @keydown="onKeydownPositiveInteger($event, state.form.failRetryTimes)"
                            @keyup="onKeyupPositiveInteger($event, state.form.failRetryTimes)"
                            @focus="inputFocus($event)"
                            @blur="fieldBlur('failRetryTimes')"
                            :onpaste="
                              () => {
                                return false
                              }
                            "
                          />
                        </div>
                      </div>
                    </div> </n-form-item
                ></div>
              </div>
            </n-form-item>

            <!-- 生效时间 -->
            <n-form-item label="生效时间：" field="schedule.effectiveDate">
              <span v-if="state.isView" class="view-box">{{
                state.form.schedule?.effectiveDate
                  ?.map?.((_) => formartTimeDate(_, '-', true))
                  ?.join?.(' ')
              }}</span>
              <n-range-date-picker-pro
                v-else
                v-model="state.form.schedule.effectiveDate"
                :placeholder="['开始日期', '结束日期']"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                allow-clear
                showTime
              >
                <template #footer>
                  <slot name="footer">
                    <div class="date-picker-footer configure-scheduling">
                      <n-button
                        variant="solid"
                        color="primary"
                        @click="
                          state.form.schedule.effectiveDate[1] = new Date('2099-12-31 23:59:59')
                        "
                      >
                        长期有效
                      </n-button>
                    </div>
                  </slot>
                </template>
              </n-range-date-picker-pro>
            </n-form-item>
          </template>
        </div>
        <div class="content-title">
          <span>问题处置策略</span>
        </div>
        <div :class="[' problem-box', state.expandTableStatus[2] ? '' : 'hide']">
          <!-- 问题责任人配置 -->
          <n-form-item label="问题责任人：" field="personInCharge">
            <span v-if="state.isView" class="view-box">{{
              JSON.parse(
                new RegExp(`{[^{}]*"id":${state.form.personInCharge}[^{}]*}`).exec(
                  JSON.stringify(state.personList),
                )?.[0] || '{}',
              )?.name
            }}</span>
            <el-select
              v-model="state.form.personInCharge"
              filterable
              clearable
              remote
              reserve-keyword
              placeholder="请输入工号或姓名"
              :remote-method="remoteMethod"
              :loading="state.searchLoading"
            >
              <el-option
                v-for="item in state.personList"
                :key="item.id"
                :label="item.name + '-' + item.username"
                :value="item.id"
              />
            </el-select>
          </n-form-item>
          <!-- 告警通知 -->
          <n-form-item label="告警通知：" field="alarmNotification" style="margin-bottom: 0">
            <div class="n-form-item-content">
              <span v-if="state.isView" class="view-box">{{
                state.form.alarmNotification === 'ON' ? '已开启' : '已关闭'
              }}</span>
              <n-switch
                v-else
                v-model="state.form.alarmNotification"
                active-value="ON"
                inactive-value="OFF"
              />
              <p v-if="!state.isView" class="explain"
                >当监测到质量问题时，对问题责任人及关注动态的相关用户发送告警通知。</p
              >
            </div>
          </n-form-item>
          <n-form-item label="治理策略：" field="governancePolicyEnable" style="margin-bottom: 0">
            <div class="n-form-item-content">
              <span v-if="state.isView" class="view-box">{{
                state.form.governancePolicyEnable === 'ON' ? '已开启' : '已关闭'
              }}</span>
              <n-switch
                v-else
                v-model="state.form.governancePolicyEnable"
                active-value="ON"
                inactive-value="OFF"
              />
            </div>
          </n-form-item>
          <n-form-item
            v-if="state.form.governancePolicyEnable === 'ON'"
            label="同步项目："
            field="governanceProjectCode"
            style="margin-bottom: 0"
          >
            <div class="n-form-item-content">
              <span v-if="state.isView" class="view-box">{{
                state.projectList?.find?.(
                  (item) => item.value === state?.form?.governanceProjectCode,
                )?.name
              }}</span>
              <n-select
                v-else
                v-model="state.form.governanceProjectCode"
                placeholder="请选择"
                filter
                :options="state.projectList"
              />
            </div>
          </n-form-item>
        </div>
      </n-form>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, computed } from 'vue'
  import TaskTable from './TaskTable'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'
  import { checkCName } from '@/utils/validate'
  import { formartTimeDate } from '@/utils/index'
  import { workbenchProjectList } from '@/api/dataQuality.js'
  const checkRangeDatePickerPro = (rule, value, callback) => {
    if (!value || (!value[0] && !value[1])) {
      return callback(new Error('请选择生效时间'))
    } else if (!value[0]) {
      return callback(new Error('请选择开始日期'))
    } else if (!value[1]) {
      return callback(new Error('请选择结束日期'))
    } else {
      return callback()
    }
  }
  export default {
    name: '',
    components: {
      TaskTable,
    },
    props: {
      drawerState: {
        type: String,
        default() {
          return ''
        },
      },
    },
    setup(props) {
      const form = ref(null)
      const publicTable = ref(null)
      const scheduleModeMap = {
        MANUAL_OPERATION: '手动调度',
        AUTO: '自动化调度',
        // ASSOCIATED_SCHEDUL: '关联任务调度',
      }
      const state = reactive({
        loading: false,
        isView: props.drawerState === '查看',
        expandTableStatus: new Array(10).fill(true),
        tableHeadTitles: [
          { prop: 'fullName', name: '规则名称', width: 160 },
          { prop: 'ruleType', name: '类型', width: 160, slot: 'tableType' },
          {
            prop: 'sexomorphicDimension',
            name: '六性维度',
            slot: 'sexomorphicDimension',
            width: 160,
          },
          { prop: 'ruleSourceText', name: '规则来源', width: 160 },
          { prop: 'ruleDisabled', name: '启用状态', slot: 'status' },
          { prop: 'ruleContentText', name: '规则内容' },
          { prop: 'ruleThresholdListText', name: '监控阈值' },
          { prop: 'relation', name: '关联任务状态', slot: 'relation' },
          { prop: 'description', name: '描述' },
          { prop: 'createByName', name: '创建人' },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'updateTime', name: '最后修改时间' },
          { prop: 'ruleWeight', name: '规则权重', slot: 'keySlot', width: 160, fixed: 'right' },
        ],
        form: {
          name: '',
          description: '',
          failRetryInterval: 0,
          failRetryTimes: 0,
          tableId: null,
          scheduleMode: 'MANUAL_OPERATION',
          personInCharge: null, // 责任人
          alarmNotification: 'ON',
          governancePolicyEnable: 'ON',
          governanceProjectCode: null,
          ruleList: [],
          schedule: {
            scheduleRateType: 'CONFIG_TIME',
            cron: '',
            fromDateTime: null,
            thruDateTime: null,
            rateTime: '',
            period: 'hour',
            schedule: true,
            extent: '',
            effectiveDate: [],
          },
          // endDate: null,
          // startDate: null,
        },
        /* ---------------------------------------分隔线--------------------------------------- */
        rerun: true,
        periodOptions: [
          {
            label: '小时',
            value: 'hour',
          },
          {
            label: '日',
            value: 'day',
          },
          {
            label: '周',
            value: 'week',
          },
          {
            label: '月',
            value: 'month',
          },
        ],
        weekList: [
          { label: '周一', value: '1' },
          { label: '周二', value: '2' },
          { label: '周三', value: '3' },
          { label: '周四', value: '4' },
          { label: '周五', value: '5' },
          { label: '周六', value: '6' },
          { label: '周日', value: '7' },
        ],
        monthList: Array(31)
          .fill('')
          .map((_, i) => ({
            label: i + 1 + '号',
            value: (i + 1).toString(),
          })),
        // 规则列表
        ruleList: [],
        // 治理项目列表
        projectList: [],

        /* ---------------------------------------分隔线--------------------------------------- */

        rules: {
          name: [{ required: true, validator: checkCName, trigger: 'blur' }],
          tableId: [
            { required: true, message: '请选择监控对象', trigger: 'change', type: 'number' },
          ],
          scheduleMode: [{ required: true, message: '请选择调度方式', trigger: 'change' }],
          personInCharge: [
            { required: true, message: '请选择问题责任人', trigger: 'change', type: 'number' },
          ],
          'schedule.effectiveDate': [
            {
              required: true,
              validator: checkRangeDatePickerPro,
              trigger: 'change',
              type: 'array',
            },
          ],
        },
        searchLoading: false,
        personList: [],
      })
      const methods = {
        // 获取项目列表
        getProjectList() {
          workbenchProjectList({}).then((res) => {
            if (res.success) {
              state.projectList = res.data.map((val) => ({
                name: val.projectName,
                value: val.projectCode,
              }))
            }
          })
        },
        remoteMethod(query) {
          state.searchLoading = true
          api.system
            .userList({
              condition: { name: query },
              pageNum: 1,
              pageSize: 100,
            })
            .then((res) => {
              state.searchLoading = false
              if (res.success) {
                state.personList = res.data.list
              }
            })
            .catch(() => {
              state.searchLoading = false
            })
        },
        // 获取规则列表
        getRuleList() {
          api.dataQuality.getConfiguredRuleList({}).then((res) => {
            let { success, data } = res
            if (success) {
              state.ruleList = data?.map((item) => ({
                value: item.tableId,
                name: item.tableName,
              }))
            }
          })
        },
        // 更改监控对象时
        changetableId: (data) => {
          publicTable.value.clearSelection()
          methods.getRuleListByTableId(data.value)
          state.availableJobList = null
          api.dataQuality.getAvailableJobList({ id: data.value }).then((res) => {
            const { success } = res
            if (success) {
              state.availableJobList = res.data
            }
            idDisabledScheduleMode.value && (state.form.scheduleMode = 'MANUAL_OPERATION')
          })
        },
        fieldBlur(key) {
          state.form[key] = Number(state.form[key])
          if (key === 'extent') {
            if (state.form[key] > 23) {
              state.form[key] = 23
            } else if (state.form[key] < 1) {
              state.form[key] = 1
            }
          }
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },

        // 通过表ID获取规则列表
        getRuleListByTableId(tableId, activeData) {
          const activeDataMap = {}
          if (activeData) {
            activeData?.forEach((item) => {
              activeDataMap[item.id] = item?.ruleWeight
              item.ruleSourceText = item.ruleSource === 'CUSTOMED' ? '自定义规则' : '规则模板'
              item.ruleContentText = ruleContentFn(item)
              item.ruleThresholdListText = ruleThresholdListFn(item)
              item.ruleTableFieldListText = ruleTableFieldListFn(item)
            })
          }

          return new Promise((resolve) => {
            if (state.isView) {
              publicTable.value.initTableData({ list: activeData })
              resolve()
            } else {
              api.dataQuality.getTableListById(tableId).then((res) => {
                let { success, data } = res
                if (success) {
                  state.form.ruleList = (activeData || [])?.concat(data)?.map((_) => ({
                    ..._,
                    ruleSourceText: _.ruleSource === 'CUSTOMED' ? '自定义规则' : '规则模板',
                    ruleContentText: ruleContentFn(_),
                    ruleThresholdListText: ruleThresholdListFn(_),
                    ruleTableFieldListText: ruleTableFieldListFn(_),
                    ruleWeight: activeDataMap[_.id] || 5,
                  }))
                  publicTable.value.initTableData({ list: state.form.ruleList })
                }
                resolve()
              })
            }
          })
        },
        // 六性转化
        sexomorphicFnitemFn(name) {
          if (name == 'ACCURACY') {
            return '准确性'
          } else if (name == 'VALIDITY') {
            return '有效性'
          } else if (name == 'COMPLETENESS') {
            return '完整性'
          } else if (name == 'UNIQUENESS') {
            return '唯一性'
          } else if (name == 'CONSISTENCY') {
            return '一致性'
          } else if (name == 'TIMELINESS') {
            return '及时性'
          }
        },
        // 时间规则解释
        timeRuleExplainFn({ period, extent, rateTime }) {
          switch (period) {
            case 'hour':
              return `配置时间 每${extent}小时`
              break
            case 'day':
              return `配置时间 每天${rateTime}点`
              break
            case 'week':
              return `配置时间 每${
                state.weekList.find((item) => item.value == extent)?.label
              }${rateTime}点`
              break
            case 'month':
              return `配置时间 每月${
                state.monthList.find((item) => item.value == extent)?.label
              }${rateTime}点`
              break
            default:
              return '__'
              break
          }
        },
        // 保存
        save() {
          const isEdit = state.form.id ? true : false
          return new Promise((resolve, reject) => {
            form.value.validate((val) => {
              if (val) {
                // 获取选中的行
                const selectRows = publicTable.value.getSelectRows()
                // 参数
                state.form.ruleList = selectRows
                api.dataQuality[isEdit ? 'qualityTaskUpdate' : 'createTask'](state.form).then(
                  (res) => {
                    let { success, data } = res
                    if (success) {
                      // 提示成功
                      ElNotification({
                        title: '成功',
                        message: isEdit ? '修改成功' : '新增成功',
                        type: 'success',
                        duration: 2000,
                      })
                      resolve()
                    } else {
                      // 重设ruleList
                      methods.getRuleListByTableId(data.tableId)
                      reject()
                    }
                  },
                )
              }
            })
          })
        },
        /* ---------------------------------------分隔线--------------------------------------- */
        //展开收起下拉内容
        expandTable(index) {
          state.expandTableStatus[index] = !state.expandTableStatus[index]
        },

        // 编辑时候回显
        async editInit(data) {
          // 请求详情数据
          if (data) {
            Object.keys(data).forEach((key) => {
              if (key in state.form) {
                state.form[key] = data[key]
              }
            })
            state.form.schedule.effectiveDate = [
              new Date(state.form.schedule.fromDateTime),
              new Date(state.form.schedule.thruDateTime),
            ]
            state.rerun = Boolean(state.form.failRetryInterval || state.form.failRetryTimes)
          }
          state.loading = true
          state.form.id = data.id
          if (data.personInCharge && data.personInChargeName) {
            methods.remoteMethod(data.personInChargeName)
          }
          const activeData = state.form.ruleList
          methods.getRuleListByTableId(data.tableId, activeData).then(() => {
            // 选中行
            publicTable.value.activeDataRow(activeData)
          })
          // state.form. ruleList
          state.loading = false
          //渲染处理数据
        },
        viewInit(data) {
          state.isView = true
          methods.editInit(data)
        },
      }
      const idDisabledScheduleMode = computed(() => {
        return !(state?.availableJobList?.length > 0)
      })
      methods.getRuleList()
      methods.getProjectList()
      watch(
        () => state.form.schedule.effectiveDate,
        (val) => {
          if (val?.[0] && val?.[1]) {
            state.form.schedule.fromDateTime = formartTimeDate(val[0], '-', true)
            state.form.schedule.thruDateTime = formartTimeDate(val[1], '-', true)
          }
        },
        {
          deep: true,
        },
      )

      return {
        form,
        publicTable,
        state,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        idDisabledScheduleMode,
        formartTimeDate,
        scheduleModeMap,
        ...methods,
      }
    },
  }
  // 转化规则内容
  const ruleContentFn = (item) => {
    if (item.ruleSource === 'CUSTOMED') {
      if (item.ruleDefinition === 'CONDITIONAL') {
        let str = ''
        if (item.ruleConfigInfoList) {
          item.ruleConfigInfoList.forEach((val, ind) => {
            if (ind === 0) {
              str +=
                (ind === 0 ? '' : item.configInfoCondition) +
                '字段值' +
                (val.dataType === 'LENGTH' ? '的长度' : '') +
                val.operation +
                val.dataValue
            }
          })
        }
        return str
      } else {
        return item.regularExpression
      }
    } else {
      return '--'
    }
  }

  // 转化监控阈值
  const ruleThresholdListFn = (item) => {
    let str = ''
    if (item.ruleThresholdList) {
      item.ruleThresholdList.forEach((val) => {
        str +=
          val.type === 'SUCCESS'
            ? '正常阈值，字段值范围' + val.operation + val.operationValue + '；'
            : '告警阈值，字段值' + val.operation + val.operationValue
      })
    }
    return str
  }

  // 转化关联字段
  const ruleTableFieldListFn = (item) => {
    if (item.ruleTableFieldList) {
      if (item.ruleType !== 'FIELD') {
        return item.ruleTableFieldList.map((val) => val.tableName).toString()
      } else {
        return item.ruleTableFieldList.length > 0
          ? item.ruleTableFieldList[0].tableName +
              '(' +
              item.ruleTableFieldList.map((val) => val.fieldName).toString() +
              ')'
          : '--'
      }
    } else {
      return '--'
    }
  }
</script>
<style lang="scss" scoped>
  //统一表单输入框样式
  $labelWidth: 70px;
  @import '@/styles/variables.scss';

  .rule-template-basic {
    height: 100%;
    color: #333;
    background: #fff;

    .nancalui-form {
      display: flex;
      flex-direction: column;
      .base-box {
        &.hide {
          height: 0;
          overflow: hidden;
        }
      }

      .nancalui-form__item--horizontal {
        .form-item-half {
          display: flex;
          width: 100%;

          .nancalui-select {
            width: calc((100% - 8px) / 2);

            &:first-child {
              margin-right: 8px;
            }
          }
        }

        .nancalui-input {
          display: flex;

          :deep(.nancalui-input__wrapper) {
            z-index: 1;
            border-radius: 6px;
          }

          :deep(.nancalui-input-slot__prepend) {
            background-color: #e6ecf8;
            border: 1px solid #a3b4db;
            border-right: 0;
          }
        }

        :deep(.nancalui-checkbox) {
          height: 32px;
        }
      }

      .content-title {
        margin-bottom: 16px;
        height: 30px;
        line-height: 30px;
        color: #1d2129;
        font-size: 16px;
        font-weight: 500;
        padding-left: 14px;
        position: relative;
        background-color: #f2f6fc;
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }
      }

      .config-box {
        &.hide {
          height: 0;
          overflow: hidden;
        }

        .rerun-content {
          margin-bottom: 16px;

          & > .nancalui-form__control {
            margin-left: 116px;
          }

          .nancalui-input {
            :deep(.nancalui-input__wrapper) {
              border-radius: 6px;
            }
          }

          .nancalui-form__control {
            .nancalui-form__control-container {
              .rerun-mechanism {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                .nancalui-form__item--horizontal {
                  margin-bottom: 0;
                }
              }

              .rerun-mechanism-div {
                position: relative;
                width: 100%;
                > .nancalui-input {
                  > .nancalui-input__wrapper {
                    padding: 0;
                    > :deep(.nancalui-input) {
                      height: 100%;
                      &::before {
                        min-width: 81px;
                        width: 81px;
                        display: flex;
                        padding: 0 12px;
                        align-items: center;
                        background-color: #e6ecf8;
                        content: '重跑次数';
                        color: var(----, rgba(0, 0, 0, 0.75));

                        /* 常用/r400/h9 */
                        font-family: 'Source Han Sans CN';
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                        border-radius: 6px 0 0 6px;
                        border-right: 1px solid #a3b4db;
                      }
                      &::after {
                        min-width: 39px;
                        width: 39px;
                        display: flex;
                        padding: 0 12px;
                        align-items: center;
                        background-color: #e6ecf8;
                        content: '次';
                        color: var(----, rgba(0, 0, 0, 0.75));

                        /* 常用/r400/h9 */
                        font-family: 'Source Han Sans CN';
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 157.143% */
                        border-radius: 0 6px 6px 0;
                        border-left: 1px solid #a3b4db;
                      }
                      .nancalui-input__wrapper {
                        border: none;
                        .nancalui-input--sm {
                        }
                      }
                    }
                  }
                  > .failRetry-times {
                    :deep(.nancalui-input) {
                      &::before {
                        content: '重跑间隔';
                      }
                      &::after {
                        content: '分';
                      }
                    }
                  }
                }
              }
            }

            .nancalui-input-slot__append {
              color: #646566;
              font-size: 12px;
              background-color: #f5f7fa;
            }
          }
        }
        .task-content-collect-rate-box {
          width: 100%;
          .tips {
            overflow: hidden;
            color: var(---, rgba(0, 0, 0, 0.55));
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Source Han Sans CN';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin: 0;
          }
          .collection-rate-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            :deep(.nancalui-col) {
              height: 32px;

              &.has-border {
                background: #ffffff;
                border: 1px solid #cfcfcf;
                border-radius: 4px;
                .nancalui-form__control {
                  display: flex;
                  align-items: center;
                }
                .nancalui-form__item--horizontal {
                  height: 100%;
                  margin-bottom: 0;
                  .nancalui-form__control-container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  }
                }
                &:hover {
                  border: 1px solid $themeBlue;
                }
              }
            }
          }
        }
        :deep(.nancalui-input-number) {
          .nancalui-input__wrapper {
            padding: 0;
            padding-left: 10px;
            border-radius: 6px;

            &.nancalui-input--disabled {
              background-color: #ebebeb;
            }

            .nancalui-input__inner {
              text-align: left;
            }
          }

          .control-button {
            position: relative;

            svg {
              display: none;
            }

            &.control-inc {
              &::after {
                position: absolute;
                bottom: 4px;
                left: 50%;
                width: 0;
                height: 0;
                border-right: 4px solid transparent;
                border-bottom: 4px solid #8a8a8a;
                border-left: 4px solid transparent;
                transform: translateX(-50%);
                content: '';
              }
            }

            &.control-dec {
              &::after {
                position: absolute;
                bottom: 4px;
                left: 50%;
                width: 0;
                height: 0;
                border-top: 4px solid #8a8a8a;
                border-right: 4px solid transparent;
                border-left: 4px solid transparent;
                transform: translateX(-50%);
                content: '';
              }
            }
          }
        }

        .nancalui-button {
          &.svg-center {
            margin: 8px 0;
            padding: 0 16px;

            &.no-margin {
              margin: 0 0 8px 0;
            }

            :deep(.button-content) {
              display: flex;
              align-items: center;

              svg {
                margin-right: 4px;
              }
            }
          }
        }

        .threshold-rule-box {
          width: 100%;

          .threshold-rule-list {
            display: flex;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 98px;
              color: rgba(0, 0, 0, 0.75);
              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              line-height: 32px;

              &::before {
                display: inline-block;
                width: 6px;
                height: 6px;
                margin-right: 8px;
                background-color: #f63838;
                border-radius: 6px;
                content: ' ';
              }
            }

            &.normal {
              .label {
                &::before {
                  background-color: #04c495;
                }
              }
            }

            .nancalui-select,
            .nancalui-input-number {
              width: calc((100% - 98px - 16px) / 2);
              margin-left: 8px;

              :deep(.nancalui-input__wrapper) {
                width: 100%;
              }
            }
          }
        }
      }

      .problem-box {
        &.hide {
          height: 0;
          overflow: hidden;
        }

        .explain {
          overflow: hidden;
          color: var(---, rgba(0, 0, 0, 0.55));
          text-overflow: ellipsis;
          white-space: nowrap;

          /* 常用/r400/h9 */
          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          margin: 0;
          margin-left: 12px;
        }
      }

      :deep(.nancalui-form__label) {
        color: #333;
        font-weight: 500;
        font-size: 14px;
        .nancalui-form__label-span:before {
          display: inline-block;
          margin-right: 2px;
          // 元素透明
          opacity: 0;
          vertical-align: middle;
          content: '*';
        }
        .nancalui-form__label--required:before {
          display: inline-block;
          margin-right: 2px;
          color: #f52f3e;
          vertical-align: middle;
          opacity: 1;
          content: '*';
        }
      }

      :deep(.nancalui-form__item--horizontal) {
        position: relative;
        display: flex;
        width: 100%;
        margin-bottom: 20px;

        .nancalui-form__control {
          width: calc(100% - #{$labelWidth});
          margin-left: 0 !important;
        }
        .el-select {
          width: 100%;
        }
      }
    }
  }
  .view-box {
    word-wrap: break-word;
    width: calc(100% - 30px);
    color: var(----, rgba(0, 0, 0, 0.75));

    /* 常用/r400/h9 */
    font-family: 'Source Han Sans CN';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  :deep(.nancalui-textarea) {
    color: var(----, rgba(0, 0, 0, 0.75));
  }
  .icon {
    width: 16px;
    height: 16px;
  }
  .illustrate {
    color: #8091b7;
    vertical-align: top;
    &:hover {
      color: $themeBlue;
    }
  }
  .n-form-item-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
  }
</style>
