<template>
  <div class="container">
    <div class="container-box">
      <moduleName :info="{ name: '新增API数据源' }" />
      <div class="container-box-content">
        <div class="h2">基础信息</div>
        <n-form
          class="form"
          :data="state.form"
          :rules="state.rules"
          ref="$Form"
          :disabled="state.pageType === 'DETAIL' ? true : false"
          labelSuffix="："
          label-width="94px"
          label-align="start"
          :pop-position="['right']"
        >
          <n-row :gutter="12">
            <n-col :span="12"
              ><n-form-item field="name" label="数据源名称">
                <n-input v-model="state.form.name" maxlength="30" /> </n-form-item
            ></n-col>
            <n-col :span="12"
              ><n-form-item field="url" label="请求地址">
                <n-input
                  v-model="state.form.url"
                  @input="inputFn"
                  maxlength="500"
                  placeholder="请输入"
                >
                  <template #prepend>
                    <n-select
                      class="compose"
                      v-model="state.form.methodType"
                      :options="[
                        { name: 'GET', value: 'GET' },
                        { name: 'POST', value: 'POST' },
                      ]"
                      @value-change="methodTypeChange"
                    />
                  </template>
                </n-input>
                <n-input v-model="state.form.url" maxlength="80" /> </n-form-item
            ></n-col>
          </n-row>

          <n-row :gutter="12">
            <n-col :span="12">
              <n-form-item label="数据分类" field="dataCategoryId">
                <TreeSelect
                  v-model="state.form.dataCategoryId"
                  :data="state.treeData"
                  :props="{
                    label: 'name',
                    children: 'children',
                    value: 'id',
                  }"
                  :disabled="state.pageType === 'DETAIL' ? true : false"
                  check-strictly
                  :render-after-expand="false"
                  placeholder="请选择数据分类"
                  style="width: 100%"
                  size="sm"
                />
              </n-form-item>
            </n-col>
            <n-col :span="12">
              <n-form-item label="数据密级" field="confidentialityLevel">
                <n-select v-model="state.form.confidentialityLevel" placeholder="请选择数据密级">
                  <n-option
                    v-for="item in state.confidentialityLevelOptions"
                    :key="item.value"
                    :name="item.name"
                    :value="item.value"
                  />
                </n-select>
              </n-form-item>
            </n-col>
          </n-row>

          <n-row :gutter="12">
            <!--            <n-col :span="12"-->
            <!--              ><n-form-item label="数据环境" field="envType">-->
            <!--                <n-select-->
            <!--                  v-model="state.form.envType"-->
            <!--                  placeholder="请选择数据环境"-->
            <!--                  allow-clear-->
            <!--                  filter-->
            <!--                >-->
            <!--                  <n-option name="生产环境" value="OFFICIAL" />-->
            <!--                  <n-option name="开发环境" value="TEST" />-->
            <!--                </n-select> </n-form-item-->
            <!--            ></n-col>-->
            <n-col :span="12"
              ><n-form-item field="table" label="表名">
                <n-input v-model="state.form.table" maxlength="80" /> </n-form-item
            ></n-col>
          </n-row>

          <n-row :gutter="12">
            <n-col :span="24"
              ><n-form-item field="description" label="描述信息">
                <n-textarea
                  v-model="state.form.description"
                  :rows="4"
                  show-count
                  maxlength="200"
                /> </n-form-item
            ></n-col>
          </n-row>
        </n-form>
        <div class="h2">配置信息</div>
        <div class="container-box-content-setting">
          <n-row :gutter="12">
            <n-col :span="24">
              <div class="title">
                <div class="title-name">请求参数</div>
                <n-button color="primary" :loading="state.loading" @click="testDatasource([], true)"
                  >测试连接</n-button
                >
              </div>
              <div class="header">
                <n-tabs v-model="state.headerTabs">
                  <n-tab
                    id="head"
                    :title="
                      '请求头（' +
                      state.headList.filter((val) => val.name && val.value).length +
                      '）'
                    "
                  />
                  <n-tab id="body" :title="'请求体（' + state.bodyDataNum + '）'" />
                </n-tabs>
                <div v-if="state.headerTabs === 'head'" class="requestHead">
                  <div v-for="(item, index) in state.headList" :key="index" class="label">
                    <n-input class="ipt" placeholder="键" maxlength="500" v-model="item.name" />
                    <n-input class="ipt" placeholder="值" maxlength="5000" v-model="item.value" />
                    <div class="btn">
                      <SvgIcon
                        icon="filter-add"
                        class="pic"
                        title="添加"
                        @click="addFn(index, 'headList')"
                      />
                      <SvgIcon
                        v-if="state.headList.length > 1"
                        icon="filter-cut"
                        class="pic cut"
                        title="删除"
                        @click="cutFn(index, 'headList')"
                      />
                    </div>
                  </div>
                </div>
                <div v-if="state.headerTabs === 'body'" class="requestBody">
                  <div class="radioBox">
                    <n-radio-group
                      direction="row"
                      v-model="state.contentType"
                      @change="getBodyDataFn"
                    >
                      <n-radio
                        v-for="(item, index) in state.contentTypeList"
                        :key="index"
                        :value="item.name"
                        :disabled="
                          (state.form.methodType === 'GET' && item.name !== 'none') ||
                          (state.form.methodType === 'POST' && item.name === 'none')
                        "
                        >{{ item.name }}</n-radio
                      >
                    </n-radio-group>
                  </div>
                  <template
                    v-if="
                      state.contentType === 'form-data' ||
                      state.contentType === 'x-www-from-urlencoded' ||
                      state.contentType === 'none'
                    "
                  >
                    <div v-for="(item, index) in state.bodyList" :key="index" class="label">
                      <n-input
                        class="ipt"
                        placeholder="键"
                        v-model="item.name"
                        maxlength="500"
                        @change="getBodyDataFn"
                      />
                      <n-input
                        class="ipt"
                        placeholder="值"
                        v-model="item.value"
                        maxlength="5000"
                        @change="getBodyDataFn"
                      />
                      <div class="btn">
                        <SvgIcon
                          icon="filter-add"
                          class="pic"
                          title="添加"
                          @click="addFn(index, 'bodyList')"
                        />
                        <SvgIcon
                          v-if="state.bodyList.length > 1"
                          icon="filter-cut"
                          class="pic cut"
                          title="删除"
                          @click="cutFn(index, 'bodyList')"
                        />
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <n-textarea
                      v-model="state.bodyText"
                      :rows="5"
                      show-count
                      maxlength="5000"
                      @change="getBodyDataFn"
                    />
                  </template>
                </div>
              </div>
              <div class="body">
                <div class="box">
                  <div class="name">数据响应</div>
                  <div class="case left">
                    <json-viewer
                      v-if="state.responseData"
                      :value="state.responseData"
                      :expand-depth="5"
                      boxed
                      sort
                    />
                  </div>
                </div>
                <img class="center" src="@img/arrow.png" />
                <div class="box">
                  <div class="name">数据结构确认<span>（是否为数据结构内字段）</span></div>
                  <div class="case right">
                    <div class="label head">
                      <n-checkbox
                        label="字段名"
                        :half-checked="state.halfChecked"
                        v-model="state.checkedAll"
                        @change="checkboxFn($event, 'ALL')"
                    /></div>
                    <div v-for="(item, index) in state.fieldList" :key="index" class="label">
                      <n-checkbox
                        :label="item.name"
                        v-model="item.checked"
                        @change="checkboxFn($event, 'SINGLE')"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </n-col>
          </n-row>
        </div>
      </div>
    </div>
    <div class="footer">
      <n-button
        v-if="state.pageType !== 'DETAIL'"
        :loading="state.loading"
        variant="solid"
        class="footer-btn"
        @click.prevent="saveFn"
        >保存</n-button
      >
      <n-button class="footer-btn" @click.prevent="cancelFn">{{
        state.pageType === 'DETAIL' ? '返回' : '取消'
      }}</n-button>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { checkCName, checkUrl, checkName } from '@/utils/validate'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { queryDataTypeTree } from '@/api/dataManage'
  import TreeSelect from '@comp/cfTreeSelect/index.vue'
  const store = useStore()
  const router = useRouter()
  // 获取当前组件实例
  const { proxy } = getCurrentInstance()
  const $Form = ref()
  const state = reactive({
    pageType: 'add',
    loading: false,
    hasTest: false,
    checkedAll: false,
    halfChecked: false,
    fieldList: [],
    form: {
      name: '',
      methodType: 'GET',
      url: '',
      envType: 'OFFICIAL',
      confidentialityLevel: '', //密级
      dataCategoryId: '', //数据分类
      table: '',
      description: '',
      type: 'PROJECT',
      status: 'CREATED',
      datasourceType: 'API',
      dataStructureType: 'ALL_STRUCTURE',
      enabled: true,
    },
    confidentialityLevelOptions: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
    rules: {
      name: [
        {
          required: true,
          validator: (...args) => {
            checkCName(...args, 'project', 'validDatasource', {
              type: 'PROJECT',
              name: state.form.name,
              id: state.id || null,
            })
          },
          trigger: 'blur',
        },
      ],
      dataCategoryId: [{ required: true, message: '请选择数据分类', trigger: 'blur' }],
      confidentialityLevel: [{ required: true, message: '请选择数据密级', trigger: 'blur' }],
      url: [{ required: true, validator: checkUrl, trigger: 'blur' }],
      envType: [{ required: true, message: '请选择数据环境', trigger: 'blur' }],
      table: [{ required: true, validator: checkName, trigger: 'blur' }],
      description: [{ required: true, message: '请输入描述信息', trigger: 'blur' }],
    },
    id: '',
    responseData: null,
    headerTabs: 'head',
    headList: [{ name: '', value: '' }],
    bodyList: [{ name: '', value: '' }],
    bodyText: '',
    contentType: 'none',
    contentTypeList: [
      { name: 'form-data' },
      { name: 'x-www-from-urlencoded' },
      { name: 'json' },
      { name: 'xml' },
      { name: 'raw' },
      { name: 'none' },
    ],
    bodyDataNum: 0,
  })

  const inputFn = () => {
    state.hasTest = false
    state.fieldList = []
    state.checkedAll = false
    state.halfChecked = false
    state.responseData = null
  }
  // 获取详情
  const getDetailFn = (id) => {
    api.project
      .getDatasourceDetailByAes({ id })
      .then((res) => {
        state.loading = false
        let { success, data } = res
        if (success) {
          let extConf = JSON.parse(data.extConf)
          state.hasTest = true
          state.form = {
            name: data.name,
            methodType: extConf.methodType,
            url: data.url,
            envType: data.envType,
            table: extConf.table,
            description: data.description,
            dataCategoryId: JSON.stringify(data.dataCategoryId),
            confidentialityLevel: data.confidentialityLevel,
            type: 'PROJECT',
            status: 'CREATED',
            datasourceType: 'API',
            dataStructureType: 'ALL_STRUCTURE',
            enabled: true,
          }
          let headList = [],
            bodyList = []
          for (let key in extConf.headers) {
            headList.push({
              name: key,
              value: extConf.headers[key],
            })
          }
          if (headList.length === 0) {
            headList = [{ name: '', value: '' }]
          }
          state.headList = headList
          state.contentType = extConf.contentType
          if (
            state.contentType === 'form-data' ||
            state.contentType === 'x-www-from-urlencoded' ||
            state.contentType === 'none'
          ) {
            for (let key in extConf.params) {
              bodyList.push({
                name: key,
                value: extConf.params[key],
              })
            }
          } else {
            state.bodyText = extConf.params
          }
          if (bodyList.length === 0) {
            bodyList = [{ name: '', value: '' }]
          }
          state.bodyList = bodyList
          nextTick(() => {
            getBodyDataFn()
            testDatasource(extConf.mapping)
          })
        }
      })
      .catch(() => {
        state.loading = false
      })
  }

  // 获取树列表
  const getTreeListFn = () => {
    queryDataTypeTree({}).then((res) => {
      if (res.code === 'SUCCESS') {
        let treeData = []
        if (res.data?.children.length > 0) {
          treeData = [res.data]
          treeData[0].selected = true
        } else {
          treeData.push({
            children: [],
            id: null,
            level: 0,
            label: '全部',
            type: 'ROOT',
          })
        }
        if (treeData.length > 0) {
          treeData[0].expanded = true
        }
        state.treeData = [...treeData]
        state.defaultTreeData = [...treeData]
        state.form.dataCategoryId = state.form.dataCategoryId ? state.form.dataCategoryId : ''
      }
    })
  }

  // 请求方式切换
  const methodTypeChange = (e) => {
    if (e.value === 'POST') {
      state.contentType = 'form-data'
    } else {
      state.contentType = 'none'
    }
    getBodyDataFn()
  }

  // 增加
  const addFn = (index, name) => {
    let item = {
      name: '',
      value: '',
    }
    state[name].splice(index + 1, 0, item)
  }
  // 减少
  const cutFn = (index, name) => {
    state[name].splice(index, 1)
    getBodyDataFn()
  }

  // 选择
  const checkboxFn = (flag, name) => {
    if (name === 'ALL') {
      state.checkedAll = flag
      state.halfChecked = false
      state.fieldList = state.fieldList.map((val) => {
        val.checked = flag
        return val
      })
    } else {
      let checkNum = state.fieldList.filter((val) => val.checked).length
      if (flag) {
        if (checkNum === state.fieldList.length) {
          state.halfChecked = false
          state.checkedAll = true
        } else {
          state.halfChecked = true
          state.checkedAll = false
        }
      } else {
        if (checkNum > 0) {
          state.checkedAll = false
          state.halfChecked = true
        } else {
          state.checkedAll = false
          state.halfChecked = false
        }
      }
    }
  }

  // 计算请求体数量
  const getBodyDataFn = () => {
    if (
      state.contentType === 'form-data' ||
      state.contentType === 'x-www-from-urlencoded' ||
      state.contentType === 'none'
    ) {
      state.bodyDataNum = state.bodyList.filter((val) => val.name && val.value).length
    } else {
      if (state.bodyText) {
        state.bodyDataNum = 1
      } else {
        state.bodyDataNum = 0
      }
    }
  }

  // 测试数据源
  const testDatasource = (mapping = [], flag = false) => {
    state.fieldList = []
    state.checkedAll = false
    state.halfChecked = false
    state.responseData = null
    $Form.value.validate((valid) => {
      if (valid) {
        state.loading = true
        let data = {
          ...state.form,
          extConf: {
            methodType: state.form.methodType,
            table: state.form.table,
            headers: {},
            contentType: state.contentType,
            params: {},
          },
        }
        state.headList.forEach((val) => {
          if (val.name) {
            data.extConf.headers[val.name] = val.value
          }
        })
        if (
          state.contentType === 'form-data' ||
          state.contentType === 'x-www-from-urlencoded' ||
          state.contentType === 'none'
        ) {
          state.bodyList.forEach((val) => {
            if (val.name) {
              data.extConf.params[val.name] = val.value
            }
          })
        } else {
          data.extConf.params = state.bodyText
        }
        data.extConf = JSON.stringify(data.extConf)
        delete data.methodType
        delete data.table
        api.project
          .testDatasource(data)
          .then((res) => {
            let { success } = res
            state.loading = false
            if (success) {
              let { success: go, data } = res.data
              if (go) {
                state.hasTest = true
                data.dataStructure.forEach((val) => {
                  let checked = false
                  mapping.forEach((item) => {
                    if (item === val) {
                      checked = true
                    }
                  })
                  state.fieldList.push({
                    name: val,
                    checked: checked,
                  })
                })
                let checkNum = state.fieldList.filter((val) => val.checked).length
                if (checkNum === state.fieldList.length) {
                  state.halfChecked = false
                  state.checkedAll = true
                } else {
                  if (checkNum > 0) {
                    state.checkedAll = false
                    state.halfChecked = true
                  }
                }
                state.responseData = JSON.parse(data.data)
                if (flag) {
                  ElNotification({
                    title: '提示',
                    message: '测试连接成功',
                    type: 'success',
                  })
                }
              } else {
                ElNotification({
                  title: '提示',
                  message: '测试连接失败',
                  type: 'error',
                })
              }
            }
          })
          .catch(() => {
            state.loading = false
          })
      }
    })
  }

  const saveFn = async () => {
    await $Form.value.validate((valid) => {
      if (valid) {
        if (!state.hasTest) {
          ElNotification({
            title: '提示',
            message: '请先测试连接',
            type: 'error',
          })
          return false
        }
        let data = {
          ...state.form,
          extConf: {
            methodType: state.form.methodType,
            table: state.form.table,
            headers: {},
            contentType: state.contentType,
            params: {},
            mapping: [],
          },
        }
        state.headList.forEach((val) => {
          if (val.name) {
            data.extConf.headers[val.name] = val.value
          }
        })
        if (
          state.contentType === 'form-data' ||
          state.contentType === 'x-www-from-urlencoded' ||
          state.contentType === 'none'
        ) {
          state.bodyList.forEach((val) => {
            if (val.name) {
              data.extConf.params[val.name] = val.value
            }
          })
        } else {
          data.extConf.params = state.bodyText
        }
        let interface_url = 'datasourceAdd'
        let message = '新增成功'
        if (state.id) {
          interface_url = 'datasourceUpdate' // 更新
          data.id = state.id
          message = '更新成功'
        }
        state.fieldList.forEach((val) => {
          if (val.checked) {
            data.extConf.mapping.push(val.name)
          }
        })
        if (data.extConf.mapping.length === 0) {
          ElNotification({
            title: '提示',
            message: '请先勾选数据响应字段',
            type: 'error',
          })
          return false
        }
        data.extConf = JSON.stringify(data.extConf)
        delete data.methodType
        delete data.table
        state.loading = true
        api.project[interface_url](data)
          .then((res) => {
            let { success } = res
            state.loading = false
            if (success) {
              ElNotification({
                title: '提示',
                message,
                type: 'success',
              })
              router.push({ name: 'dataSourceManagementIndex' })
            }
          })
          .catch(() => {
            state.loading = false
          })
      }
    })
  }

  // 取消
  const cancelFn = () => {
    router.go(-1)
  }

  // 初始化
  onMounted(() => {
    let id = router.currentRoute.value.query.id || null
    state.id = id
    state.pageType = router.currentRoute.value.query.type
    if (id) {
      state.form.id = id
      getDetailFn(id)
    }
    getTreeListFn()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    position: relative;
    &-box {
      box-sizing: border-box;
      height: calc(100% - 60px);
      padding: 12px 25px 30px 25px;
      :deep(.nancalui-select.compose) {
        width: 55px;
      }

      &-content {
        padding-left: 6px;
        .form {
          width: 90%;
          margin: 0 auto;
        }
        .h2 {
          position: relative;
          z-index: 2;
          margin-bottom: 20px;
          padding: 12px 0;
          color: #000000;
          font-weight: bold;
          font-size: 14px;
          border-bottom: 1px solid #ebedf0;
          &:before {
            position: absolute;
            bottom: 12px;
            left: 0;
            z-index: 1;
            width: 56px;
            height: 6px;
            background: #008ae7;
            opacity: 0.2;
            content: '';
          }
        }
        &-setting {
          box-sizing: border-box;
          width: 90%;
          margin: 0 auto;
          padding-left: 90px;
          .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            color: #333333;
            font-weight: bold;
            font-size: 14px;

            &-name {
              position: relative;
              //&:before {
              //  content: '*';
              //  color: #f54446;
              //  font-size: 14px;
              //}
            }
          }
          .header {
            box-sizing: border-box;
            width: 100%;
            height: 206px;
            padding: 0 16px;
            overflow-y: auto;
            border: 1px solid #cfcfcf;
            border-radius: 4px;
            .requestHead,
            .requestBody {
              box-sizing: border-box;
              height: 140px;
              margin-top: 10px;
              overflow-y: auto;
              &::-webkit-scrollbar {
                width: 5px; // 横向滚动条
                height: 5px; // 纵向滚动条
              }
              .label {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;
                .ipt {
                  width: calc((100% - 70px) / 2);
                }
                .btn {
                  width: 50px;
                  .pic {
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                    color: #999999;
                    cursor: pointer;
                    &:last-of-type {
                      margin-right: 0;
                    }
                    &:hover {
                      color: $themeBlue;
                    }
                  }
                }
              }
            }
            .requestBody {
              .radioBox {
                margin-bottom: 12px;
              }
            }
          }
          .body {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 16px;
            .box {
              width: calc((100% - 80px) / 2);
              .name {
                margin-bottom: 8px;
                color: #333333;
                font-weight: bold;
                font-size: 14px;
                span {
                  color: #666666;
                  font-weight: normal;
                  font-size: 12px;
                }
              }
              .case {
                box-sizing: border-box;
                width: 100%;
                height: 360px;
                overflow: auto;
                border: 1px solid #cfcfcf;
                border-radius: 4px;
                &::-webkit-scrollbar {
                  width: 5px; // 横向滚动条
                  height: 5px; // 纵向滚动条
                }
                &.left {
                  padding: 16px;
                  background-color: #f7f8fa;
                  .jv-container {
                    min-height: 100%;
                    background-color: transparent;
                    box-shadow: none !important;
                    &.boxed {
                      border: none;
                    }
                    :deep(.jv-code) {
                      max-height: 100% !important;
                      padding: 0;
                    }
                  }
                }
                &.right {
                  padding: 16px;
                  .label {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    height: 40px;
                    padding: 0 16px;
                    border: 1px solid #ebedf0;
                    border-top: none;
                    &.head {
                      background-color: #f2f3f6;
                      border-top: 1px solid #ebedf0;
                    }
                  }
                }
              }
            }
            .center {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      height: 60px;
      margin-top: 10px;
      margin-right: -10px;
      margin-left: -10px;
      padding: 16px 30px;
      overflow: hidden;
      background: #ffffff;
      border-radius: 4px 4px 0 0;

      &-btn {
        margin: 0 10px;
      }
    }
  }
</style>
