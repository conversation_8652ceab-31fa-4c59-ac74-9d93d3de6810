import { Graph } from '@antv/x6'
import '@antv/x6-vue-shape'
import Box from './box.vue'

const LINE_HEIGHT = 32
const NODE_WIDTH = 300
Graph.registerPortLayout(
  'erPortPosition',
  (portsPositionArgs) => {
    return portsPositionArgs.map((_, index) => {
      let y = (index + 1) * LINE_HEIGHT
      return {
        position: {
          x: 0,
          y,
        },
        angle: 0,
      }
    })
  },
  true,
)
// 生成自定义节点
export const chartNode = Graph.registerNode('combining-rect', {
  inherit: 'vue-shape',
  component: Box,
  ports: {
    groups: {
      list: {
        markup: [
          {
            tagName: 'rect',
            selector: 'portBody',
          },
          {
            tagName: 'text',
            selector: 'sortNum',
          },
          {
            tagName: 'text',
            selector: 'portDisc',
          },
          {
            tagName: 'text',
            selector: 'portBtn',
          },
          {
            tagName: 'rect',
            selector: 'portRect',
          },
        ],
        attrs: {
          portBody: {
            width: NODE_WIDTH,
            height: LINE_HEIGHT,
            strokeWidth: 1,
            stroke: '#E5E5E5',
            fill: '#fff', //内容颜色和边框
            magnet: true,
          },
          sortNum: {
            ref: 'portBody',
            refX: 0,
            refY: 0,
            fontSize: 0,
            magnet: false,
          },
          portDisc: {
            ref: 'portBody',
            refX: 16,
            refY: 10,
            fontSize: 14,
            magnet: false,
            textWrap: {
              ellipsis: true, // 文本超出显示范围时，自动添加省略号
              breakWord: true, // 是否截断单词
            },
          },
          portBtn: {
            ref: 'portBody',
            refX: '50%',
            refY: '50%',
            fontSize: 14,
            // fill: 'red',
            magnet: false,
            textWrap: {
              ellipsis: true, // 文本超出显示范围时，自动添加省略号
              breakWord: true, // 是否截断单词
            },
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
          portRect: {
            magnet: true,
            ref: 'portBody',
            refX: 288,
            refY: 13,
            width: '6px',
            height: '6px',
          },
        },
        position: 'erPortPosition',
      },
    },
  },
})
// 生成状态节点
export const statusNode = Graph.registerNode('status-node', {
  inherit: 'vue-shape',
  component: Box,
  ports: {
    groups: {
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#C2C8D5',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#C2C8D5',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
    },
  },
})
