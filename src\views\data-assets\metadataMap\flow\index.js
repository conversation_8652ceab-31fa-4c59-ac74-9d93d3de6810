import { Graph } from '@antv/x6'
import './shape'

let graph = null
export default class FlowGraph {
  static init() {
    graph = new Graph({
      container: document.getElementById('metadataMap'),
      width: 1680,
      height: 771,
      interacting: {
        nodeMovable: false,
      },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      mousewheel: {
        enabled: true,
      },
      grid: { visible: false },
      nodeStateStyles: {},
      edgeStateStyles: {}, // 配置边状态样式
      comboStateStyles: {}, // 配置分组状态样式
    })
    return graph
  }
  // 销毁
  static destroy() {
    if (graph) {
      graph.dispose()
    }
  }
}
