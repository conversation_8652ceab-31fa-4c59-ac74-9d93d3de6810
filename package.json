{"name": "yy-admin", "version": "0.0.2", "author": "能科瑞元", "scripts": {"build": "vite build --mode production", "build:dev": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode development", "build:lzos": "vite build --mode lzos", "build:pro": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode production", "deps": "yarn upgrade-interactive --latest", "dev": "vite --mode development", "dev:lzos": "vite --mode lzos", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:lint-staged": "lint-staged", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "plop": "plop", "preview": "vite preview", "serve": "vite preview"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@antv/chart-node-g6": "^0.0.4", "@antv/g6": "^4.8.7", "@antv/hierarchy": "^0.6.8", "@antv/layout": "^0.1.31", "@antv/x6": "^1.29.1", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-vue-shape": "^1.1.6", "@devui-design/icons": "^1.3.0", "@element-plus/icons-vue": "^2.0.4", "@floating-ui/dom": "^1.1.0", "@lk77/vue3-color": "^3.0.6", "@onlyoffice/document-editor-vue": "^1.4.0", "@types/three": "^0.174.0", "@vue-office/excel": "^1.7.14", "@vueuse/components": "^8.6.0", "@vueuse/core": "^8.6.0", "FileSaver": "^0.10.0", "axios": "^0.27.2", "clipboard": "^2.0.11", "codemirror": "^5.65.16", "codemirror-editor-vue3": "^2.5.8", "d3": "^7.8.5", "dagre": "^0.8.5", "docx-preview": "^0.1.22", "dom-to-image": "^2.6.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "element-plus": "2.8.8", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "insert-css": "^2.0.0", "js-beautify": "^1.15.1", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "js-pinyin": "^0.2.5", "js-web-screen-shot": "^1.9.9-rc.20", "jsonlint": "^1.6.3", "jsonlint-mod": "^1.7.6", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "luckyexcel": "^1.0.0", "mammoth": "^1.7.2", "mitt": "^3.0.0", "mockjs": "^1.1.0", "modern-screenshot": "^4.4.38", "moment": "^2.29.4", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pdfjs-dist": "^3.0.279", "pinia": "^2.0.14", "plop": "^3.1.0", "pnpm": "^8.15.9", "print-js": "^1.6.0", "qs": "^6.10.5", "script-loader": "0.7.2", "sql-formatter": "15.1.2", "swiper": "^7.4.1", "three": "^0.174.0", "vue": "^3.2.26", "vue-clipboard3": "^2.0.0", "vue-demi": "0.14.6", "vue-draggable-next": "^2.2.1", "vue-router": "^4.0.15", "vue3-draggable-resizable": "^1.6.4", "vue3-json-viewer": "^2.2.2", "vue3-treeselect": "^0.1.10", "vuex": "^4.0.2", "xlsx": "^0.18.5"}, "devDependencies": {"3d-force-graph": "^1.70.14", "@types/js-md5": "^0.4.3", "@types/node": "^17.0.40", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "@vitejs/plugin-legacy": "^1.8.0", "@vitejs/plugin-vue": "^2.3.3", "@vitejs/plugin-vue-jsx": "^1.3.10", "autoprefixer": "^10.4.7", "eslint": "^8.17.0", "eslint-config-prettier": "^8.5.0", "eslint-define-config": "^1.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.1.0", "file-saver": "^2.0.5", "import": "^0.0.6", "increase-memory-limit": "^1.0.7", "less": "^4.1.2", "less-loader": "^11.0.0", "lint-staged": "^12.4.3", "postcss": "^8.4.14", "postcss-html": "^1.4.1", "postcss-less": "^6.0.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.45.0", "stylelint": "^14.7.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "tailwindcss": "^3.0.24", "terser": "^5.37.0", "typescript": "^4.6.3", "unplugin-auto-import": "^0.8.7", "unplugin-icons": "^0.14.3", "unplugin-vue-components": "^0.19.6", "vite": "^5.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-pages": "^0.23.0", "vite-plugin-restart": "^0.1.1", "vite-plugin-static-copy": "^0.17.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^3.3.0", "vue-tsc": "^0.37.1"}}