import userApi from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

// import { resetRouter } from '@/router'
//扁平化菜单，用做按钮权限判断的数组
const flatten = (arr) => {
  return arr.reduce((pre, cur) => {
    pre.push(cur)
    if (cur.children) {
      return pre.concat(flatten(cur.children))
    } else {
      return pre
    }
  }, [])
}
const getDefaultState = () => {
  return {
    inteLogin: false, //是否是集成平台
    token: getToken(),
    name: '',
    isAdmin: false,
    enabledPt: false, //中间平台账号标识，
    authorizationCode: '',
    roleCode: null, // 三员人员标识,1.系统管理员2.安全保密员3.安全审计员
    roles: '',
    avatar: '',
    id: '',
    menu: [], // 可查看菜单列表
    buttonAuthList: [], // 可查看按钮权限列表
    menuCodeList: [], // 可查看菜单数组列表
    menuImplantList: [], // 可查看嵌入页面列表
    whiteOrderNavbar: false,
    dataGovernanceStatus: false, // 是否展示数据治理
    dataAssetsStatus: false, // 是否展示数据资产
    systemMenuStatus: false, // 是否展示系统管理
    projectMenuStatus: false, // 是否展示项目管理
    applicationMenuStatus: false, // 是否展示数据应用
    isSystemMenu: false, // 当前是否在系统管理
    defaultMenuList: [], // 默认所有路由
    menuTreeList: [], // 菜单树
    addProjectStatus: false, // 判断是否新增了场景
    currentProject: { id: '', projectCode: '', name: '' }, // 当前选择的场景
    projectList: [], //所有场景列表
    activeMenu: '', // 当前菜单栏选中的路由name
    activeMenuCode: '', // 当前菜单栏选中的路由的code
    checkFirstMenuCode: null, // 一级菜单选中项的code
    threeWebsocketUrl: '', // websocket接口地址
    mrs: {}, // 跳转其他地址
    homeType: 4, // 首页权限类型 1：管理员 2：业务人员 3：技术人员 4：普通首页人员
    clickNode: null,
    pagination: {}, //记录列表页的分页信息，便于返回到之前的页码
  }
}
const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    localStorage.setItem('isNormalLogin', '0')
    Object.assign(state, getDefaultState())
  },
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_ADMIN: (state, name) => {
    state.isAdmin = name
  },
  SET_CON_STATUS: (state, status) => {
    state.enabledPt = status
  },
  SET_INTE_STATUS: (state, status) => {
    state.inteLogin = status
  },
  SET_AUTH_CODE: (state, authorizationCode) => {
    state.authorizationCode = authorizationCode
  },
  SET_ROLE_CODE: (state, name) => {
    state.roleCode = name
  },
  SET_ROLE: (state, roles) => {
    state.roles = roles
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_MENU: (state, menu) => {
    state.menu = menu
    state.dataGovernanceStatus = false
    state.dataAssetsStatus = false
    state.systemMenuStatus = false
    state.projectMenuStatus = false
    state.applicationMenuStatus = false
    menu.forEach((node) => {
      if (node.code === 'dataGovernance') {
        state.dataGovernanceStatus = true
      }
      if (node.code === 'systemManagement') {
        state.systemMenuStatus = true
      }
      if (node.code === 'projectManage') {
        state.projectMenuStatus = true
      }
      if (node.code === 'dataAssets') {
        state.dataAssetsStatus = true
      }
      if (node.code === 'data_application') {
        state.applicationMenuStatus = true
      }
    })
  },
  SET_MENU_LIST(state, list) {
    state.menuCodeList = list
  },
  SET_MENU_IMPLANT_LIST(state, list) {
    state.menuImplantList = list
  },
  SET_DEFAULT_MENU(state, list) {
    state.defaultMenuList = list
  },
  SET_MENU_TREE(state, list) {
    state.menuTreeList = list
  },
  ADD_PROJECT(state, STATUS) {
    state.addProjectStatus = STATUS
  },
  SET_PROJECT(state, info) {
    state.currentProject = info
  },
  SET_PROJECTLIST(state, info) {
    state.projectList = info
  },
  SET_ACTIVE_MENU(state, name) {
    state.activeMenu = name
  },
  SET_ACTIVE_MENU_CODE(state, code) {
    state.activeMenuCode = code
  },
  SET_ACTIVE_SYS(state, name) {
    state.isSystemMenu = name
  },
  SET_BUTTON_AUTH_LIST(state, list) {
    state.buttonAuthList = list
  },
  SET_FIRST_MENU(state, index) {
    state.checkFirstMenuCode = index
  },
  SET_WEBSOCKET(state, info) {
    state.threeWebsocketUrl = info
  },
  SET_OTHER_MRS(state, info) {
    state.mrs = info
  },
  SET_HOME_TYPE(state, type) {
    state.homeType = type
  },
  SET_NODE(state, node) {
    state.clickNode = node
  },
  SET_AUDIT_INFO(state, flag) {
    state.isAudit = flag
  },
  SET_PAGINATION_INFO(state, info) {
    if (state.pagination) {
      state.pagination[info.routerName] = info.pagination
    } else {
      state.pagination = {
        [info.routerName]: info.pagination,
      }
    }
  },
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password, isInte } = userInfo
    return new Promise((resolve, reject) => {
      userApi
        .login({ username, password })
        .then((response) => {
          const { data } = response
          if (data === null) {
            ElMessage({
              ElMessage: '密码或账号错误',
              type: 'error',
              duration: 5 * 1000,
            })
            return
          }
          let menuTreeArr = data?.apps?.filter((val) => val.appCode === 'data')
          if (menuTreeArr.length > 0) {
            // 设置token
            commit('SET_TOKEN', data.data)
            setToken(data.data)
            // menuTreeArr[0].menus.push({
            //   resourceType: "MENU",
            //   parentCode: "dataAssetsManager",
            //   id: 21110000460,
            //   name: "资产全景",
            //   path: null,
            //   url: null,
            //   httpMethod: null,
            //   description: null,
            //   code: "newAssetPanorama",
            //   icon: null,
            //   sort: 1,
            //   isEntry: false,
            //   children:null
            // })
            commit('SET_ROLE', data.roles)
            commit('SET_ID', data.userId)
            commit('SET_NAME', data.name || data.username)
            commit('SET_ADMIN', data.isAdmin)
            commit('SET_ROLE_CODE', data.threeManagerCode || null)
            commit('SET_CON_STATUS', data.enabledPt)
            // 获取按钮权限
            let menuTree = menuTreeArr[0]?.menuTree
            menuTree.forEach((val) => {
              if (val.code === 'systemManagement') {
                let hasLinkConfigManage = false
                val.children.forEach((v) => {
                  if (v.code === 'linkConfigManage') {
                    hasLinkConfigManage = true
                  }
                })
                if (
                  data.threeManagerCode === 1 ||
                  data.threeManagerCode === 2 ||
                  data.threeManagerCode === 3 ||
                  data.isAdmin
                ) {
                  if (!hasLinkConfigManage) {
                    let item = {
                      code: 'linkConfigManage',
                      description: null,
                      httpMethod: null,
                      icon: null,
                      id: 20000000647,
                      isEntry: false,
                      name: '链接管理',
                      parentCode: 'systemManagement',
                      path: null,
                      resourceType: 'MENU',
                      sort: 8,
                      url: null,
                    }
                    val.children.push({ ...item })
                    menuTreeArr[0].menus.push({ ...item })
                  }
                } else {
                  if (hasLinkConfigManage) {
                    val.children = val.children.filter((v) => v.code !== 'linkConfigManage')
                  }
                }
              }
              // if(val.code === 'dataAssetsManager'){
              //   let routerItem = {
              //     resourceType: "MENU",
              //     parentCode: "dataAssetsManager",
              //     id: 21110000460,
              //     name: "资产全景",
              //     path: null,
              //     url: null,
              //     httpMethod: null,
              //     description: null,
              //     code: "newAssetPanorama",
              //     icon: null,
              //     sort: 1,
              //     isEntry: false,
              //     children:null
              //   }
              //   if(val?.children?.length>0){
              //     val.children.unshift({...routerItem})
              //   }else {
              //     val.children = [{...routerItem}]
              //   }
              // }
            })
            let btnList = flatten(menuTree).filter((val) => val.resourceType === 'BUTTON')
            btnList = btnList.map((val) => val.code)
            commit('SET_BUTTON_AUTH_LIST', btnList)
            commit(
              'SET_MENU_LIST',
              menuTreeArr[0].menus.filter((val) => val.resourceType === 'MENU').map((v) => v.code),
            )
            if (!!isInte) {
              const menuTreeData = [
                {
                  resourceType: 'MENU',
                  parentCode: 'assetSystem',
                  id: 20001052,
                  name: '资源目录',
                  path: null,
                  url: null,
                  httpMethod: null,
                  description: null,
                  code: 'dataResourceDirectory',
                  icon: null,
                  sort: 2,
                  isEntry: false,
                  children: [
                    {
                      resourceType: 'MENU',
                      parentCode: 'dataResourceDirectory',
                      id: 20001074,
                      name: '数据目录',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'resourceDirectory',
                      icon: null,
                      sort: 1,
                      isEntry: false,
                      children: [
                        {
                          code: 'governanceManage_modal_dataCollectionResourceLibrary_view',
                          description: null,
                          httpMethod: null,
                          icon: null,
                          id: 20001055,
                          isEntry: false,
                          name: '查看按钮',
                          parentCode: 'resourceDirectory',
                          path: null,
                          resourceType: 'BUTTON',
                          sort: 1,
                          url: null,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'resourceDirectory',
                          id: 20001059,
                          name: '状态查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataCollectionResourceLibrary_resg_view',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'resourceDirectory',
                          id: 20001061,
                          name: '注册按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataCollectionResourceLibrary_resg_edit',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'resourceDirectory',
                          id: 20001063,
                          name: '批量注册按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataCollectionResourceLibrary_resgAll_edit',
                          icon: null,
                          sort: 4,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'resourceDirectory',
                          id: 20001057,
                          name: '下载按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataCollectionResourceLibrary_download_edit',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'dataResourceDirectory',
                      id: 20001039,
                      name: '元数据地图',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'metadataMap',
                      icon: null,
                      sort: 1,
                      isEntry: false,
                      children: [],
                    },
                  ],
                },
                {
                  resourceType: 'MENU',
                  parentCode: 'all',
                  id: 20001031,
                  name: '数据采集',
                  path: null,
                  url: null,
                  httpMethod: null,
                  description: null,
                  code: 'dataIntegration',
                  icon: null,
                  sort: 2,
                  isEntry: false,
                  children: [
                    {
                      resourceType: 'MENU',
                      parentCode: 'dataIntegration',
                      id: 12,
                      name: '数据源管理',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'sceneDataSourceManagement',
                      icon: null,
                      sort: 2,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 111,
                          name: '新增按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_add',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 112,
                          name: '编辑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_edit',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 113,
                          name: '删除按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_delete',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 114,
                          name: '查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_view',
                          icon: null,
                          sort: 4,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 115,
                          name: '申请按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_apply_edit',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 116,
                          name: '发布按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_publish_edit',
                          icon: null,
                          sort: 6,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 243,
                          name: '下架按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_off_edit',
                          icon: null,
                          sort: 7,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 302,
                          name: '解绑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_unbind_edit',
                          icon: null,
                          sort: 8,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'sceneDataSourceManagement',
                          id: 303,
                          name: '测试按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataSourceManagement_test_edit',
                          icon: null,
                          sort: 9,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },

                    {
                      resourceType: 'MENU',
                      parentCode: 'dataIntegration',
                      id: 7,
                      name: '数据模型',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'dataModal',
                      icon: null,
                      sort: 2,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'dataModal',
                          id: 161,
                          name: '新增按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataModal_add',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'dataModal',
                          id: 162,
                          name: '逆向按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataModal_reverse_add',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'dataModal',
                          id: 163,
                          name: '查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataModal_view',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'dataModal',
                          id: 164,
                          name: '编辑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataModal_edit',
                          icon: null,
                          sort: 4,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'dataModal',
                          id: 165,
                          name: '删除按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'governanceManage_modal_dataModal_delete',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'dataIntegration',
                      id: 13,
                      name: '数据采集',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'DataCollection',
                      icon: null,
                      sort: 3,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 117,
                          name: '新增按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_add',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 120,
                          name: '编辑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_edit',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 249,
                          name: '删除按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_delete',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 121,
                          name: '查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_view',
                          icon: null,
                          sort: 4,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 250,
                          name: '发布按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_publish_edit',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'DataCollection',
                          id: 122,
                          name: '下架按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataManagement_dataCollection_off_edit',
                          icon: null,
                          sort: 6,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'dataIntegration',
                      id: 18,
                      name: '数据清洗',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'dataDev',
                      icon: null,
                      sort: 4,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'MENU',
                          parentCode: 'dataDev',
                          id: 20,
                          name: '离线作业',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'dataWork',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 186,
                              name: '新增按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_add',
                              icon: null,
                              sort: 1,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 189,
                              name: '数据开发按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_dev_edit',
                              icon: null,
                              sort: 2,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 190,
                              name: '发布按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_push_edit',
                              icon: null,
                              sort: 3,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 264,
                              name: '下架按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_off_edit',
                              icon: null,
                              sort: 4,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 265,
                              name: '撤回按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_cancel_edit',
                              icon: null,
                              sort: 5,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 319,
                              name: '查看按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_view',
                              icon: null,
                              sort: 6,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 191,
                              name: '删除按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_delete',
                              icon: null,
                              sort: 7,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 192,
                              name: '编辑按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_edit',
                              icon: null,
                              sort: 8,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 193,
                              name: '执行按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_run_edit',
                              icon: null,
                              sort: 9,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'dataWork',
                              id: 320,
                              name: '一键复制按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_dataWork_dev_copy_edit',
                              icon: null,
                              sort: 10,
                              isEntry: false,
                              children: [],
                            },
                          ],
                        },
                        {
                          resourceType: 'MENU',
                          parentCode: 'dataDev',
                          id: 77,
                          name: '实时作业',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'realTimeWork',
                          icon: null,
                          sort: 6,
                          isEntry: false,
                          children: [
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 201,
                              name: '新增按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_add',
                              icon: null,
                              sort: 1,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 202,
                              name: '数据开发按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_dev_edit',
                              icon: null,
                              sort: 2,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 203,
                              name: '发布按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_push_edit',
                              icon: null,
                              sort: 3,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 266,
                              name: '下架按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_off_edit',
                              icon: null,
                              sort: 4,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 204,
                              name: '删除按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_delete',
                              icon: null,
                              sort: 5,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 205,
                              name: '编辑按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_edit',
                              icon: null,
                              sort: 6,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 267,
                              name: '撤回按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_cancel_edit',
                              icon: null,
                              sort: 7,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 318,
                              name: '查看按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_see',
                              icon: null,
                              sort: 8,
                              isEntry: false,
                              children: [],
                            },
                            {
                              resourceType: 'BUTTON',
                              parentCode: 'realTimeWork',
                              id: 356,
                              name: '一键复制按钮',
                              path: null,
                              url: null,
                              httpMethod: null,
                              description: null,
                              code: 'governanceManage_dataDev_realTimeWork_dev_copy_edit',
                              icon: null,
                              sort: 9,
                              isEntry: false,
                              children: [],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
                {
                  resourceType: 'MENU',
                  parentCode: 'all',
                  id: 30,
                  name: '数据服务',
                  path: null,
                  url: null,
                  httpMethod: null,
                  description: null,
                  code: 'service',
                  icon: null,
                  sort: 5,
                  isEntry: false,
                  children: [
                    {
                      resourceType: 'MENU',
                      parentCode: 'service',
                      id: 32,
                      name: 'API管理',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'api',
                      icon: null,
                      sort: 1,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 128,
                          name: '新增按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_add',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 251,
                          name: '编辑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_edit',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 130,
                          name: '删除按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_delete',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 252,
                          name: '查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_view',
                          icon: null,
                          sort: 4,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 133,
                          name: '测试按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_test_view',
                          icon: null,
                          sort: 5,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 253,
                          name: '发布按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_publish_edit',
                          icon: null,
                          sort: 6,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'api',
                          id: 254,
                          name: '下架按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_api_off_edit',
                          icon: null,
                          sort: 7,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'service',
                      id: 82,
                      name: '监控日志',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'APIMonitoringLog',
                      icon: null,
                      sort: 2,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'APIMonitoringLog',
                          id: 137,
                          name: '查看按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_APIMonitoringLog_view',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'service',
                      id: 81,
                      name: '白名单管理',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'whitelistManagement',
                      icon: null,
                      sort: 3,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'whitelistManagement',
                          id: 134,
                          name: '新增按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_apiWhiteOrder_add',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'whitelistManagement',
                          id: 135,
                          name: '删除按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_apiWhiteOrder_delete',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'whitelistManagement',
                          id: 136,
                          name: '编辑按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_apiWhiteOrder_edit',
                          icon: null,
                          sort: 3,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                    {
                      resourceType: 'MENU',
                      parentCode: 'service',
                      id: 88,
                      name: 'token管理',
                      path: null,
                      url: null,
                      httpMethod: null,
                      description: null,
                      code: 'token_manage',
                      icon: null,
                      sort: 4,
                      isEntry: false,
                      children: [
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'token_manage',
                          id: 138,
                          name: '复制按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_tokenManagement_copy_edit',
                          icon: null,
                          sort: 1,
                          isEntry: false,
                          children: [],
                        },
                        {
                          resourceType: 'BUTTON',
                          parentCode: 'token_manage',
                          id: 282,
                          name: 'Token有效期按钮',
                          path: null,
                          url: null,
                          httpMethod: null,
                          description: null,
                          code: 'service_tokenManagement_setPeriodOfValidity_edit',
                          icon: null,
                          sort: 2,
                          isEntry: false,
                          children: [],
                        },
                      ],
                    },
                  ],
                },
              ]

              commit('SET_INTE_STATUS', true)
              commit('SET_MENU_TREE', menuTreeData)
              commit(
                'SET_MENU',
                menuTreeData.filter((val) => val.resourceType === 'MENU'),
              )
            } else {
              if (menuTree[0]?.code === 'all') {
                commit('SET_MENU_TREE', menuTree[0]?.children || [])
              } else {
                commit('SET_MENU_TREE', menuTree || [])
              }
              // 设置菜单列表
              commit(
                'SET_MENU',
                flatten(menuTree).filter((val) => val.resourceType === 'MENU'),
              )
            }
            commit('SET_ACTIVE_SYS', false)
            commit(
              'SET_AVATAR',
              'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            )
            resolve()
          } else {
            reject('暂无权限访问')
          }
          // }
        })
        .catch((error) => {
          reject(error)
        })
    })

    // commit('SET_TOKEN', 'admin-token')
    // setToken('admin-token')
  },
  // 单点登录
  singlelogin({ commit }, auth) {
    return new Promise((resolve, reject) => {
      userApi
        .singlelogin({ auth })
        .then((response) => {
          const { data } = response

          if (data === null) {
            ElMessage({
              ElMessage: '密码或账号错误',
              type: 'error',
              duration: 5 * 1000,
            })
            return
          }
          let menuTreeArr = data?.apps?.filter((val) => val.appCode === 'data')
          if (menuTreeArr.length > 0) {
            // 设置token
            commit('SET_TOKEN', data.data)
            setToken(data.data)

            commit('SET_ROLE', data.roles)
            commit('SET_ID', data.userId)
            commit('SET_NAME', data.name || data.username)
            commit('SET_ADMIN', data.isAdmin)
            commit('SET_ROLE_CODE', data.threeManagerCode || null)
            commit('SET_CON_STATUS', data.enabledPt)
            // 获取按钮权限
            let menuTree = menuTreeArr[0]?.menuTree
            menuTree.forEach((val) => {
              if (val.code === 'systemManagement') {
                let hasLinkConfigManage = false
                val.children.forEach((v) => {
                  if (v.code === 'linkConfigManage') {
                    hasLinkConfigManage = true
                  }
                })
                if (
                  data.threeManagerCode === 1 ||
                  data.threeManagerCode === 2 ||
                  data.threeManagerCode === 3 ||
                  data.isAdmin
                ) {
                  if (!hasLinkConfigManage) {
                    let item = {
                      code: 'linkConfigManage',
                      description: null,
                      httpMethod: null,
                      icon: null,
                      id: 20000000647,
                      isEntry: false,
                      name: '链接管理',
                      parentCode: 'systemManagement',
                      path: null,
                      resourceType: 'MENU',
                      sort: 8,
                      url: null,
                    }
                    val.children.push({ ...item })
                    menuTreeArr[0].menus.push({ ...item })
                  }
                } else {
                  if (hasLinkConfigManage) {
                    val.children = val.children.filter((v) => v.code !== 'linkConfigManage')
                  }
                }
              }
              // if(val.code === 'dataAssetsManager'){
              //   let routerItem = {
              //     resourceType: "MENU",
              //     parentCode: "dataAssetsManager",
              //     id: 21110000460,
              //     name: "资产全景",
              //     path: null,
              //     url: null,
              //     httpMethod: null,
              //     description: null,
              //     code: "newAssetPanorama",
              //     icon: null,
              //     sort: 1,
              //     isEntry: false,
              //     children:null
              //   }
              //   if(val?.children?.length>0){
              //     val.children.unshift({...routerItem})
              //   }else {
              //     val.children = [{...routerItem}]
              //   }
              // }
            })
            let btnList = flatten(menuTree).filter((val) => val.resourceType === 'BUTTON')
            btnList = btnList.map((val) => val.code)
            commit('SET_BUTTON_AUTH_LIST', btnList)
            commit(
              'SET_MENU_LIST',
              menuTreeArr[0].menus.filter((val) => val.resourceType === 'MENU').map((v) => v.code),
            )
            if (menuTree[0]?.code === 'all') {
              commit('SET_MENU_TREE', menuTree[0]?.children || [])
            } else {
              commit('SET_MENU_TREE', menuTree || [])
            }
            // 设置菜单列表
            commit(
              'SET_MENU',
              flatten(menuTree).filter((val) => val.resourceType === 'MENU'),
            )

            commit('SET_ACTIVE_SYS', false)
            commit(
              'SET_AVATAR',
              'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
            )
            resolve()
          } else {
            reject('暂无权限访问')
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // get user info
  getInfo({ commit, state }) {},

  // user logout
  logout({ commit, state }) {
    removeToken()
    // resetRouter()
    commit('RESET_STATE')
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  },
  // 设置审批权限
  setAudit({ commit }, type) {
    // systemApi.getAuditEnable({ auditType: type }).then((res) => {
    //   commit('SET_AUDIT_INFO', res.data)
    // })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
