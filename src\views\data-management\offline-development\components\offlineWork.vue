<template>
  <div class="work-body" @click="state.configPopupShow = false">
    <div class="work-body-btn">
      <div v-if="state.isRun" class="btn" @click="stopFn(true)">
        <SvgIcon class="icon" icon="icon-offline-stop" />
        停止
      </div>
      <div v-else class="btn active" @click="runFn">
        <SvgIcon class="icon" icon="icon-offline-start" />
        运行
      </div>
      <div v-loading="state.loading" class="work-body-box">
        <div class="btn" @click="saveFn(false)">
          <SvgIcon class="icon" icon="icon-offline-save" />
          保存
        </div>
        <div class="btn" @click="submitFn">
          <SvgIcon class="icon" icon="icon-offline-submit" />
          提交
        </div>
        <div class="btn" @click="formatFn">
          <SvgIcon class="icon" icon="icon-offline-format" />
          格式化
        </div>
        <div class="btn" @click="checkSqlFn">
          <SvgIcon class="icon" icon="icon-offline-check" />
          代码检查
        </div>
        <!--      <div class="btn">-->
        <!--        <SvgIcon class="icon" icon="icon-offline-style" />-->
        <!--        风格-->
        <!--      </div>-->
      </div>
      <div v-show="state.showSubmit" class="submit">已提交V{{ state.versionNum }}</div>
    </div>
    <div class="work-body-code">
      <div class="work-body-code-textarea" ref="textareaRef">
        <codemirror
          ref="myCm"
          v-model:value="state.sql"
          class="codemirror"
          :options="state.sqlOption"
          @ready="onCmReady"
          @focus="onCmFocus"
          @input="onCmCodeChange"
        />
      </div>
      <div
        :class="{
          'work-body-code-run': true,
          showRun: state.showRun,
        }"
        ref="codeRunEl"
      >
        <div class="drag-line" ref="el" style="cursor: n-resize; height: 10px"></div>
        <div class="work-body-code-run-tabs">
          <n-tabs v-model="state.runType" @active-tab-change="activeTabChange">
            <n-tab id="log" title="运行日志" />
            <n-tab
              :id="'result_' + index"
              v-if="
                ['OW_HIVE_SQL', 'OW_SPARK_SQL', 'OW_DORIS_SQL'].includes(state.formData.taskType)
              "
              :title="'运行结果' + (index + 1)"
              v-for="(item, index) in state.tagArr"
              :key="index"
            >
              <template #title>
                {{ '运行结果' + (index + 1) }}
                <SvgIcon
                  style="margin-left: 8px"
                  icon="icon-offline-close"
                  @click.prevent="state.tagArr.splice(index, 1)"
                />
              </template>
            </n-tab>
            <n-tab
              id="result"
              v-if="
                !['OW_HIVE_SQL', 'OW_SPARK_SQL', 'OW_DORIS_SQL'].includes(
                  state.formData.taskType,
                ) &&
                !state.resultLoading &&
                state.hasResult
              "
              title="运行结果"
            />
          </n-tabs>
          <div class="work-body-code-run-tabs-btn">
            <n-button
              v-if="state.runType === 'log'"
              color="primary"
              :disabled="!state.runLogText || state.loading"
              @click.prevent.stop="downLogFn"
              >下载日志</n-button
            >
            <n-button
              v-if="state.runType.indexOf('result') !== -1"
              v-loading="state.downLoading"
              color="primary"
              @click.prevent.stop="downFn('CSV')"
              >导出CSV</n-button
            >
            <n-button
              v-if="state.runType.indexOf('result') !== -1"
              v-loading="state.downLoading"
              color="primary"
              @click.prevent.stop="downFn('EXCEL')"
              >导出EXCEL</n-button
            >
            <SvgIcon
              :class="{ icon: true, show: state.showRun }"
              icon="icon-arrow-second"
              @click.prevent.stop="showRunFn"
            />
          </div>
        </div>
        <div
          v-if="state.showRun"
          class="work-body-code-run-text"
          :key="codeRunEl.value?.style?.height"
        >
          <template v-if="state.runType === 'log'">
            <div class="textarea-loading log" v-loading="state.logLoading" ref="logContent">
              <div v-if="state.runLogText" class="textarea" v-html="state.runLogText"></div>
              <div v-if="!state.runLogText && state.runType === 'log'" class="empty">
                <img class="empty-pic" src="@/assets/img/empty_gray.png" />
                <div class="empty-text">暂无数据</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="textarea-loading" ref="logContent">
              <CfTable
                v-if="
                  ['OW_HIVE_SQL', 'OW_SPARK_SQL', 'OW_DORIS_SQL'].includes(state.formData.taskType)
                "
                :isDisplayAction="false"
                :showEmpty="true"
                :table-head-titles="state.tagArr[curTag]?.tableResultHeadTitles"
                :tableConfig="{
                  data: state.tagArr[curTag]?.tableResultData?.slice(
                    (state.tagArr[curTag]?.currentPage - 1) * state.tagArr[curTag]?.pageSize,
                    state.tagArr[curTag]?.currentPage * state.tagArr[curTag]?.pageSize,
                  ),
                  rowKey: 'id',
                  stripe: true,
                  'cell-style': {
                    height: '36px',
                    padding: ' 0',
                    'background-color': '#FFFFFF',
                  },
                  'header-cell-style': {
                    'background-color': '#EBF4FF',
                    padding: '0',
                    color: ' #1D2129',
                    'font-size': '14px',
                    'font-style': 'normal',
                    'font-weight': '400',
                  },
                }"
                isNeedIndex
                :paginationConfig="{
                  total: state.tagArr[curTag]?.tableResultData?.length,
                  pageSize: state.tagArr[curTag]?.pageSize,
                  currentPage: state.tagArr[curTag]?.currentPage,
                  onCurrentChange: (v) => {
                    state.tagArr[curTag] && (state.tagArr[curTag].currentPage = v)
                  },
                  onSizeChange: (v) => {
                    state.tagArr[curTag] && (state.tagArr[curTag].currentPage = 1)
                    state.tagArr[curTag] && (state.tagArr[curTag].pageSize = v)
                  },
                }"
                :actionWidth="130"
              />
              <n-textarea
                v-else
                v-model="state.runResultText"
                placeholder=""
                :autosize="{ minRows: 3 }"
                readonly
              />
            </div>
          </template>
        </div>
      </div>
      <div class="work-body-code-config">
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('attr', '属性配置')"
          >属性配置</div
        >
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('schedule', '调度配置')"
          >调度配置</div
        >
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('version', '版本管理')"
          >版本管理</div
        >
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('refer', '数据参考')"
          >数据参考</div
        >
      </div>
      <!-- 属性配置弹窗 -->
      <section
        v-if="state.configPopupShow"
        :class="{
          'config-popup': true,
          attr: state.configPopupShowType === 'attr',
          version: state.configPopupShowType === 'version',
        }"
        @click.prevent.stop="preventFn"
      >
        <div class="config-popup-title">
          <span class="line"></span>
          <span class="name">{{ state.configPopupShowTypeTitle }}</span>
        </div>
        <div class="config-popup-content scroll-bar-style">
          <n-form :data="state.formData" ref="formRef" label-width="110px" message-type="text">
            <template v-if="state.configPopupShowType === 'attr'">
              <n-form-item field="name" label="作业名称：">
                <n-input
                  v-model="state.formData.name"
                  :maxLength="500"
                  placeholder="请输入作业名称"
                />
              </n-form-item>

              <n-form-item field="taskType" label="作业类型：">
                <n-select v-model="state.formData.taskType" disabled :options="state.workOptions" />
              </n-form-item>

              <n-form-item field="personInChargeName" label="责任人：">
                <n-input
                  v-model="state.formData.personInChargeName"
                  disabled
                  placeholder="请输入责任人"
                />
              </n-form-item>
              <n-form-item
                v-if="state.formData.taskType === 'OW_SPARK_SQL'"
                field="driverCores"
                label="Driver核心数："
              >
                <n-select v-model="state.formData.driverCores" :options="state.coresOptions" />
              </n-form-item>
              <n-form-item
                v-if="state.formData.taskType === 'OW_SPARK_SQL'"
                field="driverMemory"
                label="Driver内存数："
              >
                <n-select v-model="state.formData.driverMemory" :options="state.memoryOptions" />
              </n-form-item>
              <n-form-item
                v-if="state.formData.taskType === 'OW_SPARK_SQL'"
                field="numExecutors"
                label="Executor数量："
              >
                <n-select v-model="state.formData.numExecutors" :options="state.ExecutorOptions" />
              </n-form-item>
              <n-form-item
                v-if="state.formData.taskType === 'OW_SPARK_SQL'"
                field="executorMemory"
                label="Executor内存数："
              >
                <n-select v-model="state.formData.executorMemory" :options="state.memoryOptions" />
              </n-form-item>
              <n-form-item
                v-if="state.formData.taskType === 'OW_SPARK_SQL'"
                field="executorCores"
                label="Executor核心数："
              >
                <n-select v-model="state.formData.executorCores" :options="state.coresOptions" />
              </n-form-item>

              <n-form-item field="description" label="描述信息：">
                <n-textarea
                  v-model="state.formData.description"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  resize="both"
                  maxlength="200"
                  placeholder="请输入描述信息"
                />
              </n-form-item>
            </template>
            <template v-if="state.configPopupShowType === 'schedule'">
              <n-form-item field="scheduleType" label="调度类型：">
                <n-select v-model="state.formData.scheduleType" :options="state.scheduleOptions" />
              </n-form-item>
              <div class="content-title">调度依赖</div>
              <n-form-item field="scheduleRangeType" label="范围：">
                <n-select
                  v-model="state.formData.scheduleRangeType"
                  :options="state.rangeOptions"
                />
              </n-form-item>
              <n-form-item field="prevTasks" label="上游作业：">
                <el-select
                  v-model="state.formData.prevTasks"
                  multiple
                  filterable
                  placeholder="请选择上游作业"
                  @change="
                    (val) => {
                      let list = state.taskOptions.filter((val) =>
                        state.formData.prevTasks.some((v) => v === val.value),
                      )
                      selectTaskFn(list)
                    }
                  "
                >
                  <el-option
                    v-for="item in state.taskOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </n-form-item>
              <div class="content-btn">
                <div class="btn active" @click.prevent.stop="initGraph">
                  <SvgIcon class="icon" icon="icon-offline-visualization" />
                  可视化配置
                </div>
              </div>
              <n-public-table
                :isDisplayAction="true"
                :showPagination="false"
                :table-head-titles="state.tableWorkHeadTitles"
                :tableHeight="400"
                :tableData="state.tableWorkData"
                :actionWidth="80"
              >
                <template #editor="{ editor }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      variant="text"
                      @click.prevent="delWorkFn(editor.row)"
                      >删除</n-button
                    >
                  </div>
                </template>
              </n-public-table>
            </template>
            <template v-if="state.configPopupShowType === 'version'">
              <!--              <div class="content-btn">-->
              <!--                <div class="btn active">-->
              <!--                  <SvgIcon class="icon" icon="icon-offline-contrast" />-->
              <!--                  对比-->
              <!--                </div>-->
              <!--              </div>-->
              <n-public-table
                :isDisplayAction="true"
                :showPagination="false"
                :table-head-titles="state.tableVersionHeadTitles"
                :tableHeight="580"
                :tableData="state.tableVersionData"
                :actionWidth="100"
              >
                <template #editor="{ editor }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      variant="text"
                      @click.prevent="detailFn(editor.row)"
                      >详情</n-button
                    >
                    <n-button
                      class="has-right-border"
                      variant="text"
                      @click.prevent="revertFn(editor.row)"
                      >回滚</n-button
                    >
                  </div>
                </template>
              </n-public-table>
            </template>
            <template v-if="state.configPopupShowType === 'refer'">
              <div class="content-btn">
                <div class="content-btn-title">ODS表参考<span>（点击表名查看字段）</span></div>
              </div>
              <div class="ods_data_query">
                <n-input
                  v-model="state.odsDataQuery"
                  placeholder="请输入关键词"
                  clearable
                  suffix="search"
                  @clear="state.modelList = state.modelListOld"
                  @input="
                    (val) => {
                      if (val) {
                        state.modelList = state.modelListOld.filter((item) => {
                          return item.name.indexOf(val) > -1 || item.cnName.indexOf(val) > -1
                        })
                      } else {
                        state.modelList = state.modelListOld
                      }
                    }
                  "
                />
                <el-select
                  v-model="state.datasourceId"
                  placeholder="请选择数据源"
                  clearable
                  filterable
                  style="width: 240px"
                  @change="getModelListFn()"
                >
                  <el-option
                    v-for="item in state.datasourceOpt"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
              <div class="refer-table scroll-bar-style">
                <template v-if="!state.showModelName">
                  <div
                    v-for="(item, index) in state.modelList"
                    :key="index"
                    class="label"
                    @click="showFieldFn(item)"
                  >
                    <div class="label-name">{{ item.database }}.{{ item.name }}</div>
                    <SvgIcon
                      class="icon"
                      icon="icon-new-copy"
                      @click.prevent.stop="copyFn(item.database + '.' + item.name)"
                    />
                  </div>
                </template>
                <template v-else>
                  <div class="refer-table-back" @click="state.showModelName = null">
                    <SvgIcon class="icon" icon="dataset-back" />返回
                  </div>
                  <div class="refer-table-name">表名：{{ state.showModelName }}</div>
                  <div v-for="(item, index) in state.fieldList" :key="index" class="label">
                    <div class="label-name field">{{ item.name }}</div>
                    <SvgIcon
                      class="icon"
                      icon="icon-new-copy"
                      @click.prevent.stop="copyFn(item.name)"
                    />
                  </div>
                </template>
              </div>
              <div class="content-btn">
                <div class="content-btn-title">已使用列表<span>（已使用的表）</span></div>
                <div class="btn active" @click.stop.prevent="analyzingFn"> SQL解析 </div>
              </div>
              <n-public-table
                :isDisplayAction="false"
                :showPagination="false"
                :table-head-titles="state.tableReferHeadTitles"
                :tableHeight="210"
                :tableData="state.tableReferData"
              />
            </template>
          </n-form>
        </div>
      </section>
    </div>
    <n-modal
      v-model="state.showVisualization"
      title="可视化配置"
      class="largeDialog has-top-padding"
      width="960px"
      :close-on-click-overlay="false"
      @close="closeDialog"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-flow" id="flowContainer"> </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showVisualization = false">取消</n-button>
          <n-button @click.prevent="saveVisualizationFn">确定</n-button>
        </div>
      </template>
    </n-modal>
    <n-modal
      v-model="state.showDetail"
      title="详情"
      class="largeDialog has-top-padding"
      width="560px"
      :close-on-click-overlay="false"
      @close="closeDialog"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-header">
          <div class="row">
            <div class="col">
              <div class="name">版本：</div>
              <div class="value">{{ state.versionInfo.versionText }}</div>
            </div>
            <div class="col">
              <div class="name">责任人：</div>
              <div class="value">{{ state.versionInfo.personInChargeName }}</div>
            </div>
            <div class="col">
              <div class="name">启动时间：</div>
              <div class="value">{{ state.versionInfo.createTime }}</div>
            </div>
          </div>
        </div>
        <div class="modal-body-content">
          <div class="modal-body-content-box scroll-bar-style">
            <n-textarea
              v-model="state.versionInfo.sql"
              placeholder=""
              :autosize="{ minRows: 3 }"
              readonly
            />
          </div>
          <div class="modal-body-content-btn">
            <div class="btn" @click.prevent="copyFn(state.versionInfo.sql)">
              <SvgIcon class="icon" icon="icon-new-copy" />复制
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showDetail = false">取消</n-button>
          <n-button @click.prevent="state.showDetail = false">确定</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import { defineEmits, reactive, watch } from 'vue'
  import api from '@/api/index'
  import { ElNotification, ElMessage } from 'element-plus'
  import useClipboard from 'vue-clipboard3'
  const { toClipboard } = useClipboard()
  import { Model } from '@antv/x6'
  import { DagreLayout } from '@antv/layout'
  import { timestampToTime } from '@/const/public.js'
  import FlowGraph from './../graph'
  import * as sqlFormatter from 'sql-formatter'
  import beautify from 'js-beautify'
  import { useDraggable } from '@vueuse/core'
  import {
    getODSModelList,
    checkSqlTable,
    parserSqlTable,
    taskSave,
    taskDetail,
    taskList,
    taskVersionList,
    getTaskDetailLatest,
    taskRun,
    taskStop,
    taskRunResult,
    taskRunResultPage,
    taskResult,
    taskResultV2,
    taskCommit,
    processTaskLogDownload,
    getDatasourceFeignListNew,
  } from '@/api/dataManage'
  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'
  // 拖拽高度
  const el = ref(null)
  const codeRunEl = ref(null)
  const textareaRef = ref(null)

  const { x, y, style } = useDraggable(el, {
    initialValue: { x: 0, y: 0 },
    onMove: (position) => {
      const codeHeight = window.innerHeight - 17 - position.y
      codeRunEl.value.style.height = `${Math.max(codeHeight, 56)}px`
      textareaRef.value.style['margin-bottom'] = `${Math.max(codeHeight, 56)}px`
      if (codeRunEl.value.offsetHeight < 60) {
        state.showRun = false
      } else {
        state.showRun = true
      }
    },
  })
  const myCm = ref('myCm')
  const formRef = ref('formRef')

  const emits = defineEmits(['openLabelFn', 'changeLoadingStatus'])

  const props = defineProps({
    checkTreeId: {
      type: [String, Number],
      default: null,
    },
    taskId: {
      type: [String, Number],
      default: null,
    },
  })

  let pageCodemirror = null
  const state = reactive({
    odsDataQuery: '',
    datasourceId: '',
    datasourceOpt: [],
    modelListOld: [],
    tagArr: [],
    loading: false,
    logLoading: false,
    resultLoading: false,
    hasResult: false,
    downLoading: false,
    showSubmit: false,
    showDetail: false,
    showVisualization: false,
    configPopupShow: false,
    showModelName: null,
    versionNum: 1,
    configPopupShowType: '',
    configPopupShowTypeTitle: '',
    runType: 'log',
    runLogText: '',
    runResultText: '',
    isRun: false,
    runCode: null,
    runTime: null,
    showRun: false,
    treeId: null,
    nodeId: null,
    nodeCode: null,
    processId: null,
    dsProcessCode: null,
    formData: {
      description: null,
      id: null,
      name: null,
      personInCharge: null,
      personInChargeName: null,
      taskType: null,
      driverCores: 1,
      driverMemory: '512M',
      numExecutors: 2,
      executorMemory: '512M',
      executorCores: 2,
      scheduleType: null,
      prevTasks: [],
      scheduleRangeType: 'CURRENT_PROCESS',
    },
    taskOptions: [],
    workOptions: [
      { name: 'PySpark', value: 'OW_PY_SPARK' },
      { name: 'HiveSQL', value: 'OW_HIVE_SQL' },
      { name: 'DorisSQL', value: 'OW_DORIS_SQL' },
      { name: 'SparkSQL', value: 'OW_SPARK_SQL' },
      { name: 'Python', value: 'OW_PYTHON' },
      { name: 'Shell', value: 'OW_SHELL' },
    ],
    memoryOptions: [
      { name: '512M', value: '512M' },
      { name: '1G', value: '1G' },
      { name: '2G', value: '2G' },
      { name: '4G', value: '4G' },
    ],
    coresOptions: [
      { name: '1核', value: 1 },
      { name: '2核', value: 2 },
      { name: '4核', value: 4 },
    ],
    ExecutorOptions: [
      { name: '1', value: 1 },
      { name: '2', value: 2 },
      { name: '4', value: 4 },
    ],
    scheduleOptions: [
      { name: '正常调度', value: 'DEFAULT' },
      { name: '暂停调度', value: 'PAUSE' },
      { name: '空跑', value: 'DRY_RUN' },
    ],
    rangeOptions: [
      { name: '当前工作流', value: 'CURRENT_PROCESS' },
      // { name: '当前项目', value: 'CURRENT_PROJECT' },
      // { name: '所有项目', value: 'TOTAL_PROJECT' },
    ],
    sql: '',
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
    // 调度配置
    tableWorkHeadTitles: [
      { name: '上游作业', prop: 'name' },
      { name: '上游作业ID', prop: 'value' },
    ],
    tableWorkData: {
      list: [],
    },
    // 版本管理
    tableVersionHeadTitles: [
      { name: '版本', prop: 'versionText' },
      // { name: '描述', prop: 'desc' },
      { name: '责任人', prop: 'personInChargeName', width: 160 },
      { name: '提交时间', prop: 'createTime', width: 160 },
    ],
    tableVersionData: {
      list: [],
    },
    versionInfo: {},
    tableReferHeadTitles: [{ name: '表名', prop: 'table' }],
    tableReferData: {
      list: [],
    },
    modelList: [],
    fieldList: [],
    tableResultHeadTitles: [],
    tableResultData: [],
    key: 1,
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
  })
  const curTag = ref(0)

  let runStatus = ref('waiting')

  watch(
    state,
    (newValue, oldValue) => {
      let obj = sessionStorage.getItem('workCacheData')
        ? JSON.parse(sessionStorage.getItem('workCacheData'))
        : {}
      // if (newValue?.codemirror) {
      //   delete newValue.codemirror
      // }

      obj[props.taskId] = newValue
      sessionStorage.setItem('workCacheData', JSON.stringify(obj))
    },
    { deep: true },
  )

  watch(
    runStatus,
    (newValue) => {
      emits('changeLoadingStatus', {
        taskId: props.taskId,
        runStatus: newValue,
      })
    },
    { deep: true },
  )

  // 弹窗
  const showConfigFn = (type, title) => {
    if (state.configPopupShowType === type && state.configPopupShow) {
      state.configPopupShow = false
    } else {
      state.configPopupShowType = type
      state.configPopupShowTypeTitle = title
      state.configPopupShow = true
      if (type === 'schedule') {
        getTaskListFn()
      } else if (type === 'version') {
        getVersionList()
      }
    }

    return false
  }
  const closeDialog = () => {
    state.showDetail = false
    state.showVisualization = false
    return false
  }
  const preventFn = () => {
    return false
  }
  // 下载日志
  const downLogFn = () => {
    if (state.runLogText) {
      state.runLogText = state.runLogText.replace(/<[^>]+>/g, '')
      const blob = new Blob([state.runLogText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        (state.formData.tableComment || state.formData.name) +
        timestampToTime(new Date().getTime(), 'MINUTE') +
        '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  // 获取已发布数据源
  const getPulishDatasource = () => {
    getDatasourceFeignListNew({ status: 'PUBLISHED' }).then((res) => {
      state.datasourceOpt = res.data
    })
  }
  // 下载
  const downFn = (type) => {
    state.downLoading = true
    const tag = state.tagArr[curTag.value]
    processTaskLogDownload(
      ['OW_HIVE_SQL', 'OW_SPARK_SQL', 'OW_DORIS_SQL'].includes(state.formData.taskType)
        ? {
            downloadType: type,
            // "maxShowResultSize": 0,
            nodeCode: state.nodeCode,
            sort: tag?.sort,
            sql: tag?.sql,
            sqlId: tag?.sqlId,
          }
        : {
            downloadType: type,
            dsProcessCode: state.dsProcessCode,
            nodeCode: state.nodeCode,
            executeTime: state.runTime,
          },
    )
      .then((res) => {
        state.downLoading = false
        try {
          // 下载文件
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
          })
          const link = document.createElement('a')
          if (type === 'EXCEL') {
            type = 'XLSX'
          }
          let fileName =
            state.formData.name +
            timestampToTime(new Date().getTime(), 'MINUTE') +
            '.' +
            type.toUpperCase().toLowerCase()
          link.download = fileName
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        } catch (e) {
          if (res.type === 'application/json') {
            // 说明是普通对象数据，读取信息
            const fileReader = new FileReader()
            fileReader.readAsText(res)
            fileReader.onloadend = () => {
              const jsonData = JSON.parse(fileReader.result)
              // 后台信息
              ElNotification({
                title: '提示',
                message: jsonData.message,
                type: 'error',
              })
            }
          }
        }
      })
      .catch(() => {
        state.downLoading = false
      })
  }
  // 展示运行结果
  const showRunFn = () => {
    state.showRun = !state.showRun
    if (state.showRun) {
      codeRunEl.value.offsetHeight < 60 && (codeRunEl.value.style.height = '400px')
    } else {
      codeRunEl.value.style.height = '56px'
    }
    textareaRef.value.style['margin-bottom'] = `${Math.max(codeRunEl.value.offsetHeight, 56)}px`
  }
  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.sql = newCode
  }

  // 获取选中的语句
  const getSelectedText = () => {
    let selectedText = ''
    if (document.selection && document.selection.type != 'Control') {
      selectedText = document.selection.createRange().text
    } else if (window.getSelection) {
      selectedText = window.getSelection().toString()
    }
    return selectedText
  }

  // 格式化
  const formatFn = () => {
    let language = 'sql'
    if (state.formData.taskType === 'OW_PY_SPARK') {
      language = 'spark'
    } else if (state.formData.taskType === 'OW_HIVE_SQL') {
      language = 'hive'
    } else if (
      state.formData.taskType === 'OW_SPARK_SQL' ||
      state.formData.taskType === 'OW_DORIS_SQL'
    ) {
      language = 'sql'
    } else if (state.formData.taskType === 'OW_PYTHON') {
      resetLint()
      return false
    } else if (state.formData.taskType === 'OW_SHELL') {
      resetLint()
      return false
    }
    state.sql = sqlFormatter.format(state.sql, { language })
  }
  // python和shell格式化
  const resetLint = () => {
    let code = beautify(state.sql, {
      indent_size: 2,
      space_in_empty_paren: true,
      e4x: true,
      css: {
        indent_size: 1,
      },
    })
    state.sql = code
  }
  // 删除上游作业
  const delWorkFn = (item) => {
    state.formData.prevTasks = state.formData.prevTasks.filter((val) => val !== item.value)
    state.tableWorkData.list = state.tableWorkData.list.filter((val) => val.value !== item.value)
  }
  // 版本详情
  const detailFn = (item) => {
    state.versionInfo = item
    state.showDetail = true
  }
  // 回滚
  const revertFn = (item) => {
    state.sql = item.sql
  }
  // 获取ods表
  const getModelListFn = () => {
    getODSModelList({ datasourceId: state.datasourceId }).then((res) => {
      if (res.success) {
        state.modelList = res.data
        state.modelListOld = res.data
      }
    })
  }
  // 获取字段
  const showFieldFn = (item) => {
    state.showModelName = item.name
    api.model.getModeData({ id: item.id }).then((res) => {
      if (res.success) {
        state.fieldList = res.data
      }
    })
  }
  // 检查代码
  const checkSqlFn = () => {
    if (!state.sql) {
      ElNotification({
        title: '提示',
        message: '请先填写sql语句',
        type: 'warning',
      })
      return false
    }
    if (state.formData.taskType === 'OW_HIVE_SQL' || state.formData.taskType === 'OW_SPARK_SQL') {
      checkSqlTable({ sql: state.sql }).then((res) => {
        if (res.success) {
          if (res.data.errorMsg.length > 0) {
            let msg = res.data.errorMsg.toString()
            ElNotification({
              title: '提示',
              message: msg,
              type: 'warning',
            })
          } else {
            ElNotification({
              title: '提示',
              message: '校验通过',
              type: 'success',
            })
          }
        }
      })
    } else {
      ElNotification({
        title: '提示',
        message: '校验通过',
        type: 'success',
      })
    }
  }
  // 解析sql
  const analyzingFn = () => {
    parserSqlTable({ sql: state.sql }).then((res) => {
      if (res.success) {
        state.tableReferData.list = res.data
      }
    })
  }
  // 复制
  const copyFn = async (text) => {
    try {
      toClipboard(text)
      ElNotification({
        title: '提示',
        message: '复制成功',
        type: 'success',
      })
    } catch (e) {
      console.error(e)
    }
  }
  // SQL语句获取焦点时
  const onCmFocus = (cm) => {}
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    pageCodemirror = cm
    pageCodemirror.setSize('-webkit-fill-available', 'auto')
    pageCodemirror.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      pageCodemirror.showHint(config)
    })
  }

  const activeTabChange = (i) => {
    curTag.value = parseInt(i?.split('_')?.[1] || 0)
    if (!state.resultLoading) {
      const tag = state.tagArr[curTag.value]
      const taskResultApi =
        state.formData.taskType === 'OW_HIVE_SQL' ||
        state.formData.taskType === 'OW_SPARK_SQL' ||
        state.formData.taskType === 'OW_DORIS_SQL'
          ? taskResultV2
          : taskResult
      tag?.loading &&
        taskResultApi({
          condition: {
            nodeCode: state.nodeCode,
            sort: tag?.sort,
            sql: tag?.sql,
            sqlId: tag?.sqlId,
          },
          pageNum: 0,
          pageSize: 0,
          sortConditions: [
            {
              fieldName: '',
              sort: '',
            },
          ],
        })
          .then((res) => {
            if (res.success) {
              tag.tableResultHeadTitles = (
                res?.data?.columns || Object.keys(res?.data?.[0] || {})
              )?.map((val) => ({
                name: val,
                prop: val,
              }))
              tag.tableResultData = res?.data?.datas || res?.data || []
            }
          })
          .finally(() => {
            tag.loading = false
          })
    }
  }
  // 运行
  const runFn = async () => {
    let data = {
      id: state.nodeId || null,
      scheduleType: state.formData.scheduleType || null,
      taskType: state.formData.taskType || null,
      nodeCode: state.nodeCode || null,
      nodeName: state.formData.name || null,
      executeTime: timestampToTime(new Date().getTime(), true),
    }
    let sqlStr = state.sql
    if (getSelectedText()) {
      sqlStr = getSelectedText()
    }
    if (!sqlStr) {
      ElNotification({
        title: '提示',
        message: '请添加运行语句！',
        type: 'warning',
      })
      return false
    }

    // 立即存储保存状态的session，防止用户在保存过程中刷新页面
    let saveSession = {
      runStatus: 'SAVING',
      runType: 'log',
      nodeId: state.nodeId,
      formData: state.formData,
      sql: sqlStr,
      executeTime: data.executeTime,
      taskType: state.formData.taskType,
    }
    sessionStorage.setItem('pollingSession_' + state.nodeId, JSON.stringify(saveSession))

    state.runType = 'log'
    state.showRun = true
    state.logLoading = true
    runStatus.value = 'running'
    state.runLogText = ''

    try {
      await saveFn(true)

      if (state.formData.taskType === 'OW_PY_SPARK') {
        data.pySparkTaskBO = {
          pythonScript: sqlStr,
        }
      } else if (state.formData.taskType === 'OW_HIVE_SQL') {
        data.hiveSqlTaskBO = {
          sqlScript: sqlStr,
        }
      } else if (state.formData.taskType === 'OW_DORIS_SQL') {
        data.dorisSqlTaskBO = {
          sqlScript: sqlStr,
        }
      } else if (state.formData.taskType === 'OW_SPARK_SQL') {
        data.sparkSqlTaskBO = {
          sqlScript: sqlStr,
          driverCores: state.formData.driverCores,
          driverMemory: state.formData.driverMemory,
          numExecutors: state.formData.numExecutors,
          executorCores: state.formData.executorCores,
          executorMemory: state.formData.executorMemory,
        }
      } else if (state.formData.taskType === 'OW_PYTHON') {
        data.pythonTaskBO = {
          pythonScript: sqlStr,
        }
      } else if (state.formData.taskType === 'OW_SHELL') {
        data.shellTaskBO = {
          shellScript: sqlStr,
        }
      }

      state.loading = true
      state.logLoading = true
      state.resultLoading = true
      state.isRun = true
      state.showRun = true
      state.hasResult = false

      taskRun(data)
        .then((res) => {
          // 保存成功后清除保存session
          sessionStorage.removeItem('pollingSession_' + state.nodeId)
          if (res.success && state.isRun) {
            state.dsProcessCode = res.data.dsProcessCode || null
            state.runCode = res.data.runId || null
            state.runTime = res.data.runTime || null
            const sqlList = res?.data?.querySqlCutList || []
            let tagArr = []
            if (['OW_HIVE_SQL', 'OW_SPARK_SQL', 'OW_DORIS_SQL'].includes(state.formData.taskType)) {
              if (sqlList.length + state.tagArr?.length > 10) {
                ElMessage.error('运行结果上限10个，请关闭运行结果后再执行运行')
                stopFn(true)
                return
              } else {
                sqlList?.forEach(({ sql, sort, sqlId }) => {
                  const tag = {
                    i: state.tagArr.length,
                    query: data,
                    sql,
                    sqlId,
                    pageSize: 10,
                    currentPage: 1,
                    sort,
                    loading: true,
                  }
                  tagArr.push(tag)
                })
              }
            }
            if (state.timer) {
              clearTimeout(state.timer)
              state.timer = null
            }
            state.timer = setTimeout(() => {
              getResultFn('LOG', tagArr)
            }, 1000)
          } else {
            state.loading = false
            state.logLoading = false
            state.resultLoading = false
            state.isRun = false
            runStatus.value = 'fail'
          }
        })
        .catch(() => {
          state.loading = false
          state.logLoading = false
          state.resultLoading = false
          state.isRun = false
          runStatus.value = 'fail'
          // 运行失败时清除session
          sessionStorage.removeItem('pollingSession_' + state.nodeId)
        })
    } catch (error) {
      // 保存失败时清除session
      sessionStorage.removeItem('pollingSession_' + state.nodeId)
      state.loading = false
      state.logLoading = false
      state.resultLoading = false
      state.isRun = false
      runStatus.value = 'fail'
    }
  }
  // 停止运行
  const stopFn = (flag = true) => {
    taskStop({ id: state.nodeId })
      .then((res) => {
        if (res.success) {
          if (flag) {
            ElNotification({
              title: '提示',
              message: '停止运行成功',
              type: 'success',
            })
          }
        }
        if (state.timer) {
          clearTimeout(state.timer)
          state.timer = null
        }
        state.loading = false
        state.logLoading = false
        state.resultLoading = false
        state.isRun = false
        // 停止时清除session
        sessionStorage.removeItem('pollingSession_' + state.nodeId)
      })
      .catch(() => {
        state.loading = false
        state.logLoading = false
        state.resultLoading = false
        state.isRun = false
        // 停止失败时也清除session
        sessionStorage.removeItem('pollingSession_' + state.nodeId)
      })
  }
  // 获取运行结果
  const getResultFn = (runResultType, tagArr = []) => {
    if (
      state.formData.taskType === 'OW_HIVE_SQL' ||
      state.formData.taskType === 'OW_SPARK_SQL' ||
      state.formData.taskType === 'OW_DORIS_SQL'
    ) {
      taskRunResultPage({
        condition: {
          downloadType: null,
          dsProcessCode: state.dsProcessCode,
          nodeCode: state.nodeCode,
          executeTime: state.runTime,
          runResultType: runResultType || 'LOG',
        },
        pageNum: state.pageInfo.currentPage,
        pageSize: state.pageInfo.pageSize,
      })
        .then((res) => {
          if (res.data.state === 'SUCCESS' || res.data.state === 'FAIL') {
            sessionStorage.removeItem('pollingSession_' + state.nodeId)
          }
          if (res.success) {
            state.runLogText = res.data?.log?.replace(/\t/g, '  ')
            state.logLoading = false

            if (res.data.state === 'RUNNING_EXECUTION') {
              if (state.timer) {
                clearTimeout(state.timer)
                state.timer = null
              }
              runStatus.value = 'running'
              state.timer = setTimeout(() => {
                getResultFn(runResultType, tagArr)
              }, 3000)
              // 存储轮询状态
              let pollingSession = {
                runStatus: 'RUNNING_EXECUTION',
                runType: state.runType,
                dsProcessCode: state.dsProcessCode,
                nodeCode: state.nodeCode,
                runTime: state.runTime,
                formData: state.formData,
                tagArr: state.tagArr,
                // 你可以根据需要存更多信息
              }
              sessionStorage.setItem(
                'pollingSession_' + state.nodeId,
                JSON.stringify(pollingSession),
              )
            } else if (res.data.state === 'SUCCESS') {
              if (runResultType === 'RESULT') {
                state.runLogText += '<div class="green">运行成功</div>'
                state.isRun = false
                state.loading = false
                state.resultLoading = false
                state.hasResult = true
                runStatus.value = 'success'
                state.tagArr = state.tagArr.concat(tagArr)
                state.runType = 'result_' + (state.tagArr.length - 1)
                activeTabChange(state.runType)
                let tableResultData = []
                if (res.data.resultJson) {
                  tableResultData = JSON.parse(res.data.resultJson)
                  if (tableResultData.length > 0) {
                    let tableResultHeadTitles = []
                    Object.keys(tableResultData[0]).forEach((val) => {
                      tableResultHeadTitles.push({
                        name: val,
                        prop: val,
                      })
                    })
                    state.tableResultHeadTitles = tableResultHeadTitles
                  }
                }
                curTag.value && activeTabChange('result_' + curTag.value)
                setTimeout(() => {
                  state.tableResultData = tableResultData
                  state.key++
                }, 100)
                ElNotification({
                  title: '提示',
                  message: '运行成功',
                  type: 'success',
                })
              } else {
                getResultFn('RESULT', tagArr)
              }
            } else {
              state.runLogText += '<div class="red">运行失败</div>'
              state.isRun = false
              state.loading = false
              state.resultLoading = false
              runStatus.value = 'fail'
              state.tableResultData = []
              ElNotification({
                title: '提示',
                message: '运行失败',
                type: 'error',
              })
            }
          } else {
            state.isRun = false
            state.loading = false
            state.logLoading = false
            state.resultLoading = false
            runStatus.value = 'fail'
          }
        })
        .then(() =>
          setTimeout(() => {
            scrollToLatest()
          }, 1000),
        )
        .catch(() => {
          state.isRun = false
          state.loading = false
          runStatus.value = 'fail'
        })
    } else {
      taskRunResult({
        dsProcessCode: state.dsProcessCode,
        runId: state.runCode,
        nodeCode: state.nodeCode,
        executeTime: state.runTime,
      })
        .then((res) => {
          if (res.data.state === 'SUCCESS' || res.data.state === 'FAIL') {
            sessionStorage.removeItem('pollingSession_' + state.nodeId)
          }

          if (res.success) {
            // state.runLogText = new Array(parseInt(Math.random() * 5000))
            //   .fill(() => {
            //     return 'log'
            //   })
            //   .join('\n')

            state.runLogText = res.data?.log?.replace(/\t/g, '  ')
            if (state.runLogText) {
              state.logLoading = false
            }
            if (res.data.state === 'RUNNING_EXECUTION') {
              if (state.timer) {
                clearTimeout(state.timer)
                state.timer = null
              }
              runStatus.value = 'running'
              state.timer = setTimeout(() => {
                getResultFn(undefined, tagArr)
              }, 3000)
              // 存储轮询状态
              let pollingSession = {
                runStatus: 'RUNNING_EXECUTION',
                runType: state.runType,
                dsProcessCode: state.dsProcessCode,
                nodeCode: state.nodeCode,
                runTime: state.runTime,
                formData: state.formData,
                tagArr: state.tagArr,
                // 你可以根据需要存更多信息
              }
              sessionStorage.setItem(
                'pollingSession_' + state.nodeId,
                JSON.stringify(pollingSession),
              )
            } else if (res.data.state === 'SUCCESS') {
              state.runLogText += '<div class="green">运行成功</div>'
              state.isRun = false
              state.loading = false
              state.resultLoading = false
              state.logLoading = false
              runStatus.value = 'success'
              state.runResultText = res.data?.result?.replace(/\t/g, '  ')
              ElNotification({
                title: '提示',
                message: '运行成功',
                type: 'success',
              })
              state.runType = 'result'
              activeTabChange(state.runType)
            } else {
              state.runLogText += '<div class="red">运行失败</div>'
              state.isRun = false
              state.loading = false
              state.resultLoading = false
              state.logLoading = false
              runStatus.value = 'fail'
              ElNotification({
                title: '提示',
                message: '运行失败',
                type: 'error',
              })
            }
          } else {
            state.runLogText += '<div class="red">运行失败</div>'
            state.isRun = false
            state.loading = false
            state.logLoading = false
            state.resultLoading = false
            runStatus.value = 'fail'
          }
        })
        .then(() =>
          setTimeout(() => {
            scrollToLatest()
          }, 1000),
        )
        .catch(() => {
          state.isRun = false
          state.loading = false
          runStatus.value = 'fail'
        })
    }
  }
  // 选择上游作业
  const selectTaskFn = (e) => {
    state.tableWorkData.list = e
  }
  // 保存
  const saveFn = async (isRun = false) => {
    let data = {
      id: state.nodeId || null,
      basicInfoBO: {
        id: state.nodeId || null,
        name: state.formData.name || null,
        description: state.formData.description || null,
        personInCharge: state.formData.personInCharge || null,
      },
      configInfoBO: {
        id: state.nodeId || null,
        nodeName: state.formData.name || null,
        nodeCode: state.nodeCode || null,
        taskType: state.formData.taskType || null,
        scheduleType: state.formData.scheduleType || null,
        pySparkTaskBO: { pythonScript: null },
        hiveSqlTaskBO: { sqlScript: null },
        dorisSqlTaskBO: { sqlScript: null },
        sparkSqlTaskBO: { sqlScript: null },
        pythonTaskBO: { pythonScript: null },
        shellTaskBO: { shellScript: null },
      },
      scheduleInfoBO: {
        id: state.nodeId || null,
        scheduleType: state.formData.scheduleType || null,
        prevTasks: state.formData.prevTasks || [],
        location: {
          nodeCode: state.nodeCode || null,
        },
        scheduleRangeType: state.formData.scheduleRangeType || 'CURRENT_PROCESS',
      },
    }
    if (state.formData.prevTasks.length > 0) {
      data.scheduleInfoBO.prevTasksInfo = []
      state.tableWorkData.list.forEach((val) => {
        data.scheduleInfoBO.prevTasksInfo.push({
          nodeCode: val.value,
        })
      })
    }
    if (state.formData.taskType === 'OW_PY_SPARK') {
      data.configInfoBO.pySparkTaskBO.pythonScript = state.sql
    } else if (state.formData.taskType === 'OW_HIVE_SQL') {
      data.configInfoBO.hiveSqlTaskBO.sqlScript = state.sql
    } else if (state.formData.taskType === 'OW_DORIS_SQL') {
      data.configInfoBO.dorisSqlTaskBO.sqlScript = state.sql
    } else if (state.formData.taskType === 'OW_SPARK_SQL') {
      data.configInfoBO.sparkSqlTaskBO.sqlScript = state.sql
      data.configInfoBO.sparkSqlTaskBO.driverCores = state.formData.driverCores
      data.configInfoBO.sparkSqlTaskBO.driverMemory = state.formData.driverMemory
      data.configInfoBO.sparkSqlTaskBO.numExecutors = state.formData.numExecutors
      data.configInfoBO.sparkSqlTaskBO.executorCores = state.formData.executorCores
      data.configInfoBO.sparkSqlTaskBO.executorMemory = state.formData.executorMemory
    } else if (state.formData.taskType === 'OW_PYTHON') {
      data.configInfoBO.pythonTaskBO.pythonScript = state.sql
    } else if (state.formData.taskType === 'OW_SHELL') {
      data.configInfoBO.shellTaskBO.shellScript = state.sql
    }
    state.loading = true
    await taskSave(data)
      .then((res) => {
        if (res.success) {
          if (!isRun) {
            emits('openLabelFn', {
              id: state.treeId,
              name: state.formData.name || getNodeByType(state.formData.taskType),
            })
            ElNotification({
              title: '提示',
              message: '保存成功',
              type: 'success',
            })
          }
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
        state.logLoading = false
        state.showRun = false
        if (isRun) {
          runStatus.value = 'fail'
        }
      })
  }
  // 提交
  const submitFn = async () => {
    await saveFn(true)
    state.loading = true
    taskCommit({ id: state.nodeId })
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.versionNum = res.data
          state.showSubmit = true
          getVersionList()
          setTimeout(() => {
            state.showSubmit = false
          }, 3000)
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 获取节点详情
  const getDetailFn = () => {
    taskDetail({ treeId: state.treeId, id: state.nodeId }).then((res) => {
      if (res.success) {
        state.formData.name = res.data.name || null
        state.formData.taskType = res.data.taskType || null
        state.formData.description = res.data.description || null
        state.formData.personInCharge = res.data.personInCharge || null
        state.formData.personInChargeName = res.data.personInChargeName || null
        state.formData.scheduleType = res.data.scheduleType || null
        state.formData.prevTasks = res.data.prevTasks || []
        state.nodeCode = res.data.nodeCode || null
        state.processId = res.data.processId || null
        state.dsProcessCode = res.data.dsProcessCode || null
        getTaskDetailLatestFn()
      }
    })
  }
  // 获取上游作业列表
  const getTaskListFn = () => {
    taskList({ processId: state.processId, excludeDDL: true }).then((res) => {
      if (res.success) {
        state.taskOptions = res.data
          .filter((item) => item.nodeCode !== state.nodeCode)
          .map((val) => {
            return { ...val, value: val.nodeCode }
          })
        let list = state.taskOptions.filter((val) =>
          state.formData.prevTasks.some((code) => code === val.value),
        )
        selectTaskFn(list)
      }
    })
  }
  // 获取版本列表
  const getVersionList = () => {
    taskVersionList({ taskId: state.nodeId }).then((res) => {
      if (res.success) {
        res.data.forEach((val) => {
          if (val.taskType === 'OW_PY_SPARK') {
            val.sql = val.pySparkTaskBO.pythonScript
          }
          if (val.taskType === 'OW_HIVE_SQL') {
            val.sql = val.hiveSqlTaskBO.sqlScript
          }
          if (val.taskType === 'OW_DORIS_SQL') {
            val.sql = val.dorisSqlTaskBO.sqlScript
          }
          if (val.taskType === 'OW_SPARK_SQL') {
            val.sql = val.sparkSqlTaskBO.sqlScript
          }
          if (val.taskType === 'OW_PYTHON') {
            val.sql = val.pythonTaskBO.pythonScript
          }
          if (val.taskType === 'OW_SHELL') {
            val.sql = val.shellTaskBO.shellScript
          }
          val.versionText = 'V' + val.version
        })
        state.tableVersionData.list = res.data
      }
    })
  }
  // 获取最新暂存信息
  const getTaskDetailLatestFn = () => {
    getTaskDetailLatest({ taskId: state.nodeId }).then((res) => {
      if (res.success) {
        if (state.formData.taskType === 'OW_PY_SPARK') {
          state.sql = res.data?.pySparkTaskBO?.pythonScript || ''
          state.sqlOption.mode = 'text/x-sparksql'
        } else if (state.formData.taskType === 'OW_HIVE_SQL') {
          state.sql = res.data?.hiveSqlTaskBO?.sqlScript || ''
          state.sqlOption.mode = 'text/x-hive'
        } else if (state.formData.taskType === 'OW_DORIS_SQL') {
          state.sql = res.data?.dorisSqlTaskBO?.sqlScript || ''
          state.sqlOption.mode = 'text/x-mysql'
        } else if (state.formData.taskType === 'OW_SPARK_SQL') {
          state.sql = res.data?.sparkSqlTaskBO?.sqlScript || ''
          state.formData.driverCores = res.data?.sparkSqlTaskBO?.driverCores || 1
          state.formData.driverMemory = res.data?.sparkSqlTaskBO?.driverMemory || '512M'
          state.formData.numExecutors = res.data?.sparkSqlTaskBO?.numExecutors || 2
          state.formData.executorCores = res.data?.sparkSqlTaskBO?.executorCores || 2
          state.formData.executorMemory = res.data?.sparkSqlTaskBO?.executorMemory || '512M'
          state.sqlOption.mode = 'text/x-sparksql'
        } else if (state.formData.taskType === 'OW_PYTHON') {
          state.sql = res.data?.pythonTaskBO?.pythonScript || ''
          state.sqlOption.mode = 'text/x-python'
        } else if (state.formData.taskType === 'OW_SHELL') {
          state.sql = res.data?.shellTaskBO?.shellScript || ''
          state.sqlOption.mode = 'text/x-sh'
        }
        getTaskListFn()
      }
    })
  }
  // 初始化画布
  const initGraph = () => {
    state.showVisualization = true
    nextTick(() => {
      const graph = FlowGraph.init()
      let data = (Model.FromJSONData = {
        nodes: [],
        edges: [],
      })
      state.tableWorkData.list.forEach((item) => {
        data.edges.push({
          shape: 'edge',
          data: { ...item },
          source: { cell: String(item.value), port: 'bottom' },
          target: { cell: String(state.nodeCode), port: 'top' },
          attrs: {
            line: {
              stroke: '#8091B7',
              strokeWidth: 1,
              targetMarker: {
                name: 'classic',
                size: 8,
              },
            },
          },
          connector: 'normal',
          // router: {
          //   name: 'normal',
          // },
          zIndex: 0,
        })
      })
      state.tableWorkData.list.forEach((item) => {
        item.id = String(item.value)
        data.nodes.push({
          id: item.id,
          data: {
            ...item,
            isVisualization: true,
          },
          size: { width: 256, height: 44 },
          shape: getNodeByType(item.taskType),
          ports: {
            groups: {
              top: {
                id: 'top',
                position: 'top',
                attrs: {
                  circle: {
                    r: 3,
                    magnet: true,
                    stroke: '#8091B7',
                    strokeWidth: 1,
                    fill: '#fff',
                    style: {
                      visibility: 'hidden',
                    },
                  },
                },
              },
              bottom: {
                id: 'bottom',
                position: 'bottom',
                attrs: {
                  circle: {
                    r: 3,
                    magnet: true,
                    stroke: '#8091B7',
                    strokeWidth: 1,
                    fill: '#fff',
                    style: {
                      visibility: 'hidden',
                    },
                  },
                },
              },
            },
            items: [
              {
                id: 'top',
                group: 'top',
              },
              {
                id: 'bottom',
                group: 'bottom',
              },
            ],
          },
          zIndex: 1,
        })
      })
      data.nodes.push({
        id: String(state.nodeCode),
        data: {
          id: state.nodeId,
          value: state.nodeCode,
          name: state.formData.name,
          taskType: state.formData.taskType,
          processId: state.processId,
          isVisualization: true,
        },
        size: { width: 256, height: 44 },
        shape: getNodeByType(state.formData.taskType),
        ports: {
          groups: {
            top: {
              id: 'top',
              position: 'top',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#8091B7',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
            bottom: {
              id: 'bottom',
              position: 'bottom',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#8091B7',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: {
                    visibility: 'hidden',
                  },
                },
              },
            },
          },
          items: [
            {
              id: 'top',
              group: 'top',
            },
            {
              id: 'bottom',
              group: 'bottom',
            },
          ],
        },
        zIndex: 1,
      })
      const dagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: 'TB',
        align: undefined,
        ranksep: 20,
        nodesep: 20,
        controlPoints: true,
      })
      const model = dagreLayout.layout(data)
      graph.fromJSON(model)
      if (data.nodes.length > 3) {
        graph.scale(0.6)
      } else {
        graph.scale(0.8)
      }

      graph.centerContent()
    })
  }
  // 保存可视化
  const saveVisualizationFn = () => {
    const { graph } = FlowGraph
    let json = graph.toJSON()
    let nodes = json.cells.filter((val) => val.shape !== 'edge')
    let edges = json.cells.filter((val) => val.shape === 'edge')
    let list = nodes
      .filter((val) => edges.some((item) => item.source.cell === val.id))
      .map((v) => {
        return {
          ...v.data,
        }
      })
    state.formData.prevTasks = list.map((val) => Number(val.id))
    selectTaskFn(list)
    state.showVisualization = false
  }

  // 通过作业类型判断节点
  const getNodeByType = (jobType) => {
    if (jobType === 'OW_DATA_ASYNC') {
      return 'dataAsync'
    } else if (jobType === 'OW_PY_SPARK') {
      return 'PySpark'
    } else if (jobType === 'OW_HIVE_SQL') {
      return 'HiveSQL'
    } else if (jobType === 'OW_DORIS_SQL') {
      return 'DorisSQL'
    } else if (jobType === 'OW_SPARK_SQL') {
      return 'SparkSQL'
    } else if (jobType === 'OW_PYTHON') {
      return 'Python'
    } else if (jobType === 'OW_SHELL') {
      return 'Shell'
    } else if (jobType === 'OW_HIVE_DDL') {
      return 'HiveDDL'
    }
  }
  const logContent = ref(null)
  function scrollToLatest() {
    if (logContent.value) {
      logContent.value.scrollTo({
        top: logContent.value.scrollHeight,
        behavior: 'smooth',
      })
    }
  }

  onMounted(() => {
    state.treeId = props.checkTreeId
    state.nodeId = props.taskId
    getModelListFn()
    getPulishDatasource()

    if (
      JSON.parse(sessionStorage.getItem('workCacheData')) &&
      Object.keys(JSON.parse(sessionStorage.getItem('workCacheData'))).indexOf(
        JSON.stringify(props.taskId),
      ) > -1
    ) {
      let {
        loading,
        showSubmit,
        showDetail,
        showVisualization,
        configPopupShow,
        showModelName,
        versionNum,
        configPopupShowType,
        configPopupShowTypeTitle,
        runType,
        runLogText,
        runResultText,
        isRun,
        runCode,
        runTime,
        showRun,
        treeId,
        nodeId,
        nodeCode,
        processId,
        dsProcessCode,
        formData,
        taskOptions,
        sql,
        tableWorkData,
        tableVersionData,
        versionInfo,
        tableReferHeadTitles,
        tableReferData,
        modelList,
        fieldList,
      } = JSON.parse(sessionStorage.getItem('workCacheData'))[props.taskId]

      state.loading = false
      state.logLoading = false
      state.showSubmit = showSubmit
      state.showDetail = showDetail
      state.showVisualization = showVisualization
      state.configPopupShow = configPopupShow
      state.showModelName = showModelName
      state.versionNum = versionNum
      state.configPopupShowType = configPopupShowType
      state.configPopupShowTypeTitle = configPopupShowTypeTitle
      state.runType = runType
      state.runLogText = runLogText
      state.runResultText = runResultText
      state.isRun = false
      state.runCode = runCode
      state.runTime = runTime
      state.showRun = showRun
      state.treeId = treeId
      state.nodeId = nodeId
      state.nodeCode = nodeCode
      state.processId = processId
      state.dsProcessCode = dsProcessCode
      state.formData = formData
      state.taskOptions = taskOptions
      state.sql = sql
      state.tableWorkData = tableWorkData
      state.tableVersionData = tableVersionData
      state.versionInfo = versionInfo
      state.tableReferHeadTitles = tableReferHeadTitles
      state.tableReferData = tableReferData
      state.modelList = modelList
      state.fieldList = fieldList
    } else {
      getDetailFn()
    }

    // 检查是否有未完成的轮询或保存状态
    let pollingSession = sessionStorage.getItem('pollingSession_' + props.taskId)
    if (pollingSession) {
      let sessionData = JSON.parse(pollingSession)
      if (sessionData.runStatus === 'SAVING') {
        // 恢复保存状态，继续执行保存和运行逻辑
        state.runType = sessionData.runType
        state.formData = sessionData.formData
        state.sql = sessionData.sql
        state.showRun = true
        state.logLoading = true
        runStatus.value = 'running'
        state.runLogText = ''

        // 继续执行保存和运行逻辑
        runFn(sessionData)
      } else if (sessionData.runStatus === 'RUNNING_EXECUTION') {
        // 恢复必要的 state
        state.runType = sessionData.runType
        state.dsProcessCode = sessionData.dsProcessCode
        state.nodeCode = sessionData.nodeCode
        state.runTime = sessionData.runTime
        state.formData = sessionData.formData
        state.tagArr = sessionData.tagArr
        // 继续轮询
        getResultFn('LOG', state.tagArr)
      }
    }
  })
  onBeforeUnmount(() => {
    if (state.nodeId) {
      state.loading = false
      state.logLoading = false
      state.isRun = false
      stopFn(false)
    }
  })
  defineExpose({
    state,
  })
</script>
<style>
  ul.CodeMirror-hints {
    z-index: 2071;
  }
</style>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .work-body {
    position: relative;
    width: 100%;
    height: calc(100% - 47px);

    &-box {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    &-btn {
      position: relative;
      display: flex;
      flex-shrink: 0;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 14px 8px;
      border-bottom: 1px solid var(---, #dcdfe6);

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        box-sizing: border-box;
        padding: 4px 16px;
        color: #1e89ff;
        font-size: 14px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;

        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }
      }

      .submit {
        position: absolute;
        top: 0;
        right: 48px;
        bottom: 0;
        box-sizing: border-box;
        width: 58px;
        height: 20px;
        margin: auto;
        color: #00c700;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background: #f4ffeb;
        border: 1px solid #a8ed83;
        border-radius: 2px;
      }
    }

    &-code {
      position: relative;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 46px);
      padding-bottom: 1px;
      color: #1f2329;
      background: #f5f6f7;
      border: none;

      &-config {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        align-self: stretch;
        width: 48px;
        height: 100%;
        padding: 12px 8px;
        background: #fafafa;
        border-left: 1px solid #dcdfe6;

        &-btn {
          display: flex;
          gap: 8px;
          align-items: center;
          justify-content: center;
          width: 32px;
          padding: 12px 4px;
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: center;
          background: #fff;
          border: 1px solid #c9cdd4;
          border-radius: 2px;
          cursor: pointer;
        }
      }

      &-textarea {
        flex: 1;
        width: calc(100% - 48px);
        margin-bottom: 0;
        overflow: hidden;
        .codemirror {
          height: 100% !important;
          background-color: #fff;
          &:has(textarea[readOnly]) {
            :deep(.CodeMirror-cursors) {
              display: none;
            }
          }

          :deep(.CodeMirror) {
            height: 100% !important;
            overflow: hidden;
            box-shadow: none;
          }

          :deep(.CodeMirror-scroll) {
            box-sizing: border-box;
            height: 100%;
            margin-right: -6px;
            padding: 0;
            overflow-x: hidden !important;
            overflow-y: auto !important;

            .CodeMirror-sizer {
              border-right: none;

              .CodeMirror-lines {
                padding: 0;
                //.CodeMirror-selected {
                //  height: 28px !important;
                //}
              }

              .CodeMirror-cursors {
                top: 5px;
              }

              .CodeMirror-code > div {
                padding: 2px 0;
              }

              .CodeMirror-linenumber {
                padding: 0 6px;
                text-align: center;
              }

              .CodeMirror-line {
                padding: 0 10px;

                > span {
                  padding-right: 10px !important;
                  color: #046c5c;
                  font-size: 14px;
                  word-break: break-all;
                }
              }

              .CodeMirror-linebackground {
                background-color: #f0f2f5;
              }
            }
          }

          :deep(.CodeMirror-gutters) {
            width: 32px;
            min-height: 100%;
            background-color: #e6e8eb;
            border-right: none;
          }

          :deep(.CodeMirror-vscrollbar) {
            visibility: initial !important;

            &::-webkit-scrollbar-thumb {
              background-color: #b1bcd6;
              border-radius: 6px;

              &:hover {
                background-color: #b1bcd6;
              }
            }
          }
        }

        :deep(.el-loading-mask) {
          .el-loading-spinner {
            height: 350px;

            .circular {
              width: 32px !important;
              height: 32px !important;
            }
          }
        }
      }

      &-run {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2001;
        box-sizing: border-box;
        width: calc(100% - 48px);
        height: 0;
        max-height: calc(100% - 20px);
        overflow: hidden;
        background-color: #fff;
        &.showRun {
          height: 400px;
        }

        .drag-line {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          z-index: 2002;
        }

        &-tabs {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          padding: 0 16px;
          padding-top: 8px;
          background: linear-gradient(180deg, #f3f3f5 0%, #fff 100%);

          :deep(.nancalui-tabs) {
            width: calc(100% - 240px);
            height: 40px;
            .nancalui-tabs-nav-tab {
              height: 40px;
              border-bottom: none;
            }
            .nancalui-tabs-tab {
              box-sizing: border-box;
              margin-left: 0;
              padding: 0 24px;
              color: #1d2129;
              background-color: #f0f2f5;
              border: 1px solid #e5e6eb;
              border-left: none;
              &:first-of-type {
                border-left: 1px solid #e5e6eb;
              }
              &.nancalui-tabs-tab-active {
                color: #1e89ff;
                background-color: #fff;
                border-bottom: none;
              }
            }
            .nancalui-tabs-nav-ink {
              display: none;
            }
          }

          &-btn {
            .icon {
              margin-left: 10px;
              font-size: 16px;

              &.show {
                transform: rotate(180deg);
              }
            }
          }
        }

        &-text {
          box-sizing: border-box;
          height: calc(100% - 48px);
          padding: 0 16px;
          overflow: hidden;
          color: #1d2129;
          font-size: 14px;

          .textarea-loading {
            box-sizing: border-box;
            width: 100%;
            height: calc(100% - 8px);
            margin-top: 8px;
            overflow-y: auto;
            word-break: break-all;
            background: linear-gradient(181deg, #f3f3f5 0.86%, #fff 11.07%);
            &.log {
              padding-bottom: 8px;
            }
            :deep(.el-table__header-wrapper) {
              // border: 1px solid #606266;
              border-bottom: none;
            }
            :deep(.el-table__body) {
              // border: 1px solid #606266;
              border-top: none;
            }
          }
          .textarea {
            padding: 16px;
            color: rgba(0, 0, 0, 0.75);
            white-space: pre-wrap;
            word-break: break-all;
            background-color: transparent;
            border: 1px solid #e5e6eb;
            border-radius: 2px;
            :deep(.green) {
              color: #00c700;
              font-weight: bolder;
              font-size: 16px;
            }
            :deep(.red) {
              font-weight: bolder;
              font-size: 16px;
              color: #f63838;
            }
          }

          :deep(.nancalui-textarea__div) {
            .nancalui-textarea {
              padding: 0;
              color: rgba(0, 0, 0, 0.75);
              white-space: pre-wrap;
              word-break: break-all;
              background-color: transparent;
              border: 1px solid #fff;

              &:hover {
                border: 1px solid #fff !important;
                box-shadow: none !important;
              }
            }
          }

          :deep(.el-loading-mask) {
            .el-loading-spinner {
              .circular {
                width: 32px !important;
                height: 32px !important;
              }
            }
          }

          .empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;

            &-pic {
              width: 48px;
              height: 48px;
            }

            &-text {
              margin-top: 16px;
              color: #909399;
              font-size: 14px;
            }
          }
        }
      }

      .config-popup {
        position: absolute;
        top: -46px;
        right: 48px;
        z-index: 2002;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        align-items: center;
        width: 460px;
        height: calc(100% + 46px);
        background: #fff;
        box-shadow: -8px 0 24px -2px rgba(30, 47, 85, 0.1);

        &.attr {
          width: 400px;
        }

        &.version {
          width: 560px;
        }

        &-title {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          align-self: stretch;
          height: 48px;
          padding: 12px 20px 12px 0;
          border-bottom: 1px solid #f5f7fa;

          .line {
            width: 4px;
            height: 18px;
            margin-right: 12px;
            background: #1e89ff;
          }

          .name {
            color: rgba(0, 0, 0, 0.9);
            font-weight: 500;
            font-size: 16px;
            font-family: 'Source Han Sans CN';
            font-style: normal;
            line-height: 24px;
          }
        }

        &-content {
          box-sizing: border-box;
          width: 100%;
          height: calc(100% - 48px);
          padding: 16px;
          overflow-y: auto;

          .nancalui-form {
            width: 100%;

            .el-select {
              width: 100%;
            }
          }

          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 16px;
            padding-left: 13px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;

            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 3px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }

          .content-btn {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;

            .btn {
              display: flex;
              gap: 4px;
              align-items: center;
              box-sizing: border-box;
              padding: 4px 16px;
              color: #1e89ff;
              font-size: 14px;
              border: 1px solid var(---, #1e89ff);
              border-radius: 2px;

              .icon {
                font-size: 14px;
              }

              &.active,
              &:hover {
                color: #fff;
                background: #1e89ff;
              }
            }

            &-title {
              color: #1d2129;
              font-weight: 500;
              font-size: 14px;

              span {
                color: #909399;
                font-weight: normal;
              }
            }
          }

          .ods_data_query {
            display: flex;
            justify-content: center;
            margin-bottom: 8px;
            .nancalui-input {
              margin-right: 10px;
            }
          }

          .refer-table {
            box-sizing: border-box;
            height: 330px;
            margin-bottom: 10px;
            padding: 8px;
            overflow-y: auto;
            border: 1px solid var(---, #e5e6eb);
            border-radius: 2px;

            &-back {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 64px;
              height: 32px;
              color: #6e9eff;
              border-radius: 2px;
              cursor: pointer;

              &:hover {
                color: #fff;
                background-color: #1e89ff;
              }

              .icon {
                margin-right: 4px;
                font-size: 16px;
              }
            }

            &-name {
              padding: 0 16px;
              overflow: hidden;
              color: #1d2129;
              font-weight: 500;
              font-size: 14px;
              line-height: 32px;
              white-space: nowrap;
              text-overflow: ellipsis;
              word-break: break-all;
            }

            .label {
              display: flex;
              align-items: center;
              justify-content: space-between;
              box-sizing: border-box;
              width: 100%;
              height: 32px;
              margin-bottom: 4px;
              padding: 0 8px;
              border-radius: 2px;
              cursor: pointer;

              &:hover {
                background-color: #ebf4ff;

                .icon {
                  display: block;
                }

                .label-name {
                  color: #1e89ff;
                }
              }

              &-name {
                width: calc(100% - 24px);
                overflow: hidden;
                color: #606266;
                font-size: 14px;
                white-space: nowrap;
                text-overflow: ellipsis;

                &.field {
                  color: rgba(0, 0, 0, 0.75);
                }
              }

              .icon {
                display: none;
                color: #1e89ff;
                font-size: 16px;

                &:hover {
                  color: #1e89ff;
                }
              }
            }
          }
        }
      }
    }
  }

  .modal-body {
    &-header {
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .col {
          display: flex;
          flex: 1;
          flex-shrink: 0;
          align-items: center;
          justify-content: flex-start;
          height: 22px;
          color: #606266;
          font-size: 14px;
          line-height: 22px;

          .name {
            max-width: 80px;
          }

          &:last-child {
            flex: none;
            width: 210px;
          }

          .value {
            width: calc(100% - 80px);
            overflow: hidden;
            color: #1d2129;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    &-content {
      box-sizing: border-box;
      height: 320px;
      padding: 8px 6px 8px 10px;
      overflow-y: auto;
      border: 1px solid var(---, #e5e6eb);
      border-radius: 2px;

      &-box {
        height: 270px;
        color: #1d2129;
        font-size: 14px;
        line-height: 22px;

        :deep(.nancalui-textarea__div) {
          height: 100%;
          overflow-y: auto;

          .nancalui-textarea {
            padding: 0;
            color: rgba(0, 0, 0, 0.75);
            background-color: transparent;
            border: 1px solid #fff;

            &:hover {
              border: 1px solid #fff !important;
              box-shadow: none !important;
            }
          }
        }
      }

      &-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 30px;

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 30px;
          color: #1e89ff;
          font-size: 14px;
          text-align: center;
          border-radius: 2px;
          cursor: pointer;

          .icon {
            margin-right: 4px;
            font-size: 14px;
          }

          &:hover {
            color: #fff;
            background: #1e89ff;
          }
        }
      }
    }

    &-flow {
      box-sizing: border-box;
      height: 524px;
      padding: 16px;
    }
  }
</style>
