<template>
  <detailModel ref="detailModel" :isModel="true" />
</template>

<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import detailModel from '@/views/data-assets/globalSearch/detail.vue'

  export default {
    title: '',
    components: { detailModel },
    props: {},
    setup(props) {
      const router = useRouter()
      const detailModel = ref()
      const state = reactive({
        modelTitle: '',
        queryData: {},
      })
      const methods = {
        // 返回
        goBack() {
          // router.go(-1)
          if (state.queryData.status === 'DONE') {
            let params = {
              active: 'second',
            }
            router.push({
              name: 'auditRegistryList',
              query: params,
            })
          } else {
            let params = {
              active: 'first',
            }
            router.push({
              name: 'auditRegistryList',
              query: params,
            })
          }
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query
      })

      return {
        state,
        detailModel,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .full-page {
    .fixed-bottom {
      height: 66px;
      position: absolute;
      bottom: 0px;
      left: 0;
      right: 0;
      background-color: #fff;
      border-radius: 8px 8px 0px 0px;
      padding: 0 30px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .options-box-bg {
      // position: absolute;
      // bottom: 0;
      // left: 0;
      // right: 0;
      margin: 10px -10px 0 -10px;
      height: 66px;
      padding: 0 20px;
      background-color: #fff;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .content {
        width: 100%;
        height: 100%;
        padding: 20px;
        text-align: right;
        background-color: #fff;
      }
    }
    .global-search-detail {
      padding: 20px;
      height: calc(100% - 65px);
      position: relative;
      border: 4px;
      border-radius: 4px;
      background-color: #fff;
      .detail-top-box {
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .need_smallcube__title {
          margin: 0;
        }
      }
      .detail-api-content {
        height: 100%;
      }
      .content-box {
        height: 100%;
      }
    }
  }
</style>
