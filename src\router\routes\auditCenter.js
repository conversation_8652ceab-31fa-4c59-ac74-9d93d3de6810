import Layout from '@/layout'

// 功能模块-审核中心
export default [
  {
    path: '/auditCenter',
    component: Layout,
    redirect: '/auditCenter/auditRegistry',
    name: 'auditCenter',
    code: 'auditCenter',
    meta: {
      title: '资产注册审批',
      icon: 'icon-auditCenter',
      parentRouterName: 'systemManage',
      code: 'auditCenter',
      unShowProject: true,
    },
    children: [
      //注册审批
      {
        path: 'auditRegistry',
        name: 'auditRegistry',
        code: 'auditRegistry',
        redirect: '/auditCenter/auditRegistry/auditRegistryList',
        component: () => import('@/views/audit-center/registry-audit/index'),
        meta: {
          title: '数据资产审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'auditRegistry',
          unShowProject: true,
        },
        children: [
          {
            path: 'auditRegistryList',
            name: 'auditRegistryList',
            hidden: true,
            component: () => import('@/views/audit-center/registry-audit/list'),
            meta: {
              title: '资产注册审批',
              unShowProject: true,
              code: 'auditRegistry',
              activeMenu: '/auditCenter/auditRegistry',
              parentRouterName: 'systemManage',
            },
          },
          {
            path: 'approveMagsRegistry',
            name: 'approveMagsRegistry',
            component: () =>
              import('@/views/audit-center/registry-audit/component/ApproveMagaRegistry'),
            hidden: true,
            meta: {
              title: '资产注册审批查看',
              unShowProject: true,
              activeMenu: '/auditCenter/auditRegistry',
              parentRouterName: 'systemManage',
            },
          },
        ],
      },
      // 工作流审批
      {
        path: 'workFlowAudit',
        name: 'workFlowAudit',
        code: 'workFlowAudit',
        redirect: '/auditCenter/workFlowAudit/list',
        component: () => import('@/views/audit-center/work-flow-audit/index'),
        meta: {
          title: '工作流审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'workFlowAudit',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'workFlowAuditList',
            hidden: true,
            component: () => import('@/views/audit-center/work-flow-audit/list'),
            meta: {
              title: '工作流审批',
              activeMenu: '/auditCenter/workFlowAudit',
              parentRouterName: 'systemManage',
              code: 'workFlowAudit',
              unShowProject: true,
            },
          },
        ],
      },
      {
        path: 'realTimeOperAudit',
        name: 'realTimeOperAudit',
        code: 'realTimeOperAudit',
        redirect: '/auditCenter/realTimeOperAudit/list',
        component: () => import('@/views/audit-center/real-time operation-audit/index'),
        meta: {
          title: '实时作业审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'realTimeOperAudit',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'realTimeOperAuditList',
            hidden: true,
            component: () => import('@/views/audit-center/real-time operation-audit/list'),
            meta: {
              title: '实时作业审批',
              activeMenu: '/auditCenter/realTimeOperAudit',
              parentRouterName: 'systemManage',
              unShowProject: true,
              code: 'realTimeOperAudit',
            },
          },
        ],
      },
      //标签审批
      {
        path: 'tagAudit',
        name: 'tagAudit',
        code: 'tagAudit',
        redirect: '/auditCenter/tagAudit/list',
        component: () => import('@/views/audit-center/tag-audit/index'),
        meta: {
          title: '标签审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'tagAudit',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'tagAuditList',
            hidden: true,
            component: () => import('@/views/audit-center/tag-audit/list'),
            meta: {
              title: '标签审批',
              activeMenu: '/auditCenter/tagAudit',
              parentRouterName: 'systemManage',
              unShowProject: true,
              code: 'tagAudit',
            },
          },
        ],
      },
      // 数据标注审批
      {
        path: 'markAudit',
        name: 'markAudit',
        code: 'markAudit',
        redirect: '/auditCenter/markAudit/list',
        component: () => import('@/views/audit-center/mark-audit/index'),
        meta: {
          title: '数据标注审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'markAudit',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'markAuditList',
            hidden: true,
            component: () => import('@/views/audit-center/mark-audit/list'),
            meta: {
              title: '数据标注审批',
              activeMenu: '/auditCenter/markAudit',
              parentRouterName: 'systemManage',
              unShowProject: true,
              code: 'markAudit',
            },
          },
        ],
      },
      //标注审批
      {
        path: 'markAudit',
        name: 'markAudit',
        code: 'markAudit',
        redirect: '/auditCenter/markAudit/list',
        component: () => import('@/views/audit-center/mark-audit/index'),
        meta: {
          title: '标注审批',
          icon: 'icon-auditCenter',
          parentRouterName: 'systemManage',
          code: 'markAudit',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'markAuditList',
            hidden: true,
            component: () => import('@/views/audit-center/mark-audit/list'),
            meta: {
              title: '标注审批',
              activeMenu: '/auditCenter/markAudit',
              parentRouterName: 'systemManage',
              code: 'markAudit',
            },
          },
          // 查看标注
          {
            path: 'approveMagsMark',
            name: 'approveMagsMark',
            component: () => import('@/views/document-management/document-annotation/preview'),
            hidden: true,
            meta: {
              title: '标注审批查看',
              activeMenu: '/auditCenter/markAudit',
              parentRouterName: 'systemManage',
            },
          },
        ],
      },
    ],
  },
]
