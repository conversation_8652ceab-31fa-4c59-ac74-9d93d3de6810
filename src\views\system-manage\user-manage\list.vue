<template>
  <section class="container-padding16">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <n-input
            class="nancalui-input-demo__mt"
            v-model="state.formInline.keywords"
            size="small"
            placeholder="请输入"
          >
            <template #prepend>
              <n-select
                v-model="state.formInline.keyType"
                size="sm"
                :options="state.typeList"
                @value-change="onChange"
              />
            </template>
          </n-input>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          <!-- <div class="search-btn" @click.prevent="userPull">用户同步</div> -->
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 nc-p-l-12 nc-p-r-12">
        <n-input
          v-model="state.searchWords"
          size="small"
          placeholder="搜索"
          suffix="search"
          class="nc-m-b-12"
          @input="searchTreeFn"
        />
        <div class="tree-box">
          <el-tree
            ref="publicTree"
            style="max-width: 600px"
            :props="props"
            :load="loadNode"
            :filter-node-method="filterAssociationDocs"
            node-key="id"
            lazy
            :highlight-current="true"
            :default-expanded-keys="state.defaultExpandedKeys"
            :current-node-key="state.currentNodeKey"
            @node-click="handleNodeClick"
          />
        </div>
      </div>
      <div class="right nc-m-l-10">
        <section class="table nc-p-t-16" v-loading="state.loading">
          <CfTable
            :actionWidth="180"
            isNeedIndex
            :table-head-titles="state.tableHeadTitles"
            :tableConfig="{
              data: state.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: state.pagination.total,
              pageSize: state.pagination.pageSize,
              currentPage: state.pagination.currentPage,
              onCurrentChange: (v) => {
                state.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pagination.pageSize = v
                onSearch()
              },
            }"
          >
          </CfTable>
        </section>
      </div>
    </section>
  </section>
</template>

<script setup>
  import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { useStore } from 'vuex'
  const props = {
    label: 'name',
    children: 'children',
    isLeaf: 'isLeaf',
  }
  const state = reactive({
    defaultExpandedKeys: ['1'],
    currentNodeKey: '1',
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'username', name: '工号' },
      { prop: 'name', name: '姓名' },
      { prop: 'departmentFullName', name: '部门' },
      { prop: 'secretLevelName', name: '人员密级' },
      { prop: 'roleName', name: '角色' },
    ],
    pagination: {
      total: 1,
      currentPage: 1,
      pageSize: 10,
    },

    typeList: [
      {
        name: '工号',
        value: 'username',
      },
      {
        name: '姓名',
        value: 'name',
      },
      {
        name: '部门',
        value: 'departmentFullName',
      },
      {
        name: '角色',
        value: 'roleName',
      },
    ],
    departmentInfo: {
      fullName: '',
      fullId: '',
    }, // 部门信息

    //展示的
    formInline: {
      keywords: '',
      keyType: 'username',
    },
    //真实用于搜索的
    searchData: {
      keywords: '',
      keyType: 'username',
    },
    searchWords: '',
    tableList: [], // 列表数据
    loading: false,
    timer: null,
  })
  const store = useStore()
  //按钮权限
  const { buttonAuthList } = toRefs(store.state.user)
  const publicTree = ref('')
  const { proxy } = getCurrentInstance()
  const router = useRouter()

  //递归设置展开
  const setTreeRecursion = (data) => {
    data.map((item, index) => {
      if (item.children && item.children.length != 0) {
        state.defaultExpandedKeys.push(item.id)
        setTreeRecursion(item.children)
      }
    })
    return data
  }
  // 过滤方法
  const filterData = (data, query) => {
    return data.filter((node) => filterAssociationDocs(query, node))
  }
  function filterAssociationDocs(query, data) {
    if (!query) return true
    return data.name.indexOf(query) > -1
  }
  //树搜索
  const searchTreeFn = () => {
    clearTimeout(state.timer)
    state.timer = setTimeout(() => {
      api.base.getTreeSearchData({ name: state.searchWords }).then((res) => {
        let { success, data } = res
        if (success) {
          if (data) {
            state.defaultExpandedKeys = []
            setTreeRecursion([data])
            publicTree.value?.filter(state.searchWords)
          }
        }
      })
    }, 1000)
  }

  const loadingNodes = ref(new Set())
  //懒加载
  const loadNode = (node, resolve) => {
    loadingNodes.value.add(node)
    api.base.getDepartmentLazy({ pid: node.data.id }).then((res) => {
      let { success, data } = res
      if (success) {
        const filteredData = filterData(data, state.searchWords)
        resolve(data)
        // 数据加载完成后，从集合中移除当前节点
        loadingNodes.value.delete(node)
        // 检查是否所有节点都已加载完成
        if (loadingNodes.value.size === 0) {
          publicTree.value?.filter(state.searchWords)
        }
        if (node.level === 0) {
          nextTick(() => {
            state.departmentInfo = node
            onSearch(true)
          })
        }
      }
    })
  }
  //点击树节点
  const handleNodeClick = (data, node, component) => {
    state.departmentInfo = data
    state.currentNodeKey = data.id
    onSearch(true)
  }
  //切换搜索类别
  const onChange = () => {
    state.formInline.keywords = ''
  }
  //重置
  const resetFn = () => {
    state.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    state.formInline = {
      keywords: '',
      keyType: 'name',
    }
    state.searchData = {
      keywords: '',
      keyType: 'name',
    }
    state.departmentInfo.fullId = '1'
    state.departmentInfo.fullName = '全部'

    state.currentNodeKey = '1'
    onSearch()
  }
  // 用户同步
  const userPull = () => {
    api.system.userCenterPull().then((res) => {
      if (res.success) {
        ElMessage.success('用户同步成功！')
        onSearch()
      }
    })
  }
  //查询
  const startSearch = () => {
    Object.keys(state.formInline).forEach((key) => {
      state.searchData[key] = state.formInline[key]
    })

    onSearch()
  }
  // 查询列表
  const onSearch = (init = true) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    let params = {
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      condition: {
        departmentFullId: state.departmentInfo.fullId,
        departmentFullName: state.departmentInfo.fullName,
      },
    }
    if (state.searchData.keywords) {
      params.condition[state.searchData.keyType] = state.searchData.keywords
      delete params.condition.departmentFullId
      if (state.formInline.keyType !== 'departmentFullName') {
        delete params.condition.departmentFullName
      }
    }
    state.loading = true
    api.system
      .userList(params)
      .then((res) => {
        state.loading = false
        state.tableList = res.data.list
        state.pagination.total = res.data.total
      })
      .catch(() => {
        state.tableList = []
        state.loading = false
      })
  }
</script>

<style scoped lang="scss">
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container-padding16 {
    .row {
      .nancalui-input {
        width: 300px !important;
      }
      :deep(.nancalui-input-slot__prepend) {
        width: 90px;
        padding: 0;
        .nancalui-select {
          margin-right: 0 !important;
          padding: 0 10px;
          border: none !important;
          .nancalui-select__selection:hover {
            box-shadow: unset;
          }
        }
      }
    }
    .template-con-flex {
      display: flex;
      width: 100%;
      height: calc(100% - 62px);
      .left {
        width: 286px;
        background: #fff;
        border-radius: $cf-border-radius;
        .tree-box {
          height: calc(100% - 56px);
          overflow-y: auto;
        }
        :deep(.tree) {
          width: 100%;
          .resize,
          .knob {
            display: none !important;
          }
        }
      }
      .right {
        flex: 1;
        width: calc(100% - 10px - var(--aside-width));
        background: #fff;
        border-radius: $cf-border-radius;
        .table {
          height: calc(100% - 4px);
        }
      }
    }
  }
</style>
