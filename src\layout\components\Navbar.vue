<template>
  <div>
    <div class="fixed-header">
      <div :class="menuHidden ? 'navbar checked' : 'navbar'">
        <div class="left-menu">
          <div class="logo" @click.stop.prevent="goJump('ConvergencePage')">
            <img class="pic" src="@img/home/<USER>" />
          </div>
          <!--          <Tag />-->
        </div>
        <div class="right-menu">
          <div
            class="project-box"
            v-if="
              projectList.length > 0 &&
              !$route.meta?.unShowProject &&
              activeMenu !== 'projectManage' &&
              activeMenu !== 'systemManage'
            "
          >
            <div class="project-box-check" @click.stop="checkProjectFn(!showRightProject)">
              <n-input
                class="input"
                :key="key"
                size="small"
                v-model="projectName"
                @blur="blurFn"
                @focus="focusFn"
                @input="inputFn"
                @keyup.enter="handleFn"
              />
              <SvgIcon v-if="showRightProject" class="pic" icon="icon-arrow-top" />
              <SvgIcon v-else class="pic" icon="icon-arrow-bottom" />
            </div>
            <div
              v-show="showProjectList.length > 0"
              :class="showRightProject ? 'project-box-child checked' : 'project-box-child'"
            >
              <div
                class="project-box-child-box"
                :style="projectMenuStatus ? '' : 'border-bottom:none; padding-bottom: 2px;'"
              >
                <div
                  v-for="(item, index) in showProjectList"
                  :key="index"
                  :class="
                    currentProject.projectCode === item.projectCode &&
                    activeMenu !== 'projectManage' &&
                    activeMenu !== 'systemManage'
                      ? 'project-box-child-content checked'
                      : 'project-box-child-content'
                  "
                  @click.stop="checkProjectFn(false, item)"
                >
                  {{ item.name }}
                  <SvgIcon class="icon-tick" icon="icon-tick" />
                </div>
              </div>
            </div>
          </div>
          <div class="news-container">
            <n-dropdown trigger="hover" :offset="10" :shift-offset="30">
              <div class="news-wrapper">
                <div class="news-wrapper-box">
                  <SvgIcon class="icon-arrow" icon="icon-news" />
                  <div
                    v-if="messageCount > 0"
                    :class="{
                      circle: true,
                      more: messageCount > 99,
                      two: messageCount > 9 && messageCount < 100,
                    }"
                    >{{ messageCount > 99 ? '99+' : messageCount }}</div
                  >
                </div>
              </div>
              <template #menu>
                <div class="news-dropdown">
                  <div v-if="messageCount > 0" class="news-list">
                    <div class="newsBox scroll-bar-style">
                      <div
                        v-for="item in messageList"
                        :key="item.id"
                        class="news-label"
                        @click.prevent="readMessageFn(item)"
                      >
                        <div class="left">
                          <img class="icon-img" src="/src/assets/img/home/<USER>" />
                          <div v-if="item.status === 0" class="circle"></div>
                        </div>
                        <div class="right">
                          <div class="name">{{ item.messageContent }}</div>
                          <div class="time">{{ item.messageTime }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="news-empty">
                    <img class="pic" src="/src/assets/img/home/<USER>" />
                    <div class="news-empty-text">暂无未读信息</div>
                  </div>
                  <div>
                    <div class="news-more" @click.prevent="settingFn('systemManagement', 'notice')"
                      >查看更多</div
                    >
                  </div>
                </div>
              </template>
            </n-dropdown>
          </div>
          <div class="avatar-container">
            <userDropdown :contentWidth="220" algin="right" :systemMenuStatus="systemMenuStatus" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import breadcrumb from '@/components/Breadcrumb'
  import { ElNotification } from 'element-plus'
  import Tag from './Tag'
  import userDropdown from '@/components/userDropdown'
  import { TokenKey } from '@/utils/auth'
  export default {
    components: {
      breadcrumb,
      Tag,
      userDropdown,
    },
    data() {
      return {
        key: 1,
        projectList: [],
        showProjectList: [],
        breadcrumbStatus: true,
        isFullScreen: false,
        dropdownStatus: false,
        showMoreTag: false,
        timeFlag: null,
        messageList: [],
        messageCount: 0,
        projectName: '',
      }
    },
    computed: {
      ...mapGetters(['sidebar', 'avatar', 'name']),
      ...mapState({
        dataGovernanceStatus: (state) => state['user'].dataGovernanceStatus,
        dataAssetsStatus: (state) => state['user'].dataAssetsStatus,
        systemMenuStatus: (state) => state['user'].systemMenuStatus,
        projectMenuStatus: (state) => state['user'].projectMenuStatus,
        applicationMenuStatus: (state) => state['user'].applicationMenuStatus,
        isSystemMenu: (state) => state['user'].isSystemMenu,
        showRightProject: (state) => state['app'].showRightProject,
        name: (state) => state['user'].name,
        addProjectStatus: (state) => state['user'].addProjectStatus,
        currentProject: (state) => state['user'].currentProject,
        menu: (state) => state['user'].menu,
        activeMenu: (state) => state['user'].activeMenu,
        menuHidden: (state) => state['app'].menuHidden,
        checkFirstMenuCode: (state) => state['user'].checkFirstMenuCode,
        menuTreeList: (state) => state['user'].menuTreeList,
        homeType: (state) => state['user'].homeType,
        inteLogin: (state) => state['user'].inteLogin,
      }),
    },
    watch: {
      $route(to, from) {
        this.breadcrumbStatus = true
      },
      addProjectStatus(val) {
        if (val) {
          this.$store.commit('user/ADD_PROJECT', false)
          this.getCheckProjectFn()
        }
      },
    },
    mounted() {
      this.getCheckProjectFn()
      if (this.timeFlag) {
        clearInterval(this.timeFlag)
        this.timeFlag = null
      }
      this.timedRefreshFn()
      this.timeFlag = setInterval(() => {
        this.timedRefreshFn()
      }, 30 * 1000)
      window.addEventListener('resize', () => {
        if (window.screen.height - window.document.body.offsetHeight > 5) {
          this.isFullScreen = false
        } else {
          this.isFullScreen = true
        }
      })
    },
    beforeUnmount() {
      if (this.timeFlag) {
        clearInterval(this.timeFlag)
        this.timeFlag = null
      }
    },
    methods: {
      // 定时刷新
      timedRefreshFn() {
        this.getMessageListFn()
      },

      // 拉取消息
      pullMessageFn() {
        this.$api.base.pullMessageList({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.getMessageListFn()
          }
        })
      },

      //获取消息列表
      getMessageListFn() {
        this.$api.base
          .getMessageList({
            pageNum: 1,
            pageSize: 10,
            condition: { messageType: null, status: 'UNREAD' },
          })
          .then((res) => {
            if (res.code === 'SUCCESS') {
              this.messageList = res.data.list
              this.messageCount = res.data.total
            }
          })
      },

      // 读取消息
      readMessageFn(item) {
        let ids = []
        ids.push(item.id)
        this.$api.base.readMessage(ids).then((res) => {
          if (item.messageBusinessType === 'API') {
            this.goMessageFn('audit', 'auditCenterOverview')
          } else if (item.messageBusinessType === 'JOB') {
            this.goMessageFn('auditDev', 'devJobUpApproval')
          } else if (item.messageBusinessType === 'ASSETS') {
            this.goMessageFn('auditRegistry', 'auditRegistry')
          } else if (item.messageBusinessType === 'STANDARD') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('dataStandardManage', 'dataStandardManage')
            } else {
              this.goMessageFn('auditStandard', 'auditDataStandardOverview')
            }
          } else if (item.messageBusinessType === 'COLLECT') {
            this.goMessageFn('dataCollection', 'DataCollection')
          } else if (item.messageBusinessType === 'REALTIME') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('realTimeWork', 'realTimeWork')
            } else {
              this.goMessageFn('realTimeOperAudit', 'realTimeOperAudit')
            }
          } else if (item.messageBusinessType === 'OFFLINE') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('dataWork', 'dataWork')
            } else {
              this.goMessageFn('workFlowAudit', 'workFlowAudit')
            }
          } else if (item.messageBusinessType === 'DATASOURC') {
            this.goMessageFn('project', 'projectManage')
          } else if (item.messageBusinessType === 'QUALITY') {
            this.goMessageFn('operatingLog', 'qualityManage', { id: item.bizObjectId })
          } else if (item.messageBusinessType === 'INDICATOR') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('targetIndex', 'targetManage')
            } else {
              this.goMessageFn('auditTargetList', 'assetTarget')
            }
          } else if (item.messageBusinessType === 'INDICATOR_ATTR') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('targetAttribute', 'targetManage')
            } else {
              this.goMessageFn('auditTargetattriList', 'assetTargetattri')
            }
          } else if (item.messageBusinessType === 'INDICATOR_MODEL') {
            if (item.messageType === 'JOB') {
              this.goMessageFn('modelManageList', 'targetManage')
            } else {
              this.goMessageFn('auditTargetsysList', 'assetTargetsys')
            }
          } else if (item.messageBusinessType === 'INDICATOR_TARGET') {
            this.goMessageFn('targetBoardDetail', 'target', { id: item.bizObjectId, isTable: true })
          } else if (item.messageBusinessType === 'METADATA_REFRESH') {
            this.goMessageFn('resourceDirectoryCollect', 'resourceDirectoryCollect')
          }
          // 更新消息列表
          this.getMessageListFn()
        })
      },
      // 跳转消息对应模块
      goMessageFn(name, code, query) {
        const obj = this.menu.find((data) => {
          return data.code === code
        })
        if (!obj) {
          ElNotification({
            title: '提示',
            message: '暂无权限',
            type: 'error',
          })
          return
        } else {
          this.goJump(name, query)
        }
      },
      // 获取默认的场景
      getCheckProjectFn() {
        this.$api.project.getMyProjectList({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.projectList = res.data
            this.showProjectList = JSON.parse(JSON.stringify(this.projectList))
            this.$store.commit('user/SET_PROJECTLIST', this.projectList)
            if (this.projectList.length > 0) {
              let flag = true
              this.projectList.forEach((val) => {
                if (val.using && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                } else if (val.preferred && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                }
              })
              if (flag) {
                this.$store.commit('user/SET_PROJECT', this.projectList[0])
              }
            }
            this.projectName = this.currentProject.name
          }
        })
      },
      toggleSideBar() {
        this.$store.dispatch('app/toggleSideBar')
      },
      async logout() {
        localStorage.removeItem(TokenKey)
        await this.$store.dispatch('user/logout')
        this.$router.push(`/?redirect=${this.$route.fullPath}`)
      },
      // 系统管理
      settingFn(code, routeName = null) {
        let activeInfo = { activeMenu: '', activeName: '', code: '' }
        this.menuTreeList.forEach((val) => {
          if (val.code === code) {
            activeInfo = this.getActiveInfoFn(val.children, activeInfo, 1)
          }
        })
        if (routeName) {
          if (!activeInfo.activeMenu) {
            this.$notify({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
            return false
          }
        }
        this.$store.commit('user/SET_FIRST_MENU', code)
        this.$router.push({
          name: routeName ? routeName : activeInfo.activeMenu,
        })
      },
      // 获取选择模块的菜单
      getActiveInfoFn(list, activeInfo, zIndex) {
        list.forEach((val) => {
          if (val.children && val.children[0] && val.children[0].resourceType === 'MENU') {
            this.getActiveInfoFn(val.children, activeInfo, zIndex + 1)
          } else if (val.routerName && !activeInfo.activeMenu) {
            activeInfo.activeMenu = val.routerName
            activeInfo.activeName = val.name
            activeInfo.code = zIndex > 1 ? val.parentCode : val.code
          }
        })
        return activeInfo
      },
      // 点击场景下拉框
      checkProjectFn(flag, item) {
        const { isAdmin } = this.$store?.state?.user || {}
        this.$store.commit('app/CLOSE_PROJECT', flag)
        if (item) {
          this.projectName = item.name
          this.$store.commit('user/SET_PROJECT', item)
          if (isAdmin) {
            this.switchFn()
            // this.$router.push({ name: 'HomeIndex' })
          } else {
            this.$api.project
              .setMyDefaultProject({ projectCode: item.projectCode, off: true })
              .then((res) => {
                this.switchFn()
              })
          }
          this.$api.project.switchProject({ projectCode: item.projectCode }).then((res) => {})
        }
      },
      // 切换跳转
      switchFn() {
        // let activeMenu = 'HomeIndex'
        // let activeName = '首页'
        // if (this.homeType === 4) {
        //   activeMenu = this.menuTreeList[0]?.children[0]?.children[0]?.routerName
        //   activeName = this.menuTreeList[0]?.children[0]?.children[0]?.name
        // }
        // // 跳转对应路由
        // this.$router.push({
        //   name: activeMenu,
        // })
        // this.key++
        window.location.reload()
      },
      // 失去焦点
      blurFn() {
        setTimeout(() => {
          this.projectName = this.currentProject.name
          this.$store.commit('app/CLOSE_PROJECT', false)
        }, 300)
      },
      focusFn() {
        this.projectName = ''
      },
      // 输入时搜索项目
      inputFn() {
        if (this.projectName) {
          this.showProjectList = this.projectList.filter(
            (val) => val.name.indexOf(this.projectName) !== -1,
          )
        } else {
          this.showProjectList = JSON.parse(JSON.stringify(this.projectList))
        }
      },
      // 按下enter选中
      handleFn() {
        if (this.showProjectList.length > 0) {
          this.checkProjectFn(false, this.showProjectList[0])
        }
      },
      goJump(name, query) {
        if (query) {
          this.$router.push({ name: name, query })
        } else {
          this.$router.push({ name: name })
        }
      },
      // 跳转其他路径
      goOtherJump() {
        this.$router.push({ name: 'Home' })
      },
      // 设置选中的一级菜单
      checkMenuFn(code) {
        if (code !== this.checkFirstMenuCode) {
          let menuList = this.menuTreeList.filter((item) => {
            return item.code === code
          })[0].children
          this.$router.push({ name: menuList[0].routerName })
        }
      },
      // 右上角下拉事件
      dropdownFn(e) {
        this.dropdownStatus = e
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .fixed-header {
    position: relative;

    .navbar {
      display: flex;
      width: 100%;
      height: 56px;
      background-color: #1f76cb;
      transition: width 0.28s;

      .el-dropdown {
        height: 100%;
        vertical-align: middle;

        &-link {
          display: inline-block;
          height: 60px;
          padding: 0 16px;
          color: #fff;
          font-size: 14px;
          line-height: 60px;
          cursor: pointer;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.28);
        }
      }

      .left-menu {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: flex-start;
        height: 100%;
        overflow: auto;
        color: #ffffff;
        font-weight: bolder;
        font-size: 22px;
        cursor: pointer;
        .logo {
          padding: 0 0 0 24px;

          .pic {
            display: block;
            width: 337px;
            height: 42px;
            cursor: pointer;

            &.alone {
              width: 32px;
              height: 32px;
              margin-right: 6px;
            }
          }
        }
      }

      .right-menu {
        position: relative;
        z-index: 99;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 100%;
        margin-right: 0;

        &:focus {
          outline: none;
        }

        .project-box {
          position: relative;
          display: inline-block;
          width: 240px;
          height: 30px;
          margin-right: 4px;
          margin-left: 10px;
          vertical-align: middle;
          border-radius: 15px;
          cursor: pointer;

          &-check {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            height: 30px;
            padding: 0;
            overflow: hidden;
            color: #a8abb2;
            font-size: 12px;
            line-height: 28px;
            white-space: nowrap;
            text-overflow: ellipsis;
            background-color: inherit;
            :deep(.nancalui-input) {
              height: 30px;
              .nancalui-input__wrapper {
                border: none;
                border-radius: 15px !important;
                .nancalui-input__inner {
                  color: #a8abb2;
                  cursor: pointer;
                }
              }
            }
            .pic {
              position: absolute;
              top: 0;
              right: 10px;
              bottom: 0;
              width: 10px;
              height: 10px;
              margin: auto;
              color: #909399;
              font-size: 10px;
            }
          }

          &-child {
            position: absolute;
            top: 50px;
            left: 0;
            z-index: 999;
            box-sizing: border-box;
            width: 100%;
            height: 0;
            max-height: 430px;
            padding: 0 4px;
            overflow: hidden;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
            transition: all 0.2s;

            &.checked {
              top: 36px;
              height: auto;
              padding: 4px;
            }

            &-box {
              max-height: 380px;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
              }

              &::-webkit-scrollbar-thumb {
                background-color: #cfcfcf;
                border-radius: 2px;
              }
            }

            &-content {
              position: relative;
              height: 38px;
              padding: 0 10px;
              overflow: hidden;
              color: #333333;
              font-size: 12px;
              line-height: 38px;
              white-space: nowrap;
              text-overflow: ellipsis;
              background-color: #fff;
              border-radius: 4px;

              .icon-tick {
                position: absolute;
                top: 0;
                right: 10px;
                bottom: 0;
                width: 16px;
                height: 16px;
                margin: auto;
                opacity: 0;
              }

              &:hover {
                font-weight: bold;
                background-color: #f7f8fa;
              }

              &.checked {
                color: $themeBlue;

                .icon-tick {
                  opacity: 1;
                }
              }
            }

            &-manage {
              height: 38px;
              margin-top: 4px;
              color: #333333;
              font-size: 14px;
              line-height: 38px;
              text-align: center;
              background-color: #fff;
              border-radius: 4px;

              &-icon {
                width: 16px;
                height: 16px;
              }

              &:hover {
                font-weight: bold;
                background-color: #f7f8fa;
              }
            }
          }
        }

        .spaceBtn {
          color: #333333;
          cursor: pointer;
        }

        .icon-box {
          display: inline-block;
          height: 100%;
          vertical-align: middle;

          .box-img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 100%;
            margin: 0 auto;

            .home-pic {
              width: 20px;
              height: 20px;
            }

            &:hover {
              cursor: pointer;
            }

            .icon-img {
              width: 20px;
              height: 20px;
              cursor: pointer;
            }
          }

          i {
            margin-right: 28px;
            color: #333333;
            cursor: pointer;
          }
        }

        .right-menu-item {
          display: inline-block;
          height: 100%;
          padding: 0 8px;
          color: #333333;
          font-size: 18px;
          vertical-align: text-bottom;

          &.hover-effect {
            cursor: pointer;
            transition: background 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.025);
            }
          }
        }

        .avatar-container {
          display: inline-block;
          margin-right: 16px;
          margin-left: 6px;
        }
        .news-container {
          display: inline-block;
          width: 40px;
          .news-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            &-box {
              position: relative;
              width: 20px;
              height: 20px;
              margin-top: -4px;
              color: #333333;
              font-size: 20px;
              line-height: 20px;
              cursor: pointer;
              .circle {
                position: absolute;
                top: -6px;
                right: -6px;
                width: 16px;
                height: 16px;
                color: #fff;
                font-size: 12px;
                line-height: 16px;
                text-align: center;
                background-color: #f54446;
                border-radius: 9px;
                &.more {
                  right: -20px;
                  width: 30px;
                }
                &.two {
                  right: -10px;
                  width: 23px;
                }
              }
            }
          }
        }
      }
    }

    .breadcrumb-container {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 46px;
      overflow: hidden;
    }
  }

  :deep(.el-badge__content.is-fixed.is-dot) {
    top: 3px;
    right: 35px;
    background: #e02020;
  }

  .news-dropdown {
    box-sizing: border-box;
    width: 220px;
    padding: 4px;
    :deep(.el-dropdown-menu__item) {
      padding: 0;
      color: #333;
      font-size: 12px;

      &.news-list {
        margin-bottom: 4px;
        border-bottom: 1px solid #eee;
      }

      &:hover {
        background: #ffffff;
      }
    }
    .newsBox {
      width: 100%;
      max-height: 300px;
      overflow-y: auto;
      .news-label {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        width: 100%;
        padding: 9px 10px;
        border-radius: 4px;
        cursor: pointer;
        &:hover {
          background-color: #f7f8fa;
        }
        .left {
          position: relative;
          width: 30px;
          height: 30px;
          .icon-img {
            display: block;
            width: 30px;
            height: 30px;
          }
          .circle {
            position: absolute;
            top: 0;
            right: 0;
            width: 6px;
            height: 6px;
            background-color: #f54446;
            border-radius: 50%;
          }
        }
        .right {
          width: calc(100% - 37px);
          margin-left: 7px;
          line-height: normal;
          .name {
            overflow: hidden;
            color: #333333;
            font-size: 12px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .time {
            margin-top: 8px;
            color: #999999;
            font-size: 12px;
          }
        }
      }
    }
    .news-empty {
      margin-bottom: 4px;
      padding: 22px 0;
      border-bottom: 1px solid #eeeeee;
      .pic {
        display: block;
        width: 84px;
        height: 108px;
        margin: 0 auto;
      }
      &-text {
        margin-top: 15px;
        color: #999999;
        font-size: 12px;
        text-align: center;
      }
    }
    .news-more {
      width: 100%;
      height: 38px;
      color: $themeBlue;
      font-size: 12px;
      line-height: 38px;
      text-align: center;
      cursor: pointer;
      &:hover {
        background-color: #f7f8fa;
      }
    }
  }

  .icon-arrow {
    color: #fff;
  }
</style>
