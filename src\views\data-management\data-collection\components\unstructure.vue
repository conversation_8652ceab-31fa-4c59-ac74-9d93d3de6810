<!--  非结构化数据采集-->
<template>
  <!-- 模型构建 -->
  <div class="data-collection-link-mapping">
    <div class="box-bottom">
      <n-form
        ref="ruleForm"
        :data="state.ruleForm"
        :rules="state.rules"
        label-width="150px"
        class="structure-ruleForm"
        label-align="start"
      >
        <!--        <n-form-item label="存储目录：" field="treeId">-->
        <!--          <n-tree-select-->
        <!--            v-model="state.ruleForm.treeId"-->
        <!--            placeholder="请选择存储目录"-->
        <!--            allowClear-->
        <!--            filter-->
        <!--            :key="state.key"-->
        <!--            useGrayArrow-->
        <!--            :treeData="state.fileTreeData"-->
        <!--            @valueChange="treeSelectChange"-->
        <!--          />-->
        <!--        </n-form-item>-->

        <div class="top-line">
          <n-form-item label="数据源：" field="dataSourceId">
            <n-select
              v-model="state.ruleForm.dataSourceId"
              placeholder="请选择数据源"
              allow-clear
              filter
              :key="state.key"
              :options="state.org_dataSourceType"
              @value-change="dataSourceChange"
            />
          </n-form-item>
          <n-form-item label="文件选择：" field="collectFileName">
            <n-select
              v-model="state.ruleForm.collectFileName"
              placeholder="请选择"
              filter
              allow-clear
              :key="state.key"
              :options="state.dataSourceOptions"
              @value-change="fileChange"
            />
          </n-form-item>
        </div>
        <div v-show="state.showPreviewContent">
          <div class="box-bottom-title">
            <span>预览文件</span>
          </div>
          <div class="top-line">
            <n-form-item v-if="state.previewFile" label="文件编码：" field="fileEncoding">
              <div class="file-code">
                <n-tooltip
                  class="tree-btn"
                  content="如数据有乱码，请尝试切换数据编码格式"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon class="illustrate" icon="icon-illustrate" />
                </n-tooltip>
                <n-radio-group
                  direction="row"
                  v-model="state.ruleForm.fileEncoding"
                  @change="conversionFn(false)"
                >
                  <n-radio value="UTF-8">UTF-8</n-radio>
                  <n-radio value="GB18030">GB18030</n-radio>
                  <n-radio value="GB2312">GB2312</n-radio>
                </n-radio-group>
                <div v-if="state.showFirstHeader" class="file-code-title">
                  <n-checkbox
                    :isShowTitle="false"
                    label="首行为标题  仅支持预览前10条数据"
                    v-model="state.ruleForm.firstRowIsHeader"
                    @change="conversionFn(false)"
                  />
                </div>
              </div>
            </n-form-item>
          </div>
          <div v-if="state.previewFile" class="file-data-view">
            <n-public-table
              v-show="state.ruleForm.collectFileName"
              v-loading="state.loading"
              class="overflow-auto"
              :isDisplayAction="false"
              :table-head-titles="state.fileTableHeadTitles"
              :tableHeight="'auto'"
              :maxHeight="240"
              :tableData="state.fileTableData"
              :showPagination="false"
              :key="state.key"
            />
          </div>
          <n-form-item
            v-if="state.previewFile"
            v-show="state.fileTableData && state.fileTableData.list?.length"
            label="数据转换："
            field="isConvertToStructured"
          >
            <n-checkbox
              :isShowTitle="false"
              label="转换为结构化数据"
              v-model="state.ruleForm.isConvertToStructured"
              @change="conversionFn(true)"
            />
          </n-form-item>
        </div>
        <div class="box-bottom-title">
          <span>表信息</span>
        </div>
        <div v-show="state.fileInfo" class="mid-line">
          <n-form-item field="confidentialityLevel" label="文件密级：">
            <n-select
              v-model="state.ruleForm.confidentialityLevel"
              placeholder="请选择"
              filter
              allow-clear
              :options="state.confidentialityLevelOptions"
            />
          </n-form-item>
          <n-form-item field="tagList" label="标签：">
            <div class="mid-line-tag">
              <n-select
                v-model="state.ruleForm.tagList"
                :options="state.targetOptions"
                allow-clear
                multiple
                placeholder="请选择"
                :key="state.targetKey"
              />
              <n-button color="primary">
                <n-popover :position="['left']" :offset="16" :key="state.popoverKey">
                  <template #content>
                    <div class="custom">
                      <div class="custom-title">
                        自定义标签
                        <SvgIcon
                          class="icon"
                          icon="icon-close"
                          title="关闭"
                          @click.prevent.stop="changeTargetFn(false)"
                        />
                      </div>
                      <div class="custom-name">
                        <n-input
                          v-model="state.customName"
                          :maxLength="8"
                          placeholder="请输入标签名称"
                        />
                      </div>
                      <div class="custom-color"
                        >随机颜色<n-switch v-model="state.customColor" size="sm"
                      /></div>
                      <div v-if="!state.customColor" class="custom-color">
                        <div
                          v-for="(item, index) in state.colorList"
                          :key="index"
                          :style="'background-color:' + item.value"
                          :class="{ 'custom-color-label': true, checked: item.checked }"
                          @click.prevent.stop="checkFn(index)"
                        >
                          <SvgIcon class="icon" icon="icon-check-white" title="选中" />
                        </div>
                      </div>
                      <div class="custom-footer">
                        <n-button
                          variant="text"
                          color="primary"
                          size="mini"
                          @click.prevent.stop="changeTargetFn(false)"
                          >取消</n-button
                        >
                        <n-button
                          variant="solid"
                          size="mini"
                          @click.prevent.stop="changeTargetFn(false, true)"
                          >确定</n-button
                        >
                      </div>
                    </div>
                  </template>
                  <div @click.prevent.stop="changeTargetFn(true)"
                    ><SvgIcon class="icon" icon="icon-target-label" title="标签" />自定义标签</div
                  >
                </n-popover>
              </n-button>
            </div>
          </n-form-item>
        </div>
        <div v-if="state.ruleForm.isConvertToStructured" class="center-line">
          <div class="center-line-box left-top"></div>
          <div class="center-line-box right-top"></div>
          <div class="center-line-box left-bottom"></div>
          <div class="center-line-box right-bottom"></div>
        </div>
        <div v-show="state.ruleForm.isConvertToStructured">
          <div class="box-bottom-title">
            <span>设置目标表</span>
          </div>
          <div v-if="state.targetTable" class="top-line">
            <n-form-item class="check-style" label="目标表：" field="sinkModelCreateMethod">
              <n-radio-group
                direction="row"
                v-model="state.ruleForm.sinkModelCreateMethod"
                @change="modelCreatedModeChange"
              >
                <n-radio value="NEW">已有数据表</n-radio>

                <n-radio value="REVERSE">新建数据表</n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              v-if="
                state.ruleForm.sinkModelCreateMethod === 'NEW' &&
                state.ruleForm.isConvertToStructured
              "
              label="选择表："
              field="modeTable"
            >
              <n-select
                v-model="state.ruleForm.modeTable"
                placeholder="请选择"
                filter
                allow-clear
                :key="state.key"
                :disabled="state.disabled"
                :options="state.modeTableOptions"
                @value-change="modeTableChange"
              />
              <!-- <n-option
                  v-for="item in state.modeTableOptions"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                /> -->
            </n-form-item>
          </div>

          <div v-if="state.targetTable && state.ruleForm.isConvertToStructured" class="mid-line">
            <n-form-item field="cnName" label="模型中文名称：">
              <n-input
                :disabled="state.ruleForm.sinkModelCreateMethod === 'NEW'"
                v-model="state.ruleForm.cnName"
                maxlength="30"
                placeholder=""
              />
            </n-form-item>
            <n-form-item field="name" label="模型英文名称：">
              <n-input
                :disabled="state.ruleForm.sinkModelCreateMethod === 'NEW'"
                v-model="state.ruleForm.name"
                maxlength="80"
                placeholder=""
              />
            </n-form-item>
          </div>
          <n-form-item label="覆盖模式：" field="overlayOrNot">
            <n-checkbox
              :isShowTitle="false"
              label="清空已有表数据"
              v-model="state.ruleForm.overlayOrNot"
            />
          </n-form-item>
          <div class="box-bottom-title">
            <SvgIcon
              v-if="state.fieldMapping"
              icon="table-arrow-down"
              title="展开"
              @click="expandTitleFn('fieldMapping')"
            />
            <SvgIcon
              v-else
              class="noExpand"
              icon="table-arrow-down"
              title="收缩"
              @click="expandTitleFn('fieldMapping')"
            />
            <span>字段映射</span>
            <i></i>
          </div>
          <!--          <n-button-->
          <!--            class="intellect"-->
          <!--            v-if="state.ruleForm.sinkModelCreateMethod === 'REVERSE'"-->
          <!--            color="primary"-->
          <!--            ><SvgIcon class="icon" icon="icon-intellect" title="智能" />智能字段生成</n-button-->
          <!--          >-->
          <div v-show="state.fieldMapping" class="bottom-line" v-loading="state.loading">
            <!-- 连线映射 -->
            <div
              class="link-mapping-box"
              v-show="
                (state.ruleForm.sinkModelCreateMethod === 'NEW' || state.disabled) &&
                (state.leftData.length || state.rightData.length)
              "
            >
              <!-- <SvgIcon
                v-if="state.leftData.length && state.rightData.length"
                class="maximize"
                icon="maximize-icon"
                @click.prevent="maximizeLinkMapping"
              /> -->
              <sceneTableLinkDetail v-if="state.disabled" ref="sceneTableLink" :oneline="true" />
              <sceneTableLink v-else ref="sceneTableLink" :oneline="true" />
            </div>
            <!-- 表格逆向 -->
            <div
              v-show="state.ruleForm.sinkModelCreateMethod !== 'NEW' && !state.disabled"
              class="model-reverse-box"
            >
              <div class="reverse-box-left child">
                <div class="table-title"
                  >数据源：{{ state.ruleForm.dataSourceName
                  }}{{ state.activeFileItem?.name ? ' - ' + state.activeFileItem?.name : '' }}</div
                >
                <n-public-table
                  :key="state.key"
                  :isDisplayAction="false"
                  :table-head-titles="state.tableHeadTitles"
                  :showPagination="false"
                  :tableHeight="state.tableHeight"
                  :tableData="state.tableLeftData"
                  :configData="state.configData"
                  :isNeedSelection="true"
                  :editDisabled="false"
                  rowKey="number"
                  ref="leftTableDom"
                  @handle-selection-change="handleSelectionChange"
                />
              </div>
              <div class="reverse-box-right child">
                <div class="table-title">目的表：{{ state.ruleForm.name }}</div>
                <n-public-table
                  rowKey="number"
                  :isDisplayAction="false"
                  :table-head-titles="state.tableHeadTitles1"
                  :showPagination="false"
                  :tableHeight="state.tableHeight"
                  :tableData="state.tableRightData"
                  :isNeedSelection="false"
                >
                  <!-- 中文名 -->
                  <template #cnName="{ editor }">
                    <n-input
                      :disabled="state.disabled"
                      v-model="editor.row.cnName"
                      placeholder=""
                      maxlength="30"
                      :key="state.tableRightKey"
                      @blur="cNameBlur(editor.row)"
                    />
                  </template>
                  <!-- 英文名 value -->
                  <template #name="{ editor }">
                    <n-input
                      v-model="editor.row.name"
                      :class="!editor.row.isPass ? 'required-input' : ''"
                      placeholder=""
                      :disabled="state.disabled"
                      :key="state.tableKey[editor.rowIndex][0]"
                      maxlength="80"
                      @blur="eNameBlur(editor)"
                    />
                  </template>
                  <!-- 字段类型 -->
                  <template #fieldType="{ editor }">
                    <n-select
                      v-model="editor.row.fieldType"
                      placeholder="请选择"
                      :disabled="state.disabled"
                      :key="state.tableRightKey"
                      @change="fieldTypeChange(editor.row)"
                    >
                      <n-option
                        v-for="item in state.fieldTypeOptions"
                        :key="item.name"
                        :name="item.cnName + '(' + item.name + ')'"
                        :value="item.name"
                      />
                    </n-select>
                  </template>
                  <!-- 字段长度 -->
                  <template #fieldLength="{ editor }">
                    <n-input
                      :key="state.tableKey[editor.rowIndex][1]"
                      v-model="editor.row.fieldLength"
                      :class="
                        editor.row.fieldLength &&
                        editor.row.fieldLength > 0 &&
                        editor.row.fieldLength.toString().indexOf('.') === -1
                          ? ''
                          : 'required-input'
                      "
                      type="number"
                      placeholder=""
                      :disabled="state.disabled || editor.row.isRequiredFieldLength"
                      @blur="fieldBlur(editor)"
                      @keydown="onKeydownPositiveInteger($event, editor.row.fieldLength)"
                      @keyup="onKeyupPositiveInteger($event, editor.row.fieldLength)"
                      @focus="inputFocus($event)"
                      :onpaste="
                        () => {
                          return false
                        }
                      "
                    />
                  </template>
                </n-public-table>
              </div>
            </div>
          </div>
        </div>
      </n-form>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, nextTick, watch } from 'vue'

  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'

  import sceneTableLink from '../scene-table-link'
  import sceneTableLinkDetail from '../scene-table-link/detail'
  import { treeFind } from '@/utils/validate'
  import {
    checkCName,
    checkNameExcludeKeywords,
    onKeydownPositiveInteger,
    onKeyupPositiveInteger,
  } from '@/utils/validate'

  export default {
    name: '',
    components: { sceneTableLink, sceneTableLinkDetail },
    props: {
      envType: {
        type: String,
        default: null,
      },
    },
    emit: ['dataSourceTypeChange'],
    setup(props, { emit }) {
      const sceneTableLink = ref()
      const sceneTableLinkFullPage = ref()
      const leftTableDom = ref()
      const state = reactive({
        fileInfo: true,
        previewFile: true,
        targetTable: true,
        fieldMapping: true,
        showPreviewContent: false,
        org_dataSourceType: [],
        fileTreeData: [],
        type: 'add',
        allExcelData: [],
        key: 1,
        tableRightKey: 1,
        tableData: {},
        tableHeight: 280,
        configData: { selectRow: [] },
        loading: false,
        disabled: false, // 创建状态才能编辑模型
        ruleForm: {
          treeId: '', //存储组树id
          dataSourceId: '',
          treeName: '',
          fileUrl: '',
          dataSourceName: '',
          collectFileName: '', //选择的文件路径名
          dataSourceTable: '', //数据源id下表名
          confidentialityLevel: '',
          tagList: [],
          sinkModelCreateMethod: 'NEW',
          modeTable: '',
          modeFeign: '',
          modeFeignName: '',
          sinkModelId: '',
          modeTableName: '',
          overlayOrNot: true,
          cnName: '', // 模型中文名
          name: '', // 模型英文名
          isConvertToStructured: false, // 转化
          fileEncoding: 'UTF-8', // 文件编码
          firstRowIsHeader: true, // 是否首行表头
        },
        rules: {
          treeId: [
            { type: 'number', required: true, message: '请选择对象存储目录', trigger: 'change' },
          ],
          dataSourceId: [
            { type: 'number', required: true, message: '请选择数据源', trigger: 'change' },
          ],
          // fileUrl: [{ required: true, validator: checkCRelativePath, trigger: 'blur' }],
          // fileType: { required: true, message: '请选择', trigger: 'change' },
          collectFileName: { required: true, message: '请选择', trigger: 'change' },
          dataSourceTable: { required: true, message: '请选择', trigger: 'change' },
          confidentialityLevel: { required: true, message: '请选择', trigger: 'change' },
          cnName: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'model', 'validModel', {
                  nameType: 'CN',
                  name: state.ruleForm.cnName,
                  id: state.ruleForm.modeTable || null,
                }),
              trigger: 'blur',
            },
          ],
          name: [
            {
              required: true,
              validator: (...args) =>
                checkNameExcludeKeywords(...args, null, 'model', 'validModel', {
                  nameType: 'EN',
                  name: state.ruleForm.name,
                  id: state.ruleForm.modeTable || null,
                }),
              trigger: 'blur',
            },
          ],
          modeTable: [{ type: 'number', required: true, message: '请选择', trigger: 'change' }],
          sinkModelCreateMethod: [{ required: true, message: '请选择目标表', trigger: 'change' }],
        },
        fileTableHeadTitles: [{ prop: 'number', name: '序号', width: 80 }],
        fileTableData: {},
        customColor: '',
        confidentialityLevelOptions: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ],
        colorList: [
          { value: '#1F84E1', bgColor: '#EDF5FD', checked: true },
          { value: '#23BD4E', bgColor: '#EDFAF1', checked: false },
          { value: '#FFA800', bgColor: '#FFF6E5', checked: false },
          { value: '#5D5FEF', bgColor: '#F2F2FE', checked: false },
          { value: '#B7C1C7', bgColor: '#F9FAFB', checked: false },
        ],
        targetKey: 1,
        popoverKey: 1,
        targetOptions: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'oldcnName', name: '中文名' },
          { prop: 'oldname', name: '英文名' },
          { prop: 'oldfieldType', name: '字段类型' },
          { prop: 'oldfieldLength', name: '字段长度' },
        ],
        tableHeadTitles1: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '字段中文名', slot: 'cnName' },
          { prop: 'name', name: '字段英文名', slot: 'name' },
          { prop: 'fieldType', name: '字段类型', slot: 'fieldType' },
          { prop: 'fieldLength', name: '字段长度', slot: 'fieldLength' },
        ],

        modeTableOptions: [],
        sourceTableData: [],
        authorizedTableData: [],
        allData: {
          name: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [1, 1],
          authorizedPage: [1],
        },
        queryData: '',
        editId: null,
        detailId: null, // 查看模式id

        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },

        dataSourceOptions: [],
        fileCheckListOptions: [
          {
            name: 'excel',
            value: 'EXCEL',
          },
          {
            name: 'csv',
            value: 'CSV',
          },
        ],
        dataSourceTableOptions: [],

        tableLeftData: {
          list: [],
        }, // 左侧表格数据
        tableRightData: {
          list: [],
        }, // 右侧表格数据
        leftData: [], // 映射左侧数据
        rightData: [], // 映射右侧数据
        totalData: {}, // 所有数据
        activeModeTableItem: {}, // 选中的数据模型table
        activeDataSourceItem: {}, // 选中的数据源item
        activeFileItem: {}, // 选中的文件item

        layerId: null, //模型层id
        layerName: '', //模型层中文名
        //字段类型枚举
        fieldTypeOptions: [],
        checkPrames: ['name', 'fieldType', 'fieldLength'], //需校验的字段
        selectTableData: [],
        tableKey: [[]],
        showFirstHeader: true,
      })
      const ruleForm = ref()
      const router = useRouter()

      const methods = {
        // 标签输入完成
        changeTargetFn(flag, isSave) {
          if (isSave) {
            if (state.customName) {
              if (methods.customNameBlur()) {
                state.ruleForm.tagList.push(state.customName)
                let checkColorItem = state.colorList.filter((val) => val.checked)[0]
                if (state.customColor) {
                  let index = Math.floor(Math.random() * 5)
                  checkColorItem = state.colorList[index]
                }
                state.targetOptions.push({
                  name: state.customName,
                  value: state.customName,
                  color: checkColorItem.value + '_' + checkColorItem.bgColor,
                })
                state.targetKey++
              } else {
                return false
              }
            } else {
              ElNotification({
                title: '提示',
                message: '请输入标签名称！',
                type: 'warning',
              })
              return false
            }
          }
          if (flag) {
            state.customName = ''
            state.colorList = state.colorList.map((val, ind) => {
              if (ind === 0) {
                val.checked = true
              } else {
                val.checked = false
              }
              return val
            })
          } else {
            state.popoverKey++
          }
        },
        // 自定义标签输入验证
        customNameBlur() {
          let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
          let res = regex.test(state.customName)
          if (res && state.customName.length > 1) {
            if (state.targetOptions.filter((val) => val.name === state.customName).length === 0) {
              return true
            } else {
              ElNotification({
                title: '提示',
                message: '标签名重复，请重新填写！',
                type: 'warning',
              })
              return false
            }
          } else {
            ElNotification({
              title: '提示',
              message: '标签为2-8个字符，支持中文和英文！',
              type: 'warning',
            })
            return false
          }
        },
        // 选中颜色
        checkFn(index) {
          state.colorList.forEach((val, ind) => {
            val.checked = false
            if (index === ind) {
              val.checked = true
            }
          })
        },
        // 获取标签列表
        getTargetList() {
          api.dataManagement.fileUploadLocalTagList().then((res) => {
            let { success, data } = res
            if (success) {
              state.targetOptions = data.map((val) => {
                return { ...val, value: val.name }
              })
            }
          })
        },
        expandTitleFn(name) {
          state[name] = !state[name]
        },
        // 获取非结构化数据源
        getDataSourceList(type) {
          api.dataManagement
            .getDatasourceFeignList({ datasourceType: type, envType: props.envType })
            .then((res) => {
              let { data } = res
              let _org_dataSourceType = []

              data.forEach((item) => {
                _org_dataSourceType.push({
                  name: item.name,
                  value: item.id,
                  dataSourceType: item.datasourceType,
                })
              })
              state.org_dataSourceType = _org_dataSourceType
            })
        },

        //存储目录change
        treeSelectChange(data) {
          if (data?.data) {
            let item = treeFind(state.fileTreeData, state.ruleForm.treeId)
            state.ruleForm.treeName = item.name
          } else {
            state.ruleForm.treeName = ''
            state.ruleForm.treeId = ''
          }
          state.key++
        },
        // 数据源change
        dataSourceChange(data) {
          if (data) {
            let _item = state.org_dataSourceType.filter((item) => item.value === data.value)
            state.activeDataSourceItem = _item[0]
            state.ruleForm.dataSourceName = _item[0].name
            emit('dataSourceTypeChange', { dataSourceType: _item[0].dataSourceType })
          } else {
            state.ruleForm.dataSourceName = ''
            emit('dataSourceTypeChange', { dataSourceType: '' })
          }
          methods.getSftpCollectFiles()
        },
        //提取路径change
        // fileUrlChange() {
        //   methods.getSftpCollectFiles()
        // },
        // 文件类型-change
        fileCheckListChange() {
          methods.getSftpCollectFiles()
        },
        //调用sftp采集文件接口获取文件列表
        getSftpCollectFiles() {
          state.key++
          state.ruleForm.collectFileName = ''
          state.dataSourceOptions = []
          state.activeFileItem = {}
          methods.clearTableData()
          // if (methods.checkPath(state.ruleForm.fileUrl)) {
          if (state.ruleForm.dataSourceId) {
            //调用接口
            api.dataManagement
              .collectFileScan({
                collectFilePath: '',
                collectFileType: '',
                dataSourceId: state.ruleForm.dataSourceId,
              })
              .then((res) => {
                let { data, success } = res
                if (success) {
                  let _options = []
                  data.forEach((item) => {
                    _options.push({
                      value: item.name,
                      name: item.path,
                      cName: item.name,
                      path: item.path,
                      size: item.size,
                    })
                  })
                  state.dataSourceOptions = _options
                }
              })
          }
          // }
        },
        //校验路径
        checkPath(value) {
          let regex = /^([a-zA-Z\/]){2,200}$/
          let res = regex.test(value)
          let passed = false
          if (res) {
            let a = value.split('/')
            a.forEach((item, index) => {
              if (item === '' && index === 0) {
                passed = true
              } else {
                if (item === '') {
                  passed = false
                }
              }
            })
          }
          return passed
        },

        //获取存储目录树数据
        collectJobTreeList() {
          api.dataManagement.collectJobTreeList().then((res) => {
            let { success, data } = res
            state.fileTreeData = []
            if (success) {
              //过滤掉最父级
              let _data = data[0].children || []
              state.fileTreeData = methods.setTreeRecursion(_data)
            }
          })
        },
        setTreeRecursion(data) {
          data.map((item) => {
            item.value = item.id
            item.label = item.name
            if (item.children && item.children.length != 0) {
              this.setTreeRecursion(item.children)
            }
          })
          return data
        },

        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
          state.tableRightData = {
            list: data,
          }
          data?.forEach((item, index) => {
            state.tableKey[index] = [1, 2]
          })
          state.tableRightKey++
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },
        onKeydown(e) {
          let key = e.key
          let number = '1234567890'
          if (number.includes(key) || key === 'Backspace') {
            if (state.form.tokenExpireIncrementDays.toString().length === 1 && key === '0') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          } else {
            e.returnValue = false
          }
        },

        cNameBlur(data) {
          let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,30}$/
          let res = regex.test(data.cnName)
          data.isPass2 = false
          if (res && data.cnName?.length > 1 && data.cnName?.length < 31) {
            data.isPass2 = true
          }
          // state.tableRightKey++
        },

        //字段英文校验
        eNameBlur(editor) {
          let { row } = editor
          let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
          let res = regex.test(row.name)
          row.isPass = false
          if (res && row.name?.length > 1 && row.name?.length < 81) {
            row.isPass = true
          }

          state.tableKey[editor.rowIndex][0] = state.tableKey[editor.rowIndex][0] + 1
          state.configData.selectRow = state.tableRightData.list
        },
        fieldBlur(editor) {
          editor.row.fieldLength = Number(editor.row.fieldLength.toString().replace(/\D/g, ''))
          state.tableKey[editor.rowIndex][1] = state.tableKey[editor.rowIndex][1] + 1
          state.configData.selectRow = state.tableRightData.list
        },
        //校验中文
        checkCName(val) {
          let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,30}$/
          let res = regex.test(val)
          let isPass = false
          if (res && val?.length > 1 && val?.length < 31) {
            isPass = true
          }
          return isPass
        },
        //校验英文
        checkEName(val) {
          let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
          let res = regex.test(val)
          let isPass = false
          if (res && val?.length > 1 && val?.length < 81) {
            isPass = true
          }
          return isPass
        },

        // 获取字段类型
        getFieldTypeList() {
          api.model.getFieldType({}).then((res) => {
            state.fieldTypeOptions = res.data
            state.tableRightKey++
            state.configData.selectRow = state.tableRightData.list
          })
        },
        // 字段类型改变
        fieldTypeChange(item) {
          let _item = state.fieldTypeOptions.filter((list) => list.name === item.fieldType)
          item.fieldTypeName = _item[0]?.cnName || '字符串'
          methods.canInputWithFieldType(item)
        },

        //根据字段类型设置长度精度是否可编辑
        canInputWithFieldType(item) {
          switch (item.fieldType) {
            case 'BOOLEAN':
            case 'TIMESTAMP':
            case 'DATE':
              item.isRequiredFieldLength = true
              item.length = '64'
              // item.descriptionDisabled = true
              // item.description = '0'
              break
            case 'DECIMAL':
            case 'INTEGER':
              item.isRequiredFieldLength = false
              // item.descriptionDisabled = false
              break
            case 'VARCHAR':
              item.isRequiredFieldLength = false
              item.length = '64'
              // item.descriptionDisabled = true
              // item.description = '0'
              break
          }
        },

        // 获取数据源-列表
        getDatasourceFeignList() {
          state.ruleForm.dataSourceTable = ''
          api.dataManagement.getDatasourceFeignList({}).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.name = item.name
              item.value = item.id
            })
            state.dataSourceOptions = data
            state.tableData = {}
            state.leftData = []
          })
        },
        //  文件解析
        fileParsing() {
          // let _fileName = state.ruleForm.collectFileName.toUpperCase().split('.') //去名字后缀
          // state.ruleForm.cnName = _fileName[0]

          // state.totalData.collectFileType = state.ruleForm.fileType
          if (state.ruleForm.sinkModelCreateMethod === 'REVERSE') {
            state.ruleForm.cnName = state.activeFileItem?.cName.toUpperCase().split('.')[0] || ''
          }

          state.totalData.collectFileName = state.activeFileItem?.cName || ''
          state.totalData.collectFilePath = state.activeFileItem?.path || ''
          state.totalData.sourceTableName = state.activeFileItem?.cName || ''
          state.totalData.collectFileSize = state.activeFileItem?.size || 0
          methods.getSourceTables()
        },
        // 根据数据源获取对应数据--获取后端返回的excel内容
        getSourceTables() {
          state.loading = true
          let box_width = document.querySelectorAll('.structure-ruleForm')[0].clientWidth
          api.dataManagement
            .fileDataPreview({
              fileName: state.activeFileItem?.cName || null,
              fileEncoding: state.ruleForm.fileEncoding || null,
              fileFrom: 'SFTP',
              ftpSourceFilePath: state.totalData.collectFilePath,
              ftpSourceId: state.ruleForm.dataSourceId,
            })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                let _leftData = []
                let _tableHeadTitles = []
                let tableData = []
                if (data.jsonData) {
                  state.ruleForm.firstRowIsHeader = false
                  state.showFirstHeader = false
                  data.jsonData.forEach((val) => {
                    Object.keys(val).forEach((key) => {
                      if (Object.prototype.toString.call(val[key]) === '[object Object]') {
                        val[key] = JSON.stringify(val[key])
                      }
                    })
                  })
                  Object.keys(data.jsonData[0]).forEach((key, index) => {
                    _tableHeadTitles.push({
                      prop: key,
                      name: key,
                      width: box_width / 6,
                    })
                    _leftData.push({
                      colName: '',
                      length: '',
                      comment: key,

                      fieldType: 'STRING',
                      fieldTypeName: '字符串',
                      cnName: key,
                      name: '',
                      fieldLength: '64',
                      number: index + 1,
                      isRequiredFieldLength: false,
                      isPass: true,

                      oldname: '',
                      oldcnName: key,
                      oldfieldType: '',
                      oldfieldLength: '',
                    })
                  })
                  tableData = [...data.jsonData]
                } else if (data.tableLikeData) {
                  state.showFirstHeader = true
                  tableData = data.tableLikeData.map((val) => {
                    let obj = {}
                    val.forEach((v, i) => {
                      obj['col_' + (i + 1)] = v
                    })
                    return obj
                  })
                  data.tableLikeData[0].forEach((key, index) => {
                    let cName = key
                    if (!state.ruleForm.firstRowIsHeader) {
                      cName = 'col_' + (index + 1)
                    }
                    _tableHeadTitles.push({
                      prop: 'col_' + (index + 1),
                      name: cName,
                      width: box_width / 6,
                    })

                    _leftData.push({
                      colName: '',
                      length: '',
                      comment: cName,

                      fieldType: 'STRING',
                      fieldTypeName: '字符串',
                      cnName: cName,
                      name: '',
                      fieldLength: '64',
                      number: index + 1,
                      isRequiredFieldLength: false,
                      isPass: true,

                      oldname: '',
                      oldcnName: cName,
                      oldfieldType: '',
                      oldfieldLength: '',
                    })
                  })
                }
                if (state.ruleForm.firstRowIsHeader) {
                  tableData = tableData.slice(1)
                }
                state.fileTableHeadTitles = _tableHeadTitles
                state.allExcelData = tableData

                state.fileTableData = { list: tableData }
                state.configData.selectRow = state.tableRightData.list
                state.leftData = _leftData
                state.tableLeftData = { list: _leftData }
                state.key++
                state.tableRightKey++

                setTimeout(() => {
                  methods.domUpdata()
                })
              }
            })
            .catch(() => {
              state.loading = false
            })
        },

        //切换数据变化时候-重新渲染
        domUpdata() {
          if (state.ruleForm.sinkModelCreateMethod === 'NEW') {
            methods.canvasUpdate()
          }
        },

        //选择已有数据表时候 - 画布重新渲染
        canvasUpdate() {
          sceneTableLink.value.clearEdge() //清空连线
          sceneTableLink.value.changTable({
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [
              state.activeDataSourceItem?.name || '',
              state.activeFileItem?.name || '',
            ],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
          })
        },
        // 选取文件change
        fileChange(data) {
          let _item = state.dataSourceOptions.filter((item) => item.value === data.value)
          state.activeFileItem = _item[0]
          state.totalData.collectFileName = state.activeFileItem?.cName || ''
          state.totalData.collectFilePath = state.activeFileItem?.path || ''
          state.totalData.sourceTableName = state.activeFileItem?.cName || ''
          state.totalData.collectFileSize = state.activeFileItem?.size || 0
          state.key++
          methods.clearTableData()
          if (data) {
            let fileName = state.activeFileItem.path.substring(
              state.activeFileItem.path.lastIndexOf('/') + 1,
            )
            let suffix = fileName.split('.')[1]
            if (suffix === 'csv' || suffix === 'xls' || suffix === 'xlsx' || suffix === 'json') {
              state.showPreviewContent = true
              methods.fileParsing()
            } else {
              state.showPreviewContent = false
            }
          } else {
            state.showPreviewContent = false
          }
        },
        // 数据转化变换
        conversionFn(flag = false) {
          methods.clearTableData(flag)
          methods.fileParsing()
        },
        //目标表radio change
        modelCreatedModeChange(val) {
          if (val === 'NEW') {
            state.ruleForm.name = state.activeModeTableItem[0]?.name
            state.ruleForm.cnName = state.activeModeTableItem[0]?.cnName || ''
            nextTick(() => {
              methods.canvasUpdate()
            })
          } else {
            state.ruleForm.name = ''
            state.ruleForm.cnName = ''
          }
        },
        // 模型-change
        modeTableChange(data) {
          if (data) {
            state.activeModeTableItem = state.modeTableOptions.filter((item) => {
              return item.id === data.value
            })
            state.key++
            state.ruleForm.modeTableName = state.activeModeTableItem[0].name
            state.ruleForm.name = state.activeModeTableItem[0].name
            state.ruleForm.cnName = state.activeModeTableItem[0].cnName

            api.model.getModeData({ id: state.ruleForm.modeTable }).then((res) => {
              // 渲染右边表格

              state.rightData = res.data
              if (!res.data.length) return

              setTimeout(() => {
                methods.canvasUpdate()
              }, 100)
            })
          } else {
            state.ruleForm.name = ''
            state.ruleForm.cnName = ''
            state.rightData = []
            methods.canvasUpdate()
          }
        },
        //清空 table左右侧数据和勾选项目
        clearTableData(flag = false) {
          if (!flag) {
            state.fileTableData = { list: [] }
          }

          state.leftData = []
          methods.canvasUpdate()
          // state.ruleForm.name = ''
          if (!state.ruleForm.modeTable) {
            state.ruleForm.cnName = ''
          }
          state.tableLeftData = { list: [] }
          state.tableRightData = { list: [] }
          leftTableDom.value?.clearSelection()
        },
        // 获取模型层下拉列表
        async getDataModelTree() {
          let result = await api.model.getDataModelTree().then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.name === '原始数据层') {
                state.ruleForm.modeFeign = item.id
              }
            })
            state.modeFeignOptions = data
            let a = methods.modeFeignChange({ value: state.ruleForm.modeFeign })
            return a
          })
          return result
        },
        // 模型层-change
        async modeFeignChange(data) {
          let _data = state.modeFeignOptions.filter((item) => {
            return item.id === data.value
          })
          state.ruleForm.modeFeignName = _data[0].name
          let params = {
            layerId: state.ruleForm.modeFeign,
            jobType: 'COLLECT_JOB',
            envType: props.envType,
          }

          if (state.editId) {
            params.jobId = state.editId
          }
          let result = await api.model.getModelListWithLayerId(params).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.cnName = item.cnName
              item.value = item.id
            })
            state.oldModeTableOptions = data
            state.modeTableOptions = data
            return { modeTableOptions: state.modeTableOptions }
          })
          return result
        },

        // 初始化数据
        async init(data) {
          // await methods.getDataModelTree()
          state.totalData = data
          if (state.editId) {
            let {
              collectFileName,
              sourceTableName,
              sinkModelEname,
              sinkModelCname,
              tableData,
              dataSourceName,
              sinkModelId,
              sinkModelCreateMethod,
              overlayOrNot,
            } = data

            state.ruleForm.overlayOrNot = overlayOrNot // 覆盖模式
            state.ruleForm.collectFileName = collectFileName // 数据源id
            state.ruleForm.dataSourceTable = sourceTableName //// 数据源下表名
            state.ruleForm.name = sinkModelEname //模型英文名
            state.ruleForm.cnName = sinkModelCname //模型中文名
            state.ruleForm.dataSourceName = dataSourceName
            state.ruleForm.modeTable = sinkModelId //模型ID
            state.ruleForm.sinkModelCreateMethod = sinkModelCreateMethod
            state.leftData = tableData
            methods.editInit(data)
          } else {
            let { name, description } = data
            state.allData.name = name
            state.allData.description = description
          }
        },
        //编辑初始化下拉框数据
        editInit(alldata) {
          //获取数据源列表
          api.dataManagement.getDatasourceFeignList({}).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.name = item.name
              item.value = item.id
            })
            state.dataSourceOptions = data
          })

          api.dataManagement
            .getSourceTables({ dataSourceId: state.ruleForm.collectFileName })
            .then((res) => {
              let { data } = res
              let _dataSourceTableOptions = []
              data.forEach((item) => {
                _dataSourceTableOptions.push({
                  label: item,
                  value: item,
                })
              })
              state.dataSourceTableOptions = _dataSourceTableOptions
            })

          if (alldata.sinkModelCreateMethod === 'REVERSE' && !state.disabled) {
            methods.editInitTable(alldata)
          } else {
            methods.editInitCanvas(alldata)
          }
        },

        // 编辑初始化数据-映射
        async editInitCanvas(data) {
          if (data) {
            let { name, sourceTableName, sinkModelEname, layerName } = data
            state.dataSourceNames = [name, sourceTableName]
            state.modelNames = [layerName, sinkModelEname]

            await api.dataManagement
              .getSourceStructure({
                id: data.dataSourceId,
                tableName: data.sourceTableName,
              })
              .then((res1) => {
                // 渲染左边表格
                state.leftData = res1.data
              })

            await api.model.getModeData({ id: data.sinkModelId }).then((res2) => {
              // 渲染右边表格
              state.rightData = res2.data
            })
            state.loading = false

            data.graphData = {
              leftData: state.leftData,
              rightData: state.rightData,
              dataSourceNames: state.dataSourceNames,
              modelNames: state.modelNames,
            }
            data.allEdge = data.columnMapping
            nextTick(() => {
              sceneTableLink.value.changTable(data.graphData, function (that) {
                let { methods } = that
                // 连线
                if (state.totalData.allEdge) {
                  methods.addEdge({ data: state.totalData.allEdge, initLater: true })
                }
                // data.graph.scrollToPoint(600, 150)
              })
            })
          }
        },
        // 编辑初始化数据-映射
        editInitTable(data) {
          if (data) {
            let { dataSourceId, sourceTableName, columnMapping } = data

            let _data2 = {
              dataSourceId: dataSourceId,
              tableName: sourceTableName,
            }

            api.model
              .reverseMetaList(_data2)
              .then((res) => {
                if (!res.data.length) return
                // 新增序号属性
                res.data.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                    isPass: true,
                    sourceColumnName: item.name,
                    sourceColumnType: item.fieldType,
                    sourceColumnLength: item.fieldLength,
                    sourceCName: item.cnName,
                  })
                })
                let _tableRightData = []
                res.data.forEach((item) => {
                  columnMapping.forEach((key) => {
                    if (item.name === key.sourceColumnName) {
                      //英文相同
                      item.cnName = key.sourceName
                      _tableRightData.push({ ...item })
                    }
                  })
                })

                state.tableRightData = { list: _tableRightData }
                state.configData.selectRow = _tableRightData
                state.tableLeftData = { list: res.data }
                state.key++
                state.tableRightKey++

                state.configData.selectRow = state.tableRightData.list
              })
              .catch(() => {
                state.loading = false
              })
          }
        },
        // 映射关系-回传数据
        async getLinkMappingAllData() {
          let passedLink = false
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })

          if (!_result.passed) {
            return _result
          }

          if (state.activeModeTableItem.length) {
            //原始模型层下模型下拉选中项
            state.totalData.sinkModelEname = state.activeModeTableItem[0].name
            state.totalData.sinkModelCname = state.activeModeTableItem[0].cnName
          }
          state.totalData.dataSourceId = state.ruleForm.dataSourceId
          state.totalData.dataSourceName = state.activeDataSourceItem?.name
          state.totalData.layerId = state.layerId // 模型层id
          state.totalData.layerName = state.layerName // 模型层中文名

          state.totalData.treeId = state.ruleForm.treeId // 模型层中文名
          state.totalData.treeName = state.ruleForm.treeName // 模型层中文名

          state.totalData.sinkModelId = state.ruleForm.modeTable // 模型id
          state.totalData.sinkModelCreateMethod = state.ruleForm.sinkModelCreateMethod // 建模方式

          let result = sceneTableLink.value.getGraphData()
          // 连线数据
          let _columnMapping = result.cells.filter((item) => {
            return item.shape === 'edge'
          })
          let _data = result.cells.filter((cell) => {
            return cell.shape === 'er-rect'
          })

          let alldata = []
          _data.forEach((item) => {
            alldata.push(...item.ports.items)
          })
          let needColumnMapping = []
          if (_columnMapping.length) {
            passedLink = true
            _columnMapping.forEach((item, index) => {
              needColumnMapping[index] = {}
              alldata.forEach((cell) => {
                if (item.source.port === cell.id) {
                  needColumnMapping[index].sourceColumnName = cell.attrs.comment.text
                  needColumnMapping[index].sourceName = cell.attrs.comment.text
                  needColumnMapping[index].sourceColumnType = 'STRING'
                  needColumnMapping[index].sourceColumnLength = '64'
                }
                if (item.target.port === cell.id) {
                  needColumnMapping[index].sinkMetaCode = cell.attrs.colName.text
                  needColumnMapping[index].sinkMetaType = cell.attrs.dataType.text
                  needColumnMapping[index].sinkMetaLength = cell.attrs.dataLength.text
                  needColumnMapping[index].sinkSortNum = cell.attrs.sortNum.text
                }
              })
            })
          } else {
            if (state.ruleForm.isConvertToStructured) {
              passedLink = false
              ElNotification({
                title: '提示',
                message: '请建立映射关系',
                type: 'warning',
              })
            } else {
              passedLink = true
            }
          }
          state.totalData.columnMapping = needColumnMapping
          state.totalData.graphData = {
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.name],
          }
          state.totalData.allEdge = _columnMapping
          state.totalData.overlayOrNot = state.ruleForm.overlayOrNot
          state.totalData.fileEncoding = state.ruleForm.fileEncoding
          state.totalData.firstRowIsHeader = state.ruleForm.firstRowIsHeader
          state.totalData.isConvertToStructured = state.ruleForm.isConvertToStructured
          state.totalData.confidentialityLevel = state.ruleForm.confidentialityLevel
          if (state.ruleForm?.tagList?.length > 0) {
            state.totalData.tagList = state.targetOptions.filter((val) =>
              state.ruleForm.tagList.some((item) => item === val.name),
            )
          }
          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid && passedLink, data: state.totalData })
            })
          })
          return _result2
        },

        // 逆向-回传数据
        async getModelReverseAllData() {
          let _contented = true
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              if (valid) {
                if (state.tableRightData.list.length) {
                  let isPass = true
                  let isPass3 = true
                  // let isPass2 = true

                  state.tableRightData.list.forEach((item) => {
                    state.checkPrames.forEach((key) => {
                      if (!('' + item[key]).length) {
                        _contented = false
                      }
                    })
                    let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
                    let res = regex.test(item.name)
                    if (res && item.name?.length > 1 && item.name?.length < 81) {
                      item.isPass = true
                    } else {
                      _contented = false
                      item.isPass = false
                      isPass = false
                    }
                    if (item.fieldLength && item.fieldLength > 0) {
                      let _string = item.fieldLength + ''
                      if (_string.indexOf('.') !== -1) {
                        isPass3 = false
                        _contented = false
                      }
                    } else {
                      isPass3 = false
                      _contented = false
                    }
                  })
                  if (!isPass) {
                    ElNotification({
                      title: '提示',
                      message: '支持英文、数字、下划线，只能以英文开头，2~80个字符',
                      type: 'warning',
                    })
                  }
                  if (!isPass3) {
                    ElNotification({
                      title: '提示',
                      message: '字段长度仅支持正整数',
                      type: 'warning',
                    })
                  }
                } else {
                  ElNotification({
                    title: '提示',
                    message: '空数据',
                    type: 'warning',
                  })
                  _contented = false
                }
              }

              resolve({ passed: valid && _contented, data: { sinkModelCreateMethod: '' } })
            })
          })

          if (!_result.passed) {
            return _result
          }

          state.totalData.sinkModelEname = state.ruleForm.name //模型英文名
          state.totalData.sinkModelCname = state.ruleForm.cnName //模型中文名
          state.totalData.dataSourceId = state.ruleForm.dataSourceId
          state.totalData.layerId = state.layerId // 模型层id
          state.totalData.layerName = state.layerName // 模型层中文名
          state.totalData.sinkModelCreateMethod = state.ruleForm.sinkModelCreateMethod // 建模方式
          state.totalData.treeId = state.ruleForm.treeId // 模型层中文名
          state.totalData.treeName = state.ruleForm.treeName // 模型层中文名

          let needColumnMapping = []
          state.tableRightData.list.forEach((item, index) => {
            needColumnMapping[index] = {}
            needColumnMapping[index].sourceColumnName = item.oldcnName
            needColumnMapping[index].sourceColumnType = 'STRING'
            needColumnMapping[index].sourceColumnLength = '64'

            needColumnMapping[index].sinkMetaCode = item.name
            needColumnMapping[index].sinkMetaType = item.fieldType
            needColumnMapping[index].sinkMetaLength = item.fieldLength
            needColumnMapping[index].sinkSortNum = index

            needColumnMapping[index].sourceName = item.cnName
          })

          state.totalData.columnMapping = needColumnMapping
          state.totalData.tableData = state.tableRightData.list
          state.totalData.dataSourceName = state.ruleForm.dataSourceName
          state.totalData.overlayOrNot = state.ruleForm.overlayOrNot
          state.totalData.fileEncoding = state.ruleForm.fileEncoding
          state.totalData.firstRowIsHeader = state.ruleForm.firstRowIsHeader
          state.totalData.isConvertToStructured = state.ruleForm.isConvertToStructured
          state.totalData.confidentialityLevel = state.ruleForm.confidentialityLevel
          // state.totalData.graphData = {
          //   dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
          //   modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
          // }
          if (state.ruleForm?.tagList?.length > 0) {
            state.totalData.tagList = state.targetOptions.filter((val) =>
              state.ruleForm.tagList.some((item) => item === val.name),
            )
          }

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid, data: state.totalData })
            })
          })
          return _result2
        },
        async getAllData() {
          let result = {}
          if (state.ruleForm.sinkModelCreateMethod === 'NEW') {
            //映射
            result = await methods.getLinkMappingAllData()
          } else {
            //逆向
            result = await methods.getModelReverseAllData()
          }
          return result
        },
        //获取默认模型信息
        getOriginLayer() {
          api.model.getOriginLayer().then((res) => {
            let { success, data } = res
            if (success) {
              state.layerId = data.id
              state.layerName = data.name
            }
          })
        },
        //环境改变 初始化数据
        initDataWithEnvType() {
          state.ruleForm.dataSourceId = ''
          state.ruleForm.collectFileName = ''
          state.dataSourceOptions = []
          state.ruleForm.modeTable = ''
          state.modeTableOptions = []
          state.org_dataSourceType = []
          state.leftData = []
          state.rightData = []
          state.ruleForm.name = ''
          state.ruleForm.cnName = ''
          methods.getDataSourceList('SFTP')
          methods.getDataModelTree()
          methods.clearTableData()
        },
      }

      //监听环境变化  --变化后清空数据
      watch(
        () => props.envType,
        () => {
          methods.initDataWithEnvType()
        },
      )

      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.editId = state.queryData.editId
        state.detailId = state.queryData.detailId

        state.disabled =
          state.queryData.status && state.queryData.status !== 'CREATED' ? true : false
        // methods.getDatasourceFeignList()
        // if (!(state.editId || state.detailId)) {
        //   methods.getDataModelTree()
        // }

        methods.getDataModelTree()
        methods.getOriginLayer()
        methods.getFieldTypeList()
        methods.collectJobTreeList()
        methods.getDataSourceList('SFTP')
        methods.getTargetList()
      })

      return {
        state,
        ruleForm,
        leftTableDom,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        sceneTableLink,
        sceneTableLinkFullPage,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f4f4f4;
  $outBg: #eeeeee;
  @import '@/styles/variables.scss';

  .data-collection-link-mapping {
    height: 100%;
    background-color: $outBg;

    .center-line {
      position: relative;
      left: -16px;
      width: calc(100% + 32px);
      height: 8px;
      margin-bottom: 8px;
      background-color: #d8e0f4;

      &-box {
        position: absolute;
        width: 16px;
        height: 16px;
        background-color: #d8e0f4;

        &:before {
          position: absolute;
          width: 16px;
          height: 16px;
          background-color: #fff;
          content: '';
        }

        &.left-top {
          top: -16px;
          left: 0;

          &:before {
            border-radius: 0 0 0 8px;
          }
        }

        &.right-top {
          top: -16px;
          right: 0;

          &:before {
            border-radius: 0 0 8px 0;
          }
        }

        &.left-bottom {
          bottom: -16px;
          left: 0;

          &:before {
            border-radius: 8px 0 0 0;
          }
        }

        &.right-bottom {
          right: 0;
          bottom: -16px;

          &:before {
            border-radius: 0 8px 0 0;
          }
        }
      }
    }

    .box-bottom {
      height: 100%;
      background-color: $contentBoxBg;
      border-radius: 0 0 4px;

      .structure-ruleForm {
        display: flex;

        flex-direction: column;

        .nancalui-form__item--horizontal {
          margin-bottom: 16px;
        }

        :deep(.nancalui-select--disabled) {
          .nancalui-select__arrow {
            display: none;
          }
        }

        .title {
          height: 22px;
          margin: 2px 0 10px 0;
          color: #333333;
          font-weight: bolder;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          line-height: 22px;
        }

        .file-code {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          &-title {
            color: rgba(0, 0, 0, 0.75);
            font-size: 14px;
          }

          .illustrate {
            position: absolute;
            top: 2px;
            left: -84px;
            color: #8091b7;

            &:hover {
              color: $themeBlue;
            }
          }
        }

        .top-line {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 0 16px;
          box-sizing: border-box;
          width: 100%;

          .nancalui-form__item--horizontal {
            flex: 1;
          }
        }

        .file-data-view {
          min-height: 200px;
          margin-bottom: 16px;

          :deep(.overflow-auto) {
            .nancalui-table__header-wrapper,
            .nancalui-table__scroll-view {
              display: inline-table;
            }

            .nancalui-table__fix-header {
              overflow: auto !important;

              &::-webkit-scrollbar {
                width: 5px;
                height: 5px;
              }

              &::-webkit-scrollbar-thumb {
                background-color: #e1e1e1;
                border-radius: 3px;
              }
            }
          }
        }

        .mid-line {
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          padding: 0 16px;
          box-sizing: border-box;
          width: 100%;

          :deep(.nancalui-form__item--horizontal) {
            flex: 1;
          }

          &-tag {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
            max-width: 430px;

            :deep(.nancalui-select) {
              width: calc(100% - 110px);
              margin-right: 4px;

              .nancalui-select__multiple {
                max-height: 72px;
                overflow-y: auto;

                .nancalui-tag {
                  flex-shrink: 0;
                }
              }
            }

            .nancalui-button {
              padding: 0 8px;

              .button-content {
                display: flex;
                align-items: center;
                justify-content: center;

                .icon {
                  margin-right: 4px;
                  font-size: 16px;
                }
              }
            }
          }
        }

        .nancalui-tree-select,
        .nancalui-select,
        .nancalui-input {
          max-width: 430px;
        }
      }

      .intellect {
        margin-bottom: 12px;

        :deep(.button-content) {
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            margin-right: 4px;
            font-size: 16px;
          }
        }
      }

      &-title {
        margin-bottom: 16px;
        height: 30px;
        line-height: 30px;
        color: #2b71c2;
        font-size: 14px;
        background-color: #f2f6fc;
        padding-left: 14px;
        position: relative;

        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }
      }

      .bottom-line {
        position: relative;
        padding-bottom: 16px;

        .link-mapping-box {
          height: 360px;
        }

        .model-reverse-box {
          display: flex;
          height: 360px;
          //padding: 16px 20px;
          //border: 1px solid #ebedf0;
          border-radius: 8px;

          .child:not(:last-child) {
            padding-right: 20px;
          }

          .child:last-child {
            padding-left: 20px;
          }

          .child {
            width: 50%;

            .table-title {
              margin-bottom: 8px;
              color: #000000;
              font-weight: bolder;
              font-size: 12px;
              font-family: PingFangSC-Medium, PingFang SC;
              line-height: 20px;
            }
          }
        }

        .reverse-box-left {
          :deep(.nancalui-table) {
            .nancalui-table__thead tr th.is-left:first-child .header-container {
              width: max-content;
              padding-left: 16px !important;
            }
          }
        }

        :deep(.nancalui-table) {
          .nancalui-table__header-wrapper tr th .header-container {
            padding: 0 8px;
          }

          .nancalui-table__thead tr th.is-left:first-child {
            width: fit-content;
          }

          tbody > tr > td.is-left:first-child {
            width: max-content;
            padding: 0 8px 0 16px;
          }

          tbody > tr > td {
            height: 40px;

            &:not(:first-child) {
              padding: 5px 8px;
            }
          }

          .nancalui-table__empty {
            padding: 10px 0;
          }

          .nancalui-input {
            width: 100%;

            &.required-input {
              border: 1px solid red;
              border-radius: 4px;

              .nancalui-input__wrapper {
                background-color: transparent;
                border: none;
              }
            }
          }

          .nancalui-select {
            width: 100%;
          }
        }
      }
    }
  }

  .custom {
    padding: 6px;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 14px;

      .icon {
        color: #8091b7;
        font-size: 16px;
        cursor: pointer;
      }
    }

    &-name {
      margin-top: 8px;
    }

    &-color {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 12px;
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;

      .nancalui-switch {
        margin-left: 6px;
      }

      &-label {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 4px;

        .icon {
          display: none;
          color: #fff;
          font-size: 12px;
        }

        &.checked {
          box-shadow: 0 0 0 1px #447dfd;

          .icon {
            display: block;
          }
        }
      }
    }

    &-footer {
      margin-top: 14px;
      text-align: right;

      .nancalui-button {
        min-width: 40px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }
</style>
