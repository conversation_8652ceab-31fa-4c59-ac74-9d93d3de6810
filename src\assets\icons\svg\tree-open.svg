<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>文件展开</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="指标管理" transform="translate(-124.000000, -272.000000)">
            <g id="内容" transform="translate(80.000000, 164.000000)">
                <g id="树结构" transform="translate(0.000000, 52.000000)">
                    <g id="编组-21" transform="translate(16.000000, 48.000000)">
                        <g id="文件/展开" transform="translate(28.000000, 8.000000)">
                            <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                            <g id="编组-18" transform="translate(1.000000, 2.000000)" stroke-width="1" stroke="currentColor">
                                <path d="M10.9141575,12 L1.01346038,12 C0.453726212,12 0,11.5523 0,11 L0,1 C0,0.4477 0.453726212,0 1.01346038,0 L4.72120518,0 C4.9702124,0 5.21050386,0.0904 5.3961698,0.2541 L7.08956075,1.7459 C7.27532804,1.9096 7.51561949,2 7.76462671,2 L12.9862786,2 C13.6145227,2 14.0913558,2.5582 13.9851452,3.1691 L13.5972939,5.4" id="路径"></path>
                                <path d="M1.81693177,5.7253 L0.369102271,10.7253 C0.184145751,11.3641 0.670302696,12 1.34354443,12 L11.3970714,12 C11.8495815,12 12.2472633,11.704 12.3715136,11.2747 L13.8193431,6.2747 C14.0042996,5.6359 13.5181426,5 12.8449009,5 L2.79137393,5.0001 C2.33886387,5.0001 1.94118201,5.296 1.81693177,5.7253 Z" id="路径"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
