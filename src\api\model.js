import request from '@/utils/request'
import MODEL from '@/const/urlAddress'
//--------------------------------------------------------------------资产注册--------------------------------------------------------
export function registerAssets(data) {
  return request({
    url: `${MODEL.MODEL}/model/register-assets`,
    method: 'post',
    data: data,
  })
}
//统计数据
function getAssetsRegistryCount(data) {
  return request({
    url: `${MODEL.MODEL}/model/count-by-layer`,
    method: 'get',
    params: data,
  })
}
// -------------------------------------------------------------------模型管理------------------------------------------------------------------------------
/*
总览
 */
export function getOverView(data) {
  return request({
    url: `${MODEL.MODEL}/overview`,
    method: 'get',
    params: data,
  })
}

// -------------------------------------------------------------------1.主题域管理------------------------------------------------------
/*
导入主题域
 */
export function importSubJect(data) {
  return request({
    url: `${MODEL.MODEL}/subject/import`,
    method: 'post',
    data: data,
  })
}

// -------------------------------------------------------------------2.元数据管理------------------------------------------------------
/*
导入元数据
 */
export function importMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/import/excel`,
    method: 'post',
    data: data.file,
  })
}
function fileTemplate(data) {
  return request({
    // url: `/fileTemplateUrl/excel-template/download`,
    url: `${MODEL.MODEL}/excel-template/download`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}
function fileTemplateUrl() {
  return request({
    url: `/fileTemplateUrl/data-govern/template/govern-data-model/metadata-import-template.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3ROS65CGF5GN5AQE3VXC%2F20220928%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220928T014707Z&X-Amz-Expires=604800&X-Amz-Security-Token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NLZXkiOiIzUk9TNjVDR0Y1R041QVFFM1ZYQyIsImV4cCI6MTY2NDMzMzE3MiwicGFyZW50IjoibWluaW8ifQ.mQJfaDsS_Yf9oL5JO1TsSxa9zRnCHVOLpsTgURtBgcC7mHI8QHCFT3np35ETrZf4PuEWBeqmbjKb9QXqY2wbWA&X-Amz-SignedHeaders=host&versionId=null&X-Amz-Signature=99bfe6bf83e2c89dab3da10a6eae7b7d50ae2a6656402dbedbe1ac44a1d7c873`,
    method: 'get',
    // responseType: 'blob',
  })
}

/*
  获取元数据列表
 */
export function getMetaList(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/search`,
    method: 'post',
    data: data,
  })
}

/*
  新增元数据
 */
export function addMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/create`,
    method: 'post',
    data: data,
  })
}

/*
  修改元数据
 */
export function eidtMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/update`,
    method: 'post',
    data: data,
  })
}

/*
  删除元数据
 */
export function deleteMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}

/*
  查看元数据详情
 */
export function checkMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/detail/${data.id}`,
    method: 'get',
    params: data,
  })
}
/*
  查看元数据引用模型
 */
export function checkMetaModel(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/bound-models/${data.id}`,
    method: 'get',
    params: data,
  })
}
/*
  申请标准
 */
export function standardMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/standard/${data.id}`,
    method: 'get',
  })
}

/*
  逆向元数据
 */
export function reverseMeta(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/reverse`,
    method: 'post',
    data: data,
  })
}
// 导入元数据模版下载
function fileDownload(data) {
  return request({
    url: `${MODEL.MODEL}/model/excel/template`,
    method: 'get',
    responseType: 'blob',
    params: data,
  })
}

/*
字段类型列表
 */
export function getFieldType(data) {
  return request({
    url: `${MODEL.MODEL}/model/field/type/list`,
    method: 'get',
    params: data,
  })
}

export function getMetaCount() {
  return request({
    url: `${MODEL.MODEL}/metadata/count`,
    method: 'get',
  })
}
export function getMetaIncremental(data) {
  return request({
    url: `${MODEL.MODEL}/metadata/daily-incremental`,
    method: 'get',
    params: data,
  })
}
// 增加元数据校验(不重复true)
export function validMetadata(data) {
  return request({
    url: `${MODEL.MODEL}/model/create/valid/metadata`,
    method: 'post',
    data: data,
  })
}
// 增加模型校验(不重复true)
export function validModel(data) {
  return request({
    url: `${MODEL.MODEL}/model/create/valid/model`,
    method: 'post',
    data: data,
  })
}
// 增加模型校验(不重复true)
export function validModelNew(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/create/valid/model`,
    method: 'post',
    data: data,
  })
}

// -------------------------------------------------------------------3.数据模型管理------------------------------------------------------
//数据模型是否删除或者被引用
export function getModelsStudioValid(data) {
  return request({
    url: `${MODEL.MODEL}/model/studio/valid`,
    method: 'post',
    data,
  })
}
export function getModelCount() {
  return request({
    url: `${MODEL.MODEL}/model/count-by-project`,
    method: 'get',
  })
}
export function getModelIncremental(data) {
  return request({
    url: `${MODEL.MODEL}/model/daily-incremental`,
    method: 'get',
    params: data,
  })
}
/*
  查看发布详情
 */
export function checkRelease(data) {
  return request({
    url: `${MODEL.MODEL}/publish/${data.type}/${data.id}`,
    method: 'get',
    params: data,
  })
}

/*
  版本发布
 */
export function releaseEdition(data) {
  return request({
    url: `${MODEL.MODEL}/publish/${data.type}/release`,
    method: 'post',
    data: data,
  })
}

/*
  获取数据模型列表
 */
export function getDataModelList(data) {
  return request({
    url: `${MODEL.MODEL}/model/search`,
    method: 'post',
    data: data,
  })
}

/*
  画布作业搜索模型列表
 */
export function getCanvasModelList(data) {
  return request({
    url: `${MODEL.MODEL}/model/studio/search`,
    method: 'post',
    data: data,
  })
}
/*
  画布作业搜索模指标模型列表
 */
export function getCanvasMerticModelList(data) {
  return request({
    url: `${MODEL.MODEL}/model/mertic/studio/search`,
    method: 'post',
    data: data,
  })
}

/*
  新增数据模型
 */
export function createDataModal(data) {
  return request({
    url: `${MODEL.MODEL}/model/create`,
    method: 'post',
    data: data,
  })
}

/*
    修改数据模型
   */
export function EditDataModal(data) {
  return request({
    url: `${MODEL.MODEL}/model/update`,
    method: 'post',
    data: data,
  })
}

/*
    删除数据模型
   */
export function DeleteDataModal(data) {
  return request({
    url: `${MODEL.MODEL}/model/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}

/*
    数据模型详情
   */
// export function getModalDetail(data) {
//   return request({
//     url: `${MODEL.ASSETS}/assets/detail/model`,
//     method: 'get',
//     params: data,
//   })
// }
export function getModalDetail(data) {
  return request({
    url: `${MODEL.MODEL}/model/detail/${data.id}`,
    method: 'get',
    params: {},
  })
}

/*
  获取模型分层树
 */
export function getDataModelTree(data) {
  return request({
    url: `${MODEL.MODEL}/layer/list`,
    method: 'get',
    params: data,
  })
}

/*
  获取除原始层以外的模型层
 */
export function getDataModelWithoutOrigin(data) {
  return request({
    url: `${MODEL.MODEL}/layer/list/withoutOrigin`,
    method: 'get',
    params: data,
  })
}

/*
  血缘关系-获取模型分层树
 */
export function getApproveModelTree() {
  return request({
    url: `${MODEL.MODEL}/layer/tree`,
    method: 'get',
    // params: data,
  })
}

/*
  新增模型分层
 */
export function addDataModelTree(data) {
  return request({
    url: `${MODEL.MODEL}/layer/create`,
    method: 'post',
    data: data,
  })
}

/*
  修改模型分层
 */
export function updateDataModelTree(data) {
  return request({
    url: `${MODEL.MODEL}/layer/update`,
    method: 'post',
    data: data,
  })
}

/*
  删除模型分层
 */
export function delDataModelTree(data) {
  return request({
    url: `${MODEL.MODEL}/layer/delete/${data.id}`,
    method: 'post',
  })
}

/*
  逆向数据模型
 */
export function modelReverse(data) {
  return request({
    url: `${MODEL.MODEL}/model/create/withFields`,
    method: 'post',
    data: data,
  })
}
/*
  逆向批量校验关联元数据
 */
export function metaReverseCheck(data) {
  return request({
    url: `${MODEL.MODEL}/model/create/valid/metadata/batch`,
    method: 'post',
    data: data,
  })
}
/**
 *
查询指定数据源数据库的表结构
 */
export function reverseMetaList(data) {
  return request({
    url: `${MODEL.MODEL}/model/table/structure/${data.dataSourceId}/${data.tableName}`,
    method: 'get',
  })
}

/*
  查看维度/事实/汇总表
 */
export function checkDataModal(data) {
  return request({
    url: `${MODEL.MODEL}/model/get`,
    method: 'get',
    params: data,
  })
}

// -------------------------------------------------------------------4.数据字典------------------------------------------------------
/*
  查看详情
 */
export function getDictionaryDetail(data) {
  return request({
    url: `${MODEL.MODEL}/dictionary/${data.type}/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 获取主题域树第一层
function getSubjectTreeOne(data) {
  return request({
    url: `${MODEL.MODEL}/subject/trees/one`,
    method: 'get',
    params: data,
  })
}

// 获取主题域树
function getSubjectTree(data) {
  return request({
    url: `${MODEL.MODEL}/subject/trees/all`,
    method: 'get',
    params: data,
  })
}

// 新增自定义分层
function AddSubjectTree(data) {
  return request({
    url: `${MODEL.MODEL}/subject/layer/insert`,
    method: 'post',
    data: data,
  })
}

// 根据模型层查询模型列表
function getModelListWithLayerId(data) {
  return request({
    url: `${MODEL.MODEL}/model/search/${data.layerId}`,
    method: 'get',
    params: data,
  })
}
// 根据模型层查询模型列表-过滤掉已经注册为资产的
function getModelListWithLayerIdNonAssets(data) {
  return request({
    url: `${MODEL.MODEL}/model/search/${data.layerId}/non-assets`,
    method: 'get',
    params: data,
  })
}

// 查询某个模型的元数据列表
function getModeData(data) {
  return request({
    url: `${MODEL.MODEL}/model/fields`,
    method: 'get',
    params: data,
  })
}
// 查询某个模型的元数据列表-带分页
function getModelDataList(data) {
  return request({
    url: `${MODEL.MODEL}/model/fields/page`,
    method: 'post',
    data: data,
  })
}

// 查询某个模型的字段列表不分页
function getModelFieldsList(data) {
  return request({
    url: `${MODEL.MODEL}/model/fields/list`,
    method: 'post',
    data: data,
  })
}

// 模型注册资产模型数量
function getAssetModelCount(data) {
  return request({
    url: `${MODEL.MODEL}/feign/model/reg/asset/model/count`,
    method: 'get',
    params: data,
  })
}

// 根据场景code查询模型列表
function getModeDataWithProject(data) {
  return request({
    url: `${MODEL.MODEL}/model/list/${data.projectCode}`,
    method: 'get',
    params: data,
  })
}

function getDataWithProject(data) {
  return request({
    url: `${MODEL.ASSETS}/model/data/query`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data,
  })
}

// 模型的元数据列表, 按id列表查询
function getMetadataList(data) {
  return request({
    url: `${MODEL.MODEL}/model/fields`,
    method: 'get',
    params: data,
  })
}
//模型日志
function getTaskLog(data) {
  return request({
    url: `${MODEL.MODEL}/view/instance/log`,
    method: 'get',
    params: data,
  })
}

// 数据开发新建模型
function workReverseWithMetadata(data) {
  return request({
    url: `${MODEL.MODEL}/model/save`,
    method: 'post',
    data,
  })
}
//结构化数据采集 获取原始层分层信息
function getOriginLayer(data) {
  return request({
    url: `${MODEL.MODEL}/layer/getOriginLayer`,
    method: 'get',
    params: data,
  })
}

// 查询某个模型的字段列表
function searchModelFieldList(data) {
  return request({
    url: `${MODEL.MODEL}/model/fields`,
    method: 'get',
    params: data,
  })
}
// -------------------------------------------------------------------5.指标管理------------------------------------------------------
// 指标重名检验
function indicatorIsExist(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/is-exist`,
    method: 'post',
    data,
  })
}
// 指标计算函数
function indicatorCalcFunction() {
  return request({
    url: `${MODEL.MODEL}/indicator/calc-function`,
    method: 'get',
  })
}
// 获取主题树列表-扁平化
function indicatorThemeOneLineList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/list`,
    method: 'post',
    data,
  })
}
// 获取主题树+指标树
function indicatorThemeTreeList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/indicator-subject/tree-list`,
    method: 'post',
    data,
  })
}
// 获取模型表字段树
function indicatorModelTableList(data) {
  return request({
    url: `${MODEL.MODEL}/model/indicator/source/tree-list`,
    method: 'post',
    data,
  })
}

// 获取主题树列表
function indicatorSubjectList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/subject/list`,
    method: 'post',
    data,
  })
}

// 新增主题树
function indicatorSubjectSave(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/subject/save`,
    method: 'post',
    data,
  })
}

// 修改主题树
function indicatorSubjectUpdate(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/subject/update`,
    method: 'post',
    data,
  })
}

// 删除主题树
function indicatorSubjectDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/subject/delete/${data.id}`,
    method: 'post',
  })
}

// 获取指标列表
function indicatorList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/list`,
    method: 'post',
    data,
  })
}
// 获取非复合指标
function nonCompositeIndicatorList() {
  return request({
    url: `${MODEL.MODEL}/indicator/non-composite-indicator`,
    method: 'get',
  })
}

// 新增指标
function addIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/save`,
    method: 'post',
    data,
  })
}

// 修改指标
function updateIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/update`,
    method: 'post',
    data,
  })
}

// 删除指标
function indicatorDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/delete/${data.id}`,
    method: 'post',
  })
}

// 获取指标详情
function indicatorDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/get/${data.id}`,
    method: 'get',
  })
}

// 下架指标
function indicatorOffline(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/offline/${data.id}`,
    method: 'get',
  })
}

// 上架指标
function indicatorPublish(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/publish/${data.id}`,
    method: 'get',
  })
}
// 指标审批撤回
function indicatorCancelApproval(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/cancelApproval/${data.id}`,
    method: 'get',
  })
}

//-----------指标模型------------
// 获取指标模型列表
function indicatorModelList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/list`,
    method: 'post',
    data,
  })
}
// 获取指标模型树结构
function getIndicatorModelTree() {
  return request({
    url: `${MODEL.MODEL}/indicator/model/layer/list`,
    method: 'get',
  })
}
// 获取指标模型-新增-指标树结构
function getTargetTree() {
  return request({
    url: `${MODEL.MODEL}/indicator/tree/list`,
    method: 'get',
  })
}
// 获取指标模型-新增-维度树结构
function getLatitudeTree() {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/dimension/tree-list`,
    method: 'get',
  })
}
// 新增指标模型
function addIndicatorModel(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/save`,
    method: 'post',
    data,
  })
}

// 修改指标模型
function updateIndicatorModel(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/update`,
    method: 'post',
    data,
  })
}

// 删除指标模型
function indicatorModelDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/delete`,
    method: 'post',
    data,
  })
}

// 获取指标模型详情
function indicatorModelDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/get/${data.id}`,
    method: 'get',
  })
}

// 下架指标模型
function indicatorModelOffline(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/audit/offline`,
    method: 'post',
    data,
  })
}

// 上架指标模型
function indicatorModelPublish(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/audit/publish`,
    method: 'post',
    data,
  })
}
// 撤回指标模型
function indicatorModelRecall(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/audit/rollback`,
    method: 'post',
    data,
  })
}

//----------指标体系----------------
// 获取指标体系主题树列表
function indicatorThemeList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/tree`,
    method: 'get',
    params: data,
  })
}
// 新增指标体系主题树
function indicatorThemeSave(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/save`,
    method: 'post',
    data,
  })
}

// 修改指标体系主题树
function indicatorThemeUpdate(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/update`,
    method: 'post',
    data,
  })
}

// 删除指标体系主题树
function indicatorThemeDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/delete`,
    method: 'get',
    params: data,
  })
}
//查询指标下的指标
function indicatorThemeTarget(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/mounted`,
    method: 'get',
    params: data,
  })
}
//查询指标下的指标
function indicatorThemeCheckGroupName(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/theme/checkGroupName`,
    method: 'post',
    data,
  })
}

// --------------------------指标属性
// 指标属性-新增
function indicatorAttrSave(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/save`,
    method: 'post',
    data,
  })
}
// 指标属性-更新
function indicatorAttrUpdate(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/update`,
    method: 'post',
    data,
  })
}
// 指标属性-指标属性下架申请
function indicatorAttrOffline(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/offline/${data.id}`,
    method: 'get',
  })
}
// 指标属性-指标属性发布申请
function indicatorAttrPublish(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/publish/${data.id}`,
    method: 'get',
  })
}
// 指标属性-审批撤回
function indicatorAttrCancelApproval(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/cancelApproval/${data.id}`,
    method: 'get',
  })
}
// 指标属性-属性类别
function indicatorAttrCategory() {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/category`,
    method: 'get',
  })
}
// 指标属性-度量单位类别
function indicatorAttrDataUnitCategory() {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/data-unit-category`,
    method: 'get',
  })
}
// 指标属性-属性删除
function indicatorAttrDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/delete/${data.id}`,
    method: 'post',
    data,
  })
}
// 指标属性-属性详情查询
function indicatorAttrDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/get/${data.id}`,
    method: 'get',
  })
}
// 指标属性-属性分页查询
function indicatorAttrList(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/list`,
    method: 'post',
    data,
  })
}
// 指标属性-时间周期
function indicatorDateList() {
  return request({
    url: `${MODEL.MODEL}/indicator/time-period`,
    method: 'get',
  })
}

// 指标属性-时间周期描述形容词
function indicatorAttrTimePeriodDescriptive(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/time-period-descriptive-adjective`,
    method: 'get',
    data,
  })
}
// 指标属性-时间周期单位
function indicatorAttrTimePeriodUnit() {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/time-period-unit`,
    method: 'get',
  })
}
// 指标属性-类别度量单位项
function indicatorAttrUnitItems(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/unit-items`,
    method: 'get',
    params: data,
  })
}
// 指标属性-指标属性树列表
function indicatorAttrTreeList() {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/tree/list`,
    method: 'get',
  })
}
// 指标属性-修饰词来源用的模型列表
function indicatorSourceSearch(data) {
  return request({
    url: `${MODEL.MODEL}/model/indicator/source/search`,
    method: 'post',
    data,
  })
}
//指标属性修饰词来源用的模型的数据
function indicatorSourceDataQuery(data) {
  return request({
    url: `${MODEL.MODEL}/model/indicator/source/data/query`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data,
  })
}
//获取模型字段的范围最大最小值
function indicatorSourceDataQueryLimit(data) {
  return request({
    url: `${MODEL.MODEL}/model/indicator/source/data/query_limit`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data,
  })
}
//指标属性修饰词-中英文名验重
function indicatorAttrIsExist(data) {
  return request({
    url: `${MODEL.MODEL}/indicatorAttr/is-exist`,
    method: 'post',
    data,
  })
}
//-显示状态
function recordStatusEnumDisplay() {
  return request({
    url: `${MODEL.MODEL}/common/record-status-enum/display`,
    method: 'get',
  })
}

// --------------------------指标看板--------------------------
// 看板列表
function dashboardList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/page`,
    method: 'post',
    data,
  })
}

// 新建看板
function dashboardAdd(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/add`,
    method: 'post',
    data,
  })
}

// 编辑看板
function dashboardUpdate(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/update`,
    method: 'post',
    data,
  })
}

// 看板验重
function dashboardCheckName(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/checkName`,
    method: 'post',
    data,
  })
}

// 删除看板
function dashboardDelete(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/delete`,
    method: 'get',
    params: data,
  })
}
// 删除看板
function dashboardBindIcon(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/bindIcon`,
    method: 'post',
    data,
  })
}

// 看板查看列表
function dashboardDetailList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/indicator-target-items`,
    method: 'post',
    data,
  })
}

// 保存目标
function saveBoardTarget(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/target/save`,
    method: 'post',
    data,
  })
}
// 保存目标 new
function saveNewBoardTarget(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/target/new/save`,
    method: 'post',
    data,
  })
}

// 获取看板详情
function getBoardDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/get`,
    method: 'get',
    params: data,
  })
}

// 删除目标
function deleteBoardTarget(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/target/delete`,
    method: 'get',
    params: data,
  })
}

// 通过指标获取目标值
function getTargetValue(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/indicator/last/value`,
    method: 'get',
    params: data,
  })
}

// 指标追溯
function getDashboardRetrace(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/retrace/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 关注指标状态
function targetFollow(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/target/status/follow`,
    method: 'post',
    data,
  })
}

// 取消关注指标状态
function targetUnfollow(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/target/status/unfollow`,
    method: 'post',
    data,
  })
}

// 指标源数据
function getModelTableDataList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/model/data`,
    method: 'post',
    data,
  })
}

// 通过指标获取目标值new
function getAllTargetValue(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/model/indicator/new/last/value/${data.indicatorId}`,
    method: 'post',
    data,
  })
}

// 指标目标规则边界校验
function targetValidRule(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/indicator-target-rule-valid`,
    method: 'post',
    data,
  })
}

// 获取目标树列表
function boardTargetTreeList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/indicator-target-tree-list`,
    method: 'post',
    data,
  })
}
// 指标子目标总数统计
function getSubTargetTotals(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/dashboard/sub-target-totals`,
    method: 'post',
    data,
  })
}
// 指标唯一标识生成
function getIndicatorManageGenCode(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/genCode`,
    method: 'get',
    params: data,
  })
}
// 指标新增
function indicatorAdd(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/save`,
    method: 'post',
    data,
  })
}
// 指标更新
function indicatorUpdate(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/update`,
    method: 'post',
    data,
  })
}

// 指标分类列表
function getTargetCategoryList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/category/list`,
    method: 'get',
    params: data,
  })
}

// 指标分类用户列表
function getTargetCategoryUserList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/user/list/${data.categoryId}`,
    method: 'get',
  })
}

// 删除指标
function deleteTarget(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/delete`,
    method: 'get',
    params: data,
  })
}

// 指标分类新增管理者
function addTargetCategoryUser(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/user/save`,
    method: 'post',
    data,
  })
}

// 指标分类删除用户
function deleteTargetCategoryUser(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/user/delete/${data.id}`,
    method: 'post',
    data,
  })
}

// 指标分类新增
function addTargetCategory(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/category/save`,
    method: 'post',
    data,
  })
}

// 指标分类更改
function moveToCategory(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/moveToCategory`,
    method: 'post',
    data,
  })
}

// 指标分类删除
function deleteTargetCategory(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/category/delete/${data.id}`,
    method: 'post',
    data,
  })
}
// 获取指标列表
function getIndicatorList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/getList`,
    method: 'get',
    params: data,
  })
}

// 上线指标
function onlineIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/online`,
    method: 'get',
    params: data,
  })
}

// 下线指标
function offlineIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/offline`,
    method: 'get',
    params: data,
  })
}

// 刷新指标
function refreshIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/refresh`,
    method: 'post',
    data: data,
  })
}

// 更新指标所有者
function updateOwnerIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/updateOwner`,
    method: 'post',
    data: data,
  })
}

// 获取指标列表
function getCommonDims(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/getCommonDims`,
    method: 'post',
    data: data,
  })
}

// 业务域树查询
function getDomainTree(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/tree`,
    method: 'post',
    data: data,
  })
}

// 新建业务域
function addDomainTree(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/save`,
    method: 'post',
    data: data,
  })
}

// 编辑业务域
function updateDomainTree(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/update`,
    method: 'post',
    data: data,
  })
}

// 删除业务域
function deleteDomainTree(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 检查业务员树重命名
function checkDomainTreeName(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/checkGroupName`,
    method: 'post',
    data: data,
  })
}

// 查询业务域下指标定义引用信息
function queryUsedDomainTree(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/domain/queryUsed/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 主题域下指标定义列表
function getListByDomainId(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/getListByDomainId`,
    method: 'post',
    data: data,
  })
}

// 主题域下指标定义列表
function getListByDomain(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/getTreeByDomainId/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 新建指标定义
function saveDefinition(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/save`,
    method: 'post',
    data: data,
  })
}

// 编辑指标定义
function updateDefinition(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/update`,
    method: 'post',
    data: data,
  })
}

// 删除指标定义
function deleteDefinition(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 获取指标定义详情
function getDefinitionDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/get/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 查询指标定义是否被引用信息
function queryUsedDefinition(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/queryUsed/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 批量查询指标定义是否被引用
function queryUsedBatchDefinition(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/queryUsedBatch`,
    method: 'post',
    data: data,
  })
}

// 分页获取指标
function getTargetList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/list`,
    method: 'post',
    data,
  })
}
// 获取全部指标
function indicatorAll(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/allList`,
    method: 'post',
    data,
  })
}

// 获取指标折线图数据
function getTargetCanvasData(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/detail`,
    method: 'post',
    data,
  })
}

// 指标添加分类
function bindToCategory(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/bindToCategory`,
    method: 'post',
    data,
  })
}

// 克隆指标
function cloneIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/clone/${data.id}`,
    method: 'post',
    data,
  })
}

// 克隆指标
function getIndicatorResultList(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/view/result`,
    method: 'post',
    data,
  })
}

// 获取指标详情
function getIndicatorManageDetail(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/get`,
    method: 'get',
    params: data,
  })
}
// 仅获取基础指标
function getAtomicIndicator(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/atomic/indicator`,
    method: 'get',
    params: data,
  })
}
// 获取指标维度
function getIndicatorDimension(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/indicator/dimension`,
    method: 'get',
    params: data,
  })
}
// 指标定义模板文件下载
function templateFileDownload(params) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/export/template`,
    method: 'get',
    responseType: 'blob',
    params,
  })
}
// 指标定义数据导出
function targetDefineFileDownload(params) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/export/${params.domainId}`,
    method: 'get',
    responseType: 'blob',
    params,
  })
}
// 指标定义数据导入
function targetDefineFileImport(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/definition/import/${data.get('domainId')}`,
    method: 'post',
    data,
  })
}
// 复合表达式校验
function indicatorExpression(data) {
  return request({
    url: `${MODEL.MODEL}/indicator/manage/check/expression`,
    method: 'post',
    data,
  })
}

// -------------------------------------------------------------------指标数据集-----------------------------------------------------

// 指标数据集树获取
function getDatasetTreeList(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/group/tree`,
    method: 'post',
    data,
  })
}

// 数据集分组移动
function datasetGroupMove(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/group/move`,
    method: 'post',
    data,
  })
}

// 新增分组
function addGroup(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/group/save`,
    method: 'post',
    data,
  })
}

// 修改分组名
function updateGroup(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/group/update`,
    method: 'post',
    data,
  })
}

// 修改数据集名
function updateTableName(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/rename`,
    method: 'post',
    data,
  })
}

// 数据集移动
function datasetTableMove(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/move/${data.tableId}/${data.groupId}`,
    method: 'post',
    data,
  })
}

// 数据预览
function getTablePreview(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/preview`,
    method: 'post',
    data,
  })
}

// 字段预览
function getTableFieldPreview(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/field/${data.tableId}`,
    method: 'post',
    data,
  })
}

// 查询所有版本号
function getVersionList(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/version/findAll/${data.tableId}`,
    method: 'post',
    data,
  })
}

// 获取数据集详情
function getDatasetDetail(data) {
  return request({
    url: `${MODEL.MODEL}/dataset/table/detail/${data.id}`,
    method: 'post',
    data,
  })
}

export function removeTable(data) {
  return request({
    url: `${MODEL.MODEL}/table/manage/table/remove`,
    method: 'post',
    data,
  })
}

// 查询模型的数据-通过模型名字查询测试环境Hive数据
export function getModelData(data) {
  return request({
    url: `${MODEL.ASSETS}/model/data/query/preview`,
    method: 'post',
    data,
  })
}

export default {
  registerAssets,
  getAssetsRegistryCount,
  getMetaCount,
  getModelCount,
  getMetaIncremental,
  getModelIncremental,
  validMetadata,
  validModel,
  validModelNew,
  // 主题域模块
  getOverView,
  importSubJect,

  // 元数据模块
  getMetaList,
  addMeta,
  eidtMeta,
  importMeta,
  fileTemplate,
  fileTemplateUrl,
  deleteMeta,
  standardMeta,
  reverseMeta,
  fileDownload,
  checkMeta,
  checkMetaModel,
  getFieldType,
  // 数据模型模块
  getModelsStudioValid,
  checkRelease,
  releaseEdition,
  getDataModelList,
  getCanvasModelList,
  getDataModelTree,
  getDataModelWithoutOrigin,
  getModalDetail,
  getApproveModelTree,
  addDataModelTree,
  updateDataModelTree,
  delDataModelTree,

  reverseMetaList,
  metaReverseCheck,

  createDataModal,
  EditDataModal,
  DeleteDataModal,
  checkDataModal,
  modelReverse,
  getModeDataWithProject,
  // 数据字典模块
  getDictionaryDetail,
  getSubjectTreeOne,
  getSubjectTree,
  AddSubjectTree,
  getModelListWithLayerId,
  getModeData,

  getDataWithProject,
  getMetadataList,
  getTaskLog,
  workReverseWithMetadata,
  getOriginLayer,
  searchModelFieldList,
  getModelDataList,
  getModelFieldsList,
  getAssetModelCount,
  //指标管理
  nonCompositeIndicatorList,
  indicatorIsExist,
  indicatorCalcFunction,
  indicatorThemeOneLineList,
  indicatorThemeTreeList,
  indicatorModelTableList,
  indicatorSubjectList,
  indicatorSubjectSave,
  indicatorSubjectUpdate,
  indicatorSubjectDelete,
  indicatorThemeList,
  indicatorThemeSave,
  indicatorThemeUpdate,
  indicatorThemeDelete,
  indicatorThemeTarget,
  indicatorThemeCheckGroupName,
  indicatorList,
  addIndicator,
  updateIndicator,
  indicatorDelete,
  indicatorDetail,
  indicatorOffline,
  indicatorPublish,
  indicatorCancelApproval,
  indicatorModelList,
  getIndicatorModelTree,
  getTargetTree,
  getLatitudeTree,
  addIndicatorModel,
  updateIndicatorModel,
  indicatorModelDelete,
  indicatorModelDetail,
  indicatorModelOffline,
  indicatorModelPublish,
  indicatorModelRecall,
  getCanvasMerticModelList,
  getModelListWithLayerIdNonAssets,
  //指标属性
  indicatorAttrSave,
  indicatorAttrUpdate,
  indicatorAttrOffline,
  indicatorAttrPublish,
  indicatorAttrCancelApproval,
  indicatorAttrCategory,
  indicatorAttrDataUnitCategory,
  indicatorAttrDelete,
  indicatorAttrDetail,
  indicatorAttrList,
  indicatorDateList,
  indicatorAttrTimePeriodDescriptive,
  indicatorAttrTimePeriodUnit,
  indicatorAttrUnitItems,
  indicatorAttrTreeList,
  indicatorSourceSearch,
  indicatorSourceDataQuery,
  indicatorSourceDataQueryLimit,
  indicatorAttrIsExist,
  recordStatusEnumDisplay,
  dashboardList,
  dashboardAdd,
  dashboardUpdate,
  dashboardCheckName,
  dashboardDelete,
  dashboardDetailList,
  dashboardBindIcon,
  getSubTargetTotals,
  saveBoardTarget,
  saveNewBoardTarget,
  getBoardDetail,
  deleteBoardTarget,
  boardTargetTreeList,
  targetValidRule,
  getTargetValue,
  getDashboardRetrace,
  targetFollow,
  targetUnfollow,
  getModelTableDataList,
  getAllTargetValue,
  getIndicatorManageGenCode,
  indicatorAdd,
  indicatorUpdate,
  getTargetCategoryList,
  getTargetCategoryUserList,
  deleteTarget,
  addTargetCategoryUser,
  deleteTargetCategoryUser,
  addTargetCategory,
  moveToCategory,
  deleteTargetCategory,
  getIndicatorList,
  onlineIndicator,
  offlineIndicator,
  refreshIndicator,
  updateOwnerIndicator,
  getCommonDims,
  getDomainTree,
  addDomainTree,
  updateDomainTree,
  deleteDomainTree,
  checkDomainTreeName,
  queryUsedDomainTree,
  getListByDomainId,
  getListByDomain,
  saveDefinition,
  updateDefinition,
  deleteDefinition,
  getDefinitionDetail,
  queryUsedDefinition,
  queryUsedBatchDefinition,
  getTargetList,
  indicatorAll,
  getTargetCanvasData,
  bindToCategory,
  cloneIndicator,
  getIndicatorResultList,
  getIndicatorManageDetail,
  getAtomicIndicator,
  getIndicatorDimension,
  templateFileDownload,
  targetDefineFileDownload,
  targetDefineFileImport,
  indicatorExpression,
  getDatasetTreeList,
  datasetGroupMove,
  addGroup,
  updateGroup,
  updateTableName,
  datasetTableMove,
  getTablePreview,
  getTableFieldPreview,
  getVersionList,
  getDatasetDetail,
  getModelData,
}
