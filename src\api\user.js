import request from '@/utils/request'

function login(data) {
  return request({
    url: '/api/govern-management/login',
    // url: '/base/login',
    method: 'post',
    data: data,
  })
}
function singlelogin(data) {
  return request({
    url: `/api/govern-management/sso/toLogin?auth=${data.auth}`,
    method: 'post',
  })
}
// 通过cookies获取系统信息
function getInfoByCookies(data) {
  return request({
    url: '/api/govern-management/current/user/info',
    method: 'get',
    params: data,
  })
}
function getAppList(data) {
  return request({
    url: '/api/govern-management/app/list',
    method: 'get',
  })
}
function getAppMenu(data) {
  return request({
    url: '/api/govern-management/platform/current/user/info',
    method: 'get',
  })
}

export default {
  login,
  singlelogin,
  getAppList,
  getAppMenu,
  getInfoByCookies,
}
