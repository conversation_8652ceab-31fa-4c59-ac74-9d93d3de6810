<template>
  <div class="project-add container">
    <div class="project-title">
      <span>{{ state.pageStatus ? '编辑' : '新建' }}项目</span>
      <div class="goBack" size="sm" @click.prevent="cancel">返回</div>
    </div>

    <div class="project-info">
      <div class="title"><span class="line"></span><span>基础信息</span></div>
      <basic ref="basic" />
    </div>

    <div class="project-authPerson">
      <div class="title"><span class="line"></span><span>授权人员</span></div>
      <authorizedPersonnel ref="authorizedPersonnel" />
    </div>

    <div class="box-operate">
      <n-button class="cancel" size="sm" @click.prevent="cancel">取消</n-button>
      <n-button
        :loading="state.loading"
        class="save"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="next"
        >确定</n-button
      >
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, watch, getCurrentInstance } from 'vue'
  import customizeSteps from '@/components/customizeSteps'
  import basic from './components/basic'
  import linkDataSource from './components/link-data-source'
  import authorizedPersonnel from './components/authorized-personnel'
  import checkInformation from './detail'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  export default {
    name: '',
    components: { basic, linkDataSource, authorizedPersonnel, checkInformation, customizeSteps },
    props: {},

    setup() {
      const router = useRouter()
      const { proxy } = getCurrentInstance()
      const customizeSteps = ref()
      const basic = ref()
      const linkDataSource = ref()
      const authorizedPersonnel = ref()
      const checkInformation = ref()
      const store = useStore()
      let componentName = null

      const state = reactive({
        loading: false,
        allData: {
          basePage: {},
          dataSourcePage: [],
          authorizedPage: [],
        },
        editId: '',
        editData: {},
        isClick: false, //防止快速点击下一步
        formModel: {
          projectName: '',
          description: '',
        },
        pageStatus: true,
      })

      const rules = {
        projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
      }

      const methods = {
        // 下一步
        async next() {
          if (state.isClick) return
          let result = null

          // 校验是否通过
          const basicInfo = await basic.value.getAllData()
          console.log(basicInfo)

          if (basicInfo.passed) {
            // 名称唯一
            let _data = {
              name: basicInfo.data.projectName,
              id: state.editId || null,
            }
            state.isClick = true
            await api.project
              .projectNameValid(_data)
              .then((res) => {
                state.isClick = false
                let { data } = res

                if (data) {
                  ElNotification({
                    title: '提示',
                    message: '项目名称已存在',
                    type: 'warning',
                  })
                  return false
                }
              })
              .catch(() => {
                state.isClick = false
              })
          }

          state.allData.basePage = basicInfo.data

          result = authorizedPersonnel.value.getAllData()
          state.allData.authorizedPage = result
          if (result.length === 0) {
            ElNotification({
              title: '提示',
              message: '请勾选授权人员',
              type: 'warning',
            })
            return false
          }

          methods.save()
        },
        // 保存
        save() {
          let { basePage, dataSourcePage, authorizedPage } = state.allData
          // let dataSourceList = dataSourcePage.map((item) => {
          //   return item.id
          // })
          let personList = authorizedPage.map((item) => {
            return item.id
          })
          let data = {
            name: basePage.projectName,
            description: basePage.description,
            exportDataNum: basePage.exportDataNum,
            // dataSourceList,
            personList,
          }
          state.loading = true
          let interfaceUrlName = 'newProject'
          let _message = '新增成功'
          if (state.editId) {
            data.id = state.editId
            interfaceUrlName = 'projectUpdate'
            _message = '更新成功'
          }
          api.project[interfaceUrlName](data).then((res) => {
            let { success } = res
            state.loading = false
            if (success) {
              ElNotification({
                title: '提示',
                message: _message,
                type: 'success',
              })

              store.commit('user/ADD_PROJECT', true) // 更新项目列表

              router.push({ name: 'projectList', query: { refresh: 'true' } })
            }
          })
        },
        // 取消
        cancel() {
          let title = '是否确认取消新增？'
          let content = '取消后，已填写的内容将不被保存'
          if (state.editId) {
            title = '是否确认取消编辑？'
            content = '取消后，将放弃本次编辑操作'
          }
          proxy.$MessageBoxService.open({
            title,
            content,
            save: () => {
              router.back()
            },
          })
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.id // 获取路由传参
        state.pageStatus = router.currentRoute.value.query.type === 'EDIT' ? true : false
        if (state.editId) {
          // 查看
          api.project.getProjectDetail({ id: state.editId }).then((res) => {
            let { success, data } = res
            if (success) {
              state.editData = data
              basic.value.editInit(data)
              authorizedPersonnel.value.editInit(state.editData)
            }
          })
        }
      })

      return {
        state,
        rules,
        ...methods,
        customizeSteps,
        basic,
        linkDataSource,
        authorizedPersonnel,
        checkInformation,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $content: #fff;
  $activeFont: #fff;
  .project-add {
    overflow: auto;
    .project-title {
      height: 52px;
      line-height: 52px;
      background: #fff;
      border-radius: 2px;
      padding-right: 16px;
      margin-bottom: 10px;

      span {
        display: inline-block;
        height: 18px;
        line-height: 18px;
        border-left: 4px solid #1e89ff;
        padding-left: 12px;
        font-size: 16px;
        color: #1d2129;
      }
      .goBack {
        float: right;
        margin-top: 11px;
        display: inline-block;
        line-height: 30px;
        width: 62px;
        height: 30px;
        text-align: center;
        font-size: 14px;
        color: #1d2129;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        cursor: pointer;
        &:hover {
          border: 1px solid $themeBlue;
          color: $themeBlue;
        }
      }
    }

    .project-info {
      display: flex;
      padding-top: 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      align-self: stretch;
      background: #fff;
      margin-bottom: 10px;

      .title {
        display: flex;
        padding: 4px 0px;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        background: #f2f6fc;
        color: #2b71c2;

        /* 常用/r400/h9 */
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */

        .line {
          display: inline-block;
          width: 4px;
          height: 18px;
          background: #1e89ff;
        }
      }
    }

    .project-authPerson {
      display: flex;
      height: calc(100% - 367px);
      box-sizing: border-box;
      padding: 16px 0px;
      flex-direction: column;
      align-items: flex-start;
      align-self: stretch;
      background: #fff;

      .title {
        display: flex;
        padding: 4px 0px;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        background: #f2f6fc;
        color: #2b71c2;

        /* 常用/r400/h9 */
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */

        .line {
          display: inline-block;
          width: 4px;
          height: 18px;
          background: #1e89ff;
        }
      }
    }

    .add-box {
      height: 100%;
      // padding: 0 16px;
      border-radius: 2px;
      .out-box-steps {
        padding: 0 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;
      }
    }

    .box-operate {
      position: fixed;
      bottom: 12px;
      right: 12px;
      width: calc(100% - 244px);
      padding: 16px;
      text-align: right;
      background-color: $content;
      border-radius: 2px 2px 0 0;

      .nancalui-button.next,
      .nancalui-button.save {
        color: $activeFont;
        background-color: $themeBlue;
        border-radius: 2px;
      }
      .cancel {
        border-radius: 2px;
      }
    }
  }
</style>
