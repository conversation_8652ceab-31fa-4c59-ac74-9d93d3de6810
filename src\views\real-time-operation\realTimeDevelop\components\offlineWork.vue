<template>
  <div class="work-body" @click.prevent.stop="state.configPopupShow = false">
    <div class="work-body-btn">
      <div v-if="state.isRun" class="btn" @click.prevent.stop="stopFn">
        <SvgIcon class="icon" icon="icon-offline-stop" />
        停止
      </div>
      <div
        v-else
        class="btn active"
        :class="{ disabled: state.isDisabled }"
        @click.prevent.stop="runFn"
      >
        <SvgIcon class="icon" icon="icon-offline-start" />
        运行
      </div>
      <div v-loading="state.loading" class="work-body-box">
        <div class="btn" @click.prevent.stop="saveFn(false)">
          <SvgIcon class="icon" icon="icon-offline-save" />
          保存
        </div>
        <div class="btn" @click.stop.prevent="submitFn">
          <SvgIcon class="icon" icon="icon-offline-submit" />
          提交
        </div>
        <div class="btn" @click.prevent.stop="formatFn">
          <SvgIcon class="icon" icon="icon-offline-format" />
          格式化
        </div>
        <div class="btn" @click.prevent.stop="checkSqlFn">
          <SvgIcon class="icon" icon="icon-offline-check" />
          代码检查
        </div>
      </div>
      <div v-show="state.showSubmit" class="submit">已提交V{{ state.versionNum }}</div>
    </div>
    <div class="work-body-code">
      <div class="work-body-code-textarea" ref="textareaRef">
        <codemirror
          ref="myCm"
          v-model:value="state.sql"
          class="codemirror"
          :options="state.sqlOption"
          @ready="onCmReady"
          @focus="onCmFocus"
          @input="onCmCodeChange"
        />
      </div>
      <div
        :class="{
          'work-body-code-run': true,
          showRun: state.showRun,
        }"
        ref="codeRunEl"
      >
        <div class="drag-line" ref="el" style="cursor: n-resize; height: 10px"></div>
        <div class="work-body-code-run-tabs">
          <n-tabs v-model="state.runType">
            <n-tab id="log" title="运行日志" />
          </n-tabs>
          <div class="work-body-code-run-tabs-btn">
            <n-button
              v-if="state.runType === 'log'"
              color="primary"
              :disabled="!state.runLogText"
              @click.prevent.stop="downLogFn"
              >下载日志</n-button
            >
            <SvgIcon
              :class="{ icon: true, show: state.showRun }"
              icon="icon-arrow-second"
              @click.prevent.stop="showRunFn"
            />
          </div>
        </div>
        <div class="work-body-code-run-text">
          <template v-if="state.runLogText">
            <n-textarea
              v-model="state.runLogText"
              placeholder=""
              :autosize="{ minRows: 3 }"
              readonly
            />
          </template>

          <div v-if="!state.runLogText" class="cf-empty" tip="暂无数据"> </div>
        </div>
      </div>
      <div class="work-body-code-config">
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('attr', '属性配置')"
          >属性配置</div
        >
        <div
          class="work-body-code-config-btn"
          @click.prevent.stop="showConfigFn('version', '版本管理')"
          >版本管理</div
        >
      </div>
      <!-- 属性配置弹窗 -->
      <section
        v-if="state.configPopupShow"
        :class="{
          'config-popup': true,
          attr: state.configPopupShowType === 'attr',
          version: state.configPopupShowType === 'version',
        }"
        @click.prevent.stop="preventFn"
      >
        <div class="config-popup-title">
          <span class="line"></span>
          <span class="name">{{ state.configPopupShowTypeTitle }}</span>
        </div>
        <div class="config-popup-content scroll-bar-style">
          <n-form :data="state.formData" ref="formRef" label-width="140px" message-type="text">
            <template v-if="state.configPopupShowType === 'attr'">
              <n-form-item field="name" label="作业名称：">
                <n-input v-model="state.formData.name" placeholder="请输入作业名称" />
              </n-form-item>

              <n-form-item field="personInChargeName" label="责任人：">
                <n-input
                  v-model="state.formData.personInChargeName"
                  disabled
                  placeholder="请输入责任人"
                />
              </n-form-item>

              <n-form-item field="description" label="描述信息：">
                <n-textarea
                  v-model="state.formData.description"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  resize="both"
                  maxlength="200"
                  placeholder="请输入描述信息"
                />
              </n-form-item>

              <n-form-item field="jobManagerMemory" label="JobManager内存数：">
                <n-select
                  v-model="state.formData.jobManagerMemory"
                  :options="state.memoryOptions"
                />
              </n-form-item>
              <n-form-item field="taskManagerMemory" label="TaskManager内存数：">
                <n-select
                  v-model="state.formData.taskManagerMemory"
                  :options="state.memoryTaskOptions"
                />
              </n-form-item>

              <div class="content-title"> 开发环境变量 </div>
              <n-form-item field="name" label-width="182px" label="${kafka-group}：">
                <n-input v-model="state.formData.testConfig[0].value" placeholder="请输入" />
              </n-form-item>
              <n-form-item field="name" label-width="182px" label="${kafka-topic}：">
                <n-input v-model="state.formData.testConfig[1].value" placeholder="请输入" />
              </n-form-item>

              <div class="content-title"> 生产环境变量 </div>
              <n-form-item field="name" label-width="182px" label="${kafka-group}：">
                <n-input v-model="state.formData.prodConfig[0].value" placeholder="请输入" />
              </n-form-item>
              <n-form-item field="name" label-width="182px" label="${kafka-topic}：">
                <n-input v-model="state.formData.prodConfig[1].value" placeholder="请输入" />
              </n-form-item>
            </template>
            <template v-if="state.configPopupShowType === 'version'">
              <!--              <div class="content-btn">-->
              <!--                <div class="btn active">-->
              <!--                  <SvgIcon class="icon" icon="icon-offline-contrast" />-->
              <!--                  对比-->
              <!--                </div>-->
              <!--              </div>-->
              <n-public-table
                :isDisplayAction="true"
                :showPagination="false"
                :table-head-titles="state.tableVersionHeadTitles"
                :tableHeight="580"
                :tableData="state.tableVersionData"
                :actionWidth="100"
              >
                <template #editor="{ editor }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      variant="text"
                      @click.prevent="detailFn(editor.row)"
                      >详情</n-button
                    >
                    <n-button
                      class="has-right-border"
                      variant="text"
                      @click.prevent="revertFn(editor.row)"
                      >回滚</n-button
                    >
                  </div>
                </template>
              </n-public-table>
            </template>
          </n-form>
        </div>
      </section>
    </div>
    <n-modal
      v-model="state.showDetail"
      title="详情"
      class="largeDialog has-top-padding"
      width="560px"
      :close-on-click-overlay="false"
      @close="closeDialog"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-header">
          <div class="row">
            <div class="col">
              <div class="name">版本：</div>
              <div class="value">{{ state.versionInfo.versionText }}</div>
            </div>
            <div class="col">
              <div class="name">责任人：</div>
              <div class="value">{{ state.versionInfo.personInChargeName }}</div>
            </div>
            <div class="col">
              <div class="name">提交时间：</div>
              <div class="value">{{ state.versionInfo.startRunTime || '--' }}</div>
            </div>
          </div>
        </div>
        <div class="modal-body-content">
          <div class="modal-body-content-box scroll-bar-style">
            <n-textarea
              v-model="state.versionInfo.sql"
              placeholder=""
              :autosize="{ minRows: 3 }"
              readonly
            />
          </div>
          <div class="modal-body-content-btn">
            <div class="btn" @click.prevent="copyFn(state.versionInfo.sql)">
              <SvgIcon class="icon" icon="icon-new-copy" />复制
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showDetail = false">取消</n-button>
          <n-button @click.prevent="state.showDetail = false">确定</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import { defineEmits, reactive } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import useClipboard from 'vue-clipboard3'
  const { toClipboard } = useClipboard()
  import { format } from './FlinkSqlFormatter'
  import { timestampToTime } from '@/const/public.js'
  import { useDraggable } from '@vueuse/core'
  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'

  const myCm = ref('myCm')
  const formRef = ref('formRef')
  const textareaRef = ref(null)
  const el = ref(null)
  const codeRunEl = ref(null)

  const { x, y, style } = useDraggable(el, {
    initialValue: { x: 0, y: 0 },
    onMove: (position) => {
      const codeHeight = window.innerHeight - 17 - position.y
      codeRunEl.value.style.height = `${codeHeight}px`
      textareaRef.value.style['margin-bottom'] = `${Math.max(codeHeight, 56)}px`
      if (codeRunEl.value.offsetHeight < 60) {
        state.showRun = false
      } else {
        state.showRun = true
      }
    },
  })
  const emits = defineEmits(['openLabelFn', 'changeTree'])

  const props = defineProps({
    checkTreeId: {
      type: [String, Number],
      default: null,
    },
    taskId: {
      type: [String, Number],
      default: null,
    },
  })

  const state = reactive({
    loading: false,
    showSubmit: false,
    showDetail: false,
    showVisualization: false,
    configPopupShow: false,
    showModelName: null,
    versionNum: 1,
    configPopupShowType: '',
    configPopupShowTypeTitle: '',
    runType: 'log',
    runLogText: '',
    isRun: false,
    showRun: false,
    treeId: null,
    nodeId: null,
    nodeCode: null,
    processId: null,
    formData: {
      description: null,
      id: null,
      name: null,
      personInCharge: null,
      personInChargeName: null,
      scheduleType: null,
      testConfig: [
        { name: 'kafka-group', value: '' },
        { name: 'kafka-topic', value: '' },
      ],
      prodConfig: [
        { name: 'kafka-group', value: '' },
        { name: 'kafka-topic', value: '' },
      ],
      scheduleRangeType: 'CURRENT_PROCESS',
      jobManagerMemory: '2G',
      taskManagerMemory: '2G',
    },
    taskOptions: [],
    scheduleOptions: [
      { name: '正常调度', value: 'DEFAULT' },
      { name: '暂停调度', value: 'PAUSE' },
      { name: '空跑', value: 'DRY_RUN' },
    ],
    rangeOptions: [
      { name: '当前工作流', value: 'CURRENT_PROCESS' },
      // { name: '当前项目', value: 'CURRENT_PROJECT' },
      // { name: '所有项目', value: 'TOTAL_PROJECT' },
    ],
    memoryOptions: [
      { name: '1G', value: '1G' },
      { name: '2G', value: '2G' },
      { name: '3G', value: '3G' },
      { name: '4G', value: '4G' },
    ],
    memoryTaskOptions: [
      { name: '1G', value: '1G' },
      { name: '2G', value: '2G' },
      { name: '3G', value: '3G' },
      { name: '4G', value: '4G' },
      { name: '6G', value: '6G' },
      { name: '8G', value: '8G' },
    ],
    codemirror: null,
    sql: '',
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
    // 调度配置
    tableWorkHeadTitles: [
      { name: '上游作业', prop: 'name' },
      { name: '上游作业ID', prop: 'value' },
    ],
    tableWorkData: {
      list: [],
    },
    // 版本管理
    tableVersionHeadTitles: [
      { name: '版本', prop: 'versionText' },
      { name: '描述', prop: 'description' },
      { name: '责任人', prop: 'personInChargeName' },
      { name: '提交时间', prop: 'startRunTime' },
    ],
    tableVersionData: {
      list: [],
    },
    versionInfo: {},
    tableReferHeadTitles: [{ name: '表名', prop: 'table' }],
    tableReferData: {
      list: [],
    },
    modelList: [],
    fieldList: [],
  })

  // 弹窗
  const showConfigFn = (type, title) => {
    if (state.configPopupShowType === type && state.configPopupShow) {
      state.configPopupShow = false
    } else {
      state.configPopupShowType = type
      state.configPopupShowTypeTitle = title
      state.configPopupShow = true
      if (type === 'version') {
        getVersionList()
      }
    }

    return false
  }
  const closeDialog = () => {
    state.showDetail = false
    state.showVisualization = false
    return false
  }
  const preventFn = () => {
    return false
  }
  // 下载日志
  const downLogFn = () => {
    if (state.runLogText) {
      const blob = new Blob([state.runLogText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        state.formData.name + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  // 展示运行结果
  const showRunFn = () => {
    state.showRun = !state.showRun
    if (state.showRun) {
      getRunLog()
      codeRunEl.value.offsetHeight < 60 && (codeRunEl.value.style.height = '400px')
    } else {
      codeRunEl.value.style.height = '56px'
    }
    textareaRef.value.style['margin-bottom'] = `${Math.max(codeRunEl.value.offsetHeight, 56)}px`
  }

  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.sql = newCode
  }

  // 格式化
  const formatFn = () => {
    state.sql = format(state.sql)
  }
  // 版本详情
  const detailFn = (item) => {
    state.versionInfo = item
    state.showDetail = true
  }
  // 回滚
  const revertFn = (item) => {
    state.sql = item.sql
  }
  // 检查代码
  const checkSqlFn = () => {
    return new Promise((resolve, reject) => {
      if (!state.sql) {
        ElNotification({
          title: '提示',
          message: '请先填写sql语句',
          type: 'warning',
        })
        reject(false)
        return
      }
      let sqlStr = state.sql
      if (getSelectedText()) {
        sqlStr = getSelectedText()
      }
      api.realTimeDevelop.sqlCheck({ sqls: sqlStr }).then((res) => {
        if (res.success) {
          if (res.data?.errorMessage?.length > 0) {
            let msg = res.data.errorMessage.toString()
            ElNotification({
              title: '提示',
              message: msg,
              type: 'warning',
            })
            reject(false)
          } else {
            ElNotification({
              title: '提示',
              message: '校验通过',
              type: 'success',
            })
            resolve(true)
          }
        }
      })
    })
  }
  // 复制
  const copyFn = async (text) => {
    try {
      toClipboard(text)
      ElNotification({
        title: '提示',
        message: '复制成功',
        type: 'success',
      })
    } catch (e) {
      console.error(e)
    }
  }
  // SQL语句获取焦点时
  const onCmFocus = (cm) => {}
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      state.codemirror.showHint(config)
    })
  }
  // 获取选中的语句
  const getSelectedText = () => {
    let selectedText = ''
    if (document.selection && document.selection.type != 'Control') {
      selectedText = document.selection.createRange().text
    } else if (window.getSelection) {
      selectedText = window.getSelection().toString()
    }
    return selectedText
  }
  // 运行
  const runFn = async () => {
    if (state.isDisabled) return
    await checkSqlFn().then(async (res) => {
      if (!res) return
      let sqlStr = state.sql
      if (getSelectedText()) {
        sqlStr = getSelectedText()
      }
      let data = {
        content: sqlStr,
        description: state.formData.description,
        personInCharge: state.formData.personInCharge,
        prodConfig: state.formData.prodConfig,
        taskId: state.nodeId,
        taskName: state.formData.name,
        testConfig: state.formData.testConfig,
        jobManagerMemory: state.formData.jobManagerMemory || '2G',
        taskManagerMemory: state.formData.taskManagerMemory || '2G',
      }
      state.loading = true
      api.realTimeDevelop
        .testRunRealTimeDevelop(data)
        .then((res) => {
          state.showRun = true
          codeRunEl.value.style.height = '400px'
          getResultFn()
          if (state.timer) {
            clearInterval(state.timer)
            state.timer = null
          }
          state.timer = setInterval(() => {
            getResultFn()
          }, 10 * 1000)
        })
        .catch(() => {
          state.loading = false
          state.isRun = false
        })
    })
  }
  // 停止运行
  const stopFn = () => {
    api.realTimeDevelop
      .testStopRealTimeDevelop({
        sqls: '',
        taskId: state.nodeId,
      })
      .then((res) => {
        if (res.success) {
          ElNotification({
            title: '提示',
            message: '停止运行成功',
            type: 'success',
          })
        }
        if (state.timer) {
          clearTimeout(state.timer)
          state.timer = null
        }
        state.loading = false
        state.isRun = false
      })
      .catch(() => {
        state.loading = false
        state.isRun = false
      })
  }
  // 获取运行结果
  const getResultFn = () => {
    api.realTimeDevelop
      .getRealTimeDevelopContent({ sqls: '', taskId: state.nodeId })
      .then((res) => {
        if (res.success) {
          state.sql = res.data?.content || ''
          switch (res.data?.submitStatus) {
            case 'RUNNING':
              state.isDisabled = false
              state.isRun = true
              state.loading = true
              if (state.timer) {
                clearInterval(state.timer)
                state.timer = null
              }
              state.timer = setInterval(() => {
                getResultFn()
              }, 10 * 1000)
              break
            case 'READY':
              state.isDisabled = false
              state.isRun = false
              state.loading = false
              if (state.timer) {
                clearTimeout(state.timer)
                state.timer = null
              }
              break
            case 'SUBMITTING':
              state.isDisabled = true
              state.isRun = false
              state.loading = true
              break
            default:
              state.isDisabled = false
              state.isRun = false
              state.loading = false
          }
          if (res.data.runStatus === 'FAILED') {
            state.isDisabled = false
            state.isRun = false
            state.loading = false
            if (state.timer) {
              clearTimeout(state.timer)
              state.timer = null
            }
            ElNotification({
              title: '提示',
              message: '运行失败',
              type: 'error',
            })
          }
        } else {
          state.isRun = false
          state.loading = false
        }
      })
      .catch(() => {
        state.isRun = false
        state.loading = false
      })
  }
  // 保存
  const saveFn = (isRun = false) => {
    let data = {
      content: state.sql,
      description: state.formData.description,
      personInCharge: state.formData.personInCharge,
      prodConfig: state.formData.prodConfig,
      taskId: state.nodeId,
      taskName: state.formData.name,
      testConfig: state.formData.testConfig,
      jobManagerMemory: state.formData.jobManagerMemory || '2G',
      taskManagerMemory: state.formData.taskManagerMemory || '2G',
    }
    state.loading = true
    api.realTimeDevelop
      .saveRealTimeDevelop(data)
      .then((res) => {
        if (res.success) {
          if (!isRun) {
            emits('openLabelFn', {
              id: state.treeId,
              name: state.formData.name,
            })
            ElNotification({
              title: '提示',
              message: '保存成功',
              type: 'success',
            })
            state.loading = false
          }
          emits('changeTree')
        } else {
          state.loading = false
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 提交
  const submitFn = () => {
    state.loading = true
    api.realTimeDevelop
      .submitRealTimeDevelop({ sqls: '', taskId: state.nodeId })
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.versionNum = res.data
          state.showSubmit = true
          getVersionList()
          setTimeout(() => {
            state.showSubmit = false
          }, 3000)
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  // 获取节点详情
  const getDetailFn = () => {
    getTaskDetailLatestFn()
  }
  // 获取版本列表
  const getVersionList = () => {
    api.realTimeDevelop
      .getRealTimeDevelopVersionLsit({ sqls: '', taskId: state.nodeId })
      .then((res) => {
        if (res.success) {
          res.data.forEach((val) => {
            val.sql = val.content
            val.versionText = 'V' + val.version
          })
          state.tableVersionData.list = res.data
        }
      })
  }
  // 获取最新暂存信息
  const getTaskDetailLatestFn = () => {
    api.realTimeDevelop
      .getRealTimeDevelopContent({ sqls: '', taskId: state.nodeId })
      .then((res) => {
        if (res.success) {
          if (res.data.submitStatus === 'RUNNING') {
            state.loading = true
            state.isRun = true
            getResultFn()
          }
          state.data = res.data
          state.formData.personInCharge = res.data?.personInCharge
          state.formData.name = res.data?.taskName
          state.formData.personInChargeName = res.data?.personInChargeName
          state.formData.description = res.data?.description
          state.formData.jobManagerMemory = res.data?.jobManagerMemory || '2G'
          state.formData.taskManagerMemory = res.data?.taskManagerMemory || '2G'
          state.formData.testConfig = res.data?.testConfig || [
            { name: 'kafka-group', value: '' },
            { name: 'kafka-topic', value: '' },
          ]
          state.formData.prodConfig = res.data?.prodConfig || [
            { name: 'kafka-group', value: '' },
            { name: 'kafka-topic', value: '' },
          ]
          state.sql = res.data?.content || ''
          state.sqlOption.mode = 'text/x-sh'
        }
      })
  }
  // 五秒获取一次运行日志
  const getRunLog = () => {
    api.realTimeDevelop
      .getRealTimeDevelopLogList({ sqls: '', taskId: state.nodeId })
      .then((res) => {
        if (res.success) {
          state.runLogText = res.data
        }
      })
  }

  onMounted(() => {
    state.treeId = props.checkTreeId
    state.nodeId = props.taskId
    getDetailFn()
    const timer = setInterval(() => {
      state.showRun && state.timer && getRunLog()
    }, 5000)
    onBeforeUnmount(() => {
      clearInterval(timer)
      clearInterval(state.timer)
    })
  })
</script>
<style>
  ul.CodeMirror-hints {
    z-index: 2071;
  }
</style>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .work-body {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    &-box {
      display: flex;
      gap: 16px;
      align-items: center;
    }
    &-btn {
      position: relative;
      display: flex;
      flex-shrink: 0;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 14px 8px;
      border-bottom: 1px solid var(---, #dcdfe6);

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        box-sizing: border-box;
        padding: 4px 16px;
        color: #1e89ff;
        font-size: 14px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;
        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }
      }
      .submit {
        position: absolute;
        top: 0;
        right: 48px;
        bottom: 0;
        box-sizing: border-box;
        width: 58px;
        height: 20px;
        margin: auto;
        color: #00c700;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background: #f4ffeb;
        border: 1px solid #a8ed83;
        border-radius: 2px;
      }
    }
    &-code {
      position: relative;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 46px);
      padding-bottom: 1px;
      overflow: hidden;
      color: #1f2329;
      background: #f5f6f7;
      border: none;
      &-config {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        align-self: stretch;
        width: 48px;
        height: 100%;
        padding: 12px 8px;
        background: #fafafa;
        border-left: 1px solid #dcdfe6;

        &-btn {
          display: flex;
          gap: 8px;
          align-items: center;
          justify-content: center;
          width: 32px;
          padding: 12px 4px;
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: center;
          background: #fff;
          border: 1px solid #c9cdd4;
          border-radius: 2px;
          cursor: pointer;
        }
      }
      &-textarea {
        flex: 1;
        width: calc(100% - 48px);
        margin-bottom: 56px;
        overflow: hidden;
        .codemirror {
          height: 100% !important;
          background-color: #fff;
          :deep(.CodeMirror) {
            height: 100% !important;
            overflow: hidden;
            box-shadow: none;
          }
          :deep(.CodeMirror-scroll) {
            box-sizing: border-box;
            height: 100%;
            margin-right: -6px;
            padding: 0;
            overflow-x: hidden !important;
            overflow-y: auto !important;
            .CodeMirror-sizer {
              border-right: none;
              .CodeMirror-lines {
                padding: 0;
                //.CodeMirror-selected {
                //  height: 28px !important;
                //}
              }
              .CodeMirror-cursors {
                top: 5px;
              }
              .CodeMirror-code > div {
                padding: 2px 0;
              }
              .CodeMirror-linenumber {
                padding: 0 6px;
                text-align: center;
              }
              .CodeMirror-line {
                padding: 0 10px;
                > span {
                  padding-right: 10px !important;
                  color: #046c5c;
                  font-size: 14px;
                  word-break: break-all;
                }
              }
              .CodeMirror-linebackground {
                background-color: #f0f2f5;
              }
            }
          }
          :deep(.CodeMirror-gutters) {
            width: 32px;
            min-height: 100%;
            background-color: #e6e8eb;
            border-right: none;
          }
          :deep(.CodeMirror-vscrollbar) {
            visibility: initial !important;
            &::-webkit-scrollbar-thumb {
              background-color: #b1bcd6;
              border-radius: 6px;
              &:hover {
                background-color: #b1bcd6;
              }
            }
          }
        }
      }
      &-run {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2001;
        box-sizing: border-box;
        width: calc(100% - 48px);
        height: 56px;
        min-height: 56px;
        max-height: calc(100% - 20px);
        padding: 0 16px 8px 16px;
        background-color: #fff;
        border-top: 1px solid;
        border-top: 1px solid #dcdfe6;
        &.showRun {
          height: 400px;
        }

        .drag-line {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          z-index: 2002;
        }
        &-tabs {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          &-btn {
            .icon {
              margin-left: 10px;
              font-size: 16px;
              &.show {
                transform: rotate(180deg);
              }
            }
          }
        }
        &-text {
          box-sizing: border-box;
          height: calc(100% - 48px);
          margin-top: 8px;
          padding: 16px 0 8px 0;
          overflow-y: auto;
          color: #1d2129;
          font-size: 14px;
          word-break: break-all;
          :deep(.nancalui-textarea__div) {
            .nancalui-textarea {
              padding: 0;
              color: rgba(0, 0, 0, 0.75);
              background-color: transparent;
              border: 1px solid #fff;
              &:hover {
                border: 1px solid #fff !important;
                box-shadow: none !important;
              }
            }
          }
          .empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            &-pic {
              width: 48px;
              height: 48px;
            }
            &-text {
              margin-top: 16px;
              color: #909399;
              font-size: 14px;
            }
          }
        }
      }
      .config-popup {
        position: absolute;
        top: 0;
        right: 48px;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        align-items: center;
        width: 460px;
        height: 100%;
        background: #fff;
        box-shadow: -8px 0 24px -2px rgba(30, 47, 85, 0.1);
        &.attr {
          width: 400px;
        }
        &.version {
          width: 560px;
        }

        &-title {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          align-self: stretch;
          height: 48px;
          padding: 12px 20px 12px 0;
          border-bottom: 1px solid #f5f7fa;

          .line {
            width: 4px;
            height: 18px;
            margin-right: 12px;
            background: #1e89ff;
          }
          .name {
            color: rgba(0, 0, 0, 0.9);
            font-weight: 500;
            font-size: 16px;
            font-family: 'Source Han Sans CN';
            font-style: normal;
            line-height: 24px;
          }
        }
        &-content {
          box-sizing: border-box;
          width: 100%;
          height: calc(100% - 48px);
          padding: 16px;
          overflow-y: auto;
          .nancalui-form__item--horizontal[label-width] {
            :deep(.nancalui-form__label) {
              flex: 0 0 121px !important;
            }
          }
          .nancalui-form {
            width: 100%;
          }
          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 16px;
            padding-left: 13px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 3px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
          .content-btn {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            .btn {
              display: flex;
              gap: 4px;
              align-items: center;
              box-sizing: border-box;
              padding: 4px 16px;
              color: #1e89ff;
              font-size: 14px;
              border: 1px solid var(---, #1e89ff);
              border-radius: 2px;
              .icon {
                font-size: 14px;
              }

              &.active,
              &:hover {
                color: #fff;
                background: #1e89ff;
              }
            }
            &-title {
              color: #1d2129;
              font-weight: 500;
              font-size: 14px;
              span {
                color: #909399;
                font-weight: normal;
              }
            }
          }
        }
      }
    }
  }
  .modal-body {
    border: 1px solid #e5e6eb;
    &-header {
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        .col {
          display: flex;
          flex: 1;
          flex-shrink: 0;
          align-items: center;
          justify-content: flex-start;
          height: 22px;
          color: #606266;
          font-size: 14px;
          line-height: 22px;
          .name {
            max-width: 80px;
          }
          &:last-child {
            flex: none;
            width: 210px;
          }
          .value {
            width: calc(100% - 80px);
            overflow: hidden;
            color: #1d2129;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
    &-content {
      box-sizing: border-box;
      height: 320px;
      padding: 8px 6px 8px 10px;
      overflow-y: auto;
      border: 1px solid var(---, #e5e6eb);
      border-radius: 2px;
      &-box {
        height: 270px;
        color: #1d2129;
        font-size: 14px;
        line-height: 22px;
        :deep(.nancalui-textarea__div) {
          height: 100%;
          overflow-y: auto;
          .nancalui-textarea {
            padding: 0;
            color: rgba(0, 0, 0, 0.75);
            background-color: transparent;
            border: 1px solid #fff;
            &:hover {
              border: 1px solid #fff !important;
              box-shadow: none !important;
            }
          }
        }
      }
      &-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 30px;
        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 30px;
          color: #1e89ff;
          font-size: 14px;
          text-align: center;
          border-radius: 2px;
          cursor: pointer;
          .icon {
            margin-right: 4px;
            font-size: 14px;
          }
          &:hover {
            color: #fff;
            background: #1e89ff;
          }
        }
      }
    }
    &-flow {
      box-sizing: border-box;
      height: 524px;
      padding: 16px;
    }
  }
</style>
