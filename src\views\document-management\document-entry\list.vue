<template>
  <div class="container">
    <section :class="{ 'search-tools': true, 'search-tools-hide': tableState.hide }">
      <div class="search-con">
        <div class="search-input">
          <n-input
            v-model="tableState.filterSearch.keyword"
            placeholder="请输入检索关键字"
            clearable
            @clear="onSearch()"
          >
            <template #prefix>
              <SvgIcon icon="icon-search-input-btn" />
            </template>
          </n-input>
          <n-button class="search-input-btn" variant="solid" color="primary" @click="onSearch()"
            >搜索</n-button
          >
        </div>
        <div class="advanced-search" @click.prevent="tableState.searchVisiable = true">
          <SvgIcon icon="icon-advanced-search" class="nc-m-r-4" />
          高级检索</div
        >
      </div>
      <section class="record-con">
        <div class="record-title"
          ><SvgIcon icon="icon-record" class="nc-m-r-4" />非结构化数据推荐：</div
        >
        <div class="record-item-list">
          <div
            v-for="item in tableState.recordList"
            class="record-item"
            :key="item.id"
            :title="item.name"
            @click="onView(item)"
            >{{ item.showName }}</div
          >
        </div>
      </section>
      <div class="hide-tool">
        <SvgIcon
          icon="icon-arrow-bottom"
          :class="[tableState.hide ? 'hide-icon' : 'show-icon']"
          @click="tableState.hide = !tableState.hide"
        />
      </div>
    </section>

    <section :class="{ 'template-con-flex nc-m-t-10': true, 'template-con-more': tableState.hide }">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>非结构化数据分类</div>
        </div>
        <categoryTree @node-click="clickFn" />
      </div>
      <div class="right nc-m-l-10 nc-p-16">
        <div class="table" v-loading="tableState.isLoad">
          <div class="nc-m-b-10 nc-flex" style="align-items: center">
            <span>展示顺序：</span>
            <n-select
              v-model="tableState.sortConditions"
              :allow-clear="true"
              style="width: 200px"
              @value-change="onSearch()"
            >
              <n-option
                v-for="item in tableState.sortList"
                :key="item.id"
                :name="item.name"
                :value="item.id"
              />
            </n-select>
          </div>
          <div class="entry-list" v-if="tableState.tableList.length">
            <div class="entry-item" v-for="(item, index) in tableState.tableList" :key="index">
              <div class="nc-flex-line">
                <div class="nc-line-1 nc-m-r-10">
                  <span class="label">非结构化数据编号：</span>
                  <span class="value">{{ item.ruleCode }}</span>
                </div>
                <div class="nc-line-1" style="width: 70%">
                  <span class="label">非结构化数据名称：</span>
                  <span class="value" :title="item.name">{{ item.name }}</span>
                </div>
              </div>
              <div class="nc-flex">
                <span class="label font-14">标签：</span>
                <div class="label-list">
                  <level-tag
                    :auto-width="true"
                    class="nc-m-l-4"
                    :bgColor="tag.bgColor"
                    :borderColor="tag.color"
                    :color="tag.color"
                    v-for="tag in item.labelList"
                    :key="tag.id"
                    >{{ tag.text }}</level-tag
                  >
                </div>
              </div>
              <div class="nc-flex-line">
                <div class="nc-line-1" style="width: 170px">
                  <span class="label">非结构化数据类型：</span>
                  <span class="value" :title="item.type">{{ item.type }}</span>
                </div>
                <div class="nc-m-l-10 nc-line-1" style="width: 120px">
                  <div class="nc-flex">
                    <span class="label">密级：</span>
                    <level-tag
                      :bgColor="item.bgColor"
                      :borderColor="item.borderColor"
                      :color="item.color"
                      >{{ item.confidentialityLevelName }}</level-tag
                    >
                  </div>
                </div>
                <div class="nc-m-l-10 nc-line-1" style="width: 170px">
                  <span class="label font-14">创建人：</span>
                  <span class="value font-14" :title="item.createByName">{{
                    item.createByName
                  }}</span>
                </div>
                <div class="nc-m-l-10 nc-line-1">
                  <span class="label font-14">创建时间：</span>
                  <span class="value font-14" :title="item.createTime">{{ item.createTime }}</span>
                </div>
                <div class="line-right">
                  <n-button
                    :disabled="!tableState.typeIds.includes(item.categoryId)"
                    v-if="item.previewFlag"
                    color="primary"
                    @click="onView(item)"
                    ><SvgIcon icon="icon-see" class="nc-m-r-4" />预览</n-button
                  >
                  <n-button
                    :disabled="!tableState.typeIds.includes(item.categoryId)"
                    v-if="item.previewFlag"
                    color="primary"
                    @click="onRelation(item)"
                    ><SvgIcon icon="icon-relation" class="nc-m-r-4" />关联</n-button
                  >
                  <n-button
                    :disabled="!tableState.typeIds.includes(item.categoryId)"
                    color="primary"
                    @click="toApi(item)"
                  >
                    <SvgIcon icon="icon-link" class="nc-m-r-4" />
                    接口</n-button
                  >
                  <n-button
                    :disabled="!tableState.typeIds.includes(item.categoryId)"
                    v-if="item.downloadFlag"
                    color="primary"
                    @click="onDownLoad(item)"
                    ><SvgIcon icon="icon-download" class="nc-m-r-4" />下载</n-button
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="nancalui-table-page page-footer" v-if="tableState.pagination.total">
            <n-pagination
              size="sm"
              :total="tableState.pagination.total"
              v-model:pageSize="tableState.pagination.pageSize"
              v-model:pageIndex="tableState.pagination.currentPage"
              :can-view-total="true"
              :can-change-page-size="true"
              :can-jump-page="true"
              :page-size-options="[10, 20, 50, 100]"
              @page-index-change="pageIndexChange"
              @page-size-change="pageSizeChange"
            />
          </div>
          <div v-if="tableState.tableList.length === 0" class="table-no-content">
            <SvgIcon icon="pic-no-content" class="pic-no-content" />
            <div class="text">暂无数据</div>
          </div>
        </div>
      </div>
    </section>
    <relationDocs
      v-model="tableState.relationDocsVisible"
      :id="tableState.relationDocsId"
      type="entry"
    />
    <ApiDrawer :visible="showApiDrawer" :id="docId" @close="showApiDrawer = false" />
    <searchModal v-model="tableState.searchVisiable" @needRefresh="(d) => onSearch(true, d)" />
  </div>
</template>
<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue'
  import relationDocs from '../components/relation-docs.vue'
  import { useRouter } from 'vue-router'
  import searchModal from './search-modal.vue'
  import { tagList } from '../config/tag.js'
  import categoryTree from '../components/categoryTree.vue'
  import ApiDrawer from './components/api-drawer.vue'
  import { getDocumentAuthType } from '@/api/documentManage.js'

  const { proxy } = getCurrentInstance()
  const router = useRouter()
  import api from '@/api/index'
  const tableState = reactive({
    typeIds: [],
    recordList: [],
    hide: false,
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    tableList: [],
    filterSearch: {
      keyword: '',
      categoryId: null,
    },
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    relationDocsVisible: false,
    relationDocsId: '',
    searchVisiable: false,
    isLoad: true,
    sortList: [
      {
        id: 'createTime-ASC',
        name: '按创建时间正序展示',
      },
      {
        id: 'createTime-DESC',
        name: '按创建时间倒序展示',
      },
      {
        id: 'ruleCode-ASC',
        name: '按非结构化数据编号正序展示',
      },
      {
        id: 'ruleCode-DESC',
        name: '按非结构化数据编号倒序展示',
      },
      {
        id: 'name-ASC',
        name: '按非结构化数据名称正序展示',
      },
      {
        id: 'name-DESC',
        name: '按非结构化数据名称倒序展示',
      },
      {
        id: 'confidentialityLevelValue-ASC',
        name: '按密级正序展示',
      },
      {
        id: 'confidentialityLevelValue-DESC',
        name: '按密级倒序展示',
      },
    ],
    sortConditions: 'createTime-DESC',
  })
  function clickFn(id) {
    tableState.filterSearch.categoryId = id
    onSearch()
  }
  async function getEntryRecordList() {
    const res = await api.documentManage.entryRecordList()
    tableState.recordList = res.data.map((item) => {
      return {
        ...item,
        showName: item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name,
      }
    })
  }

  const getCurrentUserTypeAuth = async () => {
    const res = await getDocumentAuthType()
    tableState.typeIds = []
    tableState.typeIds = res.data.map((item) => item.categoryId)
  }

  function pageIndexChange(v) {
    tableState.pagination.currentPage = v
    onSearch(false)
  }
  function pageSizeChange(v) {
    tableState.pagination.pageSize = v
    onSearch()
  }
  // 查询
  function onSearch(init = true, data) {
    if (init) {
      tableState.pagination.currentPage = 1
    }
    tableState.isLoad = true
    const [fieldName, sort] = tableState.sortConditions.split('-')
    let sortConditions = null
    if (fieldName) {
      sortConditions = [{ fieldName, sort }]
    }
    api.documentManage
      .entryListPage({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...data,
          ...tableState.filterSearch,
          status: 'PUBLISH',
          categoryId:
            tableState.filterSearch.categoryId === 'all'
              ? null
              : tableState.filterSearch.categoryId,
        },
        sortConditions: sortConditions,
      })
      .then((res) => {
        tableState.tableList = res.data.list.map((item) => {
          const { color, bgColor, borderColor } = tagList[item.confidentialityLevelName]
          return {
            ...item,
            color,
            bgColor,
            borderColor,
            showName: item.name.length > 20 ? item.name.slice(0, 20) + '...' : item.name,
            labelList: item.labelList?.map((l) => {
              const [color, bgColor] = l.color.split('_')
              return {
                ...l,
                color,
                bgColor,
              }
            }),
          }
        })
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  // 关联非结构化数据
  function onRelation(row) {
    tableState.relationDocsId = row.id
    tableState.relationDocsVisible = true
  }
  // 预览非结构化数据
  function onView(row) {
    if (row.previewFlag) {
      router.push({
        name: 'documentEntryPreview',
        query: { id: row.id, docUrl: row.docUrl, name: row.name, type: row.type },
      })
    } else {
      proxy.$message.warning('用户密级不够，无法查看该非结构化数据！')
    }
  }
  //采集接口
  const showApiDrawer = ref(false)
  const docId = ref(null)
  function toApi(row) {
    docId.value = row.id
    showApiDrawer.value = true
  }
  // 非结构化数据下载
  function onDownLoad(row) {
    let lafix = row.docUrl.substring(row.docUrl.lastIndexOf('.'))
    const fileName = row.name + lafix
    getBlob(row.docUrl).then((blob) => {
      saveAs(blob, fileName)
    })
  }
  // 获取文件流
  function getBlob(url) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(xhr.response)
        }
      }
      xhr.send()
    })
  }
  // 下载文件
  function saveAs(blob, filename) {
    var link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = filename
    link.click()
  }
  onMounted(() => {
    getEntryRecordList()
    const { currentPage, pageSize } = router.currentRoute.value.query
    if (currentPage && pageSize) {
      tableState.pagination.currentPage = Number(currentPage)
      tableState.pagination.pageSize = Number(pageSize)
      onSearch(false)
    } else {
      onSearch()
    }
  })
  defineExpose({ getCurrentUserTypeAuth })
</script>
<!-- 定义导航守卫 -->
<script>
  import { defineComponent } from 'vue'
  export default defineComponent({
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getCurrentUserTypeAuth() // 调用已暴露的方法
      })
    },
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .search-tools {
    position: relative;
    width: 100%;
    height: 240px;
    padding: 66px 0;
    background: url('@/assets/img/record-bg.png') no-repeat center center;
    background-size: 100% 100%;
    transition: all 0.3s;
    &.search-tools-hide {
      height: 100px;
      padding: 26px 0;
      .record-con {
        display: none;
      }
    }
    .hide-tool {
      position: absolute;
      bottom: -10px;
      left: calc(50% - 40px);
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      background: #ecf7ff;
      border: 1px solid #fff;
      border-radius: 50%;
      .show-icon {
        transform: rotate(180deg);
      }
      svg {
        width: 14px;
        height: 14px;
        color: #1e89ff;
        text-align: center;
      }
    }
    .search-con {
      display: flex;
      justify-content: center;
      .search-input {
        position: relative;
        width: 740px;
        height: 50px;
        :deep(.nancalui-input) {
          height: 50px;
        }
        :deep(.nancalui-input__wrapper) {
          padding: 3px 3px 3px 20px;
          border: none !important;
          .yy-icon {
            width: 18px;
            height: 18px;
          }
        }
        :deep(.nancalui-input-slot__suffix) {
          margin-right: 74px;
        }
        .search-input-btn {
          position: absolute !important;
          top: 10px;
          right: 10px;
        }
      }
      .advanced-search {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 108px;
        height: 46px;
        margin-top: 2px;
        margin-left: 8px;
        padding: 3px 16px;
        line-height: 40px;
        background: #fff;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
  .record-con {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    padding: 0 10px;
    white-space: nowrap;
    word-break: keep-all;
    .record-title {
      display: flex;
      align-items: center;
      width: 90px;
    }
    .record-item-list {
      display: flex;
      align-items: center;
      max-width: calc(100% - 100px);
      overflow: auto;
    }
    .record-item {
      padding: 6px 16px;
      color: var(----, #1d2129);
      font-weight: 400;
      line-height: 22px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
      cursor: pointer;
      &:hover {
        color: var(---, #1e89ff);
        background: #fff;
      }
      + .record-item {
        margin-left: 16px;
      }
    }
  }
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .template-con-flex {
    height: calc(100vh - 380px);
    transition: all 0.3s;
  }
  .template-con-more {
    height: calc(100vh - 232px);
  }
  .table {
    height: calc(100% - 50px);
  }
  .entry-list {
    height: calc(100% - 38px);
    padding-bottom: 10px;
    overflow: auto;
    .entry-item {
      padding: 16px;
      border: 1px solid var(---, #e5e6eb);
      border-radius: 4px;
      + .entry-item {
        margin-top: 16px;
      }
      > div + div {
        margin-top: 16px;
      }
      .label {
        color: var(----, #606266);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        &.font-14 {
          font-size: 14px !important;
        }
      }
      .value {
        color: var(----, rgba(0, 0, 0, 0.9));
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        &.font-14 {
          font-size: 14px !important;
        }
      }
    }
  }
  .label-list {
    display: flex;
    align-items: center;
    max-width: calc(100% - 45px);
    overflow-x: auto;
    overflow-y: hidden;
    > div {
      flex-shrink: 0;
    }
  }
  .page-footer {
    display: flex;
    justify-content: end;
    width: 100%;
    padding: 10px;
    border-top: 1px solid var(---, #dcdfe6);
  }
  :deep(.el-button) + .el-button {
    margin-left: 4px;
  }
  :deep(.el-pagination__jump)::after {
    display: inline-block;
    margin: 0 4px;
  }
  .table-no-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 40px);
    img {
      width: 266px;
    }
    .text {
      margin-top: 20px;
      color: var(--el-text-color-secondary);
      font-size: 12px;
      line-height: normal;
    }
  }
  .nc-flex-line {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .line-right {
      width: 397px;
      margin-left: auto;
    }
  }
  .pic-no-content {
    width: 136px;
    height: 121px;
  }
</style>
