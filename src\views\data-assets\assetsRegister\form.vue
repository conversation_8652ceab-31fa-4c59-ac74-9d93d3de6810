<template>
  <CfTable
    actionWidth="180"
    :key="state.dataSource"
    v-loading="state.isLoad"
    ref="tableNoRef"
    :tableConfig="{
      data: state.dataSource,
      rowKey: 'id',
    }"
    :table-head-titles="state.formTableHead"
    :paginationConfig="{
      total: state.page.total,
      pageSize: state.page.pageSize,
      currentPage: state.page.pageNum,
      onCurrentChange: (v) => {
        state.page.pageNum = v
        getRegisterList()
      },
      onSizeChange: (v) => {
        state.page.pageSize = v
        getRegisterList()
      },
    }"
  >
    <template #assetsRegistered="{ row }">
      <i
        :style="{
          background: registerStatusColor(row.registerStatus),
          width: '6px',
          height: '6px',
          display: 'inline-block',
          'margin-right': '6px',
          'border-radius': '20px',
        }"
        class="status"
      ></i
      >{{ registerStatus(row.registerStatus) }}
    </template>
    <template #label="{ row }">
      <cfTag :tagArr="row.label" />
    </template>
    <template #securityLevel="{ row }">
      <div class="levelTag" :style="getSecurityLevelFn(row.securityLevel)?.style">{{
        getSecurityLevelFn(row.securityLevel)?.name
      }}</div>
    </template>

    <template #editor="{ data: { row } }">
      <n-button variant="text" color="primary" @click="goJump('assetsRegSee', row)">查看</n-button>
      <n-button
        v-if="row.registerStatus == 'UNREGISTERED' || row.registerStatus == 'INVALID'"
        variant="text"
        color="primary"
        @click="goJump('assetsRegRegister', row)"
        >注册资产</n-button
      >
    </template>
  </CfTable>
</template>

<script setup>
  import { useRoute, useRouter } from 'vue-router'
  import { confidentialityLevelOptions } from './components/utlis.js'
  import { assetsRegisterList } from '@/api/assets.js'
  import cfTag from '@/components/cfTag'

  /**
   * 路由对象
   */
  const route = useRoute()
  /**
   * 路由实例
   */
  const router = useRouter()

  const tableRef = ref()

  const state = reactive({
    showCom: true,
    isLoad: false,
    treeSearchText: '',
    tabActive: 'menu',
    formTableHead: [
      { prop: 'cnName', name: '中文名称' },
      { prop: 'name', name: '英文名称' },
      { prop: 'assetsRegistered', name: '注册状态', slot: 'assetsRegistered' },
      { prop: 'dataNum', name: '数据条数' },
      { prop: 'label', name: '标签', slot: 'label', width: 300 },
      { prop: 'securityLevel', name: '密级', slot: 'securityLevel' },
    ],
    originalFormInline: {
      name: null,
      assetsRegistered: '',
    },
    formInline: {
      name: null,
      assetsRegistered: '',
    },
    treeList: [
      {
        id: -1,
        name: '全部',
        type: 'DIRECTORY',
        isTemplateDirectory: false,
        children: [],
      },
    ],
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    tableHeight: 500,
  })

  onMounted(() => {
    getRegisterList()
  })

  const searchClickFn = (formData) => {
    for (let key in formData) {
      state.formInline[key] = formData[key]
    }
    state.page.pageNum = 1
    getRegisterList()
  }

  // 注册状态转换
  const registerStatus = (status) => {
    const obj = {
      UNREGISTERED: '未注册',
      WAIT_AUDIT: '待审批',
      REGISTERED: '注册成功',
      INVALID: '已失效',
      PUBLISH_AUDITING: '注册审核中',
      DOWN_AUDITING: '注销审核中',
    }
    return obj[status]
  }

  const registerStatusColor = (status) => {
    const obj = {
      UNREGISTERED: '#A8ABB2',
      WAIT_AUDIT: '#1e89ff',
      REGISTERED: '#2CA340',
      INVALID: '#f54446',
      PUBLISH_AUDITING: '#1e89ff',
      DOWN_AUDITING: '#1e89ff',
    }

    return obj[status]
  }

  // 获取注册列表
  const getRegisterList = async () => {
    state.isLoad = true
    const { assetsRegistered, name } = state.formInline
    const res = await assetsRegisterList({
      condition: {
        registerStatus: assetsRegistered,
        name,
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    })
    if (res.code === 'SUCCESS') {
      const { list, total } = res.data
      state.page.total = total
      state.dataSource = list
      state.isLoad = false
    } else {
      state.isLoad = false
    }
  }

  // 获取密级数组
  const getSecurityLevelFn = (node) => {
    return confidentialityLevelOptions.filter((item) => item.value === node)[0]
  }

  const goJump = (name, row) => {
    sessionStorage.setItem('assetsReg', JSON.stringify(row))
    router.push({ name })
  }

  defineExpose({
    searchClickFn,
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .levelTag {
    width: 40px;
    height: 20px;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
  }

  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
<style lang="scss">
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
  :deep(.el-loading-mask) {
    .el-loading-spinner {
      .circular {
        width: 48px !important;
        height: 48px !important;
      }
    }
  }
</style>
