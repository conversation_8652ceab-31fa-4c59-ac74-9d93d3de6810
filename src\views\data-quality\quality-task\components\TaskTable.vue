<template>
  <!-- 统一表格 -->
  <div class="common-table">
    <div class="page-top">
      <slot name="pageTop"></slot>
    </div>
    <div class="page-mid">
      <el-table
        border
        ref="publicTable"
        v-loading="state.loading"
        :data="state.tableData"
        :stripe="stripe"
        :height="state.tableHeight"
        :max-height="maxHeight"
        :row-key="getRowKeys"
        :header-cell-style="{ color: 'rgba(0, 0, 0, 0.90)', fontSize: '14px' }"
        :cell-style="{ color: 'rgba(0, 0, 0, 0.90)', fontSize: '14px' }"
        style="--el-table-row-hover-bg-color: #e3ecff; --nancalui-dividing-line: rgba(0, 0, 0, 0)"
        size="small"
        @cell-click="cellClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 是否需要栏 -->
        <el-table-column
          v-if="isNeedSelection"
          type="selection"
          :reserve-selection="true"
          :selectable="selectable"
          width="85"
        />
        <!-- 表头 -->
        <template v-for="item in tableHeadTitles">
          <!-- 操作列/自定义列 -->
          <el-table-column
            v-if="item.slot"
            :key="item.prop"
            :label="item.name"
            :prop="item.prop"
            align="left"
            :width="item.width"
            :fixed="item.fixed"
          >
            <template #header>
              <div :title="item.name"
                >{{ item.name
                }}<SvgIcon
                  class="illustrate"
                  v-if="item.headerIcon"
                  :icon="item.headerIcon"
                  @click.prevent="headerIconClickFn(item)"
                />
              </div>
            </template>
            <template #default="{ row, $index }">
              <slot :name="item.slot" :editor="{ row, $index }"></slot>
            </template>
          </el-table-column>

          <el-table-column
            v-else
            :key="item.name"
            :label="item.name"
            :prop="item.prop"
            align="left"
            :width="item.width"
            :fixed="item.fixed"
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <template #header>
              <div :title="item.name">{{ item.name }}</div>
            </template>
            <template #default="scope">
              <span :title="scope.row[item.prop]">{{
                scope.row[item.prop] || scope.row[item.prop] === 0 ? scope.row[item.prop] : '--'
              }}</span>
            </template>
          </el-table-column>
        </template>

        <!-- 其他扩展栏目 -->
        <el-table-column
          v-if="needOtherActionBar.show"
          fixed="right"
          :label="needOtherActionBar.label"
          align="left"
        >
          <template #default="{ row, $index }">
            <slot name="secondEditor" :editor="{ row, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 操作栏目 -->
        <el-table-column
          v-if="isDisplayAction"
          fixed="right"
          label="操作"
          :width="actionWidth"
          align="left"
        >
          <template #default="{ row, $index }">
            <slot name="editor" :editor="{ row, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 无数据插槽 -->

        <template #empty>
          <slot name="empty">
            <div class="table-no-content">
              <div style="width: 140px">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  fill="none"
                >
                  <g clip-path="url(#clip0_508_201501)">
                    <path
                      d="M0 39C0 40.1935 2.52856 41.3381 7.02944 42.182C11.5303 43.0259 17.6348 43.5 24 43.5C30.3652 43.5 36.4697 43.0259 40.9706 42.182C45.4714 41.3381 48 40.1935 48 39C48 37.8065 45.4714 36.6619 40.9706 35.818C36.4697 34.9741 30.3652 34.5 24 34.5C17.6348 34.5 11.5303 34.9741 7.02944 35.818C2.52856 36.6619 0 37.8065 0 39H0Z"
                      fill="#E3ECFF"
                    />
                    <path
                      d="M33.6517 4.5C34.1388 4.50004 34.6186 4.61867 35.0495 4.84565C35.4805 5.07262 35.8497 5.40112 36.1252 5.80275L43.5 16.5502L42.5558 16.5495L42.0585 15.7995H42.075L35.73 6.552C35.4544 6.15051 35.0851 5.82216 34.6542 5.59532C34.2232 5.36847 33.7435 5.24995 33.2565 5.25H14.7428C14.2557 5.25004 13.7759 5.36867 13.345 5.59565C12.914 5.82262 12.5448 6.15112 12.2692 6.55275L5.925 15.7995H5.9415L5.44425 16.5495H4.5L11.8748 5.802C12.1504 5.40051 12.5196 5.07216 12.9506 4.84532C13.3815 4.61847 13.8612 4.49995 14.3483 4.5H33.6525H33.6517Z"
                      fill="#B8B8B8"
                    />
                    <path
                      d="M43.5 16.5V36C43.5 36.7957 43.1839 37.5587 42.6213 38.1213C42.0587 38.6839 41.2957 39 40.5 39H7.5C6.70435 39 5.94129 38.6839 5.37868 38.1213C4.81607 37.5587 4.5 36.7957 4.5 36V16.5H9.75C10.5456 16.5 11.3087 16.8161 11.8713 17.3787C12.4339 17.9413 12.75 18.7044 12.75 19.5V21C12.7499 21.3784 12.8928 21.7429 13.1501 22.0204C13.4074 22.2979 13.7601 22.4679 14.1375 22.4963L14.25 22.5H33.75C34.1284 22.5001 34.4929 22.3572 34.7704 22.0999C35.0479 21.8426 35.2179 21.4899 35.2463 21.1125L35.25 21V19.5C35.25 18.7044 35.5661 17.9413 36.1287 17.3787C36.6913 16.8161 37.4543 16.5 38.25 16.5H43.5Z"
                      fill="#E3ECFF"
                    />
                    <path
                      d="M9.75 16.5C10.5456 16.5 11.3087 16.8161 11.8713 17.3787C12.4339 17.9413 12.75 18.7044 12.75 19.5V21C12.7499 21.3784 12.8928 21.7429 13.1501 22.0204C13.4074 22.2979 13.7601 22.4679 14.1375 22.4963L14.25 22.5H33.75C34.1284 22.5001 34.4929 22.3572 34.7704 22.0999C35.0479 21.8426 35.2179 21.4899 35.2463 21.1125L35.25 21V19.5C35.25 18.7044 35.5661 17.9413 36.1287 17.3787C36.6913 16.8161 37.4543 16.5 38.25 16.5H43.5V36C43.5 36.7956 43.1839 37.5587 42.6213 38.1213C42.0587 38.6839 41.2957 39 40.5 39H7.5C6.70435 39 5.94129 38.6839 5.37868 38.1213C4.81607 37.5587 4.5 36.7956 4.5 36V16.5H9.75ZM5.25 36C5.24997 36.5739 5.46924 37.1261 5.86296 37.5437C6.25668 37.9613 6.79508 38.2126 7.368 38.2463L7.5 38.25H40.5C41.0739 38.25 41.6261 38.0308 42.0437 37.637C42.4613 37.2433 42.7126 36.7049 42.7463 36.132L42.75 36V17.25H38.25C37.6761 17.25 37.1239 17.4692 36.7063 17.863C36.2887 18.2567 36.0374 18.7951 36.0037 19.368L36 19.5V21.0278L35.994 21.1672C35.9535 21.7111 35.7168 22.2216 35.3281 22.6041C34.9393 22.9865 34.4249 23.2147 33.8805 23.2463L33.75 23.25H14.2222L14.0828 23.244C13.5389 23.2035 13.0284 22.9668 12.6459 22.5781C12.2635 22.1893 12.0353 21.6749 12.0037 21.1305L12 21V19.5C12 18.9261 11.7808 18.3739 11.387 17.9563C10.9933 17.5387 10.4549 17.2874 9.882 17.2537L9.75 17.25H5.25V36Z"
                      fill="#B8B8B8"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_508_201501">
                      <rect width="48" height="48" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div class="text">{{ emptyText }}</div>
            </div>
          </slot>
        </template>
      </el-table>
      <el-pagination
        v-if="state.tableData.length && showPagination"
        background
        :current-page="state.currentPage"
        :page-sizes="state.paginationData.pageSizes"
        :page-size="state.pageSize"
        :layout="state.paginationData.layout"
        :total="state.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
  import { reactive, toRefs, ref, nextTick } from 'vue'

  export default {
    title: '',
    components: {},
    props: {
      stripe: {
        // 斑马条纹表格
        type: Boolean,
        default: false,
      },
      needOtherActionBar: {
        // 是否需要第二操作栏
        type: Object,
        default: function () {
          return {
            label: '设为默认',
            show: false,
          }
        },
      },
      isDisplayAction: {
        // 是否需要操作栏
        type: Boolean,
        default: false,
      },
      actionWidth: {
        // 操作栏宽度
        type: Number,
        default: 130,
      },
      isNeedSelection: {
        // 是否需要多选
        type: Boolean,
        default: false,
      },
      showBorder: {
        // 是否需要边框
        type: Boolean,
        default: false,
      },
      tableHeadTitles: {
        // 表格表头
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: () => {},
      },
      exceptHeight: {
        // 视口除去表格高度后的高度
        type: Number,
        default: 314,
      },
      maxHeight: {
        // 表格最大高度--流体高度
        type: Number,
        default: null,
      },
      showPagination: {
        // 是否显示页码
        type: Boolean,
        default: true,
      },
      rowKey: {
        type: String,
        default: 'id',
      },
      editDisabled: {
        // 编辑时候开启禁用已存在数据
        type: Boolean,
        default: true,
      },
      emptyText: {
        type: String,
        default: '暂无数据',
      },
      loading: {
        //loading状态
        type: Boolean,
        default: true,
      },
    },
    emits: ['cell-click', 'handle-selection-change', 'tablePageChange', 'headerIconClickFn'],
    setup(props, { emit }) {
      const publicTable = ref()
      const { pagination, rowKey, exceptHeight, editDisabled, maxHeight, loading } = toRefs(props)
      const state = reactive({
        loading: loading.value,
        tableData: [],
        currentPage: 1,
        pageSize: 12,
        total: 0,
        tableHeight: 540,
        configData: {
          // 其他配置
          selectRow: [],
        },
        paginationData: {},
        activeIds: [],
      })

      state.paginationData = {
        pageSizes: [10, 20, 50], // 每次展示条数的可配置项
        layout: 'total,prev,pager,next,sizes, jumper',
        currentPage: 1,
        pageSize: 12,
        ...pagination.value,
      }

      const methods = {
        setTableHeight() {
          state.tableHeight = maxHeight.value
            ? null
            : document.body.offsetHeight - exceptHeight.value - 14
        },
        getRowKeys(row) {
          return row[rowKey.value]
        },
        // 点击单元格
        cellClick(row, column) {
          emit('cell-click', row, column)
        },
        // 清空选中
        clearSelection() {
          publicTable.value.clearSelection()
        },
        //设置某行选中还是取消
        toggleRowSelection(item, isSelect) {
          publicTable.value.toggleRowSelection(item, isSelect)
        },
        // 获取选中的行
        getSelectRows() {
          return publicTable.value.getSelectionRows()
        },
        // 头部图标点击事件
        headerIconClickFn(val) {
          emit('headerIconClickFn', val)
        },

        // 多选
        handleSelectionChange(val) {
          emit('handle-selection-change', val)
        },
        // 切换条数
        handleSizeChange(val) {
          state.pageSize = val
          methods.getTableDate()
        },
        // 切换页数
        handleCurrentChange(val) {
          state.currentPage = val
          methods.getTableDate(false)
        },
        // 提交查询数据请求
        getTableDate(init = true) {
          state.currentPage = init ? 1 : state.currentPage
          // 查询
          let data = {
            currentPage: state.currentPage,
            pageSize: state.pageSize,
          }
          state.loading = true
          emit('tablePageChange', data)
        },
        // 已有数据选中/取消选中
        activeDataRow(data, isSelect = true) {
          data.forEach((row) => {
            state.tableData.find((item) => {
              if (row[rowKey.value] === item[rowKey.value]) {
                return publicTable.value.toggleRowSelection(item, isSelect)
              } else {
                return null
              }
            })
          })
        },

        // 设置返回的结果数据
        initTableData(data, configData = null) {
          state.loading = false
          if (data) {
            state.currentPage = data.pageNum
            state.pageSize = data.pageSize
            state.tableData = data.list ? data.list : []
            state.total = data.total
            if (configData && configData.selectRow) {
              // 勾选选中行
              state.configData = configData
              state.activeIds = configData.selectRow.map((item) => {
                return item[rowKey.value]
              })
              nextTick(() => {
                methods.activeDataRow(configData.selectRow)
              })
            }
          }
        },
        // 初始化失败，隐藏loading
        initFailed() {
          state.loading = false
        },
        // 切换查询条件
        initSwitchConditions() {
          state.loading = true
        },
        //勾选数据
        selectable(row, index) {
          if (row.disabledThisRow) {
            //根据行属性禁用
            return false
          } else {
            if (state.activeIds.includes(row[rowKey.value]) && editDisabled.value) {
              //根据行id禁用
              return false
            } else {
              return true
            }
          }
        },
      }
      watch(
        () => exceptHeight.value,
        () => {
          methods.setTableHeight()
        },
        {
          immediate: true,
        },
      )
      watch(
        () => loading.value,
        (v) => {
          state.loading = v
          methods.setTableHeight()
        },
        {
          immediate: true,
        },
      )

      methods.setTableHeight()
      // methods.initTableData({
      //   pageNum: 1,
      //   pageSize: 10,
      //   list: [],
      //   total: 0,
      // })

      return {
        publicTable,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .common-table {
    height: 100%;
    :deep(.el-pagination) {
      justify-content: center;
      padding: 20px 0;
      .el-pagination__total,
      .el-pagination__goto,
      .el-pagination__classifier {
        font-size: 12px;
      }

      button {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      ul li {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }
        .el-input__wrapper {
          height: $paginationChildHeight;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
    .page-mid {
      height: calc(100% - 120px);
      :deep(.el-table__fixed-right::before) {
        height: 0;
      }
      :deep(.el-table__empty-text) {
        line-height: normal;
      }
      :deep(.table-no-content) {
        display: flex;
        flex-direction: column;
        align-items: center;
        .text {
          color: var(--el-text-color-secondary);
          font-size: 12px;
          margin-top: 20px;
          line-height: normal;
        }
      }
      :deep(.el-scrollbar__view) {
        height: 100%;
      }
      :deep(.el-pagination.is-background) {
        &.btn-next,
        &.btn-prev,
        &.el-pager li {
          background-color: #fff;
          border: 1px solid #cfcfcf;
        }
      }

      :deep(.el-pager + button.btn-next[type='button']) {
        margin-right: 10px;
      }

      :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
        background-color: $themeBlue;
        color: #fff;
      }

      :deep(.el-table) {
        .el-table__inner-wrapper::before {
          // el-table 设置border边框 底部边线消失
          height: 0;
        }

        &:before {
          height: 0;
        }
        .el-checkbox {
          vertical-align: middle;
        }
        .el-checkbox__inner {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          &::after {
            left: 5px;
            top: 2px;
          }
        }

        thead {
          font-weight: 400;
          color: #333333;
          font-size: 14px;

          tr {
            height: 42px !important;
          }

          th {
            border-bottom: 1px solid #c5d0ea;
            background: #e3ecff !important;
            &.el-table__cell.is-leaf {
              border-bottom: none;
              border-right: 1px solid #fff;
            }
          }

          th.el-table__cell {
            //表头边框
            // border: 1px solid #dcdcdc;
            // border-left: none;
            .illustrate {
              font-size: 16px;
              margin-left: 4px;
              cursor: pointer;
            }

            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333333;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
          }
        }

        tbody {
          td.el-table__cell {
            // border-bottom: 1px solid #dcdcdc;
            border: none;
            border-bottom: 1px solid #c5d0ea;
          }

          tr {
            height: 40px !important;
          }

          tr td {
            //body边框
            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              // border-right: 1px solid #dcdcdc;

              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333;

              & > span {
                // 多行文本溢出隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }

              .edit-box {
                .has-right-border {
                  border-radius: initial;
                  height: 10px;
                  line-height: 9px;
                  padding: 0 0 0 10px;
                  border: 0;
                  border-left: 1px solid #cfcfcf !important;
                  color: var(--themeBlue);
                  font-size: 12px;
                  &.is-disabled {
                    color: #c9c9c9;
                  }
                  &:disabled {
                    color: #c9c9c9;
                  }
                }
                .has-right-border:first-child {
                  padding-left: 0;
                }

                .nancalui-button {
                  &:first-child {
                    border-left: 0 !important;
                  }
                }
                .btn-more {
                  font-size: 12px;
                  color: var(--themeBlue);
                  &:hover {
                    background-color: #fff;
                  }
                }
              }
            }
          }
        }

        //  解决操作栏坍塌的问题 解决固定列距表格的高度（注意：这里我写死了表头为一行文字时的高度）
        .el-table__fixed-body-wrapper {
          top: 42px !important;
        }

        // 解决多出来的横线(固定列右边)问题
        .el-table__fixed-right {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        // 若左右都有固定列时
        .el-table__fixed {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        /**改变table的滚动条样式*/
        // 滚动条的宽度
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 5px; // 横向滚动条
          height: 5px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          background-color: #e1e1e1;
          border-radius: 2px;
        }
      }
    }
  }
</style>
