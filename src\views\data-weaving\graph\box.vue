<template>
  <div :class="form.status ? 'box box-map' : 'box'">
    <div class="box-icon">
      <SvgIcon
        class="title-svg"
        :icon="form.status ? 'icon-green-table' : 'icon-title-table'"
        title="表对象"
      />
    </div>
    <n-tooltip>
      <template #title>
        <span class="list-pop">{{ form.name }}</span>
      </template>
      <div class="title">{{ form.name }}</div>
    </n-tooltip>
    <div v-if="form.status" :class="['port-tag', form.status]">
      <SvgIcon icon="icon-close-solid" v-if="form.status === 'failed'" />
      <SvgIcon icon="have-border-ok" v-else-if="form.status === 'success'" />
      <SvgIcon icon="history-icon" v-else-if="form.status === 'waiting'" />
      <SvgIcon icon="icon_three_rotate" v-else-if="form.status === 'running'" />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Box',
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        form: {
          name: '',
          icon: '',
          nodeType: 'obj',
          status: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.form = node.getData()

      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.form = current
      })
    },
    methods: {
      getAssetsImages(name) {
        return new URL(`/src/assets/images/${name}.png`, import.meta.url).href //本地文件路径
      },
    },
  }
</script>

<style lang="scss" scoped>
  .box {
    display: flex;
    box-sizing: border-box;
    width: 300px;
    height: 32px;
    padding: 4px;
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    background-color: #447dfd;
    border-radius: 6px 6px 0 0;
    .box-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 24px;
      height: 24px;
      margin-right: 8px;
      background-color: #fff;
      border-radius: 4px;
      .title-svg {
        font-size: 16px;
      }
    }
    .port-tag {
      display: block;
      width: 24px;
      height: 24px;
      margin-left: auto;
      font-weight: 400;
      font-size: 16px;
      text-align: center;
      background-color: #fff;
      border-radius: 4px;
      &.running svg {
        color: #333333;
        animation: spin 1s linear infinite;
      }
    }
    .title {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .box-map {
    background-color: #04c495;
    border-radius: 6px;
  }
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
