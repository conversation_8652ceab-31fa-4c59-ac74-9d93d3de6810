<template>
  <div class="login-container">
    <div class="login">
      <div class="login-logo"
        ><img class="login-logo-icon" src="/src/assets/img/login/logo-icon-cf.png"
      /></div>
      <n-form
        ref="loginForm"
        :data="loginForm"
        class="login-form"
        auto-complete="on"
        label-position="right"
      >
        <div class="login-title"
          ><img class="login-title-icon" src="/src/assets/img/login/logo-title-cf.png"
        /></div>
        <n-form-item field="username">
          <n-input
            ref="username"
            v-model="loginForm.username"
            :class="{ isActive: currentNode === 'user' }"
            placeholder="账号"
            name="username"
            prefix="search"
            type="text"
            clearable
            @focus="onFocusHandle('user', $event)"
            @blur="onBlurHandle"
          />
        </n-form-item>

        <n-form-item field="password" class="password">
          <n-input
            ref="password"
            v-model="loginForm.password"
            :class="{ isActive: currentNode === 'pass' }"
            type="password"
            placeholder="密码"
            prefix="search"
            show-password
            clearable
            @keyup.enter="handleLogin"
            @focus="onFocusHandle('pass', $event)"
            @blur="onBlurHandle"
          />
        </n-form-item>
        <div class="errorMsg">{{ errorMsg }}</div>
        <n-button
          :loading="loading"
          variant="solid"
          class="login-button"
          @click.prevent="handleLogin"
          >登录
        </n-button>
      </n-form>
    </div>
    <div class="star"><img class="pic" src="/src/assets/img/login/star-cf.png" />机密</div>
  </div>
</template>

<script>
  // import { validUsername } from '@/utils/validate'
  import { mapState } from 'vuex'
  import { storeStorage } from '@/utils/auth'
  const regExpObject = new RegExp(/\s+/g)
  export default {
    name: 'Login',
    data() {
      const validateUsername = (rule, value, callback) => {
        if (value === '' || regExpObject.test(value)) {
          callback(new Error('请输入正确的用户名'))
        } else {
          callback()
        }
      }
      const validatePassword = (rule, value, callback) => {
        if (value.length < 6) {
          callback(new Error('密码不能小于6位'))
        } else {
          callback()
        }
      }
      return {
        loginForm: {
          username: '',
          password: '',
        },
        errorMsg: '',
        currentNode: '',
        loading: false,
        passwordType: 'password',
        redirect: undefined,
      }
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect
        },
        immediate: true,
      },
    },
    computed: {
      ...mapState({
        isCon: (state) => state['user'].enabledPt,
        menuTreeList: (state) => state['user'].menuTreeList,
        menuCodeList: (state) => state['user'].menuCodeList,
      }),
      variables() {
        return variables
      },
    },
    mounted() {
      sessionStorage.setItem('TAG_LIST_CF', '[]')
    },
    methods: {
      showPwd() {
        if (this.passwordType === 'password') {
          this.passwordType = ''
        } else {
          this.passwordType = 'password'
        }
        this.$nextTick(() => {
          this.$refs.password.focus()
        })
      },
      onFocusHandle(name, e) {
        this.currentNode = name
        e.target.select()
      },
      onBlurHandle() {
        this.currentNode = ''
      },
      handleLogin() {
        if (this.loading) {
          return false
        }
        if (this.loginForm.username === '' || regExpObject.test(this.loginForm.username)) {
          this.errorMsg = '请输入正确的用户名'
          return
        }
        if (this.loginForm.password.length < 6) {
          this.errorMsg = '密码不能小于6位'
          return
        }
        this.errorMsg = ''
        this.loading = true
        sessionStorage.setItem('isNormalLogin', '1')
        //先登陆获取权限，直接登录本项目
        this.$store
          .dispatch('user/login', this.loginForm)
          .then((res) => {
            localStorage.removeItem(storeStorage)
            // 获取WebSocket
            this.getWebSocketUrlFn()
            // 获取项目列表
            this.getCheckProjectFn()
          })
          .catch((err) => {
            sessionStorage.setItem('isNormalLogin', '0')
            setTimeout(() => {
              this.loading = false
            }, 1000)
          })
      },
      // 获取默认的场景
      getCheckProjectFn() {
        this.$api.project.getMyProjectList({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.projectList = res.data
            if (this.projectList.length > 0) {
              let flag = true
              this.projectList.forEach((val) => {
                if (val.using && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                } else if (val.preferred && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                }
              })
              if (flag) {
                this.$store.commit('user/SET_PROJECT', this.projectList[0])
              }
            }
            // 对本地路由适配
            let menuTreeList = [...this.menuTreeList]
            let newMenuTreeList = this.menuTreeMateFn(
              menuTreeList,
              this.$router.options.routes,
              1,
            ).filter((val) => val.code !== 'home')
            this.$store.commit('user/SET_MENU_TREE', newMenuTreeList)
            localStorage.setItem(storeStorage, JSON.stringify(this.$store.state))
            // this.checkPro('dataIntegration')
            // 跳转对应路由
            this.$router.push({
              name: 'ConvergencePage',
            })
          }
        })
      },
      // 选中的模块切换路由地址以及设置tag等
      checkPro(code) {
        let activeInfo = { activeMenu: '', activeName: '', code: '' }
        this.menuTreeList.forEach((val) => {
          if (val.code === code) {
            activeInfo = this.getActiveInfoFn(val.children, activeInfo, 1)
          }
        })
        sessionStorage.setItem('checkedTagName', activeInfo.activeMenu)
        this.$store.commit('user/SET_FIRST_MENU', code)
        this.$store.commit('user/SET_ACTIVE_MENU', activeInfo.activeMenu)
        this.$store.commit('user/SET_ACTIVE_MENU_CODE', activeInfo.code)
        sessionStorage.setItem(
          'TAG_LIST_CF',
          JSON.stringify([
            {
              title: activeInfo.activeName,
              name: activeInfo.activeMenu,
              icon: 'menu-' + activeInfo.code + '-tag',
              query: {},
              params: {},
            },
          ]),
        )
        this.$router.push({
          name: activeInfo.activeMenu,
        })
      },
      // 路由树匹配code和name对应
      menuTreeMateFn(menuTreeList, routerList, zIndex) {
        menuTreeList.forEach((item) => {
          if (item.resourceType === 'MENU') {
            routerList.forEach((val) => {
              if (val.code === item.code) {
                item.routerName = val.name
                item.icon = val?.meta?.icon || null
              }
              if (val.children) {
                if (zIndex < 3) {
                  this.menuTreeMateFn(menuTreeList, val.children, zIndex + 1)
                }
              }
            })
            if (item.children && item.children[0]?.resourceType === 'MENU') {
              this.menuTreeMateFn(item.children, routerList, zIndex + 1)
            }
          }
        })
        return menuTreeList
      },
      // 获取选择模块的菜单
      getActiveInfoFn(list, activeInfo, zIndex) {
        list.forEach((val) => {
          if (val.children && val.children[0] && val.children[0].resourceType === 'MENU') {
            this.getActiveInfoFn(val.children, activeInfo, zIndex + 1)
          } else if (val.routerName && !activeInfo.activeMenu) {
            activeInfo.activeMenu = val.routerName
            activeInfo.activeName = val.name
            activeInfo.code = zIndex > 1 ? val.parentCode : val.code
          }
        })
        return activeInfo
      },
      // 获取webSocket地址
      getWebSocketUrlFn() {
        this.$api.base.getEnvironmentConfig({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.$store.commit('user/SET_WEBSOCKET', res.data.ws)
            this.$store.commit(
              'user/SET_OTHER_MRS',
              res.data.mrs ? res.data.mrs : { enabled: false },
            )
            if (res.data?.dceDpe?.links?.length > 0) {
              let menuCodeList = [...this.menuCodeList]
              let menuTree = [...this.menuTreeList]
              // 添加菜单的数组code
              res.data.dceDpe.links.forEach((val) => {
                menuCodeList.push(val.code)
              })
              // 添加菜单树列表
              menuTree.forEach((val) => {
                if (val.code === 'dataAssetsManager') {
                  val.children.forEach((v) => {
                    if (v.code === 'dataSecurity') {
                      v.children = v.children || []
                      res.data.dceDpe.links.forEach((value, index) => {
                        v.children.push({
                          resourceType: 'MENU',
                          parentCode: 'dataSecurity',
                          id: 21110000460 + value.code,
                          name: value.name,
                          path: null,
                          url: value.url,
                          httpMethod: null,
                          description: null,
                          code: value.code,
                          icon: null,
                          sort: index + 1,
                          isEntry: false,
                          children: null,
                        })
                      })
                    }
                  })
                }
              })
              this.$store.commit('user/SET_MENU_IMPLANT_LIST', res.data.dceDpe.links)
              this.$store.commit('user/SET_MENU_LIST', menuCodeList)
              this.$store.commit('user/SET_MENU_TREE', menuTree)
              this.$api.base.dceOpeLogin({}).then((res) => {
                if (res.success) {
                  localStorage.setItem('_pouch_check_localstorage', 'C')
                  localStorage.setItem('_pouch_dpe_database', 'a')
                  localStorage.setItem(
                    'localStaffInfo',
                    JSON.stringify({
                      accessToken: res.data.accessToken,
                      staffUUID: res.data?.staffInfo?.staffUUID,
                      pwdModifyStatus: 1,
                    }),
                  )
                }
              })
            }
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $cursor: #000;
  @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-container input {
      color: $cursor;
    }
  }

  :deep(input:-webkit-autofill) {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: #000 !important;
    caret-color: #333;
  }

  .login-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 100%;
    overflow: hidden;
    background-color: #fff;
    background-image: url(/src/assets/img/login/login_bg_cf.png);
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: 100% auto;

    .login {
      position: relative;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      box-sizing: border-box;
      width: 890px;
      height: 400px;
      margin: auto;
      background-color: #fff;
      &-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 364px;
        height: 100%;
        background: linear-gradient(90deg, #0b67a7 0%, #29b8cc 100%);
        &-icon {
          display: block;
          width: 227px;
          height: 331px;
        }
      }

      &-form {
        box-sizing: border-box;
        width: calc(100% - 364px);
        height: 100%;
        padding: 22px 30px;
        .nancalui-form__item--horizontal {
          box-sizing: border-box;
          margin-bottom: 40px;
          border: none;
          &.password {
            margin-bottom: 0;
            :deep(.nancalui-input) {
              .nancalui-input__wrapper {
                .icon-search {
                  background-image: url('/src/assets/img/login/icon-psd.png');
                }
              }
            }
          }
          :deep(.nancalui-input) {
            height: 40px;
            .nancalui-input__wrapper {
              padding: 9px 8px;
              border: 1px solid #dcdcdc;
              .icon-search {
                width: 18px;
                height: 18px;
                background-image: url('/src/assets/img/login/icon-user.png');
                background-repeat: no-repeat;
                background-position: center;
                background-size: cover;
                &:before {
                  display: none;
                }
              }
              &:not(.nancalui-input--error):not(
                  .nancalui-input--disabled
                ).nancalui-input--focus:not(.nancalui-input--error),
              &:not(.nancalui-input--error):not(.nancalui-input--disabled):not(
                  .nancalui-input--focus
                ):hover {
                border: 1px solid #0e94cd;
                box-shadow: none;
              }
              .nancalui-input__inner {
                color: #333333;
                font-size: 14px;
              }
            }
          }
        }
      }

      &-title {
        margin-bottom: 34px;
        &-icon {
          display: block;
          width: 365px;
          height: 64px;
          margin: 0 auto;
        }
      }
      .errorMsg {
        height: 20px;
        color: #f56c6c;
        font-size: 12px;
        line-height: 20px;
      }

      &-button {
        width: 100%;
        height: 38px;
        margin-top: 40px;
        padding: 0;
        font-weight: bold;
        font-size: 14px;
        line-height: 38px;
        background-color: #0076a8;
        background-size: 100%;
        border: none;
        border-radius: 2px;
        :deep(span) {
          line-height: 38px;
        }
      }
    }
    .show-pwd {
      position: absolute;
      top: 1px;
      right: 10px;
      color: #ddd;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
    }
    .star {
      position: absolute;
      top: 14px;
      right: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: #1c70a1;
      font-size: 16px;
      .pic {
        width: 18px;
        height: 18px;
        margin-right: 6px;
      }
    }
  }
</style>
