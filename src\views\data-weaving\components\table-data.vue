<template>
  <div class="table-data-drawer" v-show="visiable">
    <svgIcon icon="close-icon" @click="onClose" class="close-icon" />
    <div class="nc-flex nc-p-l-16 nc-p-r-16">
      <div class="left">
        <div>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.keyword"
            placeholder="字段中文名或英文名检索"
            suffix="search"
            @input="onSearchTable"
          />
          <div class="nc-m-t-8 table-list">
            <div class="table-item" v-for="item in state.colList" :key="item.col"
              >{{ item.prefix }}{{ item.col }}({{ item.name }}}</div
            >
          </div>
        </div>
      </div>
      <div class="right">
        <CfTable
          actionWidth="0"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData,
            rowKey: 'id',
          }"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
  })
  const state = reactive({
    keyword: '',
    colList: [
      { col: 'orderid', name: '订单编号', prefix: '$' },
      { col: 'userid', name: '工号', prefix: '#' },
      { col: 'productname', name: '商品名称', prefix: '#' },
      { col: 'ordervalue', name: '订单金额', prefix: '#' },
    ],
    tableHeadTitles: [
      { prop: 'orderid', name: '订单编号' },
      { prop: 'userid', name: '工号' },
      { prop: 'productname', name: '商品名称' },
      { prop: 'ordervalue', name: '订单金额' },
    ],
    tableData: [
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
      {
        orderid: 'CFX2024010199238113',
        userid: 'C000129',
        productname: '离心加速平衡陀螺仪',
        ordervalue: '1000000.00',
      },
    ],
  })
  const emit = defineEmits(['update:modelValue'])
  function onClose() {
    emit('update:modelValue', false)
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    (val) => {
      visiable.value = val
    },
  )
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .table-data-drawer {
    position: fixed;
    bottom: 16px;
    left: 306px;
    width: calc(100% - 322px);
    padding: 30px 0 20px 0;
    background: #fff;
    border: 1px solid #e0e0e0;
    .close-icon {
      position: absolute;
      top: 6px;
      right: 6px;
    }
  }
  .left {
    width: 280px;
    margin-right: 20px;
    background: #fff;
    .table-list {
      height: calc(100% - 200px);
      overflow: auto;
      .table-item {
        height: 40px;
        padding: 0 8px;
        line-height: 40px;
        cursor: pointer;
        &:hover {
          color: #447dfd;
          background: #f0f1f5;
        }
      }
    }
  }
  .right {
    flex: 1;
    width: calc(100% - 290px);
    height: 100%;
    background: #fff;
  }
</style>
