import { Graph, Node } from '@antv/x6'
import '@antv/x6-vue-shape'
import Box from './shape/box'

const UNIQ_GRAPH_ID = 'UNIQ_GRAPH_ID'

Graph.unregisterNode('dataAsync')

// 生成自义定节点
function ChartAnimateText(params) {
  return Graph.registerNode(params.name, {
    inherit: 'vue-shape',
    view: UNIQ_GRAPH_ID, // 需要指定 view 属性为定义的标识
    component: {
      template: `
              <Box/>`,
      components: {
        Box,
      },
    },
    data: {
      nodeName: params.name,
      name: params.title,
      icon: params.isImg ? params.img : new URL(`/src/assets/${params.img}`, import.meta.url).href,
      taskType: params.taskType,
      showMenu: false,
    },
    ports: {
      groups: {
        top: {
          id: 'top',
          position: 'top',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#8091B7',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
        bottom: {
          id: 'bottom',
          position: 'bottom',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#8091B7',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
      },
      items: [
        {
          id: 'top',
          group: 'top',
        },
        {
          id: 'bottom',
          group: 'bottom',
        },
      ],
    },
    // tools: {
    //   name: 'button-remove',
    //   args: { distance: -40 },
    // },
  })
}
// 数据同步节点
export const DataAsyncNode = ChartAnimateText({
  name: 'dataAsync',
  title: '同步至资源库',
  img: 'img/offlineDev/同步至资源库.png',
  taskType: 'dataAsync',
})
// PySpark
export const PySparkNode = ChartAnimateText({
  name: 'PySpark',
  title: 'PySpark',
  img: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIaSURBVHgB7ZQxb9NAFMffndNwkZA4RhbkfgLSjQGJFMUSA6DkE+Bs7RRnYu4EW5oFKpbSjY2UgoSEUNJPkI4w1UztVmdo0zj2Xd8p71qnaqUqydChf+knn+/evffuPZ8B7kSKXwj/pHwvuDzPYE6Ky6IDTBfTEVsqdE9DXZLyeOHYzcEcpZVT4wuqE3uFaAQDNw88nDnAoCxKjmYVBaov/py0Y0/IGJK9PHO+pRyqMItMWYae2B9hkIl5r9AbeMI3Y05zFWQf6SAu3FTMaTDs4sEQ3uJb0U5rrbe5HvuxTTbOu0gf+YLsXePSbIqIrExi28iDf8/ybFHw59hwmf89XLI9CJESskzjJmXkUsA1cmKzNO9tpIdsIRJ5YtYf3+dVleiIASuaL8lmYDYekRMf2cxkfERzPXLk0omDjJ2x0ZkEzsVpkzlyiwxMPXdp3a5VqAQRnVCS7S6NJe2vXw7gIF+Rd3BRX4G8RB4i76kEh7T5KfKIbIfIL7I38zXkA/If+WsD5Cg7Hy7qbY7foKzMpi7ZhjDuUzdzipCSWqPnMlxRpqxcGDdvbpr6X9T6tGMa29cMelrn8D6M6sHKm8b6xo/NYOVVzdpxmFYMZH31tYsXzQWlJGZaaX78HnBQE2Yz/YvWN3Y6eG0l8DhSmm9xzpupThbnFkDrpBasVkMzbn7+2YY0LTXo3Wr6EoFqTThTia/GX+GdbpnOALxaskMiHanHAAAAAElFTkSuQmCC',
  taskType: 'DB_INPUT',
  isImg: true,
})
// HiveSQL
export const HiveSQLNode = ChartAnimateText({
  name: 'HiveSQL',
  title: 'HiveSQL',
  img: 'img/offlineDev/HiveSQL.png',
  taskType: 'DB_INPUT',
})
// DorisSQL
export const DorisSQLNode = ChartAnimateText({
  name: 'DorisSQL',
  title: 'DorisSQL',
  img: 'img/offlineDev/DorisSQL.png',
  taskType: 'DB_INPUT',
})
// SparkSQL
export const SparkSQLNode = ChartAnimateText({
  name: 'SparkSQL',
  title: 'SparkSQL',
  img: 'img/offlineDev/SparkSQL.png',
  taskType: 'DB_INPUT',
})
// Python
export const PythonNode = ChartAnimateText({
  name: 'Python',
  title: 'Python',
  img: 'img/offlineDev/Python.png',
  taskType: 'DB_INPUT',
})
// Shell
export const ShellNode = ChartAnimateText({
  name: 'Shell',
  title: 'Shell',
  img: 'img/offlineDev/Shell.png',
  taskType: 'DB_INPUT',
})
// HiveDDL
export const HiveDDLNode = ChartAnimateText({
  name: 'HiveDDL',
  title: 'HiveDDL',
  img: 'img/offlineDev/HiveDDL.png',
  taskType: 'DB_INPUT',
})
// DorisDDL
export const DorisDDLNode = ChartAnimateText({
  name: 'DorisDDL',
  title: 'DorisDDL',
  img: 'img/offlineDev/DorisDDL.png',
  taskType: 'DB_INPUT',
})
