<template>
  <div class="code-content">
    <!-- 显示pdf的canvas -->
    <codemirror
      ref="myCm"
      v-model:value="state.code"
      class="codemirror"
      :options="state.option"
      @ready="onCmReady"
      @focus="onCmFocus"
      @input="onCmCodeChange"
    />
  </div>
</template>

<script setup>
  import codemirror from 'codemirror-editor-vue3'

  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/xml/xml.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/xml-hint'
  import 'codemirror/addon/hint/show-hint'

  import 'codemirror/addon/lint/lint.css'
  import 'codemirror/addon/fold/foldgutter.css'
  import 'codemirror/lib/codemirror.css'
  import 'codemirror/theme/rubyblue.css'
  import 'codemirror/mode/javascript/javascript'
  import 'codemirror/addon/lint/lint'
  import 'codemirror/addon/lint/json-lint'

  // 折叠
  import 'codemirror/addon/fold/foldgutter.css'
  import 'codemirror/addon/fold/foldcode'
  import 'codemirror/addon/fold/foldgutter'
  import 'codemirror/addon/fold/brace-fold'
  import 'codemirror/addon/fold/comment-fold'
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {
          mode: 'text/xml',
        }
      },
    },
  })
  const { option } = toRefs(props)
  const mimeModes = {
    ...CodeMirror.mimeModes,
    JSON: 'application/json',
    CSV: '',
    XML: 'text/xml',
  }
  const state = reactive({
    codemirror: null,
    code: '',
    option: {
      tabSize: 2,
      lineNumbers: true,
      line: true,
      lint: true,
      styleActiveLine: true,
      mode: mimeModes[option.value.type],
      theme: 'solarized',
      hintOptions: {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      },
      readOnly: true,
    },
  })
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
  }
  const onCmFocus = (cm) => {}
  const onCmCodeChange = (newCode) => {
    state.code = newCode
  }
  // 请求文件地址，获取文本
  const getFileContent = async (filePath, fileEncoding) => {
    const docoder = new TextDecoder(fileEncoding || 'utf-8')
    const file = await fetch(filePath)
    const bytes = await file.arrayBuffer()
    const string = docoder.decode(bytes)
    state.code = string
  }
  getFileContent(option.value.value, option.value.fileEncoding)

  onMounted(async () => {
    nextTick(() => {
      state.codemirror.on('keypress', () => {
        state.codemirror.showHint()
      })
    })
  })
</script>
<style lang="scss" scoped>
  .code-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #f6f7fb;
    border-radius: 6px;
    .codemirror {
      height: 100% !important;
      background-color: #f6f7fb;
      :deep(.CodeMirror) {
        height: 100% !important;
        overflow: hidden;
        box-shadow: none;
      }
      :deep(.CodeMirror-scroll) {
        height: 100%;
        overflow-y: auto;
        background: #f6f7fb;
        .CodeMirror-sizer {
          .CodeMirror-cursors {
            top: 5px;
          }
          .CodeMirror-code > div {
            padding: 5px 0;
          }
          .CodeMirror-linenumber {
            padding: 0 6px;
            text-align: center;
          }
          .CodeMirror-line {
            padding: 0 10px;
            > span {
              padding-right: 10px !important;
            }
          }
          .CodeMirror-linebackground {
            right: 4px;
            left: 4px;
            border-radius: 6px;
          }
        }
        .CodeMirror-activeline-background {
          background-color: transparent;
        }
      }
      :deep(.CodeMirror-gutters) {
        width: 32px;
        min-height: 100%;
        background-color: #e3ecff;
      }
          :deep(.CodeMirror-vscrollbar) {
            visibility: initial !important;
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 6px;
          &:hover {
            background-color: #b1bcd6;
          }
        }
      }
    }

    .codemirror :deep(.CodeMirror-scroll) {
      height: 100%;
    }
  }
</style>
