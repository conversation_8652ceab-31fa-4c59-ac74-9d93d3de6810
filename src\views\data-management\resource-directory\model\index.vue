<template>
  <section class="da">
    <section class="main" v-if="hasGraph">
      <section class="main-left">
        <h3>模型管理</h3>
        <ModelTree
          :isShow="isShow"
          :treeData="treeData"
          :graphList="graphList"
          :activeId="subjectId"
          @nodeClick="handleAddTag"
        />
      </section>
      <NChandleWidth hwidth="12" @widthChange="widthChange">
        <template #content> </template>
      </NChandleWidth>
      <section class="main-canvas">
        <div class="canvas" ref="graph">
          <div id="canvas" class="da"></div>
          <div class="minimapBox">
            <h3 @click="showMinMap = !showMinMap">
              <n-icon style="margin-left: 5px" v-if="showMinMap" name="icon-chevron-down" />
              <n-icon v-else name="icon-chevron-up"><ArrowUpBold /></n-icon>
              缩略图
            </h3>
            <div
              id="minimapContainer"
              :class="{ showMinMap: showMinMap, hiddenMinMap: !showMinMap }"
            ></div>
          </div>
        </div>
      </section>
    </section>
    <div class="table-no-content">
      <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
      <div>该数据表暂无ER图</div>
    </div>
    <EntityPop
      :isShow="EntityPopShow"
      :cell="currentNode"
      :graphList="graphList"
      :subjectGuid="subjectId"
      @closepop="closepop"
    />
    <TablesPop
      :isShow="TablesPopShow"
      :cell="currentNode"
      :graphList="graphList"
      :subjectGuid="subjectId"
      @closepop="closepop"
    />
    <DiagramPop v-if="DiagramPopShow" :cell="currentNode" @closepop="closepop" />
    <LineEditModal
      v-if="showLineEdit"
      ref="lineEidtNode"
      :isShow="showLineEdit"
      :cell="currentEdge"
      @closeModal="closeLineEditModal"
      :graphList="graphList"
      :subjectGuid="subjectId"
    />

    <InfoDialog
      :showInfo="showInfo"
      @close="
        () => {
          showInfo.isShow = false
          showInfo.type = ''
        }
      "
      :graphList="graphList"
      :subjectId="subjectId"
      :cell="currentNode"
      :edge="currentEdge"
    />
  </section>
</template>

<script>
  import EntityPop from './components/EntityPop/index.vue'
  import TablesPop from './components/TablesPop/index.vue'
  import DiagramPop from './components/DiagramPop/index.vue'
  import LineEditModal from './components/LineEditModal/index.vue'
  import FlowGraph from './graph'
  import { cloneDeep } from 'lodash-es'
  import ModelTree from '../components/model-tree.vue'
  import InfoDialog from './components/InfoDialog/index.vue'
  import { calculationCells, calMapCells } from './utils'
  import { setEdgeVertices } from '@/utils/graph'
  import api from '@/api/index'

  export default {
    components: {
      EntityPop,
      TablesPop,
      DiagramPop,
      LineEditModal,
      ModelTree,
      InfoDialog,
    },
    props: {
      tab: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        showInfo: { isShow: false, type: '' },
        lineRightShow: false,
        entityRightShow: false,
        oldGraphList: [],
        graph: null, // 画布对象
        // 模型树数据
        treeData: [],
        // 模型树数据结构
        defaultProps: {
          children: 'children',
          label: 'name',
        },
        iconObjTree: [], // 图表对象
        nodePosition: {}, // 对象位置
        EntityPopShow: false, // 实体弹窗
        TablesPopShow: false, // 表弹窗
        DiagramPopShow: false, // 表图弹窗
        currentNode: null, // 当前节点
        currentEdge: null, // 当前选择的线
        showLineEdit: false, // 线编辑弹框
        graphId: '', // 画布ID
        graphList: [],
        width: 240,
        height: 300,
        tagList: [],
        subjectId: '',
        subjectName: '',
        subjectList: [],
        kind: 'ORACLE',
        showMinMap: true,
        contextLeft: 220, //画布右键内容left宽带
        hasGraph: false,
      }
    },

    async mounted() {
      const res = await api.dataManagement.erPhysics({
        tab: this.tab,
      })
      if (res.data.graphs.length) {
        this.hasGraph = true
        this.$nextTick(() => {
          this.initGraph('canvas', res.data)
        })
      }
    },
    beforeUnmount() {
      window.removeEventListener('resize', this.resizeFn)
      this.graph?.dispose()
    },
    methods: {
      // 初始化画布
      initGraph(id, data) {
        FlowGraph.init({ id: id, type: 'detail', that: this })
        const { graph } = FlowGraph
        this.graph = graph
        this.resizeFn()
        this.boundEdge()
        this.boundClick()
        this.searchGraph(data)
        window.addEventListener('resize', this.resizeFn)
      },
      // 获取画布容器大小
      getContainerSize() {
        return {
          width: this.$refs.graph.offsetWidth - 5,
          height: this.$refs.graph.offsetHeight,
        }
      },
      // 画布重新设置大小
      resizeFn() {
        const { width, height } = this.getContainerSize()
        this.graph.resize(width, height)
        if (this.$store.state.app.menuHidden) {
          this.contextLeft = 50
        } else {
          this.contextLeft = 220
        }
      },

      // 画布查询
      async searchGraph(data) {
        const loading = this.$loadingService.open({
          message: '画布渲染中...',
        })
        try {
          if (data) {
            this.kind = data?.kind || 'ORACLE'
            if (this.$store.state.app.dataTypeList?.kind !== this.kind) {
              this.$store.dispatch('app/setDataTypeList', this.kind)
            }
            this.graphList = data?.graphs.map((item) => {
              return {
                ...calculationCells(item, this.modId, this.modGuid, this.modType, this.kind, true),
              }
            })
            // 这个数据在详情之后不进行操作，用于保存时判断是否是更新节点
            this.oldGraphList = cloneDeep(this.graphList)
            this.initSubjectList()
            this.switchSubject()
          } else {
            this.graph.fromJSON([])
          }
          this.getModTree()
          //清空历史队列
          this.graph.cleanHistory()
          loading.loadingInstance.close()
        } catch (e) {
          console.log(e)
          loading.loadingInstance.close()
        }
      },

      // 初始化主题域
      initSubjectList() {
        this.subjectList = this.graphList.map((item) => {
          return {
            subjectGuid: item.subjectGuid,
            subjectId: item.subjectId,
            subjectName: item.subjectName,
            kind: 1,
          }
        })
      },
      // 切换主题域
      switchSubject() {
        try {
          // 如果未选中主题域，默认选中第一个
          if (this.subjectList?.length > 0 && !this.subjectId) {
            const { subjectGuid } = this.$route.query
            if (subjectGuid) {
              const curSubject = this.subjectList?.find((item) => item.subjectGuid === subjectGuid)
              if (curSubject) {
                this.handleAddTag(curSubject)
              }
            } else {
              this.handleAddTag(this.subjectList[0])
            }
          } else {
            const activeSubject = this.subjectList?.find(
              (sub) => sub.subjectGuid === this.subjectId,
            )
            const activeTagIndex = this.tagList?.findIndex(
              (tag) => tag.subjectGuid === this.subjectId,
            )
            // 初始化跳转主题域
            if (activeSubject && activeTagIndex < 0) {
              this.handleAddTag(activeSubject)
            } else if (activeSubject && activeTagIndex > -1) {
              this.tagList[activeTagIndex].subjectName = activeSubject.subjectName
            } else {
              this.tagList.splice(activeTagIndex, 1)
              this.subjectId = this.tagList[activeTagIndex - 1]?.subjectGuid
              this.subjectName = this.tagList[activeTagIndex - 1]?.subjectName
            }
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 添加选项卡
      handleAddTag(tag) {
        // 主题域
        if (tag.kind === 1) {
          const addIndex = this.tagList.findIndex((item) => item.subjectGuid === tag.subjectGuid)
          if (addIndex < 0) {
            this.tagList.push(tag)
          }
          this.handleTagClick(tag)
          // 实体
        } else if (tag.kind === 2) {
          if (tag.subjectGuid !== this.subjectId) {
            const addIndex = this.tagList.findIndex((item) => item.subjectGuid === tag.subjectGuid)
            const newSubject = this.subjectList?.find(
              (item) => item.subjectGuid === tag.subjectGuid,
            )
            if (newSubject) {
              if (addIndex === -1) {
                this.tagList.push(newSubject)
              }
              this.handleTagClick(newSubject)
            } else {
            }
          }
          setTimeout(() => {
            const cell = this.graph?.getCellById(tag.id)
            if (cell) {
              this.graph.resetSelection(cell)
              this.graph.centerCell(cell)
            }
          }, 100)
        }
      },
      //点击选项卡
      handleTagClick(tag) {
        // 切换画布
        this.subjectId = tag.subjectGuid
        this.subjectName = tag.subjectName
        this.onGraphChange()
      },
      // 关闭选项卡
      handleTagClose(index) {
        // 默认展示前一个
        if (this.tagList[index - 1]) {
          this.handleTagClick(this.tagList[index - 1])
          // 如果前面没有了。展示后面一个
        } else if (this.tagList[index + 1]) {
          this.handleTagClick(this.tagList[index + 1])
        }
        this.tagList.splice(index, 1)
      },
      // 左侧树宽度改变
      widthChange(movement) {
        this.width += movement
        if (this.width < 0) {
          this.width = 0
        } else if (this.width > 500) {
          this.width = 500
        } else {
          const leftNode = document.querySelector('.main-left')
          const rightNode = document.querySelector('.main-canvas')
          leftNode.style.setProperty('--lwidth', this.width + 'px')
          rightNode.style.setProperty('--rwidth', 'calc(100% - ' + (this.width + 16) + 'px)')
        }
        const { width, height } = this.getContainerSize()
        this.graph.resize(width, height)
      },
      // 左侧树点击触发
      handleNodeClick(data) {
        const cell = this.graph?.getCellById(data.guid)
        if (cell) {
          this.graph.resetSelection(cell)
          this.graph.centerCell(cell)
        }
      },
      // 获取左侧模型树
      getModTree() {
        try {
          const subjectList = [
            {
              name: '主题域目录',
              type: 1,
              kind: 0,
              expanded: true,
              children: this.subjectList?.map((item) => {
                return {
                  ...item,
                  name: item.subjectName,
                  kind: 1,
                  type: 2,
                }
              }),
            },
          ]
          let cells = []
          this.graphList?.forEach((graph) => {
            if (graph.subjectGuid === this.subjectId) {
              const curJson = this.graph?.toJSON().cells
              // subjectGuid：记录共享实体在当前模型的主题域
              cells.push(
                ...curJson.map((it) => {
                  return { ...it, subjectGuid: graph.subjectGuid }
                }),
              )
            } else {
              if (graph?.json?.length > 0) {
                cells.push(
                  ...graph?.json?.map((it) => {
                    return { ...it, subjectGuid: graph.subjectGuid }
                  }),
                )
              }
            }
          })
          let relations = []
          let views = []
          cells.forEach((cell) => {
            if (cell.shape === 'edge') {
              const sourceName = cell.data.parentEntityName
              const targetName = cell.data.subEntityName
              const label = cell.data?.name
              relations.push({
                name: label
                  ? `${label}(${sourceName}-${targetName})`
                  : `${sourceName}-${targetName}`,
                id: cell.id,
                type: 4,
              })
            }
            if (cell.shape === 'nc-table' || cell.shape === 'nc-entity') {
              views.push({
                name: cell.data?.entity?.name || cell.data?.table?.tabName,
                id: cell.id,
                guid: cell.id,
                subjectGuid: cell.data.isShare ? cell.subjectGuid : cell.data.subjectGuid,
                kind: 2,
                type: 3,
              })
            }
          })
          // 实体去重
          views = views.reduce((pre, cur) => {
            return pre.findIndex((item) => item.id === cur.id) < 0 ? pre.concat(cur) : pre
          }, [])
          // 边去重
          relations = relations.reduce((pre, cur) => {
            return pre.findIndex((item) => item.id === cur.id) < 0 ? pre.concat(cur) : pre
          }, [])
          const iconObjTree = [
            {
              name: '对象目录',
              expanded: true,
              type: 1,
              children: [
                {
                  name: `${this.modType === '3' ? 'Table' : '实体'}(${views.length})`,
                  children: views,
                  type: 1,
                  expanded: true,
                },
                {
                  name: `${this.modType === '3' ? 'Relation' : '关系'}(${relations.length})`,
                  children: relations,
                  type: 1,
                  expanded: true,
                },
              ],
            },
          ]
          this.treeData = [
            {
              modGuid: this.modGuid,
              modId: this.modId,
              name: this.tab,
              type: 1,
              expanded: true,
              children: [...subjectList, ...iconObjTree],
            },
          ]
        } catch (e) {
          console.log(e)
        }
      },

      // 画布切换
      onGraphChange() {
        const loading = this.$loadingService.open({
          message: '画布渲染中...',
        })
        let currentGraph = this.graphList?.find((graph) => graph.subjectGuid === this.subjectId)
        if (currentGraph) {
          // 先清空画布，防止多个主题域重复渲染
          this.graph.clearCells()
          let json = currentGraph.json || []
          if (currentGraph?.mappingEntityList?.length > 0 && json?.length > 0) {
            json = calMapCells(currentGraph, this.graphList, this.modId)
          }
          currentGraph.json = json
          this.graph.fromJSON(json)
          this.graph.centerContent()
          this.isRender = true
          loading.loadingInstance.close()
          this.getModTree()
        }
      },

      // 关闭所有的右键菜单
      clearMenu() {
        this.showDelete = false
        this.showMapping = false
        this.entityRightShow = false
        this.entityRightShowTwo = false
        this.lineRightShow = false
        this.lineRightShowTwo = false
      },
      //
      boundEdge() {
        this.graph.on('render:done', () => {
          if (this.isRender) {
            //画布渲染完成后，给所有的边添加路径点
            const edges = this.graph.getEdges()
            edges.forEach((edge) => {
              setEdgeVertices(edge, true)
            })
            const { history } = this.graph
            // 自动布局需要可以重做
            if (!this.isAutoLayout) {
              history.undoStack = []
            }
            this.isAutoLayout = false
            this.isRender = false
            this.graph.centerContent()
          }
        })
      },
      boundClick() {
        this.graph.on('node:dblclick', ({ cell, e }) => {
          if (cell.data.type === 'textarea' || cell.data.type === 'input') {
            if (!cell.getData()?.disableMove) {
              e.target.focus()
            }
          }
          cell.data.disableMove = true
          cell.data.kind = this.kind
          this.currentNode = cell
          if (cell.shape === 'nc-entity' || cell.shape === 'nc-theme-entity') {
            this.EntityPopShow = true
          } else if (cell.shape === 'nc-table') {
            this.TablesPopShow = true
          } else if (cell.shape === 'nc-diagram') {
            this.DiagramPopShow = true
          }
        })
      },
      closepop() {
        this.EntityPopShow = false
        this.TablesPopShow = false
        this.DiagramPopShow = false
        this.getModTree()
      },
      checkInfo(type) {
        this.showInfo.isShow = true
        this.showInfo.type = type === 'edge' ? 'edge' : this.currentNode.shape
        this.entityRightShow = false
        this.lineRightShow = false
        this.lineRightShowTwo = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import './utils/style.scss';
</style>
