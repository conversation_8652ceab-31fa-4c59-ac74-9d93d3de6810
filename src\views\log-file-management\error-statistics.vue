<template>
  <div class="container-padding16">
    <iframe :src="url" frameborder="0" style="width: 100%; height: 100%"></iframe>
  </div>
</template>
<script setup>
  import api from '@/api/index'
  import { ref, onMounted } from 'vue'
  const url = ref('')
  const getUrl = () => {
    api.documentManage.getLogPlatformUrl({ groupCode: '0001' }).then((res) => {
      if (res.success) {
        url.value = res.data[0].value + '/#/errors'
      }
    })
  }
  onMounted(() => {
    getUrl()
  })
</script>
