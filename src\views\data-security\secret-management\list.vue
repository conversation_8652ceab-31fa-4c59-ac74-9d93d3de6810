<template>
  <section :class="['container', state.isLzos ? 'isLzos' : '']">
    <section class="container-box">
      <section class="tools">
        <div class="row">
          <div class="col">
            <span class="label">名称：</span>
            <n-input
              v-model="state.originalFormInline.name"
              placeholder="中文名称或英文名称"
              size="small"
              clearable
            />
            <span class="label">表密级：</span>
            <n-select v-model="state.originalFormInline.confidentialityLevel" placeholder="表密级">
              <n-option
                v-for="(item, index) in state.secretOptions"
                :key="index"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="table">
        <div class="title">密级管理</div>
        <div class="table-content">
          <CfTable
            :isDisplayAction="true"
            :tableConfig="{
              data: state.tableData,
              rowKey: 'assetsId',
            }"
            :table-head-titles="state.tableHeadTitles"
            actionWidth="104"
            :paginationConfig="{
              total: state.pagination.total,
              pageSize: state.pagination.pageSize,
              currentPage: state.pagination.currentPage,
              onCurrentChange: (v) => {
                state.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #tableLevel="{ row }">
              <div :class="['tag', row.tableLevel]">
                {{ matchingCname(row.tableLevel) }}
              </div>
            </template>
            <template #filedLevels="{ row }">
              <n-select
                v-model="row.tableLevel"
                placeholder="请选择"
                :options="state.secretOptions"
                @value-change="tableLevelFn($event, row)"
              />
            </template>

            <template #editor="{ row }">
              <div class="edit-box">
                <n-button
                  v-if="
                    state.buttonAuthList.includes('assetsManage_dataSecurity_secretManagement_view')
                  "
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.prevent="
                    goJump('secretManagementSee', {
                      id: row.assetsId,
                      tableName: row.cnName,
                      tableConfidentialityLevel: row.tableLevel,
                    })
                  "
                >
                  查看
                </n-button>
              </div>
            </template>
          </CfTable>
        </div>
      </section>
    </section>
  </section>
</template>

<script setup>
  import { onMounted, reactive, ref, toRefs, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  const store = useStore()
  const router = useRouter()
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  // 获取当前组件实例
  const { proxy } = getCurrentInstance()
  const treeRef = ref()
  const formRef = ref()
  const treeFormRef = ref()
  const scrollRef = ref()
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    buttonAuthList: [],
    hasLoad: false,
    originalFormInline: {
      name: '',
      confidentialityLevel: null,
    },
    searchForm: {
      name: '',
      level: 0,
      confidentialityLevel: null,
      domainId: null,
    },
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'number', name: '序号', width: 80 },
      { prop: 'cnName', name: '中文名称' },
      { prop: 'name', name: '英文名称' },
      // { prop: 'bizDomainName', name: '业务域' },
      { prop: 'tableLevel', name: '表密级', slot: 'tableLevel' },
      { prop: 'filedNumber', name: '字段总数' },
      { prop: 'filedLevels', name: '定密', slot: 'filedLevels' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    secretOptions: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
    tableData: [],
    tableHeight: 436,
    treeSearchText: '',
    treeData: [],
    defaultTreeData: [],
    prop: {
      label: 'name',
      value: 'id',
      children: 'childrenList',
    },
  })

  // 获取树列表
  const getTreeListFn = () => {
    sceneManage.searchTreeList().then((res) => {
      if (res.code === 'SUCCESS') {
        let treeData = []
        if (res.data.length > 0) {
          treeData = [...res.data]
          treeData[0].selected = true
          state.searchForm.domainId = treeData[0].id
          state.searchForm.level = treeData[0].level
        } else {
          treeData.push({
            children: [],
            id: null,
            level: 0,
            label: '全部',
            type: 'ROOT',
          })
        }
        if (treeData.length > 0) {
          treeData[0].expanded = true
        }
        state.treeData = [...treeData]
        state.defaultTreeData = [...treeData]
        onSearch(true)
      }
    })
  }
  //匹配中文
  const matchingCname = (confidentialityLevel) => {
    let cName = ''
    switch (confidentialityLevel) {
      case 'PUBLIC':
        cName = '公开'
        break
      case 'INTERIOR':
        cName = '内部'
        break
      case 'CONTROLLED':
        cName = '受控'
        break
      case 'SECRET':
        cName = '秘密'
        break
      case 'CONFIDENTIAL':
        cName = '机密'
        break
      case 'CORE':
        cName = '核心'
        break

      default:
        cName = '--'
        break
    }
    return cName
  }
  //匹配数组中文
  const matchingArr = (filedLevels) => {
    let arr = []
    filedLevels.forEach((val) => {
      let cName = ''
      switch (val) {
        case 'PUBLIC':
          cName = '公开'
          break
        case 'INTERIOR':
          cName = '内部'
          break
        case 'CONTROLLED':
          cName = '受控'
          break
        case 'SECRET':
          cName = '秘密'
          break
        case 'CONFIDENTIAL':
          cName = '机密'
          break
        case 'CORE':
          cName = '核心'
          break

        default:
          cName = '--'
          break
      }
      arr.push({
        name: val,
        cName: cName,
      })
    })

    return arr
  }
  // 树搜索
  const searchTreeFn = () => {
    state.treeData = filterTreeData(state.defaultTreeData, state.treeSearchText)
  }
  // 循环过滤
  const filterTreeData = (treeData, text) => {
    // 使用map复制一下节点，避免修改到原树
    return treeData
      .map((node) => ({ ...node }))
      .filter((node) => {
        node.children = node.children && filterTreeData(node.children, text)
        return (
          String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
          (node.children && node.children.length)
        )
      })
  }
  // 树点击事件
  const clickFn = (node) => {
    if (state.searchForm.domainId !== node.id) {
      state.searchForm.domainId = node.id
      state.searchForm.level = node.level
      onSearch(true)
    }
  }
  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.searchForm[key] = state.originalFormInline[key]
    }
    onSearch(true)
  }
  const onSearch = (init) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    let data = {
      condition: {
        name: state.searchForm.name || null,
        bizDomainId: state.searchForm.level < 2 ? null : state.searchForm.domainId,
        confidentialityLevel: state.searchForm.confidentialityLevel || null,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    state.loading = true
    api.assets
      .getAssetsTables(data)
      .then((res) => {
        state.loading = false
        // 新增序号属性
        res.data.list.map((item, index) => {
          return Object.assign(item, { number: index + 1 })
        })
        state.tableData = res.data.list
        state.pagination.total = res.data.total
      })
      .catch(() => {
        state.loading = false
      })
  }
  const resetFn = () => {
    state.originalFormInline = {
      name: '',
      confidentialityLevel: null,
    }
    searchClickFn()
  }

  // 展开或收缩树
  const treeChange = () => {
    if (state.searchForm.domainId) {
      state.treeData = updateTree(state.treeData, state.searchForm.domainId)
    }
  }

  // 循环修改
  const updateTree = (arr, id) => {
    arr.forEach((val) => {
      if (val.id === id) {
        val.selected = true
      } else {
        val.selected = false
      }
      if (val.children) {
        updateTree(val.children, id)
      }
    })
    return arr
  }

  // 表格操作变化
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    onSearch()
  }

  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  // 定密
  const tableLevelFn = (row, item) => {
    api.assets
      .assetsTableSave({
        assetsId: item.assetsId,
        confidentialityLevel: row.value,
      })
      .then((res) => {
        let { success } = res
        if (success) {
          onSearch(false)
        }
      })
  }
  const setTableHeight = () => {
    state.tableHeight = document.body.offsetHeight - 298
  }
  setTableHeight()
  onMounted(() => {
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList

    getTreeListFn()
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    &.isLzos {
      padding: 0;
    }
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      .tools {
        height: 50px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 2px;
        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 50px;
          padding: 0 16px;

          &-title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            width: 100%;
            height: 52px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            :deep(.button-content) {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
            }
            &-btn {
              position: absolute;
              right: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              color: $themeBlue;
              font-weight: 400;
              font-size: 16px;
              border-radius: 6px;
              cursor: pointer;

              &:hover {
                background-color: #e3ecff;
              }
            }
          }
          &.date {
            height: 36px;
            :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
              width: 260px;
            }
          }
          .col {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .label {
              color: #1d2129;
              font-size: 14px;
            }
          }
          &.tabs {
            align-items: flex-end;
            height: 48px;
            :deep(.nancalui-tabs) {
              .nancalui-tabs-nav-tab {
                border-bottom: none;
              }
            }
          }
          :deep(.button-content) {
            .add {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
              .arrow {
                margin-left: 4px;
                color: #fff;
                font-size: 16px;
              }
            }
          }

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 0 8px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 14px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
            }
          }

          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 32px;
          }

          .search {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-btn {
              width: 60px;
              height: 30px;
              text-align: center;
              line-height: 30px;
              color: #fff;
              font-weight: 400;
              font-size: 14px;
              border: 1px solid #1e89ff;
              border-radius: 2px;
              cursor: pointer;
              background-color: #1e89ff;

              &.reset {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;
                color: #1d2129;
                background-color: #fff;
                border: 1px solid #dcdfe6;
                &:hover {
                  color: #479dff;
                  background-color: #fff;
                  border: 1px solid #479dff;
                }
                .icon {
                  margin-left: 4px;
                  font-size: 10px;
                }
              }

              &:hover {
                background-color: #479dff;
                border: 1px solid #479dff;
              }
            }
          }
        }
      }
      .table {
        height: calc(100% - 60px);
        margin-top: 8px;
        background-color: #fff;
        border-radius: 8px;

        .title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            margin: auto;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }

        &-tree {
          display: inline-block;
          box-sizing: border-box;
          width: 272px;
          height: calc(100% - 52px);
          padding: 8px 16px;
          overflow-y: auto;
          vertical-align: top;
          border-right: 1px solid #e5e5e5;

          &-ipt {
            margin-bottom: 8px;

            :deep(.nancalui-input-slot__suffix) {
              opacity: 0.5;

              .icon-search {
                font-weight: normal;
                transform: scale(1.4);
              }
            }
          }

          &-header {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 92px;
            height: 32px;
            margin-bottom: 8px;
            color: $themeBlue;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
          }

          :deep(.nancalui-tree) {
            .nancalui-tree__node {
              border-radius: 6px;

              &:has(.active) {
                background-color: #ecf7ff;
              }

              &:hover {
                background-color: #ecf7ff;

                .nancalui-tree__node-content--value-wrapper {
                  .tree-icon {
                    color: $themeBlue;
                  }

                  .tree-label {
                    max-width: calc(100% - 73px);
                    color: $themeBlue;
                  }

                  .tree-btn {
                    display: inline;
                  }
                }
              }

              .nancalui-tree__node-vline {
                width: 0;
              }

              .nancalui-tree__node-content {
                border-radius: 6px;

                &:hover {
                  background-color: #ecf7ff;
                }

                > span {
                  width: 28px;
                  padding-left: 10px;
                  color: #8091b7;
                }

                .nancalui-tree__node-content--value-wrapper {
                  position: relative;
                  width: 100%;
                }

                &.active {
                  background-color: #ecf7ff;

                  .nancalui-tree__node-content--value-wrapper {
                    .tree-label,
                    .tree-icon {
                      color: $themeBlue;
                    }
                  }

                  & > span {
                    svg {
                      color: $themeBlue;
                    }
                  }
                }

                .tree-icon {
                  min-width: 16px;
                  margin-right: 4px;
                  color: #8091b7;
                  font-size: 16px;
                }

                .tree-label {
                  max-width: calc(100% - 20px);
                  overflow: hidden;
                  color: rgba(0, 0, 0, 0.85);
                  font-size: 14px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .tree-btn {
                  display: none;
                  width: 80px;

                  .icon {
                    margin-left: 4px;
                    color: $themeBlue;
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }

        &-content {
          display: inline-block;
          box-sizing: border-box;
          width: 100%;
          height: calc(100% - 46px);
          padding: 0;
          vertical-align: top;

          &-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 48px;

            :deep(.nancalui-tabs) {
              width: 100%;

              .nancalui-tabs-nav-tab {
                padding: 0 16px;
              }
            }
          }

          &-box {
            box-sizing: border-box;
            height: calc(100% - 100px);
            padding: 8px 16px;
          }

          .empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: calc(100% - 100px);

            .pic {
              width: 72px;
              height: auto;
              margin-bottom: 10px;
            }

            &-word {
              color: rgba(0, 0, 0, 0.46);
              font-weight: 400;
              font-size: 14px;
              text-align: center;
            }
          }
        }
      }
    }
  }
  .tag-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .tag {
      margin-right: 6px;
    }
    &-more {
      height: 28px;
      line-height: normal;
    }
  }
  .tag {
    width: 44px;
    height: 24px;
    padding: 0 4px;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    border: 1px solid;
    border-radius: 6px;
    &.PUBLIC {
      color: #04c495;
      background: #e6fff4;
      border-color: #04c495;
    }
    &.INTERIOR {
      color: rgba(0, 0, 0, 0.75);
      background: #e3ecff;
      border-color: #a3b4db;
    }
    &.CONTROLLED {
      color: #447dfd;
      background: #e3ecff;
      border-color: #447dfd;
    }
    &.SECRET {
      color: #ec3b9c;
      background: #fff0f6;
      border-color: #ec3b9c;
    }
    &.CONFIDENTIAL {
      color: #ff7d00;
      background: #fff6e6;
      border-color: #ff7d00;
    }

    &.CORE {
      color: #f63838;
      background: var(---Error-, #fff2f0);
      border-color: #f63838;
    }
  }
</style>
