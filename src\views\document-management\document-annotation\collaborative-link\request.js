import axios from 'axios'
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus'
// 创建一个axios实例
const service = axios.create({
  baseURL: import.meta.env.BASE_URL + '',
  timeout: 1000 * 60 * 5, // request timeout 5分钟
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    if (config.responseType && config.responseType !== 'blob') {
      //blob形式设置请求头会报错
      config.headers['Accept'] =
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'
      config.headers['Accept-Encoding'] = 'gzip,deflate'
      config.headers['Accept-Language'] = 'zh-CN,zh;q=0.9'
      config.headers['Connection'] = 'keep-alive'
      config.headers['Upgrade-Insecure-Requests'] = '1'
      config.headers['Cache-Control'] = 'no-cache,no-store'
    }
    // if (getToken()) {
    //   config.headers['X-Token'] = getToken().replace(/%20/g, ' ')
    // }
    return config
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data

    if (response.config.responseType) {
      response.headers['Connection'] = 'keep-alive'
      response.headers['Content-Type'] =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      // response.headers[
      //   'content-disposition'
      // ] = `attachment;filename*=utf-8''%E6%A8%A1%E6%9D%BF.xlsx`
      response.headers['Cache-Control'] = 'no-cache,no-store'
      return res
    }

    // 定制码不是200，则判定为错误。
    if (response.status !== 200) {
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      if (res.code === 'SUCCESS' || res.code === '200') {
        return res
      } else {
        // 50008:非法令牌;50012:其他客户端登录;TOKEN_IS_EXPIRED:令牌过期,security_1001:token失效;
        if (
          res.code === 50008 ||
          res.code === 50012 ||
          res.code === 'TOKEN_IS_EXPIRED' ||
          res.code === 'USER_CONTEXT_MISS' ||
          res.code === 'security_1001' ||
          res.code === 'security_1002'
        ) {
          // 重新登陆
          // Cookies.set(TokenKey, '', {
          //   domain: location.hostname,
          // })
        }
        if (response.config.isNoPop) {
          return res
        }
        return false
      }
    }
  },
  (error) => {
    //前端手动取消请求接口
    if (axios.isCancel(error)) {
      console.log('请求取消')
      return Promise.reject(error)
    }

    const res = error?.response?.data
    if (!res) {
      return false
    }
    if (
      res.code === 50008 ||
      res.code === 50012 ||
      res.code === 'TOKEN_IS_EXPIRED' ||
      res.code === 'USER_CONTEXT_MISS' ||
      res.code === 'security_1001' ||
      res.code === 'security_1002'
    ) {
      let msg = '登录已过期，请重新登录'
      if (res.code === 'USER_CONTEXT_MISS') {
        msg = error.message
      }
    } 
    return Promise.reject(error)
  },
)

export default service
