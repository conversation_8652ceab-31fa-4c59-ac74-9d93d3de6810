<template>
  <div class="content">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="btn" @click.stop.prevent="runFn" v-loading="state.loading">
        <SvgIcon class="icon" icon="icon-offline-start" />
        运行
      </div>
      <div class="btn" @click="submitFn(false, true)">
        <SvgIcon class="icon" icon="icon-offline-save" />
        保存
      </div>
      <div class="btn" @click.stop.prevent="submitFn(true)">
        <SvgIcon class="icon" icon="icon-offline-submit" />
        提交到生产环境
      </div>
    </div>
    <div class="page-content">
      <div class="page-title"> HiveDDL </div>
      <n-form :data="state" ref="formRef" label-width="100px" message-type="text">
        <div class="data-async">
          <div class="content-title">
            <span>基本属性</span>
          </div>
          <div class="form-row">
            <n-form-item
              field="formData.tableName"
              label="表英文名称："
              :rules="[
                { required: true, message: '请输入英文名称', trigger: 'blur' },
                { validator: validateEnName },
              ]"
            >
              <n-input v-model="state.formData.tableName" :disabled="state.type" autofocus />
            </n-form-item>
            <n-form-item
              field="formData.tableComment"
              label="表中文名称："
              :rules="[
                {
                  required: true,
                  validator: (...args) =>
                    checkCName500C(...args, null, null, {
                      nameType: 'CN',
                      name: state.formData.tableComment,
                      id: null,
                    }),
                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="state.formData.tableComment" autofocus />
            </n-form-item>
          </div>
          <div class="content-title">
            <span>表信息</span>
          </div>
          <div class="form-row">
            <n-form-item
              field="formData.securityLevel"
              label="表密级："
              :rules="[{ required: true, message: '请输入英文名称', trigger: 'change' }]"
            >
              <n-select
                v-model="state.formData.securityLevel"
                placeholder="请选择"
                filter
                allow-clear
                :options="state.confidentialityLevelOptions"
              />
            </n-form-item>

            <n-form-item field="formData.tagList" label="标签：">
              <div class="mid-line-tag">
                <el-tree-select
                  ref="selectTree"
                  v-model="state.formData.tagList"
                  :data="state.targetOptions"
                  style="width: 100%"
                  node-key="key"
                  :props="{
                    label: 'name',
                    value: 'key',
                    children: 'children',
                  }"
                  multiple
                  show-checkbox
                  :render-after-expand="false"
                  filterable
                  clearable
                />
              </div>
            </n-form-item>
          </div>

          <div class="content-title">
            <span>表结构</span>
          </div>
          <div class="form-row">
            <n-form-item field="formData.desc" label="建模模式：">
              <n-radio-group direction="row" v-model="state.formData.mode">
                <n-radio value="WIZARD">向导模式</n-radio>
                <n-radio value="DDL">DDL模式</n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              v-if="state.formData.mode === 'WIZARD'"
              field="formData.desc"
              label="表类型："
            >
              <div style="position: relative">
                <n-checkbox
                  label="非分区表"
                  :disabled="state.type"
                  :isShowTitle="false"
                  v-model="state.formData.formType"
                />
                <n-popover
                  :position="['end']"
                  align="end"
                  trigger="hover"
                  style="background-color: rgba(0, 0, 0, 1); color: #fff"
                >
                  <template #content>
                    <div>
                      *
                      默认创建hive非分区表，取消勾选则创建分区表，不支持指定分区，系统将自动分配分区。
                    </div>
                  </template>
                  <svg
                    style="position: absolute; top: 0; right: -18px"
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_923_243823)">
                      <path
                        d="M0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8C15.5 12.1421 12.1421 15.5 8 15.5C3.85786 15.5 0.5 12.1421 0.5 8ZM8.75 4.75C8.75 4.33579 8.41421 4 8 4C7.58579 4 7.25 4.33579 7.25 4.75C7.25 5.16421 7.58579 5.5 8 5.5C8.41421 5.5 8.75 5.16421 8.75 4.75ZM8.2889 6.59183C8.13836 6.48533 7.94143 6.47032 7.7765 6.55279L6.7765 7.05279C6.52951 7.17628 6.4294 7.47662 6.5529 7.72361C6.67639 7.9706 6.97673 8.07071 7.22372 7.94722L7.38809 7.86503L7.00397 10.938C6.98267 11.1084 7.05031 11.2778 7.18312 11.3867C7.31592 11.4955 7.49531 11.5286 7.65822 11.4743L9.15822 10.9743C9.42019 10.887 9.56178 10.6039 9.47445 10.3419C9.38713 10.0799 9.10397 9.93834 8.842 10.0257L8.09465 10.2748L8.49625 7.06202C8.51912 6.87905 8.43943 6.69834 8.2889 6.59183Z"
                        fill="#8091B7"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_923_243823">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </n-popover>
              </div>
            </n-form-item>
          </div>

          <n-button v-if="state.formData.mode === 'WIZARD'" color="primary" @click="addFiledClick()"
            ><svg
              style="margin-right: 4px"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M8 2.66699V13.3337"
                stroke="#1E89FF"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2.66602 8H13.3327"
                stroke="#1E89FF"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            添加字段</n-button
          >
          <n-table v-if="state.formData.mode === 'WIZARD'" :data="state.fieldArr">
            <n-column resizeable type="index" header="序号" width="80">
              <template #default="scope">
                {{ `${scope.rowIndex + 1}` }}
              </template>
            </n-column>
            <n-column resizeable field="comment" header="中文名">
              <template #default="scope">
                <n-form-item
                  :field="`fieldArr[${scope.rowIndex}].comment`"
                  :rules="
                    scope.rowIndex >= 0 && {
                      required: true,
                      validator: (...args) =>
                        checkCName500C(...args, null, null, {
                          nameType: 'CN',
                          name: scope.row.comment,
                          id: null,
                        }),
                      trigger: 'blur',
                    }
                  "
                  style="margin-top: 8px"
                >
                  <n-input v-model="scope.row.comment" placeholder="请输入中文名" />
                </n-form-item>
              </template>
            </n-column>
            <n-column resizeable field="name" header="英文名">
              <template #default="scope">
                <n-input v-model="scope.row.name" :maxLength="30" placeholder="请输入英文名" />
              </template>
            </n-column>
            <n-column resizeable field="dataType" header="字段类型">
              <template #default="scope">
                <n-select v-model="scope.row.dataType" :options="state.fieldTypeOptions" />
              </template>
            </n-column>
            <n-column resizeable field="dataLength" header="字段长度" width="160">
              <template #default="scope">
                <n-input v-model="scope.row.dataLength" placeholder="请输入字段长度" />
              </template>
            </n-column>
            <n-column resizeable field="status" header="字段提交状态" />
            <n-column resizeable header="操作" align="right">
              <template #default="scope">
                <!-- <n-button variant="text" color="primary" @click="handleClick(scope.row)"
                  >编辑</n-button
                > -->
                <n-button variant="text" color="primary" @click="delClick(scope.rowIndex)"
                  >删除</n-button
                >
              </template>
            </n-column>
          </n-table>

          <!-- 日志输出 -->
          <n-button
            v-if="state.formData.mode === 'WIZARD'"
            variant="solid"
            @click="createHiveDDLFormFunc"
            ><svg
              style="margin-right: 4px"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <rect
                x="2"
                y="1"
                width="12"
                height="14"
                rx="1"
                stroke="white"
                stroke-linecap="round"
              />
              <path d="M9 6L11 8L9 10" stroke="white" stroke-linecap="round" />
              <path d="M7 6L5 8L7 10" stroke="white" stroke-linecap="round" />
            </svg>
            DDL</n-button
          >

          <codemirror
            ref="myCm"
            v-if="state.formData.mode === 'DDL' || state.formData.ddl != ' '"
            v-model:value="state.formData.ddl"
            class="codemirror"
            :options="state.sqlOption"
            @ready="onCmReady"
            @focus="onCmFocus"
            @input="onCmCodeChange"
          />
        </div>
      </n-form>
    </div>
    <!--    <div-->
    <!--      style="width: 0"-->
    <!--      v-html="-->
    <!--        `<style>-->
    <!--        .mid-line-tag{-->
    <!--          ${toStyle}-->
    <!--        }-->
    <!--    </style>`-->
    <!--      "-->
    <!--    >-->
    <!--    </div>-->

    <section
      v-loading="state.loading"
      v-show="state.logShow"
      element-loading-text="日志加载中..."
      :class="{ logPop: true, showRun: state.showRunLog }"
    >
      <div class="logPop-head">
        <div class="logTitle">运行日志</div>

        <div class="logPop-btn">
          <n-button
            color="primary"
            :disabled="!state.runLogText || state.loading"
            @click.prevent.stop="downLogFn"
            >下载日志</n-button
          >
          <SvgIcon
            :class="{ icon: true, show: state.showRunLog }"
            icon="icon-arrow-second"
            @click.prevent.stop="state.showRunLog = !state.showRunLog"
          />
        </div>
      </div>

      <div class="logText" v-if="state.showRunLog">
        <div v-if="state.runLogText !== ''" class="textarea" v-html="state.runLogText"></div>
        <div
          v-if="state.runLogText === '' || (state.runLogText === null && state.showRunLog)"
          class="empty"
        >
          <img class="empty-pic" src="@/assets/img/empty_gray.png" />
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { ref, toRefs, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { timestampToTime } from '@/const/public.js'
  import {
    taskSave,
    createHiveDDLForm,
    taskDetail,
    taskRun,
    taskCommitRun,
    taskRunLog,
    hiveTableDefaultClause,
  } from '@/api/dataManage.js'
  import { checkCName500C } from '@/utils/validate'
  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'

  const props = defineProps({
    taskId: {
      type: String,
      default: null,
    },
  })

  const myCm = ref('myCm')
  const codemirrorEdit = ref(null)
  const selectTree = ref(null)
  let runStatus = ref('waiting')
  const state = reactive({
    logShow: false,
    runLogText: '',
    showRunLog: false,
    nodeName: '',
    type: false,
    loading: false,
    formData: {
      tableName: '',
      tableComment: '',
      securityLevel: 'INTERIOR',
      tagList: [],
      mode: 'WIZARD',
      formType: true,
      ddl: ' ',
    },
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
    customColor: '',
    fieldArr: [],
    confidentialityLevelOptions: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
    fieldTypeOptions: [
      { value: 'STRING', name: '字符串(STRING)' },
      { value: 'BINARY', name: '字节序列(BINARY)' },
      { value: 'BOOLEAN', name: '布尔(BOOLEAN)' },
      { value: 'DOUBLE', name: '数值(DOUBLE)' },
      { value: 'FLOAT', name: '浮点(FLOAT)' },
      { value: 'TIMESTAMP', name: '时间戳(TIMESTAMP)' },
      { value: 'BIGINT', name: '长整数(BIGINT)' },
      { value: 'INT', name: '整数(INT)' },
      { value: 'TINYINT', name: '短整型(TINYINT)' },
      { value: 'DECIMAL', name: '任意数字(DECIMAL)' },
      { value: 'DATE', name: '日期(DATE)' },
    ],
    colorList: [
      { value: '#1F84E1', bgColor: '#EDF5FD', checked: true },
      { value: '#23BD4E', bgColor: '#EDFAF1', checked: false },
      { value: '#FFA800', bgColor: '#FFF6E5', checked: false },
      { value: '#5D5FEF', bgColor: '#F2F2FE', checked: false },
      { value: '#B7C1C7', bgColor: '#F9FAFB', checked: false },
    ],
    targetOptions: [],
    popoverKey: 1,

    tableHeadTitles: [],
    tableData: [],
    pagination: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
  })
  let clauseRes
  hiveTableDefaultClause().then((res) => {
    clauseRes = res.data
  })

  const emits = defineEmits(['changeLoadingStatus'])
  watch(
    state,
    (newValue, oldValue) => {
      let obj = sessionStorage.getItem('workCacheData')
        ? JSON.parse(sessionStorage.getItem('workCacheData'))
        : {}
      if (newValue?.codemirror) {
        delete newValue.codemirror
      }

      obj[props.taskId] = newValue

      sessionStorage.setItem('workCacheData', JSON.stringify(obj))
    },
    { deep: true },
  )

  watch(
    runStatus,
    (newValue) => {
      emits('changeLoadingStatus', {
        taskId: props.taskId,
        runStatus: newValue,
      })
    },
    { deep: true },
  )

  function checkRunStatusCache() {
    const runStatusCache = sessionStorage.getItem(`hiveDDL_runStatus_${props.taskId}`)
    if (runStatusCache) {
      const { status, runInstanceId } = JSON.parse(runStatusCache)
      if (status === 'saving') {
        runFn()
      }
      if (status === 'running') {
        taskRunLogFn(runInstanceId)
      }
    }
  }

  onBeforeMount(() => {
    getTargetList()

    const workCacheData = sessionStorage.getItem('workCacheData')
    if (
      workCacheData &&
      Object.keys(JSON.parse(workCacheData)).indexOf(JSON.stringify(props.taskId)) > -1
    ) {
      let {
        type,
        formData,
        customColor,
        fieldArr,
        targetOptions,
        popoverKey,
        tableHeadTitles,
        tableData,
        pagination,
      } = JSON.parse(workCacheData)[props.taskId]

      state.type = type
      state.formData = formData
      state.customColor = customColor
      state.fieldArr = fieldArr
      state.targetOptions = targetOptions
      state.popoverKey = popoverKey
      state.tableHeadTitles = tableHeadTitles
      state.tableData = tableData
      state.pagination = pagination

      // 数据还原完毕后再判断 session
      checkRunStatusCache()
    } else {
      getDetailFunc().then(() => {
        // 数据获取完毕后再判断 session
        checkRunStatusCache()
      })
    }
  })

  // 下载日志
  const downLogFn = () => {
    if (state.runLogText) {
      state.runLogText = state.runLogText.replace(/<[^>]+>/g, '')
      const blob = new Blob([state.runLogText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        state.nodeName + '-' + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  // css转换 计算属性
  const toStyle = computed(() => {
    return state.formData.tagList?.map((_, i) => {
      const item = state.targetOptions.find((item) => item.name === _)
      const [color, background] = item.color.split('_')
      return `
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag {
          background: ${background} !important;
          border: 1px solid var(---, ${color}) !important;
          color: ${color} !important;
        }
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag  .el-tag__close{
            color: ${color} !important;
         }
        `
    }).join(`
        `)
  })

  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.formData.ddl = newCode
  }

  const validateEnName = (rule, value, callback) => {
    const regex = /^[a-zA-Z][a-zA-Z0-9_]{1,79}$/
    return regex.test(value)
      ? true
      : callback(new Error('请输入英文、数字、下划线，只能以英文开头，2~80个字符'))
  }

  // 运行
  const runFn = async () => {
    state.logShow = true
    if (state.loading) {
      return false
    }
    // 运行前，先存储"saving"状态
    sessionStorage.setItem(
      `hiveDDL_runStatus_${props.taskId}`,
      JSON.stringify({
        status: 'saving',
        time: Date.now(),
      }),
    )
    const { tableName, tableComment, securityLevel, mode, formType, ddl } = state.formData
    const columns = []
    let flag = false
    const nullEnField = []
    state.fieldArr.forEach((item, index) => {
      item.orderNum = index + 1
      if (!item.name) {
        flag = true
        nullEnField.push(item.orderNum)
      }
      columns.push(item)
    })
    if (flag && state.formData.mode === 'WIZARD') {
      ElMessage.error('第' + nullEnField.join(',') + '条数据英文名不能为空')
      return false
    }
    let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
    const dataTag =
      activeOptions?.map((val) => {
        return { text: val.name, color: val.color, id: val.id || null }
      }) || []

    let data = {
      hiveDdlTaskBO: {
        //任务配置-HiveDDL
        columns,
        creationMode: mode, //表创建模式:WIZARD-向导模式,DDL-DLL模式,可用值:DDL,WIZARD
        dataTag,
        ddl: ddl + '\n' + clauseRes, //建表DDL
        partitionTable: !formType, //是否是分区表
        securityLevel, //数据密级,可用值:CONFIDENTIAL,CONTROLLED,CORE,INTERIOR,PUBLIC,SECRET
        submittedToProdEnv: true, //是否已提交到生产环境
        tableComment, //表注解/中文名
        tableName, //表名
      },
      id: props.taskId, //修改ID
      taskType: 'OW_HIVE_DDL', //节点类型,可用值:CONDITIONS,DATAX,FLINK,HTTP,PYTHON,SEATUNNEL,SHELL,SPARK,SQL,SUB_PROCESS
    }
    runStatus.value = 'running'
    await submitFn(false)
    state.loading = true
    state.isRun = true

    taskRun(data)
      .then((res) => {
        sessionStorage.removeItem(`hiveDDL_runStatus_${props.taskId}`)
        if (res.success && state.isRun) {
          state.dsProcessCode = res.data.dsProcessCode || null
          state.runCode = res.data.runId || null
          state.runTime = res.data.runTime || null

          state.showRunLog = true
          taskRunLogFn(res.data.runInstanceId)
        } else {
          state.loading = false
          state.sqlOption.readOnly = false
          state.logLoading = false
          state.resultLoading = false
          state.isRun = false
          runStatus.value = 'fail'
        }
      })
      .catch(() => {
        sessionStorage.removeItem(`hiveDDL_runStatus_${props.taskId}`)
        runStatus.value = 'fail'
      })
      .finally(() => {
        sessionStorage.removeItem(`hiveDDL_runStatus_${props.taskId}`)
        state.loading = false
        state.sqlOption.readOnly = false
        state.logLoading = false
        state.resultLoading = false
        state.isRun = false
      })
  }

  // 运行日志
  const taskRunLogFn = async (runInstanceId) => {
    const res = await taskRunLog({ runInstanceId })
    state.loading = true

    if (res.code === 'SUCCESS') {
      state.runLogText = res.data.log

      // 运行中，存储状态
      if (res.data.state === 'RUNNING_EXECUTION') {
        runStatus.value = 'running'
        // 存储到 sessionStorage
        sessionStorage.setItem(
          `hiveDDL_runStatus_${props.taskId}`,
          JSON.stringify({
            runInstanceId,
            time: Date.now(),
          }),
        )
        state.stateFlag = setTimeout(() => {
          taskRunLogFn(runInstanceId)
        }, 3000)
      } else {
        // 运行结束，清除 sessionStorage
        sessionStorage.removeItem(`hiveDDL_runStatus_${props.taskId}`)
        switch (res.data.state) {
          case 'FAILURE':
            runStatus.value = 'fail'
            ElNotification({
              title: '提示',
              message: '运行失败',
              type: 'error',
            })
            state.loading = false
            break
          case 'SUCCESS':
            runStatus.value = 'success'
            ElMessage.success('运行成功')
            state.loading = false
            break
          default:
            break
        }
      }
    } else {
      // 失败也清除
      sessionStorage.removeItem(`hiveDDL_runStatus_${props.taskId}`)
      runStatus.value = 'fail'
      state.loading = false
      state.runLogText += '<div class="red">运行失败</div>'
    }
  }

  // SQL语句获取焦点时
  const onCmFocus = (cm) => {
    codemirrorEdit.value = cm
    codemirrorEdit.value.setOption('readOnly', state.formData.mode === 'WIZARD')
  }
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    codemirrorEdit.value = cm
    codemirrorEdit.value.setSize('-webkit-fill-available', 'auto')
    codemirrorEdit.value.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      codemirrorEdit.value.showHint(config)
    })
    codemirrorEdit.value.setValue(state.formData.ddl)
    codemirrorEdit.value.setOption('readOnly', state.formData.mode === 'WIZARD')
  }

  // 获取详情
  const getDetailFunc = async () => {
    const res = await taskDetail({ id: props.taskId })
    if (res.code === 'SUCCESS' && res.data.versionBO?.hiveDdlTaskBO) {
      state.nodeName = res.data.name
      const {
        tableName,
        tableComment,
        securityLevel,
        partitionTable,
        dataTag,
        columns,
        ddl,
        creationMode,
      } = res.data.versionBO?.hiveDdlTaskBO
      const tagList = dataTag?.map((item) => 'tag_' + item.id)
      if (res.data.versionBO?.hiveDdlTaskBO && res.data.versionBO?.version !== -1) {
        state.type = true
      }
      state.formData = {
        tableName,
        tableComment,
        securityLevel,
        tagList,
        mode: creationMode,
        formType: !partitionTable,
        ddl,
      }
      state.fieldArr = []
      columns?.forEach((item) => {
        item.status = '已提交'
        state.fieldArr.push(item)
      })
    }
  }

  // 标签输入完成
  const changeTargetFn = (flag, isSave) => {
    if (isSave) {
      if (state.customName) {
        if (customNameBlur()) {
          state.formData.tagList.push(state.customName)
          let checkColorItem = state.colorList.filter((val) => val.checked)[0]
          if (state.customColor) {
            let index = Math.floor(Math.random() * 5)
            checkColorItem = state.colorList[index]
          }
          state.targetOptions.push({
            name: state.customName,
            value: state.customName,
            text: state.customName,
            color: checkColorItem.value + '_' + checkColorItem.bgColor,
          })
          state.targetKey++
        } else {
          return false
        }
      } else {
        ElNotification({
          title: '提示',
          message: '请输入标签名称！',
          type: 'warning',
        })
        return false
      }
    }
    if (flag) {
      state.customName = ''
      state.colorList = state.colorList.map((val, ind) => {
        if (ind === 0) {
          val.checked = true
        } else {
          val.checked = false
        }
        return val
      })
    } else {
      state.popoverKey++
    }
  }

  // 自定义标签输入验证
  const customNameBlur = () => {
    let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
    let res = regex.test(state.customName)
    if (res && state.customName.length > 1) {
      if (state.targetOptions.filter((val) => val.name === state.customName).length === 0) {
        return true
      } else {
        ElNotification({
          title: '提示',
          message: '标签名重复，请重新填写！',
          type: 'warning',
        })
        return false
      }
    } else {
      ElNotification({
        title: '提示',
        message: '标签为2-8个字符，支持中文和英文！',
        type: 'warning',
      })
      return false
    }
  }

  // 选中颜色
  const checkFn = (index) => {
    state.colorList.forEach((val, ind) => {
      val.checked = false
      if (index === ind) {
        val.checked = true
      }
    })
  }

  const addFiledClick = () => {
    state.fieldArr.push({
      comment: '',
      name: '',
      dataType: '',
      dataLength: '',
      status: '未提交',
    })
  }

  // 删除字段
  const delClick = (indx) => {
    state.fieldArr.splice(indx, 1)
  }

  // 获取标签列表
  const getTargetList = () => {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        state.targetOptions = data
      }
    })
  }

  // 创建HiveDDL表
  const createHiveDDLFormFunc = async () => {
    const { tableName, tableComment } = state.formData
    const columns = []
    state.fieldArr.forEach((item, index) => {
      item.orderNum = index + 1
      columns.push(item)
    })
    const params = {
      columns,
      tableComment,
      tableName,
      partitionTable: !state.formData.formType,
    }
    try {
      const ddlRes = await createHiveDDLForm(params)
      if (ddlRes.code === 'SUCCESS') {
        state.formData.ddl = ddlRes.data
      }
    } catch (error) {
      console.error('生成DDL失败:', error)
      ElNotification({
        title: '错误',
        message: 'DDL生成失败',
        type: 'error',
      })
    }
  }

  // 提交到生产环境
  const submitFn = async (isCommit = false, isLog = false) => {
    const { tableName, tableComment, securityLevel, mode, tagList, formType, ddl } = state.formData
    const columns = []
    let flag = false
    const nullEnField = []
    state.fieldArr.forEach((item, index) => {
      item.orderNum = index + 1
      if (!item.name) {
        flag = true
        nullEnField.push(item.orderNum)
      }
      columns.push(item)
    })
    if (flag && state.formData.mode === 'WIZARD') {
      ElMessage.error('第' + nullEnField.join(',') + '条数据英文名不能为空')
      return false
    }
    let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
    const dataTag = []
    activeOptions?.forEach((val) => {
      if (val.color != null) {
        dataTag.push({ text: val.name, color: val.color, id: val.id || null })
      }
    })
    const params = {
      configInfoBO: {
        hiveDdlTaskBO: {
          //任务配置-HiveDDL
          columns,
          creationMode: mode, //表创建模式:WIZARD-向导模式,DDL-DLL模式,可用值:DDL,WIZARD
          dataTag,
          ddl: ddl + '\n' + clauseRes, //建表DDL
          partitionTable: !formType, //是否是分区表
          securityLevel, //数据密级,可用值:CONFIDENTIAL,CONTROLLED,CORE,INTERIOR,PUBLIC,SECRET
          submittedToProdEnv: true, //是否已提交到生产环境
          tableComment, //表注解/中文名
          tableName, //表名
        },
        id: props.taskId, //修改ID
        taskType: 'OW_HIVE_DDL', //节点类型,可用值:CONDITIONS,DATAX,FLINK,HTTP,PYTHON,SEATUNNEL,SHELL,SPARK,SQL,SUB_PROCESS
      },
      id: props.taskId, //修改ID
    }
    if (isCommit) {
      const res = await taskCommitRun(params)
      if (res.code === 'SUCCESS') {
        ElMessage.success('提交成功')
        getDetailFunc()
      } else {
        runStatus.value = 'fail'
      }
    } else {
      const res = await taskSave(params)
      if (res.code === 'SUCCESS') {
        isLog && ElMessage.success('保存成功')
        getDetailFunc()
      } else {
        runStatus.value = 'fail'
      }
    }
  }

  defineExpose({
    state,
  })
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    height: 100%;
    padding-bottom: 58px;
    overflow-y: auto;
    .toolbar {
      display: flex;
      flex-shrink: 0;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 8px;
      border-bottom: 1px solid #dcdfe6;
      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        box-sizing: border-box;
        padding: 4px 16px;
        color: #fff;
        font-size: 14px;
        background: #1e89ff;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;
        .icon {
          font-size: 16px;
        }
      }
    }
    .page-content {
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 7px 7px 7px 16px;
        color: #1d2129;
        color: var(----, #1d2129);
        font-weight: bolder;
        font-size: 16px;
        line-height: 24px;
        background-color: #fff;
        &[sub-label]::after {
          display: inline-block;
          margin-left: 12px;
          padding: 0px 4px;
          color: #1aa4ee;
          font-weight: 400;
          font-size: 12px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 22px;

          background: rgba(26, 164, 238, 0.08);
          border: 1px solid rgba(26, 164, 238, 0.4);
          border-radius: 2px;
          content: attr(sub-label);
        }
        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
    }
    .page-content {
      .data-async {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;
        .content-title {
          position: relative;
          width: 100%;
          height: 30px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
        .data-source {
          display: flex;
          flex: 1 0 0;
          gap: 6px;
          align-items: center;
          width: 100%;
          height: 32px;
          padding: 5px 6px 5px 10px;
          overflow: hidden;
          color: var(----, #1d2129);
          font-weight: 400;
          font-size: 14px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 32px;
          white-space: nowrap;
          text-overflow: ellipsis;
          background: var(---, #f5f7fa);
          border: 1px solid var(---, #e5e6eb);
          border-radius: 2px;
        }
        .form-row {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          width: 100%;
          margin-bottom: -8px;
          column-gap: 48px;
        }
      }
      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 4px 16px;
        color: var(---, #1e89ff);
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 22px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;

        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }
        &.no-border {
          color: var(---, #1e89ff);
          background: transparent;
          border: none;
        }
      }
      .log {
        display: flex;
        flex: 1 0 0;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
        align-self: stretch;
        min-height: 400px;
        padding: 8px;
        border: 1px solid var(---, #e5e6eb);
        border-radius: 2px;
      }
      .table-row {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        width: 100%;

        // grid-template-rows: repeat(3, 1fr);
        row-gap: 8px;
      }
    }

    .mid-line-tag {
      width: 100%;
      .el-select {
        width: 100%;
      }
      .nancalui-select {
        display: inline-block;
        width: calc(100% - 126px);
        height: 34px;
        margin-right: 8px;
      }
      .tagBtn {
        float: right;
      }
    }
  }

  .custom {
    padding: 6px;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 14px;

      .icon {
        color: #8091b7;
        font-size: 16px;
        cursor: pointer;
      }
    }

    &-name {
      margin-top: 8px;
    }

    &-color {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 12px;
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;

      .nancalui-switch {
        margin-left: 6px;
      }

      &-label {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 4px;

        .icon {
          display: none;
          color: #fff;
          font-size: 12px;
        }

        &.checked {
          box-shadow: 0 0 0 1px #447dfd;

          .icon {
            display: block;
          }
        }
      }
    }

    &-footer {
      margin-top: 14px;
      text-align: right;

      .nancalui-button {
        min-width: 40px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }

  :deep(.nancalui-select__multiple) {
    height: 32px;
    overflow: auto;
  }

  ul.CodeMirror-hints {
    z-index: 2071;
  }

  .codemirror {
    height: 400px !important;
    background-color: #fff;
    :deep(.CodeMirror) {
      height: 100% !important;
      overflow: hidden;
      box-shadow: none;
    }
    :deep(.CodeMirror-scroll) {
      box-sizing: border-box;
      height: 100%;
      margin-right: -6px;
      padding: 0;
      overflow-x: hidden !important;
      overflow-y: auto !important;
      .CodeMirror-sizer {
        border-right: none;
        .CodeMirror-lines {
          padding: 0;
          //.CodeMirror-selected {
          //  height: 28px !important;
          //}
        }
        .CodeMirror-cursors {
          top: 5px;
        }
        .CodeMirror-code > div {
          padding: 2px 0;
        }
        .CodeMirror-linenumber {
          padding: 0 6px;
          text-align: center;
        }
        .CodeMirror-line {
          padding: 0 10px;
          > span {
            padding-right: 10px !important;
            color: #046c5c;
            font-size: 14px;
            word-break: break-all;
          }
        }
        .CodeMirror-linebackground {
          background-color: #f0f2f5;
        }
      }
    }
    :deep(.CodeMirror-gutters) {
      width: 32px;
      min-height: 100%;
      background-color: #e6e8eb;
      border-right: none;
    }
    :deep(.CodeMirror-vscrollbar) {
      visibility: initial !important;
      &::-webkit-scrollbar-thumb {
        background-color: #b1bcd6;
        border-radius: 6px;
        &:hover {
          background-color: #b1bcd6;
        }
      }
    }
  }

  .el-select {
    width: calc(100% - 120px);
  }

  .logPop {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 200;
    box-sizing: border-box;
    width: 100%;
    height: 56px;
    background: #fff;

    &.showRun {
      height: 400px;
    }

    &-head {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      padding: 8px 16px 0;
      background: linear-gradient(180deg, #f3f3f5 0%, #fff 100%);

      .logTitle {
        width: 106px;
        height: 40px;
        color: #1e89ff;
        font-weight: bold;
        line-height: 40px;
        text-align: center;
        background: #fff;
        border: 1px solid #e5e6eb;
        border-bottom: none;
      }

      .logPop-btn {
        .icon {
          margin-left: 10px;
          font-size: 16px;

          &.show {
            transform: rotate(180deg);
          }
        }
      }
    }

    .logText {
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 56px);
      margin-top: 8px;
      padding: 16px;
      overflow-y: auto;
      word-break: break-all;
      background: linear-gradient(181deg, #f3f3f5 0.86%, #fff 11.07%);

      .textarea {
        min-height: 100%;
        padding: 16px;
        color: #000000bf;
        white-space: pre-wrap;
        word-break: break-all;
        background-color: transparent;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        &-pic {
          width: 48px;
          height: 48px;
        }
        &-text {
          margin-top: 16px;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
</style>
