$leftWidth: var(--lwidth, 240px);
$rightWidth: var(--rwidth, calc(100% - 249px));
$topHeight: var(--tHeight, 100%);
$bottomHeight: var(--bHeight, calc(100% - 336px));
.da {
  height: 100%;
}
.dragNode {
  cursor: pointer;
  width: 100%;
  height: 80px;
  background: #ecf7ff;
  border-radius: 0px 12px 12px 0px;
  position: absolute;
  top: 50%;
  transform: translate(0%, -50%);
  display: none;
  align-items: center;
  justify-content: center;
}
// .verifyShowBox {
//   min-height: 80px;
// }
.graph-contextMenu {
  max-height: 240px;
  overflow: auto;
  li {
    padding: 8px 0;
  }
}

.dropdown_class {
  padding-left: 0px;
  // padding-right: 4px;
  li {
    padding: 6px 8px;
    cursor: pointer;
    list-style: none;
    img {
      width: 30px;
      height: 16px;
      margin-right: 14px;
    }
    &:hover {
      background-color: #ecf7ff;
      color: #447dfd;
    }
  }
}
.link-title {
  margin: 0 2px;
  height: 32px;
  line-height: 32px;
  background: #f2f3f6;
  border-radius: 2px;
  text-indent: 14px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}
:deep(.el-dropdown-menu__item) {
}
.menu-container {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  section.menu-block {
    height: 120px;
    img {
      width: 20px;
      object-fit: cover;
      image-rendering: pixelated;
    }
    svg {
      font-size: 20px;
    }
    border-right: 1px solid #ebedf0;
    .menuArea {
      padding: 5px;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      .menuNode {
        cursor: pointer;
        color: #333333;
        padding: 5px;
        min-width: 40px;
        text-align: center;
        &.disabled {
          opacity: 0.3;
          cursor: not-allowed;
          pointer-events: none;
          &:hover {
            background: #ffffff;
            color: #333333;
          }
        }
        .percent {
          width: 75px;
          height: 22px;
          padding: 0 10px;
          border-radius: 4px;
          border: 1px solid #dcdcdc;
          display: flex;
          align-items: center;
          margin-bottom: 2px;
          .right-icon {
            margin-left: auto;
            img {
              width: 6px;
              height: 4px;
              display: block;
              &:nth-child(2) {
                margin-top: 4px;
              }
            }
          }
        }
        p,
        span {
          font-size: 12px;
          margin: 0;
          line-height: 12px;
          word-break: keep-all;
        }
        span {
          margin-left: 3px;
          vertical-align: super;
        }
        &:hover {
          background: #ecf7ff;
          color: $themeFontColor;
        }
      }
    }
    h4 {
      border-left: 1px solid #fff;
      border-right: 1px solid #fff;
      line-height: 20px;
      background: #f2f3f6;
      text-align: center;
      color: #000;
      margin: 0;
      font-size: 12px;
    }
  }
  .menu-content {
    width: 100%;
    display: flex;
    border-top: 1px solid #ebedf0;
    border-bottom: 1px solid #eeeeee;
  }
}

.menu {
  position: relative;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;

  &-btn {
    position: absolute;
    top: 2px;
    right: 10px;

    button {
      height: 28px;
      line-height: 28px;
      padding: 0 18px;
    }
  }
}

.main {
  position: relative;
  display: flex;
  height: calc(100% - 50px);

  &-left {
    height: 100%;
    width: $leftWidth;
    background: #f4f4f4;
    margin-right: 2px;

    h3 {
      height: 32px;
      line-height: 32px;
      color: #4e4a4a;
      font-size: 12px;
      background: #eff1f5;
      margin: 0;
      text-align: center;
    }
    .modeTree {
      padding-bottom: 20px;
      overflow: auto;
      height: calc(100% - 32px);
    }
    
    .tools {
      background: #fff;
      overflow: auto;
      .node {
        display: inline-block;
        width: 70px;
        height: 70px;
        padding-top: 10px;
        box-sizing: border-box;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #cfcfcf;
        margin: 10px 10px 0;
        cursor: pointer;
        text-align: center;
      }
    }
    .tools {
      padding-left: 10px;
    }
  }

  &-canvas {
    width: $rightWidth;
    .tag-box {
      background-color: #ffffff;
      width: 100%;
      height: 32px;
      box-sizing: border-box;
      padding: 4px 0;
      overflow-y: hidden;
      overflow-x: auto;
      white-space: nowrap;
    }
    .el-tag__content {
      display: inline-block;
      min-width: 30px;
    }
    .nancalui-tag {
      margin-right: 10px;
      cursor: pointer;
      color: #333;
      background-color: #f2f3f6;
      border: none;
      line-height: 24px;
      .el-tag__close {
        color: #999;
      }
      &:hover {
        background-color: rgba(236, 243, 255, 1);
      }
      &.checked {
        background-color: #ecf7ff;
        color: $themeFontColor;
        :deep(.el-tag__close) {
          color: $themeFontColor;
          &:hover {
            background-color: $themeFontColor;
            color: #fff;
          }
        }
        .circle {
          display: inline-block;
          width: 6px;
          height: 6px;
          vertical-align: 2px;
          border-radius: 50%;
          background-color: $themeFontColor;
          margin-right: 4px;
        }
      }
    }
  }
  .canvas {
    //width: calc(100% - 8px);
    width: 100%;
    //height: calc(100% - 32px);
    height: calc(100% - 8px);
    border: none;
    padding: 1px;
    border-radius: 2px;
    .da {
      width: 100%;
      height: 100%;
    }
  }
  .minimapBox {
    position: absolute;
    bottom: 0;
    right: 0;
    border-radius: 10px 0 0 0;
    overflow: hidden;

    h3 {
      cursor: pointer;
      color: #4a4a4a;
      font-size: 12px;
      height: 28px;
      padding: 0 16px;
      line-height: 28px;
      background: #e9ecf1;
      text-align: center;
      margin: 0;
      font-weight: normal;
    }
    #minimapContainer {
      background: #fff;
    }
    .showMinMap {
      display: block;
    }
    .hiddenMinMap {
      display: none;
    }
  }
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin: 0;
    background: #fff;
  }
  .el-tabs__nav {
    border: none !important;
  }
  .el-tabs__item {
    border: 1px solid #cfcfcf !important;
    margin-right: 4px;
    background: #f4f4f4;

    &.is-active {
      background: #fff;
      border-radius: 2px 2px 0px 0px;
      color: $themeFontColor !important;
      border-bottom-color: #fff !important;
    }
  }
}
.test-css {
  background: red;
  color: #000;
}
.tips {
  color: #999;
  margin-left: 130px;
}
// x6放大缩小框
:deep(.x6-widget-transform) {
  border: 1px solid #5070da;
  margin: -1px;
  padding: 0px;
  .x6-widget-transform-resize {
    width: 8px;
    height: 8px;
    background: #ffffff;
    border: 1px solid #5070da;
    border-radius: 0;
    &[data-position='top-left'] {
      top: -4px;
      left: -4px;
    }
    &[data-position='top'] {
      top: -4px;
      margin-left: -4px;
    }
    &[data-position='top-right'] {
      top: -4px;
      right: -4px;
    }
    &[data-position='right'] {
      right: -4px;
      margin-top: -4px;
    }
    &[data-position='bottom-right'] {
      right: -4px;
      bottom: -4px;
    }
    &[data-position='bottom'] {
      bottom: -4px;
      left: 50%;
      margin-left: -4px;
    }
    &[data-position='bottom-left'] {
      bottom: -4px;
      left: -4px;
    }
    &[data-position='left'] {
      left: -4px;
      margin-top: -4px;
    }
  }
}
.table-no-content{
  width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(----, #606266);
    text-align: center;
    gap: 10px;
    font-family: Source Han Sans CN;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: var(----, #606266);
    text-align: center;
    .pic-no-conyent{
      width: 136px;
      height: 127px;
    }
}
:deep(.x6-node-selected) {
  .node{
    border:2px solid #1e89ff;
  }
}
