<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Subtract备份@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数据目录-元数据地图-列表" transform="translate(-322.000000, -78.000000)">
            <g id="内容" transform="translate(82.000000, 58.000000)">
                <g id="Subtract备份" transform="translate(240.000000, 20.000000)">
                    <rect id="矩形" fill="#04C495" x="0" y="0" width="20" height="20" rx="2"></rect>
                    <g id="编组" transform="translate(4.000000, 4.500000)" fill="#FFFFFF" fill-rule="nonzero">
                        <path d="M9.75000001,10.0222222 L2,10.0222222 L2,11 L9.75000001,11 L9.75000001,10.0222222 Z M11.5,1.93363843e-16 L0.5,1.93363843e-16 C0.224999985,1.93363843e-16 0,0.219999999 0,0.488888895 L0,8.79999999 C0,9.06888889 0.224999999,9.28888889 0.5,9.28888889 L11.5,9.28888889 C11.775,9.28888889 12,9.06888889 12,8.79999999 L12,0.488888895 C12,0.195555558 11.775,1.93363843e-16 11.5,1.93363843e-16 Z M7.95,5.69555555 C7.8,5.91555555 7.525,5.98888889 7.3,5.86666667 L4.65,4.47333333 L3.57499999,6.11111111 L2.725,5.59777777 L4.05,3.56888888 C4.2,3.34888888 4.475,3.27555555 4.7,3.39777778 L7.35,4.79111111 L8.42500001,3.15333334 L9.275,3.66666666 L7.95,5.69555555 L7.95,5.69555555 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>