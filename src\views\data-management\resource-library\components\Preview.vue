<template>
  <div class="preview">
    <div class="preview-title">
      <div class="preview-title-left">
        <n-button variant="solid" color="primary" @click.prevent.stop="downFn"
          ><SvgIcon class="icon" icon="new-definition-download" />下载</n-button
        >
        <n-button color="primary" @click="print"
          ><SvgIcon class="icon" icon="icon-file-printing" />打印</n-button
        >
        <span
          v-if="
            state.sourceFileType !== 'CSV' &&
            state.sourceFileType !== 'JSON' &&
            state.sourceFileType !== 'LOG'
          "
          class="bor"
        ></span>

        <n-button
          v-if="
            state.sourceFileType === 'PNG' ||
            state.sourceFileType === 'JPG' ||
            state.sourceFileType === 'JPEG' ||
            state.sourceFileType === 'PDF'
          "
          variant="text"
          color="primary"
          @click="goJumpOCR"
          ><SvgIcon class="icon" icon="icon-file-discern" />文字识别</n-button
        >
        <n-button
          v-if="state.sourceFileType === 'XLSX' || state.sourceFileType === 'XLS'"
          variant="text"
          color="primary"
          @click="onlineWork"
          ><SvgIcon class="icon" icon="icon-online" />在线协同</n-button
        >

        <n-dropdown
          v-if="state.sourceFileType === 'PDF'"
          :visible="state.signShow"
          class="signSelect"
          trigger="hover"
          align="start"
          :position="['bottom-start']"
        >
          <n-button ref="signRef" variant="text" color="primary"
            ><SvgIcon class="icon" icon="icon-sign-new" />PDF签名<SvgIcon
              class="arrow"
              icon="icon-arrow-bottom"
          /></n-button>
          <template #menu>
            <div class="noSign" v-if="state.signHistory.length === 0">
              <img src="@/assets/img/empty_new.png" alt="" />
              <span>暂无历史签名</span>
            </div>
            <ul v-else class="list-menu">
              <li class="menu-item" v-for="item in state.signHistory" :key="item.waterFileObjname">
                <SvgIcon class="icon" icon="icon-close-solid" @click="delUserSignFn(item)" />
                <img :src="item.waterFileOssUrl" alt="签名" @click="state.checkedSign = item" />
              </li>
            </ul>
            <div class="createSign" @click="PdfSign"
              ><SvgIcon class="icon" icon="icon-sign" />创建签名</div
            >
          </template>
        </n-dropdown>

        <n-dropdown
          v-if="
            state.sourceFileType === 'PNG' ||
            state.sourceFileType === 'JPG' ||
            state.sourceFileType === 'JPEG' ||
            state.sourceFileType === 'DOC' ||
            state.sourceFileType === 'DOCX' ||
            state.sourceFileType === 'PDF'
          "
          :visible="state.watermarkShow"
          class="watermarkDrop"
          trigger="hover"
          align="start"
          :position="['bottom-start']"
        >
          <n-button variant="text" color="primary"
            ><SvgIcon class="icon" icon="icon-file-watermark" />水印管理<SvgIcon
              class="arrow"
              icon="icon-arrow-bottom"
          /></n-button>
          <template #menu>
            <div class="watermark-content">
              <div v-if="state.watermarkList.length === 0" class="empty">
                <img class="pic" src="@/assets/img/empty_new.png" alt="" />
                <span>暂无水印</span>
              </div>
              <div class="watermark-content-list">
                <div
                  v-for="(item, index) in state.watermarkList"
                  :key="index"
                  class="watermark-content-list-item"
                  @click.prevent.stop="checkWatermark(item)"
                >
                  <div class="pic">
                    <div
                      class="mark"
                      :style="{
                        color: 'rgb(' + (item.r + ',' + item.g + ',' + item.b) + ')',
                        'font-size': item.textContent.length > 20 ? '12px' : '16px',
                        transform: 'rotate(' + item.rotation + 'deg)',
                      }"
                      >{{ item.textContent }}</div
                    >
                  </div>
                  <div class="name">{{ item.name }}</div>
                </div>
              </div>
              <div class="watermark-content-add" @click="addWatermark"
                ><SvgIcon class="icon" icon="icon-sign" />创建水印</div
              >
            </div>
          </template>
        </n-dropdown>
        <span
          v-if="
            state.sourceFileType === 'PNG' ||
            state.sourceFileType === 'JPG' ||
            state.sourceFileType === 'JPEG' ||
            state.sourceFileType === 'DOC' ||
            state.sourceFileType === 'DOCX' ||
            state.sourceFileType === 'PDF'
          "
          class="bor"
        ></span>
        <span
          v-if="
            state.sourceFileType === 'PNG' ||
            state.sourceFileType === 'JPG' ||
            state.sourceFileType === 'JPEG' ||
            state.sourceFileType === 'DOC' ||
            state.sourceFileType === 'DOCX' ||
            state.sourceFileType === 'PDF'
          "
          class="watermark"
        >
          开启水印
          <n-switch v-model="state.watermark" @change="setWatermark(state.watermark)" />
        </span>
      </div>
      <div
        v-if="state.sourceFileType === 'XLSX' || state.sourceFileType === 'XLS'"
        class="preview-title-right"
      >
        <n-select v-model="state.version" placeholder="请选择版本" @value-change="updateFn">
          <n-option
            v-for="(item, index) in state.versionList"
            :key="index"
            :name="item.versionTag"
            :value="item.id"
          />
        </n-select>
      </div>
    </div>

    <!-- 预览 -->
    <div
      class="preview-content"
      id="preview-pint"
      v-loading="loading"
      element-loading-text="Loading..."
    >
      <component
        :is="Component"
        :option="state.option"
        :signInfo="props.signInfo"
        :watermarkInfo="state.watermarkInfo"
        :historySign="state.checkedSign"
      />
    </div>
  </div>
</template>

<script setup>
  import { delUserSign, getUserSignList } from '@/api/dataManage'
  import printJS from 'print-js'
  import PDF from './PDFJS.vue'
  import Code from './Code.vue'
  import Img from './Img.vue'
  import Txt from './Txt.vue'
  import Word from './Word.vue'
  import Excel from './Excel.vue'

  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const route = useRoute()
  const componentMap = {
    XML: Code,
    LOG: Txt,
    TXT: Txt,
    JSON: Code,
    CSV: Excel,
    PDF: PDF,
    PNG: Img,
    JPEG: Img,
    JPG: Img,
    DOCX: PDF,
    DOC: PDF,
    EXCEL: Excel,
    XLSX: Excel,
    XLS: Excel,
  }
  const props = defineProps({
    info: {
      type: Object,
      default() {
        return {}
      },
    },
    version: {
      type: [String, Number],
      default: '',
    },
    signInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const emits = defineEmits(['PdfSign'])
  const type = ref()
  const Component = ref(componentMap[type.value])
  const loading = ref(false)
  const signRef = ref(null)
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS,
    watermark: false,
    watermarkShow: false,
    option: {
      name: '',
    },
    signShow: false,
    signHistory: [],
    checkedSign: {}, // 选中签名对象
    sourceFileType: '', // 文件后缀
    version: '',
    versionList: [],
    watermarkList: [],
    watermarkInfo: {},
  })
  const targetStyles = ref([
    'padding',
    'margin',
    'color',
    'font-size',
    'white-space',
    'display',
    'gap',
    'box-sizing',
    'position',
    'left',
    'right',
    'top',
    'bottom',
    'flex',
    'align-self',
    'flex-direction',
    'justify-content',
    'user-select',
    'flex-wrap',
    'align-items',
    'word-break',
    'transform',
    'text-align',
    'width',
    'height',
    'min-width',
  ])

  // 在线协同
  const onlineWork = () => {
    let checkVersion = state.versionList.filter((val) => val.id === state.version)
    let item = { ...props.info }
    if (checkVersion.length > 0) {
      item = { ...props.info, ossUrl: checkVersion[0].versionFileUrl, id: checkVersion[0].id }
    }

    let str = JSON.stringify({
      ossUrl: item.ossUrl,
      name: item.sourceFileName,
    })
    sessionStorage.setItem('onlineWorkData', str)
    if (state.isLzos) {
      window.open(
        router.resolve({
          name: 'onlineWork',
          query: {
            id: item.id,
            jobFileId: props.info.id,
          },
        }).href,
        '_blank',
      )
    } else {
      router.push({
        name: 'onlineWork',
        query: {
          id: item.id,
          jobFileId: props.info.id,
        },
      })
    }
  }
  const updateFn = () => {
    let checkVersion = state.versionList.filter((val) => val.id === state.version)
    seeFile({
      ...props.info,
      ossUrl: checkVersion[0].versionFileUrl,
      versionId: checkVersion[0].id,
    })
  }
  const PdfSign = () => {
    emits('PdfSign')
  }
  // 跳转文字识别
  const goJumpOCR = () => {
    router.push({
      name: 'OCR',
      query: { id: route.query.id, version: props.version },
    })
  }
  // 创建水印
  const addWatermark = () => {
    router.push({
      name: 'addWatermark',
      query: { id: props.info.id },
    })
  }

  // 删除历史签名
  const delUserSignFn = async (item) => {
    const params = {
      id: item.id,
    }
    const res = await delUserSign(params)
    if (res.success) {
      ElNotification({
        title: '提示',
        message: '删除历史签名成功',
        type: 'success',
      })
      getUserSignListData()
    }
  }

  // 水印是否启用
  const setWatermark = (flag) => {
    api.dataManagement.watermarkEnabled({ id: props.info.id, enabled: flag }).then((res) => {
      if (res.success) {
        getWatermarkDetail()
      }
    })
  }
  // 获取文件数据
  const seeFile = (data) => {
    let { ossUrl, sourceFileName, id, fileEncoding, versionId } = data
    const sourceFileType = sourceFileName?.split('.')?.pop()?.toLocaleUpperCase()
    let fileId = versionId || id
    state.sourceFileType = sourceFileType
    state.option.value = ossUrl
    state.option.name = sourceFileName
    state.option.type = sourceFileType
    type.value = sourceFileType
    state.option = {
      name: sourceFileName,
      value: ossUrl,
      type: sourceFileType,
      fileEncoding,
      ...{
        DOCX: {
          value: `./api/govern-collect/collect/file/${
            versionId ? 'version' : 'word'
          }/preview/byid?id=${fileId}`,
        },
        DOC: {
          value: `./api/govern-collect/collect/file/${
            versionId ? 'version' : 'word'
          }/preview/byid?id=${fileId}`,
        },
        CSV: {
          value: `./api/govern-collect/collect/file/${
            versionId ? 'version' : 'word'
          }/preview/byid?id=${fileId}`,
        },
        XLS: {
          value: ossUrl,
        },
        XLSX: {
          value: ossUrl,
        },
      }[sourceFileType],
    }
    Component.value = componentMap[type.value]
    if (sourceFileType === 'JPG' || sourceFileType === 'JPEG' || sourceFileType === 'PNG') {
      api.dataManagement
        .fileWatermarkDownload({ fileId: props.info.id, version: state.version || null })
        .then((res) => {
          if (res.type === 'application/json') {
            // 说明是普通对象数据，读取信息
            const fileReader = new FileReader()
            fileReader.readAsText(res)
            fileReader.onloadend = () => {
              const jsonData = JSON.parse(fileReader.result)
              // 后台信息
              ElNotification({
                title: '提示',
                message: jsonData.message,
                type: 'error',
              })
            }
            loading.value = false
          } else {
            const binaryData = []
            binaryData.push(res)
            let imgUrl = window.URL.createObjectURL(
              new Blob(binaryData, { type: 'application/pdf' }),
            )
            state.option.value = imgUrl
          }
        })
    }
  }
  // 获取版本数据
  const getVersion = () => {
    api.dataManagement.versionListAll({ jobFileId: props.info.id }).then((res) => {
      if (res.success) {
        state.versionList = res.data
        if (state.versionList.length > 0) {
          if (props.version) {
            state.version = props.version
          } else {
            state.version = state.versionList[0].id
          }
        }
        let checkVersion = state.versionList.filter((val) => val.id === state.version)
        if (checkVersion.length > 0) {
          seeFile({
            ...props.info,
            ossUrl: checkVersion[0].versionFileUrl,
            versionId: checkVersion[0].id,
          })
        } else {
          seeFile({ ...props.info, versionId: null })
        }
      }
    })
  }
  // 获取水印列表
  const waterTemplateList = () => {
    api.dataManagement.getWatermarkTemplateList().then((res) => {
      if (res.success) {
        state.watermarkList = res.data
      }
    })
  }
  // 获取选择的水印信息
  const getWatermarkDetail = () => {
    api.dataManagement.getWatermarkInfo({ fileId: props.info.id }).then((res) => {
      if (res.success) {
        state.watermarkInfo = res.data
        state.watermark = res.data.status === 'ENABLED' ? true : false
        let checkVersion = state.versionList.filter((val) => val.id === state.version)
        let data = { ...props.info }
        if (checkVersion.length > 0) {
          data.ossUrl = checkVersion[0].versionFileUrl
          data.versionId = checkVersion[0].id
        }
        seeFile(data)
      }
    })
  }
  // 选择水印
  const checkWatermark = (item) => {
    let data = { ...item }
    data.isCustom = false
    data.fileId = props.info.id
    data.templateId = item.id
    data.status = 'ENABLED'
    delete data.id
    delete data.configName
    api.dataManagement.saveWatermark(data).then((res) => {
      if (res.success) {
        ElNotification({
          title: '提示',
          message: '保存成功',
          type: 'success',
        })
        setWatermark(true)
      }
    })
  }

  // 文件下载
  const downFn = () => {
    api.dataManagement
      .fileWatermarkDownload({ fileId: props.info.id, version: state.version || null })
      .then((res) => {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        } else {
          // 下载文件
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
          })
          const link = document.createElement('a')
          const fileName = props.info.ossUrl.split('/')
          link.download = fileName[fileName.length - 1]
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        }
      })
  }

  // 文件流转url
  const getFileFlowFn = (maxWidth, type) => {
    api.dataManagement
      .fileWatermarkDownload({ fileId: props.info.id, version: state.version || null })
      .then((res) => {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
          loading.value = false
        } else {
          const binaryData = []
          binaryData.push(res)
          let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }))
          printJS({
            // printable: 'preview-pint', // 标签元素id
            printable: pdfUrl,
            type: type,
            maxWidth: maxWidth,
            targetStyles: targetStyles.value,
            ignoreElements: ['page-btn', 'pagination-content'], // ['no-print']
            properties: null,
            documentTitle: '',
            scanStyles: true,
            style: `@media print { @page {size: auto; margin: 5mm; } body{margin:0 5px}}}`, // 解决出现多页打印时第一页空白问题
            onLoadingEnd: () => {
              loading.value = false
            },
          })
        }
      })
  }

  // 获取word文件流
  const getWordFileFlowFn = (maxWidth, type) => {
    api.dataManagement
      .wordToPdfDownload({ fileId: props.info.id, version: state.version || null })
      .then((res) => {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
          loading.value = false
        } else {
          const binaryData = []
          binaryData.push(res)
          let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }))
          printJS({
            // printable: 'preview-pint', // 标签元素id
            printable: pdfUrl,
            type: type,
            maxWidth: maxWidth,
            targetStyles: targetStyles.value,
            ignoreElements: ['page-btn', 'pagination-content'], // ['no-print']
            properties: null,
            documentTitle: '',
            scanStyles: true,
            style: `@media print { @page {size: auto; margin: 5mm; } body{margin:0 5px}}}`, // 解决出现多页打印时第一页空白问题
            onLoadingEnd: () => {
              loading.value = false
            },
          })
        }
      })
  }
  // 获取图片文件流
  const getImgFileFlowFn = (maxWidth, type) => {
    printJS({
      printable: state.option.value,
      type: type,
      maxWidth: maxWidth,
      targetStyles: targetStyles.value,
      ignoreElements: ['page-btn', 'pagination-content'], // ['no-print']
      properties: null,
      documentTitle: '',
      scanStyles: true,
      style: `@media print { @page {size: auto; margin: 5mm; } body{margin:0 5px}}}`, // 解决出现多页打印时第一页空白问题
      onLoadingEnd: () => {
        loading.value = false
      },
    })
  }

  //  打印
  const print = () => {
    loading.value = true

    let printable = 'preview-pint'
    let type = 'html'
    const maxWidth = '700'
    // document.getElementById('preview-pint').querySelector('div').querySelector('div')
    //   .offsetWidth < 1600
    //   ? document.getElementById('preview-pint').querySelector('div').querySelector('div')
    //       .offsetWidth
    //   : 1600

    const scanStyles = state.option.type === 'TXT' || state.option.type === 'LOG' ? false : true

    if (state.option.type === 'PDF') {
      type = 'pdf'
      getFileFlowFn(maxWidth, type)
    } else if (state.option.type === 'DOC' || state.option.type === 'DOCX') {
      type = 'pdf'
      getWordFileFlowFn(maxWidth, type)
    } else if (
      state.option.type === 'JPG' ||
      state.option.type === 'JPEG' ||
      state.option.type === 'PNG'
    ) {
      type = 'image'
      getImgFileFlowFn(maxWidth, type)
    } else {
      printJS({
        // printable: 'preview-pint', // 标签元素id
        printable: printable,
        type: type,
        maxWidth: maxWidth,
        // targetStyles: targetStyles.value,
        targetStyles: ['*'],
        ignoreElements: ['page-btn', 'pagination-content'], // ['no-print']
        properties: null,
        documentTitle: '',
        scanStyles: scanStyles,
        style: `@media print { @page {size: auto; margin: 5mm; } body{font-size:40px;margin:0 5px}}`, // 解决出现多页打印时第一页空白问题
        onLoadingEnd: () => {
          loading.value = false
        },
      })
    }
  }

  // 获取最近3次签名
  const getUserSignListData = async () => {
    const res = await getUserSignList()
    state.signHistory = res.data
  }

  onMounted(() => {
    getUserSignListData()
    nextTick(() => {
      getVersion()
      waterTemplateList()
      getWatermarkDetail()
    })
  })

  // 使用 defineExpose 暴露方法
  defineExpose({ getUserSignListData })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .preview {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 0 10px;
      border-bottom: 1px solid #c5d0ea;
      &-left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .bor {
          width: 1px;
          height: 14px;
          margin-left: 8px;
          background-color: #c5d0ea;
        }
        .watermark {
          margin-left: 16px;
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          .nancalui-switch {
            margin-left: 4px;
          }
        }
        :deep(.nancalui-button) {
          margin-left: 8px;
          padding: 0 8px;
          .button-content {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
            .arrow {
              margin-left: 4px;
              font-size: 8px;
            }
          }
          &.nancalui-button--text {
            &:hover {
              color: $themeBlue;
              background-color: #e3ecff;
            }
          }
        }
      }
    }
    &-content {
      height: calc(100% - 52px);
      padding: 16px;
      page-break-after: always;
    }
  }

  .signSelect {
    overflow: hidden;
    border-radius: 4px;

    .noSign {
      padding: 24px 34px 32px;
      text-align: center;

      img {
        width: 48px;
      }
      span {
        display: block;
        color: var(---, rgba(0, 0, 0, 0.55));
        font-size: 14px;
        font-family: 'Source Han Sans CN';
      }
    }

    .list-menu {
      margin: 0;
      padding: 16px;
      overflow: hidden;
      background: #fff;

      li {
        position: relative;
        float: left;
        box-sizing: border-box;
        width: 96px;
        height: 96px;
        margin-right: 24px;
        padding: 4px;
        text-align: center;
        list-style: none;
        background: #fff;
        border: 1px solid var(---, #c5d0ea);
        border-radius: 8px;
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }
        .icon {
          position: absolute;
          top: -8px;
          right: -8px;
          display: none;
          font-size: 16px;
          cursor: pointer;
        }

        &:hover {
          border: 1px solid #fff;
          box-shadow: 0px 4px 16px -2px rgba(30, 47, 85, 0.15);

          .icon {
            display: block;
          }
        }
      }
      img {
        display: block;
        height: 100%;
        background: var(---, #f6f7fb);
        border-radius: 6px;
      }
    }

    .createSign {
      padding: 5px 12px;
      color: var(----, rgba(0, 0, 0, 0.75));
      font-size: 14px;
      font-family: 'Source Han Sans CN';
      line-height: 21px;
      border-top: 1px solid var(---, #c5d0ea);
      border-bottom: 6px solid #fff;
      cursor: pointer;

      .icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &:hover {
        color: #447dfd;
        background: var(--unnamed, #e3ecff);
        border-top: 1px solid var(---, #c5d0ea);
      }
    }
  }
  .watermarkDrop {
    .watermark-content {
      box-sizing: border-box;
      min-width: 152px;
      max-width: 560px;
      padding-bottom: 6px;
      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 130px;
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        .pic {
          width: 48px;
          height: 48px;
          margin-bottom: 4px;
        }
      }
      &-list {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 16px 0 0 16px;
        &-item {
          position: relative;
          flex-shrink: 0;
          box-sizing: border-box;
          width: 120px;
          height: 140px;
          margin-right: 16px;
          margin-bottom: 16px;
          padding: 4px;
          background-color: #fff;
          border: 1px solid #c5d0ea;
          border-radius: 8px;
          cursor: pointer;
          &:hover {
            border: 1px solid #fff;
            box-shadow: 0 4px 16px -2px rgba(30, 47, 85, 0.15);
          }
          .icon {
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 16px;
            background-color: #fff;
          }
          .pic {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 106px;
            overflow: hidden;
            background-color: #f6f7fb;
            border-radius: 6px;
            .mark {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              justify-content: center;
              width: 80px;
              height: 80px;
              color: #cfcfcf;
              font-size: 16px;
              text-align: center;
              word-break: break-all;
              transform: rotate(-30deg);
            }
          }
          .name {
            width: 100%;
            height: 22px;
            margin-top: 4px;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            line-height: 22px;
            white-space: nowrap;
            text-align: center;
            text-overflow: ellipsis;
          }
        }
      }
      &-add {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 6px 12px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        border-top: 1px solid #c5d0ea;
        cursor: pointer;
        .icon {
          margin-right: 8px;
          font-size: 16px;
        }
        &:hover {
          color: $themeBlue;
          background-color: #e3ecff;
        }
      }
    }
  }
</style>
<style>
  .has-preview-modal .nancalui-modal__body {
    display: block !important;
    max-height: 620px;
    padding: 16px;
  }
</style>
