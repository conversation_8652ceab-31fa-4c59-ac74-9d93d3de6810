<template>
  <section class="container">
    <section class="container-box">
      <section class="tools">
        <div class="row">
          <div class="col">
            名称：
            <n-input
              v-model="state.originalFormInline.name"
              placeholder="请输入中文名/英文名"
              clearable
            />
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>

      <section class="container-box-table">
        <section class="asideTree">
          <div class="title">结构化数据</div>

          <div class="class-list">
            <n-input
              class="class-list-tree-ipt"
              v-model="state.treeSearchText"
              placeholder="请输入关键词"
              suffix="search"
              @input="(val) => treeRef.treeRef.filter(val)"
            />
            <div class="class-tree">
              <CfTtee
                ref="treeRef"
                :check-on-click-node="true"
                :filter-node-method="filterNode"
                :default-expanded-keys="classState.expandedKeys"
                :current-node-key="classState.selectedKey"
                :props="{
                  children: 'children',
                  label: 'name',
                }"
                node-key="id"
                :data="classState.data"
                @node-click="clickFn"
              />
            </div>
          </div>
        </section>
        <section ref="tableRef" class="table-container">
          <CfTable
            actionWidth="180"
            :key="state.dataSource"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.formTableHead"
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch()
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #isCollected="{ row }">
              {{ row.isCollected ? '已采集' : '未采集' }}
            </template>

            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="goJump('resourceCatalogDetail', row)"
                >查看</n-button
              >
            </template>
          </CfTable>
        </section>
      </section>
    </section>
  </section>
</template>

<script setup>
  import { nextTick } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { assetsRegisterList } from '@/api/assets.js'
  import cfTag from '@/components/cfTag'
  import CfTtee from '@/components/cfTtee'
  import api from '@/api/index'
  import { getResourceDirectoryList, getResourceClassifyTree } from '@/api/resourceClassify'

  const route = useRoute()
  const router = useRouter()

  const tableRef = ref()

  const classState = reactive({
    data: [],
    expandedKeys: [],
    selectedKey: null,
  })
  const state = reactive({
    treeSearchText: '',
    filterSearch: { categoryId: '' },
    formTableHead: [
      { prop: 'sourceName', name: '数据源名称' },
      { prop: 'tableName', name: '表名称' },
      { prop: 'tableComment', name: '表描述' },
      { prop: 'isCollected', name: '是否采集', slot: 'isCollected' },
      { prop: 'collectDestTableName', name: 'ODS表名称' },
    ],
    originalFormInline: {
      name: null,
      assetsRegistered: '',
    },
    formInline: {
      name: null,
      assetsRegistered: '',
    },
    treeList: [
      {
        id: -1,
        name: '全部',
        type: 'DIRECTORY',
        isTemplateDirectory: false,
        children: [],
      },
    ],
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    tableHeight: 500,
  })

  onMounted(() => {
    state.tableHeight = tableRef.value.clientHeight - 107
    getClassifyTreeList()
    onSearch()
  })

  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }

  // 获取分类树
  async function getClassifyTreeList() {
    const res = await getResourceClassifyTree({
      keyword: '',
    })
    const data = { ...res.data, expanded: true }
    data.name = '全部'
    classState.data = [data]
    nextTick(() => {
      classState.expandedKeys = [classState.data?.[0]?.id || 1]
      classState.selectedKey = classState.data?.[0]?.id || 1
    })
  }

  // 点击树节点
  function clickFn(node) {
    classState.selectedKey = node.id

    state.filterSearch.categoryId = node.code
    onSearch()
  }
  // 搜索
  const treeRef = ref(null)
  async function searchClassFn() {
    treeRef.value.treeFactory.searchTree(tableState.treeSearchText, {
      isFilter: true,
      matchKey: 'name',
    })
  }

  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.formInline[key] = state.originalFormInline[key]
    }
    state.page.pageNum = 1
    onSearch()
  }

  // 获取注册列表
  const onSearch = async () => {
    const { name } = state.formInline

    const res = await getResourceDirectoryList({
      condition: {
        categoryCode: state.filterSearch.categoryId,
        keyword: state.originalFormInline.name,
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    })
    if (res.code === 'SUCCESS') {
      const { list, total } = res.data
      state.page.total = total
      state.dataSource = list
    }
  }

  // 重置查询参数
  const resetFn = () => {
    state.originalFormInline = {
      name: null,
      assetsRegistered: '',
    }
    searchClickFn()
  }
  const pageChange = (pageNum) => {
    state.page.pageNum = pageNum === 0 ? 1 : pageNum
    onSearch()
  }
  const pageSizeChange = (pageSize) => {
    state.page.pageNum = 1
    state.page.pageSize = pageSize
    onSearch()
  }

  const goJump = (name, row) => {
    sessionStorage.setItem('assetsReg', JSON.stringify(row))
    router.push({ name, query: { id: row.id } })
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .tools {
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 10px 16px;
      .createTime {
        width: 260px;
        margin-right: 32px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          background: #1e89ff;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
      }
    }
  }

  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      &-table {
        display: flex;
        flex: 1;
        height: calc(100% - 60px);

        .asideTree {
          width: 300px;
          background: #fff;
          padding: 10px 0;

          .title {
            color: #1d2129;
            font-weight: 500;
            font-size: 16px;
            line-height: 18px;
            text-indent: 8px;
            border-left: 4px solid $cf-color-primary;
          }

          .class-list {
            padding: 12px;
            margin-top: 10px;
          }
          .class-tree {
            height: calc(100vh - 300px);
            margin-top: 8px;
            overflow: auto;
            :deep(.tree-box) {
              height: 100%;
            }
          }
        }
        .table-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0px 0px 2px 2px;

          .nancalui-pagination {
            position: absolute;
            bottom: 0;
            left: 0;
            justify-content: flex-end;
            width: 100%;
            padding: 14px 16px;
            border-top: 1px solid #dcdfe6;
          }
        }
      }
    }
  }
</style>
<style lang="scss">
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
