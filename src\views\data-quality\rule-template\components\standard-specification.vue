<template>
  <n-modal
    v-model="state.showStandard"
    class="largeDialog has-top-padding"
    :width="state.isForm ? '520px' : '960px'"
    :close-on-click-overlay="false"
    :show-close="false"
    style="z-index: 2000"
  >
    <template #header>
      <n-modal-header>
        <div class="modal-header">
          <n-module-name size="lg">标准规范</n-module-name>
          <SvgIcon class="icon" icon="icon-close" @click="closeDialog" />
        </div>
      </n-modal-header>
    </template>
    <div class="modal-body">
      <div v-if="state.isForm" class="modal-body-form">
        <n-form
          ref="standardForm"
          :data="state.standardForm"
          :rules="state.standardRules"
          label-align="end"
          label-width="100px"
        >
          <n-form-item label="标准规范名称：" field="name">
            <n-input
              v-model="state.standardForm.name"
              :maxLength="200"
              placeholder="请输入标准规范名称"
              @blur="checkExistFn"
            />
          </n-form-item>
          <n-form-item field="url" label="标准规范文件：">
            <div class="mark-label-item">
              <n-input v-model="state.standardForm.url" placeholder="请上传标准规范文件" />
              <n-upload accept=".csv,.xls,.xlsx,.doc,.docx" :before-upload="beforeUpload">
                <div class="upload-btn"> 上传 </div>
              </n-upload>
            </div>
          </n-form-item>
        </n-form>
      </div>
      <div v-else>
        <div class="modal-body-btn"
          ><n-button variant="solid" @click.prevent.stop="addFn">
            <SvgIcon class="icon" icon="new-add" />新增
          </n-button></div
        >
        <div class="modal-body-table">
          <div
            :class="'modal-body-table-scroll' + (state.tableData?.list?.length > 0 ? '' : ' empty')"
          >
            <CfTable
              saveWidth
              :isDisplayAction="true"
              :table-head-titles="state.tableHeadTitles"
              :showPagination="true"
              :tableHeight="396"
              :paginationConfig="{
                total: state.pageInfo.total,
                pageSize: state.pageInfo.pageSize,
                currentPage: state.pageInfo.currentPage,
                onCurrentChange: (v) => {
                  state.pageInfo.currentPage = v
                  initTable()
                },
                onSizeChange: (v) => {
                  state.pageInfo.pageSize = v
                  initTable()
                },
              }"
              :tableConfig="{
                data: state.tableData.list,
                rowKey: 'id',
              }"
              :actionWidth="120"
            >
              <template #url="{ row }">
                <div class="url" :title="row.url">{{ row.url || '--' }}</div>
              </template>
              <template #editor="{ row }">
                <div class="edit-box">
                  <n-button class="has-right-border" variant="text" @click.prevent="editFn(row)"
                    >编辑
                  </n-button>
                  <n-button class="has-right-border" variant="text" @click.prevent="delFn(row)"
                    >删除
                  </n-button>
                </div>
              </template>
            </CfTable>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button v-if="state.isForm" @click.prevent="state.isForm = false">取消</n-button>
        <n-button v-if="state.isForm" variant="solid" @click.prevent="saveFn">确定</n-button>
        <n-button v-else variant="solid" @click.prevent="submitFn">确定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import { reactive, onMounted, nextTick, getCurrentInstance, defineEmits } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(['updateFn'])
  const standardForm = ref()
  const state = reactive({
    isRepeat: false,
    hasChecked: false,
    showStandard: false,
    isForm: false,
    tableHeadTitles: [
      {
        name: '标准规范名称',
        prop: 'name',
      },
      {
        name: '标准规范文件',
        prop: 'url',
        slot: 'url',
      },
    ],
    tableData: [],
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    standardForm: {
      id: null,
      name: '',
      filename: '',
      url: '',
    },
    standardRules: {
      name: [{ required: true, message: '请输入标准规范名称', trigger: 'blur' }],
      url: [{ required: true, message: '请上传标准规范文件', trigger: 'blur' }],
    },
  })

  // 关闭前
  const closeDialog = () => {
    if (state.isForm) {
      state.isForm = false
    } else {
      emit('updateFn', true)
      state.showStandard = false
    }
  }

  // 上传文件
  const beforeUpload = (UploadRawFile) => {
    let size = UploadRawFile[0].file.size / Math.pow(1024, 2)
    let fileName = UploadRawFile[0].file.name.substring(
      UploadRawFile[0].file.name.lastIndexOf('/') + 1,
    )
    let suffix = fileName.split('.')[1]
    if (suffix === 'csv') {
      if (size > 5 * 1024) {
        ElNotification({
          title: '提示',
          message: 'csv文件不能超过5G！',
          type: 'warning',
        })
        return false
      }
    } else {
      if (size > 100) {
        ElNotification({
          title: '提示',
          message: '文件不能超过100M！',
          type: 'warning',
        })
        return false
      }
    }
    const formData = new FormData()
    formData.append('file', UploadRawFile[0].file)
    formData.append('bucket', 'data-govern')
    api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
      if (res.success) {
        state.standardForm.url = res.data.url
        state.standardForm.filename = res.data.name
      }
    })
    return false
  }

  // 添加规范
  const addFn = () => {
    state.standardForm = {
      id: null,
      name: '',
      filename: '',
      url: '',
    }
    state.isForm = true
  }
  // 编辑
  const editFn = (item) => {
    state.standardForm = {
      id: item.id,
      name: item.name,
      filename: item.filename,
      url: item.url,
    }
    state.isForm = true
  }

  // 检测是否名称重复
  const checkExistFn = async () => {
    if (state.hasChecked) {
      return false
    }
    state.hasChecked = true
    let data = {
      name: state.standardForm.name,
    }
    if (state.standardForm.id) {
      data.id = state.standardForm.id
    }
    await api.dataQuality
      .stdSpecDocExist(data)
      .then((res) => {
        state.hasChecked = false
        if (res.success) {
          state.isRepeat = res.data
          if (state.isRepeat) {
            ElNotification({
              title: '提示',
              message: '标准规范名称重复！',
              type: 'warning',
            })
          }
        }
      })
      .catch(() => {
        state.hasChecked = false
      })
  }

  // 保存规范
  const saveFn = () => {
    standardForm.value.validate(async (valid) => {
      if (valid) {
        let interfaceUrl = 'stdSpecDocAdd'
        if (state.standardForm.id) {
          interfaceUrl = 'stdSpecDocUpdate'
        }
        await checkExistFn()
        if (!state.isRepeat) {
          api.dataQuality[interfaceUrl]({ ...state.standardForm }).then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '保存成功！',
                type: 'success',
              })
              state.isForm = false
              initTable(false)
            }
          })
        }
      }
    })
  }

  // 完成
  const submitFn = () => {
    state.showStandard = false
    emit('updateFn', true)
  }

  // 删除
  const delFn = (item) => {
    proxy.$MessageBoxService.open({
      title: '是否确认删除该标准规范',
      content: '删除后，该标准规范将不可用',
      save: () => {
        api.dataQuality.stdSpecDocDelete({ id: item.id }).then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '删除成功！',
              type: 'success',
            })
            initTable(true)
          }
        })
      },
    })
  }

  // 获取规范列表
  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        filename: null,
        id: null,
        name: null,
        url: null,
      },
    }
    api.dataQuality.stdSpecDocPageList(data).then((res) => {
      if (res.success) {
        state.tableData = res.data
        state.pageInfo.total = res.data.total
      }
    })
  }

  defineExpose({
    open: (data = {}) => {
      console.log(data)
      state.showStandard = true
      state.isForm = false
      initTable(true)
    },
  })
</script>

<style lang="scss" scoped>
  .standardSpecification {
    position: relative;
  }
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .icon {
      font-size: 24px;
      color: #8091b7;
      cursor: pointer;
    }
  }
  .modal-body {
    overflow: hidden;
    position: relative;
    &-btn {
      margin-bottom: 16px;
      :deep(.button-content) {
        display: flex;
        justify-content: center;
        align-items: center;
        .icon {
          font-size: 14px;
          margin-right: 4px;
        }
      }
    }
    &-form {
      height: 100%;
      :deep(.nancalui-input--disabled) {
        background-color: #f5f7fa !important;
      }
      .mark-label-item {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .upload-btn {
          width: 60px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          box-sizing: border-box;
          border: 1px solid #1e89ff;
          color: #1e89ff;
          font-size: 14px;
          cursor: pointer;
          border-radius: 2px;
          &:hover {
            color: #447dfd;
            background: #f0f7ff;
            border: 1px solid #bfd9ff;
          }
        }
      }
    }
    &-table {
      .url {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
