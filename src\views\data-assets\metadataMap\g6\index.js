import G6 from '@antv/g6'
import './shape'
let graph = null
export default class g6FlowGraph {
  static init(flowId) {
    const container = document.getElementById(flowId)
    const width = container.scrollWidth
    const height = container.scrollHeight || 500

    graph = new G6.Graph({
      container: flowId,
      width,
      height,
      layout: {
        type: 'gForce',
        nodeSize: 84, // 可选
        linkDistance: 100,
        nodeSpacing: 20,
        nodeStrength: 30000,
        gravity: 80,
        preventOverlap: true,
        gpuEnabled: true,
        preset: {
          type: 'concentric',
        },
      },
      // renderer: 'svg',
      defaultNode: {
        // type: 'meta-node',
        type: 'circle',
        label: 'node-label',
        labelCfg: {
          position: 'bottom',
          offset: 2,
          style: {
            fill: 'rgba(0,0,0,0.65)',
            fontSize: 12,
            fontWeight: 'bolder',
          },
        },
      },
      defaultEdge: {
        type: 'line',
        style: {
          stroke: '#697A9A',
          radius: 10,
          fill: '#697A9A',
          lineWidth: 1,
          fillOpacity: 0,
          endArrow: {
            path: 'M 0,0 L 0, 6 L 10,0 L 0, -6 Z',
            d: 10,
            fill: '#697A9A',
            stroke: '#697A9A',
          },
        },
      },
      nodeStateStyles: {
        hover: {
          stroke: '#ffffff',
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowColor: 'rgba(0,0,0,0.18)',
          shadowBlur: 16,
        },
      },
      modes: {
        default: [
          {
            type: 'zoom-canvas',
            enableOptimize: false,
            optimizeZoom: 0.9,
          },
          {
            type: 'drag-canvas',
            enableOptimize: false,
          },
          'brush-select',
        ],
      },
    })

    if (typeof window !== 'undefined')
      window.onresize = () => {
        if (!graph || graph.get('destroyed')) return
        if (!container || !container.scrollWidth || !container.scrollHeight) return
        graph.changeSize(container.scrollWidth, container.scrollHeight)
        graph.layout(true)
      }

    return graph
  }

  // 销毁
  static destroy() {
    graph.destroy()
  }
}
