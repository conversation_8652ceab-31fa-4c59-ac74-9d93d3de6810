<template>
  <n-modal v-model="visiable" width="580px" title="上传非结构化数据" @close="onClose(false)">
    <n-form
      :data="state.formData"
      ref="formRef"
      :rules="state.rules"
      label-width="140px"
      label-suffix="："
    >
      <n-form-item field="file.name" label="上传文件">
        <!-- .xls,.doc, -->
        <n-upload
          droppable
          :before-upload="beforeUpload"
        >
          <div class="upload-box">
            <div class="upload-box-icon">
              <SvgIcon class="icon" icon="upload-cloud-new" title="上传" />
            </div>
            <div class="h2">点击或将文件拖拽到这里上传</div>
          </div>
        </n-upload>
      </n-form-item>
      <div class="nc-m-l-100" v-if="state.formData.docId">{{ state.formData?.file?.name }}</div>
      <div class="name-exist" v-if="state.nameExist.id">
        <svgIcon icon="icon-worning-tips" class="nc-m-r-3" />
        <span>该非结构化数据已存在，非结构化数据编号 {{ state.nameExist.ruleCode }}</span>
        <n-button variant="text" color="primary" @click="onPrewiew">查看非结构化数据 ></n-button>
      </div>
      <n-form-item field="name" label="非结构化数据名称" v-if="!id">
        <n-input v-model="state.formData.name" maxLength="200" @blur="nameExist" />
      </n-form-item>
      <n-form-item field="categoryId" label="非结构化数据分类" v-if="!id">
        <el-tree-select
          style="width: 100%"
          v-model="state.formData.categoryId"
          :data="state.classList"
          node-key="id"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
          }"
          filterable
          clearable
        />
      </n-form-item>
      <n-form-item field="confidentialityLevel" label="密级" v-if="!id">
        <n-select v-model="state.formData.confidentialityLevel" :allow-clear="true">
          <n-option
            v-for="item in state.secretList"
            :key="item.id"
            :name="item.name"
            :value="item.id"
          />
        </n-select>
      </n-form-item>
      <n-form-item field="associationDocIds" label="关联非结构化数据" v-if="!id">
        <div id="associationDoc" style="width: 100%">
          <el-tree-select
            ref="treeSelectRef"
            style="width: 100%"
            v-model="state.formData.associationDocIds"
            :load="loadAssociationDocs"
            lazy
            :default-expanded-keys="state.defaultExpandedKeys"
            filterable
            :filter-node-method="filterAssociationDocs"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            check-on-click-node
            node-key="nodeId"
            :loading="state.assLoading"
            collapse-tags
            collapse-tags-tooltip
            :props="{
              label: 'name',
              children: 'children',
              class: 'nc-line-1',
              isLeaf: 'isLeaf',
            }"
          />
        </div>
      </n-form-item>
    </n-form>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>
<script setup>
  import api from '@/api/index'
  import { getCurrentInstance, reactive, ref } from 'vue'
  const { proxy } = getCurrentInstance()
  import { useRouter } from 'vue-router'
  const router = useRouter()

  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    categoryId: { type: String, default: '' },
    id: { type: String, default: '' },
  })
  const state = reactive({
    formData: {
      file: {},
      name: '',
      categoryId: '',
      confidentialityLevel: 'INTERIOR',
      associationDocIds: [],
    },
    nameExist: {},
    rules: {
      'file.name': [{ required: true, message: '请选择文件', trigger: 'change' }],
      categoryId: [
        { required: true, message: '请选择非结构化数据分类', trigger: 'change', type: 'number' },
      ],
      name: [{ required: true, message: '请输入非结构化数据名称', trigger: 'burl' }],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
    },
    classList: [],
    assLoading: false,
    defaultExpandedKeys: [],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
  })
  function beforeUpload(UploadRawFile) {
    if (UploadRawFile.length) {
      if (!state.formData.docId) {
        // 按.分割字符串
        const arr = UploadRawFile[0].file?.name.split('.')
        // 删除最后一个元素
        arr.pop()
        // 重新拼接字符串
        state.formData.name =arr.join('.')
        nameExist()
      }
      state.formData.file = UploadRawFile[0].file
    }
    return false
  }
  // 获取详情
  function getDetail() {
    api.documentManage
      .uploadDetail({
        docId: props.id,
      })
      .then((res) => {
        if (res.success) {
          state.formData = res.data
          state.formData.file = {}
        }
      })
  }
  // 预览
  function onPrewiew() {
    router.push({
      name: 'documentUploadPreview',
      query: {
        id: state.nameExist.id,
        docUrl: state.nameExist.docUrl,
        name: state.nameExist.name,
        type: state.nameExist.type,
      },
    })
  }
  const formRef = ref(null)
  function onConfirm() {
    formRef.value.validate(async (valid) => {
      if (valid) {
        const formData = new FormData()
        let res = null
        // 重新上传
        if (state.formData.docId) {
          formData.append('file', state.formData.file)
          res = await api.documentManage.uploadAgainSave({
            fn: formData,
            bucket: 'data-govern',
            docId: state.formData.docId,
          })
          // 新增
        } else {
          const associationDocIds = state.formData.associationDocIds
            .map((i) => {
              return i.replace('doc-', '')
            })
            .join(',')
          formData.append('categoryId', state.formData.categoryId)
          formData.append('confidentialityLevel', state.formData.confidentialityLevel)
          formData.append('name', state.formData.name)
          formData.append('associationDocIds', associationDocIds)
          formData.append('file', state.formData.file)
          res = await api.documentManage.uploadSave(formData)
        }
        if (res.success) {
          proxy.$message.success('上传成功')
          onClose(true)
        }
      }
    })
  }
  const emit = defineEmits(['update:modelValue', 'needRefresh'])
  function onClose(needRefresh) {
    visiable.value = false
    emit('update:modelValue', false)
    state.nameExist = {}
    state.formData = {
      file: {},
      name: '',
      categoryId: '',
      confidentialityLevel: 'INTERIOR',
      associationDocIds: [],
    }
    if (needRefresh) {
      emit('needRefresh')
    }
  }
  // 非结构化数据重名校验
  function nameExist() {
    api.documentManage
      .uploadNameExist({
        name: state.formData.name,
      })
      .then((res) => {
        if (res.success) {
          state.nameExist = res.data || {}
        }
      })
  }
  // 获取分类树
  async function getClassifyTreeList() {
    const res = await api.documentManage.getClassifyTreeList()
    state.classList = res.data
    if (props.categoryId) {
      state.formData.categoryId = Number(props.categoryId)
    }
  }
  const treeSelectRef = ref(null)
  // 监听搜索输入，手动触发搜索
  function customSearch() {
    nextTick(() => {
      const queryInput = document.querySelector('#associationDoc .el-select__input')
      queryInput.addEventListener('input', (event) => {
        remoteAssociationDocs(event.target.value, true)
      })
    })
  }
  // 展开搜索节点，触发过滤
  let expandedKeys = []
  async function remoteAssociationDocs(query, needFilter = false) {
    if (query) {
      state.assLoading = true
      const res = await api.documentManage.associationTree({ name: query })
      const data = res.data
      if (data.length) {
        expandedKeys = []
        setExpendKeys(data, true)
        state.defaultExpandedKeys = expandedKeys
        if (needFilter) {
          treeSelectRef.value?.filter(query)
        }
      }
      state.assLoading = false
    }
  }
  // 过滤方法
  function filterAssociationDocs(query, data) {
    if (!query) return true
    return data.name.indexOf(query) > -1
  }
  // 设置需要展开的节点
  function setExpendKeys(data) {
    data.forEach((item) => {
      if (item.children?.length) {
        expandedKeys.push(item.nodeType + '-' + item.id)
        setExpendKeys(item.children)
      }
    })
  }
  // 关联非结构化数据分类不可选
  function setDisabled(data) {
    data.forEach((item) => {
      item.nodeId = item.nodeType + '-' + item.id
      item.name = item.name?.length > 10 ? item.name.slice(0, 10) + '...' : item.name
      if (item.nodeType === 'category' || String(item.id) === String(state.formData.docId)) {
        item.disabled = true
        if (item.children?.length) setDisabled(item.children)
      }
    })
  }
  // 关联非结构化数据懒加载
  async function loadAssociationDocs(node, resolve, reject) {
    const res = await api.documentManage.associationTree({ id: node?.data.id })
    if (res.success) {
      nextTick(() => {
        const ids = state.formData.associationDocIds?.map((i) => {
          return i.nodeId ? i.nodeId : i
        })
        if (ids?.length) {
          treeSelectRef.value?.setCheckedKeys(ids)
        }
      })
      const data = res.data
      setDisabled(res.data)
      return resolve(data)
    } else {
      return reject()
    }
  }

  const visiable = ref(false)
  watch(
    () => props.modelValue,
    async (val) => {
      visiable.value = val
      await getClassifyTreeList()
      if (props.id) {
        getDetail()
      }
      customSearch()
    },
  )
</script>
<style lang="scss" scoped>
  .upload-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 381px;
    height: 216;
    padding: 22px 15px;
    background: var(---, #fafafa);
    border: 1px solid var(---, #e5e6eb);
    border-radius: 2px;
    cursor: pointer;

    &-icon {
      .icon {
        font-size: 48px;
      }
    }

    .h2 {
      margin-top: 20px;
      color: var(----, #000);
      font-weight: 500;
      font-size: 14px;
      text-align: center;
    }
  }
  .name-exist {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: -10px;
    color: var(---Warning-, #ff7d00);
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    :deep(.nancalui-button--text) {
      padding: 0 0 0 6px;
    }
  }
</style>
