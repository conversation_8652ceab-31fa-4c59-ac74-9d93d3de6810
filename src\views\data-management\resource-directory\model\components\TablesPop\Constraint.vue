<template>
  <section v-loading="loading" class="entity">
    <div class="entity-left">
      <div class="table">
        <div class="table-box">
          <div class="table-node" style="background: #cfcfcf">Constraint Name</div>
          <div class="table-node" style="background: #cfcfcf">Type</div>
          <div
            class="table-node"
            style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
            :title="cellData.tabName + '_PK'"
            >{{ cellData.tabName + '_PK' }}</div
          >
          <div class="table-node">Primary Key</div>
        </div>
        <div class="table-box" style="margin-top: 15px">
          <div class="table-node" style="background: #cfcfcf">Column Name</div>
          <div class="table-node" style="background: #cfcfcf">Not Null</div>
        </div>
        <div v-for="item in mainCol" :key="item.col" class="table-box">
          <div class="table-node">{{ item.col }}</div>
          <div class="table-node">{{ item.notNull ? 'Y' : 'N' }}</div>
        </div>
      </div>
    </div>

    <div class="entity-right">
      <div class="table">
        <h3>ORACLE</h3>
        <div class="table-box">
          <div class="table-node">Percent Free</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              hideButton
              v-model="data.stPctfree"
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Percent Used</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stPctused"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Initial Trans</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stInitrans"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Max Trans</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stMaxtrans"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Initial Extent</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.initialExtent"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Initial Extent Unit</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.initialExtentUnit" :disabled="disabled">
              <n-option name="KB" value="KB" />
              <n-option name="MB" value="MB" />
              <n-option name="GB" value="GB" />
            </n-select>
          </div>
          <div class="table-node">Next Extent</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.nextExtent"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Next Extent Unit</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.nextExtentUnit" :disabled="disabled">
              <n-option name="KB" value="KB" />
              <n-option name="MB" value="MB" />
              <n-option name="GB" value="GB" />
            </n-select>
          </div>
          <div class="table-node">Min Extent</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stMinextents"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Min Extent Unit</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.minExtentUnit" :disabled="disabled">
              <n-option name="KB" value="KB" />
              <n-option name="MB" value="MB" />
              <n-option name="GB" value="GB" />
            </n-select>
          </div>
          <div class="table-node">MaxExtent</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stMaxextents"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Max Extent Unit</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.maxExtentUnit" :disabled="disabled">
              <n-option name="KB" value="KB" />
              <n-option name="MB" value="MB" />
              <n-option name="GB" value="GB" />
            </n-select>
          </div>
          <div class="table-node">Unlimited</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.stMaxextentsUnlimited" :disabled="disabled">
              <n-option name="true" :value="1" />
              <n-option name="false" :value="0" />
            </n-select>
          </div>
          <div class="table-node">Pct Increase</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.stPctincrease"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Free Lists</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.freeLists"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Free Groups</div>
          <div class="table-node">
            <n-input-number
              size="sm"
              v-model="data.freeGroups"
              hideButton
              placeholder="请输入数字"
              :disabled="disabled"
            />
          </div>
          <div class="table-node">Buffer Pool</div>
          <div class="table-node">
            <n-select size="sm" v-model="data.bufferPool" :disabled="disabled">
              <n-option name="default" value="default" />
              <n-option name="keep" value="keep" />
              <n-option name="recycle" value="recycle" />
            </n-select>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    name: 'Constraint',
    props: {
      isShow: {
        type: Boolean,
        dufault: false,
      },
      cell: {
        type: Object,
        dufault: {},
      },
    },
    data() {
      return {
        loading: false,
        modType: this.$route.query.modType,
        data: {},
        mainCol: [],
      }
    },
    computed: {
      cellData() {
        return this.cell.data.table
      },
      // 视图与血缘不可编辑
      disabled() {
        // const { type, disableMove } = this.cell.data
        // return type === 'BLOOD' || type === 'VIEW' || disableMove
        return true
      },
    },
    watch: {
      isShow: {
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.getmainCols()
              this.getData()
            })
          }
        },
      },
    },
    methods: {
      getData() {
        this.$api.model
          .getConstraint({
            tableGuid: this.cell.id,
          })
          .then((res) => {
            this.data = res.data || this.data
          })
      },
      saveConstraint() {
        const cellData = { ...this.cell.data }
        this.cell.setData(
          { ...cellData, constraint: { ...this.data, linkGuid: this.cell.id } },
          { overwrite: true },
        )
        this.$api.model
          .saveConstraint({ ...this.data, linkGuid: this.cell.id, guid: this.cell.id })
          .then((res) => {
            if (res.success) {
              this.$message.success('保存约束条件成功！')
            }
          })
      },
      getmainCols() {
        const col = this.cell.data.col
        this.mainCol = col.filter((item) => item.pk)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .entity {
    display: flex;
    padding: 14px;

    &-left {
      width: 600px;
      height: 420px;
      // border: 1px solid #cfcfcf;
      margin-right: 14px;
      overflow: auto;
      .requier {
        margin-right: 4px;
        color: #f56c6c;
      }
    }
    &-right {
      width: calc(100% - 304px);
      height: 420px;
      overflow-y: scroll;
      border: 1px solid #cfcfcf;

      :deep(.el-tabs) {
        height: 100%;

        .el-tabs__content {
          height: calc(100% - 31px);

          > div {
            height: 100%;
          }
        }
      }
    }
    .table-box {
      grid-template-columns: 150px auto !important;
    }
    :deep(.nancalui-input__wrapper) {
      .nancalui-input__inner {
        text-align: left !important;
      }

      width: 100% !important;
      height: 100%;
      background: #fff;
      border: none !important;
    }
    :deep(.nancalui-select__selection) {
      height: 100%;
      background: #fff;
      border: none !important;
    }
  }
</style>
