<template>
  <div :class="{ container: true, isLzos: state.isLzos }">
    <div class="tools">
      <div class="row">
        <div class="add-box-top-title">
          <div class="common-section-header has-bottom-border">
            <div class="title"
              >{{ state.info.sourceFileName }}
              <div
                class="confidentiality-level-label"
                :style="confidentialityLevelFn(state.info.confidentialityLevel)"
                >{{ confidentialityLevelFn(state.info.confidentialityLevel, 'name') }}</div
              >
            </div>
            <div class="detail-back-box">
              <div class="detail-back-box-btn" @click.prevent="cancelFn"
                ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
              </div>
              <div class="detail-back-box-btn checked" @click.prevent="cancelFn"
                ><SvgIcon class="icon" icon="icon-close" />关闭
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row tabs">
        <n-tabs v-model="state.tabs" @active-tab-change="tabChange">
          <n-tab title="文件预览" id="preview" />
          <n-tab
            v-if="state.info.fileFrom === 'SFTP_COLLECT' || state.info.fileFrom === 'FTP_COLLECT'"
            title="查看实例"
            id="example"
          />
          <n-tab
            v-if="
              state.info.sourceFileFormat === 'XLS' ||
              state.info.sourceFileFormat === 'XLSX' ||
              state.info.fileFrom === 'SFTP_COLLECT' ||
              state.info.fileFrom === 'FTP_COLLECT'
            "
            title="版本管理"
            id="version"
          />
        </n-tabs>
      </div>
    </div>
    <div class="content">
      <template v-if="state.info.id">
        <Preview
          v-if="state.tabs === 'preview'"
          ref="previewRef"
          :info="state.info"
          :version="state.version"
          :signInfo="state.signInfo"
          @PdfSign="pdfSignShow"
        />
        <Example v-else-if="state.tabs === 'example'" :info="state.info" />
        <Version v-else-if="state.tabs === 'version'" :info="state.info" @seeFile="seeFile" />
      </template>
    </div>
  </div>

  <PdfSign ref="pdfSignRef" @saveSign="saveSign" />
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  // 预览组件
  import Preview from './components/Preview.vue'
  import Example from './components/Example.vue'
  import Version from './components/Version.vue'
  import PdfSign from './components/PdfSign.vue'
  const store = useStore()
  const router = useRouter()
  const pdfSignRef = ref()
  const previewRef = ref(null)

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    tabs: 'preview',
    version: '',
    id: null,
    info: {},
    signInfo: { status: false },
  })
  // 取消
  const cancelFn = () => {
    router.push({ name: 'resourceLibraryList' })
  }
  // 切换tab
  const tabChange = () => {
    state.version = ''
    if (state.tabs === 'preview') {
      if (state.info.id) {
      }
    }
  }
  // 查看版本文件
  const seeFile = (version) => {
    state.tabs = 'preview'
    state.version = version
  }
  // 密级
  const confidentialityLevelFn = (type, isName = false) => {
    let color = '',
      name = '内部'
    if (type === 'PUBLIC') {
      name = '公开'
      color = 'color:#04C495;background-color:#E6FFF4;border-color:#04C495'
    } else if (type === 'CONFIDENTIAL') {
      name = '机密'
      color = 'color:#FF7D00;background-color:#FFF6E6;border-color:#FF7D00'
    } else if (type === 'CONTROLLED') {
      name = '受控'
      color = 'color:#7538F6;background-color:#F7F0FF;border-color:#7538F6'
    } else if (type === 'SECRET') {
      name = '秘密'
      color = 'color:#EC3B9C;background-color:#FFF0F6;border-color:#EC3B9C'
    } else if (type === 'CORE') {
      name = '核心'
      color = 'color:#F63838;background-color:#FFF2F0;border-color:#F63838'
    }
    if (isName) {
      return name
    } else {
      return color
    }
  }

  const pdfSignShow = () => {
    pdfSignRef.value.showAddDrawer = true
  }

  const saveSign = (obj) => {
    state.signInfo = { status: true, info: obj }
    previewRef.value.getUserSignListData()
  }
  onMounted(() => {
    state.id = String(router.currentRoute.value.query.id)
    api.assets.getFilDetail({ id: state.id }).then((res) => {
      if (res.success) {
        state.info = res.data
      }
    })
  })
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    padding: 12px;
    .tools {
      background-color: #fff;
      border-radius: 8px;
      .row {
        position: relative;
        .add-box-top-title {
          margin-bottom: 0;
          .title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .confidentiality-level-label {
              display: inline-block;
              height: 24px;
              margin-left: 8px;
              padding: 0 8px;
              color: rgba(0, 0, 0, 0.75);
              font-weight: normal;
              font-size: 14px;
              line-height: 22px;
              text-align: center;
              background-color: #e3ecff;
              border: 1px solid #6e9eff;
              border-radius: 6px;
            }
          }
          .detail-back-box-btn {
            font-weight: normal;
          }
        }
        &.tabs {
          display: flex;
          align-items: flex-end;
          height: 48px;
          padding: 0 16px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 108px);
      margin-top: 8px;
      background-color: #fff;
      border-radius: 8px;
    }
  }
</style>
