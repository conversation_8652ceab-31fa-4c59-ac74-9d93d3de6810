<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>全屏</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="元数据地图（默认）" transform="translate(-1344.000000, -89.000000)">
            <g id="搜索" transform="translate(556.000000, 78.000000)">
                <g id="编组-14" transform="translate(574.000000, 0.000000)">
                    <g id="全屏" transform="translate(214.000000, 11.000000)">
                        <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                        <g id="编组-9" transform="translate(3.000000, 3.000000)" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="0" y1="0" x2="3.75" y2="3.7123125" id="路径" stroke-width="1.5"></line>
                            <line x1="0" y1="13.4623125" x2="3.75" y2="9.75" id="路径" stroke-width="1.5"></line>
                            <line x1="13.5000375" y1="13.4623125" x2="9.787725" y2="9.75" id="路径" stroke-width="1.5"></line>
                            <line x1="13.4623125" y1="0" x2="9.75" y2="3.7123125" id="路径" stroke-width="1.5"></line>
                            <polyline id="路径" stroke-width="1.5" points="10.125 0 13.5 0 13.5 3.375"></polyline>
                            <polyline id="路径" stroke-width="1.25" points="13.5 10.125 13.5 13.5 10.125 13.5"></polyline>
                            <polyline id="路径" stroke-width="1.25" points="3.375 13.5 0 13.5 0 10.125"></polyline>
                            <polyline id="路径" stroke-width="1.5" points="0 3.375 0 0 3.375 0"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
