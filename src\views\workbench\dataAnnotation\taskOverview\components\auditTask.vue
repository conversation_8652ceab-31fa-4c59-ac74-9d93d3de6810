<template>
    <div v-loading="state.loading" class="table-box">
        <CfTable actionWidth="120" ref="tableNoRef" :tableConfig="{
            data: state.dataSource,
            rowKey: 'id',
        }" :table-head-titles="tableHead" :paginationConfig="{
            total: state.total,
            pageSize: state.searchData.pageSize,
            currentPage: state.searchData.pageNum,
            onCurrentChange: (v) => {
                state.searchData.pageNum = v
                onSearch(false)
            },
            onSizeChange: (v) => {
                state.searchData.pageSize = v
                onSearch()
            },
        }"><template #annotationDataDownloadUrl="{ row }">
                <n-button class="download-btn" color="primary" variant="text" :disabled="!row.annotationDataDownloadUrl"
                    @click="downloadFile(row.annotationDataDownloadUrl, row.annotationDataFileOriginalName)">
                    下载
                </n-button>
            </template>
            <template #ruleFileDownloadUrl="{ row }">
                <n-button class="download-btn" color="primary" variant="text"
                    @click="downloadFile(row.originalDataDownloadUrl, row.originalDataFileOriginalName)"
                    :disabled="!row.originalDataDownloadUrl">
                    下载
                </n-button>
            </template>
            <template #editor="{ row }">
                <!-- 提交完成 -->
                <n-button class="submit-btn" :disabled="row.auditStatus === '已完成'" color="primary" variant="text"
                    @click="submitTask(row)">
                    提交完成
                </n-button>
            </template>
            <template #conclusionFileUrl="{ row }">
                <div style="gap: 4px; display: flex; align-items: center">
                    <n-button class="upload-btn" color="primary" variant="text" @click="uploadFile(row)">
                        上传
                    </n-button>
                    <div class="more" :title="row?.conclusionFileOriginalName || '--'">{{
                        row?.conclusionFileOriginalName || '--'
                    }}</div>
                </div>
            </template>
        </CfTable>
    </div>
</template>

<script setup>
import api from '@/api' // 引入api
import { reactive, onMounted, defineProps } from 'vue'

const props = defineProps({
    info: {
        type: Object,
        default: {},
    },
})

const state = reactive({
    formData: {
        // 表单数据
        teamInfo: {
            name: '',
            userIdLeader: null,
        },
        teamUserList: [{}],
    },
    teamDrawerVisible: false, // 团队弹窗
    dataSource: [], // 表格数据
    searchData: {
        // 分页
        condition: {},
        pageNum: 1, // 当前页
        pageSize: 10, // 每页显示条数
    },
    total: 0, // 总条数
})
const tableHead = computed(() => {
    // 表格表头
    return [
        { name: '任务名称', prop: 'taskName' },
        { name: '上传时间', prop: 'completionTime' },
        {
            name: '标注后数据',
            prop: 'annotationDataDownloadUrl',
            slot: 'annotationDataDownloadUrl',
        },
        { name: '审核结论', prop: 'conclusionFileUrl', slot: 'conclusionFileUrl', width: 240 },
        { name: '状态', prop: 'auditStatus' },
    ]
})
const onSearch = (reset = true) => {
    // 搜索
    console.log('搜索')
    // api.dataGovernance
    //     .getAuditTask(state.searchData)
    //     .then((res) => {
    //         if (res.success) {
    //             state.dataSource = res.data.list
    //             state.total = res.data.total
    //         }
    //     })
    //     .catch(() => { })
    state.dataSource = [
        {
            "id": 1,
            "taskName": "档案图片标注",
            "taskId": 1,
            "teamName": "李伟的团队-不要删除",
            "teamId": 1,
            "taskType": "图像标注",
            "completionTime": "2025-5-30 23:59:59",
            "originalDataDownloadUrl": "http://10.41.26.51:9000/data-govern/share/2025_05_02_13_50_45_172_%E4%B8%BB%E6%95%B0%E6%8D%AE%E8%A7%84%E5%88%922025.docx",
            "originalDataFileOriginalName": "主数据规划2025.docx",
            "ruleFileDownloadUrl": "http://10.41.26.51:9000/data-govern/share/2025_05_02_13_50_38_661_datax%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%EF%BC%88%E5%AE%8C%E6%95%B4%E7%89%88%EF%BC%89.doc",
            "ruleFileOriginalName": "datax使用说明（完整版）.doc",
            "annotationDataDownloadUrl": "http://10.41.26.51:9000/data-govern/share/2025_05_02_14_14_22_602_%E4%BC%9A%E8%AE%AE%E7%BA%AA%E8%A6%81-%E6%A8%A1%E6%9D%BF.docx",
            "annotationDataFileOriginalName": "会议纪要-模板.docx",
            "conclusionFileUrl": "http://10.41.26.51:9000/data-govern/share/2025_05_02_14_15_02_375_724%E9%9B%86%E6%88%90%E6%B5%8B%E8%AF%95%E8%AF%B4%E6%98%8E.docx",
            "conclusionFileOriginalName": "724集成测试说明.docx",
            "dataValuation": "73",
            "auditStatus": "已完成",
            "taskStage": "开始",
            "userIdLeader": 10944,
            "role": null
        }
    ]
}
// 下载
const downloadFile = (url, name) => {
    // 下载文件 包括图片 pdf等
    fetch(url).then((res) => {
        // 成功
        res.blob().then((blob) => {
            // 成功
            const link = document.createElement('a') // 创建a标签
            link.href = URL.createObjectURL(blob) // 创建下载链接
            // link.download = url.split('/').pop() // 下载文件名
            link.download = name // 下载文件名
            document.body.appendChild(link) // 插入body
            link.click() // 点击下载
            document.body.removeChild(link) // 移除body
        })
    })
}
// 提交完成
const submitTask = (row) => {
    // 提交完成
    // api.dataGovernance.approvalPass({ id: row.id }).then((res) => {
    //     // 成功
    //     if (res.success) {
    //         // 成功
    //         ElMessage.success('提交完成') // 提示
    //         onSearch() // 搜索
    //     } else {
    //         // 失败
    //         ElMessage.error('提交失败') // 提示
    //     }
    // })
    ElMessage.success('提交完成') // 提示
}

// 上传
const uploadFile = (task) => {
    // 上传文件 包括图片 pdf等
    const fileInput = document.createElement('input') // 创建input标签
    fileInput.type = 'file' // 文件类型
    fileInput.onchange = (e) => {
        // 选择文件
        const file = e.target.files[0] // 获取文件
        if (file) {
            // 文件存在
            const file = e.target.files[0] // 获取文件
            console.log(file) // 打印文件
            const formData = new FormData()
            formData.append('file', file)
            formData.append('bucket', 'data-govern')
            api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
                if (res.success) {
                    // 上传成功
                    // api.dataGovernance
                    //     .uploadAuditConclusion({
                    //         id: task.id,
                    //         url: res.data.url,
                    //         fileOriginalName: file.name,
                    //     })
                    //     .then((res) => {
                    //         // 成功
                    //         if (res.success) {
                    //             // 成功
                    //             ElMessage.success('上传成功') // 成功提示
                    //             onSearch() // 刷新表格
                    //         }
                    //     })
                }
            })
        }
    }
    fileInput.click() // 点击上传
}
onSearch()
</script>

<style lang="scss" scoped>
.table-box {
    position: relative;
    width: 100%;
    height: 100%;
}
</style>