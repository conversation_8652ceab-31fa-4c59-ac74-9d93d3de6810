<template>
  <div class="container">
    <!--    <div class="btn-group">-->
    <!--      &lt;!&ndash; 任务管理 &ndash;&gt;-->
    <!--      <n-button variant="solid" @click="handleTaskManagement">-->
    <!--        任务管理-->
    <!--      </n-button>-->
    <!--    </div>-->

    <div class="task-content">
      <!-- 任务卡片 -->
      <div class="task-card" v-for="(task, index) in state.dataSource" :key="index">
        <div class="card-header">
          <div>{{ task.taskName }}</div>
        </div>
        <div class="card-body">
          <p>任务类型: {{ task.taskType }}</p>
          <p>发起单位: {{ task.initiatingUnit }}</p>
          <p>预计完成时间：{{ task.completionTime }}</p>
        </div>
        <!-- 领取任务 -->
        <div class="card-footer">
          <n-button variant="solid" @click="handleClaimTask(task)"> 领取任务 </n-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <CfTable
        :paginationConfig="{
          total: state.total,
          pageSize: state.searchData.pageSize,
          currentPage: state.searchData.pageNum,
          onCurrentChange: (v) => {
            state.searchData.pageNum = v
            onSearch(false)
          },
          onSizeChange: (v) => {
            state.searchData.pageSize = v
            onSearch()
          },
        }"
      />
    </div>
    <n-modal
      v-model="state.claimTaskModalVisible"
      title="领取任务"
      width="560px"
      :close-on-click-overlay="false"
      @close="state.claimTaskModalVisible = false"
    >
      <n-form
        :data="state.formData"
        label-width="180px"
        ref="formRef"
        style="height: 200px; display: flex; flex-direction: column; justify-content: center"
      >
        <n-form-item
          field="teamId"
          label="请选择领取该任务的团队："
          :rules="[
            { required: true, message: '请选择领取该任务的团队', trigger: 'blur', type: 'number' },
          ]"
        >
          <n-select v-model="state.formData.teamId" placeholder="请选择团队">
            <n-option
              v-for="(item, index) in state.teamList"
              :key="index"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </n-form-item>
      </n-form>
      <template #footer>
        <n-modal-footer class="dialog-footer cenetr-footer">
          <!-- 确定 -->
          <n-button color="primary" variant="solid" @click.prevent="handleClaimTaskSubmit">
            确定
          </n-button>
          <!-- 取消 -->
          <n-button color="primary" @click.prevent="state.claimTaskModalVisible = false">
            取消
          </n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>
<script setup>
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const formRef = ref(null) // 表单ref
  const state = reactive({
    dataSource: [], // 表格数据
    formData: {
      // 表单数据
      teamId: '',
      taskId: '',
    },
    claimTaskModalVisible: false, // 领取任务弹窗
    searchData: {
      // 搜索数据
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
      condition: {},
    },
    total: 0, // 总条数
    teamList: [
      // 团队列表
    ],
  })
  const tasks = ref([])
  const handleClaimTask = (task) => {
    // 领取任务
    console.log('领取任务', task)
    state.formData = {}
    state.claimTaskModalVisible = true
    state.formData.taskId = task.id
  }
  const onSearch = (reset = true) => {
    // 搜索
    if (reset) {
      // 重置页码
      state.searchData.pageNum = 1
    }
    api.dataGovernance.getTaskPageAll(state.searchData).then((res) => {
      // 成功
      if (res.success) {
        state.dataSource = res.data?.list || [] // 表格数据
        state.total = res.data?.total || 0 // 总条数
      }
    })
  }
  const handleClaimTaskSubmit = () => {
    // 领取任务提交
    console.log('领取任务提交', state.formData)
    formRef.value.validate((val) => {
      // 表单验证
      if (val) {
        // 验证通过
        api.dataGovernance.claimTask(state.formData).then((res) => {
          // 成功
          if (res.success) {
            // 成功
            ElMessage.success('领取任务成功') // 提示
            state.claimTaskModalVisible = false // 关闭弹窗
            onSearch() // 搜索
          }
        })
      }
    })
  }
  const handleTaskManagement = () => {
    // 任务管理
    console.log('任务管理')
    router.push({ name: 'taskManagement' })
  }
  // 获取团队列表
  const getTeamList = () => {
    // 获取团队列表
    api.dataGovernance.getTeamList({ type: 'OWN' }).then((res) => {
      // 成功
      if (res.success) {
        // 成功
        state.teamList = res.data || [] // 团队列表
      }
    })
  }
  onSearch()
  getTeamList()
</script>
<style lang="scss" scoped>
  .container {
    padding: 16px;
    border-radius: 0;

    .btn-group {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 16px;
      background-color: #fff;
    }

    .task-content {
      width: 100%;
      height: calc(100% - 64px);
      background-color: #fff;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: 16px;
      border-radius: 0;
      overflow-y: auto;
      padding: 16px;

      .task-card {
        height: min-content;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        position: relative;

        .card-footer {
          display: inline-block;
          position: absolute;
          right: 16px;
          bottom: 16px;
          display: flex;
        }
      }
    }

    .pagination {
      width: 100%;
      height: 64px;
      background-color: #fff;
    }
  }
</style>
