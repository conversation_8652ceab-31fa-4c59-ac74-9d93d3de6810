<template>
  <!-- -API监控日志列表 -->
  <div :class="['api-management-list container', state.isLzos ? 'isLzos' : '']">
    <div class="top-title">
      <div class="name">查看API实例</div>
      <n-button @click.prevent="goBack">返回</n-button>
    </div>
    <div class="list-box" v-loading="state.loading">
      <CfTable
        :actionWidth="104"
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
        :paginationConfig="{
          total: state.tableData.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,
          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            initTable()
          },
          onSizeChange: (v) => {
            state.pagination.pageSize = v
            initTable(true)
          },
        }"
      >
        <template #statusName="{ row }">
          <div>
            <div>
              <span
                :class="{
                  dot: true,
                  red: row.statusName === '失败',
                  blue: row.statusName === '成功',
                }"
              ></span>
              {{ row.statusName }}
            </div>
          </div>
        </template>
        <template #consumeTime="{ row }">
          <div class="edit-box"> {{ row.consumeTime }}ms</div>
        </template>
        <template #editor="{ row }">
          <div class="edit-box">
            <n-button
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="seeLog(row)"
              >日志详情
            </n-button>
          </div>
        </template>
      </CfTable>
    </div>
    <n-drawer
      v-model="state.dialogVisible"
      title=""
      :size="680"
      :esc-key-closeable="false"
      :close-on-click-overlay="true"
      :before-close="closeDialog"
      class="see-log-drawer"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">查看日志</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDialog" />
        </div>
        <div class="n-drawer-body-content">
          <div v-loading="state.logLoading" class="log">
            <div class="success-box" v-show="!state.log">
              <div class="title">请求成功,请求结果为：</div>
              <div class="content">
                <json-viewer :value="state.successData" :expand-depth="5" copyable sort />
              </div>
            </div>
            <div class="fail-box" v-show="state.log">
              <div class="title">请求失败,失败日志为：</div>
              <div class="content">
                {{ state.log }}
              </div>
            </div>
          </div>
        </div>
        <div class="options-box-bg">
          <div class="options-box-content">
            <n-button
              size="sm"
              color="primary"
              variant="solid"
              :loading="state.downloadLogLoading"
              @click.prevent="exportLog"
              >导出日志</n-button
            >
            <n-button @click.prevent="closeDialog">关闭</n-button>
          </div>
        </div>
      </div>
    </n-drawer>
  </div>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter, useRoute } from 'vue-router'
  export default {
    title: 'List',
    props: {},
    setup() {
      const route = useRoute()
      const router = useRouter()
      // 获取当前组件实例
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 400,
        tableData: {
          total: 100,
          pageNum: 1,
          pageSize: 1,
          list: [],
        },
        loading: false,
        logLoading: false,
        downloadLogLoading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'apiName', name: 'API名称' },
          { prop: 'requestMethod', name: '请求方式' },
          { prop: 'createTime', name: '调用时间' },
          { prop: 'ip', name: '调用IP' },
          { prop: 'statusName', name: '状态', slot: 'statusName' },
          { prop: 'consumeTime', name: '运行时长', slot: 'consumeTime' },
          { prop: 'dataCount', name: '分发数据量' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        logId: '',
        log: '',
        successData: {},
        logData: null,
      })

      const methods = {
        convertTimestampToDetailedTimeUnit(timestamp) {
          const totalSeconds = timestamp / 1000
          const days = Math.floor(totalSeconds / (3600 * 24))
          const hours = Math.floor((totalSeconds % (3600 * 24)) / 3600)
          const minutes = Math.floor((totalSeconds % 3600) / 60)
          const seconds = totalSeconds % 60

          let timeString = ''
          if (days > 0) {
            timeString += `${days} day${days > 1 ? 's' : ''}, `
          }
          if (hours > 0) {
            timeString += `${hours} hour${hours > 1 ? 's' : ''}, `
          }
          if (minutes > 0) {
            timeString += `${minutes} minute${minutes > 1 ? 's' : ''}, `
          }

          timeString += seconds.toFixed(3) + ' second' + (seconds !== 1 ? 's' : '')

          return timeString
        },

        goBack() {
          // router.go(-1)
          router.push({ name: 'apiMonitorList' })
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 96 - 160
        },
        closeDialog() {
          state.dialogVisible = false
          state.log = ''
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: route.query.id,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.documentManage
            .getApiMonitoringInstanceList(data)
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                state.tableData = res.data
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },

        // 查看日志
        seeLog(row) {
          state.logLoading = true
          state.dialogVisible = true
          state.logId = row.logId
          state.logData = row
          api.documentManage
            .getApiMonitoringInstanceDetail({
              logId: row.logId,
            })
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.log = data.errorMessage
                if (!state.log) {
                  state.successData = data.data
                }
              } else {
                state.log = '获取日志失败'
              }
              state.logLoading = false
            })
            .catch(() => {
              state.log = '获取日志失败'
              state.logLoading = false
            })
        },
        // 导出日志
        exportLog() {
          state.downloadLogLoading = true
          let logName = state.logData.apiName + '_' + state.logData.createTime + '的日志.log'
          methods.savefiles(state.log || state.successData, logName)
        },
        /**
         * @param data 需要保存的内容
         * @param name 保存的文件名
         */
        savefiles(data, name) {
          // 将数组转换为 JSON 字符串
          var data_string = JSON.stringify(data, null, 2) // 第三个参数是美化输出的缩进

          // 创建一个 Blob 对象，类型为 text/plain
          var export_blob = new Blob([data_string], { type: 'text/plain' })

          // 创建一个下载链接
          var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
          var urlObject = window.URL || window.webkitURL || window
          save_link.href = urlObject.createObjectURL(export_blob)
          save_link.download = name // 你可以根据需要修改文件名
          document.body.appendChild(save_link) // 将链接添加到非结构化数据中
          save_link.click() // 触发点击事件以下载文件
          document.body.removeChild(save_link) // 下载后移除链接
          state.downloadLogLoading = false
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable()
      })

      return {
        state,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    position: relative;
    &.isLzos {
      padding: 0;
    }
    .top-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 46px;
      margin-bottom: 10px;
      padding: 0 8px 0 16px;
      text-align: right;
      background-color: #fff;
      border-radius: 2px;
      .name {
        color: var(----, rgba(0, 0, 0, 0.9));
        font-weight: bolder;
        font-size: 16px;
      }
      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }
      .nancalui-button {
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        &:hover {
          color: $themeBlue;
          border: 1px solid $themeBlue;
        }
      }
    }
    .list-box {
      height: calc(100% - 54px);
      padding: 0 16px;
      padding: 16px;
      background-color: #fff;
      border-radius: 4px;

      .seeDetails {
        color: $themeBlue;
      }

      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #18ba72;
        border-radius: 50px;

        &.red {
          background: #f54446;
        }

        &.blue {
          background: $themeBlue;
        }
      }
    }
  }

  .nancalui-modal {
    .api-box-name {
      height: 20px;
      margin-bottom: 10px;
      color: #333333;
      font-size: 12px;
      line-height: 20px;
    }
    .header-box {
      display: flex;
      width: 100%;
      height: 42px;
      color: #000;
      font-size: 12px;
      line-height: 42px;
      background: #f7f8fa;
      border: 1px solid #e1e1e1;
      .number {
        width: 64px;
        text-align: center;
      }
      .ip-box {
        flex: 1;
      }
      .handle-box {
        width: 94px;
        text-align: center;
      }
    }
    .log {
      box-sizing: border-box;
      height: 310px;
      padding: 14px;

      background: #f7f8fa;
      border-radius: 8px;
      .success-box {
        height: 100%;
        .content {
          background: #fff;
        }

        .jv-container.jv-light {
        }
      }
      .fail-box {
        height: 100%;
      }
      .title {
        padding: 0 0 10px 0;
      }
      .content {
        height: calc(100% - 28px);
        overflow: auto;
        border-radius: 6px;
      }
    }
  }
</style>
<style lang="scss">
  .see-log-drawer {
    .n-drawer-body-header {
      position: relative;
      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }
    }
    .n-drawer-body-content {
      padding: 16px !important;
      &::-webkit-scrollbar {
        width: 10px !important; // 横向滚动条
        height: 10px !important; // 纵向滚动条 必写
      }
    }
    .options-box-bg {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 64px;
      padding: 16px;
      background-color: #fff;
      border-radius: 0 0 8px 8px;

      .options-box-content {
        display: inline-block;
        width: 100%;
        text-align: right;
        background-color: #fff;
      }
    }
  }
</style>
