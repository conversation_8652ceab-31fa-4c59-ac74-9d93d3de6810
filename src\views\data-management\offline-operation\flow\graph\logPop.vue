<template>
  <div v-if="props.isCanvas">
    <teleport to="body">
      <section class="logPop">
        <div class="box">
          <div class="title"
            >查看日志<SvgIcon class="icon" icon="icon-close" @click.prevent="closeFn"
          /></div>
          <pre class="log">{{ state.log }}</pre>
          <div class="footer">
            <n-button v-if="props.nodeInfo.taskInstanceId" @click="closeFn">取消</n-button>
            <n-button v-if="props.nodeInfo.taskInstanceId" variant="solid" @click="downFn"
              >下 载</n-button
            >
            <div v-else class="footer-btn" @click="closeFn">取 消</div>
            <n-button
              v-if="props.nodeInfo.taskInstanceId && state.log"
              variant="solid"
              @click="downFn"
              >下 载</n-button
            >
          </div>
        </div>
      </section>
    </teleport>
  </div>
  <n-drawer
    v-else
    v-model="state.showDrawer"
    title=""
    :size="900"
    :esc-key-closeable="false"
    :close-on-click-overlay="true"
    :before-close="closeFn"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">查看日志</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeFn" />
      </div>
      <div class="n-drawer-body-content">
        <pre class="log">{{ state.log || '暂无日志' }}</pre>
      </div>
      <div class="n-drawer-body-footer">
        <n-button v-if="props.nodeInfo.taskInstanceId" @click="closeFn">取消</n-button>
        <div v-else class="footer-btn" @click="closeFn">取 消</div>
        <n-button v-if="props.nodeInfo.taskInstanceId && state.log" variant="solid" @click="downFn"
          >下 载</n-button
        >
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import { taskRunResult, taskStateLog, instanceDag, taskStateLogDownload } from '@/api/dataManage'
  const state = reactive({
    log: '',
    showDrawer: true,
  })
  const emits = defineEmits(['seeLogFn'])
  const props = defineProps({
    nodeInfo: {
      type: Object,
      default: {},
    },
    isCanvas: {
      type: Boolean,
      default: false,
    },
  })

  const closeFn = () => {
    emits('seeLogFn', false)
  }
  const downFn = () => {
    taskStateLogDownload({ taskInstanceId: props.nodeInfo.taskInstanceId }).then((res) => {
      try {
        // 下载文件
        const blob = new Blob([res], {
          type: 'text/plain',
        })
        const link = document.createElement('a')
        let fileName = '日志.log'
        if (props.nodeInfo.name) {
          fileName = props.nodeInfo.name + '的日志.log'
        }
        link.download = fileName
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        document.body.appendChild(link)
        link.click()
        URL.revokeObjectURL(link.href)
        document.body.removeChild(link)
      } catch (e) {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        }
      }
    })
  }
  onMounted(() => {
    if (props.isCanvas) {
      let data = {
        dsTaskId: props.nodeInfo.dsTaskId || null,
        processId: props.nodeInfo.processId || null,
        taskCode: props.nodeInfo.nodeCode,
        instanceOperationType: 'VIEW_LOG',
      }
      instanceDag(data).then((res) => {
        if (res.success) {
          state.log = res.data.logs
        }
      })
      return false
    }
    if (props.nodeInfo.taskInstanceId) {
      taskStateLog({ taskInstanceId: props.nodeInfo.taskInstanceId }).then((res) => {
        if (res.success) {
          state.log = res.data.log
        }
      })
    } else {
      taskRunResult({
        dsProcessCode: props.nodeInfo.dsProcessCode,
        nodeCode: props.nodeInfo.nodeCode,
      }).then((res) => {
        if (res.success) {
          state.log = res.data.log
        }
      })
    }
  })
</script>
<style lang="scss" scoped>
  .logPop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2001;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    .box {
      position: absolute;
      top: 0;
      right: 0;
      width: 900px;
      height: 100%;
      background: #fff;
    }

    .title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 13px 16px;
      height: 52px;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 18px;
      border-bottom: 1px solid #e5e6eb;
      .icon {
        color: #8091b7;
        font-size: 24px;
      }
    }
    .log {
      box-sizing: border-box;
      height: calc(100vh - 116px);
      padding: 16px 16px 0 16px;
      overflow-y: auto;
      margin: 0;
      color: #1d2129;
      font-size: 12px;
      word-wrap: break-word;
      overflow-wrap: break-word;

      &::-webkit-scrollbar {
        width: 10px !important; // 横向滚动条
        height: 10px !important; // 纵向滚动条 必写
      }
    }
    .footer {
      display: flex;
      justify-content: flex-end;
      padding: 10px;
      &-btn {
        width: 62px;
        height: 32px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        background: #1e89ff;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background-color: #6e9eff;
        }
      }
    }
  }
  .n-drawer-body {
    height: 100%;
    .n-drawer-body-content {
      .log {
        color: #1d2129;
        font-size: 12px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      &::-webkit-scrollbar {
        width: 10px !important; // 横向滚动条
        height: 10px !important; // 纵向滚动条 必写
      }
    }
    .n-drawer-body-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px;
      padding: 0 16px;
      .footer-btn {
        width: 62px;
        height: 32px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        background: #1e89ff;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background-color: #6e9eff;
        }
      }
    }
  }
</style>
