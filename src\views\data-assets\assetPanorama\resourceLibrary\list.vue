<template>
  <!-- 资产全景-资源库 -->
  <div class="resource-library-page">
    <div class="resource-library-page-top">
      <div class="top-change-project">
        <div class="top-change-project-left">
          <span>资源库</span>
        </div>
      </div>
      <div class="mid-statistics">
        <div class="statistics-box-left">
          <div class="need_smallcube__title">数据统计</div>
        </div>
        <div class="statistics-box">
          <div
            class="statistics-list"
            v-for="(item, index) in state.statisticalDataType"
            :key="item.title"
          >
            <img :src="item.icon" alt="" />
            <div class="statistics-list-des" @click="changeTable(index)">
              <div
                :class="{
                  'statistics-list-des-box': true,
                  active: state.activeStyle === index,
                  'can-click': item.viewable,
                }"
              >
                <div class="top"
                  >{{ item.title }}
                  <SvgIcon v-if="item.viewable" icon="eye-open" />
                </div>
                <div>
                  <span class="number">{{ item.number }}</span>
                  <span class="unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="statistics-table">
      <PublicTable
        ref="publicTable"
        :isDisplayAction="true"
        :isNeedSelection="false"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :exceptHeight="440"
        :key="state.activeStyle"
        @tablePageChange="tablePageChange"
      >
        <template #pageTop>
          <div class="box-add">
            <div class="top-left">
              <span class="need_smallcube__title">
                {{ state.statisticalDataType[state.activeStyle].title }}
              </span>
            </div>
            <el-form :inline="true" :model="state.filterSearch" class="commonForm">
              <el-form-item label="名称：">
                <el-input
                  v-model="state.filterSearch.keyword"
                  size="small"
                  placeholder="请输入查询关键字"
                  clearable
                  @change="initTable"
                >
                  <template #append>
                    <n-button @click.stop.prevent="initTable">
                      <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                        <SvgIcon class="icon_search" icon="icon_search" />
                      </n-popover>
                    </n-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
            <!-- <div class="top-right"> </div> -->
          </div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              class="del-button has-right-border"
              variant="text"
              @click.stop.prevent="checkThisTypeDetails(editor)"
              >查看
            </n-button>
          </div>
        </template>
      </PublicTable>
    </div>
    <!-- 查看弹框 -->
    <detailPopup
      :dialogTitle="state.dialogTitle"
      :dialogVisible="state.dialogVisible"
      :detailRuleForm="state.detailRuleForm"
      :detailData="state.detailData"
      :width="862"
      @closeDialog="closeDialog"
    />
  </div>
</template>
<script>
  import { reactive, onMounted, nextTick, ref } from 'vue'
  // import { ElNotification } from 'element-plus'
  import detailPopup from './detailPopup'
  import api from '@/api/index'

  export default {
    name: 'AuthorizedPersonnel',
    components: {
      detailPopup,
    },
    setup() {
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/assetLibrary/${name}.png`, import.meta.url).href //本地文件路径
      }
      // 获取当前组件实例
      // const { proxy } = getCurrentInstance()
      const publicTable = ref()
      const state = reactive({
        dialogTitle: '元数据详情',
        dialogVisible: false,
        detailRuleForm: {},
        detailData: {},
        // 统计的所有类
        statisticalDataType: [
          {
            title: '数据源',
            number: '--',
            viewable: true,
            icon: getAssetsImages('resource-library-img1'),
            unit: '条',
          },
          {
            title: '数据表',
            number: '--',
            viewable: false,
            icon: getAssetsImages('resource-library-img2'),
            unit: '条',
          },
          {
            title: '存储量',
            number: '--',
            viewable: false,
            icon: getAssetsImages('resource-library-img3'),
            unit: 'KB',
          },
          {
            title: '新增存储量',
            number: '--',
            viewable: false,
            icon: getAssetsImages('resource-library-img3'),
            unit: 'KB',
          },
          // {
          //   title: '文件数',
          //   number: '--',
          //   viewable: true,
          //   icon: getAssetsImages('resource-library-img4'),
          //   unit: '条',
          // },
          // {
          //   title: '文件总量',
          //   number: '--',
          //   viewable: false,
          //   icon: getAssetsImages('resource-library-img4'),
          //   unit: '条',
          // },
          // {
          //   title: '文件增量',
          //   number: '--',
          //   viewable: false,
          //   icon: getAssetsImages('resource-library-img4'),
          //   unit: '条',
          // },
        ],
        activeStyle: 0, // 选中的查看类

        allTableHeadTitles: [
          [
            { prop: 'number', name: '序号', width: 80 },
            { prop: 'name', name: '数据源名称' },
            { prop: 'type', name: '数据库类型' },
            // { prop: 'projectName', name: '密级' },
            { prop: 'description', name: '描述信息' },
            { prop: 'createTime', name: '创建时间' },
            { prop: 'createByName', name: '创建人' },
          ],
          [
            { prop: 'number', name: '序号', width: 80 },
            { prop: 'cnName', name: '数据表名称' },
            { prop: 'name', name: '注释' },
            { prop: 'layerName', name: '密级' },
            { prop: 'bizDomain', name: '用户' },
            { prop: 'projectName', name: '数据源' },
            { prop: 'createTime', name: '存储量' },
            { prop: 'createByName', name: '记录条数' },
          ],
          [],
          [],
          [
            { prop: 'number', name: '序号', width: 80 },
            { prop: 'name', name: '文件名称' },
            { prop: 'groupName', name: '文件类型' },
            { prop: 'remark', name: '密级' },
            { prop: 'createTime', name: '文件大小' },
            { prop: 'createByName', name: '路径' },
            { prop: 'createByName', name: '创建时间' },
          ],
        ], // 类集合表头
        tableHeadTitles: [], //选中类表头
        pagination: {
          pageSizes: [10, 20, 50], // 每次展示条数的可配置项
          layout: 'total,prev,pager,next,sizes, jumper',
          currentPage: 1,
          pageSize: 12,
        },
        filterSearch: {
          keyword: '',
        },
        project_options: [],
        interfaceName: 'getResourceLibraryDatasourceList', //默认获取数据源列表
      })

      const methods = {
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取统计数据
        getStatistics() {
          api.assets.getResourceLibraryCount().then((res) => {
            let { success } = res
            if (success) {
              let { datasourceNum, tableNum, storageTotal, storageNewNum } = res.data
              state.statisticalDataType[0].number = datasourceNum
              state.statisticalDataType[1].number = tableNum
              let _storageTotal = methods.storageUnitConversion(storageTotal) //存储单位换算
              state.statisticalDataType[2].number = _storageTotal.value
              state.statisticalDataType[2].unit = _storageTotal.unit
              let _storageNewNum = methods.storageUnitConversion(storageNewNum) //存储单位换算
              state.statisticalDataType[3].number = _storageNewNum.value
              state.statisticalDataType[3].unit = _storageNewNum.unit
            }
          })
        },
        // 存储单位换算 Gb-Mb-Kb 达到0.1下级单位换下级单位
        storageUnitConversion(limit) {
          var size = ''
          var unit = 'KB'
          if (limit < 0.1 * 1024) {
            size = limit.toFixed(2)
            unit = 'KB'
          } else if (limit < 0.1 * 1024 * 1024) {
            //如果小于0.1GB转化成MB
            size = (limit / 1024).toFixed(2)
            unit = 'MB'
          } else {
            //其他转化成GB
            size = (limit / (1024 * 1024)).toFixed(2)
            unit = 'GB'
          }

          return {
            value: size,
            unit,
          }
        },
        // 关闭弹框
        closeDialog() {
          state.dialogVisible = false
        },
        // 初始化表格
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              name: state.filterSearch.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          if (state.activeStyle === 2) {
            // 标签数不传
            delete data.condition.projectCode
          }
          api.assets[state.interfaceName](data)
            .then((res) => {
              if (res.success) {
                // 新增序号属性
                res.data.list.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                  })
                })
                if (res.data && res.data.list) {
                  publicTable.value.initTableData(res.data)
                } else {
                  publicTable.value.initTableData({ list: [] })
                }
              }
            })
            .catch(() => {
              publicTable.value.initFailed()
            })
        },
        changeTable(index = 0) {
          if (!state.statisticalDataType[index].viewable) return
          switch (index) {
            case 0: //数据源
              state.interfaceName = 'getResourceLibraryDatasourceList'
              break
            default:
              state.interfaceName = 'getResourceLibraryDatasourceList'
              break
          }
          state.activeStyle = index
          state.tableHeadTitles = state.allTableHeadTitles[index]
          methods.initTable()
        },
        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          state.dialogTitle = state.statisticalDataType[state.activeStyle].title
          let activeTableHeadTitles = state.allTableHeadTitles[state.activeStyle]
          state.detailRuleForm = row
          state.detailData = {}
          activeTableHeadTitles.forEach((item) => {
            if (item.prop === 'number') return
            state.detailData[item.prop] = {
              key: item.prop,
              value: row[item.prop],
              label: item.name,
              type: item.name.includes('描述信息') ? 'textarea' : 'input',
            }
          })
          state.dialogVisible = true
        },

        // 更新表格
        updatedTable(data) {
          // 序号
          data.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          let _data = {
            total: '',
            list: data,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          nextTick(() => {
            publicTable.value.initTableData(_data)
          })
        },
      }
      onMounted(() => {
        state.tableHeadTitles = state.allTableHeadTitles[state.activeStyle]
        methods.getStatistics()
        methods.changeTable()
      })

      return {
        state,
        publicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .resource-library-page {
    background-color: #f2f3f5;
    padding: 10px;
    height: 100%;

    &-top {
      padding: 0 20px;
      background-color: #fff;
      margin-bottom: 10px;
      border-radius: 4px;
    }

    .statistics-table {
      border-radius: 4px;
      padding: 0 20px;
      background-color: #fff;
      height: calc(100% - 213px);

      .top-left {
        display: flex;
        align-items: center;
      }

      :deep(.el-pagination) {
        background-color: #fff;
      }
    }

    .need_smallcube__title {
      display: block;
      height: 16px;
      line-height: 18px;
      border-left: 4px solid var(--themeBlue);
      padding-left: 6px;
      font-size: 16px;
      color: #333333;
      font-weight: bolder;
    }

    .top-change-project {
      height: 78px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dcdcdc;

      .top-change-project-left {
        display: flex;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: bolder;
          color: #333333;
          line-height: 24px;
        }
      }
    }

    .mid-statistics {
      display: flex;
      align-items: flex-start;
      padding: 18px 0 4px;

      .statistics-box-left {
        padding: 10px 20px 0 0;
      }

      .statistics-box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding: 0 20px 0 0;

        .statistics-list {
          display: flex;
          align-content: center;
          justify-content: center;
          width: 25%;

          img {
            width: 106px;
            height: 106px;
          }

          &-des {
            display: flex;
            align-items: center;
          }

          &-des-box {
            color: #333;
            font-size: 12px;

            &.can-click {
              cursor: pointer;
            }

            .top {
              display: flex;
              align-items: center;
              line-height: 20px;
            }

            .number {
              color: #333333;
              font-size: 24px;
              font-weight: bolder;
              padding: 0 5px;
            }

            .unit {
              color: #666;
            }

            .yy-icon {
              width: 20px;
              color: #c9c9cb;
              margin-left: 10px;
            }

            &.active {
              .yy-icon {
                color: $themeFontColor;
              }
            }
          }
        }

        span {
          color: #25bfff;
        }
      }
    }

    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
    }

    .edit-box {
      .del-button {
        &:first-child {
          border-left: 0 !important;
        }

        &:last-child {
          padding-right: 0;
        }
      }
    }
  }
</style>
