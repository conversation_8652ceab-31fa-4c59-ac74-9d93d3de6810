import request from '@/utils/request'
import URL from '@/const/urlAddress'
// 离线作业目录树
function getOfflineJobTree(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/tree-list`,
    method: 'post',
    loading: true,
    data,
  })
}
// 创建目录
function createDirectory(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/create`,
    method: 'post',
    loading: true,
    data,
  })
}
// 创建业务流程
function createWorkflow(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/create-workflow`,
    method: 'post',
    loading: true,
    data,
  })
}
// 删除目录
function deleteDirectory(directoryId) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/delete/${directoryId}`,
    method: 'post',
    loading: true,
  })
}

// 移动目录
function moveDirectory(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/move`,
    method: 'post',
    loading: true,
    data,
  })
}

// 重命名目录
function renameDirectory(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/rename`,
    method: 'post',
    loading: true,
    data,
  })
}

// 离线作业下拉列表
function getOfflineJobList() {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/list`,
    method: 'get',
    loading: true,
  })
}

// 离线作业任务类型列表
function getOfflineJobTaskTypeList(params) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/type/list`,
    method: 'get',
    loading: true,
    params,
  })
}
// 创建离线作业
function createJob(data) {
  return request({
    url: `${URL.OFFLINEJOB}/nav-directory/create-job`,
    method: 'post',
    loading: true,
    data,
  })
}

// 获取离线作业生成的所有模型
function getModelList(params) {
  return request({
    url: `${URL.DEV}/offlinework/model/list`,
    method: 'get',
    loading: true,
    params,
  })
}
function getModelListV2(data) {
  return request({
    url: `${URL.DEV}/offlinework/model/list/v2`,
    method: 'post',
    loading: true,
    data,
  })
}

// 离线作业任务保存
function saveJob(data) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/save`,
    method: 'post',
    loading: true,
    data,
  })
}
// 查询数据源列表
function getDataSourceList(params) {
  return request({
    url: `${URL.BASE}/datasource/list`,
    method: 'get',
    loading: true,
    params,
  })
}

// 数据上传任务保存
function saveUploadJob(data) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/save`,
    method: 'post',
    loading: true,
    data,
  })
}

// 业务流程任务信息
function getOfflineJobTaskDetail(params) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/detail`,
    method: 'get',
    loading: true,
    params,
  })
}

// 业务流程任务版本最新版
function getTaskVersionLatest(params) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/version/latest`,
    method: 'get',
    loading: true,
    params,
  })
}
// 创建HIVE表DDL
function createHiveTableDDL(data) {
  return request({
    url: `${URL.DEV}/offlinework/hive-table/ddl`,
    method: 'post',
    loading: true,
    data,
  })
}

// 业务流程任务提交
function commitJob(data) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/commit`,
    method: 'post',
    loading: true,
    data,
  })
}
// 业务流程任务数据同步转DDL
function syncToDDL(data) {
  return request({
    url: `${URL.DEV}/offlinework/data-async/ddl`,
    method: 'post',
    loading: true,
    data,
  })
}

// 数据同步-预览表
function dataSyncPreviewTable(params) {
  return request({
    url: `${URL.DEV}/govern-data-model/model/fields?`,
    method: 'get',
    loading: true,
    params,
  })
}

// 数据同步-智能字段生成
function convertField(data) {
  return request({
    url: `${URL.MODEL}/field/convert`,
    method: 'post',
    loading: true,
    data,
  })
}

// 数据同步使用的数据源列表
function getAsyncDataSourceList(params) {
  return request({
    url: `${URL.DEV}/offlinework/biz/process/task/data/async/datasource`,
    method: 'get',
    loading: true,
    params,
  })
}

// 业务流程任务数据同步重名提示
function getAsyncDataSyncRename(data) {
  return request({
    url: `${URL.DEV}/offlinework/data-async/rename/alert`,
    method: 'post',
    loading: true,
    data,
  })
}
export default {
  getOfflineJobTree,
  createDirectory,
  createWorkflow,
  deleteDirectory,
  moveDirectory,
  renameDirectory,
  getOfflineJobList,
  getOfflineJobTaskTypeList,
  createJob,
  getModelList,
  getModelListV2,
  saveJob,
  getDataSourceList,
  saveUploadJob,
  getOfflineJobTaskDetail,
  getTaskVersionLatest,
  createHiveTableDDL,
  commitJob,
  syncToDDL,
  dataSyncPreviewTable,
  convertField,
  getAsyncDataSourceList,
  getAsyncDataSyncRename,
}
