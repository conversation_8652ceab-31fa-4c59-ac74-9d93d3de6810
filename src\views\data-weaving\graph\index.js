import { Graph, Addon, Shape } from '@antv/x6'
const { Dnd } = Addon
import insertCss from 'insert-css'
import './shape'
let graph = null
let Obj = null
insertCss(`
  @keyframes running-line {
    to {
      stroke-dashoffset: -1000;
    }
  }
`)
export default class FlowGraph {
  static init(id) {
    this.selectdNode = []
    graph = new Graph({
      container: document.getElementById(id),

      snapline: {
        enabled: true,
        clean: false,
      },
      grid: {
        size: 10,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#eee', // 主网格线颜色
            thickness: 1, // 主网格线宽度
          },
          {
            color: '#ddd', // 次网格线颜色
            thickness: 1, // 次网格线宽度
            factor: 4, // 主次网格线间隔
          },
        ],
      },
      scroller: {
        enabled: true, // 超出是否滚动条
        pannable: true, // 是否启用画布平移能力（在空白位置按下鼠标后拖动平移画布）
        pageVisible: false, // 是否分页
        pageBreak: false, // 是否显示分页符
        autoResize: true, // 是否自动扩充/缩小画布，默认为 true。 内容自适应 配置
      },
      interacting: {
        nodeMovable: true,
      },
      minimap: {
        enabled: true,
        container: document.getElementById('minimapContainer'),
        width: 249,
        height: 139,
        padding: 5,
        minScale: 0.5,
        maxScale: 1.6,
        scalable: false,
      },
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
        minScale: 0.5,
        maxScale: 2,
      },
      history: {
        enabled: true,
        ignoreAdd: false,
        ignoreRemove: false,
        ignoreChange: false,
        beforeAddCommand(event, args) {
          let val = true
          // 添加工具
          if (args.key === 'tools') {
            val = false
          }
          return val
        },
      },
      selecting: {
        enabled: true,
        multiple: true,
        rubberband: true,
        movable: true,
        modifiers: 'ctrl',
        showNodeSelectionBox: true,
        className: 'my-selecting',
      },
      connecting: {
        allowNode: false,
        allowLoop: false,
        allowMulti: true,
        anchor: 'center',
        allowBlank: false,
        snap: {
          radius: 150,
        },
        attrs: {
          line: {
            fill: 'none',
            stroke: '#3c4260',
            strokeWidth: 2,
          },
        },
        router: {
          // name: 'manhattan',
          name: 'er',
          args: {
            direction: 'H',
          },
        },
        connector: {
          name: 'rounded',
          args: {
            radius: 10,
          },
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#333',
                strokeWidth: 1,
              },
            },
          })
        },
      },
    })
    this.initEvent(id)
    this.initStencil()
    return { graph: graph, dnd: this.dnd, selectdNode: this.selectdNode }
  }
  static initStencil() {
    this.dnd = new Dnd({
      target: graph,
      scaled: false,
      animation: true,
      validateNode(droppingNode, options) {
        console.log('validateNode', droppingNode, options)
      },
    })
  }

  static initEvent() {
    graph.on('node:selected', ({ cell, node }) => {
      this.selectdNode.push(node)
    })
    graph.on('node:unselected', ({ cell, node }) => {
      let index = this.selectdNode.findIndex((item) => item.id === node.id)
      this.selectdNode.splice(index, 1)
    })
    graph.on('edge:mouseenter', ({ cell }) => {
      cell.addTools([
        {
          name: 'button-remove',
          args: { distance: -40 }, // 距离边的距离
        },
      ])
    })

    graph.on('edge:mouseleave', ({ cell }) => {
      cell.removeTools()
    })
  }

  // 销毁
  static destroy() {
    graph.dispose()
  }
}
