$cf-border-radius: 2px;
$cf-padding-10-16: 10px 16px;
$cf-padding-8-16: 8px 16px;
$cf-color-primary: #1e89ff;
$cf-gap8: 8px;
$cf-color-black: #1d2129;

:root {
  .cf-tools {
    border-radius: $cf-border-radius;
    background: var(--100, #fff);
    display: flex;
    padding: 10px 8px 10px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;

    .row {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;

      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .nancalui-input,
      .nancalui-select {
        width: 240px;
        margin-right: 32px;
      }

      .search {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: $cf-gap8;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          border-radius: $cf-border-radius;
          background-color: $cf-color-primary;
          cursor: pointer;
          height: 32px;

          &:last-of-type {
            margin-right: 0;
          }

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dcdfe6;
            color: $cf-color-black;
            background-color: transparent;
            border-radius: 2px;

            &:hover {
              background-color: transparent;
              border: 1px solid #479dff;
              color: #479dff;
            }

            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }

          &:hover {
            background-color: #479dff;
          }
        }
      }
    }
  }

  .cf-tree {
    border-radius: $cf-border-radius;
    background: var(--100, #fff);
    height: 100%;
    max-height: 100%;
    display: flex;
    width: 286px;
    padding: $cf-padding-8-16;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    gap: 4px;
  }

  // 文字样式
  .text-label {
    color: #1d2129;
    font-family: 'Source Han Sans CN';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    /* 157.143% */
  }
  .cf-page-title {
    position: relative;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    height: 46px;
    padding: 0 16px;
    color: #1d2129;
    font-weight: bolder;
    font-size: 16px;
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 2px;
    &.cf-no-bottom-margin {
      margin-bottom: 0;
    }
    &[sub-label]::after {
      margin-left: 12px;
      content: attr(sub-label);
      display: inline-block;
      padding: 0px 4px;
      border-radius: 2px;
      line-height: 22px;
      border: 1px solid rgba(26, 164, 238, 0.4);
      background: rgba(26, 164, 238, 0.08);
      color: #1aa4ee;
      font-family: 'Source Han Sans CN';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: $cf-color-primary;
      content: '';
    }
    .detail-back-box {
      position: absolute;
      top: 0;
      right: 16px;
      bottom: 0;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 30px;
      margin: auto;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      color: #1d2129;
      font-weight: normal;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        color: #479dff;
        border: 1px solid #479dff;
      }
    }
  }
}
.class-list-tree-ipt .nancalui-input__inner {
  border-right: 1px solid #e5e6eb;
}
