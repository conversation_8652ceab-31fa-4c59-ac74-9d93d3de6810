<template>
  <div v-loading="state.loading" class="full-page container">
    <div class="api-management-detail">
      <div class="detail-content">
        <div v-show="state.activeName === 'detailApi'" class="detail-content-show-box">
          <div class="detail-top-box dif">
            <div class="page_header_common_style">
              <span class="need_smallcube__title">查看</span>
              <div class="top-box-right">
                <div v-if="state.state === 'DRAFT'" class="blue">已创建</div>
                <div v-if="state.state === 'WAIT'" class="yellow">审核中</div>
                <div v-if="state.state === 'OFF_WAIT'" class="yellow">待下架</div>
                <div v-if="state.state === 'OFF'" class="grey">已下架</div>
              </div>
            </div>
          </div>
          <div class="detail-api-content">
            <detailApi ref="detailApi" />
          </div>
        </div>
        <div v-if="state.activeName === 'testApi'" class="detail-content-show-box">
          <div class="detail-top-box">
            <div class="top-box-left">
              <span class="need_smallcube__title">测试查询</span>
            </div>
            <div class="top-box-right"></div>
          </div>
          <div class="test-api-content">
            <testApi ref="testApi" :key="state.key" />
          </div>
        </div>
      </div>
    </div>
    <div class="options-box-bg">
      <div v-if="state.activeName === 'detailApi'" class="content">
        <n-button size="sm" color="primary" variant="solid" @click.prevent="goPrevPage"
          >返回</n-button
        >
      </div>
      <div v-else class="content">
        <n-button size="sm" color="primary" variant="solid" @click.prevent="goPrevPage"
          >返回</n-button
        >
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import detailApi from './components/detail-api'
  import testApi from './components/test-api'

  export default {
    title: '',
    components: { detailApi, testApi },
    props: {},
    setup() {
      const router = useRouter()
      const detailApi = ref()
      const testApi = ref()
      const state = reactive({
        activeName: 'detailApi',
        currentRef: null,
        state: null,
        detailId: '',
        type: '',
        alldata: {},
        serverUrl: '',
        key: '',
        loading: false,
      })

      const methods = {
        // 获取API详情
        getDetail(id) {
          state.loading = true
          api.dataService
            .getApiDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                if (data.paramColumns.length) {
                  data.paramColumns.map((item, index) => {
                    return Object.assign(item, { number: index + 1 })
                  })
                }

                state.alldata = data
                state.state = data.auditStatus
                // data['showUrl'] = data.protocol.toLowerCase() + '://' + data.serverUrl + data.apiUrl // 全路径
                data['showUrl'] = data.serverUrl + data.apiUrl // 全路径
                state.currentRef.init(data)
              } else {
                state.loading = false
                state.currentRef.init()
              }
            })
            .catch(() => {
              state.currentRef.init()
            })
        },

        // 返回
        goBack() {
          if (state.activeName === 'testApi') {
            state.activeName = 'detailApi'
            nextTick(() => {
              state.currentRef = detailApi.value
            })
          } else {
            router.go(-1)
          }
        },
        // 取消
        goPrevPage() {
          router.go(-1)
        },
        // 测试
        goTest() {
          state.activeName = 'testApi'
          nextTick(() => {
            state.currentRef = testApi.value
            methods.getDetail(state.detailId)
          })
        },
      }

      onMounted(() => {
        state.detailId = router.currentRoute.value.query.id
        state.type = router.currentRoute.value.query.type
        switch (state.type) {
          case 'DETAIL':
            state.currentRef = detailApi.value
            methods.getDetail(state.detailId)
            break
          case 'TESTAPI': //测试页面
            state.currentRef = testApi.value
            // methods.getDetail(state.detailId)
            methods.goTest()
            break
          default:
            break
        }
      })

      return {
        state,
        detailApi,
        testApi,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .full-page {
    .options-box-bg {
      height: 60px;
      margin-top: 10px;
      margin-left: -10px;
      margin-right: -10px;
      border-radius: 8px 8px 0 0;
      background-color: #fff;
      .content {
        width: 100%;
        height: 100%;
        padding: 0 20px;

        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .api-management-detail {
      height: calc(100% - 60px);
      position: relative;
      .detail-content {
        // padding: 0 10px 10px;
        background-color: #fff;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;
        .detail-content-show-box {
          height: 100%;
          .detail-api-content {
            height: calc(100% - 38px);
          }
          .test-api-content {
            height: calc(100% - 38px);
          }
        }
        .detail-top-box {
          width: 100%;
          padding-top: 16px;
          &.dif {
            padding-top: 0;
          }
          // width: calc(100% - 4px);
          // margin: 2px auto;
          // padding: 9px 30px;
          // height: 38px;
          // background: #f7f8fa;
          // border-radius: 3px 3px 0px 0px;

          .page_header_common_style {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 30px;
          }
          .top-box-left {
            span {
              font-weight: bolder;
              margin: 0 0 0 20px;
            }
          }
          .top-box-right {
            div {
              height: 20px;
              line-height: 20px;
              padding: 0 9px;
              box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.5);
              border-radius: 10px 2px 10px 2px;
              color: #fff;
              font-size: 14px;
            }
            .grey {
              background-color: #e1e1e1;
              color: #999999;
            }
            .blue {
              background-color: $themeBlue;
            }
            .yellow {
              background-color: #f5a623;
            }
          }
        }
      }

      .content-bg-img {
        position: absolute;
        bottom: 76px;
        right: 0;
        width: 252px;
      }
    }
  }
</style>
