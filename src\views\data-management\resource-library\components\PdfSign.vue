<template>
  <n-drawer
    v-model="state.showAddDrawer"
    :close-on-click-overlay="false"
    :size="550"
    :before-close="
      () => {
        return false
      }
    "
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <SvgIcon class="icon" icon="icon-drawer-title" />
          <div class="title">PDF签名</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.prevent="state.showAddDrawer = false" />
      </div>

      <div
        class="n-drawer-body-content has-top-padding scroll-bar-style"
        style="height: calc(100% - 60px)"
        v-loading="loading"
      >
        <n-button-group>
          <n-button
            class="tabs"
            v-for="item in state.tabs"
            :title="item"
            :key="item"
            :color="state.tabsActive === item ? 'primary' : 'secondary'"
            @click="state.tabsActive = item"
            >{{ item }}</n-button
          >
        </n-button-group>

        <!-- 图片上传 -->
        <div v-if="state.tabsActive === '图片'" class="imgUpload">
          <div class="box">
            <input v-show="!state.imageUrl" type="file" @change="handleFileUpload" />
            <img v-show="state.imageUrl" :src="state.imageUrl" />
            <div v-show="!state.imageUrl" class="tips">
              <SvgIcon class="icon" icon="icon-upload-add" />
              <span>Upload</span>
            </div>
            <div v-show="state.imageUrl" class="mask">
              <n-tooltip content="删除" position="top">
                <SvgIcon class="icon" icon="icon-delete-sign" @click="state.imageUrl = ''" />
              </n-tooltip>
            </div>
          </div>
        </div>

        <!-- 手动输入 -->
        <div v-else-if="state.tabsActive === '输入'" class="manualInput">
          <div class="box">
            <div ref="inputRef" class="inputBox">
              <input v-model="state.manualInput" placeholder="请输入" />
            </div>
          </div>
          <n-button color="primary" @click="state.manualInput = ''"
            ><SvgIcon class="icon" icon="icon-clear-sign" />清除</n-button
          >
        </div>

        <!-- 手写 -->
        <div v-show="state.tabsActive === '手写'" class="manualWrite">
          <div class="box">
            <canvas
              @mousemove="canvasMove"
              @mouseup="canvasUp"
              ref="canvas"
              width="518"
              height="300"
              @mousedown="mousedown"
              @mouseleave="canvasLeave"
            ></canvas>
          </div>
          <n-button color="primary" @click="handleClear"
            ><SvgIcon class="icon" icon="icon-clear-sign" />清除</n-button
          >
        </div>
      </div>
      <div class="n-drawer-body-footer">
        <n-button @click.prevent="state.showAddDrawer = false">取消</n-button>
        <n-button variant="solid" @click.prevent="saveSign">确认</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted, watch } from 'vue'
  import { waterUpload } from '@/api/dataManage'
  import html2canvas from 'html2canvas'
  import { base64toFile } from '@/utils/index.js'
  import api from '@/api/index'
  const emits = defineEmits(['saveSign'])

  const isDown = ref(false) // 是否可以绘制
  const canvas = ref(null) // canvas 元素
  const ctx = ref(null) // ctx承接上下文
  const inputRef = ref(null)
  const loading = ref(false)
  const fileImg = ref(null)

  /**
   * 数据部分
   */
  const state = reactive({
    showAddDrawer: false,
    tabsActive: '图片',
    tabs: ['图片', '输入', '手写'],
    imageUrl: '',
    manualInput: '',
  })
  provide('signType', state.tabsActive)

  onMounted(() => {})

  // 鼠标按下
  const mousedown = (e) => {
    let { offsetX, offsetY } = e // 拿到x，y坐标
    ctx.value = canvas.value.getContext('2d') // 上下文
    //   console.log(ctx.value)
    ctx.value.strokeStyle = '#000' // 设置线条颜色
    ctx.value.fillStyle = '#fff'
    ctx.value.lineWidth = 5 // 线条宽度
    ctx.value.beginPath() // 开始绘制路径
    ctx.value.moveTo(offsetX, offsetY) // 将鼠标移动到某一个坐标，准备绘制
    isDown.value = true // 打开绘制开关
  }
  // 鼠标移动
  const canvasMove = (e) => {
    let { offsetX, offsetY } = e // 拿到移动时的坐标信息
    isDown.value && startDraw(offsetX, offsetY) // 绘制开关为true 开始绘制
  }
  // 鼠标松开
  const canvasUp = () => {
    isDown.value = false
  }
  // 鼠标超出canvas绘制区域
  const canvasLeave = () => {
    isDown.value = false
  }
  // 绘制方法
  const startDraw = (x, y) => {
    ctx.value.lineTo(x, y) // 绘制线条
    ctx.value.stroke() //
  }
  const handleClear = () => {
    ctx.value.clearRect(0, 0, 1000, 500)
  }

  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      loading.value = true
      const validFormats = ['image/png', 'image/jpg', 'image/jpeg']
      if (validFormats.includes(file.type)) {
        const reader = new FileReader()
        reader.onload = (e) => {
          state.imageUrl = e.target.result
        }
        reader.readAsDataURL(file)
        fileImg.value = file
        loading.value = false
      } else {
        loading.value = false
        alert('只允许上传 png、jpg、jpeg 格式的图片！')
      }
    }
  }

  // 保存签名
  const saveSign = () => {
    loading.value = true
    if (state.tabsActive !== '图片') {
      // 转换为图片数据的 URL
      let dom = null
      if (state.tabsActive === '手写') {
        dom = canvas.value
        const snapshot = dom.toDataURL('image/png', 0.1) // 是图片质量
        const file = base64toFile(snapshot)
        waterUploadFn(file)
        return false
      } else {
        dom = inputRef.value
      }

      html2canvas(dom, {
        backgroundColor: 'transparent', // 设置背景颜色为透明
        allowTaint: true,
        useCORS: true,
        width: 500,
        height: 284,
      }).then((canvas) => {
        const snapshot = canvas.toDataURL('image/png', 0.1) // 是图片质量
        const file = base64toFile(snapshot)
        waterUploadFn(file)
      })
    } else {
      waterUploadFn(fileImg.value)
    }
  }

  const waterUploadFn = async (file) => {
    const formData = new FormData()
    formData.append('bucket', 'data-govern')
    formData.append('file', file)

    waterUpload(formData)
      .then((res) => {
        state.showAddDrawer = false
        loading.value = false
        state.imageUrl = res.data.webUrl
        emits('saveSign', res.data)

        ElMessage.success({
          message: '签名上传成功',
        })
      })
      .catch((error) => {
        loading.value = false
        console.error(error)
      })
  }

  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .upload-demo .upload-trigger {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 360px;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .upload-demo .upload-trigger .link {
    color: #447dfd;
  }

  .tabs {
    margin: 0 0 8px !important;

    &:nth-child(2) {
      border-left: none;
      border-right: none;
    }
  }

  .imgUpload {
    border: 1px dashed var(---, #c5d0ea);
    height: 300px;
    border-radius: 6px;
    border: 1px dashed var(---, #c5d0ea);
    background: var(--100, #fff);
    box-sizing: border-box;

    .box {
      position: relative;
      height: 100%;
      border: 9px solid #fff;
      overflow: hidden;
      border-radius: 6px;

      &:hover .mask {
        display: block;
      }
    }
    input {
      position: relative;
      z-index: 1;
      opacity: 0;
      width: 100%;
      height: 100%;
    }
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }
    .tips {
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: 0;
      transform: translate(-50%, -50%);
      text-align: center;
      font-size: 14px;

      .icon {
        font-size: 24px;
      }
      span {
        display: block;
      }
    }
    .mask {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
      z-index: 2;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 6px;

      .icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 16px;
        cursor: pointer;

        :hover {
          color: #6e9eff;
        }
      }
    }
  }

  .manualInput {
    .box {
      height: 300px;
      padding: 8px;
      border-radius: 6px;
      background: var(---, #f6f7fb);
    }
    .inputBox {
      position: relative;
      height: 100%;
      line-height: 280px;
    }
    input {
      width: 250px;
      position: absolute;
      top: 40%;
      left: 25%;
      border: none;
      outline: none;
      background: none;
      text-align: center;
      font-size: 18px;
      vertical-align: middle;
      transform: scale(2);
    }
    .nancalui-button {
      float: right;
      margin-top: 10px;

      .icon {
        margin-right: 4px;
      }
    }
  }

  .manualWrite {
    .box {
      display: flex;
      height: 300px;
      padding: 8px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-self: stretch;
      border-radius: 6px;
      background: var(---, #f6f7fb);
    }
    input {
      height: 100px;
      border: none;
      outline: none;
      background: none;
      width: 100%;
      text-align: center;
      font-size: 32px;
    }
    .nancalui-button {
      float: right;
      margin-top: 10px;

      .icon {
        margin-right: 4px;
      }
    }
  }

  :root .nancalui-drawer .n-drawer-body-content.has-top-padding {
    padding: 8px 16px 0 16px;
  }
  :root .nancalui-button--outline--primary:active,
  :root .nancalui-button--outline--secondary:active {
    color: #447dfd;
    background-color: #e6ecf8;
    border-color: #447dfd;
  }
  :root .nancalui-drawer .n-drawer-body-footer {
    position: absolute;
    bottom: 0;
    right: 0;
  }
</style>
