import { Vector2, Matrix3 } from 'three'
class ImageAnnotator {
    _lock = false; // 锁定状态
    readonly = false; // 只读状态
    // 缩放倍率限制
    scaleLimit = {
        min: 0.1, // 最小缩放倍率
        max: 10, // 最大缩放倍率 
    }
    // 创建类型
    createType = "drag"

    // 事件容器
    events = {};
    // 图片位置偏移量
    canvasOffset = new Vector2(0, 0);
    // 图片实例
    image = null;
    // 当前图片信息
    currentImgInfo = {
        x: 0, // 图片的宽度
        y: 0, // 图片的高度
    }
    // 图片在坐标系中的位置
    imagePosition = new Vector2(0, 0);
    // 矩阵相关对象
    matrixObj = {
        BASE: new Matrix3(), // 基础矩阵
        S: new Matrix3(), // 缩放矩阵 基础矩阵
        T: new Matrix3(), // 居中矩阵 基础矩阵
        S_T: new Matrix3(), // 复合矩阵 缩放=>平移
        CS: new Matrix3(), // 缩放矩阵 画布缩放
        CT: new Matrix3(), // 补偿矩阵 画布缩放补偿
        CT2: new Matrix3(), // 补偿矩阵 画布缩放补偿
        DT: new Matrix3(), // 拖拽矩阵 画布平移
        S2: new Matrix3(), // 缩放矩阵 缩放图片尺寸
        T2: new Matrix3(), // 补偿矩阵 图片居中
    }

    // 正在标注的数据
    currentAnnotation = {}

    fillStyle = 'rgba(255, 0, 0, 1)'; // 标注颜色
    strokeStyle = '#000'; // 标注边框颜色
    lineWidth = 2; // 标注边框宽度
    radius = 4; // 标注半径

    // {
    //     type: 'rect', // 标注类型 rect 圆形 circle
    //     color: 'red', // 标注颜色 
    //     "label": "circle",
    //     "coor": [], // 标注坐标
    //     "active": false,
    //     "creating": false,
    //     "dragging": false,
    //     "uuid": undefined,
    //     "labelFillStyle": "#f00",
    //     fillStyle: 'rgba(255, 0, 0, 0.5)',
    //     strokeStyle: 'red',
    //     "index": 0,
    //     "radius": 4
    // }
    // 标注完成的数据
    annotations = [];

    constructor(container, imageUrl, options = {}) {
        // 兼容
        switch (true) {
            case !container:
                throw new Error('container is required');
            case typeof container === 'string':
                container = document.querySelector(container);
            case !imageUrl:
                throw new Error('imageUrl is required');
            case typeof imageUrl !== 'string':
                throw new Error('imageUrl must be a string');
            case typeof options !== 'object':
                throw new Error('options must be an object');
        }
        // 事件容器
        this.container = container;
        this.options = options;
        this.emit = (key, ...args) => {
            if (!options[key]) return;
            // 获取形参
            options[key](...args);
        };
        // 创建canvas并挂载到容器
        this.createCanvas();
        // 初始化加载图片
        this.loadImg(imageUrl).then(this.mountEvents.bind(this));
        // 监听容器尺寸变化
        this.resizeObserver = new ResizeObserver(this.handleContainerResize.bind(this));
        this.resizeObserver.observe(this.container);
    }

    // 初始化矩阵
    initMatrix() {
        const { width: imgWidth, height: imgHeight } = this.image;
        // 根据容器尺寸计算canvas宽高
        const { width: containerWidth, height: containerHeight } = this.container.getBoundingClientRect();
        const { S2, T, T2 } = this.matrixObj;
        // 计算缩放比例
        const scale = containerWidth / imgWidth;
        S2.makeScale(scale, scale);
        // 重置画布中心点
        T.makeTranslation(containerWidth / 2, containerHeight / 2);
        this.matrixObj.BASE = new Matrix3();
        this.matrixObj.BASE.premultiply(T);

        // 图片位置补偿矩阵-居中
        T2.makeTranslation(-this.currentImgInfo.x / 2, -this.currentImgInfo.y / 2);
    }

    // 创建canvas并挂载到容器
    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.container.appendChild(this.canvas);
        this.ctx = this.canvas.getContext('2d');
        return this.ctx;
    }

    // 加载图片
    loadImg(url) {
        if (this.image === null || this.image === undefined) {
            this.image = new Image()
        }
        return new Promise((resolve, reject) => {
            this.image.crossOrigin = 'anonymous';
            this.image.src = url;
            this.image.onload = () => {
                const { width: imgWidth, height: imgHeight } = this.image;
                // 根据容器尺寸计算canvas宽高
                const { width: containerWidth, height: containerHeight } = this.container.getBoundingClientRect();
                // 加载完成后初始化矩阵
                this.initMatrix()
                // 创建标量
                this.currentImgInfo = new Vector2(imgWidth, imgHeight).applyMatrix3(this.matrixObj.S2)
                // 设置canvas尺寸
                this.canvas.width = containerWidth;
                this.canvas.height = containerHeight;
                // 绘制背景
                this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
                resolve(this.ctx);
                this.emit('load', this.image);
            };
            this.image.onerror = () => reject(new Error('图片加载失败'));
        });
    }

    // 加载完成后挂载事件
    mountEvents(ctx) {
        this.setDragMode();
        this.setScaleMode();
        this.mountSelectShapeEvent();
    }

    // 切换模式
    changeShape(type) {
        this.createType = type;
        // 注销事件
        this.offDragImageEvent?.();
        this.offPointClick?.()
        this.offPolygonEvent?.();

        switch (type) {
            case 'point':
                this.setPointMode();
                break;
            case 'drag':
                this.setDragMode();
                break;
            case 'polygon':
                this.setPolygonMode();
                break;
            default:
                throw new Error('type must be point or drag');
        }
    }

    // 绘制背景
    drawBackground({ width, height }) {
        const { BASE, T, T2 } = this.matrixObj;
        // 图片位置
        const { x, y } = this.imagePosition?.clone()
            .applyMatrix3(T2)
            .applyMatrix3(BASE)
        // 绘制背景
        this.ctx.drawImage(this.image, x, y, width * this.scale, height * this.scale);
    }

    // 多边形标注模式
    setPolygonMode() {
        this.currentAnnotation = undefined;
        this.mountPolygonEvent();
    }

    // 点标注模式
    setPointMode() {
        this.currentAnnotation = undefined;
        this.mountPointEvent();
    }

    // 拖拽模式
    setDragMode() {
        this.currentAnnotation = undefined;
        this.mountDragImageEvent();
    }
    // 缩放模式
    setScaleMode() {
        this.currentAnnotation = undefined;
        this.mountScaleCanvasEvent();
    }

    // 挂载选择图形事件
    mountSelectShapeEvent() {
        this.on('click', 'selectShape', this.selectShape.bind(this));
        // 设置注销选择图形事件方法
        this.offSelectShapeEvent = () => this.off('selectShape');
    }

    // 挂载多边形标注事件
    mountPolygonEvent() {
        this.on('click', 'polygonClick', this.polygonClick.bind(this));
        this.on('dblclick', 'polygonDblClick', this.polygonDblClick.bind(this));
        this.on('mousemove', 'polygonMove', this.polygonMove.bind(this));
        // 设置注销多边形标注事件方法
        this.offPolygonEvent = () => {
            this.off('polygonClick');
            this.off('polygonDblClick');
            this.off('polygonMove');
        }
    }
    // 挂载点标注事件
    mountPointEvent() {
        this.on('click', 'pointClick', this.pointClick.bind(this));
        // 设置注销点标注事件方法
        this.offPointClick = () => this.off('pointClick')
    }
    // 挂载拖动图片位置事件
    mountDragImageEvent() {
        this.on('mousedown', 'dragImgDown', this.dragImgDown.bind(this));
        this.on('mousemove', 'dragImgMove', this.dragImgMove.bind(this));
        this.on('mouseup', 'dragImgUp', this.dragImgUp.bind(this));
        // 设置注销拖动图片位置事件方法
        this.offDragImageEvent = () => {
            this.off('dragImgDown');
            this.off('dragImgMove');
            this.off('dragImgUp');
        }
    }

    /* 事件相关 */

    // 选择图形事件
    selectShape(e) {
        if (this.annotations.length === 0) return;
        const { offsetX, offsetY } = e;
        // 计算点在画布中的位置
        const point = this.screenToImage(new Vector2(offsetX, offsetY));
        this.ctx.globalCompositeOperation = "source-over";
        const annotation = this.annotations.find((annotation) => {
            const { type, coor, radius } = annotation;
            switch (type) {
                case 'rect':
                    return this.isPointInRect(point, coor);
                case 'point':
                    return this.isPointInPoint(point, coor[0], coor[0][2]);
                case 'polygon':
                    return this.isPointInPolygon(point, coor);
                default:
                    return false;
            }

        })
        // 记录选中的标注
        if (annotation) {
            this.selectAnnotation = annotation
            this.emit('select', annotation);
        }
    }

    // 绘制多边形-按下鼠标
    polygonClick(e) {
        // 开始绘制
        this.isDrawing = true;
        const { offsetX, offsetY } = e;
        const point = new Vector2(offsetX, offsetY);
        this.ctx.globalCompositeOperation = "destination-over";
        // 计算点在画布中的位置
        const coor = this.screenToImage(point)
        this.currentAnnotation && this.currentAnnotation.coor.push([coor.x, coor.y])
        !this.currentAnnotation && (
            this.currentAnnotation = {
                type: 'polygon',
                color: 'red',
                label: 'polygon',
                coor: [[coor.x, coor.y]],
                active: true,
                uuid: this.uuid(),
                labelFillStyle: this.fillStyle,
                fillStyle: this.fillStyle,
                strokeStyle: this.strokeStyle,
                index: this.annotations.length,
                radius: this.radius,
            })
        this.clear();
        this.drawPolygon(this.currentAnnotation);
        this.drawAnnotations(this.annotations);
        // 绘制已完成标注
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
    }
    // 绘制多边形-双击鼠标
    polygonDblClick(e) {
        // 结束绘制
        this.isDrawing = false;
        // 删除最后一个点
        this.currentAnnotation.coor.pop();
        this.annotations.push(this.currentAnnotation);
        this.ctx.globalCompositeOperation = "destination-over";
        this.clear();
        // 绘制已完成标注
        this.drawAnnotations(this.annotations);
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
        this.emit('add', this.currentAnnotation);
        this.currentAnnotation = undefined;
    }
    // 绘制多边形-移动鼠标
    polygonMove(e) {
        if (!this.isDrawing) return;
        const { offsetX, offsetY } = e;
        const point = new Vector2(offsetX, offsetY);
        this.ctx.globalCompositeOperation = "destination-over";
        // 计算点在画布中的位置
        const coor = this.screenToImage(point)
        this.clear();
        this.drawPolygon({ ...this.currentAnnotation, coor: [...this.currentAnnotation.coor, [coor.x, coor.y]] });
        this.drawAnnotations(this.annotations);
        // 绘制已完成标注
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
    }

    // 绘制点-点击鼠标
    pointClick(e) {
        const { offsetX, offsetY } = e;
        const point = new Vector2(offsetX, offsetY);
        this.ctx.globalCompositeOperation = "source-over";
        // 计算点在画布中的位置
        this.drawPoint(point, this.radius, this.fillStyle);
        const coor = this.screenToImage(point)
        this.currentAnnotation = {
            type: 'point',
            color: 'red',
            label: 'point',
            coor: [[coor.x, coor.y, this.radius]],
            active: true,
            uuid: this.uuid(),
            labelFillStyle: this.fillStyle,
            fillStyle: this.fillStyle,
            strokeStyle: this.strokeStyle,
            index: this.annotations.length,
        }
        this.annotations.push(this.currentAnnotation);
        const returnData = JSON.parse(JSON.stringify(this.currentAnnotation));
        this.emit('add', returnData);
        this.currentAnnotation = undefined;
    }

    // 拖动图片事件-按下鼠标
    dragImgDown(e) {
        // 开始拖动
        this.isDrag = true;
        const { offsetX, offsetY } = e;
        // 记录起始点
        this.endPoint = new Vector2(offsetX, offsetY);
        // 修改鼠标样式
        this.ctx.canvas.style.cursor = 'move';
        this.emit('dragStart', e);
    }

    // 拖动图片事件-移动鼠标
    dragImgMove(e) {
        if (!this.isDrag) return;
        this.ctx.canvas.style.cursor = "move";
        const { offsetX, offsetY } = e;
        const { BASE, T, DT } = this.matrixObj;

        // 计算偏移量
        const offset = new Vector2().subVectors(new Vector2(offsetX, offsetY), this.endPoint);
        this.endPoint = new Vector2(offsetX, offsetY);
        // 计算平移矩阵
        DT.makeTranslation(offset.x, offset.y);
        BASE.premultiply(DT)
        // 记录偏移量
        this.canvasOffset.add(offset);
        this.ctx.globalCompositeOperation = "destination-over";
        // 清空画布
        this.clear();
        // 绘制标注
        this.drawAnnotations(this.annotations);
        // 绘制背景
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
        this.emit('dragIng', e, this.canvasOffset);
    }

    // 拖动图片事件-抬起鼠标
    dragImgUp(e) {
        this.emit('dragEnd', e, this.canvasOffset);
        this.ctx.canvas.style.cursor = "default";
        this.isDrag = false;
    }

    // 挂载缩放画布事件
    mountScaleCanvasEvent() {
        this.on('wheel', 'scaleCanvas', this.scaleCanvas.bind(this));
    }
    scale = 1
    // 缩放画布事件-鼠标滚轮
    scaleCanvas(e) {
        e.preventDefault();
        const { offsetX, offsetY, deltaY } = e;
        const { BASE, T, S_T, CS, CT, CT2, DT, S2 } = this.matrixObj;
        const { min, max } = this.scaleLimit
        // 计算缩放系数
        const zoom = 1 + (deltaY < 0 ? 0.02 : -0.02)
        this.scale = Math.max(min, Math.min(max, this.scale * zoom))
        if (this.scale === min || this.scale === max) return;
        // 计算缩放矩阵
        CS.makeScale(zoom, zoom);
        CT.makeTranslation(-offsetX, -offsetY)
        CT2.makeTranslation(offsetX, offsetY)
        BASE.premultiply(CT).premultiply(CS).premultiply(CT2)
        this.ctx.globalCompositeOperation = "destination-over";
        // 清空画布
        this.clear();
        // 绘制标注
        this.drawAnnotations(this.annotations);
        // 绘制背景
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
        this.emit('scale', e, this.scale);
    }

    // 处理容器尺寸变化
    handleContainerResize(entries) {
        // 重新设置canvas尺寸
        const { width: containerWidth, height: containerHeight } = this.container.getBoundingClientRect();
        this.canvas.width = containerWidth;
        this.canvas.height = containerHeight;
        // 获取S2的缩放比例
        const scale = this.matrixObj.S2.elements[0];
        // 初始化矩阵
        this.initMatrix();
        const { BASE, T, DT, S2, T2 } = this.matrixObj;
        // 获取新的S2的缩放比例
        const newScale = S2.elements[0];
        // 计算缩放比例
        const zoom = newScale / scale;
        // 等比例缩放
        const { width: imgWidth, height: imgHeight } = this.image;
        this.currentImgInfo = new Vector2(imgWidth, imgHeight).applyMatrix3(S2)
        // 重新计算图片居中矩阵
        T2.makeTranslation(-this.currentImgInfo.x / 2, -this.currentImgInfo.y / 2);
        // 等比例缩放
        this.canvasOffset.multiplyScalar(zoom);
        DT.makeTranslation(this.canvasOffset.x, this.canvasOffset.y);
        BASE.premultiply(DT)
        // 清空画布
        this.clear();
        // 绘制背景
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
    }

    // 注册事件
    on(type, key, fn) {
        if (this.events[key]) {
            console.warn(`事件${key}已存在`);
            return;
        }
        this.events[key] = [fn];
        // 往第一个参数添加type
        type && this.events[key].unshift(type);
        type && this.ctx.canvas.addEventListener(type, fn);
    }

    // 根据传入的key获取事件的引用
    getEvent(key) {
        return this.events[key];
    }

    // 移除事件
    off(key) {
        const event = this.getEvent(key);
        if (!event) return;
        const [type, fn] = event;
        fn && this.ctx.canvas.removeEventListener(type, fn);
        delete this.events[key];
    }

    /* 画布相关操作 */
    // 绘制已标注数据
    drawAnnotations(annotations) {
        annotations.forEach((annotation) => {
            const { type, coor, fillStyle, strokeStyle, lineWidth, radius } = annotation;
            switch (type) {
                case 'point':
                    this.drawPoint(this.imageToScreen(...coor), radius, fillStyle);
                    break;
                case 'polygon':
                    this.drawPolygon(annotation, true);
                    break;
            }
        })
    }

    // 绘制多边形
    drawPolygon(annotation, isClose = false) {
        const { coor, radius, fillStyle, strokeStyle } = annotation;
        coor.forEach((point, index) => {
            this.drawPointWithBorder(this.imageToScreen(point), radius);
        })
        this.drawPolygonFill(coor.map((point) => this.imageToScreen(point)), undefined, isClose);
    }

    // 绘制填充区域
    drawPolygonFill(points, fillStyle = 'rgba(255, 0, 0, 0.2)', isClose = false) {
        this.ctx.beginPath();
        this.ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            this.ctx.lineTo(points[i].x, points[i].y);

        }
        isClose && this.ctx.closePath();
        this.ctx.fillStyle = fillStyle;
        this.ctx.fill();
        this.ctx.stroke();
    }

    // 绘制带边框的点
    drawPointWithBorder(point, radius = 5, fillStyle = '#fff', strokeStyle = '#000') {
        this.ctx.beginPath();
        this.ctx.fillStyle = fillStyle;
        this.ctx.strokeStyle = strokeStyle;
        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        this.ctx.stroke();
    }

    // 绘制标注点
    drawPoint(point, radius = 5, color = 'red') {
        this.ctx.beginPath();
        this.ctx.fillStyle = color;
        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // 清空画布
    clear() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // 销毁
    destroy() {
        this.ctx.canvas.remove();
        this.resizeObserver.disconnect();
        this.events = {};
        this.image = null;
        this.ctx = null;
        this.canvas = null;
        this.resizeObserver = null;
    }

    // 其他方法
    uuid() {
        let res = ''
        const template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'
        for (let i = 0, len = template.length; i < len; i += 1) {
            const s = template[i]
            const r = (Math.random() * 16) | 0
            const v = s === 'x' ? r : s === 'y' ? (r & 0x3) | 0x8 : s
            res += v.toString(16)
        }
        return res
    }

    /* 坐标转换 */
    // 屏幕坐标转图片实际坐标
    screenToImage(screenPoint) {
        // 判断screenPoint类型是否为Vector2
        if (!(screenPoint instanceof Vector2)) {
            screenPoint = new Vector2(screenPoint.x, screenPoint.y)
        }
        const { BASE, S2 } = this.matrixObj;
        // BASE逆矩阵
        const BASE_INV = BASE.clone().invert(BASE);
        // S2逆矩阵
        const S2_INV = S2.clone().invert(S2);
        // 计算点在画布中的位置)
        screenPoint.applyMatrix3(BASE_INV).applyMatrix3(S2_INV)
        const imagePoint = screenPoint.clone()
            .add(new Vector2(this.image.width / 2, this.image.height / 2))
        return imagePoint
    }

    // 图片实际坐标转屏幕坐标
    imageToScreen(imagePoint) {
        // 判断imagePoint类型是否为Vector2
        if (!(imagePoint instanceof Vector2)) {
            imagePoint = new Vector2(imagePoint[0], imagePoint[1])
        }
        const { BASE, T2, S2 } = this.matrixObj;
        const screenPoint = imagePoint.clone()
            .applyMatrix3(S2)
            .applyMatrix3(T2)
            .applyMatrix3(BASE)
        return screenPoint
    }
    get lock() {
        return this._lock;
    }
    set lock(value) {
        this._lock = value;
        if (value) {
            this.ctx.canvas.style.cursor = 'not-allowed';
            // 注销事件
            this.offDragImageEvent?.();
            this.offPointClick?.()
            this.offPolygonEvent?.();
        }
        else {
            this.ctx.canvas.style.cursor = 'default';
            this.changeShape(this.createType);
        }
    }

    updated(annotations) {
        this.annotations = annotations
        this.ctx.globalCompositeOperation = "destination-over";
        // 清空画布
        this.clear();
        // 绘制标注
        this.drawAnnotations(this.annotations);
        // 绘制背景
        this.drawBackground({ width: this.currentImgInfo.x, height: this.currentImgInfo.y });
    }
    // 判断点是否在点内
    isPointInPoint(point, center, radius = 0) {
        const centerPoint = new Vector2(center[0], center[1]);
        const distance = point.distanceTo(centerPoint);
        return distance <= radius;
    }

    // 判断点是否在多边形内
    isPointInPolygon(point, points) {
        const path = new Path2D();
        path.moveTo(...points[0]);
        for (let i = 1; i < points.length; i++) {
            path.lineTo(...points[i]);
        }
        path.closePath();
        return this.ctx.isPointInPath(path, point.x, point.y);
    }
}
export default ImageAnnotator;