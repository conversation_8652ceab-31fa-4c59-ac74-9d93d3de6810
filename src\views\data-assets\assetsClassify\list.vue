<template>
  <section class="container">
    <section class="container-box">
      <section class="cf-tools">
        <div class="row">
          <div class="col text-label">
            名称：
            <n-input v-model="condition.keyword" placeholder="分类名称/分类编号" />
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <CfTable
          ref="multipleTableRef"
          :key="state.tableList"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
            lazy: true,
            load: load,
          }"
          :actionWidth="148"
          :paginationConfig="{
            total: state.total,
            pageSize: state.filterSearch.pageSize,
            currentPage: state.filterSearch.pageNum,
            onCurrentChange: (v) => {
              state.filterSearch.pageNum = v
              onSearch()
            },
            onSizeChange: (v) => {
              state.filterSearch.pageSize = v
              state.filterSearch.pageNum = 1 
              onSearch()
            },
          }"
          :table-head-titles="state.tableHeadTitles"
        >
          <template #pageTop>
            <div class="table-top">
              <div class="btn active" @click.prevent="createDialogRef.open()">
                <SvgIcon class="icon" icon="new-add" />
                新建分类
              </div>
            </div>
          </template>
          <template #editor="{ data: { row } }">
            <el-button type="primary" link @click="editDialogRef.open(row)"> 编辑 </el-button>
            <el-button v-if="row.code !== 'DORIS_DB'" type="primary" link @click.prevent="delFn(row)"> 删除 </el-button>
            <el-button
              type="primary"
              link
              @click.prevent="
                $router.push({ name: 'assetsClassifyAuth', query: { code: row.code,name:row.name } })
              "
              >授权
            </el-button>
          </template>
        </CfTable>
      </section>
    </section>
    <CreateDialog ref="createDialogRef" @success="onSearch" />
    <EditDialog ref="editDialogRef" @success="onSearch" />
  </section>
</template>
<script setup>
  import { ElMessage } from 'element-plus'
  import {
    getAssetsClassify,
    getAssetsClassifyChildren,
    deleteAssetsClassify,
  } from '@/api/sceneManage'
  import CreateDialog from './components/createDialog.vue'
  import EditDialog from './components/editDialog.vue'
  const { proxy } = getCurrentInstance()
  const state = reactive({
    open: false,
    tableList: [],
    treeSearchText: '',
    filterSearch: {
      condition: {
        keyword: '',
      },
      pageNum: 1,
      pageSize: 10,
      sortConditions: [
        {
          fieldName: null,
          sort: null,
        },
      ],
    },
    total: 0,
    tableHeadTitles: [
      {
        name: '分类名称',
        prop: 'name',
      },
      {
        name: '分类编码',
        prop: 'code',
      },
      {
        name: '创建人',
        prop: 'createByName',
      },
      {
        name: '创建时间',
        prop: 'createTime',
      },
    ],
  })
  const createDialogRef = ref(null)
  const editDialogRef = ref(null)
  const condition = ref({})

  // 获取资产分类列表
  const getAssetsClassifyList = async () => {
    getAssetsClassify(state.filterSearch).then(({ data }) => {
      state.tableList =
        data?.list?.map((_) =>
          Object.assign(_, {
            hasChildren: true,
          }),
        ) || []
      state.total = data?.total || 0
    })
  }
  const onSearch = (init) => {
    init &&
      Object.assign(state.filterSearch, {
        pageNum: 1,
        condition: condition.value,
      })
    getAssetsClassifyList()
  }
  const load = (row, treeNode, resolve) => {
    getAssetsClassifyChildren(row).then(({ data }) => {
      resolve(
        data?.map((_) =>
          Object.assign(_, {
            hasChildren: true,
          }),
        ) || [],
      )
    })
  }
  const resetFn = (() => {
    const data = JSON.parse(JSON.stringify(state.filterSearch))
    return () => {
      state.filterSearch = JSON.parse(JSON.stringify(data))
      condition.value = {}
      onSearch()
    }
  })()
  // 删除目录
  const delFn = (data) => {
    proxy.$dialogPopup({
      title: '是否确认删除？',
      message: '删除后，分类不再展示',
      save: () => {
        deleteAssetsClassify(data).then(({ success }) => {
          if (!success) return
          onSearch()
          ElMessage.success('删除成功')
        })
      },
    })
  }
  getAssetsClassifyList()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 10px;
      display: flex;
      flex-direction: column;
      &-table {
        flex: 1;
        height: calc(100% - 60px);
        display: flex;
        gap: 10px;
        border-radius: 0px 0px 2px 2px;
        background: var(--100, #fff);
      }
    }
  }
  :deep(.page-top) {
    display: flex;
    padding: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    .btn {
      display: flex;
      padding: 4px 16px;
      align-items: center;
      gap: 4px;
      border-radius: 2px;
      border: 1px solid var(---, #1e89ff);
      color: #1e89ff;
      font-size: 14px;

      &.active,
      &:hover {
        color: #fff;
        background: #1e89ff;
      }
    }
  }
</style>
