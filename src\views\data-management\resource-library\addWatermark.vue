<template>
  <div :class="{ container: true, isLzos: state.isLzos }">
    <div class="tools">
      <div class="row">
        <div class="add-box-top-title">
          <div class="common-section-header">
            <div class="title">创建水印</div>
            <div class="detail-back-box">
              <div class="detail-back-box-btn" @click.prevent="cancelFn"
                ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
              </div>
              <div class="detail-back-box-btn checked" @click.prevent="closeFn"
                ><SvgIcon class="icon" icon="icon-close" />关闭
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-box">
        <div class="add-box-top-title">
          <div class="common-section-header has-bottom-border">
            <div class="title">配置</div>
          </div>
        </div>
        <div class="content-box-body scroll-bar-style">
          <n-form
            ref="ruleForm"
            :data="state.ruleForm"
            :rules="state.rules"
            label-width="150px"
            label-align="start"
          >
            <div class="content-box-body-title">
              <SvgIcon
                v-if="state.watermarkTitle"
                icon="table-arrow-down"
                title="展开"
                @click="expandTitleFn('watermarkTitle')"
              />
              <SvgIcon
                v-else
                class="noExpand"
                icon="table-arrow-down"
                title="收缩"
                @click="expandTitleFn('watermarkTitle')"
              />
              <span>水印内容</span>
              <i></i>
            </div>
            <n-form-item v-show="state.watermarkTitle" field="textContent" label="文字内容：">
              <div class="textarea-box">
                <n-textarea
                  v-model="state.ruleForm.textContent"
                  :maxLength="200"
                  :autosize="{ minRows: 3 }"
                  placeholder="${time} ${name} ${phone} ${department}内部使用，请勿外传"
                />
                <div class="tip"
                  >支持变量:${time} 系统时间${name} 账户姓名${phone} 电话号码${department} 部门</div
                >
              </div>
            </n-form-item>
            <div class="content-box-body-title">
              <SvgIcon
                v-if="state.styleSetting"
                icon="table-arrow-down"
                title="展开"
                @click="expandTitleFn('styleSetting')"
              />
              <SvgIcon
                v-else
                class="noExpand"
                icon="table-arrow-down"
                title="收缩"
                @click="expandTitleFn('styleSetting')"
              />
              <span>样式设置</span>
              <i></i>
            </div>
            <div v-show="state.styleSetting" class="font-box">
              <n-form-item field="fontSize" label="字号：">
                <div class="flex-space-between">
                  <div class="slider-wrapper">
                    <n-slider :min="12" :max="144" v-model="state.ruleForm.fontSize"></n-slider>
                  </div>
                  <div class="snapshot-value">
                    <n-input-number
                      v-model="state.ruleForm.fontSize"
                      :min="12"
                      :max="144"
                      :precision="0"
                      :step="1"
                    ></n-input-number>
                  </div>
                </div>
              </n-form-item>
              <n-form-item field="fontColor" label="颜色：">
                <div class="custom-color">
                  <div
                    v-for="(item, index) in state.colorList"
                    :key="index"
                    :style="'background-color:' + item.value"
                    :class="{ 'custom-color-label': true, checked: item.checked }"
                    @click.prevent.stop="checkFn(index)"
                  >
                    <SvgIcon class="icon" icon="icon-check-white" title="选中" />
                  </div>
                </div>
              </n-form-item>
              <n-form-item field="rotation" label="版式：">
                <n-select
                  v-model="state.ruleForm.rotation"
                  :options="state.rotationOptions"
                  placeholder="请选择"
                />
              </n-form-item>
            </div>
          </n-form>
        </div>
      </div>
      <div class="content-box">
        <div class="add-box-top-title">
          <div class="common-section-header has-bottom-border">
            <div class="title">预览</div>
          </div>
        </div>
        <div class="content-box-body scroll-bar-style">
          <div class="imgBox">
            <div
              class="mark"
              :style="{
                color: state.colorList.filter((val) => val.checked)[0].value,
                'font-size': state.ruleForm.fontSize + 'px',
                transform: 'rotate(' + state.ruleForm.rotation + 'deg)',
              }"
              >{{ state.ruleForm.textContent }}</div
            >
          </div>
          <div class="imgBox gray"
            ><div
              class="mark"
              :style="{
                color: state.colorList.filter((val) => val.checked)[0].value,
                'font-size': state.ruleForm.fontSize + 'px',
                transform: 'rotate(' + state.ruleForm.rotation + 'deg)',
              }"
              >{{ state.ruleForm.textContent }}</div
            ></div
          >
          <div class="footer">
            <n-button size="sm" @click.prevent="cancelFn">取消</n-button>
            <n-button
              class="save"
              :loading="state.loading"
              size="sm"
              variant="solid"
              color="primary"
              @click.prevent="submitFn"
              >确定</n-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  const store = useStore()
  const router = useRouter()
  const ruleForm = ref()

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    loading: false,
    watermarkTitle: true,
    styleSetting: true,
    fileId: null,
    ruleForm: {
      textContent: '',
      fontSize: 12,
      fontColor: '#CFCFCF',
      rotation: '-45',
    },
    rules: {
      textContent: { required: true, message: '请输入水印内容', trigger: 'change' },
    },
    colorList: [
      { r: 207, g: 207, b: 207, value: '#CFCFCF', checked: true },
      { r: 150, g: 189, b: 255, value: '#96BDFF', checked: false },
      { r: 117, g: 235, b: 194, value: '#75EBC2', checked: false },
      { r: 255, g: 200, b: 122, value: '#FFC87A', checked: false },
      { r: 255, g: 188, b: 181, value: '#FFBCB5', checked: false },
    ],
    rotationOptions: [
      { name: '水平', value: '0' },
      { name: '倾斜', value: '-45' },
    ],
  })
  const expandTitleFn = (name) => {
    state[name] = !state[name]
  }
  // 选中颜色
  const checkFn = (index) => {
    state.colorList.forEach((val, ind) => {
      val.checked = false
      if (index === ind) {
        val.checked = true
      }
    })
  }
  // 提交
  const submitFn = () => {
    state.loading = true
    ruleForm.value.validate((valid) => {
      state.loading = false
      if (valid) {
        let data = { ...state.ruleForm }
        data.r = state.colorList.filter((val) => val.checked)[0].r
        data.g = state.colorList.filter((val) => val.checked)[0].g
        data.b = state.colorList.filter((val) => val.checked)[0].b
        data.isCustom = true
        data.fileId = state.fileId
        data.status = 'ENABLED'
        api.dataManagement.saveWatermark(data).then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '保存成功',
              type: 'success',
            })
            router.go(-1)
          }
        })
      }
    })
  }
  // 取消
  const cancelFn = () => {
    router.go(-1)
  }
  // 关闭
  const closeFn = () => {
    router.push({ name: 'resourceLibraryList' })
  }
  onMounted(() => {
    state.fileId = String(router.currentRoute.value.query.id)
  })
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    padding: 12px;
    .tools {
      background-color: #fff;
      border-radius: 8px;
      .row {
        position: relative;
        .detail-back-box-btn {
          font-weight: normal;
        }
      }
    }
    .content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: calc(100% - 60px);
      margin-top: 8px;
      border-radius: 8px;
      &-box {
        width: calc(50% - 4px);
        height: 100%;
        background-color: #fff;
        border-radius: 8px;
        &-body {
          position: relative;
          height: calc(100% - 52px);
          padding: 1px 16px;
          overflow-y: auto;
          &-title {
            display: flex;
            align-items: center;
            height: 48px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 16px;

            svg {
              margin-right: 4px;
              color: #8091b7;
              font-size: 16px;
              cursor: pointer;

              &.noExpand {
                transform: rotate(180deg);
              }
            }

            i {
              display: block;
              flex: 1;
              height: 1px;
              margin-left: 8px;
              border-bottom: 1px solid #c5d0ea;
            }
          }
          .font-box {
            min-height: 200px;
            .flex-space-between {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .slider-wrapper {
                width: calc(100% - 100px);
                :deep(.nancalui-slider) {
                  .nancalui-slider__runway {
                    background-color: #e3ecff;
                  }
                }
              }
              .snapshot-value {
                width: 100px;
                margin-left: 8px;
                :deep(.nancalui-input__wrapper) {
                  padding-right: 0;
                }
              }
            }
          }
          .textarea-box {
            width: 100%;
            .tip {
              margin-top: 4px;
              color: rgba(0, 0, 0, 0.55);
              font-size: 14px;
            }
          }
          .custom-color {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: rgba(0, 0, 0, 0.55);
            font-size: 14px;

            .nancalui-switch {
              margin-left: 6px;
            }

            &-label {
              display: flex;
              align-items: center;
              justify-content: center;
              box-sizing: border-box;
              width: 20px;
              height: 20px;
              margin-right: 8px;
              border: 1px solid #fff;
              border-radius: 4px;

              .icon {
                display: none;
                color: #fff;
                font-size: 12px;
              }

              &.checked {
                box-shadow: 0 0 0 1px #447dfd;

                .icon {
                  display: block;
                }
              }
            }
          }
          .imgBox {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: calc(50% - 56px);
            margin-top: 16px;
            background-color: #f6f7fb;
            border-radius: 6px;
            &.gray {
              background-color: #b8b8b8;
            }
            .mark {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              justify-content: center;
              width: 302px;
              height: 224px;
              color: #cfcfcf;
              font-size: 36px;
              text-align: center;
              word-break: break-all;
              transform: rotate(-30deg);
            }
          }
          .footer {
            position: absolute;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            box-sizing: border-box;
            width: 100%;
            height: 64px;
            padding: 0 16px;
          }
        }
      }
    }
    .add-box-top-title {
      margin-bottom: 0;
      .title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .confidentiality-level-label {
          display: inline-block;
          height: 24px;
          margin-left: 8px;
          padding: 0 8px;
          color: rgba(0, 0, 0, 0.75);
          font-weight: normal;
          font-size: 14px;
          line-height: 22px;
          text-align: center;
          background-color: #e3ecff;
          border: 1px solid #6e9eff;
          border-radius: 6px;
        }
      }
    }
  }
</style>
