<template>
  <section :class="['container', state.isLzos ? 'isLzos' : '']">
    <section class="container-box">
      <section class="tools">
        <div class="row">
          <div class="col">
            <span class="label">任务名称：</span>
            <n-input
              v-model="state.originalFormInline.name"
              placeholder="任务名称"
              size="small"
              clearable
            />
            <span class="label">创建人：</span>
            <n-input
              v-model="state.originalFormInline.createByName"
              placeholder="创建人"
              size="small"
              clearable
            />
            <span class="label">时间范围：</span>
            <n-range-date-picker-pro
              v-model="state.originalFormInline.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              :shortcuts="state.shortcuts"
              allow-clear
            />
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="table">
        <div class="row">
          <n-button
            v-if="state.buttonAuthList.includes('governanceManage_qualityManage_qualityTask_add')"
            code="governanceManage_qualityManage_qualityTask_add"
            variant="solid"
            @click="onAdd"
          >
            <SvgIcon icon="new-add" class="icon" title="新建" />新建任务
          </n-button>
        </div>
        <div class="table-content">
          <CfTable
            actionWidth="180"
            :table-head-titles="state.tableHeadTitles"
            :tableConfig="{
              data: state.tableData,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: state.pagination.total,
              pageSize: state.pagination.pageSize,
              currentPage: state.pagination.currentPage,
              onCurrentChange: (v) => {
                state.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pagination.pageSize = v
                onSearch(true)
              },
            }"
          >
            <template #status="{ row }">
              <div class="status">
                <div :class="row.status === 'ONLINE' ? 'circle' : 'circle gray'"></div>
                {{ row.status === 'ONLINE' ? '已发布' : '已下架' }}
              </div>
            </template>
            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="handleView(row)">查看</n-button>
              <n-button
                variant="text"
                color="primary"
                v-if="row.status !== 'ONLINE'"
                @click="statusFn(row)"
                >发布</n-button
              >
              <n-button v-else variant="text" color="primary" @click="statusFn(row)">下架</n-button>
              <n-popover trigger="hover">
                <SvgIcon class="nc-m-l-14" icon="icon-btn-more" />
                <template #content>
                  <div>
                    <div
                      v-if="
                        state.buttonAuthList.includes(
                          'governanceManage_qualityManage_qualityTask_run',
                        )
                      "
                      class="popover-item"
                    >
                      <n-button
                        variant="text"
                        color="primary"
                        :disabled="row.status === 'OFFLINE'"
                        @click="runFn(row)"
                        >立即运行</n-button
                      >
                    </div>
                    <div
                      v-if="
                        state.buttonAuthList.includes(
                          'governanceManage_qualityManage_qualityTask_log',
                        )
                      "
                      class="popover-item"
                    >
                      <n-button variant="text" color="primary" @click="runRecordFn(row)"
                        >运行记录</n-button
                      >
                    </div>
                    <div
                      v-if="
                        state.buttonAuthList.includes(
                          'governanceManage_qualityManage_qualityTask_edit',
                        )
                      "
                      class="popover-item"
                    >
                      <n-button
                        variant="text"
                        color="primary"
                        :disabled="row.status === 'ONLINE'"
                        @click="editFn(row)"
                        >编辑</n-button
                      >
                    </div>
                    <div
                      v-if="
                        state.buttonAuthList.includes(
                          'governanceManage_qualityManage_qualityTask_delete',
                        )
                      "
                      class="popover-item"
                    >
                      <n-button
                        variant="text"
                        color="primary"
                        :disabled="row.status === 'ONLINE'"
                        @click="delFn(row)"
                        >删除</n-button
                      >
                    </div>
                  </div>
                </template>
              </n-popover>
            </template>
          </CfTable>
          <!--          <div class="table-content-list">-->
          <!--            <div class="table-content-list-card">-->
          <!--              <QualityTaskListItem-->
          <!--                :item="item"-->
          <!--                @delete="handleDelete"-->
          <!--                @change="onSearch"-->
          <!--                @edit="handleEdit"-->
          <!--                @view="handleView"-->
          <!--                v-for="(item, key) in state.tableData"-->
          <!--                :key="key"-->
          <!--            /></div>-->
          <!--            <div v-if="state.tableData.length === 0" class="empty">-->
          <!--              <img class="pic" src="@/assets/table-no-content-small.png" />-->
          <!--              <div class="empty-word">暂无相关任务</div>-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          <div v-if="state.tableData.length > 0" class="table-content-pagination nancalui-table-page">-->
          <!--            <n-pagination-->
          <!--              :total="state.pagination.total"-->
          <!--              v-model:pageSize="state.pagination.pageSize"-->
          <!--              v-model:pageIndex="state.pagination.currentPage"-->
          <!--              :can-change-page-size="true"-->
          <!--              canViewTotal-->
          <!--              :page-size-options="[10, 20, 50, 64]"-->
          <!--              @page-index-change="pageIndexChange"-->
          <!--              @page-size-change="pageSizeChange"-->
          <!--            />-->
          <!--          </div>-->
        </div>
      </section>
    </section>
    <!-- 配置规则 -->
    <n-drawer
      v-model="state.drawer"
      title=""
      :size="720"
      :esc-key-closeable="false"
      class="template-config-drawer"
      :key="state.drawer"
    >
      <div class="n-drawer-body" v-loading="state.pageLoading">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <SvgIcon class="icon" icon="icon-drawer-title" />
            <div class="title">{{ state.drawerState }}质量任务</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
        </div>
        <div class="n-drawer-body-content">
          <basic ref="basicRef" :drawerState="state.drawerState" />
        </div>
        <div class="box-operate" v-if="state.drawerState !== '查看'">
          <n-button size="sm" color="secondary" @click.prevent="closeDrawerFn">取消</n-button>
          <n-button
            :loading="state.btnLoading"
            class="save"
            size="sm"
            variant="solid"
            color="primary"
            @click.prevent="saveFn"
            >确定</n-button
          >
        </div>
      </div>
    </n-drawer>
  </section>
</template>

<script setup>
  import { onMounted, reactive, ref, toRefs, getCurrentInstance } from 'vue'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import QualityTaskListItem from './components/QualityTaskListItem'
  import basic from './components/basic'
  import generativeRules from '../rule-template/components/generative-rules'
  import { throttle } from 'lodash-es'
  const store = useStore()
  const router = useRouter()
  import api from '@/api/index'
  // 获取当前组件实例
  const { proxy } = getCurrentInstance()
  const basicRef = ref()
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    drawerKey: 1,
    loading: false,
    pageLoading: false,
    drawer: false,
    buttonAuthList: [],
    shortcuts: ENUM.SHORTCUTS,
    tableHeadTitles: [
      { prop: 'name', name: '任务名称' },
      { prop: 'tableName', name: '监控对象' },
      { prop: 'description', name: '描述' },
      { prop: 'scheduleModeName', name: '调度方式' },
      { prop: 'configRuleCount', name: '规则数量' },
      { prop: 'createByName', name: '问题责任人' },
      { prop: 'status', name: '状态', slot: 'status' },
    ],
    originalFormInline: {
      name: '',
      time: [],
      createByName: '',
    },
    searchForm: {
      name: '',
      time: [],
      createByName: '',
    },
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    tableData: [],
    prop: {
      label: 'name',
      value: 'id',
      children: 'childrenList',
    },
  })

  const onSearch = (init) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage
    let startTime = ''
    let endTime = ''
    if (state.searchForm.time && state.searchForm.time[0]) {
      startTime = formartTime(state.searchForm.time[0])
      endTime = formartTime(state.searchForm.time[1], true)
    }
    let data = {
      condition: {
        name: state.searchForm.name || null,
        createByName: state.searchForm.createByName || null,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    if (startTime) {
      data.condition.startTime = startTime
      data.condition.endTime = endTime
    }
    state.loading = true
    api.dataQuality
      .getTaskPage(data)
      .then((res) => {
        state.loading = false
        state.tableData = res?.data?.list || []
        state.pagination.total = res.data.total
      })
      .catch(() => {
        state.loading = false
      })
  }

  const resetFn = () => {
    state.originalFormInline = {
      name: '',
      time: [],
      createByName: '',
    }
    searchClickFn()
  }
  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.searchForm[key] = state.originalFormInline[key]
    }
    onSearch(true)
  }

  const pageSizeChange = (val) => {
    state.pagination.pageSize = val
    onSearch(true)
  }
  const pageIndexChange = (val) => {
    state.pagination.currentPage = val
    onSearch()
  }
  const onAdd = () => {
    state.drawer = true
    state.drawerState = '新建'
  }
  const closeDrawerFn = () => {
    state.drawer = false
    state.drawerState = undefined
    return false
  }
  const handleDelete = () => {
    onSearch()
  }
  // 点击运行
  const runFn = throttle((item) => {
    api.dataQuality.taskExecute({ id: item?.id }).then((res) => {
      let { success } = res
      if (success) {
        ElNotification({
          title: '提示',
          message: '运行成功',
          type: 'success',
        })
        onSearch(false)
      }
    })
  }, 5000)
  // 点击跳转运行记录
  const runRecordFn = (item) => {
    router.push({
      name: 'operatingLog',
      query: {
        id: item?.id,
        name: item?.name,
      },
    })
  }
  // 编辑
  const editFn = (row) => {
    state.drawer = true
    state.drawerState = '编辑'
    api.dataQuality.getQualityTaskDetail(row.id).then((res) => {
      basicRef.value.editInit(res.data)
    })
  }

  // 点击删除
  const delFn = (item) => {
    proxy.$dialogPopup({
      title: '是否确认删除质量任务？',
      message: '删除后，质量任务将会被停止运行',
      save: () => {
        // 如果新增指标后，需要更新指标体系数据
        if (sessionStorage.getItem('targetThemeData')) {
          sessionStorage.removeItem('targetThemeData')
        }
        api.dataQuality.deleteTaskItem({ id: item?.id }).then((res) => {
          let { success } = res
          if (success) {
            ElNotification({
              title: '提示',
              message: '删除成功',
              type: 'success',
            })
            // 如果新增指标后，需要更新指标体系数据
            if (sessionStorage.getItem('targetThemeData')) {
              sessionStorage.removeItem('targetThemeData')
            }
            onSearch(true)
          }
        })
      },
    })
  }

  // 点击上线
  const statusFn = (item) => {
    const isOnline = item?.status === 'ONLINE'
    api.dataQuality[isOnline ? 'qualityTaskOffline' : 'qualityTaskOnline']({
      id: item?.id,
    }).then((res) => {
      let { success } = res
      if (success) {
        ElNotification({
          title: '提示',
          message: isOnline ? '下架成功' : '发布成功',
          type: 'success',
        })
        onSearch(false)
      }
    })
  }

  // 查看
  const handleView = (row) => {
    state.drawer = true
    state.drawerState = '查看'
    api.dataQuality.getQualityTaskDetail(row.id).then((res) => {
      nextTick(() => {
        basicRef.value.viewInit(res.data)
      })
    })
  }
  // 确定
  const saveFn = () => {
    basicRef.value.save().then(() => {
      state.drawer = false
      state.drawerState = undefined
      onSearch(true)
    })
  }

  onMounted(() => {
    const { buttonAuthList } = toRefs(store.state.user)
    state.buttonAuthList = buttonAuthList
    onSearch(true)
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    &.isLzos {
      padding: 0;
    }

    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      .tools {
        height: 50px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 2px;
        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 50px;
          padding: 0 16px;

          &-title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            width: 100%;
            height: 52px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            :deep(.button-content) {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
            }
            &-btn {
              position: absolute;
              right: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              color: $themeBlue;
              font-weight: 400;
              font-size: 16px;
              border-radius: 6px;
              cursor: pointer;

              &:hover {
                background-color: #e3ecff;
              }
            }
          }
          &.date {
            height: 36px;
            :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
              width: 260px;
            }
          }
          .col {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .label {
              color: #1d2129;
              font-size: 14px;
            }
          }
          &.tabs {
            align-items: flex-end;
            height: 48px;
            :deep(.nancalui-tabs) {
              .nancalui-tabs-nav-tab {
                border-bottom: none;
              }
            }
          }
          :deep(.button-content) {
            .add {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
              .arrow {
                margin-left: 4px;
                color: #fff;
                font-size: 16px;
              }
            }
          }

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 0 8px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 14px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
            }
          }

          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 32px;
          }

          .search {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-btn {
              width: 60px;
              height: 30px;
              text-align: center;
              line-height: 30px;
              color: #fff;
              font-weight: 400;
              font-size: 14px;
              border: 1px solid #1e89ff;
              border-radius: 2px;
              cursor: pointer;
              background-color: #1e89ff;

              &.reset {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;
                color: #1d2129;
                background-color: #fff;
                border: 1px solid #dcdfe6;
                &:hover {
                  color: #479dff;
                  background-color: #fff;
                  border: 1px solid #479dff;
                }
                .icon {
                  margin-left: 4px;
                  font-size: 10px;
                }
              }

              &:hover {
                background-color: #479dff;
                border: 1px solid #479dff;
              }
            }
          }
        }
      }

      .table {
        height: calc(100% - 60px);
        margin-top: 10px;
        background-color: #fff;
        border-radius: 2px;

        .row {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 48px;
          padding: 8px;
        }

        &-content {
          display: inline-block;
          box-sizing: border-box;
          width: 100%;
          height: calc(100% - 48px);
          vertical-align: top;

          &-list {
            height: calc(100% - 60px);
            overflow-y: auto;
            &-card {
              display: grid;
              grid-gap: 16px;
              padding: 0 8px;
              grid-template-columns: repeat(4, calc(25% - 12px));
            }
            .empty {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: calc(100% - 100px);
              margin-top: 60px;

              .pic {
                width: 88px;
                height: auto;
                margin-bottom: 10px;
              }

              &-word {
                color: rgba(0, 0, 0, 0.55);
                font-weight: 400;
                font-size: 14px;
                text-align: center;
              }
            }
          }

          &-pagination {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 60px;
            padding: 0 16px;
            box-sizing: border-box;
            border-top: 1px solid #dcdfe6;
            background-color: #fff;

            &-total {
              color: rgba(0, 0, 0, 0.46);
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;

              span {
                color: rgba(0, 0, 0, 0.85);
              }
            }
          }
        }
      }
    }
  }

  .tableType {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .status {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .circle {
      width: 6px;
      height: 6px;
      margin-right: 8px;
      background-color: #04c495;
      border-radius: 50%;

      &.gray {
        background-color: #b8b8b8;
      }
    }
  }

  .edit-box {
    :deep(.nancalui-button) {
      padding: 0;
      color: $themeBlue;

      .icon {
        font-size: 16px;
      }

      &:last-of-type {
        margin-right: 32px;
      }
    }
  }

  .template-name-box {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    span {
      display: inline-block;
      align-content: center;
      width: 52px;
      height: 24px;
      margin-right: 6px;
      padding: 0 4px;
      color: var(----, rgba(0, 0, 0, 0.75));
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background: var(----, #e3ecff);
      border: 1px solid var(---, #a3b4db);
      border-radius: 6px;
    }
  }

  .list-menu {
    width: 118px;
    padding: 6px 0;

    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 32px;
      padding: 0 12px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      cursor: pointer;

      .icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &:hover {
        color: $themeBlue;
        background-color: #e3ecff;
      }
    }
  }

  .n-drawer-body {
    height: 100%;

    .box-steps {
      justify-content: flex-start;
    }

    .box-operate {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px;
      padding: 0 16px;
    }
  }
  .popover-item {
    width: 100px;
    .nancalui-button--disabled {
      color: #c0c4cc;
    }
  }
</style>
