import { Graph } from '@antv/x6'
import './shape'

let graph = null
export default class FlowGraph {
  static init() {
    graph = new Graph({
      container: document.getElementById('data-map'),
      width: 1608,
      height: 456,
      interacting: {
        nodeMovable: false,
      },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
    })
    return graph
  }

  // 销毁
  static destroy() {
    graph.dispose()
  }
}
