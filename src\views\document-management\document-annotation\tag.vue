<template>
  <teleport to="#tag-container">
    <div class="tag-container-item" ref="reference" :style="{ top: yContainer, left: xContainer }"> </div>
    <el-popover ref="popoverRef" popper-class="tag-popover" :teleported="false" placement="top" :virtual-ref="reference"
      :visible="show" trigger="click" virtual-triggering>
      <!-- 编辑模式 -->
      <div v-if="mode === 'edit'" style="display: flex;align-items: center;">
        <n-button v-if="!showTag" variant="solid" @click="addTag">添加标注</n-button>
        <el-select v-else ref="selectTree" v-model="state.selectedTags" placeholder="请选择标签"
           style="min-width: 200px" node-key="key" :props="{
            label: 'name',
            value: 'key',
          }" @change=""  show-checkbox :render-after-expand="false" filterable clearable >
          <el-option v-for="item in props.tagOptions"
            :key="item.key"
            :label="item.name"
            :value="item.key" />
        </el-select>
        <n-button v-if="showTag" variant="text" @click="handleConfirm">确认</n-button>
        <SvgIcon class="tree-icon-tool" @click="handleMarkDelete" style="margin-left: 8px;"
          icon="icon-tree-del" />
      </div>
      <!-- 预览模式 -->
      <div v-else class="tag-preview">
        <cfTag :tagArr="JSON.stringify(props.initialTags || [])"></cfTag>
      </div>
    </el-popover>
  </teleport>
</template>

<script setup>
import cfTag from './components/CfTag'

const props = defineProps({
  x: { type: Number, default: 0 },
  y: { type: Number, default: 0 },
  mode: { type: String, default: 'edit' }, // edit/preview
  initialTags: { type: Array, default: () => [] },
  tagOptions: { type: Array, default: () => [] }
})
const reference = ref(null), popoverRef = ref(null),selectTree = ref(null)
// visible
const show = ref( props.mode === 'preview' || props.initialTags?.length > 0 ? false : true)
const state = reactive({
  selectedTags: [...props.initialTags?.map(_=>_.id)][0], // 已选择的标签
})

// 删除标注
const handleMarkDelete = (id) => {
  emit('mark-delete', id)
}
// 确认
const handleConfirm = () => {
  show.value = false
  emit('confirm')
  handleTagSelect()
}

// 显示标签选择框
const showTag = ref(props.initialTags?.length > 0 || false)
const emit = defineEmits(['tag-update'])

const addTag = () => {
  // 打开标签选择框
  showTag.value = true
}

// 标签选择事件
const handleTagSelect = (values) => {
  let activeOptions = props.tagOptions.filter(item => state.selectedTags===item.key);

  emit('tag-update',  activeOptions
  // .filter((val) => val.nodeType === 'tag')
    .map((val) => {
    return { text: val.name, color: val.color, id: val.key || null }
  }) || [])
}

// 计算属性y-边界处理
const yContainer = computed(() => {
  // 获取容器的盒模型
  const containerRect = document.querySelector('#tag-container')?.parentElement?.getBoundingClientRect()
  // 获取容器的高度
  const containerHeight = containerRect.height
  // 将y限制在容器高度范围内，避免超出容器
  return Math.max(0, Math.min(props.y, containerHeight - 10)) + 'px'
})
// 计算属性x-边界处理
const xContainer = computed(() => {
  // 获取容器的盒模型
  const containerRect = document.querySelector('#tag-container')?.parentElement?.getBoundingClientRect()
  // 获取容器的高度
  const containerWidth = containerRect.width
  // 将y限制在容器高度范围内，避免超出容器
  return Math.max(0, Math.min(props.x, containerWidth - 10)) + 'px'
})

defineExpose({
  showTag(value) {
    if ( typeof value === 'boolean') {
      show.value = value
      return
    }
    show.value = !show.value
  }
})
</script>

<style scoped>
.tag-container-item {
  position: absolute;
  width: 0;
  height: 0;
}

.tag-preview {
  display: flex;
  flex-wrap: wrap;
}
</style>
<style>
.tag-popover {
  width: auto !important;
  min-width: auto !important;
}
</style>
