<template>
  <div class="detail-model">
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="基本信息" id="base">
        <el-form
          ref="ruleForm"
          :model="state.ruleForm"
          label-width="90px"
          label-position="left"
          class="demo-ruleForm"
        >
          <el-form-item label="中文名称：">
            <el-input v-model="state.ruleForm.cnName" maxlength="30" disabled />
          </el-form-item>
          <el-form-item label="英文名称：">
            <el-input v-model="state.ruleForm.name" disabled />
          </el-form-item>
          <el-form-item label="字段类型：">
            <el-input v-model="state.ruleForm.fieldTypeName" disabled />
          </el-form-item>
          <el-form-item label="字段长度：">
            <el-input v-model="state.ruleForm.fieldLength" disabled />
          </el-form-item>
          <el-form-item label="描述信息：">
            <el-input
              v-model="state.ruleForm.description"
              type="textarea"
              show-word-limit
              maxlength="200"
              :autosize="{ minRows: 3 }"
              disabled
            />
          </el-form-item>
        </el-form>
      </n-tab>
      <n-tab title="引用模型" id="modelData">
        <div class="table">
          <PublicTable
            ref="publicTable"
            :needOtherActionBar="state.needOtherActionBar"
            :table-head-titles="state.tableHeadTitles"
            :pagination="state.pagination"
            :exceptHeight="330"
            @tablePageChange="tablePageChange"
          />
        </div>
      </n-tab>
    </n-tabs>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import insertCss from 'insert-css'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      const router = useRouter()
      const publicTable = ref()
      const publicTablePreview = ref()
      const state = reactive({
        activeName: 'base',
        disabled: true,
        detailId: null,
        ruleForm: {
          name: '',
          cnName: '',
          fieldTypeName: '',
          fieldLength: '',
          description: '',
        },
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        tableHeadTitles: [
          { prop: 'number', name: '序号' },
          { prop: 'modelCnName', name: '引用模型中文名称' },
          { prop: 'modelName', name: '引用模型中文名称' },
          { prop: 'projectName', name: '场景' },
          { prop: 'layerName', name: '模型层' },
        ],
        pagination: {
          pageSizes: [10, 20, 50], // 每次展示条数的可配置项
          layout: 'total,prev,pager,next,sizes, jumper',
          currentPage: 1,
          pageSize: 12,
        },
        tableHeadTitlesPreview: [],
        paginationPreview: {
          pageSizes: [10, 20, 50], // 每次展示条数的可配置项
          layout: 'total,prev,pager,next,sizes, jumper',
          currentPage: 1,
          pageSize: 12,
        },
        filterSearch: {
          keyword: null,
        },
        modelList: [],
      })
      const methods = {
        // 获取模型详情
        getDetail(id) {
          state.loading = true
          api.model
            .checkMeta({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                state.ruleForm = res.data
              }
            })
            .catch(() => {})
        },
        getModeData(id) {
          api.model
            .checkMetaModel({ id })
            .then((res) => {
              if (res.code === 'SUCCESS') {
                // 新增序号属性
                res.data.modelList.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                  })
                })
                publicTable.value.initTableData({ list: res.data.modelList })
              }
            })
            .catch(() => {
              publicTable.value.initFailed()
            })
          // publicTable.value.initTableData({
          //   total: 1,
          //   pageNum: 1,
          //   pageSize: 12,
          //   list: [{ name: 'dsfsv', cnName: '物料基表', assetsMetaCname: '' }],
          // })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.getTagData(false)
        },
        //切换
        handleClick(id) {
          switch (id) {
            case 'modelData':
              methods.getModeData(state.detailId)
              break
            default:
              methods.getDetail(state.detailId)
          }
        },
      }
      onMounted(() => {
        nextTick(() => {
          state.detailId = router.currentRoute.value.query.id
          methods.getDetail(state.detailId)
        })
      })

      return {
        state,
        publicTable,
        publicTablePreview,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $labelWidth: 90px;
  .detail-model {
    color: #333;
    height: 100%;
    position: relative;
    .base-tabs {
      height: 100%;
      :deep(.nancalui-tab__content) {
        height: calc(100% - 40px);

        > div {
          width: 100%;
          height: 100%;
        }
      }
      .box-left {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
    }
    :deep(.base-tabs) {
      .demo-ruleForm {
        margin: 45px auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 600px;

        .el-form-item {
          width: 100%;
          position: relative;
          margin-bottom: 20px;
          display: flex;
          .el-form-item__label {
            position: absolute;
            right: 600px;
            top: 0;
            width: max-content !important;
          }
          &.operate-box {
            display: inline-block;
            text-align: center;
            transform: translateX($labelWidth);
          }
          .el-select {
            width: 100%;
          }
          .el-form-item__content {
            // 要想在sass的calc中使用变量，必须对这个变量使用sass的插值方法（#{$labelWidth}）
            width: calc(100% - #{$labelWidth});
            margin-left: 0 !important;
          }
        }
      }
    }
  }
</style>
