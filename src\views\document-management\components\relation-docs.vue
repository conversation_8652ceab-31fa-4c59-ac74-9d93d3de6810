<template>
  <n-modal v-model="visiable" size="720" title="关联非结构化数据" @close="onClose">
    <div id="relationDocs" v-loading="isLoad"></div>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onClose">确定</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>
<script setup>
  import { Graph } from '@antv/x6'
  import './graph/shape.js'
  import { ConcentricLayout } from '@antv/layout'
  import api from '@/api/index'
  import router from '@/router'
  import { getCurrentInstance } from 'vue'
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
    apiType: { type: String, default: 'associationRelation' },
    type: { type: String, default: 'preparation' },
  })
  const emit = defineEmits(['update:modelValue'])
  function onClose() {
    emit('update:modelValue', false)
  }
  const isLoad = ref(false)
  // 获取关联非结构化数据
  function getRelationDocs() {
    isLoad.value = true
    api.documentManage[props.apiType]({
      docId: props.id,
    })
      .then((res) => {
        const { sourceDoc, targetDocs } = res.data
        initGraph(sourceDoc, targetDocs)
      })
      .catch((e) => {
        console.log(e)
        isLoad.value = false
      })
  }
  function setLabel(name) {
    let label = ''
    if (name.length < 7) {
      label = name
    } else {
      const maxName = name.substr(0, 50)
      for (let l = 0; l < maxName.length / 6; l++) {
        if (l) {
          label += '\n'
        }
        label += name.substr(l * 6, 6)
      }
    }
    return label
  }
  function initGraph(sourceDoc, targetDocs) {
    const data = {
      nodes: [
        {
          id: `${sourceDoc.id}`,
          shape: 'nc-doc',
          data: {
            label: sourceDoc.name,
            type: sourceDoc.type,
          },
          previewFlag: sourceDoc.previewFlag,
        },
      ],
      edges: [],
    }
    targetDocs?.forEach((i) => {
      data.nodes.push({
        id: `${i.id}`,
        shape: 'nc-doc',
        data: {
          label: i.name,
          type: i.type,
        },
        previewFlag: i.previewFlag,
      })
      data.edges.push({
        source: { cell: `${sourceDoc.id}`, anchor: 'orth', connectionPoint: 'bbox' },
        target: { cell: `${i.id}`, anchor: 'orth', connectionPoint: 'bbox' },
        attrs: {
          line: {
            targetMarker: 'classic',
            stroke: '#CCD0D8',
          },
        },
      })
    })

    const graph = new Graph({
      container: document.getElementById('relationDocs'),
      width: 740,
      height: 400,
      background: { color: '#F5F7FA' },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      mousewheel: {
        enabled: true,
      },
      interacting: {
        nodeMovable: false,
      },
    })
    // 点击其他节点时，隐藏删除关系线弹窗
    graph.on('cell:click', ({ cell }) => {
      if (cell.id) {
        if (props.type === 'upload') {
          router.push({
            name: 'documentUploadPreview',
            query: { id: cell.id },
          })
        } else if (props.type === 'entry') {
          if (!cell.store.data.previewFlag) {
            proxy.$message.warning('用户密级不够，无法查看该非结构化数据！')
            return
          }
          router.push({
            name: 'documentEntryPreview',
            query: { id: cell.id },
          })
        } else if (props.type === 'collaborate') {
          router.push({
            name: 'documentCollaborationPreview',
            query: { id: cell.id },
          })
        } else {
          router.push({
            name: 'documentPreparationPreview',
            query: { id: cell.id },
          })
        }
        onClose()
      }
    })
    const concentricLayout = new ConcentricLayout({
      type: 'concentric',
      preventOverlap: true,
      nodeSize: [100, 60],
      maxLevelDiff: 1,
      clockwise: true,
    })
    if (data.edges.length) {
      const modal = concentricLayout.layout(data)
      graph.fromJSON(modal)
    } else {
      graph.fromJSON(data)
    }
    graph.centerContent()
    if (data.nodes.length > 10) {
      graph.zoomToFit()
    }
    isLoad.value = false
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    (val) => {
      visiable.value = val
      getRelationDocs()
    },
  )
</script>
<style lang="scss" scoped>
  #relationDocs {
    width: 700px;
    height: 400px;
  }
  :deep(.nancalui-modal .nancalui-modal__body) {
    padding: 16px !important;
  }
</style>
