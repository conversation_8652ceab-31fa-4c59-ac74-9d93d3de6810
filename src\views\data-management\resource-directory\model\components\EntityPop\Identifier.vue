<template>
  <section class="identify-container">
    <section class="identify-left">
      <p :class="{ actvieNode: node === 'first' }" @click="handleToggle('first')">主键（pk）</p>
      <p :class="{ actvieNode: node === 'second' }" @click="handleToggle('second')"
        >业务主键（sk）</p
      >
    </section>
    <section class="identify-right">
      <section class="identify-table">
        <h4>已选</h4>
        <div class="identify-tableMain">
          <n-my-table
            maxHeight="150px"
            ref="topTable"
            row-key="guid"
            :isSelection="!disabled"
            :show-header="false"
            :isAction="false"
            :isPage="false"
            :attrList="attrList"
            :tableData="topTableData"
            @selectChange="topSelectChange"
            @selectAllChange="topSelectChange"
          />
        </div>
      </section>
      <div class="identify-action">
        <n-button
          :disabled="(bottomTableData && bottomTableData.length === 0) || disabled"
          color="primary"
          variant="solid"
          size="sm"
          @click="handleUp"
        >
          <svg-icon icon="icon-arrow-top" size="14px" />
          <!-- <el-icon><ArrowUpBold /></el-icon> -->
        </n-button>
        <n-button
          size="sm"
          :disabled="(topTableData && topTableData.length === 0) || disabled"
          @click="handleDown"
        >
          <svg-icon icon="icon-arrow-bottom" size="14px" />
          <!-- <el-icon><ArrowDownBold /></el-icon> -->
        </n-button>
      </div>
      <section class="identify-table">
        <h4>
          <span>可选</span>
          <div class="searchDiv">
            <n-search
              v-model="searchForm.attrName"
              placeholder="请输入 属性名"
              @search="handleSearch"
            />
          </div>
        </h4>

        <!-- <el-form :model="searchForm" :inline="true" style="text-align: end">
          <el-form-item label="属性名">
            <el-input v-model="searchForm.attrName" placeholder="" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </el-form-item>
        </el-form> -->
        <div class="identify-tableMain">
          <n-my-table
            maxHeight="150px"
            ref="bottomTable"
            row-key="guid"
            :isSelection="!disabled"
            :show-header="false"
            :isAction="false"
            :isPage="false"
            :attrList="attrList"
            :tableData="bottomTableData"
            @selectChange="bottomSelectChange"
            @selectAllChange="bottomSelectChange"
          />
        </div>
      </section>
    </section>
  </section>
</template>
<script>
  import { cloneDeep } from 'lodash'
  export default {
    name: 'IdentifierGroup',
    // eslint-disable-next-line vue/require-prop-types
    props: ['cell', 'activeName'],
    data() {
      return {
        searchForm: {},
        node: 'first',
        attrList: [
          {
            name: '属性名',
            prop: 'name',
          },
          {
            name: '标准列名',
            prop: 'standardCol',
          },
          {
            name: '列名',
            prop: 'col',
          },
        ],
        topSelectData: [], // 上表格选择数据
        botSelectData: [], // 下表格选择数据
        topTableData: [], // 上表格数据
        bottomTableData: [], // 下表格数据
        allData: [], // 节点所有的数据
        // 主键时
        keyTopTable: [],
        keybottomTable: [],
        // 切换为业务主键时
        busTopTable: [],
        busBottomTable: [],
      }
    },
    computed: {
      disabled() {
        return this.cell.data.disableMove
      },
    },
    watch: {
      keyTopTable: {
        handler(val) {
          if (this.activeName === 'fourth') {
            console.log('in')
            const data = { ...this.cell?.data }
            const attr = cloneDeep(data.attr)
            attr.forEach((item) => {
              if (
                this.keyTopTable?.findIndex((key) => key?.guid && key?.guid === item?.guid) > -1
              ) {
                item.substantiveKey = true
                item.notNull = true
                item.essential = 'true'
              } else {
                item.substantiveKey = false
                item.essential = 'false'
              }
            })
            data.attr = attr
            this.cell.setData(data, { overwrite: true })
          }
        },
        deep: true,
      },
      busTopTable: {
        handler(val) {
          if (this.activeName === 'fourth') {
            console.log('in')
            const data = { ...this.cell?.data }
            const attr = cloneDeep(data.attr)
            attr.forEach((item) => {
              if (
                this.busTopTable?.findIndex((bus) => bus?.guid && bus?.guid === item?.guid) > -1
              ) {
                item.candidate = 'true'
                item.uk = 'true'
              } else {
                item.candidate = 'false'
                item.uk = 'false'
              }
            })
            data.attr = attr
            this.cell.setData(data, { overwrite: true })
          }
        },
        deep: true,
      },
      cell(val) {
        this.initData()
      },
    },
    created() {
      this.initData()
    },
    methods: {
      //点击搜索
      handleSearch() {
        if (this.searchForm.attrName) {
          this.bottomTableData = this.bottomTableData.filter(
            (item) => item.name.indexOf(this.searchForm.attrName) > -1,
          )
        } else {
          if (this.node === 'first') {
            this.bottomTableData = this.keybottomTable
          } else {
            this.bottomTableData = this.busBottomTable
          }
        }
      },
      //初始化表格数据
      initData() {
        this.node = 'first'
        this.allData = cloneDeep(this.cell?.data.attr)
        this.keybottomTable = this.allData?.filter((item) => item.substantiveKey === false)
        this.keyTopTable = this.allData?.filter((item) => item.substantiveKey === true)
        this.busBottomTable = this.allData?.filter(
          (item) => item.candidate === 'false' || item.candidate === '',
        )
        this.busTopTable = this.allData?.filter((item) => item.candidate === 'true')
        this.bottomTableData = this.keybottomTable
        this.topTableData = this.keyTopTable
      },
      // 切换主键
      handleToggle(type) {
        this.node = type
        if (type === 'first') {
          this.bottomTableData = this.keybottomTable
          this.topTableData = this.keyTopTable
        } else {
          this.bottomTableData = this.busBottomTable
          this.topTableData = this.busTopTable
        }
      },
      // 数据上移
      handleUp() {
        this.botSelectData.forEach((item) => {
          let index = this.bottomTableData.findIndex((val) => val.guid === item.guid)
          this.$refs.bottomTable.$refs.myTableComponent.store.toggleRowSelection(item)
          this.bottomTableData.splice(index, 1)
          this.topTableData.push(item)
        })
        this.botSelectData = []
      },
      // 数组下移
      handleDown() {
        this.topSelectData.forEach((item) => {
          let index = this.topTableData.findIndex((val) => val.guid === item.guid)
          this.$refs.topTable.$refs.myTableComponent.store.toggleRowSelection(item)
          this.topTableData.splice(index, 1)
          this.bottomTableData.push(item)
        })
        this.topSelectData = []
      },
      topSelectChange(...params) {
        this.topSelectData = params[params.length - 1]
      },
      bottomSelectChange(...params) {
        this.botSelectData = params[params.length - 1]
      },
    },
  }
</script>
<style lang="scss" scoped>
  .searchDiv {
    float: right;
  }
  :deep(.el-table) {
    tr:first-child {
      border-top: 1px solid !important;
    }
  }
  .identify {
    &-container {
      width: 100%;
      padding: 10px;
      display: flex;
      text-align: center;
      justify-content: space-between;
    }
    &-left {
      width: 220px;
      P {
        cursor: pointer;
        width: 100%;
        height: 50px;
        line-height: 50px;
        border: 1px solid #e9ecf1;
        &.actvieNode {
          background: #ecf5ff;
        }
      }
    }
    &-right {
      width: calc(100% - 240px);
    }
    &-table {
      border: 1px solid #e9ecf1;
      h4 {
        background: #ecf5ff;
        line-height: 30px;
        height: 30px;
      }
    }
    &-tableMain {
      padding: 10px;
    }
    &-action {
      margin: 15px 0;
    }
  }
</style>
