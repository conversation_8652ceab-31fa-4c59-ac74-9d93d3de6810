<template>
  <n-modal
    v-model="showDirDialog"
    title="往来单位字典-详情"
    width="720px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <div class="form-container">
        <div class="form-item">
          <div class="form-label">资产编码：</div>
          <div class="form-content"> {{ state.form.assetCode || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产名称：</div>
          <div class="form-content"> {{ state.form.name || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产分类：</div>
          <div class="form-content"> {{ state.form.classificationName || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">所属业务：</div>
          <div class="form-content"> {{ state.form.bizOwnership || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产提供方：</div>
          <div class="form-content"> {{ state.form.provider || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">来源系统：</div>
          <div class="form-content"> {{ state.form.sourceName || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产重要程度：</div>
          <div class="form-content"> {{ state.form.importanceLevelText || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">发布时间：</div>
          <div class="form-content"> {{ state.form.publishTime || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产持有部门：</div>
          <div class="form-content"> {{ state.form.ownerDept || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产密级：</div>
          <div class="form-content"> {{ state.form.securityLevelText || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产有效时间：</div>
          <div class="form-content"> {{ state.form.thruDate || '--' }} </div>
        </div>
        <div class="form-item">
          <div class="form-label">资产附件：</div>
          <div class="form-content"> {{ state.form.attachments || '--' }} </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import { getAssetsDetail } from '@/api/sceneManage'
  const showDirDialog = ref(false)
  const state = reactive({
    form: {},
    // 重要程度
    importanceLevelOpt: [
      { value: 'GENERAL', name: '普通' },
      { value: 'IMPORTANT', name: '核心' },
      { value: 'VITAL', name: '必要' },
    ],
    confidentialityLevelOptions: [
      { name: '公开', value: 'PUBLIC' },
      { name: '内部', value: 'INTERIOR' },
      { name: '受控', value: 'CONTROLLED' },
      { name: '秘密', value: 'SECRET' },
      { name: '机密', value: 'CONFIDENTIAL' },
      { name: '核心', value: 'CORE' },
    ],
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    cancel()
  }
  // 还原表单
  const resetForm = (() => {
    // const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      // state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(data) {
      getAssetsDetail({ id: data.id })
      resetForm()
      state.importanceLevelOpt.forEach((val) => {
        if (val.value === data.importanceLevel) {
          data.importanceLevelText = val.name
        }
      })
      state.confidentialityLevelOptions.forEach((val) => {
        if (val.value === data.securityLevel) {
          data.securityLevelText = val.name
        }
      })
      state.form = data || {}
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 16px 0;
    .form-container {
      padding: 0 16px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      row-gap: 8px;
      margin-bottom: 10%;
      .form-item {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 5px 0px;
        gap: 4px;
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        .form-label {
          width: 100px;
          text-align: right;
          margin-right: 16px;
          color: var(----, #606266);
        }
        .form-content {
          color: var(----, #1d2129);
          flex: 1;
        }
      }
    }
  }
</style>
