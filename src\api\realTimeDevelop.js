import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 实时作业目录树
function getRealTimeDevelopTree() {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime-job/nav-directory/tree-list`,
        method: 'post',
    })
}

// 创建目录
const createRealTimeDevelopTree = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime-job/nav-directory/create`,
        method: 'post',
        data
    })
}

// 删除目录
const deleteRealTimeDevelopTree = (id) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime-job/nav-directory/delete/${id}`,
        method: 'post',
    })
}

// 重命名目录
const renameRealTimeDevelopTree = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime-job/nav-directory/rename`,
        method: 'post',
        data
    })
}

// 创建实时作业
const createRealTimeDevelop = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime-job/nav-directory/create-job`,
        method: 'post',
        data
    })
}

// 实时作业运行列表
const getRealTimeDevelopList = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/monitor/list`,
        method: 'post',
        data
    })
}

// 保存实时开发作业
const saveRealTimeDevelop = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/save`,
        method: 'post',
        data
    })
}

// 实时开发作业最新内容
const getRealTimeDevelopContent = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/current/content`,
        method: 'post',
        data,
    })
}

// 提交实时开发作业
const submitRealTimeDevelop = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/submit`,
        method: 'post',
        data
    })
}
// SQL检查
const sqlCheck = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/sql-check`,
        method: 'post',
        data
    })
}

// 测试运行实时开发作业
const testRunRealTimeDevelop = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/run-in-test`,
        method: 'post',
        data
    })
}

// 启动运行的实时开发作业
const startRealTimeDevelop = (id) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/monitor/restart-job/${id}`,
        method: 'post',
    })
}

// 停止运行的实时开发作业
const stopRealTimeDevelop = (id) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/monitor/stop-job/${id}`,
        method: 'post',
    })
}

// 实时作业运行内容
const getRealTimeDevelopRunContent = (id) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/monitor/job/run-content/${id}`,
        method: 'post',
    })
}

// 停止测试运行实时开发作业
const testStopRealTimeDevelop = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/stop-in-test`,
        method: 'post',
        data
    })
}

// 实时开发作业内容版本清单
const getRealTimeDevelopVersionLsit = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/version/list`,
        method: 'post',
        data
    })
}

// 实时作业运行日志
const getRealTimeLogList = (id) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/monitor/job/log/${id}`,
        method: 'post',
    })
}

// 实时开发作业测试运行日志
const getRealTimeDevelopLogList = (data) => {
    return request({
        url: `${URL.REALTIMEDEVELOP}/realtime/run-in-test-log`,
        method: 'post',
        data
    })
}

export default {
    getRealTimeDevelopTree,
    createRealTimeDevelopTree,
    deleteRealTimeDevelopTree,
    renameRealTimeDevelopTree,
    createRealTimeDevelop,
    getRealTimeDevelopList,
    saveRealTimeDevelop,
    getRealTimeDevelopContent,
    submitRealTimeDevelop,
    sqlCheck,
    testRunRealTimeDevelop,
    testStopRealTimeDevelop,
    getRealTimeDevelopVersionLsit,
    getRealTimeLogList,
    startRealTimeDevelop,
    stopRealTimeDevelop,
    getRealTimeDevelopRunContent,
    getRealTimeDevelopLogList,
}
