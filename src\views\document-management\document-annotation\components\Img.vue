<template>
    <div class="img-content">
        <!-- 显示pdf的canvas -->
        <div class="img-Preview" ref="previewPintRef">
            <div id="tag-container"></div>
            <!-- 标注工具 -->
            <div v-if="1" class="annotator-tools">
                <!-- 矩形、圆形、椭圆、多边形、拖动、点 -->
                <!-- <div class="annotator-tools-box" @click.stop="annotator.changeShape('rect')">
                    矩形
                </div>
                <div class="annotator-tools-box" @click.stop="annotator.changeShape('circle')">
                    圆形
                </div>
                <div class="annotator-tools-box" @click.stop="annotator.changeShape('ellipse')">
                    椭圆
                </div> -->
                <img :class="['annotator-tools-box', shape === 'polygon' ? 'active' : undefined]"
                    src="@/assets/img/markAudit/polygon.png" @click.stop="annotator?.changeShape('polygon')" />
                <img :class="['annotator-tools-box', shape === 'drag' ? 'active' : undefined]"
                    src="@/assets/img/markAudit/drag.png" @click.stop="annotator?.changeShape('drag')" />
                <img :class="['annotator-tools-box', shape === 'point' ? 'active' : undefined]"
                    src="@/assets/img/markAudit/point.png" @click.stop="annotator?.changeShape('point')" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { debounce } from 'lodash-es';
import ImageAnnotator from './ImageAnnotator copy.js';
const emit = defineEmits(['imageMarkUpdate', 'load-complete', 'click-polygon', 'drag-canvas', 'zoom-canvas'])
const previewPintRef = ref(null)
const props = defineProps({
    option: {
        type: Object,
        default() {
            return {}
        },
    },
    data: {
        type: Object,
        default() {
            return {}
        }
    },
    isDrawDisabled: {
        type: Boolean,
        default: false
    },
    isDragDisabled: {
        type: Boolean,
        default: false
    }
})

const { option } = toRefs(props)
const annotator = ref(null)
// 计算属性
const shape = computed(() => {
    return annotator.value?.shape || 'drag'
})
onMounted(() => {
    annotator.value = new ImageAnnotator(previewPintRef.value, option.value.value, {
        'load-complete': () => {
            emit('load-complete')
        },
        'imageMarkUpdate': (data, scale, imgOffset) => {
            emit('imageMarkUpdate', data, scale, imgOffset)
        },
        'click-polygon': (polygon, scale, imgOffset) => {
            const id = Object.values(props.data[1]).find(val => JSON.stringify(val.data) === JSON.stringify(polygon))?.id
            id && emit('click-polygon', id, polygon, scale, imgOffset)
        },
        // 拖动画布事件
        'drag-canvas': (event) => {
            emit('drag-canvas', event)
        },
        // 缩放画布事件
        'zoom-canvas': (event) => {
            emit('zoom-canvas', event)
        },
        isDrawDisabled: props.isDrawDisabled,
        isDragDisabled: props.isDragDisabled
    });
})

// 监听data变化，更新图片
watch(() => props.data, (newVal) => {
    // 判断newVal是否是空对象
    if (newVal && JSON.stringify(newVal) !== '{}') {
        const polygons = Object.values(newVal[1]).map(val => val.data);
        annotator.value.state.polygons = polygons;
        annotator.value.ctx.clearRect(0, 0, annotator.value.ctx.canvas.width, annotator.value.ctx.canvas.height);
        annotator.value.ctx.globalCompositeOperation = "destination-over";
        annotator.value.drawBackground({ ctx: annotator.value.ctx, img: annotator.value.img, width: annotator.value.canvasWidth, height: annotator.value.canvasHeight })
    }
}, { deep: true })

onUnmounted(() => {
    annotator.value.destroy();
})
// 暴露方法
defineExpose({
    // 禁用
    disable() {
        annotator.value.setDisabled({
            isDrawDisabled: true,
            isDragDisabled: true
        })
    },
    // 启用
    enable() {
        annotator.value.setDisabled({
            isDrawDisabled: false,
            isDragDisabled: false
        })
    },
    // 获取图片标注的位置
    getImageMark(id) {
        const data = Object.values(props.data[1]).find(val => val.id === id)?.data;
        return data?.map(item => annotator.value.imgToScreen(item))
    }
})

</script>
<style lang="scss" scoped>
.img-content {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex: 1 0 0;
    align-self: stretch;
    justify-content: center;
    // height: 100%;
    background: #f6f7fb;
    border-radius: 6px;
    user-select: text;

    .img-Preview {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;

        >img {
            width: 100%;
            -webkit-user-drag: none;
            -moz-user-drag: none;
            -ms-user-drag: none;
            user-drag: none;
        }

        >.annotator-tools {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: max-content;
            gap: 10px;
            padding: 10px;
            box-sizing: border-box;
            font-size: 16px;
            font-weight: 500;
            color: #f6f7fb;
            z-index: 100;

            .annotator-tools-box {
                cursor: pointer;
                width: 32px;
                padding: 2px;
                border-radius: 4px;

                &:hover {
                    background-color: rgba(64, 158, 255, .5);
                }

                &.active {
                    background-color: rgba(33, 52, 72, 0.2);
                }
            }
        }
    }
}
</style>