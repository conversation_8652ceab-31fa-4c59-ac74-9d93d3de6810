<template>
  <n-drawer
    v-model="state.showDrawer"
    title=""
    :size="720"
    :esc-key-closeable="false"
    :close-on-click-overlay="true"
    :before-close="closeFn"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">查看日志</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeFn" />
      </div>
      <div class="n-drawer-body-content">
        <pre class="log">{{ state.log }}</pre>
      </div>
      <div class="n-drawer-body-footer">
        <n-button v-if="props.nodeInfo.instanceId" @click="closeFn">取消</n-button>
        <n-button v-if="props.nodeInfo.instanceId" variant="solid" @click="downFn">下 载</n-button>
        <div v-else class="footer-btn" @click="closeFn">取 消</div>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import { collectMonitorLog, collectMonitorLogDown } from '@/api/dataManage'
  const state = reactive({
    log: '',
    showDrawer: true,
  })
  const emits = defineEmits(['seeLogFn'])
  const props = defineProps({
    nodeInfo: {
      type: Object,
      default: {},
    },
    isCanvas: {
      type: Boolean,
      default: false,
    },
  })

  const closeFn = () => {
    emits('seeLogFn', false)
  }
  const downFn = () => {
    collectMonitorLogDown({
      instanceId: props.nodeInfo.instanceId,
      sourceType: props.nodeInfo.sourceType,
    }).then((res) => {
      try {
        // 下载文件
        const blob = new Blob([res], {
          type: 'text/plain',
        })
        const link = document.createElement('a')
        let fileName = '日志.log'
        if (props.nodeInfo.name) {
          fileName = props.nodeInfo.name + '的日志.log'
        }
        link.download = fileName
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        document.body.appendChild(link)
        link.click()
        URL.revokeObjectURL(link.href)
        document.body.removeChild(link)
      } catch (e) {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            ElNotification({
              title: '提示',
              message: jsonData.message,
              type: 'error',
            })
          }
        }
      }
    })
  }
  onMounted(() => {
    collectMonitorLog({
      instanceId: props.nodeInfo.instanceId,
      sourceType: props.nodeInfo.sourceType,
    }).then((res) => {
      if (res.success) {
        state.log = res.data
      }
    })
  })
</script>
<style lang="scss" scoped>
  .n-drawer-body {
    height: 100%;
    .n-drawer-body-content {
      .log {
        color: #1d2129;
        font-size: 12px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      &::-webkit-scrollbar {
        width: 10px !important; // 横向滚动条
        height: 10px !important; // 纵向滚动条 必写
      }
    }
    .n-drawer-body-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px;
      padding: 0 16px;
      .footer-btn {
        width: 62px;
        height: 32px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        background: #1e89ff;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background-color: #6e9eff;
        }
      }
    }
  }
</style>
