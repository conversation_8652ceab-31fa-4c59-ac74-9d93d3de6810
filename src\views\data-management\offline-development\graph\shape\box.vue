<template>
  <div class="nodeBox">
    <div
      :class="{
        nodeBoxBorder: form.showState,
        nodeBgCss: true,
        green: form.state === 'SUCCESS',
        red: form.state === 'FAILURE' || form.state === 'STOP',
        gray: form.state === 'WAITING_TO_RUN',
        orange: form.state === 'RUNNING_EXECUTION',
      }"
    ></div>
    <div :class="'nodeAntvX6 ' + colorClass + (form.incomplete ? ' highlight' : '')">
      <img class="icon" :src="form.icon" />
      <div class="name" :title="form.name">{{ form.name }}</div>
      <svg
        v-if="!form.isVisualization"
        @click="showMenu = !showMenu"
        xmlns="http://www.w3.org/2000/svg"
        width="17"
        height="16"
        viewBox="0 0 17 16"
        fill="none"
      >
        <circle cx="8.5" cy="3.5" r="0.5" fill="currentColor" stroke="currentColor" />
        <circle cx="8.5" cy="8" r="0.5" fill="currentColor" stroke="currentColor" />
        <circle cx="8.5" cy="12.5" r="0.5" fill="currentColor" stroke="currentColor" />
      </svg>
      <div v-if="showMenu" class="list-menu">
        <div class="menu-item" @click="delNode">删除</div>
        <div class="menu-item" @click="openLog">查看日志</div>
      </div>
    </div>
  </div>
  <logPop ref="logPopRef" @success="emit('change')" />
</template>

<script>
  import logPop from './logPop.vue'
  export default {
    name: 'Box',
    components: { logPop },
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        showMenu: false,
        colorClass: '', // 颜色控制
        highlightClass: '', // 配置不完全高亮控制
        form: {
          nodeName: '',
          incomplete: false,
          isVisualization: false,
          showState: false,
          state: 'WAITING_TO_RUN',
          name: '',
          input: '',
          output: '',
          description: '',
          sql: '',
          sqlType: '0',
          taskType: '',
          icon: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.form = node.getData()

      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.form = current
      })
      document.addEventListener('click', this.handleDocumentClick)
    },
    beforeUnmount() {
      document.removeEventListener('click', this.handleDocumentClick)
    },
    methods: {
      delNode() {
        const graph = this.getGraph()
        const node = this.getNode()
        graph.removeNode(node)
      },
      handleDocumentClick(event) {
        // 判断点击是否在节点之外
        if (!this.isClickInsideNode(event)) {
          // 执行隐藏逻辑
          this.showMenu = false
        }
      },
      isClickInsideNode(event) {
        return event.target.className.baseVal ? false : true
      },
      openLog() {
        this.$refs.logPopRef.open(this.form)
      },
    },
  }
</script>

<style lang="scss">
  @import '@/styles/variables.scss';
  .nodeBox {
    width: auto;
    height: 34px;
    position: relative;
    background: var(--100, #fff);
    border-radius: 2px;
    border: 1px solid #c9cdd4;
    box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
    &:has(.nodeBoxBorder) {
      border: 1px solid transparent;
    }
    &:hover {
      background: #ebf4ff;
      svg {
        color: #1e89ff;
      }
    }
    .nodeBoxBorder {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 2px;
      &.green {
        border: 1px solid #2ca340;
      }
      &.red {
        border: 1px solid #f63838;
      }
      &.gray {
        border: 1px solid #8091b7;
      }
      &.orange {
        background: linear-gradient(90deg, #ff7d00 50%, transparent 0) repeat-x,
          linear-gradient(90deg, #ff7d00 50%, transparent 0) repeat-x,
          linear-gradient(0deg, #ff7d00 50%, transparent 0) repeat-y,
          linear-gradient(0deg, #ff7d00 50%, transparent 0) repeat-y;
        animation: linearGradientMove 0.3s infinite linear;
      }
      &.nodeBgCss {
        background-size: 8px 2px, 8px 2px, 2px 8px, 2px 8px;
        background-position: 0 0, 0 100%, 0 0, 100% 0;
      }
    }
    @keyframes linearGradientMove {
      100% {
        background-position: 8px 0, -8px 100%, 0 -8px, 100% 8px;
      }
    }
    .nodeAntvX6 {
      position: relative;
      display: inline-flex;
      gap: 8px;
      align-items: center;
      width: 100%;
      height: 34px;
      padding: 8px 6px 8px 10px;
      /* 小-二级菜单 */

      .name {
        width: calc(100% - 42px);
        max-width: 300px;
        overflow: hidden;
        color: var(----, rgba(0, 0, 0, 0.75));
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 22px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .list-menu {
        position: absolute;
        right: 0;
        bottom: -64px;
        width: 100px;
        background: #fff;
        border: 1px solid var(---, #c9cdd4);
        border-radius: 2px;

        /* 小-二级菜单 */
        //box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);
        .menu-item {
          height: 30px;
          padding: 0 12px;
          color: var(----, #1d2129);
          font-weight: 400;

          font-size: 14px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 30px;
          cursor: pointer;

          &:hover {
            background: #ebf4ff;
          }
        }
      }
    }
  }
</style>
