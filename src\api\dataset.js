import request from '@/utils/request'
import URL from '@/const/urlAddress'

export function groupTree(data) {
  return request({
    url: `${URL.QUALITY}/global/quality/assessment/results/latest`,
    method: 'post',
    loading: true,
    data,
  })
}

export const addGroup = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/group/save`,
    method: 'post',
    loading: true,
    data,
  })
}

export const updateGroup = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/group/update`,
    method: 'post',
    loading: true,
    data,
  })
}

export const deleteGroup = (id) => {
  return request({
    url: `${URL.MODEL}/dataset/group/delete/${id}`,
    method: 'post',
    loading: true,
  })
}

export function getDatasourceTable(val) {
  return request({
    url: `${URL.MODEL}/datasource/tables/${val}`,
    method: 'get',
    loading: true,
  })
}

export function post(url, data, showLoading = true, timeout = 60000, hideMsg) {
  return request({
    url: url,
    method: 'post',
    loading: showLoading,
    hideMsg,
    data,
  })
}

export function engineMode() {
  return request({
    url: '/lebi-preparation/datasource/engine/mode',
    method: 'get',
    loading: true,
  })
}

export function dbPreviewFields(data) {
  return request({
    url: `${URL.MODEL}/datasource/table/structure/${data.dataSourceId}/${data.name}`,
    method: 'get',
    loading: true,
  })
}

export function dbPreview(data) {
  return request({
    url: `${URL.MODEL}/datasource/table/data`,
    method: 'get',
    loading: true,
    params: data,
  })
}

export function listDatasource() {
  return request({
    url: `${URL.MODEL}/datasource/list`,
    loading: true,
    method: 'get',
  })
}

export function isKettleRunning(showLoading = true) {
  return request({
    url: '/lebi-preparation/dataset/group/isKettleRunning',
    method: 'post',
    loading: showLoading,
  })
}

export function getTable(id) {
  return request({
    url: `${URL.MODEL}/dataset/table/detail/${id}`,
    loading: false,
    method: 'post',
  })
}

export function getDataResponsibleList() {
  return request({
    url: '/lebi-preparation/dataset/table/get/' + id,
    loading: false,
    method: 'post',
    hideMsg,
  })
}

export const getExample = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/table/task/record`,
    loading: true,
    method: 'post',
    data: data,
  })
}

export const startNow = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/table/version/resync`,
    loading: true,
    method: 'post',
    data: data,
  })
}

export const newVerisonApi = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/table/version/create/${data.tableId}`,
    loading: true,
    method: 'post',
  })
}

export const getVersionList = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/table/version/list/${data.tableId}`,
    loading: true,
    method: 'post',
  })
}

export const deleteVerisonApi = (data) => {
  return request({
    url: `${URL.MODEL}/dataset/table/version/delete`,
    loading: true,
    method: 'post',
    data,
  })
}

export const deleteDataset = (id) => {
  return request({
    url: `${URL.MODEL}/dataset/table/delete/${id}`,
    loading: true,
    method: 'post',
  })
}
