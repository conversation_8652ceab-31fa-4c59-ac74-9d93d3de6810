<template>
  <div
    :class="{ 'level-tag': true, 'auto-width': props.autoWidth }"
    :style="{ color: props.color, background: props.bgColor, borderColor: props.borderColor }"
  >
    <slot></slot>
  </div>
</template>
<script setup>
  const props = defineProps({
    color: {
      type: String,
      default: '#fff',
    },
    bgColor: {
      type: String,
      default: '#fff',
    },
    borderColor: {
      type: String,
      default: '#fff',
    },
    autoWidth: {
      type: Boolean,
      default: false,
    },
  })
</script>
<style lang="scss" scoped>
  .level-tag {
    width: 40px;
    height: 22px;
    font-weight: 400;
    font-size: 12px;
    font-style: normal;
    line-height: 20px;
    text-align: center;
    border-style: solid;
    border-width: 1px;
    border-radius: 2px;
    &.auto-width {
      width: auto;
      padding: 0 8px;
    }
  }
</style>
