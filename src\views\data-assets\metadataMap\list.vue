<template>
  <div class="metadataMap">
    <div :class="{ 'metadataMap-content': true, isFullScreen: state.isFullScreen }">
      <div class="header">
        <div class="left">
          <img class="pic" src="@/assets/img/assets/metadataMap/topic.png" />
          元数据地图——管理、理解数据的利器
        </div>
        <div class="right">
          <img class="icon-search" src="@/assets/img/assets/metadataMap/search-gray.png" />
          <n-input class="ipt" placeholder="请输入关键字" v-model="state.searchKey" clearable />
          <n-button
            class="btn"
            variant="solid"
            color="primary"
            shape="round"
            :loading="state.loading"
            @click="methods.searchFn"
            >搜索</n-button
          >
        </div>
      </div>
      <div v-loading="state.loading" class="section">
        <div class="canvas" id="flowContent"><flow /></div
      ></div>
      <div class="footer">
        <div class="label"><div class="circle yellow"></div>数据源</div>
        <div class="label"><div class="circle blue"></div>数据库</div>
        <div class="label"><div class="circle blue1"></div>数据表</div>
        <div class="label"><div class="circle blue2"></div>API名称</div>
        <div class="label"><div class="circle blue3"></div>文件名称</div>
      </div>
      <div class="label-box">
        <div class="label" @click="methods.scaleFn('add')"
          ><SvgIcon class="icon-btn" icon="metadata-map-enlarge" />放大</div
        >
        <div class="label" @click="methods.scaleFn('cut')"
          ><SvgIcon class="icon-btn" icon="metadata-map-reduce" />缩小</div
        >
        <div class="label" @click="methods.scaleFn('default')"
          ><SvgIcon class="icon-btn" icon="metadata-map-reset" />还原</div
        >
        <div class="label" v-if="state.isFullScreen" @click="methods.fullScreenFn(false)"
          ><SvgIcon class="icon-btn" icon="metadata-map-out" />退出</div
        >
        <div class="label" v-else @click="methods.fullScreenFn(true)"
          ><SvgIcon class="icon-btn" icon="metadata-map-full" />全屏</div
        >
        <!--        <div class="label" @click="methods.goJump({ name: 'metadataMapMeatList' })"-->
        <!--          ><SvgIcon class="icon-btn" icon="metadata-map-list" />列表</div-->
        <!--        >-->
      </div>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, onBeforeUnmount, reactive, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import G6 from '@antv/g6'
  import g6FlowGraph from './g6'
  import api from '@/api/index'
  const store = useStore()
  const router = useRouter()
  // 获取当前组件实例
  const state = reactive({
    loading: false,
    searchKey: '',
    isFullScreen: false,
    graph: {},
    bindFlag: null,
    routerFlag: null,
    mapData: {
      nodes: [],
      edges: [],
    },
    scaleNum: 1,
  })

  const methods = {
    // 放大缩小还原
    scaleFn(type) {
      if (type === 'add') {
        state.scaleNum += 0.1
      } else if (type === 'cut') {
        if (state.scaleNum > 0) {
          state.scaleNum -= 0.1
        }
      }
      if (type === 'default') {
        state.scaleNum = 1
      }
      state.graph.zoomTo(state.scaleNum)
      state.graph.fitCenter()
    },
    // 搜索
    searchFn() {
      if (state.searchKey) {
        state.loading = true
        let data = {
          name: state.searchKey,
          // withChildren: true
        }
        api.assets
          .metadataMapSearch(data)
          .then((res) => {
            if (res.success) {
              state.mapData.nodes.forEach((val) => {
                val.checked = false
                res.data.forEach((item) => {
                  if (val.uid === item.uid) {
                    val.checked = true
                  }
                })
              })
              state.mapData.edges.forEach((val) => {
                val.checked = false
                res.data.forEach((item) => {
                  if (val.preUid === item.uid) {
                    val.checked = true
                  }
                })
              })
              state.graph.clear()
              state.scaleNum = 0.1
              methods.setMapData(true)
            }
          })
          .catch(() => {
            state.loading = false
          })
      } else {
        state.mapData.nodes.forEach((val) => {
          val.checked = true
        })
        state.mapData.edges.forEach((val) => {
          val.checked = true
        })
        state.graph.clear()
        state.scaleNum = 1
        methods.setMapData(true)
      }
    },
    // 初始化创建画布
    initFn() {
      state.graph = g6FlowGraph.init('flowContent')
      state.graph.on('afterlayout', () => {
        if (state.bindFlag) {
          clearTimeout(state.bindFlag)
          state.bindFlag = null
        }
        state.bindFlag = setTimeout(() => {
          state.loading = false
          state.graph.fitCenter()
        }, 60)
      })

      methods.assetsLineage()
    },
    // 计算尺寸
    resizeFn() {
      const { width, height } = methods.getContainerSize()
      state.graph.changeSize(width, height)
      state.graph.layout(true)
    },
    // 获取画布容器大小
    getContainerSize() {
      return {
        width: document.getElementById('flowContent').offsetWidth,
        height: document.getElementById('flowContent').offsetHeight,
      }
    },
    // 根据数据表追溯关系
    assetsLineage() {
      let params = {}
      state.loading = true
      api.assets
        .metadataMapList(params)
        .then((res) => {
          if (res.success) {
            state.mapData.nodes = res.data.nodeList.filter((val) => {
              val.id = val.uid
              val.checked = true
              if (val.type === 'DATASOURCE') {
                val.size = 84
              } else {
                val.size = 74
              }
              return val
            })
            state.mapData.edges = res.data.relationList.map((val) => {
              val.source = val.preUid
              val.target = val.postUid
              val.checked = true
              return val
            })
            methods.setMapData()
          }
        })
        .catch(() => {
          state.loading = false
        })
    },
    // 切换画布数据
    setMapData() {
      const data = methods.getModelFromOriginData(state.mapData)
      state.graph.data(data)
      state.graph.render()
      const nodes = state.graph.getNodes()
      // 遍历节点实例，将所有节点提前。
      nodes.forEach((node) => {
        node.toFront()
      })
      state.graph.zoomTo(state.scaleNum)
      state.graph.on('node:mouseover', (evt) => {
        const { item } = evt
        const data = item.getModel().data
        state.graph.setItemState(item, 'hover', true)
        state.graph.updateItem(item, {
          label: data.name,
        })
      })
      state.graph.on('node:mouseout', (evt) => {
        const { item } = evt
        const data = item.getModel().data
        state.graph.setItemState(item, 'hover', false)
        state.graph.updateItem(item, {
          label: data.name.length > 20 ? data.name.slice(0, 20) + '...' : data.name,
        })
      })
      // 点击指标跳转详情
      state.graph.on('node:click', (evt) => {
        const { item } = evt
        let nodeData = item.getModel()
        if (nodeData.data.type !== 'COLUMN') {
          if (
            nodeData.data.type === 'FILE' ||
            nodeData.data.type === 'API' ||
            (nodeData.data.type === 'DATASOURCE' && nodeData.data.category === 'API')
          ) {
            router.push({
              name: 'metadataMapSee',
              query: { id: nodeData.data.uid, type: nodeData.data.type },
            })
          } else {
            router.push({
              name: 'metadataMapDetail',
              query: { id: nodeData.data.uid, type: nodeData.data.type },
            })
          }
        }
      })
    },
    // 转化数据
    getModelFromOriginData(originData) {
      const data = {
        nodes: [],
        edges: [],
      }
      originData.nodes.forEach((item) => {
        data.nodes?.push({
          id: item.id,
          data: item,
          zIndex: 2,
          size: item.type === 'DATASOURCE' ? 84 : 74,
          style: {
            opacity: item.checked ? 1 : 0.1,
            fill: methods.getNodeColor(item).backgroundColor,
            stroke: methods.getNodeColor(item).backgroundColor,
            lineWidth: 2,
            cursor: item.type === 'COLUMN' ? 'default' : 'pointer',
          },
          label: item.name.length > 20 ? item.name.slice(0, 20) + '...' : item.name,
          labelCfg: {
            offset: item.type === 'DATASOURCE' ? 4 : 2,
            style: {
              opacity: item.checked ? 1 : 0.1,
              fontSize: item.type === 'DATASOURCE' ? 14 : 12,
            },
          },
        })
      })
      originData.edges.forEach((item) => {
        data.edges?.push({
          source: item.source,
          target: item.target,
          type: 'line',
          style: {
            stroke: methods.getNodeColor(item).edgColor,
            fill: methods.getNodeColor(item).edgColor,
            lineWidth: 1,
            fillOpacity: 0,
            opacity: item.checked ? 1 : 0.1,
            endArrow: {
              path: G6.Arrow.triangle(10, 10, 2),
              d: 2,
              fill: methods.getNodeColor(item).edgColor,
              stroke: methods.getNodeColor(item).edgColor,
            },
          },
          zIndex: 1,
        })
      })
      return data
    },
    // 获取Node颜色
    getNodeColor(node) {
      if (node.type === 'DATASOURCE' || node.preType === 'DATASOURCE') {
        return {
          edgColor: node.checked ? 'rgba(255,159,25,0.8)' : 'rgba(255,159,25,0.5)',
          backgroundColor: '#FF9F19',
        }
      } else if (node.type === 'DATABASE' || node.preType === 'DATABASE') {
        return {
          edgColor: node.checked ? 'rgba(68,125,253,0.8)' : 'rgba(68,125,253,0.5)',
          backgroundColor: '#447DFD',
        }
      } else if (node.type === 'TABLE' || node.preType === 'TABLE') {
        return {
          edgColor: node.checked ? 'rgba(110,158,255,0.8)' : 'rgba(110,158,255,0.5)',
          backgroundColor: '#6E9EFF',
        }
      } else if (node.type === 'API' || node.preType === 'API') {
        return {
          edgColor: node.checked ? 'rgba(25,156,255,0.8)' : 'rgba(25,156,255,0.5)',
          backgroundColor: '#199CFF',
        }
      } else if (node.type === 'FILE' || node.preType === 'FILE') {
        return {
          edgColor: node.checked ? 'rgba(42,88,213,0.8)' : 'rgba(42,88,213,0.5)',
          backgroundColor: '#2A58D5',
        }
      } else if (node.type === 'COLUMN' || node.preType === 'COLUMN') {
        return {
          edgColor: node.checked ? 'rgba(191, 217, 255, 0.8)' : 'rgba(191, 217, 255, 0.5)',
          backgroundColor: '#BFD9FF',
        }
      }
    },
    // 全屏切换
    fullScreenFn(flag) {
      state.isFullScreen = flag
      nextTick(() => {
        methods.resizeFn()
      })
    },
    //跳转
    goJump(item) {
      let _router = { name: item.name }
      if (item.query) {
        _router = { name: item.name, query }
      }
      router.push(_router)
    },
  }

  onBeforeUnmount(() => {
    g6FlowGraph.destroy()
  })

  onMounted(() => {
    methods.initFn()
  })
</script>
<script>
  export default {
    name: 'MetadataMapList',
  }
</script>
<style lang="scss">
  .metadata-map-node {
    width: 74px;
    height: 74px;
    line-height: 74px;
    border-radius: 50%;
    padding: 0 10px;
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    opacity: 0.1;
    user-select: none;
    background: linear-gradient(165deg, #2b84fb 0%, #4752e1 100%);
    box-shadow: 0 0 10px 8px rgba(46, 131, 250, 0.14), inset 0 1px 6px 2px #4aacff;
    .text {
      font-size: 12px;
      color: #fff;
      line-height: 18px;
      font-weight: bold;
      text-align: center;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &:before {
      content: '';
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      background-color: transparent;
      border-radius: 50%;
      position: absolute;
      left: -10px;
      top: -10px;
      transition: all linear 0.1s;
      &.isSee {
        opacity: 0;
        &.isMainBody {
          opacity: 1;
        }
      }
    }
    &.yellow {
      width: 84px;
      height: 84px;
      line-height: 90px;
      background: linear-gradient(165deg, #eab33f 0%, #cc8635 100%);
      box-shadow: 0 0 10px 8px rgba(255, 210, 121, 0.14), inset 0 1px 6px 2px #fde77c;
      font-size: 14px;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #fde77c;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(238, 175, 65, 0.2);
        }
      }
    }
    &.purple {
      background: linear-gradient(165deg, #bb5afa 0%, #9733e5 100%);
      box-shadow: 0 0 10px 8px rgba(200, 104, 255, 0.14), inset 0 1px 6px 2px #e898ff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #e898ff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(146, 97, 255, 0.2);
        }
      }
    }
    &.blue {
      background: linear-gradient(165deg, #2b84fb 0%, #4752e1 100%);
      box-shadow: 0 0 10px 8px rgba(46, 131, 250, 0.14), inset 0 1px 6px 2px #4aacff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #4aacff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(53, 115, 242, 0.2);
        }
      }
    }
    &.green {
      background: linear-gradient(165deg, #00c8df 0%, #0891b0 100%);
      box-shadow: 0 0 10px 8px rgba(0, 197, 221, 0.14), inset 0 1px 6px 2px #00d8dd;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #00d8dd;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(2, 190, 214, 0.2);
        }
      }
    }
    &.checked {
      opacity: 1;
    }
  }
  foreignObject {
    overflow: visible;
  }
</style>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .metadataMap {
    width: 100%;
    height: calc(100vh - 50px);
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    &-content {
      width: 100%;
      height: 100%;
      position: relative;
      //background-image: url('@/assets/img/assets/metadataMap/bg.png');
      //background-position: center;
      //background-size: cover;
      //background-repeat: no-repeat;
      background: #fafafa;
      border-radius: 4px;
      box-sizing: border-box;
      border: 2px solid #fff;
      &.isFullScreen {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
      }
      .header {
        padding: 20px 20px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: absolute;
        left: 0;
        top: 0;
        box-sizing: border-box;
        width: 100%;
        z-index: 3;
        .left {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          .pic {
            width: 18px;
            height: 18px;
            margin-right: 6px;
          }
        }
        .right {
          width: 400px;
          height: 42px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          background: #fff;
          border-radius: 22px;
          border: 1px solid #e1e1e1;
          box-sizing: border-box;
          padding-left: 12px;
          &:hover {
            border: 1px solid $themeBlue;
          }
          .icon-search {
            width: 18px;
            height: 18px;
            color: #666666;
          }
          .ipt {
            width: calc(100% - 80px);
            :deep(.nancalui-input__wrapper) {
              background-color: transparent;
              border: none;
              padding: 0 6px;
              .nancalui-input__inner {
                color: #000;
                font-size: 14px;
              }
            }
          }
          .btn {
            width: 60px;
            height: 38px;
          }
        }
      }
      .section {
        width: 100%;
        height: 100%;
        .canvas {
          width: 100%;
          height: 100%;
          overflow: hidden;
        }
        :deep(.x6-graph-scroller) {
          overflow: hidden;
          &::-webkit-scrollbar {
            width: 0; // 横向滚动条
            height: 0; // 纵向滚动条 必写
          }

          // 滚动条的滑块
          &::-webkit-scrollbar-thumb {
            background-color: rgb(177, 176, 176);
            border-radius: 2px;
          }
        }
        :deep(.el-loading-mask) {
          background-color: rgba(0, 0, 0, 0.7);
        }
      }
      .label-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        flex-direction: column;
        padding: 12px 0;
        width: 44px;
        height: 256px;
        background-color: #ffffff;
        border-radius: 8px;
        position: absolute;
        border: 1px solid #e1e1e1;
        box-sizing: border-box;
        right: 20px;
        top: 0;
        bottom: 0;
        margin: auto;
        z-index: 3;
        .label {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #666666;
          font-size: 14px;
          cursor: pointer;
          .icon-btn {
            width: 18px;
            height: 18px;
            color: #000;
            font-size: 18px;
            margin-bottom: 2px;
          }
          &:hover {
            color: $themeBlue;
            .icon-btn {
              color: $themeBlue;
            }
          }
        }
      }
      .footer {
        position: absolute;
        bottom: 20px;
        right: 20px;
        height: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .label {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 16px;
          color: #666666;
          font-size: 12px;
          .circle {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
            &.yellow {
              background-color: #ff9f19;
            }
            &.purple {
              background-color: #9261ff;
            }
            &.blue {
              background-color: $themeBlue;
            }
            &.blue1 {
              background-color: #6e9eff;
            }
            &.blue2 {
              background-color: #199cff;
            }
            &.blue3 {
              background-color: #2a58d5;
            }
          }
        }
      }
    }
  }
</style>
