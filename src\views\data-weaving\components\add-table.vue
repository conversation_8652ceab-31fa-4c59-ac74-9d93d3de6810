<template>
  <n-drawer v-model="visiable" size="650" title="新建表" @close="onClose(false)">
    <n-form :data="formData" class="nc-p-16">
      <div class="nc-m-b-16 nc-flex" style="font-size: 22px">
        <svgIcon icon="icon-card-rerun" class="nc-m-r-8" />
        <svgIcon icon="icon-ddl" />
      </div>
      <n-form-item field="code">
        <n-textarea
          v-model="formData.code"
          resize="none"
          class="script-textarea scroll-bar-style"
          color="primary"
          placeholder="请输入SQL语句"
        />
      </n-form-item>
    </n-form>
    <div class="container-footer">
      <div class="my-appliction-right">
        <n-button style="margin-left: 8px" plain @click="onClose()">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onClose(true)">确定</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
  })
  const formData = reactive({
    code: `CREATE TABLE table_name (

      column1 datatype [NOT NULL] [DEFAULT default_value],

      column2 datatype [NOT NULL] [DEFAULT default_value],

      columnN datatype [NOT NULL] [DEFAULT default_value],

      PRIMARY KEY (column1, column2, ... columnN),

      UNIQUE KEY unique_key_name (column1, column2, ... columnN),

      FOREIGN KEY (column1, column2, ... columnN)

          REFERENCES parent_table (column1, column2, ... columnN)

          ON DELETE CASCADE | ON UPDATE CASCADE,

      INDEX index_name (column1, column2, ... columnN),

  ) ENGINE=storage_engine;`,
  })
  const emit = defineEmits(['update:modelValue', 'sure'])
  function onClose(sure = false) {
    emit('update:modelValue', false)
    if (sure) {
      emit('sure')
    }
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    (val) => {
      visiable.value = val
    },
  )
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  :deep(.nancalui-form__item--horizontal) {
    &.description-textarea {
      width: 1060px;
    }
    .script-textarea {
      height: calc(100vh - 200px);
      &::placeholder {
        line-height: 363px;
        text-align: center;
      }
    }
  }
</style>
