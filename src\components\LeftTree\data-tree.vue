<template>
  <div :class="isHideTree ? 'tree hide' : 'tree'">
    <div v-show="!isHideTree" class="tree-search">
      <el-form>
        <el-form-item>
          <el-input v-model="filterText" size="small" placeholder="搜索" clearable />
        </el-form-item>
      </el-form>
    </div>
    <div class="tree-btn">
      <n-button variant="solid" @click.prevent="updateFn({ id: null }, false)">
        <SvgIcon icon="icon_list_add_n" class="icon-add-svg" />添加</n-button
      >
    </div>
    <div v-show="!isHideTree" class="tree-content-box scroll-bar-style">
      <div class="tree-content">
        <el-tree
          ref="tree"
          class="filter-tree"
          :show-checkbox="showCheckbox"
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="expand"
          :check-on-click-node="true"
          node-key="id"
          default-expand-all
          :filter-node-method="filterNode"
          @node-click="clickFn"
          @check="changeFn"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <!--判断是否展示图标-->
              <span>
                <template v-if="showLeftIcon">
                  <img
                    v-if="data.children"
                    class="tree-node-icon"
                    src="/src/assets/img/tree/icon-parent.png"
                  />
                  <img
                    v-else
                    class="tree-node-icon child"
                    src="/src/assets/img/tree/icon-child.png"
                  />
                </template>
                <!--节点名称-->
                <span
                  v-if="data.children"
                  :class="showControl ? 'short tree-node-name parent' : 'tree-node-name parent'"
                  >{{ node.label }}</span
                >
                <span v-else :class="showControl ? 'short tree-node-name' : 'tree-node-name'">{{
                  node.label
                }}</span>
              </span>

              <!--父节点操作按钮-->
              <el-popover
                v-if="data.showAdd && data.children"
                v-model="data.showControl"
                class="tree-node-control"
                trigger="hover"
                placement="bottom-start"
              >
                <template #reference>
                  <n-popover class="item" content="更多" trigger="hover" :position="['bottom']">
                    <SvgIcon
                      slot="reference"
                      icon="tree-more-icon"
                      class="tree-node-control-operation hover-show"
                      @click.stop="preventFn"
                    />
                  </n-popover>
                </template>
                <template #default>
                  <span
                    v-if="data.showAdd"
                    class="el-popover-tree-name"
                    @click.stop="updateFn(data, false)"
                  >
                    <SvgIcon icon="tree-add-icon" class="el-popover-tree-name-operation" />
                    添加{{ dialogTitle }}</span
                  >
                  <span
                    v-if="data.showEdit"
                    class="el-popover-tree-name"
                    @click.stop="updateFn(data, true)"
                  >
                    <SvgIcon icon="tree-edit-icon" class="el-popover-tree-name-operation" />
                    编辑</span
                  >
                  <span v-if="data.showDel" class="el-popover-tree-name" @click.stop="delFn(data)">
                    <SvgIcon icon="tree-del-icon" class="el-popover-tree-name-operation" />
                    删除</span
                  >
                </template>
              </el-popover>
              <!--子节点操作按钮-->
              <el-popover
                v-if="!data.children"
                class="tree-node-control"
                trigger="hover"
                placement="bottom-start"
              >
                <template #reference>
                  <n-popover class="item" content="更多" trigger="hover" :position="['bottom']">
                    <SvgIcon
                      slot="reference"
                      icon="tree-more-icon"
                      class="tree-node-control-operation hover-show"
                      @click.stop="preventFn"
                    />
                  </n-popover>
                </template>
                <template #default>
                  <span
                    v-if="data.showAdd"
                    class="el-popover-tree-name"
                    @click.stop="updateFn(data, false)"
                  >
                    <SvgIcon icon="tree-add-icon" class="el-popover-tree-name-operation" />
                    添加{{ childTitle }}</span
                  >
                  <span
                    v-if="data.showEdit"
                    class="el-popover-tree-name"
                    @click.stop="updateFn(data, true)"
                  >
                    <SvgIcon icon="tree-edit-icon" class="el-popover-tree-name-operation" />
                    编辑</span
                  >
                  <span v-if="data.showDel" class="el-popover-tree-name" @click.stop="delFn(data)">
                    <SvgIcon icon="tree-del-icon" class="el-popover-tree-name-operation" />
                    删除</span
                  >
                </template>
              </el-popover>
            </span>
          </template>
        </el-tree>
      </div>
    </div>
    <div :class="isHideTree ? 'knob hide' : 'knob'" @click.prevent="hideFn">
      <img v-if="isHideTree" class="pic" src="/src/assets/img/tree/icon-arrow-right.png" />
      <img v-else class="pic" src="/src/assets/img/tree/icon-arrow-left.png" />
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'

  export default {
    name: 'PublicTree',
    // data：树基本数据数据,如 [{label:'节点',id:1,children:[]}];
    // treeAttrData：树的默认属性数据，如{showCheckbox:true,};
    // checkedNodes: 设置已选节点,如 [{id:1},{id:2}]
    props: ['data', 'treeAttrData', 'checkedNodes'],
    data() {
      return {
        isHideTree: false, // 是否左侧隐藏树
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        expand: true, // 是否在点击节点的时候展开或者收缩节点
        showCheckbox: false, // 是否显示选择框
        showControl: false, // 显示控制按钮
        showLeftIcon: false, // 显示左侧icon图标
        parentControl: '', // 父元素要展示的控制项1.增加 2.编辑 3.删除
        childControl: '', // 子元素要展示的控制项1.增加 2.编辑 3.删除
        treeData: [],
        showAddDialog: false, // 展示弹窗
        isEditDialog: false, // 判断是否是编辑状态
        dialogTitle: '目录', // 弹窗顶部名称
        childTitle: '', // 二级父类元素新增名称
        checkItem: {}, // 选中的节点
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val)
      },
      data(val) {
        let data = [...val]
        if (data.length === 0) {
          data = [
            {
              type: 'ROOT',
              id: '',
              children: [],
              name: '全部',
              description: '',
            },
          ]
        }
        this.treeData = this.reformData(data)
      },
      checkedNodes(val) {
        this.$refs.tree.setCheckedNodes(val)
      },
    },
    mounted() {
      if (this.treeAttrData) {
        // 判断是否需要修改的属性
        for (let obj in this.treeAttrData) {
          if ({}.hasOwnProperty.call(this.treeAttrData, obj)) {
            this[obj] = this.treeAttrData[obj]
          }
        }
      }
      let data = [...this.data]
      // if (data.length === 0) {
      //   data = [
      //     {
      //       type: 'ROOT',
      //       id: '',
      //       children: [],
      //       name: '全部',
      //       description: '',
      //     },
      //   ]
      // }
      this.treeData = this.reformData(data)
    },
    methods: {
      // 阻止默认事件
      preventFn() {
        return false
      },
      // 隐藏树事件
      hideFn() {
        this.isHideTree = !this.isHideTree
      },
      // 对tree数据改造
      reformData(data) {
        data.forEach((val) => {
          val.showControl = false
          val.showAdd = false
          val.showEdit = false
          val.showDel = false
          if (val.name) {
            val.label = val.name
          }
          let nodeObj = 'childControl' // 父元素的可操作对象parentControl,子元素可操作对象childControl
          if (val.children) {
            nodeObj = 'parentControl'
          }
          if (this[nodeObj].indexOf('1') !== -1) {
            // 判断是否有添加操作
            val.showAdd = true
          }
          if (this[nodeObj].indexOf('2') !== -1) {
            // 判断是否有编辑操作
            val.showEdit = true
          }
          if (this[nodeObj].indexOf('3') !== -1) {
            // 判断是否有删除操作
            val.showDel = true
          }
          if (val.children) {
            this.reformData(val.children)
          }
        })
        return data
      },
      // 遍历子节点对象属性名称
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      // 点击单个节点事件
      clickFn(item) {
        this.$emit('clickNode', item)
      },
      // 选中状态发生变化事件
      changeFn(item) {
        let data = {
          checkItem: item, // 当前选中的节点对象
          checkArr: this.$refs.tree.getCheckedNodes(), // 所有选中的节点对象
        }
        this.$emit('treeCheckNode', data)
      },
      // 点击添加或修改节点事件
      updateFn(item, isEdit) {
        // 判断是提交
        this.$emit('treeUpdateNode', {
          checkItem: item,
          isEdit,
        })
      },
      // 点击删除节点事件
      delFn(item) {
        this.$emit('treeDelNode', item)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tree {
    width: 240px;
    height: 100%;
    // padding: 0 14px;
    box-sizing: border-box;
    // overflow: hidden;
    position: relative;
    transition: all 0.3s;
    // border: 2px solid #fff;
    border-right: 1px solid #eeeeee;

    &.hide {
      width: 1px;
      padding: 0;
      margin-left: -2px;
      background-color: #fff;
      border-right: none;
    }

    .knob {
      position: absolute;
      width: 12px;
      height: 80px;
      background-color: #ecf3ff;
      top: 0;
      bottom: 0;
      right: -12px;
      z-index: 9;
      margin: auto;
      border-radius: 0 9px 9px 0;
      cursor: pointer;

      &.hide {
        border-radius: 0 9px 9px 0;
        background-color: #ecf3ff;
      }

      .pic {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        margin: auto;
        width: 7px;
        height: 10px;
      }
    }

    &-search {
      // padding-top: 10px;
      padding: 10px 14px 0;

      :deep(.el-form-item) {
        margin-bottom: 10px;
      }
    }
    &-btn {
      margin-left: 14px;
    }

    &-content-box {
      position: relative;
      height: calc(100% - 60px);
      // width: 211px;
      margin-right: 1px;
      box-sizing: border-box;
      padding-left: 14px;
      padding-right: 14px;
      // padding: 0 14px;
      // overflow-y: auto;
      overflow-y: overlay; // 出现滚动条不会影响宽度
    }

    &-content {
      // height: calc(100% - 60px);
      // overflow-y: auto;
      // box-sizing: border-box;
      padding-bottom: 10px;
      width: 100%;
      height: 100%;

      .tree-node {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;
        // &:hover {
        //   background-color: #e7edf6;
        // }
        &-icon {
          width: 20px;
          height: 20px;
          margin-right: 4px;
          vertical-align: middle;

          &.child {
            width: 18px;
            height: 18px;
          }
        }

        &-name {
          display: inline-block;
          vertical-align: middle;
          font-size: 12px;
          color: #333333;
          max-width: 132px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .parent {
          color: #000;
          font-size: 12px;
        }

        .short {
          max-width: 110px;
        }

        &:hover {
          .tree-node-control {
            opacity: 1;
          }
        }

        &-control {
          opacity: 0;
          display: inline-block;
          margin-right: 8px;
          text-align: center;

          &-operation {
            display: inline-block;
            vertical-align: middle;
            font-size: 12px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 4px;
            margin-right: 4px;

            &:last-of-type {
              margin-right: 0;
            }

            &:hover {
              color: $themeBlue;
              background-color: #e2e6ec;
            }
          }
        }
      }
    }

    :deep(.el-tree) {
      background-color: inherit;
    }

    :deep(.el-tree-node__content) {
      height: 30px;
      line-height: 32px;
      border-radius: 4px;

      .hover-show {
        opacity: 0;
      }

      &:hover {
        background-color: #f7f8fa;

        .hover-show {
          opacity: 1;
        }
      }
    }

    :deep(.el-tree-node.is-current) {
      & > .el-tree-node__content {
        background-color: #eff1f5;

        .tree-node-name {
          font-weight: bolder;
        }
      }
    }

    :deep(.el-tree-node__children) {
      overflow: inherit;
    }

    :deep(.el-textarea__inner) {
      background-color: #fff;
      padding: 8px 10px;
    }
  }
</style>
<style lang="scss">
  @import '@/styles/variables.scss';
  .el-popover {
    display: flex;
    flex-direction: column;
    padding: 4px;

    .el-popover-tree-name {
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      cursor: pointer;

      &-operation {
        font-size: 12px;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        border-radius: 4px;
      }

      &:hover {
        background: #f7f8fa;
        color: $themeBlue;
      }
    }
  }
</style>
