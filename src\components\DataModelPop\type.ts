export interface DataSourceItem {
  id: string
  dataTable: string
  datasource: string
  createBy: string
  fieldType: string
  createByName: string
  createTime: string
  dataStructureType: string
  dataBase: string
  description: string
  host: string
  name: string
  password: string
  port: string
  type: string
  userName: string
  status?: boolean
}

export interface DataSourceType {
  code: string
  name: string
}

export const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]
