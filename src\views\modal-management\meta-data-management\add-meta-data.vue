<template>
  <div class="container" style="padding: 10px 10px 13px 10px">
    <div class="container-box">
      <!-- debug -->
      <div class="form">
        <p class="page_header_common_style">
          <!-- <img src="@img/borderLeft.png" alt="" /> -->
          <span class="need_smallcube__title"> {{ $route.query.title }}</span>
        </p>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-position="right"
          label-width="66px"
        >
          <el-form-item label="中文名称:" prop="cnName">
            <el-input
              v-model="ruleForm.cnName"
              clearable
              :disabled="isDisabled || standardDis"
              size="small"
              placeholder="请输入中文名"
              maxlength="30"
              style="width: 600px"
            />
          </el-form-item>
          <el-form-item label="英文名称:" prop="name">
            <el-input
              v-model="ruleForm.name"
              clearable
              :disabled="isDisabled || standardDis"
              size="small"
              placeholder="请输入英文名"
              maxlength="256"
              style="width: 600px"
            />
          </el-form-item>

          <el-form-item label="字段类型:" prop="fieldType">
            <el-input
              v-if="isDetail"
              v-model="ruleForm.fieldTypeName"
              clearable
              :disabled="isDisabled || standardDis"
              size="small"
              placeholder="请选择字段类型"
              style="width: 600px"
            />
            <el-select
              v-else
              v-model="ruleForm.fieldType"
              size="small"
              placeholder="请选择"
              clearable
              style="width: 600px"
              :disabled="isDisabled || standardDis"
            >
              <el-option
                v-for="item in fieldList"
                :key="item.name"
                :label="item.cnName"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="字段长度:" prop="fieldLength">
            <el-input
              v-model="ruleForm.fieldLength"
              type="phone"
              clearable
              :disabled="isDisabled || standardDis"
              size="small"
              placeholder="请输入"
              style="width: 600px"
            />
          </el-form-item>
          <el-form-item label="描述信息:">
            <el-input
              v-model="ruleForm.description"
              type="textarea"
              :autosize="{ minRows: 3 }"
              maxlength="200"
              show-word-limit
              clearable
              :disabled="isDisabled || standardDis"
              size="small"
              placeholder="请输入"
              style="width: 600px"
            />
          </el-form-item>
          <el-form-item label="创建时间" v-if="isShow">
            <el-input
              v-model="ruleForm.createTime"
              clearable
              :disabled="isDisabled || isEdit"
              size="small"
              placeholder="请输入"
              style="width: 600px"
            />
          </el-form-item>
          <el-form-item label="创建人" v-if="isShow">
            <el-input
              v-model="ruleForm.createByName"
              clearable
              :disabled="isDisabled || isEdit"
              size="small"
              placeholder="请输入"
              style="width: 600px"
            />
          </el-form-item>
        </el-form>
      </div>
      <!-- <img src="@img/<EMAIL>" alt="" /> -->
    </div>
    <div class="footer">
      <n-button
        v-if="!isDisabled"
        size="sm"
        variant="solid"
        color="primary"
        :loading="loading"
        @click.stop.prevent="submit('ruleForm')"
        >保存
      </n-button>
      <n-button v-if="!isDisabled" size="sm" @click.stop.prevent="cancel">取消</n-button>
      <n-button v-if="isDisabled" size="sm" type="primary" @click.stop.prevent="cancel"
        >返回</n-button
      >
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { checkCName, checkName, checkName256, checkNum } from '@/utils/validate'

  export default {
    name: 'AddMetaData',

    data() {
      return {
        loading: false,
        ruleForm: {
          // projectName: 'projectcode1',
          cnName: '',
          name: '',
          isStandard: false,
          standardId: null,
          fieldLength: null,
          fieldType: '',
          fieldTypeName: '',
          // subjectId: '',
          description: '',
          createByName: '',
          createTime: '',
        },

        rules: {
          cnName: [{ required: !this.isDisabled, trigger: 'blur', validator: checkCName }],
          name: [{ required: !this.isDisabled, validator: checkName256, trigger: 'blur' }],
          standardId: [{ required: !this.isDisabled, message: '请选择标准', trigger: 'change' }],
          fieldType: [{ required: !this.isDisabled, message: '请选择字段类型', trigger: 'change' }],
          fieldLength: [
            {
              required: !this.isDisabled,
              message: '请输入字段长度',
              validator: checkNum,
              trigger: 'blur',
            },
          ],
          type: [{ required: !this.isDisabled, message: '请选择', trigger: 'change' }],
          // subjectId: [{ required: !this.isDisabled, message: '请选择', trigger: 'change' }],
        },
        fieldList: [],
        standardList: [],
        projectList: [],
        isDisabled: false,
        standardDis: false,
      }
    },
    computed: {
      isEdit() {
        return this.$route.query.type === 'EDIT'
      },
      isShow() {
        return this.$route.query.type !== 'ADD'
      },
      isDetail() {
        return this.$route.query.type === 'DETAIL'
      },
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },

    created() {
      this.isDisabled = this.$route.query.type === 'DETAIL' ? true : false
      this.init()
    },
    methods: {
      // checkNum(rule, value, callback) {
      //   if (!value) {
      //     return new Error('请输入字段长度')
      //   } else {
      //     if (Number(value) < 1) {
      //       callback(new Error('不能小于1'))
      //     } else if (isNaN(Number(value))) {
      //       callback(new Error('请输入数字'))
      //     } else {
      //       callback()
      //     }
      //   }
      // },
      // 初始化
      init() {
        this.getFieldTypeList()
        // if (!this.isDetail) {
        //   this.getStandardList()
        // }
        if (this.$route.query.id) {
          this.getDetail()
        }
      },

      // 获取详情数据
      getDetail() {
        let params = {
          id: this.$route.query.id,
        }
        this.$api.model.checkMeta(params).then((res) => {
          this.ruleForm = res.data
          this.ruleForm.fieldType = res.data.fieldType.toUpperCase()
        })
      },
      // 获取字段类型
      getFieldTypeList() {
        this.$api.model.getFieldType({}).then((res) => {
          this.fieldList = res.data
        })
      },
      // 获取标准
      getStandardList() {
        this.$api.project.getStandardList({}).then((res) => {
          this.standardList = res.data
        })
      },
      showChange(val) {
        if (val && this.ruleForm.standardId) {
          this.changeStandard(this.ruleForm.standardId)
        } else {
          this.standardDis = false
        }
      },
      changeStandard(data) {
        let hasStandard = this.standardList.filter((item) => item.id === data)
        if (hasStandard.length > 0) {
          this.standardDis = true
          this.ruleForm = {
            ...this.ruleForm,
            ...hasStandard[0],
            description: hasStandard[0].comment,
          }
        }
      },
      // 确定
      submit(form) {
        this.$refs[form].validate((valid) => {
          if (valid) {
            if (this.$route.query.type === 'ADD') {
              // 新增
              let params = {
                // projectCode: this.currentProject.projectCode,
                cnName: this.ruleForm.cnName,
                name: this.ruleForm.name,
                fieldLength: parseInt(this.ruleForm.fieldLength),
                fieldType: this.ruleForm.fieldType,
                // subjectId: this.ruleForm.subjectId[this.ruleForm.subjectId.length - 1],
                description: this.ruleForm.description,
              }
              this.loading = true
              this.$api.model
                .addMeta(params)
                .then((res) => {
                  this.loading = false
                  this.$refs.ruleForm.clearValidate()
                  if (res.success) {
                    this.$notify({
                      title: '成功',
                      message: '新增成功',
                      type: 'success',
                    })
                    this.$router.go(-1)
                  } else {
                    this.loading = false
                  }
                })
                .catch(() => {
                  this.loading = false
                })
            } else if (this.$route.query.type === 'EDIT') {
              // 编辑
              let params = {
                // projectCode: this.currentProject.projectCode,
                cnName: this.ruleForm.cnName,
                name: this.ruleForm.name,
                fieldLength: parseInt(this.ruleForm.fieldLength),
                fieldType: this.ruleForm.fieldType,
                // subjectId: this.ruleForm.subjectId[this.ruleForm.subjectId.length - 1],
                description: this.ruleForm.description,
                id: this.$route.query.id,
              }
              this.loading = true
              this.$api.model
                .eidtMeta(params)
                .then((res) => {
                  this.loading = false
                  if (res.success) {
                    this.$notify({
                      title: '成功',
                      message: '更新成功',
                      type: 'success',
                    })
                    if (this.$route.query.modelId) {
                      this.$router.push({
                        name: 'checkMetaData',
                        query: { modelId: this.$route.query.modelId },
                      })
                    } else {
                      this.$router.go(-1)
                    }
                  }
                  this.loading = false
                })
                .catch(() => {
                  this.loading = false
                })
            }
          }
        })
      },
      // 取消
      cancel() {
        this.$router.go(-1)
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    .container-box {
      position: relative;
      height: calc(100% - 57px);
      border-radius: 4px;

      > img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 252px;
        height: 232px;
      }
      .form {
        padding: 2px;
        color: #333;
        background: #fff;

        h2 {
          box-sizing: border-box;
          height: 38px;
          margin-top: 0;
          padding-left: 37px;
          font-size: 14px;
          line-height: 38px;
          background-color: #f7f8fa;
          border-radius: 3px;

          span {
            padding-left: 10px;
            border-left: 4px solid $themeBlue;
          }
        }
        .el-form {
          width: 700px;
          margin: 40px auto 0;

          &-item__label {
            color: #333;
            font-weight: 500;
          }

          :deep(.el-form-item) {
            position: relative;
            display: flex;
            width: 100%;
            margin-bottom: 20px;

            .el-form-item__label {
              position: absolute;
              top: 0;
              right: 700px;
              width: max-content !important;
            }

            .el-form-item__content {
              width: 100%;
              margin-left: 0 !important;
            }
          }
        }
      }
    }
    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 60px;
      padding-right: 20px;
      line-height: 60px;
      text-align: right;
      background: #fff;
      border-top: 1px solid rgba(200, 200, 200, 0.35);
    }
  }
</style>
