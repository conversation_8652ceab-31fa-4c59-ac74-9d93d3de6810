<template>
  <!-- 关联数据源 -->
  <div class="project-add-authorized-personnel">
    <div class="authorized-left">
      <PublicLeftTree
        ref="tree"
        :data="state.treeData"
        :treeAttrData="state.treeAttrData"
        :checkedNodes="state.selectTreeData"
        @treeCheckNode="treeCheckNode"
        @remoteSearch="remoteSearch"
      />
    </div>
    <div class="authorized-right">
      <CfTable
        :actionWidth="104"
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
        @handle-selection-change="handleSelectionChange"
        :paginationConfig="{
          total: state.tableData.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,

          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            tablePageChange(state.pagination)
          },
          onSizeChange: (v) => {
            state.pagination.currentPage = 1
            state.pagination.pageSize = v
            tablePageChange(state.pagination)
          },
        }"
      >
        <template #pageTop>
          <div class="box-add">
            <div class="top-left">
              <n-button
                color="primary"
                size="sm"
                variant="text"
                @click.prevent="handlerBatchDeletion"
                ><SvgIcon
                  style="margin-right: 4px; position: relative; top: -2px"
                  icon="icon-tree-del"
                />批量删除</n-button
              >
            </div>
            <div class="top-right"></div>
          </div>
        </template>
        <template #editor="{ row }">
          <div class="edit-box">
            <n-button
              class="del-button has-right-border"
              variant="text"
              @click.prevent="handlerDeletePersonnel(row)"
              >移除</n-button
            >
          </div>
        </template>
      </CfTable>

      <!-- <n-public-table
        :isDisplayAction="true"
        :isNeedSelection="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        @tablePageChange="tablePageChange"
        @handle-selection-change="handleSelectionChange"
      >
        <template #pageTop>
          <div class="box-add">
            <div class="top-left">
              <n-button
                color="primary"
                size="sm"
                variant="text"
                @click.prevent="handlerBatchDeletion"
                ><SvgIcon
                  style="margin-right: 4px; position: relative; top: -2px"
                  icon="icon-tree-del"
                />批量删除</n-button
              >
            </div>
            <div class="top-right"></div>
          </div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              class="del-button has-right-border"
              variant="text"
              @click.prevent="handlerDeletePersonnel(editor)"
              >移除</n-button
            >
          </div>
        </template>
      </n-public-table> -->
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance } from 'vue'
  import { formartTime } from '@/utils/index'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  export default {
    name: 'AuthorizedPersonnel',
    props: {
      isFull: {
        type: Boolean,
        default: false,
      },
    },
    setup(props) {
      const router = useRouter()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const tree = ref('')
      const state = reactive({
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'originalName', name: '人员名称' },
          { prop: 'roleName', name: '角色' },
          { prop: 'departmentFullName', name: '所属部门' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          dataStructure: 1,
          databaseType: 2,
          keyword: '',
        },
        earlySelected: [],
        // 对于半全选，判断全选功能的进入或是退出
        flag: false,
        org_options: [
          {
            value: '',
            label: '全选',
          },
          {
            value: '1',
            label: '结构化数据库',
          },
          {
            value: '2',
            label: '非结构化数据库',
          },
        ],
        allTableData: [], // 记录所有人员
        selectTreeData: [], //勾选中的人员
        currentPageData: [], // 展示页数据
        selectTableData: [], // 勾选中的数据
        treeData: [],
        treeAttrData: {
          showCheckbox: true,
          showControl: true,
          parentControl: '',
          childControl: '',
          nodeKey: 'id',
          remoteSearch: true,
        },
        editData: null, // 编辑初始数据
        isAdmin: [],
        tableHeight: 436,
        tableData: {},
        timer: null,
      })

      const methods = {
        setTableHeight() {
          if (props.isFull) {
            state.tableHeight = document.querySelector('.page-mid')?.offsetHeight + 14
          } else {
            state.tableHeight = document.querySelector('.page-mid')?.offsetHeight + 14
          }
        },
        // 编辑回显数据
        async editInit(data) {
          if (state.editData) return
          await methods.getTreeData()
          let treeIds = state.treeData[0].children.map((item) => item.id)

          data.userList.forEach((item) => {
            item.roleName = item.job
            if (!treeIds.includes(item.id)) {
              state.treeData[0].children.unshift({
                ...item,
                label: item.name + '-' + item.username,
                originalName: item.name,
                roleName: item.job,
              })
            }
          })
          //排序，admin身份放前面
          state.editData = data.userList.sort((a) => {
            return a.isAdmin ? -1 : 1
          })
          // 人员勾选数据
          let _editData = state.editData.filter((item) => {
            return item.name !== '系统管理员'
          })

          // 新增序号属性
          state.editData.map((item) => {
            return Object.assign(item, {
              key: item.id + 'false',
            })
          })

          // state.allTableData = _editData
          state.allTableData = state.editData
          state.selectTreeData = state.editData

          //获取管理员的id 管理员不可移除
          // state.editData.forEach((item) => {
          //   if (item.isAdmin) {
          //     item.disabledThisRow = true
          //     state.isAdmin.push(item)
          //   }
          // })

          let _data = methods.startPagination(state.editData) //前端分页第一页数据

          methods.updatedTable(_data)
        },
        // 选中人员执行
        treeCheckNode(data) {
          if (!data) return
          let { checkArr } = data
          // 选中最子集
          let lastPersonList = []
          if (checkArr.length) {
            checkArr.forEach((item) => {
              if (!item.isDepartment) {
                lastPersonList.push(item)
              }
            })

            state.allTableData = lastPersonList
          } else {
            state.allTableData = []
          }

          // if (state.isAdmin && state.isAdmin.length) {
          //   // 编辑有管理时候，追加
          //   state.allTableData.unshift(...state.isAdmin)
          // }

          if (state.allTableData.length) {
            let { currentPage, pageSize } = state.pagination

            if (state.allTableData.length <= (currentPage - 1) * pageSize) {
              //表示左侧树操作去除人员后，数据不能展示到当前页，则 自动计算出能到的最大页数
              state.pagination.currentPage = Math.ceil(state.allTableData.length / pageSize)
            }
          } else {
            state.pagination.currentPage = 1
          }
          state.currentPageData = methods.startPagination(state.allTableData)
          state.currentPageData.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          methods.updatedTable(state.currentPageData)
        },
        // 更新表格
        updatedTable(data) {
          // 序号
          data.map((item, index) => {
            return Object.assign(item, {
              number: index + 1,
              originalName: item.originalName || item.name,
            })
          })
          let _data = {
            total: state.allTableData.length,
            list: data,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.tableData = _data
        },
        // 表格操作变化
        tablePageChange(data) {
          let starPos = (data.currentPage - 1) * data.pageSize
          let endPos = data.currentPage * data.pageSize
          state.currentPageData = state.allTableData.slice(starPos, endPos)
          state.currentPageData.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          methods.updatedTable(state.currentPageData)
        },

        getAllData() {
          return state.allTableData
        },
        // 获取左侧树数据
        async getTreeData(search = '') {
          let params = {
            pageNum: 1,
            pageSize: 100,
            condition: {
              departmentFullId: '1',
              departmentFullName: '全部',
            },
          }
          if (search) {
            params.condition = {
              name: search,
            }
          }

          await api.system.userList(params).then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.selectTreeData = tree.value.getCheckedNodes().filter((item) => item.id !== 1)

                data.list.forEach((item, index) => {
                  const originalName = item.name // 存储原始值
                  item.name = item.name + '-' + item.username // 修改 item.name
                  item.originalName = originalName // 使用原始值
                })
                if (state.selectTreeData?.length) {
                  let treeIds = data.list.map((item) => {
                    return item.id
                  })
                  state.selectTreeData.forEach((item) => {
                    if (!treeIds.includes(item.id)) {
                      data.list.unshift(item)
                    }
                  })
                }

                state.treeData = [
                  {
                    departmentFullName: null,
                    fullId: '1',
                    id: 1,
                    isDepartment: true,
                    job: null,
                    label: null,
                    name: '公司',

                    username: null,
                    children: data.list,
                  },
                ]
              }
            }
          })
        },
        //远程搜索
        remoteSearch(val) {
          //防抖
          clearTimeout(state.timer)
          state.timer = setTimeout(() => {
            methods.getTreeData(val)
          }, 300)
        },

        // 找到树的最子节点
        findLastData(data, result = []) {
          if (!data) return
          if (data.length > 1) {
            data.forEach((item) => {
              methods.findLastData(item, result)
            })
          } else {
            if (data[0].children.length) {
              data[0].children.forEach((item) => {
                if (item.children.length) {
                  methods.findLastData(item.children, result)
                } else {
                  result.push(item)
                }
              })
            } else {
              result.push(data[0])
            }
          }
          return result
        },

        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
        },
        // 批量删除操作
        handlerBatchDeletion() {
          if (!state.selectTableData.length) return
          proxy.$MessageBoxService.open({
            title: '是否批量移除授权人员',
            content: '移除后人员将无法查看该场景',
            save: () => {
              methods.batchDeletion()
              ElNotification({
                title: '提示',
                message: '移除授权人员成功',
                type: 'success',
              })
            },
          })
        },
        // 批量删除
        batchDeletion() {
          state.selectTableData.forEach((select) => {
            state.allTableData = state.allTableData.filter((item) => {
              return item.id !== select.id
            })
            state.selectTreeData = [...state.allTableData] //更新左侧树勾选状态
            if (state.currentPageData.length === 1) {
              if (state.pagination.currentPage-- <= 1) state.pagination.currentPage = 1
            }
            state.currentPageData = methods.startPagination(state.allTableData)
            state.currentPageData.map((item, index) => {
              return Object.assign(item, { number: index + 1 })
            })
          })
          methods.updatedTable(state.currentPageData)
        },
        // 删除人员操作-单个
        handlerDeletePersonnel(data) {
          if (!data) return
          proxy.$MessageBoxService.open({
            title: '是否移除该授权人员',
            content: '移除后人员将无法查看该场景',
            save: () => {
              methods.deletePersonnel(data)
              ElNotification({
                title: '提示',
                message: '移除授权人员成功',
                type: 'success',
              })
            },
          })
        },
        // 删除人员-单个
        deletePersonnel(row) {
          state.allTableData = state.allTableData.filter((item) => {
            return item.id !== row.id
          })
          state.selectTreeData = [...state.allTableData] //更新左侧树勾选状态
          if (state.currentPageData.length === 1) {
            if (state.pagination.currentPage-- <= 1) state.pagination.currentPage = 1
          }
          state.currentPageData = methods.startPagination(state.allTableData)
          // 编号
          state.currentPageData.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })
          methods.updatedTable(state.currentPageData)
        },
        // 模拟分页
        startPagination(data) {
          let { currentPage, pageSize } = state.pagination
          let starPos = (currentPage - 1) * pageSize
          let endPos = currentPage * pageSize
          // 更新表格
          let filterData = data.slice(starPos, endPos)
          return filterData
        },
      }

      onMounted(() => {
        state.editId = router.currentRoute.value.query.id // 获取路由传参
        if (!state.editId) {
          methods.updatedTable([])
          methods.getTreeData()
        }
        methods.setTableHeight()
      })

      return {
        state,
        tree,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .project-add-authorized-personnel {
    display: flex;
    flex-wrap: nowrap;
    background-color: #fff;
    height: 100%;
    .authorized-left {
      // background: #f7f8fa;
      height: 100%;
    }
    .authorized-right {
      flex: 1;
      // overflow: hidden;
      min-width: 0; //解决左侧收起后 右侧数据不复原问题
    }
    .box-add {
      height: 46px;
      padding: 7px 8px;
      background-color: #fff;
      display: flex;
      justify-content: flex-end;
    }

    :deep(.nancalui-table-page) {
      padding: 0 16px;
    }
  }

  :deep(.tree-content-box) {
    height: calc(100% - 55px);
  }
</style>
