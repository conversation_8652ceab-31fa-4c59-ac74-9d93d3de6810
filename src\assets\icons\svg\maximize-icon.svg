<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数据盘点-数据采集-新增采集任务-关联映射关系（关系线交互样式）" transform="translate(-1370.000000, -461.000000)">
            <g id="域管理" transform="translate(230.000000, 96.000000)">
                <g transform="translate(0.000000, 181.000000)" id="list">
                    <g id="表头-+-表头备份-蒙版" transform="translate(20.000000, 172.000000)">
                        <g id="全局" transform="translate(1120.000000, 12.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="20" height="20"></rect>
                            <g id="编组-4" transform="translate(3.000000, 3.000000)" stroke="#333333" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="0" y1="0" x2="4.02776659" y2="3.98728754" id="路径" stroke-width="1.5"></line>
                                <line x1="0" y1="14.4594807" x2="4.02776659" y2="10.4721931" id="路径" stroke-width="1.5"></line>
                                <line x1="14.5" y1="14.4594807" x2="10.5127125" y2="10.4721931" id="路径" stroke-width="1.5"></line>
                                <line x1="14.4594807" y1="0" x2="10.4721931" y2="3.98728754" id="路径" stroke-width="1.5"></line>
                                <polyline id="路径" stroke-width="1.5" points="10.8749698 0 14.4999597 0 14.4999597 3.62498993"></polyline>
                                <polyline id="路径" stroke-width="1.25" points="14.4999597 10.8749698 14.4999597 14.4999597 10.8749698 14.4999597"></polyline>
                                <polyline id="路径" stroke-width="1.25" points="3.62498993 14.4999597 0 14.4999597 0 10.8749698"></polyline>
                                <polyline id="路径" stroke-width="1.5" points="0 3.62498993 0 0 3.62498993 0"></polyline>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>