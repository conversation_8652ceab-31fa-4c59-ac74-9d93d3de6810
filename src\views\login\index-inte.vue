<template>
  <div class="login-container">
    <img src="@img/login/logo_big_inte.png" alt="" class="logo-big" />
    <div class="login">
      <n-form
        ref="loginForm"
        :data="loginForm"
        class="login-form"
        auto-complete="on"
        label-position="right"
      >
        <div class="login-title">登录</div>
        <n-form-item field="username">
          <n-input
            ref="username"
            v-model="loginForm.username"
            :class="{ isActive: currentNode === 'user' }"
            placeholder="账号"
            name="username"
            type="text"
            clearable
            @focus="onFocusHandle('user', $event)"
            @blur="onBlurHandle"
          />
        </n-form-item>

        <n-form-item field="password" class="password">
          <n-input
            ref="password"
            v-model="loginForm.password"
            :class="{ isActive: currentNode === 'pass' }"
            type="password"
            placeholder="密码"
            show-password
            clearable
            @keyup.enter="handleLogin"
            @focus="onFocusHandle('pass', $event)"
            @blur="onBlurHandle"
          />
        </n-form-item>
        <div class="errorMsg">{{ errorMsg }}</div>
        <n-button
          :loading="loading"
          variant="solid"
          class="login-button"
          @click.prevent="handleLogin"
          >登录
        </n-button>
      </n-form>
    </div>
    <div class="introduce">
      <div class="introduce-box">
        <div class="introduce-box-title">乐数OS—数据集成平台</div>
        <div class="introduce-box-text"
          >整合统一企业内部数据，提供一致性和准确性的数据基础；促进数据共享和协作，提高数据的应用和价值挖掘；降低数据管理成本，提高数据利用效率。</div
        >
      </div>
    </div>
    <div class="footer">
      <div class="footer-left"
        >Copyright © 2018 能科科技股份有限公司 All Rights Reserved 京ICP备18044627号-4</div
      >
      <div class="footer-right"
        ><img src="@img/login/frame.png" alt="" class="footer-right-pic" /><EMAIL></div
      >
    </div>
  </div>
</template>

<script>
  // import { validUsername } from '@/utils/validate'
  import { mapState } from 'vuex'
  import { ElMessage } from 'element-plus'
  import Cookies from 'js-cookie'
  import { storeStorage} from '@/utils/auth'
  const regExpObject = new RegExp(/\s+/g)
  export default {
    name: 'Login',
    data() {
      const validateUsername = (rule, value, callback) => {
        if (value === '' || regExpObject.test(value)) {
          callback(new Error('请输入正确的用户名'))
        } else {
          callback()
        }
      }
      const validatePassword = (rule, value, callback) => {
        if (value.length < 6) {
          callback(new Error('密码不能小于6位'))
        } else {
          callback()
        }
      }
      return {
        loginForm: {
          isInte: true,
          username: '',
          password: '',
        },
        errorMsg: '',
        currentNode: '',
        loading: false,
        passwordType: 'password',
        redirect: undefined,
      }
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect
        },
        immediate: true,
      },
    },
    computed: {
      ...mapState({
        isCon: (state) => state['user'].enabledPt,
      }),
      variables() {
        return variables
      },
    },
    methods: {
      showPwd() {
        if (this.passwordType === 'password') {
          this.passwordType = ''
        } else {
          this.passwordType = 'password'
        }
        this.$nextTick(() => {
          this.$refs.password.focus()
        })
      },
      onFocusHandle(name, e) {
        this.currentNode = name
        e.target.select()
      },
      onBlurHandle() {
        this.currentNode = ''
      },
      handleLogin() {
        if (this.loading) {
          return false
        }
        if (this.loginForm.username === '' || regExpObject.test(this.loginForm.username)) {
          this.errorMsg = '请输入正确的用户名'
          return
        }
        if (this.loginForm.password.length < 6) {
          this.errorMsg = '密码不能小于6位'
          return
        }
        this.errorMsg = ''
        this.loading = true
        localStorage.setItem('isNormalLogin', '1')
        //先登陆获取权限，直接登录本项目
        this.$store
          .dispatch('user/login', this.loginForm)
          .then((res) => {
            localStorage.removeItem(storeStorage)
            // 获取WebSocket
            this.getWebSocketUrlFn()
            // 获取项目列表
            this.getCheckProjectFn()
          })
          .catch((err) => {
            localStorage.setItem('isNormalLogin', '0')
            setTimeout(() => {
              this.loading = false
            }, 1000)
          })
      },
      // 获取默认的场景
      getCheckProjectFn() {
        this.$api.project.getMyProjectList({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.projectList = res.data
            if (this.projectList.length > 0) {
              let flag = true
              this.projectList.forEach((val) => {
                if (val.using && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                } else if (val.preferred && flag) {
                  flag = false
                  this.$store.commit('user/SET_PROJECT', val)
                }
              })
              if (flag) {
                this.$store.commit('user/SET_PROJECT', this.projectList[0])
              }
            }
            // 跳转对应路由
            this.$router.push({
              name: 'ConvergencePageInte',
            })
          }
        })
      },
      // 获取webSocket地址
      getWebSocketUrlFn() {
        this.$api.base.getEnvironmentConfig({}).then((res) => {
          if (res.code === 'SUCCESS') {
            this.$store.commit('user/SET_WEBSOCKET', res.data.ws)
            this.$store.commit(
              'user/SET_OTHER_MRS',
              res.data.mrs ? res.data.mrs : { enabled: false },
            )
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $cursor: #000;
  @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-container input {
      color: $cursor;
    }
  }

  :deep(input:-webkit-autofill) {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: #000 !important;
    caret-color: #333;
  }

  .login-container {
    width: 100%;
    min-height: 100%;
    overflow: hidden;
    background-color: #fff;
    background-image: url(/src/assets/img/login/login_bg_big_inte.png);
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: 100% auto;

    .logo-big {
      position: fixed;
      top: 50px;
      left: 60px;
      width: 215px;
    }

    .login {
      position: absolute;
      top: 0;
      right: 131px;
      bottom: 0;
      box-sizing: border-box;
      width: 366px;
      height: 407px;
      margin: auto;
      padding: 55px 48px 56px 48px;
      background: #ffffff;
      border-radius: 4px;
      backdrop-filter: blur(20px);

      &-form {
        width: 100%;
        margin: 0 auto;
        .nancalui-form__item--horizontal {
          margin-bottom: 22px;
          border-bottom: 1px solid #bcc5d4;
          &.password {
            margin-bottom: 0;
          }
          :deep(.nancalui-input) {
            .nancalui-input__wrapper {
              padding: 0;
              border: none;
              .nancalui-input__inner {
                font-weight: bold;
              }
            }
          }
        }
      }

      &-title {
        margin-bottom: 58px;
        color: #000;
        font-weight: bold;
        font-size: 28px;
        text-align: left;
      }
      .errorMsg {
        height: 20px;
        color: #f56c6c;
        font-size: 12px;
        line-height: 20px;
      }

      &-button {
        width: 100%;
        height: 44px;
        margin-top: 44px;
        padding: 0;
        font-weight: bold;
        font-size: 16px;
        line-height: 44px;
        background-size: 100%;
        border: none;
        border-radius: 22px;
        :deep(span) {
          line-height: 38px;
        }
      }
    }
    .introduce {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 188px;
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;
      width: 458px;
      height: 407px;
      margin: auto;
      &-box {
        box-sizing: border-box;
        width: 458px;
        height: 183px;
        padding: 20px 26px;
        border-radius: 8px;
        backdrop-filter: blur(15px);
        &-title {
          color: #fff;
          font-weight: bold;
          font-size: 28px;
        }
        &-text {
          margin-top: 15px;
          color: #ffffff;
          font-weight: bold;
          font-size: 14px;
          line-height: 22px;
          opacity: 0.65;
        }
      }
    }

    .show-pwd {
      position: absolute;
      top: 1px;
      right: 10px;
      color: #ddd;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
    }

    .footer {
      position: fixed;
      bottom: 40px;
      left: 0;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;
      padding: 0 66px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      line-height: 17px;
      &-left {
        width: 335px;
      }
      &-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        &-pic {
          width: 16px;
          height: 17px;
          margin-right: 8px;
        }
      }
    }
  }
</style>
