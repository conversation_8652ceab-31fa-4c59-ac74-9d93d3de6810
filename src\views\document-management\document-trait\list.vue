<template>
  <div class="container">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">非结构化数据编号：</span>
          <n-input v-model="tableState.filterSearch.ruleCode" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">非结构化数据名称：</span>
          <n-input v-model="tableState.filterSearch.name" size="small" clearable />
        </div>
        <!-- <div class="col">
          <span class="label">密级：</span>
          <n-select v-model="tableState.filterSearch.confidentialityLevel" :allow-clear="true">
            <n-option
              v-for="item in tableState.secretList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </div> -->
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>非结构化数据分类</div>
        </div>
        <categoryTree @node-click="clickFn" />
      </div>
      <div class="right nc-m-l-10">
        <div class="table nc-p-t-16" v-loading="tableState.isLoad">
          <CfTable
            ref="tableRef"
            actionWidth="150"
            :table-head-titles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch()
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch(true)
              },
            }"
          >
            <template #tags="{ row }">
              <cfTag :tagArr="JSON.stringify(row.tags || [])" />
            </template>
            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="onView(row)">预览</n-button>
              <n-button variant="text" color="primary" @click="onEdit(row)">特征管理</n-button>
            </template>
          </CfTable>
        </div>
      </div>
    </section>
    <relationDocs
      v-model="tableState.relationDocsVisible"
      :id="tableState.relationDocsId"
      apiType="uploadAssoRelation"
      type="upload"
    />
  </div>
</template>
<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'

  import relationDocs from '../components/relation-docs.vue'
  import api from '@/api/index'
  import cfTag from '@/components/cfTag'
  import categoryTree from '../components/categoryTree.vue'
  const router = useRouter()
  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const tableState = reactive({
    tableList: [],
    filterSearch: {
      ruleCode: '',
      name: '',

      categoryId: null,
    },
    searchData: {
      ruleCode: '',
      name: '',
    },
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    tableHeadTitles: [
      { prop: 'ruleCode', name: '非结构化数据编号', width: 200 },
      { prop: 'name', name: '非结构化数据名称', width: 240 },
      { prop: 'categoryName', name: '非结构化数据分类' },
      { prop: 'tags', name: '标签', slot: 'tags', width: 300 },
      { prop: 'formatName', name: '数据类型' },
      { prop: 'type', name: '数据格式' },
      { prop: 'size', name: '大小' },
      { prop: 'wordCount', name: '字数' },
      { prop: 'maxValue', name: '数据最大值' },
      { prop: 'minValue', name: '数据最小值' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', width: 160 },
      { prop: 'updateTime', name: '更新时间', width: 160 },
      { prop: 'collectTime', name: '采集时间', width: 160 },
      { prop: 'collectSystem', name: '采集系统' },
      { prop: 'collectRate', name: '更新频率' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    relationDocsVisible: false,
    relationDocsId: '',
    markVisible: false,
    markId: '',
    checkeIds: [],
    name: '',
    isLoad: true,
  })
  function clickFn(id) {
    tableState.filterSearch.categoryId = id
    onSearch()
  }
  function startSearch() {
    Object.keys(tableState.filterSearch).forEach((key) => {
      tableState.searchData[key] = tableState.filterSearch[key]
    })
    onSearch(true)
  }
  // 查询
  function onSearch(init = false) {
    if (init) {
      tableState.pagination.pageNum = 1
      tableState.pagination.currentPage = 1
    }

    tableState.isLoad = true
    // .getTraitList
    api.documentManage
      .getTraitList({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...tableState.searchData,
          categoryId:
            tableState.filterSearch.categoryId === 'all'
              ? null
              : tableState.filterSearch.categoryId,
        },
      })
      .then((res) => {
        tableState.tableList = res.data.list
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  function formatSize(sizeInBytes) {
    if (sizeInBytes < 1024) {
      return sizeInBytes + 'B'
    } else if (sizeInBytes < 1024 * 1024) {
      return (sizeInBytes / 1024).toFixed(2) + 'KB'
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return (sizeInBytes / (1024 * 1024)).toFixed(2) + 'MB'
    } else {
      return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
    }
  }
  // 重置
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      ruleCode: '',
      name: '',
    }
    tableState.searchData = {
      ruleCode: '',
      name: '',
    }
    onSearch(true)
  }

  // 编辑非结构化数据
  function onEdit(row) {
    router.push({ name: 'documentTraitEdit', query: { id: row.id } })
  }
  // 预览非结构化数据
  function onView(row) {
    router.push({
      name: 'documentTagPreview',
      query: { id: row.docId },
    })
  }
  function refreshData() {
    onSearch(false)
    // const { currentPage, pageSize } = router.currentRoute.value.query
    // if (currentPage && pageSize) {
    //   tableState.pagination.currentPage = Number(currentPage)
    //   tableState.pagination.pageSize = Number(pageSize)
    //   onSearch(false)
    // } else {
    //   onSearch()
    // }
  }
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        refreshData()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true },
  )
  onMounted(() => {
    refreshData()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .popover-item {
    width: 100px;
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: 100%;
  }
  .publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .w-publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
