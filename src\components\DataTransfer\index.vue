<template>
  <el-transfer
    v-model="state.rightDefaultChecked"
    filterable
    :titles="['未选择规则', '已选择规则']"
    :format="{
      noChecked: '(${total} 项)',
      hasChecked: '(${checked}/${total} 项)',
    }"
    filter-placeholder=""
    :render-content="renderFunc"
    :data="state.data"
    @change="handleChange"
  >
    <!-- <template #default="{ option }">
      <span v-if="!option.isRight">{{ option.key }} - {{ option.label }}</span>
      <span v-else>{{ option.label }}<input type="number" /></span>
    </template> -->
  </el-transfer>
</template>

<script>
  import { ref, reactive, onMounted, defineExpose, watch, nextTick } from 'vue'
  import { ElNotification } from 'element-plus'
  // import transferInput from './components/transfer-input.vue'
  export default {
    name: '',
    // components: { transferInput },
    props: { ruleList: Array, ruleIds: String },
    setup(props) {
      const state = reactive({
        checkedWeight: [],
        checkedValue: {}, //以选规则的数组对象
        rightDefaultChecked: [], //以选中规则id
        data: [],
      })
      watch(
        () => props.ruleList,
        (newVal) => {
          methods.generateData()
        },
      )
      watch(
        () => props.ruleIds,
        (newVal) => {
          if (newVal) {
            state.checkedValue = JSON.parse(newVal)
            methods.generateData()
          }
        },
      )
      const methods = {
        filterMetho(query, item) {
          return item.initial.toLowerCase().includes(query.toLowerCase())
        },
        generateData() {
          state.data = []
          state.rightDefaultChecked =
            state.checkedValue.length > 0 ? state.checkedValue.map((item) => item.id) : []
          props.ruleList.forEach((item, index) => {
            state.data.push({
              label: item.name,
              key: item.id,
              isRight:
                state.checkedValue.length > 0
                  ? state.checkedValue.some((checkItem) => checkItem.id === item.id)
                  : false,
              weight:
                state.checkedValue.length > 0
                  ? state.checkedValue.filter((checkItem) => checkItem.id === item.id)[0]?.weight ??
                    1
                  : 1,
            })
          })
          nextTick(() => {
            methods.addChangeEvent()
          })
        },
        //input添加change事件
        addChangeEvent() {
          state.data.forEach((item, index) => {
            let inputVM = document.getElementById(item.key)
            if (inputVM) {
              inputVM.addEventListener('change', function (event) {
                state.data[index].weight = parseInt(event.target.value)
              })
            }
          })
        },
        handleChange(value, direction, movedKeys) {
          movedKeys.forEach((key) => {
            state.data.forEach((item) => {
              if (item.key === key && direction === 'right') {
                item.isRight = true
              } else if (item.key === key && direction === 'left') {
                item.isRight = false
              }
            })
          })
          nextTick(() => {
            methods.addChangeEvent()
          })
        },
        renderFunc(h, option) {
          if (option.isRight) {
            let index = null
            for (let i = 0; i < state.data.length; i++) {
              if (state.data[i].key === option.key) {
                index = i
              }
            }
            return h('span', [
              option.label,
              h('Input', {
                class: 'el-input__inner',
                type: 'number',
                min: '1',
                max: '100',
                // oninput:'if(!/^[0-9]+$/.test(value)value=value.replace(/\D/g,'');if(value>100)value=100;if(value<0)value=0',
                // props: {
                //   value: state.data[index].weight,
                // },
                value: state.data[index].weight,
                id: option.key,
                // on: {
                //   'on-change'(event) {
                //     //值改变时
                //     //将渲染后的值重新赋值给单元格值
                //     // option.weight = event.target.value
                //   },
                // },
              }),
            ])
          } else {
            return h('span', option.label)
          }
        },
        getCheckedRuleData() {
          let ruleData = []
          let msg = []
          state.data.forEach((item, ind) => {
            if (item.isRight) {
              ruleData.push({
                id: item.key,
                name: item.label,
                weight: item.weight,
              })
              if (item.weight < 1) {
                msg.push('第' + (ind + 1) + '条规则比重需大于等于1！')
              }
            }
          })
          if (ruleData.length === 0) {
            msg.push('请选择规则！')
          }
          if (msg.length > 0) {
            msg.forEach((v, i) => {
              setTimeout(() => {
                ElNotification({
                  title: '提示',
                  message: v,
                  type: 'error',
                })
              }, i * 300)
            })
            return false
          }
          return ruleData
        },
      }
      // defineExpose({
      //   getCheckedRuleData: methods.getCheckedRuleData,
      // })
      onMounted(() => {
        if (props.ruleList) {
          state.checkedValue = JSON.parse(props.ruleIds)
          methods.generateData()
        }
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .el-transfer {
    padding-left: 80px;
    box-sizing: border-box;
    --el-transfer-panel-width: 320px;
    :deep(.el-transfer__buttons) {
      padding: 0 66px;
      .el-transfer__button {
        margin: 10px 0;
      }
    }
  }
  :deep(.el-transfer-panel:last-of-type) {
    // width: 298px;
    .el-input {
      display: none;
    }
  }
  :deep(.el-transfer-panel__filter) {
    width: calc(100% - 30px);
  }
  :deep(.el-input__wrapper) {
    // flex-direction: row-reverse;
    position: relative;
    padding-right: 30px;
    .el-input__prefix {
      position: absolute;
      right: 5px;
    }
  }
  :deep(.el-transfer-panel .el-transfer-panel__header) {
    background: #fff;
    height: 32px;
    .el-checkbox .el-checkbox__label {
      font-size: 13px;
      font-weight: 500;
      color: #333;
      span {
        position: relative;
        left: 5px;
        color: #333;
      }
    }
  }
  :deep(.el-transfer-panel__body) {
    .el-checkbox__label {
      font-size: 12px;
      font-weight: 400;
      color: #333;
      .el-input__inner {
        margin-left: 5px;
        width: 35px;
        height: 18px;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 0 5px;
      }
    }
  }
  :deep(.el-transfer__buttons) {
    display: inline-flex;
    vertical-align: middle;
    padding: 0 30px;
    flex-direction: column;
    align-items: flex-end;
  }
</style>
