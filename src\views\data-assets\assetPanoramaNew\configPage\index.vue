<template>
  <div class="settings-container">
    <div class="screen">
      <div class="frame">
        <div class="text-wrapper">设置</div>
      </div>
      <!--      <div class="div-wrapper">-->
      <!--        <div class="div">返回</div>-->
      <!--      </div>-->
    </div>
    <div class="tab">
      <el-tabs v-model="tabType" class="demo-tabs">
        <el-tab-pane label="数据地图" name="dataMap"></el-tab-pane>
        <el-tab-pane label="指标数据" name="indicatorData"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="tab-container">
      <template v-if="tabType === 'dataMap'"><DataMapTab /> </template>
      <template v-else>
        <IndicatorDataTab />
      </template>
    </div>
  </div>
</template>

<script setup>
  import IndicatorDataTab from './../components/IndicatorDataTab.vue'
  import DataMapTab from './../components/DataMapTab.vue'
  const tabType = ref('dataMap')
</script>

<style lang="less" scoped>
  .settings-container {
    width: 100%;
    height: 100%;
    padding: 16px;
    .screen {
      position: relative;
      display: flex;
      align-items: center;
      height: 52px;
      padding: 0px 16px 0px 0px;
      background-color: #fff;
      border-color: #dcdfe6;
      border-bottom-width: 1px;
      border-bottom-style: solid;
      border-radius: 2px 2px 0px 0px;
      .frame {
        position: relative;
        display: flex;
        flex: 1;
        flex-grow: 1;
        align-items: center;
        padding-left: 16px;
        background-color: #fff;
        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          z-index: 1;
          display: block;
          width: 4px;
          height: 18px;
          margin: auto;
          background-color: #1e89ff;
          content: '';
        }
      }
      .text-wrapper {
        color: #1d2129;
        font-weight: 500;
        font-size: 16px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 24px; /* 150% */
      }
      .div-wrapper {
        position: relative;
        display: inline-flex;
        flex: 0 0 auto;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        margin-right: -1px;
        padding: 6px 16px;
        border: 1px solid;
        border-color: #e0e0e0;
        border-radius: 2px;
      }
      .div {
        position: relative;
        width: 30px;
        height: 20px;
        margin-top: -0.5px;
        color: #00000075;
        font-weight: 400;
        font-size: 14px;
        font-family: 'PingFang SC-Regular', Helvetica;
        line-height: normal;
        letter-spacing: 0;
      }
    }
    .tab {
      display: flex;
      flex-direction: row;
      align-items: end;
      height: 46px;
      padding: 0 10px;
      background-color: #fff;
      border-radius: 0px 0px 2px 2px;
      :deep(.el-tabs__nav-wrap:after) {
        display: none;
        height: 0;
      }
      :deep(.el-tabs__header) {
        margin: 0;
      }
    }
    .tab-container {
      display: flex;
      height: calc(100% - 108px);
      margin-top: 8px;
    }
  }
</style>
<style>
  .settings-container .el-table th.el-table__cell {
    color: #1d2129;
    font-weight: 400;
    font-size: 14px;
    font-family: 'Source Han Sans CN';
    font-style: normal;
    line-height: 22px;
    background-color: #ebf4ff;
  }
  .settings-container .el-table .el-table__cell {
    padding: 6px 0;
    overflow: hidden;
    color: #606266;
    font-weight: 400;
    font-size: 14px;
    font-family: 'PingFang SC';
    font-style: normal;
    line-height: normal;
    text-overflow: ellipsis;
  }
</style>
