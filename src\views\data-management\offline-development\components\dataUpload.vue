<template>
  <div class="content">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div v-loading="state.loading" class="btn active" @click="submitTask(false)">
        <SvgIcon class="icon" icon="icon-offline-start" />
        运行
      </div>
      <div v-loading="state.loading" class="btn active" @click="submitTask(true)">
        <SvgIcon class="icon" icon="icon-offline-submit" />
        提交到生产环境
      </div>
    </div>
    <div class="page-content">
      <div class="page-title"> 非结构化数据分析 </div>
      <n-form
        :data="dataUploadTaskBO"
        :rules="rules"
        ref="formRef"
        label-width="100px"
        message-type="text"
      >
        <div class="data-async">
          <div class="content-title">
            <span>选择表</span>
          </div>
          <div class="top-line">
            <n-form-item field="uploadReturnUrl" label="选择文件：">
              <div style="padding-left: 57px">
                <n-upload accept=".csv,.xls,.xlsx" droppable :before-upload="beforeUpload">
                  <div class="upload-box">
                    <div class="upload-box-icon">
                      <SvgIcon class="icon" icon="upload-cloud-new" title="上传" />
                    </div>
                    <div class="h2">点击或将文件拖拽到这里上传</div>
                    <div class="p"
                      >支持扩展名：.csv .xls .xlsx <br />
                      默认上传文件的第一个Sheet；.csv 最大支持5GB，其他文件最大支持100MB</div
                    >
                  </div>
                </n-upload>
                <div v-if="dataUploadTaskBO.uploadReturnUrl" class="upload-list">
                  <SvgIcon class="icon" icon="icon-attachment" title="序列" />
                  <div class="name">{{ dataUploadTaskBO.fileName }}</div>
                  <SvgIcon
                    class="icon down"
                    icon="icon-download"
                    title="下载"
                    @click.prevent.stop="
                      downFn(dataUploadTaskBO.fileName, dataUploadTaskBO.uploadReturnUrl)
                    "
                  />
                  <SvgIcon
                    class="icon del"
                    icon="icon-new-delete"
                    title="删除"
                    @click.prevent.stop="delFileFn"
                  />
                </div>
              </div>
            </n-form-item>
          </div>
          <div class="content-title">
            <span>预览上传文件</span>
          </div>
          <CfTable
            :key="state.tableDataPreview"
            :tableConfig="{
              data: state.tableDataPreview?.filter(
                (_, i) => !(dataUploadTaskBO.firstLineIsTableHeader && i === 0),
              ),
              rowKey: 'id',
            }"
            :table-head-titles="
              Object.keys(state.tableDataPreview?.[0] || {}).map((_, i) => ({
                name: (!dataUploadTaskBO.firstLineIsTableHeader && 'col_' + i) || _,
                prop: _,
              }))
            "
          >
            <template v-if="state.tableDataPreview?.length > 0" #pageTop>
              <n-form-item label="文件编码：" field="fileEncoding">
                <div class="file-encoding">
                  <n-radio-group
                    direction="row"
                    v-model="dataUploadTaskBO.fileEncoding"
                    @change="getSourceTables"
                  >
                    <n-radio value="UTF-8">UTF-8</n-radio>
                    <n-radio value="GB18030">GB18030</n-radio>
                    <n-radio value="GB2312">GB2312</n-radio>
                  </n-radio-group>
                  <div style="display: flex; align-items: center">
                    <div class="file-code-title">
                      <n-switch
                        v-model="dataUploadTaskBO.firstLineIsTableHeader"
                        @update:value="getSourceTables"
                        disabled
                      />

                      <span>首行作为表头</span>
                    </div>
                    <span>仅支持预览前10条数据</span>
                  </div>
                </div>
              </n-form-item>
            </template>
          </CfTable>
          <div class="content-title">
            <span>设置目标表</span>
          </div>
          <div class="form-row">
            <n-form-item field="sinkTableName" label="表英文名称：">
              <n-input
                v-model="dataUploadTaskBO.sinkTableName"
                :disabled="state.pageStatus"
                maxlength="30"
                placeholder="请输入"
              />
            </n-form-item>
            <n-form-item field="sinkTableComment" label="表中文名称：">
              <n-input
                v-model="dataUploadTaskBO.sinkTableComment"
                maxlength="500"
                placeholder="请输入"
              />
            </n-form-item>

            <n-form-item field="partitionTable" label="表类型：">
              <n-checkbox
                label="非分区表"
                :isShowTitle="false"
                v-model="dataUploadTaskBO.partitionTable"
              />
            </n-form-item>
          </div>

          <div class="content-title">
            <span>表信息</span>
          </div>
          <div class="form-row">
            <n-form-item field="securityLevel" label="表密级：">
              <n-select
                v-model="dataUploadTaskBO.securityLevel"
                placeholder="请选择"
                filter
                allow-clear
                :options="state.confidentialityLevelOptions"
              />
            </n-form-item>
            <n-form-item field="dataTag" label="标签：">
              <div class="mid-line-tag">
                <el-tree-select
                  ref="selectTree"
                  v-model="state.tagList"
                  :data="state.targetOptions"
                  style="width: 100%"
                  node-key="key"
                  :props="{
                    label: 'name',
                    value: 'key',
                    children: 'children',
                  }"
                  multiple
                  show-checkbox
                  :render-after-expand="false"
                  filterable
                  clearable
                />
              </div>
            </n-form-item>
          </div>
          <div class="content-title">
            <span>字段映射</span>
          </div>
          <div class="table-row">
            <!-- <div>
              <div style="display: inline-block">
                <div class="btn" @click="selectionChange"> 智能字段生成 </div>
              </div>
            </div> -->
            <div class="form-row" style="margin-bottom: 0">
              <CfTable
                :key="state.sourceTableData"
                ref="sourceTableRef"
                :tableConfig="{
                  data: state.sourceTableData,
                  rowKey: 'oldcnName',
                }"
                @handle-selection-change="selectionChange"
                isNeedSelection
                isNeedIndex
                :indexWidth="160"
                :table-head-titles="[{ prop: 'oldcnName', name: '中文名' }]"
              >
                <template #pageTop>
                  <div class="table-top">
                    <div class="label"> 数据源： </div>
                    <div class="value">
                      {{ dataUploadTaskBO.fileName || '--' }}
                    </div>
                  </div>
                </template>
              </CfTable>

              <CfTable
                :key="dataUploadTaskBO.mappingList"
                ref="targetTableRef"
                :tableConfig="{
                  data: dataUploadTaskBO.mappingList,
                  'cell-style': {
                    border: 'none',
                    height: '40px',
                    padding: ' 0',
                  },
                  rowKey: 'sourceName',
                }"
                isNeedIndex
                :table-head-titles="[
                  { prop: 'targetName', name: '中文名', slot: 'targetName' },
                  { prop: 'targetCode', name: '英文名 ', slot: 'targetCode' },
                  { prop: 'targetDataType ', name: '字段类型', slot: 'targetDataType' },
                  { prop: 'targetDataLength', name: '字段长度', slot: 'targetDataLength' },
                ]"
              >
                <template #pageTop>
                  <div class="table-top">
                    <div class="label"> 目标表： </div>
                    <div class="value">
                      {{ dataUploadTaskBO.sinkTableName || '--' }}
                    </div>
                  </div>
                </template>
                <template #targetName="{ row }">
                  <n-input
                    v-model="row.targetName"
                    placeholder=""
                    maxlength="30"
                    @blur="cNameBlur(row)"
                  />
                </template>
                <template #targetCode="{ row }">
                  <n-input
                    v-model="row.targetCode"
                    :class="!row.isPass ? 'required-input' : ''"
                    placeholder=""
                    :disabled="state.disabled"
                    maxlength="80"
                  />
                </template>
                <template #targetDataType="{ row }">
                  <n-select
                    v-model="row.targetDataType"
                    placeholder="请选择"
                    :disabled="state.disabled"
                    @value-change="inputLengthWithFieldType(row)"
                  >
                    <n-option
                      v-for="item in state.fieldTypeOptions"
                      :key="item.name"
                      :name="item.name"
                      :value="item.value"
                    />
                  </n-select>
                </template>
                <template #targetDataLength="{ row }">
                  <n-input
                    v-model="row.targetDataLength"
                    :class="
                      row.targetDataLength &&
                      row.targetDataLength > 0 &&
                      row.targetDataLength.toString().indexOf('.') === -1
                        ? ''
                        : 'required-input'
                    "
                    type="number"
                    placeholder=""
                    :disabled="state.disabled || row.isRequiredFieldLength"
                    @keydown="onKeydownPositiveInteger($event, row.targetDataLength)"
                    @keyup="onKeyupPositiveInteger($event, row.targetDataLength)"
                  />
                </template>
              </CfTable>
            </div>
            <div>
              <div style="display: inline-block">
                <div class="btn no-border" @click="createHiveDDL">
                  <SvgIcon class="icon" icon="code-icon" /> DDL
                </div>
              </div>
            </div>
          </div>
          <!-- 日志输出 -->
          <codemirror
            ref="myCm"
            v-if="state.hiveDDL?.length"
            v-model:value="state.hiveDDL"
            class="codemirror"
            :options="state.sqlOption"
            @ready="onCmReady"
            @focus="onCmFocus"
            @input="onCmCodeChange"
          />
        </div>
      </n-form>
    </div>

    <!--    <div style="width: 0" v-html="`<style>-->
    <!--        .data-async{-->
    <!--          ${toStyle}-->
    <!--        }-->
    <!--    </style>`-->
    <!--      ">-->
    <!--    </div>-->

    <section
      v-loading="state.loading"
      v-show="state.logShow"
      element-loading-text="日志加载中..."
      :class="{ logPop: true, showRun: state.showRunLog }"
    >
      <div class="logPop-head">
        <div class="logTitle">运行日志</div>

        <div class="logPop-btn">
          <n-button
            color="primary"
            :disabled="!state.runLogText || state.loading"
            @click.prevent.stop="downLogFn"
            >下载日志</n-button
          >
          <SvgIcon
            :class="{ icon: true, show: state.showRunLog }"
            icon="icon-arrow-second"
            @click.prevent.stop="state.showRunLog = !state.showRunLog"
          />
        </div>
      </div>

      <div class="logText" v-if="state.showRunLog">
        <div v-if="state.runLogText !== ''" class="textarea" v-html="state.runLogText"></div>
        <div
          v-if="state.runLogText === '' || (state.runLogText === null && state.showRunLog)"
          class="empty"
        >
          <img class="empty-pic" src="@/assets/img/empty_gray.png" />
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { watch } from 'vue'
  import CfTable from '@/components/cfTable'
  import CfTagSelect from '@/components/cfTagSelect'
  import api from '@/api/index'
  import { confidentialityLevelOptions, colorList, cNameBlur } from './dataUpload.js'
  import {
    checkCName500,
    checkNameExcludeKeywords,
    onKeydownPositiveInteger,
    onKeyupPositiveInteger,
  } from '@/utils/validate'
  import { timestampToTime } from '@/const/public.js'
  import { taskRunLog } from '@/api/dataManage.js'

  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'

  const emit = defineEmits(['close', 'changeLoadingStatus'])

  const formRef = ref(null)
  const sourceTableRef = ref(null)
  const targetTableRef = ref(null)
  const selectTree = ref(null)
  const attrs = useAttrs()
  let runStatus = ref('waiting')
  const state = reactive({
    logShow: false,
    runLogText: '',
    showRunLog: false,
    nodeName: '',
    pageStatus: false,
    sourceTableData: [],
    formData: {
      configInfoBO: {
        taskType: 'OW_DATA_UPLOAD',
        dataUploadTaskBO: {
          dataTag: [],
          fileEncoding: 'UTF-8', // 文件编码格式
          fileName: null, // 文件名
          firstLineIsTableHeader: true,
          mappingList: [],
          partitionTable: true,
          securityLevel: null,
          sinkTableComment: null,
          sinkTableName: null,
          sourceFileType: null,
          uploadReturnUrl: null,
        },
      },
      id: attrs.taskId,
    },
    customName: '',
    customColor: '',
    popoverKey: 1,
    tagList: [],
    colorList,
    tableDataPreview: [],
    modelList: [],
    // 数据源列表
    dataSourceList: [],
    confidentialityLevelOptions,
    targetOptions: [],
    fieldTypeOptions: [
      { value: 'STRING', name: '字符串(STRING)' },
      { value: 'BINARY', name: '字节序列(BINARY)' },
      { value: 'BOOLEAN', name: '布尔(BOOLEAN)' },
      { value: 'DOUBLE', name: '数值(DOUBLE)' },
      { value: 'FLOAT', name: '浮点(FLOAT)' },
      { value: 'TIMESTAMP', name: '时间戳(TIMESTAMP)' },
      { value: 'BIGINT', name: '长整数(BIGINT)' },
      { value: 'INT', name: '整数(INT)' },
      { value: 'TINYINT', name: '短整型(TINYINT)' },
      { value: 'DECIMAL', name: '任意数字(DECIMAL)' },
      { value: 'DATE', name: '日期(DATE)' },
    ],
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
    envType: 'OFFICIAL',
  })
  const { dataUploadTaskBO } = toRefs(state.formData.configInfoBO)
  const rules = {
    uploadReturnUrl: [
      {
        required: true,
        message: '请选择文件',
        trigger: 'blur',
      },
    ],
    sinkTableName: [
      {
        required: true,
        validator: (...args) => {
          if (state.dataUploadSubmitted) {
            return args[2]()
          } else {
            return checkNameExcludeKeywords(...args, null, 'model', 'validModel', {
              nameType: 'EN',
              name: dataUploadTaskBO.value.sinkTableName,
              id: null,
              envType: state.envType,
            })
          }
        },
        trigger: 'submit',
      },
    ],
    sinkTableComment: [
      {
        required: true,
        message: '请输入表中文名称',
        trigger: 'blur',
      },
    ],
    partitionTable: [
      {
        required: true,
        message: '请选择是否分区表',
        trigger: 'blur',
        type: 'bool',
      },
    ],
    securityLevel: [
      {
        required: true,
        message: '请选择密级',
        trigger: 'blur',
      },
    ],
  }

  watch(
    runStatus,
    (newValue) => {
      emit('changeLoadingStatus', {
        taskId: attrs.taskId,
        runStatus: newValue,
      })
    },
    { deep: true },
  )

  // 下载日志
  const downLogFn = () => {
    if (state.runLogText) {
      state.runLogText = state.runLogText.replace(/<[^>]+>/g, '')
      const blob = new Blob([state.runLogText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        state.nodeName + '-' + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  // 上传文件
  const beforeUpload = (UploadRawFile) => {
    let size = UploadRawFile[0].file.size / Math.pow(1024, 2)
    let fileName = UploadRawFile[0].file.name.substring(
      UploadRawFile[0].file.name.lastIndexOf('/') + 1,
    )
    let suffix = fileName.split('.')[1]
    if (suffix === 'csv') {
      if (size > 5 * 1024) {
        ElNotification({
          title: '提示',
          message: 'csv文件不能超过5G！',
          type: 'warning',
        })
        return false
      }
    } else {
      if (size > 100) {
        ElNotification({
          title: '提示',
          message: '文件不能超过100M！',
          type: 'warning',
        })
        return false
      }
    }
    const formData = new FormData()
    formData.append('file', UploadRawFile[0].file)
    formData.append('bucket', 'data-govern')
    api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
      let { success, data } = res
      if (success) {
        dataUploadTaskBO.value.fileName = data.name
        dataUploadTaskBO.value.uploadReturnUrl = data.url
        dataUploadTaskBO.value.mappingList = []

        getSourceTables()
        // methods.conversionFn(false)
      }
    })
    return false
  }
  // 删除文件
  const delFileFn = () => {
    dataUploadTaskBO.value.fileName = ''
    dataUploadTaskBO.value.fileUploadLocation = ''
    state.tableDataPreview = []
    state.sourceTableData = []
    dataUploadTaskBO.value.mappingList = []
  }
  // 下载文件
  const downFn = (name, url) => {
    const link = document.createElement('a')
    let fileName = name
    link.download = fileName
    link.style.display = 'none'
    link.href = url
    document.body.appendChild(link)
    link.click()
    URL.revokeObjectURL(link.href)
    document.body.removeChild(link)
  }

  // 根据数据源获取对应数据--获取后端返回的excel内容
  const getSourceTables = () => {
    const { fileName, fileEncoding, firstLineIsTableHeader, uploadReturnUrl } =
      dataUploadTaskBO.value
    const params = {
      fileName: fileName || null,
      fileEncoding: fileEncoding || null,
      fileFrom: 'LOCAL',
      firstLineIsTableHeader: firstLineIsTableHeader, // 第一行是表头
      fileUploadLocation: uploadReturnUrl || null,
    }
    api.dataManagement.fileDataPreview(params).then((res) => {
      let { data, success } = res
      const _fileTableData = []
      let _fileTableHeadTitles = []
      if (success) {
        if (data.jsonData) {
        } else if (data.tableLikeData) {
          _fileTableHeadTitles = data.tableLikeData?.[0]?.map((_) => {
            return { prop: _, name: _ }
          })
          data.tableLikeData.forEach((item) => {
            const _item = item
            const _data = {}
            _item.map((v, i) => {
              _data[_fileTableHeadTitles[i]?.name] = v
            })
            _fileTableData.push(_data)
          })
        }
        state.tableDataPreview = _fileTableData || []
        state.sourceTableData = []
        state.sourceTableData = Object.keys(state.tableDataPreview?.[0] || {}).map((item) => ({
          oldcnName: item,
        }))
      }
    })
  }

  //根据字段类型设置长度精度
  const inputLengthWithFieldType = (item) => {
    switch (item.targetDataType) {
      case 'TIMESTAMP':
      case 'DATE':
        item.targetDataLength = null
        break
      case 'DOUBLE':
      case 'FLOAT':
      case 'BIGINT':
      case 'INT':
      case 'TINYINT':
      case 'INTEGER':
      case 'DECIMAL':
        item.targetDataLength = 37
        break
      case 'VARCHAR':
        item.targetDataLength = 64
        break
    }
  }
  // 提交任务
  const submitTask = (flag = false) => {
    state.envType = flag ? 'OFFICIAL' : 'TEST'
    if (!flag) {
      state.logShow = true
      runStatus.value = 'running'
    }
    formRef.value.validate((valid) => {
      if (valid) {
        // 立即存储保存状态的session，防止用户在保存过程中刷新页面
        let saveSession = {
          runStatus: 'SAVING',
          nodeId: state.formData.id,
          envType: state.envType,
          flag: flag,
          formData: state.formData,
          dataUploadTaskBO: dataUploadTaskBO.value,
        }
        sessionStorage.setItem(
          'dataUploadPollingSession_' + state.formData.id,
          JSON.stringify(saveSession),
        )

        state.loading = true
        const params = {}
        let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
        const dataTag = []
        activeOptions?.forEach((val) => {
          if (val.color != null) {
            dataTag.push({ text: val.name, color: val.color, id: val.id || null })
          }
        })
        Object.assign(params, state.formData, {
          ...state.formData,
          configInfoBO: {
            ...state.formData.configInfoBO,
            dataUploadTaskBO: {
              ...dataUploadTaskBO.value,
              isDataUploadSubmitProd: flag,
              dataTag,
              partitionTable: !dataUploadTaskBO.value.partitionTable,
            },
          },
        })
        api.offlineJob
          .saveUploadJob(params)
          .then((res) => {
            if (res.success) {
              // 保存成功后清除保存session
              sessionStorage.removeItem('dataUploadPollingSession_' + state.formData.id)
              state.loading = false
              flag ? ElMessage.success('提交成功') : taskRunLogFn(res.data)
              emit('close')
            }
          })
          .catch((err) => {
            // 保存失败时清除session
            sessionStorage.removeItem('dataUploadPollingSession_' + state.formData.id)
            state.loading = false
          })
      }
    })
  }

  // 运行日志
  const taskRunLogFn = async (runInstanceId) => {
    const res = await taskRunLog({ runInstanceId })
    state.showRunLog = true
    state.loading = true

    if (res.code === 'SUCCESS') {
      state.runLogText = res.data.log

      switch (res.data.state) {
        case 'FAILURE':
          runStatus.value = 'fail'
          ElNotification({
            title: '提示',
            message: '运行失败',
            type: 'error',
          })
          state.loading = false
          sessionStorage.removeItem('dataUploadPollingSession_' + state.formData.id)
          break
        case 'RUNNING_EXECUTION':
          runStatus.value = 'running'
          // 存储轮询状态
          sessionStorage.setItem(
            'dataUploadPollingSession_' + state.formData.id,
            JSON.stringify({
              runStatus: 'RUNNING_EXECUTION',
              runInstanceId, // 这里是 taskRunLogFn 的参数
            }),
          )
          state.stateFlag = setTimeout(() => {
            taskRunLogFn(runInstanceId)
          }, 3000)
          break
        case 'SUCCESS':
          runStatus.value = 'success'
          ElMessage.success('运行成功')
          state.loading = false
          sessionStorage.removeItem('dataUploadPollingSession_' + state.formData.id)
          break
        default:
          break
      }
    } else {
      runStatus.value = 'fail'
      state.loading = false
      state.runLogText += '<div class="red">运行失败</div>'
    }
  }

  // 回显详情
  const getDetail = () => {
    return api.offlineJob.getOfflineJobTaskDetail({ id: state.formData.id }).then((res) => {
      state.nodeName = res.data.name
      state.pageStatus = res.data?.versionBO?.id ? true : false
      if (res.success && res.data?.versionBO?.dataUploadTaskBO) {
        dataUploadTaskBO.value = res.data?.versionBO?.dataUploadTaskBO
        dataUploadTaskBO.value.partitionTable = !dataUploadTaskBO.value.partitionTable
        state.dataUploadSubmitted = res.data?.dataUploadSubmitted
        state.tagList = dataUploadTaskBO.value?.dataTag?.map((item) => 'tag_' + item.id)
        getSourceTables()
      }
    })
  }

  const selectionChange = () => {
    const oldSelections = sourceTableRef.value.getSelectionRows() // 先保存选中项

    const list = oldSelections.map((_, i) => ({
      targetName: _?.oldcnName,
      targetCode: 'col_' + (i + 1),
      targetDataType: 'STRING',
      targetDataLength: 255,
      sourceCode: 'col_' + (i + 1),
      sourceDataLength: 255,
      sourceDataType: 'STRING',
      sourceName: _?.oldcnName,
    }))

    const sortedData = list.sort((a, b) => {
      const numA = parseInt(a.sourceCode.match(/\d+/)[0]) // 提取col_后的数字
      const numB = parseInt(b.sourceCode.match(/\d+/)[0])
      return numA - numB // 升序排列
    })
    sortedData.forEach((val) => {
      inputLengthWithFieldType(val)
    })
    dataUploadTaskBO.value.mappingList = sortedData
  }

  const customNameBlur = () => {
    let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
    let res = regex.test(state.customName)
    if (res && state.customName.length > 1) {
      if (state.targetOptions.filter((val) => val.name === state.customName).length === 0) {
        return true
      } else {
        ElNotification({
          title: '提示',
          message: '标签名重复，请重新填写！',
          type: 'warning',
        })
        return false
      }
    } else {
      ElNotification({
        title: '提示',
        message: '标签为2-8个字符，支持中文和英文！',
        type: 'warning',
      })
      return false
    }
  }
  // 选中颜色
  const checkFn = (index) => {
    state.colorList.forEach((val, ind) => {
      val.checked = false
      if (index === ind) {
        val.checked = true
      }
    })
  }
  const valueChangeHandle = () => {
    dataUploadTaskBO.value.dataTag = state.targetOptions.filter((item) =>
      state.tagList.includes(item.value),
    )
  }
  // 标签输入完成
  const changeTargetFn = (flag, isSave) => {
    if (isSave) {
      if (state.customName) {
        if (customNameBlur()) {
          state.tagList.push(state.customName)
          let checkColorItem = state.colorList.filter((val) => val.checked)[0]
          if (state.customColor) {
            let index = Math.floor(Math.random() * 5)
            checkColorItem = state.colorList[index]
          }
          state.targetOptions.push({
            name: state.customName,
            value: state.customName,
            text: state.customName,
            color: checkColorItem.value + '_' + checkColorItem.bgColor,
          })
          state.targetKey++
        } else {
          return false
        }
      } else {
        ElNotification({
          title: '提示',
          message: '请输入标签名称！',
          type: 'warning',
        })
        return false
      }
    }
    if (flag) {
      state.customName = ''
      state.colorList = state.colorList.map((val, ind) => {
        if (ind === 0) {
          val.checked = true
        } else {
          val.checked = false
        }
        return val
      })
    } else {
      state.popoverKey++
    }
    valueChangeHandle()
  }

  // 创建HIVE表DDL
  const createHiveDDL = () => {
    const params = {
      tableComment: dataUploadTaskBO.value.sinkTableComment,
      tableName: dataUploadTaskBO.value.sinkTableName,
      partitionTable: !dataUploadTaskBO.value.partitionTable,
      columns: dataUploadTaskBO.value.mappingList.map((_) => {
        return {
          comment: _.targetName,
          dataLength: _.targetDataLength,
          dataType: _.targetDataType,
          name: _.targetCode,
          // orderNum: 0,
        }
      }),
    }
    api.offlineJob.createHiveTableDDL(params).then((res) => {
      if (res.success) {
        state.hiveDDL = res.data
      }
    })
  }

  // 获取字段类型
  const getFieldTypeList = () => {
    api.model.getFieldType({}).then((res) => {
      if (res.success) {
        state.fieldTypeOptions = res.data
      }
    })
  }

  //   获取离线作业生成的所有模型
  const getOfflineModel = () => {
    api.offlineJob.getModelList().then((res) => {
      state.modelList = res.data || []
    })
  }

  //   查询数据源列表
  const getDataSourceList = () => {
    api.offlineJob
      .getDataSourceList({
        datasourceType: 'ORACLE',
      })
      .then((res) => {
        state.dataSourceList = res.data || []
      })
  }

  // 获取标签列表
  const getTargetList = () => {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        state.targetOptions = data
      }
    })
  }
  // css转换 计算属性
  const toStyle = computed(() => {
    return state.tagList?.map((_, i) => {
      const item = state.targetOptions.find((item) => item.name === _)
      dataUploadTaskBO.value.dataTag
      const [color, background] = item.color.split('_')
      return `
        .nancalui-tag:nth-child(${i + 1}) > .nancalui-tag__item {
          background: ${background} !important;
          border: 1px solid var(---, ${color}) !important;
          color: ${color} !important;
        }
        `
    }).join(`
        `)
  })
  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.hiveDDL = newCode
  }
  // SQL语句获取焦点时
  const onCmFocus = (cm) => {
    state.codemirror = cm
    state.codemirror.setOption('readOnly', true)
  }
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      state.codemirror.showHint(config)
    })
    state.codemirror.setValue(state.hiveDDL)
    state.codemirror.setOption('readOnly', true)
  }
  const init = () => {
    // 准备数据
    // getOfflineModel()
    getDataSourceList()
    getTargetList()
    // getFieldTypeList()
    // 详情
    getDetail().finally(() => {
      // 检查是否有未完成的轮询或保存状态
      let pollingSession = sessionStorage.getItem('dataUploadPollingSession_' + state.formData.id)
      if (pollingSession) {
        let sessionData = JSON.parse(pollingSession)
        if (sessionData.runStatus === 'SAVING') {
          // 恢复保存状态，继续执行保存和运行逻辑
          state.envType = sessionData.envType
          state.formData = sessionData.formData
          dataUploadTaskBO.value = sessionData.dataUploadTaskBO
          state.logShow = !sessionData.flag
          runStatus.value = 'running'

          // 继续执行保存和运行逻辑
          submitTask(false)
        } else if (sessionData.runStatus === 'RUNNING_EXECUTION' && sessionData.runInstanceId) {
          // 自动恢复轮询
          runStatus.value = 'running'
          taskRunLogFn(sessionData.runInstanceId)
        }
      }
    })
  }
  init()
</script>
<style lang="scss" scoped>
  .content {
    width: 100%;
    height: 100%;
    padding-bottom: 58px;
    overflow-y: auto;

    .toolbar {
      display: flex;
      flex-shrink: 0;
      gap: 16px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 8px;
      border-bottom: 1px solid #dcdfe6;

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        box-sizing: border-box;
        padding: 4px 16px;
        color: #1e89ff;
        font-size: 14px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;

        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }
      }
    }

    .page-content {
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 7px 7px 7px 16px;
        color: #1d2129;
        color: var(----, #1d2129);
        font-weight: bolder;
        font-size: 16px;
        line-height: 24px;
        background-color: #fff;

        &[sub-label]::after {
          display: inline-block;
          margin-left: 12px;
          padding: 0px 4px;
          color: #1aa4ee;
          font-weight: 400;
          font-size: 12px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 22px;
          line-height: 20px;
          background: rgba(26, 164, 238, 0.08);
          border: 1px solid rgba(26, 164, 238, 0.4);
          border-radius: 2px;
          content: attr(sub-label);
          /* 166.667% */
        }

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
    }

    .page-content {
      .data-async {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;

        .content-title {
          position: relative;
          width: 100%;
          height: 30px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }

        .data-source {
          display: flex;
          flex: 1 0 0;
          gap: 6px;
          align-items: center;
          width: 100%;
          height: 32px;
          padding: 5px 6px 5px 10px;
          overflow: hidden;
          color: var(----, #1d2129);
          font-weight: 400;
          font-size: 14px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 32px;
          white-space: nowrap;
          text-overflow: ellipsis;
          background: var(---, #f5f7fa);
          border: 1px solid var(---, #e5e6eb);
          border-radius: 2px;
          /* 157.143% */
        }

        .form-row {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          width: 100%;
          margin-bottom: -8px;
          column-gap: 48px;
        }

        .top-line {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;

          .nancalui-form__item--horizontal {
            flex: 1;
          }

          .upload-box {
            display: flex;
            flex-direction: column;
            gap: 18px;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            width: 560px;
            height: 240px;
            padding: 30px 16px;
            background: var(---, #f5f7fa);
            background-color: #f5f7fa;
            border: 1px solid var(---, #c9cdd4);
            border-radius: 1.8px;
            cursor: pointer;

            &-icon {
              .icon {
                font-size: 48px;
              }
            }

            .h2 {
              color: var(----, #1d2129);
              font-weight: bold;

              font-size: 16px;
              font-family: 'PingFang SC';
              font-style: normal;
              line-height: 24px;
              text-align: center;
              /* 150% */
            }

            .p {
              margin-top: 21px;

              color: var(---, #909399);
              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              line-height: 22px;
              text-align: center;
              /* 157.143% */
            }
          }

          .upload-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 560px;
            margin-top: 12px;

            .icon {
              color: #8091b7;
              font-size: 14px;

              &.del,
              &.down {
                cursor: pointer;

                &:hover {
                  color: #1e89ff;
                }
              }
            }

            .name {
              width: calc(100% - 60px);
              color: #1e89ff;
              font-size: 14px;
            }
          }
        }
      }

      .btn {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 4px 16px;
        color: var(---, #1e89ff);
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 22px;
        border: 1px solid var(---, #1e89ff);
        border-radius: 2px;
        /* 157.143% */

        .icon {
          font-size: 16px;
        }

        &.active,
        &:hover {
          color: #fff;
          background: #1e89ff;
        }

        &.no-border {
          color: var(---, #1e89ff);
          background: transparent;
          border: none;
        }
      }

      .log {
        display: flex;
        flex: 1 0 0;
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
        align-self: stretch;
        padding: 8px;
        white-space: pre-wrap;
        border: 1px solid var(---, #e5e6eb);
        border-radius: 2px;
      }

      .table-row {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        min-width: 100%;

        // grid-template-rows: repeat(3, 1fr);
        row-gap: 8px;
      }

      .file-encoding {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .file-code-title {
          display: flex;
          gap: 4px;
          align-items: center;

          &::after {
            display: block;
            width: 1px;
            height: 16px;
            margin: 0 24px;
            margin-left: 20px;
            background: var(---, #a8abb2);
            content: '';
          }
        }
      }
    }
  }

  // 标签
  .mid-line-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;

    :deep(.nancalui-select__multiple) {
      // flex-wrap: nowrap;
    }

    :deep(.nancalui-tag .nancalui-tag--default) {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  :deep(.nancalui-tag) {
  }

  :deep(.nancalui-tag .nancalui-tag--default .remove-button:hover) {
    color: #000000;
    background-color: transparent;
  }

  :deep(.nancalui-form__label) {
    align-self: auto;
  }

  .top-line {
    :deep(.nancalui-form__label) {
      align-self: flex-start;
    }
  }

  .codemirror {
    height: 400px !important;
    background-color: #fff;

    :deep(.CodeMirror) {
      height: 100% !important;
      overflow: hidden;
      box-shadow: none;
    }

    :deep(.CodeMirror-scroll) {
      box-sizing: border-box;
      height: 100%;
      margin-right: -6px;
      padding: 0;
      overflow-x: hidden !important;
      overflow-y: auto !important;

      .CodeMirror-sizer {
        border-right: none;

        .CodeMirror-lines {
          padding: 0;

          .CodeMirror-selected {
            height: 28px !important;
          }
        }

        .CodeMirror-cursors {
          top: 5px;
        }

        .CodeMirror-code > div {
          padding: 5px 0;
        }

        .CodeMirror-linenumber {
          padding: 0 6px;
          text-align: center;
        }

        .CodeMirror-line {
          padding: 0 10px;

          > span {
            padding-right: 10px !important;
            color: #046c5c;
            font-size: 14px;
            word-break: break-all;
          }
        }

        .CodeMirror-linebackground {
          background-color: #f0f2f5;
        }
      }
    }

    :deep(.CodeMirror-gutters) {
      width: 32px;
      min-height: 100%;
      background-color: #e6e8eb;
      border-right: none;
    }

    :deep(.CodeMirror-vscrollbar) {
      visibility: initial !important;

      &::-webkit-scrollbar-thumb {
        background-color: #b1bcd6;
        border-radius: 6px;

        &:hover {
          background-color: #b1bcd6;
        }
      }
    }
  }

  .logPop {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 200;
    box-sizing: border-box;
    width: 100%;
    height: 56px;
    background: #fff;

    &.showRun {
      height: 400px;
    }

    &-head {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      padding: 8px 16px 0;
      background: linear-gradient(180deg, #f3f3f5 0%, #fff 100%);

      .logTitle {
        width: 106px;
        height: 40px;
        color: #1e89ff;
        font-weight: bold;
        line-height: 40px;
        text-align: center;
        background: #fff;
        border: 1px solid #e5e6eb;
        border-bottom: none;
      }

      .logPop-btn {
        .icon {
          margin-left: 10px;
          font-size: 16px;

          &.show {
            transform: rotate(180deg);
          }
        }
      }
    }

    .logText {
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 56px);
      margin-top: 8px;
      padding: 16px;
      overflow-y: auto;
      word-break: break-all;
      background: linear-gradient(181deg, #f3f3f5 0.86%, #fff 11.07%);

      .textarea {
        min-height: 100%;
        padding: 16px;
        color: #000000bf;
        white-space: pre-wrap;
        word-break: break-all;
        background-color: transparent;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        &-pic {
          width: 48px;
          height: 48px;
        }
        &-text {
          margin-top: 16px;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
</style>
<style lang="scss">
  .page-top .table-top {
    display: flex;
    flex: 1 0 0;
    gap: 4px;
    align-items: center;

    margin-bottom: 8px;
    color: var(----, #606266);
    font-weight: 400;
    font-size: 14px;
    font-family: 'Source Han Sans CN';
    font-style: normal;
    line-height: 22px;

    .label {
      display: flex;
      gap: 4px;
      align-items: center;
      padding: 5px 8px;
    }

    .value {
      display: flex;
      flex: 1 0 0;
      gap: 16px;
      align-items: center;
      color: var(----, #1d2129);
    }
  }

  .data-upload {
    padding: 0 !important;

    .custom {
      width: 360px;

      & > div + div {
        margin-top: 8px;
      }

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: auto;
        height: 48px;
        padding: 16px 20px 8px 0px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: bolder;
        font-size: 14px;
        border-bottom: none !important;

        .title {
          display: flex;
          gap: 14px;
          align-items: center;

          &:before {
            display: block;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
        }

        .icon {
          color: #8091b7;
          font-size: 16px;
          cursor: pointer;
        }
      }

      &-name {
        padding: 0 20px;
      }

      &-color {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 12px;
        padding: 0 20px;
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;

        .nancalui-switch {
          margin-left: 6px;
        }

        &-label {
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          width: 20px;
          height: 20px;
          margin-right: 8px;
          border: 1px solid #fff;
          border-radius: 4px;

          .icon {
            display: none;
            color: #fff;
            font-size: 12px;
          }

          &.checked {
            box-shadow: 0 0 0 1px #447dfd;

            .icon {
              display: block;
            }
          }
        }
      }

      &-footer {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: flex-end;
        padding: 16px 20px;
      }
    }
  }
</style>
