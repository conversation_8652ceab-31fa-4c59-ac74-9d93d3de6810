<template>
  <div class="task-list">
    <CfTable
      v-if="state.pageType === 'tag'"
      actionWidth="220"
      ref="tableNoRef"
      :tableConfig="{
        data: state.dataSource,
        rowKey: 'id',
      }"
      :table-head-titles="tableHead"
      :paginationConfig="{
        total: state.total,
        pageSize: state.searchData.pageSize,
        currentPage: state.searchData.pageNum,
        onCurrentChange: (v) => {
          state.searchData.pageNum = v
          onSearch(false)
        },
        onSizeChange: (v) => {
          state.searchData.pageSize = v
          onSearch()
        },
      }"
    >
      <template #editor="{ row }">
        <!-- 提交完成 -->
        <n-button
          class="submit-btn"
          :disabled="
            row.auditStatus !== '待标注' &&
            row.auditStatus !== '审核不通过' &&
            row.auditStatus !== '待提交'
          "
          color="primary"
          variant="text"
          @click="submitTask(row)"
        >
          提交审批
        </n-button>
        <!-- 在线标注 -->
        <n-button
          class="online-btn"
          :disabled="row.auditStatus === '待审核' || row.auditStatus === '已完成'"
          color="primary"
          variant="text"
          @click="onlineStandardFn(row)"
        >
          标注
        </n-button>
        <!-- 协同链接 -->
        <n-button
          class="online-btn"
          :disabled="row.auditStatus === '待审核' || row.auditStatus === '已完成'"
          color="primary"
          variant="text"
          @click="generateCollaborativeConnection(row)"
        >
          协同链接
        </n-button>
        <!-- <n-button
          class="online-btn"
          :disabled="row.auditStatus !== '待标注' && row.auditStatus !== '审核不通过'"
          color="primary"
          variant="text"
          @click="deleteMarkTask(row)"
        >
          放弃
        </n-button> -->
      </template>
    </CfTable>

    <OnlineLabelTask
      v-if="state.pageType === 'excel'"
      :info="state.excelInfo"
      @onCancel="
        () => {
          state.pageType = 'tag'
        }
      "
    />
  </div>
</template>
<script setup>

import api from '@/api'
import Cookies from 'js-cookie'
import ClipboardJS from 'clipboard'
import { getToken, TokenKey } from '@/utils/auth'
  import { useRouter } from 'vue-router'
  import OnlineLabelTask from './OnlineLabelTask.vue'

  const router = useRouter()
  const { id: projectId } = router.currentRoute.value.query // 任务id
  const state = reactive({
    pageType: 'tag',
    excelInfo: {}, // 表格数据
    dataSource: [], // 表格数据
    searchData: {
      // 搜索数据
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
      condition: {
        projectId,
      },
    },
    total: 0, // 总条数
  })
  const emit = defineEmits(['onlineStandard'])
  const tableHead = computed(() => {
    // 表格表头
    return [
      { name: '任务名称', prop: 'taskName', width: 600 },
      { name: '任务类型', prop: 'taskType' },
      { name: '文件格式', prop: 'documentType' },
      { name: '预计完成时间', prop: 'completionTime' },
      { name: '状态', prop: 'auditStatus' },
    ]
  })
  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  const submitTask = (task) => {
    // 提交完成
    api.dataGovernance.submitApproval({ id: task.id }).then((res) => {
      // 成功
      if (res.success) {
        // 成功
        ElMessage.success('提交成功') // 成功提示
        onSearch() // 刷新表格
      }
    })
  }
  // 生成协同连接
  const generateCollaborativeConnection = (task) => {
    window.focus(); // 强制聚焦
    let authorization =''
    // 获取本用户令牌
    const token = getToken()
     if (token) {
      if (token === 'cross-domain-token') {
        let CookiesToken = Cookies.get(TokenKey)
        authorization = CookiesToken.replace(/%20/g, ' ')
      } else {
        authorization = token.replace(/%20/g, ' ')
      }
    }
    // 获取host
    // 生成协同连接
    const host = window.location.origin + '/collaborativeLink'
    const url = `${host}?id=${task.documentId}&taskId=${task.taskId}&authorization=${authorization}`
// 复制到剪贴板
ClipboardJS.copy(url)
ElMessage.success('复制成功') // 成功提示

    
  }

  // 在线标注
  const onlineStandardFn = (row) => {
    row.documentType === 'excel' ? (state.pageType = 'excel') : (state.pageType = 'tag')
    state.excelInfo = { ...row }
    if (state.pageType === 'excel') {
      return
    }
    emit('onlineStandard', { id: row.documentId, taskId: row.taskId })
  }

  // 放弃标注任务
  const deleteMarkTask = (task) => {
    api.dataGovernance
      .annotationDelete({ taskId: task.taskId, projectId: Number(projectId) })
      .then((res) => {
        if (res.success) {
          // 成功
          ElMessage.success('提交成功') // 成功提示
          onSearch() // 刷新表格
        }
      })
  }

  const onSearch = (reset = true) => {
    // 搜索
    if (reset) {
      // 重置页码
      state.searchData.pageNum = 1
    }
    api.dataGovernance.getAnnotationTask(state.searchData).then((res) => {
      // 成功
      if (res.success) {
        // 成功
        state.dataSource = res.data?.list || [] // 表格数据
        state.total = res.data?.total || 0 // 总条数
      }
    })
  }
  onSearch()
</script>
<style lang="scss" scoped>
  .task-list {
    background-color: #fff;
    height: 100%;
  }

  .more {
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
