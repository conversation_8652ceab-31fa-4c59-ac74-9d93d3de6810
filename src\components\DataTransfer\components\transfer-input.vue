<template>
  <span>123 <input type="text" name="" id="" /> </span>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  export default {
    name: '',
    props: { ruleList: Object, ruleIds: Array },
    setup(props) {
      const state = reactive({
        checkedValue: [87, 88],
        data: [],
        rightDefaultChecked: [],
      })
      const methods = {}
      onMounted(() => {})
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  :deep(.el-transfer-panel:last-of-type) {
    width: 298px;
    .el-input {
      display: none;
    }
  }
  :deep(.el-transfer-panel__filter) {
    width: calc(100% - 30px);
  }
  :deep(.el-input .el-input__inner),
  :deep(.el-textarea .el-textarea__inner) {
    background: #fff !important;
  }
  :deep(.el-input__wrapper) {
    // flex-direction: row-reverse;
    position: relative;
    padding-right: 30px;
    .el-input__prefix {
      position: absolute;
      right: 5px;
    }
  }
  :deep(.el-transfer-panel .el-transfer-panel__header) {
    background: #fff;
    height: 32px;
    .el-checkbox .el-checkbox__label {
      font-size: 13px;
      color: #333;
      span {
        position: relative;
        left: 5px;
        color: #333;
      }
    }
  }
  :deep(.el-transfer-panel__body) {
    .el-checkbox__label .el-input__inner {
      width: 50px;
      height: 20px;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 0 5px;
    }
  }
  :deep(.el-transfer__buttons) {
    display: inline-flex;
    vertical-align: middle;
    padding: 0 30px;
    flex-direction: column;
    align-items: flex-end;
  }
</style>
