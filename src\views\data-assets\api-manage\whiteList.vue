<template>
  <n-modal v-model="visiable" width="540px" title="白名单管理" @close="onClose(false)">
    <n-button @click="onRowAdd" color="primary" class="nc-m-b-10">
      <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
      添加</n-button
    >
    <n-public-table
      :tableData="state.tableData"
      :isDisplayAction="true"
      :table-head-titles="state.tableTitles"
      :showPagination="false"
      :actionWidth="80"
    >
      <template #Index="{ editor: { rowIndex } }">
        <span>{{ rowIndex + 1 }}</span>
      </template>
      <template #ip="{ editor: { row } }">
        <n-input v-model="row.ip" placeholder="请输入ip" />
      </template>
      <template #editor="{ editor: { rowIndex } }">
        <div class="edit-box">
          <n-button
            v-if="state.tableData.list.length > 1"
            variant="text"
            @click.prevent="onRowDelete(rowIndex)"
          >
            <SvgIcon icon="icon-delete-sign" />
          </n-button>
        </div>
      </template>
    </n-public-table>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
        <n-button variant="solid" @click="onConfirm">保存</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>
<script setup>
  import api from '@/api/index'
  import { getCurrentInstance, reactive, ref } from 'vue'
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
    ips: { type: Array, default: () => [] },
  })
  const state = reactive({
    tableTitles: [
      {
        prop: 'Index',
        name: '序号',
        slot: 'Index',
        width: 100,
      },
      {
        prop: 'ip',
        name: '白名单IP',
        slot: 'ip',
      },
    ],
    tableData: {
      list: [{ ip: '' }],
      total: 1,
    },
  })
  // 添加
  function onRowAdd() {
    state.tableData.list.push({ ip: '' })
  }
  // 删除
  function onRowDelete(index) {
    state.tableData.list.splice(index, 1)
  }
  // 保存
  async function onConfirm() {
    if (props.id) {
      const res = await api.documentManage.apiAuthorizeAdd({
        apiId: props.id,
        ips: state.tableData.list.filter((item) => item.ip).map((item) => item.ip),
      })
      if (res.success) {
        proxy.$message.success('操作成功')
      }
    } else {
      const ips = state.tableData.list.filter((item) => item.ip).map((item) => item.ip)
      emit('confirm', ips)
    }
    onClose()
  }
  // 初始化
  function init() {
    state.tableData.list = props.ips.map((item) => ({ ip: item }))
  }
  // 查询
  const emit = defineEmits(['update:modelValue', 'confirm'])
  function onClose() {
    visiable.value = false
    emit('update:modelValue', false)
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    async (val) => {
      visiable.value = val
      if (props.ips?.length && val) {
        init()
      }
    },
  )
</script>
