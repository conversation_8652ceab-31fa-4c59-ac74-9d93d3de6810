<template>
  <div :class="['container ', 'scroll-bar-style']">
    <div class="content-box">
      <UseFullscreen class="content-box-row" v-slot="{ toggle, isFullscreen }">
        <div class="content-box-row" :class="{ 'is-fullscreen': isFullscreen }">
          <div class="col">
            <div class="title">
              采集报表
              <div class="refresh icons">
                <svgIcon
                  icon="icon-refresh"
                  @click="
                    () => {
                      collectInfo.datasourceCount = null
                      getMapData()
                    }
                  "
                /> </div
              ><div class="fullscreen icons">
                <svgIcon
                  icon="icon-fullscreen"
                  @click="
                    () => {
                      toggle()
                    }
                  "
                />
              </div>
            </div>
            <div class="col-content" v-loading="!collectInfo.datasourceCount">
              <div class="col-content-row">
                <div class="col-content-row-item">
                  <div class="col-content-row-item-title"> 数据源（SFTP/FTP） </div>
                  <div class="col-content-row-item-content">
                    {{ collectInfo.datasourceCount }}
                  </div>
                </div>
                <div class="col-content-row-item">
                  <div class="col-content-row-item-title"> 数据采集 </div>
                  <div class="col-content-row-item-content">
                    {{ collectInfo.collectJobCount }}
                  </div>
                </div>
                <div class="col-content-row-item">
                  <div class="col-content-row-item-title"> 数据上传 </div>
                  <div class="col-content-row-item-content">
                    {{ collectInfo.dataUploadCount }}
                  </div>
                </div>
                <div class="col-content-row-item">
                  <div class="col-content-row-item-title">
                    数据总量
                    <n-tooltip
                      content="展示数据目录下存储的全部原始文件所占用的空间大小，同环比数据于每晚凌晨进行更新"
                      position="left"
                      :enterable="false"
                    >
                      <SvgIcon class="illustrate" icon="icon-illustrate" />
                    </n-tooltip>
                  </div>
                  <div class="col-content-row-item-content">
                    {{
                      new Intl.NumberFormat('en-US').format(
                        collectInfo.totalDataSize / 1024 / 1024 / 1024,
                      )
                    }}
                    G</div
                  >
                  <!-- 周环比 日同比 -->
                  <div class="col-content-row-item-content-trend">
                    <div class="col-content-row-item-content-trend-item">
                      <div class="col-content-row-item-content-trend-item-title"> 周环比 </div>
                      <div class="col-content-row-item-content-trend-item-content">
                        <span class="col-content-row-item-content-trend-item-content-icon">
                          <SvgIcon
                            :icon="
                              { '<': 'icon-down', '>': 'icon-up', '=': 'icon-equal' }[
                                collectInfo.weeklyYearOnYear > 0
                                  ? '>'
                                  : collectInfo.weeklyYearOnYear < 0
                                  ? '<'
                                  : '='
                              ]
                            "
                          />
                        </span>
                        <span class="col-content-row-item-content-trend-item-content-number">
                          {{ collectInfo.weeklyYearOnYear }}%
                        </span>
                      </div>
                    </div>
                    <div class="col-content-row-item-content-trend-item">
                      <div class="col-content-row-item-content-trend-item-title"> 日环比 </div>
                      <div class="col-content-row-item-content-trend-item-content">
                        <span class="col-content-row-item-content-trend-item-content-icon">
                          <SvgIcon
                            :icon="
                              { '<': 'icon-down', '>': 'icon-up', '=': 'icon-equal' }[
                                collectInfo.dailyBasis > 0
                                  ? '>'
                                  : collectInfo.dailyBasis < 0
                                  ? '<'
                                  : '='
                              ]
                            "
                          />
                        </span>
                        <span class="col-content-row-item-content-trend-item-content-number">
                          {{ collectInfo.dailyBasis }}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UseFullscreen>
      <div class="content-box-row col-3">
        <UseFullscreen v-slot="{ toggle, isFullscreen }">
          <div class="col" :class="{ 'is-fullscreen': isFullscreen }">
            <div class="title">
              文件类型占比 <span class="sub-title">近10天采集数据类型占比</span>
              <div class="refresh icons">
                <svgIcon
                  icon="icon-refresh"
                  @click="
                    () => {
                      state.fileTypePercentageChart?.dispose()
                      state.fileTypePercentageChart = null
                      getMapData().then(() => {
                        fileTypePercentageFn(collectInfo.fileTypeCount)
                      })
                    }
                  "
                /> </div
              ><div class="fullscreen icons">
                <svgIcon
                  icon="icon-fullscreen"
                  @click="
                    () => {
                      toggle()
                    }
                  "
                /> </div
            ></div>
            <div class="col-content" v-loading="!state.fileTypePercentageChart">
              <div
                :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
                id="fileTypePercentage"
              ></div>
            </div>
          </div>
        </UseFullscreen>
        <UseFullscreen v-slot="{ toggle, isFullscreen }">
          <div class="col" :class="{ 'is-fullscreen': isFullscreen }">
            <div class="title">
              资源利用情况 <span class="sub-title">存储服务器资源占用情况</span>
              <div class="refresh icons">
                <svgIcon
                  icon="icon-refresh"
                  @click="
                    () => {
                      state.resourceUtilizationChart?.dispose()
                      state.resourceUtilizationChart = null
                      getMapData().then(() => {
                        resourceUtilizationFn([
                          {
                            value: (
                              collectInfo.resourceUtilizationUsedSize /
                              1024 /
                              1024 /
                              1024
                            ).toFixed(2),
                            name: '已使用空间',
                          },
                          {
                            value: (
                              (collectInfo.resourceUtilizationTotalSize -
                                collectInfo.resourceUtilizationUsedSize) /
                              1024 /
                              1024 /
                              1024
                            ).toFixed(2),
                            name: '未使用空间',
                          },
                        ])
                      })
                    }
                  "
                /> </div
              ><div class="fullscreen icons">
                <svgIcon
                  icon="icon-fullscreen"
                  @click="
                    () => {
                      toggle()
                    }
                  "
                /> </div
            ></div>
            <div class="col-content" v-loading="!state.resourceUtilizationChart">
              <div
                :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
                id="resourceUtilization"
              ></div>
            </div>
          </div>
        </UseFullscreen>
        <UseFullscreen v-slot="{ toggle, isFullscreen }">
          <div class="col" :class="{ 'is-fullscreen': isFullscreen }">
            <div class="title">
              数据来源排行榜
              <div class="refresh icons">
                <svgIcon icon="icon-refresh" @click="updateDataSourceRanking" /> </div
              ><div class="fullscreen icons">
                <svgIcon
                  icon="icon-fullscreen"
                  @click="
                    () => {
                      toggle()
                      updateDataSourceRanking()
                    }
                  "
                />
              </div>
            </div>
            <div class="col-content" v-loading="!collectInfo.dataSourcesRanking">
              <div :class="['canvas', 'trend', 'ranking', state.isLzos ? 'isLzos' : '']">
                <span class="ranking-update-time"
                  >{{ timestampToTime(new Date().getTime(), true) }}（份）</span
                >
                <!-- 1 -->
                <div class="ranking-list">
                  <div
                    class="ranking-list-item"
                    v-for="(item, index) in collectInfo.dataSourcesRanking || 4"
                    :key="index"
                  >
                    <i class="ranking-list-item-index">{{ index + 1 }} </i>
                    <div class="ranking-list-item-name" :title="item.title"> {{ item.title }} </div>
                    <div
                      class="ranking-list-item-content"
                      :style="{
                        width: collectInfo.dataSourcesRanking
                          ? ((item?.count || 0) /
                              (collectInfo.dataSourcesRanking?.reduce(
                                (acc, cur) => (acc > cur?.count ? acc : cur?.count),
                                collectInfo.dataSourcesRanking?.[0]?.count || 0,
                              ) || 1)) *
                              100 +
                            '%'
                          : '0px',
                      }"
                    >
                      <svg
                        width="100%"
                        height="16"
                        viewBox="0 0 1000 16"
                        preserveAspectRatio="none"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect x="0.474823" width="100%" height="16" fill="currentColor" />
                      </svg>
                    </div>
                    <div class="ranking-list-item-count"> {{ item.count }} </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </UseFullscreen>
      </div>
      <UseFullscreen class="content-box-row" v-slot="{ toggle, isFullscreen }">
        <div
          ref="taskCollectionTrend"
          class="content-box-row col-1"
          :class="{ 'is-fullscreen': isFullscreen }"
        >
          <div class="col">
            <div class="title">
              任务采集趋势 <span class="sub-title">近10天采集任务趋势</span>

              <div class="refresh icons">
                <svgIcon icon="icon-refresh" @click="getMapData(true)" />
              </div>
              <div class="fullscreen icons">
                <svgIcon
                  icon="icon-fullscreen"
                  @click="
                    () => {
                      toggle()
                      getMapData(true)
                    }
                  "
                /> </div
            ></div>
            <div class="col-content" v-loading="!state.taskCollectionTrendChart">
              <div
                :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
                id="taskCollectionTrend"
              ></div>
            </div>
          </div>
        </div>
      </UseFullscreen>
    </div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts'
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { timestampToTime } from '@/const/public.js'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { UseFullscreen } from '@vueuse/components'
  // 导出弹框组件

  const store = useStore()
  const router = useRouter()
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    colorList: [
      '#447DFD',
      '#6E9EFF',
      '#04C495',
      '#FF7D00',
      '#F63838',
      '#2F5CD6',
      '#009E7C',
      '#26D1A1',
      '#FF9729',
      '#D96200',
      '#FF6963',
      '#CF252B',
    ],
    taskCollectionTrendChart: null,
    resourceUtilizationChart: null,
    fileTypePercentageChart: null,
    // 采集信息
    collectInfo: {},
    // 资源利用率
  })
  const { collectInfo } = toRefs(state)

  // 获取画布数据
  const getMapData = (init) => {
    state.qualityLoading = true
    return new Promise((resolve, reject) => {
      // 是否响应
      let isResponse
      api.dataManagement.collectReportDetails().then((res) => {
        state.qualityLoading = false
        let { success, data } = res
        if (success) {
          collectInfo.value = data
        }
        switch (isResponse) {
          case undefined:
            isResponse = false
            break
          case false:
            isResponse = true
          default:
            resolve()
            break
        }
      })
      if (init) {
        state.taskCollectionTrendChart?.dispose()
        state.taskCollectionTrendChart = undefined
      }
      api.dataManagement
        .collectJobInstance({
          startDate: timestampToTime(new Date().getTime() - 1000 * 60 * 60 * 24 * 10),
          endDate: timestampToTime(new Date().getTime()),
        })
        .then((res) => {
          let { success, data } = res
          if (success) {
            data && taskCollectionTrendFn(data)
          }
          switch (isResponse) {
            case undefined:
              isResponse = false
              break
            case false:
              isResponse = true
            default:
              resolve()
              break
          }
        })
    })
  }
  // 文件类型占比画布渲染
  const fileTypePercentageFn = (data) => {
    const chartDom = document.getElementById('fileTypePercentage')
    state.fileTypePercentageChart?.dispose()
    const chart = echarts.init(chartDom)
    const option = {
      color: state.colorList,
      legend: {
        bottom: '0%',
        left: 'center',
        icon: 'circle',
        padding: [0, 60],
        type: 'scroll',
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',
        className: 'quality-tooltip',
        padding: 0,
        formatter: function (item) {
          return `<div class="quality-tooltip-box">
    <div class="quality-tooltip-box-title">文件类型占比</div>
    <div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">${item.name}</div><div class="num">${item.value}条</div></div>
    <div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">占比</div><div class="num">${item.percent}%</div></div>
    </div>`
        },
      },
      series: [
        {
          name: '文件类型占比',
          type: 'pie',
          radius: ['50%', '76%'],
          center: ['50%', '46%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          padAngle: 2,
          label: {
            orient: 'vertical',
            left: 'left',
            formatter: (item) => [item.percent + '%'].join('\n'),
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 40,
              formatter: (item) => {
                return ['{a|' + item.name + '}', '{b|' + item.value + '}'].join('\n')
              },
              rich: {
                a: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  height: 30,
                },
              },
            },
          },
          data: data.map((_) => ({ name: _.fileTypeName, value: _.count })),
        },
      ],
    }
    chart.setOption(option)
    state.fileTypePercentageChart = chart
  }
  // 资源利用情况画布渲染
  const resourceUtilizationFn = (data) => {
    const chartDom = document.getElementById('resourceUtilization')
    state.resourceUtilizationChart?.dispose()
    const chart = echarts.init(chartDom)
    const option = {
      color: ['#447DFD', '#E5E5E5'],
      legend: {
        bottom: '0%',
        left: 'center',
        icon: 'circle',
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',
        className: 'quality-tooltip',
        padding: 0,
        formatter: function (item) {
          return `<div class="quality-tooltip-box">
    <div class="quality-tooltip-box-title">资源利用情况</div>
    <div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">${item.name}</div><div class="num">${item.value}GB</div></div>
    <div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">占比</div><div class="num">${item.percent}%</div></div>
    </div>`
        },
      },
      series: [
        {
          name: '质量评分',
          type: 'pie',
          radius: ['50%', '76%'],
          center: ['50%', '46%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          padAngle: 2,
          label: {
            orient: 'vertical',
            left: 'left',
            formatter: (item) => [item.percent + '%'].join('\n'),
          },
          emphasis: {
            label: {
              show: true,
              position: 'inside',
              fontSize: 40,
              formatter: (item) => {
                return [
                  '{a|' + item.name + '}',
                  '{b|' + (Math.round(item.value * 100) / 100).toFixed(2) + '%' + '}',
                ].join('\n')
              },
              rich: {
                a: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  height: 30,
                },
              },
            },
          },
          data: data,
        },
      ],
    }
    chart.setOption(option)
    state.resourceUtilizationChart = chart
  }
  // 任务采集趋势画布渲染
  const taskCollectionTrendFn = (
    { failList = [], successList = [], repeatList = [] } = {
      failList: [],
      successList: [],
      repeatList: [],
    },
  ) => {
    const chartDom = document.getElementById('taskCollectionTrend')
    const chart = echarts.init(chartDom)
    const option = {
      color: ['#04C495', '#447DFD', '#F63838'],
      legend: {
        bottom: '24',
        left: 'center',
        icon: 'rect',
      },
      grid: { left: 24, right: 24, bottom: 48, top: 48, containLabel: true },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#ffffff',
        borderColor: '#ffffff',
        className: 'task-collection-tooltip',
        padding: 0,
        formatter: function (value) {
          // return JSON.stringify(value)
          let htmlTxt = `<div class="quality-tooltip-box">
  <div class="quality-tooltip-box-title">${value[0].name}</div>`
          value.forEach((item) => {
            htmlTxt += ` <div class="quality-tooltip-box-item">
    <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2.4137" y="4" width="7.9886" height="3" fill="${item.color}"/>
    </svg><div class="text">${item.seriesName}</div><div class="num">${item.value} 次</div>
  </div>`
          })
          return htmlTxt
        },
      },
      xAxis: {
        type: 'category',
        data: successList.map((_) => _.date),
        boundaryGap: true,
      },
      yAxis: {
        type: 'value',
        name: '任务数量（次）',
      },
      series: [
        {
          name: '成功',
          data: successList.map((_) => _.count),
          type: 'line',
          showSymbol: false,
          areaStyle: { opacity: 0.15 },
        },
        {
          name: '重跑',
          data: repeatList.map((_) => _.count),
          type: 'line',
          showSymbol: false,
          areaStyle: { opacity: 0.15 },
        },
        {
          name: '失败',
          data: failList.map((_) => _.count),
          type: 'line',
          showSymbol: false,
          areaStyle: { opacity: 0.15 },
        },
      ],
    }
    chart.setOption(option)
    state.taskCollectionTrendChart = chart
  }

  // 更新数据来源排行榜
  // 更新数据来源排行榜
  const updateDataSourceRanking = () => {
    collectInfo.value.dataSourcesRanking = null
    api.dataManagement.collectReportDetails().then((res) => {
      let { success, data } = res
      if (success) {
        new Promise((resolve) => {
          setTimeout(() => {
            resolve(data)
          }, 500)
        }).then((data) => {
          collectInfo.value = data
        })
      }
    })
  }

  // 任务采集趋势画布渲染
  // 获取数据
  const getDataFn = () => {
    getMapData().then(() => {
      resourceUtilizationFn([
        {
          value: (collectInfo.value.resourceUtilizationUsedSize / 1024 / 1024 / 1024).toFixed(2),
          name: '已使用空间',
        },
        {
          value: (
            (collectInfo.value.resourceUtilizationTotalSize -
              collectInfo.value.resourceUtilizationUsedSize) /
            1024 /
            1024 /
            1024
          ).toFixed(2),
          name: '未使用空间',
        },
      ])
      fileTypePercentageFn(collectInfo.value.fileTypeCount)
    })
  }

  onBeforeUnmount(() => {
    window.onresize = null
  })
  onMounted(() => {
    nextTick(() => {
      getDataFn()
      window.onresize = () => {
        getDataFn()
      }
    })
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .container {
    width: 100%;
    height: calc(100vh - 50px);
    padding: 12px;
    overflow-y: auto;
    font-family: PingFangSC-Semibold, PingFang SC;
    background-color: #eee;
    .content-box {
      &-row {
        & + .content-box-row {
          margin-top: 8px;
        }

        .col {
          box-sizing: border-box;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
          .title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            height: 52px;
            padding: 0 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            border-bottom: 1px solid #c5d0ea;

            .illustrate {
              margin-left: 8px;
              color: #8091b7;
              &:hover {
                color: $themeBlue;
              }
            }
            .sub-title {
              color: var(---, rgba(0, 0, 0, 0.55));

              font-family: 'Source Han Sans CN';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
            }

            &:before {
              position: absolute;
              top: 17px;
              left: 0;
              width: 4px;
              height: 18px;
              background: var(
                --Radial,
                radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
              );
              border-radius: 0 4px 4px 0;
              content: '';
            }
            span {
              margin-left: 8px;
              color: rgba(0, 0, 0, 0.55);
              font-weight: normal;
              font-size: 14px;
            }
          }
          .col-content {
            padding: 16px;
            .canvas {
              display: flex;
              height: 250px;
              flex-direction: column;
              align-items: center;
              gap: 16px;
            }
            .ranking {
              padding: 8px 24px;
              display: block;
              &-update-time {
                color: var(---, rgba(0, 0, 0, 0.55));

                font-family: 'Source Han Sans CN';
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
              }
              &-list {
                height: calc(100% - 20px);
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                &-item {
                  display: grid;
                  grid-template-columns: 20px 77px 1fr 34px;
                  align-items: center;
                  color: var(----, rgba(0, 0, 0, 0.75));
                  font-family: 'Source Han Sans CN';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px;
                  &-index {
                    display: flex;
                    width: 100%;
                    height: 16px;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    border-radius: 13px;

                    color: var(--100, #fff);
                    text-align: right;
                    font-family: 'Source Han Sans CN';
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 20px;
                    margin-right: 4px;
                  }
                  &-name {
                    padding: 0 8px 0 4px;
                    color: var(--100, rgba(0, 0, 0, 0.75));
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  &-content {
                    display: flex;
                    align-items: center;
                    border-radius: 4px;
                    overflow: hidden;
                    // 动画
                    transition: all 0.3s ease-in-out;
                  }
                  &-count {
                    color: var(----, rgba(0, 0, 0, 0.75));
                    text-align: center;
                    font-family: 'PingFang SC';
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16px; /* 133.333% */
                  }
                  &:nth-child(1) {
                    color: #f63838;
                    .ranking-list-item-index {
                      background: #f63838;
                    }
                  }
                  &:nth-child(2) {
                    color: #ff7d00;
                    .ranking-list-item-index {
                      background: #ff7d00;
                    }
                  }
                  &:nth-child(3) {
                    color: #04c495;
                    .ranking-list-item-index {
                      background: #04c495;
                    }
                  }
                  &:nth-child(4) {
                    color: #447dfd;
                    .ranking-list-item-index {
                      background: #447dfd;
                    }
                  }
                }
              }
            }
            &-row {
              display: flex;
              flex-wrap: wrap;
              &-item {
                display: flex;
                padding: 16px;
                height: 110px;
                // height: 10.7vh;
                flex-direction: column;
                align-items: flex-start;
                flex: 1 0 0;
                border-radius: 6px;
                position: relative;
                &::before {
                  position: absolute;
                  bottom: 12px;
                  right: 16px;
                  width: 32px;
                  height: 32px;
                }
                &:nth-child(1) {
                  background: var(---, #447dfd);
                  &::before {
                    content: url('data:image/svg+xml;%20charset=utf8,%3Csvg%20width=%2232%22%20height=%2232%22%20viewBox=%220%200%2032%2032%22%20fill=%22none%22%20xmlns=%22http://www.w3.org/2000/svg%22%3E%0A%3Cpath%20d=%22M28.3077%208.20037C27.6239%208.63659%2026.8119%209.01753%2025.9149%209.34371C23.3294%2010.2839%2019.8238%2010.8459%2016%2010.8459C12.1762%2010.8459%208.67064%2010.2839%206.08513%209.34371C5.18813%209.01753%204.37613%208.63659%203.69232%208.20037L3.69233%2015.6838C4.10899%2016.5249%205.10847%2017.4226%206.84068%2018.21C9.13324%2019.2521%2012.3706%2019.9228%2016%2019.9228C19.6294%2019.9228%2022.8668%2019.2521%2025.1593%2018.21C26.8915%2017.4226%2027.891%2016.5249%2028.3077%2015.6839L28.3077%208.20037Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3Cpath%20d=%22M28.3077%2018.6594C27.6285%2019.1869%2026.8351%2019.6452%2025.987%2020.0307C23.3796%2021.2159%2019.8477%2021.9228%2016%2021.9228C12.1523%2021.9228%208.62047%2021.2159%206.01307%2020.0307C5.16489%2019.6452%204.37152%2019.1869%203.69233%2018.6594L3.69234%2026.2934C3.69234%2028.7652%209.20265%2030.7689%2016%2030.7689C22.7973%2030.7689%2028.3076%2028.7652%2028.3076%2026.2934L28.3077%2018.6594Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3Cpath%20d=%22M28.3077%205.53816C28.3077%205.54528%2028.3077%205.5524%2028.3076%205.55952C28.1976%205.71876%2028.041%205.89303%2027.8226%206.08077C27.2622%206.56259%2026.3927%207.04182%2025.2314%207.46412C22.917%208.30574%2019.6533%208.84585%2016%208.84585C12.3467%208.84585%209.08307%208.30574%206.76862%207.46412C5.60728%207.04182%204.73782%206.56259%204.17739%206.08077C3.95901%205.89303%203.8024%205.71876%203.69247%205.55952C3.69237%205.5524%203.69232%205.54528%203.69232%205.53816C3.69232%203.15909%209.20266%201.23047%2016%201.23047C22.7974%201.23047%2028.3077%203.15909%2028.3077%205.53816Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3C/svg%3E');
                  }
                }
                &:nth-child(2) {
                  background: var(---, #04c495);
                  &::before {
                    content: url('data:image/svg+xml;%20charset=utf8,%3Csvg%20width=%2232%22%20height=%2232%22%20viewBox=%220%200%2032%2032%22%20fill=%22none%22%20xmlns=%22http://www.w3.org/2000/svg%22%3E%0A%3Cpath%20d=%22M15.4759%201.13881C15.7998%200.95373%2016.1974%200.95373%2016.5213%201.13881L27.5061%207.41585C28.0016%207.69896%2028.0016%208.41335%2027.5061%208.69646L16.5213%2014.9735C16.1974%2015.1586%2015.7998%2015.1586%2015.4759%2014.9735L4.49112%208.69646C3.99567%208.41335%203.99567%207.69896%204.49112%207.41585L15.4759%201.13881Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3Cpath%20d=%22M2.29922%2011.1468C2.29922%2010.5805%202.91094%2010.2255%203.40258%2010.5065L14.4128%2016.798C14.7411%2016.9856%2014.9437%2017.3347%2014.9437%2017.7128V30.2626C14.9437%2030.8289%2014.3319%2031.1839%2013.8403%2030.9029L2.83005%2024.6114C2.5018%2024.4238%202.29922%2024.0747%202.29922%2023.6966V11.1468Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3Cpath%20d=%22M28.5828%2010.5065C29.0745%2010.2255%2029.6862%2010.5805%2029.6862%2011.1468V15.6489C28.3342%2014.653%2026.0681%2014%2023.5007%2014C23.126%2014%2022.7577%2014.0139%2022.3978%2014.0408L28.5828%2010.5065Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3Cpath%20d=%22M23.4956%2016.3262C22.0205%2016.3262%2020.5973%2016.5905%2019.5263%2017.0167C18.9925%2017.2291%2018.5217%2017.4914%2018.1753%2017.8015C17.8377%2018.1039%2017.5667%2018.5011%2017.5595%2018.9783C17.5517%2019.0232%2017.5476%2019.0693%2017.5476%2019.1164V28.4136C17.5476%2028.9886%2017.8394%2029.4577%2018.1996%2029.8018C18.5578%2030.144%2019.0337%2030.4153%2019.5558%2030.6262C20.6043%2031.0499%2021.9978%2031.2938%2023.496%2031.2938C24.9943%2031.2938%2026.3877%2031.0499%2027.4362%2030.6262C27.9584%2030.4153%2028.4342%2030.144%2028.7925%2029.8018C29.1527%2029.4576%2029.4445%2028.9886%2029.4445%2028.4136V19.193C29.4543%2019.1298%2029.4595%2019.0648%2029.4595%2018.998C29.4595%2018.51%2029.1818%2018.1057%2028.8375%2017.8001C28.4886%2017.4904%2028.0145%2017.2285%2027.4776%2017.0163C26.4002%2016.5905%2024.9706%2016.3262%2023.4956%2016.3262ZM19.1476%2020.7403C19.2755%2020.7951%2019.4084%2020.8465%2019.5451%2020.8947C20.6141%2021.2713%2022.0317%2021.4865%2023.4956%2021.4865C24.9595%2021.4865%2026.3835%2021.2713%2027.4589%2020.895C27.5913%2020.8487%2027.7202%2020.7994%2027.8445%2020.7469V22.2355C27.8445%2022.2409%2027.844%2022.2581%2027.8262%2022.2933C27.8067%2022.3317%2027.7663%2022.3912%2027.6873%2022.4668C27.5245%2022.6223%2027.2464%2022.7992%2026.8368%2022.9647C26.0219%2023.294%2024.8412%2023.5157%2023.496%2023.5157C22.1509%2023.5157%2020.9701%2023.294%2020.1552%2022.9647C19.7457%2022.7992%2019.4676%2022.6223%2019.3048%2022.4668C19.2257%2022.3912%2019.1853%2022.3317%2019.1659%2022.2933C19.148%2022.2581%2019.1476%2022.2409%2019.1476%2022.2355V20.7403ZM19.1476%2027.4256C19.2794%2027.491%2019.416%2027.5518%2019.5558%2027.6083C20.6043%2028.032%2021.9978%2028.2759%2023.496%2028.2759C24.9943%2028.2759%2026.3877%2028.032%2027.4362%2027.6083C27.5761%2027.5518%2027.7126%2027.491%2027.8445%2027.4256V28.4136C27.8445%2028.4189%2027.844%2028.4361%2027.8262%2028.4714C27.8067%2028.5098%2027.7663%2028.5693%2027.6873%2028.6448C27.5245%2028.8003%2027.2464%2028.9773%2026.8368%2029.1427C26.0219%2029.472%2024.8412%2029.6938%2023.496%2029.6938C22.1509%2029.6938%2020.9701%2029.472%2020.1552%2029.1427C19.7457%2028.9773%2019.4676%2028.8003%2019.3048%2028.6448C19.2257%2028.5693%2019.1853%2028.5098%2019.1659%2028.4714C19.148%2028.4361%2019.1476%2028.4189%2019.1476%2028.4136V27.4256ZM27.8445%2025.3957C27.8445%2025.401%2027.844%2025.4182%2027.8262%2025.4535C27.8067%2025.4919%2027.7663%2025.5514%2027.6873%2025.6269C27.5245%2025.7824%2027.2464%2025.9594%2026.8368%2026.1249C26.0219%2026.4541%2024.8412%2026.6759%2023.496%2026.6759C22.1509%2026.6759%2020.9701%2026.4541%2020.1552%2026.1249C19.7457%2025.9594%2019.4676%2025.7824%2019.3048%2025.6269C19.2257%2025.5514%2019.1853%2025.4919%2019.1659%2025.4535C19.148%2025.4182%2019.1476%2025.401%2019.1476%2025.3957V24.2655C19.2794%2024.3308%2019.416%2024.3917%2019.5558%2024.4482C20.6043%2024.8718%2021.9978%2025.1157%2023.496%2025.1157C24.9943%2025.1157%2026.3877%2024.8718%2027.4362%2024.4482C27.5761%2024.3917%2027.7126%2024.3308%2027.8445%2024.2655V25.3957Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3C/svg%3E%0A');
                  }
                }
                &:nth-child(3) {
                  background: var(---, #ff7d00);
                  &::before {
                    content: url('data:image/svg+xml;%20charset=utf8,%3Csvg%20width=%2232%22%20height=%2232%22%20viewBox=%220%200%2032%2032%22%20fill=%22none%22%20xmlns=%22http://www.w3.org/2000/svg%22%3E%0A%3Cpath%20d=%22M4.92309%202.46191C3.56362%202.46191%202.46155%203.56398%202.46155%204.92345V27.0773C2.46155%2028.4368%203.56362%2029.5388%204.92309%2029.5388H27.0769C28.4364%2029.5388%2029.5385%2028.4368%2029.5385%2027.0773V4.92345C29.5385%203.56398%2028.4364%202.46191%2027.0769%202.46191H4.92309ZM25.5302%2011.9006L18.6379%2019.5588C18.3673%2019.8595%2017.9661%2020.008%2017.5649%2019.956C17.1637%2019.904%2016.8136%2019.6581%2016.6286%2019.2983L13.9983%2014.1839L8.29945%2020.516C7.84473%2021.0213%207.06653%2021.0622%206.56128%2020.6075C6.05604%2020.1528%206.01508%2019.3746%206.4698%2018.8693L13.3621%2011.2112C13.6328%2010.9105%2014.0339%2010.762%2014.4351%2010.814C14.8364%2010.866%2015.1864%2011.1119%2015.3714%2011.4717L18.0017%2016.586L23.7006%2010.254C24.1553%209.74871%2024.9335%209.70776%2025.4387%2010.1625C25.944%2010.6172%2025.9849%2011.3954%2025.5302%2011.9006Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3C/svg%3E%0A');
                  }
                }
                &:nth-child(4) {
                  background: var(---, #2f5cd6);
                  &::before {
                    content: url('data:image/svg+xml;%20charset=utf8,%3Csvg%20width=%2232%22%20height=%2232%22%20viewBox=%220%200%2032%2032%22%20fill=%22none%22%20xmlns=%22http://www.w3.org/2000/svg%22%3E%0A%3Cpath%20d=%22M26.0257%2029.5388C25.4734%2029.5388%2025.0257%2029.0911%2025.0257%2028.5388V18.5047C25.0257%2017.9524%2024.5779%2017.5047%2024.0257%2017.5047H7.97437C7.42208%2017.5047%206.97437%2017.9524%206.97437%2018.5047V28.5388C6.97437%2029.0911%206.52665%2029.5388%205.97437%2029.5388H3.96582C3.56686%2029.5388%203.18425%2029.3804%202.90214%2029.0982C2.62003%2028.8161%202.46155%2028.4335%202.46155%2028.0346V3.96619C2.46155%203.56723%202.62003%203.18461%202.90214%202.90251C3.18425%202.6204%203.56686%202.46191%203.96582%202.46191H22.6929C23.2234%202.46191%2023.7321%202.67263%2024.1072%203.0477L28.9527%207.89322C29.3278%208.26829%2029.5385%208.777%2029.5385%209.30744V28.0346C29.5385%2028.4335%2029.38%2028.8161%2029.0979%2029.0982C28.8158%2029.3804%2028.4332%2029.5388%2028.0342%2029.5388H26.0257ZM22.0171%2028.5388C22.0171%2029.0911%2021.5694%2029.5388%2021.0171%2029.5388H10.9829C10.4306%2029.5388%209.98292%2029.0911%209.98292%2028.5388V21.5132C9.98292%2020.9609%2010.4306%2020.5132%2010.9829%2020.5132H21.0171C21.5694%2020.5132%2022.0171%2020.9609%2022.0171%2021.5132V28.5388Z%22%20fill=%22white%22%20fill-opacity=%220.28%22/%3E%0A%3C/svg%3E%0A');
                  }
                }
                &-title {
                  color: var(--100, #fff);

                  font-family: 'Source Han Sans CN';
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                }
                &-content {
                  color: var(--100, #fff);
                  font-feature-settings: 'clig' off, 'liga' off;

                  font-family: 'DIN Alternate';
                  font-size: 24px;
                  font-style: normal;
                  font-weight: 700;
                  line-height: 32px;
                }
                &-content-trend {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  align-self: stretch;
                  color: var(--100, #fff);
                  font-family: 'Source Han Sans CN';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px;
                  &-item {
                    display: flex;
                    align-items: center;
                    gap: 2px;
                    &-content-icon {
                      vertical-align: text-bottom;
                    }
                  }
                  .yy-icon {
                    width: 14px;
                    height: 14px;
                  }
                }
                .illustrate {
                  position: absolute;
                  top: 20px;
                  right: 16px;
                  margin-left: 8px;
                  color: #8091b7;
                  &:hover {
                    color: $themeBlue;
                  }
                }
              }
              .col-content-row-item + .col-content-row-item {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }
    .col-3 {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 8px;
      .col > .col-content {
        padding: 16px 0;
      }
    }
    .col-1 {
      .col > .col-content {
        padding: 0;
      }
    }
  }
  .refresh {
    margin-left: auto;
  }
  .icons {
    position: relative;
    & + .icons {
      margin-left: 8px;
    }
    .yy-icon {
      position: relative;
      cursor: pointer;
      color: #8091b7;
      z-index: 2;
      &:hover {
        color: $themeBlue;
      }
    }
    &:hover {
      &::before {
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #e3ecff;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 5px;
      }
    }
  }

  .is-fullscreen {
    height: 100%;
    .col,
    .canvas {
      height: 100% !important;
    }
    .col-content {
      height: calc(100% - 52px);
    }
  }
</style>

<style lang="scss">
  @import '@/styles/variables.scss';
  .quality-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: center;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 72px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
  .task-collection-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 32px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
</style>
