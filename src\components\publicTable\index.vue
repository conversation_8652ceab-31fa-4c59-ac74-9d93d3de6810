<template>
  <!-- 统一表格 -->
  <div class="common-table">
    <div class="page-top">
      <slot name="pageTop"></slot>
    </div>
    <div class="page-mid">
      <el-table
        ref="publicTable"
        v-loading="state.loading"
        :data="state.tableData"
        :stripe="stripe"
        :height="state.tableHeight"
        :max-height="maxHeight"
        :row-key="getRowKeys"
        :header-cell-style="{ color: '#333', fontSize: '12px' }"
        :cell-style="{ color: '#333', fontSize: '12px' }"
        :border="showBorder"
        size="small"
        @cell-click="cellClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 是否需要栏 -->
        <el-table-column
          v-if="isNeedSelection"
          type="selection"
          :reserve-selection="true"
          :selectable="selectable"
          width="85"
        />
        <!-- 表头 -->
        <template v-for="item in tableHeadTitles">
          <!-- 操作列/自定义列 -->
          <el-table-column
            v-if="item.slot"
            :key="item.prop"
            :label="item.name"
            :prop="item.prop"
            align="left"
            :width="item.width"
          >
            <template #header>
              <div :title="item.name"
                >{{ item.name
                }}<SvgIcon
                  class="illustrate"
                  v-if="item.headerIcon"
                  :icon="item.headerIcon"
                  @click.prevent="headerIconClickFn(item)"
                />
              </div>
            </template>
            <template #default="{ row, $index }">
              <slot :name="item.slot" :editor="{ row, $index }"></slot>
            </template>
          </el-table-column>

          <el-table-column
            v-else
            :key="item.name"
            :label="item.name"
            :prop="item.prop"
            align="left"
            :width="item.width"
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <template #header>
              <div :title="item.name">{{ item.name }}</div>
            </template>
            <template #default="scope">
              <span :title="scope.row[item.prop]">{{
                scope.row[item.prop] || scope.row[item.prop] === 0 ? scope.row[item.prop] : '--'
              }}</span>
            </template>
          </el-table-column>
        </template>

        <!-- 其他扩展栏目 -->
        <el-table-column
          v-if="needOtherActionBar.show"
          fixed="right"
          :label="needOtherActionBar.label"
          align="left"
        >
          <template #default="{ row, $index }">
            <slot name="secondEditor" :editor="{ row, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 操作栏目 -->
        <el-table-column
          v-if="isDisplayAction"
          fixed="right"
          label="操作"
          :width="actionWidth"
          align="left"
        >
          <template #default="{ row, $index }">
            <slot name="editor" :editor="{ row, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 无数据插槽 -->

        <template #empty>
          <slot name="empty">
            <div class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/img/table-no-content.png" alt="暂无内容" />
              <div class="text">{{ emptyText }}</div>
            </div>
          </slot>
        </template>
      </el-table>
      <el-pagination
        v-if="state.tableData.length && showPagination"
        background
        :current-page="state.currentPage"
        :page-sizes="state.paginationData.pageSizes"
        :page-size="state.pageSize"
        :layout="state.paginationData.layout"
        :total="state.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
  import { reactive, toRefs, ref, nextTick } from 'vue'

  export default {
    title: '',
    components: {},
    props: {
      stripe: {
        // 斑马条纹表格
        type: Boolean,
        default: false,
      },
      needOtherActionBar: {
        // 是否需要第二操作栏
        type: Object,
        default: function () {
          return {
            label: '设为默认',
            show: false,
          }
        },
      },
      isDisplayAction: {
        // 是否需要操作栏
        type: Boolean,
        default: false,
      },
      actionWidth: {
        // 操作栏宽度
        type: Number,
        default: 130,
      },
      isNeedSelection: {
        // 是否需要多选
        type: Boolean,
        default: false,
      },
      showBorder: {
        // 是否需要边框
        type: Boolean,
        default: false,
      },
      tableHeadTitles: {
        // 表格表头
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: () => {},
      },
      exceptHeight: {
        // 视口除去表格高度后的高度
        type: Number,
        default: 314,
      },
      maxHeight: {
        // 表格最大高度--流体高度
        type: Number,
        default: null,
      },
      showPagination: {
        // 是否显示页码
        type: Boolean,
        default: true,
      },
      rowKey: {
        type: String,
        default: 'id',
      },
      editDisabled: {
        // 编辑时候开启禁用已存在数据
        type: Boolean,
        default: true,
      },
      emptyText: {
        type: String,
        default: '暂无数据',
      },
      loading: {
        //loading状态
        type: Boolean,
        default: true,
      },
    },
    emits: ['cell-click', 'handle-selection-change', 'tablePageChange', 'headerIconClickFn'],
    setup(props, { emit }) {
      const publicTable = ref()
      const { pagination, rowKey, exceptHeight, editDisabled, maxHeight, loading } = toRefs(props)
      const state = reactive({
        loading: loading.value,
        tableData: [],
        currentPage: 1,
        pageSize: 12,
        total: 0,
        tableHeight: 540,
        configData: {
          // 其他配置
          selectRow: [],
        },
        paginationData: {},
        activeIds: [],
      })

      state.paginationData = {
        pageSizes: [12, 24, 48], // 每次展示条数的可配置项
        layout: 'total,prev,pager,next,sizes, jumper',
        currentPage: 1,
        pageSize: 12,
        ...pagination.value,
      }

      const methods = {
        setTableHeight() {
          state.tableHeight = maxHeight.value
            ? null
            : document.body.offsetHeight - exceptHeight.value - 14
        },
        getRowKeys(row) {
          return row[rowKey.value]
        },
        // 点击单元格
        cellClick(row, column) {
          emit('cell-click', row, column)
        },
        // 清空选中
        clearSelection() {
          publicTable.value.clearSelection()
        },
        //设置某行选中还是取消
        toggleRowSelection(item, isSelect) {
          publicTable.value.toggleRowSelection(item, isSelect)
        },

        // 头部图标点击事件
        headerIconClickFn(val) {
          emit('headerIconClickFn', val)
        },

        // 多选
        handleSelectionChange(val) {
          emit('handle-selection-change', val)
        },
        // 切换条数
        handleSizeChange(val) {
          state.pageSize = val
          methods.getTableDate()
        },
        // 切换页数
        handleCurrentChange(val) {
          state.currentPage = val
          methods.getTableDate(false)
        },
        // 提交查询数据请求
        getTableDate(init = true) {
          state.currentPage = init ? 1 : state.currentPage
          // 查询
          let data = {
            currentPage: state.currentPage,
            pageSize: state.pageSize,
          }
          state.loading = true
          emit('tablePageChange', data)
        },
        // 已有数据选中/取消选中
        activeDataRow(data, isSelect = true) {
          data.forEach((row) => {
            state.tableData.find((item) => {
              if (row[rowKey.value] === item[rowKey.value]) {
                return publicTable.value.toggleRowSelection(item, isSelect)
              } else {
                return null
              }
            })
          })
        },

        // 设置返回的结果数据
        initTableData(data, configData = null) {
          state.loading = false
          if (data) {
            state.currentPage = data.pageNum
            state.pageSize = data.pageSize
            state.tableData = data.list ? data.list : []
            state.total = data.total
            if (configData && configData.selectRow) {
              // 勾选选中行
              state.configData = configData
              state.activeIds = configData.selectRow.map((item) => {
                return item[rowKey.value]
              })
              nextTick(() => {
                methods.activeDataRow(configData.selectRow)
              })
            }
          }
        },
        // 初始化失败，隐藏loading
        initFailed() {
          state.loading = false
        },
        // 切换查询条件
        initSwitchConditions() {
          state.loading = true
        },
        //勾选数据
        selectable(row, index) {
          if (row.disabledThisRow) {
            //根据行属性禁用
            return false
          } else {
            if (state.activeIds.includes(row[rowKey.value]) && editDisabled.value) {
              //根据行id禁用
              return false
            } else {
              return true
            }
          }
        },
      }
      watch(
        () => exceptHeight.value,
        () => {
          methods.setTableHeight()
        },
        {
          immediate: true,
        },
      )
      watch(
        () => loading.value,
        (v) => {
          state.loading = v
          methods.setTableHeight()
        },
        {
          immediate: true,
        },
      )

      methods.setTableHeight()
      // methods.initTableData({
      //   pageNum: 1,
      //   pageSize: 10,
      //   list: [],
      //   total: 0,
      // })

      return {
        publicTable,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .common-table {
    height: 100%;
    :deep(.el-pagination) {
      justify-content: center;
      padding: 20px 0;
      .el-pagination__total,
      .el-pagination__goto,
      .el-pagination__classifier {
        font-size: 12px;
      }

      button {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      ul li {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }
        .el-input__wrapper {
          height: $paginationChildHeight;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
    .page-mid {
      height: calc(100% - 120px);
      :deep(.el-table__fixed-right::before) {
        height: 0;
      }
      :deep(.el-table__empty-text) {
        line-height: normal;
      }
      :deep(.table-no-content) {
        img {
          width: 266px;
        }
        .text {
          color: var(--el-text-color-secondary);
          font-size: 12px;
          margin-top: 20px;
          line-height: normal;
        }
      }
      :deep(.el-scrollbar__view) {
        height: 100%;
      }
      :deep(.el-pagination.is-background) {
        &.btn-next,
        &.btn-prev,
        &.el-pager li {
          background-color: #fff;
          border: 1px solid #cfcfcf;
        }
      }

      :deep(.el-pager + button.btn-next[type='button']) {
        margin-right: 10px;
      }

      :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
        background-color: $themeBlue;
        color: #fff;
      }

      :deep(.el-table) {
        .el-table__inner-wrapper::before {
          // el-table 设置border边框 底部边线消失
          height: 0;
        }

        &:before {
          height: 0;
        }

        .el-checkbox__inner {
          width: 16px;
          height: 16px;

          &::after {
            left: 5px;
            top: 2px;
          }
        }

        thead {
          font-weight: 400;
          color: #333333;
          font-size: 14px;

          tr {
            height: 42px !important;
          }

          th {
            background-color: #f7f8fa !important;

            &.el-table__cell.is-leaf {
              border-bottom: none;
              border-right: none;
            }
          }

          th.el-table__cell {
            //表头边框
            // border: 1px solid #dcdcdc;
            // border-left: none;
            .illustrate {
              font-size: 16px;
              margin-left: 4px;
              cursor: pointer;
            }

            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333333;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
          }
        }

        tbody {
          td.el-table__cell {
            // border-bottom: 1px solid #dcdcdc;
            border-bottom: 1px solid #ebebeb;
          }

          tr {
            height: 50px !important;
          }

          tr td {
            //body边框
            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 30px;
              }
            }

            &:last-child {
              // border-right: 1px solid #dcdcdc;

              .cell {
                padding-right: 30px;
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #333;

              & > span {
                // 多行文本溢出隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }

              .edit-box {
                .has-right-border {
                  border-radius: initial;
                  height: 10px;
                  line-height: 9px;
                  padding: 0 0 0 10px;
                  border: 0;
                  border-left: 1px solid #cfcfcf !important;
                  color: var(--themeBlue);
                  font-size: 12px;
                  &.is-disabled {
                    color: #c9c9c9;
                  }
                  &:disabled {
                    color: #c9c9c9;
                  }
                }
                .has-right-border:first-child {
                  padding-left: 0;
                }

                .nancalui-button {
                  &:first-child {
                    border-left: 0 !important;
                  }
                }
                .btn-more {
                  font-size: 12px;
                  color: var(--themeBlue);
                  &:hover {
                    background-color: #fff;
                  }
                }
              }
            }
          }
        }

        //  解决操作栏坍塌的问题 解决固定列距表格的高度（注意：这里我写死了表头为一行文字时的高度）
        .el-table__fixed-body-wrapper {
          top: 42px !important;
        }

        // 解决多出来的横线(固定列右边)问题
        .el-table__fixed-right {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        // 若左右都有固定列时
        .el-table__fixed {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        /**改变table的滚动条样式*/
        // 滚动条的宽度
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 5px; // 横向滚动条
          height: 5px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          background-color: #e1e1e1;
          border-radius: 2px;
        }
      }
    }
  }
</style>
