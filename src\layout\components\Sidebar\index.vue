<template>
  <div :class="menuHidden ? 'leftNav checked' : 'leftNav'">
    <div class="scrollbar-wrapper scroll-bar-style">
      <n-menu
        mode="vertical"
        ref="menus"
        :key="key"
        :collapsed="menuHidden"
        :default-select-keys="[activeMenu]"
        :open-keys="activeOpenMenu"
        :uniqueOpened="true"
        :collapsed-indent="30"
        @select="selectFn"
      >
        <template v-for="item in menuList">
          <template v-if="item.resourceType === 'MENU'">
            <!--第一级导航，有子导航-->
            <n-sub-menu
              class="home-item-sub"
              :popper-offset="8"
              v-if="
                item.children &&
                item.children.length > 0 &&
                item.children[0].resourceType === 'MENU'
              "
              :key="item.code || item.routerName"
              :title="item.name"
            >
              <template #icon>
                <!--                <SvgIcon-->
                <!--                  v-if="-->
                <!--                    item.children.filter(-->
                <!--                      (obj) => obj.routerName === activeMenu || obj.code === activeMenu,-->
                <!--                    ).length > 0-->
                <!--                  "-->
                <!--                  class="left-icon"-->
                <!--                  :icon="'menu-' + item.code + '-c'"-->
                <!--                />-->
                <SvgIcon class="left-icon" :icon="'menu-' + item.code + '-cf'" />
              </template>
              <template v-for="val in item.children">
                <template v-if="val.resourceType === 'MENU'">
                  <!--第二级导航，有子导航-->
                  <n-sub-menu
                    :popper-offset="12"
                    v-if="
                      val.children &&
                      val.children.length > 0 &&
                      val.children[0].resourceType === 'MENU'
                    "
                    :key="val.code || val.routerName"
                    :title="val.name"
                  >
                    <template v-for="v in val.children">
                      <template v-if="v.resourceType === 'MENU'">
                        <!--第三级导航，有子导航-->
                        <n-sub-menu
                          :popper-offset="12"
                          v-if="
                            v.children &&
                            v.children.length > 0 &&
                            v.children[0].resourceType === 'MENU'
                          "
                          :key="v.code || v.routerName"
                          :title="v.name"
                        >
                          <!--第四级子导航-->
                          <n-menu-item :key="v.code || v.routerName">{{ v.name }} </n-menu-item>
                        </n-sub-menu>
                        <!--第三级导航-->
                        <n-menu-item v-else :key="v.code || v.routerName">
                          {{ v.name }}
                        </n-menu-item>
                      </template>
                    </template>
                  </n-sub-menu>
                  <!--第二级导航-->
                  <!-- 为确保 v-if/else 分支使用唯一的 key，添加一个前缀 -->
                  <n-menu-item v-else :key="val.code || val.routerName">
                    {{ val.name }}
                  </n-menu-item>
                </template>
              </template>
            </n-sub-menu>
            <!--第一级导航-->
            <n-menu-item
              :class="{
                'home-item': true,
              }"
              v-else
              :key="item.routerName"
            >
              <template #icon>
                <!--                <SvgIcon-->
                <!--                  v-if="activeMenu === item.routerName"-->
                <!--                  class="left-icon"-->
                <!--                  :icon="'menu-' + item.code + '-c'"-->
                <!--                />-->
                <SvgIcon class="left-icon" :icon="'menu-' + item.code + '-cf'" />
              </template>
              <span class="title">{{ item.name }}</span>
            </n-menu-item>
          </template>
        </template>
      </n-menu>
      <div v-if="mrs.enabled" :class="menuHidden ? 'other-menu hide' : 'other-menu'">
        <div class="other-menu-item" @click="goOtherUrlFn"
          ><SvgIcon class="other-menu-item-icon" icon="icon-monitoring" />{{ mrs.menuName }}</div
        >
      </div>
    </div>

    <div :class="menuHidden ? 'footerNav checked' : 'footerNav'" @click.prevent="toggleMenuFn">
      <SvgIcon class="pic" icon="icon-footer-pic" />
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'

  export default {
    data() {
      return {
        key: 1,
        mainMenuInfo: { name: '', bgImgUrl: '' },
        mainMenuList: [],
        menuList: [],
        activeOpenMenu: [],
        isCollapse: false,
        showMainMenuOption: false,
      }
    },
    computed: {
      ...mapState({
        homeType: (state) => state['user'].homeType,
        menuTreeList: (state) => state['user'].menuTreeList,
        checkFirstMenuCode: (state) => state['user'].checkFirstMenuCode,
        activeMenu: (state) => state['user'].activeMenu,
        menuHidden: (state) => state['app'].menuHidden,
        dataGovernanceStatus: (state) => state['user'].dataGovernanceStatus,
        dataAssetsStatus: (state) => state['user'].dataAssetsStatus,
        systemMenuStatus: (state) => state['user'].systemMenuStatus,
        applicationMenuStatus: (state) => state['user'].applicationMenuStatus,
        isSystemMenu: (state) => state['user'].isSystemMenu,
        mrs: (state) => state['user'].mrs,
        inteLogin: (state) => state['user'].inteLogin,
      }),
    },
    watch: {
      $route(to, from) {
        // if (to?.matched[2]?.meta?.code) {
        //   this.$store.commit('user/SET_ACTIVE_MENU', to.matched[2].name)
        // } else if (to?.matched[1]?.meta?.code) {
        //   this.$store.commit('user/SET_ACTIVE_MENU', to.matched[1].name)
        // } else if (to?.matched[0]?.meta?.code) {
        //   this.$store.commit('user/SET_ACTIVE_MENU', to.matched[0].name)
        // }
        this.$store.commit('user/SET_ACTIVE_MENU', to.meta.code)

        let _activeMainCode = this.searchTreeWithCode(this.menuTreeList, to.meta.code)
        if (_activeMainCode?.code) {
          //根据路由配置左侧菜单和选中子菜单--tag点击
          this.checkMainMenuFn(_activeMainCode.code)
        } else {
          this.menuChangeFn()
        }
      },
    },
    mounted() {
      this.$nextTick(() => {
        if (!this.checkFirstMenuCode) {
          this.$store.commit('user/SET_ACTIVE_MENU_CODE', 'dataIntegration')
        }
        //根据路由配置左侧菜单和选中子菜单--复制地址进入页面
        if (this.$route && this.$route.meta?.code) {
          let _activeMainCode = this.searchTreeWithCode(this.menuTreeList, this.$route.meta?.code)
          if (_activeMainCode?.code) {
            this.checkMainMenuFn(_activeMainCode.code)
          } else {
            this.menuChangeFn()
          }
        } else {
          this.menuChangeFn()
        }
      })
    },
    methods: {
      // 判断当前传入的字段值是否存在于树数据中 并且返回最父级数据
      searchTreeWithCode(nodesArr, code, parentIndex = -1) {
        for (let index = 0; index < nodesArr.length; index++) {
          if (nodesArr[index].code === code) {
            return this.menuTreeList[parentIndex === -1 ? index : parentIndex]
          } else {
            if (nodesArr[index].children && nodesArr[index].children.length) {
              let res = this.searchTreeWithCode(
                nodesArr[index].children,
                code,
                parentIndex === -1 ? index : parentIndex,
              )
              if (res) {
                return this.menuTreeList[index]
              }
            }
          }
        }
        return null
      },

      // 是否展示主菜单下拉
      mainMenuOptionFn() {
        this.showMainMenuOption = !this.showMainMenuOption
      },
      blurFn() {
        setTimeout(() => {
          this.showMainMenuOption = false
        }, 60)
      },
      // 主菜单切换
      checkMainMenuFn(code) {
        this.$store.commit('user/SET_FIRST_MENU', code)
        this.showMainMenuOption = false
        this.menuChangeFn()
      },
      // 菜单变化
      menuChangeFn() {
        if (this.menuTreeList?.length > 0) {
          this.mainMenuList = this.menuTreeList
            .map((val) => {
              val.bgImgUrl = new URL(`/src/assets/img/menu/${val.code}.png`, import.meta.url).href
              return { ...val, children: null }
            })
            .filter((val) => val.code !== 'data_application')
          // 获取对应的一级菜单元素
          let mainMenuInfo = this.menuTreeList.filter(
            (val) => val.code === this.checkFirstMenuCode,
          )[0]
          this.mainMenuInfo = mainMenuInfo
          let routerItemMeta = this.$route?.meta
          if (
            routerItemMeta?.code === 'dataGovernanceScene' ||
            routerItemMeta?.parentCode === 'dataGovernanceScene'
          ) {
            this.menuList = mainMenuInfo?.children.filter(
              (val) => val.code === 'dataGovernanceScene',
            )
            this.activeOpenMenu = []
            this.$store.commit('user/SET_ACTIVE_MENU_CODE', routerItemMeta?.code)
          } else if (
            routerItemMeta?.code === 'annotationOverview' ||
            routerItemMeta?.parentCode === 'annotationOverview'
          ) {
            this.menuList = mainMenuInfo?.children.filter(
              (val) =>
                val.code === 'annotationOverview' ||
                val.code === 'annotationTool' ||
                val.code === 'teamManagement',
            )
            this.activeOpenMenu = []
            this.$store.commit('user/SET_ACTIVE_MENU_CODE', routerItemMeta?.code)
          } else if (
            routerItemMeta?.code === 'dataAnnotation' ||
            routerItemMeta?.parentCode === 'dataAnnotation'
          ) {
            this.menuList =
              mainMenuInfo?.children.filter((val) => val.code === 'dataAnnotation')[0]?.children ||
              []
            this.activeOpenMenu = ['dataAnnotation']
            this.$store.commit('user/SET_ACTIVE_MENU_CODE', routerItemMeta?.code)
          } else {
            this.menuList = mainMenuInfo?.children
            // 获取展开的父元素菜单
            if (this.activeMenu && this.menuList) {
              this.menuList.forEach((val) => {
                if (val.routerName === this.activeMenu) {
                  this.$store.commit('user/SET_ACTIVE_MENU_CODE', val.code)
                  this.activeOpenMenu = []
                } else {
                  if (val.children) {
                    val.children.forEach((item) => {
                      if ((item.routerName || item.code) === this.activeMenu) {
                        this.$store.commit('user/SET_ACTIVE_MENU_CODE', val.code)
                        this.activeOpenMenu = [val.code || val.activeOpenMenu]
                      }
                    })
                  }
                }
              })
            }
          }
          this.key++
        }
      },
      handleClose(key, keyPath) {
        this.$refs.menus.open(keyPath)
      },
      selectFn(e) {
        this.goJump(e.key)
      },
      goJump(name) {
        this.$router.push({ name: name })
      },
      toggleMenuFn() {
        if (this.menuHidden) {
          this.$store.commit('app/TOGGLE_LEFT_MENU', false)
        } else {
          this.$store.commit('app/TOGGLE_LEFT_MENU', true)
        }
      },
      // 跳转其他地址
      goOtherUrlFn() {
        if (this.mrs.url) {
          window.location.href = this.mrs.url
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .leftNav {
    position: relative;

    .mainMenu {
      position: relative;
      box-sizing: border-box;
      height: 60px;
      margin-bottom: 4px;
      padding: 8px;
      &.hide {
        height: 64px;
        padding: 0 4px;
        .mainMenuSelect {
          .bg {
            top: 5px;
            right: 0;
            left: 0;
            margin: auto;
          }
          .mainMenuName {
            height: 64px;
            padding: 8px;
            &:before {
              display: none;
            }
            .arrow {
              display: none;
            }
            :deep(.nancalui-input) {
              width: 100%;
              transform: translateY(20px);
              .nancalui-input__wrapper {
                padding: 0;
                .nancalui-input__inner {
                  font-size: 12px;
                }
              }
            }
          }
        }
        .mainMenuOption {
          position: fixed;
          top: 60px;
          left: 76px;
          z-index: 100000;
          width: 180px;
        }
      }
      .mainMenuSelect {
        position: relative;
        overflow: hidden;
        background-color: #2f5cd6;
        border-radius: 0px;
        cursor: pointer;
        .mainMenuName {
          position: relative;
          z-index: 2;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 44px;
          padding: 0 8px 0 38px;
          :deep(.nancalui-input) {
            width: calc(100% - 20px);
            height: 44px;
            line-height: 44px;
            cursor: pointer;

            .nancalui-input__wrapper {
              padding-left: 0;
              background-color: transparent;
              border: none;
              .nancalui-input__inner {
                color: #fff;
                font-weight: bold;
                font-size: 14px;
                cursor: pointer;
              }
            }
          }
          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 17px;
            width: 6px;
            height: 6px;
            margin: auto;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            content: '';
          }
          .arrow {
            width: 16px;
            height: 16px;
            opacity: 0.75;
          }
        }
        .bg {
          position: absolute;
          top: 10px;
          left: 116px;
          z-index: 1;
          width: 40px;
          height: 46px;
        }
      }
      .mainMenuOption {
        position: absolute;
        top: 60px;
        left: 8px;
        z-index: 3;
        box-sizing: border-box;
        width: calc(100% - 16px);
        height: 0;
        overflow: hidden;
        background-color: #151c3d;
        border-radius: 8px;
        transition: all linear 0.3s;
        &.active {
          height: auto;
        }
        .mainMenuOptionLabel {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          width: calc(100% - 16px);
          height: 44px;
          margin: 8px auto;
          padding: 0 30px;
          overflow: hidden;
          color: rgba(255, 255, 255, 0.75);
          font-weight: bold;
          font-size: 14px;
          border-radius: 0;
          cursor: pointer;
          &.checked {
            color: rgba(255, 255, 255, 1);
            background-color: #2f5cd6;
          }
          &:hover {
            color: #1d2129;
            background-color: rgba(42, 50, 90, 0.32);
          }
          .bg {
            position: absolute;
            top: 10px;
            left: 108px;
            z-index: 1;
            width: 40px;
            height: 46px;
          }
        }
      }
    }

    .footerNav {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 99;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      width: 100%;
      height: 38px;
      border-top: 1px solid #dcdfe6;
      border-radius: 0;

      &.checked {
        .pic {
          transform: rotate(0deg);
        }
      }

      .pic {
        color: #606266;
        font-size: 20px;
        transform: rotate(180deg);
        cursor: pointer;
        margin-left: 16px;
        &:hover {
          color: #1e89ff;
        }
      }
    }

    :deep(.nancalui-menu) {
      margin: 4px auto;
    }
  }

  .yy-icon {
    width: 18px !important;
    height: 18px !important;
  }
  .other-menu {
    padding: 0 8px;
    &.checked {
      .other-menu-item {
        color: rgba(255, 255, 255, 1);
        background-color: #ebf4ff;
      }
    }
    &.hide {
      padding: 0 4px;
      .other-menu-item {
        flex-direction: column;
        justify-content: center;
        width: 48px;
        height: 38px;
        padding: 0;
        font-size: 12px;
        line-height: normal;
        .other-menu-item-icon {
          width: 24px !important;
          height: 24px !important;
          margin-right: 0;
          margin-bottom: 6px;
        }
      }
    }
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      width: 204px;
      height: 44px;
      padding: 0 10px;
      color: #1d2129;
      font-weight: bold;
      font-size: 14px;
      line-height: 44px;
      border-radius: 0;
      cursor: pointer;
      &-icon {
        position: relative;
        z-index: 2;
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
      &:hover {
        color: #1d2129;
        background-color: rgba(16, 19, 30, 0.4);
      }
      &.checked {
        color: rgba(255, 255, 255, 1);
        background-color: #ebf4ff;
      }
    }
  }
  :deep(.nancalui-menu-vertical) {
    background-color: #ffffff;
    .nancalui-menu-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 44px !important;
      padding: 0 10px !important;
      color: #1d2129;
      line-height: 44px;
      cursor: pointer;
    }
    .nancalui-submenu {
      .nancalui-submenu-title {
        padding: 0 10px !important;
        .nancalui-submenu-title-content {
          height: 44px;
          line-height: 44px;
        }
      }
    }
    .nancalui-menu-item-vertical-wrapper.home-item {
      height: 38px !important;
      margin-bottom: 4px;
      padding: 0 !important;
      li {
        font-weight: bold;
        border-radius: 0;
        &:hover {
          color: #1d2129;
          background-color: rgba(16, 19, 30, 0.4);
        }
      }

      .nancalui-menu-item.nancalui-menu-item-isCollapsed {
        display: flex !important;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 48px !important;
        height: 38px !important;
        margin: 0 0 4px 0 !important;
        padding: 10px 0 !important;
        text-align: center;
        span {
          margin: 0 auto;
          .title {
            flex: none;
            height: 20px !important;
            line-height: 20px !important;
          }
          &:nth-child(2) {
            min-width: 60px !important;
            overflow: initial;
          }
        }
      }
      .nancalui-menu-item-select {
        color: #ffffff !important;
        font-weight: 500;
        background-color: #ebf4ff !important;
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background-color: #1e89ff;
          content: '';
        }
        svg {
          color: #447dfd;
        }
      }
    }
    .nancalui-menu-item-vertical-wrapper.isCollapsed.home-item {
      height: 38px !important;
      margin: 0 0 4px 0 !important;
    }
    .nancalui-menu-item-vertical-wrapper.home-item.checked {
      .nancalui-menu-item {
        margin: 0;
        color: #ffffff !important;
        font-weight: 500;
        background-color: #ebf4ff !important;

        svg {
          color: #447dfd;
        }
      }
    }
    &.nancalui-menu-collapsed {
      width: 48px !important;
      ul.nancalui-submenu {
        .nancalui-submenu-menu-item-vertical-wrapper.layer_2 {
          background-color: #fff;
          border-radius: 4px;
          padding: 4px 0;
          margin: 0;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
          .nancalui-menu-item-vertical-wrapper {
            margin-top: 4px;
            &:first-child {
              margin-top: 0;
            }
            &:hover {
              background-color: #f0f2f5;
            }
            > li {
              margin: 0;
              padding: 0 16px !important;
              > span {
                text-align: left !important;
              }
            }
          }
        }
      }
    }
    .nancalui-menu-item-vertical-wrapper:last-child {
      margin-bottom: 0;
      .nancalui-menu-item {
        margin-bottom: 0 !important;
      }
    }
  }
</style>
