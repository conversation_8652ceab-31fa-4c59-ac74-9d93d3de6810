<template>
  <!-- 利用缓存的时间戳key 保证页面保鲜 -->
  <router-view v-slot="{ Component }">
    <keep-alive :max="5">
      <component
        :is="Component"
        v-if="$route.meta.keepAlive"
        :key="$route.meta.keepAlive ? $route.name : $route.path"
      />
    </keep-alive>

    <component :is="Component" v-if="!$route.meta.keepAlive" :key="$route.name" />
  </router-view>
</template>
<script setup lang="ts"></script>
