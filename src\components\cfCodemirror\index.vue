<template>
    <div class="code-box">
        <codemirror ref="myCm" v-model:value="props.modelValue" class="codemirror" :options="state.sqlOption"
            @ready="onCmReady" @focus="onCmFocus" @input="onCmCodeChange" />
    </div>
</template>
<script setup>
import codemirror from 'codemirror-editor-vue3'
// 核心样式
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/solarized.css'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/python/python.js'
import 'codemirror/mode/shell/shell.js'
// import 'codemirror/mode/javascript/javascript.js'
// require active-line.js
import 'codemirror/addon/selection/active-line.js'
// closebrackets
import 'codemirror/addon/edit/closebrackets.js'
// keyMap
import 'codemirror/mode/clike/clike.js'
import 'codemirror/addon/edit/matchbrackets.js'
import 'codemirror/addon/comment/comment.js'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/search/searchcursor.js'
import 'codemirror/addon/search/search.js'
import 'codemirror/keymap/emacs.js'
// 引入代码自动提示插件
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/sql-hint'
import 'codemirror/addon/hint/show-hint'
// 代码校验 lint
import 'codemirror/addon/lint/lint.js'
import 'codemirror/addon/lint/lint.css'
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    }, 
})
const state = reactive({
    codemirror: null,
    syncForm: { name: '', querySql: '', tables: '', type: 'ASSETS', datasourceId: -999 }, // 类型(DATASOURCE:数据源;ASSETS:资产库),
    sqlOption: {
        autorefresh: true, // 是否自动刷新
        smartIndent: true, // 自动缩进
        tabSize: 4, // 缩进单元格为 4 个空格
        mode: 'text/x-sql', //编辑器的编程语言
        line: true, // 是否显示行数
        viewportMargin: Infinity, // 高度自适应
        highlightDifferences: true,
        autofocus: false,
        indentUnit: 2,
        readOnly: false, // 只读
        showCursorWhenSelecting: true,
        firstLineNumber: 1,
        matchBrackets: true, //括号匹配
        lineWrapping: true, //是否折叠
        foldGutter: true, // 启用行槽中的代码折叠
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true, // 显示选中行的样式
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
        highlightSelectionMatches: {
            minChars: 2,
            style: 'matchhighlight',
            showToken: true,
        },
        lineNumbers: true, //是否显示左边换行数字
        lint: true, // 打开json校验
    },
})
// SQL语句获取焦点时
const onCmFocus = (cm) => { }
// SQL语句准备完成时
const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
        const config = {
            // 自定义提示选项
            completeSingle: false, // 当匹配只有一项的时候是否自动补全
        }
        state.codemirror.showHint(config)
    })
}
// SQL语句输入时
const onCmCodeChange = (newCode) => {
    emit('update:modelValue', newCode)
}
</script>
<style lang="scss" scoped>
.code-box {
    width: 100%;
    height: 200px;
    overflow: hidden;
    padding: 10px;
    border: 1px solid #e5e6eb;

    // height: 100%;
    .codemirror {
        height: 100% !important;
        background-color: #fff;

        :deep(.CodeMirror) {
            height: 100% !important;
            overflow: hidden;
            box-shadow: none;
        }

        :deep(.CodeMirror-scroll) {
            box-sizing: border-box;
            height: 100%;
            margin-right: -6px;
            padding: 0;
            overflow-x: hidden !important;
            overflow-y: auto !important;

            .CodeMirror-sizer {
                border-right: none;

                .CodeMirror-lines {
                    padding: 0;
                    // .CodeMirror-selected {
                    //   height: 28px !important;
                    // }
                }

                .CodeMirror-cursors {
                    top: 5px;
                }

                .CodeMirror-code>div {
                    padding: 5px 0;
                }

                .CodeMirror-linenumber {
                    padding: 0 6px;
                    text-align: center;
                }

                .CodeMirror-line {
                    padding: 0 10px;

                    >span {
                        padding-right: 10px !important;
                        color: #046c5c;
                        font-size: 14px;
                        word-break: break-all;
                    }
                }

                .CodeMirror-linebackground {
                    background-color: #f0f2f5;
                }
            }
        }

        :deep(.CodeMirror-gutters) {
            width: 32px;
            min-height: 100%;
            background-color: #e6e8eb;
            border-right: none;
        }

        :deep(.CodeMirror-vscrollbar) {
            visibility: initial !important;

            &::-webkit-scrollbar-thumb {
                background-color: #b1bcd6;
                border-radius: 6px;

                &:hover {
                    background-color: #b1bcd6;
                }
            }
        }
    }
}
</style>