<template>
  <!-- 标签管理列表 -->
  <div class="tag-management-list container">
    <div class="tag-management-list-box" v-loading="state.treeloading">
      <div class="tag-left">
        <PublicLeftTree
          ref="publicTree"
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          :checkedNodes="state.allTableData"
          @treeCheckNode="treeCheckNode"
        />
      </div>
      <div class="tag-right">
        <div class="title-box">
          <div class="title"><SvgIcon class="icon-btn" icon="icon-subtract" />数据预览</div>
          <!--          <div class="label" @click="goJump({ name: 'metadataMap' })"-->
          <!--            ><SvgIcon class="icon-btn" icon="metadata-map-data" />图谱</div-->
          <!--          >-->
        </div>
        <div class="table-content-box">
          <n-public-table
            class="overflow-auto"
            :key="state.tableKey"
            :isDisplayAction="false"
            :isNeedSelection="false"
            :table-head-titles="state.tableHeadTitles"
            :pagination="state.pagination"
            :tableHeight="state.tableHeight"
            :tableData="state.tableData"
            :actionWidth="180"
            @tablePageChange="tablePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, ref, getCurrentInstance, computed, nextTick, toRefs } from 'vue'
  import { formartTime } from '@/utils/index'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import ENUM from '@/const/enum'
  import { checkCName } from '@/utils/validate'
  export default {
    name: 'AuthorizedPersonnel',
    setup() {
      // 获取当前组件实例、
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const { proxy } = getCurrentInstance()
      const publicTree = ref()
      const tagGroupProps = {
        //级联 指定绑定的字段
        checkStrictly: true,
        value: 'id',
        label: 'name',
      }
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/tagManagement/${name}.png`, import.meta.url).href //本地文件路径
      }
      const state = reactive({
        tableHeight: 400,
        tableData: {},
        loading: false,
        treeloading: false,
        tableKey: 1,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'name', name: '标签名称' },
          { prop: 'groupName', name: '标签类目' },
          { prop: 'description', name: '描述' },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'createByName', name: '创建人' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          keyword: '',
          time: [],
        },
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          expandDefault: false,
          showCheckbox: false,
          showControl: false,
          showLeftIcon: true,
          nodeKey: 'uid',
          defaultExpandedKeys: [],
        },
        datasourceId: null, //祖先id
        databaseName: null, //祖先name
        tableName: null, // 选中组id
      })

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 122 - 86
        },

        // 初始化form
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            datasourceId: state.datasourceId || null, // 标签组id
            databaseName: state.databaseName || null, // 标签组id
            tableName: state.tableName || null,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.assets
            .getMateDataList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                state.tableHeadTitles = [{ prop: 'number', name: '序号', width: 80 }]
                res.data?.columns.forEach((item) => {
                  state.tableHeadTitles.push({ prop: item, name: item, width: 200 })
                })
                // 新增序号属性
                res.data.data?.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                  })
                })

                state.tableData = {
                  ...res.data,
                  list: res.data.data,
                }
                state.tableKey++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 获取左侧树数据
        getTreeData() {
          state.treeloading = true
          state.treeAttrData.defaultExpandedKeys = []
          api.assets
            .getTableTree()
            .then((res) => {
              state.treeloading = false
              let { success, data } = res
              if (success) {
                if (data !== null) {
                  state.treeData = methods.proTreeData(data)
                  //默认树结构的第一个表数据
                  methods.getDefaultExpand(data, true)
                  methods.getDefaultTable(data)
                }
              }
            })
            .catch(() => (state.treeloading = false))
        },
        getDefaultExpand(data, isRoot = false) {
          if (isRoot) {
            if (data[0].children && data[0].children.length > 0) {
              state.treeAttrData.defaultExpandedKeys.push(data[0].uid)
              methods.getDefaultExpand(data[0].children)
            }
          } else {
            data.forEach((item) => {
              if (item.children && item.children.length > 0) {
                state.treeAttrData.defaultExpandedKeys.push(item.uid)
                methods.getDefaultExpand(item.children)
              }
            })
          }
        },
        getDefaultTable(data) {
          for (let i = 0; i < data.length; i++) {
            const item = data[i]
            if (item.type === 'TABLE') {
              state.datasourceId = item.sourceId
              state.databaseName = item?.databaseName
              state.tableName = item.name
              methods.initTable()
              return true
            } else {
              if (item.type !== 'TABLE' && item.type !== 'COLUMN' && item.children) {
                return methods.getDefaultTable(item.children)
              }
            }
          }
        },
        proTreeData(data) {
          data.forEach((item) => {
            if (item.type !== 'TABLE') {
              item.icon = 'tree-parent'
            }
            if (item.children && item.children.length > 0) {
              methods.proTreeData(item.children)
            }
          })
          return data
        },
        // 点击选中标签树行
        treeCheckNode(data) {
          if (!data) return
          let { checkItem } = data
          if (checkItem.type !== 'TABLE') return
          state.datasourceId = checkItem.sourceId
          state.databaseName = checkItem?.databaseName
          state.tableName = checkItem.name
          methods.initTable(true)
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },

        //跳转
        goJump(item) {
          let _router = { name: item.name }
          if (item.query) {
            _router = { name: item.name, query }
          }
          router.push(_router)
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        nextTick(() => {
          methods.getTreeData()
        })
      })

      return {
        state,
        buttonAuthList,
        tagGroupProps,
        publicTree,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .tag-management-list {
    &-box {
      display: flex;
      flex-wrap: nowrap;
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
    }
    .tag-left {
      height: calc(100% + 15px);
    }
    .tag-right {
      flex: 1;
      min-width: 0; //解决左侧收起后 右侧数据不复原问题
      padding: 0 16px;
      overflow: hidden;
      .title-box {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;

        .title {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          color: #000000;
          font-weight: 600;
          font-size: 18px;
          .icon-btn {
            width: 20px;
            height: 20px;
            margin-right: 6px;
          }
        }
        .label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          color: $themeBlue;
          font-size: 14px;
          cursor: pointer;
          .icon-btn {
            width: 20px;
            height: 20px;
            margin-right: 1px;
          }
        }
        :deep(.nancalui-table) {
          .nancalui-table__fix-header {
            overflow-x: scroll !important;
          }
          .nancalui-table__scroll-view {
            overflow: unset;
          }
        }
      }
      .table-content-box {
        :deep(.overflow-auto) {
          .nancalui-table__header-wrapper,
          .nancalui-table__scroll-view {
            display: inline-table;
          }
          .nancalui-table__fix-header {
            overflow: auto !important;
          }
        }
      }
    }
  }
</style>
