<template>
  <!-- 数据服务-token管理列表 -->
  <div :class="['token-management-list container', state.isLzos ? 'isLzos' : '']">
    <div class="list-box" v-loading="state.loading">
      <n-public-table
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="120"
        @tablePageChange="tablePageChange"
      >
        <template #pageTop>
          <div class="box-add">
            <div class="commonForm">
              <n-form
                :inline="true"
                :data="state.formInline"
                class="demo-form-inline search-right commonForm"
              >
                <n-form-item label="">
                  <n-input
                    v-model="state.formInline.keyword"
                    size="small"
                    placeholder="请输入API名称"
                    clearable
                    @clear="onSearch"
                  >
                    <template #append>
                      <n-button @click.prevent="onSearch">
                        <n-popover
                          class="item"
                          content="搜索"
                          trigger="hover"
                          :position="['bottom']"
                        >
                          <SvgIcon class="icon_search" icon="icon_search" />
                        </n-popover>
                      </n-button>
                    </template>
                  </n-input>
                </n-form-item> </n-form
            ></div>
          </div>
        </template>

        <template #token="{ editor }">
          <div class="token-edit-box">
            <div class="token"> {{ editor.row.token }}</div>
            <n-button
              v-if="buttonAuthList.includes('service_tokenManagement_copy_edit')"
              code="service_tokenManagement_copy_edit"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="copyToken(editor)"
              >复制
            </n-button>
          </div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="buttonAuthList.includes('service_tokenManagement_setPeriodOfValidity_edit')"
              code="service_tokenManagement_setPeriodOfValidity_edit"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="setTokenPeriodOfValidity(editor)"
              >设置有效期
            </n-button>
          </div>
        </template>
      </n-public-table>
    </div>

    <!-- 授权 -->

    <n-modal
      v-model="state.dialogVisible"
      title="Token有效期设置"
      class="has-top-padding"
      bodyClass="middleDialog"
      :close-on-click-overlay="false"
      width="460px"
      :draggable="false"
      :append-to-body="false"
      :before-close="closeDialog"
    >
      <div>
        <n-form ref="formRef" :data="state.form">
          <n-form-item
            field="neverExpire"
            label="有效期："
            :rules="[
              {
                type: 'boolean',
                required: true,
                message: '请选择有效期',
                trigger: 'change',
              },
            ]"
          >
            <n-radio-group v-model="state.form.neverExpire">
              <n-radio :value="true">永久有效</n-radio>
              <n-radio v-if="state.form.neverExpire" :value="false">期限有效</n-radio>
              <div class="inline-box">
                <n-radio v-if="!state.form.neverExpire" :value="false">期限有效</n-radio>
                <n-form-item
                  field="tokenExpireIncrementDays"
                  :rules="[
                    {
                      required: true,
                      message: '请设置有效期',
                      trigger: 'change',
                    },
                  ]"
                >
                  <div class="set-number-box" v-if="!state.form.neverExpire">
                    <n-button size="mini" @click.prevent="reduceBtn">-</n-button>
                    <n-input
                      :key="state.key"
                      v-model="state.form.tokenExpireIncrementDays"
                      placeholder=""
                      @keydown="
                        onKeydownPositiveInteger($event, state.form.tokenExpireIncrementDays)
                      "
                      @keyup="onKeyupPositiveInteger($event)"
                      @focus="inputFocus($event)"
                      @blur="fieldBlur()"
                      :onpaste="
                        () => {
                          return false
                        }
                      "
                    />
                    <n-button size="mini" @click.prevent="addBtn">+</n-button>
                  </div>
                </n-form-item>
                <div v-show="!state.form.neverExpire" class="unit">天</div>
              </div>
            </n-radio-group>
          </n-form-item>
        </n-form>
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            size="sm"
            color="primary"
            variant="solid"
            :loading="state.submiting"
            @click.prevent="saveTokenTimeSet"
            >确 定</n-button
          >
          <n-button size="sm" @click.prevent="closeDialog">取 消</n-button></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import ENUM from '@/const/enum'
  import { onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'
  export default {
    title: 'List',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const formRef = ref()
      // 获取当前组件实例
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 436,
        tableData: {},
        loading: false,
        dialogVisible: false,
        submiting: false,
        shortcuts: ENUM.SHORTCUTS,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          // { prop: 'projectName', name: '场景名称' },
          { prop: 'apiName', name: 'API名称' },
          { prop: 'token', name: 'Token令牌', width: 500, slot: 'token' },
          { prop: 'tokenExpire', name: 'Token令牌有效期' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },

        formInline: {
          // time: [],
          keyword: '',
          // startTime: null,
          // endTime: null,
        },
        form: {
          id: null,
          neverExpire: true,
          tokenExpireIncrementDays: '1',
        },
        key: 1,
      })

      const methods = {
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },

        fieldBlur() {
          state.form.tokenExpireIncrementDays = state.form.tokenExpireIncrementDays.replace(
            /\D/g,
            '',
          )
          state.key++
        },
        onKeydown(e) {
          let key = e.key
          let number = '1234567890'
          if (number.includes(key) || key === 'Backspace') {
            if (state.form.tokenExpireIncrementDays.toString().length === 1 && key === '0') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          } else {
            e.returnValue = false
          }
        },
        reduceBtn() {
          if (state.form.tokenExpireIncrementDays > 1) {
            state.form.tokenExpireIncrementDays--
          } else {
            state.form.tokenExpireIncrementDays = '1'
          }
          state.form.tokenExpireIncrementDays = state.form.tokenExpireIncrementDays.toString()
          state.key++
        },
        addBtn() {
          if (state.form.tokenExpireIncrementDays) {
            state.form.tokenExpireIncrementDays++
          }
          state.form.tokenExpireIncrementDays = state.form.tokenExpireIncrementDays.toString()
          state.key++
        },

        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 160 - 50 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 160 - 50
          }
        },
        //设置token有效期弹框
        setTokenPeriodOfValidity(editor) {
          if (editor.row.tokenExpire === '永久有效') {
            state.form.neverExpire = true
          } else {
            state.form.neverExpire = false
          }

          state.form.id = editor.row.id
          state.dialogVisible = true
        },
        //关闭token有效期弹框
        closeDialog() {
          state.dialogVisible = false
          state.form = {
            id: null,
            neverExpire: true,
            tokenExpireIncrementDays: '1',
          }
        },
        //保存有效期设置
        saveTokenTimeSet() {
          formRef.value.validate((valid) => {
            if (valid) {
              state.submiting = true
              api.dataService
                .setTokenExpire({
                  id: state.form.id,
                  neverExpire: state.form.neverExpire,
                  tokenExpireIncrementDays: state.form.tokenExpireIncrementDays,
                })
                .then((res) => {
                  state.submiting = false
                  let { success } = res
                  if (success) {
                    state.dialogVisible = false
                    ElNotification({
                      title: '提示',
                      message: '设置Token有效期成功',
                      type: 'success',
                    })
                    methods.initTable()
                  }
                })
                .catch(() => {
                  state.submiting = false
                })
            } else {
              return false
            }
          })
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              // startTime: state.formInline.startTime || null,
              // endTime: state.formInline.endTime || null,
              apiName: state.formInline.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataService['getApiTokenList'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 复制token
        copyToken(editor) {
          let { row } = editor
          let value = row.token //拿到想要复制的值
          // 先给要复制的文本或者按钮加上点击事件后，并将要复制的值传过来
          // 浏览器禁用了非安全域的 navigator.clipboard 对象。
          if (navigator?.clipboard && window?.isSecureContext) {
            //前端在页面实现点击复制功能，一般都会使用document.execCommand；虽然现在还能用，但是会提示你即将被废弃，找了一个替代方法，基于Promise，不用像execCommand一样还得选中范围；看了一下兼容性也挺不错的
            navigator.clipboard.writeText(value).then(() => {
              ElNotification({
                title: '提示',
                message: '复制token成功',
                type: 'success',
              })
            })
          } else {
            let copyInput = document.createElement('input') //创建input元素
            document.body.appendChild(copyInput) //向页面底部追加输入框
            copyInput.setAttribute('value', value) //添加属性，将url赋值给input元素的value属性
            copyInput.select() //选择input元素
            document.execCommand('Copy') //执行复制命令
            ElNotification({
              //弹出提示信息，不同组件可能存在写法不同
              title: '提示',
              message: '复制token成功',
              type: 'success',
            })
          }
        },

        // 搜索
        onSearch() {
          // let time = state.formInline.time
          // if (state.formInline.time) {
          //   if (time[0]) {
          //     state.formInline.startTime = formartTime(time[0])
          //   }
          //   if (time[1]) {
          //     state.formInline.endTime = formartTime(time[1], true)
          //   }
          // } else {
          //   state.formInline.startTime = null
          //   state.formInline.endTime = null
          // }
          methods.initTable(true)
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable(true)
      })

      return {
        state,
        formRef,
        buttonAuthList,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .token-management-list {
    &.isLzos {
      padding: 0;
    }
    .list-box {
      height: 100%;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 4px;

      .box-add {
        padding: 20px 0;
        .commonForm-search {
          padding: 16px 0;
        }
        .nancalui-form {
          display: flex;
          justify-content: flex-end;
        }
      }

      .left {
        .nancalui-button {
          background: $themeBlue;
        }
      }

      .seeDetails {
        color: $themeBlue;
      }
      .token-edit-box {
        display: flex;
        align-items: center;
        width: 100%;
        .token {
          max-width: calc(100% - 30px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .nancalui-modal {
      .nancalui-radio-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .nancalui-radio__wrapper {
          height: 32px;
        }
        .inline-box {
          display: flex;
          align-items: center;
          margin-top: 10px;
          .nancalui-form__item--horizontal {
            margin-bottom: 0;
          }
          .set-number-box {
            display: flex;
            .nancalui-input {
              width: 70px;
              margin: 0 10px 0 10px;
            }
            .nancalui-button {
              width: 30px;
              min-width: auto;
              padding: 0;
              font-size: 25px;
              :deep(.button-content) {
                margin-top: -3px;
              }
            }
          }
        }
        .unit {
          margin-left: 10px;
          font-size: 12px;
        }
      }
    }
  }
</style>
