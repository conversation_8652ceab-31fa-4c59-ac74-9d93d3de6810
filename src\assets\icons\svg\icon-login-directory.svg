<?xml version="1.0" encoding="UTF-8"?>
<svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>数据目录</title>
    <defs>
        <linearGradient x1="12.9021441%" y1="0%" x2="89.2820247%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E8EAF3" stop-opacity="0.82760172" offset="49.8724664%"></stop>
            <stop stop-color="#314F8D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="2-数据集成" transform="translate(-545.000000, -730.000000)">
            <g id="入口" transform="translate(280.000000, 220.000000)">
                <g id="模块" transform="translate(230.000000, 490.000000)">
                    <g id="数据目录" transform="translate(35.000000, 20.000000)">
                        <rect id="矩形" x="0" y="0" width="50" height="50"></rect>
                        <g id="编组" opacity="0.200000003" transform="translate(4.000000, 3.000000)" fill-rule="nonzero">
                            <path d="M20.9999999,25.9186983 C20.9999999,25.1007268 21.6715729,24.4376299 22.4999999,24.4376299 L37.4999999,24.4376299 C38.3284271,24.4376299 38.9999999,25.1007268 38.9999999,25.9186983 L38.9999999,42.5185138 C38.9999999,43.0551227 38.7063333,43.5498407 38.2319631,43.8116927 C37.757593,44.0735446 37.1768817,44.0615673 36.714,43.7803841 L30.786,40.1725013 C30.3039608,39.8796808 29.6960392,39.8796808 29.214,40.1725013 L23.286,43.7803841 C22.8231183,44.0615673 22.242407,44.0735446 21.7680369,43.8116927 C21.2936667,43.5498407 20.9999999,43.0551227 20.9999999,42.5185138 L20.9999999,25.9186983 Z" id="路径" fill="#FF9729"></path>
                            <path d="M37.4999999,24.4376299 C38.3284271,24.4376299 38.9999999,25.1007268 38.9999999,25.9186983 L38.9999999,42.5185138 C38.9999999,43.0551227 38.7063333,43.5498407 38.2319631,43.8116927 C37.757593,44.0735446 37.1768817,44.0615673 36.714,43.7803841 L30.786,40.1725013 C30.3039608,39.8796808 29.6960392,39.8796808 29.214,40.1725013 L23.286,43.7803841 C22.8231183,44.0615673 22.242407,44.0735446 21.7680369,43.8116927 C21.2936667,43.5498407 20.9999999,43.0551227 20.9999999,42.5185138 L20.9999999,25.9186983 C20.9999999,25.1007268 21.6715729,24.4376299 22.4999999,24.4376299 L37.4999999,24.4376299 Z M6.837,2.22160263 C8.12882223,2.22160263 9.2757842,3.0375448 9.684,4.24770439 L9.684,4.24770439 L9.816,4.63870647 C10.2242158,5.84886606 11.3711778,6.66511529 12.663,6.66480814 L12.663,6.66480814 L23.337,6.66480814 C24.6288222,6.66511529 25.7757842,5.84886606 26.184,4.63870647 L26.184,4.63870647 L26.316,4.24770439 C26.7238666,3.0385789 27.8692821,2.22257037 29.16,2.22160263 L29.16,2.22160263 L33,2.22160263 C34.6568542,2.22160263 36,3.5477966 36,5.18373967 L36,5.18373967 L36,21.4754929 L22.5,21.4754929 C21.3065258,21.4754929 20.1619332,21.943615 19.3180195,22.7768776 C18.4741058,23.6101403 18,24.740287 18,25.9186983 L18,25.9186983 L18,42.5185138 C17.9987506,42.9144446 18.0501908,43.3088216 18.153,43.69152 L18.153,43.69152 L3,43.69152 C1.34314575,43.69152 0,42.3653262 0,40.7293831 L0,40.7293831 L0,5.18373967 C0,3.5477966 1.34314575,2.22160263 3,2.22160263 L3,2.22160263 Z M22.5,0 C23.7426407,0 24.75,0.994645415 24.75,2.22160271 C24.75,3.44856001 23.7426407,4.44320543 22.5,4.44320543 L13.5,4.44320543 C12.2573593,4.44320543 11.25,3.44856001 11.25,2.22160271 C11.25,0.994645415 12.2573593,0 13.5,0 L22.5,0 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        </g>
                        <g id="编组" transform="translate(7.000000, 3.000000)" fill-rule="nonzero">
                            <path d="M20.9999999,25.9186983 C20.9999999,25.1007268 21.6715729,24.4376299 22.4999999,24.4376299 L37.4999999,24.4376299 C38.3284271,24.4376299 38.9999999,25.1007268 38.9999999,25.9186983 L38.9999999,42.5185138 C38.9999999,43.0551227 38.7063333,43.5498407 38.2319631,43.8116927 C37.757593,44.0735446 37.1768817,44.0615673 36.714,43.7803841 L30.786,40.1725013 C30.3039608,39.8796808 29.6960392,39.8796808 29.214,40.1725013 L23.286,43.7803841 C22.8231183,44.0615673 22.242407,44.0735446 21.7680369,43.8116927 C21.2936667,43.5498407 20.9999999,43.0551227 20.9999999,42.5185138 L20.9999999,25.9186983 Z" id="路径" fill="#FF9729"></path>
                            <path d="M37.4999999,24.4376299 C38.3284271,24.4376299 38.9999999,25.1007268 38.9999999,25.9186983 L38.9999999,42.5185138 C38.9999999,43.0551227 38.7063333,43.5498407 38.2319631,43.8116927 C37.757593,44.0735446 37.1768817,44.0615673 36.714,43.7803841 L30.786,40.1725013 C30.3039608,39.8796808 29.6960392,39.8796808 29.214,40.1725013 L23.286,43.7803841 C22.8231183,44.0615673 22.242407,44.0735446 21.7680369,43.8116927 C21.2936667,43.5498407 20.9999999,43.0551227 20.9999999,42.5185138 L20.9999999,25.9186983 C20.9999999,25.1007268 21.6715729,24.4376299 22.4999999,24.4376299 L37.4999999,24.4376299 Z M6.837,2.22160263 C8.12882223,2.22160263 9.2757842,3.0375448 9.684,4.24770439 L9.684,4.24770439 L9.816,4.63870647 C10.2242158,5.84886606 11.3711778,6.66511529 12.663,6.66480814 L12.663,6.66480814 L23.337,6.66480814 C24.6288222,6.66511529 25.7757842,5.84886606 26.184,4.63870647 L26.184,4.63870647 L26.316,4.24770439 C26.7238666,3.0385789 27.8692821,2.22257037 29.16,2.22160263 L29.16,2.22160263 L33,2.22160263 C34.6568542,2.22160263 36,3.5477966 36,5.18373967 L36,5.18373967 L36,21.4754929 L22.5,21.4754929 C21.3065258,21.4754929 20.1619332,21.943615 19.3180195,22.7768776 C18.4741058,23.6101403 18,24.740287 18,25.9186983 L18,25.9186983 L18,42.5185138 C17.9987506,42.9144446 18.0501908,43.3088216 18.153,43.69152 L18.153,43.69152 L3,43.69152 C1.34314575,43.69152 0,42.3653262 0,40.7293831 L0,40.7293831 L0,5.18373967 C0,3.5477966 1.34314575,2.22160263 3,2.22160263 L3,2.22160263 Z M22.5,0 C23.7426407,0 24.75,0.994645415 24.75,2.22160271 C24.75,3.44856001 23.7426407,4.44320543 22.5,4.44320543 L13.5,4.44320543 C12.2573593,4.44320543 11.25,3.44856001 11.25,2.22160271 C11.25,0.994645415 12.2573593,0 13.5,0 L22.5,0 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
