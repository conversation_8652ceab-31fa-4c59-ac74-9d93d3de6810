import api from '@/api/index'
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/** 登录用户白名单校验
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor', 'test']
  return valid_map.indexOf(str.trim()) >= 0
}

// 包括负数在内的含小数在内的数字
export function validNumber(rule, value, callback) {
  const myreg = /^-?\d+(\.\d+)?$/

  if (myreg.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确的数字格式'))
  }
}
// 身份证校验
export function validIdCard(rule, value, callback) {
  const myreg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (myreg.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确的身份证号码'))
  }
}
// 银行卡号校验
export function validBankCard(rule, value, callback) {
  const myreg = /^[1-9]\d{15,18}$/
  if (myreg.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确的行卡号'))
  }
}
/** 手机号校验
 * @param {string}
 * @returns {Boolean}
 */
export function validPhone(rule, value, callback) {
  const myreg = /^[1][3,4,5,7,8,9][0-9]{9}$/
  if (myreg.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确的手机号码'))
  }
}

/** 邮箱校验
 * @param {string} str
 * @returns {Boolean}
 */
export function validEmail(rule, value, callback) {
  const myreg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
  if (myreg.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确的邮箱地址'))
  }
}
/** 时间和带日期时间字符串校验 */
export function validTimeDate(rule, value, callback) {
  const myreg = /^\d{4}-\d{2}-\d{2}$/
  const myreg2 = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
  if ((myreg.test(value) && !isNaN(new Date(value))) || myreg2.test(value)) {
    return callback()
  } else {
    return callback(new Error('请输入正确日期(YYYY-MM-DD)或者日期时间(YYYY-MM-DD HH:MM:SS)'))
  }
}

// 1-20位英文
export function sexEnglish(val) {
  let reg = /^[a-zA-Z]{1,20}$/
  return reg.test(val)
}

// 中文不超过20个字符
export function checkChinese20(val) {
  let reg = /^[\u4e00-\u9fa5]{1,20}$/
  return reg.test(val)
}

// 匹配正整数
export function Integer(val) {
  let reg = /^[0-9]\d*$/
  return reg.test(val)
}

//匹配不小于一的数
export function checkNum(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入字段长度'))
  } else {
    if (Number(value) < 1) {
      callback(new Error('需大于等于1的正整数'))
    } else if (isNaN(Number(value))) {
      callback(new Error('请输入数字'))
    } else {
      callback()
    }
  }
}
// 纯中文,仅支持汉字，2~30个字符
export async function checkFullCName(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]){2,30}$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 31) {
    if (validatorType && validatorApi) {
      //测试数据

      await api[validatorType][validatorApi](validatorParams)
        .then((res) => {
          if (res.success) {
            if (typeof res.data === 'object') {
              if (res.data?.success) {
                return callback('名称重复')
              } else {
                return callback()
              }
            } else {
              if (res.data) {
                return callback('名称重复')
              } else {
                return callback()
              }
            }
          } else {
            return callback('名称校检失败')
          }
        })
        .catch(() => {
          return callback('名称校检失败')
        })
    } else {
      return callback()
    }
  } else {
    callback(new Error('仅支持汉字，2~30个字符'))
  }
}

// 表单检测名称，不为空,支持汉字、英文、数字、中划线、下划线，2~30个字符
export async function checkCName(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-]){2,30}$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 31) {
    if (validatorType && validatorApi) {
      //测试数据
      await api[validatorType][validatorApi](validatorParams)
        .then((res) => {
          if (res.success) {
            if (typeof res.data === 'object') {
              if (res.data?.success) {
                return callback('名称重复')
              } else {
                return callback()
              }
            } else {
              if (res.data) {
                return callback('名称重复')
              } else {
                return callback()
              }
            }
          } else {
            return callback('名称校检失败')
          }
        })
        .catch(() => {
          return callback('名称校检失败')
        })
    } else {
      return callback()
    }
  } else {
    return callback(new Error('支持汉字、英文、数字、中划线、下划线，2~30个字符'))
  }
}
// 表单检测名称，不为空,支持汉字、英文、数字、中划线、下划线，2~500个字符
export async function checkCName500(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-]){2,500}$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 501) {
    if (validatorType && validatorApi) {
      //测试数据
      await api[validatorType][validatorApi](validatorParams)
        .then((res) => {
          if (res.success) {
            if (typeof res.data === 'object') {
              if (res.data?.success) {
                return callback('名称重复')
              } else {
                return callback()
              }
            } else {
              if (res.data) {
                return callback('名称重复')
              } else {
                return callback()
              }
            }
          } else {
            return callback('名称校检失败')
          }
        })
        .catch(() => {
          return callback('名称校检失败')
        })
    } else {
      return callback()
    }
  } else {
    return callback(new Error('支持汉字、英文、数字、中划线、下划线，2~500个字符'))
  }
}
// 表单检测名称,必须包含汉字，可以有特殊字符，2~500个字符
export async function checkCName500C(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^[\u4e00-\u9fa5\s\S]{2,500}$/
  let containsChinese  =  /[\u4e00-\u9fa5]+/.test(value);
  // 检查长度和是否符合正则表达式
  if (!regex.test(value) || !containsChinese) {
    return callback(new Error('内容必须包含汉字，2~500个字符'));;
  }
  return callback()
}
// 表单检测名称，不为空,支持汉字、英文、数字、中划线、下划线，1~100个字符
export async function checkCName100(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-]){1,100}$/
  let res = regex.test(value)
  if (res && value.length > 0 && value.length < 101) {
    if (validatorType && validatorApi) {
      //测试数据
      await api[validatorType][validatorApi](validatorParams)
        .then((res) => {
          if (res.success) {
            if (typeof res.data === 'object') {
              if (res.data?.success) {
                return callback('名称重复')
              } else {
                return callback()
              }
            } else {
              if (res.data) {
                return callback('名称重复')
              } else {
                return callback()
              }
            }
          } else {
            return callback('名称校检失败')
          }
        })
        .catch(() => {
          return callback('名称校检失败')
        })
    } else {
      return callback()
    }
  } else {
    return callback(new Error('支持汉字、英文、数字、中划线、下划线，1~100个字符'))
  }
}
// 表单检测名称，不为空,支持汉字、英文、数字、中划线、下划线，2~80个字符
export function checkCName80(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }
  let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-]){2,80}$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 81) {
    return callback()
  } else {
    return callback(new Error('支持汉字、英文、数字、中划线、下划线，2~80个字符'))
  }
}

// 表单检测英文名称，不为空,支持英文、数字、下划线，2~80个字符
export async function checkName(
  rule,
  value,
  callback,
  pars,
  obj,
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 81) {
    if (validatorType && validatorApi) {
      //测试数据

      await api[validatorType][validatorApi](validatorParams)
        .then((res) => {
          if (res.success) {
            if (typeof res.data === 'object') {
              if (res.data?.success) {
                return callback('名称重复')
              } else {
                return callback()
              }
            } else {
              if (res.data) {
                return callback('名称重复')
              } else {
                return callback()
              }
            }
          } else {
            return callback('名称校检失败')
          }
        })
        .catch(() => {
          return callback('名称校检失败')
        })
    } else {
      return callback()
    }
  } else {
    callback(new Error('支持英文、数字、下划线，只能以英文开头，2~80个字符'))
  }
}
// 表单检测英文名称，不为空,支持英文、数字、下划线，2~256个字符
export function checkName256(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入英文名称'))
  }

  let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,256})$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 256) {
    callback()
  } else {
    callback(new Error('支持英文、数字、下划线，只能以英文开头，2~256个字符'))
  }
}

// 表单检测路由英文名称，不为空,支持英文、数字、下划线，2~50个字符
export function checkRouterName(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入路由名称'))
  }

  let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,50})$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 50) {
    callback()
  } else {
    callback(new Error('支持英文、数字、下划线，只能以英文开头，2~50个字符'))
  }
}
// 表单检测名称，可为空，限制输入3个，输入多个用英文逗号隔开，超过3个则提示
export function checkNameByComma(rule, value, callback) {
  if (!value) {
    return callback()
  }

  // let regex = /^\s*\S+(\s*,\s*\S+){0,2}\s*$/
  // let regex = /^([^,]*,[^,]*,[^,]*)$/
  let regex = /^([^,]*,){0,2}[^,]*$/

  let res = regex.test(value)
  console.log(res)
  if (res) {
    callback()
  } else {
    callback(new Error('暂时仅支持3个的限定'))
  }
}

// 表单检测编码，不为空,支持英文、下划线，只能以英文开头，2~20个字符
export function checkCCode(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入编码'))
  }
  let regex = /^[a-zA-Z]([_a-zA-Z]{1,19})$/
  let res = regex.test(value)
  if (res && value.length > 1 && value.length < 21) {
    callback()
  } else {
    callback(new Error('支持英文、下划线，只能以英文开头，2~20个字符'))
  }
}

// 表单检测ip，不为空
export function checkCIp(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入IP地址'))
  }

  let regex =
    /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9])\.((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.){2}(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$/
  let res = regex.test(value)
  if (res) {
    callback()
  } else {
    callback(new Error('IP输入不正确'))
  }
}
// 表单检测请求地址，不为空
export function checkUrl(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入IP地址'))
  }
  let regexDomain =
    /^((http|https):\/\/)?(([A-Za-z0-9]+-[A-Za-z0-9]+|[A-Za-z0-9]+)\.)+([A-Za-z]+)[/?:]?.*$/
  let regexIp =
    /^((http|https):\/\/)?(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9])\.((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.){2}(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)[/?:]?.*$/
  let res = regexDomain.test(value) || regexIp.test(value)
  if (res) {
    callback()
  } else {
    callback(new Error('请求地址输入不正确'))
  }
}

// 表单检测端口，不为空
export function checkCPort(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入端口号'))
  }

  if (1 <= 1 * Number(value) && 1 * Number(value) <= 65535) {
    callback()
  } else {
    callback(new Error('端口号为1到65535的整数'))
  }
}

// 表单检测Path相对路径，不为空，必须以 /开头，并且不能两个//相邻的英文和/的组合，2-200字符
export function checkCRelativePath(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入Path路径'))
  }
  let regex = /^([a-zA-Z\/]){2,200}$/
  let res = regex.test(value)
  if (res) {
    let passed = false
    let a = value.split('/')
    a.forEach((item, index) => {
      if (item === '' && index === 0) {
        passed = true
      } else {
        if (item === '') {
          passed = false
        }
      }
    })
    if (passed) {
      callback()
    } else {
      callback(new Error('Path输入不正确, /开头'))
    }
  } else {
    callback(new Error('Path输入不正确, /开头'))
  }
}

// 匹配是否为正整数(包含0)
export function checkNumber(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入数字'))
  }
  let regex = /^([0]|[1-9][0-9]*)$/
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('请填写≥0的正整数'))
  }
}
// 匹配是否为正整数(不包含0)
export function checkZNumber(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入数字'))
  }
  let regex = /^([1-9][0-9]*)$/
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('请填写>0的正整数'))
  }
}
// 匹配为英文名
export function checkEnName(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入英文'))
  }
  // let regex = new RegExp('')
  let regex = /^[a-zA-Z][0-9a-zA-Z_]*$/
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('支持字母或者英文+字母'))
  }
}
// 匹配为中英文名
export function checkECName(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入'))
  }
  let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('计量单位不支持数字和特殊符号'))
  }
}
// 匹配输入是否为某个文件的路径
export function checkFilePath(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入文件路径'))
  }
  // let regex = new RegExp('')
  let regex = /([a-zA-z]:(?:[\\|\/][a-zA-Z\d-_. ]+)*)([\\|\/])([a-zA-Z\d-_]+\.[a-zA-z]+)/g
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('文件路径格式不正确'))
  }
}

// 匹配输入是文件的路径
export function checkFileUrl(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入文件路径'))
  }
  let regex = /^https?:\/\/[^ \/$.?#].[^\s]*$/g
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('文件路径格式不正确'))
  }
}

/**查找树中某一个符合条件的子节点
 *
 * @param {array} tree //传入的树节点
 * @param {string|number} key
 * @param {object} options
 * @returns {array} 返回查找到的节点
/** */
export const treeFind = function (
  tree,
  key,
  { nodeKey = 'id', childrenKey = 'children' } = {},
  recursiveParams = { isFind: false, result: null },
) {
  tree.some((v) => {
    if (recursiveParams.isFind) return true
    const isSame = v[nodeKey] === key
    if (isSame) {
      recursiveParams.isFind = isSame
      recursiveParams.result = v
      return isSame
    }
    if (v[childrenKey] && v[childrenKey].length) {
      treeFind(v[childrenKey], key, { key, childrenKey }, recursiveParams)
    }
  })
  return recursiveParams.result
}

// @keydown输入框只能输入正负整数
export function onKeydownInteger(e, value) {
  let key = e.key
  let number = '-1234567890'
  if (number.includes(key) || key === 'Backspace') {
    // -号只能开头用
    if (key === '-') {
      if (value !== '') {
        e.returnValue = false
      }
    }
    if (value?.toString().length === 1 && key === '0') {
      e.returnValue = false
    } else {
      e.returnValue = true
    }
  } else {
    e.returnValue = false
  }
}

// @keydown输入框只能输入正整数 需要搭配下方的 @keyup onKeyupPositiveInteger 组织中文状态下的输入
export function onKeydownPositiveInteger(e, value) {
  let key = e.key
  let number = '1234567890'
  if (number.includes(key) || key === 'Backspace') {
    if (value?.toString().length === 0 && key === '0') {
      e.returnValue = false
    } else {
      e.returnValue = true
    }
  } else {
    e.returnValue = false
  }
}
//只能输入正整数
export function onKeyupPositiveInteger(e) {
  e.target.value = e.target.value.replace(/\D/g, '')
  let _data = e.target.value?.toString().split('')
  if (_data[0] === '0') {
    e.target.value = ''
  }
}

// @keydown输入框只能输入数字且最多两位小数  需要搭配下方的 @keyup onKeyupNumber 组织中文状态下的输入
export function onKeydownNumber(e, value) {
  let key = e.key
  let number = '-1234567890.ArrowLeftArrowRight'
  if (key === 'Backspace') {
    e.returnValue = true
  } else {
    if (number.includes(key)) {
      // -号只能开头用
      if (key === '-') {
        if (value !== '') {
          e.returnValue = false
        }
      }
      // 不能超过小数点后2位
      if (value?.toString().indexOf('.') !== -1) {
        let str = value.split('.')[1]
        if (str.length > 1) {
          e.returnValue = false
        }
      }
      // // 除小数外不能0开头
      // if (value === '0' && key !== '.') {
      //   e.returnValue = false
      // }
      if (key === '.') {
        // 不能以.开头
        if (value === '') {
          e.returnValue = false
        }
        // 不能输入2个小数点
        if (value?.toString().indexOf('.') !== -1) {
          e.returnValue = false
        }
      }
      e.returnValue = true
    } else {
      e.returnValue = false
    }
  }
}

//只能输入正整数 和小数
export function onKeyupNumber(e) {
  e.target.value = e.target.value.replace(/[^\d\.]/g, '')
}

// 逆波兰表达式的计算四则运算表达式
export function parseExpression(expression) {
  // 将表达式转换为逆波兰表达式

  var tokens = expression.split(' ')
  var stack = []
  var output = []
  var precedence = {
    '+': 1,
    '-': 1,
    '*': 2,
    '/': 2,
  }
  for (var i = 0; i < tokens.length; i++) {
    var token = tokens[i]
    if (!isNaN(token)) {
      output.push(token)
    } else if (token in precedence) {
      while (
        stack.length > 0 &&
        stack[stack.length - 1] in precedence &&
        precedence[stack[stack.length - 1]] >= precedence[token]
      ) {
        output.push(stack.pop())
      }
      stack.push(token)
    } else if (token == '(') {
      stack.push(token)
    } else if (token == ')') {
      while (stack.length > 0 && stack[stack.length - 1] != '(') {
        output.push(stack.pop())
      }
      stack.pop()
    }
  }

  while (stack.length > 0) {
    output.push(stack.pop())
  }
  // 计算逆波兰表达式的值
  var stack = []
  for (var i = 0; i < output.length; i++) {
    var token = output[i]
    if (!isNaN(token)) {
      stack.push(parseFloat(token))
    } else {
      var b = stack.pop()
      var a = stack.pop()
      if (token == '+') {
        stack.push(a + b)
      } else if (token == '-') {
        stack.push(a - b)
      } else if (token == '*') {
        stack.push(a * b)
      } else if (token == '/') {
        stack.push(a / b)
      }
    }
  }
  return stack.pop()
}
// 示例
// var expression = '13 + 4 * ( 12 - 1 )'
// var value = parseExpression(expression)

// sftp采集文件路径规则，不为空,以/开头支持汉字、英文、数字、中划线、下划线，多个以逗号分开
export function checkMoreFileUrl(rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入'))
  }
  let regex =
    /^[/]([/]|[\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-])*?([\,][/]([/]|[\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-])*)*$/
  let res = regex.test(value)
  if (res && value.length > 1) {
    return callback()
  } else {
    return callback(new Error('以/开头,支持汉字、英文、数字、中划线、下划线,多个以,隔开'))
  }
}

// 表单检测英文名称，不为空并且不包含关键字，支持英文、数字、下划线，2~20个字符
export async function checkNameExcludeKeywords(
  rule,
  value,
  callback,
  pars,
  obj,
  excludeKeywords = null, //排除的关键字
  validatorType, //验重接口所属模块
  validatorApi, //验重接口名称
  validatorParams, //验证参数
) {
  if (!value) {
    return callback(new Error('请输入名称'))
  }

  let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
  let res = regex.test(value)
  let _excludeKeywords = excludeKeywords || [
    'add',
    'admin',
    'after',
    'all',
    'alter',
    'analyze',
    'and',
    'archive',
    'array',
    'as',
    'ASC',
    'authorization',
    'before',
    'between',
    'bigint',
    'binary',
    'boolean',
    'both',
    'bucket',
    'buckets',
    'by',
    'cascade',
    'case',
    'cast',
    'change',
    'char',
    'cluster',
    'clustered',
    'clusterstatus',
    'collection',
    'column',
    'columns',
    'comment',
    'compact',
    'compactions',
    'compute',
    'concatenate',
    'conf',
    'continue',
    'create',
    'cross',
    'cube',
    'current',
    'current_date',
    'current_timestamp',
    'cursor',
    'data',
    'database',
    'databases',
    'date',
    'datetime',
    'day',
    'dbproperties',
    'decimal',
    'deferred',
    'defined',
    'delete',
    'delimited',
    'dependency',
    'desc',
    'describe',
    'directories',
    'directory',
    'disable',
    'distinct',
    'distribute',
    'double',
    'drop',
    'elem_type',
    'else',
    'enable',
    'end',
    'escaped',
    'exchange',
    'exclusive',
    'exists',
    'explain',
    'export',
    'extended',
    'external',
    'false',
    'fetch',
    'fields',
    'file',
    'fileformat',
    'first',
    'float',
    'following',
    'for',
    'format',
    'formatted',
    'from',
    'full',
    'function',
    'functions',
    'grant',
    'group',
    'grouping',
    'having',
    'hold_ddltime',
    'hour',
    'idxproperties',
    'if',
    'ignore',
    'import',
    'in',
    'index',
    'indexes',
    'inner',
    'inpath',
    'inputdriver',
    'inputformat',
    'insert',
    'int',
    'intersect',
    'interval',
    'into',
    'is',
    'items',
    'jar',
    'join',
    'keys',
    'key_type',
    'lateral',
    'left',
    'less',
    'like',
    'limit',
    'lines',
    'load',
    'local',
    'location',
    'lock',
    'locks',
    'logical',
    'long',
    'macro',
    'map',
    'mapjoin',
    'materialized',
    'minus',
    'minute',
    'month',
    'more',
    'msck',
    'none',
    'noscan',
    'not',
    'no_drop',
    'null',
    'of',
    'offline',
    'on',
    'option',
    'or',
    'order',
    'out',
    'outer',
    'OutPutDriver',
    'outputformat',
    'over',
    'overwrite',
    'owner',
    'PartialScan',
    'partition',
    'partitioned',
    'partitions',
    'percent',
    'plus',
    'preceding',
    'preserve',
    'pretty',
    'principals',
    'procedure',
    'protection',
    'purge',
    'range',
    'read',
    'readonly',
    'reads',
    'rebuild',
    'recordreader',
    'recordwriter',
    'reduce',
    'regexp',
    'reload',
    'rename',
    'repair',
    'replace',
    'restrict',
    'revoke',
    'rewrite',
    'right',
    'rlike',
    'role',
    'roles',
    'rollup',
    'row',
    'rows',
    'schema',
    'schemas',
    'second',
    'select',
    'semi',
    'serde',
    'serdeproperties',
    'server',
    'set',
    'sets',
    'shared',
    'show',
    'show_database',
    'skewed',
    'smallint',
    'sort',
    'sorted',
    'ssl',
    'statistics',
    'stored',
    'streamtable',
    'string',
    'struct',
    'table',
    'tables',
    'tablesample',
    'tblproperties',
    'temporary',
    'terminated',
    'then',
    'timestamp',
    'tinyint',
    'to',
    'touch',
    'transactions',
    'transform',
    'trigger',
    'true',
    'truncate',
    'unarchive',
    'unbounded',
    'undo',
    'union',
    'uniontype',
    'uniquejoin',
    'unlock',
    'unset',
    'unsigned',
    'update',
    'uri',
    'use',
    'user',
    'using',
    'utc',
    'utctimestamp',
    'values',
    'value_type',
    'varchar',
    'view',
    'when',
    'where',
    'while',
    'window',
    'with',
    'year',
  ]

  if (res && value.length > 1 && value.length < 81) {
    if (!_excludeKeywords.includes(value.toLowerCase())) {
      if (validatorType && validatorApi) {
        //测试数据
        await api[validatorType][validatorApi](validatorParams)
          .then((res) => {
            if (res.success) {
              if (typeof res.data === 'object') {
                if (res.data?.success) {
                  return callback('名称重复')
                } else {
                  return callback()
                }
              } else {
                if (res.data) {
                  return callback('名称重复')
                } else {
                  return callback()
                }
              }
            } else {
              return callback('名称校检失败')
            }
          })
          .catch(() => {
            return callback('名称校检失败')
          })
      } else {
        return callback()
      }
    } else {
      callback(new Error('模型英文名不能包含数据库关键字，请重新输入'))
    }
  } else {
    callback(new Error('支持英文、数字、下划线，只能以英文开头，2~80个字符'))
  }
}
//格式化数据
export function formatFn(
  originalData, //值 123400
  formatPrecision, //小数位数 2
  formatUnit, //单位值 K
  formatCurrencyLabel, //货币标志 ￥
  formatUseThousandsSeparator, //是否使用千分位分隔符
  formatType,
  measureUnit,
  showTypeName = false,
) {
  //解决 toFixedFn() 精度丢失问题：重写 toFixed 方法
  const toFixedFn = (number, precision = 2) => {
    number = Math.round(+number + 'e' + precision) / Math.pow(10, precision) + ''
    let s = number.split('.')
    if ((s[1] || '').length < precision) {
      s[1] = s[1] || ''
      s[1] += new Array(precision - s[1].length).fill('0').join('')
    }
    return s.join('.')
  }
  let _num = 1000 //byte
  let formatTypeName = '无格式'

  try {
    let _number = Number(originalData) || 0
    let _unit = ''
    let _huobiUnit = ''
    let _percentage = ''
    let _measureUnit = ''
    switch (formatType) {
      case 'NONE':
        formatTypeName = '无格式'
        break
      case 'NUMERIC':
        formatTypeName = '数值'
        break
      case 'CURRENCY':
        formatTypeName = '货币'
        break
      case 'PERCENTAGE':
        formatTypeName = '百分比'
        _percentage = '%'
        _number = _number * 100
        break
    }
    if (formatUnit) {
      //单位转换 四舍五入
      switch (formatUnit) {
        case 'AUTO':
          _unit = 'K'
          _number = toFixedFn(_number / _num, 2)
          break
        case 'K':
          _unit = 'K'
          _number = _number / _num
          break
        case 'M':
          _unit = 'M'
          _number = _number / Math.pow(_num, 2)
          break
        case 'B':
          _unit = 'B'
          _number = _number / Math.pow(_num, 3)
          break
        default:
          break
      }
    }

    if (formatPrecision || formatPrecision === 0) {
      let _get_number = _number * 1
      _number = toFixedFn(_get_number, formatPrecision)
    }
    if (formatUseThousandsSeparator) {
      let _get_number_agin = _number.split('.')
      let _part1 = ''
      let _part2 = ''
      if (_get_number_agin?.length) {
        _part1 = Number(_get_number_agin[0]).toLocaleString()
        if (_get_number_agin[1]) {
          _part2 = _get_number_agin[1]
        }
      }
      _number = _part2 ? _part1 + '.' + _part2 : _part1
    }
    if (formatCurrencyLabel) {
      _huobiUnit = formatCurrencyLabel
    }
    if (measureUnit) {
      _measureUnit = measureUnit
    }
    if (formatType === 'NONE') {
      return (showTypeName ? formatTypeName : '') + originalData + _measureUnit
    } else {
      return (
        (showTypeName ? formatTypeName : '') +
        _huobiUnit +
        _number +
        _unit +
        _percentage +
        _measureUnit
      )
    }
  } catch {
    return (showTypeName ? formatTypeName : '') + originalData
  }
}
// 表单检测英文名称，不为空,支持英文、数字、下划线，只能以英文开头1~50个字符
export function checkName50(rule, value, callback) {
  if (!value) {
    return callback(new Error('可输入字母数字下划线，以字母开头，最大50位'))
  }
  let regex = /^[a-zA-Z]([a-zA-Z_0-9]{0,49})$/
  let res = regex.test(value)
  if (res && value.length >= 1) {
    return callback()
  } else {
    return callback(new Error('名称以字母开头，包含大小写字母数字下划线，最大50位'))
  }
}