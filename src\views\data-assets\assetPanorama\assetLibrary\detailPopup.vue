<template>
  <div class="asset-library-detail-popup">
    <el-dialog
      :title="dialogTitle"
      class="largeDialog"
      :model-value="dialogVisible"
      :width="width"
      draggable
      :close-on-click-modal="false"
      @close="handleCloseDialog"
    >
      <el-form
        ref="detailRuleForm"
        :model="detailRuleForm"
        label-width="80px"
        label-position="right"
        class="rerun-ruleForm"
      >
        <el-form-item
          :label="item.label + ':'"
          v-for="item in Object.values(detailData)"
          :key="item.label"
        >
          <el-input
            v-if="item.type === 'input'"
            :model-value="detailRuleForm[item.key]"
            placeholder=""
            disabled
          />
          <el-input
            v-if="item.type === 'textarea'"
            :model-value="detailRuleForm[item.key]"
            :autosize="{ minRows: 3 }"
            type="textarea"
            placeholder=""
            disabled
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span slot="footer" class="dialog-footer">
          <n-button
            size="sm"
            variant="solid"
            color="primary"
            @click.stop.prevent="handleCloseDialog"
            >关 闭</n-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  export default {
    props: {
      dialogTitle: {
        type: String,
        default: '元数据详情',
      },
      dialogVisible: {
        type: Boolean,
        default: false,
      },
      detailRuleForm: {
        type: Object,
        default: () => {},
      },
      detailData: {
        type: Object,
        default: () => {},
      },
      width: {
        type: Number,
        default: 580,
      },
    },
    emits: ['closeDialog'],
    setup(props, { emit }) {
      const handleCloseDialog = () => {
        emit('closeDialog')
      }

      return {
        handleCloseDialog,
      }
    },
  }
</script>
