<template>
  <div class="drag-table">
    <n-form class="regInfoForm" ref="regInfoForm" :data="state" validate-on-rule-change labelSuffix="：" label-width="135px">
      <table cellspacing="0" cellpadding="0">
        <thead>
          <tr>
            <th
              v-for="(header, index) in headers"
              :key="index"
              :style="{ width: columnWidths[index] + 'px' }"
              class="drag-column-header"
              @mousedown="startDragColumn(index, $event)"
            >
              {{ header.name }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in state.data" :key="rowIndex">
            <template v-for="(cell, cellIndex) in headers" :key="cellIndex">
              <td
                v-if="cell.value === 'drag'"
                draggable="true"
                @dragstart="dragStart(rowIndex)"
                @dragover.prevent
                @drop="drop(rowIndex)"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <circle
                    cx="10"
                    cy="3.5"
                    r="0.5"
                    transform="rotate(90 10 3.5)"
                    fill="#606266"
                    stroke="#606266"
                  />
                  <circle
                    cx="6"
                    cy="3.5"
                    r="0.5"
                    transform="rotate(90 6 3.5)"
                    fill="#606266"
                    stroke="#606266"
                  />
                  <circle
                    cx="10"
                    cy="8"
                    r="0.5"
                    transform="rotate(90 10 8)"
                    fill="#606266"
                    stroke="#606266"
                  />
                  <circle
                    cx="6"
                    cy="8"
                    r="0.5"
                    transform="rotate(90 6 8)"
                    fill="#606266"
                    stroke="#606266"
                  />
                  <circle
                    cx="10"
                    cy="12.5"
                    r="0.5"
                    transform="rotate(90 10 12.5)"
                    fill="#606266"
                    stroke="#606266"
                  />
                  <circle
                    cx="6"
                    cy="12.5"
                    r="0.5"
                    transform="rotate(90 6 12.5)"
                    fill="#606266"
                    stroke="#606266"
                  />
                </svg>
              </td>
              <td v-else-if="cell.value === 'columnComment'" @click.stop="row.flag = true">
                <n-form-item
                  :field="`data[${rowIndex}].columnComment`"
                  :rules="
                    rowIndex >= 0 && {
                      required: true,
                      validator: (...args) =>
                        checkCName500C(...args, null, null, {
                          nameType: 'CN',
                          name: row.sinkMetaCnName,
                          id: null,
                        }),
                      trigger: 'change',
                    }
                  "
                  style="margin-bottom: 8px"
                  ><div class="columnComment" v-if="!row.flag">{{ row.columnComment }}</div>

                  <n-input
                    ref="firstNameRef"
                    v-else
                    v-model="row.columnComment"
                    @blur="
                      () => {
                        row.flag = false
                      }
                    "
                  />
                </n-form-item>
              </td>
              <td v-else-if="cell.value === 'hidden'"
                ><n-checkbox label="" :isShowTitle="false" v-model="row[cell.value]"
              /></td>
              <td v-else>{{ row[cell.value] }}</td>
            </template>
          </tr>
        </tbody>
      </table>
    </n-form>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { checkCName500C } from '@/utils/validate'
  const props = defineProps({
    headers: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
  })

  const regInfoForm = ref(null)
  const dragRowIndex = ref(-1)
  const columnWidths = ref([])
  const dragColumnIndex = ref(-1)
  const startX = ref(0)
  
  const state = reactive({
    data: [],
  })

  const dragStart = (index) => {
    dragRowIndex.value = index
  }

  const drop = (dropIndex) => {
    if (dragRowIndex.value === -1 || dragRowIndex.value === dropIndex) return
    const newData = [...state.data]
    const [draggedRow] = newData.splice(dragRowIndex.value, 1)
    newData.splice(dropIndex, 0, draggedRow)
    state.data = newData
    emit('update:tableData', newData)
    dragRowIndex.value = -1
  }

  const startDragColumn = (index, event) => {
    dragColumnIndex.value = index
    startX.value = event.clientX
    document.addEventListener('mousemove', dragColumn)
    document.addEventListener('mouseup', stopDragColumn)
  }

  const dragColumn = (event) => {
    if (dragColumnIndex.value === -1) return
    const diffX = event.clientX - startX.value
    const newWidth = columnWidths.value[dragColumnIndex.value] + diffX
    if (newWidth > 50) {
      columnWidths.value[dragColumnIndex.value] = newWidth
    }
    startX.value = event.clientX
  }

  const stopDragColumn = () => {
    dragColumnIndex.value = -1
    document.removeEventListener('mousemove', dragColumn)
    document.removeEventListener('mouseup', stopDragColumn)
  }

  const emit = defineEmits(['update:tableData'])

  // 初始化列宽
  onMounted(() => {
    const defaultWidth = 100
    columnWidths.value = new Array(props.headers.length).fill(defaultWidth)
  })

  defineExpose({
    validate:()=>{
     return regInfoForm.value.validate()
    }
  })

  watch(
    () => props.tableData,
    (newVal) => {
      state.data = newVal
  },
    {
      immediate: true,
    },
  )
</script>

<style scoped>
  .drag-table table {
    width: 100%;
    max-height: 500px;
    border-collapse: collapse;
  }

  .drag-table th,
  .drag-table td {
    padding: 8px;
    font-weight: 400;
    text-align: left;
    border: none;
  }

  .drag-table tr {
    border: none;
    border-bottom: 1px solid #e5e6eb;
  }

  .drag-column-header {
    position: relative;
    background: #ebf4ff;
    border-bottom: 1px solid #e5e6eb;
    cursor: col-resize;
  }

  .columnComment {
    width: 100%;
    height: 32px;
    padding-left: 10px;
    line-height: 32px;
    &:hover {
      border: 1px solid #e5e6eb;
      border-radius: 2px;
    }
  }
</style>
