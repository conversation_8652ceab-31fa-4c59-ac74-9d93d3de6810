<template>
  <div class="labelTask">
    <div class="labelTask-head">
      <n-button variant="solid" @click="onSave">保存</n-button>
      <n-button @click="onCancel">取消</n-button>
    </div>
    <div class="labelTask-body">
      <CfTable
        :key="state.key"
        :needNewline="true"
        :loading="state.loading"
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
      >
        <template #anno="scope">
          <n-input v-if="scope.index !== 0" v-model="scope.row.anno" placeholder="请输入标注结果" />
          <span v-else>{{ scope.row.anno }}</span>
        </template>
      </CfTable>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { initiateLabelTaskList, initiateLabelTaskSave } from '@/api/dataManage.js'

  const router = useRouter()
  const props = defineProps({
    info: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['onCancel'])

  const state = reactive({
    loading: false,
    tableData: { list: [] },
    tableHeadTitles: [{ prop: 'anno', name: '标注结果', slot: 'anno' }],
  })

  onMounted(() => {
    onSearch()
  })

  // 获取详情
  const onSearch = (init = false) => {
    let data = {
      problemCode: props.info.code,
      projectCode: props.info.projectCode,
    }
    state.loading = true

    initiateLabelTaskList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableHeadTitles = Object.keys(res.data.problemDetails[0])
            .map((item) => {
              if (item === 'anno') {
                return {
                  prop: 'anno',
                  name: '标注结果',
                  slot: 'anno',
                }
              }
              return {
                prop: item,
                name: item,
              }
            })
            .filter((item) => item.prop !== 'uuid')
          state.tableData.list = res.data.problemDetails
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 保存
  const onSave = () => {
    const dataAnnos = []
    state.tableData.list.forEach((item) => {
      dataAnnos.push({ anno: item.anno, uuid: item.uuid })
    })
    let data = {
      dataAnnos,
      problemCode: props.info.code,
      projectCode: props.info.projectCode,
    }
    state.loading = true
    initiateLabelTaskSave(data).then((res) => {
      state.loading = false
      if (res.success) {
        ElMessage.success('保存成功')
        emit('onCancel')
      }
    })
  }

  // 取消
  const onCancel = () => {
    emit('onCancel')
  }
</script>

<style lang="scss" scoped>
  .labelTask {
    width: 100%;
    height: 100%;

    &-head {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 16px;
    }
    &-body {
      width: 100%;
      height: calc(100% - 48px);
    }
  }
</style>
