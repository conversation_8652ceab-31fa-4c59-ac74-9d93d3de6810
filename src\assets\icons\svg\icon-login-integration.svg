<?xml version="1.0" encoding="UTF-8"?>
<svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>数据集成</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E8EAF3" stop-opacity="0.82760172" offset="49.8724664%"></stop>
            <stop stop-color="#596391" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="2.78011225%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E8EAF3" stop-opacity="0.82760172" offset="49.8724664%"></stop>
            <stop stop-color="#314F8D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="2-数据集成" transform="translate(-385.000000, -730.000000)">
            <g id="入口" transform="translate(280.000000, 220.000000)">
                <g id="模块" transform="translate(70.000000, 490.000000)">
                    <g id="数据集成" transform="translate(35.000000, 20.000000)">
                        <rect id="矩形" x="0" y="0" width="50" height="50"></rect>
                        <g id="编组" opacity="0.200000003" transform="translate(3.500000, 5.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
                            <path d="M37.1428571,28.5714286 C38.7208136,28.5714286 40,29.850615 40,31.4285714 L40,31.4285714 L40,37.1428571 C40,38.7208136 38.7208136,40 37.1428571,40 L37.1428571,40 L2.85714286,40 C1.27918643,40 0,38.7208136 0,37.1428571 L0,37.1428571 L0,31.4285714 C0,29.850615 1.27918643,28.5714286 2.85714286,28.5714286 L2.85714286,28.5714286 Z M10,32.8571429 L5.71428571,32.8571429 C4.9253075,32.8571429 4.28571429,33.4967361 4.28571429,34.2857143 C4.28571429,35.0746925 4.9253075,35.7142857 5.71428571,35.7142857 L5.71428571,35.7142857 L10,35.7142857 C10.7889782,35.7142857 11.4285714,35.0746925 11.4285714,34.2857143 C11.4285714,33.4967361 10.7889782,32.8571429 10,32.8571429 L10,32.8571429 Z M37.1428571,14.2857143 C38.7208136,14.2857143 40,15.5649007 40,17.1428571 L40,17.1428571 L40,22.8571429 C40,24.4350993 38.7208136,25.7142857 37.1428571,25.7142857 L37.1428571,25.7142857 L2.85714286,25.7142857 C1.27918643,25.7142857 0,24.4350993 0,22.8571429 L0,22.8571429 L0,17.1428571 C0,15.5649007 1.27918643,14.2857143 2.85714286,14.2857143 L2.85714286,14.2857143 Z M10,18.5714286 L5.71428571,18.5714286 C4.9253075,18.5714286 4.28571429,19.2110218 4.28571429,20 C4.28571429,20.7889782 4.9253075,21.4285714 5.71428571,21.4285714 L5.71428571,21.4285714 L10,21.4285714 C10.7889782,21.4285714 11.4285714,20.7889782 11.4285714,20 C11.4285714,19.2110218 10.7889782,18.5714286 10,18.5714286 L10,18.5714286 Z M37.1428571,0 C38.7208136,0 40,1.27918643 40,2.85714286 L40,2.85714286 L40,8.57142857 C40,10.149385 38.7208136,11.4285714 37.1428571,11.4285714 L37.1428571,11.4285714 L2.85714286,11.4285714 C1.27918643,11.4285714 0,10.149385 0,8.57142857 L0,8.57142857 L0,2.85714286 C0,1.27918643 1.27918643,0 2.85714286,0 L2.85714286,0 Z M10,4.28571429 L5.71428571,4.28571429 C4.9253075,4.28571429 4.28571429,4.9253075 4.28571429,5.71428571 C4.28571429,6.50326393 4.9253075,7.14285714 5.71428571,7.14285714 L5.71428571,7.14285714 L10,7.14285714 C10.7889782,7.14285714 11.4285714,6.50326393 11.4285714,5.71428571 C11.4285714,4.9253075 10.7889782,4.28571429 10,4.28571429 L10,4.28571429 Z" id="形状结合"></path>
                        </g>
                        <g id="编组" transform="translate(6.500000, 5.000000)" fill="url(#linearGradient-2)" fill-rule="nonzero">
                            <path d="M37.1428571,28.5714286 C38.7208136,28.5714286 40,29.850615 40,31.4285714 L40,31.4285714 L40,37.1428571 C40,38.7208136 38.7208136,40 37.1428571,40 L37.1428571,40 L2.85714286,40 C1.27918643,40 0,38.7208136 0,37.1428571 L0,37.1428571 L0,31.4285714 C0,29.850615 1.27918643,28.5714286 2.85714286,28.5714286 L2.85714286,28.5714286 Z M10,32.8571429 L5.71428571,32.8571429 C4.9253075,32.8571429 4.28571429,33.4967361 4.28571429,34.2857143 C4.28571429,35.0746925 4.9253075,35.7142857 5.71428571,35.7142857 L5.71428571,35.7142857 L10,35.7142857 C10.7889782,35.7142857 11.4285714,35.0746925 11.4285714,34.2857143 C11.4285714,33.4967361 10.7889782,32.8571429 10,32.8571429 L10,32.8571429 Z M37.1428571,14.2857143 C38.7208136,14.2857143 40,15.5649007 40,17.1428571 L40,17.1428571 L40,22.8571429 C40,24.4350993 38.7208136,25.7142857 37.1428571,25.7142857 L37.1428571,25.7142857 L2.85714286,25.7142857 C1.27918643,25.7142857 0,24.4350993 0,22.8571429 L0,22.8571429 L0,17.1428571 C0,15.5649007 1.27918643,14.2857143 2.85714286,14.2857143 L2.85714286,14.2857143 Z M10,18.5714286 L5.71428571,18.5714286 C4.9253075,18.5714286 4.28571429,19.2110218 4.28571429,20 C4.28571429,20.7889782 4.9253075,21.4285714 5.71428571,21.4285714 L5.71428571,21.4285714 L10,21.4285714 C10.7889782,21.4285714 11.4285714,20.7889782 11.4285714,20 C11.4285714,19.2110218 10.7889782,18.5714286 10,18.5714286 L10,18.5714286 Z M37.1428571,0 C38.7208136,0 40,1.27918643 40,2.85714286 L40,2.85714286 L40,8.57142857 C40,10.149385 38.7208136,11.4285714 37.1428571,11.4285714 L37.1428571,11.4285714 L2.85714286,11.4285714 C1.27918643,11.4285714 0,10.149385 0,8.57142857 L0,8.57142857 L0,2.85714286 C0,1.27918643 1.27918643,0 2.85714286,0 L2.85714286,0 Z M10,4.28571429 L5.71428571,4.28571429 C4.9253075,4.28571429 4.28571429,4.9253075 4.28571429,5.71428571 C4.28571429,6.50326393 4.9253075,7.14285714 5.71428571,7.14285714 L5.71428571,7.14285714 L10,7.14285714 C10.7889782,7.14285714 11.4285714,6.50326393 11.4285714,5.71428571 C11.4285714,4.9253075 10.7889782,4.28571429 10,4.28571429 L10,4.28571429 Z" id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
