<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd" opacity="0.800000012">
        <g id="图标" transform="translate(-1230.000000, -194.000000)">
            <g id="编组-2" transform="translate(1200.000000, 0.000000)">
                <g id="编组-17" transform="translate(0.000000, 60.000000)">
                    <g id="编组-9" transform="translate(14.000000, 120.000000)">
                        <g id="数据质量-数据标准（未选中）" transform="translate(16.000000, 14.000000)">
                            <rect id="矩形" fill-opacity="0" fill="currentColor" x="0" y="0" width="20" height="20"></rect>
                            <g id="编组-19" transform="translate(3.000000, 3.000000)" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.25">
                                <rect id="矩形" x="0" y="0" width="15" height="11.25" rx="1.25"></rect>
                                <line x1="7.5" y1="11.25" x2="7.5" y2="13.875" id="路径"></line>
                                <line x1="10.5" y1="3" x2="4.5" y2="8.25" id="路径"></line>
                                <line x1="2.25" y1="13.875" x2="12.75" y2="13.875" id="路径"></line>
                                <circle id="椭圆形" cx="4.125" cy="4.125" r="1.125"></circle>
                                <circle id="椭圆形" cx="10.875" cy="7.125" r="1.125"></circle>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>