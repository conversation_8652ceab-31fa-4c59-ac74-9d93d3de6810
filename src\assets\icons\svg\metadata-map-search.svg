<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="元数据地图（默认）" transform="translate(-568.000000, -90.000000)">
            <g id="搜索" transform="translate(556.000000, 78.000000)">
                <g id="编组" transform="translate(12.000000, 12.000000)">
                    <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="18" height="18"></rect>
                    <g id="默认" transform="translate(1.125000, 2.319516)" stroke="#FFFFFF" stroke-linejoin="round" stroke-width="1.125">
                        <path d="M6.616215,13.2323625 C10.27017,13.2323625 13.2324075,10.2702375 13.2324075,6.61620375 C13.2324075,2.9621925 10.27017,0 6.616215,0 C2.9621925,0 0,2.9621925 0,6.61620375 C0,10.2702375 2.9621925,13.2323625 6.616215,13.2323625 Z" id="路径"></path>
                        <path d="M8.81802,4.02543 C8.25462,3.4620075 7.47622125,3.1135275 6.6164175,3.1135275 C5.75667,3.1135275 4.9782825,3.4620075 4.41486,4.02543" id="路径" stroke-linecap="round"></path>
                        <line x1="11.3727825" y1="11.3725125" x2="14.67522" y2="14.6748375" id="路径" stroke-linecap="round"></line>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>