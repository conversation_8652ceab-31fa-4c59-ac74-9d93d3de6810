<template>
  <section class="container">
    <div class="container-out-box">
      <section class="container-left">
        <PublicLeftTree
          ref="pubicLeftTree"
          :data="treeData"
          :treeAttrData="treeAttrData"
          @treeUpdateNode="treeUpdateNode"
          @treeDelNode="treeDelNode"
          @treeCheckNode="treeCheckNode"
        />
      </section>
      <section class="container-box">
        <section class="tools">
          <section class="commonForm">
            <n-button
              v-if="buttonAuthList.includes('governanceManage_qualityManage_qualityRule_add')"
              code="governanceManage_qualityManage_qualityRule_add"
              variant="solid"
              @click.prevent="
                goJump('DataQualityRuleAdd', {
                  groupId: checkedItem.level === 0 ? checkedItem?.children[0]?.id : checkedItem.id,
                })
              "
            >
              新增规则
            </n-button>
            <n-form :inline="true" :data="searchForm" class="demo-form-inline">
              <n-form-item label="时间范围：">
                <n-range-date-picker-pro
                  v-model="searchForm.time"
                  :placeholder="['开始日期', '结束日期']"
                  format="YYYY-MM-DD"
                  allow-clear
                  @confirmEvent="onSearch(true)"
                />
              </n-form-item>
              <n-form-item label="">
                <n-input
                  v-model="searchForm.keyword"
                  placeholder="请输入规则名称搜素"
                  size="small"
                  clearable
                  @clear="onSearch(true)"
                >
                  <template #append>
                    <n-button @click.stop.prevent="onSearch(true)">
                      <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                        <SvgIcon class="icon_search" icon="icon_search" />
                      </n-popover>
                    </n-button>
                  </template>
                </n-input>
              </n-form-item>
            </n-form>
          </section>
        </section>
        <section class="table-box">
          <n-public-table
            ref="publicTable"
            :isDisplayAction="true"
            :needOtherActionBar="needOtherActionBar"
            :table-head-titles="tableHeadTitles"
            :pagination="pagination"
            :tableData="tableData"
            :tableHeight="tableHeight"
            :actionWidth="200"
            emptyText="暂无质量规则"
            @tablePageChange="tablePageChange"
          >
            <template #editor="{ editor }">
              <div class="edit-box">
                <n-button
                  v-if="buttonAuthList.includes('governanceManage_qualityManage_qualityRule_view')"
                  code="governanceManage_qualityManage_qualityRule_view"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.stop.prevent="
                    goJump('DataQualityRuleSee', { id: editor.row.id, groupId: editor.row.groupId })
                  "
                  >查看
                </n-button>
                <n-button
                  v-if="buttonAuthList.includes('governanceManage_qualityManage_qualityRule_edit')"
                  code="governanceManage_qualityManage_qualityRule_edit"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.stop.prevent="
                    goJump('DataQualityRuleEdit', {
                      id: editor.row.id,
                      isEdit: true,
                      groupId: editor.row.groupId,
                    })
                  "
                  >编辑
                </n-button>
                <n-button
                  v-if="
                    buttonAuthList.includes('governanceManage_qualityManage_qualityRule_delete')
                  "
                  code="governanceManage_qualityManage_qualityRule_delete"
                  class="seeDetails has-right-border"
                  variant="text"
                  @click.stop.prevent="delFn(editor.row)"
                  >删除
                </n-button>
              </div>
            </template>
          </n-public-table>
        </section>
      </section>
    </div>
  </section>
</template>

<script>
  import { mapState } from 'vuex'
  import { formartTime } from '@/utils/index'
  import ENUM from '@/const/enum'
  export default {
    data() {
      return {
        shortcuts: ENUM.SHORTCUTS,
        treeData: [],
        treeAttrData: {
          expand: false,
          showControl: false,
          showLeftIcon: true,
          parentControl: '',
          childControl: '23',
          dialogTitle: '分组',
          childTitle: '分类',
        },
        checkedItem: { id: '' }, // 选中树节点
        // 顶部搜索
        searchForm: {
          time: ['', ''],
          keyword: '',
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'num', name: '序号', width: '100px' },
          { prop: 'name', name: '规则名称', width: '200px' },
          { prop: 'context', name: '规则内容' },
          { prop: 'description', name: '描述信息' },
          { prop: 'updateTime', name: '最后修改时间', width: '200px' },
        ],
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        tableData: {},
        tableHeight: 246,
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
        buttonAuthList: (state) => state['user'].buttonAuthList,
      }),
    },
    mounted() {
      this.tableHeight = document.body.offsetHeight - 210
      this.getTreeListFn()
    },
    methods: {
      // 查询列表
      onSearch(init = false) {
        this.pagination.currentPage = init ? 1 : this.pagination.currentPage
        let startTime = ''
        let endTime = ''
        if (this.searchForm.time && this.searchForm.time[0]) {
          startTime = formartTime(this.searchForm.time[0])
          endTime = formartTime(this.searchForm.time[1], true)
        }
        let params = {
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          condition: {
            name: this.searchForm.keyword,
            groupId: this.checkedItem.level === 0 ? null : this.checkedItem.id,
          },
        }
        if (startTime) {
          params.condition.startTime = startTime
          params.condition.endTime = endTime
        }
        this.$api.dataQuality.getRuleList(params).then((res) => {
          if (res.code === 'SUCCESS') {
            // 新增序号属性
            res.data.list.map((item, index) => {
              return Object.assign(item, { num: index + 1 })
            })
            this.tableData = res.data
          }
        })
      },
      // 获取树列表
      getTreeListFn() {
        this.$api.dataQuality.getRuleTreeList({ type: 'RULE' }).then((res) => {
          if (res.code === 'SUCCESS') {
            let treeData = [
              {
                type: 'ROOT',
                id: '',
                pid: 0,
                level: 0,
                children: [],
                name: '全部',
                description: '',
              },
            ]
            if (res.data.id) {
              treeData[0] = res.data
              treeData[0].children = res.data.children || []
              treeData[0].type = 'ROOT'
              this.checkedItem = treeData[0]
              this.onSearch(true)
            } else {
              this.$refs.publicTable.initFailed()
            }
            this.treeData = treeData
          } else {
            this.$refs.publicTable.initFailed()
          }
        })
      },
      // 添加或修改节点
      treeUpdateNode(item) {
        let params = {
          description: item.ruleForm.desc,
          name: item.ruleForm.name,
          level: item.checkItem.level,
          type: 'RULE',
        }
        if (item.isEdit) {
          params.id = item.checkItem.id
          params.pid = item.checkItem.pid
          this.$api.dataQuality.updateRuleTree(params).then((res) => {
            if (res.code === 'SUCCESS') {
              this.getTreeListFn()
              this.$refs.pubicLeftTree.clearFn()
              this.$notify({
                title: '提示',
                message: '修改成功',
                type: 'success',
              })
            }
          })
        } else {
          params.pid = item.checkItem.id
          params.level = parseInt(item.checkItem.level) + 1
          this.$api.dataQuality.addRuleTree(params).then((res) => {
            if (res.code === 'SUCCESS') {
              this.getTreeListFn()
              this.$refs.pubicLeftTree.clearFn()
              this.$notify({
                title: '提示',
                message: '新增成功',
                type: 'success',
              })
            }
          })
        }
      },
      // 删除节点
      treeDelNode(item) {
        let params = {
          id: item.id,
        }
        this.$dialogPopup({
          title: '提示',
          message: '此操作将永久删除该目录, 是否继续？',
          save: () => {
            this.$api.dataQuality.delRuleTree(params).then((res) => {
              if (res.code === 'SUCCESS') {
                this.getTreeListFn()
                this.$notify({
                  title: '提示',
                  message: '删除成功',
                  type: 'success',
                })
              }
            })
          },
        })
      },
      // 选中节点
      treeCheckNode(item) {
        this.checkedItem = item.checkItem
        this.onSearch(true)
      },
      // 表格操作变化
      tablePageChange(data) {
        this.pagination.currentPage = data.currentPage
        this.pagination.pageSize = data.pageSize
        this.onSearch(false)
      },
      // 删除规则
      delFn(item) {
        let params = {
          id: item.id,
        }
        this.$dialogPopup({
          title: '提示',
          message: '此操作将永久删除该规则, 是否继续？',
          save: () => {
            this.$api.dataQuality.deleteRule(params).then((res) => {
              if (res.code === 'SUCCESS') {
                this.getTreeListFn()
                this.$notify({
                  title: '提示',
                  message: '删除成功',
                  type: 'success',
                })
              }
            })
          },
        })
      },
      goJump(name, query) {
        // if (!this.currentProject.projectCode) {
        //   this.$notify({
        //     title: '提示',
        //     message: '请联系管理员分配场景权限',
        //     type: 'warning',
        //   })
        //   return
        // }
        if (query) {
          this.$router.push({ name, query })
        } else {
          this.$router.push({ name })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    .container-out-box {
      height: 100%;
      width: 100%;
      display: flex;
      overflow: hidden;
      position: relative;
      background: inherit;
      border-radius: 4px;
    }

    &-left {
      background: #fff;
      box-sizing: border-box;
    }

    &-box {
      flex: 1;
      min-width: 0;
      box-sizing: border-box;
      background: inherit;
      position: relative;
      background-color: #fff;

      .tools {
        padding: 20px;
        background-color: #fff;

        .commonForm {
        }
      }

      .table-box {
        padding: 0 20px 0 20px;
      }
    }
  }
</style>
