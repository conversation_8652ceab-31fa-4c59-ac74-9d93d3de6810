<template>
  <div class="container overflow">
    <div class="cf-page-title">
      API编制
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>
    <div class="white-box nc-p-16" v-loading="state.loading">
      <n-form
        ref="formRef"
        :data="state.formData"
        :rules="state.rules"
        label-width="120px"
        label-align="start"
        message-type="text"
        labelSuffix="："
      >
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="API名称" field="apiName">
              <n-input
                v-model="state.formData.apiName"
                maxlength="50"
                placeholder="请输入API名称"
              />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item field="categoryId" label="分类">
              <n-tree-select
                v-model="state.formData.categoryId"
                :treeData="state.categoryTree"
                style="width: 100%"
                :allowClear="true"
                node-key="id"
                :prop="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求方式" field="requestMethod">
              <n-select
                size="sm"
                v-model="state.formData.requestMethod"
                placeholder="请选择请求方式"
              >
                <n-option
                  v-for="item in state.requestMethodOps"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="数据表" field="modelTableId">
              <div class="nc-flex" style="width: 100%">
                <n-select
                  size="sm"
                  style="width: 200px"
                  v-model="state.formData.datasourceId"
                  placeholder="请选择数据源"
                  @value-change="getTables"
                >
                  <n-option
                    v-for="item in state.dataSourceOps"
                    :key="item.id"
                    :name="item.name"
                    :value="item.id"
                  />
                </n-select>
                <n-select
                  placeholder="请选择数据数据表"
                  style="width: calc(100% - 210px); margin-left: 10px"
                  size="sm"
                  :allow-clear="true"
                  filter
                  v-model="state.formData.modelTableId"
                  @value-change="getParamsUrl"
                  :loading="state.tableLoading"
                >
                  <n-option
                    v-for="item in state.systemList"
                    :key="item.modelTableId"
                    :value="item.modelTableId"
                    :name="item.systemName"
                    :securityLevel="item.securityLevel"
                  />
                </n-select>
              </div>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="鉴权方式" field="authKind">
              <n-checkbox-group
                v-model="state.formData.authKind"
                direction="row"
                @change="onAuthKindChange"
                class="nc-flex"
                style="align-items: center"
              >
                <n-checkbox label="TOKEN" value="1" />
                <n-checkbox label="白名单" value="2" disabled style="margin-right: 4px" />
                <SvgIcon
                  style="color: #1e89ff"
                  icon="icon-edit3"
                  v-if="state.formData.authKind?.includes('2')"
                  @click="state.whiteVisiable = true"
                />
              </n-checkbox-group>
            </n-form-item>
          </n-col>
          <n-col :span="12" v-if="state.formData.authKind?.includes('1')">
            <n-form-item label="token到期时间" field="expireDate">
              <n-date-picker-pro
                style="width: 100%"
                v-model="state.formData.expireDate"
                format="YYYY-MM-DD"
                @confirmEvent="startDateChange"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="接口地址" field="url">
              <n-input v-model="url" disabled />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="密级" field="confidentialityLevel">
              <n-select
                v-model="state.formData.confidentialityLevel"
                placeholder="选择数据表后自动获取"
                filter
                allow-clear
                disabled
                :options="state.securityLevelOpt"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item class="inline" label="请求参数" field="requestParamList">
              <div class="border-box">
                <n-search
                  v-model="state.leftKeyword"
                  size="sm"
                  clearable
                  style="width: 200px; float: right; margin-bottom: 10px"
                  @clear="onSearchLeft"
                  @search="onSearchLeft"
                />
                <n-public-table
                  ref="publicTable"
                  :tableData="state.leftTable"
                  :configData="state.leftConfigData"
                  :isDisplayAction="false"
                  :isNeedSelection="true"
                  :editDisabled="false"
                  rowKey="fieldId"
                  :table-head-titles="leftTableTitles"
                  :showPagination="false"
                  :tableHeight="320"
                  @handle-selection-change="reqSelectChange"
                >
                  <template #required="{ editor }">
                    <n-switch v-model="editor.row.required">
                      <template #checkedContent>是</template>
                      <template #uncheckedContent>否</template>
                    </n-switch>
                  </template>
                </n-public-table>
              </div>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="返回参数" field="responseParamList">
              <div class="border-box">
                <n-search
                  v-model="state.rightKeyword"
                  size="sm"
                  clearable
                  style="width: 200px; float: right; margin-bottom: 10px"
                  @clear="onSearchRight"
                  @search="onSearchRight"
                />
                <n-public-table
                  :isDisplayAction="false"
                  :isNeedSelection="true"
                  :editDisabled="false"
                  :tableData="state.rightTable"
                  :configData="state.rightConfigData"
                  :table-head-titles="rightTableTitles"
                  :showPagination="false"
                  :tableHeight="320"
                  rowKey="fieldId"
                  @handle-selection-change="resSelectChange"
                />
              </div>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求间隔" field="callInterval">
              <n-input v-model="state.formData.callInterval" placeholder="请输入">
                <template #append>秒</template>
              </n-input>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="请求超时" field="timeout">
              <n-input v-model="state.formData.timeout" placeholder="请输入">
                <template #append>秒</template>
              </n-input>
            </n-form-item></n-col
          >
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="数据过滤条件" field="whereCondition">
              <n-input
                v-model="state.formData.whereCondition"
                maxlength="1000"
                placeholder="定义数据范围，如：department='研发部'"
              />
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>
    <div class="nc-m-t-10 nc-p-16" style="background-color: #fff">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack">取消</n-button>
        <n-button variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
    <whiteList
      v-model="state.whiteVisiable"
      :ips="state.formData.ipWhiteList"
      @confirm="confirmWhiteList"
    />
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import whiteList from './whiteList.vue'
  import moment from 'moment'
  import api from '@/api/index'
  import { getCurrentInstance, reactive } from 'vue'
  import { confidentialityLevelOptions } from '../assetsRegister/components/utlis.js'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const state = reactive({
    // 密级
    securityLevelOpt: confidentialityLevelOptions,
    loading: false,
    tableLoading: false,
    formData: {
      apiName: '',
      categoryId: null,
      requestMethod: 'GET',
      datasourceId: '',
      modelTableId: null,
      modelTable: '',
      whereCondition: '',
      authKind: ['2'],
      expireDate: '',
      responseParamList: [],
      requestParamList: [],
      callInterval: '1',
      timeout: '3',
      ipWhiteList: [],
      pathCode: '',
      token: '',
      confidentialityLevel: '',
    },
    prefix: '',
    rules: {
      apiName: [{ required: true, message: '请输入API名称', trigger: 'blur' }],
      categoryId: [{ required: true, message: '请选择分类', trigger: 'change', type: 'number' }],
      requestMethod: [{ required: true, message: '请选择请求方式', trigger: 'change' }],
      expireDate: [{ required: true, message: '请选择过期时间', trigger: 'change' }],
      modelTableId: [{ required: true, message: '请选择数据表', trigger: 'change' }],
      responseParamList: [
        { required: true, message: '请选择 返回参数', trigger: 'blur', type: 'array' },
      ],
      requestParamList: [
        { required: true, message: '请选择 请求参数', trigger: 'blur', type: 'array' },
      ],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
    },
    categoryTree: [],
    leftKeyword: '',
    leftTable: {},
    leftConfigData: {},
    rightKeyword: '',
    rightTable: {},
    rightConfigData: {},
    leftTableData: [],
    rightTableData: [],
    whiteVisiable: false,
    requestMethodOps: [
      {
        label: 'GET',
        value: 'GET',
      },
      {
        label: 'POST',
        value: 'POST',
      },
    ],
    dataSourceOps: [],
  })
  const url = computed(
    () =>
      state.prefix +
      state.formData.pathCode +
      (state.formData.token ? `?token=${state.formData.token}` : ''),
  )
  const leftTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名' },
    { prop: 'required', name: '是否必填', slot: 'required' },
  ]
  const rightTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名' },
  ]
  function confirmWhiteList(data) {
    state.formData.ipWhiteList = data
  }
  // token到期时间change
  const startDateChange = (e) => {
    state.formData.expireDate = formartTime(e)
  }
  const formartTime = (time, Symbol = '-') => {
    var year = time.getFullYear()
    var mon = time.getMonth() + 1
    var day = time.getDate()
    var submitTime = ''
    submitTime += year + Symbol
    if (mon >= 10) {
      submitTime += mon + Symbol
    } else {
      submitTime += '0' + mon + Symbol
    }
    if (day >= 10) {
      submitTime += day
    } else {
      submitTime += '0' + day
    }
    return submitTime
  }
  // 保存
  const formRef = ref(null)
  const publicTable = ref(null)
  function onConfirm() {
    formRef.value.validate((f) => {
      if (f) {
        state.formData.requestParamList = publicTable.value.getCheckedRows()
        const params = {
          ...state.formData,
          expireDate: state.formData.expireDate
            ? moment(state.formData.expireDate).format('YYYY-MM-DD')
            : null,
          callInterval: Number(state.formData.callInterval),
          timeout: Number(state.formData.timeout),
          url:
            state.formData.pathCode +
            (state.formData.token ? `?token=${state.formData.token}` : ''),
          modelTableId: state.formData.datasourceId ? 0 : state.formData.modelTableId,
          datasourceType: state.formData.datasourceId ? 'oracle' : 'doris',
        }
        const apiUrl = state.formData.id ? 'apiEdit' : 'apiAdd'
        api.documentManage[apiUrl](params).then((res) => {
          if (res.success) {
            const hint = !state.formData.id ? '新建' : '编辑'
            proxy.$message.success(hint + '成功!')
            router.push({ name: 'apiManage', query: { refresh: true } })
          }
        })
      }
    })
  }
  // 查询请求参数表格
  function onSearchLeft() {
    if (state.leftKeyword) {
      let data = state.leftTableData.filter(
        (item) =>
          item.column.includes(state.leftKeyword) || item.columnName.includes(state.leftKeyword),
      )
      state.leftTable = { list: data }
    } else {
      state.leftTable = { list: state.leftTableData }
    }
  }
  // 查询返回参数表格
  function onSearchRight() {
    if (state.rightKeyword) {
      let data = state.rightTableData.filter(
        (item) =>
          item.column.includes(state.rightKeyword) || item.columnName.includes(state.rightKeyword),
      )
      state.rightTable = { list: data }
    } else {
      state.rightTable = { list: state.rightTableData }
    }
  }
  // 获取路径和参数
  async function getParamsUrl(data, kind = 'all') {
    if (!state.formData.modelTableId) {
      return
    }
    if (data?.name) {
      state.formData.modelTable = data.name?.split('(')[0]
    }
    //根据数据表设置密级
    let activeItem = state.systemList.filter(
      (item) => item.modelTableId === state.formData.modelTableId,
    )[0]
    state.formData.confidentialityLevel = activeItem.securityLevel
    let res = null
    if (state.formData.datasourceId) {
      res = await await api.documentManage.getTablesStructure({
        datasourceId: state.formData.datasourceId,
        datasourceName: state.formData.modelTableId,
        kind: kind,
      })
    } else {
      res = await api.documentManage.apiParamsList({
        modelTableId: state.formData.modelTableId,
        kind: kind,
      })
    }
    const { list, pathCode } = res.data
    state.rightTableData = list.map((l) => {
      delete l.dataLength
      return l
    })
    state.leftTableData = list.map((l) => {
      const cur = state.formData.requestParamList.find((r) => r.fieldId === l.fieldId)
      delete l.dataLength
      return {
        ...l,
        required: cur?.required || false,
      }
    })
    state.leftTable = {
      list: state.leftTableData,
    }
    state.rightTable = {
      list: list,
    }
    // 切换数据表时，返回参数默认全选
    if (data) {
      state.rightConfigData = {
        selectRow: list,
      }
      state.formData.responseParamList = list
    }
    if (kind === 'all') {
      state.formData.pathCode = pathCode
    }
  }
  // 请求token
  function onAuthKindChange() {
    if (!state.formData.pathCode) {
      proxy.$message.warning('请先选择数据表！')
      state.formData.authKind = state.formData.authKind.filter((item) => item !== '1')
    }
    if (state.formData.authKind.includes('1')) {
      api.documentManage.apiToken({ url: state.formData.pathCode }).then((res) => {
        state.formData.token = res.data
      })
    } else {
      state.formData.token = ''
    }
  }
  // 请求参数变化
  function reqSelectChange(val) {
    state.formData.requestParamList = val
    formRef.value.validateFields(['requestParamList'])
  }
  // 返回参数变化
  function resSelectChange(val) {
    state.formData.responseParamList = val
    formRef.value.validateFields(['responseParamList'])
  }
  // 详情
  async function getDetail(id) {
    try {
      const res = await api.documentManage.apiDetail({ apiId: id })
      state.formData = {
        ...res.data,
        pathCode: res.data.url?.split('?token=')[0] || '',
      }
      await getTables()
      state.leftConfigData = {
        selectRow: state.formData.requestParamList,
      }
      state.rightConfigData = {
        selectRow: state.formData.responseParamList,
      }
      // 如果
      if (!state.systemList.find((item) => item.modelTableId === state.formData.modelTableId)) {
        const resItem = await api.documentManage.apiModelTables({
          tableName: state.formData.modelTable,
        })
        state.systemList = state.systemList.concat(
          resItem.data.map((item) => {
            return {
              ...item,
              modelTableId: String(item.modelTableId),
              systemName: `${item.name}(${item.cnName || ''})`,
            }
          }),
        )
      }
      state.formData.modelTableId = res.data.datasourceId
        ? res.data.modelTable
        : String(res.data.modelTableId)
      await getParamsUrl(null, 'param')
    } finally {
    }
  }
  // 请求分类
  async function getClassifyTreeList() {
    const res = await api.documentManage.apiCategoryTree()
    state.categoryTree = res.data
  }
  async function getDatasource() {
    const res = await api.documentManage.getDatasource({
      datasourceType: 'ORACLE',
    })
    state.dataSourceOps = res.data
  }
  // 请求表下拉
  async function getTables() {
    try {
      state.tableLoading = true
      state.leftTable = []
      state.rightTable = []
      state.formData.modelTableId = ''
      if (state.formData.datasourceId) {
        const res = await api.documentManage.getTablesV2({
          datasourceId: state.formData.datasourceId,
        })
        const securityLevel = state.dataSourceOps.find(
          (item) => item.id === state.formData.datasourceId,
        )?.confidentialityLevel
        if (res.data?.length !== state.systemList?.length) {
          state.systemList = res.data.map((item) => {
            return {
              modelTableId: item.name,
              systemName: `${item.name}(${item.comment || ''})`,
              securityLevel: securityLevel,
            }
          })
        }
      } else {
        const res = await api.documentManage.apiModelTables({ tableName: '' })
        if (res.data?.length !== state.systemList?.length) {
          state.systemList = res.data.map((item) => {
            return {
              ...item,
              modelTableId: String(item.modelTableId),
              systemName: `${item.name}(${item.cnName || ''})`,
            }
          })
        }
      }
    } finally {
      state.tableLoading = false
    }
  }
  // 请求路径前缀
  async function getPrefix() {
    const res = await api.documentManage.apiPrefix()
    state.prefix = res.data
  }
  // 返回
  function goBack() {
    router.go(-1)
  }
  onMounted(async () => {
    try {
      state.loading = true
      const { id, categoryId } = router.currentRoute.value.query
      await getClassifyTreeList()
      await getPrefix()
      await getDatasource()

      state.formData.categoryId = categoryId ? Number(categoryId) : null
      if (id) {
        await getDetail(id)
      }
    } finally {
      state.loading = false
    }
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  :deep(.nancalui-checkbox__group > *) {
    margin-top: 0;
  }
  .border-box {
    padding: 10px;
    border: 1px solid #e5e6eb;
  }
  :deep(.nancalui-input-slot__append) {
    background: #fafafa;
  }
</style>
