<template>
  <div :class="['api-management-add publicFormCss container', state.isLzos ? 'isLzos' : '']">
    <div class="api-management-add-box">
      <div class="page_header_common_style">
        <p v-if="state.type === 'ADD'" class="need_smallcube__title">新增API</p>
        <p v-if="state.type === 'EDIT'" class="need_smallcube__title">编辑</p>
      </div>
      <div
        :class="{
          'scroll-bar-style': true,
          'api-management-add-box-content': true,
          'only-detail': state.type === 'DETAIL',
        }"
        v-loading="state.loading"
      >
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="100px"
          label-align="end"
          :pop-position="['right']"
          class="demo-ruleForm"
          :key="state.key"
        >
          <n-form-item label="API名称：" field="apiName">
            <n-input
              v-model="state.ruleForm.apiName"
              maxlength="30"
              placeholder="请输入API名称"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item label="参数协议：" field="protocol">
            <n-select
              v-model="state.ruleForm.protocol"
              placeholder="请选择参数协议"
              :disabled="state.disabled"
              filter
              @value-change="
                () => {
                  state.key++
                }
              "
            >
              <n-option
                v-for="item in state.org_options"
                :key="item.value"
                :name="item.label"
                :value="item.value"
              />
            </n-select>
          </n-form-item>
          <n-form-item label="请求方式：" field="requestMethod">
            <n-select
              v-model="state.ruleForm.requestMethod"
              placeholder="请选择请求方式"
              :disabled="state.disabled"
              filter
              @value-change="
                () => {
                  state.key++
                }
              "
            >
              <n-option
                v-for="item in state.org_dataSourceType"
                :key="item.value"
                :name="item.label"
                :value="item.value"
              />
            </n-select>
          </n-form-item>

          <div class="inlineBlock">
            <n-form-item label="请求Path：" field="serverUrl">
              <n-input v-model="state.serverUrl" placeholder="" disabled />
            </n-form-item>
            <div class="ipPort">
              <n-form-item label="" field="apiUrl">
                <n-input
                  ref="pathInput"
                  v-model="state.ruleForm.apiUrl"
                  placeholder="自定义请求Path"
                  :disabled="state.disabled"
                />
              </n-form-item>
              <div class="verify-only"
                ><n-button color="primary" @click.prevent="checkPath">验证唯一性</n-button></div
              >
            </div>
          </div>
          <n-form-item label="描述信息：" field="description">
            <n-textarea
              v-model="state.ruleForm.description"
              show-count
              maxlength="200"
              :autosize="{ minRows: 3 }"
              :disabled="state.disabled"
            />
          </n-form-item>

          <n-form-item label="请求参数：" required="">
            <n-button
              v-show="!router.currentRoute.value.query.algorithmId"
              color="primary"
              variant="solid"
              @click.prevent="changeParams"
              >{{ state.ruleForm.paramColumns.length ? '编辑参数' : '新增参数' }}
            </n-button>
          </n-form-item>
          <div v-show="state.isHave" class="hasParams">
            <n-public-table
              :isDisplayAction="false"
              :table-head-titles="state.tableHeadTitles"
              :showPagination="false"
              :tableHeight="'auto'"
              :tableData="state.tableData"
              :maxHeight="240"
            />
          </div>
          <n-form-item label="鉴权机制：" field="authenticationMode">
            <n-checkbox-group v-model="state.ruleForm.authenticationMode" direction="row">
              <n-checkbox label="IP白名单鉴权" value="IP_WHITELIST" />
              <n-checkbox label="token鉴权" value="TOKEN" />
            </n-checkbox-group>
          </n-form-item>
          <n-form-item
            v-show="!router.currentRoute.value.query.algorithmId"
            class="filter-form-item"
            label="数据过滤："
          >
            <div>
              <div class="top-title-box">
                <n-radio-group direction="row" v-model="state.ruleForm.filterMode">
                  <n-radio value="ALL">满足以下全部条件的数据</n-radio>
                  <n-radio value="OR">满足以下任意条件的数据</n-radio>
                </n-radio-group>
                <div class="add-filter-box" @click="addFilterCondition">
                  <SvgIcon class="icon-add-svg" icon="icon_list_add_n" title="添加"
                /></div>
              </div>
              <div class="filter-condition-box">
                <div
                  class="filter-condition-list"
                  v-for="(item, index) in state.ruleForm.filterCondition"
                  :key="item"
                >
                  <n-form-item
                    label=""
                    :field="'filterCondition.' + index + '.colName'"
                    :rules="{
                      required: true,
                      message: '请选择参数',
                      trigger: 'change',
                    }"
                  >
                    <n-select
                      v-model="item.colName"
                      placeholder="请选择参数"
                      filter
                      @value-change="
                        () => {
                          state.key++
                        }
                      "
                    >
                      <n-option
                        v-for="option in state.ruleForm.paramColumns"
                        :key="option.id"
                        :name="option.name"
                        :value="option.name"
                      />
                    </n-select>
                  </n-form-item>
                  <n-form-item
                    label=""
                    :field="'filterCondition.' + index + '.operator'"
                    :rules="{
                      required: true,
                      message: '请选择过滤条件',
                      trigger: 'change',
                    }"
                  >
                    <n-select
                      v-model="item.operator"
                      placeholder="请选择过滤条件"
                      filter
                      @value-change="
                        () => {
                          state.key++
                        }
                      "
                    >
                      <n-option
                        v-for="option in state.filterOperator"
                        :key="option.value"
                        :name="option.label"
                        :value="option.value"
                      />
                    </n-select>
                  </n-form-item>
                  <n-form-item
                    label=""
                    :field="'filterCondition.' + index + '.value'"
                    :rules="{
                      required: true,
                      message: '请输入参数值',
                      trigger: 'blur',
                    }"
                  >
                    <n-input
                      v-model="item.value"
                      size="small"
                      maxlength="30"
                      placeholder="请输入参数值"
                    />
                  </n-form-item>
                  <div class="userHandle">
                    <!-- <div
                  class="handle-box"
                  @click="addFilterCondition"
                  v-show="index === state.ruleForm.filterCondition.length - 1"
                  ><SvgIcon icon="filter-add" class="add"
                /></div> -->
                    <div class="handle-box" @click="removeFilterCondition(index)">
                      <SvgIcon icon="filter-cut" class="reduce" title="删除"
                    /></div>
                  </div>
                </div>
              </div>
            </div>
          </n-form-item>
        </n-form>
      </div>
    </div>
    <div class="options-box-bg">
      <div class="options-box-content">
        <n-button
          color="primary"
          size="sm"
          variant="solid"
          :loading="state.saveLoading"
          @click.prevent="submitForm"
          >保存</n-button
        >
        <n-button size="sm" @click.prevent="goBack">取消</n-button>
      </div>
    </div>
    <!-- 新增参数 -->
    <addParameters ref="addParameters" @getSelectData="getSelectData" />
  </div>
</template>
<script>
  import addParameters from './components/add-parameters'
  import { ref, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { checkCName, checkCRelativePath } from '@/utils/validate'
  export default {
    name: '',
    components: { addParameters },
    props: {},
    setup() {
      const router = useRouter()

      const pathInput = ref()
      const addParameters = ref()
      const ruleForm = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        key: 1,
        tableData: {},
        loading: false,
        saveLoading: false,
        testLoading: false,
        passwordType: 'password',
        disabled: false, // 表单不可用
        type: 'ADD', // 页面展示形式 ADD EDIT  DETAIL
        ruleForm: {
          assetsType: '',
          modelId: '',
          bizDomainId: '',
          apiName: '',
          protocol: '',
          requestMethod: '',
          apiUrl: '',
          description: '',
          paramColumns: [], // 自定义参数
          filterMode: 'ALL', //全部满足/满足一个
          filterCondition: [], //过滤条件
          authenticationMode: ['IP_WHITELIST'],
        },
        serverUrl: 'https://{IP}/{PlatformName}/{ProjectName}',
        rules: {
          apiName: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'dataService', 'validApi', {
                  apiName: state.ruleForm.apiName,
                  id: state.ruleForm?.id || null,
                }),
              trigger: 'blur',
            },
          ],
          protocol: [{ required: true, message: '参数协议', trigger: 'change' }],
          requestMethod: [{ required: true, message: '请求方式', trigger: 'change' }],
          apiUrl: [{ required: true, validator: checkCRelativePath, trigger: 'blur' }],
          authenticationMode: [
            { type: 'array', required: true, message: '请选择鉴权机制方式', trigger: 'change' },
          ],
          // 端口号非正整数：端口号为0到65535的整数。
        },
        org_options: [],
        org_dataSourceType: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'name', name: '字段名称' },
          { prop: 'fieldTypeName', name: '类型' },
          { prop: 'fieldLength', name: '长度' },
          { prop: 'description', name: '描述' },
        ],
        isHave: false, //判断是否有参数信息
        filterOperator: [],
      })
      const methods = {
        //获取Api数据过滤算符枚举
        getFilterOperator() {
          api.dataService.getFilterOperator().then((res) => {
            let { success, data } = res
            if (success) {
              state.filterOperator = data
            }
          })
        },
        // 获取协议列表
        getProtocolList() {
          api.dataService.getProtocolList().then((res) => {
            let { success, data } = res
            if (success) {
              state.org_options = data
            }
          })
        },
        // 获取请求方式列表
        getMethodList() {
          api.dataService.getMethodList().then((res) => {
            let { success, data } = res
            if (success) {
              state.org_dataSourceType = data
            }
          })
        },
        // 获取API详情
        getDetail(id) {
          state.loading = true
          api.dataService
            .getApiDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              let _data = {}
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data[key]
                })
                state.ruleForm.id = data.id
                state.ruleForm.modelId = data.modelId
                state.ruleForm.bizDomainId = data.bizDomainId
                state.ruleForm.assetsType = data.assetsType
                if (data.paramColumns.length > 0) {
                  data.paramColumns.map((item, index) => {
                    return Object.assign(item, { number: index + 1 })
                  })
                  state.isHave = true
                } else {
                  state.isHave = false
                }
                _data = {
                  list: data.paramColumns,
                }
              }
              state.tableData = _data
            })
            .catch(() => {
              state.loading = false
              state.tableData = {}
            })
        },
        // Api调用源地址配置的读取
        getServerUrl() {
          api.dataService.getServerUrl().then((res) => {
            let { success, data } = res
            if (success) {
              state.serverUrl = data
            }
          })
        },

        // 正则验证路径合法性
        regexCheckPath(url) {
          let regex = /^([a-zA-Z\/]){2,200}$/
          let res = regex.test(url)
          let passed = false
          if (res) {
            passed = false
            let a = url.split('/')
            a.forEach((item, index) => {
              if (item === '' && index === 0) {
                // /开头
                passed = true
              } else {
                if (item === '') {
                  passed = false
                }
              }
            })
          }
          return passed
        },
        // 检验路径唯一性
        checkPath() {
          let { apiUrl } = state.ruleForm
          let result = methods.regexCheckPath(apiUrl)
          let _data = { apiUrl }
          if (result) {
            if (state.queryData.id) {
              _data.id = state.queryData.id
            }
            api.dataService.onlyApiPath(_data).then((res) => {
              let { success, data } = res
              if (success) {
                if (data) {
                  ElNotification({
                    title: '提示',
                    message: '路径验证通过',
                    type: 'success',
                  })
                } else {
                  ElNotification({
                    title: '提示',
                    message: '路径验证未通过',
                    type: 'error',
                  })
                }
              }
            })
          } else {
            ElNotification({
              title: '提示',
              message: '路径不符合规范',
              type: 'error',
            })
            pathInput.value.focus()
          }
        },
        // 新增/改变参数
        changeParams() {
          addParameters.value.init({
            selectRow: state.ruleForm.paramColumns,
            modelId: state.ruleForm.modelId,
            bizDomainId: state.ruleForm.bizDomainId,
            assetsType: state.ruleForm.assetsType,
          })
        },

        // 获取勾选数据
        getSelectData(item) {
          let { modelId, data, bizDomainId, assetsType } = item
          state.ruleForm.modelId = modelId
          state.ruleForm.bizDomainId = bizDomainId
          state.ruleForm.assetsType = assetsType
          let _paramColumns = []
          data.forEach((item, index) => {
            let { id, name } = item
            item.number = index + 1
            _paramColumns.push({
              id,
              name,
            })
          })

          state.ruleForm.paramColumns = _paramColumns
          if (data.length > 0) {
            state.isHave = true
          } else {
            state.isHave = false
          }
          let _data = {
            list: data,
          }

          state.tableData = _data

          //重置过滤条件
          if (state.ruleForm.filterCondition.length) {
            state.ruleForm.filterCondition.forEach((item) => {
              Object.keys(item).forEach((key) => {
                item[key] = null
              })
            })
          }
        },

        // 请求方式
        getDataSourceType() {
          api.project.getDataSourceType().then((res) => {
            if (res) {
              let options = []
              res.data.forEach((item) => {
                options.push({
                  value: item.code,
                  label: item.name,
                })
              })
              state.org_dataSourceType = options
            }
          })
        },
        // 保存api
        submitForm() {
          ruleForm.value.validate((valid) => {
            if (valid) {
              if (state.ruleForm.paramColumns?.length || state.queryData.algorithmId) {
                state.saveLoading = true
                let interface_url = 'addApi'
                let _message = '新增成功'
                if (state.type === 'EDIT') {
                  interface_url = 'updateApi' // 更新
                  state.ruleForm.id = state.queryData.id
                  _message = '更新成功'
                } else {
                  _message = '新增成功'
                  interface_url = 'addApi' // 新增
                }
                api.dataService[interface_url](state.ruleForm)
                  .then((res) => {
                    let { success } = res
                    if (success) {
                      ElNotification({
                        title: '提示',
                        message: _message,
                        type: 'success',
                      })
                      router.push({ name: 'apiList' })
                    }

                    state.saveLoading = false
                  })
                  .catch(() => {
                    state.saveLoading = false
                  })
              } else {
                ElNotification({
                  title: '提示',
                  message: '请勾选请求参数',
                  type: 'warning',
                })
              }
            } else {
              return false
            }
          })
        },
        // 测试API
        testDatasource() {
          state.testLoading = true
          api.project
            .testDatasource(state.ruleForm)
            .then((res) => {
              let { success, msg } = res.data
              state.testLoading = false
              if (success)
                ElNotification({
                  title: '提示',
                  message: '测试通过!',
                  type: 'success',
                })
              else {
                ElNotification({
                  title: '提示',
                  message: '测试未通过! ---' + msg,
                  type: 'error',
                })
              }
            })
            .catch(() => {
              state.testLoading = false
            })
        },
        // 返回
        goBack() {
          router.go(-1)
        },
        //新增授权ip
        addFilterCondition() {
          state.ruleForm.filterCondition.push({
            colName: '',
            operator: '',
            value: '',
          })
        },
        //移除授权ip
        removeFilterCondition(index) {
          state.ruleForm.filterCondition.splice(index, 1)
        },
        //算法详情
        getAlgorithmDetail(id) {
          api.dataDev.algorithmDetail({ id }).then((res) => {
            let { success, data } = res
            if (success) {
              state.tableData.list = []
              state.ruleForm.paramColumns = []
              data.parameterList?.forEach((item, index) => {
                if (item.parameterType === 'INPUT') {
                  state.ruleForm.paramColumns.push({
                    id: item.id,
                    name: item.parameterName,
                  })
                  state.tableData.list.push({
                    number: index + 1,
                    name: item.parameterName,
                    fieldType: 'STRING',
                    fieldTypeName: '字符串',
                    fieldLength: null,
                    description: null,
                  })
                }
              })
            }
          })
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.type = state.queryData.type ? state.queryData.type : 'ADD'
        switch (state.type) {
          case 'EDIT':
            methods.getDetail(state.queryData.id)
            break
          default:
            break
        }
        if (state.queryData?.algorithmId) {
          //算法发布为api时
          state.isHave = true
          state.ruleForm.modelId = state.queryData.algorithmId
          state.ruleForm.assetsType = 'ALGORITHM'
          methods.getAlgorithmDetail(state.queryData.algorithmId)
        }
        methods.getProtocolList()
        methods.getMethodList()
        methods.getServerUrl()
        methods.getFilterOperator()
      })
      return {
        state,
        router,
        pathInput,
        addParameters,
        ruleForm,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 100px;
  .api-management-add {
    position: relative;
    color: #333;
    &.isLzos {
      padding: 0;
    }
    :deep(.nancalui-radio-group) {
      .nancalui-radio__label {
        color: #333;
        font-weight: 400;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
    .options-box-bg {
      position: absolute;
      right: -10px;
      bottom: 0;
      left: -10px;
      height: 60px;
      padding: 0 10px;

      .options-box-content {
        display: inline-block;
        width: 100%;
        height: 100%;
        padding: 17px 30px;
        text-align: center;
        background-color: #fff;
        border-radius: 8px 8px 0px 0px;
      }
    }

    .api-management-add-box {
      position: relative;
      width: 100%;
      height: calc(100% - 60px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
      .box-content-description {
        width: calc(100% - 4px);
        height: 38px;
        margin: 2px auto;
        padding: 13px 0;
        background: #f7f8fa;
        p {
          margin: 0 0 0 30px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          font-size: 14px;
        }
      }
      .content-bg-img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 17.5%;
        min-width: 252px;
      }
      .api-management-add-box-content {
        width: 100%;
        min-width: 720px;
        max-height: calc(100% - 36px);
        margin: 0 auto;
        padding-top: 14px;
        padding-bottom: 40px;
        overflow-y: auto;
        .inlineBlock {
          position: relative;
          display: flex;
          width: 100%;
          margin-bottom: 20px;
          :deep(.nancalui-input) {
            width: 100%;
          }
          .nancalui-form__item--horizontal {
            width: 390px !important;
          }

          .ipPort {
            position: absolute !important;
            top: 0;
            right: 0;
            width: 180px !important;
            width: 300px !important;
            margin-left: 10px;
            .nancalui-form__item--horizontal {
              width: 100% !important;
            }
            .nancalui-input__wrapper {
              width: 300px !important;
            }
            .nancalui-input__inner {
              width: 300px !important;
            }
            .verify-only {
              position: absolute;
              top: 35px;
              right: 0;
              line-height: 22px;
              text-align: right;

              .nancalui-button--outline--primary {
                height: 22px;
                padding: 0 9px;
                font-size: 12px;
                line-height: 20px;
                border: none;
                border-radius: 2px;
                &:hover {
                  color: $themeBlue;
                  background: #ecf3ff;
                }
              }
            }
          }
        }
        .nancalui-form {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 700px;
          margin: 0 auto;
          .hasParams {
            width: calc(100% - 100px);
            margin-bottom: 20px;
            margin-left: 100px;
          }
          .nancalui-checkbox__group > * {
            margin-top: 0 !important;
          }
          .nancalui-form__item--horizontal {
            width: 100%;

            &.filter-form-item {
              .top-title-box {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 32px;
                padding-right: 65px;
                .add-filter-box {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  width: 38px;
                  height: 26px;
                  margin-left: 65px;
                  color: $themeBlue;
                  font-size: 20px;
                  line-height: 26px;
                  text-align: center;
                  border: 1px solid $themeBlue;
                  border-radius: 13px;
                  cursor: pointer;
                  .icon-add-svg {
                    margin-right: 0;
                    color: $themeBlue;
                  }
                }
              }
              .filter-condition-box {
                max-height: 500px;
                margin-top: 10px;
                overflow-y: auto;
              }

              .filter-condition-list {
                display: flex;
                align-content: center;
                margin-bottom: 20px;
                font-size: 12px;
                .nancalui-form__item--horizontal {
                  width: calc((100% - 154px) / 3);
                  margin-right: 20px;
                  margin-bottom: 0;
                }
                .userHandle {
                  display: flex;
                  align-items: center;
                  width: 94px;
                  font-size: 22px;
                  text-align: center;

                  .handle-box {
                    margin-right: 18px;
                    cursor: pointer;
                  }
                }
              }
            }
          }
        }
      }
    }

    .operate-box {
      position: fixed;
      right: 10px;
      bottom: 0;
      width: calc(100% - 220px);
      height: 66px;
      padding: 17px 30px;
      text-align: right;
      background-color: #fff;
      border-radius: 4px 4px 0px 0px;
    }
  }
</style>
