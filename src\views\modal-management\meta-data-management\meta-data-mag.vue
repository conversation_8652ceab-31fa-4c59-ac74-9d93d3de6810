<template>
  <div class="container">
    <div class="container-box">
      <div class="container-right">
        <div class="commonForm">
          <n-button
            v-if="buttonAuthList?.includes('governanceManage_modal_metaData_addMetaData')"
            code="governanceManage_modal_metaData_addMetaData"
            size="sm"
            variant="solid"
            color="primary"
            @click.stop.prevent="onCreate"
          >
            <SvgIcon icon="icon_list_add_n" class="icon-add-svg" title="新增元数据" />
            新增元数据
          </n-button>
          <n-button
            v-if="buttonAuthList?.includes('governanceManage_modal_metaData_uploadMeta')"
            code="governanceManage_modal_metaData_uploadMeta"
            size="sm"
            @click.stop.prevent="onUpload"
            >导入</n-button
          >
          <el-form inline :model="formInline">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="formInline.timeData"
                type="daterange"
                align="right"
                size="small"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                :shortcuts="shortcuts"
                @change="onSearch"
              />
            </el-form-item>
            <el-form-item label="" prop="keyword">
              <el-input
                v-model="formInline.keyword"
                size="small"
                placeholder="中/英文名称搜索"
                clearable
                @change="onSearch"
              >
                <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
                <template #append>
                  <n-button @click.stop.prevent="onSearch">
                    <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                      <SvgIcon class="icon_search" icon="icon_search" />
                    </n-popover>
                  </n-button>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <PublicTable
            ref="publicTable"
            :isDisplayAction="true"
            :needOtherActionBar="needOtherActionBar"
            :table-head-titles="tableHeadTitles"
            :pagination="pagination"
            :exceptHeight="250"
            :actionWidth="180"
            @tablePageChange="tablePageChange"
          >
            <!-- <template #status="{ editor }">
              {{ metaE[editor.row.status] }}
            </template> -->
            <template #referenced="{ editor }">
              {{ editor.row.referenced ? '已被引用' : '未被引用' }}
            </template>
            <template #cate="{ editor }">
              {{ cateE[editor.row.cate] }}
            </template>
            <template #source="{ editor }">
              {{ sourceE[editor.row.source] }}
            </template>
            <template #editor="{ editor }">
              <div class="edit-box">
                <n-button
                  v-if="buttonAuthList?.includes('governanceManage_modal_metaData_detailMetaData')"
                  code="governanceManage_modal_metaData_detailMetaData"
                  class="seeDetails has-right-border"
                  link
                  @click.stop.prevent="openDetail(editor)"
                  >查看</n-button
                >
                <!-- <n-button
                  v-if="buttonAuthList?.includes('governanceManage_modal_metaData_standardMeta')"
                  code="governanceManage_modal_metaData_standardMeta"
                  :disabled="editor.row.cate === 'STANDARD'"
                  class="seeDetails has-right-border"
                  link
                  @click.stop.prevent="onStandardization(editor)"
                  >标准</n-button
                > -->
                <n-button
                  v-if="buttonAuthList?.includes('governanceManage_modal_metaData_updateMetaData')"
                  code="governanceManage_modal_metaData_updateMetaData"
                  :disabled="editor.row.referenced"
                  class="seeDetails has-right-border"
                  link
                  @click.stop.prevent="onEdit(editor)"
                  >编辑</n-button
                >
                <n-button
                  v-if="buttonAuthList?.includes('governanceManage_modal_metaData_deleteMeta')"
                  code="governanceManage_modal_metaData_deleteMeta"
                  :disabled="editor.row.referenced"
                  class="seeDetails has-right-border"
                  @click.stop.prevent="onDelete(editor)"
                  link
                  >删除</n-button
                >
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
    </div>
  </div>
  <UploadByDrag :showUpload="showUpload" dialogTitle="元数据" @cancel="cancel" />
</template>

<script>
  import { mapState } from 'vuex'
  import { formartTime } from '@/utils/index'
  import UploadByDrag from './components/uploadFile/uploadByDrag'
  import ENUM from '@/const/enum'
  export default {
    name: 'CheckMetaData',
    components: { UploadByDrag },
    data() {
      return {
        loading: false,
        releObj: {}, // 发布弹框传递数据
        showOff: false,
        loadOff: false,
        offObj: {},
        newObj: {},
        showUpload: false,
        groupId: '',
        formInline: {
          keyword: '',
          timeData: '',
          status: '',
        },
        shortcuts: ENUM.SHORTCUTS,
        metaE: {
          CREATED: '已创建',
          OFFLINE: '已下架',
          ONLINE: '已上架',
        },
        cateE: { NORMAL: '普通', STANDARD: '标准' },
        sourceE: {
          NEW: '新增',
          IMPORT: '导入',
          REVERSE: '逆向',
          REALTIME_JOB: '实时作业',
          OFFLINE_JOB: '离线作业',
        },
        metaStatusList: [
          {
            value: '',
            label: '全部',
          },
          {
            value: 'CREATED',
            label: '已创建',
          },
          // {
          //   value: 'WAIT_AUDIT',
          //   label: '审核中',
          // },
          // {
          //   value: 'PUBLISHED',
          //   label: '已发布',
          // },
          // {
          //   value: 'AUDIT_FAIL',
          //   label: '审核失败',
          // },
          {
            value: 'OFFLINE',
            label: '已下架',
          },
          {
            value: 'ONLINE',
            label: '已上架',
          },
        ],
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
          // { prop: 'status', name: '状态', slot: 'status' },
          { prop: 'cate', name: '类型', slot: 'cate' },
          { prop: 'source', name: '来源', slot: 'source' },
          { prop: 'referenced', name: '状态', slot: 'referenced' },
          // { prop: 'description', name: '场景描述' },
          { prop: 'updateTime', name: '最后修改时间' },
        ],
        pagination: {
          pageSizes: [10, 20, 50], // 每次展示条数的可配置项
          layout: 'total,prev,pager,next,sizes, jumper',
          currentPage: 1,
          pageSize: 12,
        },
        total: 0,
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
        buttonAuthList: (state) => state['user'].buttonAuthList,
      }),
    },
    mounted() {
      this.onSearch()
    },
    methods: {
      // 获取左边tree切换数据
      getGroupData(val) {
        this.newObj = val
        if (val.id !== 60) {
          this.groupId = val.id
        }
        this.onSearch()
      },
      onSearch(init = true) {
        this.pagination.currentPage = init ? 1 : this.pagination.currentPage
        let startTime = ''
        let endTime = ''
        let statusList = []
        if (this.formInline.timeData) {
          startTime = formartTime(this.formInline.timeData[0])
          endTime = formartTime(this.formInline.timeData[1], 'end')
        }
        if (this.formInline.status) {
          // status = this.formInline.status.join(',')
          statusList.push(this.formInline.status)
        }
        let params = {
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          condition: {
            name: this.formInline.keyword || null,
            statusList,
            startTime: startTime || null,
            endTime: endTime || null,
            projectCode: this.currentProject.projectCode,
          },
        }
        this.$api.model
          .getMetaList(params)
          .then((res) => {
            res.data.list.map((item, index) => {
              return Object.assign(item, { number: index + 1 })
            })
            this.$refs.publicTable.initTableData(res.data)
            this.total = res.data.total
          })
          .catch(() => {
            this.$refs.publicTable.initFailed()
          })
      },
      // 表格操作变化
      tablePageChange(data) {
        this.pagination.currentPage = data.currentPage
        this.pagination.pageSize = data.pageSize
        this.onSearch(false)
      },
      indexMethod(index) {
        return (this.currentPage - 1) * this.pageSize + index + 1
      },
      //上传
      onUpload() {
        this.showUpload = true
      },
      cancel(init) {
        this.showUpload = false
        if (init) {
          this.onSearch(false)
        }
      },
      // 新增
      onCreate() {
        // if (this.currentProject.projectCode) {
        // 获取当前场景
        this.$router.push({
          name: 'addMetaData',
          query: {
            type: 'ADD',
            title: '新增元数据',
          },
        })
        // } else {
        //   this.$notify({
        //     title: '提示',
        //     message: '请先选择场景！',
        //     type: 'error',
        //   })
        // }
      },
      // 编辑
      onEdit(editor) {
        let { row } = editor
        this.$router.push({
          name: 'updateMetaData',
          query: {
            type: 'EDIT',
            id: row.id,
            title: '编辑元数据',
          },
        })
      },
      // 查看详情
      openDetail(editor) {
        let { row } = editor
        this.$router.push({
          name: 'detailMetaData',
          query: {
            type: 'DETAIL',
            id: row.id,
            title: '查看',
          },
        })
      },
      // 标准
      onStandardization(editor) {
        let { row } = editor
        this.releObj = {
          id: row.id,
        }
        this.$dialogPopup({
          title: '提示',
          message: '确认将该条元数据申请为标准元数据？',
          save: () => {
            this.confirmStandard(this.releObj)
          },
        })
      },
      confirmStandard(data) {
        this.$api.model.standardMeta(data).then((res) => {
          if (res.success) {
            this.$notify({
              title: '成功',
              message: '申请成功',
              type: 'success',
            })
            this.onSearch()
          }
        })
      },
      // 删除
      onDelete(editor) {
        let { row } = editor
        this.releObj = {
          id: row.id,
        }
        this.$dialogPopup({
          title: '提示',
          message: '是否删除选中的元数据',
          save: () => {
            this.confirmSubmit()
          },
        })
      },
      // 确定删除
      confirmSubmit() {
        this.$api.model.deleteMeta(this.releObj).then((res) => {
          if (res.success) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
            })
            let endSize = Math.ceil((this.total - 1) / this.pagination.pageSize)
            this.pagination.currentPage =
              this.pagination.currentPage >= endSize ? endSize : this.pagination.currentPage
            this.onSearch(false)
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    &-box {
      border-radius: 4px;
      height: 100%;
      background-color: #ffffff;
      display: flex;
    }

    &-left {
      border-right: 1px solid #ebebeb;
    }

    &-right {
      width: 100%;
      height: 100%;
      padding: 24px 20px 72px 20px;

      .commonForm {
        :deep(.el-form-item__label) {
          font-size: 12px;
        }
      }
    }

    .table {
      margin-top: 20px;

      .operate-btn {
        color: $themeBlue;
        padding: 0 10px;
        height: 10px;
        line-height: 10px;
        background-color: transparent;
        border: none;
        font-size: 12px;
        border-right: 1px solid #cfcfcf;
        margin-left: 0;
        border-radius: 0;

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          border-right: 1px solid transparent;
          padding-right: 0;
        }
      }
      :deep(.nancalui-button--outline) {
        background-color: transparent;
      }
    }

    .footer {
      padding: 31px 0;
      display: flex;
      justify-content: center;
    }

    .warning-title {
      color: #f5a623;
    }
  }
</style>
