:root {
  --el-border-color: #c9cdd4 !important;
  --el-border-radius-base: 2px !important;

  .container-left {
    width: auto;
  }

  //input
  .nancalui-input {
    .nancalui-input__inner {
      // color: var(----, rgba(0, 0, 0, 0.75));
      color: #1d2129;
      font-size: 14px;

      &::placeholder {
        // color: var(----, rgba(0, 0, 0, 0.35));
        color: #a8abb2;

        font-size: 14px;
        font-weight: normal;
      }
    }

    .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled) {
      padding: 0 10px;
      // border: 1px solid #e0e0e0;
      border: 1px solid $nancaluiInputDefaultBorder;
      border-radius: 2px;

      &.nancalui-input--focus:not(.nancalui-input--error) {
        border-color: $themeBlue;
      }
    }

    .nancalui-input__wrapper.nancalui-input--disabled {
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      background-color: rgb(229, 230, 235);
    }

    .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled):not(.nancalui-input--focus) {
      &:hover {
        border: 1px solid #2f5cd6;
        box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  .el-select__placeholder {
    color: #1d2129;
  }

  .nancalui-range-date-picker-pro {
    &.nancalui-range-date-picker-pro--open {
      .nancalui-range-date-picker-pro__range-picker {
        border: 1px solid $themeBlue !important;
      }
    }

    .nancalui-range-date-picker-pro__range-picker {
      border: 1px solid $nancaluiInputDefaultBorder;
      border-radius: 2px;

      .nancalui-range-date-picker-pro__input {
        flex: inherit;

        .nancalui-input {
          height: 30px;
        }

        .nancalui-input__wrapper {
          border: none !important;

          svg {
            path {
              fill: #909399;
            }
          }
        }
      }
    }

    .nancalui-range-date-picker-pro__range-picker:not(.nancalui-range-date-picker-pro--error):not(.nancalui-range-date-picker-pro--disabled):hover {
      border: 1px solid #2f5cd6 !important;
      box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
    }

    .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled):not(.nancalui-input--focus) {
      .nancalui-input__inner {
        color: var(----, #1d2129);
        font-size: 14px;

        &::placeholder {
          color: var(----, rgba(0, 0, 0, 0.35));
          font-size: 14px;
          font-weight: normal;
        }
      }

      &:hover {
        border: none;
        box-shadow: none;
      }
    }

    .nancalui-input__wrapper:not(.nancalui-input--error) {
      border: none;
    }
  }

  .nancalui-select {
    border-radius: 2px;

    &:not(:has(.nancalui-select__selection--error)):not(.nancalui-select--disabled):not(.compose) .nancalui-select__selection {
      border: 1px solid var(---, #e5e6eb);
      border-radius: 2px;
    }

    &.nancalui-select--disabled {
      background-color: #f5f7fa;

      .nancalui-select__selection {
        background-color: #f5f7fa;
        border: 1px solid #e5e5e5;
        border-radius: 2px;

        .nancalui-select__input {
          background: #f5f7fa;

          &:hover {
            background: #f5f7fa !important;
          }
        }

        .nancalui-tag {
          .nancalui-tag__item.nancalui-tag--default {
            height: 24px;
            padding: 0 4px;
            color: #b8b8b8;
            background: var(----, #ebf4ff);
            border: 1px solid var(---, #b8b8b8);
            border-radius: 2px;
          }
        }
      }
    }

    &:not(.nancalui-select--disabled) {
      .nancalui-select__selection {
        &:hover {
          border: 1px solid #2f5cd6 !important;
          box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
        }

        .nancalui-select__input {
          color: var(----, #1d2129);
          font-size: 14px;

          &::placeholder {
            color: var(----, rgba(0, 0, 0, 0.35));
            font-size: 14px;
            font-weight: normal;
          }
        }

        .nancalui-tag {
          .nancalui-tag__item.nancalui-tag--default {
            height: 24px;
            padding: 0 4px;
            background: var(----, #ebf4ff);
            border: 1px solid var(---, #a3b4db);
            border-radius: 2px;
          }
        }
      }
    }

    .nancalui-select__selection--error .nancalui-select__arrow {
      background-image: url('/src/assets/img/icon-select-arrow.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 16px 16px;

      svg {
        opacity: 0;
      }
    }
  }

  .nancalui-select__input,
  .nancalui-input__inner,
  .nancalui-textarea,
  .nancalui-tree-select-value,
  .nancalui-tree-select-dropdown-list {
    color: #000;
    font-size: 14px;

    &::placeholder {
      font-size: 14px;
      font-weight: normal;
    }
  }

  .nancalui-textarea:not(.nancalui-textarea--error) {
    border: 1px solid $nancaluiInputDefaultBorder;
    border-radius: 2px;

    .nancalui-select__selection {
      background-color: #fff;
      border: none;
      border-radius: 2px;
    }
  }

  // .nancalui-select:not(:has(.nancalui-select__selection--error)):not(
  //     .nancalui-select--disabled
  //   ):not(.compose) {
  //   border: 1px solid $nancaluiInputDefaultBorder;
  //   border-radius: 2px;

  //   .nancalui-select__selection {
  //     background-color: #fff;
  //     border: none;
  //     border-radius: 2px;
  //   }
  // }

  .nancalui-select {
    &.no-error-boder {
      &:has(.nancalui-select__selection--error) {
        border: 1px solid $nancaluiInputDefaultBorder;
        border-radius: 2px;

        .nancalui-select__selection {
          background-color: #fff;
          border: none;
          border-radius: 2px;
        }
      }
    }
  }

  no-error-boder .nancalui-select--disabled .nancalui-select__input,
  .nancalui-input--disabled,
  .nancalui-textarea--disabled,
  .nancalui-input--disabled .nancalui-input__inner {
    color: #c0c4cc;
    background-color: #f5f7fa;
  }

  .nancalui-select--disabled.nancalui-select--noborder .nancalui-select__input,
  .nancalui-input--disabled.nancalui-input--noborder,
  .nancalui-textarea--disabled.nancalui-textarea--noborder {
    padding-top: 5px;
    // font-size: 14px;
    background-color: #fff;

    .nancalui-input__inner {
      padding-top: 0;
      // font-size: 14px;
      background-color: #fff;
    }
  }

  .nancalui-textarea:not(.nancalui-textarea--error):not(.nancalui-textarea--disabled):not(.nancalui-textarea--focus):hover {
    border: 1px solid #2f5cd6;
    box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
  }

  .nancalui-flexible-overlay {
    z-index: 1071 !important;

    &.nancalui-popover__content {
      z-index: 1099 !important;
    }

    &.tag-box-clear-popover,
    &.tag-box-more-popover {
      transform: translateX(52px);
    }

    &.tag-box-clear-popover {
      padding: 4px;
    }

    &.total-menu-popover {
      width: 180px;
      padding: 8px;
      background: var(--sideBarBackColor);

      .total-menu-box {
        width: 100%;

        .label {
          height: 40px;
          margin-bottom: 4px;
          padding-left: 28px;
          color: var(--sideBarMenuColor);
          font-size: 14px;
          line-height: 40px;
          border-radius: 4px;
          cursor: pointer;

          &.checked {
            color: var(--sideBarMenuSelectColor);
          }

          &:last-of-type {
            margin-bottom: 0;
          }

          &:hover {
            background-color: var(--sideBarMenuBackColor);
          }
        }
      }
    }

    &.avatar-container-box {
      transform: translateX(112px);
    }

    &.news-container-box {
      transform: translateX(126px);
    }

    .nancalui-tree-select-item__content {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &.has-nancalui-date-picker-pro {
      transform: translateX(-104px);
    }


    &:has(.link-icon-dropdown){
      transform: translateX(-76px);
    }
  }

  .item-audit-see.nancalui-flexible-overlay.nancalui-popover__content {
    color: #fff;
    background-color: rgba(0, 0, 0, 0.65) !important;
    border-radius: 4px !important;

    .nancalui-flexible-overlay__arrow {
      width: 0px;
      height: 0px;
      background-color: transparent;
      border-color: rgba(255, 255, 255, 0) rgba(0, 0, 0, 0.65) rgba(0, 0, 0, 0.65) rgba(255, 255, 255, 0);
      border-style: solid;
      border-width: 4px;
    }
  }

  .nancalui-textarea--focus:not(.nancalui-textarea--error) {
    border-color: $themeBlue;
  }

  // tabs
  .nancalui-tabs {
    height: 46px;

    .nancalui-tabs-nav:before {
      background-color: transparent;
    }

    .nancalui-tabs-nav-tab {
      height: 46px;
      border-bottom: 1px solid #ebedf0;

      .nancalui-tabs-nav-tab-list-no-padding {
        .nancalui-tabs-tab:first-of-type {
          margin-left: 0;
        }

        .nancalui-tabs-tab-active {
          .nancalui-tabs-tab-title {
            font-weight: bold;
          }
        }
      }
    }
  }

  //nancalui-table 样式
  .nancalui-table {
    .nancalui-table__header-wrapper {
      height: 40px;
      // line-height: 40px;
      border-bottom: 1px solid #ebedf0;

      tr th .header-container {
        padding: 0 16px;
      }
    }

    tbody>tr>td {
      height: 40px;
      padding: 0 16px;
      // color: rgba(0, 0, 0, 0.75);
      color: #606266;
      font-size: $themeFont;
      border-bottom: 1px solid #e5e6eb;

      &.is-left:last-child {
        padding: 0 8px 0 20px;
      }
    }

    .nancalui-table__thead {
      tr th {
        background: #ebf4ff;
        border-bottom: 1px solid #e5e6eb;
      }
    }

    .nancalui-table__thead .header-container .title {
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: $themeFont;
      line-height: 40px;
    }

    .nancalui-table__header-wrapper tr th .header-container div {
      height: 40px;
      line-height: 40px;
    }

    .nancalui-table__view {
      height: 40px;
    }

    .nancalui-table__tbody tr.hover-enabled:hover,
    .nancalui-table__tbody tr.is-highlight {
      background-color: #ebf4ff;

      .nancalui-table--sticky-cell {
        background: #ebf4ff;
      }
    }

    &.sticky-table {
      .nancalui-table--sticky-cell {
        //background-color: transparent;
        position: sticky;
        z-index: 5;
      }
    }
  }

  // button
  .nancalui-button {
    height: $themeButtonHeight;
    padding: 0 $themeButtonPadding;
    font-size: $themeFont;
    border-radius: 2px;
  }

  .nancalui-button--sm {
    height: $themeButtonHeight;
    padding: 0 calc($themeButtonPadding + 6px);
    font-size: $themeFont;
  }

  .nancalui-button--round {
    border-radius: 20px;
  }

  .nancalui-button+.nancalui-button {
    margin-left: 12px;
  }

  .nancalui-button--solid--primary:hover {
    background-color: #6e9eff;
  }

  .nancalui-button--solid--primary:active {
    background-color: #2f5cd6;
  }

  .nancalui-button--solid--primary:focus {
    background-color: #2f5cd6;
  }

  .nancalui-button--solid--primary:disabled {
    color: #adb0b8;
    background-color: #f5f5f5;
    border: 1px solid #e1e1e1;
    border-color: #e1e1e1;
  }

  .nancalui-button--outline--secondary {
    color: #333333;
    background-color: #ffffff;
    border-color: #dcdfe6;
  }

  .nancalui-button--outline--primary:hover {
    color: #447dfd;
    background: #f0f7ff;
    //border-radius: 4px;
    border: 1px solid #bfd9ff;
  }

  .nancalui-button--outline--primary:disabled,
  .nancalui-button--outline--primary.disabled {
    color: #adb0b8;
    background-color: #f5f5f5;
    border-color: #e1e1e1;
  }

  .nancalui-button--outline--secondary:hover {
    color: #447dfd;
    background-color: #f0f7ff;
    border-color: #bfd9ff;
  }

  .nancalui-button--outline--primary:active,
  .nancalui-button--outline--secondary:active {
    color: #2f5cd6;
    background-color: #e6ecf8;
    border-color: #2f5cd6;
  }

  .nancalui-button--outline--primary {
    color: #447dfd;
    background-color: #ffffff;
    border-color: #447dfd;
  }

  .nancalui-checkbox {
    .nancalui-checkbox__label-text {
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
    }
  }

  .nancalui-radio {

    &.nancalui-radio--md .nancalui-radio__label,
    .nancalui-radio__label {
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
    }

    .nancalui-radio__material-outer {
      stroke: $nancaluiInputDefaultBorder;
    }
  }

  .nancalui-radio-group {
    .nancalui-radio-button.active {
      background-color: #1e89ff;
      border-color: #1e89ff;
    }
  }

  .nancalui-radio-group .nancalui-radio__wrapper:not(:last-child) {
    padding-right: 16px;
  }

  .nancalui-radio.active .nancalui-radio__material-inner.disabled {
    fill: #cfcfcf;
  }

  .nancalui-radio.active .nancalui-radio__material-outer.disabled {
    stroke: #e1e1e1;
  }

  .nancalui-form {
    .nancalui-form__label {
      height: 32px;
      color: var(----, rgba(0, 0, 0, 0.85));
      font-size: 14px;
      line-height: 30px;

      .nancalui-form__label-span {
        color: var(----, rgba(0, 0, 0, 0.85));
        font-weight: bolder;
      }

      // display: flex;
      // align-items: center;
    }

    &.form__label_font14 {
      .nancalui-form__label {
        font-size: 14px;
      }
    }

    .nancalui-form__item--horizontal {
      margin-bottom: 8px;

      &.label_align_required {
        align-items: flex-start;

        .nancalui-form__label::before {
          display: none;
          margin-right: 2px;
          color: #fff;
          vertical-align: middle;
          content: '*';
        }
      }

      &:has(.error-message) {
        margin-bottom: 20px;
      }
    }

    .nancalui-form__control {
      .nancalui-form__control-info {
        position: absolute;

        .error-message {
          font-size: 12px;
        }
      }
    }

    .nancalui-form__item--error {
      margin-bottom: 20px;

      //form校验不通过样式
      .nancalui-tree-select-input {
        border-color: $formDangerLineColor;
      }

      .nancalui-input--error,
      .nancalui-select__selection--error,
      .nancalui-textarea--error,
      .nancalui-range-date-picker-pro .nancalui-range-date-picker-pro--error {
        background-color: inherit;
      }
    }

    .nancalui-form__item--vertical {
      margin-bottom: 10px;
    }

    .nancalui-form__control--horizontal {
      margin-left: 0;
    }

    .nancalui-textarea__show-count {
      color: #999999;
      font-size: 12px;
    }

    &.disable-hide-border {

      //表单查看样式
      .nancalui-form__label {
        padding: 0 0 0 0;
      }

      .nancalui-form__control {
        .nancalui-cascader__icon {
          display: none;
        }

        .nancalui-textarea__show-count {
          display: none;
        }
      }
    }
  }

  .disabled-form {
    .nancalui-form__label {
      display: flex;
      align-items: center;
      height: 30px;
    }

    .nancalui-form__control {
      .nancalui-select--disabled {
        color: #000;
        font-size: 12px;
        background-color: transparent !important;
        border: none;
        cursor: default;

        .nancalui-select__selection,
        .nancalui-select__input {
          padding: 0;
          color: #000;
          font-size: 14px;
          background-color: transparent !important;
          border: none;
          cursor: default;
        }

        .nancalui-select__arrow {
          display: none;
        }
      }

      .nancalui-textarea--disabled {
        color: #000;
        font-size: 14px;
        background-color: transparent !important;
        border: none;
        cursor: default;
      }

      .nancalui-input--disabled {
        padding: 0;
        background-color: transparent !important;
        border: none;
        cursor: default;
        resize: none; //隐藏拉伸按钮

        .nancalui-input__inner {
          color: #000;
          font-size: 14px;
          cursor: default;
        }
      }
    }
  }

  .nancalui-modal {
    min-height: 20px !important;
    border-radius: 12px;

    >.btn-close {
      top: 12px;
      right: 16px;
      width: 16px;
      height: 16px;

      .nancalui-modal__close {
        height: 16px;
        background-image: url('/src/assets/img/public/close.png');
        background-repeat: no-repeat;

        >svg {
          display: none;
        }
      }
    }

    .nancalui-modal__header {
      height: auto;
      padding: 13px 16px 13px 0;
      font-family: PingFangSC-Medium, PingFang SC;
      border-bottom: none !important;

      .nancalui-modal__title {
        display: flex;
        align-items: baseline;
        position: relative;
        height: 22px;
        padding-left: 16px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: 500;
        font-size: 16px;
        //background-image: url('/src/assets/img/public/icon_01.png');
        //background-repeat: no-repeat;
        //background-position: left;
        //background-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }
      }
    }

    &.commonDialog {
      .nancalui-modal__body {
        padding: 0 30px 16px;
      }
    }

    &.largeDialog {
      .nancalui-modal__body {
        padding: 16px;
      }
    }

    &.middleDialog {
      .nancalui-modal__body {
        padding: 0 16px 16px;
      }
    }

    &.parameterDialog {
      .nancalui-modal__body {
        padding: 0 16px 0;
      }
    }

    &.targetDialog.nancalui-modal__isPadding {
      .nancalui-modal__header {
        margin-bottom: 0;
      }

      .nancalui-modal__body {
        padding: 16px;
      }

      .nancalui-modal__footer {
        padding: 16px !important;
      }
    }

    &.hasDisabledBackground {

      .nancalui-input--disabled,
      .nancalui-textarea--disabled {
        background-color: #e5e5e5 !important;
      }
    }

    &.moveDialog {
      .nancalui-modal__header {
        margin-bottom: 0;
      }

      .nancalui-modal__body {
        padding: 16px;
      }
    }

    &.nancalui-modal__isPadding.datasetNameDialog {
      .nancalui-modal__header {
        margin-bottom: 0;
      }

      .nancalui-modal__body {
        padding: 24px 16px;
      }

      .nancalui-modal__footer {
        padding: 16px !important;
      }
    }

    &.has-top-padding {
      .nancalui-modal__body {
        width: 100%;
        padding-top: 16px;
      }
    }

    &.file-view-dialog {
      .nancalui-modal__body {
        padding: 16px;
      }
    }

    &:not(.message-box-modal).nancalui-modal__body {
      padding-top: 16px !important;

      >div {
        width: 100%;
        height: 100%;
      }
    }

    .icon-close:before {
      // content: '\E071' !important;
    }

    .nancalui-tree-select-clearable {
      z-index: 1001;

      .icon-close:before {
        content: '\E073' !important;
        display: inline-block;
        font-size: 10px;
        font-weight: 900;
      }
    }

    .nancalui-input--disabled,
    .nancalui-textarea--disabled {
      background-color: transparent !important;
      border: none;
      box-shadow: none;
    }

    .nancalui-input--disabled .nancalui-input__inner {
      color: rgba(0, 0, 0, 0.28);
    }

    .nancalui-modal__footer {
      border-top: 0;
    }

    &.nancalui-modal__isPadding {
      .nancalui-modal__footer {
        padding: 16px 20px !important;

        .nancalui-button {
          margin-right: 0;
          margin-left: 8px;
        }
      }
    }

    .nancalui-modal__footer {
      padding: 16px;
    }
  }

  .nancalui-drawer {
    padding: 0;
    border-radius: 0;

    .drawer-content {
      padding: 0 20px;

      &-name {
        position: relative;
        left: -20px;
        width: calc(100% + 40px);
        height: 38px;
        padding: 0 20px;
        line-height: 38px;
        background-color: #f7f8fa;

        .close {
          position: absolute;
          top: 0;
          right: 20px;
          bottom: 0;
          width: 16px;
          height: 16px;
          margin: auto;
          cursor: pointer;
        }
      }

      .nancalui-tabs {
        margin-bottom: 15px;
      }
    }

    .n-drawer-body {
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 52px;
        padding: 0 16px;
        border-bottom: 1px solid #e5e6eb;

        &-name {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: calc(100% - 30px);
          color: rgba(0, 0, 0, 0.9);
          font-weight: bold;
          font-size: 18px;

          .icon {
            margin-right: 8px;

            font-size: 16px;
          }

          .title {
            width: calc(100% - 24px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }

        .close {
          color: #8091b7;
          font-size: 24px;
          cursor: pointer;
        }
      }

      &-content {
        box-sizing: border-box;
        height: calc(100vh - 116px);
        padding: 16px 16px 0 16px;
        overflow-y: auto;

        &.has-top-padding {
          padding: 16px 16px 0 16px;
        }

        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 8px;

          &:hover {
            background-color: #b1bcd6;
          }
        }

        .nancalui-form {
          .nancalui-form__item--horizontal {
            margin-bottom: 8px;

            &:has(.error-message) {
              margin-bottom: 20px;
            }
          }
        }
      }

      &-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 64px;
        padding: 0 16px;
      }
    }
  }

  //操作栏Popover 悬浮提示
  .nancalui-popover__content {
    &[popper-class] {
      flex-direction: column;
      min-width: 76px;
      padding: 6px 0;
      border-radius: 4px;

      .nancalui-button+.nancalui-button {
        margin-left: 0;
      }

      .nancalui-button {
        width: 100%;
        height: 32px;
        margin-left: 0;
        padding: 0 12px;
        color: $themeBlue;
        font-size: 12px;
        line-height: 32px;
        text-align: center;
        border-radius: 0;

        &:hover {
          background-color: #ebf4ff !important;
        }

        &:disabled {
          color: #c9c9c9;
        }
      }

      svg {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .card-popover {
      padding-right: 40px;
      color: #333;
      font-size: 14px;
      cursor: pointer;

      &-item {
        margin-top: 10px;

        &:first-of-type {
          margin-top: 0;
        }

        &:hover {
          color: $themeBlue;
        }

        &.disabled {
          color: #b8b8b8;
          cursor: not-allowed;

          .icon-card {
            color: #b8b8b8;
            cursor: not-allowed;
          }
        }
      }

      .icon-card {
        margin-right: 6px;
        font-size: 18px;
        vertical-align: -4px;
      }
    }

    .card-description {
      max-width: 400px;
      line-height: 20px;
      white-space: normal;
      word-break: break-all;
    }

    .btn-more-popover {
      color: red;

      .seeDetails {
        height: 32px;
        margin-left: 0;
        color: var(--themeBlue);
        font-size: 12px;
        line-height: 32px;
        text-align: center;

        &:hover {
          background-color: #f7f8fa !important;
        }

        &:disabled {
          color: #c9c9c9 !important;
        }
      }
    }

    &.btn-more-popover-target {
      transform: translateX(68px);
    }

    &.card-tips-content {
      width: 300px;
      white-space: normal;
      background-color: rgba(0, 0, 0, 0.65);
      transform: translateX(158px);

      .red-tips-popover {
        display: -webkit-box;
        width: 100%;
        overflow: hidden;
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        line-height: 22px;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 9;
        /* 超出几行省略 */
      }

      .nancalui-flexible-overlay__arrow {
        width: 0;
        height: 0;
        background-color: transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid rgba(0, 0, 0, 0.65);
        border-left: 8px solid transparent;
        transform: rotate(0deg) translateY(-4px);
      }
    }

    &.header-progress-popover {
      width: 134px;
      height: 139px;
      padding: 0;
      transform: translateX(80px);

      .header-progress-popover-box {
        width: 100%;
        padding: 2px 0;

        &-label {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          padding: 0 12px;

          &:first-of-type {
            border-bottom: 1px solid #e5e5e5;
          }

          &-name {
            color: rgba(0, 0, 0, 0.46);
            font-weight: 400;
            font-size: 14px;
          }

          &-value {
            max-width: 70px;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    &.table-list-card-popover {
      width: 132px;
      padding: 6px 0;
      box-shadow: 0 6px 32px -2px rgba(0, 0, 0, 0.1);
      transform: translateX(72px);

      &.small {
        width: 76px;
        transform: translateX(50px);
      }

      &.card {
        width: 104px;
        transform: translateX(72px);

        &.more-lang {
          width: 120px;
        }
      }

      .table-list-card-header-popover {
        width: 100%;

        &-label {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          height: 32px;
          margin-left: 0;
          padding-left: 12px;
          border: none;
          border-radius: 0;

          .icon {
            margin-right: 8px;
            font-size: 16px;
            vertical-align: -3px;
          }

          &:not(.nancalui-button--text--secondary:disabled):hover {
            color: $themeBlue;
            background-color: #ecf7ff;
          }
        }
      }
    }
  }

  // 下拉树
  .nancalui-tree-select {
    &:not(.nancalui-tree-select-open) {
      border: 1px solid #e5e6eb !important;
    }

    &:not(.nancalui-tree-select-open):hover {
      border: 1px solid #2f5cd6 !important;
      box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
    }

    &.nancalui-tree-select-open {
      .nancalui-tree-select-input {
        border: 1px solid $themeBlue !important;
      }
    }

    .nancalui-tree-select-input {
      position: relative;
      border: none !important;

      border-radius: 2px;

      .nancalui-tree-select-value {
        position: absolute;
        left: 0;
        z-index: 2;
        box-sizing: border-box;
        width: max-content;
        padding-right: 20px;
        padding-left: 10px;
        overflow: hidden;
        line-height: 24px;
        white-space: nowrap;
        text-overflow: ellipsis;

        &:has(+ .nancalui-tree-select-value) {
          position: unset;
          left: unset;
          margin-left: 4px;
        }

        +.nancalui-tree-select-value {
          position: unset;
          left: unset;
          margin-left: 4px;
        }

        .icon-box {
          margin-left: 0;
        }
      }

      input {
        position: relative;
        z-index: 3;
        padding: 0;
        font-size: 12px;
        background-color: transparent;
      }
    }

    .nancalui-tree-select-arrow {
      background-image: url('/src/assets/img/icon-select-arrow.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 16px 16px;

      .nancalui-icon__container {
        opacity: 0;
      }
    }

    .nancalui-tree-select-notclearable {
      .nancalui-tree-select-input {
        background-color: #f5f5f5;

        .nancalui-tree-select-value {
          color: #000;
        }
      }
    }

    &.nancalui-tree-select-disabled {
      .nancalui-tree-select-notclearable {
        .nancalui-tree-select-input {
          background-color: #e5e5e5 !important;
          border: 1px solid #e5e5e5;

          .nancalui-tree-select-value {
            color: rgba(0, 0, 0, 0.28);
          }
        }
      }
    }
  }

  .nancalui-select__dropdown {
    .nancalui-select__item {
      font-size: 12px;
    }
  }

  // 下拉框
  .nancalui-select {
    .nancalui-select__arrow {
      background-image: url('/src/assets/img/icon-select-arrow.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 16px 16px;

      svg {
        opacity: 0;
      }
    }

    //下拉多选框
    .nancalui-tag .nancalui-tag__item.nancalui-tag--default {
      height: 20px;
      background: #f5f5f5;
      // border: 1px solid #e0e0e0;
      border: 1px solid $nancaluiInputDefaultBorder;
      border-radius: 2px;
    }
  }

  .nancalui-cascader {
    .nancalui-cascader--drop-icon-animation {
      background-image: url('/src/assets/img/icon-select-arrow.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 16px 16px;

      svg {
        opacity: 0;
      }
    }
  }

  .nancalui-cascader__drop-menu-animation {
    .nancalui-cascader__li {
      &.leaf-active {
        span {
          color: #333;
        }
      }

      &.leaf-checked {
        span {
          color: #447dfd;
        }

        .icon-has-child {
          color: #447dfd !important;
        }
      }

      .dropdown-item-label {
        color: #333;
        font-size: 12px;
      }
    }
  }

  .nancalui-tooltip {
    background-color: #000;
  }

  .nancalui-menu {
    margin: 0;

    .home-item-sub {
      margin-bottom: 4px;
      border-radius: 0;

      .nancalui-submenu-title {
        height: 38px !important;
        padding: 0 8px !important;
        line-height: 38px;

        &:hover {
          background-color: #f0f2f5 !important;

          span {
            color: #1d2129 !important;
          }
        }

        .nancalui-menu-icon {
          display: inline-block;
          height: 16px;
          line-height: 16px;

          .left-icon {
            display: block;
            width: 16px !important;
            height: 16px !important;
            font-size: 16px;
            margin-left: 8px;
            color: #1d2129;
          }
        }

        .nancalui-submenu-title-content {
          height: 18px !important;
          margin-left: 8px !important;
          color: #1d2129;
          font-weight: 500;
          font-size: 14px !important;
          line-height: 18px !important;
        }

        .icon-chevron-up {
          width: 16px;
          height: 16px;
          color: #909399;
          background-image: url("/src/assets/img/icon-select-arrow-top.png");
          background-repeat: no-repeat;
          background-position: center;
          background-size: 16px 16px;
          &:before{
            display: none;
          }
        }
      }

      .nancalui-submenu-menu-item-vertical-wrapper {
        background-color: #ffffff;
        border-radius: 0;

        .nancalui-menu-item {
          align-items: center;
          height: 38px !important;
          margin: 0 0 4px 0;
          padding: 0 28px 0 40px !important;
          line-height: 38px;
          border-radius: 0;
          box-sizing: border-box;
          position: relative;

          &:hover {
            background-color: #f0f2f5 !important;
          }

          &.nancalui-menu-item-select {
            background-color: #ebf4ff !important;

            &:not(.nancalui-menu-item-isCollapsed) {
              &:before {
                content: '';
                width: 3px;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                background-color: #1e89ff;
              }
            }

            span {
              color: #1d2129 !important;
            }
          }

          >span {
            color: #1d2129;
            font-weight: 400;
            font-size: 14px;
            line-height: 32px;
          }
        }
      }

      &:has(.nancalui-menu-item-select) {
        >.nancalui-submenu-title span {
          color: #1e89ff !important;

          .left-icon {
            color: #1e89ff !important;
          }
        }
      }
    }

    .home-item {
      margin-bottom: 4px !important;

      .nancalui-menu-item {
        align-items: center;
        height: 38px !important;
        padding: 0 8px !important;
        line-height: 38px;
        border-radius: 2px;

        &:hover {
          background-color: #f0f2f5 !important;
        }

        .left-icon {
          font-size: 16px;
          width: 16px !important;
          height: 16px !important;
        }

        .title {
          display: block;
          height: 18px;
          color: #1d2129;
          font-weight: 500;
          font-size: 14px;
          line-height: 18px;
        }

        >span {
          display: inline-block;
          height: 18px;
          line-height: 18px;
          margin-left: 8px;

          &.nancalui-menu-icon {
            width: 16px;
            height: 16px;
          }

          &:last-child {
            line-height: 32px;
          }
        }
      }
    }

    &.nancalui-menu-collapsed {
      width: 48px !important;

      .home-item-sub {
        margin: 0 0 4px 0 !important;
        padding-bottom: 0 !important;

        &:has(.nancalui-menu-item-select) {
          background-color: #ebf4ff;

          &:before {
            content: '';
            width: 3px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            background-color: #1e89ff;
          }
        }

        .nancalui-submenu-title {
          height: 38px !important;
          padding: 0 !important;
          text-align: center;
          background-color: transparent !important;

          .nancalui-submenu-title-content {
            display: none;
            margin: 0 !important;
          }
        }

        .nancalui-submenu-menu-item-vertical-wrapper {
          .nancalui-menu-item-vertical-wrapper {
            .nancalui-menu-item {
              margin: 0 8px;
            }
          }
        }
      }

      .collapsedSpanSub {
        display: flex !important;

        .nancalui-menu-icon {
          .left-icon {
            margin-left: 0;
          }
        }
      }

      .home-item {
        .nancalui-menu-item-isCollapsed {
          width: 48px !important;
          height: 38px !important;

          span {
            .title {
              display: none;
            }
          }
        }
      }
    }
  }

  //时间选择器弹框
  .nancalui-time-popup {
    .nancalui-time-list .time-item .time-ul .time-li span {
      font-size: 12px;
    }
  }

  // Tooltip 提示
  .nancalui-tooltip {
    background-color: transparent;

    &.nancalui-trigger-popup[trigger-placement='top'].tree-btn {
      .nancalui-tooltip__content {
        max-width: 380px;
        background: rgba(0, 0, 0, 0.65);
        border-radius: 4px;
      }

      .nancalui-trigger-arrow {
        width: 0;
        height: 0;
        background-color: transparent;
        border-top: 6px solid rgba(0, 0, 0, 0.65);
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
        transform: translate(-6px, 6px) rotate(0deg) !important;
      }
    }
  }

  //分页器样式
  .nancalui-table-page {
    justify-content: flex-end;
    height: 64px;
    margin-top: 0;
    padding: 16px;

    .nancalui-pagination {
      position: relative;
      justify-content: flex-end !important;
      width: 100%;

      ul li {
        min-width: 30px;
        height: 34px;
        margin-left: 4px;

        &:first-child {
          .nancalui-pagination__link svg {
            width: 0;
            height: 0;
            margin-left: -6px;
            border: 5px solid transparent;
            border-right-color: $themeBlue;
          }

          &.disabled .nancalui-pagination__link svg {
            border-right-color: var(--nancalui-disabled-text, #c8c9cc);
          }
        }

        &:last-child {
          .nancalui-pagination__link svg {
            width: 0;
            height: 0;
            margin-left: 6px;
            border: 5px solid transparent;
            border-left-color: $themeBlue;
          }

          &.disabled .nancalui-pagination__link svg {
            border-left-color: var(--nancalui-disabled-text, #c8c9cc);
          }
        }
      }

      .nancalui-pagination__total-size {
        // position: absolute;
        // top: 50%;
        // left: 0;
        // margin-top: -11px;
        color: var(----, rgba(0, 0, 0, 0.75));
        font-size: 14px;
        line-height: 22px;

        .total-span {
          padding: 0 4px;
          color: $themeBlue;
        }
      }

      .nancalui-pagination__list {
        margin-right: 12px;

        &>li.nancalui-pagination__item>a.nancalui-pagination__link {
          width: 100%;
          height: 100%;
          margin-left: 0;
          line-height: 34px;
          color: #1d2129;
        }
      }

      .nancalui-pagination__size {
        height: 34px;

        // margin: 0;
        .nancalui-icon__container {
          color: #1d2129;
        }

        .icon-select-arrow {
          width: 16px;
          height: 16px;
          background-image: url('/src/assets/img/icon-select-arrow.png');
          background-repeat: no-repeat;
          background-position: center;
          background-size: 16px 16px;

          &::before {
            width: 0;
            height: 0;
            content: '';
          }
        }
      }

      .nancalui-pagination__jump-container {
        color: var(----, rgba(0, 0, 0, 0.75));
        font-size: 14px;
        line-height: 34px;

        .nancalui-input {
          height: 34px;
        }
      }
    }
  }

  $cf-color-primary: #1e89ff;

  .nancalui-button--solid--primary {
    border-radius: 2px;
    background: $cf-color-primary;
  }

  .nancalui-drawer {
    padding: 0;
    border-radius: 0;

    .module-name-text {
      font-size: 16px;
    }

    .module-name-border {
      width: 4px;
      height: 18px;
      margin-right: 12px;
      background-color: $cf-color-primary;
    }

    .nancalui-drawer__title {
      margin-bottom: 0 !important;
      padding: 14px 0;
      font-size: 16px;
      font-family: 'Source Han Sans CN';
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .nancalui-form {
    .nancalui-form__label {
      height: 32px;
      color: var(----, #606266);
      font-size: 14px;
      line-height: 30px;

      .nancalui-form__label-span {
        color: var(----, #606266);
        font-weight: 400;
      }
    }
  }

  .nancalui-button--outline--primary {
    color: $cf-color-primary;
    border-color: $cf-color-primary;
    border-radius: 2px;
  }

  .nancalui-button .button-content {
    display: flex;
    align-items: center;
  }

  .nancalui-button--outline {
    border-radius: 2px;
  }

  .nancalui-input .nancalui-input__wrapper {
    border-radius: 2px !important;
  }

  .nancalui-input .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled) {
    border: 1px solid var(---, #e5e6eb);
  }

  .nancalui-textarea:not(.nancalui-textarea--error) {
    border: 1px solid var(---, #e5e6eb);
    border-radius: 2px;
  }

  .common-table .page-mid .nancalui-table .nancalui-table__header-wrapper .nancalui-table__thead {
    tr {
      background-color: #ebf4ff;
      border-bottom: 1px solid #e5e6eb;

      th {
        background-color: #ebf4ff;
      }

      .title {
        color: var(----, #1d2129);
        font-weight: 400;
      }
    }
  }

  .common-table:has(.nancalui-table-page) .nancalui-table {
    border-bottom: 1px solid #dcdfe6;
  }

  .nancalui-table__cell .nancalui-button--text {
    padding: 0;
  }

  .nancalui-modal {
    border-radius: 2px;
  }

  .nancalui-pagination__jump-container .nancalui-input .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled) {
    border: 1px solid var(--nancalui-form-control-line, #e5e6eb);
    border-radius: 4px !important;
  }
}

.nancalui-notification {
  z-index: 9999 !important;
}

.nancalui-input__wrapper {
  color: #1d2129 !important;
}

.nancalui-range-date-picker-pro .nancalui-range-date-picker-pro__range-picker,
.nancalui-tree-select .nancalui-tree-select-input {
  border: 1px solid var(---, #e5e6eb) !important;
}

.nancalui-range-date-picker-pro .nancalui-range-date-picker-pro__range-picker,
.nancalui-tree-select .nancalui-tree-select-input {
  border: 1px solid var(---, #e5e6eb) !important;
}

.nancalui-dropdown__menu-wrap{
  &.link-icon-dropdown{
    padding: 0;
    box-shadow: 0 4px 16px -2px rgba(30, 47, 85, 0.15);
    border-radius: 8px;
    background: #fff;
    .icon-dropdown {
      width: 380px;
      height: 204px;
      padding: 16px;
      box-sizing: border-box;
      white-space: normal;
      word-break: break-all;

      &-close {
        width: 100%;
        text-align: right;
        margin-bottom: 16px;
        .icon{
          color: #606266;
          font-size: 16px;
        }

      }
      &-content {
        .pic {
          display: inline-block;
          width: 36px;
          height: 36px;
          margin-right: 16px;
          margin-bottom: 16px;
          cursor: pointer;
          &:nth-of-type(7n){
            margin-right: 0;
          }
        }
      }
    }
  }
}

