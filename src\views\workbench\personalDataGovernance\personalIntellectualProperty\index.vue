<template>
    <div class="container">
        <div class="personal-intellectual-property">
            <!-- 专利、成果、荣誉、标准 tab页 -->
            <div class="tabs">
                <n-tabs v-model="condition.type" @active-tab-change="onTabChange">
                    <n-tab :title="item.label" :id="item.name" v-for="(item, index) in state.tabList" :key="index" />
                </n-tabs>
            </div>
            <div class="tab-content">
                <CfTable actionWidth="120" ref="tableNoRef" :tableConfig="{
                    data: state.dataSource,
                    rowKey: 'id',
                }" :table-head-titles="tableHead" :paginationConfig="{
                    total: state.searchData.total,
                    pageSize: state.searchData.pageSize,
                    currentPage: state.searchData.pageNum,
                    onCurrentChange: (v) => {
                        state.searchData.pageNum = v
                        onSearch(false)
                    },
                    onSizeChange: (v) => {
                        state.searchData.pageSize = v
                        onSearch()
                    },
                }">
                    <template #fileOriginalName="{ row }">
                        <!-- 上传 -->
                        <n-button color="primary" variant="text" @click="handleUpload(row)">
                            上传
                        </n-button>
                        {{ row.fileOriginalName }}
                    </template>

                </CfTable>

            </div>
        </div>
    </div>
</template>
<script setup>
import { searchData } from '@/api/dataApplication';
import api from '@/api/index' // 接口
const state = reactive({
    tabList: [ // 专利、成果、荣誉、标准 tab页,
        { label: '专利', name: '专利' },
        { label: '成果', name: '成果' },
        { label: '荣誉', name: '荣誉' },
        { label: '标准', name: '标准' },
    ],
    tableHeadData: {
        // 专利
        '专利': [
            { name: '专利名称', prop: 'intellectualName' },
            { name: '排行', prop: 'ranking' },
            { name: '年度', prop: 'annual' },
            { name: '状态', prop: 'status' },
            { name: '附件', prop: 'fileOriginalName', slot: 'fileOriginalName' },
        ],
        // 成果
        '成果': [
            { name: '成果名称', prop: 'achievementName' },
            { name: '排行', prop: 'ranking' },
            { name: '年度', prop: 'annual' },
            { name: '级别', prop: 'achievementGrade' },
            { name: '附件', prop: 'fileOriginalName', slot: 'fileOriginalName' },
        ],
        // 荣誉
        '荣誉': [
            { name: '荣誉称号', prop: 'honoraryTitle' },
            { name: '年度', prop: 'annual' },
            { name: '级别', prop: 'achievementGrade' },
            { name: '授予单位', prop: 'awardingUnit' },
            { name: '附件', prop: 'fileOriginalName', slot: 'fileOriginalName' },
        ],
        // 标准
        '标准': [
            { name: '标准编号', prop: 'standardNumber' },
            { name: '年度', prop: 'annual' },
            { name: '级别', prop: 'achievementGrade' },
            { name: '状态', prop: 'status' },
            { name: '附件', prop: 'fileOriginalName', slot: 'fileOriginalName' },
        ]
    },
    dataSource: [], // 表格数据
    searchData: { // 分页
        pageNum: 1, // 当前页
        pageSize: 10, // 每页显示条数
        condition: {
            type: '荣誉', // 类型
        }
    },
    total: 0, // 总条数
})
const { condition } = toRefs(state.searchData)
const tableHead = computed(() => { // 表格表头
    return state.tableHeadData[condition.value.type] || []
})
const onTabChange = (val) => { // tab页切换
    condition.value.type = val // 类型
    state.dataSource = [] // 表格数据
    state.searchData.pageNum = 1 // 当前页
    state.searchData.pageSize = 10 // 每页显示条数
    onSearch() // 搜索
}
const onSearch = () => { // 搜索
    api.dataGovernance.getPersonDataGovernance(state.searchData).then(res => { // 调用接口
        state.dataSource = res.data.list // 表格数据
        state.total = res.data.total // 总条数
    })
}
const handleUpload = (row) => { // 上传
    console.log('上传', row)
    uploadData(row.id)
}

const uploadData = (id) => {
    const input = document.createElement('input') // 创建input元素
    input.type = 'file' // 设置input元素类型为file
    input.onchange = (e) => { // 监听input元素的change事件
        const file = e.target.files[0] // 获取文件
        console.log(file) // 打印文件
        const formData = new FormData()
        formData.append('file', file)
        formData.append('bucket', 'data-govern')
        api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
            if (res.success) { // 上传成功
                api.dataGovernance.getPersonDataGovernanceDetail({ // 上传文件
                    fileOriginalName: file.name, // 文件名称
                    id, // 文件类型
                    fileUrl: res.data.url, // 文件地址
                }).then(({ success }) => { // 上传文件
                    if (success) { // 上传成功
                        ElMessage.success('上传成功') // 提示
                        onSearch() // 搜索
                    } else { // 上传失败    
                        ElMessage.error('上传失败') // 提示
                    }
                })
            } else { // 上传失败
                ElMessage.error('上传失败') // 提示
            }
        })
    }
    input.click() // 点击input元素
}
onSearch() // 搜索
</script>
<style lang="scss" scoped>
.container {
    padding: 16px;
    border-radius: 0;

    .personal-intellectual-property {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 0;

        .tabs {
            width: 100%;
            height: 40px;
            padding: 0 16px;
        }

        .tab-content {
            height: calc(100% - 40px - 16px);
            margin-top: 16px;
        }
    }
}
</style>
