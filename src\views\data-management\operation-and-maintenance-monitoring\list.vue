<template>
  <div :class="['container ', 'scroll-bar-style']">
    <div class="content-box">
      <div class="content-box-row">
        <div class="col">
          <div class="col-content">
            <div class="col-content-row">
              <div class="col-content-row-item">
                <div class="col-content-row-item-title" unit-text="（个）"> 数据源 </div>
                <div class="col-content-row-item-content">
                  {{ state.dataSourceNum }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title" unit-text="（运行中）"> 采集任务</div>
                <div class="col-content-row-item-content">
                  <!-- {{ collectInfo.collectJobCount }} -->
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title" unit-text="（张）"> ODS层表总数</div>
                <div class="col-content-row-item-content">
                  <!-- {{ collectInfo.dataUploadCount }} -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ref="taskCollectionTrend" class="content-box-row col-1">
        <div class="col">
          <div class="title">
            任务实例监控 <span class="sub-title">近15天任务实例运行状态监控</span>
          </div>
          <div class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="taskCollectionTrend"
            ></div>
          </div>
        </div>
      </div>
      <div class="content-box-row col-2">
        <div class="col">
          <div class="title"> 采集任务趋势 <span class="sub-title">近7天采集任务趋势</span> </div>
          <div class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="fileTypePercentage"
            ></div>
          </div>
        </div>
        <div class="col">
          <div class="title"> 采集任务状态 <span class="sub-title">近7天采集任务状态</span> </div>
          <div class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="resourceUtilization"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, onBeforeUnmount } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { formartTime, formartTimeDate } from '@/utils/index'
  import * as echarts from 'echarts'
  import overview from '../data-collection-overview/index'
  import { timestampToTime } from '@/const/public.js'
  export default {
    title: 'List',
    components: { overview },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        dataSourceNum: 0,
        dataTaskNum: 0,
        key: 1,
        loading: false,
        tableData: {},
        tableHeight: 436,
        formInline: {
          sourceType: '',
          state: false,
          sourceType2: '',
          time: [],
          keyword: '',
        },
        dataStructureTypeOption: [
          {
            value: 'RUNNING_EXECUTION',
            label: '运行中',
          },
          {
            value: 'SUCCESS',
            label: '成功',
          },
          {
            value: 'FAILURE',
            label: '失败',
          },
        ],
        org_dataSourceType: [],
        org_dataSourceType2: [],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'processName', name: '采集任务名称' },
          { prop: 'dataStructureTypeName', name: '采集数据类型' },
          { prop: 'during', name: '任务运行时长' },
          { prop: 'state', name: '任务状态', slot: 'state' },
          { prop: 'startTime', name: '任务执行时间' },
          { prop: 'dispatch', name: '调度频率' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
      })
      // 环形饼图基础配置
      const monitoringEchartsOption = {
        title: {
          text: '',
          left: 'center',
          textStyle: {
            color: '#666666',
            fontSize: 14,
            fontWeight: '500',
            lineHeight: 20,
          },
        },

        legend: {
          type: 'plain',
          //  orient: 'vertical', //竖直展示，如果横着的话，这个可以直接去掉
          top: '89%',
          bottom: 'center',
          align: 'left',
          itemGap: 10,
          itemWidth: 10, // 设置宽度
          itemHeight: 17, // 设置高度
          icon: 'circle',
          symbolKeepAspect: false,
        },
        tooltip: {
          trigger: 'item', //数据项图形触发
          // formatter: '{a} <br/>{b}: {c}条  {d}%', // 展示格式
          formatter: function (params) {
            var showHtm = ''
            let { seriesName, color, name, value, percent } = params

            showHtm += `
                <div style='color:rgba(0,0,0,0.45)'>${seriesName}</div>
                `

            showHtm += `
              <div style='display:flex;justify-content: space-between;margin-top:5px'>
                <div style='margin-right:10px'>
                  <span style='display:inline-block;margin-right:4px;margin-top:4px;border-radius:10px;width:10px;height:10px;background-color:${color};'></span>
                  <span style='font-weight:400;margin-right:2px'>${name}</span>
                </div>
                <span style='font-weight:500;margin-right:4px'>${value}条</span>
              </div>
              `
            showHtm += `
              <div style='display:flex;justify-content: space-between;margin-top:5px'>
                <div style='margin-right:10px'>
                  <span style='display:inline-block;margin-right:4px;margin-top:4px;border-radius:10px;width:10px;height:10px;background-color:${color};'></span>
                  <span style='font-weight:400;margin-right:2px'>占比</span>
                </div>

                <span style='font-weight:500;'>${percent}%</span>
              </div>
              `
            return showHtm
          },
          textStyle: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: '近7天采集任务状态占比',
            type: 'pie',
            selectedMode: 'single',
            center: ['50%', '40%'], // 将0改成50%，修改圆的内径
            radius: ['45%', '65%'], // 将0改成50%，修改圆的内径
            clockwise: false,
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: function (parames) {
                  return parames.percent + '%'
                },
              },
            },
            data: [
              { value: 65.5, name: '正常', itemStyle: { color: '#73DEB3' } },
              { value: 15.2, name: '重跑', itemStyle: { color: '#73A0FA' } },
              { value: 15.2, name: '失败', itemStyle: { color: '#F75656' } },
            ],
          },
        ],
      }
      // 折线图图基础配置
      const lineChartOption = {
        Animation: true,
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: function (params) {
            var showHtm = ''
            for (var i = 0; i < params.length; i++) {
              //日期
              var time = params[i].name
              //名称
              var name = params[i].seriesName
              //值
              var value = params[i].value
              var color = params[i].color
              if (i == 0) {
                showHtm += `
                <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                `
              }
              showHtm += `
              <div style='display:flex;justify-content: space-between;margin-top:5px'>
                <div style='margin-right:10px'>
                  <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                  <span style='font-weight:400'>${name}</span>
                </div>
                <span style='font-weight:500;'>${value}</span>
              </div>
              `
            }
            return showHtm
          },
          textStyle: {
            fontSize: 12,
            color: '#333',
          },
        },
        color: ['#6E9EFF'], // 设置tooltips展示图标颜色
        legend: {
          itemWidth: 10, //粗细
          itemHeight: 1, //粗细
          data: [
            // {
            //   name: '任务数',
            //   icon: 'rect',
            //   itemStyle: {
            //     color: '#6E9EFF',
            //   },
            // },
          ],
          // data: ['成功任务', '失败任务'],
          // icon: 'circle', //  这个字段控制形状  类型包括 circle 圆形，triangle 三角形，diamond 四边形，arrow 变异三角形，none 无

          itemGap: 50, // 设置间距，
          textStyle: {
            // 图例文字的样式
            color: '#666666',
            fontSize: 12,
          },
          x: 'center', //左（left）、右（right）、居中（center）
          y: 'bottom', //上（top）、下（bottom）、居中（center）
        },
        toolbox: {
          // feature: {
          //   saveAsImage: {},
          // },
        },
        grid: {
          left: 20,
          right: 40,
          bottom: 30,
          top: 40,
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: [],
          },
        ],
        yAxis: [
          {
            type: 'value',
            offset: 10,
          },
        ],
        series: [
          {
            name: '任务数',
            type: 'line',
            // stack: 'Total', //是否堆叠面积
            lineStyle: {
              color: '#6E9EFF',
            },
            // emphasis: {
            //   focus: 'series',
            // },
            smooth: true,
            symbol: 'circle',
            symbolSize: 7,
            data: [],
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 1,
                  color: '#fff', // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: '#F0F7FFFF', // 100% 处的颜色
                },
              ]), //背景渐变色
            },
          },
        ],
      }
      let mylineChart = null
      let myCircularPieChart = null
      let myChart = null
      const methods = {
        totalCount() {
          // 获取可用数据源
          api.dataManagement.getSourceCount().then((res) => {
            let { success, data } = res
            if (success) {
              state.dataSourceNum = data
            }
          })
          // 获取采集任务综述-运行中个数
          api.dataManagement.collectTaskCount().then((res) => {
            let { success, data } = res
            if (success) {
              state.dataTaskNum = data
            }
          })
          let timestamp = new Date().getTime() // 当前日期时间戳
          let endDate = timestampToTime(timestamp - 1 * 24 * 60 * 60 * 1000),
            startDate = timestampToTime(timestamp - 15 * 24 * 60 * 60 * 1000)

          let chartDom = document.getElementById('taskCollectionTrend')
          myChart = echarts.init(chartDom)

          myChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
            // spinnerRadius: 20,
            // fontSize: 20,
          })

          // 获取任务实例监控
          api.dataManagement
            .collectJobInstance({ startDate: startDate, endDate: endDate })
            .then((res) => {
              let { success, data } = res
              myChart?.hideLoading()
              if (success) {
                methods.initEcharts(data)
              }
            })
            .catch(() => {
              myChart?.hideLoading()
            })
        },
        initEcharts(arr) {
          // chartDom.setAttribute('_echarts_instance_', '')
          if (!myChart) {
            let chartDom = document.getElementById('taskCollectionTrend')
            myChart = echarts.init(chartDom)
          }

          let xAxisData = [],
            yAxisSuccessData = [],
            yAxisFailData = []
          // 获取x轴数据
          xAxisData = arr.successList.map((item) => {
            item.day = item.date.slice(5, 10).replace(/-/g, '.')
            return item.day
          })
          // 获取y轴成功数据
          yAxisSuccessData = arr.successList.map((item) => item.count)
          // 获取y轴失败数据
          yAxisFailData = arr.failList.map((item) => item.count)
          let option
          option = {
            Animation: true,
            title: {
              text: '',
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                label: {
                  backgroundColor: '#6a7985',
                },
              },
              formatter: function (params) {
                var showHtm = ''
                for (var i = 0; i < params.length; i++) {
                  //日期
                  var time = params[i].name
                  //名称
                  var name = params[i].seriesName
                  //值
                  var value = params[i].value
                  var color = params[i].color
                  if (i == 0) {
                    showHtm += `
                  <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                  `
                  }
                  showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                    <span style='font-weight:400'>${name}</span>
                  </div>
                  <span style='font-weight:500;'>${value}</span>
                </div>
                `
                }
                return showHtm
              },
              textStyle: {
                fontSize: 12,
                color: '#333',
              },
            },
            color: ['#5AD8A6', '#EC9B6D'], // 设置tooltips展示图标颜色
            legend: {
              itemWidth: 10, //粗细
              itemHeight: 1, //粗细
              data: [
                {
                  name: '成功任务',
                  icon: 'rect',
                  itemStyle: {
                    color: '#5AD8A6',
                  },
                },
                {
                  name: '失败任务',
                  icon: 'rect',
                  itemStyle: {
                    color: '#EC9B6D', // 设置图例圆圈颜色
                  },
                },
              ],
              // data: ['成功任务', '失败任务'],
              // icon: 'circle', //  这个字段控制形状  类型包括 circle 圆形，triangle 三角形，diamond 四边形，arrow 变异三角形，none 无

              itemGap: 50, // 设置间距，
              textStyle: {
                // 图例文字的样式
                color: '#666666',
                fontSize: 12,
              },
              x: 'center', //左（left）、右（right）、居中（center）
              y: 'bottom', //上（top）、下（bottom）、居中（center）
            },
            toolbox: {
              // feature: {
              //   saveAsImage: {},
              // },
            },
            grid: {
              left: 20,
              right: 50,
              bottom: 40,
              top: 20,
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: xAxisData,
              },
            ],
            yAxis: [
              {
                type: 'value',
                offset: 10,
              },
            ],
            series: [
              {
                name: '成功任务',
                type: 'line',
                // stack: 'Total', //是否堆叠面积
                lineStyle: {
                  color: '#5AD8A6',
                },
                // emphasis: {
                //   focus: 'series',
                // },
                smooth: true,
                symbol: 'circle',
                symbolSize: 7,
                data: yAxisSuccessData,
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 1,
                      color: '#fff', // 0% 处的颜色
                    },
                    {
                      offset: 0,
                      color: '#EBFFF7FF', // 100% 处的颜色
                    },
                  ]), //背景渐变色
                },
              },
              {
                name: '失败任务',
                type: 'line',
                // stack: 'Total',

                // emphasis: {
                //   focus: 'series',
                // },
                symbol: 'circle',
                symbolSize: 7,
                smooth: true,
                data: yAxisFailData,
                lineStyle: {
                  color: '#EC9B6D',
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 1,
                      color: '#ffffff', // 0% 处的颜色
                    },
                    {
                      offset: 0,
                      color: '#FDEFE7FF', // 100% 处的颜色
                    },
                  ]), //背景渐变色
                },
              },
            ],
          }
          document.getElementById('taskCollectionTrend').setAttribute('_echarts_instance_', '')

          option && myChart.setOption(option)
        },
        dataSourceStateChinese(type) {
          let result = state.dataStructureTypeOption.filter((item) => item.value === type)
          return result[0]['label']
        },

        initData() {
          window.onresize = () => {
            methods.echartsResize()
          }
          //一进来默认展示7天的数据
          state.formInline.time = [
            new Date(methods.getSomeDayAgoTime(6)),
            new Date(methods.getSomeDayAgoTime(0)),
          ]
          methods.getMonitorStatistic()
          methods.initTable()
          methods.totalCount()
        },
        //统计接口
        getMonitorStatistic() {
          if (!mylineChart) {
            let chartDom = document.getElementById('fileTypePercentage')
            chartDom.setAttribute('_echarts_instance_', '')
            mylineChart = echarts.init(chartDom)
          }

          if (!myCircularPieChart) {
            let chartDom = document.getElementById('resourceUtilization')
            chartDom.setAttribute('_echarts_instance_', '')
            myCircularPieChart = echarts.init(chartDom)
          }
          mylineChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })
          myCircularPieChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })

          api.dataManagement
            .getMonitorStatistic({
              startDate: methods.getSomeDayAgoTime(6),
              endDate: methods.getSomeDayAgoTime(0),
              projectCode: state.formInline.projectCode,
            })
            .then((res) => {
              let { data, success } = res
              mylineChart?.hideLoading()
              myCircularPieChart?.hideLoading()
              if (success) {
                // 任务趋势-折线图
                let { trend, total, success, repeat, fail } = data
                let _taskData = []
                let _taskXData = []
                trend.forEach((item) => {
                  _taskXData.push(item.title)
                  _taskData.push(item.value)
                })

                lineChartOption.xAxis[0].data = _taskXData || []
                lineChartOption.series[0].data = _taskData || []
                document.getElementById('fileTypePercentage').setAttribute('_echarts_instance_', '')
                lineChartOption && mylineChart.setOption(lineChartOption)
                // 任务状态-折线图

                total = total === 0 ? 1 : total
                monitoringEchartsOption.series[0].data[0].value = success
                monitoringEchartsOption.series[0].data[1].value = repeat
                monitoringEchartsOption.series[0].data[2].value = fail
                monitoringEchartsOption && myCircularPieChart.setOption(monitoringEchartsOption)
              }
            })
            .catch(() => {
              mylineChart?.hideLoading()
              myCircularPieChart?.hideLoading()
            })
        },
        //获取指定几天前的日期
        getSomeDayAgoTime(someDay = 1, split = '-') {
          let times = new Date().getTime() - someDay * 24 * 60 * 60 * 1000
          let _someDayAgoTime = new Date(times)

          let year = _someDayAgoTime.getFullYear()
          let month =
            _someDayAgoTime.getMonth() + 1 < 10
              ? '0' + (_someDayAgoTime.getMonth() + 1)
              : _someDayAgoTime.getMonth() + 1
          let day =
            _someDayAgoTime.getDate() < 10
              ? '0' + _someDayAgoTime.getDate()
              : _someDayAgoTime.getDate()

          return year + split + month + split + day
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 152 - 50 - 301
        },
        // 初始化form
        initTable(str) {
          state.key++
          if (str === 'filter') {
            state.pagination.currentPage = 1
          }
          let data = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              state: state.formInline.state || null,
              name: state.formInline.keyword || null,
              projectCode: state.formInline.projectCode,
              startDate: state.formInline.time?.[0]
                ? formartTimeDate(state.formInline.time?.[0], '-')
                : null,
              endDate: state.formInline.time?.[1]
                ? formartTimeDate(state.formInline.time?.[1], '-')
                : null,
            },
          }
          if (state.formInline.projectCode) {
            state.loading = true
            api.dataManagement
              .monitorInstanceList(data)
              .then((res) => {
                state.loading = false
                // 新增序号属性
                res.data.list.map((item, index) => {
                  let period = ''
                  let rateTime = item.rateTime ? item.rateTime : ''
                  switch (item.period) {
                    case 'hour':
                      period = '间隔小时' + item.extent
                      break
                    case 'day':
                      period = '每天'
                      break
                    case 'week':
                      period = '每周' + item.extent
                      break
                    case 'month':
                      period = '每月' + item.extent + '日'
                      break
                    default:
                      period = item.cron || null
                  }
                  if (item.period === 'hour') {
                    return Object.assign(item, {
                      number: index + 1,
                      dispatch: period ? period + ' | ' + item.startTime.slice(11) : '',
                    })
                  } else {
                    return Object.assign(item, {
                      number: index + 1,
                      dispatch: item.cron ? period : period ? period + ' | ' + rateTime : '',
                      // dispatch: period ? period + ' | ' + rateTime : '',
                    })
                  }
                })
                state.tableData = res.data
              })
              .catch(() => {
                state.loading = false
              })
          } else {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
            state.tableData = {}
          }
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看
        seeDetails(editor) {
          let { row } = editor
          router.push({ name: 'instanceCheck', query: { id: row.id } })
        },
        // 屏幕onresize echarts重新绘制
        echartsResize() {
          //echarts重新绘制
          if (mylineChart) {
            mylineChart.resize()
          }
          if (myCircularPieChart) {
            myCircularPieChart.resize()
          }
        },
      }

      methods.setTableHeight()
      onBeforeUnmount(() => {
        window.onresize = null

        if (mylineChart) {
          mylineChart.dispose() //销毁
        }
        if (myCircularPieChart) {
          myCircularPieChart.dispose() //销毁
        }
      })

      onMounted(() => {
        const { projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        methods.initData()
      })

      return {
        state,
        buttonAuthList,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .container {
    width: 100%;
    height: calc(100vh - 50px);
    padding: 16px;
    overflow-y: auto;
    font-family: PingFangSC-Semibold, PingFang SC;
    background-color: #eee;
    .content-box {
      &-row {
        & + .content-box-row {
          margin-top: 16px;
        }

        .col {
          box-sizing: border-box;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
          overflow: hidden;
          .title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            height: 52px;
            padding: 0 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            border-bottom: 1px solid #c5d0ea;

            .illustrate {
              margin-left: 8px;
              color: #8091b7;
              &:hover {
                color: $themeBlue;
              }
            }
            .sub-title {
              color: var(---, rgba(0, 0, 0, 0.55));

              font-family: 'Source Han Sans CN';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
            }

            &:before {
              position: absolute;
              top: 17px;
              left: 0;
              width: 4px;
              height: 18px;
              background: var(
                --Radial,
                radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
              );
              border-radius: 0 4px 4px 0;
              content: '';
            }
            span {
              margin-left: 8px;
              color: rgba(0, 0, 0, 0.55);
              font-weight: normal;
              font-size: 14px;
            }
          }
          .col-content {
            padding: 16px;
            .canvas {
              display: flex;
              height: 256px;
              flex-direction: column;
              align-items: center;
              gap: 16px;
            }
            .ranking {
              padding: 8px 24px;
              display: block;
              &-update-time {
                color: var(---, rgba(0, 0, 0, 0.55));

                font-family: 'Source Han Sans CN';
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
              }
              &-list {
                height: calc(100% - 20px);
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                &-item {
                  display: grid;
                  grid-template-columns: 20px 77px 1fr 34px;
                  align-items: center;
                  color: var(----, rgba(0, 0, 0, 0.75));
                  font-family: 'Source Han Sans CN';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px;
                  &-index {
                    display: flex;
                    width: 100%;
                    height: 16px;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    border-radius: 13px;

                    color: var(--100, #fff);
                    text-align: right;
                    font-family: 'Source Han Sans CN';
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 20px;
                    margin-right: 4px;
                  }
                  &-name {
                    padding: 0 8px 0 4px;
                    color: var(--100, rgba(0, 0, 0, 0.75));
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  &-content {
                    display: flex;
                    align-items: center;
                    border-radius: 4px;
                    overflow: hidden;
                    // 动画
                    transition: all 0.3s ease-in-out;
                  }
                  &-count {
                    color: var(----, rgba(0, 0, 0, 0.75));
                    text-align: center;
                    font-family: 'PingFang SC';
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16px; /* 133.333% */
                  }
                  &:nth-child(1) {
                    color: #f63838;
                    .ranking-list-item-index {
                      background: #f63838;
                    }
                  }
                  &:nth-child(2) {
                    color: #ff7d00;
                    .ranking-list-item-index {
                      background: #ff7d00;
                    }
                  }
                  &:nth-child(3) {
                    color: #04c495;
                    .ranking-list-item-index {
                      background: #04c495;
                    }
                  }
                  &:nth-child(4) {
                    color: #447dfd;
                    .ranking-list-item-index {
                      background: #447dfd;
                    }
                  }
                }
              }
            }
            &-row {
              display: flex;
              flex-wrap: wrap;
              &-item {
                display: flex;
                padding: 16px;
                height: 64px;
                // height: 10.7vh;
                align-items: center;
                flex: 1 0 0;
                border-radius: 6px;
                position: relative;
                &::before {
                  position: absolute;
                  display: block;
                  margin: auto;
                  top: 0;
                  bottom: 0;
                  right: 16px;
                  width: 32px;
                  height: 32px;
                }
                &:nth-child(1) {
                  background: var(---, #2a99ff);
                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI4LjMwNzggOC4yMDAzN0MyNy42MjM5IDguNjM2NTkgMjYuODEyIDkuMDE3NTMgMjUuOTE1IDkuMzQzNzFDMjMuMzI5NCAxMC4yODM5IDE5LjgyMzkgMTAuODQ1OSAxNi4wMDAxIDEwLjg0NTlDMTIuMTc2MyAxMC44NDU5IDguNjcwNyAxMC4yODM5IDYuMDg1MTkgOS4zNDM3MUM1LjE4ODE5IDkuMDE3NTMgNC4zNzYyIDguNjM2NTkgMy42OTIzOCA4LjIwMDM3TDMuNjkyMzkgMTUuNjgzOEM0LjEwOTA1IDE2LjUyNDkgNS4xMDg1MyAxNy40MjI2IDYuODQwNzQgMTguMjFDOS4xMzMzIDE5LjI1MjEgMTIuMzcwNyAxOS45MjI4IDE2LjAwMDEgMTkuOTIyOEMxOS42Mjk1IDE5LjkyMjggMjIuODY2OCAxOS4yNTIxIDI1LjE1OTQgMTguMjFDMjYuODkxNiAxNy40MjI2IDI3Ljg5MTEgMTYuNTI0OSAyOC4zMDc3IDE1LjY4MzlMMjguMzA3OCA4LjIwMDM3WiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yIi8+CjxwYXRoIGQ9Ik0yOC4zMDc3IDE4LjY1OTRDMjcuNjI4NSAxOS4xODY5IDI2LjgzNTIgMTkuNjQ1MiAyNS45ODcgMjAuMDMwN0MyMy4zNzk2IDIxLjIxNTkgMTkuODQ3OCAyMS45MjI4IDE2LjAwMDEgMjEuOTIyOEMxMi4xNTI0IDIxLjkyMjggOC42MjA1MyAyMS4yMTU5IDYuMDEzMTMgMjAuMDMwN0M1LjE2NDk1IDE5LjY0NTIgNC4zNzE1OCAxOS4xODY5IDMuNjkyMzkgMTguNjU5NEwzLjY5MjQgMjYuMjkzNEMzLjY5MjQgMjguNzY1MiA5LjIwMjcxIDMwLjc2ODkgMTYgMzAuNzY4OUMyMi43OTc0IDMwLjc2ODkgMjguMzA3NyAyOC43NjUyIDI4LjMwNzcgMjYuMjkzNEwyOC4zMDc3IDE4LjY1OTRaIiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjIiLz4KPHBhdGggZD0iTTI4LjMwNzggNS41MzgxNkMyOC4zMDc4IDUuNTQ1MjggMjguMzA3NyA1LjU1MjQgMjguMzA3NiA1LjU1OTUyQzI4LjE5NzcgNS43MTg3NiAyOC4wNDExIDUuODkzMDMgMjcuODIyNyA2LjA4MDc3QzI3LjI2MjMgNi41NjI1OSAyNi4zOTI4IDcuMDQxODIgMjUuMjMxNSA3LjQ2NDEyQzIyLjkxNyA4LjMwNTc0IDE5LjY1MzQgOC44NDU4NSAxNi4wMDAxIDguODQ1ODVDMTIuMzQ2OCA4Ljg0NTg1IDkuMDgzMTMgOC4zMDU3NCA2Ljc2ODY4IDcuNDY0MTJDNS42MDczNCA3LjA0MTgyIDQuNzM3ODkgNi41NjI1OSA0LjE3NzQ2IDYuMDgwNzdDMy45NTkwOCA1Ljg5MzAzIDMuODAyNDYgNS43MTg3NiAzLjY5MjUzIDUuNTU5NTJDMy42OTI0MyA1LjU1MjQgMy42OTIzOCA1LjU0NTI4IDMuNjkyMzggNS41MzgxNkMzLjY5MjM4IDMuMTU5MDkgOS4yMDI3MiAxLjIzMDQ3IDE2LjAwMDEgMS4yMzA0N0MyMi43OTc0IDEuMjMwNDcgMjguMzA3OCAzLjE1OTA5IDI4LjMwNzggNS41MzgxNloiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMiIvPgo8L3N2Zz4K');
                  }
                }
                &:nth-child(2) {
                  background: var(---, #09bfa1);
                  &::before {
                    content: url('data:image/svg+xml;base64,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');
                  }
                }
                &:nth-child(3) {
                  background: var(---, #ff9e42);
                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMyAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjMyMDMgMi4wNDAwNEg1LjM2MDM1QzMuNzYwMzUgMi4wNDAwNCAyLjQyNyAzLjM3MzM4IDIuNDI3IDQuOTczMzhWMjcuMDI2N0MyLjQyNyAyOC42MjY3IDMuNzYwMzUgMjkuOTYgNS4zNjAzNSAyOS45NkgyNC40NTM3QzI0Ljg4MDMgMjkuOTYgMjUuMjUzNyAyOS41ODY3IDI1LjI1MzcgMjkuMTZWNS4wMjY3M0MyNS4yNTM3IDMuMzczMzggMjMuOTIwMyAyLjA0MDA0IDIyLjMyMDMgMi4wNDAwNFpNNy40OTM2NiA4LjQyNjczSDEzLjE0N0MxMy43MzM3IDguNDI2NzMgMTQuMjEzNyA4LjkwNjczIDE0LjIxMzcgOS40OTMzOEMxNC4yMTM3IDEwLjA4IDEzLjczMzcgMTAuNTYgMTMuMTQ3IDEwLjU2SDcuNDkzNjZDNi45MDcgMTAuNTYgNi40MjcgMTAuMDggNi40MjcgOS40OTMzOEM2LjQyNyA4LjkwNjczIDYuOTA3IDguNDI2NzMgNy40OTM2NiA4LjQyNjczWk0xNS45NzM3IDE1LjUySDcuNDkzNjZDNi45MDcgMTUuNTIgNi40MjcgMTUuMDQgNi40MjcgMTQuNDUzNEM2LjQyNyAxMy44NjY3IDYuOTA3IDEzLjM4NjcgNy40OTM2NiAxMy4zODY3SDE1Ljk3MzdDMTYuNTYwMyAxMy4zODY3IDE3LjA0MDMgMTMuODY2NyAxNy4wNDAzIDE0LjQ1MzRDMTcuMDQwMyAxNS4wNCAxNi41NjAzIDE1LjUyIDE1Ljk3MzcgMTUuNTJaTTI4LjY2NyAxMi43MzM0SDI3LjM4N1YyOS45MDY3SDI3Ljk3MzdDMjkuNTczNyAyOS45MDY3IDMwLjkwNyAyOC41NzM0IDMwLjkwNyAyNi45NzM0VjE0Ljk3MzRDMzAuOTA3IDEzLjc0NjcgMjkuODkzNyAxMi43MzM0IDI4LjY2NyAxMi43MzM0WiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yIi8+Cjwvc3ZnPgo=');
                  }
                }
                &-title {
                  color: var(--100, #fff);

                  font-family: 'Source Han Sans CN';
                  font-size: 18px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 26px;
                  &[unit-text]::after {
                    content: attr(unit-text);
                    color: var(--100, #fff);

                    font-family: 'Source Han Sans CN';
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;
                  }
                }
                &-content {
                  margin-left: 22%;
                  color: var(--100, #fff);

                  /* 常用/r500/h5 */
                  font-family: 'Source Han Sans CN';
                  font-size: 24px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 32px;
                }
                &-content-trend {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  align-self: stretch;
                  color: var(--100, #fff);
                  font-family: 'Source Han Sans CN';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px;
                  &-item {
                    display: flex;
                    align-items: center;
                    gap: 2px;
                    &-content-icon {
                      vertical-align: text-bottom;
                    }
                  }
                  .yy-icon {
                    width: 14px;
                    height: 14px;
                  }
                }
                .illustrate {
                  position: absolute;
                  top: 20px;
                  right: 16px;
                  margin-left: 8px;
                  color: #8091b7;
                  &:hover {
                    color: $themeBlue;
                  }
                }
              }
              .col-content-row-item + .col-content-row-item {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }
    .col-2 {
      display: grid;
      grid-template-columns: 66fr 34fr;
      gap: 16px;
      .col > .col-content {
        padding: 24px 0;
      }
    }
    .col-1 {
      .col > .col-content {
        padding: 0;
      }
    }
  }
  .refresh {
    margin-left: auto;
  }
  .icons {
    position: relative;
    & + .icons {
      margin-left: 8px;
    }
    .yy-icon {
      position: relative;
      cursor: pointer;
      color: #8091b7;
      z-index: 2;
      &:hover {
        color: $themeBlue;
      }
    }
    &:hover {
      &::before {
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #e3ecff;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 5px;
      }
    }
  }
</style>

<style lang="scss">
  @import '@/styles/variables.scss';
  .quality-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: center;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 72px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
  .task-collection-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 32px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
</style>
