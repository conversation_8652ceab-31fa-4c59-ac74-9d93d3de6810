<template>
  <div class="container" v-loading="state.isLoading">
    <div class="cf-tools">
      <div class="row">
        <div class="col text-label">
          分类名称：
          <n-input v-model="state.formInline.keyword" placeholder="请输入" />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </div>
    <div class="white-box nc-m-t-10">
      <div class="row nc-p-8">
        <n-button @click="onCreate" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          新建分类
        </n-button>
      </div>
      <div class="table-content">
        <div ref="scrollRef" class="table-list scroll-bar-style">
          <div
            class="table-list-container"
            :style="state.tableData.list.length === 0 ? 'height:36px' : ''"
          >
            <div class="header-div-row">
              <div class="div-col">分类名称</div>
              <div class="div-col">编号规则</div>
              <div class="div-col">创建人</div>
              <div class="div-col">创建时间</div>
              <div class="header-div-row-operate">操作</div>
            </div>
            <n-tree
              v-if="state.tableData.list.length > 0"
              ref="treeDom"
              :data="state.tableData.list"
              :key="state.treeKey"
            >
              <template #content="{ nodeData }">
                <div class="docs-div-row">
                  <div class="div-col" :title="nodeData.name">{{ nodeData.name || '--' }}</div>
                  <div class="div-col" :title="nodeData.codeRuleName">{{
                    nodeData.codeRuleName || '--'
                  }}</div>
                  <div class="div-col" :title="nodeData.createByName">{{
                    nodeData.createByName || '--'
                  }}</div>
                  <div class="div-col" :title="nodeData.createTime">{{
                    nodeData.createTime || '--'
                  }}</div>
                  <div class="docs-div-row-operate">
                    <n-button variant="text" color="primary" @click="onEditManage(nodeData)"
                      >编辑</n-button
                    >
                    <n-button variant="text" color="primary" @click="onDelManage(nodeData)"
                      >删除</n-button
                    >
                    <n-button variant="text" color="primary" @click="onAuthorization(nodeData)"
                      >授权</n-button
                    >
                  </div>
                </div>
              </template>
              <template #icon="{ nodeData, toggleNode }">
                <span
                  v-if="nodeData.isLeaf"
                  class="nancalui-tree-node__indent"
                  :style="{
                    width: '22px',
                    marginRight: '6px',
                  }"
                ></span>
                <span
                  v-else
                  @click="
                    (event) => {
                      event.stopPropagation()
                      toggleNode(nodeData)
                      treeChange(nodeData)
                    }
                  "
                >
                  <SvgIcon
                    v-if="nodeData.expanded"
                    class="nancalui-tree-switch"
                    icon="tree-contract-new"
                  />
                  <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
                </span>
              </template>
            </n-tree>
          </div>
          <div v-if="state.tableData.list.length === 0 && state.isLoading" class="empty">
            <img class="pic" src="@/assets/img/table-no-content.png" />
            <div class="empty-word">暂⽆数据</div>
          </div>
        </div>
        <div class="nancalui-table-page table-content-pagination">
          <n-pagination
            size="sm"
            :total="state.pagination.total"
            v-model:pageSize="state.pagination.pageSize"
            v-model:pageIndex="state.pagination.currentPage"
            :can-view-total="true"
            :can-change-page-size="true"
            :can-jump-page="true"
            :page-size-options="[10, 20, 50, 100]"
            @page-index-change="pageIndexChange"
            @page-size-change="pageSizeChange"
          />
        </div>
        <!-- <div class="page-footer" v-if="state.pagination.total">
          <el-pagination
            background
            layout="prev,pager,next,jumper,total"
            :current-page="state.pagination.currentPage"
            :page-size="state.pagination.pageSize"
            :total="state.pagination.total"
            @current-change="pageIndexChange"
          >
          </el-pagination>
        </div>
        <div
          v-html="
            `
          <style >.el-pagination__jump::after {
          content: '显示${(state.pagination.currentPage - 1) * state.pagination.pageSize + 1}到${
              (state.pagination.currentPage - 1) * state.pagination.pageSize +
              state.tableData.list.length
            }条,';
          }
          </style>`
          "
        ></div> -->
      </div>
    </div>
    <n-modal
      v-model="state.isShow"
      :title="(state.treeForm.id ? '编辑' : '新建') + '非结构化数据分类'"
      class="largeDialog docClassification"
      width="560px"
      :close-on-click-overlay="false"
      @close="state.isShow = false"
    >
      <n-form
        ref="treeFormRef"
        class="form__label_font14"
        :data="state.treeForm"
        :rules="state.treeFormRules"
        label-align="end"
        label-width="80px"
      >
        <n-form-item label="分类名称：" field="name">
          <n-input
            v-model="state.treeForm.name"
            :maxLength="200"
            placeholder="请输入"
            @blur="setDisabled(state.classifyOpts)"
          />
        </n-form-item>
        <n-form-item label="上级分类：" field="pid">
          <TreeSelect
            v-model="state.treeForm.pid"
            :data="state.classifyOpts"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择数据分类"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="编号规则：" field="codeRuleId">
          <n-select
            v-model="state.treeForm.codeRuleId"
            :allow-clear="true"
            filter
            placeholder="请选择"
          >
            <n-option
              class="rule-opts"
              v-for="item in state.ruleList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button @click.prevent="state.isShow = false">取 消</n-button>
          <n-button v-loading="state.editLoading" variant="solid" @click.prevent="saveTreeFn"
            >确 定</n-button
          >
        </div>
      </template>
    </n-modal>
  </div>
</template>
<script setup>
  import { reactive, onMounted, nextTick, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api'
  import TreeSelect from '@/components/cfTreeSelect'
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const treeFormRef = ref()
  const state = reactive({
    tableHeadTitles: [
      { prop: 'name', name: '分类名称' },
      { prop: 'codeRuleName', name: '编号规则' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间' },
    ],
    tableHeight: 651,
    isLoading: false,
    formInline: {},
    searchData: { keyword: '' },
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    tableData: { list: [] },
    treeKey: 0,
    isShow: false,
    editLoading: false,
    treeForm: {
      id: null,
      name: null,
      pid: null,
      codeRuleId: null,
    },
    treeFormRules: {
      name: [
        {
          required: true,
          message: '请输入分类名称',
          trigger: 'blur',
        },
      ],
      codeRuleId: [
        {
          required: true,
          message: '请选择编号规则',
          type: 'number',
          trigger: 'blur',
        },
      ],
    },
    classifyOpts: [
      {
        name: '一级 1',
        id: '1',
        children: [
          {
            name: '二级 1-1',
            id: '1-1',
            children: [
              {
                name: '三级 1-1-1',
                id: '1-1-1',
              },
            ],
          },
        ],
      },
      {
        name: '一级 2',
        id: '2',
        children: [
          {
            name: '二级 2-1',
            id: '2-1',
            children: [
              {
                name: '三级 2-1-1',
                id: '2-1-1',
              },
            ],
          },
          {
            name: '二级 2-2',
            id: '2-2',
            children: [],
          },
        ],
      },
      {
        name: '一级 3',
        id: '3',
        children: [],
      },
      {
        name: '一级 4',
        id: '4',
        children: [],
      },
    ],
    ruleList: [],
    expandedKeys: [],
    editId: '',
  })
  function getRuleCodeList() {
    api.documentManage.getRuleCodeList().then((res) => {
      if (res.success && res.data && res.data.length > 0) {
        state.ruleList = []
        res.data.forEach((rule) =>
          state.ruleList.push({ name: rule.codeRuleName, id: rule.codeRuleId }),
        )
      }
    })
  }
  function getClassifyTreeList() {
    return api.documentManage.getClassifyTreeList()
  }

  function resizeFun() {
    // 窗口变化执行的操作
    let containerNode = document.querySelector('.container')
    state.tableHeight = containerNode.offsetHeight - 210
  }
  function tablePageChange(data) {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    localStorage.setItem('pageSize', data.pageSize)
    onSearch(false)
  }

  // 授权
  function onAuthorization(nodeData) {
    router.push({
      name: 'docClassifyAuthorizationList',
      query: {
        title: nodeData.name,
        id: nodeData.id,
        pagination: JSON.stringify(state.pagination),
      },
    })
  }
  // 删除
  function onDelManage(nodeData) {
    if (nodeData.isLeaf) {
      api.documentManage.deleteClassify({ kind: 'check', id: nodeData.id }).then((res) => {
        if (res.success) {
          proxy.$MessageBoxService.open({
            title: '是否确认该条分类',
            content: '删除后将不可恢复',
            save: () => {
              api.documentManage
                .deleteClassify({ kind: 'execute', id: nodeData.id })
                .then((res) => {
                  if (res.success) {
                    state.tableData.list = updateTree(state.tableData.list, null, { ...nodeData })
                    ElNotification({
                      title: '提示',
                      message: '操作成功！',
                      type: 'success',
                    })
                  }
                })
            },
          })
        }
      })
    } else {
      ElNotification({
        title: '提示',
        message: '该分类下存在子分类!',
        type: 'warning',
      })
    }
  }
  // 新建
  async function onCreate() {
    state.editId = null
    getRuleCodeList()
    const res = await getClassifyTreeList()
    if (res.success) {
      state.classifyOpts = res.data || []
      state.isShow = true
    }
    state.treeForm = {
      id: null,
      name: null,
      pid: null,
      codeRuleId: null,
    }
  }
  // 编辑
  async function onEditManage(nodeData) {
    state.editId = nodeData.id
    getRuleCodeList()
    const res = await getClassifyTreeList()
    if (res.success) {
      state.classifyOpts = res.data || []
      state.isShow = true
    }
    state.treeForm = {
      id: state.editId,
      name: nodeData.name,
      pid: nodeData.pid || null,
      codeRuleId: nodeData.codeRuleId || null,
    }
  }
  // 当前名称不能作为父级
  function setDisabled(data) {
    data.forEach((item) => {
      if (item.name === state.treeForm.name) {
        item.disabled = true
        if (item.children?.length) setDisabled(item.children)
      }
    })
  }
  //确认
  function saveTreeFn() {
    treeFormRef.value.validate((val) => {
      if (val) {
        let data = { ...state.treeForm }
        let url = 'addClassifyTree'
        if (data.id) {
          url = 'updateClassifyTree'
        }
        state.editLoading = true
        api.documentManage[url](data)
          .then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '保存成功！',
                type: 'success',
              })
              state.isShow = false
              // if (data.id) {
              //   //编辑操作先不更新视图，本地操作数据更新视图
              //  state.tableData.list = updateTree(state.tableData.list,data.id)
              // }else{
              onSearch(false)
              // }
            }
          })
          .finally(() => {
            state.editLoading = false
          })
      }
    })
  }
  // 重置
  function resetFn() {
    state.formInline = {}
    state.searchData.keyword = ''
    onSearch()
  }
  function pageSizeChange(val) {
    state.pagination.pageSize = val
    onSearch(true)
  }
  function pageIndexChange(val) {
    state.pagination.currentPage = val
    onSearch(false)
  }
  const treeDom = ref(null)
  // 展开或收缩树
  function treeChange(node) {
    if (node.expanded) {
      //记录展开的id
      state.expandedKeys.push(node.id)
      state.expandedKeys = Array.from(new Set(state.expandedKeys))
      // state.expandedKeys = state.expandedKeys.push(node.id).filter((item, index) =>  state.expandedKeys.indexOf(item) !== index)
      if (!node.children || node.children.length == 0) {
        onSearch(false, node.id)
      }
    } else {
      //更新展开id集
      state.expandedKeys = state.expandedKeys.filter((item) => item !== node.id)
    }
    // state.treeKey++
  }
  // 循环修改
  function updateTree(arr, updateID = null, addChild = null) {
    arr.forEach((val, index) => {
      if (state.expandedKeys.includes(val.id)) {
        val.expanded = true
      } else {
        val.expanded = false
      }
      if (updateID) {
        //编辑
        if (val.id === updateID) {
          val.name = state.treeForm.name
          val.pid = state.treeForm.pid
          val.codeRuleId = state.treeForm.codeRuleId
        }
      }
      if (addChild) {
        if (Array.isArray(addChild)) {
          if (addChild.some((childItm) => childItm.pid === val.id)) {
            val.children = [...addChild]
          }
        } else {
          if (addChild.id === val.id) {
            arr.splice(index, 1)
          }
        }
      }
      if (val.children) {
        // val.isLeaf = false
        updateTree(val.children, updateID, addChild)
      }
    })
    return arr
  }
  //添加子层级
  function addChild(arr, domainId, childData) {
    arr.forEach((val) => {
      if (val.id === domainId) {
        val.children = [...childData]
      } else {
        addChild(val.children, domainId, childData)
      }
    })
    return arr
  }
  function startSearch() {
    state.searchData.keyword = state.formInline.keyword
    onSearch(true)
  }
  // 查询
  function onSearch(init = true, domainId = null) {
    if (init) {
      state.pagination.pageNum = 1
      state.pagination.currentPage = 1
    }
    let params = {
      pageSize: state.pagination.pageSize,
      pageNum: state.pagination.currentPage,
      kind: domainId ? 'child' : 'root',
      condition: {},
    }
    if (state.searchData.keyword) {
      params.condition.name = state.searchData.keyword
    }
    if (domainId) {
      params.condition.pid = domainId
    } else {
      state.expandedKeys = []
    }
    state.isLoading = true
    api.documentManage
      .getClassifyList(params)
      .then((res) => {
        if (res.success) {
          if (!domainId) {
            state.pagination.total = res.data.total
            state.tableData.list = res.data.list
          }
          state.tableData.list = updateTree(
            state.tableData.list,
            null,
            domainId ? res.data.list : null,
          )
        }
      })
      .finally(() => {
        setTimeout(() => (state.isLoading = false), 500)
      })
  }

  onMounted(() => {
    if (router.currentRoute.value.query.pagination) {
      state.pagination = JSON.parse(router.currentRoute.value.query.pagination)
      onSearch(false)
    } else {
      onSearch()
    }
    nextTick(() => {
      resizeFun()
    })
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  $paginationChildHeight: 28px;
  .white-box {
    height: calc(100% - 62px);

    .table-content {
      display: inline-block;
      width: 100%;
      height: calc(100% - 48px);
      vertical-align: top;
      background-color: #fff;
      .nancalui-tree-switch {
        margin: 0 6px 0 24px;
        font-size: 16px;
      }

      &-title {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 48px;
        .upload-demo {
          .upload-content {
            display: flex;
            align-items: center;
            :deep(.up-load-box) {
              margin-left: 12px;
              .button-content {
                color: #000;
              }
            }
          }
        }
      }

      &-pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 10px 16px;
        background-color: #fff;
        border-top: 1px solid var(---, #dcdfe6);
        &-total {
          color: rgba(0, 0, 0, 0.75);
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;

          span {
            color: $themeBlue;
          }
        }
      }
      :deep(.el-pagination) {
        justify-content: flex-end;
        .el-pagination__total,
        .el-pagination__goto,
        .el-pagination__classifier {
          font-size: 12px;
        }

        button {
          min-width: $paginationChildHeight;
          height: $paginationChildHeight;
          font-size: 12px;
        }
        ul li {
          min-width: $paginationChildHeight;
          height: $paginationChildHeight;
          font-size: 12px;
        }
        .el-pagination__sizes,
        .el-pagination__jump {
          .el-pagination__editor {
            height: $paginationChildHeight;
          }
          .el-input__wrapper {
            height: $paginationChildHeight;
            .el-input__inner {
              height: 100%;
            }
          }
        }
      }
      .page-footer {
        width: 100%;
        padding: 16px;
        border-top: 1px solid var(---, #dcdfe6);
      }
      :deep(.el-button) + .el-button {
        margin-left: 4px;
      }
      :deep(.el-pagination__jump)::after {
        display: inline-block;
        margin: 0 4px;
      }
    }
    .table-list {
      position: relative;
      display: inline-block;
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 64px);
      overflow-y: auto;
      vertical-align: top;

      &-container {
        width: 100%;
        min-width: 840px;
        height: 100%;

        :deep(.nancalui-tree__node) {
          box-sizing: border-box;
          height: 36px;
          border-bottom: 1px solid #e5e6eb;

          .nancalui-tree__node-vline,
          .nancalui-tree__node-hline {
            display: none;
          }

          &:not(.active):hover {
            background-color: #f5f7fa;
          }

          .nancalui-tree__node-content {
            .svg-arrow {
              margin-left: 16px !important;
              color: #8091b7;
            }
          }

          .nancalui-tree__node-content:not(.active):hover,
          .nancalui-tree__node-content.active {
            background-color: transparent;
            transition: none;
          }

          .nancalui-tree__node-content--value-wrapper {
            width: 100%;
            height: 36px !important;
            overflow: visible;
          }
        }

        .docs-div-row,
        .header-div-row {
          position: relative;
          z-index: 99;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          width: 100%;
          height: 36px;

          .div-col {
            position: relative;
            flex: none;
            width: 180px;
            height: 36px;
            padding-right: 16px;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.75);
            font-size: 14px;
            line-height: 36px;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:first-child {
              flex: 1;
            }
          }

          &-operate {
            position: sticky;
            right: 0;
            z-index: 999;
            display: flex;
            flex: none;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            width: 192px;
            height: 36px;
            background-color: #fff;
            border-bottom: 1px solid #e5e6eb;

            &:before {
              position: relative;
              left: -16px;
              width: 8px;
              height: 36px;
              // background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.05) 100%);
              filter: blur(0px);
              content: '';
            }
            .nancalui-button {
              padding: 0;
              color: #1e89ff;
            }
            .icon {
              margin-right: 12px;
              color: $themeBlue;
              font-size: 16px;
              cursor: pointer;
              &.disabled {
                color: #b8b8b8;
                cursor: not-allowed;
                &:hover {
                  color: #b8b8b8;
                }
              }

              &:last-child {
                margin-right: 0;
              }

              &:hover {
                color: $themeBlue;
              }
            }
          }

          &:hover {
            .docs-div-row-operate {
              background-color: #f5f7fa;
            }
          }
        }

        .header-div {
          &-row {
            position: relative;
            background-color: #ebf4ff;
            border-bottom: 1px solid #e5e6eb;

            .div-col {
              color: #1d2129;
              font-weight: 400;
              font-size: 14px;

              &:first-child {
                width: 180px;
                padding-left: 24px;
              }
            }

            &-operate {
              color: #1d2129;
              font-weight: 400;
              font-size: 14px;
              background-color: #ebf4ff;
            }
          }
        }
      }

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: calc(100% - 36px);

        .pic {
          width: 260px;
          height: 160px;
          margin-bottom: 4px;
        }

        &-word {
          color: rgba(0, 0, 0, 0.46);
          font-weight: 400;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
  :deep(.nancalui-tree-select-clearable) {
    .nancalui-tree-select-clear {
      z-index: 999;
    }
  }
</style>
<style lang="scss">
  .rule-opts.nancalui-select__item {
    width: 440px !important;
  }
</style>
