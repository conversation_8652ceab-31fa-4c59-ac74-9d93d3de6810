<template>
  <div class="overview-box">
    <!--    <div class="switch">-->
    <!--      <div-->
    <!--        :class="state.tabs === 'scene' ? 'switch-label checked' : 'switch-label'"-->
    <!--        @click.prevent.stop="checkTabsFn('scene')"-->
    <!--        >场景定义</div-->
    <!--      >-->
    <!--      <div-->
    <!--        :class="state.tabs === 'data' ? 'switch-label checked' : 'switch-label'"-->
    <!--        @click.prevent.stop="checkTabsFn('data')"-->
    <!--        >数据定义</div-->
    <!--      >-->
    <!--    </div>-->
    <div v-if="state.tabs === 'scene'" class="overview">
      <div class="overview-left">
        <img v-if="props.info.posterUrl" class="pic" :src="props.info.posterUrl" />
        <div class="table center">
          <div class="tr">
            <div class="td onlyOne">项目成员</div>
          </div>
          <div class="tr header">
            <div class="td">姓名</div>
            <div class="td">工号</div>
            <div class="td">角色</div>
          </div>
          <div v-for="(item, index) in state.memberList" :key="index" class="tr">
            <div class="td" :title="item.name">{{ item.name }}</div>
            <div class="td" :title="item.jobCode">{{ item.jobCode }}</div>
            <div class="td" :title="item.role">{{ item.role }}</div>
          </div>
          <div v-if="state.memberList.length === 0" class="tr"
            ><div class="td">暂无相关成员</div></div
          >
        </div>
      </div>
      <div class="overview-right">
        <div class="table">
          <div class="tr leftHeader">
            <div class="td first">项目名称</div>
            <div class="td">{{ props.info.name }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据集名称</div>
            <div class="td">{{ props.info.dataAssetName }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">场景所属流程</div>
            <div class="td">{{ props.info.flow }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据范围</div>
            <div class="td">{{ props.info.dataRange }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据责任部门</div>
            <div class="td">{{ props.info.managementDepartment }}</div>
          </div>
          <div class="tr leftHeader">
            <div class="td first">数据使用部门</div>
            <div class="td">{{ props.info.dataUser }}</div>
          </div>
          <div class="tr header">
            <div class="td">当前现状及问题</div>
          </div>
          <div class="tr">
            <div class="td more">{{ props.info.actuality }}</div>
          </div>
          <div class="tr header">
            <div class="td">治理目标</div>
          </div>
          <div class="tr">
            <div class="td more">{{ props.info.expectedResults }}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="table-box">
      <CfTable
        :key="state.key"
        :isDisplayAction="false"
        :table-head-titles="state.tableHeadTitles"
        :paginationConfig="{
          total: state.pageInfo.total,
          pageSize: state.pageInfo.pageSize,
          currentPage: state.pageInfo.currentPage,
          onCurrentChange: (v) => {
            state.pageInfo.currentPage = v
            initTable()
          },
          onSizeChange: (v) => {
            state.pageInfo.pageSize = v
            initTable(true)
          },
        }"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
      >
      </CfTable>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchDefinitionList, workbenchMemberList } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    key: 1,
    tabs: 'scene',
    loading: false,
    memberList: [],
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'businessObject', name: '业务对象' },
      { prop: 'boGeneratedModules', name: '业务对象产生模块' },
      { prop: 'dataModel', name: '数据实体' },
      { prop: 'attribute', name: '属性' },
      { prop: 'metadata', name: '元数据' },
    ],
  })

  const checkTabsFn = (type) => {
    state.tabs = type
  }

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchDefinitionList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = { list: [] }
        state.loading = false
      })
  }

  onMounted(() => {
    initTable(true)
    workbenchMemberList({
      condition: {
        projectCode: props.info.code,
      },
      pageNum: 1,
      pageSize: 100,
    }).then((res) => {
      if (res.success) {
        state.memberList = res.data.list
      }
    })
  })
</script>

<style lang="scss" scoped>
  .overview-box {
    .switch {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      &-label {
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #c5d0ea;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        border-radius: 2px;
        &:first-of-type {
          border-right: none;
          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
        }
        &:last-of-type {
          border-left: none;
          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
        }
      }
    }
    .overview {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding-bottom: 16px;

      &-left {
        width: 330px;
        .pic {
          display: block;
          width: 100%;
          height: auto;
          max-height: 240px;
          margin-bottom: 16px;
        }
      }
      &-right {
        width: calc(100% - 346px);
      }
      .table {
        width: 100%;
        border-top: 1px solid #ebebeb;
        border-left: 1px solid #ebebeb;
        &.center {
          .tr {
            .td {
              text-align: center;
            }
          }
        }

        .tr {
          display: flex;
          justify-content: space-between;
          align-items: center;
          &.header {
            background-color: rgb(235, 244, 255);
            .td {
              color: #1d2129;
            }
          }
          &.leftHeader {
            .td {
              &.first {
                width: 160px;
                flex: none;
                background-color: rgb(235, 244, 255);
                color: #1d2129;
              }
            }
          }
          .td {
            flex: 1;
            flex-shrink: 0;
            height: 36px;
            font-size: 14px;
            color: #606266;
            line-height: 36px;
            text-align: left;
            border-right: 1px solid #ebebeb;
            border-bottom: 1px solid #ebebeb;
            padding: 0 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &.onlyOne {
              color: #1d2129;
              font-size: 16px;
              font-weight: bolder;
            }
            &.more {
              min-height: 36px;
              height: auto;
              line-height: 22px;
              white-space: normal;
              padding: 6px 12px;
            }
          }
        }
      }
    }
    .table-box {
      height: 478px;
    }
  }
</style>
