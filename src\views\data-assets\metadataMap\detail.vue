<template>
  <div class="detail">
    <div class="content">
      <div class="info">
        <n-module-name size="md">查看</n-module-name>
        <div v-if="state.type === 'DATASOURCE'" class="label-box">
          <div v-for="(item, index) in state.datasource" :key="index" class="label">
            <div class="name">{{ item.name }}：</div>
            <div class="value">{{ item.value || '暂无数据' }}</div>
          </div>
        </div>
        <div v-else-if="state.type === 'DATABASE'" class="label-box">
          <div v-for="(item, index) in state.database" :key="index" class="label">
            <div class="name">{{ item.name }}：</div>
            <div class="value">{{ item.value || '暂无数据' }}</div>
          </div>
        </div>
        <div v-else-if="state.type === 'TABLE'" class="label-box">
          <div v-for="(item, index) in state.table" :key="index" class="label">
            <div class="name">{{ item.name }}：</div>
            <div class="value">{{ item.value || '暂无数据' }}</div>
          </div>
        </div>
        <div v-else-if="state.type === 'COLUMN'" class="label-box">
          <div v-for="(item, index) in state.column" :key="index" class="label">
            <div class="name">{{ item.name }}：</div>
            <div class="value">{{ item.value || '暂无数据' }}</div>
          </div>
        </div>
      </div>
      <div v-loading="state.loading" class="canvas" id="metadataMapDetail">
        <flow />
      </div>
    </div>
    <div class="footer">
      <n-button class="footer-btn" variant="solid" @click.prevent="methods.cancelFn">返回</n-button>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted, reactive, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import G6 from '@antv/g6'
  import g6FlowGraph from './g6'
  import api from '@/api/index'
  const store = useStore()
  const router = useRouter()
  // 获取当前组件实例
  const state = reactive({
    loading: false,
    graph: {},
    mapData: {
      nodes: [],
      edges: [],
    },
    datasource: [
      { name: '数据源名称', value: '' },
      { name: '数据源类型', value: '' },
      { name: '数据源版本', value: '' },
      { name: '数据源日志信息', value: '' },
      { name: '数据源备份信息', value: '' },
      { name: '数据库列表', value: '' },
      { name: '数据源大小', value: '' },
      { name: '所有者', value: '' },
      { name: '创建时间', value: '' },
    ],
    database: [
      {
        name: '数据库名称',
        value: '',
      },
      { name: '数据源引擎', value: '' },
      { name: '数据库日志信息', value: '' },
      { name: '数据库备份信息', value: '' },
      { name: '数据库字符集', value: '' },
      { name: '数据库大小', value: '' },
      { name: '所有者', value: '' },
      { name: '创建时间', value: '' },
      { name: 'catalog名称', value: '' },
      { name: '默认字符集', value: '' },
      { name: '默认校对规则', value: '' },
    ],
    table: [
      { name: '表名', value: '' },
      { name: '表类型', value: '' },
      { name: '主键', value: '' },
      { name: '外键', value: '' },
      { name: '索引', value: '' },
      { name: '约束', value: '' },
      { name: '分区', value: '' },
      { name: '数据行数', value: '' },
      { name: '数据大小', value: '' },
      { name: '所属数据源', value: '' },
      { name: '所属数据库', value: '' },
      { name: '所有者', value: '' },
      { name: '创建时间', value: '' },
      { name: '修改时间', value: '' },
      { name: '表的状态', value: '' },
      { name: '存储引擎类型', value: '' },
      { name: '存储引擎版本', value: '' },
      { name: '表的最近检查时间', value: '' },
    ],
    column: [
      { name: '所有者', value: '' },
      { name: '创建时间', value: '' },
    ],
    id: '',
    type: '',
  })

  const methods = {
    // 初始化创建画布
    initFn() {
      state.graph = g6FlowGraph.init('metadataMapDetail')
      methods.setMapData()
      state.graph.on('afterlayout', () => {
        state.graph.focusItem(state.id, true, {
          easing: 'easeLinear',
          duration: 60,
        })
      })
    },
    // 计算尺寸
    resizeFn() {
      const { width, height } = methods.getContainerSize()
      state.graph.changeSize(width, height)
      state.graph.layout(true)
      state.graph.on('afterlayout', () => {
        state.graph.focusItem(state.id, true, {
          easing: 'easeLinear',
          duration: 60,
        })
      })
    },
    // 获取画布容器大小
    getContainerSize() {
      return {
        width: document.getElementById('metadataMapDetail').offsetWidth,
        height: document.getElementById('metadataMapDetail').offsetHeight,
      }
    },
    // 切换画布数据
    setMapData() {
      const data = methods.getModelFromOriginData(state.mapData)
      state.graph.data(data)
      state.graph.render()
      const nodes = state.graph.getNodes()
      // 遍历节点实例，将所有节点提前。
      nodes.forEach((node) => {
        node.toFront()
      })
      state.graph.zoomTo(1)
      state.graph.fitCenter()
      state.loading = false
      state.graph.on('node:mouseover', (evt) => {
        const { item } = evt
        const data = item.getModel().data
        state.graph.updateItem(item, {
          label: data.name,
        })
      })
      state.graph.on('node:mouseout', (evt) => {
        const { item } = evt
        const data = item.getModel().data
        state.graph.updateItem(item, {
          label: data.name.length > 20 ? data.name.slice(0, 20) + '...' : data.name,
        })
      })
    },
    // 转化数据
    getModelFromOriginData(originData) {
      const data = {
        nodes: [],
        edges: [],
      }
      originData.nodes.forEach((item) => {
        data.nodes?.push({
          id: item.id,
          data: item,
          size: item.type === 'DATASOURCE' ? 84 : 74,
          zIndex: 2,
          anchorPoints: [
            [0.5, 0.5],
            [0.5, 0.5],
          ],
          style: {
            opacity: item.checked ? 1 : 0.1,
            fill: methods.getNodeColor(item).backgroundColor,
            stroke: item.isMainBody ? '#ffffff' : methods.getNodeColor(item).backgroundColor,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowColor: 'rgba(0,0,0,0.18)',
            shadowBlur: item.isMainBody ? 16 : 0,
            lineWidth: 2,
            cursor: 'default',
          },
          label: item.name.length > 20 ? item.name.slice(0, 20) + '...' : item.name,
          labelCfg: {
            style: {
              opacity: item.checked ? 1 : 0.1,
            },
          },
        })
      })
      originData.edges.forEach((item) => {
        data.edges?.push({
          source: item.source,
          target: item.target,
          type: 'line',
          style: {
            stroke: methods.getNodeColor(item).edgColor,
            fill: methods.getNodeColor(item).edgColor,
            lineWidth: 1,
            fillOpacity: 0,
            endArrow: {
              path: G6.Arrow.triangle(10, 10, 40),
              d: 40,
              fill: methods.getNodeColor(item).edgColor,
              stroke: methods.getNodeColor(item).edgColor,
            },
          },
          zIndex: 1,
        })
      })
      return data
    },
    // 获取Node颜色
    getNodeColor(node) {
      if (node.type === 'DATASOURCE' || node.preType === 'DATASOURCE') {
        return {
          edgColor: node.checked ? 'rgba(255,159,25,0.8)' : 'rgba(255,159,25,0.5)',
          backgroundColor: '#FF9F19',
        }
      } else if (node.type === 'DATABASE' || node.preType === 'DATABASE') {
        return {
          edgColor: node.checked ? 'rgba(68,125,253,0.8)' : 'rgba(68,125,253,0.5)',
          backgroundColor: '#447DFD',
        }
      } else if (node.type === 'TABLE' || node.preType === 'TABLE') {
        return {
          edgColor: node.checked ? 'rgba(110,158,255,0.8)' : 'rgba(110,158,255,0.5)',
          backgroundColor: '#6E9EFF',
        }
      } else if (node.type === 'API' || node.preType === 'API') {
        return {
          edgColor: node.checked ? 'rgba(25,156,255,0.8)' : 'rgba(25,156,255,0.5)',
          backgroundColor: '#199CFF',
        }
      } else if (node.type === 'FILE' || node.preType === 'FILE') {
        return {
          edgColor: node.checked ? 'rgba(42,88,213,0.8)' : 'rgba(42,88,213,0.5)',
          backgroundColor: '#2A58D5',
        }
      } else if (node.type === 'COLUMN' || node.preType === 'COLUMN') {
        return {
          edgColor: node.checked ? 'rgba(191, 217, 255, 0.8)' : 'rgba(191, 217, 255, 0.5)',
          backgroundColor: '#BFD9FF',
        }
      }
    },
    // 返回
    cancelFn() {
      router.go(-1)
    },
  }

  onUnmounted(() => {
    g6FlowGraph.destroy()
  })

  onMounted(() => {
    state.id = router.currentRoute.value.query.id
    state.type = router.currentRoute.value.query.type
    state.loading = true
    api.assets.metadataMapDetail({ uid: state.id, type: state.type }).then((res) => {
      if (res.success) {
        state.mapData.nodes = res.data.metadataMap.nodeList.filter((val) => {
          val.id = val.uid
          val.checked = true
          val.isSee = true
          if (val.type === 'DATASOURCE') {
            val.size = 84
          } else {
            val.size = 74
          }
          if (val.uid === state.id) {
            val.isMainBody = true
          }
          return val
        })
        state.mapData.edges = res.data.metadataMap.relationList.map((val) => {
          val.source = val.preUid
          val.target = val.postUid
          val.checked = true
          return val
        })
        methods.initFn()
        if (state.type === 'DATASOURCE') {
          state.datasource[0].value = res.data.datasource.name
          state.datasource[1].value = res.data.datasource.category
          state.datasource[2].value = res.data.datasource.productVersion
          state.datasource[3].value = res.data.datasource.logFileLocation
          state.datasource[4].value = res.data.datasource.backupFileLocation
          state.datasource[5].value = res.data.datasource?.databaseList?.toString()
          state.datasource[6].value = res.data.datasource.storedDataSize
          state.datasource[7].value = res.data.datasource.datasourceOwner
          state.datasource[8].value = res.data.datasource.createTime
        } else if (state.type === 'DATABASE') {
          state.database[0].value = res.data.database.name
          state.database[1].value = res.data.database.databaseEngine
          state.database[2].value = res.data.database.logFileLocation
          state.database[3].value = res.data.database.backupFileLocation
          state.database[4].value = res.data.database.characterSet
          state.database[5].value = res.data.database.storedDataSize
          state.database[6].value = res.data.database.databaseOwner
          state.database[7].value = res.data.database.createTime
          state.database[8].value = res.data.database.catalogName
          state.database[9].value = res.data.database.defaultCharacterSetName
          state.database[10].value = res.data.database.defaultCollationName
        } else if (state.type === 'TABLE') {
          state.table[0].value = res.data.table.name
          state.table[1].value = res.data.table.tableType
          state.table[2].value = res.data.table.primaryKey
          state.table[3].value = res.data.table.foreignKey
          state.table[4].value = res.data.table.tableIndexes
          state.table[5].value = res.data.table.tableConstraints
          state.table[6].value = res.data.table.tablePartition
          state.table[7].value = res.data.table.tableRows
          state.table[8].value = res.data.table.dataSize
          state.table[9].value = res.data.table.datasourceName
          state.table[10].value = res.data.table.databaseName
          state.table[11].value = res.data.table.tableOwner
          state.table[12].value = res.data.table.createTime
          state.table[13].value = res.data.table.updateTime
          state.table[14].value = res.data.table.status
          state.table[15].value = res.data.table.engine
          state.table[16].value = res.data.table.version
          state.table[17].value = res.data.table.checkTime
        }
      }
    })
  })
</script>

<style lang="scss">
  .metadata-map-node {
    width: 74px;
    height: 74px;
    line-height: 74px;
    border-radius: 50%;
    padding: 0 10px;
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    opacity: 0.1;
    user-select: none;
    background: linear-gradient(165deg, #2b84fb 0%, #4752e1 100%);
    box-shadow: 0 0 10px 8px rgba(46, 131, 250, 0.14), inset 0 1px 6px 2px #4aacff;
    .text {
      font-size: 12px;
      color: #fff;
      line-height: 18px;
      font-weight: bold;
      text-align: center;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &:before {
      content: '';
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      background-color: transparent;
      border-radius: 50%;
      position: absolute;
      left: -10px;
      top: -10px;
      transition: all linear 0.1s;
      &.isSee {
        opacity: 0;
        &.isMainBody {
          opacity: 1;
        }
      }
    }
    &.yellow {
      width: 84px;
      height: 84px;
      line-height: 90px;
      background: linear-gradient(165deg, #eab33f 0%, #cc8635 100%);
      box-shadow: 0 0 10px 8px rgba(255, 210, 121, 0.14), inset 0 1px 6px 2px #fde77c;
      font-size: 14px;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #fde77c;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(238, 175, 65, 0.2);
        }
      }
    }
    &.purple {
      background: linear-gradient(165deg, #bb5afa 0%, #9733e5 100%);
      box-shadow: 0 0 10px 8px rgba(200, 104, 255, 0.14), inset 0 1px 6px 2px #e898ff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #e898ff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(146, 97, 255, 0.2);
        }
      }
    }
    &.blue {
      background: linear-gradient(165deg, #2b84fb 0%, #4752e1 100%);
      box-shadow: 0 0 10px 8px rgba(46, 131, 250, 0.14), inset 0 1px 6px 2px #4aacff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #4aacff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(53, 115, 242, 0.2);
        }
      }
    }
    &.green {
      background: linear-gradient(165deg, #00c8df 0%, #0891b0 100%);
      box-shadow: 0 0 10px 8px rgba(0, 197, 221, 0.14), inset 0 1px 6px 2px #00d8dd;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #00d8dd;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(2, 190, 214, 0.2);
        }
      }
    }
    &.checked {
      opacity: 1;
    }
  }
  foreignObject {
    overflow: visible;
  }
</style>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .detail {
    width: 100%;
    height: calc(100vh - 50px);
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    position: relative;
    .content {
      width: 100%;
      height: calc(100% - 70px);
      position: relative;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      background-color: #fff;
      padding: 10px;
      box-sizing: border-box;
      .info {
        width: 520px;
        height: 100%;
        overflow-y: auto;
        padding-left: 10px;
        box-sizing: border-box;
        .label-box {
          margin-top: 24px;
          padding-right: 10px;
          .label {
            margin-bottom: 12px;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            .name {
              width: 130px;
              color: #000000;
              font-size: 14px;
              font-weight: bold;
              text-align: right;
              word-break: break-all;
            }
            .value {
              width: calc(100% - 130px);
              color: #333333;
              font-size: 14px;
              word-break: break-all;
            }
          }
        }
      }
      .canvas {
        width: calc(100% - 520px);
        height: 100%;
        overflow: hidden;
        background-color: #f7f8fa;
        border-radius: 4px;
        :deep(.x6-graph-scroller) {
          overflow: hidden;
          &::-webkit-scrollbar {
            width: 0; // 横向滚动条
            height: 0; // 纵向滚动条 必写
          }

          // 滚动条的滑块
          &::-webkit-scrollbar-thumb {
            background-color: rgb(177, 176, 176);
            border-radius: 2px;
          }
        }
        :deep(.el-loading-mask) {
          background-color: rgba(0, 0, 0, 0.7);
        }
      }
    }
    .footer {
      margin-top: 10px;
      height: 60px;
      overflow: hidden;
      padding: 16px 30px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 4px 4px 0 0;
      margin-left: -10px;
      margin-right: -10px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
