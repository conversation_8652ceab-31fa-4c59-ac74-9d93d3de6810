<template>
  <div class="search-box scroll-bar-style scrollElement">
    <div class="search-content" v-loading="loading">
      <div class="header">
        <img class="header-bg" src="/src/assets/img/assets/assets-bg-04.png" />
        <div class="header-title">全局数据检索</div>
        <div class="header-box">
          <div class="header-box-content">
            <div class="header-box-content-search">
              <n-input
                v-model="searchKey"
                clearable
                placeholder="请输入关键字"
                @change="initTable"
                @clear="initTable"
              >
                <template #append>
                  <n-button
                    size="sm"
                    variant="solid"
                    color="primary"
                    @click.prevent.stop="initTable"
                    >搜索</n-button
                  >
                </template>
              </n-input>
            </div>
          </div>
        </div>
      </div>
      <div class="result">
        <div class="result-title">
          <n-tabs v-model="catalogType" class="tabs" @active-tab-change="typeFn">
            <n-tab
              v-for="(item, index) in typeList"
              :key="index"
              :id="item.type"
              :title="item.name"
            />
          </n-tabs>
          <div class="rusult-count">
            共<span>{{ resultCount }}</span
            >个查询结果</div
          >
        </div>
        <div class="result-content">
          <div v-for="(item, index) in list" :key="index" class="result-content-list">
            <div class="result-content-list-title">
              <span v-html="item.chineseName"></span>
              <span v-if="item.chineseName && item.englishName"> ｜ </span>
              <span v-html="item.englishName"></span>
              <span :class="item.catalogType + ' node-tag'">
                <img class="node-icon" :src="item.imgSrc" />
                {{ typeList.filter((items) => items.type === item.catalogType)[0]?.name }}
              </span>
            </div>
            <div class="result-content-list-text">
              <div class="result-content-list-text-left">
                <!-- 数据模型 -->
                <div class="left-item">
                  <div>创建人：{{ item.createByName }}</div>
                  <div>创建时间：{{ item.createTime }}</div>
                </div>
                <div class="describe">描述信息：{{ item.description }}</div>
              </div>
              <div class="result-content-list-text-right">
                <n-button
                  v-if="buttonAuthList?.includes('assetsManage_dataAssets_globalSearch_view')"
                  size="sm"
                  color="primary"
                  @click.stop.prevent="checkDetails(item)"
                >
                  查看</n-button
                >
              </div>
            </div>
          </div>
          <div v-if="list.length === 0" class="noData">
            <img class="noData-pic" src="@/assets/img/table-no-content.png" alt="暂无内容" />
            <div class="noData-text">暂无数据</div>
          </div>
        </div>
        <div class="pagination">
          <el-pagination
            background
            :current-page="pagination.currentPage"
            :page-size="pagination.pageSize"
            :page-sizes="[9, 10, 20, 50, 100]"
            layout="total,prev,pager,next,sizes, jumper"
            :total="resultCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { reactive, onMounted, toRef, toRefs } from 'vue'
  import moduleName from '@/components/ModuleName'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'

  export default {
    components: { moduleName },
    setup() {
      const router = useRouter()
      const state = reactive({
        buttonAuthList: [],
        loading: true,
        searchKey: '',
        catalogType: 'ALL',
        typeList: [
          { name: '全部', type: 'ALL' },
          { name: '模型', type: 'MODEL' },
          { name: '标签', type: 'TAG' },
          { name: 'API', type: 'API' },
          // { name: '元数据', type: 'METADATA' },
        ],
        list: [],
        resultCount: 0,
        searchType: 0,
        modelType: [
          { label: '模型名称', value: 0 },
          { label: '数据标签', value: 1 },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        hasMore: false,
        interfaceName: 'getAssetsLibraryModelList', //默认获取数据模型列表
      })
      // 获取当前场景code
      const store = useStore()
      const projectCode = toRefs(store.state.user.currentProject, 'projectCode').value
      const methods = {
        getAssetsImages(name) {
          return new URL(`/src/assets/img/assets/assetSearch/${name}.png`, import.meta.url).href //本地文件路径
        },
        // 切换条数
        handleSizeChange(val) {
          state.pagination.pageSize = val
          methods.initTable(false)
        },
        // 切换页数
        handleCurrentChange(val) {
          state.pagination.currentPage = val
          methods.initTable(false)
        },
        // 查看
        checkDetails(item) {
          router.push({
            name: 'globalSearchDetail',
            query: {
              id: item.dataId,
              modelTitle: item.cnName || item.name,
              modelName: item.name,
              type: item.catalogType,
            },
          })
        },
        // 监听页面滚动到底部
        scrollFn() {
          const el = document.querySelector('.scrollElement')
          const offsetHeight = el.offsetHeight
          el.onscroll = () => {
            const scrollTop = el.scrollTop
            const scrollHeight = el.scrollHeight
            if (offsetHeight + scrollTop - scrollHeight >= -1) {
              if (state.hasMore) {
                state.hasMore = false
                state.pagination.currentPage += 1
                methods.initTable(false)
              }
            }
          }
        },
        // 将字符串转化为脚本
        evalFn(fn) {
          let Fun = Function // 防止有些前端编译工具报错
          return new Fun('return ' + fn)()
        },
        typeFn(value) {
          state.catalogType = value
          methods.initTable()
        },
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              keyword: state.searchKey || null,
              catalogType: state.catalogType === 'ALL' ? null : state.catalogType,
              // projectCode: projectCode,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }

          api.assets
            .globalSearchList(data)
            .then((res) => {
              state.loading = false
              if (res.code === 'SUCCESS') {
                state.resultCount = res.data.total
                res.data.list.forEach((val) => {
                  val.chineseName = val.cnName
                  val.englishName = val.name
                  val.contentText = val.description
                  val.imgSrc = methods.getAssetsImages(val.catalogType)
                  // val.imgSrc = getAssetsImages(val.catalogType)
                })
                state.pagination.currentPage = res.data.pageNum
                if (res.data.pages > state.pagination.currentPage) {
                  state.hasMore = true
                } else {
                  state.hasMore = false
                }
                let newList = methods.checkTextFn(res.data.list)
                // if (init) {
                state.list = newList
                // } else {
                //   state.list = state.list.concat(newList)
                // }
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 高亮搜索文字
        checkTextFn(list) {
          let reg = methods.evalFn('/' + state.searchKey + '/g')
          list.forEach((val) => {
            val.chineseName = val.chineseName
              ? val.chineseName.replace(reg, `<span class="key">${state.searchKey}</span>`)
              : ''
            val.englishName = val.englishName
              ? val.englishName.replace(reg, `<span class="key">${state.searchKey}</span>`)
              : ''
            val.contentText = val.contentText
              ? val.contentText.replace(reg, `<span class="key">${state.searchKey}</span>`)
              : ''
          })
          return list
        },
      }
      onMounted(() => {
        const { buttonAuthList } = toRefs(store.state.user)
        state.buttonAuthList = buttonAuthList
        methods.initTable()
      })
      const params = toRefs(state)
      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .search-box {
    padding: 10px;
    // padding-top: 0;
    height: calc(100vh - $navbarHeight);
    overflow-y: auto;

    .search-content {
      height: 100%;
      background-color: #fff;

      .header {
        position: relative;

        &-bg {
          display: block;
          width: 100%;
          height: 200px;
          // height: auto;
        }

        &-title {
          position: absolute;
          width: 144px;
          height: 33px;
          left: 0;
          // top: 24%;
          top: 30px;
          right: 0;
          margin: auto;
          z-index: 2;
          color: #333;
          font-size: 24px;
        }

        &-box {
          position: absolute;
          width: calc(100% - 80px);
          min-width: 880px;
          height: 160px;
          // bottom: -60px;
          top: 36px;
          left: 0;
          right: 0;
          margin: auto;
          z-index: 3;
          // background: linear-gradient(180deg, #f3f5f8 0%, #ffffff 100%);
          // box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
          border-radius: 8px;
          display: flex;
          justify-content: center;
          align-items: center;

          &-content {
            width: 800px;
            &-search {
              // border: 1px solid #dcdcdc;
              border-radius: 4px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              background-color: #fff;
              .el-select {
                width: 100px;
                margin: 0 10px;
                position: relative;
                :deep(.el-input__wrapper) {
                  box-shadow: none !important;
                }
                &::after {
                  content: '';
                  width: 1px;
                  height: 30px;
                  position: absolute;
                  background-color: #dcdcdc;
                  top: 0;
                  right: -6px;
                  bottom: 0;
                  margin: auto;
                }
              }
              .nancalui-input {
                width: 100%;
                height: 46px;
                :deep(.nancalui-input__wrapper) {
                  box-shadow: none;
                  .icon-close:before {
                    content: '\E071' !important;
                  }
                }
                .nancalui-input-slot__append {
                  padding: 0;
                  .nancalui-button--solid.nancalui-button--solid--primary {
                    color: #fff;
                    width: 80px;
                    height: 46px;
                    border: 1px solid #fff;
                    background-color: $themeBlueActive !important;
                    .button-content {
                      font-size: 14px;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .result {
        background-color: #fff;
        padding: 10px 40px 10px 40px;

        &-title {
          // margin-bottom: 10px;
          position: relative;
          &-content {
            width: 800px;

            &-type {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              margin-bottom: 8px;

              &-label {
                color: #333333;
                font-size: 14px;
                margin-right: 20px;
                padding: 2px 9px;
                border-radius: 4px;
                cursor: pointer;

                &.checked {
                  color: #fff;
                  background-color: $themeBlueActive;
                }
              }
            }
          }
          .tabs {
            width: 100%;
          }
          .rusult-count {
            position: absolute;
            right: 0;
            top: 15px;
            font-size: 12px;
            color: #666;
            span {
              color: $themeBlue;
            }
          }
        }

        &-content {
          // padding-right: 80px;
          width: calc(100% + 10px);
          height: calc(100vh - 404px);
          overflow-y: scroll;
          overflow-x: hidden;
          &::-webkit-scrollbar {
            width: 4px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: transparent;
            position: absolute;
          }
          &:hover::-webkit-scrollbar-thumb {
            background-color: #e1e1e1;
            border-radius: 2px;
          }
          &-list {
            padding: 30px 10px 10px;
            border-bottom: 1px solid #ebedf0;
            position: relative;

            &:hover {
              background-color: #f7f8fa;
              border-radius: 4px;
            }

            &-title {
              color: #333333;
              font-size: 14px;
              font-weight: bold;
              .node-tag {
                display: inline-block;
                // width: 52px;
                height: 22px;
                line-height: 14px;
                background: #f0f7ff;
                border-radius: 11px;
                padding: 4px 6px 4px 23px;
                box-sizing: border-box;
                margin-left: 10px;
                position: relative;
                font-size: 12px;
                font-weight: 500;
                img {
                  position: absolute;
                  left: 6px;
                  top: 4px;
                  width: 14px;
                }
              }
              .MODEL {
                background-color: #f0f7ff;
                color: #447dfd;
              }
              .TAG {
                background-color: rgba(105, 122, 154, 0.1);
                color: #697a9a;
              }
              .API {
                background-color: rgba(49, 178, 204, 0.1);
                color: #31b2cc;
              }
              .METADATA {
                background-color: #f0faf2;
                color: #18ba72;
              }
            }

            &-text {
              margin-top: 9px;
              font-size: 14px;
              color: #666666;
              line-height: 22px;
              display: flex;

              &-left {
                flex: 1;
                padding-right: 60px;
                font-size: 12px;
                .left-item {
                  display: flex;
                  justify-content: flex-start;
                  padding-bottom: 10px;
                  div {
                    margin-right: 60px;
                  }
                }
                .describe {
                  // width: 100%;
                  max-width: 1550px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              &-right {
                width: 100px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                top: 30px;
                right: -15px;
                .n-button,
                .nancalui-button--outline--primary {
                  height: 28px;
                  padding: 0 14px;
                  line-height: 26px;
                  border-radius: 14px;
                  border: 1px solid $themeBlue;
                  font-size: 12px;
                  color: $themeBlue;
                  &:hover {
                    background: $themeBlue;
                    color: #ffffff;
                  }
                }
              }
            }
            hr {
              width: calc(100% - 180px);
              height: 1px;
              border: none;
              background-color: #ebedf0;
              margin: 0;
              position: absolute;
              left: 0;
              bottom: 0;
            }
            :deep(.key) {
              color: $themeBlueActive;
            }
          }
        }

        .pagination {
          width: 100%;
          background-color: white;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .noData {
      width: 100%;
      height: 220px;
      padding-top: 100px;
      box-sizing: border-box;

      &-pic {
        display: block;
        width: 266px;
        height: 150px;
        margin: 0 auto;
      }

      &-text {
        text-align: center;
        color: #999999;
        font-size: 12px;
        line-height: 60px;
      }
    }
    :deep(.el-pagination) {
      justify-content: center;
      padding: 30px 0;
      .el-pagination__total,
      .el-pagination__goto,
      .el-pagination__classifier {
        font-size: 12px;
      }

      button {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      ul li {
        font-size: 12px;
        height: $paginationChildHeight;
        min-width: $paginationChildHeight;
      }
      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }
        .el-input__wrapper {
          height: $paginationChildHeight;
          .el-input__inner {
            height: 100%;
          }
        }
      }
    }
  }
</style>
