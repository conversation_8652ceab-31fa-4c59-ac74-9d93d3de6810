<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="请输入名称"
            size="small"
            clearable
          />
          <span class="label">任务状态：</span>
          <n-select
            v-model="state.originalFormInline.sourceType"
            placeholder="状态"
            allow-clear
            filter
          >
            <n-option
              v-for="item in state.statusList"
              :key="item.value"
              :name="item.name"
              :value="item.value"
            />
          </n-select>
          <span class="label">任务触发时间：</span>
          <n-range-date-picker-pro
            v-model="state.originalFormInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :shortcuts="state.shortcuts"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">数据分类</div>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          />
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-title">实例监控</div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                :key="state.key"
                saveWidth
                :isDisplayAction="state.isDisplayAction"
                :table-head-titles="state.tableHeadTitles"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="180"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    init()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    init()
                  },
                }"
              >
                <template #name="{ row }">
                  <div class="taskName" :title="row.name">
                    {{ row.name }}
                  </div>
                </template>
                <template #state="{ row }">
                  <div class="taskName">
                    <span v-if="row.state === 1">运行中</span>
                    <span v-else-if="row.state === 6">运行失败</span>
                    <span v-else-if="row.state === 7">运行成功</span>
                    <span v-else>--</span>
                  </div>
                </template>
                <template #editor="{ row }">
                  <div class="edit-box">
                    <!-- <n-button class="has-right-border" v-if="
                      buttonAuthList.includes('dataManagement_collectionMonitor_view') &&
                      row.state === 7
                    " code="dataManagement_collectionMonitor_view" variant="text"
                      :disabled="!row.state || row.state === 1" @click.prevent="cardSeeFn(row)">查看结果</n-button> -->
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_run_edit') &&
                        row.state !== 1
                      "
                      code="dataManagement_collectionMonitor_run_edit"
                      variant="text"
                      :disabled="!row.state || row.state === 1"
                      @click.prevent="cardRunFn(row)"
                      >重跑</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_refresh_edit') &&
                        row.state === 1
                      "
                      code="dataManagement_collectionMonitor_refresh_edit"
                      variant="text"
                      :disabled="!row.state || row.state === 1"
                      @click.prevent="init(false)"
                      >刷新</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_refresh_edit') &&
                        row.state !== 1
                      "
                      code="dataManagement_collectionMonitor_refresh_edit"
                      variant="text"
                      @click.prevent="cardLogFn(row)"
                      >查看日志</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-modal
      v-model="state.showResult"
      title="查看结果"
      class="largeDialog has-top-padding"
      width="720px"
      :close-on-click-overlay="false"
      @close="closeDialog"
      style="z-index: 2000"
    >
      <div class="modal-body">
        <div class="modal-body-header">
          <div class="row">
            <div class="col">
              <div class="name">采集任务名称：</div>
              <div class="value">{{ state.optionItemData.name }}</div>
            </div>
            <div class="col">
              <div class="name">任务触发时间：</div>
              <div class="value">{{ state.optionItemData.startTime }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="name">中文名称：</div>
              <div class="value">{{ state.optionItemData.destinationEntryName }}</div>
            </div>
            <div class="col">
              <div class="name">英文名称：</div>
              <div class="value" :title="state.optionItemData.destinationEntry">{{
                state.optionItemData.destinationEntry
              }}</div>
            </div>
          </div>
        </div>
        <div class="modal-body-table">
          <div
            :class="
              'modal-body-table-scroll' + (state.tableDataModal?.list?.length > 0 ? '' : ' empty')
            "
            :style="'width:' + state.tableHeadTitlesModal.length * 135 + 'px;height:400px;'"
          >
            <CfTable
              saveWidth
              :isDisplayAction="false"
              :table-head-titles="state.tableHeadTitlesModal"
              :showPagination="false"
              :tableHeight="396"
              :tableConfig="{
                data: state.tableDataModal.list,
                rowKey: 'id',
              }"
            />
          </div>
        </div>
        <!--        <div v-if="state.tableDataModal.list.length>0" class="nancalui-table-page">-->
        <!--          <n-pagination-->
        <!--            :total="state.pageInfoModal.total"-->
        <!--            v-model:pageSize="state.pageInfoModal.pageSize"-->
        <!--            v-model:pageIndex="state.pageInfoModal.currentPage"-->
        <!--            :can-change-page-size="false"-->
        <!--            canViewTotal-->
        <!--            :page-size-options="[10, 20, 50, 100]"-->
        <!--            @page-index-change="cardCurrentChange"-->
        <!--            @page-size-change="cardSizeChange"-->
        <!--          />-->
        <!--        </div>-->
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" @click.prevent="state.showResult = false">取消</n-button>
        </div>
      </template>
    </n-modal>
    <logPop v-if="state.showDirDialog" :nodeInfo="state.nodeInfo" @seeLogFn="seeLogFn" />
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import ENUM from '@/const/enum'
  import { formartTimeDate } from '@/utils/index'
  import {
    queryDataTypeTree,
    collectMonitorList,
    collectResultSee,
    collectResultRun,
  } from '@/api/dataManage'
  import { useRoute } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import logPop from './logPop'
  import CfTtee from '@/components/cfTtee'
  export default {
    name: '',
    components: { logPop },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const route = useRoute()

      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableData: {},
        tableHeight: 436,
        key: 1,
        collectType: 'ALL',
        loading: false,
        showDirDialog: false,
        nodeInfo: {},
        showResult: false,
        isDisplayAction: true,
        originalFormInline: {
          sourceType: route.query?.type,
          keyword: null,
          time: [
            ((route.query?.time && new Date(parseInt(route.query.time))) || new Date()).setHours(
              0,
              0,
              0,
              0,
            ),
            ((route.query?.time && new Date(parseInt(route.query.time))) || new Date()).setHours(
              23,
              59,
              59,
              999,
            ),
          ],
        },
        formInline: {
          sourceType: route.query?.type,
          keyword: null,
          time: [
            ((route.query?.time && new Date(parseInt(route.query.time))) || new Date()).setHours(
              0,
              0,
              0,
              0,
            ),
            ((route.query?.time && new Date(parseInt(route.query.time))) || new Date()).setHours(
              23,
              59,
              59,
              999,
            ),
          ],
        },
        optionItemData: {}, // 操作的任务数据
        statusList: [
          { name: '运行中', value: '1' },
          { name: '运行成功', value: '7' },
          { name: '运行失败', value: '6' },
        ],
        shortcuts: ENUM.SHORTCUTS,
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'name', name: '采集任务名称', slot: 'name' },
          { prop: 'sourceType', name: '数据源类型' },
          { prop: 'state', name: '运行状态', slot: 'state' },
          { prop: 'startTime', name: '任务触发时间' },
          { prop: 'endTime', name: '任务结束时间' },
          { prop: 'timeText', name: '运行时长' },
        ],
        pageInfoModal: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableDataModal: { list: [] },
        tableHeadTitlesModal: [],
        tModalList: [],
        categoryId: null,
        treeSearchText: '',
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        expandedKeys: [],
        selectedKey: null,
      })
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 298
        },
        closeDialog() {
          state.showResult = false
        },
        seeLogFn(flag) {
          state.showDirDialog = flag
        },
        cardLogFn(item) {
          state.nodeInfo = {
            name: item.name,
            instanceId: item.instanceId,
            sourceType: item.sourceType,
          }
          state.showDirDialog = true
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        // 查看
        cardSeeFn(item) {
          state.optionItemData = item
          state.tModalList = []
          api.model
            .getModelDataList({
              condition: {
                id: state.optionItemData.destinationId,
              },
              pageNum: 1,
              pageSize: 999,
            })
            .then((res) => {
              let { success } = res
              if (success) {
                res?.data?.list?.map(({ cnName, name }) => {
                  state.tModalList.push({
                    prop: name,
                    name: cnName || name,
                    width: 135,
                  })
                })
                methods.getModalList(true)
              }
            })
        },
        // 获取表数据
        getModalList(init = false) {
          state.pageInfoModal.currentPage = init ? 1 : state.pageInfoModal.currentPage
          let data = {
            pageNum: state.pageInfoModal.currentPage,
            pageSize: state.pageInfoModal.pageSize,
            condition: {
              id: state.optionItemData.destinationId || null,
              limitPreview: 20,
            },
          }
          let tableHeadTitlesModal = state.tModalList || []
          api.model.getDataWithProject(data).then((res) => {
            if (res.success) {
              if (res.data.list.length > 0) {
                if (tableHeadTitlesModal.length < 5) {
                  tableHeadTitlesModal.forEach((val) => {
                    val.width = 688 / tableHeadTitlesModal.length
                  })
                }
                state.tableHeadTitlesModal = tableHeadTitlesModal
                res.data.list.forEach((val) => {
                  tableHeadTitlesModal.forEach((v) => {
                    if (typeof val[v.prop] === 'boolean') {
                      val[v.prop] = String(val[v.prop])
                    }
                  })
                })
              }
              state.pageInfoModal.total = res.data.total
              state.tableDataModal = res.data
              state.showResult = true
            }
          })
        },
        // 重跑
        cardRunFn(item) {
          collectResultRun({ id: item.instanceId }).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '执行成功',
                type: 'success',
              })
              methods.init(false)
            }
          })
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            sourceType: null,
            keyword: null,
            time: [],
          }
          methods.searchClickFn()
        },
        //搜索
        onSearch() {
          state.key++
          methods.init(true)
        },
        // 获取树列表
        getTreeListFn() {
          queryDataTypeTree({}).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              if (res.data?.children.length > 0) {
                treeData = [res.data]
                treeData[0].selected = true
                state.categoryId = treeData[0].enName === 'data_cate_all' ? null : treeData[0].id
              } else {
                treeData.push({
                  children: [],
                  id: null,
                  level: 0,
                  label: '全部',
                  type: 'ROOT',
                })
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]
              nextTick(() => {
                state.expandedKeys = [state.treeData?.[0]?.id || 1]
                state.selectedKey = state.treeData?.[0]?.id || 1
              })
              methods.onSearch(true)
            }
          })
        },
        // 树搜索
        searchTreeFn() {
          state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.id) {
            state.categoryId = node.enName === 'data_cate_all' ? null : node.id
            methods.onSearch(true)
          }
        },
        // 初始化
        init(init = false) {
          methods.initTable(init)
        },
        // 初始化表格（列表）
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let startTime = ''
          let endTime = ''
          if (state.formInline.time && state.formInline.time[0]) {
            if (Object.prototype.toString.call(state.formInline.time[0]) === '[object Date]') {
              startTime = formartTimeDate(state.formInline.time[0], '-', true)
              endTime = formartTimeDate(state.formInline.time[1], '-', true)
            } else {
              startTime = state.formInline.time[0]
              endTime = state.formInline.time[1]
            }
          }
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              state: state.formInline.sourceType || null,
              name: state.formInline.keyword || null,
              sourceCategoryId: state.categoryId,
            },
          }
          if (startTime) {
            data.condition.startTime = startTime
            data.condition.endTime = endTime
          }
          state.loading = true
          collectMonitorList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.number = index + 1
                  item.timeText = methods.diffTime(item)
                })
                state.tableData = res.data
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 时长转化
        diffTime(item) {
          if (item.startTime && item.endTime) {
            let startDate = new Date(item.startTime)
            let endDate = new Date(item.endTime)
            let diff = endDate.getTime() - startDate.getTime() //时间差的毫秒数

            //计算出相差天数
            let days = Math.floor(diff / (24 * 3600 * 1000))
            //计算出小时数
            let leave1 = diff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
            let hours = Math.floor(leave1 / (3600 * 1000))
            //计算相差分钟数
            let leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
            let minutes = Math.floor(leave2 / (60 * 1000))

            //计算相差秒数
            let leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
            let seconds = Math.round(leave3 / 1000)

            let returnStr = seconds < 10 ? '0' + seconds : seconds
            if (minutes > 0) {
              returnStr = (minutes < 10 ? '0' + minutes : minutes) + ':' + returnStr
            } else {
              returnStr = '00:' + returnStr
            }
            if (hours > 0) {
              returnStr = (hours < 10 ? '0' + hours : hours) + ':' + returnStr
            } else {
              returnStr = '00:' + returnStr
            }
            if (days > 0) {
              returnStr = days + '天' + returnStr
            }
            return returnStr
          } else {
            return '--'
          }
        },
        // 切换分页数量
        cardSizeChange(val) {
          state.pageInfoModal.pageSize = val
          methods.getModalList(true)
        },
        // 切换分页
        cardCurrentChange(val) {
          state.pageInfoModal.currentPage = val
          methods.getModalList()
        },

        // 列表（表格）操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.init()
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        const { name, projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        state.formInline.projectName = name.value
        methods.getTreeListFn()
      })
      return {
        buttonAuthList,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;

    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;

      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;

          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }

        &.date {
          height: 36px;

          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }

        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }

        &.tabs {
          align-items: flex-end;
          height: 48px;

          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }

        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }

            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-color: #1e89ff;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;

              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }

              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }

    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }

  .data-collection-page {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    height: 100%;
    padding: 0;
    border-radius: 0;

    .data-collection-page-tree {
      box-sizing: border-box;
      width: 286px;
      height: 100%;
      padding: 8px 0;
      background-color: #fff;
      border-radius: 2px;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 36px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }

      &-ipt {
        margin-bottom: 8px;
        padding: 0 12px;

        :deep(.nancalui-input-slot__suffix) {
          opacity: 0.5;

          .icon-search {
            font-weight: normal;
            transform: scale(1.4);
          }
        }
      }

      :deep(.tree-box) {
        padding: 0 12px;
        height: calc(100% - 76px);
      }
    }

    .data-collection-page-content {
      width: calc(100% - 10px - var(--aside-width));
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;

      .out-box {
        height: 100%;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
      }

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;

        :deep(.el-table__body) {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }

          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;

              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }

          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }

          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;

              &.PUBLISH {
                background-color: #04c495;
              }

              &.CREATED {
                background-color: #447dfd;
              }

              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }

      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }

      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }
      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;
        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }
        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }
  .modal-body {
    &-header {
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        .col {
          display: flex;
          flex: 1;
          flex-shrink: 0;
          align-items: center;
          justify-content: flex-start;
          height: 22px;
          color: #606266;
          font-size: 14px;
          line-height: 22px;
          .name {
            width: 100px;
          }
          .value {
            width: calc(100% - 100px);
            max-width: 230px;
            overflow: hidden;
            color: #1d2129;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }
      }
    }
    &-table {
      width: 100%;
      overflow-x: auto;
      &-scroll {
        min-width: 100%;
        &.empty {
          :deep(.el-table__empty-block) {
            height: 300px !important;
          }
        }
      }
    }
  }
</style>
