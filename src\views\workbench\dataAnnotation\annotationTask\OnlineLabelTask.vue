<template>
  <div class="labelTask">
    <div class="labelTask-head">
      <n-button variant="solid" @click="onSave">保存</n-button>
      <n-button @click="onCancel">取消</n-button>
    </div>
    <div class="labelTask-body" v-loading="state.loading">
      <CfTable
        :key="state.key"
        :needNewline="true"
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
      >
        <template #anno="scope">
          <el-select
            v-if="scope.index !== 0"
            v-model="scope.row.anno"
            placeholder="请选择标注结果"
            node-key="key"
            :props="{
              label: 'name',
              value: 'key',
            }"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            show-checkbox
            :render-after-expand="false"
            filterable
            clearable
          >
            <el-option
              v-for="item in state.tagOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
          <span v-else>{{ scope.row.anno }}</span>
        </template>
      </CfTable>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { excelAnnotationList, excelAnnotationSave } from '@/api/dataGovernance.js'

  const router = useRouter()
  const props = defineProps({
    info: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['onCancel'])

  const state = reactive({
    loading: false,
    tableData: { list: [] },
    tagOptions: [],
    docUrl: '',
    tableHeadTitles: [{ prop: 'anno', name: '标注结果', slot: 'anno' }],
  })

  onMounted(() => {
    getTargetList()
    getDocUrl()
  })

  // 获取文档地址
  function getDocUrl(id) {
    state.loading = true
    api.documentManage
      .outsideGet({ id: props.info.documentId })
      .then((res) => {
        const { docUrl } = res.data
        state.docUrl = docUrl
        onSearch()
      })
      .catch(() => {
        state.loading = false
      })
  }

  // 获取详情列表
  const onSearch = (init = false) => {
    let data = {
      docId: props.info.documentId,
      docUrl: state.docUrl,
    }

    excelAnnotationList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableHeadTitles = Object.keys(res.data.details[0])
            .map((item) => {
              if (item === 'anno') {
                return {
                  prop: 'anno',
                  name: '标注结果',
                  slot: 'anno',
                }
              }
              return {
                prop: item,
                name: item,
              }
            })
            .filter((item) => item.prop !== 'uuid')
          state.tableData.list = res.data.details.map((item) => {
            item.anno = JSON.parse(item.anno || '[]')
            return item
          })
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 获取标签列表
  const getTargetList = () => {
    api.dataGovernance.getTaskLabelDetail(props.info.taskId).then((res) => {
      if (res.success) {
        const { data } = res
        state.tagOptions = JSON.parse(data?.labels || '[]')
      }
    })
  }

  // 保存
  const onSave = () => {
    const dataAnnos = []
    state.tableData.list.forEach((item) => {
      dataAnnos.push({ anno: JSON.stringify(item.anno), uuid: item.uuid })
    })
    let data = {
      dataAnnos,
      docId: props.info.documentId,
      docUrl: state.docUrl,
    }
    state.loading = true
    excelAnnotationSave(data).then((res) => {
      state.loading = false
      if (res.success) {
        ElMessage.success('保存成功')
        emit('onCancel')
      }
    })
  }

  // 取消
  const onCancel = () => {
    emit('onCancel')
  }
</script>

<style lang="scss" scoped>
  .labelTask {
    width: 100%;
    height: 100%;

    &-head {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 16px;
    }
    &-body {
      width: 100%;
      height: calc(100% - 48px);
    }
  }
</style>
