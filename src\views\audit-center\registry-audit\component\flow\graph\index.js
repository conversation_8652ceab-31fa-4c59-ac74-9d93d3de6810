import { Graph, FunctionExt, Shape } from '@antv/x6'
import './shape'

let graph = null
export default class FlowGraph {
  static init() {
    graph = new Graph({
      container: document.getElementById('flowContainer'),
      width: 2000,
      height: 1000,
      interacting: {
        nodeMovable: false,
      },
      grid: {
        size: 20,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: 'rgb(229,229,299)',
            thickness: 1,
          },
          {
            color: 'rgb(229,229,299)',
            thickness: 1,
            factor: 4,
          },
        ],
      },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      // 画布选中
      selecting: {
        enabled: false,
        multiple: true,
        movable: true,
      },
      connecting: {
        allowNode: true,
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        highlight: true,
        snap: true,
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#697A9A',
                strokeWidth: 1,
                targetMarker: {
                  name: 'classic',
                  size: 8,
                },
              },
            },
            router: {
              name: 'manhattan',
            },
            zIndex: 0,
          })
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          if (sourceView === targetView) {
            return false
          }
          if (!sourceMagnet) {
            return false
          }
          if (!targetMagnet) {
            return false
          }
          return true
        },
      },
    })
    FlowGraph.initEvent()
    return graph
  }

  static showPorts(ports, show) {
    for (let i = 0, len = ports.length; i < len; i = i + 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden'
    }
  }

  static initEvent() {
    // const container = document.getElementById('flowContainer')

    graph.on('node:contextmenu', ({ cell, view }) => {
      const oldText = cell.attr('text/textWrap/text')
      const elem = view.container.querySelector('.x6-edit-text')
      if (elem === null) {
        return
      }
      cell.attr('text/style/display', 'none')
      if (elem) {
        elem.style.display = ''
        elem.contentEditable = 'true'
        elem.innerText = oldText
        elem.focus()
      }

      const onBlur = () => {
        cell.attr('text/textWrap/text', elem.innerText)
        cell.attr('text/style/display', '')
        elem.style.display = 'none'
        elem.contentEditable = 'false'
      }
      elem.addEventListener('blur', () => {
        onBlur()
        elem.removeEventListener('blur', onBlur)
      })
    })
    graph.on(
      'node:mouseenter',
      FunctionExt.debounce(({ cell }) => {
        // const ports = container.querySelectorAll('.x6-port-body')
        // FlowGraph.showPorts(ports, true)
      }),
      500,
    )
    graph.on('node:mouseleave', () => {
      // const ports = container.querySelectorAll('.x6-port-body')
      // FlowGraph.showPorts(ports, false)
    })

    graph.on('node:collapse', ({ node, e }) => {
      e.stopPropagation()
      node.toggleCollapse()
      const collapsed = node.isCollapsed()
      const cells = node.getDescendants()
      cells.forEach((n) => {
        if (collapsed) {
          n.hide()
        } else {
          n.show()
        }
      })
    })

    // graph.on('edge:mouseenter', ({ cell }) => {
    //   cell.addTools([{ name: 'button-remove', args: { distance: -40 } }])
    // })
    //
    // graph.on('edge:mouseleave', ({ cell }) => {
    //   cell.removeTools()
    // })

    graph.bindKey('backspace', () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.removeCells(cells)
      }
    })
    graph.bindKey('delete', () => {
      const cells = graph.getSelectedCells()
      if (cells.length) {
        graph.removeCells(cells)
      }
    })
  }

  // 销毁
  static destroy() {
    graph.dispose()
  }
}
