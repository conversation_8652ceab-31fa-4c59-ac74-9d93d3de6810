<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>返回</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数据治理-数据计算-快速开发（点进去）" transform="translate(-1240.000000, -211.000000)">
            <g id="内容" transform="translate(230.000000, 96.000000)">
                <g id="编组-3" transform="translate(1000.000000, 20.000000)">
                    <g id="返回" transform="translate(10.000000, 95.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <g id="编组" transform="translate(2.000000, 4.000000)" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3">
                            <line x1="1.99999613e-05" y1="4" x2="12" y2="4" id="路径"></line>
                            <polyline id="路径" points="3.99998222 8 0 4 3.99998222 0"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>