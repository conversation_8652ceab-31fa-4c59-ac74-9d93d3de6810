<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="false"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
      actionWidth="200"
    >
      <template #pageTop>
        <!-- 上传规则文件 -->
        <div class="upload-box">
          <n-button class="upload-btn" color="primary" @click="uploadFile"> 上传规则文件 </n-button>
        </div>
      </template>
      <template #editor="{ row }">
        <!-- 下载、删除 -->
        <n-button color="primary" variant="text" @click="downloadFile(row.fileUrl, row.fileName)"
          >下载</n-button
        >
        <n-button color="primary" variant="text" @click="deleteFn(row)">删除</n-button>
      </template>
    </CfTable>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchAnnotationRuleSearch } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { name: '文件名称', prop: 'fileName', width: 900 },
      { name: '上传人', prop: 'uploadUser' },
      { name: '上传时间', prop: 'uploadTime' },
    ],
  })
  const deleteFn = (row) => {
    // 删除
    state.tableData.list = state.tableData.list.filter((item) => item.id !== row.id)
    ElMessage.success('删除成功')
  }
  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.projectCode,
      },
    }
    state.loading = true
    workbenchAnnotationRuleSearch(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = { list: [] }
        state.loading = false
      })
  }
  const uploadFile = () => {
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.onchange = (event) => {
      const file = event.target.files[0]
      ElMessage.success('上传成功')
    }
    fileInput.click()
  }

  // 下载
  const downloadFile = (url, name) => {
    // 下载文件 包括图片 pdf等
    fetch(url).then((res) => {
      // 成功
      res.blob().then((blob) => {
        // 成功
        const link = document.createElement('a') // 创建a标签
        link.href = URL.createObjectURL(blob) // 创建下载链接
        link.download = name + url.substring(url.lastIndexOf('.')) // 下载文件名
        document.body.appendChild(link) // 插入body
        link.click() // 点击下载
        document.body.removeChild(link) // 移除body
      })
    })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
    .upload-box {
      margin-bottom: 16px;
    }
  }
</style>
