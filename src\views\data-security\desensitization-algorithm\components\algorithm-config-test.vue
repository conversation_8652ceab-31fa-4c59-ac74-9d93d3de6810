<template>
  <!-- 算法 测试 -->
  <n-drawer
      v-model="state.drawer"
      title=""
      :size="550"
      :esc-key-closeable="false"
      :close-on-click-overlay="false"
      class="test-custom-algorithm-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">算法测试</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
      </div>
      <div class="n-drawer-body-content">
        <div class="test-content">
          <n-form
              ref="ruleForm"
              :data="state.ruleForm"
              label-width="81px"
              label-align="start"
              :pop-position="['right']"
              class="custom-algorithm-ruleForm scroll-box scroll-bar-style"
          >
            <!-- 日期类型截断 -->
            <n-form-item
                v-if="state.desensitizer === 'DATE_TYPE_TRUNCATION'"
                label="内容："
                field="content"
                :rules="[{ required: true, validator: validTimeDate, trigger: 'blur' }]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <!-- 数字类型截断 -->
            <n-form-item
                v-else-if="state.desensitizer === 'NUMERIC_TYPE_TRUNCATION'"
                label="内容："
                field="content"
                :rules="[
                {
                  required: true,
                  validator: validNumber,
                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <!-- 身份证号码掩码 -->
            <n-form-item
                v-else-if="state.desensitizer === 'IDENTITY_CARD_NUMBER_MASK'"
                label="内容："
                field="content"
                :rules="[
                {
                  required: true,
                  validator: validIdCard,

                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <!-- 银行卡号码掩码 -->
            <n-form-item
                v-else-if="state.desensitizer === 'BANK_CARD_NUMBER_MASK'"
                label="内容："
                field="content"
                :rules="[
                {
                  required: true,
                  validator: validBankCard,
                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="formattedValue" placeholder="请输入测试内容" @input="formatInput" />
            </n-form-item>
            <!-- 邮箱掩码 -->
            <n-form-item
                v-else-if="state.desensitizer === 'EMAIL_MASK'"
                label="内容："
                field="content"
                :rules="[
                {
                  required: true,
                  validator: validEmail,
                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <!-- 手机号码掩码 -->
            <n-form-item
                v-else-if="state.desensitizer === 'PHONE_NUMBER_MASK'"
                label="内容："
                field="content"
                :rules="[
                {
                  required: true,
                  validator: validPhone,
                  trigger: 'blur',
                },
              ]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <n-form-item
                v-else
                label="内容："
                field="content"
                :rules="[{ required: true, message: '请输入测试内容', trigger: 'blur' }]"
            >
              <n-input v-model="state.ruleForm.content" placeholder="请输入测试内容" />
            </n-form-item>
            <n-form-item
                v-if="state.desensitizedContent"
                label="结果："
                class="label_align_required"
            >
              <div class="result-item">{{ state.desensitizedContent }}</div>
            </n-form-item>
          </n-form>
        </div>
      </div>
      <div class="options-box-bg">
        <div class="options-box-content">
          <n-button color="secondary" size="sm" @click.prevent="closeDrawerFn">取消</n-button>
          <n-button
              color="primary"
              size="sm"
              variant="solid"
              :loading="state.loading"
              @click.prevent="testForm"
          >
            测试</n-button
          >
        </div>
      </div>
    </div>
  </n-drawer>
</template>
<script>
import { ref, reactive } from 'vue'
import api from '@/api/index'
import { ElNotification } from 'element-plus'
import {
  validPhone,
  validEmail,
  validNumber,
  validIdCard,
  validBankCard,
  validTimeDate,
} from '@/utils/validate'
export default {
  setup() {
    const ruleForm = ref()
    const state = reactive({
      drawer: false,
      loading: false,
      ruleForm: {
        content: '',
      },
      id: null,
      desensitizedContent: '',
      desensitizer: null, //算法唯一标识
    })

    const formattedValue = computed({
      // 计算属性用于展示给用户的值，添加空格
      get: () => {
        return state.ruleForm.content.replace(/(\d{4})(?=\d)/g, '$1 ')
      },
      // 设置值时，去除空格，更新实际绑定值
      set: (value) => {
        state.ruleForm.content = value.replace(/\s/g, '')
      },
    })

    const methods = {
      formatInput(value) {
        // 直接操作DOM来格式化输入的值

        let _value = value.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ')
        // 将最终的值设置回计算属性，以更新rawValue
        formattedValue.value = _value
      },
      //关闭抽屉
      closeDrawerFn() {
        state.drawer = false
      },

      init(data) {
        state.ruleForm.content = ''
        state.id = data.id
        state.desensitizer = data.desensitizer
        state.desensitizedContent = null
        state.drawer = true
      },
      //测试脱敏算法
      testForm() {
        ruleForm.value.validate((valid) => {
          if (valid) {
            state.loading = true
            api.assets
                .desensitizationAlgoTest({
                  content: state.ruleForm.content,
                  id: state.id,
                })
                .then((res) => {
                  state.loading = false
                  let { success, data } = res
                  if (success) {
                    state.desensitizedContent = data.desensitizedContent
                  }
                })
                .catch(() => {
                  state.loading = false
                })
          }
        })
      },
    }

    return {
      state,
      ruleForm,
      validPhone,
      validEmail,
      validNumber,
      validIdCard,
      validBankCard,
      validTimeDate,
      formattedValue,
      ...methods,
    }
  },
}
</script>

<style lang="scss">
.test-custom-algorithm-drawer {
  .nancalui-drawer__title {
    display: none;
  }
  .n-drawer-body {
    height: 100%;
  }

  &.nancalui-drawer .n-drawer-body-content {
    height: calc(100% - 52px);
    padding: 0;

    .test-content {
      padding: 16px;
      .custom-algorithm-ruleForm {
        .result-item {
          width: 100%;
          padding: 5px 6px 5px 0px;
          color: rgba(0, 0, 0, 0.55);
          font-weight: 400;
          font-size: 14px;
          font-style: normal;
          line-height: 22px;
          word-break: break-word;
        }
      }
    }
    .btn-box {
      height: 48px;
      padding: 8px 16px;
      border-bottom: 1px solid var(---, #c5d0ea);
      svg {
        margin: -2px 4px 0 0;
      }
    }
    .config-content {
      padding: 16px;
    }
  }
  .n-drawer-body-header{
    position: relative;
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }
  }
  .n-drawer-body-content {
    .content-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding: 0 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bolder;
      font-size: 16px;
      line-height: 24px;
      svg {
        margin-right: 4px;
        font-size: 16px;
        cursor: pointer;
        &.noExpand {
          transform: rotate(180deg);
        }
      }
      i {
        display: block;
        flex: 1;
        height: 1px;
        margin-left: 8px;
        border-bottom: 1px solid #e5e5e5;
      }
    }
  }
  .options-box-bg {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 64px;
    padding: 16px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;

    .options-box-content {
      display: inline-block;
      width: 100%;
      text-align: right;
      background-color: #fff;
    }
  }
}
</style>
