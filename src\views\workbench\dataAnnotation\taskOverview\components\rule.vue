<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="true"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
      :actionWidth="100"
      actionName="对应代码"
    >
      <template #editor="{ row }">
        <div class="edit-box">
          <n-button
            class="has-right-border"
            code="dataManagement_collectionMonitor_view"
            variant="text"
            :disabled="!row.correspondingCodeUrl"
            @click.prevent="downloadFile(row.correspondingCodeUrl, row.content)"
            >下载</n-button
          >
        </div>
      </template>
    </CfTable>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchRuleList } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'businessDomain', name: '规则类型', width: 400 },
      // { prop: 'businessObjectName', name: '业务对象名称' },
      // { prop: 'dataEntityName', name: '数据实体名称' },
      // { prop: 'attribute', name: '属性' },
      { prop: 'content', name: '规则说明', width: 'auto' },
      // { prop: 'origin', name: '来源系统' },
      // { prop: 'generatingUnit', name: '产生单位', width: 'auto' },
    ],
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchRuleList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 下载
  const downloadFile = (url, name) => {
    // 下载文件 包括图片 pdf等
    fetch(url).then((res) => {
      // 成功
      res.blob().then((blob) => {
        // 成功
        const link = document.createElement('a') // 创建a标签
        link.href = URL.createObjectURL(blob) // 创建下载链接
        link.download = name + url.substring(url.lastIndexOf('.')) // 下载文件名
        document.body.appendChild(link) // 插入body
        link.click() // 点击下载
        document.body.removeChild(link) // 移除body
      })
    })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
