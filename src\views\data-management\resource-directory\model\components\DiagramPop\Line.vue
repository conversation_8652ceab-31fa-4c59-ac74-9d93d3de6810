<template>
  <div class="line">
    <div class="borderLine">
      <div class="row">
        <label>种类</label>
        <n-select
          v-model="borderAttrData.out.style"
          placeholder="请选择种类"
          @value-change="updateFn"
        >
          <n-option
            v-for="item in options"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
      </div>
      <div class="row">
        <label>粗细</label>
        <n-input-number
          v-model="borderAttrData.out.size"
          :min="1"
          :max="10"
          label="边框粗细"
          @change="updateFn"
        />
      </div>
      <div class="row">
        <label>颜色</label>
        <n-color-picker
          v-model="borderAttrData.out.color"
          style="width: 40px; height: 40px"
          :swatches="predefineColors"
          @change="updateFn"
        />
      </div>
    </div>
    <div class="medialLine">
      <div class="row">
        <label>种类</label>
        <n-select
          v-model="borderAttrData.inside.style"
          placeholder="请选择种类"
          @value-change="updateFn"
        >
          <n-option
            v-for="item in options"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
      </div>
      <div class="row">
        <label>粗细</label>
        <n-input-number
          v-model="borderAttrData.inside.size"
          :min="1"
          :max="10"
          label="边框粗细"
          @change="updateFn"
        />
      </div>
      <div class="row">
        <label>颜色</label>
        <n-color-picker
          style="width: 40px; height: 40px"
          v-model="borderAttrData.inside.color"
          :swatches="predefineColors"
          @change="updateFn"
        />
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      borderAttr: {
        type: Object,
        default: () => {
          return {
            out: {
              color: '#000',
              style: 'solid',
              size: 1,
            },
            inside: {
              color: '#000',
              style: 'solid',
              size: 1,
            },
          }
        },
      },
    },
    data() {
      return {
        options: [
          {
            // 边框种类
            value: 'solid',
            label: '实线',
          },
          {
            value: 'dashed',
            label: '虚线',
          },
          {
            value: 'double',
            label: '双线',
          },
          {
            value: 'none',
            label: '无',
          },
        ],
        borderAttrData: {
          out: {
            style: 'solid', // 外部边框种类
            size: 1, // 外部边框粗细
            color: '#000', // 外部边框颜色
          },
          inside: {
            style: 'solid', // 内侧边框种类
            size: 1, // 内侧边框粗细
            color: '#000', // 内部边框颜色
          },
        },
        predefineColors: [
          // 颜色选择器
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          'rgba(255, 69, 0, 0.68)',
          'rgb(255, 120, 0)',
          'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577',
        ],
      }
    },
    mounted() {
      this.borderAttrData = JSON.parse(JSON.stringify(this.borderAttr))
    },
    methods: {
      // 操作后更新数据
      updateFn() {
        this.$emit('borderAttrFn', this.borderAttrData)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .line {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    .borderLine,
    .medialLine {
      width: 300px;
      height: 200px;
      border: 1px solid #eee;
      padding: 20px;
      position: relative;
      .row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 6px 0;
        label {
          width: 60px;
        }
      }
    }
    .borderLine:before,
    .medialLine:before {
      content: '边框线';
      position: absolute;
      left: 10px;
      top: -10px;
      background-color: #fff;
      padding: 0 4px;
    }
    .medialLine:before {
      content: '内侧线';
    }
    .borderLine {
      margin-right: 20px;
    }
  }
</style>
