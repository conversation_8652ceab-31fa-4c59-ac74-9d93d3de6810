import Layout from '@/layout'

// 功能模块-首页
export default [
  {
    path: '/dataManagement',
    component: Layout,
    redirect: '/dataManagement/dataSourceManagement',
    name: 'dataManagement',
    code: 'dataManagement',
    meta: {
      title: '数据采集',
      icon: 'align-text-top-one',
      parentRouterName: 'governanceManage',
      code: 'dataManagement',
    },
    children: [
      {
        path: 'offlineDevelopment',
        name: 'offlineDevelopmentWork',
        code: 'offlineDevelopmentWork',
        component: () => import('@/views/data-management/offline-development/list.vue'),
        meta: {
          title: '离线作业',
          // keepAlive:true,
          code: 'offlineDevelopmentWork',
          parentRouterName: 'governanceManage',
          isIframe: true,
        },
      },
      {
        path: 'dataCollectionOverview',
        name: 'dataCollectionOverview',
        code: 'DataCollectionOverview',
        component: () => import('@/views/data-management/data-collection-overview/index.vue'),
        meta: {
          title: '采集总览',
          code: 'DataCollectionOverview',
          parentRouterName: 'governanceManage',
          unShowProject: true,
        },
      },
      {
        path: 'odsDirectory',
        name: 'odsDirectory',
        code: 'odsDirectory',
        redirect: '/dataManagement/odsDirectory/list',
        component: () => import('@/views/data-management/ods-directory/index.vue'),
        meta: {
          title: 'ODS目录',
          code: 'odsDirectory',
          parentRouterName: 'governanceManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'odsList',
            hidden: true,
            component: () => import('@/views/data-management/ods-directory/list.vue'),
            meta: {
              title: 'ODS目录',
              code: 'odsDirectory',
              activeMenu: '/dataManagement/odsDirectory',
              parentRouterName: 'governanceManage',
              unShowProject: true,
              keepAlive: true,
            },
          },
          {
            path: 'see',
            name: 'odsSee',
            hidden: true,
            component: () => import('@/views/data-management/ods-directory/see.vue'),
            meta: {
              title: 'ODS目录',
              activeMenu: '/dataManagement/odsDirectory',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
        ],
      },
      {
        path: 'dataSourceManagement',
        name: 'sceneDataSourceManagement',
        code: 'sceneDataSourceManagement',
        redirect: '/dataManagement/dataSourceManagement/index',
        component: () => import('@/views/data-management/data-source-management/index.vue'),
        meta: {
          title: '数据源管理',
          parentRouterName: 'governanceManage',
          code: 'sceneDataSourceManagement',
          unShowProject: true,
        },
        children: [
          {
            path: 'index',
            name: 'dataSourceManagementIndex',
            hidden: true,
            component: () => import('@/views/data-management/data-source-management/list.vue'),
            meta: {
              title: '数据源管理',
              activeMenu: '/dataManagement/dataSourceManagement',
              parentRouterName: 'governanceManage',
              unShowProject: true,
              keepAlive: true,
            },
          },
          {
            path: 'add',
            name: 'dataSourceManagementAdd',
            hidden: true,
            component: () => import('@/views/data-management/data-source-management/detail.vue'),
            meta: {
              title: '新增数据源',
              activeMenu: '/dataManagement/dataSourceManagement',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'edit',
            name: 'dataSourceManagementEdit',
            hidden: true,
            component: () => import('@/views/data-management/data-source-management/detail.vue'),
            meta: {
              title: '编辑数据源',
              activeMenu: '/dataManagement/dataSourceManagement',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'detail',
            name: 'dataSourceManagementDetail',
            hidden: true,
            component: () => import('@/views/data-management/data-source-management/see.vue'),
            meta: {
              title: '查看数据源',
              activeMenu: '/dataManagement/dataSourceManagement',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'editApi',
            name: 'dataSourceManagementApiEdit',
            hidden: true,
            component: () => import('@/views/data-management/data-source-management/apiSource.vue'),
            meta: {
              title: '编辑数据源',
              activeMenu: '/dataManagement/dataSourceManagement',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
        ],
      },
      {
        path: 'DataCollection',
        name: 'DataCollection',
        code: 'DataCollection',
        redirect: '/dataManagement/DataCollection/index',
        component: () => import('@/views/data-management/data-collection/index.vue'),
        meta: {
          title: '数据采集',
          parentRouterName: 'governanceManage',
          code: 'DataCollection',
          unShowProject: true,
        },
        children: [
          {
            path: 'index',
            name: 'dataCollectionIndex',
            hidden: true,
            component: () => import('@/views/data-management/data-collection/list.vue'),
            meta: {
              title: '数据采集',
              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
              keepAlive: true,
              code: 'DataCollection',
            },
          },
          {
            path: 'collectionAdd',
            name: 'collectionAdd',
            hidden: true,
            component: () => import('@/views/data-management/data-collection/add.vue'),
            meta: {
              title: '新增数据采集',

              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'collectionMoreAdd',
            name: 'collectionMoreAdd',
            hidden: true,
            component: () => import('@/views/data-management/data-collection/moreAdd.vue'),
            meta: {
              title: '新增数据采集',
              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'collectionEdit',
            name: 'collectionEdit',
            hidden: true,
            component: () => import('@/views/data-management/data-collection/add.vue'),
            meta: {
              title: '编辑数据采集',
              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'collectionDetail',
            name: 'collectionDetail',
            hidden: true,
            component: () => import('@/views/data-management/data-collection/structure-detail.vue'),
            meta: {
              title: '查看数据采集',
              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
          {
            path: 'collectionUnstructureDetail',
            name: 'collectionUnstructureDetail',
            hidden: true,
            component: () =>
              import('@/views/data-management/data-collection/unstructure-detail.vue'),
            meta: {
              title: '查看数据采集',
              activeMenu: '/dataManagement/DataCollection',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 资源目录
      {
        path: 'resourceDirectoryCollect',
        name: 'resourceDirectoryCollect',
        code: 'resourceDirectoryCollect',
        redirect: '/dataManagement/resourceDirectoryCollect/index',
        component: () => import('@/views/data-management/resource-directory/index.vue'),
        meta: {
          title: '资源目录',
          parentRouterName: 'governanceManage',
          code: 'resourceDirectoryCollect',
          unShowProject: true,
        },
        children: [
          {
            path: 'index',
            name: 'resourceDirectoryCollectIndex',
            hidden: true,
            component: () => import('@/views/data-management/resource-directory/list.vue'),
            meta: {
              title: '元数据目录',
              parentRouterName: 'governanceManage',
              unShowProject: true,
              code: 'resourceDirectoryCollect',
              keepAlive: true,
            },
          },
          {
            path: 'detail',
            name: 'resourceDirectoryCollectDetail',
            hidden: true,
            component: () => import('@/views/data-management/resource-directory/detail.vue'),
            meta: {
              title: '资源目录详情',
              parentRouterName: 'governanceManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 采集监控
      {
        path: 'collectionMonitor',
        name: 'collectionMonitor',
        code: 'collectionMonitor',
        redirect: '/dataManagement/collectionMonitor/index',
        component: () => import('@/views/data-management/collection-monitor/index.vue'),
        meta: {
          title: '采集监控',
          parentRouterName: 'governanceManage',
          code: 'collectionMonitor',
          unShowProject: true,
        },
        children: [
          {
            path: 'index',
            name: 'collectionMonitorIndex',
            hidden: true,
            component: () => import('@/views/data-management/collection-monitor/list.vue'),
            meta: {
              title: '采集监控',
              parentRouterName: 'governanceManage',
              unShowProject: true,
              code: 'collectionMonitor',
              keepAlive: true,
            },
          },
        ],
      },
      //运维监控
      {
        path: 'operationAndMaintenanceMonitoring',
        name: 'operationAndMaintenanceMonitoring',
        code: 'operationAndMaintenanceMonitoring',
        redirect: '/dataManagement/operationAndMaintenanceMonitoring/operationIndex',
        component: () =>
          import('@/views/data-management/operation-and-maintenance-monitoring/index.vue'),
        meta: {
          title: '运维监控',
          parentRouterName: 'governanceManage',
          code: 'operationAndMaintenanceMonitoring',
        },
        children: [
          {
            path: 'operationIndex',
            name: 'operationIndex',
            hidden: true,
            component: () =>
              import('@/views/data-management/operation-and-maintenance-monitoring/list.vue'),
            meta: {
              title: '运维监控',
              code: 'operationAndMaintenanceMonitoring',
              activeMenu: '/dataManagement/operationAndMaintenanceMonitoring',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: '/reportcollection',
            name: 'reportcollection',
            code: 'reportcollection',
            component: () => import('@/views/data-management/report-collection/list.vue'),
            meta: {
              title: '采集报表',
              activeMenu: '/dataManagement/operationAndMaintenanceMonitoring',
              parentRouterName: 'governanceManage',
              code: 'reportcollection',
            },
          },
        ],
      },
      // 元数据统计
      {
        path: 'metadataStatistics',
        name: 'metadataStatistics',
        code: 'metadataStatistics',
        component: () => import('@/views/data-management/metadata-statistics/list.vue'),
        meta: {
          title: '元数据统计',
          parentRouterName: 'governanceManage',
          code: 'metadataStatistics',
        },
      },

      // 日志文件管理
      {
        path: 'logFileManagement',
        name: 'logFileManagement',
        code: 'logFileManagement',
        redirect: '/dataManagement/logFileManagement/logQuery',
        component: () => import('@/views/log-file-management/index.vue'),
        meta: {
          title: '日志文件管理',
          parentRouterName: 'governanceManage',
          code: 'logFileManagement',
        },
        children: [
          {
            path: 'logQuery',
            name: 'logQuery',
            code: 'logQuery',
            component: () => import('@/views/log-file-management/log-query.vue'),
            meta: {
              title: '日志查询',
              activeMenu: '/dataManagement/logFileManagement',
              parentRouterName: 'governanceManage',
              code: 'logQuery',
            },
          },
          {
            path: 'alarmManagement',
            name: 'alarmManagement',
            code: 'alarmManagement',
            component: () => import('@/views/log-file-management/alarm-management.vue'),
            meta: {
              title: '报警管理',
              activeMenu: '/dataManagement/logFileManagement',
              parentRouterName: 'governanceManage',
              code: 'alarmManagement',
            },
          },
          {
            path: 'errorStatistics',
            name: 'errorStatistics',
            code: 'errorStatistics',
            component: () => import('@/views/log-file-management/error-statistics.vue'),
            meta: {
              title: '错误统计',
              activeMenu: '/dataManagement/logFileManagement',
              parentRouterName: 'governanceManage',
              code: 'errorStatistics',
            },
          },
        ],
      },
    ],
  },
]
