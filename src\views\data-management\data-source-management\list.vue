<template>
  <div :class="{ 'data-source-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <!-- <div class="row tabs">
        <n-tabs v-model="state.approve" @active-tab-change="tabChange">
          <n-tab title="可用数据源" id="0" />
          <n-tab title="未授权数据源" id="1" />
        </n-tabs>
      </div> -->
      <div class="row">
        <div class="col">
          <div class="searchTool">
            数据源类型：
            <n-select
              v-model="state.originalFormInline.sourceType"
              placeholder="数据源类型"
              allow-clear
              filter
              :options="
                state.org_dataSourceType.map((val) => {
                  return { ...val, name: val.label, value: val.value }
                })
              "
              :key="state.key"
            />
            数据源密级：
            <n-select
              v-model="state.originalFormInline.confidentialityLevel"
              allow-clear
              filter
              :key="state.key"
              placeholder="请选择数据源密级"
            >
              <n-option
                v-for="item in state.confidentialityLevelOptions"
                :key="item.value"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
            创建时间：
            <n-range-date-picker-pro
              class="createTime"
              v-model="state.originalFormInline.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              :shortcuts="state.shortcuts"
              allow-clear
            />
            数据源名称：
            <n-input
              v-model="state.originalFormInline.keyword"
              placeholder="请输入数据源名称"
              size="small"
              clearable
            />
            IP检索：
            <n-input
              v-model="state.originalFormInline.host"
              placeholder="请输入数据源IP"
              size="small"
              clearable
            />
          </div>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <section class="main">
      <!-- 数据分类树 -->
      <section class="asideTree">
        <div class="asideTree-title">
          <div>
            <span class="line"></span>
            <span>数据分类</span>
          </div>

          <SvgIcon
            class="tree-add"
            @click="dataTreeClick('add', state.treeData[0])"
            icon="icon-tree-add2"
          />
        </div>
        <div class="asideTree-box">
          <n-input
            class="asideTree-search-input"
            v-model="state.treeSearchText"
            placeholder="请输入关键词"
            @input="searchTreeFn"
          >
            <template #append>
              <n-button class="search-btn" icon="search" />
            </template>
          </n-input>

          <CfTtee
            ref="treeRef"
            isFloating
            :check-on-click-node="true"
            :default-expanded-keys="[state.selectedKey]"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          >
            <template #btns="{ node, data: nodeData }">
              <el-popover trigger="click" placement="right-start">
                <template #reference>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    @click="getNodeIdFn(node)"
                  >
                    <circle
                      cx="8"
                      cy="12.5"
                      r="0.5"
                      transform="rotate(-90 8 12.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="8"
                      r="0.5"
                      transform="rotate(-90 8 8)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                    <circle
                      cx="8"
                      cy="3.5"
                      r="0.5"
                      transform="rotate(-90 8 3.5)"
                      fill="currentColor"
                      stroke="currentColor"
                    />
                  </svg>
                </template>
                <ul class="cf-list">
                  <li class="cf-list-item" @click="dataTreeClick('add', nodeData)">
                    <SvgIcon class="tree-icon-tool" icon="icon-tree-add2" />
                    新增</li
                  >
                  <li
                    class="cf-list-item"
                    v-if="nodeData.parentEnName !== null"
                    @click="dataTreeClick('edit', nodeData)"
                  >
                    <SvgIcon class="tree-icon-tool" icon="icon-tree-edit2" />
                    编辑</li
                  >
                  <li
                    class="cf-list-item"
                    v-if="nodeData.parentEnName !== null"
                    @click="delDataTree(nodeData)"
                  >
                    <SvgIcon class="tree-icon-tool" @click="" icon="icon-tree-del" />
                    删除</li
                  >
                </ul>
              </el-popover>
            </template>
          </CfTtee>
        </div>
      </section>

      <!-- 内容 -->
      <div class="content">
        <div class="content-title">
          <div>
            <n-button
              v-if="buttonAuthList.includes('dataManagement_dataSourceManagement_add')"
              code="dataManagement_dataSourceManagement_add"
              variant="solid"
              class="add"
              @click.prevent.stop="checkDataSourceType"
            >
              <SvgIcon icon="new-add" class="icon" title="新建数据源" />新建数据源
            </n-button>
          </div>
          <div class="switch">
            <div v-if="state.switchType === 1" class="switch-label" @click.prevent="switchFn(0)">
              <SvgIcon class="icon-switch" icon="icon-card-switch" />
              <span>卡片</span>
            </div>
            <div v-else class="switch-label" @click.prevent="switchFn(1)">
              <SvgIcon class="icon-switch" icon="icon-table-switch" />
              <span>列表</span>
            </div>
          </div>
        </div>

        <div class="data-source-page">
          <div v-loading="state.loading" class="data-source-page-content">
            <div :class="state.cardData.length > 0 ? 'table-list' : 'table-list empty-list'">
              <div v-show="state.cardData.length > 0 && state.switchType === 0" class="card-list">
                <div class="card" v-for="item in state.cardData" :key="item">
                  <div class="card-top">
                    <div class="top-left">
                      <img class="card-right-img" :src="getAssetsImages(item.datasourceType)" />
                    </div>
                    <div class="top-right">
                      <div class="title">
                        <div class="title-name" :title="item.name">{{ item.name }}</div>
                        <div class="title-operate">
                          <n-popover
                            :class="{
                              'table-list-card-popover': true,
                              'card ': true,
                              'more-lang':
                                item.type === 'PROJECT' &&
                                item.envType === 'TEST' &&
                                item.status === 'CREATED',
                            }"
                            :position="['bottom-end']"
                            align="end"
                            trigger="hover"
                          >
                            <template #content>
                              <div class="table-list-card-header-popover">
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_unbind_edit',
                                    ) && item.type === 'PUBLIC'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardUnbind(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-card-unbind" />解绑
                                </n-button>
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_off_edit',
                                    ) &&
                                    item.type === 'PROJECT' &&
                                    item.envType === 'TEST' &&
                                    item.status === 'CREATED'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardSyncFn(item)"
                                >
                                  <span>
                                    <SvgIcon class="icon" icon="icon-card-sync-new" />同步至生产
                                  </span>
                                </n-button>
                                <!-- <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_off_edit',
                                    ) &&
                                    item.status === 'PUBLISHED' &&
                                    item.type !== 'PUBLIC'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardOffFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-card-off-new" />下架
                                </n-button>
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_publish_edit',
                                    ) &&
                                    item.status !== 'PUBLISHED' &&
                                    item.type !== 'PUBLIC'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardSendFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-card-send-new" />发布
                                </n-button> -->
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_edit',
                                    )
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardEditFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-new-edit" />编辑
                                </n-button>
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_delete',
                                    ) &&
                                    item.type !== 'PUBLIC' &&
                                    item.status !== 'PUBLISHED'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardDelFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-new-delete" />删除
                                </n-button>
                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_test_edit',
                                    ) &&
                                    item.type !== 'PUBLIC' &&
                                    item.status !== 'PUBLISHED'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardTestConnected(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-card-test-new" />测试
                                </n-button>

                                <n-button
                                  v-if="
                                    buttonAuthList.includes(
                                      'dataManagement_dataSourceManagement_view',
                                    ) &&
                                    item.status === 'PUBLISHED' &&
                                    item.type !== 'PUBLIC'
                                  "
                                  class="table-list-card-header-popover-label"
                                  variant="text"
                                  @click.stop.prevent="cardSeeFn(item)"
                                >
                                  <SvgIcon class="icon" icon="icon-new-see" />查看
                                </n-button>
                              </div>
                            </template>
                            <SvgIcon class="icon-more" icon="icon-more" @click.prevent.stop="" />
                          </n-popover>
                        </div>
                      </div>
                      <div class="message">{{ '创建人 ' + item.createByName }}</div>
                    </div>
                  </div>
                  <div class="card-mid">
                    <div class="description" :title="item.description">{{
                      item.description || '暂无描述'
                    }}</div>
                  </div>
                  <div class="card-bottom">
                    <div class="card-bottom-col">
                      <div :class="['card-bottom-col-label', stateTransition(item.status, true)]">{{
                        stateTransition(item.status, 'cName')
                      }}</div>
                      <div
                        :class="[
                          'card-bottom-col-label',
                          stateTransition(item.confidentialityLevel, true),
                        ]"
                        >{{ stateTransition(item.confidentialityLevel, 'cName') }}</div
                      >
                    </div>
                    <div class="card-bottom-col">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
              <div v-if="state.cardData.length === 0 && state.switchType === 0" class="empty">
                <img class="empty-img" src="@/assets/table-no-content.png" />
                <p class="empty-text">暂无数据源，请先新增</p>
              </div>
              <div v-show="state.switchType === 1" class="table-list-content">
                <CfTable
                  ref="publicTable"
                  saveWidth
                  :isDisplayAction="true"
                  :table-head-titles="state.tableHeadTitles"
                  :tableConfig="{
                    data: state.tableData.list,
                    rowKey: 'id',
                  }"
                  actionWidth="200"
                  :paginationConfig="{
                    total: state.pageInfo.total,
                    pageSize: state.pageInfo.pageSize,
                    currentPage: state.pageInfo.currentPage,
                    onCurrentChange: (v) => {
                      state.pageInfo.currentPage = v
                      initTable(false)
                    },
                    onSizeChange: (v) => {
                      state.pageInfo.pageSize = v
                      initTable(false)
                    },
                  }"
                  emptyText="暂无运行结果"
                  @tablePageChange="tablePageChange"
                >
                  <template #name="{ row }">
                    <div class="taskName">
                      {{ row.name }}
                    </div>
                  </template>
                  <template #datasourceType="{ row }">
                    <div class="datasourceType">
                      <img
                        v-if="row.datasourceType"
                        class="datasourceType-img"
                        :src="getAssetsImages(row.datasourceType)"
                      />
                      {{ row.datasourceType }}
                    </div>
                  </template>
                  <template #confidentialityLevel="{ row }">
                    <div
                      :class="[
                        'confidentialityLevel',
                        stateTransition(row.confidentialityLevel, true),
                      ]"
                    >
                      {{ stateTransition(row.confidentialityLevel, 'cName') }}
                    </div>
                  </template>
                  <template #envType="{ row }">
                    <div class="envType">
                      <div :class="{ 'envType-name': true, test: row.envType === 'TEST' }">{{
                        row.envType === 'TEST'
                          ? '开发环境'
                          : row.envType === 'OFFICIAL'
                          ? '正式环境'
                          : '--'
                      }}</div>
                    </div>
                  </template>

                  <template #taskStatus="{ row }">
                    <div class="taskStatus">
                      <div :class="['circle', stateTransition(row.status, 'iconName')]"></div>
                      {{ stateTransition(row.status, 'cName') }}
                    </div>
                  </template>
                  <template #editor="{ row }">
                    <div class="edit-box">
                      <n-button
                        v-if="
                          buttonAuthList.includes(
                            'dataManagement_dataSourceManagement_unbind_edit',
                          ) && row.type === 'PUBLIC'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardUnbind(row)"
                      >
                        解绑
                      </n-button>
                      <n-button
                        v-if="
                          buttonAuthList.includes('dataManagement_dataSourceManagement_off_edit') &&
                          row.type === 'PROJECT' &&
                          row.status === 'CREATED' &&
                          row.envType === 'TEST'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardSyncFn(row)"
                      >
                        同步至生产
                      </n-button>
                      <!-- <n-button
                        v-if="
                          buttonAuthList.includes('dataManagement_dataSourceManagement_off_edit') &&
                          row.status === 'PUBLISHED' &&
                          row.type !== 'PUBLIC'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardOffFn(row)"
                      >
                        下架
                      </n-button>
                      <n-button
                        v-if="
                          buttonAuthList.includes(
                            'dataManagement_dataSourceManagement_publish_edit',
                          ) &&
                          row.status !== 'PUBLISHED' &&
                          row.type !== 'PUBLIC'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardSendFn(row)"
                        >发布
                      </n-button> -->
                      <n-button
                        v-if="buttonAuthList.includes('dataManagement_dataSourceManagement_edit')"
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardEditFn(row)"
                      >
                        编辑
                      </n-button>
                      <n-button
                        v-if="
                          buttonAuthList.includes('dataManagement_dataSourceManagement_delete') &&
                          row.type !== 'PUBLIC' &&
                          row.status !== 'PUBLISHED'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardDelFn(row)"
                      >
                        删除
                      </n-button>
                      <n-button
                        v-if="
                          buttonAuthList.includes(
                            'dataManagement_dataSourceManagement_test_edit',
                          ) &&
                          row.type !== 'PUBLIC' &&
                          row.status !== 'PUBLISHED'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardTestConnected(row)"
                      >
                        测试
                      </n-button>
                      <n-button
                        v-if="
                          buttonAuthList.includes('dataManagement_dataSourceManagement_view') &&
                          row.status === 'PUBLISHED' &&
                          row.type !== 'PUBLIC'
                        "
                        class="seeDetails has-right-border"
                        variant="text"
                        @click.stop.prevent="cardSeeFn(row)"
                      >
                        查看
                      </n-button>
                    </div>
                  </template>
                </CfTable>
              </div>
            </div>
            <div
              v-if="state.switchType === 0"
              class="data-source-page-content-pagination nancalui-table-page"
            >
              <n-pagination
                :total="state.pageInfo.total"
                v-model:pageSize="state.pageInfo.pageSize"
                v-model:pageIndex="state.pageInfo.currentPage"
                :can-change-page-size="true"
                canViewTotal
                :page-size-options="[10, 20, 50, 100]"
                @page-index-change="cardCurrentChange"
                @page-size-change="cardSizeChange"
              />
            </div>
            <checkDataSourceType
              ref="allDataSourceType"
              @refresh="initTable"
              @checkSource="checkSource"
            />
          </div>
        </div>
      </div>
    </section>

    <!--同步至生产弹窗-->
    <n-modal
      v-model="state.syncDialog"
      class="largeDialog"
      width="580px"
      :close-on-click-overlay="false"
      @close="state.syncDialog = false"
    >
      <div class="sync-content">
        <div class="tips"
          ><n-icon name="icon-warning-o" />
          <div>数据源同步提醒</div>
        </div>
        <div class="h2">确定修改数据源名称并发布至生产？</div>
        <n-form
          ref="syncForm"
          :data="state.syncForm"
          :rules="state.syncRules"
          label-align="start"
          label-width="126px"
        >
          <n-form-item label="数据源名称：" field="name">
            <n-input v-model="state.syncForm.name" maxlength="30" placeholder="请输入数据源名称" />
          </n-form-item>
        </n-form>
      </div>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button variant="solid" :loading="state.saveLoading" @click.prevent="saveSyncFn"
            >确 定</n-button
          >
          <n-button @click.prevent="state.syncDialog = false">取 消</n-button>
        </div>
      </template>
    </n-modal>
    <!--新建数据源-->
    <n-modal
      v-model="state.showAddDrawer"
      width="560px"
      class="addDatasourcePop"
      bodyClass="addDatasourceBodyPop"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="true"
      :before-close="
        () => {
          state.showAddDrawer = false
        }
      "
    >
      <template #header>
        <n-modal-header>
          <span class="line"></span>
          <span v-if="state.form.isSee" class="title">查看数据源</span>
          <span v-else-if="state.form.id" class="title">编辑数据源</span>
          <span v-else class="title">新建数据源</span>
        </n-modal-header>
      </template>
      <div class="n-drawer-body">
        <div
          class="n-drawer-body-content has-top-padding scroll-bar-style"
          :style="state.form.isSee ? 'height: calc(100% - 60px)' : ''"
        >
          <dataSourceForm
            ref="dataSourceFormRef"
            v-if="state.showAddDrawer"
            :categoryId="state.categoryId"
            :query="state.form"
            @close="closeFn"
          />
        </div>
      </div>
    </n-modal>

    <!-- 数据分类树增改弹窗 -->
    <n-modal
      v-model="state.dataTreeShow"
      width="560px"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="false"
      :before-close="
        () => {
          state.dataTreeShow = false
        }
      "
      class="dataTreePop"
      bodyClass="dataTreeBodyPop"
    >
      <template #header>
        <n-modal-header>
          <span class="line"></span>
          <span>{{ state.dataTreeData.type === 'add' ? '新建' : '编辑' }}数据分类</span>
        </n-modal-header>
      </template>
      <n-form :data="state.dataTreeData" ref="dataTreeRef" labelSuffix="：" label-width="90px">
        <n-form-item
          field="name"
          label="分类名称"
          :rules="[{ required: true, message: '分类名称不能为空', trigger: 'blur' }]"
        >
          <n-input v-model="state.dataTreeData.name" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-modal-footer style="text-align: right; padding-right: 30px">
          <n-button class="dataTreePop-btn" @click="state.dataTreeShow = false">取消</n-button>
          <n-button class="dataTreePop-btn" @click="saveDataTree" variant="solid">确定</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>

  <section class="soldOutPop" v-show="state.soldOutShow">
    <div class="box">
      <div class="title"
        ><n-icon class="logo" name="icon-warning-o" />
        <span>数据源下架提醒</span>
        <svg
          class="close"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          width="24px"
          height="24px"
          viewBox="0 0 24 24"
          @click="state.soldOutShow = false"
        >
          <path
            d="M4.5 19.4995L19.4995 4.49998"
            stroke="#8091B7"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.5 4.50049L19.4995 19.5"
            stroke="#8091B7"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <div class="msg">{{ state.msg }}</div>
      <div class="footer">
        <n-button variant="solid" @click="soldOutFn">确认</n-button>
      </div>
    </div>
  </section>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, toRef, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { checkCName } from '@/utils/validate'
  import api from '@/api/index'
  import {
    queryDataTypeTree,
    addDataTypeTree,
    editDataTypeTree,
    delDataTypeTree,
  } from '@/api/dataManage'
  import cardList from '@/components/CardList'
  import { formartTime } from '@/utils/index'
  import { ElNotification } from 'element-plus'
  import ENUM from '@/const/enum'
  import checkDataSourceType from './components/check-dataSoure-type'
  import dataSourceForm from './detail'
  import CfTtee from '@/components/cfTtee'

  export default {
    name: '',
    components: { cardList, checkDataSourceType, dataSourceForm },
    props: {},

    setup() {
      const router = useRouter()
      const store = useStore()
      const dataTreeRef = ref()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      // const buttonAuthList = [
      //   'dataManagement_dataSourceManagement_add',
      //   'dataManagement_dataSourceManagement_delete',
      //   'dataManagement_dataSourceManagement_edit',
      //   'dataManagement_dataSourceManagement_apply',
      //   'dataManagement_dataSourceManagement_see',
      //   'dataManagement_dataSourceManagement_publishAndStoped',
      // ]
      const allDataSourceType = ref()
      const syncForm = ref()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        msg: '',
        soldOutShow: false,
        soldOutData: null,
        key: 1,
        switchType: 1, //列表展示样式(0-卡片，1-表格)
        approve: '0', //批准(0-可用，1-未授权)
        dataStructureType: 'ALL_STRUCTURE',
        loading: true,
        saveLoading: false,
        dataTreeShow: false,
        dataTreeData: {
          name: '',
          type: 'add',
        },
        confidentialityLevelOptions: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ],
        shortcuts: ENUM.SHORTCUTS,
        org_dataSourceStatus: [],
        originalFormInline: {
          sourceType: null,
          time: [],
          keyword: null,
          confidentialityLevel: '',
          host: '',
        },
        formInline: {
          sourceType: null,
          envType: null,
          time: [],
          keyword: null,
          status: 'all',
          createTime: ['', ''],
          confidentialityLevel: '',
        },
        showAddDrawer: false,
        form: { isSee: false, databaseType: null },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'name', name: '数据源名称', slot: 'name', width: 200 },
          { prop: 'datasourceType', name: '数据库类型', slot: 'datasourceType' },
          { prop: 'confidentialityLevel', name: '数据源密级', slot: 'confidentialityLevel' },
          // { prop: 'typeText', name: '数据源权限' },
          // { prop: 'envType', name: '数据环境', slot: 'envType' },
          { prop: 'status', name: '状态', slot: 'taskStatus' },
          { prop: 'dataCategoryName', name: '数据分类' },
          { prop: 'host', name: 'IP地址' },
          { prop: 'description', name: '描述信息' },
          { prop: 'createTime', name: '创建时间', width: 170 },
          { prop: 'createByName', name: '创建人' },
        ],
        org_dataSourceType: [],
        cardData: [],
        tableData: [],
        tableHeight: 336,
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
          pages: 1,
        },
        startTime: null,
        endTime: null,
        isAdmin: false,
        name: null,
        syncForm: {
          id: '',
          name: '',
        },
        syncRules: {
          name: [{ required: true, validator: checkCName, trigger: 'blur' }],
        },
        syncDialog: false,
        treeSearchText: '',
        categoryId: '',
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        expandedKeys: [],
        selectedKey: null,
      })
      const projectCode = toRef(store.state.user.currentProject, 'projectCode').value

      // 查找当前node.id的父级id
      function findParentId(tree, id, parentId = null) {
        for (let node of tree) {
          if (node.id === id) {
            return parentId
          }
          if (node.children && node.children.length) {
            const res = findParentId(node.children, id, node.id)
            if (res !== null) return res
          }
        }
        return null
      }

      const methods = {
        resetFn() {
          state.originalFormInline = {
            sourceType: null,
            envType: null,
            time: [],
            keyword: null,
            host: '',
            confidentialityLevel: '',
          }
          methods.searchClickFn()
        },
        //匹配状态设置中文名称和图标
        stateTransition(status, needCn = false) {
          let _status = 2
          let _statusCn = '待发布'
          let iconClassName = 'blue'
          switch (status) {
            case 'CREATED':
              _status = 2
              _statusCn = '待发布'
              iconClassName = 'blue'
              break
            case 'PUBLISHED':
              _status = 1
              _statusCn = '已发布'
              iconClassName = 'green'
              break
            case 'OFFLINE':
              _status = 0
              _statusCn = '已下架'
              iconClassName = 'gray'
              break
            case 'PUBLIC':
              _status = 0
              _statusCn = '公开'
              iconClassName = 'skyblue'
              break
            case 'INTERIOR':
              _status = 0
              _statusCn = '内部'
              iconClassName = 'orange'
              break

            case 'CONTROLLED':
              _status = 0
              _statusCn = '受控'
              iconClassName = 'cBlue'
              break
            case 'SECRET':
              _status = 0
              _statusCn = '秘密'
              iconClassName = 'red'
              break
            case 'CONFIDENTIAL':
              _status = 0
              _statusCn = '机密'
              iconClassName = 'brown'
              break

            case 'CORE':
              _status = 0
              _statusCn = '核心'
              iconClassName = 'drakBlue'
              break
            default:
              break
          }

          return needCn ? (needCn === 'cName' ? _statusCn : iconClassName) : _status
        },
        // 获取图片
        getAssetsImages(name) {
          return new URL(`/src/assets/img/dev/card-source/${name}.png`, import.meta.url).href //本地文件路径
        },
        // 切换展示类型
        switchFn(index) {
          state.switchType = index
          // state.pageInfo.pageSize = state.switchType === 0 ? 12 : 10
          localStorage.setItem('projectSourceSwitchType', index)
          methods.initTable(true)
        },
        // 同步至生产
        saveSyncFn() {
          syncForm.value.validate((val) => {
            if (val) {
              let params = {
                id: state.syncForm.id,
                name: state.syncForm.name,
              }
              state.saveLoading = true
              api.project
                .copyAsOfficialDataSource(params)
                .then((res) => {
                  state.saveLoading = false
                  if (res.success) {
                    state.syncDialog = false
                    ElNotification({
                      title: '提示',
                      message: '同步数据源成功!',
                      type: 'success',
                    })
                    if (state.switchType === 0) {
                      methods.initTable(true)
                    } else {
                      methods.initTable(false)
                    }
                  }
                })
                .catch(() => (state.saveLoading = false))
            }
          })
        },
        //未授权数据源申请授权
        cardDataSourceApply(item) {
          proxy.$MessageBoxService.open({
            title: '申请授权',
            content: '确定向项目管理人员发出数据源使用申请',
            save: () => {
              api.project.datasourceApply({ id: item.id }).then((res) => {
                if (res.success) {
                  ElNotification({
                    title: '提示',
                    message: '申请授权成功!',
                    type: 'success',
                  })
                }
              })
            },
          })
        },
        // 解绑数据源
        cardUnbind(item) {
          api.project.unbindDatasource({ id: item.id }).then((res) => {
            let { success, data } = res
            if (success) {
              if (data.success) {
                ElNotification({
                  title: '提示',
                  message: '解绑成功！！！',
                  type: 'success',
                })
                if (state.switchType === 0) {
                  methods.initTable(true)
                } else {
                  methods.initTable(false)
                }
              } else {
                if (data.reason === 'USED_IN_DATA_COLLECT_AND_JOB_RUNNING') {
                  proxy.$MessageBoxService.open({
                    title: '数据源解绑提醒',
                    content: '此数据源已被使用，不可解绑！！！',
                    save: () => {},
                  })
                } else if (
                  data.reason === 'USED_IN_DATA_COLLECT_BUT_JOB_NOT_RUNNING' ||
                  data.reason === 'DATASOURCE_UNBIND_ACK'
                ) {
                  let msg = '解绑此数据源将会导致对应的采集任务不可用，确认解绑？'
                  if (data.reason === 'DATASOURCE_UNBIND_ACK') {
                    msg = '确定解绑此数据源？'
                  }
                  proxy.$MessageBoxService.open({
                    title: '数据源解绑提醒',
                    content: msg,
                    save: () => {
                      api.project.unbindSafeDatasource({ id: data.dataSourceAckId }).then((res) => {
                        if (res.success) {
                          ElNotification({
                            title: '提示',
                            message: '解绑成功！！！',
                            type: 'success',
                          })
                          if (state.switchType === 0) {
                            methods.initTable(true)
                          } else {
                            methods.initTable(false)
                          }
                        }
                      })
                    },
                  })
                }
              }
            }
          })
        },
        // 同步至生产
        cardSyncFn(item) {
          state.syncForm.id = item.id
          state.syncForm.name = ''
          state.syncDialog = true
        },
        //新增数据源
        checkDataSourceType() {
          allDataSourceType.value.show()
          // if (projectCode) {
          //   allDataSourceType.value.show()
          // } else {
          //   ElNotification({
          //     title: '提示',
          //     message: '请联系管理员分配场景权限',
          //     type: 'warning',
          //   })
          // }
        },
        tabChange() {
          state.formInline.status = 'all'
          state.formInline.sourceType = null
          state.formInline.keyword = null
          state.formInline.time = []
          setTimeout(() => {
            methods.initTable(true)
          }, 10)
        },
        // 获取树列表
        getTreeListFn(status) {
          queryDataTypeTree({}).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []

              treeData = [res.data]
              treeData[0].selected = true
              state.categoryId = treeData[0].id

              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]

              if (!status) {
                // 恢复展开和选中
                nextTick(() => {
                  state.expandedKeys = [state.treeData?.[0]?.id || 1]
                  state.selectedKey = state.treeData?.[0]?.id || 1
                })
              }

              methods.onSearch(true)
            }
          })
        },
        dataTreeClick(type, node) {
          state.dataTreeData.name = ''
          state.dataTreeShow = true
          state.dataTreeData.type = type
          state.dataTreeData.info = node
          if (type === 'edit') {
            state.dataTreeData.name = node.name
          }
        },
        // 保存数据分类树
        saveDataTree() {
          dataTreeRef.value.validate((isValid) => {
            if (isValid) {
              const url = state.dataTreeData.type === 'add' ? addDataTypeTree : editDataTypeTree
              const params = {
                id: state.dataTreeData.info.id,
                name: state.dataTreeData.name,
                parentEnName: state.dataTreeData.info.enName,
              }
              if (state.dataTreeData.type !== 'add') {
                delete params.parentEnName
              }

              url(params).then((res) => {
                if (res.success) {
                  const hint = state.dataTreeData.type === 'add' ? '新建' : '编辑'
                  methods.getTreeListFn('update')
                  state.dataTreeShow = false
                  state.dataTreeData.name = ''

                  ElNotification({
                    title: '提示',
                    message: hint + '成功!',
                    type: 'success',
                  })
                }
              })
            }
          })
        },
        delDataTree(node) {
          proxy.$MessageBoxService.open({
            title: '删除当前数据分类',
            content: `是否确定删除${node.name}？`,
            save: () => {
              delDataTypeTree({
                id: node.id,
              }).then((res) => {
                if (res.success) {
                  ElNotification({
                    title: '提示',
                    message: `删除${node.name}成功!`,
                    type: 'success',
                  })

                  state.selectedKey = findParentId(state.treeData, node.id)
                  methods.getTreeListFn('delete')
                }
              })
            },
          })
        },
        // 树搜索
        searchTreeFn() {
          state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        getNodeIdFn(node) {
          state.selectedKey = node.data.id
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.id) {
            state.categoryId = node.id === -1 ? null : node.id
            methods.onSearch(true)
          }
        },
        // 发布
        cardSendFn(item) {
          proxy.$MessageBoxService.open({
            title: '是否确认发布该数据源',
            content: '发布后，该数据源内的数据将用于数据任务，确定是否需要发布？',
            save: () => {
              const loading = proxy.$loading({
                lock: true,
                text: '发布中。。。',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
              })
              api.project
                .sendDatasource({ id: item.id })
                .then((res) => {
                  loading?.close()
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '发布成功!',
                      type: 'success',
                    })
                    if (state.switchType === 0) {
                      methods.initTable(true)
                    } else {
                      methods.initTable(false)
                    }
                  }
                })
                .catch(() => {
                  loading?.close()
                })
            },
          })
        },
        // 删除
        async cardDelFn(item) {
          //开发环境数据源先调用接口查询是否可以删除
          let canDel = false
          if (item.envType === 'TEST') {
            canDel = await api.project
              .deleteDatasourceValid({ id: item.id })
              .then((res) => {
                let { success, data } = res
                if (success) {
                  if (data.reason === 'DATASOURCE_DELETE_ACK' && data.dataSourceAckId) {
                    return true
                  } else {
                    return false
                  }
                } else {
                  canDel = 'error'
                }
              })
              .catch(() => {
                canDel = 'error'
              })
          } else {
            canDel = true
          }

          if (canDel === 'error') {
            return
          }
          if (!canDel) {
            proxy.$MessageBoxService.open({
              title: '提示',
              content: '该数据源已被使用，暂不可删除',
            })
            return
          }
          proxy.$MessageBoxService.open({
            title: '是否确认删除该数据源',
            content: '删除后数据将不可恢复',
            save: () => {
              api.project.deleteDatasource({ id: item.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除成功',
                    type: 'success',
                  })
                  if (state.switchType === 0) {
                    methods.initTable(true)
                  } else {
                    methods.initTable(false)
                  }
                }
              })
            },
          })
        },
        // 编辑
        cardEditFn(item) {
          if (item.datasourceType === 'API') {
            router.push({ name: 'dataSourceManagementApiEdit', query: { id: item.id } })
          } else {
            state.form = { id: item.id, type: 'EDIT', databaseType: item.datasourceType }
            state.showAddDrawer = true
          }
        },
        // 下架
        cardOffFn(item) {
          api.project.downDatasource({ id: item.id }).then((res) => {
            let { success, data } = res
            if (success) {
              if (data.success) {
                ElNotification({
                  title: '提示',
                  message: '下架成功！！！',
                  type: 'success',
                })
                if (state.switchType === 0) {
                  methods.initTable(true)
                } else {
                  methods.initTable(false)
                }
              } else {
                if (
                  data.reason === 'USED_IN_DATA_COLLECT_AND_JOB_RUNNING' ||
                  data.reason === 'USED_IN_DATA_COLLECT'
                ) {
                  state.soldOutShow = true
                  state.msg = '此数据源已被使用，不可下架！！！'
                } else if (
                  data.reason === 'USED_IN_DATA_COLLECT_BUT_JOB_NOT_RUNNING' ||
                  data.reason === 'DATASOURCE_OFFLINE_ACK'
                ) {
                  state.soldOutShow = true
                  state.msg = '下架此数据源将会导致对应的采集任务不可用，确认下架？'
                  if (data.reason === 'DATASOURCE_OFFLINE_ACK') {
                    state.msg = '确定下架此数据源？'
                  }
                  state.soldOutData = data
                }
              }
            }
          })
        },
        soldOutFn() {
          state.soldOutShow = false

          if (state.msg !== '此数据源已被使用，不可下架！！！') {
            api.project.offSafeDatasource({ id: state.soldOutData.dataSourceAckId }).then((res) => {
              if (res.success) {
                ElNotification({
                  title: '提示',
                  message: '下架成功！！！',
                  type: 'success',
                })
                if (state.switchType === 0) {
                  methods.initTable(true)
                } else {
                  methods.initTable(false)
                }
              }
            })
          }
        },
        // 查看
        cardSeeFn(item) {
          // state.form = {
          //   id: item.id,
          //   isSee: true,
          //   type: 'DETAIL',
          //   databaseType: item.datasourceType,
          //   dataStructureType: item.dataStructureType,
          // }
          // state.showAddDrawer = true

          console.log(item)

          router.push({
            name:
              item.datasourceType === 'API'
                ? 'dataSourceManagementApiEdit'
                : 'dataSourceManagementDetail',
            query: { type: 'DETAIL', id: item.id },
          })
        },
        // 运行
        cardTestConnected(item) {
          let { id } = item
          let _ruleForm = {
            id,
          }
          state.loading = true
          api.project
            .testDatasourceWithId(_ruleForm)
            .then((res) => {
              let { success } = res.data
              state.loading = false
              if (success) {
                item.dataSourceStatus = 1
                ElNotification({
                  title: '提示',
                  message: '测试连接成功',
                  type: 'success',
                })
              } else {
                item.dataSourceStatus = 2
                ElNotification({
                  title: '提示',
                  message: '测试连接失败',
                  type: 'error',
                })
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 获取数据源状态下拉
        getDatasourceStatus() {
          api.project.getDatasourceStatus().then((res) => {
            let { success, data } = res
            if (success) {
              data.unshift({ label: '全部', value: 'all' })
              state.org_dataSourceStatus = data
            }
          })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.initTable(false)
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch()
        },
        onSearch() {
          let { time, keyword } = state.formInline
          state.name = keyword ? keyword : null
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }
          state.key++
          methods.initTable(true)
        },
        // 时间转化
        convertDateFormat(dateStr) {
          const date = new Date(dateStr)
          const year = date.getFullYear()
          const month = String(date.getMonth() + 1).padStart(2, '0')
          const day = String(date.getDate()).padStart(2, '0')
          const hours = String(date.getHours()).padStart(2, '0')
          const minutes = String(date.getMinutes()).padStart(2, '0')
          const seconds = String(date.getSeconds()).padStart(2, '0')
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        },
        initTable(init = false) {
          // init 是否初始化为第一页
          let _status = null
          if (state.formInline.status === 'all') {
            _status = null
          } else {
            _status = state.formInline.status
          }
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage

          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              status: _status || null,
              datasourceType: state.formInline.sourceType || null,
              envType: state.formInline.envType || null,
              name: state.name || null,
              startTime: state.startTime || null,
              host: state.formInline.host || null,
              // startTime: createTime,
              endTime: state.endTime || null,
              // createTime: null,
              dataStructureType: null,
              approve: Number(state.approve),
              dataCategoryId: state.categoryId || null,
              confidentialityLevel: state.formInline.confidentialityLevel || null,
            },
          }
          if (init) {
            state.loading = true
          }
          api.project
            .getDatasourceList(data)
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                data.list.forEach((item) => {
                  let {
                    name,
                    createTime,
                    createByName,
                    image,

                    status,
                    type,
                  } = item
                  item.cardTitle = name
                  item.cardDate = createTime
                  item.cardUser = createByName
                  item.cardLogo = image
                  // item.dataSourceStatus = 0
                  item.cardStatus = methods.changeStatus(status)

                  item.cardOperationLabel = null

                  item.cardMappingTitle = '基础信息'
                  // item.envTypeText = item.envType === 'OFFICIAL' ? '生产环境' : '开发环境'
                  item.cardMappingLabel = [
                    {
                      name: '数据源权限',
                      value: type === 'PROJECT' ? '场景' : '公共',
                      type: 'PROJECT',
                    },
                    // {
                    //   name: '数据环境',
                    //   value: item.envTypeText,
                    //   type: 'PROJECT',
                    // },
                  ]

                  // a：发布 b：删除 c：编辑 d：下架 e：查看 f:立即执行 g:撤回 h:测试 i:解绑 j:开发 k:申请
                  item.cardOperation = []

                  if (type === 'PROJECT') {
                    //type  类型(PROJECT-场景 PUBLIC-公共)
                    item.typeText = '场景数据源'
                    if (item.cardStatus === 1) {
                      if (
                        buttonAuthList.value.includes(
                          'dataManagement_dataSourceManagement_off_edit',
                        )
                      ) {
                        item.cardOperation.push('d')
                      }
                      if (
                        buttonAuthList.value.includes('dataManagement_dataSourceManagement_view')
                      ) {
                        item.cardOperation.push('e')
                      }
                    } else {
                      if (item.envType === 'TEST') {
                        item.cardOperation.push('l')
                      }
                      if (
                        buttonAuthList.value.includes(
                          'dataManagement_dataSourceManagement_publish_edit',
                        ) &&
                        item.envType !== 'TEST'
                      ) {
                        item.cardOperation.push('a')
                      }
                      if (
                        buttonAuthList.value.includes('dataManagement_dataSourceManagement_edit')
                      ) {
                        item.cardOperation.push('c')
                      }
                      if (
                        buttonAuthList.value.includes('dataManagement_dataSourceManagement_delete')
                      ) {
                        item.cardOperation.push('b')
                      }

                      if (
                        buttonAuthList.value.includes(
                          'dataManagement_dataSourceManagement_test_edit',
                        ) &&
                        item.status !== 'PUBLISHED'
                      ) {
                        item.cardOperation.push('h')
                      }

                      // item.cardOperation = 'abceh'
                      // if (datasourceType === 'EXCEL') {
                      //   item.cardOperation = 'ab'
                      // }
                    }
                  } else {
                    item.typeText = '公共数据源'
                    if (
                      state.approve === '0' &&
                      buttonAuthList.value.includes('dataManagement_dataSourceManagement_view')
                    ) {
                      item.cardOperation.push('e')
                    }
                    if (
                      state.approve === '0' &&
                      buttonAuthList.value.includes(
                        'dataManagement_dataSourceManagement_unbind_edit',
                      )
                    ) {
                      item.cardOperation.push('i')
                    }
                    if (
                      state.approve === '1' &&
                      buttonAuthList.value.includes(
                        'dataManagement_dataSourceManagement_apply_edit',
                      )
                    ) {
                      item.cardOperation.push('k')
                    }
                  }
                  item.cardOperation = item.cardOperation.join('')
                })
                if (init) {
                  state.cardData = []
                }
                if (state.switchType === 0) {
                  state.cardData = data.list
                } else {
                  state.tableData = data
                }
                state.pageInfo.total = data.total
                state.pageInfo.pages = data.pages
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 后端发布状态转换为前端状态， 0 下架 1 发布 其他 新增
        changeStatus(status) {
          let _status = 7
          switch (status) {
            case 'PUBLISHED':
              _status = 1
              break
            case 'OFFLINE':
              _status = 0
              break
            default:
              break
          }
          return _status
        },

        // 获取所有数据库类型
        getDataSourceType() {
          api.project.getDataSourceType().then((res) => {
            let { success, data } = res
            if (success) {
              let options = []
              data.forEach((item) => {
                options.push({
                  value: item,
                  label: item,
                })
              })
              state.org_dataSourceType = options
            }
          })
        },
        // 切换分页数量
        cardSizeChange(val) {
          state.pageInfo.pageSize = val
          methods.initTable(true)
        },
        // 切换分页
        cardCurrentChange(val) {
          state.pageInfo.currentPage = val
          methods.initTable()
        },
        // 选中数据源
        checkSource(code) {
          state.form.databaseType = code
          state.form.type = 'ADD'
          state.showAddDrawer = true
          allDataSourceType.value.handleClose()
        },
        // 关闭抽屉框
        closeFn(needRefresh = false) {
          if (needRefresh) {
            methods.initTable(false)
          }
          state.showAddDrawer = false
        },
      }

      onMounted(() => {
        const { isAdmin } = toRefs(store.state.user)
        if (localStorage.getItem('projectSourceSwitchType')) {
          state.switchType = Number(localStorage.getItem('projectSourceSwitchType'))
          // state.pageInfo.pageSize = state.switchType === 0 ? 12 : 10
        }
        if (state.isLzos) {
          state.tableHeight = document.body.offsetHeight - 242
        } else {
          state.tableHeight = document.body.offsetHeight - 304
        }
        state.isAdmin = isAdmin
        methods.getDataSourceType()
        methods.getDatasourceStatus()

        methods.initTable(true)
        methods.getTreeListFn()
      })
      return {
        state,
        dataTreeRef,
        buttonAuthList,
        allDataSourceType,
        syncForm,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .data-source-page-out-box {
    height: 100%;
    padding: 16px;
    background: #f0f2f5;

    &.isLzos {
      padding: 0;
    }

    .tools {
      background-color: #fff;
      border-radius: 2px;

      &.open {
        height: 146px;
      }

      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 10px 16px;
        border-bottom: none !important;

        &.date {
          align-items: flex-start;
          height: 42px;

          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }

        .createTime {
          width: 260px;
          margin-right: 32px;
        }

        .col {
          width: calc(100% - 150px);
          overflow: auto;

          .searchTool {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 1520px;
          }
        }

        &.tabs {
          align-items: flex-end;
          height: 48px;

          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }

        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }

            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 16px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            padding: 5px 16px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            background: #1e89ff;
            border: 1px solid $themeBlue;
            border-radius: 2px;
            cursor: pointer;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background: #fff;
              border: 1px solid #dcdfe6;

              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }
          }
        }

        &:first-of-type {
          box-sizing: border-box;
          border-bottom: 1px solid #c5d0ea;
        }
      }
    }

    .main {
      display: flex;
      height: 100%;
    }

    .asideTree {
      width: 286px;
      height: calc(100% - 60px);
      margin-top: 10px;
      margin-right: 10px;
      padding: 8px 0;
      background: #fff;
      border-radius: 2px;

      &-title {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 6px 12px 6px 0px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        .line {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 8px;
          background: #1e89ff;
        }

        span {
          vertical-align: middle;
        }

        .tree-add {
          cursor: pointer;
        }
      }

      &-box {
        height: calc(100% - 50px);
        padding: 0 16px;

        :deep(.tree-box) {
          height: calc(100% - 36px);
          .btn-box {
            opacity: 1;
            background-color: #fff;
          }
        }
      }

      &-search-input {
        margin-bottom: 8px;

        :deep(.nancalui-input__wrapper) {
          border: 1px solid #e5e6eb !important;
          border-radius: 2px !important;
          border-top-right-radius: 0 !important;
          border-bottom-right-radius: 0 !important;
        }

        .search-btn {
          width: 32px;
        }

        .nancalui-input-slot__append {
          border-color: #e5e6eb;
        }
      }

      :deep(.nancalui-tree__node .nancalui-tree__node-content) {
        box-sizing: content-box;
        padding: 0 80px 0 8px;
        font-size: 14px;

        &:hover .tree-icon {
          display: inline-block;
        }
      }

      :deep(.tree-icon-tool) {
        display: inline-block;
      }

      :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
        font-size: 14px;
        background: #ebf4ff;
      }

      :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
        background: #ebf4ff;
      }

      :deep(.nancalui-tree__node-vline) {
        display: none;
      }

      .tree-icon {
        margin: 0 4px;
        font-size: 16px;
      }

      .tree-label {
        max-width: 140px;
        margin-right: 8px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .tree-tool {
        width: 60px;
      }

      .tree-icon-tool {
        margin-left: 4px;
        font-size: 16px;
      }

      .nancalui-tree-switch {
        position: relative;
        top: -2px;
        color: #1d2129;
        font-size: 16px;
      }
    }

    .content {
      flex: 1;
      height: calc(100% - 60px);
      margin-top: 10px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 52px;
        padding: 0 8px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bolder;
        font-size: 18px;

        :deep(.button-content) {
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            margin-right: 4px;
            font-size: 16px;
          }
        }

        .add {
          color: #fff;
        }

        &-btn {
          position: absolute;
          right: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 16px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #e3ecff;
          }
        }

        .switch {
          &-label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 14px;
            color: #1e89ff;
            font-size: 16px;
            line-height: 32px;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;

            &:hover {
              color: $themeBlue;
              background-color: #ecf7ff;
            }

            span {
              margin-left: 4px;
              font-weight: 400;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .data-source-page {
    height: calc(100% - 52px);
    padding: 0;
    background-color: #fff;

    .data-source-page-content {
      height: 100%;
      border-radius: 4px;

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 60px);

        // padding: 16px 16px 0 16px;
        .nancalui-table {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }

          .confidentialityLevel {
            display: inline-block;
            box-sizing: border-box;
            height: 24px;
            padding: 0 8px;
            color: rgba(0, 0, 0, 0.75);
            font-size: 14px;
            line-height: 24px;
            background-color: #e3ecff;
            border: 1px solid #a3b4db;
            border-radius: 2px;

            &.skyblue {
              color: #1aa4ee;
              background-color: rgba(26, 164, 238, 0.08);
              border: 1px solid rgba(26, 164, 238, 0.4);
            }

            &.orange {
              color: #fe8624;
              background-color: #fff4e6;
              border: 1px solid #ffba70;
            }

            &.cBlue {
              color: #1e89ff;
              background-color: #ebf4ff;
              border: 1px solid #99c9ff;
            }

            &.red {
              color: #d40000;
              background-color: #ffeded;
              border: 1px solid #ef7777;
            }

            &.brown {
              color: #7a0000;
              background-color: rgba(122, 0, 0, 0.08);
              border: 1px solid rgba(122, 0, 0, 0.4);
            }

            &.drakBlue {
              color: #224ecd;
              background-color: rgba(34, 78, 205, 0.08);
              border: 1px solid rgba(34, 78, 205, 0.4);
            }
          }

          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: rgba(0, 0, 0, 0.75);
              font-size: 12px;
              line-height: 22px;
              background: #e3ecff;
              border: 1px solid #a3b4db;
              border-radius: 6px;

              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }

          .taskStatus {
            color: rgba(0, 0, 0, 0.75);
            font-size: 14px;

            .icon-status-svg {
              margin-right: 4px;
              font-size: 14px;
            }
          }
        }

        &.empty-list {
          height: 100%;
          background-color: #fff;
          border-radius: 8px;

          .table-list-content {
            height: 100%;
          }
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }

      .card-list {
        width: 100%;
        height: 100%;
        padding: 0 8px 8px 8px;
        overflow: auto;

        .card {
          position: relative;
          display: inline-block;
          width: calc((100% - 32px) / 2);
          margin: 0 16px 16px 0;

          padding: 16px 20px;
          overflow: hidden;
          vertical-align: top;
          border: 1px solid #c5d0ea;
          border-radius: 2px;

          // background: #ecf0f4;
          &:hover {
            border: 1px solid #fff;
            box-shadow: 0 4px 16px -2px rgba(0, 0, 0, 0.1);
          }

          .card-top {
            display: flex;

            .top-left {
              margin-right: 8px;

              img {
                width: 40px;
                height: 40px;
                border-radius: 6px;
              }
            }

            .top-right {
              width: calc(100% - 48px);
              color: rgba(0, 0, 0, 0.48);
              font-size: 12px;

              .title {
                display: flex;
                justify-content: space-between;
                color: rgba(0, 0, 0, 0.85);
                font-weight: bolder;
                font-size: 16px;
                line-height: 24px;

                .title-name {
                  width: calc(100% - 24px);
                  overflow: hidden;
                  color: rgba(0, 0, 0, 0.9);
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .title-operate {
                  display: flex;
                  align-items: center;
                  width: 24px;
                  height: 24px;
                  padding: 4px;
                  color: $themeBlue;
                  border-radius: 6px;

                  &:hover {
                    background-color: #ecf7ff;
                  }
                }
              }

              .message {
                overflow: hidden;
                color: rgba(0, 0, 0, 0.55);
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }

          .card-mid {
            margin: 8px 0 12px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 400;
            font-size: 14px;

            .card-mid-first {
              display: flex;
              margin-bottom: 8px;
              line-height: 22px;
            }

            .description {
              width: 100%;
              height: 22px;
              margin-bottom: 12px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.75);
              line-height: 22px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .card-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 24px;

            &-col {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: rgba(0, 0, 0, 0.55);
              font-weight: 400;
              font-size: 12px;
              font-family: 'PingFang SC';
              font-style: normal;
              line-height: 24px;
              font-feature-settings: 'clig' off, 'liga' off;

              &-label {
                box-sizing: border-box;
                height: 24px;
                margin-right: 4px;
                padding: 0 8px;
                color: rgba(0, 0, 0, 0.75);
                font-size: 14px;
                line-height: 24px;
                background-color: #e3ecff;
                border: 1px solid #a3b4db;
                border-radius: 2px;

                &.green {
                  color: #04c495;
                  background-color: #e6fff4;
                  border: 1px solid #04c495;
                }

                &.blue {
                  color: $themeBlue;
                  background-color: #e3ecff;
                  border: 1px solid $themeBlue;
                }

                &.skyblue {
                  color: #1aa4ee;
                  background-color: rgba(26, 164, 238, 0.08);
                  border: 1px solid rgba(26, 164, 238, 0.4);
                }

                &.orange {
                  color: #fe8624;
                  background-color: #fff4e6;
                  border: 1px solid #ffba70;
                }

                &.cBlue {
                  color: #1e89ff;
                  background-color: #ebf4ff;
                  border: 1px solid #99c9ff;
                }

                &.red {
                  color: #d40000;
                  background-color: #ffeded;
                  border: 1px solid #ef7777;
                }

                &.brown {
                  color: #7a0000;
                  background-color: rgba(122, 0, 0, 0.08);
                  border: 1px solid rgba(122, 0, 0, 0.4);
                }

                &.drakBlue {
                  color: #224ecd;
                  background-color: rgba(34, 78, 205, 0.08);
                  border: 1px solid rgba(34, 78, 205, 0.4);
                }
              }
            }
          }
        }
      }

      &-pagination {
        padding: 0 16px;
      }
    }

    .taskStatus {
      .circle {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 4px;
        background-color: $themeBlue;
        border-radius: 50%;

        &.green {
          background-color: #00ca5f;
        }

        &.gray {
          background-color: #b8b8b8;
        }
      }
    }

    .datasourceType {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &-img {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
    }
  }

  .sync-content {
    .tips {
      display: flex;
      align-items: center;
      margin-right: 0;
      padding: 20px 0;
      color: #000000d9;
      font-weight: bolder;
      font-size: 14px;
      text-align: left;

      .nancalui-icon__container {
        margin-top: 2px;
        margin-right: 5px;
        color: #ffb540;
        font-size: 14px;
      }
    }

    .h2 {
      margin-bottom: 28px;
      color: #333;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
    }
  }

  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .nancalui-drawer {
    .n-drawer-body-content {
      height: calc(100vh - 52px) !important;
    }
  }

  .data-source-page-out-box .tools .row .nancalui-select,
  .nancalui-datepicker-container,
  .data-source-page-out-box .tools .row .nancalui-input {
    width: 180px;
    margin-right: 32px;
  }

  :deep(.input-container) {
    width: 100%;
  }

  :deep(.data-source-page-content-pagination) {
    padding: 14px 16px !important;
  }

  :deep(.nancalui-table-page .nancalui-pagination .nancalui-pagination__total-size) {
    position: relative;
    top: 0;
    left: 0;
    margin: 0;
  }

  :deep(.nancalui-table-page) {
    padding: 14px 16px;
  }

  .dataTreePop {
    border-radius: 2px;

    .nancalui-modal__header {
      padding: 15px 16px 15px 0px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;

      .line {
        position: relative;
        top: 2px;
        display: inline-block;
        width: 4px;
        height: 18px;
        margin-right: 12px;
        background: #1e89ff;
      }
    }

    &-btn {
      padding: 0 16px;
      border-radius: 2px;
    }
  }

  :deep(.dataTreePop) {
    border-radius: 2px;
  }

  :deep(.dataTreeBodyPop) {
    padding: 24px 16px;
  }

  .addDatasourcePop {
    border-radius: 2px;

    .nancalui-modal__header {
      padding: 15px 16px 15px 0px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;

      .line {
        position: relative;
        top: 2px;
        display: inline-block;
        width: 4px;
        height: 18px;
        margin-right: 12px;
        background: #1e89ff;
      }
    }
  }

  .soldOutPop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);

    .box {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 430px;
      background: #fff;
      border-radius: 4px;
      transform: translate(-50%, -50%);

      .title {
        position: relative;
        padding: 12px 16px;
        font-weight: bold;
        font-size: 16px;

        .logo {
          margin-right: 4px;
          color: #ffb540;
          vertical-align: middle;
        }

        span {
          vertical-align: middle;
        }

        .close {
          position: absolute;
          top: 10px;
          right: 12px;
          font-size: 16px;
        }
      }

      .msg {
        padding: 16px;
        font-size: 12px;
      }

      .footer {
        padding: 16px;
        text-align: right;
      }
    }
  }

  :deep(.addDatasourceBodyPop) {
    padding: 8px 16px;
  }

  :deep(.addDatasourcePop) {
    border-radius: 2px;
  }

  .nancalui-tree {
    height: calc(100% - 40px);
    overflow: auto;
  }

  :deep(.nancalui-tree__node .nancalui-tree__node-content--value-wrapper) {
    overflow: inherit;
  }

  :deep(.nancalui-form__label--required:before) {
    font-size: 18px;
  }

  /*屏幕宽度大于1220时展示3个卡片*/
  @media only screen and (min-width: 1220px) {
    .data-source-page {
      .data-source-page-content {
        .card-list {
          .card {
            width: calc((100% - 32px) / 3);

            &:nth-of-type(3n) {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  /*屏幕宽度大于1630时展示4个卡片*/
  @media only screen and (min-width: 1630px) {
    .data-source-page {
      .data-source-page-content {
        .card-list {
          .card {
            width: calc((100% - 48px) / 4);

            &:nth-of-type(3n) {
              margin-right: 16px;
            }

            &:nth-of-type(4n) {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  :deep(.nancalui-table__tbody tr td) {
    border-bottom: 1px solid #c9cdd4;
  }

  :deep(.nancalui-range-date-picker-pro .nancalui-range-date-picker-pro__range-picker) {
    border: 1px solid #c9cdd4;
  }

  .page-top {
    display: none;
  }

  :deep(.nancalui-input--disabled .nancalui-input__inner) {
    padding: 0 10px;
    background: #f5f5f5;
  }

  :deep(.nancalui-input .nancalui-input__wrapper.nancalui-input--disabled) {
    padding: 0;
  }
</style>
