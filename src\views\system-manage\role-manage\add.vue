<template>
  <section class="container-padding16">
    <section class="container-padding16-box" v-loading="state.loading">
      <div class="page_header_common_style">
        <span class="need_smallcube__title">{{ state.title }}</span>
      </div>
      <div class="form">
        <n-form
          ref="form"
          :data="state.form"
          :rules="state.rules"
          label-width="82px"
          label-align="right"
          :pop-position="['right']"
          label-suffix="："
        >
          <n-row justify="between" :gutter="24">
            <n-col :span="12">
              <n-form-item label="角色编码" field="code">
                <n-input
                  v-model="state.form.code"
                  placeholder="请输入角色编码"
                  size="small"
                  maxlength="50"
                  clearable
                />
              </n-form-item>
            </n-col>
            <n-col :span="12">
              <n-form-item label="角色名称" field="name">
                <n-input
                  v-model="state.form.name"
                  placeholder="请输入角色名称"
                  size="small"
                  maxlength="50"
                  clearable
                />
              </n-form-item>
            </n-col>
          </n-row>
          <n-row justify="between" :gutter="24">
            <n-col :span="24">
              <n-form-item label="角色描述" field="description">
                <n-textarea
                  v-model="state.form.description"
                  placeholder="请输入角色描述"
                  :autosize="{ minRows: 3 }"
                  maxlength="200"
                  show-count
                  resize="both"
                />
              </n-form-item>
            </n-col>
          </n-row>

          <n-row justify="between" :gutter="24" class="tree-box-row">
            <n-col :span="24">
              <n-form-item class="tree-box" label="拥有权限">
                <n-tree
                  ref="tree"
                  :prop="state.defaultProps"
                  :data="state.dataTree"
                  check="downward"
                  :key="state.key"
                  @check-change="hanleCheck"
                />
              </n-form-item>
            </n-col>
          </n-row>
        </n-form>
      </div>
    </section>
    <div class="container-padding16-footer">
      <div class="my-appliction">
        <n-button size="sm" @click.prevent="goBack">取消</n-button>
        <n-button
          :loading="loading"
          color="primary"
          size="sm"
          variant="solid"
          @click.prevent="onConfirm"
          >确定</n-button
        >
      </div>
    </div>
  </section>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { useStore } from 'vuex'
  import { checkFullCName, checkName } from '@/utils/validate'

  export default {
    name: 'DataScriptDetail',

    setup() {
      const router = useRouter()
      const store = useStore()
      const tree = ref()
      const form = ref()
      const state = reactive({
        title: '新增角色',
        treeData: [],
        form: {
          code: '',
          name: '',
          description: '',
        },
        roleAllArr: '', // 角色下拉列表
        rules: {
          code: [
            {
              required: true,
              validator: checkName,
              trigger: 'blur',
            },
          ],
          name: [
            {
              required: true,
              validator: (...args) =>
                checkFullCName(...args, 'system', 'checkRoleName', {
                  name: state.form.name || null,
                  id: state.query?.id || null,
                }),
              trigger: 'blur',
            },
          ],
        },
        dataTree: [],
        defaultProps: {
          children: 'children',
          label: 'name',
          key: 'id',
          group: true,
        },
        loading: false,
        checkChildIds: [],
        query: {},
        key: 1,
      })

      const methods = {
        //指定最后一级不关联父级

        hanleCheck(data) {
          // console.log(tree.value.treeFactory.getCheckedNodes())

          // 获取当前节点是否被选中

          // const isChecked = tree.value.getNode(data).checked
          const isChecked = data.checked
          // 如果当前节点被选中，则遍历上级节点和下级子节点并选中，如果当前节点取消选中，则遍历下级节点并取消选中
          if (isChecked) {
            // 判断是否有上级节点，如果有那么遍历设置上级节点选中
            data.parentId && setParentChecked(data)
          }
          function setParentChecked(data) {
            // 获取该id的父级node
            // const parentNode = tree.value.getNode(parentId)
            const parentNode = tree.value.treeFactory.getParent(data)

            // 如果该id的父级node存在父级id则继续遍历
            parentNode && parentNode.parentId && setParentChecked(parentNode)
            //  设置该id的节点为选中状态
            // tree.value.setChecked(parentId, true)
            parentNode.checked = true
          }
        },
        // 角色数据详情查询
        roleDetail() {
          let params = {
            id: state.query.id,
          }
          api.system
            .roleDetail(params)
            .then((res) => {
              state.loading = false
              if (res.code === 'SUCCESS') {
                state.form = res.data
                if (res.data.menuIds) {
                  let menuIds = res.data.menuIds

                  methods.editCheckNodes(state.dataTree, menuIds)

                  state.key++
                }
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        //编辑回显选中
        editCheckNodes(data, ids = []) {
          data.map((item) => {
            if (item.children && item.children.length != 0) {
              this.editCheckNodes(item.children, ids)
            }
            if (ids.includes(item.id)) {
              item.checked = true
            }
          })
        },
        // 计算选中的子节点
        checkChildFn(allIds, checkIds) {
          allIds.forEach((val) => {
            if (val.children.length > 0) {
              checkIds = checkIds.filter((item) => {
                return item !== val.id
              })
              checkIds = methods.checkChildFn(val.children, checkIds)
            }
          })
          return checkIds
        },
        menuTree() {
          let params = {}
          state.loading = true
          api.system
            .menuTree(params)
            .then((res) => {
              if (res.success) {
                if (state.query.id) {
                  state.title = '编辑角色'
                  methods.roleDetail()
                } else {
                  state.loading = false
                }
                res.data.forEach((item) => {
                  item.children = item.children[0]?.children
                  if (item.code === 'data') {
                    item.children = item.children.filter((val) => val.code !== 'data_application')
                  }
                })
                // state.dataTree = methods.setTreeRecursion(res.data[0].children)
                state.dataTree = methods.setTreeRecursion(res.data)
                // tree.value.treeFactory.expandNode(state.dataTree)
              } else {
                state.loading = false
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        setTreeRecursion(data) {
          data.map((item) => {
            item.expanded = true
            if (item.children && item.children.length !== 0) {
              methods.setTreeRecursion(item.children)
            }
          })
          return data
        },
        onConfirm() {
          // let passed = false
          // let checkedNodes = tree.value.getCheckedNodes()
          // let mustCheckOneNode = ['管理人员首页', '业务人员首页', '技术人员首页']
          // checkedNodes.forEach((item) => {
          //   if (mustCheckOneNode.includes(item.name)) {
          //     passed = true
          //   }
          // })
          // if (!passed) {
          //   ElNotification({
          //     title: '提示',
          //     message: '首页至少选择一个',
          //     type: 'warning',
          //   })
          //   return
          // }

          if (state.query.id) {
            methods.updateRole()
          } else {
            methods.addRole()
          }
        },
        // 添加用户
        addRole() {
          let getCheckedNodes = tree.value.treeFactory.getCheckedNodes()
          let checkedNodesIds = getCheckedNodes.map((item) => {
            return item.id
          })
          let params = {
            name: state.form.name,
            code: state.form.code,
            menuIds: checkedNodesIds,
            description: state.form.description,
          }

          // let halfIds = tree.value.getHalfCheckedKeys()
          // params.menuIds = params.menuIds.concat(halfIds)
          form.value
            .validate((valid) => {
              if (valid) {
                state.loading = true
                api.system.addRole(params).then((res) => {
                  if (res.code === 'SUCCESS') {
                    ElNotification({
                      title: '提示',
                      message: '添加角色成功',
                      type: 'success',
                    })
                    state.loading = false
                    methods.goBack()
                  }
                })
              }
            })
            .catch(() => (state.loading = false))
        },
        // 编辑用户
        updateRole() {
          let getCheckedNodes = tree.value.treeFactory.getCheckedNodes()
          let checkedNodesIds = getCheckedNodes.map((item) => {
            return item.id
          })
          let params = {
            id: state.query.id,
            code: state.form.code,
            name: state.form.name,
            menuIds: checkedNodesIds,
            description: state.form.description,
          }
          // let halfIds = tree.value.getHalfCheckedKeys()
          // params.menuIds = params.menuIds.concat(halfIds)
          form.value
            .validate((valid) => {
              if (valid) {
                state.loading = true
                api.system.updateRole(params).then((res) => {
                  if (res.code === 'SUCCESS') {
                    ElNotification({
                      title: '提示',
                      message: '编辑角色成功',
                      type: 'success',
                    })
                    state.loading = false
                    methods.goBack()
                  }
                })
              }
            })
            .catch(() => (state.loading = false))
        },
        // 查询当前用户权限
        functions() {
          let params = {}
          api.system.functions(params).then((res) => {
            store.commit('user/SET_MENU', res.data)
          })
        },
        // 设置已选择菜单
        setCheckedKeys(ids) {
          tree.value.setCheckedKeys(ids)
        },
        goBack() {
          // router.back()
          router.push({ name: 'roleManageList', query: { refresh: 'true' } })
        },
      }
      onMounted(() => {
        state.query = router.currentRoute.value.query
        methods.menuTree()
      })

      return {
        state,
        tree,
        form,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container-padding16 {
    // padding-bottom: 70px;
    &-box {
      height: calc(100% - 74px);
      border-radius: 4px;
      overflow: hidden;
    }

    &-footer {
      height: 64px;
      padding: 16px;
      text-align: right;
      background: #ffffff;
      margin-top: 10px;
      border-radius: 0px 0px 2px 2px;
    }

    :deep(.el-input) {
      width: 320px;
    }
  }

  .form {
    height: calc(100% - 52px);
    background: #fff;
    padding: 16px 58px;
    color: #333;

    .nancalui-form {
      width: 880px;
      :deep(.el-form-item__label) {
        padding: 0;
      }
    }

    h2 {
      font-size: 14px;
      padding-left: 5px;
      border-left: 4px solid $themeBlue;
      margin: 0 0 20px;
    }

    .nancalui-form {
      height: 100%;
      margin: 0 auto;
      transform: translateX(-41px);
      height: 100%;
      .tree-box-row {
        height: calc(100% - 120px);
      }
      :deep(.tree-box) {
        width: 100%;
        height: 100%;
        .nancalui-form__control,
        .nancalui-form__control-container {
          width: 100%;
          height: 100%;
        }
        > .nancalui-tree-list {
          align-items: flex-start;
        }
        .nancalui-tree {
          width: 100%;
          max-height: calc(100vh - 450px);
          overflow-y: auto;
          display: flex;
          justify-content: space-between;
          padding: 10px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          .nancalui-checkbox.disabled .nancalui-checkbox__material {
            border-color: #dcdfe6;
            background-color: #f2f6fc !important;
          }

          .nancalui-tree__node {
            height: 26px;
          }
          .nancalui-tree__node-vline,
          .nancalui-tree__node-hline {
            display: none;
          }
          .nancalui-tree__node-title {
            font-size: 12px;
          }

          .el-tree-node__content {
            .el-tree-node__label {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  :deep(.el-input, .el-select) {
    width: 100% !important;
  }
</style>
