<template>
  <div
    :class="{ node: true, views: data.type === 'VIEW', [data.bloodType]: true }"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div class="head">
      <span>{{ tabName }}</span>
      <span v-if="data && data.isShare" class="is-quote">引</span>
      <span v-else-if="data && data.isQuote" class="is-quote">本</span>
      <span v-else-if="data && data.table?.sharedEntity === 'true'" class="is-quote">原</span>
    </div>
    <div class="content">
      <div v-for="item in col" :key="item.col" class="name">
        {{ setTag(item) }}{{ item.col }}{{ setSuffix(item) }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {
          table: {
            tabName: '表',
          },
        },
        width: 0,
        height: 0,
      }
    },
    computed: {
      // 列重新排序PK>FK>其他
      col() {
        const col = this.data.col
        let newCol = []
        if (col?.length > 0) {
          const pks = col.filter((item) => item.pk)
          const fks = col.filter((item) => item.fk && !item.pk)
          const others = col.filter((item) => !item.fk && !item.pk)
          // console.log('attr', pks, fks, others)
          newCol = [...pks, ...fks, ...others]
        }
        return newCol
      },
      tabName() {
        return this.data?.table?.tabName
      },
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      const { width, height } = node.size()
      this.width = width
      this.height = height
      node.on('change:size', ({ current }) => {
        this.width = current.width
        this.height = current.height
      })
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.data = current
        // node.size({ width: 95, height: 28 + current.length * 28 })
      })
    },
    methods: {
      setSuffix(item) {
        let suffix = ''
        if (item.pk) {
          suffix += '(PK)'
        }
        if (item.fk) {
          suffix += '(FK)'
        }
        if (item.type === 'System') {
          suffix += '(s)'
        }
        return suffix
      },
      setTag(item) {
        let tag = ''
        switch (true) {
          case item.pk:
            tag = '# '
            break
          case item.candidate:
            tag = '(#) '
            break
          case item.notNull:
            tag = '* '
            break
          case !item.notNull:
            tag = 'o '
            break
        }
        return tag
      },
    },
  }
</script>

<style scoped lang="scss">
  .node {
    display: inline-block;
    padding: 1px;
    overflow: hidden;
    border: 1px solid #697a9a;
    border-radius: 4px;
    &.isBlood {
      border-color: #00d09a;
      .head {
        background: #00d09a;
      }
    }
    &.isBloodRelation {
      border-color: #f5a623;
      .head {
        background: #f5a623;
      }
    }
    &.views {
      border-color: green;
      .head {
        background: green;
      }
    }
    // border-radius: 4px;
    .head {
      height: 28px;
      padding-left: 10px;
      color: #fff;
      font-size: 12px;
      line-height: 28px;
      background: #697a9a;
      border-radius: 3px 3px 0px 0px;
      .is-quote {
        position: absolute;
        right: 2px;
        display: inline-block;
        width: 28px;
        height: 28px;
        color: #ffffff;
        font-size: 12px;
        line-height: 28px;
        text-align: center;
        background: #f54446;
        border-radius: 0px 3px 0px 0px;
      }
    }
    .content {
      height: calc(100% - 30px);
      overflow: auto;
      color: #0c0c0c;
      font-size: 12px;
      background: #fff;
      .name {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      div {
        padding: 5px 10px;
        font-size: 12px;
      }
    }
    img {
      height: 14px;
      margin-right: 5px;
    }
    span {
      vertical-align: middle;
    }
  }
</style>
