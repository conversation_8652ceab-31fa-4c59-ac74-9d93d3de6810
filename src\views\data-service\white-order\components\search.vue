<template>
  <div class="commonForm-search">
    <div class="search-left">
      <slot name="searchLeft"></slot>
    </div>
    <div class="commonForm">
      <n-form
        :inline="true"
        :data="state.formInline"
        class="demo-form-inline search-right commonForm"
      >
        <n-form-item label="时间范围：">
          <n-range-date-picker-pro
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :shortcuts="state.shortcuts"
            allow-clear
            @confirmEvent="onSearch"
          />
        </n-form-item>
        <n-form-item label="">
          <n-input
            v-model="state.formInline.keyword"
            size="small"
            placeholder="请输入API名称"
            clearable
            @clear="onSearch"
          >
            <template #append>
              <n-button @click.prevent="onSearch">
                <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                  <SvgIcon class="icon_search" icon="icon_search" />
                </n-popover>
              </n-button>
            </template>
          </n-input>
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<script>
  import { reactive, onMounted } from 'vue'
  import ENUM from '@/const/enum'
  import api from '@/api/index'
  export default {
    title: '',
    components: {},
    props: {},
    emits: ['handleSearch'],
    setup(props, { emit }) {
      const state = reactive({
        shortcuts: ENUM.SHORTCUTS,
        formInline: {
          status: 'all',
          time: [],
          keyword: '',
        },
        org_apiStatus: [],
      })
      const methods = {
        onSearch() {
          emit('handleSearch', state.formInline)
        },
        // 所有状态枚举
        getApiStatusEnums() {
          api.dataService['getApiStatusEnums']().then((res) => {
            let { success, data } = res
            if (success) {
              data.unshift({ label: '全部', value: 'all' })
              state.org_apiStatus = data
            }
          })
        },
      }
      onMounted(() => {
        methods.getApiStatusEnums()
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .commonForm-search {
    display: flex;
    justify-content: space-between;
    padding: 16px;
  }
</style>
