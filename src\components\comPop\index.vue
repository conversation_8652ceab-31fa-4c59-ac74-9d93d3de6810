<template>
  <teleport to="body">
    <section :class="['pop', props.class]">
      <div class="box" :style="{ width: props.width + 'px' }">
        <div class="title">
          <span class="name">{{ props.title }}</span>
          <svg
            v-loading="state.hasClick"
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            @click="onClose"
          >
            <path
              d="M1.30078 12.6992L12.8008 1.19928"
              stroke="#606266"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.30078 1.19922L12.8008 12.6992"
              stroke="#606266"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>

        <div class="content">
          <slot></slot>
        </div>

        <div class="footer">
          <n-button v-loading="state.hasClick" @click="onClose">取消</n-button>
          <n-button variant="solid" v-loading="state.hasClick" @click="onConfirm">确定</n-button>
        </div>
      </div>
    </section>
  </teleport>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onBeforeMount, onMounted, watchEffect, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  //console.log('1-开始创建组件-setup')
  /**
   * 数据部分
   */
  const emits = defineEmits(['onClose', 'onConfirm'])
  const props = defineProps({
    title: {
      type: String,
      default: '标题',
    },
    width: {
      type: Number,
      default: 320,
    },
    class: {
      type: String,
      default: '',
    },
  })
  const state = reactive({
    hasClick: false,
  })
  onMounted(() => {
    //console.log('3.-组件挂载到页面之后执行-------onMounted')
  })

  const onClose = () => {
    if (state.hasClick) {
      return false
    }
    emits('onClose')
  }
  const onConfirm = () => {
    if (state.hasClick) {
      return false
    }
    state.hasClick = true
    emits('onConfirm')
  }
  watchEffect(() => {})
  // 使用toRefs解构
  // let { } = { ...toRefs(data) }
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .pop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    .box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 2px;
      min-width: 320px;

      .title {
        display: flex;
        height: 48px;
        padding: 16px 20px 8px 0px;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;

        .name {
          color: var(----, rgba(0, 0, 0, 0.9));

          /* 常用/r500/h8 */
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px; /* 150% */

          &::before {
            content: '';

            display: inline-block;
            width: 4px;
            height: 18px;
            background: var(---, #1e89ff);
            margin-right: 12px;
            vertical-align: middle;
          }
        }
      }

      .content {
        padding: 16px 20px;
      }

      .footer {
        display: flex;
        padding: 16px 20px;
        justify-content: flex-end;
        align-items: center;
        align-self: stretch;
      }
    }
  }
</style>
