<template>
  <div class="container">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
    </div>
    <!-- 预览 -->
    <div
      class="preview-content"
      id="preview-pint"
      v-loading="state.loading"
      element-loading-text="Loading..."
    >
      <component :is="componentKey" :option="state.option" />
    </div>
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import Excel from '@/views/data-management/resource-library/components/Excel.vue'
  import Img from '@/views/data-management/resource-library/components/Img.vue'
  import PDF from '@/views/data-management/resource-library/components/PDFJS.vue'
  import Txt from '@/views/data-management/resource-library/components/Txt.vue'
  import Word from '@/views/document-management/components/word.vue'

  import api from '@/api/index'
  const router = useRouter()
  const state = reactive({
    name: '非结构化数据预览',
    option: {},
    type: '',
    loading: false,
  })
  const componentMap = {
    image_png: Img,
    image_jpg: Img,
    image_jpeg: Img,
    pdf: PDF,
    word: Word,
    excel: Excel,
    text_txt: Txt,
  }
  const componentKey = computed(() => componentMap[state.type])
  function getDocUrl(id) {
    api.documentManage.outsideGet({ id: id }).then((res) => {
      const { name, type, docUrl } = res.data
      state.name = name
      state.type = type
      state.option = {
        value: docUrl,
        name: name,
      }
    })
  }

  function closeFn() {
    router.go(-1)
  }
  onMounted(() => {
    const { id } = router.currentRoute.value.query
    getDocUrl(id)
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .preview-content {
    height: calc(100vh - 175px);
    padding: 10px;
    background: #fff;
  }
  :deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;
    .img-Preview {
      width: 100% !important;
      height: max-content;
      min-height: 100%;
      overflow-x: unset;
      overflow-y: unset;
    }
  }
  .title {
    width: calc(100% - 80px);
  }
</style>
