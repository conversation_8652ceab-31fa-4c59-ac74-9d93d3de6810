<template>
    <div class="container">
        <div class="cf-page-title">
            <div class="nc-line-1 title">{{ state.name }}</div>
            <n-button variant="solid" @click="closeFn">退出预览</n-button>
        </div>
        <!-- 预览 -->
        <div class="preview-content" id="preview-pint" ref="previewPintRef" v-loading="state.loading"
            element-loading-text="Loading...">
            <FilePreview :option="state.option" :type="state.type" @pageChange="pageChange" isDrawDisabled
                isDragDisabled @loadComplete="getPreviewData(router.currentRoute.value.query.id)"
                :data="state.annotationsPage" @click-polygon="(id,data ,scale, imgOffset = { x: 0, y: 0 })  => {
          tagRefs[id].showTag()
          const maxX = Math.max(...data.map(item => item.x * scale + imgOffset.x))
          const maxY = Math.min(...data.map(item => item.y * scale + imgOffset.y))
          Object.assign(state.annotationsPage[state.currentPage] [id], {x: maxX, y: maxY})
        }"/>
        </div>
        <template v-for="(item, key) in tagList" :key="key">
            <Tag :ref="(ref) => tagRefs[item.id] = ref" :tagOptions="state.tagOptions" :x="item.x" :y="item.y"
                :initialTags="item.tags" mode="preview" />
        </template>
    </div>
</template>
<script setup>
import { throttle, isEmpty } from "lodash-es"
import { useRouter } from 'vue-router'
import Tag from './tag.vue'
import FilePreview from './components/FilePreview.vue'
import * as d3 from 'd3'
import api from '@/api/index'
const router = useRouter()
const previewPintRef = ref(null)
const tagRefs = {}
const state = reactive({
    name: '文档预览',
    option: {},
    type: '',
    loading: false,
    tagOptions: [],
    annotations: {},
    // 分页标注data
    annotationsPage: {

    },
    currentPage: 1,

});
const pageChange = (page) => {
    setTimeout(() => {
        state.currentPage = page
        renderWaveLine()
    });
}
function getDocUrl(id) {
    api.documentManage.outsideGet({ id: id }).then((res) => {
        const { name, type, docUrl } = res.data
        state.name = name
        state.type = type
        state.option = {
            value: docUrl,
            name: name,
        }
    })
}
// 获取预览数据
function getPreviewData(id) {
    api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
        if (res.success) {
            try {
                const { mark, paragraph } = res.data
                state.annotationsPage = JSON.parse(mark || '{}')
                switch (true) {
                    case ['pdf', 'word'].includes(state.type):
                        renderWaveLine()
                        break;
                    case state.type.includes('image'):
                        break;
                }
            } catch (error) {
                state.annotationsPage = {}
            }
        }
    })
}
function closeFn() {
    router.go(-1)
}
onMounted(() => {
    const { id } = router.currentRoute.value.query
    getDocUrl(id)
})

// 获取标签列表
const getTargetList = () => {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
        let { success, data } = res
        if (success) {
            state.tagOptions = data
        }
    })
}
getTargetList()
const tagList = computed(() => {
    // state.annotations为空时展示当前页的标注
    if (isEmpty(state.annotations || {})) {
        return state.annotationsPage[state.currentPage] || {}
    }
    const newAnnotations = {
        ...(state.annotationsPage[state.currentPage] || {}),
        ...(state.annotations || {})
    }
    return newAnnotations
})

// 渲染本页的波浪线
function renderWaveLine() {
    // 清除之前的data-temp为true的div
    document.querySelectorAll('[data-temp="true"]').forEach(el => el.remove());
    // 查询
    // 渲染标记完成的波浪线
    Object.values(state.annotationsPage[state.currentPage] || {}).forEach(item => {
        if (item.sortedRects.length) {
            const { top, left, width, height } = item.boundingRect
            // 清除重复的
            document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach(el => el.remove());
            item.sortedRects.forEach(rect => addWaveLine({ dataTemp: false, offsetY: rect.height, width: rect.width, top: rect.top - top, left: rect.left - left, annotationId: item.id }));
        }
    })
}
// 添加波浪线
function addWaveLine({ dataTemp = true, offsetY = 0, top = 0, left = 0, width = 2000, height = 10, wavelength = 20, lineWidth = 1, annotationId } = { dataTemp: true, offsetY: 0, top: 0, left: 0, width: 2000, height: 10, wavelength: 50, lineWidth: 1, annotationId }) {
    const amplitude = height * .5;    // 振幅
    const centerY = height / 2;      // 中心Y坐标

    const svg = d3.select("#wave-container").append("svg")
        .attr("width", width)
        .attr("height", height)
        .attr("style", `position: absolute;top: ${top + offsetY}px;left:${left}px;color: #f00;z-index: 100000;`)
        .attr('data-annotation-id', annotationId)
        .attr('data-temp', dataTemp)

    // 添加点击事件
    svg.on("click", function (event) {
        tagRefs[annotationId]?.showTag()
    })

    const waveData = [];
    for (let x = 0; x < width; x++) {
        const y = (amplitude - lineWidth * 2) * Math.sin((x / wavelength) * Math.PI * 2) + centerY;
        waveData.push([x, y]);
    }
    svg.append("path")
        .datum(waveData)
        .attr("d", d3.line().x(d => d[0]).y(d => d[1]).curve(d3.curveBasis))
        .attr("stroke", "currentColor")
        .attr("fill", "none");
}

</script>
<style lang="scss" scoped>
@import '/src/styles/variables.scss';
@import '/src/styles/cf.scss';

.preview-content {
    height: calc(100vh - 175px);
    padding: 10px;
    background: #fff;
}

:deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;

    .img-Preview {
        width: 100% !important;
        height: max-content;
        min-height: 100%;
        overflow-x: unset;
        overflow-y: unset;
    }
}

.title {
    width: calc(100% - 80px);
}
</style>
<style>
#wave-container,
#tag-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100000;
    width: 0;
    height: 0;
}
</style>
