<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="true"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
      :actionWidth="100"
      actionName="操作"
    >
      <template #ruleCode="{ row }">
        <n-button
          class="has-right-border"
          code="dataManagement_collectionMonitor_view"
          variant="text"
          @click.prevent="handleSeeCodeFn(row.correspondingCodeUrl)"
          >查看</n-button
        >
      </template>
      <template #editor="{ row }">
        <div class="edit-box">
          <n-button
            class="has-right-border"
            code="dataManagement_collectionMonitor_view"
            variant="text"
            :disabled="!row.correspondingCodeUrl"
            @click.prevent="dataServiceFn(row)"
            >数据服务</n-button
          >
        </div>
      </template>
    </CfTable>
  </div>

  <!-- 规则代码弹窗 -->
  <n-drawer
    v-model="state.showDrawer"
    title=""
    :size="560"
    :close-on-click-overlay="true"
    :before-close="closeDrawer"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">规则代码</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="state.showDrawer = false" />
      </div>
      <div class="n-drawer-body-content">
        <pre>{{ state.ruleCodeTxt }}</pre>
      </div>
      <div class="n-drawer-body-footer">
        <n-button @click.prevent="state.showDrawer = false">取 消</n-button>
        <n-button variant="solid" @click="copyCodeFn">复制代码</n-button>
      </div>
    </div>
  </n-drawer>

  <!-- 数据服务弹窗 -->
  <n-drawer
    v-model="state.serviceShowDrawer"
    title=""
    :size="660"
    :close-on-click-overlay="true"
    :before-close="closeDrawer"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">数据服务</div>
        </div>
        <SvgIcon
          class="close"
          icon="icon-close"
          @click.stop.prevent="state.serviceShowDrawer = false"
        />
      </div>
      <div class="n-drawer-body-content">
        <n-form :data="state.serviceForm" label-width="150px" label-align="start">
          <n-form-item label="接口地址：">
            {{ state.serviceForm.url }}?name={{ state.serviceForm.name }}
          </n-form-item>

          <n-form-item label="请求方式：">
            {{ state.serviceForm.requestMethod }}
          </n-form-item>

          <!-- <n-form-item label="请求参数：">
            <pre>{{ state.serviceForm.requestParameter }}</pre>
          </n-form-item> -->

          <n-form-item label="返回参数：">
            <pre>{{ state.serviceForm.responseParameter }}</pre>
          </n-form-item>
        </n-form>
      </div>
      <div class="n-drawer-body-footer">
        <n-button variant="solid" @click.prevent="state.serviceShowDrawer = false">确 定</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchRuleList, workbenchRuleService } from '@/api/dataManage.js'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    showDrawer: false,
    serviceShowDrawer: false,
    ruleCodeTxt: '',
    tableData: { list: [] },
    serviceForm: {},
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'dataEntityName', name: '数据实体' },
      { prop: 'dataItem', name: '数据项' },
      { prop: 'name', name: '规则名称' },
      { prop: 'type', name: '规则类型' },
      { prop: 'content', name: '规则说明', width: 'auto' },
      { prop: 'ruleCode', name: '规则代码', slot: 'ruleCode', fixed: 'right' },
      // { prop: 'origin', name: '来源系统' },
      // { prop: 'generatingUnit', name: '产生单位', width: 'auto' },
    ],
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchRuleList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 数据服务
  const dataServiceFn = (item) => {
    state.serviceShowDrawer = true

    workbenchRuleService({ code: item.code }).then((res) => {
      state.serviceForm = res.data
      state.serviceForm.name = item.name
    })
  }

  // 查看规则代码
  const handleSeeCodeFn = (txt) => {
    state.showDrawer = true
    state.ruleCodeTxt = txt
  }

  // 复制代码的函数
  const copyCodeFn = () => {
    const textarea = document.createElement('textarea')
    textarea.value = state.ruleCodeTxt
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    ElMessage.success('复制成功')
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .nancalui-button--text {
    color: #1e89ff;
    font-size: 12px;
  }

  :deep(.common-table .page-mid .el-table tbody tr td > .cell > span) {
    text-overflow: inherit;
    -webkit-line-clamp: inherit;
  }
</style>
