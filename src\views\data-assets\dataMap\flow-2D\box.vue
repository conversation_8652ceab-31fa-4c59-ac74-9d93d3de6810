<template>
  <el-tooltip class="box-item" effect="dark" placement="top-start">
    <template #content>
      <div class="tool-box">
        <span>{{ nodeInfo.nName }}</span>
        <span>{{ nodeInfo.nEnName }}</span>
      </div>
    </template>
    <div
      :class="{ name: true, 'neighbor-node': isNeighbors }"
      :style="`border:1px solid ${nodeColor} ;background-color: ${nodeBorder};`"
      >{{ nodeInfo.nName }}</div
    >
  </el-tooltip>
</template>

<script>
  // import { reactive, toRefs, inject } from 'vue'

  export default {
    inject: ['getGraph', 'getNode'],
    // setup() {
    //   const state = reactive({
    //     node: null,
    //     nodeInfo: {
    //       nName: '',
    //       nEnName: '',
    //     },
    //   })
    //   state.node = inject('getNode')
    //   const params = toRefs(state)
    //   return {
    //     ...params,
    //   }
    // },
    data() {
      return {
        nodeInfo: {
          nName: '',
          nEnName: '',
        },
        graph: null,
        isNeighbors: false,
        nodeColor: '',
        nodeBorder: '',
        isGreen: false,
      }
    },
    mounted() {
      const node = this.getNode()
      this.getNodeColor(node?.data)
      this.graph = this.getGraph()
      this.nodeInfo = node.getData()
      this.boxEvent()
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.nodeInfo = current
        this.getNodeColor(this.nodeInfo?.data)
      })
    },
    methods: {
      boxEvent() {
        this.graph.on('node:mouseenter', ({ cell }) => {
          let allBorder = this.graph.getConnectedEdges(cell) // 获取边
          allBorder?.map((item) => {
            item.attr('line/stroke', '#697A9A')
            item.attr('line/style/animation', 'ant-line 30s infinite linear')
            item.attr('line/style/strokeDasharray', 5)
          })
          let NbNodes = this.graph.getNeighbors(cell)
          NbNodes?.map((item) => {
            item.data.isNeighbors = true
          })
          this.getNodeColor(this.getNode().data)
        })
        this.graph.on('node:mouseleave', ({ cell }) => {
          let allBorder = this.graph.getConnectedEdges(cell) // 获取边
          allBorder?.map((item) => {
            item.attr('line/stroke', '#CCD0D8')
            item.attr('line/style/animation', '')
            item.attr('line/style/strokeDasharray', 0)
          })
          let NbNodes = this.graph.getNeighbors(cell)
          NbNodes?.map((item) => {
            item.data.isNeighbors = false
          })
          this.getNodeColor(this.getNode().data)
          cell.removeTools()
        })
      },

      getNodeColor(node) {
        let color = '#fac2d6'
        let colorB = '#f3689a'
        this.isNeighbors = node?.isNeighbors || false
        switch (node.dataLineageType) {
          // switch (node.user) {
          case '实时数据':
            color = '#A2E3C6'
            colorB = '#18BA72'
            break
          case '离线数据':
            color = '#F0F7FF'
            break
          case '输出数据源':
            color = '#A6DD82'
            break
          case '数据服务':
            color = '#A6DD82'
            break
          case 'API':
            color = '#D1CAFC'
            colorB = '#8D7AF8'
            break
          case 'DATASOURCE':
            if (node.workType === 'REAL_TIME' && node.nodeType === 'SOURCE') {
              //实时输入
              color = '#A2E3C6'
              colorB = '#18BA72'
            } else if (node.workType === 'REAL_TIME' && node.nodeType === 'SINK') {
              //实时输出
              color = '#FBDBA7'
              colorB = '#F5A623'
            } else if (node.workType === 'OFFLINE' && node.nodeType === 'SOURCE') {
              //离线输入
              if (node.nodeDataType === 'STRUCTURED') {
                //结构化输入
                // color = '#8CD9F8'
                color = '#fac2d6'
                colorB = '#f3689a'
              } else if (node.nodeDataType === 'UNSTRUCTURED') {
                //非结构化
                color = '#CFDBF0'
              }
            }
            break
          case 'MODEL':
            // color = '#FFFFFF'
            color = '#D5EEFF'
            colorB = '#36AEFF'
            break
          default:
            color = '#fac2d6'
            colorB = '#f3689a'
            break
        }
        this.nodeColor = colorB
        this.nodeBorder = color
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tool-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 12px;
    span {
      display: block;
    }
  }
  .name {
    width: 48px;
    height: 48px;
    line-height: 48px;
    border-radius: 50%;
    border: 1px #f3689a solid;
    padding: 0 6px;
    box-sizing: border-box;
    background-color: #fac2d6;
    font-size: 12px;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    position: relative;
  }
  .name:hover {
    background-color: #ffe596 !important;
    border: 2px #fdbf3e solid !important;
    box-shadow: 0px 0px 16px 0px #ffe596;
  }
  .neighbor-node {
    background-color: #ffe596 !important;
    border: 1px #fdbf3e solid !important;
  }
</style>
