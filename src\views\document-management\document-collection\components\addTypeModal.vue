<template>
  <n-modal v-model="visible" style="width: 560px" :close-on-click-overlay="false" :before-close="onCancel">
    <template #title>
      <SvgIcon icon="icon-release-m" class="mr-4"></SvgIcon>
      新建接口
    </template>
    <div class="type-con">
      <div v-for="item in typeList" :key="item.id">
        <span class="title">{{ item.label }}</span>
        <div :class="{ 'type-item': true, 'is-active': activeId === item.id }" @click="onOk(item.id)"></div>
      </div>
    </div>
  </n-modal>
</template>
<script setup>
  import { ref, watch } from 'vue'

  const props = defineProps(['isShow'])
  const emit = defineEmits(['close'])
  const typeList = reactive([
    {
      id: 'documentCollectionAdd',
      label: 'SFTP',
    },
    {
      id: 'documentCollectionApiAdd',
      label: 'API',
    },
  ])
  const activeId = ref('')
  function onOk(id) {
    activeId.value = id
    if (!activeId.value) {
      ElNotification({
        title: '提示',
        message: '请选择一种类型！',
        type: 'warning',
      })
      return
    }
    visible.value = false
    emit('close', activeId.value)
  }
  function onCancel() {
    visible.value = false
    emit('close')
  }
  const visible = ref(false)
  watch(
    () => props.isShow,
    (val) => {
      visible.value = val
    },
    {
      immediate: true,
    }
  )
</script>
<style lang="less" scoped>
  .type-con {
    width: 100%;
    height: 256px;
    align-items: center;
    display: flex;
    justify-content: space-around;
    .title {
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-weight: 500;
    }
    div:nth-child(1) .type-item {
      background: url('/src/assets/img/collect/icon-restful.png') no-repeat center;
      background-size: 40% 50%;
    }
    div:nth-child(2) .type-item {
      background: url('/src/assets/img/collect/icon-api.png') no-repeat center;
      background-size: 40% 50%;
    }
    .type-item {
      width: 200px;
      height: 110px;
      padding: 29px 74px;
      border-radius: 6px;
      border: 1px solid #c5d0ea;
      margin-top: 16px;
      cursor: pointer;
      &:hover {
        border: 1px solid #2f5cd6;
      }
      &.is-active {
        border: 1px solid #2f5cd6;
      }
    }
  }
</style>
