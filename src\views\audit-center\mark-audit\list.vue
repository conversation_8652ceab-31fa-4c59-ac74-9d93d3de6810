<template>
  <section class="container" ref="tableRef">
    <section class="tools">
      <div class="row">
        <div class="col">
          资产名称：
          <n-input
            v-model="state.formInline.name"
            placeholder="请输入资产名称"
            size="small"
            clearable
            @clear="onSearch(true)"
          />
          申请人：

          <el-select
            v-model="state.formInline.person"
            placeholder="请选择"
            clearable
            filterable
            remote
            :remote-method="filterDataFn"
            @change="selectChange"
            @blur="getDeptPresonFn('')"
          >
            <el-option
              v-for="val in state.personOpt"
              :key="val.id"
              :value="val.id"
              :label="val.name + '-' + val.username"
            />
          </el-select>
          <!-- <n-tree-select
            v-model="state.formInline.person"
            filter
            allowClear
            placeholder="请选择"
            :leafOnly="true"
            :key="state.personOpt"
            :treeData="state.personOpt"
            :prop="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
          >
            <template #icon="{ nodeData, toggleNode }">
              <span
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '14.5px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="12"
                  height="12"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="#8a8e99"
                  />
                </svg>
              </span>
            </template>
            <template #default="{ item }">
              {{ item.name }}
            </template>
          </n-tree-select> -->

          申请时间：
          <n-range-date-picker-pro
            class="createTime"
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="tabs-box">
        <div class="tabs">
          <div :class="['tab', state.tabActive ? 'active' : '']" @click="tabChange(true)"
            >待审批</div
          >
          <div :class="['tab', !state.tabActive ? 'active' : '']" @click="tabChange(false)"
            >已审批</div
          >
        </div>

        <n-button v-if="state.tabActive" variant="text" color="primary" @click="allApprove('all')"
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            style="margin-right: 4px"
          >
            <path
              d="M2 12.6249L2.00173 9.64089C2.00207 9.4339 2.16992 9.26612 2.37694 9.2659C3.62715 9.2659 4.87739 9.2659 6.12762 9.2659C6.47318 9.2659 6.47135 8.95938 6.47135 8.22963C6.47135 7.49989 4.63326 6.88523 4.63326 4.31977C4.63326 1.75431 6.53742 1 8.11994 1C9.70247 1 11.4261 1.75431 11.4261 4.31977C11.4261 6.88523 9.59769 7.29312 9.59769 8.22963C9.59769 9.16611 9.59769 9.2659 9.89041 9.2659C11.1353 9.2659 12.3801 9.2659 13.625 9.2659C13.8321 9.2659 14 9.43382 14 9.64089V12.6249H2Z"
              stroke="currentColor"
              stroke-linejoin="round"
            />
            <path
              d="M2 15H14"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          一键审批</n-button
        >
      </div>

      <div class="table">
        <template v-if="state.tabActive">
          <CfTable
            actionWidth="120"
            :key="state.dataSource"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.applyTableHead"
            isNeedSelection
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #name="{ row }">
              {{ JSON.parse(row.objectContent).name }}
            </template>
            <template #ruleCode="{ row }">
              {{ JSON.parse(row.objectContent).ruleCode }}
            </template>
            <template #confidentialityLevel="{ row }">
              {{ JSON.parse(row.objectContent).confidentialityLevel }}
            </template>
            <template #applyTypeName="{ row }">
              <div :class="[row.applyType, 'audit-type']"> {{ row.applyTypeName }}</div>
            </template>
            <template #editor="{ row }">
              <n-button variant="text" color="primary" @click="onView(row)">查看</n-button>
              <n-button variant="text" color="primary" @click="allApprove(row)">审批</n-button>
            </template>
          </CfTable>
        </template>

        <template v-else>
          <CfTable
            :key="state.dataSource"
            ref="tableNoRef"
            :tableConfig="{
              data: state.dataSource,
              rowKey: 'id',
            }"
            :table-head-titles="state.auditTableHead"
            :paginationConfig="{
              total: state.page.total,
              pageSize: state.page.pageSize,
              currentPage: state.page.pageNum,
              onCurrentChange: (v) => {
                state.page.pageNum = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.page.pageSize = v
                onSearch()
              },
            }"
          >
            <template #name="{ row }">
              {{ JSON.parse(row.objectContent).name }}
            </template>
            <template #ruleCode="{ row }">
              {{ JSON.parse(row.objectContent).ruleCode }}
            </template>
            <template #confidentialityLevel="{ row }">
            </template>
            <template #applyTypeName="{ row }">
              <div :class="[row.applyType, 'audit-type']"> {{ row.applyTypeName }}</div>
            </template>
            <template #auditStatusName="{ row }">
              <i
                :style="{
                  background: row.auditStatusName === '审核通过' ? '#2CA340' : '#F63838',
                  width: '6px',
                  height: '6px',
                  display: 'inline-block',
                  'margin-right': '6px',
                  'border-radius': '20px',
                }"
                class="status"
              ></i>
              {{ row.auditStatusName === '审核通过' ? '通过' : '驳回' }}
            </template>
          </CfTable>
        </template>
      </div>
    </section>
  </section>

  <comPop
    v-if="state.auditPopShow"
    title="标签审批"
    width="540"
    @onClose="onClosePop"
    @onConfirm="onConfirmAudit"
  >
    <n-form
      :data="state.auditForm"
      ref="auditformRef"
      labelSuffix="："
      label-width="100px"
      message-type="text"
      style="width: 100%"
    >
      <n-form-item field="radio" label="审批结果">
        <n-radio-group direction="row" v-model="state.auditForm.radio">
          <n-radio value="0">通过</n-radio>
          <n-radio value="1">驳回</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item field="description" label="审批备注">
        <n-textarea v-model="state.auditForm.description" rows="5" placeholder="请输入描述信息" />
      </n-form-item>
    </n-form>
  </comPop>
</template>

<script setup>
  import { useRouter } from 'vue-router'
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import { formartTime } from '@/utils/index'
  import comPop from '@comp/comPop/index.vue'
  import { assetsRegDetail } from '@/api/dataManage.js'
  import api from '@/api/index.js'
  const router = useRouter()

  /**
   * 数据部分
   */
  const tableRef = ref()
  const tableNoRef = ref()
  const auditformRef = ref()
  const state = reactive({
    auditPopShow: false,
    tabActive: true,
    tableHeight: 500,
    formInline: {
      name: '',
      person: null,
      time: [],
    },
    searchData: {
      name: '',
      person: null,
      time: [],
    },

    auditForm: {
      radio: '0',
      description: '',
    },
    applyTableHead: [
      { prop: 'ruleCode', name: '非结构化数据编号', slot: 'ruleCode' },
      { prop: 'projectName', name: '非结构化数据名称', slot: 'name' },
      { prop: 'confidentialityLevel', name: '密级', slot: 'confidentialityLevel' },
      { prop: 'applyTime', name: '申请时间' },
      { prop: 'applyByName', name: '申请人' },
    ],
    auditTableHead: [
      { prop: 'ruleCode', name: '非结构化数据编号', slot: 'ruleCode' },
      { prop: 'projectName', name: '非结构化数据名称', slot: 'name' },
      { prop: 'applyTypeName', name: '审批类型', slot: 'applyTypeName' },
      { prop: 'applyTime', name: '申请时间' },
      { prop: 'auditByName', name: '审批人' },
      { prop: 'auditTime', name: '审批时间' },
      { prop: 'auditStatusName', name: '审批结果', slot: 'auditStatusName' },
    ],
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    personOpt: [],
    auditArr: [],
    timer: null,
    activePerson: null,
  })
  
  // 预览非结构化数据
  function onView(row) {
    router.push({
      name: 'approveMagsMark',
      query: { id: row.objectId },
    })
  }
  //下拉框change
  const selectChange = (value) => {
    if (value) {
      state.activePerson = state.personOpt.filter((item) => item.id == value)[0]
    } else {
      //清除时候
      state.activePerson = null
      getDeptPresonFn()
    }
  }
  // 获取部门人员
  const getDeptPresonFn = (search = null) => {
    let params = {
      pageNum: 1,
      pageSize: 100,
      condition: {
        departmentFullId: '1',
        departmentFullName: '全部',
      },
    }
    if (search) {
      params.condition = {
        name: search,
      }
    }
    api.system.userList(params).then((res) => {
      let { success, data } = res
      if (success && data !== null) {
        state.personOpt = data.list
        //能否找到设置人，找不到前端push一条
        if (state.formInline.person) {
          let result = state.personOpt.find((obj) => obj.id === state.formInline.person)
          if (!result) {
            state.personOpt.unshift(state.activePerson)
          }
        }
      }
    })
  }
  //人员下拉搜索
  const filterDataFn = (words) => {
    if (words) {
      //防抖
      clearTimeout(state.timer)
      state.timer = setTimeout(() => {
        getDeptPresonFn(words)
      }, 300)
    }
  }
  //开始搜索
  const startSearch = () => {
    Object.keys(state.formInline).forEach((key) => {
      state.searchData[key] = state.formInline[key]
    })

    onSearch(true)
  }
  //获取列表
  const onSearch = async (init = false) => {
    state.page.pageNum = init ? 1 : state.page.pageNum
    let { time, name, person } = state.searchData
    let startTime = ''
    let endTime = ''
    if (time) {
      if (time[0]) {
        startTime = formartTime(time[0])
      }
      if (time[1]) {
        endTime = formartTime(time[1], true)
      }
    }
    const params = {
      condition: {
        name,
        applyBy: person,
        startTime: startTime || null,
        endTime: endTime || null,
        status: state.tabActive ? 'WAIT' : 'DONE',
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    }
    const res = await api.auditCenter.documentMarkAuditList(params)
    if (res.success && res.data) {
      state.dataSource = res.data.list
      state.page.total = res.data.total
    }
  }

  const tabChange = (stuats) => {
    state.tabActive = stuats
    state.page.pageNum = 1
    onSearch()
  }

  //一键审批按钮弹框
  const allApprove = (type) => {
    if (tableNoRef.value.getSelectionRows().length === 0 && type === 'all') {
      ElMessage.error('请选择要审批的数据')
      return false
    }
    state.auditPopShow = true
    state.auditArr = type
  }
  const onClosePop = () => {
    state.auditPopShow = false
    state.auditForm = { radio: '0', description: '' }
  }
  //单个审批
  const onConfirmAudit = () => {
    if (state.auditForm.radio === '0') {
      workFlowApprovalFn({ status: 'PASS' })
    } else {
      workFlowApprovalFn({ status: 'REJECT' })
    }
  }
  // 审批接口调用
  const workFlowApprovalFn = async (data) => {
    const arr = []

    if (state.auditArr === 'all') {
      tableNoRef.value.getSelectionRows().forEach((item) => {
        arr.push({
          auditComment: state.auditForm.description,
          auditStatus: data.status,
          bizType: 'DOCUMENT_MARK',
          id: item.id,
        })
      })
    } else {
      arr.push({
        auditComment: state.auditForm.description,
        auditStatus: data.status,
        bizType: 'DOCUMENT_MARK',
        id: state.auditArr.id,
      })
    }
    const params = arr
    const res = await api.auditCenter.tagAuditCommit(params)
    if (res.success) {
      state.auditPopShow = false
      state.auditForm = { radio: '0', description: '' }
      data.status === 'PASS' ? ElMessage.success('审批通过') : ElMessage.success('审批驳回')
      onSearch(true)
    }
  }
  //重置
  const resetFn = () => {
    state.formInline = {
      name: '',
      person: '',
      time: [],
    }
    state.searchData = {
      name: '',
      person: '',
      time: [],
    }
    onSearch(true)
  }
  onMounted(() => {
    state.tableHeight = tableRef.value.clientHeight - 206
    onSearch()
    getDeptPresonFn()
  })
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .tools {
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 10px 8px 10px 16px;
      .createTime {
        margin-right: 32px;
        width: 260px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select,
      .el-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          background: #1e89ff;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
        // border-bottom: 1px solid #c5d0ea;
      }
    }
  }

  .content {
    position: relative;
    height: calc(100% - 62px);
    background: #fff;
    margin-top: 10px;
    border-radius: 2px;
  }
  .table {
    height: calc(100% - 48px);
    .common-table {
      :deep(.el-table) {
        .audit-type {
          display: flex;
          align-items: center;
          &::before {
            display: inline-block;
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 3px;
            margin-right: 6px;
            background: var(---Success-, #a8abb2);
          }

          &.PUBLISH {
            &::before {
              background: var(---Success-, #2ca340);
            }
          }
        }
      }
    }
  }

  .tabs-box {
    display: flex;
    padding: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    height: 48px;

    .tabs {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      align-self: stretch;
    }

    .tab {
      display: flex;
      width: 60px;
      height: 22px;
      box-sizing: content-box;
      padding: 5px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 2px 0px 0px 2px;
      background: #fff;
      font-size: 14px;
      color: #1d2129;
      border: 1px solid #dcdfe6;
      cursor: pointer;

      &.active {
        color: #fff;
        border-color: #1e89ff;
        background: #1e89ff;
      }
    }
  }

  .detailTit {
    display: flex;
    padding: 4px 0px;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    background: #f2f6fc;

    .name {
      color: var(----, rgba(0, 0, 0, 0.9));

      /* 常用/r500/h8 */
      font-family: 'Source Han Sans CN';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 150% */

      &::before {
        content: '';

        display: inline-block;
        width: 3px;
        height: 16px;
        background: var(---, #1e89ff);
        margin-right: 12px;
        vertical-align: middle;
      }
    }
  }

  .showName {
    width: 175px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .showFile {
    width: 500px;
    word-wrap: break-word;
    white-space: pre-wrap;

    P {
      margin: 0;
    }
  }
  :deep(.nancalui-table__empty) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  :deep(.nancalui-table .nancalui-table__thead .header-container .title) {
    font-weight: 300;
  }
</style>
