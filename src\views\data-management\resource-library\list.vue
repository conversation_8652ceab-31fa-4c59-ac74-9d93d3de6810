<template>
  <section
    :class="['container', state.isLzos ? 'isLzos' : '']"
    :style="{
      '--lzos-height': state.isLzos ? '100%' : ' calc(100% - 40px)',
    }"
  >
    <section class="container-box">
      <section class="tools">
        <div class="row">
          <n-tabs v-model="state.activeName" @active-tab-change="tabChange">
            <n-tab title="结构化数据" id="ALL_STRUCTURE" />
            <n-tab title="非结构化数据" id="NOT_STRUCTURE" />
          </n-tabs>
        </div>
        <div class="row">
          <div class="col" v-if="state.activeName === 'ALL_STRUCTURE'">
            <n-input
              v-model="state.filterSearch.keyword"
              size="small"
              placeholder="请输入模型名称"
              clearable
              @clear="initTable"
              @change="initTable"
            />

            <n-select
              v-model="state.filterSearch.projectCode"
              placeholder="请选择场景"
              filter
              :options="state.myProjectList"
              @value-change="getTreeData"
            />
          </div>
          <div class="col" v-else>
            <n-input
              v-model="state.filterSearch.keyword"
              size="small"
              placeholder="文件名称"
              clearable
              @change="onSearch"
              @clear="onSearch()"
            />
            <n-select
              v-model="state.filterSearch.projectCode"
              placeholder="请选择场景"
              filter
              :options="state.myProjectList"
              @value-change="getTreeData"
            />
            <n-cascader
              ref="cascaderHandle"
              class="mb-2"
              v-model="state.filterSearch.fileArray"
              :options="state.activeFileFormats"
              path-mode
              clearable
              filterable
              style="width: 260px"
              @change="onSearch"
            />
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="onSearch(true)">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
            <div
              class="search-btn reset"
              v-if="state.activeName !== 'ALL_STRUCTURE'"
              @click.prevent="openFn"
              >{{ state.open ? '收起' : '展开' }}
              <SvgIcon v-if="state.open" icon="icon-arrow-top" class="icon" title="收起" />
              <SvgIcon v-else icon="icon-arrow-bottom" class="icon" title="展开" />
            </div>
          </div>
        </div>
        <div v-if="state.open && state.activeName !== 'ALL_STRUCTURE'" class="row date">
          <div class="col">
            <n-range-date-picker-pro
              v-model="state.filterSearch.time"
              :placeholder="['开始日期', '结束日期']"
              format="YYYY-MM-DD"
              allow-clear
              @confirmEvent="onSearch(true)"
            />
          </div>
        </div>
      </section>
      <section
        class="table"
        :style="{
          height:
            state.open && state.activeName !== 'ALL_STRUCTURE'
              ? 'calc(100% - 150px)'
              : 'calc(100% - 112px)',
        }"
      >
        <div class="table-tree scroll-bar-style">
          <n-input
            class="table-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />
          <n-tree ref="treeRef" :data="state.treeData" @node-click="treeCheckNode">
            <template #content="{ nodeData }">
              <SvgIcon v-if="nodeData?.expanded" class="tree-icon" icon="tree-open" />
              <SvgIcon v-else class="tree-icon" icon="tree-retract" />
              <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
              <div class="tree-btn" v-if="state.activeName !== 'ALL_STRUCTURE'">
                <n-tooltip
                  v-if="nodeData.level < 4"
                  class="tree-btn"
                  content="创建目录"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon
                    class="icon"
                    icon="filter-add"
                    @click.prevent.stop="publicLeftTree.updateFn(nodeData, false, true)"
                  />
                </n-tooltip>
                <n-tooltip
                  v-if="nodeData.level !== 0 && nodeData.level !== 1"
                  class="tree-btn"
                  content="删除目录"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon
                    class="icon"
                    icon="filter-cut"
                    @click.prevent.stop="treeDelNode(nodeData, 'tree')"
                  />
                </n-tooltip>
                <n-tooltip
                  v-if="nodeData.level !== 0 && nodeData.level !== 1"
                  class="tree-btn"
                  content="编辑目录"
                  position="top"
                  :enterable="false"
                >
                  <SvgIcon
                    class="icon"
                    icon="icon-new-edit"
                    @click.prevent.stop="publicLeftTree.updateFn(nodeData, true)"
                  />
                </n-tooltip>
              </div>
            </template>
            <template #icon="{ nodeData, toggleNode }">
              <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
              <span
                v-else
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '6px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="8"
                  height="8"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="currentColor"
                  />
                </svg>
              </span>
            </template>
          </n-tree>
        </div>
        <div class="table-content">
          <template v-if="state.activeName === 'ALL_STRUCTURE'">
            <div class="box-add commonForm">
              <div class="top-left">
                <n-button
                  v-if="
                    buttonAuthList.includes(
                      'governanceManage_modal_dataCollectionResourceLibrary_resgAll_edit',
                    )
                  "
                  size="sm"
                  variant="solid"
                  color="primary"
                  @click.prevent="checkBatchRegister()"
                  >批量注册</n-button
                >
              </div>
            </div>
            <n-public-table
              ref="publicTable"
              v-loading="state.tableLoading"
              :key="state.keyCunt"
              :isDisplayAction="true"
              :isNeedSelection="true"
              :table-head-titles="state.tableHeadTitles"
              :tableData="state.tableDataList"
              :pagination="state.pagination"
              :tableHeight="state.tableHeight - 40"
              :actionWidth="250"
              @tablePageChange="tablePageChange"
              @handle-selection-change="handleSelectionChange"
            >
              <template #status="{ editor }">
                <span
                  v-if="
                    buttonAuthList.includes(
                      'governanceManage_modal_dataCollectionResourceLibrary_resg_view',
                    )
                  "
                  :class="
                    editor.row?.status === 'AUDIT_SUCCESS'
                      ? 'status-box success-box'
                      : editor.row?.status === 'AUDIT_FAIL'
                      ? 'status-box fail-box'
                      : 'status-box'
                  "
                  @click.prevent="getStatusDetail(editor.row)"
                >
                  <SvgIcon
                    v-if="editor.row?.status === 'AUDIT_SUCCESS'"
                    class="icon-status"
                    icon="status_publish"
                    title="注册成功"
                  />
                  <SvgIcon
                    v-else-if="editor.row?.status === 'WAIT_AUDIT'"
                    class="icon-status"
                    icon="status_auditing"
                    title="审核中"
                  />
                  <SvgIcon
                    v-else-if="editor.row?.status === 'AUDIT_FAIL'"
                    class="icon-status"
                    icon="status_error"
                    title="注册驳回"
                  />
                  <SvgIcon v-else class="icon-status" icon="icon-wait" title="未注册" />
                  {{ state.AUDIT_EUM[editor.row?.status] || '--' }}
                </span>
              </template>
              <template #editor="{ editor }">
                <div class="edit-box">
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'governanceManage_modal_dataCollectionResourceLibrary_view',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="checkThisTypeDetails(editor)"
                    >查看
                  </n-button>
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'governanceManage_modal_dataCollectionResourceLibrary_resg_edit',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    :disabled="
                      editor.row?.status === 'AUDIT_SUCCESS' ||
                      editor.row?.status === 'WAIT_AUDIT' ||
                      editor.row?.disabledThisRow
                    "
                    @click.prevent="checkRegister(editor)"
                    >资产注册
                  </n-button>
                </div>
              </template>
            </n-public-table>
          </template>
          <template v-else>
            <n-public-table
              ref="publicTable"
              v-loading="state.tableLoading"
              :key="state.keyCunt"
              :isDisplayAction="true"
              :table-head-titles="state.tableHeadTitlesFile"
              :tableData="state.tableDataFile"
              :pagination="state.pagination"
              :tableHeight="state.tableHeight"
              :actionWidth="80"
              @tablePageChange="tablePageChange"
              ><template #sourceFileName="{ editor }">
                <div class="file-name" :title="editor.row.fileName">
                  <img
                    class="pic"
                    :src="getAssetsImages(editor.row.sourceFileName?.split('.')?.pop())"
                  />
                  {{ editor.row.sourceFileName || '--' }}
                </div>
              </template>
              <template #fileFrom="{ editor }">
                <div class="file-name" :title="editor.row.fileName">
                  {{
                    [
                      { name: '本地文件上传', value: 'LOCAL' },
                      { name: 'HTTP文件上传', value: 'HTTP' },
                      { name: 'SFTP文件上传', value: 'SFTP' },
                      { name: 'FTP文件上传', value: 'FTP' },
                      { name: 'SFTP数据源采集', value: 'SFTP_COLLECT' },
                      { name: 'FTP数据源采集', value: 'FTP_COLLECT' },
                    ].find((item) => item.value === editor.row.fileFrom)?.name || '--'
                  }}
                </div>
              </template>
              <template #sourceFileSize="{ editor }">
                <div class="file-size-box"
                  >{{ getfilesize(editor.row.sourceFileSize) || '--' }}
                </div>
              </template>
              <template #tagList="{ editor }">
                <cfTag :tagArr="JSON.stringify(editor.row.tagList || [])" />
              </template>
              <template #confidentialityLevel="{ editor }">
                <div class="confidentiality-level">
                  <div
                    class="confidentiality-level-label"
                    :style="confidentialityLevelFn(editor.row.confidentialityLevel)"
                    >{{ editor.row.confidentialityLevelName }}</div
                  >
                </div>
              </template>
              <template #isConvertToStructured="{ editor }">
                <div class="file-size-box"
                  >{{
                    editor.row.isConvertToStructured
                      ? '是' + '（' + editor.row.structuredModelName + '）'
                      : '否'
                  }}
                </div>
              </template>
              <template #createByName="{ editor }">
                <div class="avatar-wrapper">
                  <div class="name">
                    {{ editor.row.createByName?.slice(0, 1)?.toUpperCase() }}
                  </div>
                  <img class="bg" src="@img/menu/avatar.png" />
                </div>
                {{ editor.row.createByName }}
              </template>

              <template #editor="{ editor }">
                <div class="edit-box">
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'governanceManage_modal_dataCollectionResourceLibrary_view',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="seeFn(editor.row)"
                  >
                    <n-tooltip class="tree-btn" content="查看" position="top">
                      <SvgIcon icon="new-target-service-see" class="icon" title="查看" />
                    </n-tooltip>
                  </n-button>
                  <!--                  <n-button-->
                  <!--                    v-if="-->
                  <!--                      buttonAuthList.includes(-->
                  <!--                        'governanceManage_modal_dataCollectionResourceLibrary_download_edit',-->
                  <!--                      )-->
                  <!--                    "-->
                  <!--                    class="del-button has-right-border"-->
                  <!--                    variant="text"-->
                  <!--                    @click.prevent="downLoadFile(editor.row)"-->
                  <!--                  >-->
                  <!--                    <n-tooltip class="tree-btn" content="下载" position="top">-->
                  <!--                      <SvgIcon-->
                  <!--                        icon="icon-resource-library-llst-download"-->
                  <!--                        class="icon"-->
                  <!--                        title="下载"-->
                  <!--                      />-->
                  <!--                    </n-tooltip>-->
                  <!--                  </n-button>-->
                  <n-tooltip
                    v-if="route?.query?.docx && 'DOCX'.includes(editor.row?.sourceFileFormat)"
                    class="tree-btn"
                    content="编辑word"
                    position="top"
                  >
                    <div
                      @click="
                        router.push({
                          name: 'office',
                          query: editor.row,
                        })
                      "
                    >
                      word
                    </div>
                  </n-tooltip>

                  <!-- <n-button
                    v-if="
                      buttonAuthList.includes(
                        'governanceManage_modal_dataCollectionResourceLibrary_delete',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="handlerBatchDeletion(editor)"
                    >删除
                  </n-button> -->
                  <!--                  <n-button-->
                  <!--                    class="del-button has-right-border"-->
                  <!--                    variant="text"-->
                  <!--                    @click.prevent="onlineWork(editor.row)"-->
                  <!--                    >预览-->
                  <!--                  </n-button>-->
                </div>
              </template>
            </n-public-table>
          </template>
        </div>
      </section>
    </section>
  </section>

  <!--新增目录-->

  <!--添加/编辑目录弹出框-->
  <PublicLeftTree
    v-show="0"
    ref="publicLeftTree"
    :data="state.treeData"
    :treeAttrData="state.treeAttrData"
    @treeCheckNode="treeCheckNode"
    @treeUpdateNode="treeUpdateNode"
    @treeDelNode="treeDelNode"
    :key="state.key"
  />
  <n-modal
    v-model="state.dialogVisible"
    class="has-top-padding"
    bodyClass="largeDialog"
    :draggable="false"
    width="480px"
    :before-close="closeDialog"
    :close-on-click-overlay="false"
    :append-to-body="false"
    :title="state.dialogTitle"
  >
    <n-form
      ref="ruleForm"
      :data="state.ruleForm"
      :rules="state.rules"
      label-width="120px"
      label-align="end"
      :pop-position="['right']"
    >
      <n-form-item
        v-if="state.selectTableData.length > 0"
        label="注册资产数："
        field="registryCont"
      >
        <n-input v-model="state.ruleForm.registryCont" maxlength="30" disabled />
      </n-form-item>
      <n-form-item v-if="state.selectTableData.length <= 0" label="模型名称-中文：" field="cnName">
        <n-input
          v-model="state.ruleForm.cnName"
          maxlength="30"
          placeholder="请输入字段中文名称"
          disabled
          noborder
        />
      </n-form-item>
      <n-form-item v-if="state.selectTableData.length <= 0" label="模型名称-英文：" field="name">
        <n-input
          v-model="state.ruleForm.name"
          maxlength="30"
          placeholder="请输入字段中文名称"
          disabled
          noborder
        />
      </n-form-item>
      <n-form-item label="选择业务域：" field="bizDomainId">
        <n-tree-select
          v-model="state.ruleForm.bizDomainId"
          placeholder="请选择"
          :treeData="state.bizDomainIdType"
          :prop="{
            label: 'name',
            value: 'id',
            children: 'children',
          }"
          :key="state.key"
          @valueChange="state.key++"
          leafOnly
          allowClear
          useGrayArrow
          filter
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-modal-footer class="dialog-footer">
        <n-button
          color="primary"
          size="sm"
          variant="solid"
          :loading="state.loading"
          @click.prevent="submitRegistry"
          >保 存</n-button
        >
        <n-button size="sm" @click.prevent="closeDialog">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
  <n-modal
    v-model="state.showDetail"
    class="has-top-padding"
    bodyClass="largeDialog"
    :draggable="false"
    width="580px"
    :close-on-click-overlay="false"
    :append-to-body="false"
    title="审批信息"
  >
    <n-form
      ref="ruleAuditForm"
      :data="state.ruleAuditForm"
      label-width="120px"
      label-align="end"
      :pop-position="['right']"
      class="audit-form"
    >
      <n-form-item label="审批结果：" field="auditResultName">
        <n-input v-model="state.ruleAuditForm.auditResultName" maxlength="30" disabled noborder />
      </n-form-item>
      <n-form-item label="备注信息：" field="auditComment">
        <n-textarea
          v-model="state.ruleAuditForm.auditComment"
          :rows="3"
          maxlength="200"
          placeholder="暂无信息"
          disabled
          noborder
        />
      </n-form-item>
    </n-form>
    <template #footer>
      <n-modal-footer class="dialog-footer">
        <n-button
          color="primary"
          size="sm"
          variant="solid"
          @click.prevent="state.showDetail = false"
          >关闭</n-button
        >
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script>
  import { reactive, onMounted, ref, nextTick, getCurrentInstance, toRefs, watch } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import { checkCName, checkName } from '@/utils/validate'
  import cfTag from '@/components/cfTag'

  export default {
    name: 'AuthorizedPersonnel',
    components: { cfTag },
    setup() {
      const router = useRouter()
      const route = useRoute()
      const store = useStore()
      // 获取路由参数
      const { docx: isDocx } = route.query
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const filePreviewDom = ref()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const publicTable = ref()
      const ruleForm = ref()
      const ruleAuditForm = ref()
      const publicLeftTree = ref()
      const cascaderHandle = ref()
      const previewRef = ref()

      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS,
        showAddDialog: false,
        myProjectList: [],
        showTree: false,
        key: 1,
        storageDirectoryId: null,
        shortcuts: ENUM.SHORTCUTS,
        activeType: 'dataDirectory',
        activeName: 'ALL_STRUCTURE',
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          showLeftIcon: true,
          // showCheckbox: true,
          showAddDirectory: true,
          showControl: false,
          isHideSearch: false,
          parentControl: '',
          childControl: '',
          dialogTitle: '目录',
          childTitle: '文件',
          validatorModuleName: 'dataManagement',
          validatorApiFnName: 'collectJobTreeNameValid',
        },
        dialogTitle: '资产注册',
        dialogVisible: false,
        loading: false,
        tableLoading: false,
        disabled: false,
        registryModelId: null, //当前注册的模型id
        ruleForm: {
          registryCont: '0个',
          cnName: '',
          name: '',
          bizDomainId: '',
          status: 'CREATED',
        },
        ruleAuditForm: {
          auditResultName: '注册成功',
          auditComment: '',
        },
        options: [], // 级联数据
        bizDomainIdType: [],
        keyCunt: 0,
        rules: {
          bizDomainId: [
            { required: true, message: '请选择业务域', trigger: 'change', type: 'number' },
          ],
          cnName: [{ required: true, validator: checkCName, trigger: 'blur' }],
          name: [{ required: true, validator: checkName, trigger: 'blur' }],
        },
        AUDIT_EUM: {
          CREATED: '未注册',
          WAIT_AUDIT: '审核中',
          // PUBLISHED: '已发布',
          AUDIT_SUCCESS: '注册成功',
          AUDIT_FAIL: '注册驳回',
        },
        showDetail: false,
        detailRuleForm: {},
        detailData: {},
        tableDataFile: {},
        fileTableHeight: 400,
        tableHeadTitlesFile: [
          { prop: 'sourceFileName', name: '文件名称', slot: 'sourceFileName' },
          { prop: 'fileFrom', name: '数据来源', slot: 'fileFrom' },
          { prop: 'sourceFileSize', name: '文件大小', slot: 'sourceFileSize' },
          {
            prop: 'isConvertToStructured',
            name: '是否转换为结构化数据',
            slot: 'isConvertToStructured',
          },
          { prop: 'tagList', name: '标签', slot: 'tagList', width: 220 },
          { prop: 'confidentialityLevel', name: '密级', slot: 'confidentialityLevel', width: 100 },
          { prop: 'createTime', name: '创建时间', width: 160 },
          { prop: 'createByName', name: '创建人', slot: 'createByName' },
          // { prop: 'sourceFileType', name: '文件类型' },
          // { prop: 'sourceFileFormat', name: '文件格式' },
          // { prop: 'jobName', name: '所属采集任务' },
        ],
        tableDataList: {},
        tableHeight: document.body.offsetHeight - 326,
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'layerName', name: '模型层' },
          { prop: 'description', name: '描述' },
          { prop: 'dataCount', name: '数据条数' },
          { prop: 'status', name: '注册状态', slot: 'status' },
        ], //选中类表头

        pagination: {
          currentPage: 1,
          pageSize: 10,
        },

        filterSearch: {
          keyword: null,
          fileFormat: null,
          fileArray: [],
          sourceFileType: null,
          time: [],
          treeId: null,
          treeName: null,
          projectCode: null,
        },
        activeFileFormats: [], //选中文件类型枚举
        startTime: null,
        endTime: null,
        dataModelId: '',
        project_options: [],
        selectTableData: [],
        tableData: [],
        firstInit: true, //首次初始化
      })
      if ('resourceLibraryActiveName' in localStorage) {
        state.activeName = localStorage.getItem('resourceLibraryActiveName')
      }
      const cascaderProps = {
        // expandTrigger: 'hover',
        checkStrictly: true,
        expandTrigger: 'hover',
        emitPath: true, //在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
      }

      const methods = {
        confidentialityLevelFn(name) {
          if (name === 'PUBLIC') {
            return 'color:#04C495;background-color:#E6FFF4;border-color:#04C495'
          } else if (name === 'CONFIDENTIAL') {
            return 'color:#FF7D00;background-color:#FFF6E6;border-color:#FF7D00'
          } else if (name === 'CONTROLLED') {
            return 'color:#7538F6;background-color:#F7F0FF;border-color:#7538F6'
          } else if (name === 'SECRET') {
            return 'color:#EC3B9C;background-color:#FFF0F6;border-color:#EC3B9C'
          } else if (name === 'CORE') {
            return 'color:#F63838;background-color:#FFF2F0;border-color:#F63838'
          }
        },
        getAssetsImages(name) {
          return new URL(`/src/assets/img/collect/${name}.png`, import.meta.url).href //本地文件路径
        },
        // 展开或收缩树
        treeChange() {
          if (state.filterSearch.treeId) {
            state.treeData = methods.updateTree(state.treeData, Number(state.filterSearch.treeId))
          }
        },
        updateTree(arr, id) {
          arr.forEach((val) => {
            if (val.level === 0 || val.level === 1) {
              val.expanded = true
            }
            if (val.id === id) {
              val.selected = true
            } else {
              val.selected = false
            }
            if (val.children) {
              methods.updateTree(val.children, id)
            }
          })
          return arr
        },
        // 获取项目列表
        async getMyProjectCode() {
          await api.project.getMyProjectList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data?.length) {
                let _data = data.map((item) => {
                  return Object.assign(item, { value: item.projectCode })
                })
                //记录当前场景code
                state.filterSearch.projectCode = _data[0].projectCode
                state.myProjectList = _data
              }
            }
          })
        },
        openFn() {
          state.open = !state.open
          methods.setTableHeight()
        },
        setTableHeight() {
          if (state.isLzos) {
            if (state.open) {
              state.tableHeight = document.body.offsetHeight - 306 + 72
            } else {
              state.tableHeight = document.body.offsetHeight - 268 + 72
            }
          } else {
            if (state.open) {
              state.tableHeight = document.body.offsetHeight - 302
            } else {
              state.tableHeight = document.body.offsetHeight - 264
            }
          }
        },
        // 查看
        seeFn(item) {
          api.dataManagement.permissionCheck({ id: item.id }).then((res) => {
            if (res.success) {
              if (res.data) {
                router.push({
                  name: 'resourceLibrarySee',
                  query: {
                    id: item.id,
                  },
                })
              } else {
                ElNotification({
                  title: '提示',
                  message: '用户密级不匹配！',
                  type: 'warning',
                })
              }
            }
          })
        },
        //下载文件
        async downLoadFile(item) {
          api.dataManagement.permissionCheck({ id: item.id }).then(async (resp) => {
            if (resp.success) {
              if (resp.data) {
                let _objName = item.ossUrl.split('/data-govern/')
                const res = await api.dataManagement.fileDownload({
                  bucket: 'data-govern',
                  objName: decodeURIComponent(_objName[_objName.length - 1]), //把转义过的地址转回来
                })

                if (res.type === 'application/json') {
                  // 说明是普通对象数据，读取信息
                  const fileReader = new FileReader()
                  fileReader.readAsText(res)
                  fileReader.onloadend = () => {
                    const jsonData = JSON.parse(fileReader.result)
                    // 后台信息
                    ElNotification({
                      title: '提示',
                      message: jsonData.message,
                      type: 'error',
                    })
                  }
                } else {
                  // 下载文件
                  const blob = new Blob([res], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
                  })
                  const link = document.createElement('a')
                  const fileName = item.ossUrl.split('/')
                  link.download = fileName[fileName.length - 1]
                  link.style.display = 'none'
                  link.href = URL.createObjectURL(blob)
                  document.body.appendChild(link)
                  link.click()
                  URL.revokeObjectURL(link.href)
                  document.body.removeChild(link)
                }
              } else {
                ElNotification({
                  title: '提示',
                  message: '用户密级不匹配！',
                  type: 'warning',
                })
              }
            }
          })
        },
        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
        },
        // 批量删除二次确认操作
        handlerBatchDeletion(data) {
          if (data) {
            //单
            proxy.$MessageBoxService.open({
              title: '是否删除选中文件',
              content: '删除后的文件将不可恢复',
              save: () => {
                methods.batchDeletion(data)
              },
            })
          } else {
            // 多

            if (state.selectTableData.length) {
              proxy.$MessageBoxService.open({
                title: '是否批量删除选中文件',
                content: '删除后的文件将不可恢复',
                save: () => {
                  methods.batchDeletion()
                },
              })
            } else {
              ElNotification({
                title: '提示',
                message: '请勾选数据',
                type: 'warning',
              })
            }
          }
        },
        batchDeletion(data) {
          let _ids = []
          if (data) {
            _ids.push(data.row.id)
          } else {
            state.selectTableData.forEach((item) => {
              _ids.push(item.id)
            })
          }

          // return
          api.assets.fileDeleteBatch(_ids).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '删除文件成功',
                type: 'success',
              })
              if (data) {
                //单
                publicTable.value.toggleRowSelection(data.row, false)
                if (state.tableData.length === 1) {
                  methods.initTable()
                } else {
                  methods.initTable(false)
                }
              } else {
                publicTable.value.clearSelection()
                methods.initTable()
              }
            }
          })
        },
        // 文件大小转换
        getfilesize(size) {
          if (!size) return ''
          var num = 1024.0 //byte
          if (size < num) return size + 'B'
          if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + 'K' //kb
          if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + 'M' //M
          if (size < Math.pow(num, 4)) return (size / Math.pow(num, 3)).toFixed(2) + 'G' //G
          return (size / Math.pow(num, 4)).toFixed(2) + 'T' //T
        },
        tabChange(val) {
          state.treeSearchText = ''
          state.open && methods.openFn()
          state.key++
          state.keyCunt++
          localStorage.resourceLibraryActiveName = val
          state.activeName = val
          state.tableLoading = true
          methods.initData()
          nextTick(() => {
            methods.getTreeData()
          })
        },
        //注册
        //获取业务域下拉
        getSelectData() {
          sceneManage.searchTreeList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.bizDomainIdType = methods.reformData(data)
              }
            }
          })
        },
        // 对tree数据改造
        reformData(data) {
          data.forEach((val) => {
            val.label = val.name
            val.value = val.id
            val.expanded = true
            if (val.children) {
              methods.reformData(val.children)
            }
          })
          return data
        },
        // 关闭弹框
        closeDialog() {
          state.dialogVisible = false
        },
        submitRegistry() {
          ruleForm.value.validate((valid) => {
            if (valid) {
              if (state.selectTableData.length > 0) {
                //批量
                methods.registryModel(true)
              } else {
                methods.registryModel()
              }
            }
          })
        },
        checkRegister(editor) {
          publicTable.value?.clearSelection()
          state.selectTableData = []
          state.ruleForm.registryCont = '0个'
          const { row } = editor
          state.registryModelId = row.id
          state.ruleForm.name = row.name
          state.ruleForm.cnName = row.cnName
          state.ruleForm.status = row.status
          state.ruleForm.bizDomainId = ''
          if (row.bizDomainId) {
            state.ruleForm.bizDomainId = row.bizDomainId
          }
          methods.getSelectData()
          state.dialogVisible = true
        },
        // 在线协作
        onlineWork(item) {
          if (item.sourceFileFormat !== 'XLSX') {
            ElNotification({
              title: '提示',
              message: '暂不支持XLSX以外的非结构化数据！',
              type: 'warning',
            })
          } else {
            let str = JSON.stringify({
              ossUrl: item.ossUrl,
              fileType: item.sourceFileType,
              fileFormat: item.sourceFileFormat,
            })
            sessionStorage.setItem('onlineWorkData', str)
            router.push({
              name: 'onlineWork',
              query: {
                id: item.id,
              },
            })
          }
        },
        // 注册资产/批量
        registryModel(isPack = false) {
          let params = []
          if (isPack) {
            state.selectTableData.map((item) => {
              params.push({
                status: item.status,
                bizDomainId: state.ruleForm.bizDomainId,
                id: item.id,
              })
            })
          } else {
            params.push({
              status: state.ruleForm.status,
              bizDomainId: state.ruleForm.bizDomainId,
              id: state.registryModelId,
            })
          }
          state.dialogVisible = false
          api.assets
            .registerAssets(params)
            .then((res) => {
              const { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: '发起注册成功',
                  type: 'success',
                })
                methods.initTable(false)
              }
            })
            .catch(() => (state.dialogVisible = false))
        },
        getStatusDetail(row) {
          const { id, status } = row
          if (status === 'AUDIT_SUCCESS' || status === 'AUDIT_FAIL') {
            state.ruleAuditForm.auditResultName = state.AUDIT_EUM[status]
            api.assets.getStatusList({ id }).then((res) => {
              if (res.success) {
                state.ruleAuditForm.auditComment = res.data.auditComment
                state.showDetail = true
              }
            })
          }
        },
        checkBatchRegister() {
          if (state.selectTableData.length) {
            proxy.$MessageBoxService.open({
              title: '提示',
              content: '是否批量注册选中的数据',
              save: () => {
                state.ruleForm.registryCont = state.selectTableData.length + '个'
                state.ruleForm.bizDomainId = ''
                methods.getSelectData()
                state.dialogVisible = true
              },
            })
          } else {
            ElNotification({
              title: '提示',
              message: '请勾选数据',
              type: 'warning',
            })
          }
        },
        // 选中人员执行
        treeCheckNode(checkItem) {
          state.filterSearch.fileFormat = null
          if (checkItem.id === 0 || checkItem.id === 'ROOT') {
            state.filterSearch.treeId = null
            state.filterSearch.treeName = null
          } else {
            state.filterSearch.treeId = checkItem.id
            state.filterSearch.treeName = checkItem.name
          }

          methods.initTable()
        },
        //编辑和新增目录树
        treeUpdateNode(item) {
          let { ruleForm, checkItem, isEdit } = item
          let _interfaceName = 'collectJobTreeCreate'
          let _data = {}
          let _message = ''
          if (isEdit) {
            // 编辑
            _message = ruleForm?.subkeysAddAvailable ? '编辑目录成功' : '编辑文件成功'
            _interfaceName = 'collectJobTreeUpdate'
            _data = {
              description: ruleForm.desc,
              id: checkItem.id,
              level: checkItem.level,
              name: ruleForm.name,
              pid: checkItem.pid,
            }
          } else {
            _message = ruleForm?.subkeysAddAvailable ? '新增目录成功' : '新增文件成功'
            // 新增
            _data = {
              description: ruleForm.desc,
              level: checkItem.level + 1,
              name: ruleForm.name,
              pid: checkItem.id,
              groupType: ruleForm.subkeysAddAvailable ? 'DICTORY' : 'FILE',
            }
          }
          api.dataManagement[_interfaceName](_data)
            .then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: _message,
                  type: 'success',
                })
                publicLeftTree.value.clearFn()
                methods.getTreeData()
              }
            })
            .catch(publicLeftTree.value.clearLoading())
        },
        //删除目录树
        treeDelNode(item) {
          if (item.children && item.children.length) {
            ElNotification({
              title: '提示',
              message: '该节点含有子节点不可删除',
              type: 'warning',
            })
          } else {
            proxy.$MessageBoxService.open({
              title: '是否确认删除该目录',
              content: '删除后该目录将不可恢复',
              save: () => {
                api.dataManagement.collectJobTreeDel({ id: item.id }).then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除目录成功',
                      type: 'success',
                    })
                    methods.getTreeData()
                  }
                })
              },
            })
          }
        },
        resetFn() {
          state.filterSearch = {
            keyword: null,
            fileFormat: null,
            fileArray: [],
            sourceFileType: null,
            time: [],
            treeId: null,
            treeName: null,
            projectCode: state.myProjectList[0].projectCode,
          }
          methods.onSearch()
        },
        onSearch(init) {
          let { time } = state.filterSearch

          if (time[0] && time[1]) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          } else {
            state.startTime = null
            state.endTime = null
          }
          methods.initTable(init)
        },

        // 初始化结构化表格
        initTable(init = true) {
          if (state.filterSearch.projectCode) {
            let interFaceUrl = ''
            let data = {}
            state.pagination.currentPage = init ? 1 : state.pagination.currentPage
            if (state.activeName === 'ALL_STRUCTURE') {
              interFaceUrl = 'getResourceLibraryTableList'
              data = {
                condition: {
                  name: state.filterSearch.keyword || null,
                  layerId: state.filterSearch.treeId,
                  layerName: state.filterSearch.treeName,
                  projectCodeList: [state.filterSearch.projectCode],
                },
                pageNum: state.pagination.currentPage,
                pageSize: state.pagination.pageSize,
              }
            } else {
              interFaceUrl = 'getThisFileTypeListSearch'
              if (init) {
                publicTable.value?.clearSelection()
              }
              if (state.filterSearch.fileArray?.length) {
                state.filterSearch.sourceFileType = state.filterSearch.fileArray[0]
                state.filterSearch.fileFormat =
                  state.filterSearch.fileArray[1] || state.filterSearch.fileArray[0]
                // state.filterSearch.fileArray[1] || null
              } else {
                state.filterSearch.sourceFileType = null
                state.filterSearch.fileFormat = null
              }
              data = {
                condition: {
                  fileFormat: state.filterSearch.fileFormat || null,
                  startTime: state.startTime || null,
                  endTime: state.endTime || null,
                  fileName: state.filterSearch.keyword || null,
                  treeId: state.filterSearch.treeId,
                  projectCode: state.filterSearch.projectCode,
                },
                pageNum: state.pagination.currentPage,
                pageSize: state.pagination.pageSize,
              }
            }
            api.assets[interFaceUrl](data)
              .then((res) => {
                state.tableLoading = false
                // 新增序号属性
                state.firstInit = false
                if (res.success) {
                  res.data.list.map((item, index) => {
                    if (!item.sourceFileName && item.ossUrl) {
                      let fileName = item.ossUrl.split('/')
                      item.sourceFileName = fileName[fileName.length - 1]
                    }
                    return Object.assign(item, {
                      number: index + 1,
                      //注册成功状态、审核中或为原始层下数据时设为true
                      disabledThisRow:
                        item.status === 'AUDIT_SUCCESS' ||
                        item.status === 'WAIT_AUDIT' ||
                        item.isOriginLayer,
                      // disabledThisRow: true,
                    })
                  })
                  state.keyCunt++
                  state.tableData = res.data.list
                  state.tableDataList = res.data
                  state.tableDataFile = res.data
                }
              })
              .catch(() => {
                state.tableLoading = false
              })
          } else {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
          }
        },

        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          let data = {}
          data.layerId = row.layerId
          data.layerName = row.layerName
          data.projectName = row.projectName
          data.description = row.description
          router.push({
            name: 'resourceLibraryDetail',
            query: {
              id: row.id,
              modelTitle: row.cnName || row.name,
              modelName: row.name,
              type: 'DATAMODEL',
              ruleForm: JSON.stringify(data),
            },
          })
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取左侧树数据
        //新增区分场景，接口支持传入场景参数
        getTreeData() {
          if (state.filterSearch.projectCode) {
            if (state.activeName === 'ALL_STRUCTURE') {
              // 模型分层下拉列表
              api.model
                .getDataModelTree({ projectCode: state.filterSearch.projectCode })
                .then((res) => {
                  let { success, data } = res
                  if (success) {
                    if (data !== null) {
                      if (data.length > 0) {
                        data[0].expanded = true
                      }
                      state.treeData = [
                        {
                          description: '全部',
                          id: 0,
                          name: '全部',
                          children: data,
                        },
                      ]
                      state.filterSearch.treeId = state.treeData[0]?.id || null
                      methods.initTable()
                    }
                  }
                })
                .catch(() => {})
            } else {
              api.dataManagement
                .collectJobTreeList({ projectCode: state.filterSearch.projectCode })
                .then((res) => {
                  let { success, data } = res
                  if (success) {
                    if (data !== null) {
                      state.treeData = data
                      if (data.length > 0) {
                        data[0].expanded = true
                      }
                      if (state.storageDirectoryId) {
                        state.filterSearch.treeId = state.storageDirectoryId
                        methods.treeChange()
                      } else {
                        state.filterSearch.treeId = data[0]?.id || null
                      }
                      //默认点击
                      methods.initTable()
                      state.key++
                    }
                  }
                })
                .catch(() => {})
            }
          } else {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
          }
        },

        matchingIcon(data) {
          let _icon = 'tree-child'
          switch (data.value) {
            case 'EXCEL':
              _icon = 'icon-excel'
              break
            case 'CSV':
              _icon = 'icon-csv'
              break
            case 'PDF':
              _icon = 'icon-pdf'

              break
            case 'WORD':
              _icon = 'icon-word'

              break
            case 'IMAGE':
              _icon = 'icon-image'
              break
            default:
              _icon = 'tree-child'
              break
          }
          return _icon
        },
        //获取文件格式树
        getFileTypeList() {
          api.dataManagement.getFileTypeList().then((res) => {
            let { success, data } = res
            state.activeFileFormats = []
            if (success) {
              state.activeFileFormats = this.reformFile(data)
            }
          })
        },
        reformFile(data) {
          data.forEach((val) => {
            if (val.groupType === 'DICTORY') {
              val.subkeysAddAvailable = true
            }
            if (val.children && val.children.length > 0) {
              methods.reformFile(val.children)
            } else {
              val.children = null
            }
          })
          return data
        },
        treeSelectChange() {
          if (state.filterSearch.fileArray?.length) {
            state.filterSearch.sourceFileType = state.filterSearch.fileArray[0]
            state.filterSearch.fileFormat = state.filterSearch.fileArray[1] || null
          } else {
            state.filterSearch.sourceFileType = null
            state.filterSearch.fileFormat = null
          }

          methods.initTable()

          // state.filterSearch.fileFormat
          //  state.filterSearch.fileType
          // cascaderHandle.value.togglePopperVisible()
        },

        // // 点击树节点
        // clickNode(data) {
        //   state.dataModelId = data.id ? data.id : null
        //   if (state.dataModelId) {
        //     methods.initTable()
        //   }
        // },

        // 初始化数据
        initData() {
          state.filterSearch.keyword = null
          state.selectTableData = []

          if (state.activeName === 'ALL_STRUCTURE') {
            state.treeAttrData.parentControl = ''
            state.treeAttrData.parentControl = ''
            state.treeAttrData.showControl = false
            state.tableDataList = {}
          } else {
            state.treeAttrData.parentControl = '1'
            state.treeAttrData.childControl = '123'
            state.treeAttrData.showControl = true
            state.tableDataFile = {}
          }
          state.key++
          state.showTree = true
        },
      }
      onMounted(() => {
        state.storageDirectoryId = router.currentRoute.value.query.storageDirectoryId || null
        nextTick(async () => {
          methods.setTableHeight()
          methods.initData()
          await methods.getMyProjectCode()
          if (state.storageDirectoryId) {
            methods.tabChange('NOT_STRUCTURE')
          } else {
            methods.getTreeData()
          }
          methods.getFileTypeList()
        })
      })
      const treeRef = ref(null)
      watch(
        () => state.treeSearchText,
        (val) => {
          treeRef.value.treeFactory.searchTree(val, {
            isFilter: true,
          })
        },
      )
      return {
        state,
        treeRef,
        buttonAuthList,
        ruleForm,
        ruleAuditForm,
        publicTable,
        publicLeftTree,
        cascaderHandle,
        filePreviewDom,
        cascaderProps,
        isDocx,
        router,
        route,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    &.isLzos {
      padding: 0;
    }

    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      .tools {
        background-color: #fff;
        border-radius: 8px;

        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 52px;
          padding: 0 16px;
          &.date {
            height: 40px;
            margin-top: -2px;
            padding-bottom: 8px;
            :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
              width: 260px;
            }
          }

          .col {
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 0 8px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 14px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
            }
          }

          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 16px;
          }

          .search {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-btn {
              margin-right: 8px;
              padding: 5px 15px;
              color: $themeBlue;
              font-weight: 400;
              font-size: 14px;
              border: 1px solid $themeBlue;
              border-radius: 6px;
              cursor: pointer;

              &:last-of-type {
                margin-right: 0;
              }

              &.reset {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 5px 8px;
                border: 1px solid transparent;

                .icon {
                  margin-left: 4px;
                  font-size: 10px;
                }
              }

              &:hover {
                background-color: #e3ecff;
                border: 1px solid #e3ecff;
              }
            }
          }

          &:first-of-type {
            box-sizing: border-box;
            border-bottom: 1px solid #c5d0ea;
          }
        }
      }

      .table {
        height: calc(100% - 112px);
        margin-top: 8px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 8px;

        .title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 52px;
          padding: 0 16px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          border-bottom: 1px solid #c5d0ea;

          &:before {
            position: absolute;
            top: 17px;
            left: 0;
            width: 4px;
            height: 18px;
            background: var(
              --Radial,
              radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
            );
            border-radius: 0 4px 4px 0;
            content: '';
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }

        &-tree {
          display: inline-block;
          box-sizing: border-box;
          width: 272px;
          height: 100%;
          padding: 8px 16px;
          overflow-y: auto;
          vertical-align: top;
          border-right: 1px solid #e5e5e5;

          &-ipt {
            margin-bottom: 8px;

            :deep(.nancalui-input-slot__suffix) {
              opacity: 0.5;

              .icon-search {
                font-weight: normal;
                transform: scale(1.4);
              }
            }
          }

          &-header {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 92px;
            height: 32px;
            margin-bottom: 8px;
            color: $themeBlue;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
          }

          :deep(.nancalui-tree) {
            .nancalui-tree__node {
              border-radius: 6px;

              &:has(.active) {
                background-color: #ecf7ff;
              }

              &:hover {
                background-color: #ecf7ff;

                .nancalui-tree__node-content--value-wrapper {
                  .tree-icon {
                    color: $themeBlue;
                  }

                  .tree-label {
                    max-width: calc(100% - 73px);
                    color: $themeBlue;
                  }

                  .tree-btn {
                    display: inline;
                  }
                }
              }

              .nancalui-tree__node-vline {
                width: 0;
              }

              .nancalui-tree__node-content {
                border-radius: 6px;

                &:hover {
                  background-color: #ecf7ff;
                }

                > span {
                  width: 28px;
                  padding-left: 10px;
                  color: #8091b7;
                }

                .nancalui-tree__node-content--value-wrapper {
                  position: relative;
                  width: 100%;
                }

                &.active {
                  background-color: #ecf7ff;

                  .nancalui-tree__node-content--value-wrapper {
                    .tree-label,
                    .tree-icon {
                      color: $themeBlue;
                    }
                  }

                  & > span {
                    svg {
                      color: $themeBlue;
                    }
                  }
                }

                .tree-icon {
                  min-width: 16px;
                  margin-right: 4px;
                  color: #8091b7;
                  font-size: 16px;
                }

                .tree-label {
                  max-width: calc(100% - 20px);
                  overflow: hidden;
                  color: rgba(0, 0, 0, 0.85);
                  font-size: 14px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .tree-btn {
                  display: none;
                  width: 80px;

                  .icon {
                    margin-left: 4px;
                    color: $themeBlue;
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }

        &-content {
          display: inline-block;
          box-sizing: border-box;
          width: calc(100% - 272px);
          height: 100%;
          padding: 16px;
          vertical-align: top;

          &-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 48px;

            :deep(.nancalui-tabs) {
              width: 100%;

              .nancalui-tabs-nav-tab {
                padding: 0 16px;
              }
            }
          }

          &-box {
            box-sizing: border-box;
            height: 100%;
            padding: 8px 16px;
          }

          .empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: calc(100% - 100px);

            .pic {
              width: 72px;
              height: auto;
              margin-bottom: 10px;
            }

            &-word {
              color: rgba(0, 0, 0, 0.46);
              font-weight: 400;
              font-size: 14px;
              text-align: center;
            }
          }

          .file-name {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .pic {
              width: 28px;
              height: 28px;
              margin-right: 6px;
              background-color: transparent;
            }
          }

          .avatar-wrapper {
            position: relative;
            display: inline-block;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            line-height: 28px;
            text-align: center;

            .user-avatar {
              width: 28px;
              height: 28px;
              border-radius: 100px;
            }

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              z-index: 1;
              width: 100%;
              height: 100%;
            }

            .name {
              position: relative;
              z-index: 2;
              color: #fff;
              font-weight: bold;
              font-size: 16px;
            }
          }

          :deep(.common-table) {
            height: var(--lzos-height);

            .page-mid {
              height: 100%;

              .nancalui-table-page {
                padding-bottom: 0;
              }
            }

            .nancalui-table__tbody tr td .nancalui-table__cell {
              & > div {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }

  .tag-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .tag {
      margin-right: 6px;
    }

    &-more {
      height: 28px;
      line-height: normal;
    }
  }

  .tag {
    width: 44px;
    height: 24px;
    padding: 0 4px;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    border: 1px solid;
    border-radius: 6px;

    &.PUBLIC {
      color: #04c495;
      background: #e6fff4;
      border-color: #04c495;
    }

    &.INTERIOR {
      color: rgba(0, 0, 0, 0.75);
      background: #e3ecff;
      border-color: #a3b4db;
    }

    &.CONTROLLED {
      color: #447dfd;
      background: #e3ecff;
      border-color: #447dfd;
    }

    &.SECRET {
      color: #ec3b9c;
      background: #fff0f6;
      border-color: #ec3b9c;
    }

    &.CONFIDENTIAL {
      color: #ff7d00;
      background: #fff6e6;
      border-color: #ff7d00;
    }

    &.CORE {
      color: #f63838;
      background: var(---Error-, #fff2f0);
      border-color: #f63838;
    }
  }

  .commonForm {
    margin-bottom: 8px;
  }

  :deep(.nancalui-tabs) {
    overflow: unset;

    .nancalui-tabs-nav-tab {
      height: 48px;
      border-bottom: none;
    }
  }

  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 6px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
