<template>
  <div class="tag-content">
    <div class="tag-box" ref="tagParent">
      <div class="tag-box-width" ref="tagChild">
        <n-tag
          v-for="tag in tagList"
          :key="tag.name"
          :class="tag.name === checkedTagName ? 'checked' : ''"
          type="info"
          size="small"
          deletable
          @click.prevent.stop="handleClick(tag)"
        >
          <SvgIcon class="menu-icon" :icon="tag.icon" @click.prevent.stop="handleClose(tag,tag.name === checkedTagName)" />
          {{ tag.title }}
          <SvgIcon class="icon-close" icon="icon-close" @click.prevent.stop="handleClose(tag,tag.name === checkedTagName)" />
        </n-tag>
      </div>
    </div>
    <div class="tag-box-more">
      <div class="tag-box-more-bg"></div>
      <n-popover
        v-if="showMoreTag"
        :position="['bottom-end']"
        align="end"
        class="tag-box-more-popover"
        trigger="hover"
      >
        <template #content>
          <div class="tag-box-more-content scroll-bar-style">
            <div
              :class="
                tag.name === checkedTagName
                  ? 'tag-box-more-content-label checked'
                  : 'tag-box-more-content-label'
              "
              v-for="tag in tagList"
              :key="tag.name"
              @click.prevent="handleClick(tag)"
              >{{ tag.title }}
            </div>
          </div>
        </template>
        <div class="icon-more-box clear">
          <n-popover class="item" content="更多" trigger="hover" :position="['bottom']">
            <SvgIcon class="icon-more" icon="icon-more" />
          </n-popover>
        </div>
      </n-popover>
      <n-popover
        :position="['bottom-end']"
        align="end"
        class="tag-box-clear-popover"
        trigger="hover"
      >
        <template #content>
          <div class="tag-box-clear-content">
            <div class="tag-box-clear-content-label" @click.prevent="filterDelFn('all')"
              ><SvgIcon class="icon-clear" icon="icon-all-clear" title="全部清除" />全部清除</div
            >
            <div class="tag-box-clear-content-label" @click.prevent="filterDelFn('left')"
              ><SvgIcon class="icon-clear" icon="icon-left-clear" title="向左清除" />向左清除</div
            >
            <div class="tag-box-clear-content-label" @click.prevent="filterDelFn('right')"
              ><SvgIcon class="icon-clear" icon="icon-right-clear" title="向右清除" />向右清除</div
            >
          </div>
        </template>
        <div class="icon-more-box">
          <SvgIcon class="icon-more" icon="icon-all-clear" />
        </div>
      </n-popover>
    </div>
  </div>
</template>

<script>
  import { reactive, onMounted, watch, toRefs, nextTick, onBeforeMount } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter, onBeforeRouteUpdate, onBeforeRouteLeave } from 'vue-router'

  export default {
    setup() {
      const state = reactive({
        tagList: [],
        menuTreeList: [],
        showMoreTag: false,
        checkedTagName: null,
        checkFirstMenuCode: null,
        timeFlag: null,
      })
      const store = useStore()
      const router = useRouter()
      const tagParent = ref(null) // tag父dom宽度
      const tagChild = ref(null) // tagList当前宽度

      watch(
        () => store.state.user.checkFirstMenuCode,
        (newVal) => {
          state.checkFirstMenuCode = newVal
          sessionStorage.setItem('TAG_LIST_CF','[]')
          state.tagList = []
        },
      )
      watch(
        () => store.state.user.menuTreeList,
        (newVal) => {
          state.menuTreeList = newVal
        },
      )
      // 监控父路由变化
      onBeforeRouteLeave((to) => {
        if (state.timeFlag) {
          clearTimeout(state.timeFlag)
          state.timeFlag = null
        }
        state.timeFlag = setTimeout(() => {
          methods.routerChangeFn(to)
        }, 100)
      })
      // 监控子路由变化
      onBeforeRouteUpdate((to) => {
        if (state.timeFlag) {
          clearTimeout(state.timeFlag)
          state.timeFlag = null
        }
        state.timeFlag = setTimeout(() => {
          methods.routerChangeFn(to)
        }, 100)
      })
      const methods = {
        // 获取vuex信息
        getVuexData() {
          state.menuTreeList = store.state.user.menuTreeList
          state.checkFirstMenuCode = store.state.user.checkFirstMenuCode
        },
        // 监控路由变化
        routerChangeFn(to) {
          setTimeout(() => {
            methods.getVuexData()
            let routerItem = null
            let routerList = [...to.matched].reverse()
            routerList.some((item) => {
              if (item?.meta?.code) {
                routerItem = { ...item }
                return true
              }
            })
            //  判断对应公共菜单栏的路由名称
            if (routerItem) {
              let tagList = sessionStorage.getItem('TAG_LIST_CF')
              if(tagList){
                tagList = JSON.parse(tagList)
              }else {
                tagList = []
              }
              let tags = [...tagList]
              let activeMenu = routerItem.name
              state.checkedTagName = activeMenu
              let activeInfo = {}
              if (state.menuTreeList?.length > 0) {
                console.log(methods.getMenuFn(routerItem.name))
                activeInfo = { ...methods.getMenuFn(routerItem.name) }
              }
              // 修改tag对应中文名称
              if (activeInfo.routerName !== routerItem.name) {
                if (activeInfo?.children?.[0]?.resourceType === 'MENU') {
                  activeInfo.children.some((val) => {
                    if (val.routerName === routerItem.name) {
                      activeInfo.name = val.name
                      return true
                    }
                  })
                }
              }
              if (routerItem.name === 'Home') {
                activeInfo = {
                  name: '首页',
                  code: 'home',
                }
              }
              sessionStorage.setItem('checkedTagName', activeMenu)
              tags = tags.filter((val) => val.name !== activeMenu)
              tags.unshift({
                title: activeInfo.name,
                name: activeMenu,
                query: to.query,
                params: to.params,
                icon: 'menu-' + activeInfo.code + '-tag',
              })
              sessionStorage.setItem('TAG_LIST_CF',JSON.stringify(tags))
              console.log(tags)
              state.tagList = [...tags]
              methods.checkTagWidthFn(tags)
            }
          }, 500)
        },
        // 获取当前选中菜单的code
        getMenuFn(activeMenu) {
          let activeInfo = {}
          let mainMenuInfo = state.menuTreeList.filter(
            (val) => val.code === state.checkFirstMenuCode,
          )[0]?.children
          if (mainMenuInfo) {
            mainMenuInfo.forEach((val) => {
              if (val.routerName === activeMenu) {
                activeInfo = { ...val }
              } else {
                if (val.children) {
                  val.children.forEach((item) => {
                    if ((item.routerName || item.code) === activeMenu) {
                      activeInfo = { ...val }
                    }
                  })
                }
              }
            })
          }

          return activeInfo
        },
        // 删除路由历史
        handleClose(tag,flag) {
          let tags = JSON.parse(JSON.stringify(state.tagList))
          tags = tags.filter((item) => item.name !== tag.name)
          sessionStorage.setItem('TAG_LIST_CF',JSON.stringify(tags))
          state.tagList = [...tags]
          methods.checkTagWidthFn(tags)
          if(state.tagList.length === 0){
            router.push({ name: 'ConvergencePage'})
            return
          }
          if(flag){
            let item = tags[0]
            router.push({ name: item.name, query: item.query, icon: item.icon, params: item.params })
          }

        },

        // 历史路由筛选删除
        filterDelFn(type) {
          let tags = JSON.parse(JSON.stringify(state.tagList))
          let isCheck = false
          let newTags = []
          if (type === 'all') {
            newTags = []
          } else if (type === 'left') {
            tags.forEach((val) => {
              if (val.name === state.checkedTagName) {
                isCheck = true
              }
              if (isCheck) {
                newTags.push(val)
              }
            })
          } else if (type === 'right') {
            tags.forEach((val) => {
              if (!isCheck) {
                newTags.push(val)
              }
              if (val.name === state.checkedTagName) {
                isCheck = true
              }
            })
          }
          sessionStorage.setItem('TAG_LIST_CF',JSON.stringify(newTags))
          state.tagList = [...newTags]
          methods.checkTagWidthFn(newTags)
          if(newTags.length === 0){
            router.push({ name: 'ConvergencePage'})
          }
        },
        // 选择当前路由历史
        handleClick(tag) {
          router.push({ name: tag.name, query: tag.query, icon: tag.icon, params: tag.params })
        },
        // 检测选项卡是否超过一行宽度
        checkTagWidthFn(tags = null) {
          let tagList = sessionStorage.getItem('TAG_LIST_CF')
          if(tagList){
            state.tagList = JSON.parse(tagList)
          }else {
            state.tagList = []
          }
          nextTick(() => {
            if (tagParent.value && tagChild.value) {
              let parentWidth = tagParent.value.clientWidth
              let childWidth = tagChild.value.clientWidth
              if (childWidth >= parentWidth) {
                state.showMoreTag = true
              } else {
                state.showMoreTag = false
              }
            }
            if (tags) {
              state.tagList = tags
            }
          })
        },
      }
      // 初始化
      onBeforeMount(() => {
        if (sessionStorage.getItem('checkedTagName')) {
          state.checkedTagName = sessionStorage.getItem('checkedTagName')
        }
      })
      onMounted(() => {
        methods.getVuexData()
        setTimeout(()=>{
          methods.checkTagWidthFn()
        },110)
      })
      const params = toRefs(state)
      return {
        tagParent,
        tagChild,
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .tag-content {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    background-color: #fff;
    z-index: 99;
    .tag-box {
      box-sizing: border-box;
      width: calc(100% - 60px);
      height: 36px;
      padding: 3px 0 3px 16px;
      overflow: hidden;

      &-width {
        display: inline-block;
        white-space: nowrap;

        .nancalui-tag {
          box-sizing: border-box;
          height: 30px;
          margin-right: 10px;
          padding: 0 12px;
          color: #606266;
          line-height: 30px;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dcdfe6;
          border-radius: 1px;
          cursor: pointer;
          :deep(.nancalui-tag__item) {
            height: 30px;
            padding: 0;
            font-size: 14px;
            border: none;
            cursor: pointer;
            .remove-button {
              display: none;
            }
          }

          .icon-close {
            padding: 0;
            color: #999;
            font-size: 16px;
          }
          .circle {
            display: none;
            width: 6px;
            height: 6px;
            margin-right: 4px;
            vertical-align: 2px;
            background-color: #447dfd;
            border-radius: 50%;
          }
          .menu-icon {
            display: none;
            color: #447dfd;
            font-size: 14px;
          }
          &.checked,
          &:hover {
            color: #447dfd;
            background-color: #ebf4ff;
            border: 1px solid #99c9ff;
            .icon-close {
              color: #447dfd;
            }
          }
          &.checked {
            .menu-icon {
              display: none;
              margin-right: 3px;
            }
            .circle {
              display: inline-block;
            }
          }
        }
      }
    }

    .tag-box-more {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-sizing: border-box;
      width: 90px;
      height: 36px;
      margin: auto;
      padding: 6px 6px 6px 8px;
      &-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #fff;
        filter: blur(5px);
      }
      .icon-more-box {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        margin-left: 16px;
        text-align: center;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background-color: #e9ecf1;
          border: 1px solid #e9ecf1;
        }
        &.clear {
          margin-left: 0;
          .icon-more {
            font-size: 16px;
          }
        }
        .icon-more {
          color: #333333;
          font-size: 20px;
        }
      }
    }
  }
  .tag-box-clear-popover {
    .tag-box-clear-content {
      &-label {
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: #333333;
        font-size: 12px;
        line-height: 32px;
        cursor: pointer;
        .icon-clear {
          margin-right: 4px;
          font-size: 20px;
        }
        &:hover {
          background-color: #f7f8fa;
          border-radius: 4px;
        }
      }
    }
  }
  .tag-box-more-popover {
    .tag-box-more-content {
      max-height: 488px;
      overflow-y: auto;

      &-label {
        padding: 0 10px;
        overflow: hidden;
        color: #333333;
        font-size: 12px;
        line-height: 32px;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;

        &:hover {
          background-color: #f7f8fa;
          border-radius: 4px;
        }

        &.checked {
          color: $themeBlue;
          font-weight: bold;
          background-color: #ecf3ff;
        }
      }
    }
  }
</style>
