<template>
  <!-- 资产全景-资产目录 -->
  <div class="asset-library-page container">
    <div class="statistics-table">
      <div class="statistics-left">
        <PublicLeftTree
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          @treeCheckNode="treeCheckNode"
        />
      </div>
      <div class="statistics-right">
        <n-public-table
          ref="publicTable"
          :isDisplayAction="true"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :tableData="state.tableData"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          :actionWidth="150"
          @tablePageChange="tablePageChange"
        >
          <template #pageTop>
            <div class="box-add commonForm">
              <div class="top-left"> </div>
              <n-form :inline="true" :model="state.filterSearch">
                <n-form-item label="时间范围：">
                  <n-range-date-picker-pro
                    v-model="state.filterSearch.timeData"
                    :clearable="true"
                    :placeholder="['开始日期', '结束日期']"
                    @confirmEvent="initTable"
                  />
                </n-form-item>
                <n-form-item label="">
                  <n-input
                    v-model="state.filterSearch.keyword"
                    placeholder="请输入模型名称"
                    clearable
                    @clear="initTable"
                  >
                    <template #append>
                      <n-button @click.stop.prevent="initTable">
                        <n-popover
                          class="item"
                          content="搜索"
                          trigger="hover"
                          :position="['bottom']"
                        >
                          <SvgIcon class="icon_search" icon="icon_search" />
                        </n-popover>
                      </n-button>
                    </template>
                  </n-input>
                </n-form-item>
              </n-form>
            </div>
          </template>

          <template #status="{ editor }">
            {{
              editor.row.status === 'AUDIT_SUCCESS' && editor.row.assetsRegistered
                ? '未注销'
                : editor.row.status === 'WAIT_AUDIT' && editor.row.assetsRegistered
                ? '审核中'
                : '--'
            }}
          </template>
          <template #editor="{ editor }">
            <div class="edit-box">
              <n-button
                v-if="state.buttonAuthList?.includes('assetsManage_dataAssets_targetLibrary_view')"
                class="del-button has-right-border"
                variant="text"
                @click.stop.prevent="checkThisTypeDetails(editor)"
                >查看
              </n-button>
            </div>
          </template>
        </n-public-table></div
      >
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, ref, toRefs, nextTick } from 'vue'
  import { ElNotification } from 'element-plus'
  import { formartTime } from '@/utils/index'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'

  export default {
    name: 'AuthorizedPersonnel',
    components: {},
    setup() {
      const router = useRouter()
      const { proxy } = getCurrentInstance()
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/assetLibrary/${name}.png`, import.meta.url).href //本地文件路径
      }
      // 获取当前组件实例
      // const { proxy } = getCurrentInstance()
      const store = useStore()
      const publicTable = ref()
      const state = reactive({
        buttonAuthList: [],
        bizDomainId: null,
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          showLeftIcon: true,
          // showCheckbox: true,
          showControl: false,
          parentControl: '',
          childControl: '',
        },
        detailRuleForm: {},
        detailData: {},

        // 统计的所有类
        statisticalDataType: [
          // {
          //   title: '元数据', // 名称
          //   number: '--', // 数量
          //   viewable: true, // 可查看
          //   icon: getAssetsImages('asset-library-1'), // 图片
          //   unit: '条', // 单位
          // },
          {
            title: '数据模型',
            number: '--',
            viewable: true,
            icon: getAssetsImages('asset-library-2'),
            unit: '条',
          },
        ],

        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '指标模型中文名' },
          { prop: 'name', name: '指标模型英文名' },
          { prop: 'layerName', name: '指标模型分类' },
          { prop: 'dataCount', name: '数据条数' },
          { prop: 'description', name: '描述信息' },
        ], //选中类表头
        tagList: [],
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
        tableData: {},
        tableHeight: 400,

        filterSearch: {
          keyword: '',
          tagName: '',
          timeData: '',
        },
        dataModelId: '',
        project_options: [],
        interfaceName: 'getAssetsLibraryModelList', //默认获取元数据列表
      })

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 240
        },
        getTagList() {
          api.assets.getAssetsTagList().then((res) => {
            let { success } = res
            if (success) {
              state.tagList = res.data
            }
          })
        },
        // 选中人员执行
        treeCheckNode(data) {
          if (
            data.checkItem.id === 1 ||
            data.checkItem.id === 'ROOT' ||
            data.checkItem.name === '全部'
          ) {
            state.bizDomainId = null
          } else {
            state.bizDomainId = data.checkItem.id
          }

          methods.initTable()
        },
        // 获取统计数据
        getStatistics() {
          api.assets.getAssetsLibraryCount().then((res) => {
            let { success } = res
            if (success) {
              let { metadataNum, modelNum, tagNum, apiNum } = res.data
              state.statisticalDataType[0].number = metadataNum
              state.statisticalDataType[1].number = modelNum
              state.statisticalDataType[2].number = tagNum
              state.statisticalDataType[3].number = apiNum
            }
          })
        },

        // 初始化表格
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let startTime = ''
          let endTime = ''
          if (state.filterSearch.timeData[0] && state.filterSearch.timeData[1]) {
            startTime = formartTime(state.filterSearch.timeData[0])
            endTime = formartTime(state.filterSearch.timeData[1], true)
          }
          let params = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              name: state.filterSearch.keyword || null,
              bizId: state.bizDomainId,
              startTime,
              endTime,
            },
          }
          api.assets.indicatorModelList(params).then((res) => {
            if (res.success) {
              // 新增序号属性
              res.data.list.map((val, index) => {
                return Object.assign(val, { number: index + 1 })
              })
              state.tableData = res.data
            }
          })
        },

        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          let data = {}
          data.layerId = row.layerId
          data.layerName = row.layerName

          data.projectName = row.projectName
          data.description = row.description
          router.push({
            name: 'targetLibraryDetail',
            query: {
              id: row.id,
              targetTitle: row.cnName || row.name,
              targetName: row.name,
              type: 'DATAMODEL',
              ruleForm: JSON.stringify(data),
            },
          })
        },
        //注销
        checkRegister(editor) {
          const { row } = editor
          proxy.$MessageBoxService.open({
            title: '资产注销',
            content: `注销后，该数据会被移除资产目录，确定是否进行资产注销？`,
            save: () => {
              methods.submitRegister(row)
            },
          })
        },
        submitRegister(row) {
          let params = [row.id]
          api.assets.deregisterAssets(params).then((res) => {
            const { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '发起注销成功',
                type: 'success',
              })
              methods.initTable(false)
            }
          })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取左侧树数据
        getTreeData(status) {
          sceneManage.searchTreeList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                let haveRoot = data.some((item) => {
                  if (item.name === '全部') {
                    return true
                  }
                })
                if (haveRoot) {
                  state.treeData = data
                } else {
                  state.treeData = [
                    {
                      description: '全部',
                      id: 0,
                      name: '全部',
                      children: data,
                    },
                  ]
                }
                if (status) {
                  // methods.initTable()
                  //默认点击
                  nextTick(() => {
                    const firstNode = document.getElementsByClassName('el-tree-node')[0]
                    // firstNode.click()
                    methods.initTable()
                  })
                }
              }
            }
          })
        },
      }
      onMounted(() => {
        nextTick(() => {
          const { buttonAuthList } = toRefs(store.state.user)
          state.buttonAuthList = buttonAuthList
          methods.setTableHeight()
          methods.getTreeData(true)
          methods.getTagList()
        })
      })

      return {
        state,
        publicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .asset-library-page {
    background-color: #f2f3f5;
    padding: 10px;
    height: 1;
    &.container {
      height: 100%;
    }

    &-top {
      padding: 0 20px;
      background-color: #fff;
      // margin-bottom: 10px;
      border-radius: 4px 4px 0 0;
    }

    .statistics-table {
      border-radius: 4px;
      // padding: 0 20px;
      background-color: #fff;
      height: 100%;
      display: flex;
      padding-right: 20px;
      .statistics-left {
        .title {
          height: 32px;
          line-height: 28px;
          background: #f2f3f6;
          border-radius: 4px;
          border: 2px solid #ffffff;
          color: #333333;
          font-size: 12px;
          text-align: center;
          font-weight: bolder;
        }
      }
      .statistics-right {
        flex: 1;
        min-width: 0;
        padding: 0 0 0 20px;
        :deep(.el-table) {
          .total-row {
            span {
              padding-right: 5px;
              &:nth-of-type(2) {
                color: #333;
                padding: 0 5px;
              }
            }
          }
          :deep(.nancalui-button--outline) {
            background-color: transparent;
          }
        }
      }
      .top-left {
        display: flex;
        align-items: center;
      }

      :deep(.el-pagination) {
        background-color: #fff;
      }
    }

    .need_smallcube__title {
      display: block;
      height: 16px;
      line-height: 18px;
      border-left: 4px solid var(--themeBlue);
      padding-left: 6px;
      font-size: 16px;
      color: #333333;
      font-weight: bolder;
    }

    .top-change-project {
      height: 78px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dcdcdc;

      .top-change-project-left {
        display: flex;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: bolder;
          color: #333333;
          line-height: 24px;
        }
      }
    }

    .mid-statistics {
      display: flex;
      align-items: flex-start;
      padding: 18px 0 4px;

      .statistics-box-left {
        padding: 10px 20px 0 0;
      }

      .statistics-box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding: 0 20px 0 0;

        .statistics-list {
          display: flex;
          align-content: center;
          justify-content: center;
          width: 25%;

          img {
            width: 106px;
            height: 106px;
          }

          &-des {
            display: flex;
            align-items: center;
          }

          &-des-box {
            cursor: pointer;
            color: #333;
            font-size: 12px;

            &.can-click {
              cursor: pointer;
            }

            .top {
              display: flex;
              align-items: center;
              line-height: 20px;
            }

            .number {
              color: #333333;
              font-size: 24px;
              font-weight: bolder;
              padding: 0 5px;
            }

            .unit {
              color: #666;
            }

            .yy-icon {
              width: 20px;
              color: #c9c9cb;
              margin-left: 10px;
            }

            &.active {
              .yy-icon {
                color: $themeFontColor;
              }
            }
          }
        }

        span {
          color: #25bfff;
        }
      }
    }

    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
    }

    .edit-box {
      .del-button {
        &:first-child {
          border-left: 0 !important;
        }

        &:last-child {
          padding-right: 0;
        }
      }
    }
  }
</style>
