<template>
  <n-modal
    draggable
    :model-value="showInfo.isShow"
    :title="
      showInfo.type === 'edge'
        ? '关系对象信息'
        : showInfo.type === 'nc-table'
        ? '表对象信息'
        : '实体对象信息'
    "
    :before-close="beforeClose"
    width="800px"
  >
    <div class="tras content-table">
      <n-my-table
        :tableData="tableData"
        :attrList="columns"
        :isAction="false"
        :isPage="false"
        style="margin-top: 20px"
      />
    </div>

    <template #footer>
      <n-modal-footer>
        <n-button color="primary" plain @click="handlecloseDialog(false)">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script>
  export default {
    props: {
      showInfo: {
        type: Boolean,
        default: false,
      },
      cell: {
        type: Object,
        default: null,
      },
      edge: {
        type: Object,
        default: null,
      },
      graphList: {
        type: Array,
        default: () => {
          return []
        },
      },
      subjectId: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        columns: [
          { prop: 'name', name: '项目' },
          { prop: 'info', name: '信息' },
        ],
        tableData: [],
      }
    },
    watch: {
      showInfo: {
        handler(val) {
          if (val.isShow) {
            const graph = this.graphList.filter((graph) => graph.subjectGuid === this.subjectId)[0]
            if (this.showInfo.type === 'edge') {
              this.tableData = [
                { name: '关系名称', info: this.edge?.data?.name },
                { name: 'UID', info: this.edge?.id },
                { name: '类型', info: 'relation' },
                { name: '创建时间', info: graph?.createTime },
                { name: '修改时间', info: graph?.updateTime },
              ]
            } else if (this.showInfo.type === 'nc-table') {
              this.tableData = [
                { name: '实体名称', info: this.cell.data?.table?.tabName },
                { name: 'UID', info: this.cell.data.guid },
                { name: '类型', info: 'table' },
                { name: '创建时间', info: graph?.createTime },
                { name: '修改时间', info: graph?.updateTime },
              ]
            } else {
              this.tableData = [
                { name: '实体名称', info: this.cell.data?.entity?.name },
                { name: 'UID', info: this.cell.data.guid },
                { name: '类型', info: 'entity' },
                { name: '创建时间', info: graph?.createTime },
                { name: '修改时间', info: graph?.updateTime },
              ]
            }
          }
        },
        deep: true,
      },
    },
    methods: {
      beforeClose(done) {
        this.tableData = []
        done()
        this.handlecloseDialog()
      },
      // 关闭弹窗，清除选中数据和查询条件
      handlecloseDialog() {
        this.$emit('close')
      },
    },
  }
</script>

<style lang="scss"></style>
