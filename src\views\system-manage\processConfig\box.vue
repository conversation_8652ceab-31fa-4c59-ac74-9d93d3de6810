<template>
  <div class="box">
    <div v-if="state.nodeInfo.isSide" class="isSide">{{ state.nodeInfo.name }}</div>
    <div v-else class="box-person">
      <div class="box-person-title">
        审批人：
        <img class="del" src="/src/assets/img/close-circle-fill.png" />
      </div>

      <el-select
        v-model="state.nodeInfo.assignee"
        placeholder="请选择审批人"
        filterable
        :filter-method="filterDataSystemFn"
        @blur="getLeftTreeDataFn(false)"
      >
        <el-option v-for="val in state.options" :key="val.id" :value="val.id" :label="val.name" />
      </el-select>
    </div>
    <img v-if="!state.nodeInfo.isEnd" class="add" src="/src/assets/img/circle-add.png" />
  </div>
</template>

<script setup>
  import { reactive, inject, onMounted, nextTick } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'

  const sourceData = []
  const state = reactive({
    node: null,
    currentPerson: {},
    nodeInfo: {
      isEnd: false,
      isSide: false,
      name: '',
      assignee: '',
    },
    timer: null,
    options: [],
  })
  state.node = inject('getNode')()

  const filterMethod = (value) => {
    state.options = [...sourceData].filter((item) => item.name.includes(value))
  }
  const filterDataSystemFn = (words) => {
    if (words) {
      //防抖
      clearTimeout(state.timer)
      state.timer = setTimeout(() => {
        getLeftTreeDataFn(words, 'selectDataSystem')
      }, 300)
    }
  }

  const getLeftTreeDataFn = (search = null, type = null) => {
    // state.loading = true
    let params = {
      pageNum: 1,
      pageSize: 100,
      condition: {
        departmentFullId: '1',
        departmentFullName: '全部',
      },
    }
    if (search) {
      params.condition = {
        name: search,
      }
    }
    api.system
      .userList(params)
      .then((res) => {
        let { success, data } = res
        if (success) {
          if (data !== null) {
            state.options = data.list
            state.options.push(state.currentPerson)
          }
        }
      })
      .catch(() => {
        state.loading = false
      })
  }
  onMounted(() => {
    state.nodeInfo = state.node.getData() || {
      name: '',
      assignee: '',
      isEnd: false,
      isSide: false,
      opts: [],
    }

    if (state.nodeInfo.nodeType === 'TASK') {
      state.currentPerson = {
        id: state.nodeInfo.assignee,
        name: state.nodeInfo.assigneeName,
      }
    }
    getLeftTreeDataFn()

    // 监听数据改变事件
    state.node.on('change:data', ({ current }) => {
      state.nodeInfo = current
    })
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .box {
    position: relative;
    box-sizing: border-box;
    width: 260px;
    height: 88px;
    line-height: 88px;
    text-align: center;
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
    &:has(.isSide) {
      background-color: transparent;
      box-shadow: none;
      .isSide {
        width: 88px;
        height: 88px;
        line-height: 88px;
        text-align: center;
        border-radius: 50%;
        margin: 0 auto;
        background-color: #ffffff;
        font-weight: bolder;
        font-size: 28px;
        box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
      }
    }

    &-person {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 88px;
      padding: 0 10px;

      &-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        width: 100%;
        height: 20px;
        margin-bottom: 6px;
        .del {
          width: 24px;
          height: 24px;
          cursor: pointer;
        }
      }
      :deep(.el-select) {
        width: 100%;
      }
    }

    &:hover {
      .add {
        display: block;
      }
    }
    .add {
      display: none;
      position: absolute;
      width: 24px;
      height: 24px;
      left: 0;
      right: 0;
      bottom: -3px;
      margin: auto;
      background-color: #fff;
      box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      cursor: pointer;
    }
  }
</style>
