<template>
  <!-- 项目列表 -->
  <div class="container-padding16 project-list">
    <div class="list-box" v-loading="state.loading">
      <CfTable
        actionWidth="240"
        :key="state.tableData"
        ref="tableNoRef"
        :tableConfig="{
          data: state.tableData,
          rowKey: 'id',
        }"
        :table-head-titles="state.tableHeadTitles"
        :paginationConfig="{
          total: state.pagination.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,
          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            initTable()
          },
          onSizeChange: (v) => {
            state.pagination.pageSize = v
            initTable(true)
          },
        }"
      >
        <template #pageTop>
          <div class="box-add">
            <search @handleSearch="handleSearch">
              <template #searchLeft>
                <n-button
                  code="projectManage_project_add"
                  color="primary"
                  size="sm"
                  variant="solid"
                  @click.prevent="goJumpAdd"
                >
                  <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
                  新建项目
                </n-button>
              </template>
            </search>
          </div>
        </template>

        <template #editor="{ data: { row } }">
          <div class="edit-box">
            <n-button
              code="projectManage_project_view"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="seeDetails(row)"
              >查看</n-button
            >
            <n-button
              code="projectManage_project_edit"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="editDetails(row)"
              >编辑</n-button
            >
            <n-button
              code="projectManage_project_delete"
              variant="text"
              class="seeDetails has-right-border"
              @click.prevent="delDetails(row)"
              >删除</n-button
            >
            <n-button
              class="seeDetails has-right-border"
              variant="text"
              @click.stop.prevent="editPerson(row)"
              >项目人员管理
            </n-button>
          </div>
        </template>
      </CfTable>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs, watch } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter, useRoute } from 'vue-router'
  import { ElNotification } from 'element-plus'
  export default {
    title: 'List',
    name: 'Ewewew',
    components: { search },
    props: {},
    setup() {
      const router = useRouter()
      const route = useRoute()
      const state = reactive({
        loading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'name', name: '项目名称' },
          { prop: 'description', name: '项目描述' },
          { prop: 'updateTime', name: '最后修改时间' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0,
          // pageSizes: [2, 5, 30, 50, 100], // 每次展示条数的可配置项
        },
        startTime: '',
        endTime: '',
        keyword: null,
        isAdmin: false,
        tableData: [],
        tableHeight: 436,
        routerName: 'project',
      })

      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)

      // 获取当前组件实例
      const { proxy } = getCurrentInstance()

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 302
        },
        // 初始化form
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              name: state.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          //记录当前页码信息
          store.commit('user/SET_PAGINATION_INFO', {
            pagination: state.pagination,
            routerName: state.routerName,
          })
          api.project
            .searchMyProjectList(data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, {
                  number: index + 1,
                })
              })

              state.tableData = res.data.list
              state.pagination.total = res.data.total
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize

          methods.initTable()
        },
        // 新增项目
        goJumpAdd() {
          router.push({ name: 'projectAdd' })
        },
        // 查看
        seeDetails(row) {
          router.push({
            name: 'projectDetail',
            query: {
              id: row.id,
              type: 'DETAIL',
              pageNum: state.pagination.currentPage,
              pageSize: state.pagination.pageSize,
            },
          })
        },
        // 编辑项目
        editDetails(row) {
          router.push({ name: 'projectEdit', query: { id: row.id, type: 'EDIT' } })
        },
        // 编辑项目人员
        editPerson(row) {
          router.push({ name: 'projectAddPerson', query: { id: row.id } })
        },
        // 删除项目
        delDetails(row) {
          proxy.$MessageBoxService.open({
            title: '是否确认删除项目',
            content: '删除后，项目将不可恢复',
            save: () => {
              api.project.deleteProject({ id: row.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除成功',
                    type: 'success',
                  })
                  this.$store.commit('user/ADD_PROJECT', true)
                  if (state.tableData.length === 1) {
                    methods.initTable(true)
                  } else {
                    methods.initTable()
                  }
                }
              })
            },
          })
        },
        // 搜索
        handleSearch(data) {
          let { time, keyword } = data
          state.keyword = keyword
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }
          methods.initTable(true)
        },

        // 设置为默认项目
        setAsDefault(editor) {
          let { row } = editor

          api.project
            .setMyDefaultProject({ projectCode: row.projectCode, off: row.preferred })
            .then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: '操作成功',
                  type: 'success',
                })
              } else {
                editor.row.preferred = !editor.row.preferred
              }
              methods.initTable()
            })
            .catch(() => {
              editor.row.preferred = !editor.row.preferred
            })
        },

        refreshData() {
          const { name, isAdmin, pagination } = toRefs(store.state.user)
          state.isAdmin = isAdmin
          console.log(router)
          methods.initTable(false)
          // if (pagination.value && pagination.value && pagination.value[state.routerName]) {
          //   //回到上次的页码数和每页展示条数
          //   state.pagination.currentPage = pagination.value[state.routerName].currentPage
          //   state.pagination.pageSize = pagination.value[state.routerName].pageSize
          //   methods.initTable(false)
          // } else {
          //   methods.initTable(true)
          // }
        },
      }
      methods.setTableHeight()
      watch(
        () => route.query?.refresh,
        (newQuery, oldQuery) => {
          if (newQuery === 'true') {
            methods.refreshData()
            // 移除 refresh 参数
            const { refresh, ...newQuery } = route.query
            router.push({ name: route.name, query: newQuery })
          }
        },
        { immediate: true }, // { immediate: true } 表示立即执行一次
      )

      onMounted(() => {
        methods.refreshData()
      })

      return {
        buttonAuthList,
        ...methods,
        state,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .project-list {
    background: #f0f2f5;
    .list-box {
      height: 100%;
      border-radius: 4px;
      .box-add {
        .nancalui-button {
          padding: 0 16px;
        }
      }
    }
    :deep(.nancalui-table-page) {
      background: #fff;
    }

    :deep(.page-mid) {
      background: #fff;
      height: calc(100% - 110px);
    }

    :deep(.nancalui-table-page .nancalui-pagination .nancalui-pagination__total-size) {
      position: relative;
      top: 0;
      left: 0;
      margin: 0;
    }
    :deep(.nancalui-table-page) {
      padding: 14px 16px;
    }
  }
</style>
