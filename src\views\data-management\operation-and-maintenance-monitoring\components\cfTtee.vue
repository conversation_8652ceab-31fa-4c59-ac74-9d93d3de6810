<template>
  <div class="tree-box">
    <el-tree v-bind="$attrs" ref="treeRef" class="filter-tree">
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span class="text-label" :title="node.label">{{ node.label }}</span>
          <div class="btn-box">
            <i class="add" @click="addFn(data)"></i>
            <i class="edit" @click="updateFn(data, false)"></i>
            <i class="del" @click="delFn(data)"></i>
          </div>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
  const treeRef = ref(null)
  const state = reactive({})
  const {} = toRefs(state)
  const emit = defineEmits(['updateFn', 'delFn'])
  const updateFn = (data, isEdit) => {
    emit('updateFn', data, isEdit)
  }
  const addFn = (data) => {
    emit('addFn', data)
  }
  const delFn = (data) => {
    emit('delFn', data)
  }
  const preventFn = (e) => {
    e.stopPropagation()
  }
  defineExpose({
    treeRef,
    name: '你是审美之间',
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/cf.scss';
  .tree-box {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
  .filter-tree {
    width: 100%;
    height: 100%;
    :deep(.el-tree-node__expand-icon) {
      padding: 4px;
      transform: rotate(0deg);
      > svg {
        display: none;
      }
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
    :deep(.expanded) {
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTkgMUgyLjk5OTg4QzEuODk1MzEgMSAwLjk5OTg3OCAxLjg5NTQzIDAuOTk5ODc4IDNWMTNDMC45OTk4NzggMTQuMTA0NiAxLjg5NTMxIDE1IDIuOTk5ODggMTVIMTIuOTk5OUMxNC4xMDQ0IDE1IDE0Ljk5OTkgMTQuMTA0NiAxNC45OTk5IDEzVjNDMTQuOTk5OSAxLjg5NTQzIDE0LjEwNDQgMSAxMi45OTk5IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTg4IDdINS45OTk4OEM1LjQ0NzU5IDcgNC45OTk4OCA3LjQ0NzcyIDQuOTk5ODggOEM0Ljk5OTg4IDguNTUyMjggNS40NDc1OSA5IDUuOTk5ODggOUg5Ljk5OTg4QzEwLjU1MjIgOSAxMC45OTk5IDguNTUyMjggMTAuOTk5OSA4QzEwLjk5OTkgNy40NDc3MiAxMC41NTIyIDcgOS45OTk4OCA3WiIgZmlsbD0iIzU4NjQ3NSIvPgo8L3N2Zz4K');
      }
    }
    :deep(.el-tree-node__content) {
      height: 30px;
      gap: 4px;
    }
    :deep(.el-tree-node__content) {
      &:hover {
        border-radius: 2px;
        background: #ebf4ff;
      }
    }
    .custom-tree-node {
      display: flex;
      align-items: center;
      gap: 4px;
      .text-label {
        // 超出省略
        width: 100px;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEuNSAzQzEuNSAyLjQ0NzcyIDEuOTQ3NzIgMiAyLjUgMkgxMy41QzE0LjA1MjMgMiAxNC41IDIuNDQ3NzIgMTQuNSAzVjEzQzE0LjUgMTMuNTUyMyAxNC4wNTIzIDE0IDEzLjUgMTRIMi41QzEuOTQ3NzIgMTQgMS41IDEzLjU1MjMgMS41IDEzVjNaIiBzdHJva2U9IiM1ODY0NzUiLz4KPGxpbmUgeDE9IjUuNSIgeTE9IjEiIHgyPSI1LjUiIHkyPSIzIiBzdHJva2U9IiM1ODY0NzUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8bGluZSB4MT0iMTMuNSIgeTE9IjciIHgyPSIyLjUiIHkyPSI3IiBzdHJva2U9IiM1ODY0NzUiIHN0cm9rZS1saW5lY2FwPSJzcXVhcmUiLz4KPGxpbmUgeDE9IjEwLjUiIHkxPSIxIiB4Mj0iMTAuNSIgeTI9IjMiIHN0cm9rZT0iIzU4NjQ3NSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .btn-box {
        display: flex;
        align-items: center;
        gap: 4px;
      }
      .add {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfMV8yMjA5MjApIj4KICAgIDxjaXJjbGUgY3g9IjgiIGN5PSI4IiByPSI3IiBzdHJva2U9IiM2MDYyNjYiLz4KICAgIDxwYXRoIGQ9Ik01IDhIMTEiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgICA8cGF0aCBkPSJNOCA1VjExIiBzdHJva2U9IiM2MDYyNjYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgogIDwvZz4KICA8ZGVmcz4KICAgIDxjbGlwUGF0aCBpZD0iY2xpcDBfMV8yMjA5MjAiPgogICAgICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9IndoaXRlIi8+CiAgICA8L2NsaXBQYXRoPgogIDwvZGVmcz4KPC9zdmc+Cg==');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .edit {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNOS4wODMzMyAxLjVIMi41QzEuOTQ3NzIgMS41IDEuNSAxLjk0NzcyIDEuNSAyLjVWMTMuNUMxLjUgMTQuMDUyMyAxLjk0NzcyIDE0LjUgMi41IDE0LjVIMTMuNUMxNC4wNTIzIDE0LjUgMTQuNSAxNC4wNTIzIDE0LjUgMTMuNVY3LjQ1ODMzIiBzdHJva2U9IiM2MDYyNjYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgogIDxwYXRoIGQ9Ik03IDcuNEwxMy40IDFMMTUgMi42TDguNiA5SDdWNy40WiIgc3Ryb2tlPSIjNjA2MjY2IiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .del {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfMV8yMjA5MjIpIj4KICAgIDxwYXRoIGQ9Ik0yLjUgNEgxMy41SDIuNVpNMTQgMTRDMTQgMTQuODI4NCAxMy4zMjg0IDE1LjUgMTIuNSAxNS41SDMuNUMyLjY3MTU3IDE1LjUgMiAxNC44Mjg0IDIgMTRIM0MzIDE0LjI3NjEgMy4yMjM4NiAxNC41IDMuNSAxNC41SDEyLjVDMTIuNzc2MSAxNC41IDEzIDE0LjI3NjEgMTMgMTRIMTRaTTMuNSAxNS41QzIuNjcxNTcgMTUuNSAyIDE0LjgyODQgMiAxNFY0SDNWMTRDMyAxNC4yNzYxIDMuMjIzODYgMTQuNSAzLjUgMTQuNVYxNS41Wk0xNCA0VjE0QzE0IDE0LjgyODQgMTMuMzI4NCAxNS41IDEyLjUgMTUuNVYxNC41QzEyLjc3NjEgMTQuNSAxMyAxNC4yNzYxIDEzIDE0VjRIMTRaIiBmaWxsPSIjNjA2MjY2Ii8+CiAgICA8cGF0aCBkPSJNNCA0SDEySDRaTTEyLjUgMkMxMi41IDEuMTcxNTcgMTEuODI4NCAwLjUgMTEgMC41SDVDNC4xNzE1NyAwLjUgMy41IDEuMTcxNTcgMy41IDJINC41QzQuNSAxLjcyMzg2IDQuNzIzODYgMS41IDUgMS41SDExQzExLjI3NjEgMS41IDExLjUgMS43MjM4NiAxMS41IDJIMTIuNVpNNSAwLjVDNC4xNzE1NyAwLjUgMy41IDEuMTcxNTcgMy41IDJWNEg0LjVWMkM0LjUgMS43MjM4NiA0LjcyMzg2IDEuNSA1IDEuNVYwLjVaTTEyLjUgNFYyQzEyLjUgMS4xNzE1NyAxMS44Mjg0IDAuNSAxMSAwLjVWMS41QzExLjI3NjEgMS41IDExLjUgMS43MjM4NiAxMS41IDJWNEgxMi41WiIgZmlsbD0iIzYwNjI2NiIvPgogICAgPGxpbmUgeDE9IjEiIHkxPSIzLjUiIHgyPSIxNSIgeTI9IjMuNSIgc3Ryb2tlPSIjNjA2MjY2IiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KICAgIDxsaW5lIHgxPSIxMS41IiB5MT0iMTIuNSIgeDI9IjQuNSIgeTI9IjEyLjUiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF8xXzIyMDkyMiI+CiAgICAgIDxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0id2hpdGUiLz4KICAgIDwvY2xpcFBhdGg+CiAgPC9kZWZzPgo8L3N2Zz4K');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
  }
</style>
