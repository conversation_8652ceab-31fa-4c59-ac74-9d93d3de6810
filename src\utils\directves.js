// vue 自定义指令
export default (app) => {
  app.directive('loadMore', {
    mounted(el, binding) {
      const SELECTDOWN_DOM = document.querySelector(
        '.loadMore .loadMore.el-select-dropdown .el-select-dropdown__wrap',
      )
      // 这里不能使用箭头函数！
      // eslint-disable-next-line func-names
      if (SELECTDOWN_DOM) {
        SELECTDOWN_DOM.addEventListener('scroll', function () {
          /**
           * scrollHeight 获取元素内容高度(只读)
           * scrollTop 获取或者设置元素的偏移值,
           *  常用于:计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
           * clientHeight 读取元素的可见高度(只读)
           * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
           * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
           */
          const CONDITION = this.scrollHeight - this.scrollTop - 1 <= this.clientHeight
          if (CONDITION) {
            binding.value()
          }
        })
      }
    },
  })
}
