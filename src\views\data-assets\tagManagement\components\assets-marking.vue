<template>
  <div class="assets-marking-box" v-loading="state.loading">
    <div class="add-button-box" v-if="!state.queryData?.type">
      <n-button size="sm" color="primary" variant="solid" @click.prevent="save">打标</n-button>
    </div>
    <div class="assets-message-box">
      <n-form
        ref="ruleForm"
        :data="state.ruleForm"
        :rules="state.rules"
        label-width="90px"
        label-align="end"
        class="demo-ruleForm disable-hide-border disabled-form"
      >
        <div class="title">资产信息</div>
        <n-form-item label="中文名称：">
          <n-input v-model="state.ruleForm.cnName" placeholder=" " noborder />
        </n-form-item>
        <n-form-item label="英文名称：">
          <n-input v-model="state.ruleForm.name" placeholder=" " noborder />
        </n-form-item>
        <n-form-item label="所属业务域：">
          <n-input v-model="state.ruleForm.bizName" placeholder=" " noborder />
        </n-form-item>
        <n-form-item label="描述信息：">
          <div class="description">{{ state.ruleForm.description }}</div>
        </n-form-item>
        <n-form-item label="创建时间：">
          <n-input v-model="state.ruleForm.createTime" placeholder=" " noborder />
        </n-form-item>
        <n-form-item label="创建人：">
          <n-input v-model="state.ruleForm.createByName" placeholder=" " noborder />
        </n-form-item>
      </n-form>
    </div>
    <div class="marking-message-box">
      <div class="title">打标信息</div>
      <div class="tag-infos-box" v-if="state.ruleForm.tagInfos?.length">
        <div
          class="tag-item"
          v-for="item in state.ruleForm.tagInfos"
          :key="item.id"
          :title="item.name"
          >{{ item.name }}
          <SvgIcon
            v-if="!state.queryData?.type"
            icon="mark-del"
            class="reduce"
            title="删除"
            @click="delMarking(item)"
          />
        </div>
      </div>
      <div v-else class="tag-infos-box">无打标信息</div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  export default {
    name: '',
    components: {},
    props: {},
    setup(props, { emit }) {
      // const getAssetsImages = (name) => {
      //   return new URL(`/src/assets/img/management/${name}.png`, import.meta.url).href //本地文件路径
      // }
      //按钮权限
      const { proxy } = getCurrentInstance()
      const router = useRouter()
      const state = reactive({
        loading: false,
        activeName: 'ASSETS_MARKING',
        ruleForm: {
          cnName: '',
          name: '',
          bizName: '',
          description: '',
          createTime: '',
          createByName: '',
          tagInfos: [],
        },
      })

      const methods = {
        //删除某个标签
        delMarking(data) {
          proxy.$MessageBoxService.open({
            title: '是否确认删除该标签',
            content: '删除后该标签将不可使用',
            save: () => {
              let _tagInfos = state.ruleForm.tagInfos.filter((item) => {
                return item.id !== data.id
              })

              emit('delSingerMarking', { tagInfos: _tagInfos })
            },
          })
        },
        //资产打标
        save() {
          emit('showTagDialog', { tagInfos: state.ruleForm.tagInfos })
        },
        tabChange(name) {
          state.activeName = name
        },
        //查询资产信息
        getModeRecords() {
          state.loading = true
          api.assets
            .getModeRecords({
              condition: {
                modelId: state.queryData.modelId,
              },
            })
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data.modelDTO[key]
                })
                state.ruleForm.bizName = state.queryData.bizName
                state.ruleForm.tagInfos = data.tagInfos
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        //初始或者保存后初始化
        init() {
          methods.getModeRecords()
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        methods.getModeRecords()
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .assets-marking-box {
    height: 100%;
    .add-button-box {
      margin-top: 20px;
    }

    .assets-message-box {
      margin-top: 10px;
      .title {
        // width: 56px;
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bolder;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin: 20px 0;
      }
      :deep(.nancalui-form) {
        .description {
          color: #333333;
          font-size: 12px;
          margin-left: 11px;
          height: 22px;
          line-height: 22px;
        }
      }
    }
    .marking-message-box {
      .title {
        // width: 56px;
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bolder;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin: 20px 0;
      }
      .tag-infos-box {
        display: flex;
        align-items: center;
        font-size: 12px;

        max-height: 200px;

        .tag-item {
          flex-shrink: 0; //不压缩
          // width: 68px;
          height: 28px;
          background: #f2f3f6;
          border-radius: 14px;

          line-height: 28px;
          text-align: center;
          margin: 0 10px 10px 0;
          cursor: default;
          font-size: 12px;
          color: #333;
          padding: 0 10px;
          position: relative;
          &:hover {
            .yy-icon {
              display: block;
              cursor: pointer;
            }
          }
          .yy-icon {
            width: 18px;
            height: 18px;
            position: absolute;
            top: -8px;
            right: -8px;
            display: none;
          }
          // &:nth-of-type(3) {
          //   margin-right: 50px;
          // }
        }
        .more-box {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          padding: 0 10px;
          // background-color: #fff;
        }
        .more-icon {
          color: #333;

          width: 22px;
          height: 18px;
          cursor: pointer;
          &:hover {
            color: #4d4e4eff;
            background-color: #eff1f5ff;
          }
        }
      }
    }
  }
</style>
