<template>
  <div :class="['container ', 'scroll-bar-style']">
    <div class="content-box">
      <div class="row">
        <div class="col">
          统计时间：
          <n-date-picker-pro
            v-model="state.chooseDate"
            placeholder="选择时间"
            format="YYYY-MM-DD"
            allow-clear
            @confirmEvent="confirmEventFn"
          />
        </div>

        <div class="search">
          <div class="btn active" @click.prevent="initData(false)">查询</div>
          <div class="btn" @click.prevent="resetFn">重置</div>
        </div>
      </div>
      <div class="content-box-row" v-loading="state.loading">
        <div class="col">
          <div class="col-content">
            <div class="col-content-row">
              <div class="col-content-row-item">
                <div class="col-content-row-item-title" unit-text=""> 数据表 </div>
                <div class="col-content-row-item-content">
                  {{ state.statistic.tableTotal || '--' }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title"> 数据总量</div>
                <div class="col-content-row-item-content">
                  {{ state.statistic.tableStorage || '--' }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title"> 数据增量</div>
                <div class="col-content-row-item-content">
                  {{ state.statistic.tableStorageIncrement || '--' }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title"> 数据质量</div>
                <div class="col-content-row-item-content">
                  {{ state.statistic.qualityTotal || '--' }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title"> 数据服务量</div>
                <div class="col-content-row-item-content">
                  {{ state.statistic.dataServiceTable || '--' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-box-row">
        <div class="col">
          <div class="title">数据量变化趋势</div>
          <div class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="taskCollectionTrend"
            ></div>
          </div>
        </div>
      </div>
      <div class="content-box-row">
        <div class="col">
          <div class="title">数据质量变化趋势</div>
          <div v-loading="state.historyLoading" class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="resourceUtilization"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, onBeforeUnmount } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { formartTimeDate } from '@/utils/index'
  import * as echarts from 'echarts'
  import { timestampToTime } from '@/const/public.js'
  export default {
    title: 'List',
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const state = reactive({
        key: 1,
        loading: false,
        statistic: {},
        chooseDate: null,
        startTime: null,
        endTime: new Date().getTime(),
        historyLoading: false,
      })
      let myChart = null
      let historyChart = null
      const methods = {
        confirmEventFn(e) {
          if (e) {
            state.endTime = new Date(e).getTime()
            state.startTime = state.endTime - 15 * 24 * 60 * 60 * 1000
          } else {
            state.startTime = null
            state.endTime = null
          }
        },
        resetFn() {
          state.chooseDate = new Date()
          state.endTime = new Date().getTime()
          state.startTime = state.endTime - 15 * 24 * 60 * 60 * 1000
          methods.initData(false)
        },
        totalCount() {
          // 获取可用数据源
          let chartDom = document.getElementById('taskCollectionTrend')
          myChart = echarts.init(chartDom)
          myChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })
          api.assets
            .metadataStatistics({
              searchData: formartTimeDate(state.endTime, '-'),
            })
            .then((res) => {
              myChart?.hideLoading()
              let { success, data } = res
              if (success) {
                state.statistic = data
                methods.initEcharts(data.dataChangeTrend || [])
                methods.getTrendDataFn()
              }
            })
            .catch(() => {
              myChart?.hideLoading()
            })
        },
        initEcharts(arr) {
          if (!myChart) {
            let chartDom = document.getElementById('taskCollectionTrend')
            myChart = echarts.init(chartDom)
          }

          let xAxisData = [],
            yAxisSuccessData = [],
            yAxisFailData = []
          // 获取x轴数据
          xAxisData = arr.map((item) => {
            item.day = item.statisticsData.slice(5, 10).replace(/-/g, '.')
            return item.day
          })
          // 获取y轴成功数据
          yAxisSuccessData = arr.map((item) => (item.tableTotal ? parseFloat(item.tableTotal) : 0))
          // 获取y轴失败数据
          yAxisFailData = arr.map((item) =>
            item.tableStorageIncrement ? parseFloat(item.tableStorageIncrement) : 0,
          )

          let option
          option = {
            title: {
              text: '',
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                label: {
                  backgroundColor: '#6a7985',
                },
              },
              formatter: function (params) {
                let showHtm = ''
                for (let i = 0; i < params.length; i++) {
                  //日期
                  let time = params[i].name
                  //名称
                  let name = params[i].seriesName
                  //值
                  let value = params[i].value
                  let color = params[i].color
                  if (i == 0) {
                    showHtm += `
                  <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                  `
                  }
                  showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                    <span style='font-weight:400'>${name}</span>
                  </div>
                  <span style='font-weight:500;'>${value}</span>
                </div>
                `
                }
                return showHtm
              },
              textStyle: {
                fontSize: 12,
                color: '#333',
              },
            },
            color: ['#13bfff', '#4cb15d'], // 设置tooltips展示图标颜色
            legend: {
              bottom: 0,
            },
            grid: {
              left: 20,
              right: 50,
              bottom: 40,
              top: 30,
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: xAxisData,
              },
            ],
            yAxis: [
              {
                name: '数据量',
                nameTextStyle: { align: 'left' },
                type: 'value',
              },
            ],
            series: [
              {
                name: '数据总量',
                type: 'line',
                lineStyle: {
                  color: '#13bfff',
                },
                showSymbol: false,
                smooth: true,
                data: yAxisSuccessData,
              },
              {
                name: '数据增量',
                type: 'line',
                showSymbol: false,
                smooth: true,
                data: yAxisFailData,
                lineStyle: {
                  color: '#4cb15d',
                },
              },
            ],
          }
          document.getElementById('taskCollectionTrend').setAttribute('_echarts_instance_', '')

          option && myChart.setOption(option)
        },

        initData(flag = false) {
          if (flag) {
            window.onresize = () => {
              methods.echartsResize()
            }
          }
          methods.totalCount()
        },
        // 屏幕onresize echarts重新绘制
        echartsResize() {
          //echarts重新绘制
          if (myChart) {
            myChart.resize()
          }
          if (historyChart) {
            historyChart.resize()
          }
        },
        // 获取历史评分趋势
        getTrendDataFn() {
          state.historyLoading = true
          api.dataQuality
            .qualityReportHistoryScope({
              tableId: null,
              ratingSystem: 'PRECENTAGE_SCORE',
              startTime: formartTimeDate(state.startTime, '-', false),
              endTime: formartTimeDate(state.endTime, '-', false),
            })
            .then((res) => {
              state.historyLoading = false
              let { success, data } = res
              if (success) {
                let xData = []
                let yData = []
                data.forEach((val) => {
                  xData.push(val.date.slice(5, 10))
                  yData.push(val.score || 0)
                })
                if (data.length > 0) {
                  state.statistic.qualityTotal = data[data.length - 1].score
                }
                methods.trendFn(xData, yData)
              }
            })
        },
        // 质量评分趋势
        trendFn(xData, yData) {
          let chartDom = document.getElementById('resourceUtilization')
          historyChart = echarts.init(chartDom)
          let option = {
            color: '#f63838',
            legend: {
              bottom: 0,
            },
            grid: {
              left: 20,
              right: 50,
              bottom: 40,
              top: 30,
              containLabel: true,
            },
            tooltip: {
              trigger: 'axis',
              backgroundColor: '#ffffff',
              borderColor: '#ffffff',
              className: 'quality-tooltip',
              padding: 0,
              formatter: function (value) {
                let item = value[0]
                return `<div class="quality-tooltip-box">
<div class="quality-tooltip-box-title">质量平均分</div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">日期</div><div class="num">${item.name}</div></div>
<div class="quality-tooltip-box-item"><div class="circle" style="background-color: ${item.color}"></div><div class="text">评分</div><div class="num">${item.value}分</div></div>
</div>`
              },
            },
            xAxis: {
              type: 'category',
              data: xData,
              boundaryGap: false,
            },
            yAxis: {
              type: 'value',
              name: '质量平均分',
            },
            series: [
              {
                name: '质量评分',
                data: yData,
                type: 'line',
                smooth: true,
                showSymbol: false,
              },
            ],
          }
          historyChart.setOption(option)
        },
      }

      onBeforeUnmount(() => {
        window.onresize = null

        if (myChart) {
          myChart.dispose() //销毁
        }
        if (historyChart) {
          historyChart.dispose() //销毁
        }
      })

      onMounted(() => {
        const { projectCode } = toRefs(store.state.user.currentProject)
        state.chooseDate = new Date()
        state.endTime = new Date().getTime()
        state.startTime = state.endTime - 15 * 24 * 60 * 60 * 1000
        methods.initData(true)
      })

      return {
        state,
        buttonAuthList,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;

  .container {
    width: 100%;
    height: calc(100vh - 50px);
    padding: 16px;
    overflow-y: auto;
    font-family: PingFangSC-Semibold, PingFang SC;
    background-color: #eee;

    .content-box {
      display: flex;
      flex-direction: column;
      gap: 16px;

      &-row {
        .col {
          box-sizing: border-box;
          overflow: hidden;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);

          .title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            height: 52px;
            padding: 0 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            border-bottom: 1px solid #c5d0ea;

            .illustrate {
              margin-left: 8px;
              color: #8091b7;

              &:hover {
                color: $themeBlue;
              }
            }

            .sub-title {
              color: var(---, rgba(0, 0, 0, 0.55));

              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              line-height: 22px;
            }

            &:before {
              position: absolute;
              top: 17px;
              left: 0;
              width: 4px;
              height: 18px;
              background: #1e89ff;
              content: '';
            }

            span {
              margin-left: 8px;
              color: rgba(0, 0, 0, 0.55);
              font-weight: normal;
              font-size: 14px;
            }
          }

          .col-content {
            padding: 16px;

            .canvas {
              display: flex;
              flex-direction: column;
              gap: 16px;
              align-items: center;
              height: calc(50vh - 262px);
            }

            .ranking {
              display: block;
              padding: 8px 24px;

              &-update-time {
                color: var(---, rgba(0, 0, 0, 0.55));

                font-weight: 400;
                font-size: 12px;
                font-family: 'Source Han Sans CN';
                font-style: normal;
                line-height: 20px;
              }

              &-list {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                height: calc(100% - 20px);

                &-item {
                  display: grid;
                  grid-template-columns: 20px 77px 1fr 34px;
                  align-items: center;
                  color: var(----, rgba(0, 0, 0, 0.75));
                  font-weight: 400;
                  font-size: 12px;
                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 20px;

                  &-index {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 16px;
                    margin-right: 4px;
                    color: var(--100, #fff);
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Source Han Sans CN';
                    font-style: normal;
                    line-height: 20px;
                    text-align: right;
                    border-radius: 13px;
                  }

                  &-name {
                    padding: 0 8px 0 4px;
                    overflow: hidden;
                    color: var(--100, rgba(0, 0, 0, 0.75));
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }

                  &-content {
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    border-radius: 4px;
                    // 动画
                    transition: all 0.3s ease-in-out;
                  }

                  &-count {
                    color: var(----, rgba(0, 0, 0, 0.75));
                    font-weight: 400;
                    font-size: 12px;
                    font-family: 'PingFang SC';
                    font-style: normal;
                    line-height: 16px;
                    text-align: center;
                    /* 133.333% */
                  }

                  &:nth-child(1) {
                    color: #f63838;

                    .ranking-list-item-index {
                      background: #f63838;
                    }
                  }

                  &:nth-child(2) {
                    color: #ff7d00;

                    .ranking-list-item-index {
                      background: #ff7d00;
                    }
                  }

                  &:nth-child(3) {
                    color: #04c495;

                    .ranking-list-item-index {
                      background: #04c495;
                    }
                  }

                  &:nth-child(4) {
                    color: #447dfd;

                    .ranking-list-item-index {
                      background: #447dfd;
                    }
                  }
                }
              }
            }

            &-row {
              display: flex;
              flex-wrap: wrap;

              &-item {
                position: relative;
                display: flex;
                flex: 1 0 0;
                flex-direction: column;
                height: 96px;
                padding: 16px 24px;
                border-radius: 6px;
                cursor: pointer;

                &::before {
                  position: absolute;
                  right: 16px;
                  bottom: 12px;
                  display: block;
                  width: 32px;
                  height: 32px;
                  margin: auto;
                }

                &:nth-child(1) {
                  background: var(---, #1e89ff);

                  &::before {
                    content: url('data:image/svg+xml;base64,CiAgICAgICAgICA8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiB2aWV3Qm94PSIwIDAgMzIgMzIiIGZpbGw9Im5vbmUiPgogIDxwYXRoIGQ9Ik0xNC45MjQ2IDQuMjQ0MTRIMTQuODc3N0M3Ljc3MTQ5IDQuMjQ0MTQgMi4wMDU4NiAxMC4wMDk4IDIuMDA1ODYgMTcuMTE2QzIuMDA1ODYgMjQuMjIyMyA3Ljc3MTQ5IDI5Ljk4NzkgMTQuODc3NyAyOS45ODc5QzIxLjk2ODQgMjkuOTg3OSAyNy43MzQgMjQuMjUzNSAyNy43NDk2IDE3LjE2MjlIMTQuOTI0NlY0LjI0NDE0WiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xNy4xNzc3IDIuMDA1ODZIMTcuMTMwOVYxNC44NDY1SDMwLjA0OTZDMzAuMDM0IDcuNzQwMjQgMjQuMjY4NCAyLjAwNTg2IDE3LjE3NzcgMi4wMDU4NloiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMjgiLz4KPC9zdmc+');
                  }
                }

                &:nth-child(2) {
                  background: var(---, #13bfff);

                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNSIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xNiA5VjE1LjgxOUMxNiAxNS45MzU5IDE2LjA0MSAxNi4wNDkyIDE2LjExNTkgMTYuMTM5MUwyMSAyMiIgc3Ryb2tlPSIjMTNCRkZGIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4=');
                  }
                }

                &:nth-child(3) {
                  background: var(---, #4cb15d);

                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNSIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xMCAxNkwxNCAyMEwyMyAxMyIgc3Ryb2tlPSIjNENCMTVEIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=');
                  }
                }

                &:nth-child(4) {
                  background: var(---, #f63838);

                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNCIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xMS4wNDk4IDExLjA0OThMMjAuOTQ5MyAyMC45NDkzIiBzdHJva2U9IiNGNjM4MzgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPHBhdGggZD0iTTIwLjk1MDIgMTEuMDQ5OEwxMS4wNTA3IDIwLjk0OTMiIHN0cm9rZT0iI0Y2MzgzOCIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+');
                  }
                }
                &:nth-child(5) {
                  background: var(---, #13bfff);

                  &::before {
                    content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNSIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xNiA5VjE1LjgxOUMxNiAxNS45MzU5IDE2LjA0MSAxNi4wNDkyIDE2LjExNTkgMTYuMTM5MUwyMSAyMiIgc3Ryb2tlPSIjMTNCRkZGIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4=');
                  }
                }

                &-title {
                  color: var(--100, #fff);

                  font-weight: 400;
                  font-size: 16px;

                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 24px;

                  /* 150% */
                  &[unit-text]::after {
                    color: var(--100, #fff);
                    font-weight: 400;
                    font-size: 14px;
                    font-family: 'Source Han Sans CN';
                    font-style: normal;
                    line-height: 22px;
                    content: attr(unit-text);
                  }
                }

                &-content {
                  color: var(--100, #fff);
                  font-weight: 700;
                  font-size: 32px;
                  font-family: 'DIN Alternate';
                  font-style: normal;
                  line-height: 40px;
                  font-feature-settings: 'liga' off, 'clig' off;
                  /* 125% */
                }

                &-content-trend {
                  display: flex;
                  gap: 8px;
                  align-items: center;
                  align-self: stretch;
                  color: var(--100, #fff);
                  font-weight: 400;
                  font-size: 12px;
                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 20px;

                  &-item {
                    display: flex;
                    gap: 2px;
                    align-items: center;

                    &-content-icon {
                      vertical-align: text-bottom;
                    }
                  }

                  .yy-icon {
                    width: 14px;
                    height: 14px;
                  }
                }

                .illustrate {
                  position: absolute;
                  top: 20px;
                  right: 16px;
                  margin-left: 8px;
                  color: #8091b7;

                  &:hover {
                    color: $themeBlue;
                  }
                }
              }

              .col-content-row-item + .col-content-row-item {
                margin-left: 16px;
              }
            }
          }
        }
      }
    }

    .col-2 {
      display: grid;
      grid-template-columns: 66fr 34fr;
      gap: 16px;

      .col > .col-content {
        padding: 24px;
      }
    }

    .col-1 {
      .col > .col-content {
        padding: 0;
      }
    }
  }

  .refresh {
    margin-left: auto;
  }

  .icons {
    position: relative;

    & + .icons {
      margin-left: 8px;
    }

    .yy-icon {
      position: relative;
      z-index: 2;
      color: #8091b7;
      cursor: pointer;

      &:hover {
        color: $themeBlue;
      }
    }

    &:hover {
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #e3ecff;
        border-radius: 5px;
        transform: translate(-50%, -50%);
        content: '';
      }
    }
  }
</style>

<style lang="scss">
  @import '@/styles/variables.scss';

  .quality-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;

      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: center;
      }

      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        .circle {
          width: 8px;
          height: 8px;
          background-color: #f63838;
          border-radius: 50%;
        }

        .text {
          width: 72px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }

        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }

  .task-collection-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;

      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }

      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }

        .text {
          width: 32px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }

        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  .row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
    padding: 10px 8px 10px 16px;
    background: var(--100, #fff);
    border-radius: 2px;

    .switch-label {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 14px;
      color: #1e89ff;
      font-size: 14px;
      cursor: pointer;

      .icon-switch {
        margin-right: 4px;
        font-size: 16px;
      }

      &:hover {
        color: #479dff;
      }
    }

    .search {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
    }

    .btn {
      display: flex;
      gap: 4px;
      align-items: center;
      box-sizing: border-box;
      padding: 4px 16px;
      color: #1e89ff;
      font-size: 14px;
      border: 1px solid var(---, #1e89ff);
      border-radius: 2px;

      .icon {
        font-size: 16px;
      }

      &.active,
      &:hover {
        color: #fff;
        background: #1e89ff;
      }
    }
  }
</style>
