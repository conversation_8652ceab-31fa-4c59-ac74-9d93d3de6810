<template>
  <section class="container" v-loading="state.loading" element-loading-text="查询中...">
    <section class="query">
      <div class="title">图查询</div>
      <div class="search">
        <div class="select">
          <span class="name">数据源：</span>
          <n-select v-model="state.datasourceId">
            <n-option
              v-for="item in state.datasourceOpts"
              :key="item.id"
              :value="item.id"
              :name="item.name"
            />
          </n-select>
        </div>
        <div class="btn">
          <n-button variant="solid" @click="pictureQueryFn">查询</n-button>
          <n-button @click="clearFn">清空</n-button>
        </div>
      </div>
      <n-textarea v-model="state.sql" :rows="5" placeholder="请输入查询语句" />
      <p class="hint">请输入查询语句，如查询整个图数据:g.V()</p>
    </section>

    <section class="pirture">
      <div class="tabs">
        <div
          :class="{ btn: true, active: state.page === 'picture' }"
          @click="state.page = 'picture'"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
          >
            <g clip-path="url(#clip0_1720_273920)">
              <path
                d="M8.25 1.01036C8.7141 0.742414 9.2859 0.742414 9.75 1.01036L15.5442 4.35566C16.0083 4.62361 16.2942 5.1188 16.2942 5.6547V12.3453C16.2942 12.8812 16.0083 13.3764 15.5442 13.6443L9.75 16.9896C9.2859 17.2576 8.7141 17.2576 8.25 16.9896L2.45577 13.6443C1.99167 13.3764 1.70577 12.8812 1.70577 12.3453V5.6547C1.70577 5.1188 1.99167 4.62361 2.45577 4.35566L8.25 1.01036Z"
                stroke="currentColor"
              />
              <path d="M8.37891 5.87695L5.62573 10.6879" stroke="currentColor" stroke-width="0.8" />
              <path d="M9.62109 5.87695L12.3743 10.6879" stroke="currentColor" stroke-width="0.8" />
              <path d="M11.8125 11.8125L6.1875 11.8125" stroke="currentColor" stroke-width="0.8" />
              <circle cx="9" cy="4.5" r="1.1875" stroke="currentColor" />
              <circle cx="5.0625" cy="11.8125" r="1.1875" stroke="currentColor" />
              <circle cx="12.9375" cy="11.8125" r="1.1875" stroke="currentColor" />
              <circle cx="13.5" cy="6.75" r="1.125" fill="currentColor" />
              <circle cx="4.5" cy="6.75" r="1.125" fill="currentColor" />
              <circle cx="9" cy="14.0625" r="1.125" fill="currentColor" />
              <path
                d="M4.5 6.75L9 9M9 9L13.5 6.75M9 9V13.5"
                stroke="currentColor"
                stroke-width="0.8"
              />
            </g>
            <defs>
              <clipPath id="clip0_1720_273920">
                <rect width="18" height="18" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <div>图</div>
        </div>
        <div :class="{ btn: true, active: state.page === 'table' }" @click="state.page = 'table'">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
          >
            <rect
              x="2.25"
              y="1.125"
              width="13.5"
              height="15.75"
              rx="2"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.625 9H12.375"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.625 12.375H10.125"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.625 5.625H12.375"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <div>表格</div>
        </div>
        <div :class="{ btn: true, active: state.page === 'json' }" @click="state.page = 'json'">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
          >
            <rect
              x="2.25"
              y="1.125"
              width="13.5"
              height="15.75"
              rx="2"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10.125 6.75L12.375 9L10.125 11.25"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M7.875 6.75L5.625 9L7.875 11.25"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <div>Json</div>
        </div>
      </div>
      <div class="content" ref="tableRef">
        <pictureView :dataView="state.graphView" v-if="state.page === 'picture'" />
        <tableView :dataView="state.tableView" v-else-if="state.page === 'table'" />
        <jsonView :dataView="state.jsonView.data" v-else />
      </div>
    </section>
  </section>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted, computed } from 'vue'
  import { pictureDatasoure, pictureQuery } from '@/api/pictureQuery'

  import pictureView from './components/pictureView.vue'
  import tableView from './components/tableView.vue'
  import jsonView from './components/jsonView.vue'

  const tableRef = ref()

  /**
   * 数据部分
   */
  const state = reactive({
    loading: false,
    page: 'table',
    datasourceId: '',
    datasourceOpts: [],
    sql: '',
    graphView: {},
    tableView: {},
    jsonView: {},
  })

  onMounted(() => {
    datasoureFn()
  })

  // 数据源查询
  const datasoureFn = async () => {
    const params = {
      status: 'PUBLISHED',
      datasourceType: 'GRAPH',
    }
    const res = await pictureDatasoure(params)
    if (res.success && res.data) {
      state.datasourceOpts = res.data
    }
  }

  // 清空sql
  const clearFn = () => {
    state.sql = ''
    state.graphView = {}
    state.tableView = {}
    state.jsonView = {}
  }

  // 图查询
  const pictureQueryFn = async () => {
    state.loading = true
    const { datasourceId, sql } = state
    const params = {
      datasourceId,
      sql,
    }
    if (datasourceId && sql) {
      const res = await pictureQuery(params)
      if (res.success && res.data) {
        state.loading = false
        if (!res.data.success) {
          ElMessage.error(res.data.msg)
          return false
        }
        state.graphView = res.data.gremlinResult.graph_view
        state.tableView = res.data.gremlinResult.table_view
        state.jsonView = res.data.gremlinResult.json_view
        state.page = state.page === '' ? 'picture' : state.page
      }
    } else {
      state.loading = false
      ElMessage.error('请选择数据源和输入查询语句')
    }
  }

  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">
  .container {
    .query {
      background: #fff;
      border-radius: 2px;

      .title {
        display: flex;
        padding: 7px 8px 7px 0px;
        align-items: center;
        align-self: stretch;
        color: var(----, rgba(0, 0, 0, 0.9));

        /* 常用/r500/h8 */
        font-family: 'Source Han Sans CN';
        font-size: 16px;
        font-style: normal;
        font-weight: bolder;
        line-height: 24px; /* 150% */

        &::before {
          content: '';

          display: inline-block;
          width: 4px;
          height: 18px;
          background: var(---, #1e89ff);
          margin-right: 12px;
          vertical-align: middle;
        }
      }

      .search {
        display: flex;
        padding: 8px 16px;
        justify-content: space-between;
        align-items: flex-start;
        align-self: stretch;

        .nancalui-select {
          display: inline-block;
          width: 240px;
        }
        .name {
          color: var(----, #1d2129);
          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }

      .nancalui-textarea__div {
        width: auto !important;
        margin: 0 16px;
      }
      .hint {
        overflow: hidden;
        color: #a8abb2;
        text-overflow: ellipsis;

        /* 常用/r400/h9 */
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        padding-left: 16px;
        padding-bottom: 16px;
      }
    }

    .pirture {
      display: flex;
      height: calc(100% - 275px);
      margin-top: 10px;
      background: #fff;

      .tabs {
        display: flex;
        width: 48px;
        height: 100%;
        padding: 16px;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        align-self: stretch;
        border-right: 1px solid var(---, #dcdfe6);
        text-align: center;

        .btn {
          color: var(----, rgba(0, 0, 0, 0.9));

          /* 常用/r400/h9 */
          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */

          &.active {
            color: #1e89ff;
            box-shadow: none;
          }
        }
      }

      .content {
        width: calc(100% - 48px);
        padding: 16px;
        overflow: auto;
      }
    }
  }
</style>
