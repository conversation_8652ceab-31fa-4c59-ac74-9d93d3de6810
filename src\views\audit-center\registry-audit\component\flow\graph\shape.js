import { Graph } from '@antv/x6'
import '@antv/x6-vue-shape'
import Box from './box'

Graph.registerNode('audit-box', {
  inherit: 'vue-shape',
  component: {
    template: `
          <Box/>`,
    components: {
      Box,
    },
  },
  ports: {
    groups: {
      top: {
        id: 'top',
        position: 'top',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#697A9A',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
      bottom: {
        id: 'bottom',
        position: 'bottom',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#697A9A',
            strokeWidth: 1,
            fill: '#fff',
            style: {
              visibility: 'hidden',
            },
          },
        },
      },
    },
    items: [
      {
        id: 'top',
        group: 'top',
      },
      {
        id: 'bottom',
        group: 'bottom',
      },
    ],
  },
})
