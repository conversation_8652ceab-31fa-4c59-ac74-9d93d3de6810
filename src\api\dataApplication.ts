import request from '@/utils/request'
import URL from '@/const/urlAddress'
// import { isDev } from '@/utils'
/* 2d接口 */
// iotdb
export const searchIot = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/iot`,
    method: 'post',
    data,
    timeout: 100 * 1000,
  })
// 保存布局
export const save2D = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional`,
    method: 'post',
    data,
  })
// 获取布局
export const get2D = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/${data.applicationId}`,
    method: 'get',
  })

// 新增
export const createTask = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/task`,
    method: 'post',
    data,
  })
// 修改
export const updateTask = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/task/edit`,
    method: 'post',
    data,
  })
// 查询
export const searchTask = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/task/search`,
    method: 'post',
    data,
  })

// 查询
export const searchData = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/${data.graphicId}`,
    method: 'get',
  })

// 保存数据项
export const graphicConfigure = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/graphic/configure`,
    method: 'post',
    data,
  })
// 查询数据项
export const getGraphicConfigure = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/graphic/configure/${data.graphicId}`,
    method: 'get',
  })
// 删除
export const deleteTask = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/task/delete/${data.id}`,
    method: 'post',
    data,
  })
// 下载快照
export const download2dSnapshot = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/snapshot/${data.applicationId}`,
    method: 'post',
    data,
    responseType: 'blob',
  })
// 上传快照
export const upload2dSnapshot = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/snapshot`,
    method: 'post',
    data,
  })
// 生成链接
export const getPythonLog = (data: { applicationId: number }) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/${data.applicationId}/links`,
    method: 'get',
  })

//  获取python的输入字段
export const getPythonInput = (data: { time: number }) =>
  request({
    url: `${URL.DIMENSIONAL}/dataApplication/pythonLog/get/${data.time}`,
    method: 'get',
  })
/* 3d接口 */
// export const getPlaybackModelListUrl = (modelsId: number[], startTime, endTime) => {
//   return `ws://${
//     isDev ? '*************:10038' : window.location.host
//   }/3d/${modelsId.join()}/${startTime}/${endTime}/167859d7791ff29f24e9cf7a4124f015`
// }
// 获取图表的所有参数名
export const searchDetail = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/params/${data.graphicId}`,
    method: 'get',
  })

// 数据明细下载
export const downloadDetail = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/detail/download`,
    method: 'post',
    responseType: 'blob',
    data,
  })

// 获取图表明细
export const dataSearchDetail = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/two/dimensional/data/search/detail`,
    method: 'post',
    data,
  })

export const getPlaybackModelList = (data) =>
  request({
    url: `${URL.MODEL}/model/getPlaybackModelList`,
    method: 'post',
    data,
  })

// 恒温炉

export const curingOven = () =>
  request({
    url: `${URL.DIMENSIONAL}/cruing/oven/curing/oven`,
    method: 'get',
  })
// 获取数据模型详情
export const modelGet = (data) =>
  request({
    url: `${URL.MODEL}/model/get/twod?modelId=${data.id}`,
    method: 'get',
    data,
  })

// 查询数据模型列表数据
export const searchModelList = (data) =>
  request({
    url: `${URL.MODEL}/model/search`,
    method: 'get',
    params: data,
  })
// 获取分组扁平化列表
export const queryDataModelTreeList = () =>
  request({
    url: `${URL.MODEL}/layer/list`,
    method: 'get',
  })

// 获取图表明细
export const getQualityTraceMap = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/quality/trace/map`,
    method: 'post',
    data,
  })

// 搜索图
export const searchQualityTraceMap = (data) =>
  request({
    url: `${URL.DIMENSIONAL}/quality/trace/map/left/right`,
    method: 'post',
    data,
  })
