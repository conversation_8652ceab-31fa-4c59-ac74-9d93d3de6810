<template>
  <div class="container">
    <div class="cf-tools">
      <div class="row">
        <div class="col text-label">
          规则名称：
          <n-input v-model="state.formInline.ruleName" placeholder="请输入" />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </div>
    <div class="white-box nc-m-t-10">
      <div class="row nc-p-8">
        <n-button @click="onCreate" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          新建规则</n-button
        >
      </div>
      <div class="table" v-loading="state.isLoad">
        <CfTable
          @sort-change="sortFn"
          actionWidth="120"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              onSearch(false)
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              onSearch()
            },
          }"
        >
          <template #editor="{ data: { row } }">
            <div class="edit-box">
              <n-button variant="text" color="primary" @click="onEditManage(row)">编辑</n-button>
              <n-button variant="text" color="primary" @click="onDelManage(row)">删除</n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
    <addDeawer v-model="state.isShow" :id="state.editId" @reload="onSearch" />
  </div>
</template>
<script setup>
  import addDeawer from './add.vue'
  import { reactive, onMounted, getCurrentInstance } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  const { proxy } = getCurrentInstance()
  const state = reactive({
    tableHeadTitles: [
      { prop: 'ruleName', name: '规则名称' },
      { prop: 'preview', name: '规则预览', width: '600' },
      { prop: 'createByName', name: '创建人' },
      {
        prop: 'createTime',
        name: '创建时间',
        sortable: 'custom',
      },
    ],
    formInline: {},
    searchData: { ruleName: '' },
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    tableData: [],
    isShow: false,
    editId: '',
    isLoad: false,
  })
  // 删除
  async function onDelManage(row) {
    const res = await api.documentManage.ruleDeleteCheck({ id: row.id })
    if (res.success) {
      proxy.$MessageBoxService.open({
        title: '是否确认该条规则',
        content: '删除后将不可恢复',
        save: () => {
          api.documentManage.ruleDelete({ id: row.id }).then((res) => {
            if (res.success) {
              onSearch()
              ElNotification({
                title: '提示',
                message: '操作成功！',
                type: 'success',
              })
            }
          })
        },
      })
    }
  }
  // 新建
  function onCreate() {
    state.isShow = true
    state.editId = null
  }
  // 编辑
  function onEditManage(row) {
    state.isShow = true
    state.editId = row.id
  }
  // 重置
  function resetFn() {
    state.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    state.formInline = { ruleName: '' }
    state.searchData = { ruleName: '' }
    onSearch()
  }
  // 排序
  function sortFn(data) {
    const { order, prop } = data
    const sortConditions = [
      {
        fieldName: prop,
        sort: order === 'descending' ? 'DESC' : 'ASC',
      },
    ]
    onSearch(false, sortConditions)
  }
  function startSearch() {
    state.searchData.ruleName = state.formInline.ruleName
    onSearch(true)
  }
  // 查询
  function onSearch(init = true, sortConditions) {
    if (init) {
      state.pagination.currentPage = 1
    }
    state.isLoad = true
    api.documentManage
      .ruleListPage({
        pageSize: state.pagination.pageSize,
        pageNum: state.pagination.currentPage,
        condition: { ruleName: state.searchData.ruleName },
        sortConditions: sortConditions,
      })
      .then((res) => {
        if (res.success) {
          state.pagination.total = res.data.total
          state.tableData = res.data.list
          state.isLoad = false
        }
      })
      .catch(() => {
        state.tableData = []
        state.isLoad = false
      })
  }
  onMounted(() => {
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .table {
    height: calc(100% - 48px);
  }
  .white-box {
    height: calc(100% - 62px);
  }
</style>
