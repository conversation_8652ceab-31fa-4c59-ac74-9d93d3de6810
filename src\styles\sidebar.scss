#app {
  .main-container {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    padding-top: 56px;
    padding-left: $sideBarWidth;
    background-color: var(--themeContentBg);
    transition: margin-left 0.28s;

    &.checked {
      padding-left: 48px;
    }
    &.isLzos {
      padding: 0;
    }
  }

  .sidebar-container {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1001;
    box-sizing: border-box;
    width: $sideBarWidth;
    height: calc(100% - $navbarHeight + 40px);
    overflow: hidden;
    font-size: 0;
    background: #ffffff;
    transition: width 0.28s;
    box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);

    &.checked {
      width: 48px;
    }

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      width: 100%;
      height: calc(100% - 110px);
      margin: 0 auto;
      overflow-x: hidden;
      overflow-y: auto;
      &.scroll-bar-style {
        &::-webkit-scrollbar {
          width: 6px; // 横向滚动条
          height: 6px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 3px;
        }
      }
    }

  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      width: $sideBarWidth !important;
      transition: transform 0.28s;
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

