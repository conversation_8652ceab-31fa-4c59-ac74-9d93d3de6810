<template>
  <!-- 选择数据源类型-->
  <div class="all-data-source">
    <n-modal
      v-model="state.dialogVisible"
      class="allDataSourcePop"
      bodyClass="all-data-source-dialog commonDialog"
      title="新建数据源"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="false"
      :before-close="handleClose"
      width="560px"
    >
      <template #header>
        <n-modal-header>
          <span class="line"></span>
          <span>新建数据源</span>
        </n-modal-header>
      </template>
      <div class="data-source-content">
        <!-- <n-tabs v-model="state.activeName" class="tabs" @change="tabsChange">
          <n-tab title="全部" id="ALL" />
          <n-tab title="结构化数据源" id="ALL_STRUCTURE" />
          <n-tab title="非结构化数据源" id="NOT_STRUCTURE" />
        </n-tabs> -->

        <div class="data-source-content-bottom scroll-bar-style">
          <!-- 结构化数据库展示 -->

          <div class="structured">
            <div class="content-box">
              <div
                v-for="(item, index) in state.datasourceTypeList[state.activeName].data"
                :key="index"
              >
                <div class="content-title">
                  <span class="line"></span>
                  <span>{{ item.name }}</span>
                </div>
                <div
                  :class="[
                    'data-source-content-container',
                    state.expandTableStatus[index].expand ? '' : 'hide',
                  ]"
                >
                  <div
                    v-for="(child, index2) in item.children"
                    :key="index2"
                    :class="{ 'list-box': true }"
                  >
                    <div :class="{ list: true }" @click.prevent="handleActiveDataSource(child)">
                      <img :src="getAssetsImages(child.code)" alt="" />
                      <span>{{ child.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script>
  import { reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'

  export default {
    title: 'List',
    props: {},
    emits: ['cancelSelectData', 'checkSource'],
    setup(props, { emit }) {
      const router = useRouter()
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/dev/${name}.png`, import.meta.url).href //本地文件路径
      }
      const state = reactive({
        dialogVisible: false,
        activeName: 'ALL',
        datasourceTypeList: {
          ALL: [],
          ALL_STRUCTURE: {
            name: '',
          },
          NOT_STRUCTURE: {
            name: '',
          },
        },
        expandTableStatus: [
          {
            expand: true,
          },
          {
            expand: true,
          },
        ],
      })
      const methods = {
        tabsChange() {
          state.expandTableStatus.forEach((item) => {
            item.expand = true
          })
        },
        //展开收起table内容
        expandTable(index) {
          state.expandTableStatus[index].expand = !state.expandTableStatus[index].expand
        },
        // 选中新增的数据库类型
        handleActiveDataSource(data) {
          if (data.code) {
            if (data.code === 'API') {
              state.dialogVisible = false
              router.push({
                name: 'dataSourceManagementApiEdit',
                query: { databaseType: data.code },
              })
            } else {
              emit('checkSource', data.code)
            }
          }
        },
        // 获取所有数据库类型
        getAllDatasourceTree() {
          api.project.getAllDatasourceTree().then((res) => {
            let { success, data } = res
            if (success) {
              if (data && data.length) {
                let _data = {}
                data.forEach((item) => {
                  // if (item.code === 'ALL_STRUCTURE') {
                  let children = []
                  item.children?.forEach((val) => {
                    children = children.concat(val.children)
                  })
                  _data[item.code] = { ...item, children: children }
                  // }
                })
                _data['ALL'] = {
                  data: [_data['ALL_STRUCTURE'], _data['NOT_STRUCTURE']],
                  // data: [_data['ALL_STRUCTURE']],
                }
                state.datasourceTypeList = Object.assign({}, _data)
              }
            }
          })
        },
        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
          state.activeName = 'ALL'
          emit('cancelSelectData')
        },
        //显示弹框
        show() {
          state.dialogVisible = true
          methods.getAllDatasourceTree()
        },
      }
      return {
        state,
        getAssetsImages,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  :deep(.nancalui-modal) {
    font-family: PingFangSC-Medium, PingFang SC;
    border-radius: 12px;
    box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
    .nancalui-modal__header {
      display: flex;
      align-items: center;
      padding: 15px 16px 15px 0px;
      font-size: 16px;
      .line {
        display: inline-block;
        width: 4px;
        height: 18px;
        margin-right: 12px;
        background: #1e89ff;
      }
      span {
        color: rgba(0, 0, 0, 0.85);
        font-weight: bolder;
        font-size: 16px;
        line-height: 24px;
      }
    }
    .nancalui-modal__body {
      padding: 0;
      .nancalui-tabs {
        .nancalui-tabs-nav-tab {
          padding-left: 26px;
        }
      }
    }
  }
  :deep(.allDataSourcePop) {
    border-radius: 2px;
  }
  :deep(.all-data-source-dialog) {
    &.nancalui-modal__body {
      max-height: inherit;
    }
    .data-source-content {
      height: 60vh;
      min-height: 40vh;
      max-height: 60vh;
      .data-source-content-bottom {
        height: calc(100% - 46px);

        padding: 16px;
        overflow: auto;
        border-left: 0;
        border-radius: 0px 6px 6px 0px;

        .content-title {
          display: flex;
          gap: 10px;
          align-items: center;
          align-self: stretch;
          padding: 4px 0px;
          color: #2b71c2;
          font-weight: 400;
          font-size: 14px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 22px;
          background: #f2f6fc;

          .line {
            display: inline-block;
            width: 4px;
            height: 18px;
            background: #1e89ff;
          }
          svg {
            margin-right: 4px;
            font-size: 16px;
            cursor: pointer;
            &.noExpand {
              transform: rotate(180deg);
            }
          }
        }
        .structured {
          .content-box {
            margin-bottom: 50px;
          }
          .data-source-content-container {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            justify-content: flex-start;
            margin: 8px 0;

            &.hide {
              height: 0;
              overflow: hidden;
            }
            .list-box {
              box-sizing: border-box;
              width: 118px;

              .list {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
                width: 120px;
                height: 114px;
                margin: 0 auto;
                padding: 4px;
                background: #ffffff;
                border: 1px solid #ebedf0;
                border-radius: 2px;
                cursor: pointer;

                span {
                  display: block;
                  padding-top: 5px;
                  color: #333333;
                  font-weight: 400;
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                }
                &:hover {
                  background: #ffffff;
                  border: 1px solid #1e89ff;
                  box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
                  span {
                    display: block;
                    color: $themeBlue;
                  }
                }

                img {
                  display: block;
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
