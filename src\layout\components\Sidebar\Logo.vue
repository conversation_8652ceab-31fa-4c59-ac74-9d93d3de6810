<template>
  <div class="logo">
    <img class="pic" src="@img/home/<USER>" />
  </div>
</template>

<script>
  export default {
    props: {
      menuHidden: Boolean,
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    float: left;
    height: 100%;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .pic {
      display: block;
      margin-left: 10px;
      width: 182px;
      height: 40px;
    }

    .title {
      color: #fff;
      font-weight: bold;
      font-size: 18px;
      margin-left: 10px;
    }
  }
</style>
