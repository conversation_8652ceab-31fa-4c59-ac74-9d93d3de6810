<template>
  <div class="merge">
    <!-- <div class="header">
      <n-tabs v-model="state.activeName" @active-tab-change="tabsChangeFn"> -->
    <!--        <n-tab v-if="!state.isLzos" title="指标资产目录" id="targetCatalog" />-->
    <!-- <n-tab title="资产目录" id="assetsCatalog" />
      </n-tabs>
    </div> -->
    <div :class="['content', state.isLzos ? 'isLzos' : '']">
      <!-- <template v-if="state.activeName === 'targetCatalog'">
        <targetCatalog />
      </template>
      <template v-else-if="state.activeName === 'assetsCatalog'"> -->
      <assetsCatalog />
      <!-- </template> -->
    </div>
  </div>
</template>

<script setup>
  import { onBeforeMount, reactive } from 'vue'
  import targetCatalog from './targetLibrary/list'
  import assetsCatalog from './assetLibrary/list'
  const state = reactive({
    // activeName: import.meta.env.VITE_APP_LZOS ? 'assetsCatalog' : 'targetCatalog',
    activeName: 'assetsCatalog',
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
  })
  const tabsChangeFn = (e) => {
    sessionStorage.setItem('tabsCatalog', e)
  }
  onBeforeMount(() => {
    if (sessionStorage.getItem('tabsCatalog')) {
      state.activeName = sessionStorage.getItem('tabsCatalog')
    }
  })
</script>

<style lang="scss" scoped>
  .merge {
    position: relative;
    min-height: 100%;

    .header {
      box-sizing: border-box;
      height: 46px;
      padding: 0 20px;
      background-color: #fff;
      border-top: 1px solid #ebedf0;
    }

    .content {
      height: calc(100vh - 96px);

      &.isLzos {
        height: 100%;
      }
    }
  }
</style>
