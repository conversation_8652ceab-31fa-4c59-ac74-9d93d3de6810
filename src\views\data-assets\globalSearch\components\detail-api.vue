<template>
  <div class="detail-api">
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="基本信息" id="base">
        <n-form
          class="demo-ruleForm disable-hide-border disabled-form"
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="90px"
        >
          <n-form-item label="API名称：">
            <n-input
              v-model="state.ruleForm.apiName"
              maxlength="30"
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item label="参数协议：">
            <n-input
              v-model="state.ruleForm.protocol"
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item label="请求方式：">
            <n-input
              v-model="state.ruleForm.requestMethod"
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item label="请求Path：">
            <n-input v-model="state.ruleForm.showUrl" placeholder="" disabled noborder />
          </n-form-item>
          <n-form-item label="描述信息：" class="input-textarea">
            <n-textarea
              v-model="state.ruleForm.apiStory"
              :rows="3"
              :autosize="{ minRows: 3 }"
              placeholder=""
              maxlength="200"
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="Token：">
            <n-input v-model="state.ruleForm.token" placeholder="" disabled noborder />
          </n-form-item>
          <n-form-item label="创建人：">
            <n-input v-model="state.ruleForm.createByName" placeholder="" disabled noborder />
          </n-form-item>
          <n-form-item label="创建时间：">
            <n-input v-model="state.ruleForm.createTime" placeholder="" disabled noborder />
          </n-form-item>
        </n-form>
      </n-tab>
      <n-tab title="参数信息" id="params">
        <div class="params-info">
          <n-button
            :class="{ active: state.firstButtonActive }"
            link
            @click.stop.prevent="getRequestParams"
            >请求参数</n-button
          >
          <n-button
            :class="{ active: !state.firstButtonActive }"
            link
            @click.stop.prevent="getReturnParams"
            >返回参数</n-button
          >
        </div>
        <div v-show="state.paramsType === 'request'" class="params-content">
          <n-public-table
            ref="requestPublicTable"
            :table-head-titles="state.requestTableHeadTitles"
            :tableData="state.requestTableData"
            :showPagination="false"
            :tableHeight="state.tableHeight"
          >
            <!-- 参数 value -->
            <template #parameterValue="{ editor }">
              <div>{{ editor.row.value }}</div>
              <!-- <el-input
                v-model="editor.row.value"
                :class="!editor.row.value && editor.row.isRequired ? 'required-input' : ''"
                :type="editor.row.type ? editor.row.type : 'text'"
                @keydown="onKeydown($event, editor.row)"
                placeholder=""
                @focus="inputFocus($event, editor.row)"
                @input="changeMolecule(editor.row, editor.$index)"
              /> -->
            </template>
            <!-- 是否必填 -->
            <template #isRequired="{ editor }">
              <div v-if="editor.row.isRequired" class="isRequired">
                <span class="required">*</span>{{ editor.row.isRequired }}
              </div>
              <div v-else>{{ editor.row.isRequired }}</div>
            </template>
          </n-public-table>
        </div>
        <div v-show="state.paramsType === 'return'" class="params-content">
          <n-public-table
            ref="returnPublicTable"
            :table-head-titles="state.returnTableHeadTitles"
            :tableData="state.returnTableData"
            :showPagination="false"
            :tableHeight="state.tableHeight"
          />
        </div>
      </n-tab>
      <n-tab title="返回示例" id="backData">
        <div class="test-api-box-bottom" v-loading="state.backDataLoading">
          <div class="title">请求返回</div>
          <div class="back-content">
            <div class="content-left">
              <div class="code">Code</div>
              <div class="code-number">{{ state.statusCode }}</div>
            </div>
            <div class="content-right">
              <div class="back-title">响应内容</div>
              <div class="back-data-box">
                <div class="back-data">
                  <div v-show="state.showJsonViewer">
                    <json-viewer :value="state.treeData" :expand-depth="5" copyable boxed sort />
                    <!-- <JsonViewer :value="state.treeData" :expand-depth="5" copyable boxed sort /> -->
                  </div>

                  <div v-show="!state.showJsonViewer">
                    {{ state.errorMessage }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-tab>
    </n-tabs>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      const router = useRouter()
      const requestPublicTable = ref()
      const returnPublicTable = ref()
      const state = reactive({
        firstButtonActive: true,
        activeName: 'base',
        disabled: true,
        detailId: null,
        paramsType: 'request',
        ruleForm: {
          projectCode: '',
          apiName: '',
          protocol: '',
          requestMethod: '',
          example: '',
          apiUrl: '',
          showUrl: '',
          apiStory: '',
          token: '',
          createByName: '',
          createTime: '',
          updateByName: '',
          updateTime: '',
          auditComments: '',
          paramColumns: [], // 自定义参数
        },
        returnTableHeadTitles: [
          // 必须为name 否则渲染不出表头

          { prop: 'name', name: '元数据' },
          { prop: 'fieldTypeName', name: '类型' },
          { prop: 'fieldLength', name: '长度' },
          { prop: 'description', name: '描述' },
        ],
        returnTableData: {},
        requestTableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'name', name: '参数名称' },
          { prop: 'value', name: '参数值', slot: 'parameterValue', width: 300 },
          { prop: 'fieldType', name: '参数类型' },
          { prop: 'isRequired', name: '是否必填', slot: 'isRequired' },
          { prop: 'description', name: '参数说明' },
        ],
        requestTableData: {},

        tableHeight: 400,
        allParamColumns: [],
        backDataLoading: false,
        treeData: {},
        showJsonViewer: false,
        statusCode: '',
        errorMessage: '',
        queryData: '',
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 303
        },
        // 查看 返回/请求 参数
        getRequestParams() {
          state.paramsType = 'request'
          state.firstButtonActive = true
        },
        getReturnParams() {
          state.paramsType = 'return'
          state.firstButtonActive = false
        },

        // 获取API详情
        getDetail(id) {
          state.loading = true
          api.dataService
            .getApiDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  state.ruleForm[key] = data[key]
                })
                // state.ruleForm['showUrl'] =
                //   data.protocol.toLowerCase() + '://' + data.serverUrl + data.apiUrl // 全路径
                state.ruleForm['showUrl'] = data.serverUrl + data.apiUrl // 全路径
                state.allParamColumns = data.paramColumns
              }
            })
            .catch(() => {})
        },
        getParams() {
          // 返回参数
          state.returnTableData = { list: state.allParamColumns }
          //请求参数
          let allrequestColumns = JSON.parse(JSON.stringify(state.allParamColumns)) //深拷贝返回参数
          allrequestColumns.map((item) => {
            return Object.assign(item, { value: '', isRequired: false })
          })
          allrequestColumns = [
            {
              name: 'pageNum',
              value: 1,
              fieldType: 'Number',
              isRequired: true,
              description: '当前页',
              type: 'number',
            },
            {
              name: 'pageSize',
              value: 10,
              fieldType: 'Number',
              isRequired: true,
              description: '每页多少条,示例值(10)',
              type: 'number',
            },
            ...state.allParamColumns,
          ]
          state.requestTableData = { list: allrequestColumns }
        },
        init(data) {
          if (data) {
            Object.keys(state.ruleForm).forEach((key) => {
              state.ruleForm[key] = data[key]
            })
            state.state = data.auditStatus
            nextTick(() => {
              hasParamsTable.value.initTableData({ list: data.paramColumns })
            })
          } else {
            hasParamsTable.value.initFailed()
          }
        },
        // 改变参数
        changeParams() {
          addParameters.value.init()
        },
        // 请求方式
        getDataSourceType() {
          api.project.getDataSourceType().then((res) => {
            if (res.success) {
              let options = []
              res.data.forEach((item) => {
                options.push({
                  value: item.code,
                  label: item.name,
                })
              })
              state.org_dataSourceType = options
            }
          })
        },
        // 测试API
        testDatasource() {
          state.testLoading = true
          api.project
            .testDatasource(state.ruleForm)
            .then((res) => {
              let { success, msg } = res.data
              state.testLoading = false
              if (success)
                ElNotification({
                  title: '提示',
                  message: '测试通过',
                  type: 'success',
                })
              else {
                ElNotification({
                  title: '提示',
                  message: '测试未通过 ---' + msg,
                  type: 'error',
                })
              }
            })
            .catch(() => {
              state.testLoading = false
            })
        },
        changeMolecule() {},
        // 点击全选input内容
        inputFocus(e, editor) {
          e.target.select()
        },
        // input的type=number时可以输入字母e+-的解决方式 e在数学上代表的是⽆理数，是⼀个⽆限不循环的⼩数
        onKeydown(e, row) {
          if (row.type === 'number') {
            let key = e.key
            if (key === 'e' || key === 'E' || key === '+' || key === '-') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          }
        },
        //切换
        handleClick(id) {
          if (id === 'backData') {
            methods.testApi()
          } else if (id === 'params') {
            methods.setTableHeight()
            methods.getParams()
          }
        },
        testApi() {
          // 验证参数
          let passed = true
          let data = {}
          state.allParamColumns.forEach((item) => {
            data[item.name] = item.value ? item.value : null
            if (item.isRequired && !item.value) {
              passed = false
            }
          })
          if (passed) {
            state.backDataLoading = true
            let _data = []

            state.allParamColumns.forEach((item) => {
              if (item.id) {
                _data.push({
                  metadataId: item.id,
                  name: item.name,
                  textValue: item.value,
                })
              } else {
                _data.push({
                  name: item.name,
                  textValue: item.value,
                })
              }
            })
            let _object = { apiParamForms: _data, id: state.detailId, token: state.ruleForm.token }
            api.dataService
              .apiTest(_object)
              .then((res) => {
                let { success } = res
                state.backDataLoading = false
                if (success) {
                  state.statusCode = 200
                  state.showJsonViewer = true
                  state.treeData = res
                  ElNotification({
                    title: '提示',
                    message: '测试成功',
                    type: 'success',
                  })
                }
              })
              .catch((err) => {
                state.backDataLoading = false
                state.statusCode = '失败'
                state.showJsonViewer = false
                state.treeData = {}
                state.errorMessage = err.message
              })
          } else {
            ElNotification({
              title: '提示',
              message: '参数信息-请求参数-必填字段丢失',
              type: 'error',
            })
          }
        },
      }
      onMounted(() => {
        state.detailId = router.currentRoute.value.query.id
        methods.getDetail(state.detailId)
      })

      return {
        state,
        requestPublicTable,
        returnPublicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .detail-api {
    color: #333;
    height: 100%;
    position: relative;
    .base-tabs {
      height: 100%;
      :deep(.nancalui-tab__content) {
        height: calc(100% - 60px);

        > div {
          height: 100%;
        }
      }
    }
    :deep(.base-tabs) {
      .nancalui-form {
        .nancalui-form__item--horizontal.input-textarea {
          align-items: start;
          .nancalui-form__label {
            align-self: start;
          }
        }
      }
    }
    .params-info {
      display: flex;
      padding: 0 0 10px 0;
      :deep(.nancalui-button) {
        width: 88px;
        height: 32px;
        border: 1px solid #e1e1e1;
        margin-left: 0;
        border-radius: 0;
        &.active {
          background: #f0f7ff;
          color: $themeBlue;
          border: 1px solid $themeBlue;
        }
        > span {
          font-size: 13px;
        }
      }
      div {
        padding-right: 20px;
        cursor: pointer;
      }
    }
    .params-content {
      .common-table {
        font-size: 200px;
        :deep(.el-table) {
          .el-table__cell {
            .required {
              color: #f54446;
            }
            .required-input .el-input__wrapper {
              border: 1px solid #f54446;
              &.is-focus {
                box-shadow: none;
              }
            }
          }
        }
      }
    }
    .test-api-box-bottom {
      height: 100%;
      font-size: 12px;
      .title {
        height: 57px;
        padding: 10px 0;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
      .back-content {
        display: flex;
        height: calc(100% - 80px);
        font-weight: 400px;
        .content-left {
          width: 260px;
          .code {
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 5px;
            line-height: 20px;
          }
          .code-number {
            padding-top: 20px;
          }
        }
        .content-right {
          flex: 1;
          .back-title {
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 5px;
            line-height: 20px;
          }
          .back-data-box {
            padding: 10px 0;
            height: calc(100% - 20px);
            .back-data {
              height: 100%;
              overflow: auto;
              // background: #f4f4f4;
              // border-radius: 4px;

              & > div {
                height: 100%;
              }
              :deep(.jv-container.jv-light) {
                background-color: #f4f4f4;
                padding: 10px 0;
                height: calc(100% - 20px);
                .jv-code {
                  overflow: auto;
                  &.boxed {
                    height: calc(100% - 100px);
                    max-height: 600px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
</style>
