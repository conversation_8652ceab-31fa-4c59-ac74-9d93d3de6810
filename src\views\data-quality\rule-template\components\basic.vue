<template>
  <!-- 基础信息 -->
  <div class="rule-template-basic publicFormCss">
    <div class="add-basic-box">
      <n-form
        ref="form"
        class="form__label_font14"
        :data="state.form"
        label-width="100px"
        :rules="state.rules"
        :pop-position="['right']"
        v-loading="state.loading"
      >
        <div class="content-title">
          <span>基础信息</span>
        </div>
        <div :class="['base-box', state.expandTableStatus[0] ? '' : 'hide']">
          <n-form-item label="规则名称：" field="name">
            <n-input v-model="state.form.name" maxlength="30" placeholder="规则ID" clearable>
              <template #prepend>{{ state.templateName }}</template></n-input
            >
          </n-form-item>
          <n-form-item label="描述：" class="label_align_required">
            <n-textarea
              v-model="state.form.description"
              placeholder="请输入描述信息"
              maxlength="200"
              :autosize="{ minRows: 3 }"
              resize="both"
              show-count
            />
          </n-form-item>
          <n-form-item label="标准规范：" field="refStdSpec">
            <div class="standard-box">
              <n-select
                v-model="state.form.refStdSpec"
                placeholder="请选择标准规范"
                :allow-clear="true"
                filter
                :options="state.standardOptions"
                @value-change="standardChangeFn"
              />
              <n-button color="primary" @click.prevent.stop="standardFn"> 管理 </n-button>
            </div>
          </n-form-item>
          <n-form-item label="规则来源：" field="ruleSource">
            <n-select
              disabled
              v-model="state.form.ruleSource"
              placeholder="请选择模板类型"
              :options="state.comeTypeOptions"
            />
          </n-form-item>
          <n-form-item
            v-if="state.form.ruleSource === 'CUSTOMED'"
            label="规则定义："
            field="ruleDefinition"
          >
            <n-radio-group
              direction="row"
              v-model="state.form.ruleDefinition"
              @change="ruleDefinitionChange"
            >
              <n-radio value="CONDITIONAL">条件规则</n-radio>
              <n-radio value="REGULAR">正则表达式</n-radio>
              <n-radio value="SQL">SQL表达式</n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            v-if="state.form.ruleSource === 'CUSTOMED'"
            label="六性维度："
            field="sexomorphicDimension"
          >
            <n-select
              v-model="state.form.sexomorphicDimension"
              placeholder="请选择六性维度"
              :options="state.sexomorphicDimensionOptions"
            />
          </n-form-item>
          <n-form-item v-else label="规则模板：" field="templeId">
            <div class="form-item-half">
              <span>{{ state.ruleTypeName }}</span>
              <!-- <n-select
                class="no-error-boder"
                :disabled="state.fromRuleTemplateDisabled"
                v-model="state.form.ruleType"
                placeholder="请选择模板类型"
                :options="state.ruleTypeOptions"
                @value-change="ruleTypeChange"
              /> -->
              <n-select
                :disabled="state.fromRuleTemplateDisabled"
                :key="state.key"
                v-model="state.form.templeId"
                placeholder="请选择规则模板"
                :options="state.ruleTypeList"
                @value-change="ruleTempleChange"
              />
            </div>
          </n-form-item>
          <n-form-item label="分组名称：" field="groupId">
            <n-tree-select
              v-model="state.form.groupId"
              filter
              allowClear
              useGrayArrow
              placeholder="选择所属分组"
              :treeData="state.treeData"
            />
          </n-form-item>
        </div>

        <div class="content-title">
          <span>配置信息</span>
        </div>
        <div :class="[' config-box', state.expandTableStatus[1] ? '' : 'hide']">
          <!-- 自定义 -->
          <div v-if="state.form.ruleSource === 'CUSTOMED'">
            <!-- 条件规则 -->
            <div v-if="state.form.ruleDefinition == 'CONDITIONAL'">
              <n-form-item label="条件满足：" field="configInfoCondition">
                <div class="full-box">
                  <n-select
                    v-model="state.form.configInfoCondition"
                    size="small"
                    placeholder="请选择"
                  >
                    <n-option name="满足全部（&&）" value="&&" />
                    <n-option name="满足任意（||）" value="||" />
                  </n-select>
                  <n-button class="svg-center" size="sm" color="primary" @click="addConditionFn">
                    <SvgIcon icon="new-add" class="icon" title="添加" />添加
                  </n-button>
                  <div class="condition-box">
                    <div
                      class="condition-list"
                      v-for="(item, index) in state.form.ruleConfigInfoList"
                      :key="index"
                    >
                      <div class="condition-list-name">字段值</div>
                      <n-select
                        v-model="item.operation"
                        placeholder="请选择"
                        :options="state.conditionList"
                      />
                      <n-select
                        v-model="item.dataType"
                        placeholder="请选择"
                        :options="state.conditionType"
                      />
                      <n-input-number v-model="item.dataValue" placeholder="请输入" />

                      <n-button
                        variant="text"
                        class="del-btn"
                        color="primary"
                        :disabled="state.form.ruleConfigInfoList?.length === 1"
                        @click="removeConditionFn(index)"
                      >
                        <SvgIcon icon="new-target-service-delete" class="icon" title="移除" />
                      </n-button>
                    </div>
                  </div>
                </div>
              </n-form-item>
              <n-form-item label="逻辑：" class="label_align_required">
                <div class="full-box bg">
                  <div
                    class="condition-desc"
                    v-for="(item, index) in state.form.ruleConfigInfoList"
                    :key="index"
                  >
                    {{ index === 0 ? '' : state.form.configInfoCondition }}字段值{{
                      item.dataType === 'LENGTH' ? '的长度' : ''
                    }}{{ item.operation }}{{ item.dataValue }}
                  </div>
                </div>
              </n-form-item>
            </div>
            <!-- 正则表达式 -->
            <div v-else-if="state.form.ruleDefinition == 'REGULAR'">
              <n-form-item label="正则表达式：" field="regularExpression">
                <n-textarea
                  v-model="state.form.regularExpression"
                  resize="none"
                  :rows="5"
                  placeholder="请输入java正则表达式，如([1-9]\d*\.?\d*)|(0\.\d*[1-9])"
                  @change="regularExpressionChange"
                />
              </n-form-item>
              <n-form-item label="测试数据：" class="label_align_required" field="selfTestValue">
                <div class="full-box">
                  <n-button class="svg-center no-margin" size="sm" color="primary" @click="testFn">
                    <SvgIcon icon="new-target-service-test" class="icon" title="添加" />测试
                  </n-button>
                  <n-textarea
                    v-model="state.form.selfTestValue"
                    resize="none"
                    :rows="6"
                    placeholder="请输入测试数据"
                  />
                </div>
              </n-form-item>
            </div>
            <!-- SQL表达式 -->
            <div v-else>
              <n-form-item label="SQL表达式1:" field="sqlScript">
                <div class="sql-box">
                  <n-button
                    class="svg-center no-margin"
                    color="primary"
                    @click="editSqlFn('sqlScript')"
                  >
                    <SvgIcon icon="icon-table-edit" class="icon" title="编辑" />编辑
                  </n-button>
                  <n-textarea
                    v-model="state.form.sqlScript"
                    resize="none"
                    :disabled="true"
                    readonly=""
                    :rows="6"
                    placeholder="请输入完整的SQL语句，返回结果需返回一行一列的值作为阈值范围监控结果。"
                  />
                </div>
              </n-form-item>

              <n-form-item label="SQL表达式2:" field="warnQuerySql">
                <div class="sql-box">
                  <n-button
                    class="svg-center no-margin"
                    color="primary"
                    @click="editSqlFn('warnQuerySql')"
                  >
                    <SvgIcon icon="icon-table-edit" class="icon" title="编辑" />编辑
                  </n-button>
                  <n-textarea
                    v-model="state.form.warnQuerySql"
                    resize="none"
                    :disabled="true"
                    readonly=""
                    :rows="6"
                    placeholder="请输入查询问题数据明细的SQL语句。"
                  />
                </div>
              </n-form-item>
            </div>
          </div>
          <!-- 规则模板配置 -->
          <n-form-item
            v-show="state.form.ruleSource === 'CUSTOMED'"
            label="阈值规则："
            field="filterNub"
          >
            <div class="threshold-rule-box">
              <n-tooltip
                class="tree-btn"
                :content="state.thresholdExplain"
                position="top"
                :enterable="false"
              >
                <SvgIcon class="illustrate" icon="icon-illustrate" />
              </n-tooltip>
              <div class="threshold-rule-list normal">
                <div class="label">正常阈值范围</div>
                <n-select
                  class="no-error-boder"
                  v-model="state.form.filterSymbol"
                  placeholder="请选择"
                  :options="state.conditionList"
                  @value-change="filterSymbolChange"
                />
                <n-input-number
                  :min="state.form.ruleDefinition === 'SQL' ? state.inputNumberMin : 0"
                  :max="state.form.ruleDefinition === 'SQL' ? state.inputNumberMax : 1"
                  :step="state.form.ruleDefinition === 'SQL' ? state.inputNumberStep : 0.1"
                  v-model="state.form.filterNub"
                  placeholder="请输入"
                />
              </div>
              <div class="threshold-rule-list alarm">
                <div class="label">告警阈值范围</div>
                <n-select
                  v-model="state.form.alarmSymbol"
                  placeholder=""
                  disabled
                  :options="state.conditionList"
                />
                <n-input-number disabled v-model="state.form.filterNub" placeholder="请输入" />
              </div>
            </div>
          </n-form-item>
          <n-form-item
            v-if="state.templeEnName === 'FIELD_VALUE_RANGE_CHECK'"
            label="范围类型："
            field="templeId"
          >
            <n-radio-group
              v-model="state.form.fieldRangeConfig.rangeType"
              :disabled="state.form.id"
              direction="row"
            >
              <n-radio value="NUMBER_RANGE">数值范围</n-radio>
              <!--              <n-radio value="DATE_RANGE">时间范围</n-radio>-->
            </n-radio-group>
          </n-form-item>
          <n-form-item
            v-if="
              state.templeEnName === 'FIELD_VALUE_RANGE_CHECK' &&
              state.form.fieldRangeConfig.rangeType === 'NUMBER_RANGE'
            "
            label="数值范围："
            field="fieldRangeConfig"
          >
            <div class="field-box">
              <div class="field-box-col">
                <div class="field-box-col-title">最小值</div>
                <div class="field-box-col-item"
                  ><n-input-number
                    :step="1"
                    :hideButton="true"
                    v-model="state.form.fieldRangeConfig.minNum"
                    placeholder="请输入"
                /></div>
                <div class="field-box-col-item"
                  ><n-checkbox
                    label="包含"
                    :isShowTitle="false"
                    v-model="state.form.fieldRangeConfig.includeMin"
                /></div>
              </div>
              <div class="field-box-col">
                <div class="field-box-col-title">最大值</div>
                <div class="field-box-col-item"
                  ><n-input-number
                    :step="1"
                    :hideButton="true"
                    v-model="state.form.fieldRangeConfig.maxNum"
                    placeholder="请输入"
                /></div>
                <div class="field-box-col-item"
                  ><n-checkbox
                    label="包含"
                    :isShowTitle="false"
                    v-model="state.form.fieldRangeConfig.includeMax"
                /></div>
              </div>
            </div>
          </n-form-item>
          <n-form-item
            v-if="
              state.templeEnName === 'FIELD_VALUE_RANGE_CHECK' &&
              state.form.fieldRangeConfig.rangeType === 'DATE_RANGE'
            "
            label="时间范围："
            field="fieldRangeConfig"
          >
            <div class="range-box">
              <n-range-date-picker-pro
                v-model="state.form.fieldRangeConfig.date"
                :placeholder="['开始日期', '结束日期']"
                format="YYYY-MM-DD"
                allow-clear
              />
              <div class="field-box">
                <div class="field-box-col">
                  <div class="field-box-col-item"
                    ><n-checkbox
                      label="包含"
                      :isShowTitle="false"
                      v-model="state.form.fieldRangeConfig.includeMin"
                  /></div>
                </div>
                <div class="field-box-col">
                  <div class="field-box-col-item"
                    ><n-checkbox
                      label="包含"
                      :isShowTitle="false"
                      v-model="state.form.fieldRangeConfig.includeMax"
                  /></div>
                </div>
              </div>
            </div>
          </n-form-item>
          <n-form-item
            v-if="state.templeEnName === 'FIELD_LENGTH_CHECK'"
            label="字段长度："
            field="fieldLengthConfig"
          >
            <div class="field-box">
              <div class="field-box-col">
                <div class="field-box-col-title">最小值</div>
                <div class="field-box-col-item"
                  ><n-input-number
                    :step="1"
                    :hideButton="true"
                    v-model="state.form.fieldLengthConfig.minNum"
                    placeholder="请输入"
                /></div>
                <div class="field-box-col-item"
                  ><n-checkbox
                    label="包含"
                    :isShowTitle="false"
                    v-model="state.form.fieldLengthConfig.includeMin"
                /></div>
              </div>
              <div class="field-box-col">
                <div class="field-box-col-title">最大值</div>
                <div class="field-box-col-item"
                  ><n-input-number
                    :step="1"
                    :hideButton="true"
                    v-model="state.form.fieldLengthConfig.maxNum"
                    placeholder="请输入"
                /></div>
                <div class="field-box-col-item"
                  ><n-checkbox
                    label="包含"
                    :isShowTitle="false"
                    v-model="state.form.fieldLengthConfig.includeMax"
                /></div>
              </div>
            </div>
          </n-form-item>
          <n-form-item
            v-if="state.templeEnName === 'ENNUMERATION_VALUE_CHECK'"
            label="枚举值："
            field="enumConfig"
          >
            <div class="enumerate">
              <n-button class="enumerate-btn" color="primary" @click.prevent.stop="addEnumerateFn"
                ><SvgIcon icon="new-add" class="icon" title="新建" />添加</n-button
              >
              <div v-if="state.form.enumConfig.enumsList.length > 0" class="enumerate-box">
                <div
                  v-for="(item, index) in state.form.enumConfig.enumsList"
                  :key="index"
                  class="enumerate-box-row"
                >
                  <n-input class="ipt" v-model="item.value" />
                  <SvgIcon
                    icon="icon-new-delete"
                    class="icon"
                    title="删除"
                    @click.prevent.stop="delEnumerateFn(index)"
                  />
                </div>
              </div>
            </div>
          </n-form-item>
          <n-form-item
            v-show="state.form.ruleSource === 'TEMPLATE'"
            label="阈值规则："
            field="filterNub"
          >
            <div class="threshold-rule-box">
              <n-tooltip
                class="tree-btn"
                :content="state.thresholdExplain"
                position="top"
                :enterable="false"
              >
                <SvgIcon class="illustrate" icon="icon-illustrate" />
              </n-tooltip>
              <div class="threshold-rule-list normal">
                <div class="label">正常阈值范围</div>
                <n-select
                  v-model="state.form.filterSymbol"
                  class="no-error-boder"
                  placeholder="请选择"
                  :options="state.conditionList"
                  @value-change="filterSymbolChange"
                />
                <n-input-number
                  :min="state.inputNumberMin"
                  :max="state.inputNumberMax"
                  :step="state.inputNumberStep"
                  v-model="state.form.filterNub"
                  placeholder="请输入"
                />
              </div>
              <div class="threshold-rule-list alarm">
                <div class="label">告警阈值范围</div>
                <n-select
                  v-model="state.form.alarmSymbol"
                  placeholder=""
                  disabled
                  :options="state.conditionList"
                />
                <n-input-number disabled v-model="state.form.filterNub" placeholder="请输入" />
              </div>
            </div>
          </n-form-item>

          <n-form-item label="禁用：" class="label_align_required">
            <n-checkbox v-model="state.form.ruleDisabled" />
          </n-form-item>
        </div>
      </n-form>
    </div>
    <editSql
      :isShow="state.showSql"
      :sqlFieldName="state.sqlFieldName"
      :selectedData="state.form"
      @closeSql="closeSqlFn"
    />
    <standardSpecification ref="standardSpecification" @updateFn="stdSpecDocFn" />
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { checkCName } from '@/utils/validate'
  import { formartTimeDate } from '@/utils/index'
  import editSql from './edit-sql'
  import standardSpecification from './standard-specification'
  export default {
    name: '',
    components: { editSql, standardSpecification },
    setup(props, { emit }) {
      // 检查枚举值
      const checkEnum = (rule, value, callback) => {
        if (value.enumsList.length > 0) {
          let isError = false
          value.enumsList.forEach((val) => {
            if (!val.value) {
              isError = true
            }
          })
          if (isError) {
            callback(new Error('枚举值不能为空'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请添加枚举值'))
        }
      }
      // 检查字段长度
      const checkFieldNum = (rule, value, callback) => {
        if (value.minNum === '' || value.minNum === undefined) {
          callback(new Error('最小值不能为空'))
        } else if (value.maxNum === '' || value.maxNum === undefined) {
          callback(new Error('最大值不能为空'))
        } else if (value.minNum >= value.maxNum) {
          callback(new Error('最小值必须小于最大值'))
        } else {
          callback()
        }
      }

      // 检查字段范围
      const checkRangeNum = (rule, value, callback) => {
        if (value.rangeType === 'NUMBER_RANGE') {
          if (value.minNum === '' || value.minNum === undefined) {
            callback(new Error('最小值不能为空'))
          } else if (value.maxNum === '' || value.maxNum === undefined) {
            callback(new Error('最大值不能为空'))
          } else if (value.minNum >= value.maxNum) {
            callback(new Error('最小值必须小于最大值'))
          } else {
            callback()
          }
        } else {
          if (value.date && value.date[0]) {
            callback()
          } else {
            callback(new Error('请选择日期'))
          }
        }
      }

      const form = ref()
      const standardSpecification = ref()
      const state = reactive({
        loading: false,
        showSql: false,
        sqlFieldName: '',
        expandTableStatus: new Array(10).fill(true),
        templateName: '$表名_字段级_$字段名_条件规则_',
        thresholdExplain: '支持输入为大于等于0的整数作为阈值监控指标。',
        form: {
          configInfoCondition: '&&',
          description: '',
          name: '',
          regularExpression: '',
          ruleConfigInfoList: [
            {
              operation: '==',
              dataType: 'VALUE',
              dataValue: 0,
            },
          ],
          ruleDefinition: 'CONDITIONAL',
          ruleDisabled: false,
          refStdSpec: '',
          refStdSpecName: '',
          ruleSource: 'TEMPLATE',
          ruleThresholdList: [],
          ruleTableFieldList: [],
          ruleType: '',
          templeId: '',
          templeName: '',
          groupId: '',

          filterSymbol: '==',
          alarmSymbol: '!=',
          filterNub: '',
          selfTestValue: '',
          sexomorphicDimension: '',
          applicableDataType: null,

          sqlFieldName: '',
          querySql: '',
          sqlScript: '',
          warnQuerySql: '',

          enumConfig: {
            enumsList: [],
          },
          fieldRangeConfig: {
            includeStart: false,
            includeEnd: false,
            includeMax: false,
            includeMin: false,
            startDate: false,
            endDate: false,
            date: '',
            maxNum: 100,
            minNum: 0,
            rangeType: 'NUMBER_RANGE',
          },
          fieldLengthConfig: {
            includeMax: false,
            includeMin: false,
            maxNum: 100,
            minNum: 0,
          },
        },

        rules: {
          name: [{ required: true, validator: checkCName, trigger: 'blur' }],
          refStdSpec: [{ required: true, type: 'number', message: '标准规范', trigger: 'blur' }],
          ruleSource: [{ required: true, message: '规则来源', trigger: 'blur' }],
          ruleDefinition: [{ required: true, message: '请设置规则定义', trigger: 'change' }],
          sexomorphicDimension: [{ required: true, message: '请选择六性维度', trigger: 'change' }],
          templeId: [
            { type: 'number', required: true, message: '请选择规则模板', trigger: 'change' },
          ],
          configInfoCondition: [{ required: true, message: '请选择条件', trigger: 'change' }],
          regularExpression: [{ required: true, message: '请输入java正则表达式', trigger: 'blur' }],
          selfTestValue: [{ required: true, message: '请输入测试数据', trigger: 'change' }],
          groupId: [
            { type: 'number', required: true, message: '请选择分组名称', trigger: 'change' },
          ],
          filterNub: [
            { type: 'number', required: true, message: '请输入阈值规则', trigger: 'blur' },
          ],
          sqlScript: [{ required: true, message: '请添加SQL语句', trigger: 'blur' }],
          warnQuerySql: [{ required: true, message: '请添加SQL语句', trigger: 'blur' }],
          enumConfig: [{ required: true, validator: checkEnum, trigger: 'blur' }],
          fieldLengthConfig: [{ required: true, validator: checkFieldNum, trigger: 'blur' }],
          fieldRangeConfig: [{ required: true, validator: checkRangeNum, trigger: 'blur' }],
        },

        comeTypeOptions: [
          {
            name: '自定义规则',
            value: 'CUSTOMED',
          },
          {
            name: '规则模板',
            value: 'TEMPLATE',
          },
        ],
        standardOptions: [],
        conditionList: [
          { name: '等于(==)', value: '==', operator: '==' },
          { name: '不等于(!=)', value: '!=', operator: '!=' },
          { name: '小于(<)', value: '<', operator: '<' },
          { name: '大于(>)', value: '>', operator: '>' },
          { name: '小于或者等于(<=)', value: '<=', operator: '<=' },
          { name: '大于或者等于(>=)', value: '>=', operator: '>=' },
        ],
        conditionType: [
          { name: '数值', value: 'VALUE' },
          { name: '长度', value: 'LENGTH' },
        ],
        ruleTypeOptions: [
          {
            name: '字段级',
            value: 'FIELD',
          },
          {
            name: '表级',
            value: 'TABLE',
          },

          {
            name: '跨表级',
            value: 'ACROSS_TABLE',
          },
        ],
        sexomorphicDimensionOptions: [
          {
            name: '准确性',
            value: 'ACCURACY',
          },
          {
            name: '有效性',
            value: 'VALIDITY',
          },
          {
            name: '完整性',
            value: 'COMPLETENESS',
          },
          {
            name: '唯一性',
            value: 'UNIQUENESS',
          },
          {
            name: '一致性',
            value: 'CONSISTENCY',
          },
          {
            name: '及时性',
            value: 'TIMELINESS',
          },
        ],
        ruleTypeList: [],

        selfTestResult: false, //正则是否测试通过
        fromRuleTemplateDisabled: false, //从模板页面新增直接禁用
        inputNumberMin: 0,
        inputNumberMax: 1,
        inputNumberStep: 1,
        treeData: [],
        ruleTypeName: '规则模板',
        tableName: '$表名', //表名
        fieldName: '$字段名', //字段名
        templeEnName: '', //模板英文名
      })
      const methods = {
        // 获取规范列表
        stdSpecDocFn() {
          api.dataQuality
            .stdSpecDocList({
              filename: null,
              id: null,
              name: null,
              url: null,
            })
            .then((res) => {
              if (res.success) {
                res.data.forEach((val) => {
                  val.value = val.id
                })
                state.standardOptions = res.data
              }
            })
        },
        // 打开规范管理
        standardFn() {
          standardSpecification.value.open()
        },
        // 选中规范
        standardChangeFn(e) {
          if (e) {
            state.form.refStdSpecName = e.name
          } else {
            state.form.refStdSpecName = ''
          }
        },
        // 增加枚举值
        addEnumerateFn() {
          state.form.enumConfig.enumsList.push({ value: '' })
        },
        // 删除枚举值
        delEnumerateFn(index) {
          state.form.enumConfig.enumsList.splice(index, 1)
        },
        ruleDefinitionChange(val) {
          if (val) {
            emit('changeRuleDefinition', val)
            methods.setTempleName()
          }
        },
        //展开收起下拉内容
        expandTable(index) {
          state.expandTableStatus[index] = !state.expandTableStatus[index]
        },
        // 获取分组树列表
        async getTreeListFn() {
          await api.dataQuality.getRuleTreeListV2({}).then((res) => {
            let { success, data } = res
            if (success) {
              let treeData = []
              if (res.data.length > 0) {
                treeData = [...res.data]
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              methods.setTreeRecursion(state.treeData)
            }
          })
        },
        setTreeRecursion(data) {
          data.map((item, index) => {
            item.value = item.id
            item.label = item.name
            if (item.children && item.children.length != 0) {
              methods.setTreeRecursion(item.children)
            }
          })
          return data
        },
        //根据模板配置阈值规则的范围值
        setRuleThreshold(enName) {
          state.templeEnName = enName
          switch (enName) {
            //表行数 表大小 字段空值数  字段重复数 字段唯一值数
            case 'TABLE_ROW_NUMBER':
            case 'TABLE_SIZE':
            case 'FIELD_NULL_NUMBER':
            case 'NUMBER_OF_FIELD_DUPLICATES':
            case 'NUMBER_OF_FIELD_UNIQUE_VALUES':
              console.log(Infinity)
              state.inputNumberMin = 0
              state.inputNumberMax = Infinity
              state.inputNumberStep = 1
              break
            //字段空值率  字段重复率
            case 'FIELD_NULL_RATE':
            case 'FIELD_REPETITION_RATE':
              state.inputNumberMin = 0
              state.inputNumberMax = 1
              state.inputNumberStep = 2
              break
            case 'FIELD_MAXIMUM':
            case 'FIELD_MINIMUM':
            case 'FIELD_MEAN':
            case 'FIELD_SUMMARY_VALUE':
              state.inputNumberMin = -Infinity
              state.inputNumberMax = Infinity
              state.inputNumberStep = 1
              break
            default:
              state.inputNumberMin = 0
              state.inputNumberMax = 1
              state.inputNumberStep = 1
              break
          }
        },
        //规则模板type change
        ruleTypeChange(val) {
          if (val) {
            state.ruleTypeName = val.name
            state.form.templeId = ''
            state.form.templeName = ''
            state.key++
            methods.getQualityRuleTemplateList()
            methods.setTempleName()
          }
        },
        //规则模板change
        ruleTempleChange(val) {
          if (val) {
            state.form.ruleType = val.ruleType
            state.ruleTypeName = val.ruleTypeName
            state.form.templeName = val.name
            state.form.applicableDataType = val.applicableDataType || null
            methods.setRuleThreshold(val.enName)
            methods.setTempleName()
          }
        },
        setTempleName() {
          if (state.form.ruleSource === 'CUSTOMED') {
            //自定义
            let _ruleDefinitionName = '条件规则_'
            if (state.form.ruleDefinition === 'CONDITIONAL') {
              _ruleDefinitionName = '条件规则_'
            } else if (state.form.ruleDefinition === 'REGULAR') {
              _ruleDefinitionName = '正则表达式_'
            } else if (state.form.ruleDefinition === 'SQL') {
              _ruleDefinitionName = 'SQL表达式_'
              state.inputNumberMin = -Infinity
              state.inputNumberMax = Infinity
              state.inputNumberStep = 1
              state.form.ruleType = 'TABLE'
            } else {
              _ruleDefinitionName = '条件规则_'
            }
            if (state.form.ruleDefinition === 'SQL') {
              state.thresholdExplain = '支持输入任意数值作为阈值监控指标。'
              state.templateName = `${state.tableName}_${_ruleDefinitionName}`
            } else {
              state.thresholdExplain =
                '支持输入0到1的数值作为阈值监控指标，运行结果为0表示每一条字段值都不满足该规则，运行结果为1则表示每一条字段值都满足该规则。'
              state.templateName = `${state.tableName}_字段级_${state.fieldName}_${_ruleDefinitionName}`
            }
          } else {
            //模板

            if (state.form.ruleType === 'TABLE' || !state.form.ruleType) {
              state.templateName = `${state.tableName}_${state.ruleTypeName}_${
                state.form.templeName ? state.form.templeName : '规则模板名'
              }_`
            } else {
              state.templateName = `${state.tableName}_${state.ruleTypeName}_${state.fieldName}_${
                state.form.templeName ? state.form.templeName : '规则模板名'
              }_`
            }
            switch (state.templeEnName) {
              case 'TABLE_ROW_NUMBER':
              case 'TABLE_SIZE':
              case 'FIELD_NULL_NUMBER':
              case 'NUMBER_OF_FIELD_DUPLICATES':
              case 'NUMBER_OF_FIELD_UNIQUE_VALUES':
                state.thresholdExplain = '支持输入为大于等于0的整数作为阈值监控指标。'
                break
              case 'FIELD_NULL_RATE':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为1时表示每一条字段值全部为空，运行结果为0时则表示每一条字段值全部非空。'
                break
              case 'FIELD_REPETITION_RATE':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为0时则表示每一条字段值都唯一。'
                break
              case 'FIELD_MAXIMUM':
              case 'FIELD_MINIMUM':
              case 'FIELD_MEAN':
              case 'FIELD_SUMMARY_VALUE':
                state.thresholdExplain = '支持输入任意数值作为阈值监控指标。'
                break
              case 'FIELD_VALUE_RANGE_CHECK':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为0表示每一条字段值都不满足字段值范围，运行结果为1则表示每一条字段值都满足字段值范围。'
                break
              case 'FIELD_LENGTH_CHECK':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为0表示每一条字段值都不满足字段长度，运行结果为1则表示每一条字段值都满足字段长度。'
                break
              case 'CROSS_TABLE_FIELD_CONSISTENCY':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为0表示两个跨表字段是完全不一致的，即主表字段的每一条字段值都不能在比对表字段的字段值中找到；运行结果为1表示两个跨表字段是一致的，即主表字段的值完全能在比对表的字段值中找到。。'
                break
              case 'ENNUMERATION_VALUE_CHECK':
                state.thresholdExplain =
                  '支持输入0到1的数值作为阈值监控指标，运行结果为0表示每一条字段值都不合法，运行结果为1则表示每一条字段值都合法。'
                break
              default:
                state.thresholdExplain = '支持输入为大于等于0的整数作为阈值监控指标。'
                break
            }
          }
        },
        //获取质量规则模板列表全量
        async getQualityRuleTemplateList(templeId = null) {
          await api.dataQuality
            .getQualityRuleTemplateList({
              // ruleType: state.form.ruleType || null,
              ruleType: null,
            })
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.ruleTypeList = data.map((val) => {
                  if (templeId && templeId === val.id) {
                    state.templeEnName = val.enName
                  }
                  return { ...val, value: val.id }
                })
              } else {
                state.ruleTypeList = []
              }
            })
        },

        // 点击测试
        testFn() {
          // form.value.validate((valid) => {
          //   if (valid) {
          if (state.form.regularExpression === '') {
            ElNotification({
              title: '提示',
              message: '请先输入正则表达式!',
              type: 'warning',
            })
            return false
          } else if (state.form.selfTestValue === '') {
            ElNotification({
              title: '提示',
              message: '请先输入测试数据!',
              type: 'warning',
            })
            return false
          }
          let data = {
            pattern: state.form.regularExpression,
            value: state.form.selfTestValue,
            ruleType: 'REGULAR',
            validType: 'VALUE',
          }
          api.dataQuality.getCommonRuleTest(data).then((res) => {
            if (res.code === 'SUCCESS') {
              let message = '测试未通过'
              if (res.data) {
                state.selfTestResult = true
                message = '测试通过'
              } else {
                state.selfTestResult = false
                let message = '测试未通过'
              }
              ElNotification({
                title: '提示',
                message,
                type: state.selfTestResult ? 'success' : 'warning',
              })
            }
          })
          //   }
          // })
        },

        // 编辑SQL
        editSqlFn(fieldName) {
          state.showSql = true
          state.sqlFieldName = fieldName
        },
        // 关闭sql弹窗
        closeSqlFn(item) {
          state.showSql = false
          if (item) {
            state.form[state.sqlFieldName || 'sqlScript'] = item.sqlScript
            if (state.sqlFieldName === 'sqlScript') {
              state.form = {
                ...state.form,
                sqlFieldName: item.sqlFieldName,
                querySql: item.querySql,
              }
            }
          }
        },

        // 增加条件
        addConditionFn() {
          state.form.ruleConfigInfoList.push({
            operation: '==',
            dataType: 'VALUE',
            dataValue: 0,
          })
        },
        //移除条件
        removeConditionFn(index) {
          state.form.ruleConfigInfoList.splice(index, 1)
        },
        //阈值改变
        filterSymbolChange() {
          switch (state.form.filterSymbol) {
            case '>':
              state.form.alarmSymbol = '<='
              break
            case '>=':
              state.form.alarmSymbol = '<'
              break
            case '<':
              state.form.alarmSymbol = '>='
              break
            case '<=':
              state.form.alarmSymbol = '>'
              break
            case '==':
              state.form.alarmSymbol = '!='
              break
            case '!=':
              state.form.alarmSymbol = '=='
              break

            default:
              break
          }
        },
        //怎则表达式输入change
        regularExpressionChange() {
          state.selfTestResult = false
        },
        // 校验是否通过
        getAllData() {
          let passed = new Promise((resolve) => {
            form.value.validate((valid) => {
              if (valid) {
                if (
                  state.form.ruleSource === 'CUSTOMED' &&
                  state.form.ruleDefinition === 'REGULAR'
                ) {
                  //怎则表达式必须测试通过
                  if (!state.selfTestResult) {
                    valid = false
                    ElNotification({
                      title: '提示',
                      message: '请填写测试数据并点击测试按钮进行校验',
                      type: 'warning',
                    })
                  }
                }
                state.form.ruleThresholdList = [
                  {
                    operation: state.form.filterSymbol,
                    operationValue: state.form.filterNub,
                    type: 'SUCCESS',
                  },
                  {
                    operation: state.form.alarmSymbol,
                    operationValue: state.form.filterNub,
                    type: 'WARNING',
                  },
                ]
              }

              //根据实际情况设置
              let _formCopy = JSON.parse(JSON.stringify(state.form))
              if (_formCopy.ruleSource === 'CUSTOMED') {
                //自定义
                _formCopy.ruleType = _formCopy.ruleDefinition === 'SQL' ? 'TABLE' : 'FIELD'
                _formCopy.templeId = null
                _formCopy.templeName = null
              } else {
                //模板
                _formCopy.ruleDefinition = null
                _formCopy.sexomorphicDimension = null
                _formCopy.configInfoCondition = null
                _formCopy.ruleConfigInfoList = null
              }

              if (state.templeEnName === 'ENNUMERATION_VALUE_CHECK') {
                _formCopy.enumConfig.enumsList = _formCopy.enumConfig.enumsList.map((val) => {
                  return val.value
                })
              }
              if (
                state.templeEnName === 'FIELD_VALUE_RANGE_CHECK' &&
                state.form.fieldRangeConfig.rangeType === 'DATE_RANGE'
              ) {
                if (
                  Object.prototype.toString.call(state.form.fieldRangeConfig.date[0]) ===
                  '[object Date]'
                ) {
                  _formCopy.fieldRangeConfig.startDate = formartTimeDate(
                    state.form.fieldRangeConfig.date[0],
                    '-',
                    true,
                  )
                  _formCopy.fieldRangeConfig.endDate = formartTimeDate(
                    state.form.fieldRangeConfig.date[1],
                    '-',
                    true,
                  )
                }
              }
              if (_formCopy?.fieldRangeConfig?.date) {
                delete _formCopy.fieldRangeConfig.date
              }
              if (_formCopy?.ruleConfigInfoList?.length === 0) {
                _formCopy.ruleConfigInfoList = null
              }

              resolve({
                passed: valid,
                data: _formCopy,
              })
            })
          })
          return passed
        },

        // 编辑时候回显
        async editInit(data, otherConfig = null) {
          if (data) {
            Object.keys(data).forEach((key) => {
              if (key in state.form) {
                if (key === 'groupId' || key === 'templeId') return
                state.form[key] = data[key]
              }
            })
            if (data.ruleTableFieldList?.length) {
              state.tableName = data.ruleTableFieldList[0].tableName
              state.fieldName = data.ruleTableFieldList[0].fieldName
            }
          }
          if (!state.form.ruleType) {
            state.ruleTypeName = '规则模版'
          } else if (state.form.ruleType === 'FIELD') {
            state.ruleTypeName = '字段级'
          } else if (state.form.ruleType === 'TABLE') {
            state.ruleTypeName = '表级'
          } else {
            state.ruleTypeName = '跨表级'
          }

          state.loading = true
          if (data.id) {
            //编辑
            state.form.id = data.id
            await methods.getTreeListFn()
            await methods.getQualityRuleTemplateList(data.templeId)

            state.form.fieldRangeConfig = data.fieldRangeConfig
              ? data.fieldRangeConfig
              : {
                  includeStart: false,
                  includeEnd: false,
                  includeMax: false,
                  includeMin: false,
                  startDate: false,
                  endDate: false,
                  date: '',
                  maxNum: 100,
                  minNum: 0,
                  rangeType: 'NUMBER_RANGE',
                }

            state.form.groupId = data.groupId
            state.form.templeId = data.templeId
            //获取最新templeName
            let activeRuleType = state.ruleTypeList.find(
              (item) => item.value === state.form.templeId,
            )

            state.form.templeName = activeRuleType?.name
            state.form.applicableDataType = activeRuleType?.applicableDataType || null

            if (state.form.ruleSource === 'CUSTOMED') {
              //自定义
              state.form.ruleConfigInfoList?.forEach((config) => {
                config.dataValue = Number(config.dataValue)
              })
            } else {
              if (state.templeEnName) {
                methods.setRuleThreshold(state.templeEnName)
              }
            }

            state.templateName = data.fullName.replace(data.name, '')
            if (state.form.ruleThresholdList) {
              state.form.ruleThresholdList.forEach((item) => {
                state.form.filterNub = Number(item.operationValue)
                if (item.type === 'SUCCESS') {
                  state.form.filterSymbol = item.operation
                } else {
                  state.form.alarmSymbol = item.operation
                }
              })
            }
          } else {
            //新增
            await methods.getTreeListFn()
            state.form.groupId = data.groupId
            if (state.form.ruleSource === 'TEMPLATE') {
              await methods.getQualityRuleTemplateList()

              state.form.templeId = data.templeId
            }
          }

          if (otherConfig) {
            if (otherConfig?.fromRuleTemplate) {
              state.fromRuleTemplateDisabled = true

              state.form.templeName = otherConfig.templateName
              state.form.applicableDataType = otherConfig.applicableDataType || null

              methods.setRuleThreshold(otherConfig.enName)
            }
          }
          methods.setTempleName()
          methods.stdSpecDocFn()
          state.loading = false
          //渲染处理数据
        },
      }

      return {
        form,
        standardSpecification,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  //统一表单输入框样式
  $labelWidth: 70px;
  @import '@/styles/variables.scss';

  .rule-template-basic {
    height: 100%;
    padding-right: 10px;

    color: #333;
    background: #fff;

    .nancalui-form {
      display: flex;
      flex-direction: column;

      .base-box {
        &.hide {
          height: 0;
          overflow: hidden;
        }
      }

      .nancalui-form__item--horizontal {
        .form-item-half {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          span {
            width: 90px;
            line-height: 30px;
            text-align: center;
            background: #fafafa;
            border: 1px solid var(---, #e5e6eb);
            border-right: none;
            border-radius: 6px;
          }

          .nancalui-select {
            &:first-child {
              margin-right: 8px;
            }
          }
        }

        .nancalui-input {
          display: flex;

          :deep(.nancalui-input__wrapper) {
            z-index: 1;
            border-radius: 0 6px 6px 0;
          }

          :deep(.nancalui-input-slot__prepend) {
            background-color: #e6ecf8;
            border: 1px solid #a3b4db;
            border-right: 0;
            border-radius: 6px 0 0 6px;
          }
        }

        :deep(.nancalui-checkbox) {
          height: 32px;
        }
      }

      .content-title {
        position: relative;
        height: 30px;
        margin-bottom: 16px;
        padding-left: 14px;
        color: #2b71c2;
        font-weight: 500;
        font-size: 14px;
        line-height: 30px;
        background-color: #f2f6fc;
        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }

      .standard-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .nancalui-select {
          width: calc(100% - 70px);
        }
      }

      .config-box {
        &.hide {
          height: 0;
          overflow: hidden;
        }

        .full-box {
          width: 100%;

          &.bg {
            min-height: 129px;
            padding: 5px 12px 5px 10px;
            background: #e5e5e5;
            border-radius: 6px;

            .condition-desc {
              color: rgba(0, 0, 0, 0.35);
              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              line-height: 22px;
            }
          }

          .nancalui-select {
            height: auto;
          }

          .condition-box {
            padding: 16px;
            background: var(---, #f6f7fb);
            border-radius: 6px;

            .condition-list {
              display: flex;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .condition-list-name {
                width: 60px;
                height: 32px;
                padding: 0 8px;
                color: rgba(0, 0, 0, 0.75);
                font-size: 14px;
                line-height: 32px;
                background: #d8e0f4;
                border-radius: 6px;
              }

              .nancalui-select,
              .nancalui-input-number {
                width: calc((100% - 60px - 28px - 24px) / 3);
                margin-left: 8px;
                text-align: left;

                :deep(.nancalui-input__wrapper) {
                  border-radius: 6px;

                  .nancalui-input-slot__suffix {
                    display: none;
                  }

                  .nancalui-input__inner {
                    text-align: left;
                  }
                }
              }

              .del-btn {
                margin: 0;
                margin-left: 12px;
                padding: 0;
                color: #8091b7;
                font-size: 16px;

                &.nancalui-button--disabled {
                  color: #b8b8b8;
                }
              }
            }
          }
        }

        :deep(.nancalui-input-number) {
          .nancalui-input__wrapper {
            padding: 0;
            padding-left: 10px;
            border-radius: 6px;

            &.nancalui-input--disabled {
              background-color: #ebebeb;
            }

            .nancalui-input__inner {
              text-align: left;
            }
          }

          .control-button {
            position: relative;

            svg {
              display: none;
            }

            &.control-inc {
              &::after {
                position: absolute;
                bottom: 4px;
                left: 50%;
                width: 0;
                height: 0;
                border-right: 4px solid transparent;
                border-bottom: 4px solid #8a8a8a;
                border-left: 4px solid transparent;
                transform: translateX(-50%);
                content: '';
              }
            }

            &.control-dec {
              &::after {
                position: absolute;
                bottom: 4px;
                left: 50%;
                width: 0;
                height: 0;
                border-top: 4px solid #8a8a8a;
                border-right: 4px solid transparent;
                border-left: 4px solid transparent;
                transform: translateX(-50%);
                content: '';
              }
            }
          }
        }

        .nancalui-button {
          &.svg-center {
            margin: 8px 0;
            padding: 0 16px;

            &.no-margin {
              margin: 0 0 8px 0;
            }

            :deep(.button-content) {
              display: flex;
              align-items: center;

              svg {
                margin-right: 4px;
              }
            }
          }
        }

        .threshold-rule-box {
          position: relative;
          width: 100%;
          .illustrate {
            position: absolute;
            top: 9px;
            left: -24px;
            color: #8091b7;
            &:hover {
              color: $themeBlue;
            }
          }
          .threshold-rule-list {
            display: flex;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 98px;
              color: rgba(0, 0, 0, 0.75);
              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              line-height: 32px;

              &::before {
                display: inline-block;
                width: 6px;
                height: 6px;
                margin-right: 8px;
                background-color: #f63838;
                border-radius: 6px;
                content: ' ';
              }
            }

            &.normal {
              .label {
                &::before {
                  background-color: #04c495;
                }
              }
            }

            .nancalui-select,
            .nancalui-input-number {
              width: calc((100% - 98px - 16px) / 2);
              margin-left: 8px;

              :deep(.nancalui-input__wrapper) {
                width: 100%;
              }
            }
          }
        }
        .sql-box {
          width: 100%;
          .svg-center.no-margin {
            margin: 0 0 4px 0;
          }
          :deep(.nancalui-textarea) {
            background-color: #e5e5e5;
            &:not(.nancalui-textarea--error) {
              border-color: #e5e5e5;
            }
          }
        }
        .enumerate {
          width: 100%;
          &-btn {
            :deep(.button-content) {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
            }
          }
          &-box {
            width: 578px;
            margin-top: 8px;
            padding: 16px;
            background-color: #f6f7fb;
            border-radius: 6px;
            &-row {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              margin-bottom: 8px;
              :deep(.nancalui-input) {
                &.ipt {
                  box-sizing: border-box;
                  width: 518px;
                  background-color: #fff;
                  .nancalui-input__wrapper {
                    border-radius: 6px;
                  }
                }
              }
              .icon {
                margin-left: 12px;
                color: #a3b4db;
                font-size: 16px;
                cursor: pointer;
                &:hover {
                  color: $themeBlue;
                }
              }
            }
          }
        }
        .range-box {
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 100%;
            .nancalui-range-date-picker-pro__normal-input {
              flex: 1;
            }
          }
        }
        .field-box {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          width: 100%;
          &-col {
            width: 285px;
            &-title {
              height: 32px;
              color: rgba(0, 0, 0, 0.55);
              font-size: 14px;
              line-height: 32px;
            }
            &-item {
              margin-top: 4px;
              :deep(.nancalui-input-number) {
                .nancalui-input__wrapper {
                  width: 100%;
                }
              }
            }
          }
        }
      }

      :deep(.nancalui-form__label) {
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }

      :deep(.nancalui-form__item--horizontal) {
        position: relative;
        display: flex;
        width: 100%;
        margin-bottom: 20px;

        .nancalui-form__control {
          width: calc(100% - #{$labelWidth});
          margin-left: 0 !important;
        }
      }
    }
  }
</style>
