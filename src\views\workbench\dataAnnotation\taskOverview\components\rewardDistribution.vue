<template>
    <div v-loading="state.loading" class="table-box">
        <CfTable :key="state.key" :isDisplayAction="false" :table-head-titles="state.tableHeadTitles" :paginationConfig="{
            total: state.pageInfo.total,
            pageSize: state.pageInfo.pageSize,
            currentPage: state.pageInfo.currentPage,
            onCurrentChange: (v) => {
                state.pageInfo.currentPage = v
                initTable()
            },
            onSizeChange: (v) => {
                state.pageInfo.pageSize = v
                initTable(true)
            },
        }" :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
        }">
            <template #money="{ row }">
                <div class="money-box">
                    <span class="money" v-if="!row.isEdit">{{ row.money }}</span>
                    <n-input-number v-if="row.isEdit" v-model="row.money" @blur="row.isEdit = false" />
                    <SvgIcon icon="icon-edit2" class="edit-icon" @click="row.isEdit = true" />
                </div>
            </template>
            <template #workload="{ row }">
                <div class="workload-box">
                    <span class="workload" v-if="!row.isWorkloadEdit">{{ row.workload }}</span>
                    <n-input-number v-if="row.isWorkloadEdit" v-model="row.workload"
                        @blur="row.isWorkloadEdit = false" />
                    <SvgIcon icon="icon-edit2" class="edit-icon" @click="row.isWorkloadEdit = true" />
                </div>
            </template>
        </CfTable>
    </div>
</template>

<script setup>
import { reactive, onMounted, defineProps } from 'vue'
import { workbenchModelList } from '@/api/dataManage.js'

const props = defineProps({
    info: {
        type: Object,
        default: {},
    },
})

const state = reactive({
    loading: false,
    tableData: { list: [] },
    pageInfo: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
    },
    tableHeadTitles: [
        { name: '姓名', prop: 'name', width: 400 },
        { name: '预计金额（元）', prop: 'money', slot: 'money', width: 400 },
        { name: '工作量', prop: 'workload', slot: 'workload', },
    ],
})

const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
        pageNum: state.pageInfo.currentPage,
        pageSize: state.pageInfo.pageSize,
        condition: {
            projectCode: props.info.code,
        },
    }
    state.loading = true
    // workbenchModelList(data)
    //     .then((res) => {
    //         state.loading = false
    //         if (res.success) {
    //             state.tableData = res.data
    //             state.pageInfo.total = res.data.total
    //         }
    //     })
    //     .catch(() => {
    //         state.tableData = { list: [] }
    //         state.loading = false
    //     })

    state.tableData = {
        list: [{
            name: '张三',
            money: 1000,
            workload: 10,
        },
        {
            name: '李四',
            money: 2000,
            workload: 20,
        },
        {
            name: '王五',
            money: 3000,
            workload: 30,
        }]
    }
    state.loading = false
}

onMounted(() => {
    initTable(true)
})
</script>

<style lang="scss" scoped>
.table-box {
    position: relative;
    width: 100%;
    height: 100%;

    .money-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .workload-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}
</style>