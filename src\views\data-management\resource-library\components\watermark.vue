<template>
  <div
    v-if="props.watermarkInfo.status === 'ENABLED'"
    class="mark"
    :style="{
      color:
        'rgb(' +
        (props.watermarkInfo.r + ',' + props.watermarkInfo.g + ',' + props.watermarkInfo.b) +
        ')',
      'font-size': props.watermarkInfo.fontSize + 'px',
      transform: 'rotate(' + props.watermarkInfo.rotation + 'deg)',
    }"
    >{{ props.watermarkInfo.textContent }}</div
  >
</template>

<script setup>
  const props = defineProps({
    watermarkInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  })
</script>

<style lang="scss" scoped>
  .mark {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 50%;
    min-width: 140px;
    height: 100px;
    margin: auto;
    color: #cfcfcf;
    font-size: 16px;
    text-align: center;
    word-break: break-all;
    transform: rotate(-30deg);
  }
</style>
