<template>
  <div v-loading="state.loading" class="full-page container">
    <div class="detail-model">
      <moduleName :info="{ name: state.targetTitle }" />
      <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
        <n-tab title="基本信息" id="base" />
        <n-tab title="字段信息" id="metaData" />
        <n-tab title="数据预览" id="dataPreview" />
      </n-tabs>
      <div v-if="state.activeName === 'base'" class="container-box-form">
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">指标模型中文名：</div>
              <div class="value">{{ state.ruleForm.cnName }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">指标模型英文名：</div>
              <div class="value">{{ state.ruleForm.name }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">指标模型分类：</div>
              <div class="value">{{ state.ruleForm.layerName }}</div>
            </div>
          </n-col>
          <n-col :span="8">
            <div class="label">
              <div class="name">最后修改时间：</div>
              <div class="value">{{ state.ruleForm.updateTime }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">创建人：</div>
              <div class="value">{{ state.ruleForm.createByName }}</div>
            </div>
          </n-col>
        </n-row>
        <n-row :gutter="30">
          <n-col :span="8">
            <div class="label">
              <div class="name">描述信息：</div>
              <div class="value">{{ state.ruleForm.description }}</div>
            </div>
          </n-col>
        </n-row>
      </div>
      <div v-if="state.activeName === 'metaData'" class="table" v-loading="state.loadingData">
        <n-public-table
          ref="publicTable"
          :table-head-titles="state.tableHeadTitles"
          :showPagination="false"
          :tableData="state.tableData"
          :tableHeight="state.tableHeight"
        />
      </div>
      <div v-if="state.activeName === 'dataPreview'" v-loading="state.loadingPreview">
        <n-public-table
          ref="publicTablePreview"
          :key="state.keyCunt"
          :table-head-titles="state.tableHeadTitlesPreview"
          :tableData="state.previewTableData"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          @tablePageChange="tablePageChange"
        />
      </div>
    </div>
    <div class="fixed-bottom">
      <n-button size="sm" variant="solid" color="primary" @click.stop.prevent="goBack"
        >返回</n-button
      >
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { ElMessage } from 'element-plus'
  export default {
    name: '',
    components: {},
    props: {},
    setup(props) {
      const store = useStore()
      const router = useRouter()
      const publicTable = ref()
      const publicTablePreview = ref()
      const state = reactive({
        buttonActive: 0,
        activeName: 'base',
        targetTitle: '',
        disabled: true,
        detailId: null,
        ruleForm: {
          projectCode: '',
          projectName: '',
          createByName: '',
          layerId: '',
          layerName: '',
          createTime: '',
          updateByName: '',
          updateTime: '',
          description: '',
          paramColumns: [], // 自定义参数
        },
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        tableHeadTitles: [
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '字段中文名' },
          { prop: 'name', name: '字段英文名' },
          { prop: 'fieldTypeName', name: '字段类型' },
          { prop: 'fieldLength', name: '字段长度' },
          { prop: 'fieldPrecision', name: '字段精度' },
        ],
        tableData: {},
        tableHeight: 400,
        keyCunt: 0,
        checkedItem: {},
        showEmpty: true,
        tableHeadTitlesPreview: [],
        loadingPreview: false,
        loadingData: false,
        previewTableData: {},
        tagList: [],
        colTableHeadTitles: [
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldTypeName', name: '字段类型' },
          { prop: 'fieldLength', name: '字段长度' },
          { prop: 'description', name: '描述信息' },
          { prop: 'tagInfos', name: '标签', slot: 'tagInfos', width: 288 },
        ],
        colTableData: {},
        tagTableHeight: 436,
        rowTableData: {},
        rowTableHeadTitles: [],
        batchMarkData: {
          filterType: 'allData',
          filterCondition: [
            // {
            //   colName: '',
            //   operator: '',
            //   value: '',
            //   colType: '',
            // },
          ],
        },
        allParamColumns: [],
        allTableData: [],
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
      })
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 302
        },
        // 获取模型详情
        getDetail(id) {
          state.loading = true
          api.model
            .getModalDetail({ id })
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                state.ruleForm = data
              }
            })
            .catch(() => {})
        },
        getMetaDataByModel(id) {
          state.loadingData = true
          api.model
            .getModeData({ id })
            .then((res) => {
              state.loadingData = false
              let { data, success } = res
              if (success) {
                res.data.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                state.tableData = { list: res.data }
              }
            })
            .catch(() => {})
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.getDataPreview(false)
        },
        // 获取批量打标过滤的元数据列表
        getMetadataList() {
          api.model
            .getModeData({
              id: state.detailId,
            })
            .then((res) => {
              if (res.success) {
                // 新增序号属性
                let _allParamColumns = []
                if (res.data && res.data.length) {
                  res.data.forEach((item) => {
                    _allParamColumns.push({
                      prop: item.name,
                      name: item.name,
                    })
                  })
                  state.allParamColumns = _allParamColumns
                  state.tableHeadTitlesPreview = [
                    { prop: 'number', name: '序号', width: 80 },
                  ].concat(state.allParamColumns)
                  let noShowParames = ['assetsContent', 'tagInfos', 'assetsIndex', 'ds']
                  state.allParamColumns.forEach((item) => {
                    if (noShowParames.includes(item.prop)) return
                    state.rowTableHeadTitles.push({
                      prop: item.prop,
                      name: item.prop,
                    })
                  })
                }
              }
            })
        },
        getDataPreview(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              // projectCode: toRef(store.state.user.currentProject, 'projectCode').value,
              projectCode: state.ruleForm.projectCode,
              modelName: router.currentRoute.value.query.targetName,
              // modelName: 'ci_tun',
              // projectCode: 'xaadybcdwebcxadfe',
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loadingPreview = true
          let allData = { list: [] }
          api.model
            .getDataWithProject(data)
            .then((res) => {
              state.loadingPreview = false
              let { success, data } = res
              if (success) {
                if (data.list && data.list.length > 0) {
                  state.tableHeadTitlesPreview = [
                    { prop: 'number', name: '序号', width: 80 },
                  ].concat(state.allParamColumns)
                  data.list.map((item, index) => {
                    let _itemKeys = []
                    Object.keys(item).forEach((_item) => {
                      _itemKeys.push(_item.toLowerCase())
                    })
                    let _object = {}
                    state.tableHeadTitlesPreview.forEach((key) => {
                      if (_itemKeys.includes(key.prop.toLowerCase())) {
                        _object[key.prop] = item[key.prop.toLowerCase()]
                      } else {
                        _object[key.prop] = null
                      }
                    })
                    _object['number'] = index + 1
                    allData.list.push(_object)
                  })
                  data.list = allData.list
                }
                state.keyCunt++
                state.previewTableData = data
              }
            })
            .catch(() => {
              state.loadingPreview = false
            })
        },
        //切换
        handleClick(id) {
          state.activeName = id
          switch (id) {
            case 'metaData':
              methods.setTableHeight()
              methods.getMetaDataByModel(state.detailId)
              break
            case 'dataPreview':
              methods.setTableHeight()
              methods.getDataPreview()
              break

            default:
              methods.getDetail(state.detailId)
          }
        },
        goBack() {
          router.go(-1)
        },
      }
      onMounted(() => {
        nextTick(() => {
          state.detailId = router.currentRoute.value.query.id
          state.targetTitle = router.currentRoute.value.query.targetTitle
          methods.getMetadataList()
          methods.getDetail(state.detailId)
        })
      })

      return {
        state,
        publicTable,
        publicTablePreview,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .full-page {
    .fixed-bottom {
      height: 60px;
      position: absolute;
      bottom: 0px;
      left: 0;
      right: 0;
      background-color: #fff;
      border-radius: 8px 8px 0px 0px;
      padding: 0 30px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .detail-model {
      color: #333;
      height: 100%;
      position: relative;
      padding: 20px;
      height: calc(100% - 59px);
      position: relative;
      border: 4px;
      border-radius: 4px;
      background-color: #fff;
      .base-tabs {
        // height: 100%;
        margin: 10px 0 17px 0;
      }
      .container-box-form {
        margin-top: 21px;
        .nancalui-row {
          margin-bottom: 8px;
        }
        .label {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .name {
            width: 108px;
            color: #666666;
            font-size: 12px;
            text-align: right;
          }
          .value {
            width: calc(100% - 72px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #000000;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
