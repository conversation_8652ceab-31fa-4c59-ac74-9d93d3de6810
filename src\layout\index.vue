<template>
  <div :class="classObj + ' app-wrapper'">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click.prevent="handleClickOutside"
    ></div>
    <template v-if="!isLzos">
      <!-- <img
        v-if="menuHidden"
        class="menu-img"
        src="@img/menu/menu-open.png"
        @click.stop.prevent="toggleMenuFn"
      /> -->
      <sidebar class="sidebar-container" />
    </template>
    <div
      :class="{
        'main-container': true,
        checked: menuHidden,
        isLzos: isLzos,
      }"
    >
      <div v-if="!isLzos" :class="menuHidden ? 'nav-bar checked' : 'nav-bar'">
        <navbar />
      </div>
      <Tag />
      <app-main />
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { Navbar, Sidebar, AppMain, Tag } from './components'
  import { storeStorage} from '@/utils/auth'

  export default {
    name: 'Layout',
    components: {
      Navbar,
      Sidebar,
      AppMain,
      Tag,
    },
    data() {
      return {
        isLzos: import.meta.env.VITE_APP_LZOS,
      }
    },
    // mixins: [ResizeMixin],
    computed: {
      ...mapState({
        menuHidden: (state) => state['app'].menuHidden,
        bodyClick: (state) => state['app'].bodyClick,
      }),
      sidebar() {
        return this.$store.state.app.sidebar
      },
      device() {
        return this.$store.state.app.device
      },
      fixedHeader() {
        return this.$store.state.settings.fixedHeader
      },
      classObj() {
        return {
          hideSidebar: !this.sidebar.opened,
          openSidebar: this.sidebar.opened,
          withoutAnimation: this.sidebar.withoutAnimation,
          mobile: this.device === 'mobile',
        }
      },
    },
    mounted() {
      if (localStorage.getItem(storeStorage) && sessionStorage.getItem('isNormalLogin') === '1') {
        this.$store.replaceState(
          Object.assign({}, this.$store.state, JSON.parse(localStorage.getItem(storeStorage))),
        )
      }
    },
    methods: {
      handleClickOutside() {
        this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
      },
      toggleMenuFn() {
        if (this.menuHidden) {
          this.$store.commit('app/TOGGLE_LEFT_MENU', false)
        } else {
          this.$store.commit('app/TOGGLE_LEFT_MENU', true)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/mixin.scss';
  @import '@/styles/variables.scss';

  .app-wrapper {
    @include clearfix;
    position: relative;
    width: 100%;
    height: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    position: absolute;
    top: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.3;
  }

  .menu-img {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 9;
    width: 22px;
    height: 52px;
    margin: auto;
    cursor: pointer;
  }

  .nav-bar {
    position: fixed;
    width: 100%;
    height: 50px;
    top: 0;
    right: 0;
    z-index: 100;
    transition: width 0.28s;
  }

  .tabs-container {
    height: 36px;
    background-color: #f7f8fa;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
