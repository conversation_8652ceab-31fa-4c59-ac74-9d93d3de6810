<template>
  <n-drawer
    v-model="visible"
    size="890"
    v-loading="isLoad"
    :closable="true"
    @close="onClose"
    title="接口详情"
  >
    <div class="drawer-body" v-loading="isLoad">
      <section class="container-section">
        <PageCon icon="icon-doc" title="基础信息">
          <div class="p-16">
            <n-form :model="baseForm" labelSuffix="：" autocomplete="off" label-width="100px">
              <n-form-item label="接口名称">
                <span> {{ baseForm.name }} </span>
              </n-form-item>
              <n-form-item label="调用地址">
                <span>{{ baseForm.url }}</span>
              </n-form-item>
              <n-form-item label="请求类型">
                <span> {{ baseForm.requestMethod }} </span>
              </n-form-item>
            </n-form>
          </div>
        </PageCon>
      </section>
      <section class="container-section pb-1">
        <pageCon icon="icon-doc" title="请求参数">
          <div class="p-16">
            <n-form :model="paramsForm" labelSuffix="：" autocomplete="off" label-width="100px">
              <n-form-item label="请求头">
                <div class="params-box">
                  <div
                    class="params-item"
                    :key="`questHead-${index}`"
                    v-for="(item, index) in paramsForm.headers"
                  >
                    <span> {{ item.filedName }} </span>
                    <span> {{ item.value }} </span>
                    <span> {{ item.description }} </span>
                  </div>
                </div>
              </n-form-item>
              <n-form-item label="请求参数">
                <div class="mt-5">
                  <div class="params-box mt-13">
                    <div
                      class="params-item"
                      :key="index"
                      v-for="(item, index) in paramsForm.requestForm"
                    >
                      <span> {{ item.filedName }} </span>
                      <span> {{ item.value }} </span>
                      <span> {{ item.description }} </span>
                    </div>
                  </div>
                </div>
              </n-form-item>
              <n-form-item label="响应体">
                <div class="mt-5">
                  <div class="params-box mt-13">
                    <n-tree class="params-tree" :data="paramsForm.responseBody" ref="responseBody">
                      <template #icon="{ nodeData, toggleNode }">
                        <span
                          v-if="nodeData.isLeaf"
                          style="margin: 0 6px 0 5px"
                          class="nancalui-tree-node__indent"
                        >
                          <svg
                            width="5"
                            height="5"
                            version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle r="2.5" fill="#C8C9CC" cx="2.5" cy="2.5" />
                          </svg>
                        </span>
                        <span
                          v-else
                          @click="
                            (event) => {
                              event.stopPropagation()
                              toggleNode(nodeData)
                            }
                          "
                        >
                          <svg
                            :style="{
                              transform: nodeData.expanded ? 'rotate(90deg)' : '',
                              marginLeft: '4px',
                              marginRight: '4px',
                              cursor: 'pointer',
                            }"
                            viewBox="0 0 1024 1024"
                            width="8"
                            height="8"
                          >
                            <path
                              d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                              fill="#8a8e99"
                            />
                          </svg>
                        </span>
                      </template>
                      <template #content="{ nodeData }">
                        <span> {{ nodeData.filedName }} </span>
                        <span class="ml-10"> {{ nodeData.type }} </span>
                        <span class="ml-10"> {{ nodeData.description }} </span>
                      </template>
                    </n-tree>
                  </div>
                </div>
              </n-form-item>
            </n-form>
          </div>
        </pageCon>
      </section>
    </div>
    <div class="drawerFooter">
      <n-button style="margin-right: 8px" @click="onClose">取消</n-button>
      <n-button variant="solid" @click="onDownLoad">下载接口非结构化数据</n-button>
    </div>
  </n-drawer>
</template>
<script setup>
  import { objToArray, downLoadFile } from '@/utils/index.js'
  import * as api from '@/api/index.js'
  const emit = defineEmits(['close'])
  const props = defineProps(['visible', 'id'])
  const baseForm = reactive({})
  const paramsForm = reactive({})

  const state = reactive({})

  const visible = ref(false)
  function onClose() {
    visible.value = false
    emit('close')
  }
  function onDownLoad() {
    api.default.documentManage
      .collectDownload({
        categoryId: props.id,
        url: window.location.origin + '/api/govern-document/',
      })
      .then((res) => {
        // const { name, file } = res
        // let fileName = ''
        // if (name?.indexOf("''") > -1) {
        //   fileName = name?.split("''")[1]
        // } else {
        //   fileName = name?.split('=')[1]
        //   fileName = fileName.replaceAll('"', '')
        // }
        let fileName = '非结构化数据采集新增接口.docx'
        const blob = new Blob([res])
        downLoadFile(blob, fileName)
      })
  }
  watch(
    () => props.visible,
    (val) => {
      visible.value = val
      getdetail()
    },
  )
  const responseBody = ref(null)
  const isLoad = ref(false)
  function getdetail() {
    try {
      isLoad.value = true
      api.default.documentManage
        .collectView({
          categoryId: props.id,
          url: window.location.origin + '/api/govern-document/',
        })
        .then((res) => {
          baseForm.name = res.data.name
          baseForm.url = res.data.urlPath
          // baseForm.url = res.data.urlPrefix + res.data.urlPath
          baseForm.requestMethod = res.data.requestMethod
          paramsForm.requestType = res.data.requestType
          paramsForm.headers = res.data.headers
          paramsForm.requestForm = res.data.requestForm
          paramsForm.responseBody = objToArray(res.data.responseBody)
          nextTick(() => {
            responseBody.value?.treeFactory.expandAllNodes()
          })
        })
    } finally {
      isLoad.value = false
    }
  }
</script>
<style lang="scss" scoped>
  .params-box {
    background: #f5f5f5;
    border-radius: 6px;
    padding: 16px;
    .ml-10 {
      margin-left: 5px;
    }
    .params-item {
      span {
        word-break: break-all;
        + span {
          margin-left: 10px;
        }
      }
      & + .params-item {
        margin-top: 8px;
      }
    }
  }
  :deep(.nl-tree) {
    background: transparent !important;
    .nl-tree-node-content-wrapper {
      color: rgba(0, 0, 0, 0.65) !important;
    }
  }
  .drawer-body {
    overflow: auto;
    height: calc(100% - 90px);
    padding: 0 20px;
  }
  :deep(.nancalui-drawer.nancalui-drawer--right) {
    overflow: hidden !important;
  }
  :deep(.nancalui-form__control .nancalui-form__control-container--horizontal) {
    display: block;
  }
</style>
