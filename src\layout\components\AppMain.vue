<template>
  <section
    :class="{
      'app-main': true,
      open: !menuHidden,
      isLzos: state.isLzos,
    }"
  >
    <router-view v-slot="{ Component }">
      <keep-alive :include="['MetadataMapList','offlineDevelopment']">
        <component
            :is="Component"
            :class="{
            'component-main': true,
          }"
        />
      </keep-alive>
    </router-view>
<!--    <template v-if="!state.isLzos">-->
<!--      <template v-for="(item, ind) in state.imgNum">-->
<!--        <img-->
<!--          class="staple"-->
<!--          src="@img/menu/staple.png"-->
<!--          :style="'top:' + (ind === 0 ? 56 : 56 + ind * 138) + 'px'"-->
<!--        />-->
<!--      </template>-->
<!--    </template>-->
  </section>
</template>

<script>
  import { computed, onMounted, reactive } from 'vue'
  import { useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  export default {
    name: 'AppMain',
    setup() {
      const route = useRoute()
      const store = useStore()
      const menuHidden = computed(() => store.state.app.menuHidden)

      const currentPath = computed(() => {
        return route.fullPath
      })
      const state = reactive({
        imgNum: 0,
        isLzos: import.meta.env.VITE_APP_LZOS,
      })
      onMounted(() => {
        let bodyHeight = document.body.offsetHeight
        state.imgNum = parseInt(bodyHeight / 138)
      })
      return { currentPath, menuHidden, state }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  div {
    height: 100%;
  }
  .app-main {
    position: relative;
    width: 100%;
    min-width: 1024px;
    height: calc(100% - $navbarHeight);
    min-height: calc(100vh - $navbarHeight);
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 3;
    border-radius: 0;
    .staple {
      position: absolute;
      top: 56px;
      left: 5px;
      z-index: 2;
      width: 18px;
      height: 8px;
    }
    //&.open {
    //  &:before {
    //    position: absolute;
    //    top: -4px;
    //    left: -4px;
    //    z-index: 2;
    //    width: 12px;
    //    height: 12px;
    //    background-color: #2a325a;
    //    content: '';
    //  }
    //  &:after {
    //    position: absolute;
    //    top: 0;
    //    left: 0;
    //    z-index: 3;
    //    width: 12px;
    //    height: 12px;
    //    background-color: #d8e0f4;
    //    border-radius: 10px 0 0 0;
    //    content: '';
    //  }
    //}
    &.isLzos {
      min-height: calc(100% - $navbarHeight);
      padding: 0;
      &:before,
      &:after {
        display: none;
      }
      .component-main {
        padding: 0;
        > :deep(.container) {
          height: 100%;
          padding: 0;
        }
      }
    }
    .component-main {
      position: relative;
      z-index: 1;
      box-sizing: border-box;
      height: 100%;
      background-color: #F0F2F5;
      border-right: none;
      border-bottom: none;
    }
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }
</style>

<style lang="scss">
  .el-popup-parent--hidden {
    .fixed-header {
      padding-right: 15px;
    }
  }
</style>
