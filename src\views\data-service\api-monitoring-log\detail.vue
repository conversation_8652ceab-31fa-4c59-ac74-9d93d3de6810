<template>
  <!-- 数据服务-API监控日志列表 -->
  <div :class="['api-management-list container', state.isLzos ? 'isLzos' : '']">
    <div class="list-box" v-loading="state.loading">
      <n-public-table
        :isDisplayAction="false"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        @tablePageChange="tablePageChange"
      >
        <template #pageTop>
          <div class="box-add">
            <search @handleSearch="handleSearch" />
          </div>
        </template>

        <template #status="{ editor }">
          <div>
            <div>
              <span
                :class="{
                  dot: true,
                  red: editor.row.status === 'FAILURE' || editor.row.status === 'STOP',
                  blue: editor.row.status === 'RUNNING_EXECUTION',
                }"
              ></span>
              {{ runState(editor.row.status) }}
            </div>
          </div>
        </template>
      </n-public-table>
    </div>

    <n-modal
      v-model="state.dialogVisible"
      title="查看日志"
      bodyClass="commonDialog"
      :close-on-click-overlay="false"
      :append-to-body="false"
      :draggable="false"
      width="680px"
      :before-close="closeDialog"
    >
      <div class="log">{{ state.log }} </div>

      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            size="sm"
            color="primary"
            variant="solid"
            :loading="state.submiting"
            @click.prevent="exportLog"
            >导出日志</n-button
          >
          <n-button size="sm" @click.prevent="closeDialog">取 消</n-button></n-modal-footer
        >
      </template>
    </n-modal>
    <div class="fixed-bottom">
      <n-button color="primary" size="sm" variant="solid" @click.prevent="goBack">返回</n-button>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import api from '@/api/index'
  import { useRouter, useRoute } from 'vue-router'
  import { checkCIp } from '@/utils/validate'

  export default {
    title: 'List',
    components: { search },
    props: {},
    setup() {
      const route = useRoute()
      const router = useRouter()
      const form = ref()
      const runState = (val) => {
        let text = ''
        switch (val) {
          case 'SUCCESS':
            text = '成功'
            break
          case 'RUNNING_EXECUTION':
            text = '运行中'
            break
          default:
            text = '失败'
            break
        }
        return text
      }
      // 获取当前组件实例
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 400,
        tableData: {},
        loading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'startTime', name: '开始时间' },
          { prop: 'endTime', name: '完成时间' },
          { prop: 'timeConsuming', name: '运行时长' },
          { prop: 'status', name: '运行状态', slot: 'status' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        apiId: '',
        log: '',
        startTime: null,
        endTime: null,
        keyword: null,
        status: '',
        authorizeId: '',
      })

      const methods = {
        goBack() {
          router.go(-1)
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 218 - 86
        },
        closeDialog() {
          state.dialogVisible = false
          state.authorizeData = {
            apiName: '',
            ips: [{ ip: '' }],
          }
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage

          let data = {
            condition: {
              apiId: route.query.id,
              auditStatus: state.status || null,
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              apiName: state.keyword || null,
            },

            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataService['apiMonitorLogSearch'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看日志
        searchLog(editor) {
          let { row } = editor
          state.dialogVisible = true
          state.log = row.log
          state.apiId = row.apiId
        },
        // 导出日志
        exportLog() {
          methods.savefiles(state.log, `${state.apiId}.txt`)
        },
        /**
         * @param data 需要保存的内容
         * @param name 保存的文件名
         */
        savefiles(data, name) {
          //Blob为js的一个对象，表示一个不可变的, 原始数据的类似文件对象，这是创建文件中不可缺少的！
          var urlObject = window.URL || window.webkitURL || window
          var export_blob = new Blob([data])
          var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
          save_link.href = urlObject.createObjectURL(export_blob)
          save_link.download = name
          save_link.click()
        },
        // 搜索
        handleSearch(data) {
          let { time, keyword, status } = data
          state.keyword = keyword ? keyword : null
          state.status = status
          if (status === 'all') {
            state.status = null
          }
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }
          state.pagination.currentPage = 1
          methods.initTable()
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable()
      })

      return {
        state,
        checkCIp,
        form,
        runState,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    position: relative;
    &.isLzos {
      padding: 0;
    }
    .fixed-bottom {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 60px;
      padding: 17px 30px;
      text-align: center;
      background-color: #fff;
      border-radius: 8px 8px 0px 0px;
    }
    .list-box {
      height: calc(100% - 60px);
      padding: 0 16px;
      background-color: #fff;
      border-radius: 4px;

      .box-add {
        .commonForm-search {
          padding: 16px 0;
        }
      }

      .left {
        .n-button {
          background: $themeBlue;
        }
      }

      .seeDetails {
        color: $themeBlue;
      }

      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #18ba72;
        border-radius: 50px;

        &.red {
          background: #f54446;
        }

        &.blue {
          background: $themeBlue;
        }
      }
    }
  }

  .el-dialog {
    .api-box-name {
      height: 20px;
      margin-bottom: 10px;
      color: #333333;
      font-size: 12px;
      line-height: 20px;
    }
    .header-box {
      display: flex;
      width: 100%;
      height: 42px;
      color: #000;
      font-size: 12px;
      line-height: 42px;
      background: #f7f8fa;
      border: 1px solid #e1e1e1;
      .number {
        width: 64px;
        text-align: center;
      }
      .ip-box {
        flex: 1;
      }
      .handle-box {
        width: 94px;
        text-align: center;
      }
    }
    .log {
      box-sizing: border-box;
      height: 310px;
      padding: 14px;
      overflow: auto;
      background: #f7f8fa;
      border-radius: 8px;
    }
  }
</style>
