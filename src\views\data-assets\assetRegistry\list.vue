<template>
  <!-- 资产管理-资产注册 -->
  <div class="asset-library-page container">
    <div class="statistics-table">
      <div class="statistics-left">
        <PublicLeftTree
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          @treeCheckNode="treeCheckNode"
        >
          <!-- <template #pageTop>
            <div class="title">场景目录树</div>
          </template> -->
        </PublicLeftTree>
      </div>
      <div class="statistics-right">
        <PublicTable
          ref="publicTable"
          :isDisplayAction="true"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :pagination="state.pagination"
          :exceptHeight="240"
          @tablePageChange="tablePageChange"
        >
          <template #pageTop>
            <div class="box-add">
              <div class="top-left"> </div>
              <el-form :inline="true" :model="state.filterSearch" class="commonForm">
                <el-form-item label="是否注册：">
                  <el-select
                    v-model="state.filterSearch.assetsRegistered"
                    size="small"
                    placeholder="请选择"
                    clearable
                    width="150"
                    @change="initTable"
                  >
                    <el-option
                      v-for="item in state.assetsRegisteredList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="">
                  <el-input
                    v-model="state.filterSearch.keyword"
                    size="small"
                    placeholder="请输入查询关键字"
                    clearable
                    @change="initTable"
                  >
                    <template #append>
                      <n-button @click.prevent="initTable">
                        <n-popover
                          class="item"
                          content="搜索"
                          trigger="hover"
                          :position="['bottom']"
                        >
                          <SvgIcon class="icon_search" icon="icon_search" />
                        </n-popover>
                      </n-button>
                    </template>
                  </el-input>
                </el-form-item>
              </el-form>
              <!-- <div class="top-right"> </div> -->
            </div>
          </template>
          <template #assetsRegistered="{ editor }">{{
            editor.row.assetsRegistered ? '是' : '否'
          }}</template>
          <template #qualityScore="{ editor }">
            {{ editor.row.qualityScore ? `${editor.row.qualityScore}分` : '--' }}
          </template>

          <template #status="{ editor }">
            <!-- <i :class="editor.row.status"></i> -->
            {{ state.AUDIT_EUM[editor.row.status] || '--' }}
          </template>
          <template #editor="{ editor }">
            <div class="edit-box">
              <n-button
                v-if="buttonAuthList.includes('assetsSupervise_assetRegistry_see')"
                code="assetsSupervise_assetRegistry_see"
                class="del-button has-right-border"
                variant="text"
                @click.prevent="checkThisTypeDetails(editor)"
                >查看
              </n-button>
              <n-button
                v-if="buttonAuthList.includes('assetsSupervise_assetRegistry_registryAndLogout')"
                code="assetsSupervise_assetRegistry_registryAndLogout"
                class="del-button has-right-border"
                variant="text"
                @click.prevent="
                  checkRegister(
                    editor,
                    (editor.row.status === 'AUDIT_SUCCESS' || editor.row.status === 'AUDIT_FAIL') &&
                      editor.row.assetsRegistered
                      ? false
                      : true,
                  )
                "
                >{{
                  (editor.row.status === 'AUDIT_SUCCESS' ||
                    editor.row.status === 'WAIT_AUDIT' ||
                    editor.row.status === 'AUDIT_FAIL') &&
                  editor.row.assetsRegistered
                    ? '注销'
                    : '注册'
                }}
              </n-button>
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <el-dialog
      class="largeDialog"
      :title="state.dialogTitle"
      v-model="state.dialogVisible"
      :close-on-click-modal="false"
      width="580px"
      :before-close="closeDialog"
    >
      <el-form
        ref="ruleForm"
        :model="state.ruleForm"
        :rules="state.rules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="模型名称-中文：" prop="cnName">
          <el-input
            v-model="state.ruleForm.cnName"
            maxlength="30"
            placeholder="请输入字段中文名称"
            disabled
          />
        </el-form-item>
        <el-form-item label="模型名称-英文：" prop="name">
          <el-input
            v-model="state.ruleForm.name"
            maxlength="30"
            placeholder="请输入字段中文名称"
            disabled
          />
        </el-form-item>
        <el-form-item label="选择业务域：" prop="bizDomainId">
          <!-- <el-cascader
              v-model="state.ruleForm.tagGroup"
              :options="state.options"
              :props="tagGroupProps"
              clearable
              :disabled="state.disabled"
            /> -->
          <el-tree-select
            v-model="state.ruleForm.bizDomainId"
            placeholder="请选择"
            :data="state.bizDomainIdType"
            :render-after-expand="false"
            clearable
            filterable
            node-key="id"
            :default-expanded-keys="state.defaultExpandedKeys"
            default-expand-all
          >
            <!--            <el-option-->
            <!--              v-for="item in state.bizDomainIdType"-->
            <!--              :key="item.id"-->
            <!--              :label="item.name"-->
            <!--              :value="item.id"-->
            <!--            />-->
          </el-tree-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <n-button
            color="primary"
            size="sm"
            variant="solid"
            :loading="state.loading"
            @click.prevent="registryModel"
            >保 存</n-button
          >
          <n-button size="sm" @click.prevent="closeDialog">取 消</n-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { reactive, onMounted, ref, getCurrentInstance, nextTick, toRefs } from 'vue'
  import { ElNotification, ElMessage } from 'element-plus'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { checkCName, checkName } from '@/utils/validate'

  export default {
    name: 'AuthorizedPersonnel',
    components: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)

      // const buttonAuthList = [
      //   'assetsSupervise_assetRegistry_registryAndLogout',
      //   'assetsSupervise_assetRegistry_see',
      // ]
      const router = useRouter()
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/assetRegistry/${name}.png`, import.meta.url).href //本地文件路径
      }
      // 获取当前组件实例
      // const { proxy } = getCurrentInstance()
      const publicTable = ref()
      const state = reactive({
        defaultExpandedKeys: [], //默认展开的节点数组
        AUDIT_EUM: {
          CREATED: '未审批',
          WAIT_AUDIT: '审核中',
          // PUBLISHED: '已发布',
          AUDIT_SUCCESS: '审核成功',
          AUDIT_FAIL: '审核失败',
        },
        treeData: [],
        treeAttrData: {
          showLeftIcon: true,
          // showCheckbox: true,
          showControl: false,
          parentControl: '',
          childControl: '',
          isHideSearch: false,
        },
        defaultProps: {
          children: 'name',
          label: 'label',
        },
        dialogTitle: '资产注册',
        dialogVisible: false,
        loading: false,
        disabled: false,
        registryModelId: null, //当前注册的模型id
        ruleForm: {
          cnName: '',
          name: '',
          bizDomainId: '',
          status: 'CREATED',
        },
        options: [], // 级联数据
        bizDomainIdType: [],
        rules: {
          bizDomainId: [{ required: true, message: '请选择标签', trigger: 'change' }],
          cnName: [{ required: true, validator: checkCName, trigger: 'blur' }],
          name: [{ required: true, validator: checkName, trigger: 'blur' }],
        },
        formData: {
          projectCode: '', // 选中场景
        },
        // 统计的所有类
        statisticalDataType: [
          {
            title: 'ODS', // 名称
            sum: 0,
            number: 0, // 数量
            icon: getAssetsImages('ODS@2x'),
            icon_bg: getAssetsImages('ODS_bg@2x'),
          },
          {
            title: 'DWD',
            sum: 0,
            number: 0,
            icon: getAssetsImages('DWD@2x'),
            icon_bg: getAssetsImages('DWD_bg@2x'),
          },
          {
            title: 'DWS',
            sum: 0,
            number: 0,
            icon: getAssetsImages('DWS@2x'),
            icon_bg: getAssetsImages('DWS_bg@2x'),
          },
          {
            title: 'ADS',
            sum: 0,
            number: 0,
            icon: getAssetsImages('ADS@2x'),
            icon_bg: getAssetsImages('ADS_bg@2x'),
          },
        ],
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'layerName', name: '模型层' },
          { prop: 'bizDomainName', name: '业务域' },
          { prop: 'projectName', name: '场景' },
          { prop: 'description', name: '描述信息' },
          { prop: 'dataCount', name: '数据条数' },
          { prop: 'qualityScore', name: '质量评分', slot: 'qualityScore' },

          // { prop: 'tagDataCount', name: '打标数量' },
          { prop: 'assetsRegistered', name: '是否注册', slot: 'assetsRegistered' },
          { prop: 'status', name: '审批状态', slot: 'status' },
          { prop: 'auditComment', name: '备注' },
        ],
        pagination: {
          pageSizes: [10, 20, 50], // 每次展示条数的可配置项
          layout: 'total,prev,pager,next,sizes, jumper',
          currentPage: 1,
          pageSize: 12,
        },
        filterSearch: {
          keyword: '',
          assetsRegistered: '',
        },
        assetsRegisteredList: [
          { name: '全部', id: '' },
          { name: '是', id: true },
          { name: '否', id: false },
        ],
        project_options: [],
      })
      const { proxy } = getCurrentInstance()
      const methods = {
        //获取业务域下拉
        getSelectData() {
          const params = {
            name: state.ruleForm.bizDomainId,
          }
          sceneManage.searchTreeList({}).then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.defaultExpandedKeys = []
                state.bizDomainIdType = methods.reformData(data)
                data[0].children?.forEach((item) => {
                  state.defaultExpandedKeys.push(item.id)
                })
              }
            }
          })
        },
        // 对tree数据改造
        reformData(data) {
          data.forEach((val) => {
            val.label = val.name
            val.value = val.id
            if (val.children) {
              this.reformData(val.children)
            }
          })
          return data
        },
        // 获取场景列表
        getProjectList() {
          return new Promise((reslove, reject) => {
            api.project
              .getMyProjectList()
              .then((res) => {
                reslove(res)
              })
              .catch((error) => {
                reject(error)
              })
          })
        },

        checkRegister(editor, isRegister = true) {
          const { row } = editor
          if (!isRegister) {
            //注销
            proxy.$MessageBoxService.open({
              title: '提示',
              content: '该数据注册已审批，是否取消注册该数据',
              save: () => {
                let params = {
                  auditType: 'CANCEL_REGISTER',
                  bizDomainId: row.bizDomainId,
                  id: row.id,
                  status: row.status,
                }
                api.assets.cancelAssets(params).then((res) => {
                  const { success } = res
                  if (success) {
                    ElMessage({
                      type: 'success',
                      message: '已发起注销审批',
                    })
                    state.formData.bizDomainId = ''
                    methods.initTable(false)
                  }
                })
              },
            })
          } else {
            if (row.status !== 'WAIT_AUDIT') {
              state.registryModelId = row.id
              state.ruleForm.name = row.name
              state.ruleForm.cnName = row.cnName
              state.ruleForm.status = row.status
              state.ruleForm.bizDomainId = ''
              if (row.bizDomainId) {
                state.disabled = true
                state.ruleForm.bizDomainId = row.bizDomainId
              }
              state.dialogVisible = true
            } else {
              proxy.$MessageBoxService.open({
                title: '提示',
                content: `该数据${
                  (row.status === 'AUDIT_SUCCESS' ||
                    row.status === 'WAIT_AUDIT' ||
                    row.status === 'AUDIT_FAIL') &&
                  row.assetsRegistered
                    ? '注销'
                    : '注册'
                }正在审批中，不能重复${
                  (row.status === 'AUDIT_SUCCESS' ||
                    row.status === 'WAIT_AUDIT' ||
                    row.status === 'AUDIT_FAIL') &&
                  row.assetsRegistered
                    ? '注销'
                    : '注册'
                }`,
                save: () => {},
              })
            }
          }
        },
        // 注册资产
        registryModel() {
          const params = {
            status: state.ruleForm.status,
            bizDomainId: state.ruleForm.bizDomainId,
            id: state.registryModelId,
          }
          state.loading = true
          proxy.MessageBoxService.open({
            title: '提示',
            content: '已发起注册审批，审批通过后可注册为资产',
            save: () => {
              api.assets
                .registerAssets(params)
                .then((res) => {
                  state.loading = false
                  const { success } = res
                  if (success) {
                    state.dialogVisible = false
                    methods.initTable(false)
                  }
                })
                .catch((err) => (state.loading = false))
            },
          })
        },
        // 关闭弹框
        closeDialog() {
          state.dialogVisible = false
        },
        // 初始化表格
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              name: state.filterSearch.keyword || null,
              assetsRegistered: state.filterSearch.assetsRegistered,
              projectCode: state.formData.projectCode,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }

          api.assets
            .getAssetsLibraryModelList(data)
            .then((res) => {
              if (res.success) {
                res.data.list.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                  })
                })

                if (res.data && res.data.list) {
                  publicTable.value.initTableData(res.data)
                } else {
                  publicTable.value.initTableData({ list: [] })
                }
              }
            })
            .catch(() => {
              publicTable.value.initFailed()
            })
        },
        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          let data = {}
          data.layerId = row.layerId
          data.layerName = row.layerName
          data.projectCode = row.projectCode
          data.projectName = row.projectName
          data.description = row.description
          router.push({
            name: 'assetRegistryDetail',
            query: {
              id: row.id,
              modelTitle: row.cnName || row.name,
              modelName: row.name,
              type: 'MODEL',
              ruleForm: JSON.stringify(data),
            },
          })
        },

        // 更换场景
        changeProject() {},

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取左侧树数据
        getTreeData(status) {
          sceneManage.searchSceneList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                // state.bizDomainId = data[0].id
                state.treeData = [
                  {
                    description: '全部',
                    id: null,
                    name: '全部',
                    children: data,
                  },
                ]
                // state.formData.projectCode = data[0].projectCode
                if (status) {
                  // methods.initTable()
                  //默认点击
                  nextTick(() => {
                    const firstNode = document.getElementsByClassName('el-tree-node')[0]
                    // firstNode.click()
                    methods.initTable()
                  })
                }
              }
            }
          })
        },
        treeCheckNode(data) {
          if (data.checkItem.id === 0 || data.checkItem.id === 'ROOT') {
            state.formData.projectCode = ''
          } else {
            state.formData.projectCode = data.checkItem.projectCode
          }

          methods.initTable()
        },
      }
      onMounted(() => {
        methods.getTreeData(true)
        methods.getSelectData()
      })

      return {
        buttonAuthList,
        state,
        publicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .asset-library-page {
    background-color: #f2f3f5;
    padding: 10px;
    height: 1;

    &-top {
      padding: 0 20px;
      background-color: #fff;
      // margin-bottom: 10px;
      border-radius: 4px 4px 0 0;
    }

    .statistics-table {
      border-radius: 4px;
      // padding: 0 20px;
      background-color: #fff;
      height: 100%;
      display: flex;
      padding-right: 20px;

      .statistics-left {
        .title {
          height: 32px;
          line-height: 28px;
          background: #f2f3f6;
          border-radius: 4px;
          border: 2px solid #ffffff;
          color: #333333;
          font-size: 12px;
          text-align: center;
          font-weight: bolder;
        }
      }
      .statistics-right {
        flex: 1;
        padding: 0 0 0 20px;
        min-width: 0; //解决左侧收起后 右侧数据不复原问题
        overflow: hidden;
      }
      .top-left {
        display: flex;
        align-items: center;
      }
      :deep(.el-pagination) {
        background-color: #fff;
      }
      i {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
      .CREATED {
        background-color: #447dfd;
      }
      .WAIT_AUDIT {
        background-color: #f5a623;
      }
      .AUDIT_SUCCESS {
        background-color: #18ba72;
      }
      .AUDIT_FAIL {
        background-color: #f54446;
      }
      .PUBLISHED {
        background-color: #8d7af8;
      }
    }

    .need_smallcube__title {
      display: block;
      height: 16px;
      line-height: 18px;
      border-left: 4px solid var(--themeBlue);
      padding-left: 6px;
      font-size: 16px;
      color: #333333;
      font-weight: bolder;
    }

    .top-change-project {
      height: 78px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dcdcdc;

      .top-change-project-left {
        display: flex;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: bolder;
          color: #333333;
          line-height: 24px;
        }
      }
    }

    .mid-statistics {
      padding: 18px 0 4px;
      display: flex;
      justify-content: space-between;
      width: 100%;

      .statistics-list {
        padding: 23px 30px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        width: calc(25% - 15px);
        height: 120px;
        background: linear-gradient(180deg, #f3f6f8 0%, #ffffff 100%);
        border: 1px solid #ebedf0;
        border-radius: 8px;
        position: relative;
        .icon_bg {
          width: 113px;
          height: 85px;
          display: none;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        .top {
          position: relative;
          padding: 0 20px 0 46px;
          box-sizing: border-box;
          margin-right: 20px;
          height: 80px;
          line-height: 80px;
          text-align: center;
          font-size: 16px;
          font-weight: 6500;
          border-right: 1px solid rgba(105, 122, 154, 0.1);
          img {
            width: 32px;
            height: 32px;
            position: absolute;
            left: 0;
            top: calc(50% - 18px);
          }
        }
        .content {
          min-width: 140px;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          flex-direction: column;
          flex-wrap: wrap;
          section {
            // flex-grow: 1;
            font-size: 12px;
            color: #666;
            span {
              margin-left: 10px;
              font-size: 22px;
              color: #000;
              font-weight: 500;
              min-width: 50px;
              text-align: center;
              display: inline-block;
            }
          }
        }
      }
      .statistics-list:nth-of-type(2) {
        background: linear-gradient(180deg, #edf7f0 0%, #ffffff 100%);
        border-color: #edf7f0;
      }
      .statistics-list:nth-of-type(3) {
        background: linear-gradient(180deg, #f4edfa 0%, #ffffff 100%);
        border-color: #f4edfa;
      }
      .statistics-list:last-of-type {
        background: linear-gradient(180deg, #edf4f7 0%, #ffffff 100%);
        border-color: #edf4f7;
      }
      .statistics-list:hover {
        background: linear-gradient(180deg, #f3f5f8 0%, #ffffff 100%);
        box-shadow: 0px 6px 16px 2px rgba(55, 99, 170, 0.06);
        border: 2px solid #ffffff;
      }
      .statistics-list:hover .icon_bg {
        // display: block;
      }
    }

    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
    }

    .edit-box {
      .del-button {
        &:first-child {
          border-left: 0 !important;
        }

        &:last-child {
          padding-right: 0;
        }
      }
    }
  }
  .largeDialog {
    .el-form {
      :deep(.el-cascader) {
        width: 100%;
      }
      :deep(.el-input.is-disabled .el-input__wrapper) {
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
      }
    }
  }

  :deep(.el-select) {
    width: 100%;
  }
</style>
