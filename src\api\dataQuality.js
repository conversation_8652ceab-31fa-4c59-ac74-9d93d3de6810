import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 分组树列表
function getRuleTreeList(data) {
  return request({
    url: `${URL.QUALITY}/group/tree/${data.type}`,
    method: 'get',
    params: {},
  })
}

// 分组树列表V2
function getRuleTreeListV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/group/tree`,
    method: 'post',
    data: data,
  })
}

// 分组创建
function addRuleTree(data) {
  return request({
    url: `${URL.QUALITY}/group/save`,
    method: 'post',
    data: data,
  })
}
// 分组创建V2
function addRuleTreeV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/group/save`,
    method: 'post',
    data: data,
  })
}

// 分组更新
function updateRuleTree(data) {
  return request({
    url: `${URL.QUALITY}/group/update`,
    method: 'post',
    data: data,
  })
}
// 分组更新V2
function updateRuleTreeV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/group/update`,
    method: 'post',
    data: data,
  })
}
// 分组删除
function delRuleTree(data) {
  return request({
    url: `${URL.QUALITY}/group/${data.id}`,
    method: 'get',
    params: {},
  })
}
// 分组删除V2
function delRuleTreeV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/group/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 数据质量-质量规则列表
function getRuleList(data) {
  return request({
    url: `${URL.QUALITY}/project-rule/page`,
    method: 'post',
    data: data,
  })
}

// 数据质量-质量规则列表V2
function getRuleListV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/search`,
    method: 'post',
    data: data,
  })
}

// 数据质量-新增规则
function saveRule(data) {
  return request({
    url: `${URL.QUALITY}/project-rule/save`,
    method: 'post',
    data: data,
  })
}

// 数据质量-新增规则V2
function saveRuleV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/save`,
    method: 'post',
    data: data,
  })
}

// 数据质量-更新规则
function updateRule(data) {
  let id = data.id
  delete data.id
  return request({
    url: `${URL.QUALITY}/project-rule/update/${id}`,
    method: 'post',
    data: data,
  })
}

// 数据质量-更新规则V2
function updateRuleV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/update`,
    method: 'post',
    data: data,
  })
}

// 数据质量-删除规则
function deleteRule(data) {
  return request({
    url: `${URL.QUALITY}/project-rule/delete/${data.id}`,
    method: 'post',
    data: {},
  })
}

// 数据质量-删除规则V2
function deleteRuleV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/delete/${data.id}`,
    method: 'post',
    data: {},
  })
}

// 数据质量-规则总数查询
function getRuleStatistics(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/statistics`,
    method: 'post',
    data: data,
  })
}
// 数据质量-SQL预览
function getRuleSqlPreview(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/sql/preview`,
    method: 'post',
    data: data,
  })
}

// 数据质量-获取规则
function getRuleDetail(data) {
  return request({
    url: `${URL.QUALITY}/project-rule/detail/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-获取规则V2
function getRuleDetailV2(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/get/${data.id}`,
    method: 'post',
    data,
  })
}

// 任务状态枚举
function getTaskStatus(data) {
  return request({
    url: `${URL.QUALITY}/task/status`,
    method: 'get',
    params: data,
  })
}

// 数据质量-任务列表
function getTaskList(data) {
  return request({
    url: `${URL.QUALITY}/task/model/page`,
    method: 'post',
    data: data,
  })
}

// 数据质量-保存任务
function saveTask(data) {
  return request({
    url: `${URL.QUALITY}/task/save`,
    method: 'post',
    data: data,
  })
}

// 数据质量-更新任务
function updateTask(data) {
  return request({
    url: `${URL.QUALITY}/task/update`,
    method: 'post',
    data: data,
  })
}

// 数据质量-删除任务
function deleteTask(data) {
  return request({
    url: `${URL.QUALITY}/task/delete/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-获取任务详情
function getTaskDetail(data) {
  return request({
    url: `${URL.QUALITY}/task/detail/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-任务列表
function getTaskResultList(data) {
  return request({
    url: `${URL.QUALITY}/score/model/page`,
    method: 'post',
    data: data,
  })
}

// 数据质量-质量得分分级统计
function getTaskScoreLevel(data) {
  return request({
    url: `${URL.QUALITY}/score/scoreLevel/${data.modelId}`,
    method: 'get',
    params: {},
  })
}
// 数据质量-质量得分分级统计
function getTaskScoreCount(data) {
  return request({
    url: `${URL.QUALITY}/score/${data.modelId}/${data.ruleId}/${data.columnName}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-质量得分分级统计
function getTaskScoreList(data) {
  return request({
    url: `${URL.QUALITY}/score/${data.modelId}/${data.ruleId}/${data.ruleType}/${data.columnId}/${data.ds}/${data.pageNum}/${data.pageSize}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-下线任务
function taskOffline(data) {
  return request({
    url: `${URL.QUALITY}/task/offline/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-上线任务
function taskOnline(data) {
  return request({
    url: `${URL.QUALITY}/task/online`,
    method: 'post',
    data: data,
  })
}

// 数据质量-运行任务
function taskRun(data) {
  return request({
    url: `${URL.QUALITY}/task/run/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-编辑任务调度配置
function scheduleUpdate(data) {
  return request({
    url: `${URL.QUALITY}/task/schedule/update`,
    method: 'post',
    data: data,
  })
}

// 数据质量-获取质量任务结果趋势图
function getTaskResultTrend(data) {
  return request({
    url: `${URL.QUALITY}/score/trend/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-获取质量任务结果最近得分列表
function getTaskLatestResult(data) {
  return request({
    url: `${URL.QUALITY}/score/latest`,
    method: 'post',
    data: data,
  })
}

// 数据质量-获取引用规则列表
function getCommonRuleList(data) {
  return request({
    url: `${URL.QUALITY}/common/rule/list`,
    method: 'get',
    params: data,
  })
}

//规则表达式测试
function getCommonRuleTest(data) {
  return request({
    url: `${URL.QUALITY}/common/rule/test`,
    method: 'post',
    data: data,
  })
}

// 数据质量-获取质量规则类型
function getRuleTypeList(data) {
  return request({
    url: `${URL.QUALITY}/task/rule/list/${data.id}`,
    method: 'get',
    params: {},
  })
}

// 数据质量-获取质量规则类型
function getRuleTypeListName(data) {
  return request({
    url: `${URL.QUALITY}/task/${data.id}/rule-standard`,
    method: 'get',
    params: {},
  })
}

// 数据质量-获取质量规则类型
function exportRule(data) {
  return request({
    url: `${URL.QUALITY}/score/export/${data.modelId}/${data.columnId}/${data.ruleId}/${data.ruleType}/${data.ds}`,
    method: 'get',
    responseType: 'blob',
    params: {},
  })
}

// 数据质量-业务域下质量任务平均得分统计
function getTaskAverageNum(data) {
  return request({
    url: `${URL.QUALITY}/score/${data.bidId}`,
    method: 'get',
    params: {},
  })
}
// 统计过程质量控制与分析-搜索设备
function getEqList(data) {
  return request({
    url: `${URL.SPC}/eq/search`,
    method: 'post',
    data: data,
  })
}
// 统计过程质量控制与分析-spc
function getSPC(data) {
  return request({
    url: `${URL.SPC}/spc/X-MR`,
    method: 'get',
    params: data,
  })
}

//获取质量规则模板列表
function getQualityRuleTemplateList(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/template/list`,
    method: 'post',
    data,
  })
}
//获取质量规则模板列表-分页
function getQualityRuleTemplatePage(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/template/page`,
    method: 'post',
    data,
  })
}
//质量规则新增
function qualityRuleSave(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/save`,
    method: 'post',
    data,
  })
}
//质量规则更新
function qualityRuleUpdate(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/update`,
    method: 'post',
    data,
  })
}
// 获取配置了规则的表列表
function getConfiguredRuleList() {
  return request({
    url: `${URL.QUALITY}/quality/rule/table/configured/list`,
    method: 'get',
  })
}
// 通过表ID获取规则列表
function getTableListById(id) {
  return request({
    url: `${URL.QUALITY}/quality/rule/listByTable`,
    method: 'get',
    params: {
      tableId: id,
    },
  })
}
// 新增质量任务
function createTask(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/create`,
    method: 'post',
    data,
  })
}
// 质量任务分页
function getTaskPage(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/page`,
    method: 'post',
    data,
  })
}
// 质量任务删除
function deleteTaskItem(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/delete`,
    method: 'post',
    data,
  })
}
// 质量任务运行
function taskExecute(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/execute`,
    method: 'post',
    data,
  })
}
// 质量任务上架
function qualityTaskOnline(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/online`,
    method: 'post',
    data,
  })
}

// 运行记录-质量任务
function qualityTaskRecord(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record`,
    method: 'post',
    data,
  })
}

// 运行记录-质量规则
function qualityRuleRecord(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/${data.id}`,
    method: 'post',
    data,
  })
}
// 运行记录-质量规则
function qualityTaskRecordLog(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/log/${data.id}`,
    method: 'post',
    data,
  })
}
// 质量任务下架
function qualityTaskOffline(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/offline`,
    method: 'post',
    data,
  })
}

// 质量任务详情
function getQualityTaskDetail(id) {
  return request({
    url: `${URL.QUALITY}/quality/task/get`,
    method: 'get',
    params: {
      id,
    },
  })
}
// 质量任务编辑
function qualityTaskUpdate(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/update`,
    method: 'post',
    data,
  })
}

// 获取可用的关联作业列表
function getAvailableJobList(params) {
  return request({
    url: `${URL.QUALITY}/quality/task/studio/job/list`,
    method: 'get',
    params,
  })
}

// 运行记录-质量规则
function qualityTaskRecordDetail(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/detail`,
    method: 'post',
    data,
  })
}

// 质量任务-问题数据明细分页查询
function getQualityTaskRecordDetail(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/problem/data`,
    method: 'post',
    data,
  })
}

// 质量任务-数据导出
function qualityTaskRecordExport(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/problem/data/download`,
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 运行记录-刷新
function qualityTaskRecordRefresh(data) {
  return request({
    url: `${URL.QUALITY}/quality/task/record/detail/${data.id}`,
    method: 'post',
    data,
  })
}

// 质量报告-质量评分
function qualityReportScope(data) {
  return request({
    url: `${URL.QUALITY}/quality/report/scope`,
    method: 'post',
    data,
  })
}
// 质量报告-六性评分
function qualityReportSixScope(data) {
  return request({
    url: `${URL.QUALITY}/quality/report/sexomorphic/dimension`,
    method: 'post',
    data,
  })
}

// 质量报告-历史评分趋势
function qualityReportHistoryScope(data) {
  return request({
    url: `${URL.QUALITY}/quality/report/history/average/scope`,
    method: 'post',
    data,
  })
}
// 质量报告-表
function qualityReportTable(data) {
  return request({
    url: `${URL.QUALITY}/quality/report/table`,
    method: 'post',
    data,
  })
}

// 质量报告-规则
function qualityReportTableRule(data) {
  return request({
    url: `${URL.QUALITY}/quality/report/table/rule`,
    method: 'post',
    data,
  })
}
// 关注动态
function dynamicAdd(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/attention/dynamic/add`,
    method: 'post',
    data,
  })
}
// 取关动态
function dynamicRemove(data) {
  return request({
    url: `${URL.QUALITY}/quality/rule/attention/dynamic/remove`,
    method: 'post',
    data,
  })
}

// 质量标准规范非结构化数据列表
function stdSpecDocList(data) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/list`,
    method: 'post',
    data,
  })
}

// 质量标准规范非结构化数据分页列表
function stdSpecDocPageList(data) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/page-list`,
    method: 'post',
    data,
  })
}

// 质量标准规范非结构化数据添加
function stdSpecDocAdd(data) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/add`,
    method: 'post',
    data,
  })
}

// 质量标准规范非结构化数据修改
function stdSpecDocUpdate(data) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/update`,
    method: 'post',
    data,
  })
}

// 质量标准规范非结构化数据删除
function stdSpecDocDelete(data) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/delete/${data.id}`,
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
  })
}

// 质量标准规范非结构化数据名称是否存在
function stdSpecDocExist(params) {
  return request({
    url: `${URL.QUALITY}/quality/std-spec-doc/check/std-spec-doc/exist`,
    method: 'get',
    params,
  })
}

// Word下载
function downWord(params) {
  return request({
    url: `${URL.QUALITY}/quality/report/export/word/${params.tableId}`,
    method: 'get',
    responseType: 'blob',
    params,
  })
}

// 数据治理项目列表
export const workbenchProjectList = (params) => {
  return request({
    url: `${URL.QUALITY}/governance/project/list`,
    method: 'get',
    params,
  })
}

export default {
  getRuleTreeList,
  getRuleTreeListV2,
  addRuleTree,
  addRuleTreeV2,
  updateRuleTree,
  updateRuleTreeV2,
  delRuleTree,
  delRuleTreeV2,
  getRuleList,
  getRuleListV2,
  saveRule,
  saveRuleV2,
  updateRule,
  updateRuleV2,
  getRuleDetail,
  getRuleDetailV2,
  getTaskStatus,
  deleteRule,
  deleteRuleV2,
  getRuleStatistics,
  getRuleSqlPreview,
  getTaskList,
  saveTask,
  updateTask,
  deleteTask,
  getTaskDetail,
  getTaskResultList,
  getTaskScoreLevel,
  getTaskScoreCount,
  getTaskScoreList,
  taskOffline,
  taskOnline,
  taskRun,
  scheduleUpdate,
  getTaskResultTrend,
  getTaskLatestResult,
  getCommonRuleList,
  getCommonRuleTest,
  getRuleTypeList,
  getRuleTypeListName,
  exportRule,
  getTaskAverageNum,
  getEqList,
  getSPC,
  getQualityRuleTemplateList,
  getQualityRuleTemplatePage,
  qualityRuleSave,
  qualityRuleUpdate,
  getConfiguredRuleList,
  getTableListById,
  createTask,
  getTaskPage,
  deleteTaskItem,
  taskExecute,
  qualityTaskRecord,
  qualityRuleRecord,
  qualityTaskRecordLog,
  qualityTaskRecordDetail,
  qualityTaskRecordRefresh,
  qualityReportScope,
  qualityReportSixScope,
  qualityReportHistoryScope,
  qualityReportTable,
  qualityReportTableRule,
  qualityTaskOnline,
  qualityTaskOffline,
  getQualityTaskDetail,
  qualityTaskUpdate,
  dynamicAdd,
  dynamicRemove,
  getAvailableJobList,
  getQualityTaskRecordDetail,
  qualityTaskRecordExport,
  stdSpecDocList,
  stdSpecDocPageList,
  stdSpecDocAdd,
  stdSpecDocUpdate,
  stdSpecDocDelete,
  stdSpecDocExist,
  downWord,
}
