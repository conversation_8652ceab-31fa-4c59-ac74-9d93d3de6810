<template>
  <div class="img-content">
    <!-- 显示pdf的canvas -->
    <div class="img-Preview">
      <img :src="option.value" alt="" srcset="" />
      <!--      <watermark :watermarkInfo="props.watermarkInfo" />-->
    </div>
  </div>
</template>

<script setup>
  import watermark from './watermark'
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
    watermarkInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
</script>
<style lang="scss" scoped>
  .img-content {
    position: relative;
    display: flex;
    flex: 1 0 0;
    align-self: stretch;
    justify-content: center;
    height: 100%;
    padding: 8px;
    background: #f6f7fb;
    border-radius: 6px;
    user-select: text;

    .img-Preview {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 700px;
      overflow-x: hidden;
      overflow-y: auto;
      > img {
        width: 100%;
      }
    }
  }
</style>
