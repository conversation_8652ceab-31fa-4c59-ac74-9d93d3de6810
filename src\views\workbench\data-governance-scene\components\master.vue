<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="false"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
      actionWidth="120"
    >
      <template #editor="{ row }">
        <n-button
          class="has-right-border"
          code="dataManagement_collectionMonitor_view"
          variant="text"
          :disabled="!row.detailsUrl"
          @click.prevent="downloadFile(row.detailsUrl, row.name)"
          >详情</n-button
        >
        <n-button
          class="has-right-border"
          code="dataManagement_collectionMonitor_view"
          variant="text"
          :disabled="!row.detailsUrl"
          @click.prevent="downloadFile(row.detailsUrl, row.name)"
          >下载</n-button
        >
      </template>
    </CfTable>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchMasterDataList } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    tableData: { list: [{ name: '车间' }] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'name', name: '主数据名称' },
      { prop: 'classification', name: '主数据分类' },
      { prop: 'screeningConditions', name: '筛选条件', width: 'auto' },
    ],
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchMasterDataList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 下载
  const downloadFile = (url, name) => {
    // 下载文件 包括图片 pdf等
    fetch(url).then((res) => {
      // 成功
      res.blob().then((blob) => {
        // 成功
        const link = document.createElement('a') // 创建a标签
        link.href = URL.createObjectURL(blob) // 创建下载链接
        link.download = name + url.substring(url.lastIndexOf('.')) // 下载文件名
        document.body.appendChild(link) // 插入body
        link.click() // 点击下载
        document.body.removeChild(link) // 移除body
      })
    })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
