<template>
  <section class="page-box">
    <div class="page-box-header">
      <img class="logo" src="/src/assets/img/home/<USER>" />
      <div class="info">
        <div class="myWait" @click="state.backLogShow=!state.backLogShow">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M6.43372 4.09999H13.6337C14.5004 4.09999 15.1671 3.39999 15.1671 2.56666C15.1671 1.69999 14.4671 1.03333 13.6337 1.03333H6.43372C5.56706 1.03333 4.90039 1.73333 4.90039 2.56666C4.90039 3.39999 5.60039 4.09999 6.43372 4.09999ZM6.56706 13.7333C6.06706 13.7333 5.63372 14.1333 5.63372 14.6667C5.63372 15.1667 6.03372 15.6 6.56706 15.6C7.06706 15.6 7.50039 15.2 7.50039 14.6667C7.50039 14.1667 7.06706 13.7333 6.56706 13.7333Z" fill="white"/>
            <path d="M16.8342 2.56665C16.8342 4.13332 15.9009 5.36665 14.8009 5.36665H5.20091C4.06758 5.36665 3.16758 4.09998 3.16758 2.56665C2.10091 2.63332 1.26758 3.53332 1.26758 4.59998V16.9333C1.26758 18.0667 2.20091 19 3.33424 19H16.6676C17.8009 19 18.7342 18.0667 18.7342 16.9333V4.59998C18.7342 3.53332 17.9009 2.63332 16.8342 2.56665ZM6.56758 16.7333C5.43424 16.7333 4.50091 15.8 4.50091 14.6667C4.50091 13.5333 5.43424 12.6 6.56758 12.6C7.70091 12.6 8.63424 13.5333 8.63424 14.6667C8.63424 15.8 7.70091 16.7333 6.56758 16.7333ZM9.00091 8.59998L6.40091 10.9667C6.30091 11.0667 6.13424 11.1333 6.00091 11.1333C5.83425 11.1333 5.70091 11.0667 5.56758 10.9333L4.13424 9.46665C3.90091 9.23332 3.90091 8.83332 4.13424 8.59998C4.36758 8.36665 4.76758 8.36665 5.00091 8.59998L6.00091 9.66665L8.16758 7.69998C8.40091 7.46665 8.80091 7.49998 9.03424 7.73332C9.26758 7.99998 9.23424 8.36665 9.00091 8.59998ZM15.5009 15.5H11.6342C11.1676 15.5 10.8009 15.1333 10.8009 14.6667C10.8009 14.2 11.1676 13.8333 11.6342 13.8333H15.5009C15.9676 13.8333 16.3342 14.2 16.3342 14.6667C16.3342 15.1333 15.9676 15.5 15.5009 15.5ZM15.5009 10.1667H11.6342C11.1676 10.1667 10.8009 9.79998 10.8009 9.33332C10.8009 8.86665 11.1676 8.49998 11.6342 8.49998H15.5009C15.9676 8.49998 16.3342 8.86665 16.3342 9.33332C16.3342 9.79998 15.9676 10.1667 15.5009 10.1667Z" fill="white"/>
          </svg>
          我的待办
          <span>{{state.backLog.tableData.length>=99?'99+':state.backLog.tableData.length}}</span>
          <svg :class="state.backLogShow?'active':''"  xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M8 11L4.25 6H11.75L8 11Z" fill="white"/>
          </svg>
        </div>
        <div class="avatar-container">
          <userDropdown />
        </div>
        <div class="star"><img class="pic" src="/src/assets/img/login/star-cf.png" />机密</div>
      </div>
    </div>
    <div class="page-box-content" :style="'transform:scale(' + state.scale + ')'">
      <div class="content">
        <img class="air" src="/src/assets/img/home/<USER>" />
        <img class="air-col" src="/src/assets/img/home/<USER>" />
        <div class="content-box">
          <div v-if="state.showSecondLink" class="second-box">
            <div class="second-box-label">
              <template v-for="(item, index) in state.secondList" :key="index">
                <div
                  class="second-box-label-card"
                  @click.prevent.stop="goItemFn(item.menuType, item.tripartiteJumpUrl)"
                >
                  <img class="second-box-label-card-img" :src="item.logoUrl" :title="item.name" />
                  <div class="second-box-label-card-name" :title="item.name">{{ item.name }}</div>
                </div>
              </template>
            </div>
          </div>
          <div class="label">
            <template v-for="(item, index) in state.menuList" :key="index">
              <div
                :class="item.checked ? 'label-box checked' : 'label-box'"
                @click="checkMenuFn(item)"
              >
                <img
                  class="label-box-icon"
                  :src="item?.landingPage?.logoUrl"
                  :title="item?.landingPage?.name"
                />
                <div class="label-box-name">{{ item?.landingPage?.name }}</div>
              </div>
            </template>
            <div
              v-if="roleCode === 1 || roleCode === 2 || roleCode === 3 || isAdmin"
              class="arrow"
              @click.prevent.stop="goJump('linkConfigManage')"
            >
              <SvgIcon icon="icon-home-set" class="icon" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <Backlog v-if="state.backLogShow" :backLog="state.backLog" />
  </section>
</template>
<script>
  import { reactive, computed, onMounted, getCurrentInstance, toRefs } from 'vue'
  import api from '@/api'
  import { getToken } from '@/utils/auth'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import userDropdown from '@/components/userDropdown'
  import { storeStorage, TokenKey } from '@/utils/auth'
  import { ElMessage } from 'element-plus'
  import Backlog from './components/Backlog.vue'

  export default {
    name: 'ConvergencePage',
    components: { userDropdown, Backlog },
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { roleCode, isAdmin } = toRefs(store.state.user)
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()

      const getAssetsImages = (num) => {
        return new URL(`/src/assets/img/home/<USER>//本地文件路径
      }

      const state = reactive({
        scale: 1,
        showSecondLink: false,
        dropdownStatus: false,
        projectList: [],
        backLogShow:false,
        backLog: {
          titles: [],
          tableData: [],
          waitAllData:[]
        },
        homeType: '',
        name: '',
        menuList: [],
        secondList: [],
        menuTreeList: [],
        whiteMenuList: [],
        proList: [
          {
            imgCode: getAssetsImages(1),
            appName: '数据采集',
            icon: 'icon-login-integration',
            code: 'dataIntegration',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(5),
            appName: '非结构化数据管理',
            icon: 'icon-login-target',
            code: 'unstructuredDataManager',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(2),
            appName: '数据开发',
            icon: 'icon-login-directory',
            code: 'dataDevelopmentManager',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(3),
            appName: '数据资产',
            icon: 'icon-login-assets',
            code: 'dataAssetsManager',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(4),
            appName: '数据服务',
            icon: 'icon-login-algorithm',
            code: 'dataServerManager',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(6),
            appName: '消息中心',
            icon: 'icon-login-system',
            code: 'systemManagement',
            isShow: false,
          },
          {
            imgCode: getAssetsImages(7),
            appName: '原数据中台',
            icon: 'icon-login-old-platform',
            code: 'oldPlatform',
            isShow: true,
          },
        ],
      })
      state.name = computed(() => {
        return store.state.user.name
      })
      state.homeType = computed(() => {
        return store.state.user.homeType
      })
      state.menuTreeList = computed(() => {
        return store.state.user.menuTreeList
      })

      // 获取待办数据
      const landingWaitFn = () => {
        api.system.landingWait({ waitProcessQueryBO: '' }).then((res) => {
          if (res.success) {
            state.backLog.tableData = []
            state.backLog.waitAllData = res.data.waitProcessList

            // 计算每个 title 的 count
            const titlesWithCount = res.data.titles.map((title) => {
              let count = 0

              if (title.code === 'ALL') {
                // ALL 统计所有 code 下 dataList 的总数
                count = state.backLog.waitAllData.reduce((total, parent) => {
                  return total + (parent.dataList ? parent.dataList.length : 0)
                }, 0)
              } else {
                // 根据 code 匹配对应的 dataList 总数
                const matchedData = state.backLog.waitAllData.find((parent) => parent.code === title.code)
                count = matchedData && matchedData.dataList ? matchedData.dataList.length : 0
              }

              return {
                ...title,
                count: count,
              }
            })

            state.backLog.titles = titlesWithCount

            // 构建表格数据
            state.backLog.waitAllData.forEach((parent) => {
              parent.dataList.forEach((item) => {
                state.backLog.tableData.push(item)
              })
            })
          }
        })
      }

      const methods = {
        goItemFn(status, url) {
          if (state.whiteMenuList.includes(url.split('#')[1]) || status === 'EXTERNAL_MENU') {
            window.open(url, '_blank')
          } else {
            ElMessage.error('无权限，请联系管理员')
          }
        },
        checkMenuFn(item) {
          if (item?.landingPage?.type === 'ALL') {
            methods.goJump('Entrance')
          } else {
            if (item.checked) {
              item.checked = false
              state.showSecondLink = false
              state.secondList = []
            } else {
              state.menuList.forEach((val) => {
                val.checked = false
              })
              item.checked = true
              state.showSecondLink = true
              // state.secondList = item.landingConfigList || []
              state.secondList =
                item?.landingConfigList?.filter((val) => val?.linkType === 'FUNCTION') || []
              // if (state.secondList.length === 0) {
              //   let url = item?.landingPage?.tripartiteJumpUrl
              //   if (url) {
              //     methods.goItemFn(url)
              //   }
              // }
            }
          }
        },
        goJump(name, query) {
          if (query) {
            router.push({ name: name, query })
          } else {
            router.push({ name: name })
          }
        },
        getAppList() {
          api.user.getAppList().then((res) => {
            const { data } = res
            state.proList = data
          })
        },
        async logout() {
          localStorage.removeItem(TokenKey)
          await store.dispatch('user/logout')
          router.push(`/login`)
        },
        // 去流程列表
        goProcessFn() {
          router.push(`/processList`)
        },
        // 选中的模块切换路由地址以及设置tag等
        async checkPro(code) {
          if (!localStorage.getItem(TokenKey)) {
            proxy.$message.warning('登录过期，请重新登录')
            return false
          }
          //跳转到老数据中台
          if (code === 'oldPlatform') {
            const res = await api.base.ssoSys()
            const { data } = res
            if (data) {
              window.open(data)
            }
            return false
          }
          let activeInfo = { activeMenu: '', activeName: '', code: '' }
          state.menuTreeList.forEach((val) => {
            if (val.code === code) {
              activeInfo = methods.getActiveInfoFn(val.children, activeInfo, 1)
            }
          })
          sessionStorage.setItem('checkedTagName', activeInfo.activeMenu)
          store.commit('user/SET_FIRST_MENU', code)
          store.commit('user/SET_ACTIVE_MENU', activeInfo.activeMenu)
          store.commit('user/SET_ACTIVE_MENU_CODE', activeInfo.code)
          setTimeout(() => {
            let tagList = sessionStorage.getItem('TAG_LIST_CF')
            if (tagList) {
              tagList = JSON.parse(tagList)
            } else {
              tagList = []
            }
            tagList = tagList.filter((val) => val.name !== activeInfo.activeMenu)
            tagList.unshift({
              title: activeInfo.activeName,
              name: activeInfo.activeMenu,
              icon: 'menu-' + activeInfo.code + '-tag',
              query: {},
              params: {},
            })
            sessionStorage.setItem('TAG_LIST_CF', JSON.stringify(tagList))
          }, 100)
          if (code === 'dataIntegration') {
            localStorage.showType = 'table'
            sessionStorage.setItem('projectSourceSwitchType', 1)
          }
          router.push({
            name: activeInfo.activeMenu,
          })
        },
        // 路由树匹配code和name对应
        menuTreeMateFn(menuTreeList, routerList, zIndex) {
          menuTreeList.forEach((item) => {
            if (item.resourceType === 'MENU') {
              if (item.code === 'admin_home') {
                store.commit('user/SET_HOME_TYPE', 1)
              } else if (item.code === 'business_home') {
                if (state.homeType > 2) {
                  store.commit('user/SET_HOME_TYPE', 2)
                }
              } else if (item.code === 'technology_home') {
                if (state.homeType > 3) {
                  store.commit('user/SET_HOME_TYPE', 3)
                }
              }
              routerList.forEach((val) => {
                if (val.code === item.code) {
                  item.routerName = val.name
                  item.icon = val?.meta?.icon || null
                }
                if (val.children) {
                  if (zIndex < 3) {
                    methods.menuTreeMateFn(menuTreeList, val.children, zIndex + 1)
                  }
                }
              })
              if (item.children && item.children[0]?.resourceType === 'MENU') {
                methods.menuTreeMateFn(item.children, routerList, zIndex + 1)
              }
            }
          })
          return menuTreeList
        },
        // 获取选择模块的菜单
        getActiveInfoFn(list, activeInfo, zIndex) {
          list.forEach((val) => {
            if (val.children && val.children[0] && val.children[0].resourceType === 'MENU') {
              methods.getActiveInfoFn(val.children, activeInfo, zIndex + 1)
            } else if (val.routerName && !activeInfo.activeMenu) {
              activeInfo.activeMenu = val.routerName
              activeInfo.activeName = val.name
              activeInfo.code = zIndex > 1 ? val.parentCode : val.code
            }
          })
          return activeInfo
        },
        // 获取浏览器缩放比例
        getZoom() {
          let ratio = 0,
            screen = window.screen,
            ua = navigator.userAgent.toLowerCase()

          if (window.devicePixelRatio !== undefined) {
            ratio = window.devicePixelRatio
          } else if (~ua.indexOf('msie')) {
            if (screen.deviceXDPI && screen.logicalXDPI) {
              ratio = screen.deviceXDPI / screen.logicalXDPI
            }
          } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
            ratio = window.outerWidth / window.innerWidth
          }

          if (ratio) {
            ratio = Math.round(ratio * 100)
          }
          if (ratio > 100) {
            state.scale = 100 / ratio + 0.1
            if (ratio >= 110) {
              state.scale = 100 / ratio
              if (window.innerWidth < 1024 && ratio < 150) {
                state.scale = 100 / ratio - 0.1
              }
            }
            if (ratio >= 150) {
              state.scale = 100 / ratio - 0.05
            }
          } else {
            state.scale = 100 / ratio
          }
          const screenWidth = window.screen.width
          const width = window.innerWidth
          if (screenWidth * (ratio / 100) >= 1920) {
            state.scale = ((width * (ratio / 100)) / 1920) * state.scale
          }
          if (screenWidth === 1600 && ratio <= 100) {
            state.scale = state.scale * 0.8
          }
          if (screenWidth === 1440 && ratio <= 100) {
            state.scale = state.scale * 0.72
          }
          if (screenWidth === 1366 && ratio <= 100) {
            state.scale = state.scale * 0.66
          }
          if (screenWidth === 1280 && ratio <= 100) {
            state.scale = state.scale * 0.7
          }
          if (screenWidth === 1098 && ratio <= 100) {
            state.scale = state.scale * 0.95
          }
          if (screenWidth === 1024 && ratio <= 100) {
            state.scale = state.scale * 0.65
          }
        },
      }
      onMounted(() => {
        landingWaitFn()
        const localhoststore = JSON.parse(localStorage.getItem(storeStorage)).user.menuCodeList

        router.getRoutes().forEach((val) => {
          if (localhoststore.includes(val.meta.code)) {
            state.whiteMenuList.push(val.path)
          }
        })
        // 匹配要展示的模块
        state.menuTreeList.forEach((val) => {
          state.proList.forEach((item) => {
            if (val.code === item.code) {
              item.isShow = true
            }
          })
        })
        // 对本地路由适配
        let menuTreeList = [...state.menuTreeList]
        let newMenuTreeList = methods
          .menuTreeMateFn(menuTreeList, router.options.routes, 1)
          .filter((val) => val.code !== 'home')
        store.commit('user/SET_MENU_TREE', newMenuTreeList)
        localStorage.setItem(storeStorage, JSON.stringify(store.state))
        methods.getZoom()
        window.addEventListener('resize', () => {
          methods.getZoom()
        })
        api.system.loginMenuList().then((res) => {
          state.loading = false
          if (res.success) {
            res.data.forEach((val) => {
              val.checked = false
            })
            state.menuList = res.data
          }
        })
      })

      return {
        state,
        roleCode,
        isAdmin,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .page-box {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(253deg, #36dcdc 0%, #00489a 81.96%);
    &-header {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px 20px 0;
      .logo {
        width: 350px;
        height: auto;
        margin-left: 10px;
      }
      .info {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        position: relative;
        gap: 35px;

        .myWait {
          display:flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size:18px;
          cursor:pointer;

          svg{
            margin-left:2px;
            margin-right:4px;
          }

          span{
            display: flex;
            height: 18px;
            line-height:18px;
            padding: 0px 4px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius:12px;
            background:#F63838;
            margin-left:2px;
            font-size:12px;
          }
          .active{
            transform:rotate(180deg);
          }
        }
        .star {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: #1c70a1;
          font-size: 16px;
          height: 22px;
          .pic {
            width: 18px;
            height: 18px;
            margin-right: 6px;
          }
        }
        .avatar-container {
          min-width: 32px;
          margin-right: 10px;
          .avatar-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 22px;
            margin: 0 auto;
            line-height: 22px;
            text-align: center;
            cursor: pointer;
            .icon-user {
              width: 16px;
              margin-right: 4px;
              color: #fff;
              font-size: 16px;
            }
            .name {
              position: relative;
              z-index: 2;
              max-width: 100px;
              overflow: hidden;
              color: #fff;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .icon-arrow {
              width: 8px;
              margin-left: 4px;
              color: #fff;
              font-size: 8px;
            }
          }
        }
      }
    }
    &-content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100vw;
      height: 100vh;
    }

    .air {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 580px;
      height: auto;
      margin: auto;
    }
    .air-col {
      position: absolute;
      bottom: -74px;
      left: -330px;
      width: 429px;
      height: auto;
    }
    .content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1420px;
      height: 740px;

      &-box {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        transform: scale(1);
        transition: all linear 0.3s;

        .second-box {
          width: 100%;
          position: absolute;
          left: 0;
          bottom: 166px;
          display: flex;
          justify-content: center;
          align-items: center;
          &-label {
            min-width: 186px;
            min-height: 119px;
            padding: 7px 24px;
            border-radius: 16px;
            border: 1px solid #ffffff;
            background: rgba(255, 255, 255, 0.45);
            backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: center;
            &-card {
              position: relative;
              width: 112px;
              cursor: pointer;
              margin: 0 12px;
              box-sizing: border-box;
              padding: 17px 0 4px 0;
              border-radius: 10px;
              &:hover {
                background: rgba(6, 88, 161, 0.3);
              }
              &-img {
                display: block;
                margin: 0 auto;
                width: 56px;
                height: 56px;
                border-radius: 8px;
                background-color: #64bfd5;
              }
              &-name {
                width: 100%;
                font-size: 14px;
                text-align: center;
                color: #ffffff;
                margin-top: 6px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        .label {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: auto;

          &-box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            width: 120px;
            height: 120px;
            margin: 0 25px;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
            border-radius: 16px;
            border: 1px solid transparent;
            cursor: pointer;
            &:hover,
            &.checked {
              border: 1px solid #fff;
              background: rgba(39, 241, 255, 0.75);
            }
            &-icon {
              width: 70px;
              height: 70px;
            }
            &-name {
              position: absolute;
              bottom: -34px;
              left: -6px;
              width: 132px;
              height: 24px;
              color: rgba(255, 255, 255, 0.65);
              font-weight: 500;
              font-size: 16px;
              line-height: 24px;
              text-align: center;
            }
          }
          .arrow {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 27px;
            color: #ffffff;
            backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid transparent;
            &:hover {
              border: 1px solid #fff;
              background: rgba(39, 241, 255, 0.75);
            }
          }
        }
      }
    }
  }
</style>
