<template>
  <div
    :class="
      'name ' +
      colorClass +
      (nodeInfo.checked ? ' checked' : '') +
      (nodeInfo.isMainBody ? ' isMainBody' : '') +
      (nodeInfo.isSee ? ' isSee' : '')
    "
  >
    <el-popover
      :disabled="nodeInfo.name.length < 10"
      effect="dark"
      trigger="hover"
      placement="bottom-start"
    >
      <template #reference>
        <!-- <div
          ><div v-if="nodeInfo?.nameArray[0]" class="text">{{ nodeInfo?.nameArray[0] }}</div>
          <div v-if="nodeInfo?.nameArray[1]" class="text">{{ nodeInfo?.nameArray[1] }}</div>
          <div v-if="nodeInfo?.nameArray[2]" class="text">{{ nodeInfo?.nameArray[2] }}</div></div
        > -->
        <div class="p">{{ nodeInfo.name }}</div>
      </template>
      <template #default>
        <div class="text">{{ nodeInfo.name }}</div>
      </template>
    </el-popover>
  </div>
</template>

<script>
  export default {
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        nodeInfo: {
          name: '',
          nameArray: [],
          checked: true,
        },
        graph: null,
        colorClass: '',
        edgColor: '',
        isGreen: false,
      }
    },
    mounted() {
      const node = this.getNode()
      this.getNodeColor(node?.data)
      this.graph = this.getGraph()
      node.getData().nameArray = this.getNodeTextArray(node.getData().name)
      this.nodeInfo = node.getData()
      let allBorder = this.graph.getOutgoingEdges(node) // 获取边
      allBorder?.map((item) => {
        item.attr('line/stroke', this.edgColor)
      })
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        current.nameArray = this.getNodeTextArray(current.name)
        this.nodeInfo = current
        this.getNodeColor(this.nodeInfo?.data)
      })
    },
    methods: {
      // 获取node的文字数组
      getNodeTextArray(name) {
        let arr = []
        if (name.length > 5) {
          arr.push(name.slice(0, 3))
          arr.push(name.slice(3, 8))
        } else {
          arr.push(name)
          arr.push('')
        }
        if (name.length >= 11) {
          arr.push(name.slice(8, 10) + '...')
        } else {
          arr.push(name.slice(8, 10))
        }
        return arr
      },
      // 获取Node颜色
      getNodeColor(node) {
        switch (node.type) {
          case 'DATASOURCE':
            this.colorClass = 'yellow'
            this.edgColor = node.checked ? 'rgba(240,178,66,0.4)' : 'rgba(240,178,66,0.1)'
            break
          case 'DATABASE':
            this.colorClass = 'purple'
            this.edgColor = node.checked ? 'rgba(146,97,255,0.5)' : 'rgba(146,97,255,0.1)'
            break
          case 'TABLE':
            this.colorClass = 'blue'
            this.edgColor = node.checked ? 'rgba(24,160,251,0.5)' : 'rgba(24,160,251,0.1)'
            break
          default:
            this.colorClass = 'green'
            this.edgColor = node.checked ? 'rgba(0, 197, 221, 0.14)' : 'rgba(0, 197, 221, 0.1)'
            break
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .tool-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    font-size: 12px;
    span {
      display: block;
    }
  }
  .name {
    width: 74px;
    height: 74px;
    line-height: 74px;
    border-radius: 50%;
    padding: 0 6px;
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    opacity: 0.1;
    .text {
      font-size: 12px;
      color: #fff;
      line-height: 18px;
      font-weight: bold;
      text-align: center;
    }
    .p {
      font-size: 12px;
      color: #fff;
      line-height: 18px;
      font-weight: bold;
      text-align: center;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:before {
      content: '';
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      background-color: transparent;
      border-radius: 50%;
      position: absolute;
      left: -10px;
      top: -10px;
      transition: all linear 0.1s;
      &.isSee {
        opacity: 0;
        &.isMainBody {
          opacity: 1;
        }
      }
    }
    :deep(.el-tooltip__trigger) {
      position: relative;
      z-index: 2;
    }
    &.yellow {
      width: 84px;
      height: 84px;
      line-height: 90px;
      background: linear-gradient(165deg, #eab33f 0%, #cc8635 100%);
      box-shadow: 0 0 10px 8px rgba(255, 210, 121, 0.14), inset 0 1px 6px 2px #fde77c;
      .text,
      .p {
        font-size: 14px;
      }
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #fde77c;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(238, 175, 65, 0.2);
        }
      }
    }
    &.purple {
      background: linear-gradient(165deg, #bb5afa 0%, #9733e5 100%);
      box-shadow: 0 0 10px 8px rgba(200, 104, 255, 0.14), inset 0 1px 6px 2px #e898ff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #e898ff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(146, 97, 255, 0.2);
        }
      }
    }
    &.blue {
      background: linear-gradient(165deg, #2b84fb 0%, #4752e1 100%);
      box-shadow: 0 0 10px 8px rgba(46, 131, 250, 0.14), inset 0 1px 6px 2px #4aacff;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #4aacff;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(53, 115, 242, 0.2);
        }
      }
    }
    &.green {
      background: linear-gradient(165deg, #00c8df 0%, #0891b0 100%);
      box-shadow: 0 0 10px 8px rgba(0, 197, 221, 0.14), inset 0 1px 6px 2px #00d8dd;
      &.isSee {
        box-shadow: inset 0 1px 6px 2px #00d8dd;
      }
      &:hover:not(.isSee),
      &.isMainBody {
        &:before {
          background-color: rgba(2, 190, 214, 0.2);
        }
      }
    }
    &.checked {
      opacity: 1;
    }
  }
</style>
