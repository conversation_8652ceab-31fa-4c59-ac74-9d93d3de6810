<template>
  <div class="container">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">模板名称：</span>
          <n-input
            v-model="tableState.filterSearch.name"
            placeholder="请输入模板名称"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>模板分类</div>
          <SvgIcon class="nc-right nc-m-r-12" icon="icon-tree-add2" size="16" @click="onClassAdd" />
        </div>
        <div class="class-list nc-m-t-10">
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="classState.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="onSearchClass"
          />
          <div class="nc-m-t-8 class-list-wrap class-tree">
            <n-tree :data="classState.data" ref="treeRef" @node-click="onClassClick">
              <template #content="{ nodeData }">
                <SvgIcon class="tree-icon" v-if="nodeData.children?.length" icon="open-file" />
                <SvgIcon class="tree-icon" v-else icon="icon-file-tree" />
                <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
                <div class="tree-tool" v-if="nodeData.id !== 'all'">
                  <SvgIcon
                    class="tree-icon-tool"
                    icon="icon-new-edit"
                    size="16"
                    @click.stop="onClassEdit(nodeData)"
                  />
                  <SvgIcon
                    class="tree-icon-tool"
                    icon="filter-cut"
                    size="16"
                    @click.stop="onClassDelete(nodeData)"
                  />
                </div>
              </template>
              <template #icon="{ nodeData, toggleNode }">
                <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
                <span
                  v-else
                  @click="
                    (event) => {
                      event.stopPropagation()
                      toggleNode(nodeData)
                    }
                  "
                >
                  <SvgIcon
                    v-if="nodeData.expanded"
                    class="nancalui-tree-switch"
                    icon="tree-contract-new"
                  />
                  <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
                </span>
              </template>
            </n-tree>
            <!-- <div
              :class="{ 'class-list-item': true, 'is-active': classState.editData.id === item.id }"
              v-for="item in classState.data"
              :key="item.id"
              @click="onClassClick(item)"
            >
              <SvgIcon icon="open-file" class="nc-m-r-4" />
              <div class="class-con">
                <n-tooltip :content="item.name">
                  <div class="nc-line-1 class-label">{{ item.name }}</div>
                </n-tooltip>
                <div class="class-tools">
                  <SvgIcon
                    class="nc-m-r-8"
                    icon="icon-new-edit"
                    size="16"
                    @click.stop="onClassEdit(item)"
                  />
                  <SvgIcon icon="filter-cut" size="16" @click.stop="onClassDelete(item)" />
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onTemplateAdd" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          新建模板</n-button
        >
        <div class="table" v-loading="tableState.isLoad">
          <CfTable
            @sort-change="sortFn"
            actionWidth="160"
            :table-head-titles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="onTemplateView(row)">查看</n-button>
              <n-button variant="text" color="primary" @click="onTemplateEdit(row)">编辑</n-button>
              <n-button variant="text" color="primary" @click="onTemplateDelete(row)"
                >删除</n-button
              >
            </template>
          </CfTable>
        </div>
      </div>
      <n-modal
        v-model="classState.open"
        title="新建模板分类"
        class="largeDialog has-top-padding"
        width="560px"
        :close-on-click-overlay="false"
        @close="classState.open = false"
      >
        <n-form :data="classState.editData" :rules="classState.rules" ref="classFormRef">
          <n-form-item field="name" label="分类名称">
            <n-input v-model="classState.editData.name" placeholder="" maxLength="200" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-modal-footer>
            <n-button @click="classState.open = false">取消</n-button>
            <n-button color="primary" variant="solid" @click="onClassSave">确定</n-button>
          </n-modal-footer>
        </template>
      </n-modal>
    </section>
  </div>
</template>
<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import { getCurrentInstance, reactive } from 'vue'
  import api from '@/api/index'
  import { cloneDeep } from 'lodash'
  const { proxy } = getCurrentInstance()
  const router = useRouter()
  const route = useRoute()

  const classState = reactive({
    rules: {
      name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    },
    treeSearchText: '',
    open: false,
    editData: {},
    data: [],
  })
  const tableState = reactive({
    tableList: [],
    filterSearch: {
      name: '',
    },
    searchData: {
      name: '',
    },

    tableHeadTitles: [
      { prop: 'name', name: '模板名称' },
      { prop: 'className', name: '模板分类' },
      { prop: 'templateType', name: '模版类型' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', sortable: 'custom' },
    ],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    },
    isLoad: false,
  })
  // 排序
  function sortFn(data) {
    const { order, prop } = data
    const sortConditions = [
      {
        fieldName: prop,
        sort: order === 'descending' ? 'DESC' : 'ASC',
      },
    ]
    onSearch(false, sortConditions)
  }
  function startSearch() {
    tableState.searchData.name = tableState.filterSearch.name
    onSearch(true)
  }
  // 查询
  function onSearch(init = true, sortConditions) {
    if (init) {
      tableState.pagination.currentPage = 1
    }
    tableState.isLoad = true
    api.documentManage
      .templateListPage({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          classId: classState.editData.id === 'all' ? '' : classState.editData.id,
          name: tableState.searchData.name,
        },
        sortConditions: sortConditions,
      })
      .then((res) => {
        tableState.tableList = res.data.list
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
        router.replace({
          query: {
            ...router.currentRoute.value.query,
            currentPage: tableState.pagination.currentPage,
            pageSize: tableState.pagination.pageSize,
          },
        })
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  // 重置
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      name: '',
    }
    tableState.searchData = {
      name: '',
    }
    classState.editData = {}
    onSearch(true)
  }
  // 选择分类
  function onClassClick(row) {
    classState.editData = row
    onSearch()
  }
  // 新增分类
  function onClassAdd() {
    classState.open = true
    classState.editData = {
      name: '',
    }
  }
  // 编辑分类
  function onClassEdit(row) {
    classState.open = true
    classState.editData = cloneDeep(row)
  }
  // 删除分类
  async function onClassDelete(row) {
    const res = await api.documentManage.templateClassDeleteCheck({ id: row.id })
    if (res.success) {
      proxy.$MessageBoxService.open({
        title: '是否确认该条分类',
        content: '删除后将不可恢复',
        save: () => {
          api.documentManage.templateClassDelete({ id: row.id }).then((res) => {
            if (res.success) {
              onSearchClass()
              proxy.$message.success('删除成功！')
            }
          })
        },
      })
    }
  }
  // 保存分类
  const classFormRef = ref(null)
  function onClassSave() {
    classFormRef.value.validate().then((valid) => {
      if (valid) {
        const apiUrl = classState.editData.id ? 'templateClassEdit' : 'templateClassSave'
        api.documentManage[apiUrl](classState.editData).then((res) => {
          if (res.success) {
            classState.open = false
            classState.treeSearchText = ''
            proxy.$message.success(classState.editData.id ? '保存成功' : '新增成功')
            onSearchClass()
          }
        })
      }
    })
  }
  // 搜索分类
  const treeRef = ref(null)
  function onSearchClass() {
    api.documentManage.templateClassList({ name: classState.treeSearchText }).then((res) => {
      classState.data = [
        {
          id: 'all',
          name: '全部',
          selected: true,
          expanded: true,
          children: res.data,
        },
      ]
    })
  }
  // 新增模板
  function onTemplateAdd() {
    router.push({ name: 'templateAdd', query: { classId: classState.editData.id } })
  }
  // 编辑模板
  function onTemplateEdit(row) {
    router.push({ name: 'templateEdit', query: { id: row.id } })
  }

  // 预览模板
  function onTemplateView(row) {
    router.push({ name: 'templatePreview', query: { id: row.id } })
  }
  // 删除模板
  function onTemplateDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条模板',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.templateDelete({ id: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            ElNotification({
              title: '提示',
              message: '操作成功！',
              type: 'success',
            })
          }
        })
      },
    })
  }
  function refreshData() {
    onSearchClass()
    onSearch(false)
    // const { currentPage, pageSize } = router.currentRoute.value.query
    // if (currentPage && pageSize) {
    //   tableState.pagination.currentPage = Number(currentPage)
    //   tableState.pagination.pageSize = Number(pageSize)
    //   onSearch(false)
    // } else {
    //   onSearch()
    // }
  }
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        refreshData()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true }, // { immediate: true } 表示立即执行一次
  )
  onMounted(() => {
    refreshData()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    width: 100%;
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .class-list {
    width: 100%;
    padding: 12px;
    .class-list-wrap {
      height: calc(100vh - 280px);
      overflow: auto;
    }
    .class-tree {
      height: calc(100vh - 280px);
      overflow: auto;
      .tree-tool {
        width: 40px;
        margin-left: auto;
      }
      .tree-icon-tool {
        display: none;
        margin-left: 4px;
        font-size: 16px;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
      &:hover .tree-icon {
        display: inline-block;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content--value-wrapper) {
      display: flex;
      width: 100%;
      > svg {
        flex-shrink: 0;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:hover .tree-icon-tool) {
      display: inline-block;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .class-list-item {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      line-height: 20px;
      cursor: pointer;
      .class-con {
        display: flex;
        align-items: center;
        width: 230px;
        .class-label {
          flex: 1;
        }
      }
      .class-tools {
        display: none;
        margin-left: 14px;
      }
      &.is-active {
        background: #ebf4ff;
      }
      &:hover {
        background: #ebf4ff;
        border-radius: 2px;
        .class-tools {
          display: block;
        }
      }
    }
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
</style>
