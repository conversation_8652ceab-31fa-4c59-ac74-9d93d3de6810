<template>
  <n-drawer v-model="visiable" size="600" title="高级搜索" @close="onClose(false)">
    <n-form
      class="nc-p-20"
      :data="state.formData"
      ref="formRef"
      :rules="state.rules"
      label-width="130px"
      label-suffix="："
    >
      <n-form-item field="matchType" label="匹配方式">
        <n-radio-group v-model="state.formData.matchType" direction="row">
          <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
            item.name
          }}</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item field="ruleCode" label="非结构化数据编号">
        <n-input v-model="state.formData.ruleCode" />
      </n-form-item>
      <n-form-item field="name" label="非结构化数据名称">
        <n-input v-model="state.formData.name" />
      </n-form-item>
      <n-form-item field="confidentialityLevel" label="密级">
        <n-select v-model="state.formData.confidentialityLevel" :allow-clear="true">
          <n-option
            v-for="item in state.secretList"
            :key="item.id"
            :name="item.name"
            :value="item.id"
          />
        </n-select>
      </n-form-item>
      <n-form-item field="label" label="标签">
        <n-input v-model="state.formData.label" />
      </n-form-item>
      <n-form-item field="createByName" label="创建人">
        <n-input v-model="state.formData.createByName" />
      </n-form-item>
      <n-form-item field="time" label="创建时间">
        <n-range-date-picker-pro
          style="width: 100%"
          v-model="state.formData.time"
          :placeholder="['开始日期', '结束日期']"
          format="YYYY-MM-DD HH:mm:ss"
          :showTime="true"
          allow-clear
        />
      </n-form-item>
    </n-form>
    <div class="container-footer">
      <div class="my-appliction-right">
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">查询</n-button>
      </div>
    </div>
  </n-drawer>
</template>
<script setup>
  import moment from 'moment'
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
  })
  const state = reactive({
    formData: {
      matchType: 'LIKE',
      ruleCode: '',
      name: '',
      createByName: '',
      confidentialityLevel: '',
      time: [],
      startTime: '',
      endTime: '',
      label: '',
    },
    typeList: [
      { name: '模糊匹配', id: 'LIKE' },
      { name: '精准匹配', id: 'EQUALS' },
    ],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
  })
  function onConfirm() {
    const [start, end] = state.formData.time
    if (start && end) {
      state.formData.startTime = moment(start).format('YYYY-MM-DD HH:mm:ss')
      state.formData.endTime = moment(end).format('YYYY-MM-DD HH:mm:ss')
      state.formData.time = undefined
    }
    onClose(true)
  }
  const emit = defineEmits(['update:modelValue', 'needRefresh'])
  function onClose(needRefresh) {
    emit('update:modelValue', false)
    if (needRefresh) {
      emit('needRefresh', { ...state.formData, time: undefined })
    }
    state.formData = {
      matchType: 'LIKE',
      ruleCode: '',
      name: '',
      createByName: '',
      confidentialityLevel: '',
      time: [],
      startTime: '',
      endTime: '',
      label: '',
    }
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    async (val) => {
      visiable.value = val
    },
  )
</script>
<style lang="scss" scoped></style>
