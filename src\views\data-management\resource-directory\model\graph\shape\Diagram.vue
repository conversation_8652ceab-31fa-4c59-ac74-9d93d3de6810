<template>
  <div class="node" :style="{ width: width + 'px', height: height + 'px' }">
    <div class="head"><span>表图</span></div>
    <template v-if="data.diagramData.length > 0">
      <div
        class="content"
        :style="
          'border: ' +
          data.borderAttr.out.size +
          'px ' +
          data.borderAttr.out.style +
          ' ' +
          data.borderAttr.out.color
        "
      >
        <div v-for="(tr, index) in data.diagramData" :key="index" class="tr">
          <div
            v-for="(td, ind) in tr.child"
            :key="index + '-' + ind"
            class="tr_td"
            :style="
              'border-bottom: ' +
              data.borderAttr.inside.size +
              'px ' +
              data.borderAttr.inside.style +
              ' ' +
              data.borderAttr.inside.color +
              ';border-right: ' +
              data.borderAttr.inside.size +
              'px ' +
              data.borderAttr.inside.style +
              ' ' +
              data.borderAttr.inside.color +
              ';background-color:' +
              data.cellAttr.backColor
            "
          >
            <input
              v-model="td.value"
              :class="data.cellAttr.weight ? 'input weight' : 'input'"
              :style="
                'font-family:' +
                data.cellAttr.family +
                ';font-style:' +
                data.cellAttr.style +
                ';font-size:' +
                data.cellAttr.size +
                'px;color:' +
                data.cellAttr.color +
                ';text-decoration:' +
                (data.cellAttr.delChecked
                  ? 'line-through'
                  : data.cellAttr.underlineChecked
                  ? 'underline'
                  : '') +
                ';text-align:' +
                data.cellAttr.textAlign
              "
              @input="inputFn"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
  export default {
    name: 'TableChart',
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {
          diagramData: [],
          borderAttr: {},
          cellAttr: {},
        },
        width: 0,
        height: 0,
      }
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      const { width, height } = node.size()
      this.width = width
      this.height = height
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.data = current
        let nodeHeight = current.diagramData.length * 30 + 30 // 设置节点最大高度
        if (nodeHeight > 310) {
          nodeHeight = 310
        }
        node.size(300, nodeHeight)
      })
      node.on('change:size', ({ current }) => {
        this.width = current.width
        this.height = current.height
      })
    },
    methods: {
      // 输入框输入事件
      inputFn() {
        this.getNode().setData(this.data)
      },
    },
  }
</script>

<style scoped lang="scss">
  .node {
    display: inline-block;
    overflow: hidden;
    .head {
      height: 28px;
      line-height: 28px;
      padding-left: 10px;
      color: #fff;
      font-size: 12px;
      background: #447dfd;
    }
    .content {
      height: calc(100% - 30px);
      box-sizing: border-box;
      overflow: auto;
      color: #0c0c0c;
      font-size: 12px;
      background: #fff;
      border: 1px solid #447dfd;
      .tr {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        &_td {
          flex: 1;
          flex-shrink: 0;
          min-width: 50px;
          border-right: 1px solid #447dfd;
          border-bottom: 1px solid #447dfd;
          box-sizing: border-box;
          height: max-content;
          .input {
            width: 100%;
            min-height: 29px;
            line-height: 26px;
            border: none;
            outline: none;
            color: #000;
            font-size: 12px;
            background-color: inherit;
            font-weight: normal;
          }
          .weight {
            font-weight: bold;
          }
        }
        :deep(.tr_td:last-of-type) {
          border-right: none !important;
        }
      }
      .tr:last-of-type {
        :deep(.tr_td) {
          border-bottom: none !important;
        }
      }
    }
  }
</style>
