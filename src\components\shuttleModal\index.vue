<template>
  <div>
    <div class="delivery">
      <div class="delivery-box">
        <div class="delivery-top">
          <span class="box-title">源列表</span>
          <span class="proportion">{{ leftCheckData.length }}/{{ tableData.length }}</span>
        </div>
        <div class="delivery-center">
          <el-input
            v-model="keyword"
            size="mini"
            clearable
            placeholder="请输入关键词"
            @change="onSubmit"
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <i slot="suffix" class="el-input__icon el-icon-search" @click.prevent="onSubmit"></i
          ></el-input>
        </div>
        <div class="delivery-table">
          <el-table
            ref="leftTable"
            :data="tableData"
            :height="tableHeight"
            :header-cell-style="{
              background: '#efefef',
              color: '#333333',
              fontFamily: 'PingFangSC-Regular',
              fontSize: '12px',
              padding: '5px 0px',
              textAlign: 'center',
            }"
            :cell-style="{
              color: '#666',
              fontFamily: 'Arial',
              fontSize: '12px',
              padding: '5px 0px',
              textAlign: 'center',
            }"
            @selection-change="leftToggleCheck"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" width="50" label="序号">
              <template #default="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fieldKey" width="70" show-overflow-tooltip label="字段" />
            <el-table-column prop="type" width="70" show-overflow-tooltip label="类型" />
            <el-table-column prop="length" width="70" show-overflow-tooltip label="长度" />
            <el-table-column prop="metaDesc" show-overflow-tooltip label="描述" />
          </el-table>
        </div>
      </div>
      <!--                                               -->
      <div>
        <div class="buttom-box">
          <div class="delivery-buttom">
            <n-button
              :disabled="leftCheckData.length === 0"
              color="primary"
              icon="el-icon-arrow-right"
              @click.prevent="leftToRight"
            />
          </div>
          <div class="delivery-buttom margin-top28">
            <n-button
              :disabled="rightCheckData.length === 0"
              color="primary"
              plain
              icon="el-icon-arrow-left"
              @click.prevent="rightToLeft"
            />
          </div>
        </div>
      </div>
      <!--                         右框                      -->
      <div class="delivery-box">
        <div class="delivery-top">
          <span class="box-title">目的列表</span
          ><span class="proportion">{{ rightCheckData.length }}/{{ tableDataRight.length }}</span>
        </div>
        <div class="delivery-center">
          <el-input
            v-model="keywordRight"
            size="mini"
            clearable
            placeholder="请输入关键词"
            @change="onSubmitRight"
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click.prevent="onSubmitRight"
            ></i
          ></el-input>
        </div>
        <div class="delivery-table">
          <el-table
            ref="rightTable"
            :data="tableDataRight"
            :height="tableHeight"
            :header-cell-style="{
              background: '#efefef',
              color: '#333333',
              fontFamily: 'PingFangSC-Regular',
              fontSize: '12px',
              padding: '5px 0px',
              textAlign: 'center',
            }"
            :cell-style="{
              color: '#666',
              fontFamily: 'Arial',
              fontSize: '12px',
              padding: '5px 0px',
              textAlign: 'center',
            }"
            @selection-change="rightToggleCheck"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" width="50" label="序号"
              ><template #default="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fieldKey" width="70" show-overflow-tooltip label="字段" />
            <el-table-column prop="type" width="70" show-overflow-tooltip label="类型" />
            <el-table-column prop="length" width="70" show-overflow-tooltip label="长度" />
            <el-table-column prop="metaDesc" show-overflow-tooltip label="描述" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    props: ['deliveryTableData', 'columns'],
    data() {
      return {
        tableHeight: this.tableHeight ? this.tableHeight : '190',

        leftCheckData: [], // 左侧选择数据
        rightCheckData: [], // 有侧选择数据

        checkedAll: false,
        isIndeterminateLeft: false,
        keyword: '',
        tableData: [],
        tableDataCopy: [],
        // 右框数据
        checkedAllRight: false,
        isIndeterminateRight: false,
        keywordRight: '',
        tableDataRight: [],
        tableDataRightCopy: [],
        searchRight: [],
        searchLeft: [],
      }
    },
    watch: {
      deliveryTableData(n, o) {
        n.forEach((item) => {
          this.$set(item, 'checked', false)
        })
        this.tableData = n
        this.tableDataCopy = n
        this.tableDataRight = []
        this.tableDataRightCopy = []
      },
      keyword(newName, oldName) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return item.fieldKey.indexOf(newName) !== -1
        })
      },
      keywordRight(newName, oldName) {
        this.tableDataRight = this.tableDataRightCopy.filter((item) => {
          return item.fieldKey.indexOf(newName) !== -1
        })
      },
    },
    mounted() {
      let data = this.deliveryTableData
      data.forEach((item) => {
        this.$set(item, 'checked', false)
      })
      this.tableData = data
      this.tableDataCopy = data
      if (this.$route.query.id && this.columns.length > 0) {
        this.columns.forEach((node) => {
          data.forEach((item) => {
            if (node === item.fieldKey) {
              this.tableDataRight.push(item)
              this.tableDataRightCopy.push(item)
            }
          })
        })
      }
    },
    methods: {
      // 左单选
      leftToggleCheck(val) {
        this.leftCheckData = val
        this.tableDataCopy = this.tableData
      },
      // 穿梭到右边
      leftToRight() {
        this.tableDataRight.push(...this.leftCheckData)

        this.tableData.forEach((item) => {
          this.$set(item, 'checked', false)
          this.leftCheckData.forEach((val) => {
            if (val.fieldKey === item.fieldKey) {
              this.$set(item, 'checked', true)
            }
          })
        })

        let arr = this.tableData.filter((item) => {
          return item.checked === false
        })

        this.tableData = arr

        this.leftCheckData = []
        this.$refs.leftTable.clearSelection()

        this.$emit('getReverseData', this.tableDataRight)
      },
      // 左搜索表
      onSubmit(val) {
        // this.tableData = this.searchLeft.filter((v) => {
        //   return v.fieldKey.indexOf(val) > -1
        // })
      },
      // ==============右边==============
      // 右单选
      rightToggleCheck(val) {
        this.rightCheckData = val
        this.tableDataRightCopy = this.tableDataRight
      },
      // 穿梭到左边
      rightToLeft() {
        this.tableData.push(...this.rightCheckData)
        this.rightCheckData = []
        this.$refs.rightTable.clearSelection()

        let arr = []

        this.tableData.forEach((item) => {
          this.tableDataRight.forEach((val, index) => {
            if (val.fieldKey === item.fieldKey) {
              this.tableDataRight.splice(index, 1)
            }
          })
        })

        // this.tableDataRight = arr

        this.$emit('getReverseData', this.tableDataRight)
      },
      // 右搜索表
      onSubmitRight(val) {
        // this.tableDataRight = this.searchRight.filter((v) => {
        //   return v.fieldKey.indexOf(val) > -1
        // })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .delivery {
    display: flex;
    margin-top: 10px;
  }
  .delivery-box {
    width: 380px;
    height: 100%;
    font-size: 12px;
    font-family: 'PingFangSC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #333333;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .delivery-top {
    padding: 0px 10px;
    height: 32px;
    line-height: 32px;
    background: rgba(0, 0, 0, 0.04);
  }
  .box-title {
    // position: relative;
    // top: -30px;
    // left: -25px;
  }
  .proportion {
    float: right;
  }
  .delivery-center {
    text-align: center;
    height: 36px;
    line-height: 36px;
  }
  .delivery-table {
    width: 100%;
  }
  :deep(.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf) {
    border-bottom: 0px;
  }
  :deep(.el-table--scrollable-y .el-table__body-wrapper) {
    overflow-x: hidden;
    overflow-y: auto;
  }
  :deep(.el-table--scrollable-x .el-table__body-wrapper) {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .margin-left {
    margin-left: 20px;
  }
  .buttom-box {
    padding: 0px 20px;
    margin-top: 80px;
    display: flex;
    flex-direction: column;
  }
  .delivery-buttom {
    width: 28px;
    height: 28px;
  }
  .margin-top28 {
    margin-top: 28px;
  }
</style>
