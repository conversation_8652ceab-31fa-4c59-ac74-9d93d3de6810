<template>
  <div class="container">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <n-button variant="solid" @click="state.tagPopup = true">标注结果</n-button>
      <n-button variant="solid" @click="closeFn">退出预览</n-button>
      <n-button variant="solid" @click="emit('detail', { id: props.info.id, taskId: props.info.taskId })">详情</n-button>
    </div>
    <!-- 预览 -->
    <div
      class="preview-content-box"
      id="preview-pint"
      ref="previewPintRef"
      v-loading="state.loading"
      element-loading-text="Loading..."
    >
      <FilePreview
      ref="filePreviewRef"
        :option="state.option"
        :type="state.type"
        @pageChange="pageChange"
        @loadComplete="getPreviewData(props.info.id)"
        isDrawDisabled
        isDragDisabled
        :data="state.annotationsPage"
        @click-polygon="clickPolygon"
      />
    </div>
    <template v-for="(item, key) in tagList" :key="key">
      <Tag
        :ref="(ref) => (tagRefs[item.id] = ref)"
        :tagOptions="state.tagOptions"
        :x="item.x"
        :y="item.y"
        :initialTags="item.tags"
        mode="preview"
      />
    </template>

    <!-- 标注结果 -->
    <div class="tagPopup" v-show="state.tagPopup">
      <div class="tagPopup-title">
        <span>标注结果</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          @click="state.tagPopup = false"
        >
          <path
            d="M4.5 19.4998L19.4995 4.50023"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4.5 4.50024L19.4995 19.4998"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="tagPopup-btn">
        <!-- 增加段落 -->
        <!-- <n-button color="primary" @click="addParagraph">增加段落</n-button> -->
        <!-- 导出结果 -->
        <n-button color="primary" @click="exportResult">导出结果</n-button>
      </div>
      <div class="tagPopup-content">
        <!-- 自动标注段落 -->
                <div :class="['tagPopup-paragraph']">

          <!-- 标注段落的内容 -->
          <!-- 标题 -->
          <div class="tagPopup-paragraph-title"> 段落1： </div>
          <!-- 内容 -->
          <div class="tagPopup-paragraph-content">
            <div
              class="tagPopup-content-item"
              v-for="(item, key) in state.autoTagResult"
              :key="key"
            >
              <div class="tag-content">
                <h2>内容：</h2>
                <div class="tag-content-text"> {{ item.selectedText }} </div>
              </div>
              <div class="tag-result">
                <h2>标签：</h2>
                <div
                  class="tag"
                  :style="getTagStyle(tag.color)"
                  v-for="(tag, tagKey) in item.tags"
                  :key="tagKey"
                >
                  {{ tag.text }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 标注段落 -->
        <div
          v-for="(item, i) in state.annotationsParagraph"
          :class="['tagPopup-paragraph', item.active && 'active']"
          @click="activeParagraph(item.id)"
          :key="i"
        >
          <!-- 标注段落的内容 -->
          <!-- 标题 -->
          <div class="tagPopup-paragraph-title"> 段落{{ i + 2 }}： </div>
          <!-- 内容 -->
          <div class="tagPopup-paragraph-content">
            <div
              :class="['tagPopup-content-item', item.id === state.currentAnnotationId  ? 'selected':'']"
              :id="item.id"
              v-for="(item, key) in paragraphLinks(item.links)"
              :key="key"
               @click.prevent="linkAnnotation(item.id)"
            >
              <div class="tag-content">
                <h2>内容：</h2>
                <div class="tag-content-text"> {{ item.selectedText }} </div>
              </div>
              <div class="tag-result">
                <h2>标签：</h2>
                <div
                  class="tag"
                  :style="getTagStyle(tag.color)"
                  v-for="(tag, tagKey) in item.tags"
                  :key="tagKey"
                >
                  {{ tag.text }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { throttle, isEmpty } from 'lodash-es'
  import { useRouter } from 'vue-router'
  import Tag from '@/views/document-management/document-annotation/tag.vue'
  import FilePreview from '@/views/document-management/document-annotation/components/FilePreview.vue'
  import * as d3 from 'd3'
  import api from '@/api/index'
  import { uuid } from '@/utils/index' // 工具函数
  const emit = defineEmits(['close'])
  const router = useRouter()
  const previewPintRef = ref(null)
  const tagRefs = {},filePreviewRef = ref(null)
  const props = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
  const state = reactive({
    name: '文档预览',
    option: {},
    type: '',
    loading: false,
    tagPopup: false,
    tagOptions: [],
    annotations: {},
    // 分页标注data
    annotationsPage: {},
    currentPage: 1,
    tagPopupData: [], // 标签弹窗数据
    annotationsParagraph: [], // 标注段落
  })
  const pageChange = (page) => {
    setTimeout(() => {
      state.currentPage = page
      renderWaveLine()
    })
  }
  function getDocUrl(id) {
    api.documentManage.outsideGet({ id: id }).then((res) => {
      if (res.success) {
        const { name, type, docUrl } = res.data
        state.name = name
        state.type = type
        state.option = {
          value: docUrl,
          name: name,
        }
      }
    })
  }
  // 获取预览数据
  function getPreviewData(id) {
    api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
      if (res.success) {
        try {
          const { mark, paragraph } = res.data
          state.annotationsPage = JSON.parse(mark || '{}')
          state.tagPopupData = Object.values(state.annotationsPage[1] || {})
          state.annotationsParagraph = JSON.parse(paragraph || '[]')
          if (state.annotationsParagraph?.length === 0) addParagraph()
          switch (true) {
            case ['pdf', 'word'].includes(state.type):
              renderWaveLine()
              break
            case state.type.includes('image'):
              break
          }
        } catch (error) {
          state.annotationsPage = {}
        }
        nextTick(() => {
          document
            .querySelector('[data-annotation-id="wave-1748328583814-w0bltvjm0"]')
            ?.scrollIntoView()
        })
      }
    })
  }
  function closeFn() {
    emit('close')
  }
  onMounted(() => {
    const { id } = router.currentRoute.value.query
    getDocUrl(props.info.id)
  })

  // 获取标签列表
  const getTargetList = () => {
    api.dataGovernance.getTaskLabelDetail(props.info.taskId).then((res) => {
      let { success, data } = res
      if (success) {
        state.tagOptions = JSON.parse(data?.labels || '[]')
        getAutoTag(props.info.taskId)
      }
    })
  }
  getTargetList()
  const tagList = computed(() => {
    // state.annotations为空时展示当前页的标注
    if (isEmpty(state.annotations || {})) {
      return state.annotationsPage[state.currentPage] || {}
    }
    const newAnnotations = {
      ...(state.annotationsPage[state.currentPage] || {}),
      ...(state.annotations || {}),
    }
    return newAnnotations
  })

  // 计算属性：通过id获取段落关联的标注
  const paragraphLinks = computed(() => (links) => {
    const tagPopupData = []
    Object.values(state.annotationsPage || {})
      .flatMap((item) => Object.values(item || {}))
      .map((item) => {
        links.find((link) => {
          if (item.id === link) {
            tagPopupData.push(item)
          }
        })
      })
    return tagPopupData
  })
  // 渲染本页的波浪线
  function renderWaveLine() {
    // 清除之前的data-temp为true的div
    document.querySelectorAll('[data-temp="true"]').forEach((el) => el.remove())

    // 渲染标记完成的波浪线
    Object.values(state.annotationsPage[state.currentPage] || {}).forEach((item) => {
      if (item.sortedRects.length) {
        const { top, left, width, height } = item.boundingRect
        // 清除重复的
        document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach((el) => el.remove())
        item.sortedRects.forEach((rect) => {
          addWaveLine({
            dataTemp: false,
            offsetY: rect.height,
            width: rect.width,
            top: rect.top - top,
            left: rect.left - left,
            annotationId: item.id,
          })
        })
      }
    })
  }
  // 添加波浪线
  function addWaveLine(
    {
      dataTemp = true,
      offsetY = 0,
      top = 0,
      left = 0,
      width = 2000,
      height = 10,
      wavelength = 20,
      lineWidth = 1,
      annotationId,
    } = {
      dataTemp: true,
      offsetY: 0,
      top: 0,
      left: 0,
      width: 2000,
      height: 10,
      wavelength: 50,
      lineWidth: 1,
      annotationId,
    },
  ) {
    const amplitude = height * 0.5 // 振幅
    const centerY = height / 2 // 中心Y坐标
    const tagData = state.tagPopupData.find((item) => item.id === annotationId)

    const svg = d3
      .select('#wave-container')
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr(
        'style',
        `position: absolute;top: ${top + offsetY}px;left:${left}px;color: ${
          getTagStyle(tagData.tags[0].color).color
        };z-index: 100000;`,
      )
      .attr('data-annotation-id', annotationId)
      .attr('data-temp', dataTemp)

    // 添加点击事件
    svg.on('click', function (event) {
      tagRefs[annotationId]?.showTag(true)
      state.tagPopup = true
      state.currentAnnotationId = annotationId
        nextTick(() => {
          document
            .querySelector(`#${annotationId}`)
            ?.scrollIntoView()
        })

    })

    const waveData = []
    for (let x = 0; x < width; x++) {
      const y = (amplitude - lineWidth * 2) * Math.sin((x / wavelength) * Math.PI * 2) + centerY
      waveData.push([x, y])
    }
    svg
      .append('path')
      .datum(waveData)
      .attr(
        'd',
        d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1])
          .curve(d3.curveBasis),
      )
      .attr('stroke', 'currentColor')
      .attr('fill', 'none')
  }

  // 获取标签样式
  function getTagStyle(tag) {
    const color = tag.split('_')
    return {
      color: color[0],
      background: color[1],
      borderColor: color[2],
    }
  }

  // 激活段落
  function activeParagraph(id) {
    state.annotationsParagraph.forEach((t) => {
      t.active = t.id === id
    })
  }
  function addParagraph() {
    // 数组长度
    const length = state.annotationsParagraph.length
    // 关联标注
    state.annotationsParagraph.push({ id: uuid(), text: '', active: length === 0, links: [] })
  }
  // 导出
  function exportResult() {
    // 通过文件流下载文件
    api.documentManage.exportDocumentAnnotationExcel(props.info.id).then((blob) => {
      // 下载文件
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '标注结果.xlsx'
      link.click()
      URL.revokeObjectURL(link.href)
    })
  }

  // 获取自动标注结果
  function getAutoTag(taskId) {
    api.dataGovernance.autoAnnotationResult({ taskId: props.info.taskId }).then((res) => {
      if (res.success) {
        const tableHead = res?.data?.excelDataList?.shift?.()?.rowData
        state.autoTagResult = (res?.data?.excelDataList || []).map((item) => {
          const rowData = item.rowData
          const data = {
            selectedText: rowData[0].value,
          }
          // 根据tag名称查询标签
          const tags =
            state.tagOptions?.filter((tag) => tableHead.find((row) => row.value === tag.text)) || []
          data.tags = tags.map((tag) => {
            return { text: tag.text, color: tag.color }
          })
          return data
        })
        state.annotationsParagraph.push({ id: uuid(), text: '', active: length === 0, links: [] })
      }
    })
  }

  // 联动标注
  function linkAnnotation(id) {
    state.currentAnnotationId = id
        // 所有的tagRefs[id]?.showTag()
        Object.values(tagRefs).forEach((ref) => {
          ref?.showTag(false)
        })
        nextTick(() => {
        tagRefs[id]?.showTag(true)
          const line = document.querySelector(`[data-annotation-id="${id}"]`)
          line?.scrollIntoView()
      })
        const data = filePreviewRef?.value?.getImageMark(id)
        const maxX = Math.max(...data.map(item => item.x))
        const maxY = Math.min(...data.map(item => item.y))
          Object.assign(state.annotationsPage[state.currentPage] [id], {x: maxX, y: maxY})
  }
  function clickPolygon (id,data ,scale, imgOffset = { x: 0, y: 0 })   {
      state.tagPopup = true
      state.currentAnnotationId = id
      nextTick(() => {
          document
            .querySelector(`#${id}`)
            ?.scrollIntoView()
      })
          tagRefs[id].showTag()
          const maxX = Math.max(...data.map(item => item.x * scale + imgOffset.x))
          const maxY = Math.min(...data.map(item => item.y * scale + imgOffset.y))
          Object.assign(state.annotationsPage[state.currentPage] [id], {x: maxX, y: maxY})
        }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .container {
    width: 100%;
    height: 100% !important;
    position: relative;
    overflow-y: auto;
    padding: 0 !important;
  }

  .preview-content-box {
    height: calc(100% - 56px);
    padding: 0;
    background: #fff;
    overflow-y: auto;
    box-sizing: border-box;
    .preview-content {
      height: auto !important;
    }
  }

  :deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;

    .img-Preview {
      width: 100% !important;
      height: max-content;
      min-height: 100%;
      overflow-x: unset;
      overflow-y: unset;
    }
  }

  .title {
    width: calc(100% - 80px);
  }
  .tagPopup {
    position: absolute;
    top: 0;
    right: 0;
    width: 400px;
    height: 100%;
    background: #fff;
    z-index: 100000;
    border: 1px solid #ccc;
    &-title {
      position: relative;
      font-size: 16px;
      font-weight: bold;
      line-height: 52px;
      text-align: center;
      border-bottom: 1px solid #ccc;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;

      svg {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      ::before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }
    &-btn {
      padding: 8px 16px;
    }
    &-paragraph {
      padding: 10px;
      min-height: 300px;
      border: 3px dashed #ccc;
      border-radius: 4px;
      margin-bottom: 10px;
      padding: 10px;
      cursor: pointer;
      // 激活状态
      &.active {
        border-color: #1e89ff;
        // 动画
        animation: pulse 1s infinite;
        @keyframes pulse {
          0% {
            border-color: #1e89ff;
          }
          50% {
            border-color: #ccc;
          }
          100% {
            border-color: #1e89ff;
          }
        }
      }
    }
    &-content {
      height: calc(100% - 53px);
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;

      &-item {
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;
        background: #f5f5f5;
   &.selected{
   
          background: rgba(232, 113, 10, 0.2);
          // 发光效果
          box-shadow: 0 0 10px rgba(232, 113, 10, 0.5);
        }
        h2 {
          font-size: 14px;
          font-weight: bold;
          line-height: 32px;
          margin: 0;
          border: none;
        }
        .tag-content-text {
          font-size: 14px;
          line-height: 32px;
          color: #333;
        }

        .tag-result .tag {
          font-size: 14px;
          line-height: 32px;
          color: #333;
          display: inline-block;
          padding: 0 14px;
          border: 1px solid transparent;
          border-radius: 4px;
          margin-right: 10px;
        }
      }
    }
  }
</style>
<style>
  #wave-container,
  #tag-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100000;
    width: 0;
    height: 0;
  }
</style>
