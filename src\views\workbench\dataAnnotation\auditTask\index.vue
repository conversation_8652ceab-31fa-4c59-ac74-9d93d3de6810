<template>
  <div class="action-list">
    <CfTable
      actionWidth="120"
      ref="tableNoRef"
      :tableConfig="{
        data: state.dataSource,
        rowKey: 'id',
      }"
      :table-head-titles="tableHead"
      :paginationConfig="{
        total: state.total,
        pageSize: state.searchData.pageSize,
        currentPage: state.searchData.pageNum,
        onCurrentChange: (v) => {
          state.searchData.pageNum = v
          onSearch(false)
        },
        onSizeChange: (v) => {
          state.searchData.pageSize = v
          onSearch()
        },
      }"
      ><template #annotationDataDownloadUrl="{ row }">
        <!-- 预览 -->
        <n-button
          class="preview-btn"
          color="primary"
          variant="text"
          @click.prevent="emit('previewStandard', { id: row.documentId ,taskId: row.taskId})"
        >
          预览 </n-button
        >
      </template>
      <template #taskStatus="{ row }">
        <div v-if="row.auditStatus === '待审核'" class="taskStatus"
          ><div :class="['circle', 'yellow']"></div>待审核</div
        >
        <div v-else-if="row.auditStatus === '已完成'" class="taskStatus"
          ><div :class="['circle', 'blue']"></div>已完成</div
        >
        <div v-else-if="row.auditStatus === '审核不通过'" class="taskStatus"
          ><div :class="['circle', 'res']"></div>审核不通过</div
        >
      </template>
      <template #editor="{ row }">
        <!-- 提交完成 -->
        <n-button
          class="submit-btn"
          :disabled="row.auditStatus === '已完成'"
          color="primary"
          variant="text"
          @click="submitTask(row, 'pass')"
        >
          通过
        </n-button>
        <n-button
          class="submit-btn"
          :disabled="row.auditStatus === '已完成'"
          color="primary"
          variant="text"
          @click="submitTask(row, 'reject')"
        >
          驳回
        </n-button>
      </template>
      <template #conclusionFileUrl="{ row }">
        <div style="gap: 4px; display: flex; align-items: center">
          <n-button class="upload-btn" color="primary" variant="text" @click="uploadFile(row)">
            上传
          </n-button>
          <div class="more" :title="row?.conclusionFileOriginalName || '--'">{{
            row?.conclusionFileOriginalName || '--'
          }}</div>
        </div>
      </template>
    </CfTable>
  </div>
</template>
<script setup>
  import api from '@/api'
  import { useRouter } from 'vue-router'
  const emit = defineEmits(['previewStandard'])
  const router = useRouter()
  const { id: projectId } = router.currentRoute.value.query // 任务id
  const formRef = ref(null) // 表单ref
  const state = reactive({
    formData: {
      // 表单数据
      teamInfo: {
        name: '',
        userIdLeader: null,
      },
      teamUserList: [{}],
    },
    teamDrawerVisible: false, // 团队弹窗
    dataSource: [], // 表格数据
    searchData: {
      // 分页
      condition: {
        projectId,
      },
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
    },
    total: 0, // 总条数
  })
  const tableHead = computed(() => {
    // 表格表头
    return [
      { name: '任务名称', prop: 'taskName', width: 500 },
      { name: '文件格式', prop: 'documentType' },
      { name: '标注人', prop: 'submitByName' },
      {
        name: '标注后数据',
        prop: 'annotationDataDownloadUrl',
        slot: 'annotationDataDownloadUrl',
      },
      // { name: '审核结论', prop: 'conclusionFileUrl', slot: 'conclusionFileUrl', width: 240 },
      { name: '状态', prop: 'auditStatus', slot: 'taskStatus' },
    ]
  })
  const onSearch = (reset = true) => {
    // 搜索
    console.log('搜索')
    api.dataGovernance
      .getAuditTask(state.searchData)
      .then((res) => {
        if (res.success) {
          state.dataSource = res.data.list
          state.total = res.data.total
        }
      })
      .catch(() => {})
  }
  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  // 下载
  const downloadFile = (url, name) => {
    // 下载文件 包括图片 pdf等
    fetch(url).then((res) => {
      // 成功
      res.blob().then((blob) => {
        // 成功
        const link = document.createElement('a') // 创建a标签
        link.href = URL.createObjectURL(blob) // 创建下载链接
        // link.download = url.split('/').pop() // 下载文件名
        link.download = name // 下载文件名
        document.body.appendChild(link) // 插入body
        link.click() // 点击下载
        document.body.removeChild(link) // 移除body
      })
    })
  }
  // 提交
  const submitTask = (row, type) => {
    if (type === 'pass') {
      api.dataGovernance.approvalPass({ id: row.id }).then((res) => {
        // 成功
        if (res.success) {
          // 成功
          ElMessage.success('已通过') // 提示
          onSearch() // 搜索
        }
      })
    } else {
      api.dataManagement.workbenchAuditReject({ id: row.id }).then((res) => {
        if (res) {
          if (res.success) {
            // 成功
            ElMessage.success('已驳回') // 提示
            onSearch() // 搜索
          }
        }
      })
    }
  }

  // 上传
  const uploadFile = (task) => {
    // 上传文件 包括图片 pdf等
    const fileInput = document.createElement('input') // 创建input标签
    fileInput.type = 'file' // 文件类型
    fileInput.onchange = (e) => {
      // 选择文件
      const file = e.target.files[0] // 获取文件
      if (file) {
        // 文件存在
        const file = e.target.files[0] // 获取文件
        console.log(file) // 打印文件
        const formData = new FormData()
        formData.append('file', file)
        formData.append('bucket', 'data-govern')
        api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
          if (res.success) {
            // 上传成功
            api.dataGovernance
              .uploadAuditConclusion({
                id: task.id,
                url: res.data.url,
                fileOriginalName: file.name,
              })
              .then((res) => {
                // 成功
                if (res.success) {
                  // 成功
                  ElMessage.success('上传成功') // 成功提示
                  onSearch() // 刷新表格
                }
              })
          }
        })
      }
    }
    fileInput.click() // 点击上传
  }
  onSearch()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .action-list {
    background-color: #fff;
    height: 100%;
  }

  .more {
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .taskStatus {
    .circle {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 4px;
      background-color: $themeBlue;
      border-radius: 50%;

      &.yellow {
        background-color: #ffba70;
      }

      &.red {
        background-color: #f63838;
      }
    }
  }
</style>
