<template>
  <div id="bloodMap"></div>

  <section
    :style="{ left: state.tooltip.point.x + 'px', top: state.tooltip.point.y + 'px' }"
    class="tooltipPop"
    v-show="state.tooltipShow"
  >
    <div class="header">{{ state.tooltip.label }}</div>
    <p>存储总量：{{ state.tooltip.storage }}</p>
    <p>字段数量：{{ state.tooltip.fields }}</p>
    <p>更新时间：{{ state.tooltip.updateTime }}</p>
    <div class="btn">
      <n-button @click="state.dataModelShow = true" variant="text" color="primary"
        >查看模型</n-button
      >
      <n-button variant="text" color="primary">数据预览</n-button>
    </div>
  </section>

  <n-modal v-model="state.dataModelShow" title="数据模型">
    <img src="@/assets/img/erDemoImg.jpg" alt="" />

    <template #footer>
      <n-modal-footer>
        <n-button @click="state.dataModelShow = false" variant="solid">取消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script setup>
  import G6 from '@antv/g6'
  import { workbenchBloodMap } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const data = ref({
    nodes: [],
    edges: [],
  })
  const state = reactive({
    dataModelShow: false,
    tooltip: {
      label: '',
      storage: '7331.55MB',
      fields: 56,
      updateTime: '2021-06-15',
      point: {
        x: 0,
        y: 0,
      },
    },
    tooltipShow: false,
  })

  let graph = null

  G6.registerEdge(
    'can-running',
    {
      afterDraw(cfg, group) {
        // get the first shape in the group, it is the edge's path here=
        const shape = group.get('children')[0]
        // the start position of the edge's path
        const startPoint = shape.getPoint(0)

        // add red circle shape
        const circle = group.addShape('circle', {
          attrs: {
            x: startPoint.x,
            y: startPoint.y,
            fill: '#1890ff',
            r: 3,
          },
          // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
          name: 'circle-shape',
        })

        // animation for the red circle
        circle.animate(
          (ratio) => {
            const tmpPoint = shape.getPoint(ratio)
            // returns the modified configurations here, x and y here
            return {
              x: tmpPoint.x,
              y: tmpPoint.y,
            }
          },
          {
            repeat: true, // Whether executes the animation repeatly
            duration: 3000, // the duration for executing once
          },
        )
      },
      setState(name, value, item) {
        const shape = item.get('keyShape')
        if (name === 'running') {
          if (value) {
            shape.attr('stroke', '#FFD194')
          } else {
            shape.attr('stroke', '#CCD0D8')
            // shape.stopAnimate()
            shape.attr('lineDash', null)
          }
        }
      },
    },
    'line',
  )

  G6.registerNode(
    'sphere-node',
    {
      drawShape: function drawShape(cfg, group) {
        // 主球体（半径动态配置）
        const radius = cfg.size || 20 // 可通过节点size字段控制大小
        const fill = cfg.style.fill || '#99C9FF'
        const stroke = cfg.style.stroke || '#1E89FF'
        const width = cfg.style.width

        // 绘制主体球型
        const sphere = group.addShape('rect', {
          attrs: {
            x: -width / 2,
            y: -15,
            width,
            height: 30,
            radius: 15,
            stroke,
            fill,
            lineWidth: 1.2,
            fillOpacity: 1,
          },
          name: 'sphere-body',
        })
        return sphere
      },
      getAnchorPoints: function () {
        return [
          [0, 0.5], // 左侧锚点
          [1, 0.5], // 右侧锚点
        ]
      },
      update: function (cfg, item) {
        const group = item.getContainer()
        const children = group.get('children')

        // 更新主体颜色
        const sphere = children[0]
        const anchorPoint = children[1]

        if (cfg.style) {
          if (cfg.style.stroke) {
            sphere.attr('stroke', cfg.style.stroke)
            anchorPoint.attr('fill', cfg.style.stroke)
          }
          if (cfg.style.fill) {
            sphere.attr('fill', cfg.style.fill)
          }
        }
      },
    },
    'single-node',
  )

  const tooltip = new G6.Tooltip({
    offsetX: 10,
    offsetY: 10,
    trigger: 'mouseenter',
    fixToNode: [1, 0.5],
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node', 'edge'],
    // 自定义 tooltip 内容
    getContent: (e) => {
      const outDiv = document.createElement('div')
      outDiv.style.width = 'fit-content'
      outDiv.style.height = 'fit-content'
      const model = e.item.getModel()

      if (e.item.getType() === 'node') {
        outDiv.innerHTML = `<div>表中文名：${model.nodeData.foblOrViewChnName}<br/>
          表英文名：${model.nodeData.foblOrViewPhnName}<br/>
          来源系统：${model.nodeData.odsTableOrigin}<br/></div>`
      } else {
        const edge = e.item.getModel()
        let tableHtml = `<table style="border-collapse: collapse;">
          <thead>
            <tr>
              <th style="border: 1px solid #ccc; padding: 8px;">序号</th>
              <th style="border: 1px solid #ccc; padding: 8px;">加工规则</th>
            </tr>
          </thead>
          <tbody>`
        if (edge.ruleInfo && edge.ruleInfo.length > 0) {
          edge.ruleInfo.forEach((rule, index) => {
            tableHtml += `<tr>
              <td style="border: 1px solid #ccc; padding: 8px;">${index + 1}</td>
              <td style="border: 1px solid #ccc; padding: 8px;">${rule.ruleDesc}</td>
            </tr>`
          })
        } else {
          tableHtml += `<tr>
            <td colspan="2" style="border: 1px solid #ccc; padding: 8px; text-align: center;">暂无加工规则</td>
          </tr>`
        }
        tableHtml += `</tbody></table>`
        outDiv.innerHTML = tableHtml
      }
      return outDiv
    },
  })

  const booldColor = {
    ODS: '#99C9FF',
    DWD: 'green',
    ADM: 'red',
  }

  // 获取所有血缘数据
  const getAllMap = async () => {
    const res = await workbenchBloodMap({
      projectCode: props.info.code,
    })

    data.value = {
      nodes: [],
      edges: [],
    }

    res.data.nodes.forEach((node) => {
      data.value.nodes.push({
        id: node.pk,
        name: node.foblOrViewChnName,
        label: node.foblOrViewChnName,
        nodeData: node,
        style: { fill: booldColor[node.foblOrViewEngName] },
      })
    })

    res.data.nodeRels.forEach((edge) => {
      data.value.edges.push({
        source: edge.prntNodeId,
        target: edge.childNodeId,
        label: '',
        ruleInfo: edge.relRuleList,
        type: 'can-running',
      })
    })

    initData(data.value)
  }

  onMounted(() => {
    getAllMap()
  })

  onUnmounted(() => {
    if (graph) {
      graph.destroy()
    }
  })

  const initData = (data) => {
    const container = document.getElementById('bloodMap')
    const width = container.scrollWidth
    const height = container.scrollHeight || 500

    const minimap = new G6.Minimap({
      size: [150, 100],
      className: 'g6-minimap',
      type: 'keyShape',
      keyShapeConfig: {
        fill: '#f0f0f0',
        stroke: '#999',
        lineWidth: 1,
      },
    })

    graph = new G6.Graph({
      container: 'bloodMap',
      width,
      height,
      layout: {
        type: 'dagre',
        rankdir: 'LR',
        nodesep: 30,
        ranksep: 100,
      },
      plugins: [tooltip, minimap],
      modes: {
        default: [
          { type: 'drag-canvas', enableOptimize: true },
          {
            type: 'zoom-canvas',
            fixSelectedItems: {
              fixAll: true,
              fixState: 'yourStateName', // 'selected' by default
            },
          },
        ],
      },
      defaultEdge: {
        type: 'polyline',
        style: {
          stroke: '#CCD0D8',
          lineWidth: 3, // 设置边的宽度为 2 像素
        },
        labelCfg: {
          style: {
            fill: '#1D2129',
            autoRotate: true,
            fontSize: 12, // 设置边文字的大小
          },
        },
      },
      defaultNode: {
        type: 'sphere-node',
        size: 50, // 控制球体半径
        style: {
          fill: '#99C9FF',
          stroke: '#1E89FF',
          width: 150,
        },
      },
    })

    graph.data(data)
    graph.render()
    // graph.fitView(50)
    // graph.zoomTo(1)

    graph.on('node:mouseenter', (ev) => {
      const nodeCurrent = ev.item
      nodeCurrent._cfg.model.style.fill = '#FF9E42'
      nodeCurrent._cfg.model.style.lineWidth = 1

      graph.setItemState(nodeCurrent, 'highlight', true)

      // 遍历边，获取相关联的节点
      const relatedNodes = []
      const edges = nodeCurrent.getEdges()
      edges.forEach((edge) => {
        relatedNodes.push(edge.getSource())
        relatedNodes.push(edge.getTarget())

        graph.setItemState(edge, 'running', true)
        edge._cfg.model.labelCfg.style.fill = '#FFD194'
        edge.refresh()
      })

      relatedNodes.forEach((node) => {
        node._cfg.model.style.fill = '#FFD194'
        node._cfg.model.style.lineWidth = 1
        graph.setItemState(node, 'highlight', true)
      })
    })

    graph.on('node:mouseleave', () => {
      graph.setAutoPaint(false)
      graph.getNodes().forEach((node) => {
        graph.clearItemStates(node)
      })
      graph.getEdges().forEach((edge) => {
        edge._cfg.model.labelCfg.style.fill = '#1D2129'
        edge.refresh()
        graph.clearItemStates(edge)
      })
      graph.paint()
      graph.setAutoPaint(true)
    })

    graph.on('node:contextmenu', (evt) => {
      const node = evt.item
      const model = node.getModel()
      state.tooltipShow = true

      // 获取节点位置
      const { x, y } = model
      const point = graph.getCanvasByPoint(x, y)

      // 设置弹窗内容
      state.tooltip = {
        label: model.name,
        storage: '7331.55MB',
        fields: 56,
        updateTime: '2021-06-15',
        point,
      }
    })

    // 点击画布空白区域关闭弹窗
    graph.on('canvas:click', () => {
      state.tooltipShow = false
    })
  }

  // 全局阻止浏览器默认右键菜单
  document.addEventListener(
    'contextmenu',
    (e) => {
      e.preventDefault()
    },
    false,
  )
</script>

<style scoped>
  #bloodMap {
    position: relative;
    width: 100%;
    height: 100%;
  }

  :deep(.g6-minimap) {
    position: absolute;
    right: 10px;
    bottom: 10px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  .tooltipPop {
    position: absolute;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 4px 8px 0 rgba(37, 43, 58, 0.2);

    .header {
      text-align: center;
      background: #eee;
      line-height: 28px;
    }

    p {
      margin: 0;
      padding: 0 10px;
    }
  }
</style>
