import axios from 'axios'
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus'
import store from '@/store'
import router from '@/router'
import Cookies from 'js-cookie'
import { getToken, TokenKey } from '@/utils/auth'
let hasShowMessage = false // 限制多个未登录弹窗
let hasResetLoginWays = false // 调取重新登录接口
// 创建一个axios实例
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  baseURL: import.meta.env.BASE_URL + '',
  // withCredentials: true, // 当跨域请求时发送cookie
  timeout: 1000 * 60 * 5, // request timeout 5分钟
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么

    // if (getToken() && sessionStorage.getItem('isNormalLogin') === '1') {
    if (getToken()) {
      if (getToken() === 'cross-domain-token') {
        let CookiesToken = Cookies.get(TokenKey)
        if (config.url !== '/api/govern-management/current/user/info') {
          return false
        }
        config.headers['Authorization'] = CookiesToken.replace(/%20/g, ' ')
      } else {
        config.headers['Authorization'] = getToken().replace(/%20/g, ' ')
      }
    }
    if (config.responseType && config.responseType !== 'blob') {
      //blob形式设置请求头会报错
      config.headers['Accept'] =
        'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'
      config.headers['Accept-Encoding'] = 'gzip,deflate'
      config.headers['Accept-Language'] = 'zh-CN,zh;q=0.9'
      config.headers['Connection'] = 'keep-alive'
      config.headers['Upgrade-Insecure-Requests'] = '1'
      config.headers['Cache-Control'] = 'no-cache,no-store'
    }
    // if (getToken() && sessionStorage.getItem('isNormalLogin') !== '1') {
    if (getToken()) {
      config.headers['X-Token'] = getToken().replace(/%20/g, ' ')
      // config.headers['Lz-token'] = getToken().replace(/%20/g, ' ')
    }
    return config
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果您想获得http信息 headers or status
   * 请 return  response => response
   */

  /**
   * 通过自定义代码确定请求状态
   * 这里只是一个例子
   * 您也可以通过HTTP状态码来判断状态
   */
  (response) => {
    const res = response.data

    if (response.config.responseType) {
      response.headers['Connection'] = 'keep-alive'
      response.headers['Content-Type'] =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      // response.headers[
      //   'content-disposition'
      // ] = `attachment;filename*=utf-8''%E6%A8%A1%E6%9D%BF.xlsx`
      response.headers['Cache-Control'] = 'no-cache,no-store'
      return res
    }

    // 定制码不是200，则判定为错误。
    if (response.status !== 200) {
      if (!hasShowMessage) {
        ElMessage({
          message: res.message || 'Error',
          type: 'warning',
          duration: 5 * 1000,
        })
        hasShowMessage = true
      }

      setTimeout(() => {
        hasShowMessage = false
      }, 1000)

      // 50008:非法令牌;50012:其他客户端登录;TOKEN_IS_EXPIRED:令牌过期,security_1001:token失效;
      if (
        res.code === 50008 ||
        res.code === 50012 ||
        res.code === 'TOKEN_IS_EXPIRED' ||
        res.code === 'USER_CONTEXT_MISS' ||
        res.code === 'security_1001' ||
        res.code === 'security_1002'
      ) {
        // 重新登陆
        ElMessageBox.confirm('您已经注销，您可以取消此页面，或重新登录', '确定退出', {
          confirmButtonText: '重新登录',
          // cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          // Cookies.set(TokenKey, '', {
          //   domain: location.hostname,
          // })
          localStorage.removeItem(TokenKey)

          sessionStorage.setItem('isNormalLogin', '0')
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      if (res.code === 'SUCCESS' || res.code === '200') {
        return res
      } else {
        if (!response.config.isNoPop && !hasShowMessage) {
          if (res.code === 'USER_CONTEXT_MISS' && import.meta.env.VITE_APP_LZOS) {
            if (!hasResetLoginWays) {
              hasResetLoginWays = true
              store.dispatch('user/resetToken').then(() => {
                sessionStorage.setItem('isNormalLogin', '0')
                hasResetLoginWays = false
                location.reload()
              })
            }
          } else {
            ElNotification({
              title: '提示',
              message: res.message,
              type: 'warning',
            })
            hasShowMessage = true
            setTimeout(() => {
              hasShowMessage = false
            }, 1000)
          }
        }

        // 50008:非法令牌;50012:其他客户端登录;TOKEN_IS_EXPIRED:令牌过期,security_1001:token失效;
        if (
          res.code === 50008 ||
          res.code === 50012 ||
          res.code === 'TOKEN_IS_EXPIRED' ||
          res.code === 'USER_CONTEXT_MISS' ||
          res.code === 'security_1001' ||
          res.code === 'security_1002'
        ) {
          // 重新登陆
          // Cookies.set(TokenKey, '', {
          //   domain: location.hostname,
          // })
          localStorage.removeItem(TokenKey)
          sessionStorage.setItem('isNormalLogin', '0')
          setTimeout(() => {
            store.dispatch('user/resetToken').then(() => {
              location.reload()
            })
          }, 1000)
        }
        if (response.config.isNoPop) {
          return res
        }
        return false
      }
    }
  },
  (error) => {
    //前端手动取消请求接口
    if (axios.isCancel(error)) {
      console.log('请求取消')
      return Promise.reject(error)
    }

    const res = error?.response?.data
    if (!res) {
      return false
    }
    if (
      res.code === 50008 ||
      res.code === 50012 ||
      res.code === 'TOKEN_IS_EXPIRED' ||
      res.code === 'USER_CONTEXT_MISS' ||
      res.code === 'security_1001' ||
      res.code === 'security_1002'
    ) {
      // 重新登陆
      // Cookies.set(TokenKey, '', {
      //   domain: location.hostname,
      // })
      localStorage.removeItem(TokenKey)
      let msg = '登录已过期，请重新登录'
      if (res.code === 'USER_CONTEXT_MISS') {
        msg = error.message
      }
      if (!hasShowMessage) {
        ElMessage({
          message: msg,
          type: 'warning',
          duration: 2000,
        })
        hasShowMessage = true
      }
      // 重新登陆
      setTimeout(() => {
        hasShowMessage = false
        store.dispatch('user/resetToken').then(() => {
          router.push({ name: 'ConvergencePage' })
        })
      }, 1000)
    } else {
      if (!hasShowMessage) {
        ElMessage({
          message: error.message,
          type: 'warning',
          duration: 5 * 1000,
        })
        hasShowMessage = true
      }
      setTimeout(() => {
        hasShowMessage = false
      }, 1000)
    }
    return Promise.reject(error)
  },
)

export default service
