// 基础变量
$nancalui-global-bg: var(--nancalui-global-bg, #f3f6f8); // 全局带底色背景
$nancalui-global-bg-normal: var(--nancalui-global-bg-normal, #ffffff); // 全局白色背景
$nancalui-base-bg: var(--nancalui-base-bg, #ffffff); // 基础区块背景白色
$nancalui-base-bg-dark: var(--nancalui-base-bg-dark, #333854); // 基础区块背景深色(固定)
$nancalui-brand: var(--nancalui-brand, #447dfd); // 品牌色
$nancalui-brand-foil: var(--nancalui-brand-foil, #859bff); // 品牌色辅助色、正衬色
$nancalui-brand-hover: var(--nancalui-brand-hover, #6e9eff); // 品牌色高亮色（加亮）
$nancalui-brand-active: var(--nancalui-brand-active, #2f5cd6); // 品牌色激活色（加深）
$nancalui-brand-active-focus: var(--nancalui-brand-active-focus, #0069af); // 品牌色焦点色（重度加深）
$nancalui-contrast: var(--nancalui-contrast, #f63838); // 品牌色撞色、对比色、反衬色、第二品牌色
$nancalui-text: var(--nancalui-text, #252b3a); // 正文文本
$nancalui-text-weak: var(--nancalui-text-weak, #575d6c); // 弱化的正文信息（手风琴子项，表头）
$nancalui-aide-text: var(--nancalui-aide-text, #8a8e99); // 辅助文本、帮助信息（面包屑）
$nancalui-aide-text-stress: var(--nancalui-aide-text-stress, #575d6c); // 辅助文本、帮助信息里的强调色
$nancalui-placeholder: var(--nancalui-placeholder, #8a8e99); // 占位符
$nancalui-light-text: var(--nancalui-light-text, #ffffff); // 有色深色背景下字体颜色（固定）
$nancalui-dark-text: var(--nancalui-dark-text, #252b3a); // 有色浅色背景下字体颜色（固定）
$nancalui-link: var(--nancalui-link, #2f5cd6); // 链接文本颜色
$nancalui-link-active: var(--nancalui-link-active, #0069af); // 链接文本悬停/激活颜色
$nancalui-link-light: var(--nancalui-link-light, #67c7ff); // 深色背景下链接文本颜色
$nancalui-link-light-active: var(--nancalui-link-light-active, #94dfff); // 深色背景下链接文本悬停/激活颜色
$nancalui-line: var(--nancalui-line, #c8c9cc); // 边框分割线，仅用于边框
$nancalui-dividing-line: var(--nancalui-dividing-line, #e1e1e1); // 内容分割线，用于内容之间的分割
$nancalui-block: var(--nancalui-block, #ffffff); // 大面积的不可折叠区块的背景色（例如顶部导航背景色）
$nancalui-area: var(--nancalui-area, #f8f8f8); // 可折叠区块的背景色（展开区域颜色）
$nancalui-danger: var(--nancalui-danger, #f66f6a); // 失败、错误、告警
$nancalui-warning: var(--nancalui-warning, #fac20a); // 警告、需注意类型提示
$nancalui-waiting: var(--nancalui-waiting, #9faad7); // 等待中
$nancalui-success: var(--nancalui-success, #50d4ab); // 成功、正确
$nancalui-info: var(--nancalui-info, #447dfd); // 通知、一般提示、执行中
$nancalui-initial: var(--nancalui-initial, #f0f7ff); // 初始化、未执行
$nancalui-unavailable: var(--nancalui-unavailable, #f7f8fa); // 不可用、禁用状态
$nancalui-shadow: var(--nancalui-shadow, rgba(0, 0, 0, 0.2)); // 阴影色
$nancalui-light-shadow: var(--nancalui-light-shadow, rgba(0, 0, 0, 0.1)); // 弱化阴影色

// 图标
$nancalui-icon-text: var(--nancalui-icon-text, #252b3a); // 文字图标颜色，同 正文颜色
$nancalui-icon-bg: var(--nancalui-icon-bg, #ffffff); // svg图标 背景色
$nancalui-icon-fill: var(--nancalui-icon-fill, #d3d5d9); // svg图标 灰色填充色
$nancalui-icon-fill-hover: var(--nancalui-icon-fill-hover, #adb5ce); // svg图标 灰色填充色悬停反馈色
$nancalui-icon-fill-active: var(--nancalui-icon-fill-active, #447dfd); // svg图标 高亮填充色（激活状态）
$nancalui-icon-fill-active-hover: var(--nancalui-primary-hover, #2f5cd6); // svg图标 高亮填充色悬停反馈色
// 表单
$nancalui-form-control-line: var(--nancalui-form-control-line, #c8c9cc); // 表单控件边框色，同 边框分割线
$nancalui-form-control-line-hover: var(--nancalui-form-control-line-hover, #575d6c); // 表单控件边框悬停反馈色
$nancalui-form-control-line-active: var(--nancalui-primary-hover, #447dfd); // 表单控件边框激活色，用于获得焦点
$nancalui-form-control-line-active-hover: var(--nancalui-primary-hover-hover, #0069af); // 表单控件边框激活色，用于获得焦点且悬停
// 列表
$nancalui-list-item-active-bg: var(--nancalui-list-item-active-bg, #447dfd); // 列表类型单选选中背景
$nancalui-list-item-active-text: var(
  --nancalui-list-item-active-text,
  #ffffff
); // 列表类型单选选中背景搭配文本，同 有色深色背景下字体颜色（固定）
$nancalui-list-item-active-hover-bg: var(--nancalui-list-item-active-hover-bg, #2f5cd6); // 列表类型单选选中背景悬停反馈色（仅用于分页等）
$nancalui-list-item-hover-bg: var(--nancalui-list-item-hover-bg, #f2f5fc); // 列表类型普通选项悬停背景
$nancalui-list-item-hover-text: var(--nancalui-list-item-hover-text, #2f5cd6); // 列表类型普通选项悬停背景搭配文本
$nancalui-list-item-selected-bg: var(--nancalui-list-item-selected-bg, #f0f7ff); // 列表类型多选被选中的行色，仅用于表格类
$nancalui-list-item-strip-bg: var(--nancalui-list-item-strip-bg, #f2f5fc); // 列表类型斑马纹色，仅用于表格类
// 禁用
$nancalui-disabled-bg: var(--nancalui-disabled-bg, #f7f8fa); // disabled背景颜色
$nancalui-disabled-line: var(--nancalui-disabled-line, #e1e1e1); // disabled边框颜色
$nancalui-disabled-text: var(--nancalui-disabled-text, #c8c9cc); // disabled文字颜色
$nancalui-primary-disabled: var(--nancalui-primary-disabled, #94dfff); //主要按钮disabled状态文字颜色
$nancalui-icon-fill-active-disabled: var(--nancalui-icon-fill-active-disabled, #94dfff); // svg图标激活状态禁用色
// 特殊背景色
$nancalui-label-bg: var(--nancalui-label-bg, #eef0f5); // 默认标签化选项背景色
$nancalui-connected-overlay-bg: var(--nancalui-connected-overlay-bg, #ffffff); // 有连接点的弹出菜单层背景色
$nancalui-connected-overlay-line: var(--nancalui-connected-overlay-line, #2f5cd6); // 有连接点的弹出菜单层边框色
$nancalui-fullscreen-overlay-bg: var(--nancalui-fullscreen-overlay-bg, #ffffff); // 全屏类型的弹出内容层背景色（模态弹窗）
$nancalui-feedback-overlay-bg: var(--nancalui-feedback-overlay-bg, #464d6e); // 信息提示反馈类型的漂浮层背景色（toast、popover）
$nancalui-feedback-overlay-text: var(--nancalui-feedback-overlay-text, #e1e1e1); // 信息提示反馈类型的漂浮层背景色搭配文本色
$nancalui-embed-search-bg: var(--nancalui-embed-search-bg, #f2f5fc); // 被嵌套的无边框搜索框背景色
$nancalui-embed-search-bg-hover: var(--nancalui-embed-search-bg-hover, #eef0f5); // 被嵌套的无边框搜索框背景色
$nancalui-float-block-shadow: var(--nancalui-float-block-shadow, rgba(94, 124, 224, 0.3)); // 特殊浮层背景色（待修正）
$nancalui-highlight-overlay: var(--nancalui-highlight-overlay, rgba(255, 255, 255, 0.8)); // 局部半透明全局浮层背景色（比如底部）
$nancalui-range-item-hover-bg: var(--nancalui-range-item-hover-bg, #f0f7ff); // datepicker范围中的日期hover的反馈背景色

// 按钮
$nancalui-primary: var(--nancalui-primary, #447dfd); // 主要按钮，同品牌色
$nancalui-primary-hover: var(--nancalui-primary-hover, #6e9eff); // 主要按钮悬停
$nancalui-primary-active: var(--nancalui-primary-active, #0069af); // 主要按钮激活

$nancalui-contrast-hover: var(--nancalui-contrast-hover, #d64a52); // 突出按钮悬停
$nancalui-contrast-active: var(--nancalui-contrast-active, #e72b2d); // 突出按钮激活

// 状态
$nancalui-success-active: var(--nancalui-primary-active, #0069af); // 成功激活色

$nancalui-danger-line: var(--nancalui-danger-line, #f66f6a); // 失败边框
$nancalui-danger-bg: var(--nancalui-danger-bg, #ffeeed); // 失败底色

$nancalui-warning-line: var(--nancalui-warning-line, #fa9841); // 警告边框
$nancalui-warning-bg: var(--nancalui-warning-bg, #fff3e8); // 警告底色

$nancalui-info-line: var(--nancalui-info-line, #447dfd); // 通知边框
$nancalui-info-bg: var(--nancalui-info-bg, #f2f5fc); // 通知底色

$nancalui-success-line: var(--nancalui-success-line, #75EBC2); // 成功边框
$nancalui-success-bg: var(--nancalui-success-bg, #edfff9); // 成功底色
$nancalui-primary-line: var(--nancalui-primary-line, #447dfd); // 主要边框
$nancalui-primary-bg: var(--nancalui-primary-bg, #447dfd); // 主要底色

$nancalui-default-line: var(--nancalui-default-line, #447dfd); // 默认边框
$nancalui-default-bg: var(--nancalui-default-bg, #f3f6f8); // 默认底色
