<template>
  <n-modal v-model="visiable" width="1100px" title="角色授权" @close="onClose(false)">
    <CfTable
      v-loading="state.loading"
      actionWidth="0"
      :calcHeight="false"
      :table-head-titles="state.tableHeadTitles"
      :tableConfig="{
        data: state.tableData,
        rowKey: 'id',
      }"
      :paginationConfig="{
        total: state.pagination.total,
        pageSize: state.pagination.pageSize,
        currentPage: state.pagination.currentPage,
        onCurrentChange: (v) => {
          state.pagination.currentPage = v
          onSearch(false)
        },
        onSizeChange: (v) => {
          state.pagination.pageSize = v
          onSearch()
        },
      }"
    >
      <template #tagList="{ row }">
        <level-tag :bgColor="row.bgColor" :borderColor="row.borderColor" :color="row.color">{{
          row.secretLevelName
        }}</level-tag>
      </template>
    </CfTable>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose()">取消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>
<script setup>
  import api from '@/api/index'
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
  })
  const state = reactive({
    tableData: [],
    tagList: {
      非涉密: {
        color: '#1AA4EE',
        borderColor: 'rgba(26, 164, 238, 0.40)',
        bgColor: 'rgba(26, 164, 238, 0.08)',
      },
      一般: {
        color: '#FE8624',
        borderColor: '#FFBA70',
        bgColor: '#FFF4E6',
      },
      重要: {
        color: '#D40000',
        borderColor: '#EF7777',
        bgColor: '#FFEDED',
      },
      核心: {
        color: '#224ECD',
        borderColor: 'rgba(34, 78, 205, 0.40)',
        bgColor: 'rgba(34, 78, 205, 0.08)',
      },
    },
    tableHeadTitles: [
      { prop: 'username', name: '工号' },
      { prop: 'name', name: '姓名' },
      { prop: 'departmentFullName', name: '部门' },
      { prop: 'secretLevelName', name: '密级' }, //, slot: 'tagList'
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    loading: false,
  })
  // 获取人员列表
  function onSearch(init = true) {
    if (init) {
      state.pagination.currentPage = 1
    }
    state.loading = true
    api.system
      .queryRoleUserListPage({
        condition: { roleId: props.id },
        pageNum: state.pagination.currentPage,
        pageSize: state.pagination.pageSize,
      })
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data.list.map((i) => {
            const { color, bgColor, borderColor } = state.tagList[i.secretLevelName]
            return {
              ...i,
              bgColor,
              color,
              borderColor,
            }
          })
          state.pagination.total = res.data.total
        }
      })
      .catch((e) => {
        console.log(e)
        state.loading = false
        state.tableData = []
      })
  }
  const emit = defineEmits(['update:modelValue'])
  function onClose() {
    visiable.value = false
    emit('update:modelValue', false)
  }

  const visiable = ref(false)
  watch(
    () => props.modelValue,
    (val) => {
      console.log('props.modelValue', val)
      visiable.value = val
      if (props.id) {
        onSearch()
      }
    },
  )
</script>
<style lang="scss" scoped></style>
