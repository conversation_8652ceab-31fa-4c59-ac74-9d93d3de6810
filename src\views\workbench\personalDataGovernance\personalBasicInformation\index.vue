<template>
  <div class="container">
    <div class="content-box">
      <div class="content-box-title">基本信息</div>
      <div class="content-box-row">
        <div class="content-box-row-info">
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">人员编码：</div>
              <div class="value">{{ state.info.code }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">姓名：</div>
              <div class="value">{{ state.info.name }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">证件类型：</div>
              <div class="value">{{ state.info.certificateType }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">证件号码：</div>
              <div class="value">{{ state.info.certificateNumber }}</div>
            </div>
          </div>
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">性别：</div>
              <div class="value">{{ state.info.sex }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">出生日期：</div>
              <div class="value">{{ state.info.birthDate }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">籍贯：</div>
              <div class="value">{{ state.info.nativePlace }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">户口性质：</div>
              <div class="value">{{ state.info.householdRegistrationType }}</div>
            </div>
          </div>
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">民族：</div>
              <div class="value">{{ state.info.nationality }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">政治面貌：</div>
              <div class="value">{{ state.info.politicsStatus }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">健康状况：</div>
              <div class="value">{{ state.info.healthCndition }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">婚姻状况：</div>
              <div class="value">{{ state.info.maritalStatus }}</div>
            </div>
          </div>
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">学历：</div>
              <div class="value">{{ state.info.educationBackground }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">学位：</div>
              <div class="value">{{ state.info.degree }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">专业技术职务：</div>
              <div class="value">{{ state.info.technicalPosts }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">参加工作日期：</div>
              <div class="value">{{ state.info.joiningWorkDate }}</div>
            </div>
          </div>
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">户口所在地：</div>
              <div class="value">{{ state.info.householdRegistrationPlace }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">入党(团)日期：</div>
              <div class="value">{{ state.info.joiningPartyDate }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">离退休日期：</div>
              <div class="value">{{ state.info.retirementDate }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">档案所在地：</div>
              <div class="value">{{ state.info.archivesLocation }}</div>
            </div>
          </div>
          <div class="flex-box">
            <div class="flex-box-item">
              <div class="name">技能鉴定等级：</div>
              <div class="value">{{ state.info.skillAppraisalLevel }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">集团人员编码：</div>
              <div class="value">{{ state.info.groupPersonnelCode }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">姓名拼音：</div>
              <div class="value">{{ state.info.namePinyin }}</div>
            </div>
            <div class="flex-box-item">
              <div class="name">公积金账号：</div>
              <div class="value">{{ state.info.providentFundAccount }}</div>
            </div>
          </div>
        </div>
        <img v-if="state.info.personnelPhoto" class="pic" :src="state.info.personnelPhoto" />
      </div>
      <div class="content-box-title">任职记录</div>
      <div class="table-box">
        <CfTable
          :key="state.key"
          :isDisplayAction="false"
          :table-head-titles="state.tableHeadTitles"
          :paginationConfig="{
            total: state.pageInfo.total,
            pageSize: state.pageInfo.pageSize,
            currentPage: state.pageInfo.currentPage,
            onCurrentChange: (v) => {
              state.pageInfo.currentPage = v
              initTable()
            },
            onSizeChange: (v) => {
              state.pageInfo.pageSize = v
              initTable(true)
            },
          }"
          :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
          }"
        >
        </CfTable>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { workbenchUserInfo, workbenchUserPosition } from '@/api/dataManage.js'
  const state = reactive({
    loading: false,
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'number', name: '序号' },
      { prop: 'startDate', name: '开始日期' },
      { prop: 'endDate', name: '结束日期' },
      { prop: 'employeeNumber', name: '员工号' },
      { prop: 'organization', name: '组织' },
      { prop: 'personnelCategory', name: '人员类别' },
      { prop: 'department', name: '部门' },
      { prop: 'position', name: '岗位' },
      { prop: 'positionSequence', name: '岗位序列' },
      { prop: 'onDuty', name: '在岗', width: 'auto' },
    ],
    info: {},
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        fieldName: null,
      },
    }
    state.loading = true
    workbenchUserPosition(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          res.data.list.forEach((item, index) => {
            item.number = index + 1
          })
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = { list: [] }
        state.loading = false
      })
  }

  onMounted(() => {
    workbenchUserInfo().then((res) => {
      if (res.success) {
        state.info = res.data
      }
    })
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: calc(100vh - 96px);
    padding: 16px;
    .content-box {
      height: 100%;
      background-color: #ffffff;
      border-radius: 2px;
      position: relative;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 18px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
      &-row {
        padding: 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        &-info {
          width: 100%;
          .flex-box {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-top: 16px;
            &-item {
              width: 25%;
              flex-shrink: 0;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              color: #606266;
              font-size: 14px;
              .name {
                width: 100px;
                text-align: right;
              }
              .value {
                width: calc(100% - 100px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                text-align: left;
              }
            }
          }
        }
        .pic {
          flex-shrink: 0;
          display: block;
          width: 220px;
          height: auto;
          max-height: 213px;
        }
      }
      .table-box {
        padding: 0 16px;
        height: calc(100% - 318px);
      }
    }
  }
</style>
