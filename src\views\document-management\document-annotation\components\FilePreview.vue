<template>
    <component ref="previewPintRef" :is="componentMap[$attrs.type]" v-bind="$attrs" />
</template>
<script setup>
import Excel from '@/views/data-management/resource-library/components/Excel.vue'
import Img from './Img.vue'
import PDF from './PDFJS.vue'
import Txt from '@/views/data-management/resource-library/components/Txt.vue'
import Word from './Word.vue'
import * as d3 from 'd3'
import api from '@/api/index'
const previewPintRef = ref(null)
const state = reactive({
    loading: false,
});
const componentMap = {
    image_png: Img,
    image_jpg: Img,
    image_jpeg: Img,
    pdf: PDF,
    word: Word,
    excel: Excel,
    text_txt: Txt,
}
defineExpose({
    disable() {
        previewPintRef?.value?.disable?.()
    },
    enable() {
        previewPintRef?.value?.enable?.()
    },
    getImageMark(id) {
        return previewPintRef?.value?.getImageMark?.(id)
    }
})
</script>
<style lang="scss" scoped>
@import '/src/styles/variables.scss';
@import '/src/styles/cf.scss';

:deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;

    .img-Preview {
        width: 100% !important;
        height: max-content;
        min-height: 100%;
        overflow-x: unset;
        overflow-y: unset;
    }
}
</style>
<style>
#wave-container,
#tag-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100000;
    width: 0;
    height: 0;
}
</style>
