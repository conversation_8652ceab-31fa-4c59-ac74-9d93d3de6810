<template>
  <n-drawer
    v-model="showDirDialog"
    title=""
    :size="720"
    :esc-key-closeable="false"
    :close-on-click-overlay="true"
    :before-close="cancel"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">查看日志</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="cancel" />
      </div>
      <div class="n-drawer-body-content">
        <pre class="log">{{ state.logText }}</pre>
      </div>
      <div class="n-drawer-body-footer">
        <n-button @click.prevent="cancel">取 消</n-button>
        <n-button variant="solid" :disabled="!state.logText" @click.prevent="downLogFn"
          >下 载</n-button
        >
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import api from '@/api/index'
  import { timestampToTime } from '@/const/public.js'
  const showDirDialog = ref(false)
  const state = reactive({
    logText: '',
    taskName: '',
  })

  const cancel = () => {
    showDirDialog.value = false
  }
  const getRealTimeLogList = async (id) => {
    api.realTimeDevelop.getRealTimeLogList(id).then((res) => {
      state.logText = res.data || ''
    })
  }
  // 下载日志
  const downLogFn = () => {
    if (state.logText) {
      const blob = new Blob([state.logText], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName = state.taskName + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }
  defineExpose({
    open(node) {
      state.taskName = node.taskName
      getRealTimeLogList(node?.id)
      showDirDialog.value = true
    },
  })
</script>
<style lang="scss" scoped>
  .n-drawer-body {
    height: 100%;
    .n-drawer-body-content {
      .log {
        color: #1d2129;
        font-size: 12px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
      &::-webkit-scrollbar {
        width: 10px !important; // 横向滚动条
        height: 10px !important; // 纵向滚动条 必写
      }
    }
    .n-drawer-body-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64px;
      padding: 0 16px;
      .footer-btn {
        width: 62px;
        height: 32px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        background: #1e89ff;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background-color: #6e9eff;
        }
      }
    }
  }
</style>
