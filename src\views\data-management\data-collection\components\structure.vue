<!-- 结构化数据采集 -->
<template>
  <!-- 模型构建 -->
  <div class="data-collection-link-mapping">
    <div class="box-bottom">
      <n-form
        ref="ruleForm"
        :data="state.ruleForm"
        :rules="state.rules"
        label-width="150px"
        class="structure-ruleForm"
        label-align="start"
        :key="state.key"
      >
        <div class="top-line">
          <n-form-item field="dataSourceId" label="数据源：">
            <n-select
              v-model="state.ruleForm.dataSourceId"
              placeholder="请选择"
              filter
              allow-clear
              :options="state.dataSourceOptions"
              :disabled="state.disabled || state.isOffline"
              @value-change="dataSourceChange"
            />
          </n-form-item>
          <n-form-item class="dataSourceTable" field="dataSourceTable" label="数据源表：">
            <div class="top-line-tag">
              <el-select
                v-model="state.ruleForm.dataSourceTable"
                clearable
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                :disabled="state.disabled || state.isOffline"
                :remote-method="filterTable"
                @blur="blurFn"
                @change="
                  (value) => {
                    if (value) {
                      let itemArr = state.dataSourceTableOptions.filter((val) => val.name === value)
                      dataSourceTableChange(itemArr[0])
                    } else {
                      dataSourceTableChange('')
                    }
                  }
                "
                :loading="state.tableLoading"
              >
                <el-option
                  v-for="item in state.dataSourceTableOptions"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </div>
          </n-form-item>
        </div>
        <div class="top-line">
          <n-form-item field="filterSql" label="数据过滤：">
            <n-input
              v-model="state.ruleForm.filterSql"
              maxlength="1024"
              placeholder="请输入过滤条件，如:type=1"
            />
          </n-form-item>
        </div>
        <div class="top-line">
          <n-form-item label="采集规则：" field="collectWay" class="collect-rule">
            <n-radio-group direction="row" v-model="state.ruleForm.collectWay">
              <n-radio
                v-for="item in state.wayOptions"
                :key="item.value"
                :value="item.value"
                :disabled="state.disabled || state.isOffline"
                >{{ item.label }}</n-radio
              >
            </n-radio-group>
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.collectWay === 'INCREMENT'"
            field="incrementColumn"
            label="增量字段："
          >
            <n-select
              v-model="state.ruleForm.incrementColumn"
              placeholder="请选择"
              filter
              allow-clear
              :options="state.columnOptions"
              :disabled="state.disabled || state.isOffline"
              @value-change="
                (item) => {
                  if (item) {
                    console.log(item)
                    state.ruleForm.incrementColumnType = item.dataType || item.convertFileType
                  } else {
                    state.ruleForm.incrementColumnType = ''
                  }
                }
              "
            />
          </n-form-item>
        </div>
        <div class="content-title">
          <span>表信息</span>
        </div>
        <div class="top-line">
          <n-form-item field="confidentialityLevel" label="表密级：">
            <n-select
              v-model="state.ruleForm.confidentialityLevel"
              placeholder="请选择"
              filter
              :disabled="state.isOffline"
              allow-clear
              :options="state.confidentialityLevelOptions"
            />
          </n-form-item>
          <n-form-item field="tagList" label="标签：">
            <div class="top-line-tag">
              <el-tree-select
                ref="selectTree"
                v-model="state.ruleForm.tagList"
                :data="state.targetOptions"
                style="width: 100%"
                node-key="key"
                :props="{
                  label: 'name',
                  value: 'key',
                  children: 'children',
                }"
                multiple
                show-checkbox
                :render-after-expand="false"
                filterable
                clearable
              />
            </div>
          </n-form-item>
        </div>
        <!--        <div v-if="state.ruleForm.dataSourceTable">-->
        <!--          <div class="gray-row"></div>-->
        <!--          <div class="white-row"></div>-->
        <!--          <div class="content-title">-->
        <!--            <span>预览表</span>-->
        <!--          </div>-->
        <!--          <div class="mid-line">-->
        <!--            <n-public-table-->
        <!--              :key="state.key"-->
        <!--              :isDisplayAction="false"-->
        <!--              :table-head-titles="state.tableHeadPreviewTitles"-->
        <!--              :showPagination="false"-->
        <!--              :tableHeight="240"-->
        <!--              :tableData="state.tablePreviewData"-->
        <!--            />-->
        <!--          </div>-->
        <!--          <div class="white-row"></div>-->
        <!--        </div>-->
        <div class="gray-row"></div>
        <div class="white-row"></div>
        <div class="content-title">
          <span>设置目标表</span>
        </div>
        <!--        <div class="top-line">-->
        <!--          <n-form-item class="check-style" label="目标表：" field="sinkModelCreateMethod">-->
        <!--            <n-radio-group-->
        <!--              direction="row"-->
        <!--              :disabled="state.disabled"-->
        <!--              v-model="state.ruleForm.sinkModelCreateMethod"-->
        <!--              @change="modelCreatedModeChange"-->
        <!--            >-->
        <!--              <n-radio value="NEW">已有数据表</n-radio>-->
        <!--              <n-radio value="REVERSE">新建数据表</n-radio>-->
        <!--            </n-radio-group>-->
        <!--          </n-form-item>-->
        <!--          <n-form-item-->
        <!--            v-if="state.ruleForm.sinkModelCreateMethod === 'NEW'"-->
        <!--            label="选择表："-->
        <!--            field="modeTable"-->
        <!--          >-->
        <!--            <n-select-->
        <!--              v-model="state.ruleForm.modeTable"-->
        <!--              placeholder="请选择"-->
        <!--              filter-->
        <!--              allow-clear-->
        <!--              :options="state.modeTableOptions"-->
        <!--              :disabled="state.disabled"-->
        <!--              @value-change="modeTableChange"-->
        <!--            />-->
        <!--          </n-form-item>-->
        <!--        </div>-->
        <div class="mid-line">
          <n-form-item field="name" label="表英文名称：" class="dif">
            <n-input
              v-model="state.ruleForm.name"
              maxlength="80"
              placeholder=""
              @input="nameInput"
              @blur="nameBlur"
            />
          </n-form-item>
          <n-form-item field="cnName" label="表中文名称：">
            <n-input
              :disabled="
                state.ruleForm.sinkModelCreateMethod === 'NEW' || state.disabled || state.isOffline
              "
              v-model="state.ruleForm.cnName"
              maxlength="255"
              placeholder=""
            />
          </n-form-item>
        </div>
        <div class="mid-line">
          <n-form-item label="表类型：" field="overlayOrNot">
            <div class="overlayOrNot-box">
              <n-checkbox
                :isShowTitle="false"
                label="非分区表"
                :disabled="state.isOffline"
                v-model="state.ruleForm.overlayOrNot"
              />
              <n-tooltip
                class="tree-btn"
                content="默认创建hive非分区表，取消勾选则创建分区表，不支持指定分区，系统将自动分配分区。"
                position="top"
                :enterable="false"
              >
                <SvgIcon class="illustrate" icon="icon-illustrate" />
              </n-tooltip>
            </div>
          </n-form-item>
        </div>
        <div
          class="bottom-line"
          v-loading="state.loading"
          v-show="state.tableRightData.list.length || state.tableLeftData.list.length"
        >
          <div class="gray-row"></div>
          <div class="white-row"></div>
          <div class="content-title">
            <span>字段映射</span>
          </div>
          <!-- 连线映射 -->
          <div
            v-show="
              (state.ruleForm.sinkModelCreateMethod === 'NEW' || state.disabled) &&
              (state.rightData.length || state.leftData.length)
            "
            class="link-mapping-box"
          >
            <sceneTableLinkDetail v-if="state.disabled" ref="sceneTableLink" :oneline="true" />
            <sceneTableLink v-else ref="sceneTableLink" :oneline="true" />
          </div>
          <!-- 表格逆向 -->
          <div
            v-show="state.ruleForm.sinkModelCreateMethod !== 'NEW' && !state.disabled"
            class="model-reverse-box"
            id="model-reverse-box"
          >
            <div class="reverse-box-left child">
              <div
                class="table-title"
                :title="
                  state.ruleForm.dataSourceName +
                  (state.ruleForm.dataSourceTable ? ' - ' + state.ruleForm.dataSourceTable : '')
                "
                >数据源：{{
                  state.ruleForm.dataSourceName +
                  (state.ruleForm.dataSourceTable ? ' - ' + state.ruleForm.dataSourceTable : '')
                }}</div
              >
              <el-table-v2
                :columns="state.tableHeadTitles"
                :header-height="36"
                :row-height="36"
                header-class="table-header"
                row-class="table-row"
                :data="state.tableLeftData.list"
                :width="state.tableWidth"
                :height="state.tableHeight"
                fixed
              />
            </div>
            <div class="reverse-box-right child">
              <div class="table-title" :title="state.ruleForm.name"
                >目的表：{{ state.ruleForm.name }}</div
              >
              <el-table-v2
                :columns="state.tableHeadTitles1"
                :data="state.tableRightData.list"
                :header-height="36"
                :row-height="36"
                header-class="table-header"
                row-class="table-row"
                :width="state.tableWidth"
                :height="state.tableHeight"
                fixed
              />
            </div>
          </div>
          <div class="ddl">
            <n-button class="ddl-btn" variant="text" color="primary" @click.prevent.stop="ddlFn"
              ><SvgIcon class="icon" icon="icon-ddl" title="智能" />DDL</n-button
            >
            <div v-if="state.ddl" class="ddl-content">
              <n-textarea
                v-model="state.ddl"
                placeholder="请输入描述信息"
                :autosize="{ minRows: 3 }"
                readonly
              />
            </div>
          </div>
        </div>
      </n-form>
    </div>
    <!--    <div-->
    <!--      style="width: 0"-->
    <!--      v-html="-->
    <!--        `<style>-->
    <!--        .top-line{-->
    <!--          ${toStyle}-->
    <!--        }-->
    <!--    </style>`-->
    <!--      "-->
    <!--    >-->
    <!--    </div>-->
  </div>
</template>
<script lang="jsx">
  import { ref, toRefs, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
  import api from '@/api/index'
  import * as sqlFormatter from 'sql-formatter'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import sceneTableLink from '../scene-table-link'
  import sceneTableLinkDetail from '../scene-table-link/detail'
  import { collectJobDDL } from '@/api/dataManage'
  import {
    checkCName,
    checkNameExcludeKeywords,
    onKeydownPositiveInteger,
    onKeyupPositiveInteger,
  } from '@/utils/validate'

  export default {
    name: '',
    components: { sceneTableLink, sceneTableLinkDetail },
    props: {
      envType: {
        type: String,
        default: null,
      },
      listenEnvTypeChange: {
        type: Boolean,
        default: true,
      },
      id: {
        type: Number,
        default: null,
      },
    },
    emit: ['dataSourceTypeChange'],
    setup(props, { emit }) {
      const store = useStore()
      const sceneTableLink = ref()
      const sceneTableLinkFullPage = ref()
      const selectTree = ref()
      // css转换 计算属性
      const toStyle = computed(() => {
        return state.ruleForm.tagList?.map((_, i) => {
          const item = state.targetOptions.find((item) => item.name === _)
          const [color, background] = item.color.split('_')
          return `
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag {
          background: ${background} !important;
          border: 1px solid var(---, ${color}) !important;
          color: ${color} !important;
        }
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag  .el-tag__close{
            color: ${color} !important;
         }
        `
        }).join(`
        `)
      })

      const state = reactive({
        type: 'add',
        key: 1,
        targetKey: 1,
        userName: '',
        datasourceType: '',
        ddl: '',
        tableRightKey: 1,
        popoverKey: 1,
        tableHeadPreviewTitles: [],
        tablePreviewData: { list: [] },
        tableData: {},
        tableWidth: 500,
        tableHeight: 300,
        configData: { selectRow: [] },
        loading: false,
        tableLoading: false,
        tableFocus: false,
        tableSearchKey: 1,
        disabled: false, // 创建状态才能编辑模型
        isOffline: false, // 下架状态只能编辑字段映射
        collected: false, // 表是否已采集
        ruleForm: {
          dataSourceName: '',
          database: '',
          dataSourceId: '', //绑定的数据源id
          dataSourceTable: '', //数据源id下表名
          sinkModelCreateMethod: 'REVERSE',
          modeTable: '',
          modeFeign: '',
          modeFeignName: '',
          sinkModelId: '',
          modeTableName: '',
          confidentialityLevel: '',
          overlayOrNot: true,
          collectWay: 'FULL',
          incrementColumn: '',
          incrementColumnType: '',
          filterSql: '', // 数据过滤
          cnName: '', // 模型中文名
          name: '', // 模型英文名
          tagList: [], // 标签
        },
        rules: {
          dataSourceId: {
            type: 'number',
            required: true,
            message: '请选择数据源',
            trigger: 'change',
          },
          dataSourceTable: { required: true, message: '请选择数据源', trigger: 'change' },
          overlayOrNot: {
            required: true,
            type: 'boolean',
            message: '请选择表类型',
            trigger: 'change',
          },
          collectWay: {
            required: true,
            message: '请选择采集规则',
            trigger: 'change',
          },
          cnName: [
            {
              required: true,
              // validator: (...args) =>
              //   checkCName(...args, 'model', 'validModel', {
              //     nameType: 'CN',
              //     name: state.ruleForm.cnName,
              //     id: state.ruleForm.modeTable || null,
              //   }),
              message: '请输入表中文名',
              trigger: 'blur',
            },
          ],
          name: [
            {
              required: true,
              validator: (rule, value, callback) => {
                // 忽略大小写
                if (!value?.toLocaleUpperCase()?.startsWith('ODS_')) {
                  callback(new Error('表名必须以ODS_开头'))
                } else if (!/^ODS_[A-Za-z0-9_]*$/i.test(value)) {
                  callback(new Error('表名只能包含字母、数字和下划线'))
                } else {
                  api.dataManagement
                    .checkTargetExists({ id: props.id, tableName: value })
                    .then((res) => {
                      if (res.data?.exist) {
                        callback(new Error(res.data?.msg || 'ODS表重复，请修改表名后采集'))
                      } else {
                        callback()
                      }
                    })
                }
              },
              trigger: 'blur',
            },
          ],
          modeTable: [
            { type: 'number', required: true, message: '请选择数据源表', trigger: 'change' },
          ],
          sinkModelCreateMethod: [{ required: true, message: '请选择目标表', trigger: 'change' }],
          confidentialityLevel: { required: true, message: '请选择表密级', trigger: 'change' },
          incrementColumn: { required: true, message: '请选择增量字段', trigger: 'change' },
        },
        tableHeadTitles: [
          {
            key: 'selection',
            width: 50,
            cellRenderer: ({ rowData }) => {
              return (
                <div class='table-v-row'>
                  <div class='table-v-row-col checkbox'>
                    <n-checkbox
                      isShowTitle={false}
                      v-model={rowData.checked}
                      onChange={() => methods.checkboxChangeFn(rowData, 'col')}
                    />
                  </div>
                </div>
              )
            },
            headerCellRenderer: () => {
              return (
                <div class='table-v-header'>
                  <n-checkbox
                    v-if="HeaderCellSlotProps.column.dataKey === 'number'"
                    isShowTitle={false}
                    v-model={state.selectAll}
                    key={state.key}
                    onChange={() => methods.checkboxChangeFn(state.selectAll, 'all')}
                  />
                </div>
              )
            },
          },
          // 必须为name 否则渲染不出表头
          { prop: 'number', dataKey: 'number', title: '序号', name: '序号', width: 80 },
          {
            prop: 'oldcnName',
            dataKey: 'oldcnName',
            title: '字段中文名',
            name: '字段中文名',
            width: 100,
          },
          {
            prop: 'oldname',
            dataKey: 'oldname',
            title: '字段英文名',
            name: '字段英文名',
            width: 140,
          },
          {
            prop: 'oldfieldType',
            dataKey: 'oldfieldType',
            title: '字段类型',
            name: '字段类型',
            width: 140,
          },
          {
            prop: 'oldfieldLength',
            dataKey: 'oldfieldLength',
            title: '字段长度',
            name: '字段长度',
            width: 100,
          },
        ],
        tableHeadTitles1: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', dataKey: 'number', title: '序号', name: '序号', width: 80 },
          {
            prop: 'cnName',
            dataKey: 'cnName',
            title: '字段中文名',
            name: '字段中文名',
            slot: 'cnName',
            width: 100,
            cellRenderer: ({ rowData }) => {
              return (
                <div class='table-v-row-col'>
                  <n-input
                    key={state.tableRightKey}
                    disabled={state.disabled}
                    v-model={rowData.cnName}
                    placeholder=''
                    maxlength='30'
                  />
                </div>
              )
            },
          },
          {
            prop: 'name',
            dataKey: 'name',
            title: '字段英文名',
            name: '字段英文名',
            slot: 'name',
            width: 140,
            cellRenderer: ({ rowData }) => {
              return (
                <div class='table-v-row-col'>
                  <n-input
                    key={state.tableRightKey}
                    disabled={state.disabled}
                    v-model={rowData.name}
                    placeholder=''
                    maxlength='30'
                  />
                </div>
              )
            },
          },
          {
            prop: 'fieldType',
            dataKey: 'fieldType',
            title: '字段类型',
            name: '字段类型',
            slot: 'fieldType',
            width: 140,
            cellRenderer: ({ rowData }) => {
              return (
                <div class='table-v-row-col'>
                  <n-select
                    v-model={rowData.fieldType}
                    placeholder='请选择'
                    disabled={state.disabled}
                    key={state.tableRightKey}
                    onValueChange={() => methods.fieldTypeChange(rowData)}
                  >
                    {state.fieldTypeOptions.map((item) => (
                      <n-option key={item.name} name={item.name} value={item.name} />
                    ))}
                  </n-select>
                </div>
              )
            },
          },
          {
            prop: 'fieldLength',
            dataKey: 'fieldLength',
            title: '字段长度',
            name: '字段长度',
            slot: 'fieldLength',
            width: 100,
            cellRenderer: ({ rowData }) => {
              return (
                <div class='table-v-row-col'>
                  <n-input
                    v-model={rowData.fieldLength}
                    class={
                      rowData.fieldLength &&
                      rowData.fieldLength > 0 &&
                      rowData.fieldLength.toString().indexOf('.') === -1
                        ? ''
                        : ''
                    }
                    type='number'
                    placeholder=''
                    disabled={state.disabled || rowData.isRequiredFieldLength}
                    onBlur={() => methods.fieldBlur({ rowData })}
                    onKeydown={() => methods.onKeydownPositiveInteger($event, rowData.fieldLength)}
                    onKeyup={() => methods.onKeyupPositiveInteger($event, rowData.fieldLength)}
                    onFocus={() => methods.inputFocus($event)}
                    onPaste={() => {
                      return false
                    }}
                  />
                </div>
              )
            },
          },
        ],

        sourceTableData: [],
        authorizedTableData: [],
        allData: {
          name: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [1, 1],
          authorizedPage: [1],
        },
        queryData: '',
        editId: null,
        detailId: null, // 查看模式id

        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },

        dataSourceOptions: [],
        columnOptions: [],
        dataSourceTableOptions: [],
        data: [],
        tableLeftData: {
          list: [],
        }, // 左侧表格数据
        tableRightData: {
          list: [],
        }, // 右侧表格数据
        leftData: [], // 映射左侧数据
        rightData: [], // 映射右侧数据
        totalData: {}, // 所有数据
        activeModeTableItem: {}, // 选中的数据模型table
        layerId: null, //模型层id
        layerName: '', //模型层中文名
        //字段类型枚举
        fieldTypeOptions: [
          { name: 'STRING', cnName: '字符串' },
          // { name: 'BINARY', cnName: '' },
          { name: 'BOOLEAN', cnName: '布尔' },
          { name: 'DOUBLE', cnName: '' },
          { name: 'FLOAT', cnName: '浮点' },
          { name: 'TIMESTAMP', cnName: '时间戳' },
          { name: 'BIGINT', cnName: '' },
          { name: 'INT', cnName: '整数' },
          { name: 'TINYINT', cnName: '' },
          // { name: 'DECIMAL', cnName: '小数' },
          { name: 'DATE', cnName: '日期' },
        ],
        checkPrames: ['name', 'fieldType', 'fieldLength'], //需校验的字段
        selectTableData: [],
        firstInit: true,
        tableKey: [[]],
        targetOptions: [],
        confidentialityLevelOptions: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ],
        colorList: [
          { value: '#FA5924', bgColor: '#FFF2E8', checked: true },
          { value: '#ED4EA5', bgColor: '#FFF0F6', checked: false },
          { value: '#8D54DA', bgColor: '#F9F0FF', checked: false },
          { value: '#41A4FF', bgColor: '#E6F7FF', checked: false },
          { value: '#37CCCB', bgColor: '#E6FFFB', checked: false },
        ],
        selectAll: false,
        queryItem: { query: '' },
        wayOptions: [
          {
            label: '全量',
            value: 'FULL',
          },
          {
            label: '增量',
            value: 'INCREMENT',
          },
        ],
      })

      const ruleForm = ref()
      const leftTableDom = ref()
      const router = useRouter()
      const methods = {
        // 获取元素信息
        getTableInfo() {
          state.tableWidth = (document.body.offsetWidth - 312) / 2
        },
        // 获取ddl
        async ddlFn() {
          let result = await methods.getAllData()
          if (!result.passed) {
            return false
          }
          let columnMapping = result.data.columnMapping.map((val) => {
            return {
              sourceCode: val.sourceColumnName,
              sourceDataLength: val.sourceColumnLength,
              sourceDataType: val.sourceColumnType,
              sourceName: val.sourceName || null,
              sourceOrderNum: val.sinkSortNum,
              targetCode: val.sinkMetaCode,
              targetDataLength: val.sinkMetaLength,
              targetDataType: val.sinkMetaType,
              targetName: val.sourceName,
              targetOrderNum: val.sinkSortNum,
            }
          })
          let ddlBO = {
            name: state.ruleForm.name,
            overlayOrNot: state.ruleForm.overlayOrNot,
            mappingList: columnMapping,
          }
          collectJobDDL(ddlBO).then((res) => {
            if (res.success) {
              // state.ddl = sqlFormatter.format(res.data, { language: 'hive' })
              state.ddl = res.data
            }
          })
        },
        nameBlur() {
          // 失焦时，将Input修改后的值赋值给ruleForm.name
          state.ruleForm.name = state.tempName
        },
        nameInput(txt) {
          let value = txt.replace(/[^A-Za-z0-9_]/g, '')
          if (!value?.toLocaleUpperCase()?.startsWith('ODS_')) {
            const reg = /^(ODS_|ODS)/i
            const result = str.match(reg)
            value = `${result}_${value.replace(reg, '')}`
          }
          // 将修改后的值暂时保存
          state.tempName = value
        },
        // 标签输入完成
        changeTargetFn(flag, isSave) {
          if (isSave) {
            if (state.customName) {
              if (methods.customNameBlur()) {
                state.ruleForm.tagList.push(state.customName)
                let checkColorItem = state.colorList.filter((val) => val.checked)[0]
                if (state.customColor) {
                  let index = Math.floor(Math.random() * 5)
                  checkColorItem = state.colorList[index]
                }
                state.targetOptions.push({
                  name: state.customName,
                  label: state.customName,
                  value: state.customName,
                  color: checkColorItem.value + '_' + checkColorItem.bgColor,
                })
                state.targetKey++
              } else {
                return false
              }
            } else {
              ElNotification({
                title: '提示',
                message: '请输入标签名称！',
                type: 'warning',
              })
              return false
            }
          }
          if (flag) {
            state.customName = ''
            state.colorList = state.colorList.map((val, ind) => {
              if (ind === 0) {
                val.checked = true
              } else {
                val.checked = false
              }
              return val
            })
          } else {
            state.popoverKey++
          }
        },
        // 自定义标签输入验证
        customNameBlur() {
          let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
          let res = regex.test(state.customName)
          if (res && state.customName.length > 1) {
            if (state.targetOptions.filter((val) => val.label === state.customName).length === 0) {
              return true
            } else {
              ElNotification({
                title: '提示',
                message: '标签名重复，请重新填写！',
                type: 'warning',
              })
              return false
            }
          } else {
            ElNotification({
              title: '提示',
              message: '标签为2-8个字符，支持中文和英文！',
              type: 'warning',
            })
            return false
          }
        },
        // 选中颜色
        checkFn(index) {
          state.colorList.forEach((val, ind) => {
            val.checked = false
            if (index === ind) {
              val.checked = true
            }
          })
        },
        // 获取标签列表
        getTargetList() {
          api.documentManage.getTagLibraryClassListHasTag().then((res) => {
            let { success, data } = res
            if (success) {
              state.targetOptions = data
            }
          })
        },
        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
          data?.forEach((item, index) => {
            state.tableKey[index] = [1, 2]
          })
          // data.forEach((item) => {
          //   item.fieldLength = ''
          // })
          state.tableRightData = {
            list: data.map((val) => {
              val.dataType = val.convertFileType
              val.fieldType = val.convertFileType
              return val
            }),
          }
          state.tableRightKey++
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },
        onKeydown(e) {
          let key = e.key
          let number = '1234567890'
          if (number.includes(key) || key === 'Backspace') {
            if (state.form.tokenExpireIncrementDays.toString().length === 1 && key === '0') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          } else {
            e.returnValue = false
          }
        },

        cNameBlur(data) {
          let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,30}$/
          let res = regex.test(data.cnName)
          data.isPass2 = false
          if (res && data.cnName?.length > 1 && data.cnName?.length < 31) {
            data.isPass2 = true
          }
          // state.tableRightKey++
        },
        //字段英文校验
        eNameBlur(editor) {
          let { row } = editor
          let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
          let res = regex.test(row.name)
          row.isPass = false
          if (res && row.name?.length > 1 && row.name?.length < 81) {
            row.isPass = true
          }

          state.tableKey[editor.rowIndex][0] = state.tableKey[editor.rowIndex][0] + 1
          state.configData.selectRow = state.tableRightData.list
        },
        //校验中文
        checkCName(val) {
          let regex = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,30}$/
          let res = regex.test(val)
          let isPass = false
          if (res && val?.length > 1 && val?.length < 31) {
            isPass = true
          }
          return isPass
        },
        //校验英文
        checkEName(val) {
          let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
          let res = regex.test(val)
          let isPass = false
          if (res && val?.length > 1 && val?.length < 81) {
            isPass = true
          }
          return isPass
        },
        fieldBlur(RowSlotProps) {
          RowSlotProps.rowData.fieldLength = Number(
            RowSlotProps.rowData.fieldLength.toString().replace(/\D/g, ''),
          )
          state.tableKey[RowSlotProps.rowIndex][1] = state.tableKey[RowSlotProps.rowIndex][1] + 1
          state.configData.selectRow = state.tableRightData.list
        },

        // 获取字段类型
        getFieldTypeList() {
          api.model.getFieldType({}).then((res) => {
            state.fieldTypeOptions = res.data
            state.tableRightKey++
            state.configData.selectRow = state.tableRightData.list
          })
        },
        // 字段类型改变
        fieldTypeChange(item) {
          let _item = state.fieldTypeOptions.filter((list) => list.name === item.fieldType)
          item.fieldTypeName = _item[0]?.cnName || '字符串'
          methods.canInputWithFieldType(item)
        },

        //根据字段类型设置长度精度是否可编辑
        canInputWithFieldType(item) {
          switch (item.fieldType) {
            case 'TIMESTAMP':
            case 'DATE':
              // item.isRequiredFieldLength = true
              item.fieldLength = null
              break
            case 'DOUBLE':
            case 'FLOAT':
            case 'BIGINT':
            case 'INT':
            case 'TINYINT':
            case 'INTEGER':
            case 'DECIMAL':
              item.isRequiredFieldLength = false
              item.fieldLength = 37
              // item.descriptionDisabled = false
              break
            case 'VARCHAR':
              item.isRequiredFieldLength = false
              item.fieldLength = 64
              // item.descriptionDisabled = true
              // item.description = '0'
              break
          }
        },

        // 获取数据源-列表
        getDatasourceList() {
          state.ruleForm.dataSourceTable = ''
          api.dataManagement.getDatasourceFeignList({ includeNonInvertible: false }).then((res) => {
            let { data, success } = res
            if (success) {
              data.length
                ? data.forEach((item) => {
                    item.value = item.id
                  })
                : ''
              state.dataSourceOptions = data
              state.tableData = {}
              state.leftData = []
            }
          })
        },

        // 获取数据源-列表
        getIncUsableFieldList() {
          api.dataManagement
            .incUsableFields({
              dataSourceId: state.ruleForm.dataSourceId,
              tableName: state.ruleForm.dataSourceTable,
            })
            .then((res) => {
              let { data, success } = res
              if (success) {
                state.columnOptions = data.map((val) => {
                  return {
                    name: val.colName,
                    value: val.colName,
                    dataType: val.dataType,
                    convertFileType: val.convertFileType,
                  }
                })
              }
            })
        },

        //  数据源下拉层-change
        dataSourceChange(data) {
          if (data) {
            let _data = state.dataSourceOptions.filter((item) => {
              return item.id === data.value
            })
            state.ruleForm.dataSourceName = _data[0].name
            state.ruleForm.database = _data[0].database
            state.userName = _data[0].userName
            state.datasourceType = _data[0].datasourceType
            state.ruleForm.confidentialityLevel = _data[0].confidentialityLevel
            emit('dataSourceTypeChange', { dataSourceType: _data[0].datasourceType })
            methods.clearTableData()
            state.tableData = {}
            state.ruleForm.dataSourceTable = ''
            state.dataSourceTableOptions = []
            state.ruleForm.incrementColumn = ''
            state.ruleForm.incrementColumnType = ''
            state.columnOptions = []
            methods.getSourceTables(
              {
                condition: {
                  dataSourceId: state.ruleForm.dataSourceId,
                  name: '',
                },
                pageNum: 1,
                pageSize: 300,
              },
              false,
            )
            state.key++
          } else {
            methods.clearTableData()
            state.ruleForm.dataSourceTable = ''
            state.dataSourceTableOptions = []
            emit('dataSourceTypeChange', { dataSourceType: '' })
            methods.dataSourceTableChange(data)
          }
        },
        filterTable(query) {
          state.tableLoading = true
          methods.getSourceTables(
            {
              condition: {
                dataSourceId: state.ruleForm.dataSourceId,
                name: query,
              },
              pageNum: 1,
              pageSize: 300,
            },
            true,
          )
        },
        // 根据数据源下拉层获取表数据
        async getSourceTables(data, flag = false) {
          state.queryItem = { query: data.condition.name }
          await api.dataManagement
            .getSourceTablesPageV2(data)
            .then((res) => {
              state.tableLoading = false
              if (res.success) {
                let _dataSourceTableOptions = []
                res.data.list.forEach((item) => {
                  _dataSourceTableOptions.push({
                    name: item.name,
                    value: item.name,
                    cName: item.comment || '',
                  })
                })
                if (flag) {
                  let filterItem = _dataSourceTableOptions.filter(
                    (val) => val.name === data.condition.name,
                  )
                  if (filterItem.length > 0) {
                    state.queryItem = { ...filterItem[0], query: data.condition.name }
                  }
                }
                state.dataSourceTableOptions = _dataSourceTableOptions
                state.leftData = []
              }
            })
            .catch(() => {
              state.tableLoading = false
            })
        },
        // 失去焦点
        blurFn() {
          if (state.tableLoading) {
            setTimeout(() => {
              methods.blurFn()
            }, 1000)
          } else {
            if (state.queryItem.query && state.ruleForm.dataSourceTable !== state.queryItem.query) {
              if (state.queryItem.name) {
                state.ruleForm.dataSourceTable = state.queryItem.query
                methods.dataSourceTableChange(state.queryItem)
              } else {
                ElNotification({
                  title: '提示',
                  message: '数据表不存在',
                  type: 'warning',
                })
              }
            }
          }
        },
        //清空 table左右侧数据和勾选项目
        clearTableData() {
          state.tableLeftData = { list: [] }
          state.tableRightData = { list: [] }
          // leftTableDom.value?.clearSelection()
        },
        // 数据源下拉层change获取表数据
        async dataSourceTableChange(data) {
          if (data) {
            state.ruleForm.cnName = data.cName || ''
            state.originalValue = data.value
            methods.checkExistsFn(data.value)
          } else {
            state.originalValue = data
            state.ruleForm.cnName = ''
          }
          state.ruleForm.incrementColumn = ''
          state.ruleForm.incrementColumnType = ''
          state.columnOptions = []
          methods.clearTableData()
          if (data) {
            let { dataSourceId, dataSourceTable } = state.ruleForm
            if (!(dataSourceId && dataSourceTable)) return
            if (state.datasourceType === 'ORACLE') {
              state.ruleForm.name = 'ODS_' + dataSourceTable.replace(/\./g, '_')
            } else if (state.datasourceType === 'API') {
              state.ruleForm.name = 'ODS_api_' + dataSourceTable.replace(/\./g, '_')
            } else {
              state.ruleForm.name =
                'ODS_' +
                state.ruleForm.database +
                '_' +
                state.userName +
                '_' +
                dataSourceTable.replace(/\./g, '_')
            }
            let _data = {
              dataSourceId: dataSourceId,
              tableName: dataSourceTable,
            }
            state.key++
            state.loading = true
            await api.dataManagement
              .getSourceStructureV2(_data)
              .then((res) => {
                // 渲染左边表格
                if (res.success) {
                  // res.data.forEach((val) => {
                  //   val.dataType = val.convertFileType
                  // })

                  if (res.data.length > 0) {
                    let tableHeadPreviewTitles = []
                    Object.keys(res.data[0]).forEach((key) => {
                      tableHeadPreviewTitles.push({
                        prop: key,
                        name: key,
                      })
                    })
                    state.tableHeadPreviewTitles = tableHeadPreviewTitles
                    state.tablePreviewData.list = res.data
                  }
                  state.leftData = res.data
                  res.data.map((item, index) => {
                    return Object.assign(item, {
                      number: index + 1,
                      isPass: true,
                      oldname: item.colName,
                      oldfieldType: item.dataType,
                      oldfieldLength: item.length,
                      oldcnName: item.comment,
                      cnName: item.comment,
                      fieldLength: item.length,
                      fieldType: item.dataType,
                      fieldTypeName: null,
                      name: item.colName,
                      convertFileType: item.convertFileType,
                    })
                  })
                  res.data.forEach((val) => {
                    val.checked = true
                  })
                  state.selectAll = true
                  state.tableLeftData = { list: res.data }
                  state.key++
                  state.tableRightKey++
                  state.configData.selectRow = state.tableRightData.list
                  if (!res.data.length) return
                }
              })
              .catch(() => {
                state.loading = false
              })
            // let _data2 = {
            //   dataSourceId: dataSourceId,
            //   tableName: dataSourceTable,
            // }
            //
            // await api.model
            //   .reverseMetaList(_data2)
            //   .then((res) => {
            //     if (!res.data.length) return
            //     // 新增序号属性
            //
            //     res.data.map((item, index) => {
            //       return Object.assign(item, {
            //         number: index + 1,
            //         isPass: true,
            //         oldname: item.name,
            //         oldfieldType: item.fieldType,
            //         oldfieldLength: item.fieldLength,
            //         oldcnName: item.cnName,
            //       })
            //     })
            //     state.tableLeftData = { list: res.data }
            //     state.key++
            //     state.tableRightKey++
            //
            //     state.configData.selectRow = state.tableRightData.list
            //   })
            //   .catch(() => {
            //     state.loading = false
            //   })
            state.loading = false
            methods.domUpdata()
            methods.getIncUsableFieldList()
          } else {
            state.leftData = []
            state.tableLeftData = { list: [] }
            if (state.ruleForm.sinkModelCreateMethod === 'NEW') {
              if (!state.ruleForm.modeTable) {
                state.ruleForm.name = ''
              }
            } else {
              state.ruleForm.name = ''
            }
            methods.domUpdata()
          }
        },
        // 判断该表是否已采集
        checkExistsFn(tableName) {
          state.collected = false
          // api.dataManagement
          //   .checkExists({ tableName: encodeURIComponent(tableName) })
          //   .then((res) => {
          //     if (res) {
          //       state.collected = res.data.collected
          //       if (res.data.collected) {
          //         ElNotification({
          //           title: '提示',
          //           message: '该表已采集',
          //           type: 'warning',
          //         })
          //       }
          //     }
          //   })
        },
        //切换数据变化时候-重新渲染
        domUpdata() {
          if (state.ruleForm.sinkModelCreateMethod === 'NEW') {
            methods.canvasUpdate()
          }
          methods.checkboxChangeFn(true, 'all')
        },

        //选择已有数据表时候 - 画布重新渲染
        canvasUpdate() {
          sceneTableLink.value.clearEdge() //清空连线
          sceneTableLink.value.changTable({
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
          })
        },

        // 获取模型层下拉列表
        async getDataModelTree() {
          let result = await api.model.getDataModelTree().then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.name === '原始数据层') {
                state.ruleForm.modeFeign = item.id
              }
            })
            state.modeFeignOptions = data
            let a = methods.modeFeignChange({ value: state.ruleForm.modeFeign })
            return a
          })
          return result
        },
        // 模型层-change
        async modeFeignChange(data) {
          let _data = state.modeFeignOptions.filter((item) => {
            return item.id === data.value
          })
          state.ruleForm.modeFeignName = _data[0].name
          let params = {
            layerId: state.ruleForm.modeFeign,
            jobType: 'COLLECT_JOB',
            envType: props.envType,
          }
          if (state.editId) {
            params.jobId = state.editId
          }
          let result = await api.model.getModelListWithLayerId(params).then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.cnName = item.cnName
              item.value = item.id
            })
            state.oldModeTableOptions = data
            state.modeTableOptions = data
            return { modeTableOptions: state.modeTableOptions }
          })
          return result
        },
        // 模型-change
        // modeTableChange(data) {
        //   if (data) {
        //     state.activeModeTableItem = state.modeTableOptions.filter((item) => {
        //       return item.id === data.value
        //     })
        //     state.key++
        //     state.ruleForm.modeTableName = state.activeModeTableItem[0].name
        //     state.ruleForm.name = state.activeModeTableItem[0].name.replace(/./g, '_')
        //     state.ruleForm.cnName = state.activeModeTableItem[0].cnName
        //     state.loading = true
        //
        //     api.model
        //       .getModeData({ id: state.ruleForm.modeTable })
        //       .then((res) => {
        //         // 渲染右边表格
        //         state.loading = false
        //         state.rightData = res.data
        //         if (!res.data.length) return
        //         setTimeout(() => {
        //           methods.canvasUpdate()
        //         }, 100)
        //       })
        //       .catch(() => {
        //         state.loading = false
        //       })
        //   } else {
        //     state.ruleForm.name = ''
        //     state.ruleForm.cnName = ''
        //     state.rightData = []
        //     methods.canvasUpdate()
        //   }
        // },
        //目标表radio change
        // modelCreatedModeChange(val) {
        //   if (val === 'NEW') {
        //     state.ruleForm.name = state.activeModeTableItem[0]?.name?.replace(/./g, '_')
        //     nextTick(() => {
        //       methods.canvasUpdate()
        //     })
        //   } else {
        //     state.ruleForm.name = state.ruleForm.dataSourceTable.replace(/./g, '_')
        //   }
        // },

        // 初始化数据
        async init(data) {
          state.totalData = data

          if (state.editId) {
            let {
              dataSourceId,
              sourceTableName,
              sinkModelEname,
              sinkModelCname,
              tableData,
              dataSourceName,
              sinkModelId,
              sinkModelCreateMethod,
              dataSourceType,
              overlayOrNot,
              collectWay,
              incrementColumn,
              incrementColumnType,
              filterSql,
              confidentialityLevel,
              tagList,
            } = data

            if (!state.queryData.disabledDataSource) {
              //编辑时候数据源可用时
              state.ruleForm.overlayOrNot = overlayOrNot // 覆盖模式
              state.ruleForm.collectWay = collectWay || 'FULL' // 采集方式
              state.ruleForm.incrementColumn = incrementColumn || '' // 增量数据
              state.ruleForm.incrementColumnType = incrementColumnType || '' // 增量数据
              state.ruleForm.filterSql = filterSql || '' // 过滤
              state.ruleForm.dataSourceId = dataSourceId // 数据源id
              state.ruleForm.dataSourceTable = sourceTableName //// 数据源下表名
              state.ruleForm.name = sinkModelEname //模型英文名
              state.ruleForm.cnName = sinkModelCname //模型中文名
              state.ruleForm.dataSourceName = dataSourceName
              state.ruleForm.modeTable = sinkModelId //模型ID
              state.ruleForm.sinkModelCreateMethod = sinkModelCreateMethod
              state.ruleForm.confidentialityLevel = confidentialityLevel
              state.ruleForm.tagList = tagList?.map((item) => 'tag_' + item.id)
              state.leftData = tableData
              emit('dataSourceTypeChange', { dataSourceType })
              methods.editInit(data)
            }
          } else {
            let { name, description } = data
            state.allData.name = name
            state.allData.description = description
          }
        },
        //编辑初始化下拉框数据
        async editInit(alldata) {
          //获取数据源列表
          api.dataManagement.getDatasourceFeignList({ includeNonInvertible: false }).then((res) => {
            let { data, success } = res
            if (success) {
              data.forEach((item) => {
                item.label = item.name
                item.value = item.id
                if (item.id === state.ruleForm.dataSourceId) {
                  state.ruleForm.dataSourceName = item.name
                  state.ruleForm.database = item.database
                  state.userName = item.userName
                  state.datasourceType = item.datasourceType
                }
              })
              state.dataSourceOptions = data
            }
          })

          state.dataSourceTableOptions = [
            {
              name: state.ruleForm.dataSourceTable,
              value: state.ruleForm.dataSourceTable,
              cName: state.ruleForm.cnName,
            },
          ]
          methods.getIncUsableFieldList()
          api.dataManagement
            .getSourceStructure({
              id: state.ruleForm.dataSourceId,
              tableName: alldata.sourceTableName,
            })
            .then((res) => {
              // 渲染左边表格
              if (res.success) {
                // res.data.forEach((val) => {
                //   val.dataType = val.convertFileType
                // })
                if (res.data.length > 0) {
                  let tableHeadPreviewTitles = []
                  Object.keys(res.data[0]).forEach((key) => {
                    tableHeadPreviewTitles.push({
                      prop: key,
                      name: key,
                    })
                  })
                  state.tableHeadPreviewTitles = tableHeadPreviewTitles
                  state.tablePreviewData.list = res.data
                  state.key++
                }
                state.leftData = res.data
                if (!res.data.length) return
              }
            })
          if (alldata.sinkModelCreateMethod === 'REVERSE' && !state.disabled) {
            methods.editInitTable(alldata)
          } else {
            methods.editInitCanvas(alldata)
          }
        },

        // 编辑初始化数据-映射
        async editInitCanvas(data) {
          if (data) {
            let { sourceTableName, sinkModelEname, layerName, dataSourceName } = data
            state.dataSourceNames = [dataSourceName, sourceTableName]
            state.modelNames = [layerName || '原始数据层', sinkModelEname]

            await api.dataManagement
              .getSourceStructure({
                id: data.dataSourceId,
                tableName: data.sourceTableName,
              })
              .then((res1) => {
                // 渲染左边表格
                state.leftData = res1.data
              })

            await api.model.getModeData({ id: data.sinkModelId }).then((res2) => {
              // 渲染右边表格
              state.rightData = res2.data
            })
            state.loading = false

            data.graphData = {
              leftData: state.leftData,
              rightData: state.rightData,
              dataSourceNames: state.dataSourceNames,
              modelNames: state.modelNames,
            }
            data.allEdge = data.columnMapping
            nextTick(() => {
              sceneTableLink.value.changTable(data.graphData, function (that) {
                let { methods } = that
                // 连线
                if (state.totalData.allEdge) {
                  methods.addEdge({ data: state.totalData.allEdge, initLater: true })
                }
                // data.graph.scrollToPoint(600, 150)
              })
            })
          }
        },
        // 编辑初始化数据-逆向表格
        editInitTable(data) {
          if (data) {
            let { dataSourceId, sourceTableName, columnMapping } = data

            let _data2 = {
              dataSourceId: dataSourceId,
              tableName: sourceTableName,
            }

            api.model
              .reverseMetaList(_data2)
              .then((res) => {
                if (!res.data.length) return
                // 新增序号属性
                res.data.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                    isPass: true,
                    oldname: item.name,
                    oldfieldType: item.fieldType,
                    oldfieldLength: item.fieldLength,
                    oldcnName: item.cnName,
                    convertFileType: item.convertFileType,
                  })
                })
                let _tableRightData = []
                res.data.forEach((item, index) => {
                  columnMapping.forEach((key) => {
                    if (
                      item.name === key.sourceColumnName ||
                      item.name.substring(item.name.lastIndexOf('.') + 1) === key.sourceColumnName
                    ) {
                      //英文相同
                      item.cnName = key.sourceName
                      item.name = key.sinkMetaCode
                      item.fieldType = key.sinkMetaType
                      item.fieldLength = key.sinkMetaLength
                      item.oldfieldType = key.sourceColumnType
                      item.oldfieldLength = key.sourceColumnLength
                      _tableRightData.push(item)
                    }
                  })
                })
                _tableRightData.forEach((val, ind) => {
                  state.tableKey[ind] = [1, 2]
                })
                state.tableRightData = { list: _tableRightData }
                res.data.forEach((val) => {
                  val.checked = false
                  _tableRightData.forEach((v) => {
                    if (v.name === val.name) {
                      val.checked = true
                    }
                  })
                })
                state.tableLeftData = { list: res.data }
                methods.checkIsAllFn()
                state.key++
                state.tableRightKey++
                state.configData.selectRow = _tableRightData
                methods.ddlFn()
              })
              .catch(() => {
                state.loading = false
              })
          }
        },
        // 选中发生变化
        checkboxChangeFn(item, type) {
          if (type === 'all') {
            state.tableLeftData.list.forEach((val) => {
              val.checked = item
            })
          }
          let checkData = state.tableLeftData.list.filter((val) => val.checked)
          checkData?.forEach((item, index) => {
            state.tableKey[index] = [1, 2]
          })
          state.tableRightData = {
            list: checkData.map((val, ind) => {
              val.name = val.oldname.substring(val.oldname.lastIndexOf('.') + 1)
              val.cnName = val.oldcnName
              val.number = ind + 1
              val.dataType = (
                val.convertFileType ? val.convertFileType : val.fieldType
              )?.toUpperCase()
              val.fieldType = (
                val.convertFileType ? val.convertFileType : val.fieldType
              )?.toUpperCase()
              return val
            }),
          }
          methods.checkIsAllFn()
          state.tableRightKey++
        },
        // 检测是否全选
        checkIsAllFn() {
          if (state.tableLeftData.list.length === state.tableRightData.list.length) {
            state.selectAll = true
          } else {
            state.selectAll = false
          }
        },
        // 映射关系-回传数据
        async getLinkMappingAllData() {
          let passedLink = false
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          if (!_result.passed) {
            return _result
          }
          if (state.activeModeTableItem.length) {
            state.totalData.sinkModelEname = state.activeModeTableItem[0].name
            state.totalData.sinkModelCname = state.activeModeTableItem[0].cnName
          }
          state.totalData.dataSourceId = state.ruleForm.dataSourceId // 数据源id
          state.totalData.sourceTableName = state.ruleForm.dataSourceTable
          state.totalData.dataSourceName = state.ruleForm.dataSourceName
          state.totalData.layerId = state.layerId // 模型层id
          state.totalData.layerName = state.layerName // 模型层中文名

          state.totalData.sinkModelId = state.ruleForm.modeTable // 模型id
          state.totalData.sinkModelCreateMethod = state.ruleForm.sinkModelCreateMethod // 建模方式
          state.totalData.confidentialityLevel = state.ruleForm.confidentialityLevel
          if (state.ruleForm?.tagList?.length > 0) {
            let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
            state.totalData.tagList =
              activeOptions?.map((val) => {
                return { text: val.name, color: val.color, id: val.id || null }
              }) || []
          }

          let result = sceneTableLink.value.getGraphData()
          // 连线数据
          let _columnMapping = result.cells.filter((item) => {
            return item.shape === 'edge'
          })
          let _data = result.cells.filter((cell) => {
            return cell.shape === 'er-rect'
          })
          // 所有右侧数据
          // let _sourceDate = _data.filter((cell) => {
          //   return cell.attrs.text.text === '目的表'
          // })
          let alldata = []
          _data.forEach((item) => {
            alldata.push(...item.ports.items)
          })
          let needColumnMapping = []
          if (_columnMapping.length) {
            passedLink = true
            _columnMapping.forEach((item, index) => {
              needColumnMapping[index] = {}
              alldata.forEach((cell) => {
                if (item.source.port === cell.id) {
                  needColumnMapping[index].sourceColumnName = cell.attrs.colName.text
                  needColumnMapping[index].sourceColumnType = cell.attrs.dataType.text
                  needColumnMapping[index].sourceColumnLength = cell.attrs.dataLength.text
                }
                if (item.target.port === cell.id) {
                  needColumnMapping[index].sinkMetaCode = cell.attrs.colName.text
                  needColumnMapping[index].sinkMetaType = cell.attrs.dataType.text
                  needColumnMapping[index].sinkMetaLength = cell.attrs.dataLength.text
                  needColumnMapping[index].sinkSortNum = cell.attrs.sortNum.text
                }
              })
            })
          } else {
            passedLink = false
            ElNotification({
              title: '提示',
              message: '请建立映射关系',
              type: 'warning',
            })
          }
          state.totalData.columnMapping = needColumnMapping
          state.totalData.graphData = {
            leftData: state.leftData,
            rightData: state.rightData,
            dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
            modelNames: [state.ruleForm.modeFeignName, state.ruleForm.name],
          }
          state.totalData.allEdge = _columnMapping
          state.totalData.overlayOrNot = state.ruleForm.overlayOrNot
          state.totalData.collectWay = state.ruleForm.collectWay
          state.totalData.incrementColumn = state.ruleForm.incrementColumn
          state.totalData.incrementColumnType = state.ruleForm.incrementColumnType
          state.totalData.filterSql = state.ruleForm.filterSql

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid && passedLink, data: state.totalData })
            })
          })
          return _result2
        },

        // 逆向-回传数据
        async getModelReverseAllData() {
          let _contented = true
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              if (valid) {
                if (!state.disabled) {
                  //画布可编辑时候
                  if (state.tableRightData.list.length) {
                    let isPass = true
                    let isPass3 = true
                    // let isPass2 = true

                    state.tableRightData.list.forEach((item) => {
                      state.checkPrames.forEach((key) => {
                        if (!('' + item[key]).length) {
                          _contented = false
                        }
                      })

                      // let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,19})$/

                      // let regex2 = /^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9]){2,20}$/

                      // let res2 = regex2.test(item.cName)

                      let regex = /^[a-zA-Z]([a-zA-Z_0-9]{1,80})$/
                      let res = regex.test(item.name)
                      if (res && item.name?.length > 1 && item.name?.length < 81) {
                        item.isPass = true
                      } else {
                        _contented = false
                        item.isPass = false
                        isPass = false
                      }

                      if (item.fieldLength && item.fieldLength > 0) {
                        let _string = item.fieldLength + ''
                        if (_string.indexOf('.') !== -1) {
                          isPass3 = false
                          _contented = false
                        }
                      } else {
                        item.fieldLength = null
                        // isPass3 = false
                        // _contented = false
                      }

                      // if (!res2) {
                      //   _contented = false
                      //   item.isPass2 = false
                      //   isPass2 = false
                      // } else {
                      //   item.isPass2 = true
                      // }
                    })
                    if (!isPass) {
                      ElNotification({
                        title: '提示',
                        message: '支持英文、数字、下划线，只能以英文开头，2~80个字符',
                        type: 'warning',
                      })
                    }
                    if (!isPass3) {
                      ElNotification({
                        title: '提示',
                        message: '字段长度仅支持正整数',
                        type: 'warning',
                      })
                    }
                    // if (!isPass2) {
                    //   ElNotification({
                    //     title: '提示',
                    //     message: '中文名仅支持汉字、字母、数字2-20位',
                    //     type: 'warning',
                    //   })
                    // }
                  } else {
                    ElNotification({
                      title: '提示',
                      message: '空数据',
                      type: 'warning',
                    })
                    _contented = false
                  }
                }
              }

              resolve({ passed: valid && _contented, data: { sinkModelCreateMethod: '' } })
            })
          })

          if (!_result.passed) {
            return _result
          }

          state.totalData.dataSourceId = state.ruleForm.dataSourceId // 数据源id
          state.totalData.sourceTableName = state.ruleForm.dataSourceTable //数据源下表名
          state.totalData.sinkModelEname = state.ruleForm.name //模型英文名
          state.totalData.sinkModelCname = state.ruleForm.cnName //模型中文名
          // state.totalData.sinkModelId = state.ruleForm.sinkModelId // 模型id
          state.totalData.layerId = state.layerId // 模型层id
          state.totalData.layerName = state.layerName // 模型层中文名
          state.totalData.sinkModelCreateMethod = state.ruleForm.sinkModelCreateMethod // 建模方式
          let needColumnMapping = []
          if (state.disabled) {
            //结构化新建表下架时候编辑
            needColumnMapping = state.totalData.columnMapping
          } else {
            state.tableRightData.list.forEach((item, index) => {
              needColumnMapping[index] = {}
              needColumnMapping[index].sourceColumnName = item.oldname
              needColumnMapping[index].sourceColumnType = item.oldfieldType
              needColumnMapping[index].sourceColumnLength = item.oldfieldLength

              needColumnMapping[index].sinkMetaCode = item.name
              needColumnMapping[index].sinkMetaType = item.fieldType
              needColumnMapping[index].sinkMetaLength = item.fieldLength
              needColumnMapping[index].sinkSortNum = index
              needColumnMapping[index].sourceName = item.cnName
            })
          }

          state.totalData.columnMapping = needColumnMapping
          state.totalData.tableData = state.tableRightData.list
          state.totalData.dataSourceName = state.ruleForm.dataSourceName
          state.totalData.overlayOrNot = state.ruleForm.overlayOrNot
          state.totalData.collectWay = state.ruleForm.collectWay
          state.totalData.incrementColumn = state.ruleForm.incrementColumn
          state.totalData.incrementColumnType = state.ruleForm.incrementColumnType
          state.totalData.filterSql = state.ruleForm.filterSql
          state.totalData.confidentialityLevel = state.ruleForm.confidentialityLevel
          // state.totalData.graphData = {
          //   dataSourceNames: [state.ruleForm.dataSourceName, state.ruleForm.dataSourceTable],
          //   modelNames: [state.ruleForm.modeFeignName, state.ruleForm.modeTableName],
          // }
          if (state.ruleForm?.tagList?.length > 0) {
            let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
            state.totalData.tagList =
              activeOptions?.map((val) => {
                return { text: val.name, color: val.color, id: val.id || null }
              }) || []
          }

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid, data: state.totalData })
            })
          })
          return _result2
        },
        async getAllData() {
          let result = {}
          if (state.ruleForm.sinkModelCreateMethod === 'NEW') {
            //映射
            result = await methods.getLinkMappingAllData()
          } else {
            //逆向
            result = await methods.getModelReverseAllData()
          }
          if (state.collected) {
            ElNotification({
              title: '提示',
              message: '该表已采集',
              type: 'warning',
            })
            result.passed = false
          }
          return result
        },
        //获取默认模型信息
        getOriginLayer() {
          api.model.getOriginLayer().then((res) => {
            let { success, data } = res
            if (success) {
              state.layerId = data.id
              state.layerName = data.name
            }
          })
        },

        //环境改变 初始化数据
        initDataWithEnvType() {
          state.ruleForm.dataSourceId = ''
          state.ruleForm.modeTable = ''
          state.dataSourceOptions = []
          state.modeTableOptions = []
          state.dataSourceTableOptions = []
          state.columnOptions = []
          state.ruleForm.name = ''
          state.ruleForm.cnName = ''
          state.leftData = []
          state.rightData = []
          methods.getDatasourceList()
          // methods.getDataModelTree()
          methods.clearTableData()
        },
      }
      //监听环境变化  --变化后清空数据
      watch(
        () => props.envType,
        () => {
          if (props.listenEnvTypeChange) {
            methods.initDataWithEnvType()
          }
        },
      )

      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.editId = state.queryData.editId
        state.detailId = state.queryData.detailId

        state.disabled =
          state.queryData.status &&
          state.queryData.status !== 'WAITING_PUBLISH' &&
          state.queryData.status !== 'OFFLINE'
            ? true
            : false
        state.isOffline =
          state.queryData.status && state.queryData.status === 'OFFLINE' ? true : false
        nextTick(() => {
          methods.getTableInfo()
        })
        window.addEventListener('resize', () => {
          methods.getTableInfo()
        })
        methods.getDatasourceList()
        // methods.getDataModelTree()
        // methods.getOriginLayer()
        // methods.getFieldTypeList()
        methods.getTargetList()
      })
      onBeforeUnmount(() => {
        window.removeEventListener('resize', () => {
          methods.getTableInfo()
        })
      })

      return {
        state,
        ruleForm,
        leftTableDom,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        sceneTableLink,
        sceneTableLinkFullPage,
        selectTree,
        toStyle,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f4f4f4;
  $outBg: #eeeeee;

  .data-collection-link-mapping {
    height: 100%;
    background: #eeeeee;
    background-color: $outBg;
    .box-bottom {
      height: 100%;
      background-color: $contentBoxBg;
      border-radius: 0 0 4px;
      // overflow: hidden;
      .structure-ruleForm {
        display: flex;
        flex-direction: column;
        .gray-row {
          height: 10px;
          background-color: #f0f2f5;
        }
        .white-row {
          height: 16px;
          background-color: #fff;
          border-radius: 2px 2px 0 0;
        }
        .content-title {
          position: relative;
          height: 30px;
          margin-bottom: 16px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;

          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
        .nancalui-form__item--horizontal {
          margin-bottom: 16px;
        }

        :deep(.nancalui-select--disabled) {
          .nancalui-select__arrow {
            display: none;
          }
        }

        .title {
          height: 22px;
          margin: 2px 0 10px 0;
          color: #333333;
          font-weight: bolder;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          line-height: 22px;
        }

        .top-line {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          width: 100%;
          padding: 0 16px;

          .collect-rule {
            display: flex;
            flex: 1;
            align-items: center;
            margin-bottom: 0;
            .nancalui-form__control {
              padding: 0;
            }
          }
          :deep(.dataSourceTable:has(.error-message)) {
            .el-select {
              .el-select__wrapper {
                box-shadow: 0 0 0 1px #f63838 inset;
              }
            }
          }
          :deep(.el-select) {
            width: 100%;
          }
          &-tag {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
            max-width: 430px;

            //:deep(.el-select) {
            //  width: calc(100% - 110px);
            //  margin-right: 4px;
            //
            //}

            .nancalui-button {
              padding: 0 8px;

              .button-content {
                display: flex;
                align-items: center;
                justify-content: center;

                .icon {
                  margin-right: 4px;
                  font-size: 16px;
                }
              }
            }
          }
          .nancalui-form__item--horizontal {
            flex: 1;
          }
          .check-style {
            width: 100%;

            .nancalui-select {
              max-width: 328px;
            }
          }

          .nancalui-radio-group.is-row {
            display: flex;
            align-items: center;
            width: 100%;
            .nancalui-form__item--horizontal {
              margin-bottom: 0;
            }
            .nancalui-radio__wrapper:not(:last-child) {
              padding-right: 8px;
            }
            .nancalui-radio__wrapper:last-child {
              margin-left: 16px;
            }
          }
        }
        .mid-line {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          width: 100%;
          padding: 0 16px;
          .overlayOrNot-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .illustrate {
              margin-left: 4px;
              color: #909399;
            }
          }

          :deep(.nancalui-form__item--horizontal) {
            flex: 1;
          }
        }
        .nancalui-select,
        .nancalui-input {
          max-width: 430px;
        }
      }
      .bottom-line {
        position: relative;
        padding-bottom: 16px;
        .link-mapping-box {
          height: 360px;
          padding: 16px;
        }
        .model-reverse-box {
          display: flex;
          height: 370px;
          padding: 16px 16px 0 16px;
          //padding: 16px 20px;
          //border: 1px solid #ebedf0;
          border-radius: 8px;

          .child:not(:last-child) {
            padding-right: 20px;
          }
          .child:last-child {
            padding-left: 20px;
          }
          .child {
            width: 50%;

            .table-title {
              margin-bottom: 8px;
              overflow: hidden;
              color: #000000;
              font-weight: bolder;
              font-size: 12px;
              font-family: PingFangSC-Medium, PingFang SC;
              line-height: 20px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          :deep(.nancalui-table-v) {
            .nancalui-table-v-header__header {
              .nancalui-table-v__header-cell {
                flex: 1 !important;
                flex-shrink: 0;
                width: auto;
                &:first-of-type {
                  flex: none !important;
                  flex-shrink: 0 !important;
                  width: 80px !important;
                }
                .table-v-header {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  .nancalui-checkbox {
                    margin-right: 4px;
                  }
                }
              }
            }
            .nancalui-table-v__body {
              .nancalui-table-v__row {
                .nancalui-table-v__row-cell {
                  flex: 1 !important;
                  width: auto;
                  &:first-of-type {
                    flex: none !important;
                    flex-shrink: 0 !important;
                    width: 80px !important;
                  }
                }
                .table-v-row {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                  &-col {
                    flex: 1;
                    flex-shrink: 0;
                    box-sizing: border-box;
                    padding: 0 8px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    &.checkbox {
                      display: flex;
                      align-items: center;
                      justify-content: flex-start;
                      .nancalui-checkbox {
                        margin-right: 4px;
                      }
                    }
                    &:first-of-type {
                      flex: none;
                      width: 80px !important;
                    }
                  }
                }
              }
            }
          }
        }
        .reverse-box-left {
          :deep(.nancalui-table) {
            .nancalui-table__thead tr th.is-left:first-child .header-container {
              width: max-content;
              padding-left: 16px !important;
            }
          }
        }
        :deep(.nancalui-table) {
          .nancalui-table__header-wrapper tr th .header-container {
            padding: 0 8px;
          }
          .nancalui-table__thead tr th.is-left:first-child {
            width: fit-content;
          }
          .nancalui-table__thead tr th.is-left:first-child .header-container {
            padding-left: 16px !important;
            // width: max-content;
          }
          tbody > tr > td.is-left:first-child {
            width: max-content;
            padding: 0 8px 0 16px;
          }
          tbody > tr > td {
            height: 40px;
            &:not(:first-child) {
              padding: 5px 8px;
            }
          }
          .table-no-content {
            min-height: 150px;
            padding-bottom: 20px;
            div {
              margin-top: 0;
            }
          }

          .nancalui-table__empty {
            padding: 10px 0;
          }
          .nancalui-input {
            width: 100%;
            &.required-input {
              border: 1px solid red;
              border-radius: 4px;
              .nancalui-input__wrapper {
                background-color: transparent;
                border: none;
              }
            }
          }
          .nancalui-select {
            width: 100%;
          }
        }
      }
    }
  }
  .ddl {
    &-btn {
      margin-bottom: 4px;

      :deep(.button-content) {
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
          margin-right: 4px;
          font-size: 16px;
        }
      }
    }
    &-content {
      min-height: 200px;
      margin: 0 16px;
      padding: 8px;
      border: 1px solid #a3b4db;
      border-radius: 6px;
      :deep(.nancalui-textarea__div) {
        .nancalui-textarea {
          padding: 0;
          color: rgba(0, 0, 0, 0.75);
          background-color: transparent;
          border: 1px solid #fff;
          &:hover {
            border: 1px solid #fff !important;
            box-shadow: none !important;
          }
        }
      }
    }
  }

  .custom {
    padding: 6px;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 14px;

      .icon {
        color: #8091b7;
        font-size: 16px;
        cursor: pointer;
      }
    }

    &-name {
      margin-top: 8px;
    }

    &-color {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 12px;
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;

      .nancalui-switch {
        margin-left: 6px;
      }

      &-label {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 4px;

        .icon {
          display: none;
          color: #fff;
          font-size: 12px;
        }

        &.checked {
          box-shadow: 0 0 0 1px #447dfd;

          .icon {
            display: block;
          }
        }
      }
    }

    &-footer {
      margin-top: 14px;
      text-align: right;

      .nancalui-button {
        min-width: 40px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }
  :deep(.nancalui-table-v) {
    .nancalui-table-v__main {
      background-color: #fff;
    }
    .nancalui-table-v-header__header {
      background-color: #ebf4ff;
      .nancalui-table-v__header-cell {
        background-color: #ebf4ff;
      }
    }
    .nancalui-table-v__header-cell-text {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .table-header {
    background-color: #ebf4ff;
    padding: 0;
    color: #1d2129;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .table-row {
    border: none;
    height: 36px;
    padding: 0;
  }
</style>
