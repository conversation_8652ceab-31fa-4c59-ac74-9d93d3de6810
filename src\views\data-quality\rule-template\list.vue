<template>
  <!-- 数据服务-api管理列表 -->
  <div
    :class="['rule-template-list container', state.isLzos ? 'isLzos' : '']"
    v-loading="state.loading"
  >
    <section class="tools">
      <div class="row">
        <div class="col">
          规则模板：
          <n-input
            v-model="state.originalFormInline.name"
            size="small"
            placeholder="规则模板"
            clearable
          />
          类型：
          <n-select
            v-model="state.originalFormInline.ruleType"
            placeholder="选择类型"
            filter
            allow-clear
            :options="state.ruleTemplateOptions"
          />
          六性维度：
          <n-select
            v-model="state.originalFormInline.sexomorphicDimension"
            placeholder="选择维度"
            filter
            allow-clear
            :options="state.sexomorphicDimensionOptions"
          />
          适用数据范围：
          <n-select
            v-model="state.originalFormInline.applicableDataType"
            placeholder="请选择数据适用范围"
            filter
            allow-clear
            :options="state.applicableDataTypeOptions"
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>

    <div :class="['rule-template-page-bottom', state.expend ? 'expend' : '']">
      <div class="common-section-header">
        <div class="title">规则模板</div>
      </div>
      <div class="rule-template-page-bottom-table-box">
        <CfTable
          :isDisplayAction="true"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              initTable()
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              initTable()
            },
          }"
          actionWidth="120"
        >
          <template #name="{ row }">
            <div class="template-name-box" :title="row.name">
              {{ row.name }}
              <span v-if="!row.templateId">内置</span>
            </div></template
          >
          <template #ruleType="{ row }">
            <div class="template-type-box">
              <div>
                <SvgIcon
                  v-if="row.ruleType === 'TABLE'"
                  icon="new-rule-template-table"
                  class="icon"
                />
                <SvgIcon
                  v-else-if="row.ruleType === 'FIELD'"
                  icon="new-rule-template-field"
                  class="icon"
                />
                <SvgIcon v-else icon="new-rule-template-cross-table" class="icon" />
                {{ row.ruleTypeName }}
              </div>
            </div></template
          >

          <template #sexomorphicDimensionName="{ row }">
            <div :title="row.sexomorphicDimensionName">
              <span>{{ row.sexomorphicDimensionName }}</span>
            </div></template
          >
          <template #applicableDataType="{ row }">
            <div class="template-type-box">
              <div class="template-type-box-item">
                <SvgIcon
                  v-if="row.applicableDataType === 'ALL'"
                  icon="new-rule-template-filter-all"
                  class="icon"
                />
                <SvgIcon v-else icon="new-rule-template-filter-number" class="icon" />
                <div class="name" :title="row.applicableDataTypeName">{{
                  row.applicableDataTypeName
                }}</div>
              </div>
            </div></template
          >
          <template #description="{ row }">
            <div class="template-desc-box" :title="row.description">
              {{ row.description }}
            </div></template
          >

          <template #editor="{ row }">
            <div class="edit-box">
              <n-button
                v-if="
                  buttonAuthList.includes('governanceManage_qualityManage_ruleTemplate_config_edit')
                "
                variant="text"
                class="seeDetails has-right-border"
                @click.prevent="config(row)"
              >
                <!-- <n-tooltip class="tree-btn" content="配置质量规则" position="top">
                  <SvgIcon icon="new-target-service-config" class="icon" title="配置" />
                </n-tooltip> -->
                配置质量规则
              </n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
    <!-- 新建自定义算法脱敏 -->

    <n-drawer
      v-model="state.drawer"
      title=""
      :size="720"
      :esc-key-closeable="false"
      :close-on-click-overlay="false"
      class="template-config-drawer"
      :key="state.drawerKey"
      :before-close="
        () => {
          return false
        }
      "
    >
      <div class="n-drawer-body" v-loading="state.pageLoading">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <SvgIcon class="icon" icon="icon-drawer-title" />
            <div class="title">配置质量规则</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
        </div>
        <div class="n-drawer-body-content quality-content">
          <!-- 步骤条 -->
          <customizeSteps ref="customizeSteps" :stepsData="state.stepsData" />
          <div class="box-content">
            <!-- 配置信息 -->
            <div v-show="state.activeIndex === 0" class="list scroll-bar-style">
              <basic ref="basic" />
            </div>
            <!-- 生成规则 -->
            <div v-show="state.activeIndex === 1" class="list scroll-bar-style">
              <createRules ref="createRules" />
            </div>
          </div>
        </div>
        <div class="box-operate">
          <n-button size="sm" color="secondary" @click.prevent="closeDrawerFn">取消</n-button>
          <n-button
            size="sm"
            v-if="state.activeIndex !== 0"
            variant="solid"
            color="primary"
            @click.prevent="prev"
            >上一步</n-button
          >
          <n-button
            v-if="state.activeIndex !== state.stepsData.length - 1"
            class="next"
            size="sm"
            variant="solid"
            color="primary"
            @click.prevent="next"
            >下一步</n-button
          >

          <n-button
            v-if="state.activeIndex === state.stepsData.length - 1"
            :loading="state.btnLoading"
            class="save"
            size="sm"
            variant="solid"
            color="primary"
            @click.prevent="save"
            >生成规则</n-button
          >
        </div>
      </div>
    </n-drawer>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import customizeSteps from '@/components/customizeSteps'
  import basic from './components/basic'
  import createRules from './components/generative-rules'

  export default {
    title: 'List',
    components: { customizeSteps, basic, createRules },
    props: {},
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const form = ref('')
      const customizeSteps = ref()

      const basic = ref()
      const createRules = ref()
      let componentName = null
      let nextComponentName = null
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        drawerKey: 1,
        drawer: false,
        stepsData: [
          {
            label: '配置信息',
          },
          {
            label: '生成规则',
          },
        ],
        activeIndex: 0, // 所处步数
        expend: false, //展开，收起
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        loading: false,
        saveLoading: false,
        btnLoading: false,
        dialogVisible: false,
        originalFormInline: {
          name: '',
          ruleType: '',
          sexomorphicDimension: '',
          applicableDataType: '',
        },
        formInline: {
          name: '',
          ruleType: '',
          sexomorphicDimension: '',
          applicableDataType: '',
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'name', name: '规则模板', slot: 'name', width: 200 },
          { prop: 'ruleType', name: ' 类型', slot: 'ruleType', width: 120 },
          {
            prop: 'sexomorphicDimensionName',
            name: '六性维度',
            slot: 'sexomorphicDimensionName',
            width: 110,
          },
          {
            prop: 'applicableDataType',
            name: '适用数据范围',
            slot: 'applicableDataType',
            width: 240,
          },
          { prop: 'description', name: '描述', slot: 'description' },
        ],
        tableData: [],
        tableHeight: 436,
        pagination: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        ruleTemplateOptions: [
          {
            name: '字段级',
            value: 'FIELD',
          },
          {
            name: '表级',
            value: 'TABLE',
          },
          {
            name: '跨表级',
            value: 'ACROSS_TABLE',
          },
        ],
        sexomorphicDimensionOptions: [
          {
            name: '准确性',
            value: 'ACCURACY',
          },
          {
            name: '有效性',
            value: 'VALIDITY',
          },
          {
            name: '完整性',
            value: 'COMPLETENESS',
          },
          {
            name: '唯一性',
            value: 'UNIQUENESS',
          },
          {
            name: '一致性',
            value: 'CONSISTENCY',
          },
          {
            name: '及时性',
            value: 'TIMELINESS',
          },
        ],
        applicableDataTypeOptions: [
          {
            name: '不限范围',
            value: 'ALL',
          },
          {
            name: '数值型',
            value: 'NUMBERICAL_TYPE',
          },
        ],

        form: {
          basicData: {},
          tableData: {},
        },
        editData: {}, //编辑时候的数据
        editId: null,
      })

      const methods = {
        //展开 收起
        expendSearch() {
          state.expend = !state.expend
          methods.setTableHeight()
        },
        //设置表格高度
        setTableHeight() {
          if (state.expend) {
            state.tableHeight = document.body.offsetHeight - 151 - 160 - 42
          } else {
            state.tableHeight = document.body.offsetHeight - 151 - 160
          }
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            name: '',
            ruleType: '',
            sexomorphicDimension: '',
            applicableDataType: '',
          }
          methods.searchClickFn()
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.initTable(true)
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              name: state.formInline.name || null,
              ruleType: state.formInline.ruleType || null,
              sexomorphicDimension: state.formInline.sexomorphicDimension || null,
              applicableDataType: state.formInline.applicableDataType || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataQuality['getQualityRuleTemplatePage'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              let { success, data } = res
              if (success) {
                data.list.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                state.tableData = data.list
                state.pagination.total = data.total
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },

        //关闭抽屉
        closeDrawerFn() {
          state.drawer = false
          state.drawerKey++
          state.activeIndex = 0
        },
        //配置
        config(row) {
          state.drawer = true
          nextTick(() => {
            basic.value?.editInit(
              {
                ruleSource: 'TEMPLATE',
                ruleType: row.ruleType,
                templeId: row.id,
              },
              {
                fromRuleTemplate: true,
                templateName: row.name,
                enName: row.enName,
                applicableDataType: row.applicableDataType,
              },
            )
          })
        },

        // 上一步
        prev() {
          if (state.activeIndex-- <= 0) {
            state.activeIndex = 0
          }
          customizeSteps.value.updatedActive({ index: state.activeIndex })
        },
        // 下一步
        async next() {
          let passed = false
          let result = null

          // 分步校验
          switch (state.activeIndex) {
            case 0:
              // 校验是否通过
              result = await basic.value.getAllData()

              passed = result.passed
              state.form.basicData = result.data
              nextComponentName = createRules.value
              break
            case 1:
              result = componentName.getAllData()
              if (result.length) {
                passed = true
              } else {
                passed = false
                ElNotification({
                  title: '提示',
                  message: '数据为空',
                  type: 'warning',
                })
              }
              state.form.tableData = result
              break
          }

          if (!passed) return
          if (state.activeIndex++ >= state.stepsData.length - 1) {
            state.activeIndex = state.stepsData.length - 1
          }

          if (state.activeIndex === 1) {
            nextComponentName.editInit(state.form.basicData)
          }
          customizeSteps.value.updatedActive({ index: state.activeIndex })
        },
        // 保存规则
        save() {
          let data = { ...state.form.basicData }
          let ruleTableFieldList = createRules.value.getAllData()
          data.ruleTableFieldList = ruleTableFieldList
          if (data.ruleTableFieldList.length === 0) {
            ElNotification({
              title: '提示',
              message: '请添加表！',
              type: 'warning',
            })
            return false
          }
          state.btnLoading = true
          let interfaceUrlName = 'qualityRuleSave'
          let _message = '新增成功'
          if (state.editId) {
            data.id = state.editId
            interfaceUrlName = 'qualityRuleUpdate'
            _message = '更新成功'
          }

          api.dataQuality[interfaceUrlName](data)
            .then((res) => {
              let { success } = res
              state.btnLoading = false
              if (success) {
                ElNotification({
                  title: '提示',
                  message: _message,
                  type: 'success',
                })

                state.drawer = false
                router.push({ name: 'dataQualityIndex' })
              }
            })
            .catch(() => {
              state.btnLoading = false
            })
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable(true)
      })
      watch(
        () => state.activeIndex,
        (val) => {
          switch (val) {
            case 1:
              componentName = createRules.value // 生成规则
              nextComponentName = null
              break
            default: // 基础信息
              componentName = basic.value
              nextComponentName = createRules.value
              break
          }
        },
      )

      return {
        buttonAuthList,
        state,
        form,
        basic,
        createRules,
        customizeSteps,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .tools {
    background-color: #fff;
    border-radius: 2px;
    margin-bottom: 10px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 10px 16px;
      .createTime {
        margin-right: 32px;
        width: 260px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          background: #1e89ff;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
      }
    }
  }

  .rule-template-list {
    height: 100%;

    &.isLzos {
      padding: 0;
    }

    .rule-template-page-top {
      margin-bottom: 8px;
      background-color: #fff;
      border-radius: 8px;

      .add-box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 52px;
        padding: 10px 16px;
        border-bottom: 1px solid #c5d0ea;

        svg {
          font-size: 16px;
          cursor: pointer;
        }

        .top-left {
          .nancalui-button {
            padding: 0 16px;
            color: #fff;
            font-size: 14px;
            background: $themeBlue;
            border-radius: 6px;

            &:hover {
              color: #fff;
              background-color: $themeBlueHover;
            }

            :deep(.button-content) {
              display: flex;
              align-items: center;
            }
          }

          .nl-btn-link {
            padding: 0 8px;
          }

          svg {
            margin-right: 4px;
          }
        }
      }

      .top-bottom {
        display: flex;
        justify-content: space-between;
        padding: 0 10px 0 16px;

        .search-box-left {
          display: flex;
          flex-wrap: wrap;
          width: 828px;
          height: 42px;
          overflow: hidden;

          &.expend {
            height: auto;
            padding-bottom: 10px;
          }

          .item-box {
            margin-top: 10px;
          }

          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 16px;
          }
        }

        .search-box-right {
          display: flex;
          padding: 10px 0;

          :deep(.nancalui-button) {
            padding: 0 15px;
            &:hover {
              background-color: #e3ecff;
            }
            &.nancalui-button--text {
              margin-left: 8px;
              padding: 0 7px;
              &:last-of-type {
                margin-left: 0;
              }
            }

            .button-content {
              display: flex;
              align-items: center;

              svg.expend {
                transform: rotate(180deg);
              }
            }
          }

          svg {
            margin-left: 4px;
            font-size: 16px;
          }
        }
      }
    }

    .rule-template-page-bottom {
      height: calc(100% - 60px);
      background-color: #fff;
      border-radius: 2px;

      &.expend {
        height: calc(100% - 60px - 42px);
      }

      .common-section-header {
        position: relative;
        height: 52px;
        padding: 0px 16px;
        color: var(----, rgba(0, 0, 0, 0.9));
        font-weight: bolder;
        font-size: 18px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 52px;
        border: none;

        &::before {
          position: absolute;
          top: 17px;
          left: 0;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          border-radius: 0;
          content: '';
        }
      }

      .rule-template-page-bottom-table-box {
        height: calc(100% - 52px);
        .template-name-box {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          span {
            display: inline-block;
            align-content: center;
            width: 52px;
            height: 24px;
            margin-right: 6px;
            padding: 0 4px;
            color: #ed4ea5;
            font-size: 14px;
            line-height: 22px;
            text-align: center;
            background: var(----, #fff0f6);
            border: 1px solid var(---, #ffd4e6);
            border-radius: 2px;
          }
        }

        .template-type-box {
          &-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .name {
              max-width: 200px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          svg {
            margin-right: 6px;
            font-size: 16px;
          }
        }

        .template-desc-box {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .taskStatus {
          .circle {
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 4px;
            background-color: $themeBlue;
            border-radius: 50%;

            &.green {
              background-color: #04c495;
            }

            &.yellow {
              background-color: #ff7d00;
            }

            &.grey {
              background-color: #b8b8b8;
            }
          }
        }

        .edit-box {
          .seeDetails {
            color: $themeBlue;

            &.nancalui-button--disabled {
              svg {
                color: #b8b8b8;
              }
            }
          }
        }
      }
    }
  }
</style>
<style lang="scss">
  $content: #fff;
  $activeFont: #fff;
  @import '@/styles/variables.scss';

  .template-config-drawer {
    .quality-content {
      padding: 8px 0 0 16px !important;
      overflow-y: hidden !important;

      .box-steps {
        justify-content: flex-start;
        height: 48px;
        padding: 8px 0;
        border-bottom: none;
      }

      .box-content {
        height: calc(100% - 52px);

        .list {
          height: 100%;
          overflow-y: auto;
        }
      }
    }

    .box-operate {
      height: 64px;
      padding: 16px;
      text-align: right;
      background-color: $content;
      border-radius: 8px 8px 0 0;

      .nancalui-button {
        padding: 0 16px;
      }

      .nancalui-button.next,
      .nancalui-button.save {
        color: $activeFont;
        background-color: $themeBlue;
      }
    }

    .options-box-bg {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 64px;
      padding: 16px;
      background-color: #fff;
      border-radius: 0 0 8px 8px;

      .options-box-content {
        display: inline-block;
        width: 100%;
        text-align: right;
        background-color: #fff;
      }
    }
  }
</style>
