<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>线框/复合指标@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0.5" fill="none" fill-rule="evenodd">
        <g id="指标管理-新建复合指标-编辑表达式" transform="translate(-747.000000, -124.000000)">
            <g id="编组-13备份-3" transform="translate(720.000000, 0.000000)">
                <g id="穿梭框（左）" transform="translate(16.000000, 68.000000)">
                    <g id="选项/默认" transform="translate(0.000000, 49.000000)">
                        <g id="线框/复合指标" transform="translate(12.000000, 8.000000)">
                        
                            <rect id="矩形" stroke="#04C495" fill="#E6FFF4" fill-rule="nonzero" x="0" y="0" width="16" height="16" rx="4"></rect>
                            <g id="复" transform="translate(2.684000, 2.088000)" fill="#04C495" fill-rule="nonzero">
                                <path d="M2.652,1.752 L10.068,1.752 L10.068,0.996 L3.048,0.996 C3.168,0.72 3.276,0.432 3.372,0.144 L2.52,0 C2.076,1.344 1.236,2.472 0,3.408 L0.516,4.08 C1.044,3.684 1.5,3.252 1.908,2.784 L1.908,6.372 L3.444,6.372 C2.796,7.236 1.788,7.944 0.408,8.496 L0.852,9.228 C1.476,8.94 2.04,8.616 2.532,8.28 C3.18,8.844 3.912,9.324 4.728,9.72 C3.492,10.08 2.004,10.296 0.264,10.368 L0.612,11.124 C2.628,11.004 4.332,10.692 5.748,10.164 C7.116,10.692 8.688,10.992 10.44,11.076 L10.644,10.284 C9.228,10.236 7.932,10.044 6.78,9.708 C7.764,9.204 8.556,8.58 9.156,7.812 L9.156,7.188 L3.78,7.188 C3.996,6.924 4.2,6.66 4.38,6.372 L9.132,6.372 L9.132,2.58 L2.076,2.58 C2.28,2.316 2.472,2.04 2.652,1.752 Z M5.76,9.372 C4.788,8.988 3.924,8.496 3.156,7.896 L8.088,7.896 C7.5,8.484 6.72,8.976 5.76,9.372 Z M8.292,5.676 L2.76,5.676 L2.76,4.788 L8.292,4.788 L8.292,5.676 Z M2.76,4.14 L2.76,3.264 L8.292,3.264 L8.292,4.14 L2.76,4.14 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>