import Layout from '@/layout'

// 功能模块-非结构化数据管理
export default [
  {
    path: '/documentManage',
    component: Layout,
    redirect: '/documentManage/numberRules',
    name: 'documentManage',
    code: 'documentManage',
    meta: {
      title: '非结构化数据管理',
      icon: 'icon-config',
      parentRouterName: 'governanceManage',
      code: 'documentManage',
    },
    children: [
      {
        path: 'numberRules',
        name: 'numberRules',
        code: 'numberRules',
        redirect: '/documentManage/numberRules/rulesList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '编号规则管理',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'numberRules',
        },
        children: [
          {
            path: 'rulesList',
            name: 'rulesList',
            hidden: true,
            component: () => import('@/views/document-management/number-rules/list'),
            meta: {
              title: '编号规则',
              activeMenu: '/documentManage/numberRules',
              parentRouterName: 'documentManage',
              code: 'numberRules',
            },
          },
        ],
      },
      {
        path: 'templateManage',
        name: 'templateManage',
        code: 'templateManage',
        redirect: '/documentManage/templateManage/templateManageList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '模板管理',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'templateManage',
        },
        children: [
          {
            path: 'templateManageList',
            name: 'templateManageList',
            hidden: true,
            component: () => import('@/views/document-management/template-manage/list'),
            meta: {
              title: '模板管理',
              activeMenu: '/documentManage/templateManage',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'templateManage',
            },
          },
          {
            path: 'templateAdd',
            name: 'templateAdd',
            hidden: true,
            component: () => import('@/views/document-management/template-manage/add'),
            meta: {
              title: '新增模板',
              activeMenu: '/documentManage/templateManage',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'templateEdit',
            name: 'templateEdit',
            hidden: true,
            component: () => import('@/views/document-management/template-manage/add'),
            meta: {
              title: '编辑模板',
              activeMenu: '/documentManage/templateManage',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'templatePreview',
            name: 'templatePreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '模板预览',
              activeMenu: '/documentManage/templateManage',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'docClassification',
        name: 'docClassification',
        code: 'docClassification',
        redirect: '/documentManage/docClassification/docClassificationList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '非结构化数据分类',
          icon: 'icon-config',
          parentRouterName: 'governanceManage',
          code: 'docClassification',
        },
        children: [
          {
            path: 'docClassificationList',
            name: 'docClassificationList',
            hidden: true,
            component: () => import('@/views/document-management/document-classification/list'),
            meta: {
              title: '非结构化数据分类',
              activeMenu: '/documentManage/docClassification',
              parentRouterName: 'governanceManage',
              code: 'docClassification',
            },
          },
          {
            path: 'docClassifyAuthorizationList',
            name: 'docClassifyAuthorizationList',
            hidden: true,
            component: () =>
              import('@/views/document-management/document-classification/authorization-list'),
            meta: {
              title: '分类授权管理',
              activeMenu: '/documentManage/docClassification',
              parentRouterName: 'governanceManage',
            },
          },
        ],
      },
      {
        path: 'documentPreparation',
        name: 'documentPreparation',
        code: 'documentPreparation',
        redirect: '/documentManage/documentPreparation/documentPreparationList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '非结构化数据编制',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentPreparation',
        },
        children: [
          {
            path: 'documentPreparationList',
            name: 'documentPreparationList',
            hidden: true,
            component: () => import('@/views/document-management/document-preparation/list'),
            meta: {
              title: '非结构化数据编制',
              activeMenu: '/documentManage/documentPreparation',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentPreparation',
            },
          },
          {
            path: 'documentPreparationAdd',
            name: 'documentPreparationAdd',
            hidden: true,
            component: () => import('@/views/document-management/document-preparation/add'),
            meta: {
              title: '非结构化数据编制',
              activeMenu: '/documentManage/documentPreparation',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentPreparationPreview',
            name: 'documentPreparationPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '非结构化数据预览',
              activeMenu: '/documentManage/documentPreparation',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentPreparationVersion',
            name: 'documentPreparationVersion',
            hidden: true,
            component: () => import('@/views/document-management/components/version'),
            meta: {
              title: '版本管理',
              activeMenu: '/documentManage/documentPreparation',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'documentCollaboration',
        name: 'documentCollaboration',
        code: 'documentCollaboration',
        redirect: '/documentManage/documentCollaboration/documentCollaborationList',
        component: () => import('@/views/document-management/index.vue'),
        meta: {
          title: '非结构化数据协同',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentCollaboration',
        },
        children: [
          {
            path: 'documentCollaborationList',
            name: 'documentCollaborationList',
            hidden: true,
            component: () => import('@/views/document-management/document-collaboration/list.vue'),
            meta: {
              title: '非结构化数据协同',
              activeMenu: '/documentManage/documentCollaboration',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentCollaboration',
            },
          },
          {
            path: 'documentCollaborationAdd',
            name: 'documentCollaborationAdd',
            hidden: true,
            component: () => import('@/views/document-management/document-collaboration/add.vue'),
            meta: {
              title: '非结构化数据协同编制',
              activeMenu: '/documentManage/documentCollaboration',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollaborationPreview',
            name: 'documentCollaborationPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview.vue'),
            meta: {
              title: '非结构化数据协同预览',
              activeMenu: '/documentManage/documentCollaboration',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollaborationVersion',
            name: 'documentCollaborationVersion',
            hidden: true,
            component: () => import('@/views/document-management/components/version.vue'),
            meta: {
              title: '协同版本管理',
              activeMenu: '/documentManage/documentCollaboration',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'documentUpload',
        name: 'documentUpload',
        code: 'documentUpload',
        redirect: '/documentManage/documentUpload/documentUploadList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '非结构化数据上传',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentUpload',
        },
        children: [
          {
            path: 'documentUploadList',
            name: 'documentUploadList',
            hidden: true,
            component: () => import('@/views/document-management/document-upload/list'),
            meta: {
              title: '非结构化数据上传',
              activeMenu: '/documentManage/documentUpload',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentUpload',
            },
          },
          {
            path: 'documentUploadEdit',
            name: 'documentUploadEdit',
            hidden: true,
            component: () => import('@/views/document-management/document-upload/edit'),
            meta: {
              title: '非结构化数据上传编辑',
              activeMenu: '/documentManage/documentUpload',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentUploadPreview',
            name: 'documentUploadPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '非结构化数据上传预览',
              activeMenu: '/documentManage/documentUpload',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentUploadVersion',
            name: 'documentUploadVersion',
            hidden: true,
            component: () => import('@/views/document-management/components/version'),
            meta: {
              title: '非结构化数据上传版本管理',
              activeMenu: '/documentManage/documentUpload',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'documentCollection',
        name: 'documentCollection',
        code: 'documentCollection',
        redirect: '/documentManage/documentCollection/documentCollectionList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '非结构化数据采集',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentCollection',
        },
        children: [
          {
            path: 'documentCollectionList',
            name: 'documentCollectionList',
            hidden: true,
            component: () => import('@/views/document-management/document-collection/list'),
            meta: {
              title: '非结构化数据采集',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentCollection',
            },
          },
          {
            path: 'documentCollectionAdd',
            name: 'documentCollectionAdd',
            hidden: true,
            component: () => import('@/views/document-management/document-collection/add'),
            meta: {
              title: '非结构化数据采集新增',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionApiAdd',
            name: 'documentCollectionApiAdd',
            hidden: true,
            component: () => import('@/views/document-management/document-collection/add-api'),
            meta: {
              title: '非结构化数据采集新增',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionTask',
            name: 'documentCollectionTask',
            hidden: true,
            component: () => import('@/views/document-management/document-collection/task-list'),
            meta: {
              title: '任务管理',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionApi',
            name: 'documentCollectionApi',
            hidden: true,
            component: () => import('@/views/document-management/document-collection/api-list'),
            meta: {
              title: '源端管理',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionEdit',
            name: 'documentCollectionEdit',
            hidden: true,
            component: () => import('@/views/document-management/document-preparation/add'),
            meta: {
              title: '非结构化数据采集编辑',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionPreview',
            name: 'documentCollectionPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '非结构化数据预览',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentCollectionVersion',
            name: 'documentCollectionVersion',
            hidden: true,
            component: () => import('@/views/document-management/components/version'),
            meta: {
              title: '版本管理',
              activeMenu: '/documentManage/documentCollection',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'documentTag',
        name: 'documentTag',
        code: 'documentTag',
        redirect: '/documentManage/documentTag/documentTagList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '标签管理',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentTag',
        },
        children: [
          {
            path: 'documentTagList',
            name: 'documentTagList',
            hidden: true,
            component: () => import('@/views/document-management/document-tag/list'),
            meta: {
              title: '标签管理',
              activeMenu: '/documentManage/documentTag',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentTag',
            },
          },
          {
            path: 'documentTagPreview',
            name: 'documentTagPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '标签预览',
              activeMenu: '/documentManage/documentTag',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'documentTrait',
        name: 'documentTrait',
        code: 'documentTrait',
        redirect: '/documentManage/documentTrait/documentTraitList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '特征管理',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentTrait',
        },
        children: [
          {
            path: 'documentTraitList',
            name: 'documentTraitList',
            hidden: true,
            component: () => import('@/views/document-management/document-trait/list'),
            meta: {
              title: '特征管理',
              activeMenu: '/documentManage/documentTrait',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentTrait',
            },
          },
          {
            path: 'documentTraitEdit',
            name: 'documentTraitEdit',
            hidden: true,
            component: () => import('@/views/document-management/document-trait/edit'),
            meta: {
              title: '非结构化数据特征编辑',
              activeMenu: '/documentManage/documentTrait',
              parentRouterName: 'documentManage',
            },
          },
          {
            path: 'documentTraitPreview',
            name: 'documentTraitPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '非结构化数据预览',
              activeMenu: '/documentManage/documentTrait',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      {
        path: 'tagLibraryManagement',
        name: 'tagLibraryManagement',
        code: 'tagLibraryManagement',
        redirect: '/documentManage/tagLibraryManagement/tagLibraryManagementList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '标签库管理',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'tagLibraryManagement',
        },
        children: [
          {
            path: 'tagLibraryManagementList',
            name: 'tagLibraryManagementList',
            hidden: true,
            component: () => import('@/views/document-management/tagLibrary-management/list'),
            meta: {
              title: '标签库管理',
              activeMenu: '/documentManage/tagLibraryManagement',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'tagLibraryManagement',
            },
          },
        ],
      },
      {
        path: 'documentEntry',
        name: 'documentEntry',
        code: 'documentEntry',
        redirect: '/documentManage/documentEntry/documentEntryList',
        component: () => import('@/views/document-management/index'),
        meta: {
          title: '非结构化数据条目',
          icon: 'icon-config',
          parentRouterName: 'documentManage',
          code: 'documentEntry',
        },
        children: [
          {
            path: 'documentEntryList',
            name: 'documentEntryList',
            hidden: true,
            component: () => import('@/views/document-management/document-entry/list'),
            meta: {
              title: '非结构化数据条目',
              activeMenu: '/documentManage/documentEntry',
              parentRouterName: 'documentManage',
              keepAlive: true,
              code: 'documentEntry',
            },
          },
          {
            path: 'documentEntryPreview',
            name: 'documentEntryPreview',
            hidden: true,
            component: () => import('@/views/document-management/components/preview'),
            meta: {
              title: '非结构化数据条目预览',
              activeMenu: '/documentManage/documentEntry',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
      // 数据打标
      {
        path: 'dataLabel',
        name: 'dataLabel',
        hidden: true,
        meta: {
          title: '非结构化数据标注',
          parentRouterName: 'documentManage',
          code: 'dataLabel',
        },
        redirect: '/documentManage/dataLabel/list',
        children: [
          {
            path: 'list',
            name: 'dataLabelList',
            component: () => import('@/views/document-management/document-annotation/list'),
            meta: {
              title: '非结构化数据标注',
              parentRouterName: 'documentManage',
              code: 'dataLabel',
            },
          },
          // 数据打标标记页
          {
            path: 'mark',
            name: 'dataLabelMark',
            component: () => import('@/views/document-management/document-annotation/mark'),
            meta: {
              title: '数据打标标记',
              parentRouterName: 'documentManage',
            },
          },
          // 数据打标-预览页
          {
            path: 'preview',
            name: 'dataLabelPreview',
            component: () => import('@/views/document-management/document-annotation/preview'),
            meta: {
              title: '数据打标预览',
              parentRouterName: 'documentManage',
            },
          },
        ],
      },
    ],
  },
]
