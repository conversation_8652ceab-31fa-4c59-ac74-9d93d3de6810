<template>
  <section class="backlog">
    <div class="tab">
      <div
        :class="{ 'tab-node': true, active: item.code === state.activeCode }"
        v-for="item in state.titles"
        :key="item.code"
        @click="tabChange(item.code)"
        >{{ item.name }}<span>({{ item.count || 0 }})</span></div
      >
    </div>

    <div class="table">
      <div class="table-row" v-for="item in state.tableData" :key="item.taskName">
        <div class="table-cell"><a :href="item.url">[{{ item.taskName }}]{{item.taskDesc}}</a></div>
        <div class="table-cell">{{item.appsendName}}</div>
        <div class="table-cell">{{item.sendTime}}</div>
      </div>
    </div>

  </section>
</template>

<script setup>
  import { ref, reactive, toRefs, onMounted } from 'vue'
  import api from '@/api/index'

const props=defineProps({
  backLog: {
    type: Object,
    default: () => {},
  },
})

  watch(
    () => props.backLog,
    (newVal) => {
      state.titles = newVal.titles
      state.waitAllData = newVal.waitAllData
      state.tableData = newVal.tableData
    },
    { deep: true },
  )

  const state = reactive({
    activeCode: 'ALL',
    titles: [],
    waitAllData: [],
    tableData: [],
  })


  const tabChange = (code) => {
    state.activeCode = code
    state.tableData = []
    state.waitAllData.forEach((parent) => {
      if (code === 'ALL') {
        parent.dataList.forEach((item) => {
          state.tableData.push(item)
        })
      } else {
        if (parent.code === code) {
          parent.dataList.forEach((item) => {
            state.tableData.push(item)
          })
        }
      }
    })
  }

  const landingWaitFn = () => {
    api.system
      .landingWait({ waitProcessQueryBO: '' })
      .then((res) => {
        if (res.success) {
          state.tableData = []
          state.waitAllData = res.data.waitProcessList

          // 计算每个 title 的 count
          const titlesWithCount = res.data.titles.map((title) => {
            let count = 0

            if (title.code === 'ALL') {
              // ALL 统计所有 code 下 dataList 的总数
              count = state.waitAllData.reduce((total, parent) => {
                return total + (parent.dataList ? parent.dataList.length : 0)
              }, 0)
            } else {
              // 根据 code 匹配对应的 dataList 总数
              const matchedData = state.waitAllData.find((parent) => parent.code === title.code)
              count = matchedData && matchedData.dataList ? matchedData.dataList.length : 0
            }

            return {
              ...title,
              count: count,
            }
          })

          state.titles = titlesWithCount

          // 构建表格数据
          state.waitAllData.forEach((parent) => {
            parent.dataList.forEach((item) => {
              state.tableData.push(item)
            })
          })
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  }

  onMounted(() => {
    // landingWaitFn()
    state.titles = props.backLog.titles
      state.waitAllData = props.backLog.waitAllData
      state.tableData = props.backLog.tableData
  })
</script>
<style scoped lang="scss">
  .backlog {
    position: absolute;
    top: 70px;
    right: 20px;
    z-index: 1;
    width: 30.7%;
    height:368px;
    padding:0 16px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;

    .tab {
      display: flex;
      gap: 24px;
      align-items: center;
      height: 48px;
      line-height: 48px;
      overflow-x: auto;
      overflow-y:hidden;
      white-space: nowrap;
      border-bottom: 1px solid var(---, #DCDFE6);

      .tab-node {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        height: 100%;
        border-bottom: 2px solid transparent;
        cursor: pointer;

        span{
          color:#1E89FF;
        }

        &:hover,
        &.active {
          color: #1E89FF;
          border-color:#1E89FF;
        }
      }
    }

    .table{
      height: calc(100% - 48px);
      overflow: auto;
      padding:16px 0;

      .table-row{
        display: flex;
        align-items: center;
        height: 48px;
        line-height: 48px;
        &:hover{
          background: #EBF4FF;
        }
        &:nth-child(odd){
          background: #EBF4FF;
        }
        .table-cell{
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding:0 16px;
          color:#606266;
          font-size:14px;

          &:nth-child(2){
            flex: 0 0 100px;
          }
          &:nth-child(3){
            flex: 0 0 160px;
          }

          a{
            color:#1E89FF;
          }
        }
      }
    }
  }
</style>
