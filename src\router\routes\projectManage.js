// 系统管理
import Layout from '@/layout'

export default [
  {
    path: '/projectManage',
    component: Layout,
    redirect: '/projectManage/project',
    name: 'frameworkManagement',
    code: 'frameworkManagement',
    meta: {
      title: '架构管理',
      code: 'frameworkManagement',
      icon: 'icon-framework',
      parentRouterName: 'systemManage',
      unShowProject: true,
    },
    children: [
      // 项目管理
      {
        path: 'project',
        name: 'projectManage',
        code: 'projectManage',
        redirect: '/projectManage/project/list',
        component: () => import('@/views/project-manage/project/index'),
        meta: {
          title: '项目管理',
          code: 'projectManage',
          parentRouterName: 'systemManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            hidden: true,
            name: 'projectList',
            component: () => import('@/views/project-manage/project/list'),
            meta: {
              title: '场景',
              activeMenu: '/projectManage/project',
              parentRouterName: 'systemManage',
              keepAlive: true,
              unShowProject: true,
            },
          },
          {
            path: 'add',
            hidden: true,
            name: 'projectAdd',
            component: () => import('@/views/project-manage/project/add'),
            meta: {
              title: '新增场景',
              activeMenu: '/projectManage/project',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
          {
            path: 'addPerson',
            hidden: true,
            name: 'projectAddPerson',
            component: () => import('@/views/project-manage/project/add-person'),
            meta: {
              title: '新增场景人员',
              activeMenu: '/projectManage/project',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
          {
            path: 'detail',
            hidden: true,
            name: 'projectDetail',
            component: () => import('@/views/project-manage/project/detail'),
            meta: {
              title: '查看',
              activeMenu: '/projectManage/project',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
          {
            path: 'edit',
            hidden: true,
            name: 'projectEdit',
            component: () => import('@/views/project-manage/project/add'),
            meta: {
              title: '编辑场景',
              activeMenu: '/projectManage/project',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
        ],
      },
      // 消息中心
      {
        path: 'notice',
        name: 'notice',
        code: 'notice',
        component: () => import('@/views/notice/list'),
        meta: {
          title: '消息中心',
          icon: '',
          parentRouterName: 'systemManage',
          code: 'notice',
          unShowProject: true,
        },
      },
      // 链接管理
      {
        path: 'linkConfigManage',
        name: 'linkConfigManage',
        code: 'linkConfigManage',
        redirect: '/projectManage/linkConfigManage/list',
        component: () => import('@/views/system-manage/LinkConfig/index'),
        meta: {
          title: '链接管理',
          icon: '',
          parentRouterName: 'systemManage',
          code: 'linkConfigManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'linkConfigList',
            hidden: true,
            component: () => import('@/views/system-manage/LinkConfig/list.vue'),
            meta: {
              title: '链接管理',
              unShowProject: true,
              parentRouterName: 'systemManage',
              code: 'linkConfigManage',
            },
          },
        ],
      },
      // 流程管理
      {
        path: 'processConfigManage',
        name: 'processConfigManage',
        code: 'processConfigManage',
        redirect: '/projectManage/processConfigManage/list',
        component: () => import('@/views/system-manage/processConfig/index'),
        meta: {
          title: '流程管理',
          icon: '',
          parentRouterName: 'systemManage',
          code: 'processConfigManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'processConfigList',
            hidden: true,
            component: () => import('@/views/system-manage/processConfig/list.vue'),
            meta: {
              title: '流程管理',
              unShowProject: true,
              parentRouterName: 'systemManage',
              code: 'processConfigManage',
            },
          },
          {
            path: 'set',
            name: 'processConfigSet',
            hidden: true,
            component: () => import('@/views/system-manage/processConfig/set.vue'),
            meta: {
              title: '流程管理配置',
              unShowProject: true,
              parentRouterName: 'systemManage',
            },
          },
        ],
      },
    ],
  },
]
