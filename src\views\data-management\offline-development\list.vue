<script>
  export default {
    name: 'offlineDevelopment',
  }
</script>
<script setup>
  import api from '@/api/index'
  import { taskClear } from '@/api/dataManage'
  import { useRouter } from 'vue-router'
  import LocalTree from './components/tree.vue'
  import workFlow from './components/workFlowBox.vue'
  import offlineWork from './components/offlineWork'
  import hiveDDL from './components/hiveDDL.vue'
  import DorisDDL from './components/DorisDDL.vue'
  import DirDialog from './components/dirDialog.vue'
  // 数据同步
  import dataAsync from './components/dataAsync.vue'
  import dataUpload from './components/dataUpload.vue'

  // 业务流程弹窗组件
  import OperationFlowDialog from './components/operationFlowDialog.vue'

  const images = import.meta.glob('@/assets/img/offlineDev/*.png', { eager: true })
  const { proxy } = getCurrentInstance()
  const treeRef = ref(null)
  const currentComponent = ref(workFlow)
  const dirDialogRef = ref(null)
  const operationFlowDialogRef = ref(null)
  const allComRef = ref(null)

  const state = reactive({
    oldPage: null,
    treeSearchText: '',
    currentWorkFlowId: null, //当前treeId
    taskId: null, //当前任务ID
    processId: null,
    checkTreeId: null, // 当前标签选中的树id
    tagArr: [],
    treeList: [
      {
        id: -1,
        name: '全部',
        type: 'DIRECTORY',
        isTemplateDirectory: false,
        children: [],
      },
    ],
    // 当前节点
    currentNode: null,
    // 展示目录弹窗
    showDirDialog: false,
    pos: {
      x: 160,
      y: 530,
    },
  })

  const visibleTag = ref(false)
  const currentIndex = ref(null)

  const handleContextMenu = (e, index) => {
    e.preventDefault()
    state.pos = {
      x: e.pageX,
      y: e.pageY + 20,
    }
    currentIndex.value = index
    visibleTag.value = true
  }

  const handleCommand = (command) => {
    visibleTag.value = false
    switch (command) {
      case 'closeAll':
        state.tagArr = []
        break
      case 'closeLeft':
        state.tagArr = state.tagArr.slice(currentIndex.value)
        break
      case 'closeRight':
        state.tagArr = state.tagArr.slice(0, currentIndex.value + 1)
        break
      case 'closeOther':
        state.tagArr = [state.tagArr[currentIndex.value]]
        break
      case 'closeCurrent':
        state.tagArr.splice(currentIndex.value, 1)
        break
    }
    if (state.tagArr.length > 0) {
      const tag = state.tagArr[state.tagArr.length - 1]
      state.checkTreeId = tag.treeId
      state.currentWorkFlowId = tag.treeId
      state.processId = tag.processId || null
      state.taskId = tag.taskId || null

      currentComponent.value = tag.com
    }
  }

  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }

  // 双击画布打开标签
  const openLabelFn = (item) => {
    methods.showLabelFn(item)
  }
  const changeTree = (data) => {
    methods.getTreeList().then(() => {
      if (data) {
        nextTick(() => {
          switch (data.type) {
            case 'delFn':
              const index = state.tagArr.findIndex((v) => v.treeId === data.id)
              state.tagArr.splice(index, 1)
              if (state.tagArr.length > 0) {
                const tag = state.tagArr[state.tagArr.length - 1]
                state.checkTreeId = tag.treeId
                state.currentWorkFlowId = tag.treeId
                state.processId = tag.processId || null
                state.taskId = tag.taskId || null

                currentComponent.value = tag.com
              }
              break
            case 'JOB':
              treeRef.value.treeRef.setCurrentKey(data.id, true)
              state.currentNode = data
              methods.showLabelFn(data)
              break
            default:
              break
          }
        })
      }
    })
  }

  // 修改状态
  const changeLoadingStatus = (item) => {
    state.tagArr.forEach((val) => {
      // 优先使用精确匹配：同时匹配treeId和taskId，或者匹配treeId和processId
      if (
        (val.treeId === item.treeId && val.taskId === item.taskId) ||
        (val.treeId === item.treeId && val.processId === item.processId)
      ) {
        val.runStatus = item.runStatus // 1.'等待运行：waiting'  2.'运行中：running' 3.'运行成功：success' 4.'运行失败：fail'
      }
      // 如果精确匹配失败，则使用原来的逻辑作为备选
      else if (val.name === item.name || val.taskId === item.taskId) {
        val.runStatus = item.runStatus
      }
    })
  }

  onBeforeUnmount(() => {
    if (sessionStorage.getItem('workCacheData')) {
      sessionStorage.removeItem('workCacheData')
    }
  })

  const methods = {
    // 单机目录树
    nodeClick(val) {
      state.currentNode = val

      methods.showLabelFn(val)
    },
    // 判断新增或打开标签页
    showLabelFn(val) {
      if (
        state.tagArr.length >= 15 &&
        !state.tagArr.find((item) => item.treeId == val.id) == true
      ) {
        ElMessage.error('当前标签已达15个上限，请先关闭标签')
        return false
      }
      let hasLabel = false // 判断是否已有该标签
      state.curTobType = val.jobType
      state.tagArr.forEach((v) => {
        if (v.treeId === val.id) {
          hasLabel = true
          v.name = val.name
          currentComponent.value = v.com
          state.curTobType = v.jobType
        }
      })

      switch (val.type) {
        case 'WORKFLOW':
          state.currentWorkFlowId = val.id
          state.processId = val.processId
          state.checkTreeId = val.id
          break
        case 'JOB':
          state.currentWorkFlowId = null
          state.checkTreeId = val.id
          state.taskId = val.taskId
          break
        default:
          return
          break
      }

      let data = {
        name: val.name,
        treeId: val.id,
        processId: val.processId || null,
        taskId: val.taskId || null,
        com: offlineWork,
        jobType: val.jobType,
        nodeCode: val.nodeCode,
        isLoading: false,
      }
      if (!hasLabel) {
        if (val.type === 'WORKFLOW') {
          data.img = 'flow'
          data.com = workFlow
        } else if (val.type === 'JOB') {
          if (val.jobType === 'OW_DATA_UPLOAD') {
            data.img = '非结构化数据分析'
            data.com = dataUpload
          } else if (val.jobType === 'OW_DATA_ASYNC') {
            data.img = '同步至资源库'
            data.com = dataAsync
          } else if (val.jobType === 'OW_PY_SPARK') {
            data.img = 'PYSpark'
          } else if (val.jobType === 'OW_HIVE_SQL') {
            data.img = 'HiveSQL'
          } else if (val.jobType === 'OW_DORIS_SQL') {
            data.img = 'DorisSQL'
          } else if (val.jobType === 'OW_SPARK_SQL') {
            data.img = 'SparkSQL'
          } else if (val.jobType === 'OW_PYTHON') {
            data.img = 'Python'
          } else if (val.jobType === 'OW_SHELL') {
            data.img = 'Shell'
          } else if (val.jobType === 'OW_HIVE_DDL') {
            data.img = 'HiveDDL'
            data.com = hiveDDL
          } else if (val.jobType === 'OW_DORIS_DDL') {
            data.img = 'DorisDDL'
            data.com = DorisDDL
          }
        }

        currentComponent.value = data.com
        state.oldPage = data

        state.tagArr.push(data)
      }
    },
    // 获取目录树
    getTreeList() {
      return new Promise((resolve, reject) => {
        api.offlineJob.getOfflineJobTree().then((res) => {
          state.treeList[0].children = res?.data?.children || []
          // state.currentWorkFlowId = state.treeList[0].children[0]?.id
          // state.checkTreeId = state.treeList[0].children[0]?.id
          resolve()
        })
      })
    },
    // 创建目录弹窗
    createDirDialog() {
      // dirDialogRef.value.open(state.currentNode)
      dirDialogRef.value.open()
    },
    // 创建业务流程
    createOperationFlow() {
      // operationFlowDialogRef.value.open(state.currentNode)
      operationFlowDialogRef.value.open()
    },
  }

  // 获取图片地址
  const getImageSrc = (url) => {
    return new URL(`/src/assets/img/offlineDev/${url}.png`, import.meta.url).href //本地文件路径
  }

  methods.getTreeList()

  // 改变tag
  const changeTag = (item) => {
    state.curTobType = item.jobType
    // 不缓存OW_HIVE_SQL类型的sessionStorage
    // if (state.oldPage) {
    //   let obj = sessionStorage.getItem('workCacheData')
    //     ? JSON.parse(sessionStorage.getItem('workCacheData'))
    //     : {}
    //   if (state.oldPage.img === 'flow') {
    //     obj[state.oldPage.treeId] = {
    //       ...allComRef.value.state,
    //       graphCells: allComRef.value.FlowGraph.graph.toJSON(),
    //     }
    //   } else {
    //     let newObj = allComRef.value?.state
    //     if (newObj?.codemirror) {
    //       delete newObj.codemirror
    //     }
    //
    //     obj[state.oldPage.taskId] = newObj
    //   }
    //
    //   sessionStorage.setItem('workCacheData', JSON.stringify(obj))
    // }

    state.oldPage = item

    state.checkTreeId = item.treeId
    state.currentWorkFlowId = item.treeId
    state.processId = item.processId || null
    state.taskId = item.taskId || null
    if (item.jobType) {
      state.currentWorkFlowId = null
    }
    currentComponent.value = item.com
  }
  // 删除tag
  const delTag = (index) => {
    let obj = sessionStorage.getItem('workCacheData')
      ? JSON.parse(sessionStorage.getItem('workCacheData'))
      : {}
    if (state.tagArr[index].img === 'flow' && obj[state.tagArr[index].treeId]) {
      delete obj[state.tagArr[index].treeId]
    } else if (obj[state.tagArr[index].taskId]) {
      delete obj[state.tagArr[index].taskId]
    }
    sessionStorage.setItem('workCacheData', JSON.stringify(obj))
    // 关闭OW_SPARK_SQL节点时需要清除任务
    if (['OW_SPARK_SQL'].includes(state.tagArr[index].jobType)) {
      taskClear({
        nodeCode: state.tagArr[index].nodeCode,
      })
    }
    state.tagArr.splice(index, 1)
    if (state.tagArr.length > 0) {
      const tag = state.tagArr[state.tagArr.length - 1]
      nextTick(() => {
        changeTag(tag)
      })
    }
  }
</script>
<template>
  <section class="container" @click="visibleTag = false">
    <DirDialog ref="dirDialogRef" @success="methods.getTreeList" />
    <OperationFlowDialog
      ref="operationFlowDialogRef"
      :treeList="state.treeList"
      @success="methods.getTreeList"
    />
    <section class="container-box">
      <section class="container-box-table">
        <section class="cf-tree">
          <div class="row">
            <div class="title">离线作业{{ state.checkTreeId }}</div>
            <n-popover trigger="hover" :position="['bottom-start']" align="start">
              <i class="cf-icon-add"> </i>
              <template #content>
                <ul class="cf-list">
                  <li class="cf-list-item" @click="methods.createDirDialog">
                    <svg
                      width="16"
                      height="17"
                      viewBox="0 0 16 17"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M15 8.5V5.5C15 4.94772 14.5523 4.5 14 4.5H8.66147C8.41579 4.5 8.17871 4.40956 7.99545 4.24594L6.32455 2.75406C6.14129 2.59044 5.90421 2.5 5.65853 2.5H2C1.44772 2.5 1 2.94772 1 3.5V13.5C1 14.0523 1.44772 14.5 2 14.5H8.5"
                        stroke="currentColor"
                        stroke-linecap="round"
                      />
                      <path
                        d="M10 12L15 12"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M12.5 9.5L12.5 14.5"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    新建目录
                  </li>
                  <li class="cf-list-item" @click="methods.createOperationFlow"
                    ><svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="17"
                      viewBox="0 0 16 17"
                      fill="none"
                    >
                      <circle
                        cx="1.75"
                        cy="1.75"
                        r="1.75"
                        transform="matrix(1 0 0 -1 1.30078 15.5)"
                        stroke="currentColor"
                      />
                      <circle
                        cx="1.75"
                        cy="1.75"
                        r="1.75"
                        transform="matrix(1 0 0 -1 11.3008 15.5)"
                        stroke="currentColor"
                      />
                      <circle
                        cx="1.75"
                        cy="1.75"
                        r="1.75"
                        transform="matrix(1 0 0 -1 1.30078 5.5)"
                        stroke="currentColor"
                      />
                      <line
                        x1="0.5"
                        y1="-0.5"
                        x2="6.5"
                        y2="-0.5"
                        transform="matrix(3.49691e-08 -1 -1 -3.49691e-08 2.5 12)"
                        stroke="currentColor"
                        stroke-linecap="square"
                      />
                      <path
                        d="M13 11.5C13 10.3571 13 8.5 10.9643 8.5C9.33571 8.5 7.76103 8.5 6.16315 8.5C4.56526 8.5 3.5 7.55714 3.5 5.5"
                        stroke="currentColor"
                        stroke-linecap="round"
                      />
                    </svg>
                    新建业务流程</li
                  >
                </ul>
              </template>
            </n-popover>
          </div>
          <div class="cf-tree-container">
            <n-input
              class="table-tree-ipt"
              v-model="state.treeSearchText"
              placeholder="请输入关键词"
              suffix="search"
              @change="(val) => treeRef.treeRef.filter(val)"
            />
            <LocalTree
              ref="treeRef"
              highlight-current
              :check-on-click-node="true"
              :default-expanded-keys="[]"
              :current-node-key="state.checkTreeId"
              :filter-node-method="filterNode"
              :props="{
                children: 'children',
                label: 'name',
              }"
              :showBtns="true"
              node-key="id"
              @node-dblclick="methods.nodeClick"
              @node-click="methods.nodeClick"
              :data="state.treeList"
              @change="changeTree"
            />
          </div>
        </section>

        <section class="table-container">
          <!-- 标签 -->
          <div v-if="state.tagArr.length > 0" class="tag">
            <div class="tag-box">
              <div
                :class="['tag-node', state.checkTreeId === item.treeId ? 'active' : '']"
                v-for="(item, index) in state.tagArr"
                :key="`${item.treeId}-${item.taskId || ''}-${index}`"
                @click="changeTag(item)"
                @contextmenu.prevent="handleContextMenu($event, index)"
              >
                <img :src="getImageSrc(item.img)" alt="" class="tag-icon" />
                <span
                  :class="[
                    'tag-name',
                    {
                      'status-success': item.runStatus === 'success',
                      'status-fail': item.runStatus === 'fail',
                      'status-running': item.runStatus === 'running',
                    },
                  ]"
                  >{{ item.name }}</span
                >
                <SvgIcon
                  class="icon"
                  icon="icon-offline-close"
                  @click.prevent.stop="delTag(index)"
                />
              </div>
              <div
                class="tagPop"
                :style="'left:' + state.pos.x + 'px;top:' + state.pos.y + 'px;'"
                v-show="visibleTag"
              >
                <div class="tagMenu">
                  <div class="node" @click="handleCommand('closeAll')">关闭全部</div>
                  <div class="node" @click="handleCommand('closeLeft')">关闭左侧</div>
                  <div class="node" @click="handleCommand('closeRight')">关闭右侧</div>
                  <div class="node" @click="handleCommand('closeOther')">关闭其他</div>
                  <div class="node" @click="handleCommand('closeCurrent')">关闭当前</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 渲染所有标签，只保存OW_HIVE_SQL、OW_SPARK_SQL作业类型的状态 -->
          <keep-alive>
            <template
              v-for="(item, index) in state.tagArr"
              :key="`${item.treeId}-${item.taskId || ''}-${index}`"
            >
              <transition
                v-show="
                  state.currentWorkFlowId
                    ? state.currentWorkFlowId === item.treeId
                    : item.taskId === state.taskId
                "
                name="fade"
              >
                <component
                  :currentId="item.treeId"
                  :processId="item.processId"
                  :taskId="item.taskId"
                  :checkTreeId="item.treeId"
                  :is="item.com"
                  :treeList="state.treeList"
                  @openLabelFn="openLabelFn"
                  @changeTree="methods.getTreeList"
                  @changeLoadingStatus="changeLoadingStatus"
                />
              </transition>
            </template>
          </keep-alive>
          <div v-if="state.tagArr.length === 0" class="cf-empty" tip="请在左侧选择作业"> </div>
        </section>
      </section>
    </section>
  </section>
</template>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .container {
    padding: 16px;
    border-radius: 0;

    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      .cf-tree {
        padding: 8px 0;

        .row {
          display: flex;
          align-items: center;
          align-self: stretch;
          justify-content: space-between;
        }

        .title {
          display: flex;
          align-items: center;
          align-self: stretch;
          justify-content: space-between;
          padding: 6px 12px 6px 0px;

          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;
          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 24px;

          &::before {
            width: 4px;
            height: 18px;
            margin-right: 8px;
            background: #1e89ff;
            content: '';
          }
        }

        .cf-tree-container {
          width: 100%;
          height: calc(100% - 36px);
          padding: 0 12px;

          .tree-box {
            height: calc(100% - 40px);
            margin-top: 8px;
          }

          :deep(.nancalui-input__inner) {
            border-right: 1px solid #e5e6eb;
          }

          .tree-box {
            :deep(.nancalui-input__inner) {
              border-right: none;
            }
          }

          :deep(.icon-search)::before {
            display: block;
            width: 16.504px;
            height: 16px;
            cursor: pointer;
            content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNyIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE3IDE2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTYuNzc5MyAwSDAuMjc1MzkxVjE2SDE2Ljc3OTNWMFoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTEyLjEzNjcgMTEuNzkzNUwxNS4yMzEyIDE0Ljc5MzUiIHN0cm9rZT0iIzkwOTM5OSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgPHBhdGggZD0iTTcuNDk1NjIgMTMuMDAxQzEwLjkxMzcgMTMuMDAxIDEzLjY4NDYgMTAuMzE0NyAxMy42ODQ2IDcuMDAwOThDMTMuNjg0NiAzLjY4NzI3IDEwLjkxMzcgMS4wMDA5OCA3LjQ5NTYyIDEuMDAwOThDNC4wNzc1NCAxLjAwMDk4IDEuMzA2NjQgMy42ODcyNyAxLjMwNjY0IDcuMDAwOThDMS4zMDY2NCAxMC4zMTQ3IDQuMDc3NTQgMTMuMDAxIDcuNDk1NjIgMTMuMDAxWiIgc3Ryb2tlPSIjOTA5Mzk5Ii8+Cjwvc3ZnPg==');
          }
        }

        .cf-icon-add {
          display: block;
          width: 16px;
          height: 16px;
          margin-right: 12px;
          cursor: pointer;

          &::before {
            display: block;
            width: 16px;
            height: 16px;
            content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfNzkyXzE1MTg5NykiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzYwNjI2NiIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjNjA2MjY2IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF83OTJfMTUxODk3Ij4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
          }

          &:hover {
            &::before {
              content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfODM2XzE1NjIwMCkiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzAwNkRFQSIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjMDA2REVBIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzAwNkRFQSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF84MzZfMTU2MjAwIj4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
            }
          }
        }
      }

      &-table {
        display: flex;
        flex: 1;
        gap: 10px;
        height: calc(100% - 60px);

        .table-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0 0 2px 2px;

          .tag {
            display: flex;
            flex-shrink: 0;
            align-items: flex-start;
            align-self: stretch;
            box-sizing: border-box;
            width: 100%;
            padding: 8px;
            overflow-x: auto;
            background: #fff;
            border-bottom: 1px solid var(---, #dcdfe6);
            &-box {
              white-space: nowrap;
            }

            &-name {
              min-width: 40px;
              max-width: 180px;
              margin: 0 4px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              vertical-align: middle;

              &.status-success {
                color: #00ca5f;
              }

              &.status-fail {
                color: #f63838;
              }

              &.status-running {
                color: #1e89ff;
              }
            }

            &-node {
              display: inline-flex;
              gap: 4px;
              align-items: center;
              align-self: stretch;
              height: 30px;
              margin-right: 6px;
              padding: 1px 6px 1px 10px;
              border: 1px solid #dcdfe6;
              border-radius: 2px;
              cursor: pointer;
              vertical-align: middle;

              &:hover,
              &.active {
                color: rgba(30, 137, 255, 1);
                background: #ebf4ff;
                border-color: rgba(194, 222, 255, 1);

                .yy-icon {
                  color: rgba(30, 137, 255, 1);
                }
              }

              img {
                width: 16px;
                vertical-align: middle;
              }

              .yy-icon {
                color: #dcdfe6;
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    .tagPop {
      position: fixed;
      top: 160px;
      left: 530px;
      z-index: 9;
      background: #fff;
      border-radius: 2px;
      box-shadow: 0 4px 8px 0 rgba(37, 43, 58, 0.2);

      .node {
        padding: 0 10px;
        font-size: 12px;
        line-height: 28px;
        text-align: center;
        cursor: pointer;

        &:hover {
          color: #1e89ff;
          background: #ebf4ff;
        }
      }
    }
  }
</style>
<style lang="scss">
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;

    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;

      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
