<template>
  <div class="problem">
    <div class="row">
      <div class="switch">
        <div
          :class="state.tabs === 'business' ? 'switch-label checked' : 'switch-label'"
          @click.prevent.stop="checkTabsFn('business')"
          >业务表单</div
        >
        <div
          :class="state.tabs === 'map' ? 'switch-label checked' : 'switch-label'"
          @click.prevent.stop="checkTabsFn('map')"
          >流程数据流图</div
        >
      </div>
    </div>
    <div v-loading="state.loading" v-show="state.tabs === 'business'" class="table-box">
      <CfTable
        :key="state.key"
        :isDisplayAction="false"
        :table-head-titles="state.tableHeadTitles"
        :paginationConfig="{
          total: state.pageInfo.total,
          pageSize: state.pageInfo.pageSize,
          currentPage: state.pageInfo.currentPage,
          onCurrentChange: (v) => {
            state.pageInfo.currentPage = v
            initTable()
          },
          onSizeChange: (v) => {
            state.pageInfo.pageSize = v
            initTable(true)
          },
        }"
        :tableConfig="{
          data: state.tableData.list,
          rowKey: 'id',
        }"
      >
      </CfTable>
    </div>
    <div v-show="state.tabs === 'map'" class="table-box">
      <iframe v-if="state.iframeUrl" class="iframe" :src="state.iframeUrl"></iframe>
      <div v-else class="empty">
        <img class="empty-img" src="@/assets/table-no-content.png" />
        <p class="empty-text">暂无流程数据流图</p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import api from '@/api/index'
  import { workbenchBiztblList, workbenchFlowGraphUrl } from '@/api/dataManage.js'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    key: 1,
    loading: false,
    tabs: 'business',
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'prowkObjName', name: '流程场景输入输出', minWidth: '25%' },
      { prop: 'problemDescription', name: '问题描述', minWidth: '25%' },
      { prop: 'l6TplteModelName', name: '对应L6模板表单名称', minWidth: '25%' },
      { prop: 'belongL3l4CircutName', name: '所属L3/L4末端流程名称', minWidth: '25%' },
      { prop: 'usedL3l4CircutName', name: '后端使用L3/L4末端流程名称', minWidth: '25%' },
    ],
    iframeUrl: null,
  })

  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }

  const checkTabsFn = (type) => {
    state.tabs = type
  }

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchBiztblList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          res.data.list.forEach((item, index) => {
            item.number = index + 1
          })
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  onMounted(() => {
    initTable(true)
    workbenchFlowGraphUrl().then((res) => {
      if (res.success) {
        let url = res.data
        if (url.indexOf('?') !== -1) {
          url += '&name=' + props.info.flowGraphName
        } else {
          url += '?name=' + props.info.flowGraphName
        }
        fetch(url)
          .then((res) => res.json())
          .then((resp) => {
            state.iframeUrl = resp.data
          })
      }
    })
  })
</script>

<style lang="scss" scoped>
  .problem {
    position: relative;
    width: 100%;
    height: 100%;
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .switch {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 10px;
        &-label {
          width: 100px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          box-sizing: border-box;
          border: 1px solid #c5d0ea;
          border-left: none;
          color: #333;
          font-size: 14px;
          cursor: pointer;
          border-radius: 2px;
          &:first-of-type {
            border-left: 1px solid #c5d0ea;
          }
          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
          &.checked + .switch-label {
            border-left: none;
          }
        }
      }
    }

    .table-box {
      position: relative;
      margin-top: 10px;
      height: calc(100% - 52px);
      .iframe {
        width: 100%;
        height: calc(100% - 6px);
        border: none;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: flex-start;
        flex-shrink: 0;
      }
      .empty {
        width: 100%;
        height: calc(100% - 50px);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        &-img {
          display: block;
          width: 140px;
          height: auto;
          margin: 0 auto;
        }

        &-text {
          margin-top: 20px;
          color: #999999;
          font-size: 12px;
          text-align: center;
        }
      }
    }
  }

  :deep(.common-table .page-mid .el-table tbody tr td > .cell > span) {
    text-overflow: inherit;
    -webkit-line-clamp: inherit;
  }
</style>
