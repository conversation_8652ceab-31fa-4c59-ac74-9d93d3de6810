<template>
  <!-- 非数据结构采集 - 数据预览 -->
  <div class="data-collection-data-source-view">
    <el-dialog
      v-model="state.dialogVisible"
      class="data-view-dialog commonDialog"
      title="数据预览"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div>
        <n-public-table
          :isDisplayAction="false"
          :table-head-titles="state.tableHeadTitles"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          :showPagination="false"
          :key="state.key"
        >
          <!-- <template #empty>
          <slot name="empty">
            <div class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/img/table-no-content.png" alt="暂无内容" />
              <div>无可用数据源，请先新增或者发布！</div>
            </div>
          </slot>
        </template> -->
        </n-public-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <n-button
            color="primary"
            variant="solid"
            size="sm"
            @click.prevent="state.dialogVisible = false"
            >取 消</n-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const state = reactive({
        key: 1,
        tableData: {},
        tableHeight: 400,
        dialogVisible: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cName', name: '中文名' },
          { prop: 'eName', name: '英文名' },
          { prop: 'fieldType', name: '字段类型' },
          { prop: 'isRequired', name: '字段长度' },
          { prop: 'description', name: '字符精度' },
        ],
      })
      const dataViewDialog = ref()
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 500
        },
        // 数据预览
        init(data) {
          state.dialogVisible = true
          // 新增序号属性

          if (data.length) {
            // let _tableHeadTitles = [{ prop: 'number', name: '序号', width: 80 }]
            let _tableHeadTitles = []
            Object.keys(data[0]).forEach((key) => {
              _tableHeadTitles.push({
                prop: key,
                name: key,
              })
            })
            state.tableHeadTitles = _tableHeadTitles
            state.tableData = { list: data }
            state.key++
          }
        },
        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
        },
      }
      methods.setTableHeight()

      return {
        state,
        dataViewDialog,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .data-collection-data-source-view {
    :deep(.data-view-dialog) {
      thead th.el-table__cell {
        border: 1px solid #e1e1e1;
        border-bottom: 1px solid #e1e1e1 !important;
        border-left: 0;
        border-right: 0;
        // background-color: #eee !important;
        &:first-of-type {
          border-left: 1px solid #e1e1e1;
        }
        &:last-of-type {
          border-right: 1px solid #e1e1e1 !important;
        }
      }
      tbody td.el-table__cell {
        // border-right: 1px solid #ebebeb;
        &:first-of-type {
          border-left: 1px solid #ebebeb;
        }
        &:last-of-type {
          border-right: 1px solid #ebebeb;
        }
        .el-input__wrapper {
          // box-shadow: 0 0 0 1px #ebebeb, #ebebeb inset;
          box-shadow: 0 0 0 0 #ebebeb inset;
          background-color: transparent;
          &.is-focus {
            box-shadow: 0 0 0 0 #ebebeb inset !important;
            background-color: transparent;
          }
        }
      }
    }
  }
</style>
