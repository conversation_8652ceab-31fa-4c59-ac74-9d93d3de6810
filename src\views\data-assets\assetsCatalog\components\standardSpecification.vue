<template>
  <n-modal
    v-model="showDirDialog"
    title="标准规范"
    width="720px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <div class="form-container">
        <div class="form-item">
          <div class="form-label">标准规范：</div>
          <div class="form-content">
            <div v-if="state.stdSpecDocs.length === 0">--</div>
            <template v-else>
              <div
                v-for="(item, index) in state.stdSpecDocs"
                :key="index"
                class="form-content-item"
                >{{ item.filename }}</div
              >
            </template>
          </div>
        </div>
        <div class="form-item">
          <div class="form-label">质量明细：</div>
          <div class="form-content tableBox">
            <CfTable
              :tableConfig="{
                data: state.tableData.list,
                rowKey: 'qualityRuleId',
              }"
              :table-head-titles="state.tableHeadTitles"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">关 闭</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import { assetQualityDetail } from '@/api/assets'
  const showDirDialog = ref(false)
  const state = reactive({
    tableData: {
      list: [],
    },
    tableHeadTitles: [
      { prop: 'dimensional', name: '六性维度', width: '100px' },
      { prop: 'qualityRuleName', name: '校验规则', width: '200px' },
      { prop: 'qualityInspectionTime', name: '校验时间', width: '172px' },
      { prop: 'qualityScore', name: '质量评分', width: '100px' },
    ],
    stdSpecDocs: [],
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  defineExpose({
    open(data) {
      assetQualityDetail({ id: data.id }).then((res) => {
        if (res.success) {
          state.stdSpecDocs = res.data.stdSpecDocs || []
          state.tableData.list = res.data.sixDimensionalQuality || []
        }
      })
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 16px 0;
    .form-container {
      padding: 0 16px;
      .form-item {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 5px 0;
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        .form-label {
          width: 100px;
          text-align: right;
          margin-right: 16px;
          color: #606266;
        }
        .form-content {
          color: #1d2129;
          width: calc(100% - 116px);
          &.tableBox {
            height: 300px;
          }
        }
      }
    }
  }
</style>
