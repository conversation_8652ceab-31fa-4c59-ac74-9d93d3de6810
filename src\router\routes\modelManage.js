import Layout from '@/layout'

// 功能模块-模型管理
export default [
  {
    path: '/modal',
    component: Layout,
    redirect: '/modal/dataModal',
    name: 'modal',
    code: 'modal',
    meta: {
      title: '数据盘点',
      code: 'modal',
      icon: 'icon-model',
      parentRouterName: 'governanceManage',
    },
    children: [
      {
        path: 'dataCollectionResourceLibrary',
        name: 'dataCollectionResourceLibrary',
        code: 'resourceDirectory',
        redirect: '/modal/dataCollectionResourceLibrary/resourceLibraryList',
        component: () => import('@/views/data-management/resource-library/index.vue'),
        meta: {
          title: '资源目录',
          code: 'resourceDirectory',
          parentRouterName: 'governanceManage',
        },
        children: [
          {
            path: 'resourceLibraryList',
            name: 'resourceLibraryList',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/list.vue'),
            meta: {
              title: '资源目录',
              code: 'resourceDirectory',
              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'office',
            name: 'office',
            hidden: true,
            component: () => import('@/demo/office.vue'),
            meta: {
              title: 'office',
              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'resourceLibrarySee',
            name: 'resourceLibrarySee',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/see.vue'),
            meta: {
              title: '资源目录查看',
              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'addWatermark',
            name: 'addWatermark',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/addWatermark.vue'),
            meta: {
              title: '添加水印',
              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'OCR',
            name: 'OCR',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/OCR.vue'),
            meta: {
              title: '文字识别',
              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'onlineWork',
            name: 'onlineWork',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/onlineWork.vue'),
            meta: {
              title: '在线协作',

              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
          {
            path: 'resourceLibraryDetail',
            name: 'resourceLibraryDetail',
            hidden: true,
            component: () => import('@/views/data-management/resource-library/detail.vue'),
            meta: {
              title: '结构化数据',

              activeMenu: '/modal/dataModal',
              parentRouterName: 'governanceManage',
            },
          },
        ],
      },
    ],
  },
]
