<template>
  <section class="cell">
    <div
      class="table"
      :style="
        'border: ' + borderAttr.out.size + 'px ' + borderAttr.out.style + ' ' + borderAttr.out.color
      "
    >
      <div v-for="(tr, index) in tableData" :key="index" class="table_tr">
        <div
          v-for="(td, ind) in tr.child"
          :key="index + '-' + ind"
          class="table_tr_td"
          :style="
            'border-bottom: ' +
            borderAttr.inside.size +
            'px ' +
            borderAttr.inside.style +
            ' ' +
            borderAttr.inside.color +
            ';border-right: ' +
            borderAttr.inside.size +
            'px ' +
            borderAttr.inside.style +
            ' ' +
            borderAttr.inside.color +
            ';background-color:' +
            cellAttrData.backColor
          "
        >
          <input
            v-model="td.value"
            :class="cellAttrData.weight ? 'input weight' : 'input'"
            :style="
              'font-family:' +
              cellAttrData.family +
              ';font-style:' +
              cellAttrData.style +
              ';font-size:' +
              cellAttrData.size +
              'px;color:' +
              cellAttrData.color +
              ';text-decoration:' +
              (cellAttrData.delChecked
                ? 'line-through'
                : cellAttrData.underlineChecked
                ? 'underline'
                : '') +
              ';text-align:' +
              cellAttrData.textAlign
            "
          />
        </div>
      </div>
    </div>
    <div class="setting">
      <h2>设置表大小</h2>
      <div class="row">
        <label>行大小</label
        ><n-input-number v-model="cellAttrData.row" :min="1" @change="rowAndColFn($event, 'row')" />
      </div>
      <div class="row">
        <label>列大小</label
        ><n-input-number v-model="cellAttrData.col" :min="1" @change="rowAndColFn($event, 'col')" />
      </div>
      <h2>设置已选单元格</h2>
      <div class="row">
        <label>单元格字体</label>
        <n-select
          v-model="cellAttrData.family"
          class="select-short"
          placeholder="请选择字体"
          @value-change="updateFn"
        >
          <n-option
            v-for="item in fontFamilyOptions"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
        <n-select
          v-model="cellAttrData.style"
          class="select-short"
          placeholder="请选择字形"
          @value-change="updateFn"
        >
          <n-option
            v-for="item in fontStyleOptions"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
        <n-select
          v-model="cellAttrData.size"
          class="select-short"
          placeholder="请选择大小"
          @value-change="updateFn"
        >
          <n-option
            v-for="item in fontSizeOptions"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
        <n-color-picker
          v-model="cellAttrData.color"
          :swatches="predefineColors"
          style="width: 40px; height: 40px"
          @change="updateFn"
        />
      </div>
      <div class="row">
        <label>单元格字体线</label>
        <n-checkbox
          v-model="cellAttrData.delChecked"
          class="select-short select-short-checkbox"
          @change="updateFn('delChecked')"
          >删除线</n-checkbox
        >
        <n-checkbox
          v-model="cellAttrData.underlineChecked"
          class="select-short select-short-checkbox"
          @change="updateFn('underlineChecked')"
          >下划线</n-checkbox
        >
        <n-checkbox
          v-model="cellAttrData.weight"
          class="select-short select-short-checkbox"
          @change="updateFn"
          >加粗</n-checkbox
        >
      </div>
      <div class="row">
        <label>单元格背景颜色</label>
        <n-color-picker
          style="width: 40px; height: 40px"
          v-model="cellAttrData.backColor"
          :swatches="predefineColors"
          @change="updateFn"
        />
      </div>
      <div class="row">
        <label>单元格文字排列</label>
        <n-select v-model="cellAttrData.textAlign" placeholder="请选择" @value-change="updateFn">
          <n-option
            v-for="item in textAlignOptions"
            :key="item.value"
            :name="item.label"
            :value="item.value"
          />
        </n-select>
      </div>
    </div>
  </section>
</template>

<script>
  export default {
    props: {
      borderAttr: {
        type: Object,
        default: () => {
          return {
            out: {
              color: '#000',
              style: 'solid',
              size: 1,
            },
            inside: {
              color: '#000',
              style: 'solid',
              size: 1,
            },
          }
        },
      },
      cellAttr: {
        type: Object,
        default: () => {
          return {
            row: 6, // 表格行数
            col: 6, // 表格列数
            family: 'inherit',
            style: 'normal',
            weight: false,
            size: 12,
            textAlign: 'left',
            color: '#000',
            backColor: '#fff',
            delChecked: false,
            underlineChecked: false,
          }
        },
      },
      diagramData: {
        type: Array,
        default: () => {
          return []
        },
      },
    },
    data() {
      return {
        tableData: [],
        cellAttrData: {},
        fontFamilyOptions: [
          {
            value: '微软雅黑',
            label: '微软雅黑',
          },
          {
            value: 'Songti SC',
            label: '宋体',
          },
          {
            value: 'Sana',
            label: 'Sana',
          },
          {
            value: 'Arial',
            label: 'Arial',
          },
          {
            value: 'inherit',
            label: '继承',
          },
        ], // 字体样式数组
        fontStyleOptions: [
          {
            value: 'normal',
            label: '常规',
          },
          {
            value: 'italic',
            label: '斜体',
          },
        ], // 字体形态数组
        fontSizeOptions: [
          {
            value: '12',
            label: '12',
          },
          {
            value: '14',
            label: '14',
          },
          {
            value: '16',
            label: '16',
          },
          {
            value: '18',
            label: '18',
          },
          {
            value: '20',
            label: '20',
          },
          {
            value: '22',
            label: '22',
          },
          {
            value: '24',
            label: '24',
          },
          {
            value: '26',
            label: '26',
          },
          {
            value: '28',
            label: '28',
          },
          {
            value: '30',
            label: '30',
          },
          {
            value: '32',
            label: '32',
          },
          {
            value: '36',
            label: '36',
          },
          {
            value: '48',
            label: '48',
          },
          {
            value: '72',
            label: '72',
          },
        ], // 字体大小数组
        textAlignOptions: [
          {
            value: 'left',
            label: '左对齐',
          },
          {
            value: 'center',
            label: '居中对齐',
          },
          {
            value: 'right',
            label: '右对齐',
          },
        ], // 字体对齐方式数组
        predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          'rgba(255, 69, 0, 0.68)',
          'rgb(255, 120, 0)',
          'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577',
        ], // 颜色选择器
      }
    },
    watch: {
      tableData: {
        handler() {
          this.$emit('diagramDataFn', this.tableData)
        },
        deep: true,
      },
    },
    mounted() {
      this.cellAttrData = JSON.parse(JSON.stringify(this.cellAttr))
      this.tableData = JSON.parse(JSON.stringify(this.diagramData))
    },
    methods: {
      // 单元格行列变化控制
      rowAndColFn(num, name) {
        if (num > 0) {
          if (name === 'row') {
            if (num <= this.tableData.length) {
              this.tableData = this.tableData.splice(0, num)
            } else {
              for (let i = 0, len = num - this.tableData.length; i < len; i++) {
                this.tableData.push({
                  child: [],
                })
                for (let j = 0, leng = this.tableData[0].child.length; j < leng; j++) {
                  this.tableData[this.tableData.length - 1].child.push({
                    value: '',
                  })
                }
              }
            }
          } else if (name === 'col') {
            if (num <= this.tableData[0].child.length) {
              this.tableData.forEach((val) => {
                val.child = val.child.splice(0, num)
              })
            } else {
              let len = num - this.tableData[0].child.length
              this.tableData.forEach((val) => {
                for (let i = 0; i < len; i++) {
                  val.child.push({
                    value: '',
                  })
                }
              })
            }
          }
        } else {
          this.tableData.splice(0, 1)
        }
        this.updateFn()
      },
      // 操作后更新数据
      updateFn(name) {
        if (name === 'delChecked') {
          if (this.cellAttrData.delChecked) {
            this.cellAttrData.underlineChecked = false
          }
        } else if (name === 'underlineChecked') {
          if (this.cellAttrData.underlineChecked) {
            this.cellAttrData.delChecked = false
          }
        }
        this.$emit('cellAttrFn', this.cellAttrData)
        this.$emit('diagramDataFn', this.tableData)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .cell {
    padding: 20px;
    .table {
      border: 1px solid #000;
      width: 100%;
      max-height: 242px;
      overflow: auto;
      box-sizing: border-box;
      &_tr {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        &_td {
          flex-shrink: 0;
          flex: 1;
          min-width: 50px;
          height: 40px;
          line-height: 40px;
          border-right: 1px solid #000;
          border-bottom: 1px solid #000;
          box-sizing: border-box;
          .input {
            width: 100%;
            height: 30px;
            line-height: 30px;
            border: none;
            outline: none;
            color: #000;
            font-size: 12px;
            background-color: inherit;
            font-weight: normal;
          }
          .weight {
            font-weight: bold;
          }
        }
        :deep(.table_tr_td:last-of-type) {
          border-right: none !important;
        }
      }
      &_tr:last-of-type {
        :deep(.table_tr_td) {
          border-bottom: none !important;
        }
      }
    }
    .setting {
      border: 1px solid #000;
      margin-top: 30px;
      padding: 20px;
      h2 {
        font-size: 18px;
        font-weight: bold;
      }
      .row {
        padding: 4px 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        label {
          font-size: 14px;
          color: #999;
          width: 130px;
        }
        .select-short {
          width: 120px;
          margin-right: 20px;
        }
        .select-short-checkbox {
          width: 80px;
        }
      }
    }
  }
</style>
