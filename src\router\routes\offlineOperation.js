import Layout from '@/layout'

// 功能模块-首页
export default [
  {
    path: '/offlineOperation',
    component: Layout,
    redirect: '/offlineOperation/operationFlow',
    name: 'offlineOperation',
    code: 'offlineOperation',
    meta: {
      title: '离线运维',
      icon: 'icon-quality',
      parentRouterName: 'assetsManage',
      code: 'offlineOperation',
    },
    children: [
      // 业务流程作业
      {
        path: 'operationFlow',
        name: 'operationFlow',
        code: 'operationFlow',
        hidden: true,
        redirect: '/offlineOperation/operationFlow/flowList',
        meta: { title: '业务流程作业', parentRouterName: 'assetsManage', code: 'offlineOperation' },
        component: () => import('@/views/data-management/offline-operation/index'),
        children: [
          {
            path: 'flowList',
            name: 'operatingList',
            hidden: true,
            component: () => import('@/views/data-management/offline-operation/flowList'),
            meta: {
              title: '业务流程监控',
              parentRouterName: 'assetsManage',
              keepAlive: true,
              code: 'operationFlow',
            },
          },
          {
            path: 'dagDetail',
            name: 'dagDetail',
            hidden: true,
            component: () => import('@/views/data-management/offline-operation/dagDetail'),
            meta: { title: '业务流程DAG详情', parentRouterName: 'assetsManage' },
          },
        ],
      },
      // 离线作业
      {
        path: 'offlineWork',
        name: 'offlineWork',
        code: 'offlineWork',
        hidden: true,
        redirect: '/offlineOperation/offlineWork/list',
        meta: { title: '离线作业', parentRouterName: 'assetsManage', code: 'offlineOperation' },
        component: () => import('@/views/data-management/offline-operation/index'),
        children: [
          {
            path: 'List',
            name: 'offlineWorkList',
            hidden: true,

            component: () => import('@/views/data-management/offline-operation/offlineWork/list'),
            meta: { title: '离线监控', code: 'offlineWork', parentRouterName: 'assetsManage' },
          },
          {
            path: 'detail',
            name: 'offlineWorkDetail',
            hidden: true,
            component: () => import('@/views/data-management/offline-operation/offlineWork/detail'),
            meta: { title: '离线作业实例详情', parentRouterName: 'assetsManage' },
          },
        ],
      },
      // 离线实例
      {
        path: 'offlineExample',
        name: 'offlineExample',
        code: 'offlineExample',
        hidden: true,
        redirect: '/offlineOperation/offlineExample/list',
        meta: { title: '离线实例', parentRouterName: 'assetsManage', code: 'offlineOperation' },
        component: () => import('@/views/data-management/offline-operation/index'),
        children: [
          {
            path: 'List',
            name: 'offlineExampleList',
            hidden: true,

            component: () =>
              import('@/views/data-management/offline-operation/offlineExample/list'),
            meta: {
              title: '离线实例',
              code: 'offlineExample',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
      // 补数据实例
      {
        path: 'supplementData',
        name: 'supplementData',
        code: 'supplementData',
        hidden: true,
        redirect: '/offlineOperation/supplementData/List',
        meta: { title: '补数据实例', parentRouterName: 'assetsManage', code: 'offlineOperation' },
        component: () => import('@/views/data-management/offline-operation/index'),
        children: [
          {
            path: 'List',
            name: 'supplementDataList',
            hidden: true,

            component: () =>
              import('@/views/data-management/offline-operation/supplementData/list'),
            meta: {
              title: '补数据实例',
              code: 'supplementData',
              parentRouterName: 'assetsManage',
              keepAlive: true,
            },
          },
        ],
      },
      // 表管理
      {
        path: 'tableManage',
        name: 'tableManage',
        code: 'tableManage',
        hidden: true,
        redirect: '/offlineOperation/tableManage/List',
        meta: { title: '表管理', parentRouterName: 'assetsManage', code: 'tableManage' },
        component: () => import('@/views/data-management/offline-operation/index'),
        children: [
          {
            path: 'List',
            name: 'tableManageList',
            hidden: true,

            component: () => import('@/views/data-management/offline-operation/tableManage/list'),
            meta: {
              title: '资源库管理',
              code: 'tableManage',
              parentRouterName: 'assetsManage',
              keepAlive: true,
            },
          },
        ],
      },
      // 表授权管理
      {
        path: 'tableAuthManage',
        name: 'tableAuthManage',
        code: 'tableAuthManage',
        hidden: true,
        redirect: '/offlineOperation/tableAuthManage/List',
        meta: { title: '表授权管理', parentRouterName: 'assetsManage', code: 'tableAuthManage' },
        children: [
          {
            path: 'List',
            name: 'tableAuthManageList',
            hidden: true,
            component: () =>
              import('@/views/data-management/offline-operation/tableAuthManage/list'),
            meta: {
              title: '表管理',
              code: 'tableAuthManage',
              parentRouterName: 'assetsManage',
              keepAlive: true,
            },
          },
        ],
      },
      // 即席查询
      {
        path: 'adHocQuery',
        name: 'adHocQuery',
        code: 'adHocQuery',
        hidden: true,
        redirect: '/offlineOperation/adHocQuery/list',
        meta: { title: '即席查询', parentRouterName: 'assetsManage', code: 'offlineOperation' },
        component: () => import('@/views/data-management/index'),
        children: [
          {
            path: 'list',
            name: 'adHocQueryList',
            hidden: true,
            component: () => import('@/views/data-management/adHocQuery/list'),
            meta: { title: '即席查询', code: 'adHocQuery', parentRouterName: 'assetsManage' },
          },
        ],
      },
    ],
  },
]
