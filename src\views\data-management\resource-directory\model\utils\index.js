import {
  modTypeList,
  dataTypeCanLen,
  dataTypeCanScale,
  dataTypeNameCanLen,
  dataTypeNameCanScale,
  dataTypeName,
} from './config'
import { setLineArrows, setLineAttr } from '@/utils/graph'
import { substrLen, randomGuid } from '@/utils/index.js'
import { DagreLayout } from '@antv/layout'
import { cloneDeep } from 'lodash'
import G6 from '@antv/g6'
import FlowGraph from '../graph'

const arrowPath = G6.Arrow.triangle(6, 6, 0) // 这是爪形
const arrowPathV = G6.Arrow.triangleRect(6, 6, 6, 2, 5, 0) // 这是爪形带竖线的
const arrowPathVOnly = G6.Arrow.rect(6, 2, 1) // 这是十字的
const lineArrowType = {
  1: { startArrow: null, endArrow: { path: arrowPath, fill: '#CCD0D8' } }, //1:M Optional
  2: { startArrow: null, endArrow: { path: arrowPath, fill: '#CCD0D8' } }, //1:M Mandatory
  3: { startArrow: null, endArrow: { path: arrowPathV, fill: '#CCD0D8' } }, //1:M Indentify
  4: {
    startArrow: { path: arrowPath, fill: '#CCD0D8' },
    endArrow: { path: arrowPath, fill: '#CCD0D8' },
  }, //M:M
  5: { startArrow: null, endArrow: null }, // 1:1 Mandatory
  6: { startArrow: null, endArrow: { path: arrowPathVOnly, fill: '#CCD0D8' } }, //1:1 Indentify
}

function setListToCells(cells, localEntityList, localRelationList, localTabList) {
  const newCells = cloneDeep(cells)
  if (localEntityList?.length) {
    newCells?.forEach((cell) => {
      const sameEntity = localEntityList?.find((entity) => cell.id === entity.guid)
      if (sameEntity) {
        cell.data.attr = sameEntity?.fieldList?.map((item) => {
          const curArr = cell.data?.attr?.find((r) => r.id === item.guid)
          return {
            ...item,
            standard: String(item.standard?.code),
            fromParentGuid: curArr?.fromParentGuid,
            charUsed: String(item?.charUsed?.code),
          }
        })
        delete sameEntity?.fieldList
        cell.data.entity = {
          ...sameEntity,
          modType: String(sameEntity.modType?.code || ''),
          state: String(sameEntity.state?.code || ''),
          standard: String(sameEntity.standard?.code || ''),
          stage: String(sameEntity.stage?.code || ''),
          occurrenceCycle: String(sameEntity.occurrenceCycle?.code || ''),
        }
      }
    })
  } else if (localTabList?.length) {
    newCells?.forEach((cell) => {
      const sameTable = localTabList?.find((table) => cell.id === table.guid)
      if (sameTable) {
        cell.data.col = sameTable?.columnList?.map((item) => {
          const curCol = cell.data?.col?.find((r) => r.id === item.guid)
          return {
            ...item,
            standard: String(item.standard?.code),
            guid: item.guid ? item.guid : `${new Date().getTime()}${Math.random()}`,
            fromParentGuid: curCol?.fromParentGuid,
            charUsed: String(item?.charUsed?.code),
          }
        })
        delete sameTable?.columnList
        cell.data.table = {
          ...sameTable,
          charUsed: String(sameTable?.charUsed?.code),
          standard: String(sameTable?.standard?.code),
          stage: String(sameTable?.stage?.code),
        }
      }
    })
  }
  if (localRelationList?.length) {
    newCells?.forEach((cell) => {
      const sameRela = localRelationList?.find((rela) => cell.id === rela.guid)
      if (sameRela) {
        cell.data = { ...sameRela, lineType: String(sameRela?.lineType?.code) }
      }
    })
  }
  return newCells
}

// 计算新的节点信息。isView：是否查看，v2以上版本直接渲染json
export function calculationCells(currentGraph, modId, modGuid, modType, kind, isView) {
  try {
    const {
      localEntityList,
      localRelationList,
      localTabList,
      entityList,
      entityMappingList,
      tabList,
      tabMappingList,
      relationList,
      relationMappingList,
      subjectGuid,
    } = currentGraph
    let mappingEntityList = []
    const json = currentGraph.json ? JSON.parse(currentGraph.json) : []
    const cells = []
    currentGraph.entity = []
    currentGraph.table = []
    currentGraph.relation = []
    json?.forEach((item) => {
      if (item.shape === 'edge') {
        if (item.source.port.includes('-') || item.target.port.includes('-')) {
          delete item.vertices
          item.source.port = item.source.port.split('-')[0]
          item.target.port = item.target.port.split('-')[0]
        }
        let data = item.data
        const relation = relationList?.find((relation) => relation.originGuid === item.id)
        if (relation) {
          item.id = relation.guid
          data = { ...data, ...relation }
          item.source.cell = relation.parentEntityId
          item.target.cell = relation.subEntityId
        }
        // 映射的为新增
        if (!relation) {
          currentGraph.relation.push(item.id)
        } else {
          currentGraph.isSave = true
        }
        data = { ...data, modId: modId, modGuid: modGuid, modType: modType }
        const mapRelation = relationMappingList?.find((rela) => rela.guid === item.id)
        if (mapRelation) {
          item.labels = mapRelation.name ? [mapRelation.name] : null
          data = { ...mapRelation, lineType: String(mapRelation?.lineType?.code) }
          data.isQuote = true
          data.disableMove = true
          mappingEntityList.push({
            guid: mapRelation.guid,
            subjectGuid: mapRelation.subjectGuid,
            subjectId: mapRelation.subjectId,
            modId: mapRelation.modId,
            type: 3, //类型：1实体, 2表, 3关系
          })
        }
        const localRelation = localRelationList?.find((rela) => rela.guid === item.id)
        if (localRelation || mapRelation || relation || isView) {
          cells.push({
            ...item,
            labels: item?.labels?.length > 0 ? item.labels : null,
            data: data,
          })
        }
      } else if (['nc-table', 'nc-entity'].includes(item.shape)) {
        if (item.ports) {
          item.ports.items = item.ports?.items?.filter((p) => !p.id.includes('-'))
        }
        let isNeed = false
        let data = item.data
        if (modType === '3') {
          const table = tabList?.find((table) => table.originGuid === item.id)
          if (item.data?.entity?.type === 'pseudo') return
          if (table) {
            item.id = table.guid
            data.guid = table.guid
            data.subjectGuid = subjectGuid
            // 删除不必要参数
            const surplusField = [
              'dataProcessingForm',
              'name',
              'synonymous',
              'alternateName',
              'classify',
              'abbreviation',
              'state',
              'occurrenceCycle',
              'monthlyOccurrence',
              'shelfLife',
              'totalQuantity',
            ]
            surplusField.forEach((field) => delete data.entity[field])
            data.table = {
              ...data.entity,
              id: table.id,
              mappingId: table.mappingId,
              mappingName: table.mappingName,
              charUsed: '1',
              dataProcesForm: '',
              guid: data.guid,
              kind: kind,
              modId: modId,
              modGuid: modGuid,
              name: table.name,
              subject: subjectGuid,
            }
            delete data.entity
            let arr = new Map([...dataTypeName[kind]])
            const tsurplusField = [
              'standardCol',
              'entityId',
              'entityGuid',
              'alternateName',
              'synonymous',
              'core',
              'essential',
              'candidate',
              'sync',
              'nonInheritance',
              'Inclusive',
              'field',
              'reverse',
              'encryption',
            ]
            const col = data.attr?.map((cl) => {
              cl.dataType = arr.get(cl.dataType)
              // 删除多余属性
              tsurplusField.forEach((field) => delete cl[field])
              return {
                ...cl,
                charUsed: '1',
                pk: cl.substantiveKey,
                tabGuid: table.guid,
                tabId: table.id,
                guid: cl.guid ? cl.guid : `${new Date().getTime()}${Math.random()}`, //兼容guid为空的情况
              }
            })
            delete data.attr
            data = { ...data, guid: table.guid, col: col }
          }
          const mapTable = tabMappingList?.find(
            (tab) => tab.guid === item.id || tab.guid === table?.originGuid,
          )
          if (mapTable) {
            data.col = mapTable.columnList.map((i) => {
              return {
                ...i,
                charUsed: String(i?.charUsed?.code),
                standard: String(i?.standard?.code),
                guid: i.guid ? i.guid : `${new Date().getTime()}${Math.random()}`, //兼容guid为空的情况
              }
            })
            data.demoList = mapTable.demoList
            data.indexColumnInfoDOList = mapTable.indexColumnInfoDOList
            delete mapTable.columnList
            delete mapTable.demoList
            delete mapTable.indexColumnInfoDOList
            const { standard, charUsed, stage, subjectGuid, subjectId } = mapTable
            data.table = {
              ...mapTable,
              charUsed: charUsed?.code,
              standard: standard?.code,
              stage: stage?.code,
              subject: subjectGuid,
            }
            mappingEntityList.push({
              guid: data.guid,
              subjectGuid: subjectGuid,
              subjectId: subjectId,
              modId: mapTable?.modId,
              type: 2, //类型：1实体, 2表, 3关系
            })
          }
          // 映射的为新增
          if (!table) {
            currentGraph.table.push(item?.id)
          } else {
            currentGraph.isSave = true
          }
          const localTable = localTabList?.find((table) => item.id === table.guid)
          if (table || mapTable || localTable) {
            isNeed = true
          }
        } else {
          const entity = entityList?.find((entity) => entity.originGuid === item.id)
          if (entity) {
            item.id = entity.guid
            data.guid = entity.guid
            data.subjectGuid = subjectGuid
            data.attr = data.attr?.map((at) => {
              return {
                ...at,
                entityGuid: entity.guid,
              }
            })
            data.entity = {
              ...data.entity,
              id: entity.id,
              mappingId: entity.mappingId,
              mappingName: entity.mappingName,
              modId: modId,
              modGuid: modGuid,
              modType: modType,
              subject: subjectGuid,
            }
            data = { ...data, guid: entity.guid }
          }
          const mapEntity = entityMappingList?.find(
            (map) => map.guid === item.id || map.guid === entity?.originGuid,
          )
          if (mapEntity) {
            data.attr = mapEntity.fieldList?.map((item) => {
              return {
                ...item,
                standard: item.standard?.code,
              }
            })
            delete mapEntity.fieldList
            const {
              modType,
              occurrenceCycle,
              standard,
              stage,
              state,
              sharedEntity,
              subjectGuid,
              subjectId,
            } = mapEntity
            data.entity = {
              ...mapEntity,
              modType: String(modType?.code),
              occurrenceCycle: String(occurrenceCycle?.code),
              standard: String(standard?.code),
              stage: String(stage?.code),
              state: String(state?.code),
              guid: data.guid,
              sharedReference: sharedEntity,
              subject: subjectGuid,
            }
            mappingEntityList.push({
              guid: data.guid,
              subjectGuid: subjectGuid,
              subjectId: subjectId,
              modId: mapEntity?.modId,
              type: 1, //类型：1实体, 2表, 3关系
            })
          }
          // 映射的为新增
          if (!entity) {
            currentGraph.entity.push(item.id)
          } else {
            currentGraph.isSave = true
          }
          const localEntity = localEntityList?.find((entity) => item.id === entity.guid)
          if (entity || mapEntity || localEntity) {
            isNeed = true
          }
        }
        if (isNeed || isView) {
          cells.push({
            id: item?.id,
            width: item.size?.width || item.width,
            height: item.size?.height || item.height,
            attrs: item.attrs,
            shape: modType === '3' ? 'nc-table' : item.shape,
            data: data,
            x: item.x || item.position?.x,
            y: item.y || item.position?.y,
          })
        }
        // 添加输入框、气球文字、表图
        if (['nc-input-component', 'nc-pop-input', 'nc-diagram'].includes(item.shape)) {
          cells.push(item)
        }
      } else {
        // 添加输入框、气球文字、表图
        cells.push(item)
      }
    })
    // 删除不必要属性
    const extraFields = [
      'entityList',
      'entityMappingList',
      'relationList',
      'relationMappingList',
      'tabList',
      'tabMappingList',
    ]
    extraFields.forEach((key) => {
      delete currentGraph[key]
    })
    const newCells = setListToCells(cells, localEntityList, localRelationList, localTabList)
    currentGraph.json = newCells
    currentGraph.mappingEntityList = mappingEntityList
    return currentGraph
  } catch (e) {
    console.log(e)
  }
}
// 画布切换时前端引用实体计算
export function calMapCells(currentGraph, graphList, modId) {
  const { subjectGuid, mappingEntityList, json } = currentGraph
  //  把除当前画布的所有节点集合
  const allCells = graphList
    ?.filter((item) => item?.subjectGuid !== subjectGuid)
    ?.reduce((pre, current) => pre.concat(current.json), [])
  let newJson = json
  if (!(newJson instanceof Array)) {
    newJson = JSON.parse(newJson)
  }
  newJson = newJson?.map((cell) => {
    let data = cell.data
    let labels = null
    // 当前画布上引用的实体
    const map = mappingEntityList?.find(
      (map) => map.guid === cell.id && String(map.modId) === String(modId),
    )
    if (map) {
      // 去查找其他画布上真实的节点数据
      const originCell = allCells?.find((item) => item?.id === cell?.id && !item.data?.isQuote)
      data = { ...originCell?.data, isQuote: true }
      labels = originCell?.labels
      // 重新计算引用边的样式
      if (cell.shape === 'edge') {
        const { lineType, parentMust, subMust, keyUse } = data
        //1.实线，2.虚线 3.虚-实，4.实-虚
        let type = 1
        if (parentMust === 'false' && subMust === 'false') {
          type = 2
        } else if (parentMust === 'false' && subMust === 'true') {
          type = 3
        } else if (parentMust === 'true' && subMust === 'false') {
          type = 4
        }
        const { attrs, markup } = setLineAttr(type, lineType, keyUse, data)
        cell.attrs = attrs
        cell.markup = markup
      }
    }
    return {
      ...cell,
      data: data,
      labels: labels,
    }
  })
  return newJson
}
// 总图节点计算
export function summaryCells(graphList, dontNeedV) {
  let multObj = {
    len: 0,
    xMax: 0,
    yMax: 0,
    row: 0,
  }
  let cells = []
  graphList?.forEach((graph) => {
    let json = []
    if (graph.json instanceof Array) {
      json = graph.json || []
    } else {
      json = JSON.parse(graph.json) || []
    }
    let minX = 0
    let maxX = 0
    let minY = 0
    let maxY = 0
    json?.forEach((item) => {
      const width = item.width || item.size?.width
      const height = item.height || item.size?.height
      const x = item.x || item.position?.x || 0
      const y = item.y || item.position?.y || 0
      // 边
      if (item.shape === 'edge') {
        cells.push({
          ...item,
          vertices: dontNeedV
            ? null
            : item?.vertices?.map((ve) => {
                return {
                  x: ve.x + multObj.xMax,
                  y: ve.y + (multObj.row ? multObj.yMax : 0),
                }
              }),
          labels: item?.labels?.length > 0 ? item.labels : null,
          data: { ...item.data, disableMove: true },
        })
        //实体
      } else if (item.shape === 'nc-entity') {
        cells.push({
          id: item.id,
          width: width,
          height: height,
          attrs: item.attrs,
          shape: item.shape,
          data: { ...item.data, disableMove: true },
          x: x + multObj.xMax,
          y: y + (multObj.row ? multObj.yMax : 0),
        })
        // 表
      } else if (item.shape === 'nc-table') {
        cells.push({
          id: item.id,
          width: width,
          height: height,
          attrs: item.attrs,
          shape: item.shape,
          data: { ...item.data, disableMove: true },
          x: x + multObj.xMax,
          y: y + (multObj.row ? multObj.yMax : 0),
        })
      } else {
        cells.push({
          id: item.id,
          width: width,
          height: height,
          attrs: item.attrs,
          shape: item.shape,
          data: { ...item.data, disableMove: true },
          x: x + multObj.xMax,
          y: y + (multObj.row ? multObj.yMax : 0),
        })
      }
      if (item.shape !== 'edge') {
        minX = Math.min(x, minX)
        maxX = Math.max(x + width, maxX)
        minY = Math.min(y, minY)
        maxY = Math.max(y + height, maxY)
      }
    })
    let xMax = Math.max(maxX - minX + multObj.xMax + 40, multObj.xMax)
    const yMax = Math.max(maxY - minY + multObj.yMax + 40, multObj.yMax)
    if (xMax > 1000) {
      xMax = 0
      multObj.row++
    }
    multObj.xMax = xMax
    multObj.yMax = yMax
    multObj.len++
  })
  return cells
}

// 总图节点计算
export function summaryCellsTotal(graphList, shape, width, height) {
  let data = { nodes: [], edges: [] }
  let domainList = []
  let domain = 0
  graphList?.forEach((item) => {
    const { entityList, relationList } = item
    relationList?.forEach((r) => {
      let id = r.guid
      // 有不同边的id相同时，重新设置id
      if (
        data.edges?.find(
          (i) => i.id === item.id && (i.source !== item.source || i.target !== item.target),
        )
      ) {
        id = randomGuid()
      }
      const source = entityList?.find((e) => e.guid === r.parentEntityId)
      const target = entityList?.find((e) => e.guid === r.subEntityId)
      if (source && target) {
        data.edges.push({
          id: id,
          lineType: r.lineType,
          source: r.parentEntityId,
          target: r.subEntityId,
          label: r.name,
          style: {
            ...lineArrowType[r.lineType],
          },
        })
      }
    })
    const hasLineEntity = entityList.find((e) =>
      data?.edges?.find((d) => e.guid === d.target || e.guid === d.source),
    )
    if (hasLineEntity) {
      domain++
      domainList.push(domain)
    }
    entityList?.forEach((e) => {
      const name = (shape === 'nc-entity' ? e.name : e.tabName) || ''
      const tabName = (shape === 'nc-entity' ? e.tabName : e.name) || ''
      const hasEdge = data?.edges?.find((d) => e.guid === d.target || e.guid === d.source)
      const nums = entityList.length
      data.nodes.push({
        domain: hasEdge ? domain : undefined,
        nums: nums,
        label: substrLen(name, 4),
        des: name,
        tabName: tabName,
        id: e.guid ? e.guid : randomGuid(),
        type: 'dice-er-box',
        modId: e.modId,
        size: 60,
        hasEdge: hasEdge,
        style: hasEdge
          ? {
              stroke: '#447DFD',
              strokeWidth: 1,
              fill: '#A9C9FF',
            }
          : null,
      })
    })
  })
  let layout = {
    preset: {
      type: 'fruchterman',
      gpuEnabled: true,
      gravity: 1,
      width: 1000,
      height: 1000,
    },
    type: 'force2',
    linkDistance: 50,
    maxSpeed: 2000,
    damping: 0.3,
    interval: 0.05,
    minMovement: 5,
    getCenter: (d, degree) => {
      if (degree === 0) return [width / 2 + 200, height / 2 + 200, -1]
      return [
        width / 2 + Math.sin(2 * Math.PI * (d.domain / domainList.length)) * 200,
        height / 2 + Math.cos(2 * Math.PI * (d.domain / domainList.length)) * 200,
        100,
      ]
    },
  }
  const time = new Date()
  const subgraphLayout = new G6.Layout[layout.type]({
    ...layout,
    width,
    height,
  })
  // 初始化布局，灌入子图数据
  subgraphLayout.init(data)
  // 执行布局
  subgraphLayout.execute()
  console.log(new Date() - time)
  return data
}
// 接口返回节点数据转换
export function dataToCell(data, type) {
  const cellData = {
    guid: data.guid,
    subjectName: data.subjectName,
    type: 'BLOOD',
  }
  if (type === 'nc-entity') {
    cellData.attr = data.fieldList?.map((item) => {
      return {
        ...item,
        standard: item.standard?.code,
      }
    })
    delete data.fieldList
    const { modType, occurrenceCycle, standard, stage, state, sharedEntity, subjectGuid } = data
    cellData.entity = {
      ...data,
      modType: String(modType.code),
      occurrenceCycle: String(occurrenceCycle.code),
      standard: String(standard.code),
      stage: String(stage.code),
      state: String(state.code),
      guid: data.guid,
      sharedReference: sharedEntity,
      subject: subjectGuid,
    }
  } else if (type === 'nc-table') {
    cellData.col = data.columnList
    cellData.demoList = data.demoList
    cellData.indexColumnInfoDOList = data.indexColumnInfoDOList
    delete data.columnList
    delete data.demoList
    delete data.indexColumnInfoDOList
    const { standard, stage, subjectGuid } = data
    cellData.table = {
      ...data,
      standard: standard?.code,
      stage: stage?.code,
      subject: subjectGuid,
    }
  }
  return cellData
}

export function splicingData(list) {
  const modIdList = []
  const modeList = []
  list?.forEach((graphList) => {
    const cells = JSON.parse(graphList)
    cells?.forEach((cell) => {
      let cellModId = ''
      if (cell.shape === 'edge') {
        cellModId = String(cell.data.modId)
      } else if (cell.shape === 'nc-entity') {
        cellModId = String(cell?.data?.entity.modId)
      } else if (cell.shape === 'nc-table') {
        cellModId = String(cell?.data?.table.modId)
      }
      const index = modIdList.indexOf(cellModId)
      if (index === -1) {
        modIdList.push(cellModId)
        modeList.push([{ ...cell }])
      } else {
        modeList[index].push({ ...cell })
      }
    })
  })
  return modeList
}

// 选择引用实体后节点计算
export function mappingCells(data, currentGraph, graphList, graphCells, mapLocation) {
  // data 引用的实体  currentGraph 引用实体进来的主题域画布对象 graphList所有的主题域画布对象 graphCell引用实体进来画布上的实体信息
  const map = new Map()
  data?.forEach((item) => {
    if (map.has(item.subjectGuid)) {
      const value = map.get(item.subjectGuid)
      value.push(item.guid)
      map.set(item.subjectGuid, value)
    } else {
      map.set(item.subjectGuid, [item.guid])
    }
  })
  let mappingCells = []
  map?.forEach((value, key) => {
    // 找到引用实体原本的主题域里面的节点信息
    const cells = graphList?.find((item) => key === item.subjectGuid)?.json
    // 被引用的实体设置原生标记
    cells?.forEach((cell) => {
      if (value?.includes(cell.id)) {
        cell.data.isOrigin = true
      }
    })
    // 筛选出符合条件的节点，和节点对应的线
    const selectedCells = cells?.filter((cell) => {
      return (
        value?.includes(cell.id) ||
        (value?.includes(cell?.source?.cell) && value?.includes(cell?.target?.cell))
      )
    })
    mappingCells = mappingCells.concat(selectedCells)
  })
  const nodes = graphCells.filter((cell) => cell.shape !== 'edge')
  let maxX = 0
  let maxY = 0
  // 是否是右键引用实体，带坐标
  if (mapLocation) {
    maxX = mapLocation.x
    maxY = mapLocation.y
  } else {
    nodes?.forEach((item) => {
      maxX = Math.max((item.position?.x || item.x) + (item.size?.width || item.width) || 0, maxX)
      maxY = Math.max((item.position?.y || item.y) + (item.size?.height || item.height) || 0, maxY)
    })
  }
  const mappingEntityList = currentGraph.mappingEntityList || []
  // 判断重名，设置不可编辑属性
  const newCells = mappingCells.map((cell, index) => {
    let newCell
    const data = cell.data
    if (cell.shape === 'edge') {
      newCell = {
        ...cell,
        data: { ...data, disableMove: true, isQuote: true },
      }
      const target = mappingCells?.find((map) => map.id === cell?.target?.cell && !map.data.isQuote)
      if (mappingEntityList.every((m) => m.guid !== cell.id)) {
        mappingEntityList.push({
          guid: cell.id,
          subjectGuid: data.subjectGuid || target.data.subjectGuid,
          subjectId: data.subjectId || target.data.subjectId,
          modId: data?.modId || target.data.modId,
          type: 3, //类型：1实体, 2表, 3关系
        })
      }
    } else {
      newCell = {
        ...cell,
        data: { ...data, isQuote: true },
        x: maxX + 350 * (index % 5),
        y: maxY + 250 * Math.floor(index / 5),
      }
      if (mappingEntityList.every((m) => m.guid !== cell.id)) {
        mappingEntityList.push({
          guid: cell.id,
          subjectGuid: data.subjectGuid,
          subjectId: data.subjectId,
          modId: data.entity?.modId || data.table?.modId,
          type: cell.shape === 'nc-entity' ? 1 : 2, //类型：1实体, 2表, 3关系
        })
      }
    }
    return newCell
  })
  return { newCells: newCells, mappingEntityList: mappingEntityList }
}
// 同步线和父子节点数据
export function syncCells(currentEdge, graphList, modType, subjectId, isRemove) {
  const { graph } = FlowGraph
  graphList.forEach((item) => {
    const { parentEntityId, subEntityId } = currentEdge.data
    // 判断父子实体是否是引用
    const parentNode = item.json?.find(
      (cell) => cell.id === parentEntityId && cell.shape !== 'edge',
    )
    const subNode = item.json?.find((cell) => cell.id === subEntityId && cell.shape !== 'edge')
    let isQuote = false
    const data = currentEdge.data
    if (parentNode?.data?.isQuote && subNode?.data?.isQuote) {
      isQuote = true
      data.isQuote = true
      data.subjectGuid = parentNode.data?.entity?.subjectGuid || parentNode.data?.table?.subjectGuid
      data.subjectId = parentNode.data?.entity?.subjectId || parentNode.data?.table?.subjectId
    }
    // 其他画布
    if (item.subjectGuid !== subjectId) {
      const sameEdge = item.json?.find(
        (cell) => cell.id === currentEdge?.id && cell.shape === 'edge',
      )
      // 如果已经有关系线。进行更新
      if (sameEdge) {
        const { isQuote } = sameEdge.data
        if (!isRemove) {
          sameEdge.data = { ...currentEdge.data, isQuote: isQuote }
          sameEdge.attrs = currentEdge.attrs
          sameEdge.markup = currentEdge.markup
          sameEdge.labels = currentEdge.labels
        } else {
          // 删除线和引用关系
          item.json = item.json.filter((i) => i.id !== currentEdge?.id)
          item.mappingEntityList = item.mappingEntityList.filter((i) => i.guid !== currentEdge?.id)
        }
      } else {
        if (!isRemove) {
          // 没有进行添加
          const edge = currentEdge.toJSON()
          if (parentNode && subNode) {
            item.json.push({ ...edge, data: data })
          }
          if (isQuote) {
            if (!item.mappingEntityList.find((m) => m.guid === currentEdge.id)) {
              item.mappingEntityList.push({
                guid: currentEdge.id,
                subjectGuid: data.subjectGuid,
                subjectId: data.subjectId,
                modId: data.modId,
                type: 3, //类型：1实体, 2表, 3关系
              })
            }
          }
        }
      }
      // 刷新关系线对应父子实体的属性
      const curParentNode = graph.getCellById(parentEntityId)
      const curSubNode = graph.getCellById(subEntityId)
      let attr = 'attr'
      if (modType === '3') {
        attr = 'col'
      }
      if (parentNode && curParentNode) {
        parentNode.data[attr] = curParentNode.data[attr]
      }
      if (subNode && curSubNode) {
        subNode.data[attr] = curSubNode.data[attr]
      }
    } else {
      // 当前画布
      if (isRemove) {
        item.mappingEntityList = item.mappingEntityList.filter((i) => i.guid !== currentEdge.id)
      } else {
        // 添加引用关系
        if (isQuote) {
          currentEdge.setData(null, { overwrite: true })
          currentEdge.setData(data, { overwrite: true })
          if (!item.mappingEntityList.find((m) => m.guid === currentEdge.id)) {
            item.mappingEntityList.push({
              guid: currentEdge.id,
              subjectGuid: data.subjectGuid,
              subjectId: data.subjectId,
              modId: data.modId,
              type: 3, //类型：1实体, 2表, 3关系
            })
          }
        }
      }
    }
  })
  return graphList
}
// 选择共享实体后节点计算
export function shareCells(data, mappingEntitys, graphCells, mapLocation) {
  const nodes = graphCells.filter((cell) => cell.shape !== 'edge')
  let maxX = 0
  let maxY = 0
  // 是否是右键引用实体，带坐标
  if (mapLocation) {
    maxX = mapLocation.x
    maxY = mapLocation.y
  } else {
    nodes?.forEach((item) => {
      maxX = Math.max((item.position?.x || item.x) + (item.size?.width || item.width) || 0, maxX)
      maxY = Math.max((item.position?.y || item.y) + (item.size?.height || item.height) || 0, maxY)
    })
  }
  const mappingEntityList = mappingEntitys || []
  const newCells = data.map((cell, index) => {
    let newCell
    const data = cell.data
    if (cell.shape === 'edge') {
      newCell = {
        ...cell,
        data: { ...data, disableMove: true, isShare: true },
      }
      if (mappingEntityList.every((m) => m.guid !== cell.id)) {
        mappingEntityList.push({
          guid: cell.id,
          subjectGuid: data.subjectGuid,
          subjectId: data.subjectId,
          modId: data.modId,
          type: 3, //类型：1实体, 2表, 3关系
        })
      }
    } else {
      newCell = {
        ...cell,
        data: { ...data, disableMove: true, isShare: true },
        x: maxX + 350 * (index % 5),
        y: maxY + 250 * Math.floor(index / 5),
      }
      if (mappingEntityList.every((m) => m.guid !== cell.id)) {
        mappingEntityList.push({
          guid: cell.id,
          subjectGuid: data.subjectGuid,
          subjectId: data.subjectId,
          modId: data.entity?.modId || data.table?.modId,
          type: cell.shape === 'nc-entity' ? 1 : 2, //类型：1实体, 2表, 3关系
        })
      }
    }
    return newCell
  })
  return { newCells: newCells, mappingEntityList: mappingEntityList }
}
// 根据数据生成节点信息
export function dataToNodes(data, type, kind) {
  // data：节点数据，type：节点类型，kind：数据库类型
  const { entityList, relationList } = data
  let cells = []
  entityList?.forEach((item) => {
    if (type === 'entity') {
      cells.push({
        id: item.guid,
        width: 200,
        height: 228,
        shape: 'nc-entity',
        data: {
          guid: item.guid,
          // 实体数据
          entity: {
            ...item,
            fieldList: undefined,
            sharedEntity: 'true', // 共享实体
            sharedReference: 'true', // 共享参考
            sourceEntity: 'false',
            modType: String(item.modType?.code),
            state: String(item.state?.code),
            standard: String(item.standard?.code),
            stage: String(item.stage?.code),
            occurrenceCycle: String(item.occurrenceCycle?.code),
          },
          // 属性数据
          attr: item.fieldList?.map((field) => {
            return { ...field, standard: String(field.standard?.code) }
          }),
          // 关系数据
          relation: {},
          subjectId: item.subjectId,
          subjectGuid: item.subjectGuid,
          kind: kind,
        },
      })
    } else if (type === 'table') {
      cells.push({
        id: item.guid,
        width: 200,
        height: 228,
        shape: 'nc-table',
        data: {
          guid: item.guid,
          // 表
          table: {
            ...item,
            fieldList: undefined,
            sharedEntity: 'true', // 共享实体
            sharedReference: 'true', // 共享参考
            sourceEntity: 'false',
            charUsed: String(item?.charUsed?.code),
            standard: String(item?.standard?.code),
            stage: String(item?.stage?.code),
          },
          // 列
          col: item.fieldList?.map((field) => {
            return { ...field, standard: String(field.standard?.code) }
          }),
          // 关系数据
          relation: {},
          subjectId: item.subjectId,
          subjectGuid: item.subjectGuid,
          kind: kind,
        },
      })
    }
  })
  relationList?.forEach((item) => {
    const { lineType, parentMust, subMust, keyUse } = item
    //1.实线，2.虚线 3.虚-实，4.实-虚
    let type = 1
    if (parentMust === 'false' && subMust === 'false') {
      type = 2
    } else if (parentMust === 'false' && subMust === 'true') {
      type = 3
    } else if (parentMust === 'true' && subMust === 'false') {
      type = 4
    }
    const { attrs, markup } = setLineAttr(type, String(lineType?.code), keyUse, data)
    cells.push({
      id: item.guid,
      source: { cell: item.parentEntityId, port: 'right-5' },
      target: { cell: item.subEntityId, port: 'left-5' },
      labels: item.name ? [item.name] : '',
      shape: 'edge',
      attrs: attrs,
      markup: markup,
      data: {
        ...item,
        sharedReference: 'true',
        lineType: String(item?.lineType?.code),
      },
    })
  })
  return cells
}
// 模板导入实体处理
export function importCells(data, modType, modId, kind) {
  const { entityList, relationList, subjectName, subjectGuid, subjectId } = data
  let cells = []
  if (entityList) {
    entityList?.forEach((item, index) => {
      // 物理模型
      if (modType === 3) {
        cells.push({
          x: 350 * (index % 5),
          y: 250 * Math.floor(index / 5),
          width: 200,
          height: 228,
          shape: 'nc-table',
          id: item.guid,
          data: {
            // 表
            guid: item.guid,
            table: {
              charUsed: '1', // charUsed
              dataProcesForm: '', // 数据处理形态
              definition: item.definition, // 定义
              guid: '', // 节点id
              kind: kind, // kind
              label: '', // 标签
              modGuid: '', // 模型guid
              modId: item.modId, // 模型ID
              notepad: '', // 记事本
              owner: '', // db_owner
              specialMatters: '', // 特别事项
              stage: '1', // 阶段:1实用性,2本质
              standard: '1', // 标准化:1继承,2对象,3非对象
              subject: subjectGuid, // 主题域
              name: item.entityName, // 名称
              tabName: item.tabName, // 表名
              type: 'Derived', // 类型
              mappingId: item.mappingId,
              sharedEntity: 'false', // 共享实体
              sharedReference: 'false', // 共享参考
              sourceEntity: 'false',
            },
            // 列
            col: item.fieldList?.map((field) => {
              return {
                ...field,
                mappingDictionId: 0,
                name: field.fieldName,
                substantiveKey: field.pk,
                type: 'Derived',
                standard: '1', // 标准化
                encryptFlag: 'false', // 是否加密
                protect: 'false', // 是否保护信息
              }
            }),
            subjectGuid: subjectGuid,
            // 关系数据
            relation: relationList?.find((rela) => rela.parentEntityName === item.entityName),
          },
        })
      } else {
        cells.push({
          x: 350 * (index % 5),
          y: 250 * Math.floor(index / 5),
          width: 200,
          height: 228,
          shape: 'nc-entity',
          id: item.guid,
          data: {
            guid: item.guid,
            // 实体数据
            entity: {
              id: item.id,
              abbreviation: '', // 缩写
              alternateName: '', // 候补名
              classify: '', // 分类
              dataProcessingForm: '', // 数据处理形态
              definition: item.definition, // 定义
              guid: '', // 节点id
              label: '', // 缩写标签
              modId: modId, // 模型ID
              modType: modType, // 模型类型：1概念，2逻辑
              monthlyOccurrence: '', // 月间发生量
              name: item.entityName, // 名称
              notepad: '', // 记事本
              occurrenceCycle: '', // 发生周期
              owner: '', // db_owner
              sharedEntity: 'false', // 共享实体
              sharedReference: 'false', // 共享参考
              shelfLife: '', // 保存期限
              sourceEntity: 'false', // 来源实体
              specialMatters: '', // 特别事项
              stage: '1', // 阶段：1实用性  2本质
              standard: '1', // 标准化：1继承，2对象，3非对象
              state: '', // 状态：1调查中，2分析中，3完成
              subject: subjectGuid, // 主题域
              synonymous: '', // 同义词
              tabName: item.tabName, // 表名
              totalQuantity: '', // 总数量
              type: 'Normal', // 类型
              mappingId: item.mappingId,
            },
            // 属性数据
            attr: item.fieldList?.map((field) => {
              return {
                ...field,
                mappingDictionId: 0,
                name: field.fieldName,
                substantiveKey: field.pk,
                type: 'Derived',
                standard: '1', // 标准化
                encryptFlag: 'false', // 是否加密
                protect: 'false', // 是否保护信息
              }
            }),
            subjectGuid: subjectGuid,
            // 关系数据
            relation: relationList?.find((rela) => rela.parentEntityName === item.entityName),
          },
        })
      }
    })
  }
  if (relationList) {
    relationList?.forEach((item) => {
      const sourceEntity = entityList?.find((entity) => entity.entityName === item.parentEntityName)
      const targetEntity = entityList?.find((entity) => entity.entityName === item.subEntityName)
      if (sourceEntity && targetEntity) {
        const lineArr = setLineArrows(String(item.lineTypeVal), item.keyUse)
        cells.push({
          shape: 'edge',
          labels: [`${item.name}`],
          source: sourceEntity.guid,
          target: targetEntity.guid,
          data: {
            ...item,
            lineType: String(item.lineTypeVal),
            parentMust: 'false',
            subMust: 'false',
            keyUse: 'false',
            nonInheritanceProces: 'false',
            type: 'Normal',
          },
          attrs: {
            line: {
              fill: 'none',
              stroke: '#3c4260',
              strokeWidth: 2,
              ...lineArr,
            },
          },
        })
      }
    })
  }
  delete data.entityList
  delete data.relationList
  data.subjectGuid = subjectGuid
  data.subjectId = subjectId
  data.subjectName = subjectName
  data.json = cells
  return data
}
// 实体编辑同步，实体主题域修改，data：修改后的节点信息，
// sourceSubject：修改前的主题域，targetSubject：修改后的主题域；subjectId：当前画布的主题域
export function asyncCells(graphList, cell, sourceSubject, subjectId, modId) {
  try {
    //修改后的节点信息
    const data = { ...cell?.data }
    const targetSubject = data.entity?.subject || data.table?.subject
    // 1.主题域未修改，只需要找到原生的节点进行更改
    // 2.主题域修改需要把新主题域上的节点设为原生，其他设置为引用
    if (sourceSubject !== targetSubject) {
      // curGraph：当前主题域，targetGraph：目标主题域，sourceGraph：之前的主题域
      const curIndex = graphList?.findIndex((item) => item.subjectGuid === subjectId)
      const targetIndex = graphList?.findIndex((item) => item.subjectGuid === targetSubject)
      const sourceIndex = graphList?.findIndex((item) => item.subjectGuid === sourceSubject)
      // 修改为其他主题域
      if (targetSubject !== subjectId) {
        if (subjectId === sourceSubject) {
          //原生节点，当前主题域需添加引用
          if (
            !graphList[curIndex]?.mappingEntityList?.find(
              (item) => item.subjectGuid === targetSubject,
            )
          ) {
            graphList[curIndex]?.mappingEntityList.push({
              guid: cell.id,
              subjectGuid: targetSubject,
              type: cell.shape === 'nc-entity' ? 1 : 2,
              modId: modId,
            })
          }
        } else {
          // 原来的域，新增引用
          graphList[sourceIndex]?.mappingEntityList?.push({
            guid: cell.id,
            subjectGuid: targetSubject,
            type: cell.shape === 'nc-entity' ? 1 : 2,
            modId: modId,
          })
        }
        //目标主题域是否有该节点
        const hasCell =
          graphList[targetIndex]?.json?.findIndex((item) => item.id === data.guid) > -1
        // 没有节点需要添加
        if (!hasCell) {
          delete data.isQuote
          let maxX = 0,
            maxY = 0
          const nodes = graphList[targetIndex]?.json
          nodes?.forEach((item) => {
            maxX = Math.max(
              (item.position?.x || item.x) + (item.size?.width || item.width) || 0,
              maxX,
            )
            maxY = Math.max(
              (item.position?.y || item.y) + (item.size?.height || item.height) || 0,
              maxY,
            )
          })
          // 更改主题域添加节点需要添加标记，方便后端刷数据
          data.changeSubjectFlag = true
          const node = {
            ...cell,
            x: maxX + 40,
            y: maxY,
          }
          graphList[targetIndex]?.json?.push(node)
        } else {
          // 如果有该节点，删除目标主题域该实体的引用信息
          const mappingEntityList = graphList[targetIndex]?.mappingEntityList ?? []
          if (graphList[targetIndex]?.mappingEntityList?.length > 0) {
            graphList[targetIndex].mappingEntityList = mappingEntityList.filter(
              (item) => item.guid !== data.guid,
            )
          }
        }
        // 修改为当前主题域
      } else {
        // 修改为当前主题域，代表以前是引用实体
        // 当前主题域删除引用
        const mappingEntityList = graphList[curIndex]?.mappingEntityList ?? []
        if (mappingEntityList?.length > 0) {
          graphList[curIndex].mappingEntityList = mappingEntityList.filter(
            (item) => item.guid !== data.guid,
          )
        }
        // 原来的域，新增引用
        graphList[sourceIndex]?.mappingEntityList?.push({
          guid: cell.id,
          subjectGuid: targetSubject,
          type: cell.shape === 'nc-entity' ? 1 : 2,
          modId: modId,
        })
      }
      // 更新所有的引用列表
      graphList?.forEach((item) => {
        item?.mappingEntityList?.forEach((map) => {
          if (map.subjectGuid === sourceSubject && map.guid === data.guid) {
            map.subjectGuid = targetSubject
          }
        })
      })
    }
    // 更新【新】原生节点的信息
    if (targetSubject !== subjectId) {
      const targetIndex = graphList?.findIndex((item) => item.subjectGuid === targetSubject)
      graphList[targetIndex]?.json?.forEach((item) => {
        if (item.id === data.guid && JSON.stringify(item.data) !== JSON.stringify(data)) {
          delete data.isQuote
          item.data = data
        }
      })
    }
    return graphList
  } catch (e) {
    console.log(e)
  }
}

const getPort = (edges, position, index, judgeEdge) => {
  // 查询所有线，看有无重复的
  let noRepeat = true
  let port = position + '-' + index
  edges?.forEach((edge) => {
    if (edge.source.cell === judgeEdge.source.cell && edge.source.port === port) {
      noRepeat = false
    }
  })
  if (noRepeat) {
    return port
  } else {
    if (index < 9) {
      return getPort(edges, position, Number(index) + 1, judgeEdge)
    } else {
      return port
    }
  }
}

// 层级布局 cells:所有的节点，disableMove是否可以移动
export function dagreLayout(cells, disableMove = false) {
  const layout = new DagreLayout({
    type: 'dagre',
    rankdir: 'LR',
    align: 'UL',
    ranksep: 100,
    nodesep: 100,
    controlPoints: true,
  })
  let finalCells = [...cells].filter((item) => item.shape !== 'edge')
  // 遍历cells，将没有关系线的放在后面去
  if (disableMove) {
    const edges = [...cells].filter((item) => item.shape === 'edge')
    const newEdges = []
    const noRelations = []
    for (let i = 0; i < finalCells.length; i++) {
      const nowCell = finalCells[i]
      if (
        nowCell.shape !== 'edge' &&
        edges?.findIndex(
          (edge) => edge?.source?.cell === nowCell.id || edge?.target?.cell === nowCell.id,
        ) === -1
      ) {
        noRelations.push(finalCells.splice(i, 1)[0])
        i = i - 1
      }
    }
    edges?.forEach((edge) => {
      // 如果，来源那边的连接点不在右边，把他调整到右边
      // 计算：判断调整之后的点，是否已经被占用，占用了就往下，如果已经占用完，就放在最后那个链接桩
      if (edge.source.port.indexOf('right') === -1) {
        const port = getPort(newEdges, 'right', '', edge)
        edge.source.port = port
      }
      if (edge.source.port.indexOf('right') > -1) {
        const port = getPort(newEdges, 'right', Number(edge.source.port.split('-')[1]), edge)
        edge.source.port = port
      }
      // 如果，目标那边的连接点是不在作边，把他调整到左边
      if (edge.target.port.indexOf('left') === -1) {
        edge.target.port = `left-${edge.target.port.split('-')[1]}`
      }
      newEdges.push(edge)
    })
    finalCells = [...finalCells, ...newEdges, ...noRelations]
  }
  const finishCells = disableMove ? finalCells : cells
  const data = {
    nodes: finishCells
      .filter((item) => item.shape !== 'edge')
      .map((item) => {
        return {
          id: item.id,
          shape: item.shape,
          width: item.width || item.size?.width,
          height: item.height || item.size?.height,
          attrs: item.attrs,
          data: { ...item.data, disableMove: disableMove },
        }
      }),
    edges: finishCells
      .filter((item) => item.shape === 'edge')
      .map((item) => {
        return { ...item, vertices: [] }
      }),
  }
  const model = layout.layout(data)
  return model
}
// 引入处理
export function modTypeValue(key) {
  return modTypeList[key]
}
// 是否可以设置长度 type:Zn中文，En英文
export function notSetLen(dataType, type) {
  const arry = type === 'Zn' ? dataTypeNameCanLen : dataTypeCanLen
  return !arry.includes(dataType)
}
// 是否可以设置小数
export function notSetScale(dataType, type) {
  const arry = type === 'Zn' ? dataTypeNameCanScale : dataTypeCanScale
  return !arry.includes(dataType)
}
// 属性\列名重命名
// list:数据；field：比较字段，value：当前值
export function reName(list, field, value) {
  if (list.find((i) => i[field]?.includes(value))) {
    const index = list.reduce((pre, current) => {
      let times = 0
      if (current[field]?.includes(value)) {
        const name = current[field].replace(value, '')
        times = name.includes('_') ? (name?.split('_')[1] === 'NaN' ? 0 : name?.split('_')[1]) : 0
      }
      return Math.max(times, pre)
    }, 0)
    return `${value}_${index + 1}`
  } else {
    return value
  }
}

function findFathers(nodes, edges, node, saveList) {
  const fathers = []
  edges?.forEach((edge) => {
    if (edge.target.cell === node?.id) {
      if (saveList) {
        saveList.push(edge.id)
      } else {
        fathers.push(edge.id)
      }
      const fatherNode = nodes?.find((item) => item.id === edge?.source?.cell)
      if (fatherNode) {
        if (saveList) {
          saveList.push(fatherNode.id)
        } else {
          fathers.push(fatherNode.id)
        }
        findFathers(nodes, edges, fatherNode, saveList ? saveList : fathers)
      }
    }
  })
  return fathers
}

function findChildren(nodes, edges, node, saveList) {
  const children = []
  edges?.forEach((edge) => {
    if (edge?.source?.cell === node?.id) {
      if (saveList) {
        saveList.push(edge.id)
      } else {
        children.push(edge.id)
      }
      const childNode = nodes?.find((item) => item.id === edge.target.cell)
      if (childNode) {
        if (saveList) {
          saveList.push(childNode.id)
        } else {
          children.push(childNode.id)
        }
        findChildren(nodes, edges, childNode, saveList ? saveList : children)
      }
    }
  })
  return children
}

export function findAllBloodNodes(nodes, edges, node) {
  const fathers = findFathers(nodes, edges, node)
  const children = findChildren(nodes, edges, node)
  const finalGraph = fathers.concat(children)
  return finalGraph
}

export function getEntityByObject(data, params) {
  if (data && data instanceof Object) {
    let nodes = []
    let row = 0
    let index = 0
    let attrParams = {
      mappingDictionId: 0,
      standardCol: '',
      col: '', // 列名
      substantiveKey: false, // 实质键
      notNull: false, // NotNull
      type: 'Derived', // 类型
      dataType: params.kind === 'ORACLE' ? '定长字符串' : '变长字符串', // 数据类型
      length: '', // 长度
      scale: '', // 小数点
      fk: false, // fk
      uk: false, // uk
      remarks: '', // 备注
      standard: '1', // 标准化
      encryptFlag: 'false', // 是否加密
      protect: 'false', // 是否保护信息
      // 其它
      alternateName: '', // 候补名
      synonymous: '', // 同义词
      core: '', // 核心
      essential: '', // 本质标识符
      candidate: '', // 候补标识符
      sync: '', // 同步
      nonInheritance: '', // 非继承
      Inclusive: '', // Inclusive
      defVal: '', // 默认值
      field: '', // 域
      reverse: '', // 逆向信息
      // 信息安全
      privacyClassify: '', // 隐私分类
      infoSecurityLevel: '', // 信息安全等级
      encryption: '', // 加密方法
      dataOwner: '', // 数据所有者

      definition: '', // 定义
    }
    nodes = data.map((item) => {
      let attr =
        item?.uniqueInfoList?.map((row, index) => {
          attrParams.guid = `${new Date().getTime()}${Math.random() + index}`
          attrParams.name = row
          return attrParams
        }) || []
      let attr1 =
        item?.attributeNameList?.map((row, index) => {
          attrParams.name = row
          attrParams.guid = `${new Date().getTime()}${Math.random() + index}`
          attrParams.substantiveKey = true
          attrParams.notNull = true
          attrParams.essential = 'true'
          return attrParams
        }) || []
      let obj = {
        width: 200,
        height: 228,
        shape: 'nc-entity',
        data: {
          // 实体数据
          entity: {
            abbreviation: '', // 缩写
            alternateName: '', // 候补名
            classify: '', // 分类
            dataProcessingForm: '', // 数据处理形态
            definition: '', // 定义
            guid: '', // 节点id
            label: '', // 缩写标签
            modId: params.modId, // 模型ID
            modType: params.modType, // 模型类型：1概念，2逻辑
            monthlyOccurrence: '', // 月间发生量
            name: item.objectName, // 名称
            mappingId: String(item.objectId), // 映射实体
            notepad: '', // 记事本
            occurrenceCycle: '', // 发生周期
            owner: '', // db_owner
            sharedEntity: 'false', // 共享实体
            sharedReference: 'false', // 共享参考
            shelfLife: '', // 保存期限
            sourceEntity: 'false', // 来源实体
            specialMatters: '', // 特别事项
            stage: '1', // 阶段：1实用性  2本质
            standard: '1', // 标准化：1继承，2对象，3非对象
            state: '', // 状态：1调查中，2分析中，3完成
            subject: params.subjectName, // 主题域
            synonymous: '', // 同义词
            tabName: '', // 表名
            totalQuantity: '', // 总数量
            type: 'Normal', // 类型
            // isAdd: true,
          },
          // 属性数据
          attr: attr.concat(attr1),
          // 关系数据
          relation: {},
          subjectGuid: params.subjectId,
          kind: params.kind,
        },
        // position: {
        x: index * 240 + 20,
        y: row * 268 + 20,
        // },
      }
      index++
      if (index === 5) {
        index = 0
        row++
      }
      return obj
    })
    return nodes
  } else {
    return []
  }
}
// 数据库逆向，pd文件导入表
export function addTableNodes(data, cells, modGuid, modId, subjectId) {
  let newCells = cells
  let maxX = 0
  let maxY = 0
  cells?.forEach((cell) => {
    if (cell.shape !== 'edge') {
      const width = cell.width || cell.size?.width
      const x = cell.x || cell.position?.x || 0
      const y = cell.y || cell.position?.y || 0
      maxX = Math.max(maxX, x + width)
      maxY = Math.max(maxY, y)
    }
  })
  let newCellsName = []
  // 已经导入的表不重复导入
  const addData = data.filter((d) => !cells?.find((cel) => cel.data?.table?.tabName === d.tabName))
  addData?.forEach((item, index) => {
    const newCell = {
      x: maxX + 50 + 250 * (index % 5),
      y: maxY + 250 * Math.floor(index / 5),
      width: 200,
      height: 228,
      shape: 'nc-table',
      id: item.guid,
      data: {
        // 表
        type: item.tableType,
        scripts: item.scripts,
        table: {
          charUsed: '1', // charUsed
          dataProcesForm: '', // 数据处理形态
          definition: item.definition, // 定义
          guid: '', // 节点id
          kind: item.kind, // kind
          label: '', // 标签
          modGuid: modGuid, // 模型guid
          modId: modId, // 模型ID
          notepad: '', // 记事本
          owner: item.owner, // db_owner
          specialMatters: '', // 特别事项
          stage: '1', // 阶段:1实用性,2本质
          standard: '1', // 标准化:1继承,2对象,3非对象
          subject: subjectId, // 主题域
          tabName: item.tabName, // 表名
          name: item.name,
          type: 'Derived', // 类型
          tableType: item.tableType,
          sharedEntity: 'false', // 共享实体
          sharedReference: 'false', // 共享参考
          sourceEntity: 'false', // 来源实体
        },
        // 列
        col: item.columns.map((col) => {
          return {
            ...col,
            guid: `${new Date().getTime()}${Math.random()}`,
          }
        }),
        // 关系数据
        relation: {},
        subjectGuid: subjectId,
      },
    }
    newCells.push(newCell)
    newCellsName.push(item.tabName)
  })
  return [newCells, newCellsName]
}
// 累计外键备注
export function addName(val, item) {
  console.log(val, item)
  let arr = val ? val.split(',') : []
  if (!arr?.includes(item)) {
    arr.push(item)
  }
  return arr.join(',')
}

// 删除外键备注
export function delName(val, item) {
  let arr = val ? val.split(',') : []
  if (arr?.includes(item)) {
    arr = arr.filter((i) => i !== item)
  }
  return arr.join(',')
}
// 删除节点外键
export function delNodeFK(curNode, edge, modType) {
  const data = { ...curNode.getData() }
  const parentEntityName = edge.data.parentEntityName
  const fkCol = edge.data.fatherChildKeys.map((k) => {
    return k.childKey
  })
  if (modType === '3') {
    data.col = data.col.map((cl) => {
      if (cl.fk && fkCol.includes(cl.col)) {
        const parentName = edge?.data?.fatherChildKeys?.find((i) => i.childKey === cl.col).fatherKey
        cl.remarks = delName(cl.remarks, `${parentEntityName}(${parentName})`)
        cl.fk = cl.remarks ? true : false
      }
      return cl
    })
  } else {
    data.attr = data.attr.map((cl) => {
      if (cl.fk && fkCol.includes(cl.name)) {
        const parentName = edge?.data?.fatherChildKeys?.find(
          (i) => i.childKey === cl.name,
        ).fatherKey
        cl.remarks = delName(cl.remarks, `${parentEntityName}(${parentName})`)
        cl.fk = cl.remarks ? true : false
        cl.candidate = ''
      }
      return cl
    })
  }
  // 多重置一次，防止不更新情况
  curNode.setData(null, { overwrite: true })
  curNode.setData(data, { overwrite: true })
}
