<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>线框/基础指标@2x</title>
    <g id="页面-1" stroke="none" stroke-width="0.5" fill="none" fill-rule="evenodd">
        <g id="指标管理-新建复合指标-编辑表达式" transform="translate(-747.000000, -220.000000)">
            <g id="编组-13备份-3" transform="translate(720.000000, 0.000000)">
                <g id="穿梭框（左）" transform="translate(16.000000, 68.000000)">
                    <g id="选项/默认备份" transform="translate(0.000000, 145.000000)">
                        <g id="线框/基础指标" transform="translate(12.000000, 8.000000)">
                           
                            <rect id="矩形" stroke="#FF7D00" fill="#FFF6E6" fill-rule="nonzero" x="0" y="0" width="16" height="16" rx="4"></rect>
                            <g id="衍" transform="translate(2.492000, 2.220000)" fill="#FF7D00" fill-rule="nonzero">
                                <path d="M4.656,0.072 L3.864,0.816 C4.596,1.416 5.172,1.968 5.58,2.484 L6.372,1.692 C5.904,1.152 5.328,0.612 4.656,0.072 Z M4.512,3 L3.732,3.756 C4.452,4.356 5.016,4.908 5.424,5.424 L6.216,4.632 C5.76,4.092 5.184,3.552 4.512,3 Z M2.316,0 C1.824,1.092 1.068,2.04 0.036,2.856 L0.372,3.972 C1.764,2.94 2.748,1.764 3.336,0.468 L2.316,0 Z M2.568,3.06 C2.016,4.248 1.164,5.292 0,6.204 L0.336,7.284 C0.696,7.008 1.044,6.72 1.38,6.432 L1.38,10.896 L2.484,10.896 L2.484,5.268 C2.928,4.716 3.288,4.128 3.576,3.528 L2.568,3.06 Z M8.496,10.86 C9.3,10.86 9.708,10.416 9.708,9.54 L9.708,4.848 L11.004,4.848 L11.004,3.756 L6.648,3.756 L6.648,4.848 L8.568,4.848 L8.568,9.264 C8.568,9.648 8.412,9.84 8.1,9.84 C7.668,9.84 7.224,9.816 6.768,9.78 L7.008,10.86 L8.496,10.86 Z M5.436,6.456 C4.836,7.788 4.152,8.94 3.408,9.924 L4.224,10.824 C4.956,9.852 5.652,8.616 6.324,7.128 L5.436,6.456 Z M6.864,0.636 L6.864,1.716 L10.668,1.716 L10.668,0.636 L6.864,0.636 Z" id="形状"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>