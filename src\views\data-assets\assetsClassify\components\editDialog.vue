<template>
  <n-modal
    v-model="showDirDialog"
    title="编辑分类"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item label="分类名称" field="name">
          <n-input v-model="state.syncForm.name" maxlength="30" placeholder="请输入名称" />
        </n-form-item>
        <n-form-item label="父级分类">
          <TreeSelect
            v-model="state.syncForm.parentCode"
            :data="state.treeData?.children"
            :props="{
              label: 'name',
              value: 'code',
              children: 'children',
            }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择父级分类"
            style="width: 100%"
          />
        </n-form-item>
<!--        <n-form-item label="分类编号">-->
<!--          <el-input-->
<!--            v-model="state.syncForm.parentCode"-->
<!--            disabled-->
<!--            axlength="30"-->
<!--            placeholder="请输入编号"-->
<!--          />-->
<!--        </n-form-item>-->
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import { updateAssetsClassify, getAssetsClassifyTree } from '@/api/sceneManage'
  import { checkCName } from '@/utils/validate'
  import TreeSelect from './treeSelect'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({
    treeData: null,
    syncForm: { name: null },
    syncRules: {
      name: [
        {
          required: true,
          validator: (...args) =>
            checkCName(...args, 'model', 'validModel', {
              nameType: 'CN',
              name: state.syncForm.name,
              id: null,
            }),
          trigger: 'blur',
        },
      ],
      // 父级分类
      pid: [
        {
          required: true,
          message: '请选择父级分类',
          trigger: 'change',
        },
      ],
      // 分类编号
      code: [
        {
          required: true,
          message: '请输入分类编号',
          trigger: 'blur',
        },
      ],
    },
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        updateAssetsClassify(state.syncForm).then(({ success }) => {
          if (!success) return
          emit('success')
          showDirDialog.value = false
          ElMessage.success('编辑成功')
        })
      }
    })
  }
  const _getAssetsClassifyTree = () => {
    getAssetsClassifyTree({
      keyword: '',
    }).then(({ data }) => {
      assignLevels(data)
      state.treeData = data
    })
  }
  function assignLevels(node, level = 0) {
    node.level = level
    ;(node.code === state.nodeCode || level > 4) && (node.disabled = true)
    node.children?.forEach((child) => {
      assignLevels(child, level + 1)
    })
  }

  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(node) {
      console.log(node)
      resetForm()
      state.nodeCode = node.code
      state.syncForm.id = node.id
      state.syncForm.name = node.name
      state.syncForm.parentCode = node.parentCode
      showDirDialog.value = true
      _getAssetsClassifyTree()
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
  }
</style>
