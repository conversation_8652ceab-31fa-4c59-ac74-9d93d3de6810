<template>
  <section class="entity">
    <div class="entity-left">
      <div class="table">
        <h3>主键索引</h3>
        <div class="table-box">
          <div
            class="table-node"
            style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
            :title="indexColumnInfoDOList[0] && indexColumnInfoDOList[0].indexName"
          >
            {{ indexColumnInfoDOList[0] && indexColumnInfoDOList[0].indexName }}
          </div>
          <div class="table-node">
            <n-select v-model="indexColumnInfoDOList[0].uniqueness" placeholder="请选择" disabled>
              <n-option name="unique" :value="1" />
            </n-select>
          </div>
        </div>

        <h3>
          其它索引
          <n-button :disabled="disabled" color="primary" @click="addOtherIndex">新增</n-button
          ><n-button :disabled="disabled" color="primary" @click="deleteSelectIndex">删除</n-button>
        </h3>
        <div
          v-for="(item, index) in indexColumnInfoDOList"
          :key="index"
          class="table-box"
          :style="{ borderColor: selectedIndex.index === index ? '$themeFontColor' : '#e1e1e1' }"
          @click="selectIndex(item, index)"
        >
          <div v-if="item.type === 'other'" class="table-node">
            <n-input
              :ref="`name${index}`"
              v-model="indexColumnInfoDOList[index].indexName"
              :disabled="disabled"
              @blur="checkRepeatIndex(index)"
            />
          </div>
          <div v-if="item.type === 'other'" class="table-node">
            <n-select
              v-model="indexColumnInfoDOList[index].uniqueness"
              placeholder="请选择"
              :disabled="disabled"
            >
              <n-option name="unique" :value="1" />
              <n-option name="non-unique" :value="2" />
            </n-select>
          </div>
        </div>
      </div>
    </div>

    <div class="entity-right">
      <n-tabs v-model="activeName" type="card" @active-tab-change="handleClick">
        <n-tab title="索引信息" id="first">
          <div class="table" style="height: 168px; overflow-y: scroll; border: 1px solid #e1e1e1">
            <h3>已选</h3>
            <div class="table-box" style="grid-template-columns: 200px auto">
              <div class="table-node">
                <span class="checkbox-span"> </span>
                <span style="line-height: 32px">Column</span>
              </div>
              <div class="table-node">
                <span class="checkbox-span-right"> Desc</span>
              </div>
            </div>
            <div
              v-for="item in selectedIndex.indexDetailDTOList"
              :key="item.guid"
              class="table-box"
              style="grid-template-columns: 200px auto"
            >
              <div class="table-node">
                <span class="checkbox-span">
                  <n-checkbox
                    :disabled="disabled"
                    :value="item.col"
                    @change="
                      (isSelect) => {
                        selectedSelect(!isSelect, item)
                      }
                    "
                  />
                </span>
                {{ item.col }}
              </div>
              <div class="table-node">
                {{ item.columnName }}
                <span class="checkbox-span-right">
                  <n-select v-model="item.descFlag" :disabled="disabled">
                    <n-option :value="1" name="是" />
                    <n-option :value="0" name="否" />
                  </n-select>
                </span>
              </div>
            </div>
          </div>
          <div class="identify-action">
            <n-button
              variant="solid"
              color="primary"
              icon="icon-arrow-up-l"
              @click="selectUnselectRows"
              :disabled="disabled"
            />
            <n-button
              variant="solid"
              color="primary"
              icon="icon-arrow-down-l"
              @click="cancelSelect"
              :disabled="disabled"
            />
          </div>
          <div class="table" style="height: 168px; overflow-y: scroll; border: 1px solid #e1e1e1">
            <h3 style="height: 32px">
              未选
              <n-search
                style="float: right; height: 100%"
                v-model="searchForm.attrName"
                @search="handleSearch"
              />
            </h3>

            <n-checkbox-group v-model="unselectedSelects">
              <div
                v-for="item in showCanSelect.filter(
                  (row) => selectedColList.indexOf(row.guid) === -1,
                )"
                :key="item.guid"
                class="table-box"
                style="grid-template-columns: 200px auto"
              >
                <div class="table-node">
                  <span class="checkbox-span">
                    <n-checkbox
                      :disabled="disabled"
                      :value="item.guid"
                      @change="
                        (isSelect) => {
                          unselectedSelect(!isSelect, item)
                        }
                      "
                    />
                  </span>
                  {{ item.col }}
                </div>
                <div class="table-node">
                  <span>
                    {{ item.columnName }}
                  </span>
                </div>
              </div>
            </n-checkbox-group>
          </div>
        </n-tab>
        <n-tab title="脚本" id="second">
          <n-textarea
            v-model="script"
            :disabled="disabled"
            style="width: 100%; height: 100%; position: relative"
          />
        </n-tab>
      </n-tabs>
    </div>
  </section>
</template>

<script>
  import { cloneDeep } from 'lodash'
  export default {
    props: {
      activeNameTable: {
        type: String,
        dufault: '',
      },
      cell: {
        type: Object,
        dufault: {},
      },
    },
    data() {
      return {
        searchForm: { attrName: '' },
        activeName: 'first', // 当前tabs
        data: {},
        selectedIndex: {},
        indexColumnInfoDOList: [],
        canSelectRow: [],
        showCanSelect: [],
        unselectedSelects: [],
        unselectedSelectRows: [],
        selectedSelects: [],
        selectedSelectRows: [],
        selectedColList: [],
        script: '',
      }
    },
    computed: {
      indexColumns() {
        let arr = []
        this.indexColumnInfoDOList.forEach((item) => {
          if (item.indexColumnInfoDOList && item.indexColumnInfoDOList.length) {
            // 先去掉已经存在的列
            const notIn = item.indexColumnInfoDOList?.filter((index) => arr.indexOf(index) < 0)
            arr = arr.concat(notIn)
          }
        })
        return arr
      },
      // 不可编辑
      disabled() {
        // const { disableMove } = this.cell.data
        // return disableMove
        return true
      },
    },
    watch: {
      indexColumns: {
        handler() {
          const data = this.cell.data
          const cols = data.col
          cols.forEach((item) => {
            if (
              this.indexColumns?.findIndex((index) => index?.guid && index?.guid === item?.guid) >
              -1
            ) {
              item.uk = true
            } else {
              item.uk = false
            }
          })
          this.cell.setData({ ...data, col: cols }, { overwrite: true })
        },
        deep: true,
        immediate: true,
      },
      activeNameTable: {
        handler(val) {
          if (val === 'fourth') {
            const oldCanSelectRow = cloneDeep(this.data.col?.filter((item) => !item.pk))
            this.canSelectRow = oldCanSelectRow.map((item) => {
              return {
                columnGuId: item.tabGuid,
                descFlag: 0,
                col: item.name,
                columnName: item.col,
                guid: item.guid,
              }
            })
            this.handleSearch()
          }
        },
      },
      cell(val) {
        this.PKChanged()
      },
    },
    created() {
      this.PKChanged()
    },

    methods: {
      //点击搜索
      handleSearch() {
        if (this.searchForm.attrName) {
          this.showCanSelect = this.canSelectRow?.filter(
            (item) =>
              item.col.indexOf(this.searchForm.attrName) > -1 ||
              item.columnName.indexOf(this.searchForm.attrName) > -1,
          )
        } else {
          this.showCanSelect = this.canSelectRow
        }
      },
      PKChanged() {
        this.data = { ...this.cell.data }
        const keyList = cloneDeep(this.data.col?.filter((item) => item.pk))
        const mainKeys = keyList.map((item, index) => {
          return {
            columnGuId: item.tabGuid,
            descFlag: 0,
            col: item.name,
            columnName: item.col,
            guid: item.guid,
            indexOrder: index + 1,
          }
        })
        const initFirstCol = {
          tabGuid: this.cell.id,
          tabId: this.data.id,
          tableName: this.data.table.tabName,
          indexName: this.data.table.tabName + '_PK',
          type: 'pk',
          uniqueness: 1,
          indexDetailDTOList: mainKeys,
        }

        this.indexColumnInfoDOList = this.data.indexColumnInfoDOList || []
        this.indexColumnInfoDOList[0] = initFirstCol
      },
      checkRepeatIndex(index) {
        let indexNames = []
        this.indexColumnInfoDOList.forEach((item) => {
          indexNames.push(item?.indexName)
        })
        // 判重:
        let newArr = []
        indexNames.forEach((name) => {
          if (newArr.indexOf(name) === -1) {
            newArr.push(name)
          }
        })
        if (newArr.length !== indexNames.length) {
          this.$message.error('索引名称不能重复！')
          if (index !== undefined) {
            this.$refs[`name${index}`][0].focus()
            if (this.indexColumnInfoDOList[index]) {
              this.indexColumnInfoDOList[index].indexName = ''
            }
          }
          return true
        }
        return false
      },
      handleClick() {
        // 这里做一次判重？
        if (this.activeName === 'second') {
          let indexRepeat = this.checkRepeatIndex()
          if (indexRepeat) {
            return
          }
          this.selectIndex()
          this.indexColumnInfoDOList.forEach((item) => {
            // eslint-disable-next-line no-unused-expressions
            item.indexDetailDTOList?.forEach((row, index) => {
              row.indexOrder = index + 1
            })
            item.indexColumnInfoDOList = item.indexDetailDTOList
          })

          this.$api.dataModel.getScriptIndex([...this.indexColumnInfoDOList]).then((res) => {
            this.script = res.data.join('\n')
          })
        }
      },
      selectUnselectRows() {
        if (this.selectedIndex.index !== undefined) {
          this.selectedIndex.indexDetailDTOList = this.selectedIndex.indexDetailDTOList?.concat(
            this.unselectedSelectRows.map((item) => {
              return { ...item, descFlag: 0 }
            }),
          )
          this.selectedColList = this.selectedColList.concat(
            this.unselectedSelectRows.map((item) => item.guid),
          )
          this.unselectedSelects = []
          this.unselectedSelectRows = []
        } else {
          this.$message.error('请先选中需要修改的索引')
        }
      },
      cancelSelect() {
        this.selectedIndex.indexDetailDTOList = this.selectedIndex.indexDetailDTOList?.filter(
          (item) => this.selectedSelectRows.findIndex((select) => select.guid === item.guid) < 0,
        )
        this.selectedColList = this.selectedColList?.filter(
          (item) => this.selectedSelectRows.findIndex((select) => select.guid === item) < 0,
        )
        this.selectedSelects = []
        this.selectedSelectRows = []
      },
      // 未选中列表选中后操作
      unselectedSelect(isSelect, item) {
        if (isSelect) {
          this.unselectedSelectRows.push({ ...item })
        } else {
          this.unselectedSelectRows = this.unselectedSelectRows?.filter(
            (row) => row.guid !== item.guid,
          )
        }
      },
      // 已选中列表选中后操作
      selectedSelect(isSelect, item) {
        if (isSelect) {
          this.selectedSelectRows.push({ ...item })
        } else {
          this.selectedSelectRows = this.selectedSelectRows?.filter((row) => row.guid !== item.guid)
        }
      },
      selectIndex(item, index) {
        if (this.selectedIndex.index === index) {
          this.selectedIndex = {}
          this.selectedColList = []
        } else {
          // 保存一次再切换
          if (this.selectedIndex.index) {
            this.indexColumnInfoDOList.splice(this.selectedIndex.index, 1, {
              ...this.selectedIndex,
              tableName: this.data.table.tabName,
              indexName: this.indexColumnInfoDOList[this.selectedIndex.index]?.indexName,
              uniqueness: this.indexColumnInfoDOList[this.selectedIndex.index]?.uniqueness,
            })
          }
          if (index) {
            this.selectedIndex = { ...item, index }
            this.selectedColList = item.indexDetailDTOList?.map((item) => item.guid)
          }
        }
        this.selectedSelects = []
        this.selectedSelectRows = []
        this.unselectedSelects = []
        this.unselectedSelectRows = []
      },
      addOtherIndex() {
        const hasEmpty = this.indexColumnInfoDOList.findIndex((item) => !item?.indexName) > 0
        if (hasEmpty) {
          this.$message.warning('请先填写索引，再新增！')
          return false
        }
        this.indexColumnInfoDOList.push({
          uniqueness: 1,
          type: 'other',
          tableName: this.data.table.tabName,
          indexName: '',
          tabGuid: this.cell.id,
          tabId: this.data.id,
          indexDetailDTOList: [],
        })
      },
      deleteSelectIndex() {
        if (this.selectedIndex.index) {
          this.indexColumnInfoDOList.splice(this.selectedIndex.index, 1)
          this.selectIndex({}, this.selectedIndex.index)
        } else {
          this.$message.error('请先选中需要删除的项')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .entity {
    display: flex;
    padding: 14px;
    .table-box {
      grid-template-columns: 150px auto !important;
      width: 100%;
    }
    &-left {
      width: 290px;
      height: 420px;
      margin-right: 14px;
      overflow: auto;
      border: 1px solid #cfcfcf;
      .requier {
        margin-right: 4px;
        color: #f56c6c;
      }
    }
    &-right {
      width: calc(100% - 304px);

      :deep(.nancalui-tabs) {
        height: 100%;

        .el-tabs__content {
          height: calc(100% - 31px);

          > div {
            height: 100%;
          }
          .el-textarea {
            height: 100%;

            &__inner {
              height: 100%;
              background: #fff;
              border: none;
            }
          }
        }
      }
    }

    .operator-select {
      margin: 5px 10px;
      text-align: center;
    }
    :deep(.el-checkbox__label) {
      display: none;
    }
    :deep(.nancalui-checkbox__group) {
      width: 100% !important;
    }
    .checkbox-span {
      float: left;
      width: 32px;
      height: 100%;
      margin-right: 5px;
      border-right: 1px solid #e1e1e1;
      &-right {
        float: right;
        width: 100px;
        height: 100%;
        padding-left: 12px;
        border-left: 1px solid #e1e1e1;
      }
    }
    .identify-action {
      margin: 10px 0;
      text-align: center;
    }
  }
</style>
