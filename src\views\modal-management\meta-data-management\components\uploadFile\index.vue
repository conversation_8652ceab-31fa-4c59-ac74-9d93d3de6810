<template>
  <div class="main">
    <el-upload
      ref="upload"
      :show-file-list="false"
      :action="fileUrl"
      accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.zip,.rar,.txt,.png,.jpeg,.html,.rtf,.mp4"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :auto-upload="false"
      :on-change="changeFile"
    >
      <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
      <n-button slot="trigger" size="sm" variant="solid" color="primary">添加文件</n-button>
      <n-button
        :disabled="fileData.length < 1"
        style="margin-left: 10px"
        size="sm"
        color="success"
        @click.stop.prevent="submitUpload"
        >上传文件</n-button
      >
    </el-upload>
    <div v-if="fileData.length > 0" class="showList">
      <ul>
        <div v-for="item in fileData" :key="item.uid">
          <li v-if="item.status === 'ready'">
            <svg-icon style="margin-right: 10px" icon-class="correct" />
            <span style="margin-right: 20px">添加文件成功：{{ item.name }} &nbsp;请上传！</span>
            <svg-icon class="delete" icon-class="icon_delete_n" @click="clearFile" />
          </li>
          <li v-else-if="item.status === 'success'">
            <svg-icon style="margin-right: 10px" icon-class="correct" />
            <span style="margin-right: 20px">数据导入成功：{{ item.name }} &nbsp;！</span>
            <svg-icon class="delete" icon-class="icon_delete_n" @click="clearFile" />
          </li>
          <li v-else-if="item.status === 'fail'">
            <svg-icon style="margin-right: 10px" icon-class="correct" />
            <span style="margin-right: 20px">添加文件失败：{{ item.name }} &nbsp;请重新添加！</span>
            <svg-icon class="delete" icon-class="icon_delete_n" @click="clearFile" />
          </li>
          <li v-else-if="item.status === 'error'">
            <svg-icon style="margin-right: 10px" icon-class="error" />
            <span style="margin-right: 20px"
              >数据导入失败：{{ item.name }} &nbsp;请核对模板内容！</span
            >
            <svg-icon class="delete" icon-class="icon_delete_n" @click="clearFile" />
            <a href="" @click="clearFile">ddd</a>
          </li>
        </div>
      </ul>
    </div>
  </div>
</template>

<script>
  import { importSubJect } from '@/api/model'
  export default {
    name: 'Index',
    data() {
      return {
        fileUrl: 'https://jsonplaceholder.typicode.com/posts/', // 接口地址
        uploadFile: '', //  存放上传文件的内容
        fileData: [], // 添加文件展示数据
      }
    },
    computed: {},
    created() {},
    methods: {
      submitUpload() {
        this.$refs.upload.submit()
      },
      handleRemove(file, fileList) {
        console.log('2222', file, fileList)
      },
      handleSuccess(res, file) {
        this.$message.success(file.name + '上传成功')
      },
      handleError(err, file) {
        this.$message.error(file.name + '上传失败')
      },
      changeFile(file, fileList) {
        if (fileList.length > 1) {
          fileList.shift()
        }
        const isLt2M = file.size / 1024 / 1024 <= 10
        if (!isLt2M) {
          return this.$message({
            message: '上传文件大小不能超过 10MB!',
            type: 'warning',
          })
        }
        this.uploadFile = file
        this.fileData = fileList
      },
      clearFile() {
        this.$refs.upload.uploadFiles.splice(0, this.$refs.upload.uploadFiles.length)
      },
      fileUploads(e) {
        let index = e.file.name.lastIndexOf('.')
        let type = e.file.name.substring(index + 1, e.file.name.length)
        const formData = new FormData()
        formData.append('file', e.file)
        formData.append('type', type)
        importSubJect(formData).then((res) => {
          if (res.code === '200') {
            // this.fileNameAt = this.attachmentFile.name
            // this.applyData.enclosureName = this.attachmentFile.name
            // this.applyData.enclosure = res.data
          } else if (res.code === '705') {
            this.$message({
              message: '文件为空，请重新上传',
              type: 'warning',
            })
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .main {
    .showList {
      > ul {
        list-style: none;
        padding: 0;
        position: absolute;
        left: 0;
        width: 50%;
        > div {
          > li {
            padding-left: 5px;
            border-radius: 2px;
            .delete {
              display: none;
            }
            &:hover {
              background-color: rgb(245, 247, 250);
              transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
              .delete {
                display: inline-block;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
</style>
