import request from '@/utils/request'
import URL from '@/const/urlAddress'

// ---------------------------------------审核中心接口  ----------------------------------------------------

// 取消申请
function auditCancelApply(data) {
  return request({
    url: `${URL.BASE}/apply/cancel/${data.id}`,
    method: 'post',
    data: data,
  })
}

// ---------------------------------------api部门管理接口  ----------------------------------------------------

// 根据id删除分组详情
function deleteDepartmentGroupingDetail(data) {
  return request({
    url: `${URL.AUTH}/department/${data.id}`,
    method: 'post',
    data: data,
  })
}

// 新增部门
function addDepartmentInsert(data) {
  return request({
    url: `${URL.AUTH}/department/add`,
    method: 'post',
    data: data,
  })
}
// 部门详情
function getDepartmentDetail(data) {
  return request({
    url: `${URL.AUTH}/department/${data.id}`,
    method: 'get',
  })
}

// 获取分组目录树
function getDepartmentTrees(data) {
  return request({
    url: `${URL.AUTH}/department/tree`,
    method: 'get',
    params: data,
  })
}

// 修改部门
function updateDepartment(data) {
  return request({
    url: `${URL.AUTH}/department/update`,
    method: 'post',
    data: data,
  })
}

// 获取webSocket地址
function getEnvironmentConfig(data) {
  return request({
    url: `${URL.BASE}/environment/config`,
    method: 'get',
    params: data,
  })
}

// 拉取信息
function pullMessageList(data) {
  return request({
    url: `${URL.BASE}/message/pull`,
    method: 'post',
    data: data,
  })
}

// 获取导航栏消息列表
function getNavMessageList(data) {
  return request({
    url: `${URL.BASE}/message/navigation/list`,
    method: 'post',
    data: data,
  })
}
// 获取所有消息列表
function getMessageList(data) {
  return request({
    url: `${URL.BASE}/message/page/list`,
    method: 'post',
    data: data,
  })
}

// 标记已读
function readMessage(data) {
  return request({
    url: `${URL.BASE}/message/read`,
    method: 'post',
    data: data,
  })
}

// 删除信息
function deleteMessage(data) {
  return request({
    url: `${URL.BASE}/message/delete`,
    method: 'post',
    data: data,
  })
}

// 获取菜单管理左侧树列表
function menuTree(data) {
  return request({
    url: `${URL.BASE}/menu/tree`,
    method: 'get',
    params: data,
  })
}

// 获取单个菜单下的路由子菜单
function getMenuChild(data) {
  return request({
    url: `${URL.BASE}/menu/search`,
    method: 'post',
    data: data,
  })
}

// 获取菜单详情
function getMenuDetail(data) {
  return request({
    url: `${URL.BASE}/menu/detail`,
    method: 'get',
    params: data,
  })
}

// 菜单删除
function deleteMenu(data) {
  return request({
    url: `${URL.BASE}/menu/delete`,
    method: 'post',
    data: data,
  })
}

// 菜单或按钮保存
function saveMenuOrButton(data) {
  return request({
    url: `${URL.BASE}/menu/save`,
    method: 'post',
    data: data,
  })
}
// 三员管理员设置页面回显
function getThreeManagerInfo(data) {
  return request({
    url: `${URL.BASE}/threeManager/threeManagerInfo`,
    method: 'get',
    params: data,
  })
}
// 三员管理员设置
function setThreeManager(data) {
  return request({
    url: `${URL.BASE}/threeManager/threeManager/set`,
    method: 'post',
    data: data,
  })
}
// 安全日志分页查询
function gitLogsFindByPage(data) {
  return request({
    url: `${URL.BASE}/log/findByPage`,
    method: 'post',
    data: data,
  })
}
// 管理日志分页查询-新
function gitLogsFindByPageNew(data) {
  return request({
    url: `${URL.BASE}/syslog/threeManager/page`,
    method: 'post',
    data: data,
  })
}
// 用户日志分页查询
function gitSyslogByPage(data) {
  return request({
    url: `${URL.BASE}/syslog/page`,
    method: 'post',
    data: data,
  })
}
// 用户日志分页查询 - 新
function gitSyslogByPageNew(data) {
  return request({
    url: `${URL.BASE}/syslog/user/page`,
    method: 'post',
    data: data,
  })
}
// 生命周期图表统计数据
function gitLifecycleStatistics(data) {
  return request({
    url: `${URL.BASE}/lifecycle/data/statistics`,
    method: 'get',
    params: data,
  })
}
// 首页统计接口
function gitHomeStatistics(data) {
  return request({
    url: `${URL.BASE}/statistic/homeStatistics`,
    method: 'get',
    params: data,
  })
}
// 同级菜单验重
function checkMenuName(data) {
  return request({
    url: `${URL.BASE}/menu/checkMenuName`,
    method: 'post',
    data: data,
  })
}

// 获取部门树(懒加载)
function getDepartmentLazy(data) {
  return request({
    url: `${URL.BASE}/department/lazy/tree`,
    method: 'post',
    data,
  })
}

// 部门树搜索
function getTreeSearchData(data) {
  return request({
    url: `${URL.BASE}/department/tree/search`,
    method: 'post',
    data,
  })
}

// 数据访问日志保存
function saveDataAccessLog(data) {
  return request({
    url: `${URL.BASE}/syslog/data/save`,
    method: 'post',
    data,
  })
}

// 数据访问日志列表
function getDataAccessLogList(data) {
  return request({
    url: `${URL.BASE}/syslog/data/page`,
    method: 'post',
    data,
  })
}
// 数据访问日志查看详情
function getDataAccessLogDetail(id) {
  return request({
    url: `${URL.BASE}/syslog/get/${id}`,
    method: 'get',
  })
}

// 上传部门和用户信息
function uploadUserData(data) {
  const form = new FormData()
  form.append('file', data.file)
  return request({
    url: `${URL.BASE}/portal/data/import/${data.type}`,
    method: 'post',
    data: form,
    responseType: 'blob',
  })
}

// 数据安全嵌入页面登录
function dceOpeLogin(data) {
  return request({
    url: `${URL.BASE}/dce/ope/login`,
    method: 'post',
    data,
  })
}

function ssoSys() {
  return request({
    url: `${URL.AUTH}/sso/sys/forward`,
    method: 'get',
  })
}
export default {
  auditCancelApply,
  deleteDepartmentGroupingDetail,
  addDepartmentInsert,
  getDepartmentDetail,
  getDepartmentTrees,
  updateDepartment,
  getEnvironmentConfig,
  pullMessageList,
  getNavMessageList,
  getMessageList,
  readMessage,
  deleteMessage,
  menuTree,
  getMenuChild,
  getMenuDetail,
  deleteMenu,
  saveMenuOrButton,
  getThreeManagerInfo,
  setThreeManager,
  gitLogsFindByPage,
  gitLogsFindByPageNew,
  gitSyslogByPage,
  gitSyslogByPageNew,
  gitLifecycleStatistics,
  gitHomeStatistics,
  checkMenuName,
  getDepartmentLazy,
  getTreeSearchData,
  saveDataAccessLog,
  getDataAccessLogList,
  getDataAccessLogDetail,
  uploadUserData,
  dceOpeLogin,
  ssoSys,
}
