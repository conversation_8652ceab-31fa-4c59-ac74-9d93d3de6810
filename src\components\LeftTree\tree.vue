<template>
  <div :class="isHideTree ? 'tree left-box hide' : 'tree left-box'">
    <div class="inside-box">
      <div class="resize"></div>
      <!-- <div :class="{ 'right-shadow': true, hide: isHideTree }"></div> -->
      <div class="tree-top">
        <slot name="pageTop"></slot>
      </div>

      <div v-show="!isHideTree && !isHideSearch" class="tree-search">
        <n-form>
          <n-form-item>
            <n-input
              v-model="filterText"
              size="small"
              placeholder="搜索"
              clearable
              suffix="search"
            />
          </n-form-item>
        </n-form>
      </div>
      <div v-show="!isHideTree" class="tree-content-box scroll-bar-style">
        <div class="tree-content" v-if="hasLoad">
          <el-tree
            ref="tree"
            class="filter-tree"
            :show-checkbox="showCheckbox"
            :default-checked-keys="defaultCheckedKeys"
            :current-node-key="currentNodeKey"
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="expand"
            :check-on-click-node="true"
            :highlight-current="true"
            :node-key="nodeKey"
            :default-expanded-keys="defaultExpandedKeys"
            :default-expand-all="expandDefault"
            :indent="0"
            :filter-node-method="filterNode"
            @node-click="clickFn"
            @check="changeFn"
          >
            <template #default="{ node, data }">
              <span class="tree-node" @mousedown="treeClick($event, data)">
                <!--判断是否展示图标-->
                <span :class="{ 'tree-node-label': true, 'last-tree-node': !data.children }">
                  <template v-if="showLeftIcon">
                    <SvgIcon
                      v-if="data.subkeysAddAvailable || data.level === 0 || data.children?.length"
                      :icon="data.icon ? data.icon : 'tree-open'"
                      class="tree-node-icon"
                    />
                    <SvgIcon
                      v-else
                      :icon="data.icon ? data.icon : 'tree-close'"
                      class="tree-node-icon child"
                    />
                  </template>
                  <!--节点名称-->
                  <span
                    v-if="data.children"
                    :title="node.label"
                    :class="showControl ? 'short tree-node-name parent' : 'tree-node-name parent'"
                    >{{ node.label }}</span
                  >
                  <span
                    v-else
                    :class="showControl ? 'short tree-node-name' : 'tree-node-name'"
                    :title="node.label"
                    >{{ node.label }}</span
                  >
                </span>

                <!--父节点操作按钮-->
                <el-popover
                  v-if="
                    showControl &&
                    (data.showAdd || data.showRun || data.showEdit || data.showDel || data.showAddT)
                  "
                  v-model="data.showControl"
                  popper-class="tree-node-control"
                  trigger="hover"
                  placement="right-end"
                >
                  <template #reference>
                    <div class="tree-more-icon-box hover-show">
                      <!-- <n-popover class="item" content="更多" trigger="hover" :position="['bottom']"> -->
                      <SvgIcon
                        slot="reference"
                        icon="tree-more-icon"
                        class="tree-node-control-operation"
                        @click.stop="preventFn"
                      />
                      <!-- </n-popover> -->
                    </div>
                  </template>
                  <template #default>
                    <span
                      v-if="data.showAdd && showAddDirectory"
                      class="el-popover-tree-name"
                      @click.stop="updateFn(data, false, true)"
                    >
                      <SvgIcon icon="icon-tree-directory" class="el-popover-tree-name-operation" />
                      新增{{ dialogTitle ? dialogTitle : '目录' }}</span
                    >
                    <span
                      v-if="data.showRun"
                      class="el-popover-tree-name"
                      @click.stop="runFn(data)"
                    >
                      <SvgIcon icon="icon-tree-run" class="el-popover-tree-name-operation" />
                      运行</span
                    >
                    <span
                      v-if="data.showAddT"
                      class="el-popover-tree-name"
                      @click.stop="addTFn(data)"
                    >
                      <SvgIcon icon="icon-tree-target" class="el-popover-tree-name-operation" />
                      新增指标</span
                    >
                    <span
                      v-if="data.showAdd && showAddChildDirectory"
                      class="el-popover-tree-name"
                      @click.stop="updateFn(data, false)"
                    >
                      <SvgIcon icon="icon-tree-file" class="el-popover-tree-name-operation" />
                      新增{{ childTitle }}</span
                    >
                    <span
                      v-if="data.showEdit"
                      class="el-popover-tree-name"
                      @click.stop="updateFn(data, true)"
                    >
                      <SvgIcon icon="icon-tree-edit" class="el-popover-tree-name-operation" />
                      编辑{{ data.subkeysAddAvailable ? dialogTitle : childTitle }}</span
                    >
                    <span
                      v-if="data.showDel"
                      class="el-popover-tree-name"
                      @click.stop="delFn(data)"
                    >
                      <SvgIcon icon="icon-tree-delete" class="el-popover-tree-name-operation" />
                      删除{{ data.subkeysAddAvailable ? dialogTitle : childTitle }}</span
                    >
                  </template>
                </el-popover>
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!--添加/编辑分组弹出框-->
      <n-modal
        v-model="showAddDialog"
        class="commonDialog has-top-padding"
        :title="
          (isEditDialog ? '编辑' : '新增') +
          (ruleForm.type === 'ROOT' || ruleForm.subkeysAddAvailable ? dialogTitle : childTitle)
        "
        width="580px"
        @close="cancelFn('showAddDialog')"
      >
        <div>
          <n-form
            ref="ruleForm"
            class="form__label_font14"
            :data="ruleForm"
            :rules="rules"
            label-align="end"
            label-width="62px"
          >
            <n-form-item :label="dialogName + '：'" field="name">
              <n-input
                v-model="ruleForm.name"
                size="small"
                maxlength="30"
                placeholder="请输入名称"
                clearable
                style="width: 100%"
              />
            </n-form-item>
            <n-form-item v-if="isShowDesc" :label="dialogDesc + '：'" field="desc">
              <n-input
                v-model="ruleForm.desc"
                clearable
                type="textarea"
                :autosize="{ minRows: 3 }"
                class="textarea"
                size="small"
                maxlength="200"
                show-word-limit
                placeholder="请输入描述"
                style="width: 100%"
              />
            </n-form-item>
          </n-form>
        </div>
        <template #footer>
          <n-modal-footer class="dialog-footer">
            <n-button @click.prevent="cancelFn('showAddDialog')" :loading="loading">取 消</n-button>
            <n-button variant="solid" @click.prevent="updateFn(false)" :loading="loading"
              >保 存</n-button
            >
          </n-modal-footer>
        </template>
      </n-modal>
      <div :class="isHideTree ? 'knob hide' : 'knob'" @click.prevent="hideFn">
        <n-popover
          v-if="isHideTree"
          class="item"
          content="展开"
          trigger="hover"
          :position="['bottom']"
        >
          <SvgIcon icon="icon-tree-arrow" class="pic rotate" />
        </n-popover>
        <n-popover v-else class="item" content="收缩" trigger="hover" :position="['bottom']">
          <SvgIcon icon="icon-tree-arrow" class="pic" />
        </n-popover>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { mapState } from 'vuex'
  import { checkCName } from '@/utils/validate'
  // import moduleName from '@/components/ModuleName'
  export default {
    name: 'PublicTree',
    components: {},
    // data：树基本数据数据,如 [{label:'节点',id:1,children:[]}];
    // treeAttrData：树的默认属性数据，如{showCheckbox:true,};
    // checkedNodes: 设置已选节点,如 [{id:1},{id:2}]
    props: ['data', 'treeAttrData', 'checkedNodes', 'remoteSearch'],
    data() {
      return {
        isHideSearch: false, // 是否左侧顶部搜索框
        isHideTree: false, // 是否左侧隐藏树
        hasLoad: false, // 是否已加载
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        nodeKey: 'id',
        expand: true, // 是否在点击节点的时候展开或者收缩节点
        showCheckbox: false, // 是否显示选择框
        defaultExpandedKeys: [], //  默认展开的节点的 key 的数组
        expandDefault: true, //是否展开全部
        defaultCheckedKeys: [], //  默认勾选的节点的 key 的数组
        currentNodeKey: '', // 当前选中的节点
        showControl: false, // 显示控制按钮
        showLeftIcon: false, // 显示左侧icon图标
        showAddDirectory: false, // 是否可新增目录
        showAddChildDirectory: true, // 是否可新增脚本
        loading: false,
        isShowDesc: true, // 新增/编辑是否有描述框
        parentControl: '', // 父元素要展示的控制项1.增加 2.编辑 3.删除 4.运行 5.新增指标
        childControl: '', // 子元素要展示的控制项1.增加 2.编辑 3.删除 4.运行 5.新增指标
        maxLevel: 100, // 最大展示文件夹层级 即大于等于改层级无新增功能
        treeData: [],
        showAddDialog: false, // 展示弹窗
        isEditDialog: false, // 判断是否是编辑状态
        dialogTitle: '目录', // 弹窗顶部名称
        dialogName: '名称', // 弹窗输入目录名称
        dialogDesc: '描述', // 弹窗描述
        childTitle: '', // 二级父类元素新增名称
        checkItem: {}, // 选中的节点
        ruleForm: {
          name: '',
          desc: '',
          type: '',
          subkeysAddAvailable: false,
        },
        validatorModuleName: null, //新增编辑弹框及时校验模块名
        validatorApiFnName: null, //新增编辑弹框及时校验模块接口方法名
        validatorApiDataKeys: ['name', 'id'], //新增编辑弹框接口参数keys
        rules: {
          // name: [{ required: true, validator: checkCName, trigger: 'blur' }],
          name: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, this.validatorModuleName, this.validatorApiFnName, {
                  name: this.validatorApiDataKeys.includes('name') ? this.ruleForm?.name : null,
                  id:
                    this.validatorApiDataKeys.includes('id') && this.isEditDialog
                      ? this.checkItem?.id
                      : null,
                  //额外参数
                  pid: this.validatorApiDataKeys.includes('pid')
                    ? this.isEditDialog
                      ? this.checkItem?.pid
                      : this.checkItem?.id
                    : null,
                  type: this.validatorApiDataKeys.includes('type') ? this.checkItem?.type : null,
                  subkeysAddAvailable: this.validatorApiDataKeys.includes('subkeysAddAvailable')
                    ? this.isEditDialog
                      ? this.checkItem?.subkeysAddAvailable
                      : this.ruleForm.subkeysAddAvailable
                    : null,
                }),
              trigger: 'blur',
            },
          ],

          desc: [{ required: false, message: '请输入', trigger: 'blur' }],
        },
        remoteSearch: false, //是否是远程搜索
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
      }),
    },
    watch: {
      filterText(val) {
        if (this.remoteSearch) {
          this.$emit('remoteSearch', val)
        } else {
          this.$refs.tree.filter(val)
        }
      },
      data(val) {
        let data = [...val]
        if (data.length === 0) {
          data = [
            {
              type: 'ROOT',
              id: '',
              level: 0,
              children: [],
              name: '全部',
              description: '',
            },
          ]
        }
        this.treeData = this.reformData(data)
      },
      checkedNodes(val) {
        this.$nextTick(() => {
          this.$refs.tree.setCheckedNodes(val)
        })
      },
    },
    mounted() {
      if (this.treeAttrData) {
        // 判断是否需要修改的属性

        for (let obj in this.treeAttrData) {
          if ({}.hasOwnProperty.call(this.treeAttrData, obj)) {
            this[obj] = this.treeAttrData[obj]
          }
        }
      }
      let data = [...this.data]
      if (data.length === 0) {
        data = [
          {
            type: 'ROOT',
            id: '',
            level: 0,
            children: [],
            name: '全部',
            description: '',
          },
        ]
      }
      this.treeData = this.reformData(data)
      this.$nextTick(() => {
        this.hasLoad = true
        this.dragControllerDiv()
      })
    },
    methods: {
      getCheckedNodes() {
        return this.$refs.tree.getCheckedNodes()
      },
      // 设置节点是否被选中, 使用此方法必须设置 node-key 属性 (key/data, checked, deep) 接收三个参数
      setChecked(data, checked, deep) {
        this.$refs.tree.setChecked(data, checked, deep) // 所有选中的节点对象
      },
      //通过key 设置节点是否选中
      setCurrentKey(nodeKey) {
        this.$refs.tree.setCurrentKey(nodeKey)
      },
      // 阻止默认事件
      preventFn() {
        return false
      },
      // 隐藏树事件
      hideFn() {
        this.isHideTree = !this.isHideTree
        var leftBox = document.getElementsByClassName('left-box')
        var resize = document.getElementsByClassName('resize')
        leftBox[0].style.width = ''
        if (this.isHideTree) {
          resize[0].style.left = '0px'
        } else {
          resize[0].style.left = '237px'
        }
        this.$emit('treeWidthChangeClick', true)
      },
      // 点击树获取event事件
      treeClick(e, data) {
        this.$emit('treeClick', { e: e, data })
      },
      // 对tree数据改造
      reformData(data) {
        data.forEach((val) => {
          val[this.nodeKey] = val[this.nodeKey] || 'ROOT'
          val.showControl = false
          val.showAdd = false
          val.showEdit = false
          val.showDel = false
          val.showAddT = false
          val.forbiddenOperation = val?.forbiddenOperation || false
          if (val.name) {
            val.label = val.name
          }
          let nodeObj = 'childControl' // 父元素的可操作对象parentControl,子元素可操作对象childControl
          if ((val.children && val.children.length > 0) || val.type === 'ROOT') {
            nodeObj = 'parentControl'
          }
          //forbiddenOperation - true 表示禁止有操作
          if (!val.forbiddenOperation) {
            if (val.parentOnlyAdd) {
              //父级只有新增按钮
              val.showAdd = true
            } else {
              if (this[nodeObj].indexOf('1') !== -1) {
                // 判断是否有添加操作
                val.showAdd = true
                if (val?.level > this.maxLevel) {
                  val.showAdd = false
                }
                if (val.subkeysAddAvailable !== undefined && !val.subkeysAddAvailable) {
                  val.showAdd = false
                }
              }

              if (this[nodeObj].indexOf('2') !== -1 && val?.level !== 0 && !val.isOriginLayer) {
                // 判断是否有编辑操作
                val.showEdit = true
              }

              if (
                this[nodeObj].indexOf('3') !== -1 &&
                val?.level !== 0 &&
                val.isDefault !== 'DEFAULT' &&
                !val.isOriginLayer
              ) {
                // 判断是否有删除操作
                val.showDel = true
              }

              if (this[nodeObj].indexOf('4') !== -1 && val.script) {
                // 判断是否有运行操作
                val.showRun = true
              }
              if (this[nodeObj].indexOf('5') !== -1 && val.level !== 0) {
                // 判断是否有运行操作
                val.showAddT = true
              }
            }
          }

          if (val.children && val.children.length > 0) {
            this.reformData(val.children)
          } else {
            val.children = null
          }
        })
        return data
      },
      // 遍历子节点对象属性名称
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      // 点击单个节点事件
      clickFn(item, attr, e) {
        this.$emit('clickNode', item, attr, e)
      },
      // 选中状态发生变化事件
      changeFn(item) {
        let data = {
          checkItem: item, // 当前选中的节点对象
          checkArr: this.$refs.tree.getCheckedNodes(), // 所有选中的节点对象
        }
        this.$emit('treeCheckNode', data)
      },
      // 取消所有选中
      cancelFocusFn() {
        this.$refs.tree.setCurrentKey(null)
      },
      // 取消弹窗事件
      cancelFn(name) {
        this[name] = false
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.resetFields()
          this.ruleForm = {
            name: '',
            desc: '',
            type: '',
            subkeysAddAvailable: false,
          }
        }
      },
      // 点击添加或修改节点事件
      updateFn(item, isEdit, subkeysAddAvailable = false) {
        // if (!this.currentProject.projectCode) {
        //   this.$notify({
        //     title: '提示',
        //     message: '请先选择场景！',
        //     type: 'warning',
        //   })
        //   return
        // }

        console.log(item)
        if (item) {
          // 判断是打开弹窗
          this.showAddDialog = true
          this.isEditDialog = isEdit
          this.ruleForm.type = item.type
          this.ruleForm.subkeysAddAvailable = subkeysAddAvailable
          if (isEdit) {
            this.ruleForm = {
              name: item.label,
              desc: item.description,
              type: item.type,
              subkeysAddAvailable: item.subkeysAddAvailable,
            }
          }
          this.checkItem = item
        } else {
          // 判断是提交
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {
              this.loading = true
              this.$emit('treeUpdateNode', {
                checkItem: this.checkItem,
                ruleForm: { ...this.ruleForm },
                isEdit: this.isEditDialog,
              })
            }
          })
        }
      },
      // 运行事件
      runFn(item) {
        this.$emit('treeRunNode', item)
      },
      // 新增指标事件
      addTFn(item) {
        this.$emit('treeAddTarget', item)
      },
      // 初始化
      clearFn() {
        this.$refs.ruleForm.resetFields()
        this.ruleForm = {
          name: '',
          desc: '',
          type: '',
          subkeysAddAvailable: false,
        }
        this.loading = false
        this.showAddDialog = false
      },
      clearLoading() {
        this.loading = false
      },
      // 点击删除节点事件
      delFn(item) {
        this.$emit('treeDelNode', item)
      },
      dragControllerDiv() {
        let resize = document.getElementsByClassName('resize')

        let leftBox = document.getElementsByClassName('left-box')
        for (let i = 0; i < resize.length; i++) {
          resize[i].onmousedown = function (e) {
            let startX = e.clientX
            resize[i].left = resize[i].offsetLeft
            document.onmousemove = function (e) {
              let endX = e.clientX
              let moveLen = resize[i].left + (endX - startX)
              if (moveLen < 0) moveLen = 0
              resize[i].style.left = moveLen - resize[i].offsetWidth + 'px'
              for (let j = 0; j < leftBox.length; j++) {
                leftBox[j].style.width = moveLen + 'px'
              }
            }
            document.onmouseup = function (evt) {
              document.onmousemove = null
              document.onmouseup = null
              resize[i].releaseCapture && resize[i].releaseCapture()
            }
            resize[i].setCapture && resize[i].setCapture()
            return false
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .tree {
    box-sizing: border-box;
    width: 240px;
    height: 100%;
    padding: 1px 0 1px 1px;
    border-radius: 4px 0px 0px 4px;
    &.hide {
      .inside-box {
        // background-color: #f2f3f5;
      }
    }

    &-search {
      padding: 8px 16px 0 !important;
    }
    .inside-box {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      // background-color: #f6f7fb;
      transition: all 0.3s;
    }

    .right-shadow {
      position: absolute;
      top: 0;
      right: 0;
      width: 22px;
      height: 100%;
      background: linear-gradient(90deg, rgba(217, 217, 217, 0) 0%, #d9d9d9 100%);
      opacity: 0.3;
      &.hide {
        width: 0;
      }
    }

    .resize {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      box-sizing: border-box;
      width: 3px;
      height: 100%;
      border-right: 1px solid #eeeeee;
      cursor: e-resize;
    }
    .tree-top {
      display: block;
      :deep(.title) {
        height: 32px;
        color: #333333;
        font-weight: bolder;
        font-size: 12px;
        line-height: 28px;
        text-align: center;
        background: #f2f3f6;
        border: 2px solid #ffffff;
        border-radius: 4px;
      }
    }
    &.hide {
      width: 1px;
      margin-left: -2px;
      padding: 0;
      background-color: #fff;
      border-right: none;
      .tree-top {
        display: none;
      }
    }

    .knob {
      position: absolute;
      top: 0;
      right: -12px;
      bottom: 0;
      z-index: 9;
      width: 12px;
      height: 80px;
      margin: auto;
      background-color: #f0f7ff;
      border-radius: 0 9px 9px 0;
      cursor: pointer;

      &.hide {
        background-color: #ecf3ff;
        border-radius: 0 9px 9px 0;
      }

      .pic {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        color: $themeBlue;
        font-size: 12px;
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }

    &-search {
      padding: 20px 14px 0;

      .nancalui-form {
        .nancalui-form__item--horizontal {
          margin-bottom: 10px;
        }
        :deep(.nancalui-input) {
          .nancalui-input-slot__suffix {
            position: relative;
            .nancalui-icon__container:first-of-type {
              background-image: url('/src/assets/img/icon-search.png');
              background-repeat: no-repeat;
              background-position: center;
              background-size: 18px 18px;
              .icon-search {
                font-size: 18px !important;
                opacity: 0;
              }
            }
            > .nancalui-input__clear--icon {
              position: absolute;
              top: 6px;
              bottom: 0;
              left: -6px;
              width: 16px;
              height: 16px;
              margin: auto;
              .icon {
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    &-content-box {
      position: relative;
      box-sizing: border-box;
      width: calc(100% - 20px);
      height: calc(100% - 90px);
      margin: 0 auto;
      overflow-y: overlay;
    }

    &-content {
      width: 100%;
      height: 100%;
      padding-bottom: 10px;

      :deep(.filter-tree) {
        .el-tree-node {
          position: relative;
          padding-left: 4px;
        }

        .el-tree-node__children {
          padding-left: 14px;
          .el-tree-node__content:has(.is-leaf) {
            padding-left: 6px !important;
          }
        }

        .el-tree-node::before {
          position: absolute;
          top: 32px;
          left: 15px;
          width: 1px;
          height: calc(100% - 40px);
          border-width: 1px;
          border-left: 1px solid #e1e1e180;
          content: '';
        }

        .el-tree-node {
          .last-tree-node::before {
            display: inline-block;
            width: 5px;
            height: 5px;
            margin-right: 5px;
            margin-left: 9px;
            background: #c8c9cc;
            border-radius: 5px;
            content: '';
          }
        }

        & > .el-tree-node::after {
          border-top: none;
        }

        .el-tree-node__expand-icon {
          font-size: 12px;
          &.is-leaf {
            display: none;
            color: transparent;
          }
        }
      }

      .tree-node {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        width: 100%;
        user-select: none;
        &:hover {
          .tree-node-icon {
            color: $themeBlue;
          }
        }
        &-icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          color: #8091b7;
          vertical-align: middle;
          &.child {
            width: 16px;
            height: 16px;
          }
        }

        &-name {
          display: inline-block;
          max-width: 132px;
          overflow: hidden;
          color: #333333;
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: middle;
        }

        .parent {
          color: #000;
          font-size: 12px;
        }

        &:hover {
          .tree-node-name {
            padding-right: 20px;
          }
          .tree-node-control {
            opacity: 1;
            svg {
              color: #333333;
            }
          }
        }

        &-label {
          width: calc(100% - 20px);
        }

        &-control {
          position: absolute;
          right: 0;
          display: inline-block;
          height: 30px;
          margin-right: 8px;
          padding: 0;
          line-height: 30px;
          text-align: center;
          opacity: 0;

          &-operation {
            display: inline-block;
            width: 16px;
            height: 16px;
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            vertical-align: middle;
            border-radius: 4px;
          }
        }
      }
    }

    :deep(.el-tree) {
      background-color: inherit;
    }

    :deep(.el-tree-node__content) {
      box-sizing: border-box;
      width: 100%;
      height: 30px;
      line-height: 32px;
      border-radius: 4px;
      &:hover {
        background-color: #e6ecf8;
        .hover-show {
          opacity: 1;
        }
      }
      .hover-show {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 5px;
        border-radius: 4px;
        opacity: 0;
        &:hover {
          background-color: #cedcf9;
        }
      }
    }

    :deep(.el-tree-node.is-current) {
      & > .el-tree-node__content {
        background-color: #e6ecf8;
        .tree-node-icon {
          color: $themeBlue;
        }

        .tree-node-name {
          color: $themeBlue;
          font-weight: bolder;
        }
      }
    }

    :deep(.el-tree-node__children) {
      overflow: inherit;
    }

    :deep(.el-textarea__inner) {
      padding: 8px 10px;
      background-color: #fff;
    }

    :deep(.tree-content .filter-tree .el-tree-node::before) {
      border-left: none;
    }
  }
</style>
<style lang="scss">
  .el-popover {
    display: flex;
    flex-direction: column;
    padding: 4px;

    .el-popover-tree-name {
      height: 32px;
      padding: 0 8px;
      color: #333333;
      line-height: 32px;
      cursor: pointer;

      &-operation {
        width: 16px;
        height: 16px;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        vertical-align: -2px;
        border-radius: 4px;
      }

      &:hover {
        background: #e6ecf8;
      }
    }
  }
</style>
