<template>
  <div class="dialog">
    <el-dialog
      v-model="isDialog"
      class="middleDialog"
      :width="width"
      :show-close="true"
      :top="top"
      center
      @close="close"
    >
      <template #header="{ close }">
        <div class="header">
          <div class="titleClass">{{ title }}</div>
          <i class="icon-close" @click.prevent="close"></i>
        </div>
      </template>
      <div class="main">
        <slot>正在开发中</slot>
      </div>
      <template #footer>
        <slot name="footer">
          <span class="dialog-footer">
            <n-button variant="solid" @click.prevent="confirm">确 定</n-button>
            <n-button @click.prevent="cancel">取 消</n-button>
          </span>
        </slot>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, withDefaults, toRefs, defineEmits } from 'vue'

  interface Props {
    isDialog: boolean
    message?: string
    width?: string | number | undefined
    height?: string | number | undefined
    right?: string | number | undefined
    top?: string
    title?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    message: '',
    width: '500px',
    top: '',
    title: '提示',
    isDialog: false,
  })
  const { width, top, title, isDialog, height, right } = toRefs(props)
  const style = {
    height: height?.value,
    position: 'relative',
    right: right?.value,
  }
  if (right?.value !== undefined) {
    style.position = 'absolute'
  } else {
    style.position = 'relative'
  }
  // v-model:money
  const emit = defineEmits(['confirm', 'cancel', 'close', 'update:isDialog'])
  // 取消
  function cancel() {
    emit('cancel')
  }
  // 确定
  function confirm() {
    emit('confirm')
  }
  // 关闭
  function close() {
    emit('close')
    emit('update:isDialog', false)
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .dialog {
    .header {
      display: flex;
      justify-content: space-between;

      .titleClass {
        font-size: 15px;
        font-weight: bolder;
        color: #000000d9;
        line-height: 18px;
        height: 16px;
        display: flex;
        align-items: center;
        &::before {
          content: '';

          display: inline-block;
          width: 4px;
          height: 16px;
          margin-right: 6px;
          background: $themeBlue;
        }
      }
    }

    .main {
      padding: 30px 30px;
      height: 100%;
      max-height: 500px;
      overflow-y: auto;
    }

    :deep(.el-dialog) {
      height: v-bind('style.height');
      position: v-bind('style.position');
      right: v-bind('style.right');
      overflow-y: auto;
    }

    :deep(.el-dialog--center) {
      display: flex;
      flex-direction: column;
      background-color: #fff;
    }

    :deep(.el-dialog__header) {
      margin-right: 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    }

    :deep(.el-dialog__body) {
      flex: 1;
    }

    :deep(.el-dialog__footer) {
      padding-top: 12px;
      border-top: 1px solid rgba(0, 0, 0, 0.09);
    }
  }
</style>
