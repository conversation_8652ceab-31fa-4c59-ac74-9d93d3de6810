<template>
  <div class="newAssetPanorama">
    <iframe ref="myIframe" class="iframe" v-if="state.iframeUrl" :src="state.iframeUrl"></iframe>
  </div>
</template>

<script setup>
  import { useStore } from 'vuex'
  import { useRoute } from 'vue-router'
  import api from '@/api/index'
  const store = useStore()
  const route = useRoute()
  const state = reactive({
    iframeUrl: null,
    timeFlag: null,
  })

  // 通过路由code比对获取对应嵌入页面地址
  const getUrlFn = (code) => {
    api.base.dceOpeLogin({}).then((res) => {
      if (res.success) {
        localStorage.setItem('_pouch_check_localstorage', 'C')
        localStorage.setItem('_pouch_dpe_database', 'a')
        localStorage.setItem(
          'localStaffInfo',
          JSON.stringify({
            accessToken: res.data.accessToken,
            staffUUID: res.data?.staffInfo?.staffUUID,
            pwdModifyStatus: 1,
          }),
        )
        const { menuImplantList } = store.state.user
        menuImplantList.forEach((val) => {
          if (val.code === code) {
            state.iframeUrl = val.url
          }
        })
      }
    })
  }

  // 监听路由变化
  watch(
    () => route,
    (newRoute) => {
      if (state.timeFlag) {
        clearTimeout(state.timeFlag)
        state.timeFlag = null
      }
      state.timeFlag = setTimeout(() => {
        getUrlFn(newRoute.meta.code)
      }, 500)
    },
    { deep: true, immediate: true },
  )
  onMounted(() => {
    if (state.timeFlag) {
      clearTimeout(state.timeFlag)
      state.timeFlag = null
    }
    state.timeFlag = setTimeout(() => {
      getUrlFn(route.meta.code)
    }, 500)
  })
  onBeforeUnmount(() => {
    if (state.timeFlag) {
      clearTimeout(state.timeFlag)
      state.timeFlag = null
    }
  })
</script>

<style lang="scss" scoped>
  .newAssetPanorama {
    position: relative;
    width: 100%;
    height: 100%;
    .iframe {
      flex-shrink: 0;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;
      height: 100%;
      border: none;
    }
  }
</style>
