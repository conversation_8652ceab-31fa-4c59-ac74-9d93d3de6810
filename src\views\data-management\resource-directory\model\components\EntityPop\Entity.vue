<template>
  <section v-loading="isLoad" class="entity">
    <div class="entity-left">
      <div class="table">
        <h3>实体编辑</h3>
        <div class="table-box">
          <div class="table-node"><span class="requier">*</span>实体名</div>
          <div class="table-node">
            <n-tooltip :content="data.name" :disabled="!data.name" position="top">
              <n-input :disabled="disabled" v-model="data.name" />
            </n-tooltip>
          </div>
          <div v-if="modType === '1'" class="table-node">映射业务对象</div>
          <div v-else class="table-node">映射概念实体</div>
          <div class="table-node">
            <n-tooltip :content="mappingName" :disabled="!mappingName" position="top">
              <n-select
                v-if="!disabled"
                v-model="data.mappingId"
                :loading="loading"
                remote
                allow-clear
                :filter="remoteMethods"
                :placeholder="`可输入${modType === '1' ? '业务对象名称' : '实体名'}查询`"
                @clear="remoteMethods"
                @toggle-change="remoteMethods"
                @value-change="onChange"
              >
                <n-option
                  v-for="item in mappingOptions"
                  :key="item.value"
                  :value="item.value"
                  :name="item.label"
                />
              </n-select>
              <n-input v-else disabled v-model="data.mappingName" />
            </n-tooltip>
          </div>
          <div class="table-node">表名</div>
          <div class="table-node">
            <n-tooltip :content="data.tabName" :disabled="!data.tabName" position="top">
              <n-input :disabled="disabled" v-model="data.tabName" />
            </n-tooltip>
          </div>

          <div class="table-node">同义词</div>
          <div class="table-node"><n-input :disabled="disabled" v-model="data.synonymous" /></div>

          <div class="table-node">DB Owner</div>
          <div class="table-node"><n-input :disabled="disabled" v-model="data.owner" /></div>
        </div>

        <h3>实体设置</h3>
        <div class="table-box">
          <div class="table-node">所属</div>
          <div class="table-node">
            <n-tooltip :content="subjectName" :disabled="!subjectName" position="top">
              <n-select v-if="!disabled" v-model="data.subject" placeholder="请选择">
                <n-option
                  v-for="item in subjectList"
                  :name="item.subject"
                  :value="item.guid"
                  :key="item.guid"
                />
              </n-select>
              <n-input v-else disabled v-model="data.subjectName" />
            </n-tooltip>
          </div>

          <div class="table-node">标准化</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.standard" placeholder="请选择">
              <n-option name="继承" value="1" />
              <n-option name="对象" value="2" />
              <n-option name="非对象" value="3" />
            </n-select>
          </div>
        </div>

        <h3>设置实体共享</h3>
        <div class="table-box">
          <div class="table-node">共享实体</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.sharedEntity" placeholder="请选择">
              <n-option name="True" value="true" />
              <n-option name="False" value="false" />
            </n-select>
          </div>
        </div>

        <h3>其它</h3>
        <div class="table-box">
          <div class="table-node">状态</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.state" placeholder="请选择">
              <n-option name="调查中" value="1" />
              <nl-option name="分析中" value="2" />
              <n-option name="完成" value="3" />
            </n-select>
          </div>

          <div class="table-node">发生周期</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.occurrenceCycle" placeholder="请选择">
              <n-option name="随时" value="1" />
              <n-option name="日" value="2" />
              <n-option name="周间" value="3" />
              <n-option name="月间" value="4" />
              <n-option name="季度" value="5" />
              <n-option name="半年" value="6" />
              <n-option name="年间" value="7" />
            </n-select>
          </div>

          <div class="table-node">月间发生量</div>
          <div class="table-node">
            <n-input :disabled="disabled" v-model="data.monthlyOccurrence" />
          </div>

          <div class="table-node">保存期限(月)</div>
          <div class="table-node">
            <n-input :disabled="disabled" v-model="data.shelfLife" />
          </div>

          <div class="table-node">总数量</div>
          <div class="table-node">
            <n-input :disabled="disabled" v-model="data.totalQuantity" />
          </div>
        </div>
      </div>
    </div>

    <div class="entity-right">
      <n-tabs :disabled="disabled" v-model="activeName" type="wrapped">
        <n-tab title="定义" id="first">
          <n-textarea
            :disabled="disabled"
            v-model="data.definition"
            placeholder="请输入内容"
            resize="both"
          />
        </n-tab>
      </n-tabs>
    </div>
  </section>
</template>

<script>
  import { cloneDeep } from 'lodash'
  export default {
    props: ['cell', 'subjectList'],
    data() {
      return {
        activeName: 'first', // 当前tabs
        data: {
          definition: '', // 定义
          dataProcessingForm: '', // 数据处理形态
          specialMatters: '', // 特别事项
          notepad: '', // 记事本

          // 实体编辑
          name: '', // 实体名
          tabName: '', // 表名
          synonymous: '', // 同义词
          alternateName: '', // 候补名
          owner: '', // DB Owner

          // 实体设置
          classify: '', // 分类
          stage: '1', // 阶段
          type: 'Normal', // 类型
          subjet: '', // 所属
          standard: '1', // 标准化
          abbreviation: '', // 缩写
          label: '', // 标签

          // 实体共享
          sharedEntity: 'false', // 共享实体
          sourceEntity: 'false', // 来源实体
          sharedReference: 'false', // 共享参考

          // 其它
          state: '', // 状态
          occurrenceCycle: '', // 发生周期
          monthlyOccurrence: '', // 月间发生量
          shelfLife: '', // 保存期限(月)
          totalQuantity: '', // 总数量
        },
        mappingOptions: [],
        loading: false,
        isLoad: false,
      }
    },
    computed: {
      disabled() {
        return this.cell.data.type === 'BLOOD' || this.cell.data.disableMove
      },
      modType() {
        return (
          this.$route.query.modType ||
          this.data?.modType ||
          (this.data?.modType?.code ? String(this.data?.modType?.code) : '')
        )
      },
      mappingName() {
        return (
          this.mappingOptions.find((item) => item.value === this.data.mappingId)?.label ||
          this.data.mappingName ||
          ''
        )
      },
      subjectName() {
        return (
          this.subjectList?.find((item) => item.guid === this.data.subject)?.subject ||
          this.data.subjectName ||
          ''
        )
      },
    },
    watch: {
      cell(val) {
        this.initData()
      },
    },
    async created() {
      await this.initData()
    },
    methods: {
      onChange() {
        this.data.mappingName = this.mappingOptions.find(
          (item) => item.value === this.data.mappingId,
        )?.label
        this.data.mappingOriginalName = this.mappingOptions.find(
          (item) => item.value === this.data.mappingId,
        )?.originalName
      },
      // 初始化数据
      async initData() {
        try {
          this.isLoad = true
          this.data = {}
          const nodeData = cloneDeep(this.cell.data)
          // 防止逻辑模型的映射实体选不中
          if (!this.disabled) {
            if (nodeData.entity?.mappingId) {
              await this.getEntityById(nodeData.entity?.mappingId)
            } else {
              await this.remoteMethods()
            }
          }
          this.data = cloneDeep(nodeData.entity)
          // 为了把以前的所属文本改为id
          const subject = this.subjectList?.find((item) => item.subject === this.data.subject)
          if (subject) {
            this.data.subject = subject.guid
          }
          // 如果关联的实体找不到，则清空
          if (!this.mappingOptions.length) {
            this.data.mappingId = ''
          }
          this.isLoad = false
        } catch (e) {
          this.isLoad = false
        }
      },
      // 实体搜索
      async remoteMethods(query) {
        try {
          if (!query) {
            return
          }
          if (typeof query === 'boolean' && query) {
            query = ''
          }
          const mapList = this.$store.state.app.mapList
          if (mapList?.length === 0 || query) {
            this.loading = true
            let res = []
            if (this.modType === '1') {
              res = await this.$api.dataModel.businessObjectList({ name: query })
            } else {
              res = await this.$api.dataModel.entityList({ name: query, kind: 'concept' })
            }
            // 保存映射对象列表，防止重复请求
            if (!query) {
              this.$store.dispatch('app/setMapList', res.data)
            }
            this.mappingOptions = res.data.map((item) => {
              return {
                value: this.modType === '1' ? String(item.objectId) : item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
          } else {
            this.mappingOptions = mapList.map((item) => {
              return {
                value: this.modType === '1' ? String(item.objectId) : item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
          }
          // console.log(this.mappingOptions)
          this.loading = false
        } catch (e) {
          console.log(e)
          this.loading = false
        }
      },
      async getEntityById(id) {
        try {
          const mapList = this.$store.state.app.mapList
          const curMap = mapList.find((item) => item.guid === id || String(item.objectId) === id)
          if (!curMap) {
            this.loading = true
            let res = []
            if (this.modType === '1') {
              res = await this.$api.dataModel.businessObjectList({ mappingId: id })
            } else {
              res = await this.$api.dataModel.entityList({ mappingId: id, kind: 'concept' })
            }
            this.mappingOptions = res.data.map((item) => {
              return {
                value: this.modType === '1' ? String(item.objectId) : item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
            this.loading = false
            if (mapList.length > 50) {
              this.$store.dispatch('app/setMapList', mapList.concat(res.data))
            }
          } else {
            this.mappingOptions = mapList.map((item) => {
              return {
                value: this.modType === '1' ? String(item.objectId) : item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
          }
        } catch (e) {
          console.log(e)
          this.loading = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .entity {
    display: flex;
    padding: 14px;

    &-left {
      width: 350px;
      height: 420px;
      border: 1px solid #cfcfcf;
      margin-right: 14px;
      overflow: auto;
      .requier {
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    &-right {
      width: calc(100% - 304px);
      // border: 1px solid #cfcfcf;

      :deep(.el-tabs) {
        height: 100%;

        .el-tabs__content {
          height: calc(100% - 31px);
          > div {
            height: 100%;
          }
          .el-textarea {
            height: 100%;

            &__inner {
              height: 100%;
              background: #fff;
              border: none;
            }
          }
        }
      }
    }
  }
</style>
