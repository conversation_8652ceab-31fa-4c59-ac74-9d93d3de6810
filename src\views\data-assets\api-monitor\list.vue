<template>
  <!-- 数据服务-API监控日志列表 -->
  <div :class="['api-management-list container-padding16', state.isLzos ? 'isLzos' : '']">
    <div class="page-top-dif">
      <search @handleSearch="handleSearch" @reset="resetFn" />
    </div>
    <div class="page-bottom">
      <div class="list-box-left asideTree">
        <div class="list-tree-title">API分类</div>
        <n-input
          class="list-tree-input"
          v-model="state.treeSearchText"
          placeholder="请输入"
          suffix="search"
          @input="searchTreeFn"
        />
        <n-tree ref="treeRef" :data="state.treeData" @node-click="clickFn">
          <template #content="{ nodeData }">
            <SvgIcon class="tree-icon" v-if="nodeData.children?.length" icon="open-file" />
            <SvgIcon class="tree-icon" v-else icon="icon-file-tree" />
            <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
          </template>
          <template #icon="{ nodeData, toggleNode }">
            <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
            <span
              v-else
              @click="
                (event) => {
                  event.stopPropagation()
                  toggleNode(nodeData)
                }
              "
            >
              <SvgIcon
                v-if="nodeData.expanded"
                class="nancalui-tree-switch"
                icon="tree-contract-new"
              />
              <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch" />
            </span>
          </template>
        </n-tree>
      </div>
      <div class="list-box-right nc-m-l-10" v-loading="state.loading">
        <CfTable
          :actionWidth="104"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.tableData.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              initTable()
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              initTable(true)
            },
          }"
        >
          <template #editor="{ row }">
            <div class="edit-box">
              <n-button
                class="seeDetails has-right-border"
                variant="text"
                @click.prevent="searchTodo(row)"
                >查看实例
              </n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'

  export default {
    title: 'List',
    components: { search },
    props: {},
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      // 获取当前组件实例
      const treeRef = ref()
      const state = reactive({
        treeData: [],
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 436,
        tableData: {
          total: 0,
          pageNum: 1,
          pageSize: 10,
          list: [],
        },
        loading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'apiName', name: 'API名称' },
          { prop: 'modelTable', name: '数据表', width: 300 },
          { prop: 'url', name: '请求URL', width: 400 },
          { prop: 'callTotal', name: '调用次数' },
          { prop: 'latestCallTime', name: '最近调用时间', width: 160 },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        keyword: null,
        categoryId: '',
      })

      const methods = {
        // 获取树列表
        getTreeListFn() {
          api.documentManage.apiCategoryTree().then((res) => {
            let { success, data } = res
            if (success) {
              if (data?.length > 0) {
                state.treeData = [
                  {
                    id: 'ROOT',
                    name: '全部',
                    pid: null,
                    children: data,
                    expanded: true,
                    selected: true,
                  },
                ]

                methods.initTable(true)
              }
            }
          })
        },
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 96 - 150 - 24 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 96 - 150 - 24
          }
        },
        //点击树节点
        clickFn(node) {
          if (node) {
            state.categoryId = node.id
            methods.initTable(true)
          }
        },
        //重置
        resetFn() {
          state.categoryId = ''
          state.keyword = ''
          let node = treeRef.value?.treeFactory.getSelectedNode()
          treeRef.value?.treeFactory.deselectNode(node)
          methods.initTable(true)
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              categoryId: state.categoryId === 'ROOT' ? null : state.categoryId || null,
              name: state.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }

          state.loading = true
          api.documentManage
            .getApiMonitoringList(data)
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                state.tableData = res.data
              }
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看实例
        searchTodo(row) {
          router.push({ name: 'apiMonitorDetail', query: { id: row.id } })
        },
        // 搜索
        handleSearch(data) {
          let { keyword } = data
          state.keyword = keyword ? keyword : null
          state.pagination.currentPage = 1
          methods.initTable()
        },
      }
      onMounted(() => {
        methods.getTreeListFn()
      })
      methods.setTableHeight()
      return {
        state,
        buttonAuthList,
        treeRef,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .page-top-dif {
      background-color: #fff;
    }
    .page-bottom {
      display: flex;
      justify-content: space-between;
      height: calc(100% - 62px);
      .list-box-left {
        box-sizing: border-box;

        min-width: 280px;
        height: 100%;

        padding-top: 10px;
        background-color: #fff;
        border-radius: 2px;
        .list-tree-title {
          margin-bottom: 22px;
          color: #1d2129;
          font-weight: 500;
          font-size: 16px;
          line-height: 18px;
          text-indent: 8px;
          border-left: 4px solid #1e89ff;
          // &:before {
          //   position: absolute;
          //   top: 0;
          //   bottom: 0;
          //   left: 0;
          //   width: 4px;
          //   height: 18px;
          //   margin: auto;
          //   background: #1e89ff;
          //   content: '';
          // }
        }
        .list-tree-input {
          margin: 0 0 8px;
          padding: 0 12px;
        }
        .nancalui-tree {
          height: calc(100% - 92px);
          padding: 0 12px;
          overflow: auto;

          :deep(.nancalui-tree__node .nancalui-tree__node-content) {
            width: 100%;
            padding: 0 8px;
            font-size: 14px;
            &:hover .tree-icon {
              display: inline-block;
            }
          }
          :deep(.nancalui-tree__node .nancalui-tree__node-content--value-wrapper) {
            display: flex;
            width: 100%;
            > svg {
              flex-shrink: 0;
            }
          }
          :deep(.nancalui-tree__node .nancalui-tree__node-content:hover .tree-icon-tool) {
            display: inline-block;
          }
          :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
            font-size: 14px;
            background: #ebf4ff;
          }
          :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
            background: #ebf4ff;
          }
          .tree-icon {
            margin: 0 4px;
            font-size: 16px;
          }
          .tree-label {
            max-width: 140px;
            margin-right: 8px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .list-box-right {
        width: calc(100% - var(--aside-width));
        height: 100%;
        padding-top: 16px;
        background-color: #fff;
        border-radius: 4px;
        :deep(.page-top) {
          display: none;
        }

        .left {
          .nancalui-button {
            background: $themeBlue;
          }
        }

        .seeDetails {
          color: $themeBlue;
        }

        .dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          margin-right: 6px;
          background: #18ba72;
          border-radius: 50px;

          &.red {
            background: #f54446;
          }

          &.blue {
            background: $themeBlue;
          }
        }
      }
    }
  }
</style>
