<template>
  <!-- 数据服务-api管理列表 -->
  <div :class="['container', 'api-management-list', state.isLzos ? 'isLzos' : '']">
    <div class="list-box" v-loading="state.loading">
      <n-public-table
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="200"
        @tablePageChange="tablePageChange"
      >
        <template #taskStatus="{ editor }">
          <span v-if="editor.row.auditStatus === 'CREATED'" class="taskStatus"
            ><span class="circle"></span>已创建</span
          >
          <span v-else-if="editor.row.auditStatus === 'PUBLISH'" class="taskStatus"
            ><span class="circle green"></span>已发布</span
          >
          <span v-else-if="editor.row.auditStatus === 'PUBLISH_AUDITING'" class="taskStatus"
            ><span class="circle yellow"></span>发布审核</span
          >
          <span v-else-if="editor.row.auditStatus === 'PUBLISH_AUDIT_REJECT'" class="taskStatus"
            ><span class="circle red"></span>发布失败</span
          >
          <span v-else-if="editor.row.auditStatus === 'OFFLINE'" class="taskStatus"
            ><span class="circle gray"></span>已下架</span
          >
          <span v-else-if="editor.row.auditStatus === 'OFFLINE_AUDITING'" class="taskStatus"
            ><span class="circle yellow"></span>下架审核</span
          >
          <span v-else-if="editor.row.auditStatus === 'OFFLINE_AUDIT_REJECT'" class="taskStatus"
            ><span class="circle red"></span>下架失败</span
          >
        </template>
        <template #pageTop>
          <div class="box-add">
            <search @handleSearch="handleSearch">
              <template #searchLeft>
                <div class="left">
                  <n-button
                    v-if="buttonAuthList.includes('service_api_add')"
                    code="service_api_add"
                    color="primary"
                    variant="solid"
                    @click.prevent="goJumpAdd"
                  >
                    <!-- <SvgIcon class="icon-add-svg" icon="icon_list_add_n" /> -->
                    新增API
                  </n-button>
                </div>
              </template>
            </search>
          </div>
        </template>

        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="
                buttonAuthList.includes('service_api_off_edit') &&
                (editor.row.auditStatus === 'PUBLISH' ||
                  editor.row.auditStatus === 'OFFLINE_AUDIT_REJECT')
              "
              code="service_api_off_edit"
              variant="text"
              class="seeDetails has-right-border"
              :disabled="disabledButtom('下架', editor.row)"
              @click.prevent="downApi(editor)"
              >下架
            </n-button>
            <n-button
              v-if="
                buttonAuthList.includes('service_api_publish_edit') &&
                !(
                  editor.row.auditStatus === 'PUBLISH' ||
                  editor.row.auditStatus === 'OFFLINE_AUDIT_REJECT'
                )
              "
              code="service_api_publish_edit"
              variant="text"
              :disabled="disabledButtom('发布', editor.row)"
              class="seeDetails has-right-border"
              @click.prevent="publishApi(editor)"
              >发布
            </n-button>
            <n-button
              v-if="buttonAuthList.includes('service_api_test_view')"
              code="service_api_test_view"
              variant="text"
              class="seeDetails has-right-border"
              @click.prevent="testApi(editor)"
              >测试
            </n-button>
            <n-button
              v-if="buttonAuthList.includes('service_api_view')"
              code="service_api_view"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="detailApi(editor)"
              >查看
            </n-button>

            <n-popover
              placement="bottom"
              :width="100"
              :show-arrow="false"
              :offset="4"
              popper-class="btn-more-popover"
              trigger="hover"
            >
              <n-button class="seeDetails has-right-border" variant="text"
                ><SvgIcon class="btn-more" icon="icon-spot" title="更多"
              /></n-button>

              <template #content>
                <n-button
                  v-if="buttonAuthList.includes('service_api_edit')"
                  code="service_api_edit"
                  class="seeDetails has-right-border"
                  variant="text"
                  :disabled="disabledButtom('编辑', editor.row)"
                  @click.prevent="editApi(editor)"
                  >编辑
                </n-button>
                <n-button
                  v-if="buttonAuthList.includes('service_api_delete')"
                  code="service_api_delete"
                  class="seeDetails has-right-border"
                  variant="text"
                  :disabled="disabledButtom('删除', editor.row)"
                  @click.prevent="deleteApi(editor)"
                  >删除
                </n-button>
              </template>
            </n-popover>
          </div>
        </template>
      </n-public-table>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs, getCurrentInstance } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { checkCIp } from '@/utils/validate'

  export default {
    title: 'List',
    components: { search },
    props: {},
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)

      const form = ref('')
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 436,
        loading: false,
        dialogVisible: false,
        authorizeData: {
          apiName: '',
          ips: [
            {
              ip: '',
            },
          ],
        },
        formRules: {
          ip: [{ required: true, validator: checkCIp, trigger: 'blur' }],
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'apiName', name: 'API名称' },
          { prop: 'apiUrl', name: 'API路径' },
          { prop: 'auditStatus', name: '状态', slot: 'taskStatus' },
          { prop: 'updateTime', name: '最后修改时间' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        startTime: null,
        endTime: null,
        keyword: '',
        status: '',
        authorizeId: '',
        tableData: [],
      })

      const methods = {
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 151 - 50 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 151 - 50
          }
        },
        closeDialog() {
          state.dialogVisible = false
          state.authorizeData = {
            apiName: '',
            ips: [{ ip: '' }],
          }
        },

        // CREATED("已创建"),
        //   OFFLINE("已下架"),
        //   PUBLISH("已发布"),
        //   PUBLISH_AUDITING("发布审核中"),
        //   PUBLISH_AUDIT_REJECT("发布审核失败"),
        //   OFFLINE_AUDITING("下架审核中"),
        //   OFFLINE_AUDIT_REJECT("下架审核失败");

        disabledButtom(name, row) {
          let disabled = false
          let abledStatus = []
          let { auditStatus, assetsType } = row
          switch (name) {
            case '查看': //都可查看
              disabled = false
              break
            case '编辑': // 已创建 ，已下架，发布审核失败 -可编辑
            case '删除':
            case '发布':
              // abledStatus = '035'
              abledStatus = ['CREATED', 'OFFLINE', 'PUBLISH_AUDIT_REJECT']
              if (abledStatus.includes(auditStatus)) {
                if (assetsType === 'ALGORITHM') {
                  disabled = true
                } else {
                  disabled = false
                }
              } else {
                disabled = true
              }
              break

            case '下架':
              // abledStatus = '2'
              abledStatus = ['PUBLISH', 'OFFLINE_AUDIT_REJECT']
              if (abledStatus.includes(auditStatus)) {
                if (assetsType === 'ALGORITHM') {
                  disabled = true
                } else {
                  disabled = false
                }
              } else {
                disabled = true
              }
              break
          }
          return disabled
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              auditStatus: state.status || null,
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              apiName: state.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataService['getApiList'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看
        detailApi(editor) {
          let { row } = editor
          router.push({ name: 'detailApi', query: { id: row.id, type: 'DETAIL' } })
        },
        //测试
        testApi(editor) {
          let { row } = editor
          router.push({ name: 'detailApi', query: { id: row.id, type: 'TESTAPI' } })
        },

        // 编辑
        editApi(editor) {
          let { row } = editor
          router.push({ name: 'editApi', query: { id: row.id, type: 'EDIT' } })
        },
        // 删除
        deleteApi(editor) {
          let { row } = editor
          proxy.$MessageBoxService.open({
            title: '是否确认删除API',
            content: '删除后将不可恢复',
            save: () => {
              api.dataService.deleteApi({ id: row.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除成功',
                    type: 'success',
                  })
                  if (state.tableData.length === 1) {
                    methods.initTable(true)
                  } else {
                    methods.initTable()
                  }
                }
              })
            },
          })
        },
        // 发布
        publishApi(editor) {
          let { row } = editor
          proxy.$MessageBoxService.open({
            title: '是否确认发布API',
            content: '发布后可以立即使用',
            save: () => {
              api.dataService.publishApi({ id: row.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '发布请求已成功推送至审核中心',
                    type: 'success',
                  })
                  methods.initTable()
                }
              })
            },
          })
        },
        // 下架
        downApi(editor) {
          let { row } = editor
          proxy.$MessageBoxService.open({
            title: '确认是否下架API服务',
            content: '下架后API服务会关闭，是否发起下架审批',
            save: () => {
              api.dataService.downApi({ id: row.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '下架请求已成功推送至审核中心',
                    type: 'success',
                  })
                  methods.initTable()
                }
              })
            },
          })
        },
        //授权
        // authorize(editor) {
        //   let { row } = editor
        //   state.authorizeId = row.id
        //   state.authorizeData.apiName = row.apiName
        //   api.dataService.getApiAuthInformation({ id: row.id }).then((res) => {
        //     let { success, data } = res
        //     if (success) {
        //       state.dialogVisible = true
        //       let ips = []
        //       if (data.ips) {
        //         data.ips.forEach((item) => {
        //           ips.push({ ip: item })
        //         })
        //         state.authorizeData.ips = ips
        //       }
        //     }
        //   })
        // },
        //保存授权数据
        apiAuth() {
          form.value.validate((valid) => {
            if (valid) {
              let data = {}
              data.id = state.authorizeId

              let ips = state.authorizeData.ips.map((item) => {
                return item.ip
              })
              data.ips = ips
              api.dataService.apiAuth(data).then((res) => {
                let { success } = res
                if (success) {
                  state.dialogVisible = false
                  ElNotification({
                    title: '提示',
                    message: '授权成功',
                    type: 'success',
                  })
                  methods.initTable()
                }
              })
            } else {
              return false
            }
          })
        },
        //新增授权ip
        addParameters() {
          state.authorizeData.ips.push({
            ip: '',
          })
        },
        //移除授权ip
        removeParameters(index) {
          state.authorizeData.ips.splice(index, 1)
        },

        // 搜索
        handleSearch(data) {
          let { time, keyword, status } = data
          state.keyword = keyword ? keyword : null
          state.status = status
          if (status === 'all') {
            state.status = null
          }
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }
          state.pagination.currentPage = 1
          methods.initTable()
        },
        // 新增api
        goJumpAdd() {
          router.push({ name: 'addApi' })
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable(true)
      })

      return {
        buttonAuthList,
        state,
        checkCIp,
        form,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    &.isLzos {
      padding: 0;
    }
    .list-box {
      height: 100%;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 4px;
      .taskStatus {
        .circle {
          display: inline-block;
          width: 6px;
          height: 6px;
          margin-right: 4px;
          background-color: $themeBlue;
          border-radius: 50%;

          &.green {
            background-color: #04c495;
          }

          &.yellow {
            background-color: #ff7d00;
          }
          &.red {
            background-color: #f63838;
          }
          &.gray {
            background-color: #b8b8b8;
          }
        }
      }
      .box-add {
        .commonForm-search {
          padding: 16px 0;
        }
      }

      .left {
        .nancalui-button {
          background: $themeBlue;
        }
      }

      .seeDetails {
        color: $themeBlue;
      }
    }
  }
</style>
