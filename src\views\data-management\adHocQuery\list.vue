<template>
  <section class="container">
    <section class="container-box">
      <section class="container-box-table">
        <section class="cf-tree">
          <div class="row">
            <div class="title"> 即席查询 </div>
            <i class="cf-icon-add" @click="resetFn"> </i>
          </div>
          <div class="cf-tree-container">
            <n-input
              class="table-tree-ipt"
              v-model="state.treeSearchText"
              placeholder="请输入关键词"
              suffix="search"
              @change="(val) => treeRef.treeRef.filter(val)"
            />
            <LocalTree
              ref="treeRef"
              :check-on-click-node="true"
              :filter-node-method="filterNode"
              :props="{
                children: 'children',
                label: 'name',
              }"
              :showBtns="true"
              node-key="id"
              :data="treeList"
              @change="getTreeList"
              @node-click="getDetail"
            />
          </div>
        </section>
        <section class="talble-container">
          <n-form
            ref="formRef"
            style="width: 100%; height: 100%"
            :data="state.syncForm"
            :rules="syncRules"
            label-align="start"
            label-width="96px"
          >
            <section class="table">
              <div class="page-top">
                <!-- 任务名称 -->
                <n-form-item label="任务名称：" field="name">
                  <n-input
                    v-model="state.syncForm.name"
                    size="small"
                    maxlength="30"
                    placeholder="请输入名称"
                    clearable
                    style="width: 100%"
                /></n-form-item>
              </div>
              <!-- 过滤条件 -->
              <div class="work-body">
                <div class="work-body-btn">
                  <div class="btn active" @click.prevent.stop="onSearch()">
                    <SvgIcon class="icon" icon="icon-query" />
                    查询
                  </div>
                  <div v-loading="state.loading" class="work-body-box">
                    <div class="btn" @click.prevent.stop="onAdd">
                      <SvgIcon class="icon" icon="icon-offline-save" />
                      保存
                    </div>
                    <div class="btn" @click.prevent.stop="formatFn">
                      <SvgIcon class="icon" icon="icon-offline-format" />
                      格式化
                    </div>
                    <div class="btn" @click.prevent.stop="checkCode">
                      <SvgIcon class="icon" icon="icon-offline-check" />
                      代码检查
                    </div>
                    <!--      <div class="btn">-->
                    <!--        <SvgIcon class="icon" icon="icon-offline-style" />-->
                    <!--        风格-->
                    <!--      </div>-->
                  </div>
                  <div v-show="state.showSubmit" class="submit">已提交V{{ state.versionNum }}</div>
                  <div class="btn" @click.prevent.stop="resetFn">
                    <SvgIcon class="icon" icon="icon-clear copy" />
                    清空
                  </div>
                </div>
                <div class="layout-box">
                  <div class="work-body-code">
                    <div class="work-body-code-textarea" ref="textareaRef">
                      <codemirror
                        ref="myCm"
                        v-model:value="state.syncForm.querySql"
                        class="codemirror"
                        :options="state.sqlOption"
                        @ready="onCmReady"
                        @focus="onCmFocus"
                        @input="onCmCodeChange"
                      />
                    </div>

                    <div
                      :class="{
                        'work-body-code-run': true,
                        showRun: state.showRun,
                      }"
                      ref="codeRunEl"
                    >
                      <div class="drag-line" ref="el" style="cursor: n-resize; height: 10px"></div>
                      <div
                        v-if="state.showRun"
                        class="drag-line"
                        ref="el"
                        style="cursor: n-resize; height: 10px"
                      ></div>
                      <div class="work-body-code-run-tabs">
                        <n-tabs v-model="state.runType" @active-tab-change="activeTabChange">
                          <n-tab id="log" key="-1" title="运行日志" />
                          <n-tab
                            :id="'result_' + index"
                            v-for="(item, index) in state.tagArr"
                            :key="index"
                          >
                            <template #title>
                              {{ '运行结果' + (index + 1) }}
                              <SvgIcon
                                style="margin-left: 8px"
                                icon="icon-offline-close"
                                @click.prevent="state.tagArr.splice(index, 1)"
                              ></SvgIcon>
                            </template>
                          </n-tab>
                        </n-tabs>
                        <div class="work-body-code-run-tabs-btn">
                          <n-button
                            color="primary"
                            :disabled="state.runType !== 'log' || !state.errorLog"
                            @click.prevent.stop="downLogFn"
                            >下载日志</n-button
                          >
                          <n-button
                            v-loading="state.downLoading"
                            :disabled="
                              !state.tagArr[curTag]?.tableResultData?.list ||
                              state.runType === 'log'
                            "
                            color="primary"
                            @click.prevent.stop="onDownload('CSV')"
                            >导出CSV</n-button
                          >
                          <n-button
                            v-loading="state.downLoading"
                            :disabled="
                              !state.tagArr[curTag]?.tableResultData?.list ||
                              state.runType === 'log'
                            "
                            color="primary"
                            @click.prevent.stop="onDownload('EXCEL')"
                            >导出EXCEL</n-button
                          >
                          <SvgIcon
                            :class="{ icon: true, show: state.showRun }"
                            icon="icon-arrow-second"
                            @click.prevent.stop="showRunFn"
                          />
                        </div>
                      </div>
                      <div v-if="state.showRun" class="work-body-code-run-text">
                        <div class="table-box" v-loading="state.loading">
                          <template v-if="state.runType === 'log'">
                            <div class="error-log">{{ state.errorLog }}</div>
                          </template>
                          <template v-else>
                            <CfTable
                              ref="multipleTableRef"
                              :key="colConfigDrawerRef?.key"
                              :showEmpty="true"
                              :tableConfig="{
                                data: state.tagArr[curTag]?.tableResultData?.list,
                                rowKey: 'id',
                                'cell-style': {
                                  height: '36px',
                                  padding: ' 0',
                                  'background-color': '#FFFFFF',
                                },
                                'header-cell-style': {
                                  'background-color': '#EBF4FF',
                                  padding: '0',
                                  color: ' #1D2129',
                                  'font-size': '14px',
                                  'font-style': 'normal',
                                  'font-weight': '400',
                                },
                              }"
                              indexSortable
                              isNeedIndex
                              :paginationConfig="{
                                total: state.tagArr[curTag]?.total,
                                pageSize: state.tagArr[curTag]?.filterSearch?.pageSize,
                                currentPage: state.tagArr[curTag]?.filterSearch?.pageNum,
                                onCurrentChange: (v) => {
                                  !isNaN(state.tagArr[curTag]?.filterSearch?.pageNum) &&
                                    (state.tagArr[curTag].filterSearch.pageNum = v)
                                  onSearch(state.tagArr[curTag])
                                },
                                onSizeChange: (v) => {
                                  !isNaN(state.tagArr[curTag]?.filterSearch?.pageSize) &&
                                    (state.tagArr[curTag].filterSearch.pageSize = v)
                                  !isNaN(state.tagArr[curTag]?.filterSearch?.pageNum) &&
                                    (state.tagArr[curTag].filterSearch.pageNum = 1)
                                  onSearch(state.tagArr[curTag])
                                },
                              }"
                              :table-head-titles="
                                Object.keys(
                                  state.tagArr[curTag]?.tableResultData?.list?.[0] || {},
                                ).map((_) => ({
                                  name: _,
                                  prop: _,
                                }))
                              "
                            >
                              <template #index="{ index }">
                                {{
                                  index +
                                  1 +
                                  state.filterSearch.pageSize * (state.filterSearch.pageNum - 1)
                                }}
                              </template>
                            </CfTable>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>

                  <ConfigDrawer ref="configDrawerRef" v-model="state.syncForm" />
                </div>
              </div>
            </section>
          </n-form>
        </section>
      </section>
    </section>
  </section>
</template>
<script setup>
  import LocalTree from './components/tree'
  import { formartTime } from '@/utils/index'
  import { getAssetsList } from '@/api/sceneManage'
  import api from '@/api/index'
  import ConfigDrawer from './components/configDrawer'
  import { timestampToTime } from '@/const/public.js'
  import codemirror from 'codemirror-editor-vue3'
  import { checkCName100 } from '@/utils/validate'
  import * as sqlFormatter from 'sql-formatter'
  import { useDraggable } from '@vueuse/core'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  import 'codemirror/mode/python/python.js'
  import 'codemirror/mode/shell/shell.js'
  // import 'codemirror/mode/javascript/javascript.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'
  // 代码校验 lint
  import 'codemirror/addon/lint/lint.js'
  import 'codemirror/addon/lint/lint.css'
  // 拖拽高度
  const textareaRef = ref(null)
  const el = ref(null)
  const codeRunEl = ref(null)
  const myCm = ref('myCm')
  const formRef = ref('formRef')

  const treeRef = ref(null)
  const configDrawerRef = ref(null)

  const { x, y, style } = useDraggable(el, {
    initialValue: { x: 0, y: 0 },
    onMove: (position) => {
      const codeHeight = window.innerHeight - 17 - position.y
      codeRunEl.value.style.height = `${codeHeight}px`
      textareaRef.value.style['margin-bottom'] = `${Math.max(codeHeight, 56)}px`
      if (codeRunEl.value.offsetHeight < 60) {
        state.showRun = false
      } else {
        state.showRun = true
      }
    },
  })
  const state = reactive({
    runType: 'log',
    errorLog: '',
    tagArr: [],
    tableColumns: [],
    tableList: [],
    syncForm: { name: '', querySql: '', tables: '', type: 'ASSETS', datasourceId: -999 }, // 类型(DATASOURCE:数据源;ASSETS:资产库),
    showRun: true,
    loading: false,
    // 数据来源
    filterSearch: {
      pageNum: 1,
      pageSize: 10,
    },
    total: 0,
    tableHeadTitles: [
      {
        name: '创建时间',
        prop: 'createTime',
      },
    ],
    sqlOption: {
      autorefresh: true, // 是否自动刷新
      smartIndent: true, // 自动缩进
      tabSize: 4, // 缩进单元格为 4 个空格
      mode: 'text/x-sql', //编辑器的编程语言
      line: true, // 是否显示行数
      viewportMargin: Infinity, // 高度自适应
      highlightDifferences: true,
      autofocus: false,
      indentUnit: 2,
      readOnly: false, // 只读
      showCursorWhenSelecting: true,
      firstLineNumber: 1,
      matchBrackets: true, //括号匹配
      lineWrapping: true, //是否折叠
      foldGutter: true, // 启用行槽中的代码折叠
      autoCloseBrackets: true, // 自动闭合符号
      styleActiveLine: true, // 显示选中行的样式
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      highlightSelectionMatches: {
        minChars: 2,
        style: 'matchhighlight',
        showToken: true,
      },
      lineNumbers: true, //是否显示左边换行数字
      lint: true, // 打开json校验
    },
  })
  const syncRules = {
    name: [
      {
        required: true,
        validator: (...args) =>
          checkCName100(...args, 'model', 'validModel', {
            nameType: 'CN',
            name: state.syncForm.name,
            id: null,
          }),
        trigger: 'blur',
      },
    ],
  }
  const treeList = ref([])

  const filterNode = (value, data) => {
    if (!value) return true
    return data.name?.includes(value)
  }
  // 格式化
  const formatFn = () => {
    let language = 'sql'
    state.syncForm.querySql = sqlFormatter.format(state.syncForm.querySql, { language })
  }
  let oldSql = ''
  // 搜索
  const onSearch = (tagInfo) => {
    if (!state.syncForm.querySql) {
      ElMessage.warning('请输入SQL')
      return
    }
    state.showRun = true
    codeRunEl.value.style.height = '400px'
    state.loading = true
    const params = Object.assign({}, state.syncForm, tagInfo?.filterSearch || state.filterSearch)
    if (tagInfo) {
      params.querySql = tagInfo.sql
    } else {
      const newSql = state.codemirror.getSelection()
      newSql !== oldSql && (state.filterSearch.pageNum = 1)
      params.querySql = newSql || params.querySql
      oldSql = newSql
    }
    if (!params.querySql) {
      ElNotification({
        title: '提示',
        message: '请添加运行语句！',
        type: 'warning',
      })
      return false
    }
    if (!tagInfo) {
      state.errorLog = ''
    }
    api.dataManagement
      .getAdhocQueryLeftListQuery(params)
      .then((res) => {
        if (!res.success) return
        let errorLog = '运行成功'
        !tagInfo &&
          res.data.forEach((item) => {
            const tag = {
              i: state.tagArr.length,
              sql: item?.querySql,
              loading: true,
              tableResultData: item?.resultData,
              errorLog: item?.errorLog,
              filterSearch: {
                pageNum: item?.resultData?.pageNum || 1,
                pageSize: item?.resultData?.pageSize || 1,
              },
              total: item?.resultData?.total || 0,
            }
            if (item.errorLog && errorLog === '运行成功') {
              errorLog = item?.errorLog
            }
            if (!item.errorLog) {
              state.tagArr.push(tag)
            }
          })
        if (!tagInfo) {
          state.runType = 'log'
          state.errorLog = errorLog
        }
        activeTabChange(state.runType)
        if (tagInfo) {
          Object.assign(state.tagArr[curTag.value], {
            loading: true,
            tableResultData: res.data[0]?.resultData,
            errorLog: res.data[0]?.errorLog,
            filterSearch: {
              pageNum: res.data[0]?.resultData?.pageNum || 1,
              pageSize: res.data[0]?.resultData?.pageSize || 1,
            },
            total: res.data[0]?.resultData?.total || 0,
          })
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
      .finally(() => {
        state.loading = false
      })
  }

  // 保存
  const onAdd = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        const url = isNaN(state.syncForm.id)
          ? 'getAdhocQueryLeftListAdd'
          : 'getAdhocQueryLeftListEdit'
        api.dataManagement[url](state.syncForm).then((res) => {
          if (!res.success) return
          ElMessage.success((isNaN(state.syncForm.id) ? '新增' : '修改') + '成功')
          state.syncForm.id = res.data
          getTreeList()
        })
      }
    })
  }
  // 展示运行结果
  const showRunFn = () => {
    state.showRun = !state.showRun
    if (state.showRun) {
      codeRunEl.value.offsetHeight < 60 && (codeRunEl.value.style.height = '400px')
    } else {
      codeRunEl.value.style.height = '56px'
    }
    textareaRef.value.style['margin-bottom'] = `${Math.max(codeRunEl.value.offsetHeight, 56)}px`
  }
  // 下载查询结果
  const onDownload = (downloadType) => {
    const params = Object.assign({}, state.syncForm, {
      querySql: state.tagArr[curTag.value]?.sql,
      downloadType,
    })
    if (!params.querySql) {
      ElNotification({
        title: '提示',
        message: '请添加运行语句！',
        type: 'warning',
      })
      return false
    }
    api.dataManagement
      .getAdhocQueryLeftListExport(params)
      .then((biob) => {
        const url = window.URL.createObjectURL(biob)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          `${state.syncForm.name}_${new Date().getFullYear()}-${
            new Date().getMonth() + 1
          }-${new Date().getDate()}_${new Date().getHours()}-${new Date().getMinutes()}.${
            { CSV: 'csv', EXCEL: 'xlsx' }[downloadType]
          }`,
        )
        document.body.appendChild(link)
        link.click()
      })
      .finally(() => {
        state.loading = false
      })
  }

  // 代码检查
  const checkCode = () => {
    if (!state.syncForm.querySql) {
      ElMessage.warning('请输入SQL')
      return
    }
    const params = Object.assign({}, state.syncForm, {
      pageNum: 1,
      pageSize: 10,
    })
    api.dataManagement.getAdhocQueryLeftListQuery(params).then((res) => {
      if (!res.success) return
      ElMessage.success('SQL语法正确')
    })
  }

  // SQL语句获取焦点时
  const onCmFocus = (cm) => {}
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
      const config = {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      }
      state.codemirror.showHint(config)
    })
  }
  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.sql = newCode
  }
  const curTag = ref(0)
  const activeTabChange = (i) => {
    curTag.value = parseInt(i?.split('_')?.[1] || 0)
  }

  // 下载日志
  const downLogFn = () => {
    if (state.errorLog) {
      const blob = new Blob([state.errorLog], {
        type: 'text/plain',
      })
      const link = document.createElement('a')
      let fileName =
        state.syncForm.name + timestampToTime(new Date().getTime(), 'MINUTE') + '的日志.log'
      link.download = fileName
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  // 数据准备
  // 获取详情
  const getDetail = (row) => {
    resetFn()
    api.dataManagement.getAdhocQueryLeftListDetail(row.id).then((res) => {
      state.syncForm = res.data || {}
      nextTick(() => {
        configDrawerRef.value?.initFn(res.data)
      })
    })
  }

  // 获取树形结构
  const getTreeList = () => {
    api.dataManagement.getAdhocQueryLeftList().then((res) => {
      treeList.value = res.data || []
    })
  }
  const resetFn = (() => {
    const data = JSON.parse(JSON.stringify(state))
    return () => {
      Object.assign(state, JSON.parse(JSON.stringify(data)))
      configDrawerRef.value?.resetFn()
    }
  })()
  getTreeList()
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .container {
    padding: 16px;
    border-radius: 0;

    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      .row {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
      }

      .title {
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-content: space-between;
        padding: 6px 12px 6px 0px;

        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 24px;

        &::before {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 8px;
          background: #1e89ff;
          content: '';
        }
      }

      .tabs-tools {
        align-items: center;
        align-self: stretch;
        justify-content: space-between;

        .tabs {
          display: flex;
          gap: 8px;
          align-items: center;
          align-self: stretch;
          height: 48px;
          line-height: 48px;

          & + .tabs {
            border-bottom: 1px solid var(---, #dcdfe6);
          }

          > .tab {
            display: flex;
            gap: 4px;
            align-items: center;
            padding: 5px 12px;

            color: #a1a1aa;
            font-weight: 400;
            font-size: 14px;
            font-family: 'PingFang HK';
            font-style: normal;
            line-height: 22px;

            &:nth-child(n + 2) {
              color: #333;
              cursor: pointer;

              &:hover {
                color: #1e89ff;
              }
            }

            &.active {
              color: #1e89ff;
              background: #ecf3ff;
              border-radius: 2px;
            }
          }
        }
      }

      &-table {
        display: flex;
        flex: 1;
        gap: 10px;
        height: calc(100% - 106px);

        .cf-tree {
          padding: 8px 0;

          .cf-tree-container {
            width: 100%;
            height: calc(100% - 36px);

            .tree-box {
              height: calc(100% - 40px);
              margin-top: 8px;
            }

            :deep(.nancalui-input__inner) {
              border-right: 1px solid #e5e6eb;
            }

            .tree-box {
              :deep(.nancalui-input__inner) {
                border-right: none;
              }
            }

            :deep(.icon-search)::before {
              display: block;
              width: 16.504px;
              height: 16px;
              cursor: pointer;
              content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNyIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE3IDE2IiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNMTYuNzc5MyAwSDAuMjc1MzkxVjE2SDE2Ljc3OTNWMFoiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTEyLjEzNjcgMTEuNzkzNUwxNS4yMzEyIDE0Ljc5MzUiIHN0cm9rZT0iIzkwOTM5OSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgPHBhdGggZD0iTTcuNDk1NjIgMTMuMDAxQzEwLjkxMzcgMTMuMDAxIDEzLjY4NDYgMTAuMzE0NyAxMy42ODQ2IDcuMDAwOThDMTMuNjg0NiAzLjY4NzI3IDEwLjkxMzcgMS4wMDA5OCA3LjQ5NTYyIDEuMDAwOThDNC4wNzc1NCAxLjAwMDk4IDEuMzA2NjQgMy42ODcyNyAxLjMwNjY0IDcuMDAwOThDMS4zMDY2NCAxMC4zMTQ3IDQuMDc3NTQgMTMuMDAxIDcuNDk1NjIgMTMuMDAxWiIgc3Ryb2tlPSIjOTA5Mzk5Ii8+Cjwvc3ZnPg==');
            }
          }

          .cf-icon-add {
            display: block;
            width: 16px;
            height: 16px;
            cursor: pointer;

            &::before {
              display: block;
              width: 16px;
              height: 16px;
              content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfNzkyXzE1MTg5NykiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzYwNjI2NiIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjNjA2MjY2IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzYwNjI2NiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF83OTJfMTUxODk3Ij4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
            }

            &:hover {
              &::before {
                content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfODM2XzE1NjIwMCkiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzAwNkRFQSIvPgogICAgPHBhdGggZD0iTTUgOEgxMSIgc3Ryb2tlPSIjMDA2REVBIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICAgIDxwYXRoIGQ9Ik04IDVWMTEiIHN0cm9rZT0iIzAwNkRFQSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPC9nPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwMF84MzZfMTU2MjAwIj4KICAgICAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ3aGl0ZSIvPgogICAgPC9jbGlwUGF0aD4KICA8L2RlZnM+Cjwvc3ZnPg==');
              }
            }
          }
        }

        .talble-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          width: 100%;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0px 0px 2px 2px;
        }
      }
    }
  }
</style>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .table {
    gap: 10px;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background: var(--100, #fff);
    border-radius: 0px 0px 2px 2px;

    .page-top {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 48px;
      align-items: flex-start;
      align-self: stretch;
      height: 48px;
      padding: 8px 16px;
      border-bottom: 1px solid var(--200, #dcdfe6);
    }

    .table-box {
      position: relative;
      height: 100%;
      :deep(.nancalui-pagination) {
        padding-left: 190px;
        .nancalui-pagination__link {
          // 不换行
          white-space: nowrap;
        }
        .total-span {
          white-space: nowrap;
        }
      }
      :deep(.el-table__header-wrapper) {
        // border: 1px solid #606266;
        border-bottom: none;
      }
      :deep(.el-table__body) {
        // border: 1px solid #606266;
        border-top: none;
      }

      .download-btn {
        position: absolute;
        bottom: 16px;
        left: 0;
        display: flex;
        gap: 4px;
        align-items: center;
      }

      .error-log {
        display: flex;
        flex: 1 0 0;
        flex-direction: column;
        gap: 6px;
        height: 100%;
        padding: 16px;
        overflow: auto;
        white-space: pre-wrap;
        background-color: #fff;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }
    }
  }
</style>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .layout-box {
    display: flex;
    height: calc(100% - 46px);
    overflow: hidden;
  }

  .work-body {
    position: relative;
    width: 100%;
    height: calc(100% - 48px);
    overflow: hidden;

    .btn {
      display: flex;
      gap: 4px;
      align-items: center;
      box-sizing: border-box;
      padding: 4px 16px;
      color: #1e89ff;
      font-size: 14px;
      border: 1px solid var(---, #1e89ff);
      border-radius: 2px;

      .icon {
        font-size: 16px;
      }

      &.active,
      &:hover {
        color: #fff;
        background: #1e89ff;
      }
    }

    &-box {
      display: flex;
      flex: 1;
      gap: 8px;
      align-items: center;
    }

    &-btn {
      position: relative;
      display: flex;
      flex-shrink: 0;
      gap: 8px;
      align-items: center;
      align-self: stretch;
      height: 46px;
      padding: 14px 8px;
      border-bottom: 1px solid var(---, #dcdfe6);

      .submit {
        position: absolute;
        top: 0;
        right: 48px;
        bottom: 0;
        box-sizing: border-box;
        width: 58px;
        height: 20px;
        margin: auto;
        color: #00c700;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background: #f4ffeb;
        border: 1px solid #a8ed83;
        border-radius: 2px;
      }
    }

    &-code {
      position: relative;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding-bottom: 1px;
      overflow: hidden;
      color: #1f2329;
      background: #f5f6f7;
      border: none;

      &-config {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        align-self: stretch;
        width: 48px;
        height: 100%;
        padding: 12px 8px;
        background: #fafafa;
        border-left: 1px solid #dcdfe6;

        &-btn {
          display: flex;
          gap: 8px;
          align-items: center;
          justify-content: center;
          width: 32px;
          padding: 12px 4px;
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: center;
          background: linear-gradient(181deg, #f3f3f5 0.86%, #fff 11.07%);
          border: 1px solid #c9cdd4;
          border-radius: 2px;
          cursor: pointer;
        }
      }

      &-textarea {
        flex: 1;
        width: 100%;
        overflow: hidden;
        margin-bottom: 56px;

        // height: 100%;
        .codemirror {
          height: 100% !important;
          background-color: #fff;

          :deep(.CodeMirror) {
            height: 100% !important;
            overflow: hidden;
            box-shadow: none;
          }

          :deep(.CodeMirror-scroll) {
            box-sizing: border-box;
            height: 100%;
            margin-right: -6px;
            padding: 0;
            overflow-x: hidden !important;
            overflow-y: auto !important;

            .CodeMirror-sizer {
              border-right: none;

              .CodeMirror-lines {
                padding: 0;
                // .CodeMirror-selected {
                //   height: 28px !important;
                // }
              }

              .CodeMirror-cursors {
                top: 5px;
              }

              .CodeMirror-code > div {
                padding: 5px 0;
              }

              .CodeMirror-linenumber {
                padding: 0 6px;
                text-align: center;
              }

              .CodeMirror-line {
                padding: 0 10px;

                > span {
                  padding-right: 10px !important;
                  color: #046c5c;
                  font-size: 14px;
                  word-break: break-all;
                }
              }

              .CodeMirror-linebackground {
                background-color: #f0f2f5;
              }
            }
          }

          :deep(.CodeMirror-gutters) {
            width: 32px;
            min-height: 100%;
            background-color: #e6e8eb;
            border-right: none;
          }

          :deep(.CodeMirror-vscrollbar) {
            visibility: initial !important;

            &::-webkit-scrollbar-thumb {
              background-color: #b1bcd6;
              border-radius: 6px;

              &:hover {
                background-color: #b1bcd6;
              }
            }
          }
        }
      }

      &-run {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2001;
        box-sizing: border-box;
        width: 100%;
        height: 56px;
        min-height: 56px;
        max-height: calc(100% - 20px);
        background-color: #fff;
        &.showRun {
          height: 400px;
        }

        .drag-line {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          z-index: 2002;
        }

        &-tabs {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          padding: 0 16px;
          padding-top: 8px;
          background: linear-gradient(180deg, #f3f3f5 0%, #fff 100%);

          :deep(.nancalui-tabs) {
            width: calc(100% - 240px);
            height: 40px;
            .nancalui-tabs-nav-tab {
              height: 40px;
              border-bottom: none;
            }
            .nancalui-tabs-tab {
              box-sizing: border-box;
              margin-left: 0;
              padding: 0 24px;
              color: #1d2129;
              background-color: #f0f2f5;
              border: 1px solid #e5e6eb;
              border-left: none;
              &:first-of-type {
                border-left: 1px solid #e5e6eb;
              }
              &.nancalui-tabs-tab-active {
                color: #1e89ff;
                background-color: #fff;
                border-bottom: none;
              }
            }
            .nancalui-tabs-nav-ink {
              display: none;
            }
          }

          &-btn {
            display: flex;
            align-items: center;
            .icon {
              margin-left: 10px;
              font-size: 16px;

              &.show {
                transform: rotate(180deg);
              }
            }
          }
        }

        .title {
          display: flex;
          align-items: center;
          align-self: stretch;
          justify-content: flex-start;
          height: 48px;
          padding: 16px 8px 8px 0px;
        }

        &-text {
          box-sizing: border-box;
          height: calc(100% - 48px);
          padding: 0 16px;
          overflow: hidden;
          color: #1d2129;
          font-size: 14px;
        }
      }
    }
  }
</style>
