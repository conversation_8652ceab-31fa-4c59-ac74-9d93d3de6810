:root {
  --sideBarBackColor: linear-gradient(90deg, #132a53 0%, #16315d 100%);
  --sideBarMenuBackColor: rgba(255, 255, 255, 0.08);
  --sideBarMenuSubBackColor: #152f5a;
  --sideBarMenuColor: rgba(255, 255, 255, 0.55);
  --sideBarMenuSelectColor: #ffffff;
  --sideBarMenuSelectBackColor: #447dfd;
  --sideBarMenuHoverBackColor: rgba(255, 255, 255, 0.1);
  --sideBarHideSelectBackColor: #447dfd;
  --menu-item-height: 44px;
  --menu-vertical-item-height: 40px;

  --headerBackColor: #ffffff;
  --headerFontColor: #697a9a;
  --headerFontSelectColor: #447dfd;
  --headerMenuSelectColor: linear-gradient(
    180deg,
    rgba(24, 160, 251, 0.4) 0%,
    rgba(53, 206, 253, 0.16) 100%
  );
  --buttonPrimaryColor: #447dfd;
  --themeBlue: #1E89FF;
  --themeBlueHover: #6e9eff;
  --normallBtnBorderHover: #6e9eff;
  --normallBtnBgHover: #e3ecff;
  --normallBtnTextHover: #6e9eff;
  --themeContentBg: #f0f2f5;
  --nancalui-danger-line: #f66f6a;
  --nancalui-input-default-border: #a3b4db; //输入框默认边框颜色
  --nancalui-split-line-border: #c5d0ea; //分割线颜色
}
