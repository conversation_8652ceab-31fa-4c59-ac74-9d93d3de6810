<template>
  <n-tree ref="treeDomRef" :data="dataSource" key="id">
    <template #content="{ nodeData, index }">
      <div class="docs-div-row">
        <div class="div-col root-col">
          <n-input
            v-model="editableData[nodeData.id]['filedName']"
            @keyup="
              editableData[nodeData.id]['filedName'] = editableData[nodeData.id][
                'filedName'
              ].replace(/[\u4e00-\u9fa5]/gi, '')
            "
            :maxlength="50"
            :disabled="editableData[nodeData.id]['disabled'] || props.disabled"
            placeholder="参数名"
          />
        </div>
        <div class="div-col">
          <n-select
            v-model="editableData[nodeData.id]['type']"
            :disabled="props.disabled"
            @value-change="typeChange(nodeData.id, editableData[nodeData.id]['type'])"
            :options="typeOptions"
            placeholder="请选择类型"
            show-arrow
        /></div>
        <div class="div-col">
          <n-input
            v-model="editableData[nodeData.id]['description']"
            :disabled="props.disabled"
            placeholder="备注"
            :maxlength="50"
        /></div>

        <div v-if="!props.disabled" class="div-col fixed-col">
          <SvgIcon
            class="pointer ml-9"
            v-if="canAdd(editableData[nodeData.id]['filedName'], editableData[nodeData.id]['type'])"
            icon="add-circle"
            @click.stop.prevent="onItemAdd(nodeData.id)"
          />
          <SvgIcon
            class="pointer ml-9"
            v-if="
              editableData[nodeData.id]['filedName'] !== 'root' &&
              !editableData[nodeData.id]['notOperation']
            "
            icon="icon-del"
            @click.stop.prevent="onItemDel(nodeData.id)"
          />
        </div>
      </div>
    </template>
    <template #icon="{ nodeData, toggleNode }">
      <span
        @click="
          (event) => {
            event.stopPropagation()
            toggleNode(nodeData)
          }
        "
      >
        <svg
          :style="{
            transform: nodeData.expanded ? 'rotate(90deg)' : '',
            marginLeft: '-2.5px',
            marginRight: '14.5px',
            cursor: 'pointer',
          }"
          viewBox="0 0 1024 1024"
          width="12"
          height="12"
        >
          <path
            d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
            fill="#8a8e99"
          />
        </svg>
      </span>
    </template>
  </n-tree>
</template>
<script setup>
  import { reactive, ref, defineExpose, defineProps } from 'vue'
  const props = defineProps(['data', 'disabled'])
  import { generateGuid } from '@/utils/tools'

  const columns = [
    {
      name: '参数名',
      prop: 'filedName',
      slot: 'filedName',
      width: '31%',
    },
    {
      name: '类型',
      prop: 'type',
      slot: 'type',
      width: '31%',
    },
    {
      name: '备注',
      prop: 'description',
      slot: 'description',
      width: '31%',
    },
  ]

  const dataSource = reactive([])
  const typeOptions = [
    {
      value: 'string',
      name: 'string',
    },
    {
      value: 'number',
      name: 'number',
    },
    {
      value: 'integer',
      name: 'integer',
    },
    {
      value: 'array',
      name: 'array',
    },
    {
      value: 'object',
      name: 'object',
    },
    {
      value: 'boolean',
      name: 'boolean',
    },
    {
      value: 'double',
      name: 'double',
    },
    {
      value: 'date',
      name: 'date',
    },
    {
      value: 'datetime',
      name: 'datetime',
    },
    {
      value: 'char',
      name: 'char',
    },
  ]
  const editableData = reactive({})
  const getDeepData = (data) => {
    data.forEach((item) => {
      if (!editableData[item.id]) {
        editableData[item.id] = { ...item }
      }
      if (item.children?.length) {
        expandedRowKeys.push(item.id)
        getDeepData(item.children)
      }
    })
  }
  const tableRef = ref(null)
  const expandedRowKeys = reactive([])
  function onExpand(expanded, record) {
    if (expanded) {
      if (record.id) {
        expandedRowKeys.push(record.id)
      }
    } else {
      const index = expandedRowKeys.findIndex((key) => key === record.id)
      if (index > -1) {
        expandedRowKeys.splice(index, 1)
      }
    }
  }
  function canAdd(filedName, type) {
    if (type !== 'object') {
      return false
    } else {
      return true
    }
  }
  function typeChange(id, value) {
    if (value === 'array') {
      const child = {
        id: generateGuid(),
        filedName: 'items',
        type: 'string',
        description: '',
        disabled: true,
        notOperation: true,
        expanded:true,
        children: [],
      }
      appendChild(dataSource, id, child)
      expandedRowKeys.push(id)
      getDeepData(dataSource)
    } else {
      setTimeout(() => {
        // 如果从数组换为其他类型，删除items子类
        const someItem = getRowData(dataSource[0], id)
        if (someItem?.children?.length) {
          const hasItem = someItem.children.some((i) => i.filedName === 'items')
          if (hasItem) {
            someItem.children = []
          }
        }
      }, 200)
    }
  }
  // 根据id查询表格行数据
  function getRowData(data, id) {
    if (data.id === id) {
      return data
    } else if (data?.children?.length) {
      for (let d of data.children) {
        return getRowData(d, id)
      }
    }
  }
  // 添加子节点
  function onItemAdd(id) {
    appendChild(dataSource, id)
    expandedRowKeys.push(id)
    getDeepData(dataSource)
  }
  function appendChild(data, id, values) {
    const someItem = data?.find((i) => i.id === id)
    if (someItem) {
      if (values) {
        someItem.children?.push(values)
      } else {
        someItem.children?.push({
          id: generateGuid(),
          filedName: '',
          type: 'string',
          description: '',
          expanded: true,
          children: [],
        })
      }
      return null
    } else {
      for (let d of data)
        if (d.children) {
          appendChild(d.children, id, values)
        }
    }
  }
  function removeChild(data, id) {
    try {
      const index = data?.findIndex((i) => i.id === id)
      if (index > -1) {
        data.splice(index, 1)
        return null
      } else {
        for (let d of data)
          if (d.children) {
            removeChild(d.children, id)
          }
      }
    } catch (e) {
      console.log(e)
    }
  }
  function onItemDel(id) {
    removeChild(dataSource, id)
    const index = expandedRowKeys.findIndex((key) => key === id)
    if (index > -1) {
      expandedRowKeys.splice(index, 1)
    }
    getDeepData(dataSource)
  }
  function calcData(arr) {
    arr.forEach((a, index) => {
      if (editableData[a.id]) {
        a.filedName = editableData[a.id].filedName
        a.type = editableData[a.id].type
        a.description = editableData[a.id].description
        a.sort = index + 1
      }
      if (a.children?.length) {
        calcData(a.children)
      }
    })
  }
  function finshData() {
    calcData(dataSource)
    return dataSource
  }
  watch(
    () => props.data,
    () => {
      if (props.data) {
        dataSource.length = 0
        dataSource.push(...props.data)
        // 将树性表格设置为对象，方便编辑
        getDeepData(dataSource)
      }
    },
    {
      deep: true,
      immediate: true,
    },
  )
  defineExpose({ dataSource, finshData })
</script>
<style lang="scss" scoped>
  .docs-div-row {
    position: absolute;
    right: 0;
    left: 14px;
    z-index: 99;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-left: 20px;
    .div-col {
      position: relative;
      height: 32px;
      line-height: 32px;
      margin-right: 5px;
      .nancalui-input {
        display: inline-block;
        width: 100%;
        min-width: 100px;

        :deep(.nancalui-input__wrapper) {
          border: 1px solid #ffffff;

          &.nancalui-input--focus {
            border: 1px solid #0069ff;
          }

          .nancalui-input__inner {
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
          }
        }
      }
      :deep(.nancalui-select) {
        width: 100%;
        .nancalui-select__selection {
          box-sizing: border-box;
          height: 32px;

          .nancalui-select__input {
            height: 100%;
          }
        }
      }

      // .btn-icon {
      //   margin-left: 8px !important;
      //   font-size: 16px;
      //   &:first-child {
      //     margin-left: 0 !important;
      //   }
      //   &.disabled {
      //     color: #b8b8b8;
      //     cursor: no-drop;
      //   }
      // }

      &:first-child {
        flex: 1;
      }

      &:nth-child(2) {
        width: 100px;
      }

      &:nth-child(3) {
        width: 100px;
      }

      &.fixed-col {
        width: 80px;
        .pointer {
          font-size: 16px;
        }
      }
    }
  }
  :deep(.nancalui-tree__node) {
    height: 40px;
    border-bottom: 1px solid #eee;

    .nancalui-tree__node-vline,
    .nancalui-tree__node-hline {
      display: none;
    }

    &:not(.active):hover {
      background-color: #ecf7ff;
    }

    .nancalui-tree__node-content {
      svg {
        margin-left: 8px !important;
      }

      .nancalui-progress__circle {
        height: 16px;
        margin-left: 0 !important;
      }
    }

    .nancalui-tree__node-content:not(.active):hover,
    .nancalui-tree__node-content.active {
      background-color: transparent;
      transition: none;
    }

    .nancalui-tree__node-content--value-wrapper {
      width: 100%;
      height: 40px !important;
    }
  }
</style>
