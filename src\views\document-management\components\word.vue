<template>
  <div class="word-box">
    <div id="docBox"></div>
  </div>
</template>
<script setup>
  import { renderAsync } from 'docx-preview'
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
  function previewDocx(buffer) {
    let _options = {
      className: 'docx', // 默认和非结构化数据样式类的类名/前缀
      inWrapper: true, // 启用围绕非结构化数据内容渲染包装器
      ignoreWidth: false, // 禁止页面渲染宽度
      ignoreHeight: false, // 禁止页面渲染高度
      ignoreFonts: false, // 禁止字体渲染
      breakPages: true, // 在分页符上启用分页
      ignoreLastRenderedPageBreak: true, //禁用lastRenderedPageBreak元素的分页
      experimental: false, //启用实验性功能（制表符停止计算）
      trimXmlDeclaration: true, //如果为真，xml声明将在解析之前从xml非结构化数据中删除
      debug: false, // 启用额外的日志记录
    }
    renderAsync(buffer, document.getElementById('docBox'), null, _options)
  }
  // 获取文件流
  function getBlob(url) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(xhr.response)
        }
      }
      xhr.send()
    })
  }
  // 获取文件流
  function init(docUrl) {
    getBlob(docUrl).then((blob) => {
      let reader = new FileReader()
      reader.readAsArrayBuffer(blob, 'utf-8') //blob转ArrayBuffer数据类型
      reader.onload = function () {
        previewDocx(reader.result)
      }
    })
  }
  onMounted(() => {
    init(option.value.value)
  })
</script>
<style lang="scss" scoped>
  .word-box {
    width: 100%;
    height: 100%;
    overflow: auto;
    :deep(.docx-wrapper) {
      background: none !important;
      .docx {
        width: 100%;
        padding: 0;
        box-shadow: none;
      }
    }
  }
</style>
