<template>
  <!-- 按钮操作 -->
  <div class="btn-group">
    <div class="btn active" v-if="!state.loading" @click="save('run')">
      <SvgIcon class="icon" icon="icon-offline-start" />
      运行
    </div>
    <div v-if="state.loading" class="btn" @click="stopFunc">
      <SvgIcon class="icon" icon="icon-offline-stop" />
      停止
    </div>
    <div v-loading="state.loading || state.hasClickSave" class="btn" @click="save('save')">
      <SvgIcon class="icon" icon="icon-offline-save" />
      保存
    </div>
    <div v-loading="state.loading" class="btn" @click="save('commit')">
      <SvgIcon class="icon" icon="icon-offline-submit" />
      提交
    </div>
  </div>

  <!-- 画布 -->
  <div v-loading="state.loading" class="canvas">
    <!-- 侧边栏 -->
    <div class="canvas-sidebar" @click="state.attrConfigShow = false">
      <template v-for="(item, index) in state.asideBarArr" :key="item.name">
        <div class="title">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path d="M8 11L4.25 6H11.75L8 11Z" fill="#9199AC" />
          </svg>
          <span>{{ item.name }}</span>
        </div>
        <div class="canvas-sidebar-box">
          <div
            class="canvas-sidebar-node"
            @click="menuDrag($event, node)"
            v-for="node in item.content"
            :key="node"
          >
            <img v-if="node.name !== 'PySpark'" :src="getImageSrc(node.name)" alt="" />
            <img
              v-else
              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIaSURBVHgB7ZQxb9NAFMffndNwkZA4RhbkfgLSjQGJFMUSA6DkE+Bs7RRnYu4EW5oFKpbSjY2UgoSEUNJPkI4w1UztVmdo0zj2Xd8p71qnaqUqydChf+knn+/evffuPZ8B7kSKXwj/pHwvuDzPYE6Ky6IDTBfTEVsqdE9DXZLyeOHYzcEcpZVT4wuqE3uFaAQDNw88nDnAoCxKjmYVBaov/py0Y0/IGJK9PHO+pRyqMItMWYae2B9hkIl5r9AbeMI3Y05zFWQf6SAu3FTMaTDs4sEQ3uJb0U5rrbe5HvuxTTbOu0gf+YLsXePSbIqIrExi28iDf8/ybFHw59hwmf89XLI9CJESskzjJmXkUsA1cmKzNO9tpIdsIRJ5YtYf3+dVleiIASuaL8lmYDYekRMf2cxkfERzPXLk0omDjJ2x0ZkEzsVpkzlyiwxMPXdp3a5VqAQRnVCS7S6NJe2vXw7gIF+Rd3BRX4G8RB4i76kEh7T5KfKIbIfIL7I38zXkA/If+WsD5Cg7Hy7qbY7foKzMpi7ZhjDuUzdzipCSWqPnMlxRpqxcGDdvbpr6X9T6tGMa29cMelrn8D6M6sHKm8b6xo/NYOVVzdpxmFYMZH31tYsXzQWlJGZaaX78HnBQE2Yz/YvWN3Y6eG0l8DhSmm9xzpupThbnFkDrpBasVkMzbn7+2YY0LTXo3Wr6EoFqTThTia/GX+GdbpnOALxaskMiHanHAAAAAElFTkSuQmCC"
              alt=""
            />
            <span>{{ node.name }}</span>
          </div>
        </div>
        <div v-if="state.asideBarArr.length - 1 !== index" class="line"></div>
      </template>
    </div>
    <!-- 画布区域 -->
    <div ref="canvasGraphRef" id="flowContainer" class="canvasGraph" @click="drawClick"></div>
    <div id="minimapContainer"></div>

    <!-- 配置区域 -->
    <div class="config-area">
      <div class="config-area-btn" @click="showAttrConfig('attr')">属性配置</div>
      <div class="config-area-btn" @click="showAttrConfig('schedule')">调度配置</div>
    </div>

    <div class="hint">
      <n-popover
        :position="['right-end']"
        align="end"
        trigger="hover"
        style="background-color: rgba(0, 0, 0, 1); color: #fff"
      >
        <template #content>
          <div>
            * 作业间的依赖不可形成环路<br />
            * 业务流视图只显示已建立依赖的作业<br />
            * 选中连线后，按DEL键可删除连线
          </div>
        </template>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <g clip-path="url(#clip0_923_243823)">
            <path
              d="M0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8C15.5 12.1421 12.1421 15.5 8 15.5C3.85786 15.5 0.5 12.1421 0.5 8ZM8.75 4.75C8.75 4.33579 8.41421 4 8 4C7.58579 4 7.25 4.33579 7.25 4.75C7.25 5.16421 7.58579 5.5 8 5.5C8.41421 5.5 8.75 5.16421 8.75 4.75ZM8.2889 6.59183C8.13836 6.48533 7.94143 6.47032 7.7765 6.55279L6.7765 7.05279C6.52951 7.17628 6.4294 7.47662 6.5529 7.72361C6.67639 7.9706 6.97673 8.07071 7.22372 7.94722L7.38809 7.86503L7.00397 10.938C6.98267 11.1084 7.05031 11.2778 7.18312 11.3867C7.31592 11.4955 7.49531 11.5286 7.65822 11.4743L9.15822 10.9743C9.42019 10.887 9.56178 10.6039 9.47445 10.3419C9.38713 10.0799 9.10397 9.93834 8.842 10.0257L8.09465 10.2748L8.49625 7.06202C8.51912 6.87905 8.43943 6.69834 8.2889 6.59183Z"
              fill="#8091B7"
            />
          </g>
          <defs>
            <clipPath id="clip0_923_243823">
              <rect width="16" height="16" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </n-popover>
    </div>
  </div>

  <!-- 属性配置弹窗 -->
  <section v-if="state.attrConfigShow" class="attrConfigPop">
    <div class="title">
      <span class="line"></span>
      <span class="name">{{ state.attrConfigType === 'attr' ? '属性配置' : '调度配置' }}</span>
    </div>

    <n-form :data="state.formData" ref="formRef" label-width="130px" message-type="text">
      <template v-if="state.attrConfigType === 'attr'">
        <n-form-item field="name" label="业务流程：">
          <n-input v-model="state.formData.name" placeholder="工作流1" />
        </n-form-item>

        <n-form-item field="personInChargeName" label="责任人：">
          <n-input disabled v-model="state.formData.personInChargeName" placeholder="责任人" />
        </n-form-item>

        <n-form-item field="alarm" label="告警通知：">
          <n-switch v-model="state.formData.alarm" />
        </n-form-item>

        <n-form-item field="description" label="描述信息：">
          <n-textarea
            v-model="state.formData.description"
            :autosize="{ minRows: 4, maxRows: 8 }"
            resize="both"
            maxlength="200"
            placeholder="请输入内容信息"
          />
        </n-form-item>
      </template>

      <template v-else>
        <n-form-item field="preTask" label="前置任务：">
          <n-switch v-model="state.formData.preTask" />
        </n-form-item>

        <!-- 前置任务设置 -->
        <template v-if="state.formData.preTask">
          <n-form-item field="preTask" label="采集失败重跑触发：">
            <n-switch v-model="state.formData.collectFailRestart" />
          </n-form-item>
          <!--          <n-form-item field="preTask" label="采集任务：">-->
          <!--            <n-select-->
          <!--              v-model="state.formData.collectTask"-->
          <!--              @value-change="addCollectList"-->
          <!--              :allow-clear="true"-->
          <!--              filter-->
          <!--            >-->
          <!--              <n-option-->
          <!--                v-for="(item, index) in state.collectTaskArr"-->
          <!--                :key="index"-->
          <!--                :value="item.id"-->
          <!--                :name="item.name"-->
          <!--              />-->
          <!--            </n-select>-->
          <!--          </n-form-item>-->
          <div
            class="table-box"
            :style="
              'height:' +
              (state.formData.collectJobIds.length > 0
                ? state.formData.collectJobIds.length * 36 + 36
                : 300) +
              'px'
            "
          >
            <CfTable
              :key="state.key"
              :isDisplayAction="false"
              :table-head-titles="[
                { prop: 'name', name: '采集任务名称' },
                { prop: 'scheduleText', name: '采集周期', width: 120 },
              ]"
              :tableConfig="{
                data: state.formData.collectJobIds,
                rowKey: 'id',
              }"
            />
          </div>
          <n-form-item label="最新采集时间：">
            <div class="form-item-text">{{ state.nextScheduleTime || '--' }}</div>
          </n-form-item>
          <div class="tips">*此时间仅做参考，实际开始时间以采集完成为准</div>
        </template>

        <!-- 非前置任务设置 -->
        <template v-else>
          <n-form-item field="configureTimes" label="采集规则：">
            <n-radio-group direction="row" v-model="state.formData.configureTimes">
              <n-radio value="time">配置时间</n-radio>
              <n-radio value="cron">cron表达式</n-radio>
            </n-radio-group>
          </n-form-item>

          <!-- 配置时间 -->
          <template v-if="state.formData.configureTimes === 'time'">
            <n-form-item field="period" label="调度周期：">
              <n-radio-group direction="row" v-model="state.formData.period">
                <n-radio
                  v-for="item in state.periodOptions"
                  :key="item.value"
                  :value="item.value"
                  @change="periodChange"
                  >{{ item.label }}</n-radio
                >
              </n-radio-group>
            </n-form-item>

            <n-form-item
              v-if="state.formData.period == 'week' || state.formData.period == 'month'"
              class="time-box"
              label=" "
              field="extent"
            >
              <div class="timeBox">
                <n-select
                  v-if="state.formData.period === 'week'"
                  v-model="state.formData.extent"
                  class="timeBox-child"
                  placeholder="请选择周几"
                  filter
                  @value-change="
                    () => {
                      state.key++
                    }
                  "
                >
                  <n-option
                    v-for="item in state.weekList"
                    :key="item.value"
                    :name="item.label"
                    :value="item.value"
                  />
                </n-select>
                <n-select
                  v-else-if="state.formData.period === 'month'"
                  v-model="state.formData.extent"
                  class="timeBox-child"
                  placeholder="请选择几号"
                  filter
                  @value-change="
                    () => {
                      state.key++
                    }
                  "
                >
                  <n-option
                    v-for="item in state.monthList"
                    :key="item.value"
                    :name="item.label"
                    :value="item.value"
                  />
                </n-select>
                <n-time-picker
                  v-model="state.formData.rateTime"
                  placeholder="00:00:00"
                  class="timeBox-child"
                />
              </div>
            </n-form-item>
            <n-form-item
              v-if="state.formData.period === 'day'"
              class="time-box"
              label=" "
              field="rateTime"
            >
              <n-time-picker
                v-model="state.formData.rateTime"
                placeholder="00:00:00"
                style="width: 100%"
              />
            </n-form-item>
          </template>

          <!-- cron表达式 -->
          <template v-else>
            <n-form-item field="cron" label=" ">
              <n-input v-model="state.formData.cron" placeholder="请输入" />
            </n-form-item>
          </template>

          <n-form-item field="resstRun" label="失败重试：">
            <n-switch v-model="state.formData.resstRun" />
          </n-form-item>

          <n-form-item v-if="state.formData.resstRun" field="resstRun" label=" ">
            <div class="resetRun">
              <n-input-number
                v-model="state.formData.failRetryTimes"
                :min="0"
                :key="key"
                :precision="0"
                :hideButton="true"
                placeholder="请输入重试次数"
                @blur="blurFn('failRetryTimes')"
              >
                <template #prepend>重试次数</template>
                <template #append>次</template>
              </n-input-number>
              <n-input-number
                v-model="state.formData.failRetryInterval"
                :min="0"
                :key="key"
                :precision="0"
                :hideButton="true"
                placeholder="请输入重试间隔"
                @blur="blurFn('failRetryInterval')"
              >
                <template #prepend>重试间隔</template>
                <template #append>分</template>
              </n-input-number>
            </div>
          </n-form-item>

          <n-form-item field="effectiveDate" label="生效日期：">
            <n-range-date-picker-pro
              v-model="state.formData.effectiveDate"
              :placeholder="['开始日期', '结束日期']"
              :showTime="true"
              format="YYYY-MM-DD HH:mm:ss"
              class="effectiveDate"
              @toggleChange="datePickerToggleFn"
            />
          </n-form-item>
        </template>
      </template>
    </n-form>
  </section>

  <CreateOfflineWorking
    ref="workingDialogRef"
    :key="$attrs.treeList?.[0].children"
    :treeList="$attrs.treeList"
    @success="createNode"
  />

  <n-modal
    v-model="state.showSubmitModal"
    title="业务流程提交"
    class="largeDialog has-top-padding"
    width="960px"
    :close-on-click-overlay="false"
    @close="closeDialog"
    style="z-index: 2000"
  >
    <div class="modal-body">
      <div class="modal-body-name"
        >您将提交【{{ state?.tableData[0]?.name }}】至生产环境，包含【{{
          state?.tableData[0]?.taskList?.length || '0'
        }}】个离线作业，提交后将发起审批流程，请确认是否继续？</div
      >
      <div class="modal-body-table">
        <n-table
          ref="tableRef"
          :data="state.tableData"
          row-key="name"
          :expand-row-keys="state.expandKey"
          @expand-change="expandChange"
        >
          <n-column resizeable type="expand">
            <template #default="rowData">
              <div class="table-expand-child">
                <n-checkbox-group
                  v-model="state.commitTaskIds"
                  :beforeChange="
                    (isChecked, label) => {
                      return label !== 'DELETED'
                    }
                  "
                >
                  <div class="table-expand-child-row header">
                    <div class="table-expand-child-row-col first">
                      <n-checkbox label="" value="all" @change="allChecked" />
                    </div>
                    <div class="table-expand-child-row-col">作业名称</div>
                    <div class="table-expand-child-row-col">责任人</div>
                    <div class="table-expand-child-row-col">调度类型</div>
                    <div class="table-expand-child-row-col">状态</div>
                    <div class="table-expand-child-row-col date">更新时间</div>
                    <div class="table-expand-child-row-col">提交人</div>
                    <div class="table-expand-child-row-col">提交版本</div>
                  </div>
                  <template v-if="rowData.row.taskList">
                    <div
                      v-for="item in rowData.row.taskList"
                      :key="item"
                      class="table-expand-child-row"
                    >
                      <div class="table-expand-child-row-col first">
                        <n-checkbox
                          label=""
                          :disabled="item.operateType === 'DELETED'"
                          :value="item.id"
                          @change="singleChecked(item.id)"
                        />
                      </div>
                      <div class="table-expand-child-row-col" :title="item.name">{{
                        item.name
                      }}</div>
                      <div class="table-expand-child-row-col">{{ item.personInChargeName }}</div>
                      <div class="table-expand-child-row-col">{{ item.scheduleTypeName }}</div>
                      <div class="table-expand-child-row-col">{{
                        operateType[item.operateType] || '--'
                      }}</div>
                      <div class="table-expand-child-row-col date">{{ item.updateTime }}</div>
                      <div class="table-expand-child-row-col">{{ rowData.row.createByName }}</div>
                      <div class="table-expand-child-row-col">{{ 'V' + item.version }}</div>
                    </div>
                  </template>
                  <div v-else class="table-expand-child-empty">
                    <img class="table-expand-child-empty-pic" src="@/assets/img/empty_gray.png" />
                    <div class="table-expand-child-empty-text">暂无数据</div>
                  </div>
                </n-checkbox-group>
              </div>
            </template>
          </n-column>
          <n-column resizeable field="name" header="业务流程" />
          <n-column resizeable field="personInChargeName" header="责任人" />
          <n-column resizeable field="scheduleText" header="调度频率">
            <template #default="rowData">
              <div :title="rowData.row.scheduleText">{{ rowData.row.scheduleText }}</div>
            </template>
          </n-column>
          <n-column resizeable field="updateTime" header="更新时间" width="160px" />
          <n-column resizeable field="createByName" header="提交人" />
          <n-column resizeable field="version" header="提交版本" />
        </n-table>
      </div>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="state.showSubmitModal = false">取消</n-button>
        <n-button variant="solid" @click.prevent="submitFn">确定</n-button>
      </div>
    </template>
  </n-modal>

  <TeleportContainer />
</template>

<script setup>
  import { ref, reactive, toRefs, defineEmits, onMounted, onBeforeUnmount } from 'vue'
  import FlowGraph from './../graph'
  import CreateOfflineWorking from './createOfflineWorking.vue'
  import {
    offlineworkSave,
    offlineworkDetail,
    colletTaskList,
    workFlowRun,
    workFlowStop,
    workFlowCommit,
    getOfficialInfo,
    taskRunState,
    workFlowProcessTask,
  } from '@/api/dataManage.js'
  import { changeFrequencyChinese } from '@/utils/index.js'
  import { useTeleport } from '@antv/x6-vue-shape'
  import { ElNotification } from 'element-plus'
  const UNIQ_GRAPH_ID = 'UNIQ_GRAPH_ID'

  const TeleportContainer = useTeleport(UNIQ_GRAPH_ID)
  const emits = defineEmits(['openLabelFn', 'changeTree', 'changeLoadingStatus'])
  const props = defineProps({
    currentId: {
      type: String,
      default: window.iframeGetCurrentId,
    },
    processId: {
      type: String,
      default: window.iframeGetProcessId,
    },
  })

  const canvasGraphRef = ref()
  const workingDialogRef = ref(null)
  let runStatus = ref('waiting')

  /**
   * 数据部分
   */
  const state = reactive({
    key: 0,
    commitTaskIds: [],
    resizeFn: null,
    showSubmitModal: false,
    loading: false,
    tableData: [],
    expandKey: [],
    e: null,
    currentNode: null,
    // 侧边栏节点树
    asideBarArr: [
      {
        name: '数据集成',
        content: [
          { name: '非结构化数据分析', shape: 'upload', nodeCode: 'OW_DATA_UPLOAD' },
          { name: '同步至资源库', shape: 'dataAsync', nodeCode: 'OW_DATA_ASYNC' },
        ],
      },
      {
        name: '数据开发',
        content: [
          { name: 'PySpark', shape: 'PySpark', nodeCode: 'OW_PY_SPARK' },
          { name: 'HiveSQL', shape: 'HiveSQL', nodeCode: 'OW_HIVE_SQL' },
          { name: 'DorisSQL', shape: 'DorisSQL', nodeCode: 'OW_DORIS_SQL' },
          { name: 'SparkSQL', shape: 'SparkSQL', nodeCode: 'OW_SPARK_SQL' },
        ],
      },
      {
        name: '通用作业',
        content: [
          { name: 'Python', shape: 'Python', nodeCode: 'OW_PYTHON' },
          { name: 'Shell', shape: 'Shell', nodeCode: 'OW_SHELL' },
        ],
      },
      {
        name: '建表作业',
        content: [
          { name: 'HiveDDL', shape: 'HiveDDL', nodeCode: 'OW_HIVE_DDL' },
          { name: 'DorisDDL', shape: 'DorisDDL', nodeCode: 'OW_DORIS_DDL' },
        ],
      },
    ],
    collectTaskArr: [],
    formData: {
      name: '',
      personInCharge: '',
      personInChargeName: '',
      alarm: true,
      description: '',
      collectFailRestart: true,

      preTask: true,
      collectTask: '',
      collectJobIds: [],
      configureTimes: 'time',
      period: 'day',
      extent: '',
      rateTime: '00:10:00',
      resstRun: true,
      cron: '',
      failRetryInterval: 0,
      failRetryTimes: 0,
      effectiveDate: [
        new Date().setHours(0, 0, 0, 0),
        new Date('2099-12-31').setHours(23, 59, 59, 999),
      ], //有效时间区间
    },
    nextScheduleTime: '',
    attrConfigShow: false,
    attrConfigType: 'attr',
    attrConfigHistoryType: '',
    periodOptions: [
      // {
      //   label: '小时',
      //   value: 'HOUR',
      // },
      {
        label: '日',
        value: 'day',
      },
      {
        label: '周',
        value: 'week',
      },
      {
        label: '月',
        value: 'month',
      },
    ],
    weekList: [
      { label: '周一', value: '1' },
      { label: '周二', value: '2' },
      { label: '周三', value: '3' },
      { label: '周四', value: '4' },
      { label: '周五', value: '5' },
      { label: '周六', value: '6' },
      { label: '周日', value: '7' },
    ],
    monthList: [],
    stateFlag: null,
    hasClickSave: false,
  })

  const operateType = {
    NEW: '新增',
    UPDATE: '变更',
    DELETED: '删除',
    NO_UPDATE: '未变更',
  }

  // 离线作业类型枚举
  const offlineJobTypeList = {
    OW_PY_SPARK: 'PySpark',
    OW_HIVE_SQL: 'HiveSQL',
    OW_SPARK_SQL: 'SparkSQL',
    OW_PYTHON: 'Python',
    OW_SHELL: 'Shell',
    OW_HIVE_DDL: 'HiveDDL',
    OW_DATA_ASYNC: 'dataAsync',
    OW_DATA_UPLOAD: 'upload',
    OW_DORIS_DDL: 'DorisDDL',
    OW_DORIS_SQL: 'DorisSQL',
  }

  watch(
    runStatus,
    (newValue) => {
      let data = {
        name: state.formData.name,
        runStatus: newValue,
      }
      emits('changeLoadingStatus', data)
      window?.parent?.iframeChangeLoadingStatus(data)
    },
    { deep: true },
  )

  onMounted(async () => {
    initDate()
    initGraph()
    await getCollectTaskListFunc()

    if (
      JSON.parse(sessionStorage.getItem('workCacheData')) &&
      Object.keys(JSON.parse(sessionStorage.getItem('workCacheData'))).indexOf(
        JSON.stringify(props.currentId),
      ) > -1
    ) {
      let {
        key,
        resizeFn,
        showSubmitModal,
        tableData,
        expandKey,
        e,
        currentNode,
        collectTaskArr,
        formData,
        attrConfigShow,
        attrConfigType,
        attrConfigHistoryType,
        periodOptions,
        nextScheduleTime,
        weekList,
        monthList,
        graphCells,
        stateFlag,
      } = JSON.parse(sessionStorage.getItem('workCacheData'))[props.currentId]
      state.key = key
      state.resizeFn = resizeFn
      state.showSubmitModal = showSubmitModal
      state.tableData = tableData
      state.expandKey = expandKey
      state.e = e
      state.currentNode = currentNode
      state.collectTaskArr = collectTaskArr
      state.formData = formData
      state.attrConfigShow = attrConfigShow
      state.attrConfigType = attrConfigType
      state.attrConfigHistoryType = attrConfigHistoryType
      state.periodOptions = periodOptions
      state.weekList = weekList
      state.monthList = monthList
      state.nextScheduleTime = nextScheduleTime
      state.stateFlag = stateFlag

      const { graph } = FlowGraph
      graph.fromJSON(graphCells)
    } else {
      await detailData(props.currentId)
    }

    // 这里再获取 pollingSession
    let pollingSession = sessionStorage.getItem('workFlowPollingSession_' + props.processId)
    if (pollingSession) {
      let sessionData = JSON.parse(pollingSession)
      if (sessionData.runStatus === 'RUNNING_EXECUTION' && sessionData.runInstanceId) {
        runStateFunc(sessionData.runInstanceId)
      } else if (sessionData.runStatus === 'SAVING') {
        state.loading = true
        runStatus.value = 'running'
        save('run')
      }
    }
  })
  onBeforeUnmount(() => {
    if (state.stateFlag) {
      clearTimeout(state.stateFlag)
      state.stateFlag = null
    }
    window.removeEventListener('resize', state.resizeFn)
  })

  const closeDialog = () => {
    state.showSubmitModal = false
    return false
  }

  const allChecked = () => {
    if (state.commitTaskIds.includes('all')) {
      state.commitTaskIds = ['all']
      state.tableData[0].taskList.forEach((item) => {
        state.commitTaskIds.push(item.id)
      })
    } else {
      state.commitTaskIds = []
      state.tableData[0].taskList.forEach((item) => {
        if (item.operateType === 'DELETED') {
          state.commitTaskIds.push(item.id)
        }
      })
    }
  }

  const singleChecked = (label) => {
    if (
      !state.commitTaskIds.includes('all') &&
      state.tableData[0].taskList.length === state.commitTaskIds.length
    ) {
      state.commitTaskIds.push('all')
    } else if (
      state.commitTaskIds.includes('all') &&
      state.tableData[0].taskList.length >= state.commitTaskIds.length
    ) {
      state.commitTaskIds = state.commitTaskIds.filter((item) => item !== 'all')
    }
  }

  // 任务展开
  const expandChange = (item) => {
    if (!item.expand) {
      state.expandKey.push(item.name)
    } else {
      state.expandKey = state.expandKey.filter((val) => val !== item.name)
    }
    item.expand = !item.expand
    // if (!item.child) {
    //   api.dataQuality
    //       .qualityReportTableRule({
    //         tableId: item.tableId,
    //         ratingSystem: state.formInline.ratingSystem,
    //         startTime: state.formInline.startTime,
    //         endTime: state.formInline.endTime,
    //       })
    //       .then((res) => {
    //         let { success, data } = res
    //         if (success) {
    //           state.tableData.forEach((val) => {
    //             if (item.name === val.name) {
    //               val.child = [...data]
    //             }
    //           })
    //         }
    //       })
    // }
  }
  // 切换调度周期
  const periodChange = () => {
    state.formData.extent = ''
  }

  // 获取工作流前置任务
  const workFlowProcessTaskFn = async () => {
    const { graph } = FlowGraph
    let nodes = graph.getNodes()
    const taskIds = []

    nodes.forEach((item) => {
      if (item.data.taskType === 'OW_HIVE_SQL' || item.data.taskType === 'OW_SPARK_SQL') {
        taskIds.push(item.data.id)
      } else if (item.data.jobType === 'OW_HIVE_SQL' || item.data.jobType === 'OW_SPARK_SQL') {
        taskIds.push(item.data.taskId)
      }
    })

    const res = await workFlowProcessTask({ taskIds })

    const { collectJobsInfo } = res.data

    let nextScheduleTime = ''
    if (collectJobsInfo?.length > 0) {
      nextScheduleTime = collectJobsInfo[0].nextScheduleTime
      collectJobsInfo.forEach((val) => {
        val.scheduleText = changeTextFn(val?.schedulingConfigInfo?.appointedTime) || '--'
      })
    }
    state.nextScheduleTime = nextScheduleTime

    state.formData.collectJobIds = collectJobsInfo || []
  }

  // init日期
  const initDate = () => {
    let monthList = []
    for (let i = 1; i <= 31; i++) {
      monthList.push({
        label: i + '号',
        value: i.toString(),
      })
    }
    state.monthList = monthList
  }

  // 初始化画布
  const initGraph = () => {
    const graph = FlowGraph.init()

    const resizeFn = () => {
      if (canvasGraphRef.value) {
        const { width, height } = getContainerSize()
        graph.resize(width, height)
      }
    }
    resizeFn()
    window.addEventListener('resize', resizeFn)
    state.resizeFn = resizeFn
    graph.on('node:dblclick', ({ cell }) => {
      let data = { ...cell.getData() }
      emits('openLabelFn', data)
      window?.parent?.iframeGetOpenLabelFn(data)
    })

    graph.on('cell:added', ({ cell }) => {
      workFlowProcessTaskFn()
    })
    graph.on('cell:removed', ({ cell }) => {
      workFlowProcessTaskFn()
    })
  }

  const getContainerSize = () => {
    return {
      width: canvasGraphRef.value.offsetWidth,
      height: canvasGraphRef.value.offsetHeight,
    }
  }

  // 获取采集任务
  const getCollectTaskListFunc = async () => {
    state.loading = true
    const res = await colletTaskList()
    state.collectTaskArr = res.data
  }

  // 添加采集任务
  const addCollectList = (data) => {
    const isVal = state.formData.collectJobIds.filter((item) => item.id === data.value)
    if (isVal.length > 0) {
      return false
    }
    const node = state.collectTaskArr.filter((item) => item.id === data.value)

    state.formData.collectJobIds.push(node[0])
  }

  // 删除采集任务
  const delCollectTaskClick = (row) => {
    const newArr = []
    for (let item of state.formData.collectJobIds) {
      if (!Object.is(item, row)) {
        newArr.push(item)
      }
    }

    state.formData.collectJobIds = newArr
  }

  // 拖拽节点
  const menuDrag = (e, item) => {
    workingDialogRef.value.open(
      { id: props.currentId },
      { workFlowId: props.processId, node: item },
    )
    state.e = e
    state.currentNode = item
  }

  // 获取图片地址
  const getImageSrc = (url) => {
    return new URL(`/src/assets/img/offlineDev/${url}.png`, import.meta.url).href //本地文件路径
  }

  const blurFn = (name) => {
    if (!state.formData[name]) {
      state.formData[name] = 0
    }
  }

  const datePickerToggleFn = () => {
    nextTick(() => {
      document.querySelectorAll('.nancalui-flexible-overlay').forEach((parent) => {
        if (parent.querySelector('.nancalui-date-picker-pro__panel')) {
          parent.classList.add('has-nancalui-date-picker-pro')
        }
      })
    })
  }

  // 显示属性配置和调度配置
  const showAttrConfig = (type) => {
    state.attrConfigShow = state.attrConfigHistoryType === type ? !state.attrConfigShow : true

    state.attrConfigHistoryType = type
    state.attrConfigType = type

    if (type === 'schedule' && state.attrConfigShow) {
      workFlowProcessTaskFn()
    }
  }

  // 运行
  const runFunc = () => {
    state.loading = true
    runStatus.value = 'running'
    workFlowRun({
      id: props.processId,
    })
      .then((res) => {
        // 保存成功后清除保存session，开始运行
        sessionStorage.removeItem('workFlowPollingSession_' + props.processId)
        if (res.success) {
          ElNotification({
            title: '提示',
            message: '开始运行',
            type: 'success',
          })
          runStateFunc(res.data)
        } else {
          runStatus.value = 'fail'
        }
        state.loading = false
      })
      .catch(() => {
        // 保存成功后清除保存session，开始运行
        sessionStorage.removeItem('workFlowPollingSession_' + props.processId)
        state.loading = false
        runStatus.value = 'fail'
      })
  }

  // 停止
  const stopFunc = () => {
    workFlowStop({
      id: props.processId,
    }).then((res) => {
      if (res.success) {
        ElNotification({
          title: '提示',
          message: '停止成功',
          type: 'success',
        })
      }
      state.loading = false
    })
  }

  // 获取运行状态
  const runStateFunc = (runData) => {
    const { graph } = FlowGraph
    taskRunState(runData)
      .then((res) => {
        if (res.success) {
          if (state.stateFlag) {
            clearTimeout(state.stateFlag)
            state.stateFlag = null
          }
          let nodes = graph.getNodes()
          nodes.forEach((item) => {
            let data = item.getData()
            let state = 'WAITING_TO_RUN'
            res.data?.taskStateList?.forEach((val) => {
              if (data.nodeCode === val.nodeCode) {
                state = val.state
              }
            })
            item.setData({ ...item.getData(), showState: true, state })
          })
          switch (res.data.state) {
            case 'FAILURE':
              ElNotification({
                title: '提示',
                message: '运行失败',
                type: 'error',
              })
              runStatus.value = 'fail'
              sessionStorage.removeItem('workFlowPollingSession_' + props.processId)
              break
            case 'RUNNING_EXECUTION':
              runStatus.value = 'running'
              // 存储轮询状态
              sessionStorage.setItem(
                'workFlowPollingSession_' + props.processId,
                JSON.stringify({
                  runStatus: 'RUNNING_EXECUTION',
                  runInstanceId: res.data.runInstanceId,
                }),
              )
              state.stateFlag = setTimeout(() => {
                runStateFunc(res.data)
              }, 3000)
              break
            case 'SUCCESS':
              runStatus.value = 'success'
              sessionStorage.removeItem('workFlowPollingSession_' + props.processId)
              break
            default:
              break
          }
        } else {
          runStatus.value = 'fail'
        }
      })
      .catch(() => {
        runStatus.value = 'fail'
      })
  }
  // 提交
  const commitFunc = () => {
    getOfficialInfo({ processId: props.processId }).then((res) => {
      if (res.success) {
        if (res.data.schedule) {
          res.data.scheduleText = changeFrequencyChinese(res.data) || '--'
        } else if (res.data?.collectJobsInfo?.length > 0) {
          let names = res.data.collectJobsInfo.map((val) => val.name)
          res.data.scheduleText = '前置任务 | ' + names.toString()
        } else {
          res.data.scheduleText = '--'
        }
        res.data.version = '--'
        state.tableData = [{ ...res.data }]
        state.commitTaskIds = ['all']
        state.expandKey = []
        state.tableData[0].taskList?.forEach((item) => {
          // if (item.operateType === 'DELETED') {
          //   state.commitTaskIds.push(item.id)
          // }
          state.commitTaskIds.push(item.id)
          state.expandKey.push(item.name)
        })
        state.showSubmitModal = true
        nextTick(() => {
          allChecked()
        })
      }
    })
  }

  let isCommit = true
  // 确认提交
  const submitFn = async () => {
    if (!isCommit) {
      return false
    }
    isCommit = false
    if (state.commitTaskIds.length === 0) {
      ElNotification({
        title: '提示',
        message: '请选择要提交的数据',
        type: 'warning',
      })
      return false
    }

    let commitTaskIds = state.commitTaskIds.filter((item) => item !== 'all')
    const res = await workFlowCommit({
      id: props.processId,
      taskIds: commitTaskIds,
    })
    if (res.code === 'SUCCESS') {
      isCommit = true
      ElNotification({
        title: '提示',
        message: '提交成功',
        type: 'success',
      })
    } else {
      isCommit = true
    }
    state.showSubmitModal = false
    state.commitTaskIds = [...commitTaskIds]
  }

  // 保存工作流
  const save = async (runState) => {
    const { graph } = FlowGraph
    const allEdges = graph.getCells()
    const location = []
    const relation = []
    let nodes = []
    let edges = []

    allEdges.forEach((edge) => {
      edge && edge.isEdge() ? edges.push(edge) : nodes.push(edge)
    })

    nodes.forEach((item) => {
      let flag = false
      edges.forEach((edge) => {
        if (String(item.id) === String(edge.source.cell)) {
          flag = true
        }
      })

      if (graph.getConnectedEdges(item).length === 0) {
        flag = true
      }

      if (flag) {
        relation.push({
          postNodeCode: Number(item.getData().nodeCode),
          preNodeCode: 0,
        })
      }
    })

    allEdges.forEach((edge) => {
      if (edge && edge.isEdge()) {
        const sourceNode = edge.getSourceCell()
        const targetNode = edge.getTargetCell()

        sourceNode.id = sourceNode.getData().nodeCode
        targetNode.id = targetNode.getData().nodeCode

        relation?.push({
          postNodeCode: targetNode.getData().nodeCode,
          preNodeCode: sourceNode.getData().nodeCode,
        })
      } else {
        const pos = edge.getPosition()
        const x = pos.x
        const y = pos.y
        location.push({
          nodeCode: edge.getData().nodeCode,
          x,
          y,
        })
      }
    })

    function uniqueArray(arr) {
      const seen = new Set()
      return arr.filter((item) => {
        const key = `${item.postNodeCode}-${item.preNodeCode}`
        if (!seen.has(key)) {
          seen.add(key)
          return true
        }
        return false
      })
    }

    const unique = uniqueArray(relation)

    const {
      name,
      personInCharge,
      alarm,
      description,
      collectFailRestart,

      failRetryInterval,
      collectJobIds,
      failRetryTimes,
      configureTimes,
      cron,
      effectiveDate,
      period,
      rateTime,
      extent,
    } = state.formData

    const jsonData = graph.toJSON()

    // 过滤出有连线的节点
    const connectedNodes = jsonData.cells.filter((cell) => {
      if (cell.shape !== 'edge') {
        // 检查节点是否有入边或出边
        return jsonData.cells.some((otherCell) => {
          return (
            otherCell.shape === 'edge' &&
            (otherCell.source.cell === cell.id || otherCell.target.cell === cell.id)
          )
        })
      }
      return false
    })

    // 构建新的 JSON 数据只包含有连线的节点和相关的边
    // const frontEndStyle = {
    //   cells: connectedNodes.concat(
    //     jsonData.cells.filter(
    //       (cell) =>
    //         cell.shape === 'edge' &&
    //         connectedNodes.some(
    //           (node) => node.id === cell.source.cell || node.id === cell.target.cell,
    //         ),
    //     ),
    //   ),
    // }
    const frontEndStyle = jsonData.cells

    let collectJobIdsArr = collectJobIds.map((item) => item.id)

    const params = {
      basicInfo: {
        alarm: alarm ? 'YES' : 'NO',
        description,
        name,
        personInCharge,
        collectFailRestart: collectFailRestart ? 1 : 0,
      },
      dagInfo: {
        location,
        relation: unique,
        frontEndStyle: JSON.stringify(frontEndStyle),
      },
      treeId: props.currentId,
      scheduleInfo: {
        collectJobIds: collectJobIdsArr, //前置采集任务ID列表
        failRetryInterval, //失败重试间隔
        failRetryTimes, //失败重试次数
        schedule: {
          cron: configureTimes === 'time' ? '' : cron,
          isCron: cron === '' ? false : true,
          extent: extent,
          fromDateTime: effectiveDate[0] ? formartTime(effectiveDate[0]) : '',
          thruDateTime: effectiveDate[1] ? formartTime(effectiveDate[1]) : '',
          period: period,
          rateTime: rateTime,
          // schedule: true,
        },
        scheduleConfig: state.formData.preTask ? 'COLLECT_JOB' : 'SCHEDULE',
      },
    }
    if (state.hasClickSave) {
      return false
    }
    state.hasClickSave = true

    // 如果是运行操作，在保存前先存储session
    if (runState === 'run') {
      sessionStorage.setItem(
        'workFlowPollingSession_' + props.processId,
        JSON.stringify({
          runStatus: 'SAVING',
          processId: props.processId,
        }),
      )
    }

    const res = await offlineworkSave(params)
    state.hasClickSave = false
    if (res.code === 'SUCCESS') {
      switch (runState) {
        case 'save':
          ElNotification({
            title: '提示',
            message: '保存成功',
            type: 'success',
          })
          break
        case 'run':
          runFunc()
          break
        case 'commit':
          commitFunc()
          break
        default:
          break
      }
    } else {
      // 保存失败时也要清除session
      if (runState === 'run') {
        sessionStorage.removeItem('workFlowPollingSession_' + props.processId)
      }
    }
  }

  const formartTime = (t) => {
    const now = typeof t === 'number' ? new Date(t) : t

    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  const drawClick = () => {
    state.attrConfigShow = false
  }

  // 字体宽度计算
  const getTextWidth = (text, font = '14px Source Han Sans CN') => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx.font = font // 必须与实际字体完全一致[3,7](@ref)
    return ctx.measureText(text).width
  }

  const createNode = (item) => {
    emits('changeTree')
    window?.parent?.iframeChangeTree()
    const { graph, dnd } = FlowGraph
    const shape = offlineJobTypeList[item.jobType]
    if (!shape || shape === 'upload' || shape === 'HiveDDL' || shape === 'DorisDDL') {
      return false
    }

    let fontWidth = getTextWidth(item.name) + 74

    let data = {
      width: fontWidth > 374 ? 374 : fontWidth,
      height: 34,
      interacting: true,
      shape,
      data: item,
    }

    let node = graph.createNode(data)
    dnd.start(node, state.e)
  }
  let dsProcessCode
  // 工作流详情查询
  const detailData = async (treeId) => {
    const { graph } = FlowGraph
    state.loading = true
    const res = await offlineworkDetail({ treeId })
    state.loading = false
    if (res.code === 'SUCCESS') {
      const {
        name,
        personInChargeName,
        personInCharge,
        description,
        collectFailRestart,
        alarm,
        collectJobsInfo,
        failRetryInterval,
        failRetryTimes,
        schedule,
        frontEndStyle,
        taskDef,
        relation,
        scheduleConfig,
      } = res.data
      dsProcessCode = res.data.dsProcessCode

      let nextScheduleTime = ''
      if (collectJobsInfo?.length > 0) {
        nextScheduleTime = collectJobsInfo[0].nextScheduleTime
        collectJobsInfo.forEach((val) => {
          val.scheduleText = changeTextFn(val?.schedulingConfigInfo?.appointedTime) || '--'
        })
      }
      state.nextScheduleTime = nextScheduleTime
      state.formData = {
        name,
        personInChargeName,
        personInCharge,
        alarm: alarm === 'YES' ? true : false,
        description,
        collectFailRestart: collectFailRestart === 1 ? true : false,
        preTask: scheduleConfig === 'COLLECT_JOB' ? true : false,
        collectTask: '',
        collectJobIds: collectJobsInfo ? collectJobsInfo : [],
        configureTimes: schedule?.isCron ? 'cron' : 'time',
        period: schedule?.period ? schedule?.period : 'day',
        extent: schedule?.extent ? schedule?.extent : '',
        rateTime: schedule?.rateTime ? schedule?.rateTime : '00:10:00',
        resstRun: true,
        cron: schedule?.isCron ? schedule.cron : '',
        failRetryInterval: failRetryInterval ? failRetryInterval : 0,
        failRetryTimes: failRetryTimes ? failRetryTimes : 0,
        //有效时间区间
        effectiveDate: [
          schedule?.startDate
            ? new Date(schedule?.startDate).getTime()
            : new Date().setHours(0, 0, 0, 0),
          schedule?.endDate
            ? new Date(schedule?.endDate).getTime()
            : new Date('2099-12-31').setHours(23, 59, 59, 999),
        ],
      }

      const cells = []

      const jsonArr = JSON.parse(frontEndStyle)
      let cellsArr = []
      if (Array.isArray(jsonArr)) {
        cellsArr = jsonArr
      } else {
        cellsArr = jsonArr?.cell || []
      }
      cellsArr?.forEach((cell) => {
        if (cell.shape === 'edge') {
          const sourceCell = cellsArr.filter((item) => cell.source.cell === item.id)[0]
          const targetCell = cellsArr.filter((item) => cell.target.cell === item.id)[0]

          if (sourceCell && sourceCell.data && sourceCell.data.nodeCode) {
            cell.source.cell = sourceCell.data.nodeCode.toString()
          } else {
            console.warn('Source cell not found or missing nodeCode:', cell.source.cell)
            return // 跳过这个边，不添加到cells中
          }

          if (targetCell && targetCell.data && targetCell.data.nodeCode) {
            cell.target.cell = targetCell.data.nodeCode.toString()
          } else {
            console.warn('Target cell not found or missing nodeCode:', cell.target.cell)
            return // 跳过这个边，不添加到cells中
          }

          cells.push(cell)
        }
      })

      cellsArr?.forEach((cell) => {
        if (cell.shape !== 'edge') {
          delete cell.data.icon
          const matchedCell = cellsArr.filter((item) => cell.id === item.id)[0]

          if (matchedCell && matchedCell.data && matchedCell.data.nodeCode) {
            cell.id = matchedCell.data.nodeCode.toString()
            let fontWidth = getTextWidth(cell.data.name) + 74
            cell.size.width = fontWidth > 374 ? 374 : fontWidth
            cells.push(cell)
          } else {
            console.warn('Cell not found or missing nodeCode:', cell.id)
          }
        }
      })

      if (taskDef?.length > 0) {
        taskDef.forEach((item, index) => {
          let fontWidth = getTextWidth(item.name) + 74
          cells.push({
            position: {
              x: item.location ? item.location.x : 20,
              y: item.location ? item.location.y : index === 0 ? 20 : index * 100 + 20,
            },
            size: { width: fontWidth > 374 ? 374 : fontWidth, height: 34 },
            shape: offlineJobTypeList[item.taskType],
            interacting: true,
            data: { ...item, showState: false, state: 'WAITING_TO_RUN' },
            id: item.nodeCode.toString(),
            zIndex: 1,
          })
        })

        relation.forEach((item) => {
          if (item.preNodeCode !== 0 && item.postNodeCode !== 0) {
            cells.push({
              shape: 'edge',
              attrs: {
                line: {
                  stroke: '#8091B7',
                  strokeWidth: 1,
                  targetMarker: {
                    name: 'classic',
                    size: 8,
                  },
                },
              },
              connector: 'normal',
              // router: {
              //   name: 'normal',
              // },
              zIndex: 0,
              source: { cell: JSON.stringify(item.preNodeCode), port: 'bottom' },
              target: { cell: JSON.stringify(item.postNodeCode), port: 'top' },
            })
          }
        })
      }

      graph.fromJSON(cells)
    }
  }
  // 英文转周期
  const changeTextFn = (period) => {
    switch (period) {
      case 'HOUR':
        return `小时`
        break
      case 'DAY':
        return `天`
        break
      case 'WEEK':
        return `周`
        break
      case 'MONTH':
        return `月`
        break
      default:
        return '__'
        break
    }
  }

  defineExpose({
    state,
    FlowGraph,
    ...toRefs(state),
  })

  window.iframeGetState = () => {
    return state
  }
  window.iframeGetFlowGraph = () => {
    return FlowGraph
  }
  window.iframeCreateNode = (e, item) => {
    const { graph, dnd } = FlowGraph
    const shape = offlineJobTypeList[item.jobType]
    if (!shape || shape === 'upload' || shape === 'HiveDDL' || shape === 'DorisDDL') {
      return false
    }
    let fontWidth = getTextWidth(item.name) + 74
    let data = {
      width: fontWidth > 374 ? 374 : fontWidth,
      height: 34,
      interacting: true,
      shape: shape,
      data: item,
    }

    let node = graph.createNode(data)
    dnd.start(node, e)
  }
</script>
<style scoped lang="scss">
  .btn-group {
    display: flex;
    flex-shrink: 0;
    gap: 16px;
    align-items: center;
    align-self: stretch;
    height: 46px;
    padding: 14px 8px;
    border-bottom: 1px solid var(---, #dcdfe6);

    .btn {
      display: flex;
      gap: 4px;
      align-items: center;
      padding: 4px 16px;
      color: #1e89ff;
      font-size: 14px;
      border: 1px solid var(---, #1e89ff);
      border-radius: 2px;

      &.active,
      &:hover {
        color: #fff;
        background: #1e89ff;
      }
    }
  }
  .canvas {
    position: relative;
    display: flex;
    width: 100%;
    height: calc(100% - 46px);
    :deep(.el-loading-mask) {
      .el-loading-spinner {
        .circular {
          width: 32px !important;
          height: 32px !important;
        }
      }
    }
    &-sidebar {
      display: flex;
      flex-direction: column;
      align-items: center;
      align-self: stretch;
      width: 200px;
      height: 100%;
      padding: 8px 0px;
      border-right: 1px solid #eceff5;

      .title {
        display: flex;
        gap: 4px;
        align-items: center;
        align-self: stretch;
        height: 32px;
        padding: 5px 12px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        cursor: pointer;
      }

      &-box {
        width: 100%;
        padding: 0 12px;
      }

      &-node {
        display: flex;
        flex: 1 0 0;
        gap: 8px;
        align-items: center;
        margin-bottom: 8px;
        padding: 8px 6px 8px 10px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        white-space: nowrap;
        text-overflow: ellipsis;
        background: #fff;
        border: 1px solid #e5e6eb;
        border-radius: 2px;
        cursor: pointer;
        user-select: none;
      }
      .line {
        width: 100%;
        height: 1px;
        background: #eceff5;
      }
    }

    .canvasGraph {
      flex: 1;
      height: 100%;
    }

    #minimapContainer {
      position: absolute;
      right: 73px;
      bottom: 25px;
      box-sizing: border-box;
      width: 250px;
      height: 140px;
      overflow: hidden;
      background-color: #fff;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      box-shadow: 4px 4px 16px -2px rgba(0, 0, 0, 0.1);
      transition: all linear 0.3s;

      :deep(.x6-widget-minimap) {
        right: 0;
        bottom: 0;
        .x6-graph {
          box-shadow: none;
        }
      }
    }

    .config-area {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
      align-self: stretch;
      width: 48px;
      height: 100%;
      padding: 12px 8px;
      background: #fafafa;
      border-left: 1px solid #dcdfe6;

      &-btn {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        width: 32px;
        padding: 12px 4px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        text-align: center;
        background: #fff;
        border: 1px solid #c9cdd4;
        border-radius: 2px;
        cursor: pointer;
      }
    }

    .hint {
      position: absolute;
      bottom: 20px;
      left: 230px;
      width: 201px;

      div {
        color: var(---, #c0c4cc);
        font-weight: 400;
        font-size: 12px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 20px; /* 166.667% */
      }
    }
  }

  .attrConfigPop {
    position: fixed;
    top: 0;
    right: 48px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: center;
    width: 460px;
    height: 100%;
    background: #fff;
    box-shadow: -8px 0px 24px -2px rgba(30, 47, 85, 0.1);

    .title {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      align-self: stretch;
      height: 48px;
      padding: 12px 20px 12px 0px;
      border-bottom: 1px solid #f5f7fa;

      .line {
        width: 4px;
        height: 18px;
        margin-right: 12px;
        background: #1e89ff;
      }
      .name {
        color: var(----, rgba(0, 0, 0, 0.9));
        font-weight: 500;
        font-size: 16px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 24px; /* 150% */
      }
    }

    .nancalui-form {
      width: 100%;
      padding: 16px;
    }

    :deep(.nancalui-input__wrapper) {
      border: 1px solid #e5e6eb !important;
    }

    .effectiveDate {
      width: 100%;

      :deep(.nancalui-input__wrapper) {
        border: none !important;
      }
      :deep(.nancalui-range-date-picker-pro__range-picker) {
        border: 1px solid #e5e6eb;
        border-radius: 2px;
      }
    }

    :deep(.nancalui-textarea) {
      border: 1px solid #e5e6eb !important;
    }

    .resetRun {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .nancalui-input-slot {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &:last-child {
          justify-content: flex-end;
        }
      }

      :deep(.nancalui-input-number .nancalui-input-slot__prepend) {
        width: 72px;
        min-height: 32px;
        margin: 0;
        background: rgba(250, 250, 250, 1);
        border-right: none;
        border-radius: 3px 0 0 3px;
      }
      :deep(
          .nancalui-input
            .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled)
        ) {
        width: 63px;
        height: 32px;
        min-height: 32px;
        padding: 0;
        border-radius: 0;
      }
      :deep(.nancalui-input-slot__append) {
        min-height: 32px;
        margin: 0;
        background: rgba(250, 250, 250, 1);
        border-left: none;
        border-radius: 0 3px 3px 0;
      }
    }
    .table-box {
      max-height: 500px;
      border-bottom: 1px solid #ebebeb;
      :deep(.el-table__header) {
        width: 100% !important;
      }
      :deep(.el-scrollbar__view) {
        width: 100% !important;
        .el-table__body {
          width: 100% !important;
        }
      }
    }
    .form-item-text {
      color: #1d2129;
      font-size: 14px;
    }
    .tips {
      color: #a8abb2;
      font-size: 12px;
    }
    .delBtn {
      color: #1e89ff;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .timeBox {
    display: flex;
    justify-content: space-between;

    &-child {
      width: calc(50% - 2px);
    }
    .nancalui-select {
      border: 1px solid #e5e6eb !important;
    }
  }
  .modal-body {
    &-name {
      margin-bottom: 16px;
      color: #1d2129;
      font-size: 14px;
    }
    &-table {
      :deep(.nancalui-table) {
        height: 100%;
        tr {
          &.expanded {
            .icon-expand-row {
              transform: rotate(0deg);
            }
          }
          .icon-expand-row {
            width: 16px;
            height: 16px;
            background-image: url('/src/assets/img/quality/arrow-bottom.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            transform: rotate(-90deg);
            i {
              display: none;
            }
          }
        }
        tbody > tr > td:has(.table-expand-child) {
          padding: 0;
        }
        .nancalui-table__container {
          &::-webkit-scrollbar-thumb {
            background-color: #b1bcd6;
            border-radius: 8px;
            &:hover {
              background-color: #b1bcd6;
            }
          }
        }
        .table-expand-child {
          width: 100%;
          &-row {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            &.header {
              .table-expand-child-row-col {
                color: rgba(0, 0, 0, 0.9);
                font-weight: bolder;
                background-color: #ebf4ff;
                border-top: none;
                &.first {
                  background-color: #f6f7fb;
                }
                &.date {
                  width: 160px;
                }
              }
            }
            &:not(.header):hover {
              background-color: #ebf4ff;
            }
            &-col {
              flex: 1;
              box-sizing: border-box;
              height: 40px;
              padding-left: 20px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.75);
              font-size: 14px;
              line-height: 40px;
              white-space: nowrap;
              text-align: left;
              text-overflow: ellipsis;
              border-top: 1px solid #c5d0ea;
              &.first {
                flex: none;
                flex-shrink: 0;
                width: 60px;
                padding-top: 6px;
                background-color: #f6f7fb;
                border-top: none;
              }
              &.date {
                flex: none;
                flex-shrink: 0;
                width: 160px;
              }
            }
          }
          &-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            min-height: 200px;
            &-pic {
              width: 48px;
              height: 48px;
            }
            &-text {
              margin-top: 16px;
              color: #909399;
              font-size: 14px;
            }
          }
        }
        .nancalui-table__empty {
          display: flex;
          align-items: center;
          justify-content: center;
          height: calc(100% - 42px);
          .nancalui-empty__image {
            width: 48px;
            height: 48px;
            background-image: url('/src/assets/table-no-content-small.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            svg {
              display: none;
            }
          }
          .nancalui-empty__description {
            margin-top: 4px;
          }
        }
      }
    }
  }

  .nancalui-checkbox__group.is-column {
    display: block;
  }
</style>
