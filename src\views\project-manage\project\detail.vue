<template>
  <!-- 核对信息 -->
  <div class="dif project-add-check-information container-padding16">
    <!-- <div v-if="state.type === 'DETAIL'" class="box-top-title">
      <div class="box-top-title-box"><span class="need_smallcube__title">查看</span></div>
    </div> -->
    <div class="box-top">
      <div class="box-top-content">
        <div class="content-top">
          <div v-if="state.allData.projectName" class="project-name">
            <span class="need_smallcube__title">{{ state.allData.projectName }} </span>
          </div>
          <div class="project-user">
            <div v-if="state.allData.createTime" class="project-user-content">
              <span v-if="state.allData.createByName" class="hasbg"
                >创建人：{{ state.allData.createByName }}</span
              >
              <span class="hasbg">创建时间：{{ state.allData.createTime }}</span>
            </div>
          </div>
        </div>

        <div class="project-describe">{{
          state.allData.description ? state.allData.description : '暂无描述'
        }}</div>
      </div>
    </div>
    <div :class="state.type === 'DETAIL' ? 'dif box-bottom ' : 'box-bottom'">
      <n-tabs v-model="state.activeName">
        <!-- <n-tab title="关联数据源" id="dataSource" /> -->
        <n-tab title="授权人员" id="authorized" />
      </n-tabs>
      <!-- <div class="table-box" v-show="state.activeName === 'dataSource'">
        <n-public-table
          :isDisplayAction="false"
          :showPagination="false"
          :table-head-titles="state.tableHeadTitles_dataSource"
          :tableData="state.dataSourceTableData"
          :tableHeight="state.tableHeight"
        />
      </div> -->
      <div class="table-box" v-show="state.activeName === 'authorized'">
        <CfTable
          :actionWidth="104"
          :table-head-titles="state.tableHeadTitles_authorized"
          :tableConfig="{
            data: state.authorizedTableData.list,
            rowKey: 'id',
          }"
        ></CfTable>
        <!-- <n-public-table
          :isDisplayAction="false"
          :showPagination="false"
          :table-head-titles="state.tableHeadTitles_authorized"
          :tableData="state.authorizedTableData"
          :tableHeight="state.tableHeight"
        /> -->
      </div>
    </div>
    <div v-if="state.type === 'DETAIL'" class="detail-option">
      <n-button size="sm" variant="solid" color="primary" @click.prevent="goBack">返回</n-button>
    </div>
  </div>
</template>
<script>
  import { reactive, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const router = useRouter()

      const state = reactive({
        dataSourceTableData: {},
        authorizedTableData: {},
        activeName: 'authorized',
        tableHeight: 200,
        tableHeadTitles_dataSource: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 100 },
          { prop: 'name', name: '数据源名称' },
          // { prop: 'database', name: '实例名称' },
          { prop: 'dataStructureTypeName', name: '数据结构类型' },
          { prop: 'datasourceType', name: '数据库类型' },
        ],
        tableHeadTitles_authorized: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 100 },
          { prop: 'name', name: '人员名称' },
          { prop: 'job', name: '角色' },
          { prop: 'departmentFullName', name: '所属部门' },
        ],
        sourceTableData: [],
        allData: {
          projectName: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [],
          authorizedPage: [],
        },
        queryData: '',
        detailId: null, // 查看编辑模式id
        type: '',
      })
      const methods = {
        setTableHeight(height = 396) {
          state.tableHeight = document.body.offsetHeight - height
        },
        // 初始化数据
        init(data) {
          let { basePage, dataSourcePage, authorizedPage } = data
          state.allData.projectName = basePage.projectName

          state.allData.description = basePage.description
          state.allData.dataSourcePage = dataSourcePage
          state.allData.authorizedPage = authorizedPage
          nextTick(() => {
            methods.initTable()
          })
        },
        // 表格初始化
        initTable() {
          // 新增序号属性
          // state.allData.dataSourcePage.map((item, index) => {
          //   return Object.assign(item, { number: index + 1 })
          // })
          state.allData.authorizedPage.map((item, index) => {
            return Object.assign(item, { number: index + 1 })
          })

          // let _dataSource = {
          //   list: state.allData.dataSourcePage,
          // }
          let _authorized = {
            list: state.allData.authorizedPage,
          }
          // state.dataSourceTableData = _dataSource
          state.authorizedTableData = _authorized
        },
        // 回传数据
        getAllData() {
          return state.allData
        },
        goBack() {
          router.back()
        },
      }
      methods.setTableHeight(380)
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.detailId = state.queryData.id
        state.type = state.queryData.type

        if (state.detailId) {
          if (state.type === 'DETAIL') {
            // 查看
            api.project.getProjectDetail({ id: state.detailId }).then((res) => {
              let { success } = res
              if (success) {
                let { name, createTime, createByName, description, datasourceList, userList } =
                  res.data
                state.allData.projectName = name
                state.allData.createTime = createTime
                state.allData.createByName = createByName
                state.allData.description = description
                state.allData.dataSourcePage = datasourceList
                state.allData.authorizedPage = userList
                nextTick(() => {
                  methods.initTable()
                })
              }
            })
          }
        }
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f7f8fa;

  .project-add-check-information {
    position: relative;

    height: 100%;
    &.dif {
      height: calc(100vh - 68px);
      .need_smallcube__title {
        margin: 0;
      }
    }
    .box-top-title {
      background: #fff;
      padding: 2px 2px 20px;

      &-box {
        height: 38px;
        padding-left: 18px;
        display: flex;
        align-items: center;
        background: #f7f8fa;
        border-radius: 3px 3px 0px 0px;
      }
    }
    .box-top {
      padding: 0 20px;
      background-color: $contentBoxBg;
      .box-top-content {
        padding: 14px 0;
        border-radius: 8px;
        height: 88px;
        // border: 1px dashed #e1e1e1;
        .content-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      .need_smallcube__title {
        margin: 0;
      }

      .project-name {
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: $font;
        line-height: 14px;
        display: flex;
        i {
          padding: 0 10px;
        }
      }
      .project-user {
        color: #666666;

        .project-user-content {
          padding: 8px 0 0;
        }
        span {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          padding: 0 10px;
          margin-right: 12px;
          background: $spanBg;
          border-radius: 22px;
          font-size: $themeFont;
        }
      }
      .project-describe {
        padding: 10px 0 0 10px;
        font-size: $themeFont;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $font;
        line-height: 20px;
        // border-top: 1px solid $border;
        max-height: 86px;
        // min-height: 47px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
    .box-bottom {
      padding: 10px 20px 0;
      height: calc(100% - 72px);
      background-color: $contentBoxBg;
      border-radius: 4px;
      &.dif {
        height: calc(100% - 188px);
      }
      .table-box {
        margin-top: 20px;
        height: calc(100% - 86px);
      }

      :deep(.nancalui-tabs--card > .nancalui-tabs__header .nancalui-tabs__nav) {
        border: none;
      }
    }
    .detail-option {
      // width: 100%;
      height: 64px;
      padding: 16px;
      margin-top: 10px;
      text-align: right;
      background: #ffffff;
      // margin-top: 10px;
      // margin-left: -10px;
      // margin-right: -10px;
      border-radius: 0px 0px 2px 2px;
    }
  }
</style>
