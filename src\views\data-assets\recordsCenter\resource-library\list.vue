<template>
  <!-- 资产全景-资源目录 -->
  <div class="asset-library-page-out-box">
    <div class="asset-library-page container">
      <div class="statistics-table">
        <div class="statistics-left">
          <PublicLeftTree
            v-if="state.showTree"
            ref="publicLeftTree"
            :data="state.treeData"
            :treeAttrData="state.treeAttrData"
            @treeCheckNode="treeCheckNode"
            @treeUpdateNode="treeUpdateNode"
            @treeDelNode="treeDelNode"
            :key="state.key"
          />
        </div>
        <div class="statistics-right">
          <n-tabs v-model="state.activeName" @active-tab-change="tabChange">
            <n-tab title="结构化数据" id="ALL_STRUCTURE" />
            <n-tab title="非结构化数据" id="NOT_STRUCTURE" />
          </n-tabs>
          <div v-if="state.activeName === 'ALL_STRUCTURE'" class="statistics-right-table">
            <n-public-table
              ref="publicTable"
              :key="state.keyCunt"
              :isDisplayAction="true"
              :table-head-titles="state.tableHeadTitles"
              :tableData="state.tableDataList"
              :pagination="state.pagination"
              :tableHeight="state.tableHeight"
              :actionWidth="100"
              @tablePageChange="tablePageChange"
            >
              <template #pageTop>
                <div class="box-add commonForm">
                  <div class="top-left">
                    <span>模型层：原始数据层</span>
                  </div>
                  <n-form :inline="true" :data="state.filterSearch">
                    <n-form-item label="">
                      <n-input
                        v-model="state.filterSearch.keyword"
                        size="small"
                        placeholder="请输入查询关键字"
                        clearable
                        @clear="initTable"
                      >
                        <template #append>
                          <n-button @click.prevent="initTable">
                            <n-popover
                              class="item"
                              content="搜索"
                              trigger="hover"
                              :position="['bottom']"
                            >
                              <SvgIcon class="icon_search" icon="icon_search" />
                            </n-popover>
                          </n-button>
                        </template>
                      </n-input>
                    </n-form-item>
                  </n-form>
                  <!-- <div class="top-right"> </div> -->
                </div>
              </template>
              <template #iconName="{ editor }">
                <div class="icon-box">
                  <SvgIcon class="icon-name" icon="icon-target-name" title="表名" />
                  {{ editor.row.cnName }}
                </div>
              </template>
              <template #editor="{ editor }">
                <div class="edit-box">
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'assetsManage_recordsCenter_recordsCenterLibraryList_all_structure_view',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="checkThisTypeDetails(editor)"
                    >查看
                  </n-button>
                </div>
              </template>
            </n-public-table></div
          >
          <div v-else class="statistics-right-table">
            <n-public-table
              ref="publicTable"
              :key="state.keyCunt"
              :isDisplayAction="true"
              :table-head-titles="state.tableHeadTitlesFile"
              :tableData="state.tableDataFile"
              :pagination="state.pagination"
              :tableHeight="state.tableHeight"
              :actionWidth="200"
              @tablePageChange="tablePageChange"
            >
              <template #pageTop>
                <div class="box-add commonForm">
                  <div class="top-left"> </div>
                  <n-form :inline="true" :model="state.filterSearch">
                    <n-form-item label="文件对象：">
                      <n-select
                        class="mb-2"
                        v-model="state.filterSearch.fileObject"
                        placeholder="请选择文件对象"
                        :allow-clear="true"
                        style="width: 120px"
                        @value-change="onSearch"
                        @clear="onSearch"
                        :key="state.keyCunt"
                        :options="state.activeFileObj"
                        filter
                      />
                    </n-form-item>
                    <n-form-item label="文件格式：">
                      <n-cascader
                        ref="cascaderHandle"
                        class="mb-2"
                        v-model="state.filterSearch.fileArray"
                        :options="state.activeFileFormats"
                        path-mode
                        clearable
                        filterable
                        style="width: 120px"
                        @change="initTable"
                      />
                    </n-form-item>

                    <n-form-item label="时间范围：">
                      <n-range-date-picker-pro
                        v-model="state.filterSearch.time"
                        :clearable="true"
                        :placeholder="['开始日期', '结束日期']"
                        @confirmEvent="onSearch"
                      />
                    </n-form-item>
                    <n-form-item label="">
                      <n-input
                        v-model="state.filterSearch.keyword"
                        size="small"
                        placeholder="请输入查询关键字"
                        clearable
                        @clear="onSearch"
                      >
                        <template #append>
                          <n-button @click.prevent="onSearch">
                            <n-popover
                              class="item"
                              content="搜索"
                              trigger="hover"
                              :position="['bottom']"
                            >
                              <SvgIcon class="icon_search" icon="icon_search" />
                            </n-popover>
                          </n-button>
                        </template>
                      </n-input>
                    </n-form-item>
                  </n-form>
                  <!-- <div class="top-right"> </div> -->
                </div>
              </template>

              <template #sourceFileSize="{ editor }">
                <div class="file-size-box">{{ getfilesize(editor.row.sourceFileSize) }} </div>
              </template>
              <template #editor="{ editor }">
                <div class="edit-box">
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'assetsManage_recordsCenter_recordsCenterLibraryList_not_structure_view',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="seeFile(editor.row)"
                    >查看
                  </n-button>
                  <n-button
                    v-if="
                      buttonAuthList.includes(
                        'assetsManage_recordsCenter_recordsCenterLibraryList_not_structure_download_edit',
                      )
                    "
                    class="del-button has-right-border"
                    variant="text"
                    @click.prevent="downLoadFile(editor.row)"
                    >下载
                  </n-button>
                </div>
              </template>
            </n-public-table></div
          >
        </div>
      </div>
    </div>
    <filePreview ref="filePreviewDom" />
  </div>
</template>
<script>
  import { reactive, onMounted, ref, nextTick, getCurrentInstance, toRefs } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import ENUM from '@/const/enum'
  import { formartTime } from '@/utils/index'
  import filePreview from '@/components/filePreview/index'
  import { checkCName, checkName } from '@/utils/validate'

  export default {
    name: 'AuthorizedPersonnel',
    components: { filePreview },
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const filePreviewDom = ref()
      const projectCode = ref()
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/assetLibrary/${name}.png`, import.meta.url).href //本地文件路径
      }
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const publicTable = ref()
      const ruleForm = ref()
      const ruleAuditForm = ref()
      const publicLeftTree = ref()
      const cascaderHandle = ref()

      const state = reactive({
        showTree: false,
        key: 1,
        keyCunt: 0,
        shortcuts: ENUM.SHORTCUTS,
        activeName: 'ALL_STRUCTURE',
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          showLeftIcon: true,
          // showCheckbox: true,
          showControl: false,
          isHideSearch: false,
          parentControl: '',
          childControl: '',
          maxLevel: 3,
        },
        dialogTitle: '资产注册',
        dialogVisible: false,
        loading: false,
        disabled: false,
        registryModelId: null, //当前注册的模型id
        ruleForm: {
          registryCont: '0个',
          cnName: '',
          name: '',
          bizDomainId: '',
          status: 'CREATED',
        },
        ruleAuditForm: {
          auditResultName: '注册成功',
          auditComment: '',
        },
        options: [], // 级联数据
        bizDomainIdType: [],
        defaultExpandedKeys: [],
        rules: {
          bizDomainId: [{ required: true, message: '请选择业务域', trigger: 'change' }],
          cnName: [{ required: true, validator: checkCName, trigger: 'blur' }],
          name: [{ required: true, validator: checkName, trigger: 'blur' }],
        },
        AUDIT_EUM: {
          CREATED: '未注册',
          WAIT_AUDIT: '审核中',
          // PUBLISHED: '已发布',
          AUDIT_SUCCESS: '注册成功',
          AUDIT_FAIL: '注册失败',
        },
        detailRuleForm: {},
        detailData: {},
        tableHeadTitlesFile: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'sourceFileName', name: '文件名称' },
          { prop: 'sourceFileType', name: '文件类型' },
          { prop: 'treeName', name: '对象分组' },
          { prop: 'sourceFileFormat', name: '文件格式' },
          { prop: 'sourceFileSize', name: '文件大小', slot: 'sourceFileSize' },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'createByName', name: '创建人' },
        ],
        tableDataFile: {},
        fileTableHeight: 400,
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称', slot: 'iconName' },
          { prop: 'name', name: '英文名称' },
          { prop: 'projectName', name: '所属场景' },
          { prop: 'description', name: '描述信息' },
          { prop: 'dataCount', name: '数据条数' },
        ], //选中类表头
        tableDataList: {},
        tableHeight: 400,
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          keyword: null,
          fileFormat: null,
          fileObject: null,
          fileArray: [],
          fileType: null,
          time: [],
          treeId: null,
          treeName: null,
        },
        activeFileFormats: [], //选中文件类型枚举
        activeFileObj: [],
        startTime: null,
        endTime: null,
        dataModelId: '',
        project_options: [],
        tableData: [],
        firstInit: true, //首次初始化
      })
      if ('resourceLibraryActiveName' in localStorage) {
        state.activeName = localStorage.getItem('resourceLibraryActiveName')
      }
      const cascaderProps = {
        // expandTrigger: 'hover',
        checkStrictly: true,
        expandTrigger: 'hover',
        emitPath: true, //在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
      }

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 286
        },
        //查看文件
        async seeFile(item) {
          // let { fileType, fileFormat, url } = item
          // window.open('http://10.41.32.79:8012/onlinePreview?url=' + encodeURIComponent(url)) //要预览文件的访问地址

          let { sourceFileType, sourceFileFormat, ossUrl } = item
          let _objName = ossUrl.split('/data-govern/')
          const res = await api.dataManagement.fileDownload({
            bucket: 'data-govern',
            objName: decodeURIComponent(_objName[_objName.length - 1]), //把转义过的地址转回来
          })
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
          })
          filePreviewDom.value.init({
            blob,
            fileType: sourceFileType,
            fileFormat: sourceFileFormat,
          })
        },
        //下载文件
        async downLoadFile(item) {
          let _objName = item.ossUrl.split('/data-govern/')
          const res = await api.dataManagement.fileDownload({
            bucket: 'data-govern',
            objName: decodeURIComponent(_objName[_objName.length - 1]), //把转义过的地址转回来
          })

          if (res.type === 'application/json') {
            // 说明是普通对象数据，读取信息
            const fileReader = new FileReader()
            fileReader.readAsText(res)
            fileReader.onloadend = () => {
              const jsonData = JSON.parse(fileReader.result)
              // 后台信息
              ElNotification({
                title: '提示',
                message: jsonData.message,
                type: 'error',
              })
            }
          } else {
            // 下载文件
            const blob = new Blob([res], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
            })
            const link = document.createElement('a')
            const fileName = item.ossUrl.split('/')
            link.download = fileName[fileName.length - 1]
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          }
        },

        // 文件大小转换
        getfilesize(size) {
          if (!size) return ''
          var num = 1024.0 //byte
          if (size < num) return size + 'B'
          if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + 'K' //kb
          if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + 'M' //M
          if (size < Math.pow(num, 4)) return (size / Math.pow(num, 3)).toFixed(2) + 'G' //G
          return (size / Math.pow(num, 4)).toFixed(2) + 'T' //T
        },
        tabChange(val) {
          state.key++
          localStorage.resourceLibraryActiveName = val
          state.activeName = val
          methods.initData()
          nextTick(() => methods.getTreeData())
        },

        // 对tree数据改造
        reformData(data) {
          data.forEach((val) => {
            val.label = val.name
            val.value = val.id
            if (val.children) {
              this.reformData(val.children)
            }
          })
          return data
        },
        // 选中人员执行
        treeCheckNode(data) {
          let { checkItem } = data
          state.filterSearch.fileFormat = null
          if (checkItem.projectCode === null || checkItem.projectCode === 'ROOT') {
            state.filterSearch.treeId = null
            state.filterSearch.treeName = null
          } else {
            state.filterSearch.treeId = checkItem.projectCode
            state.filterSearch.treeName = checkItem.name
          }
          methods.getFileObjList(state.filterSearch.treeId)
          methods.initTable()
        },
        //编辑和新增目录树
        treeUpdateNode(item) {
          let { ruleForm, checkItem, isEdit } = item
          let _message = '新增目录成功'
          let _interfaceName = 'collectJobTreeCreate'
          let _data = {}
          if (isEdit) {
            // 编辑
            _message = '编辑目录成功'
            _interfaceName = 'collectJobTreeUpdate'
            _data = {
              description: ruleForm.desc,
              id: checkItem.id,
              level: checkItem.level,
              name: ruleForm.name,
              pid: checkItem.pid,
            }
          } else {
            // 新增
            _data = {
              description: ruleForm.desc,
              level: checkItem.level + 1,
              name: ruleForm.name,
              pid: checkItem.id,
            }
          }
          api.dataManagement[_interfaceName](_data)
            .then((res) => {
              let { success } = res
              if (success) {
                ElNotification({
                  title: '提示',
                  message: _message,
                  type: 'success',
                })
                publicLeftTree.value.clearFn()
                methods.getTreeData()
              }
            })
            .catch(publicLeftTree.value.clearLoading())
        },
        //删除目录树
        treeDelNode(item) {
          if (item.children && item.children.length) {
            ElNotification({
              title: '提示',
              message: '该节点含有子节点不可删除',
              type: 'warning',
            })
          } else {
            proxy.$MessageBoxService.open({
              title: '是否确认删除该目录',
              content: '删除后该目录将不可恢复',
              save: () => {
                api.dataManagement.collectJobTreeDel({ id: item.id }).then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除目录成功',
                      type: 'success',
                    })
                    methods.getTreeData()
                  }
                })
              },
            })
          }
        },
        onSearch() {
          let { time, keyword } = state.filterSearch

          if (time[0] && time[1]) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          } else {
            state.startTime = null
            state.endTime = null
          }
          methods.initTable()
        },

        // 初始化结构化表格
        initTable(init = true) {
          let interFaceUrl = ''
          let data = {}
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          if (state.activeName === 'ALL_STRUCTURE') {
            interFaceUrl = 'getResourceLibraryGlobalTableList'
            data = {
              condition: {
                name: state.filterSearch.keyword || null,
                projectCodeList: [state.filterSearch.treeId],
              },
              pageNum: state.pagination.currentPage,
              pageSize: state.pagination.pageSize,
            }
          } else {
            interFaceUrl = 'getThisFileTypeListGlobalSearch'
            if (state.filterSearch.fileArray?.length) {
              state.filterSearch.fileType = state.filterSearch.fileArray[0]
              state.filterSearch.fileFormat =
                state.filterSearch.fileArray[1] || state.filterSearch.fileArray[0]
              // state.filterSearch.fileArray[1] || null
            } else {
              state.filterSearch.fileType = null
              state.filterSearch.fileFormat = null
            }
            data = {
              condition: {
                fileFormat: state.filterSearch.fileFormat || null,
                treeId: state.filterSearch.fileObject || null,
                startTime: state.startTime || null,
                endTime: state.endTime || null,
                fileName: state.filterSearch.keyword || null,
                projectCode: state.filterSearch.treeId,
              },
              pageNum: state.pagination.currentPage,
              pageSize: state.pagination.pageSize,
            }
          }

          api.assets[interFaceUrl](data)
            .then((res) => {
              // 新增序号属性
              state.firstInit = false
              if (res.success) {
                res.data.list.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                    //注册成功状态或为原始层下数据时设为true
                    disabledThisRow: item.status === 'AUDIT_SUCCESS' || item.isOriginLayer,
                    // disabledThisRow: true,
                  })
                })
                state.keyCunt++
                state.tableData = res.data.list
                state.tableDataList = res.data
                state.tableDataFile = res.data
              }
            })
            .catch(() => {})
        },

        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          let data = {}
          data.layerId = row.layerId
          data.layerName = row.layerName
          data.projectName = row.projectName
          data.description = row.description
          router.push({
            name: 'recordsCenterLibraryDetail',
            query: {
              id: row.id,
              modelTitle: row.cnName || row.name,
              modelName: row.name,
              type: 'DATAMODEL',
              ruleForm: JSON.stringify(data),
            },
          })
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取左侧树数据
        async getTreeData() {
          const res = await api.project.getAllProjectList()
          let _treeData = [
            {
              children: [],
              projectCode: null,
              label: '全部场景',
              type: 'ROOT',
            },
          ]

          _treeData[0].children = res.data
          state.treeData = _treeData
          state.filterSearch.treeId = state.treeData?.projectCode || null
          methods.initTable()
        },
        matchingIcon(data) {
          let _icon = 'tree-child'
          switch (data.value) {
            case 'EXCEL':
              _icon = 'icon-excel'
              break
            case 'CSV':
              _icon = 'icon-csv'
              break
            case 'PDF':
              _icon = 'icon-pdf'

              break
            case 'WORD':
              _icon = 'icon-word'

              break
            case 'IMAGE':
              _icon = 'icon-image'
              break
            default:
              _icon = 'tree-child'
              break
          }
          return _icon
        },
        //获取文件格式树
        getFileTypeList() {
          api.dataManagement.getFileTypeList().then((res) => {
            let { success, data } = res
            state.activeFileFormats = []
            if (success) {
              state.activeFileFormats = methods.reformFile(data)
            }
          })
        },
        reformFile(data) {
          data.forEach((val) => {
            if (val.children && val.children.length > 0) {
              methods.reformFile(val.children)
            } else {
              val.children = null
            }
          })
          return data
        },
        //获取文件对象
        getFileObjList(projectCode = null) {
          api.assets.getGlobalTreeSearch({ projectCode }).then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.activeFileObj = data.map((item) => {
                  return { ...item, value: item.id }
                })
              }
            }
          })
        },
        treeSelectChange() {
          if (state.filterSearch.fileArray?.length) {
            state.filterSearch.fileType = state.filterSearch.fileArray[0]
            state.filterSearch.fileFormat = state.filterSearch.fileArray[1] || null
          } else {
            state.filterSearch.fileType = null
            state.filterSearch.fileFormat = null
          }

          methods.initTable()

          // state.filterSearch.fileFormat
          //  state.filterSearch.fileType
          // cascaderHandle.value.togglePopperVisible()
        },

        // // 点击树节点
        // clickNode(data) {
        //   state.dataModelId = data.id ? data.id : null
        //   if (state.dataModelId) {
        //     methods.initTable()
        //   }
        // },

        // 初始化数据
        initData() {
          state.filterSearch = {
            keyword: null,
            fileFormat: null,
            fileObject: null,
            fileArray: [],
            fileType: null,
            time: [],
            treeId: null,
            treeName: null,
          }
          state.treeAttrData.parentControl = ''
          state.treeAttrData.childControl = ''
          state.showTree = true
        },
      }
      onMounted(() => {
        nextTick(() => {
          const { name, projectCode } = toRefs(store.state.user.currentProject)
          state.projectCode = projectCode.value
          methods.setTableHeight()
          methods.initData()
          methods.getTreeData()
          methods.getFileTypeList()
          methods.getFileObjList(null)
        })
      })

      return {
        state,
        buttonAuthList,
        ruleForm,
        ruleAuditForm,
        publicTable,
        publicLeftTree,
        cascaderHandle,
        filePreviewDom,
        cascaderProps,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .asset-library-page-out-box {
    user-select: none;

    .asset-library-page {
      background-color: #f2f3f5;
      height: calc(100vh - 96px);
      padding: 10px;
      margin-top: -3px;

      &-top {
        padding: 0 20px;
        background-color: #fff;
        // margin-bottom: 10px;
        border-radius: 4px 4px 0 0;
      }

      .statistics-table {
        border-radius: 4px;
        // padding: 0 20px;
        background-color: #fff;
        height: 100%;
        display: flex;
        padding-right: 20px;
        .statistics-left {
          .title {
            height: 32px;
            line-height: 28px;
            background: #f2f3f6;
            border-radius: 4px;
            border: 2px solid #ffffff;
            color: #333333;
            font-size: 12px;
            text-align: center;
            font-weight: bolder;
          }
        }
        .statistics-right {
          flex: 1;
          padding: 0 0 0 20px;
          min-width: 0; //解决左侧
        }
        .statistics-right-table {
          .status-box {
            color: #333;
            font-size: 12px;
          }
          .success-box {
            color: #447dfd;
            &:hover {
              cursor: pointer;
            }
          }
          .fail-box {
            color: #f54446;
            &:hover {
              cursor: pointer;
            }
          }
        }
        .top-left {
          display: flex;
          align-items: center;
          span {
            display: inline-block;
            text-align: center;
            line-height: 32px;
            width: 136px;
            height: 32px;
            background: #f0f7ff;
            border-radius: 4px;
            border: 1px solid #bfd9ff;
            font-size: 12px;
            font-weight: 500;
            color: #333333;
          }
        }

        :deep(.el-pagination) {
          background-color: #fff;
        }
      }

      .need_smallcube__title {
        display: block;
        height: 16px;
        line-height: 18px;
        border-left: 4px solid var(--themeBlue);
        padding-left: 6px;
        font-size: 16px;
        color: #333333;
        font-weight: bolder;
      }

      .top-change-project {
        height: 78px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #dcdcdc;

        .top-change-project-left {
          display: flex;
          align-items: center;

          span {
            font-size: 16px;
            font-weight: bolder;
            color: #333333;
            line-height: 24px;
          }
        }
      }

      .mid-statistics {
        display: flex;
        align-items: flex-start;
        padding: 18px 0 4px;

        .statistics-box-left {
          padding: 10px 20px 0 0;
        }

        .statistics-box {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          padding: 0 20px 0 0;

          .statistics-list {
            display: flex;
            align-content: center;
            justify-content: center;
            width: 25%;

            img {
              width: 106px;
              height: 106px;
            }

            &-des {
              display: flex;
              align-items: center;
            }

            &-des-box {
              cursor: pointer;
              color: #333;
              font-size: 12px;

              &.can-click {
                cursor: pointer;
              }

              .top {
                display: flex;
                align-items: center;
                line-height: 20px;
              }

              .number {
                color: #333333;
                font-size: 24px;
                font-weight: bolder;
                padding: 0 5px;
              }

              .unit {
                color: #666;
              }

              .yy-icon {
                width: 20px;
                color: #c9c9cb;
                margin-left: 10px;
              }

              &.active {
                .yy-icon {
                  color: $themeFontColor;
                }
              }
            }
          }

          span {
            color: #25bfff;
          }
        }
      }

      .box-add {
        height: 64px;
        padding: 16px 0;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
      }
      .icon-box {
        .icon-name {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }

      .edit-box {
        .del-button {
          &:first-child {
            border-left: 0 !important;
          }

          &:last-child {
            padding-right: 0;
          }
        }
      }
    }
    :deep(.el-dialog) {
      .el-input.is-disabled .el-input__wrapper,
      .el-textarea.is-disabled .el-textarea__inner {
        background-color: transparent;
        border: none;
        box-shadow: none;
      }
    }
  }
</style>
