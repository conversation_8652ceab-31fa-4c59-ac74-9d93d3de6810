import request from '@/utils/request'
import URL from '@/const/urlAddress'
// 项目管理部分接口
// 新增场景
function newProject(data) {
  return request({
    url: `${URL.BASE}/project/create`,
    method: 'post',
    data: data,
  })
}
// 场景name判重
function projectNameValid(data) {
  return request({
    url: `${URL.BASE}/project/valid/repeat`,
    method: 'get',
    params: data,
  })
}

// 删除场景
function deleteProject(data) {
  return request({
    url: `${URL.BASE}/project/delete`,
    method: 'post',
    data: data,
  })
}
// 场景列表修改
function projectUpdate(data) {
  return request({
    url: `${URL.BASE}/project/update`,
    method: 'post',
    data: data,
  })
}

// 获取组织架构树-过滤admin
function getDepartment(data) {
  return request({
    url: `${URL.AUTH}/department/tree/person`,
    method: 'get',
    params: data,
  })
}
// 获取组织架构树-不过滤 三员专用
function getThreeDepartment(data) {
  return request({
    url: `${URL.AUTH}/department/three/tree/person`,
    method: 'get',
    params: data,
  })
}
// 场景详情
function getProjectDetail(data) {
  return request({
    url: `${URL.BASE}/project/get/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 对应节点下人员列表
function getDepartmentUnderPerson(data) {
  return request({
    url: `${URL.BASE}/project/person/search`,
    method: 'get',
    params: data,
  })
}
// 搜索场景列表
function searchProjectList(data) {
  return request({
    url: `${URL.BASE}/project/search`,
    method: 'get',
    params: data,
  })
}
// 项目管理-我的场景列表带分页
function searchMyProjectList(data) {
  return request({
    url: `${URL.BASE}/project/my/page/list`,
    method: 'post',
    data: data,
  })
}
// 项目管理-新增场景名称校验
function validProject(data) {
  return request({
    url: `${URL.BASE}/project/valid/repeat`,
    method: 'get',
    params: data,
  })
}
// 项目管理-设置我的默认场景
function setMyDefaultProject(data) {
  return request({
    url: `${URL.BASE}/project/preferred/${data.projectCode}/${data.off}`,
    method: 'post',
    data: data,
  })
}
// 项目管理-我的场景列表-所有
function getMyProjectList(data) {
  return request({
    url: `${URL.BASE}/project/my/list`,
    method: 'get',
    params: data,
  })
}
// 项目管理-数据中心-所有
function getAllProjectList(data) {
  return request({
    url: `${URL.BASE}/project/all/list`,
    method: 'get',
    params: data,
  })
}

// 项目管理-下拉场景列表-所有
function switchProject(data) {
  return request({
    url: `${URL.BASE}/project/switch/${data.projectCode}`,
    method: 'get',
    params: {},
  })
}

// 数据源管理-查询该用户的数据源列表--带分页
function getDatasourceList(data) {
  return request({
    url: `${URL.BASE}/datasource/search`,
    method: 'post',
    data: data,
  })
}
// 数据源管理-场景下的数据源列表-带分页
function getProjectDatasourceList(data) {
  return request({
    url: `${URL.BASE}/datasource/project/list`,
    method: 'post',
    data: data,
  })
}

// 数据源管理-同步数据源到生产环境
function copyAsOfficialDataSource(data) {
  return request({
    url: `${URL.BASE}/datasource/copyAsOfficialDataSource`,
    method: 'post',
    data: data,
  })
}
// 数据源管理-不分页查询数据源列表
function getDatasourceSelect(data) {
  return request({
    url: `${URL.BASE}/datasource/list`,
    method: 'get',
    params: data,
  })
}
// 未授权数据源申请
function datasourceApply(data) {
  return request({
    url: `${URL.BASE}/datasource/apply/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-数据源类型下拉列表
function getDataSourceType(data) {
  return request({
    url: `${URL.BASE}/datasource/type/all`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-数据结构下拉列表
function getDataStructureType(data) {
  return request({
    url: `${URL.BASE}/datasource/getDataStructureType`,
    method: 'get',
    params: data,
  })
}
// 判断数据源名称是否重名
function validDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/name/repeat/valid`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-新增数据源
function datasourceAdd(data) {
  return request({
    url: `${URL.BASE}/datasource/add`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}

// 数据源管理-编辑数据源
function datasourceUpdate(data) {
  return request({
    url: `${URL.BASE}/datasource/update`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}

// 数据源管理-查看等根据id查询信息-会脱敏
function getDatasourceDetail(data) {
  return request({
    url: `${URL.BASE}/datasource/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-查看等根据id查询全部信息
function getDatasourceAllDetail(data) {
  return request({
    url: `${URL.BASE}/datasource/aes/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-编辑根据id查询信息-不会脱敏
function getDatasourceDetailByAes(data) {
  return request({
    url: `${URL.BASE}/datasource/aes/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-测试数据源
function testDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/connected`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}
//列表测试连通
function testDatasourceWithId(data) {
  return request({
    url: `${URL.BASE}/datasource/list/connected`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-发布数据源
function sendDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/publish/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-判断能否删除数据源
function deleteDatasourceValid(data) {
  return request({
    url: `${URL.BASE}/datasource/delete/valid`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-删除数据源
function deleteDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/delete`,
    method: 'post',
    data: data,
  })
}
// 数据源管理-判断能否下架数据源
function downDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/offline/valid`,
    method: 'get',
    params: data,
  })
}
// 数据源管理-安全下架数据源
function offSafeDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/offline`,
    method: 'post',
    data: data,
  })
}
// 数据源管理-数据源状态下拉列表
function getDatasourceStatus(data) {
  return request({
    url: `${URL.BASE}/datasource/status`,
    method: 'get',
    params: data,
  })
}
// 项目管理-删除数据源
function removeDatasource({ data, projectCode }) {
  return request({
    url: `${URL.BASE}/project/remove/${projectCode}`,
    method: 'post',
    data: data,
  })
}
// 数据源管理-数据源类型-全列表
function getAllDatasourceTree(data) {
  return request({
    url: `${URL.BASE}/datasource/type/tree`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-判断能否解绑数据源
function unbindDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/unbind/valid`,
    method: 'get',
    params: data,
  })
}

// 数据源管理-安全解绑数据源
function unbindSafeDatasource(data) {
  return request({
    url: `${URL.BASE}/datasource/unbind`,
    method: 'post',
    data: data,
  })
}

// 数据质量-质量规则列表
function getRuleList(data) {
  return request({
    url: `${URL.QUALITY}/global-rule/page`,
    method: 'post',
    data: data,
  })
}
// 数据质量-新增规则
function saveRule(data) {
  return request({
    url: `${URL.QUALITY}/global-rule/save`,
    method: 'post',
    data: data,
  })
}
// 数据质量-更新规则
function updateRule(data) {
  let id = data.id
  delete data.id
  return request({
    url: `${URL.QUALITY}/global-rule/update/${id}`,
    method: 'post',
    data: data,
  })
}
// 数据质量-删除规则
function deleteRule(data) {
  return request({
    url: `${URL.QUALITY}/global-rule/delete/${data.id}`,
    method: 'post',
    data: {},
  })
}
// 数据质量-获取规则
function getRuleDetail(data) {
  return request({
    url: `${URL.QUALITY}/global-rule/detail/${data.id}`,
    method: 'get',
    params: {},
  })
}
// 数据质量-名称校验
function validRule(data) {
  return request({
    url: `${URL.QUALITY}/global-rule/checkRuleName`,
    method: 'post',
    data: data,
  })
}

// 非结构数据源-excel文件预览
function getDatasourceExcelPreview(data) {
  return request({
    url: `${URL.MODEL}/model/excel/preview`,
    method: 'get',
    params: data,
  })
}

//--------------数据标准
function searchDataStandard(data) {
  return request({
    url: `${URL.AUTH}/datastandard/search`,
    method: 'post',
    data: data,
  })
}
function saveStandard(data) {
  return request({
    url: `${URL.AUTH}/datastandard/save`,
    method: 'post',
    data: data,
  })
}
function deleteStandard(data) {
  return request({
    url: `${URL.AUTH}/datastandard/delete/${data.id}`,
    method: 'post',
    data: data,
  })
}
function releaseStandard(data) {
  return request({
    url: `${URL.AUTH}/datastandard/release`,
    method: 'post',
    data: data,
  })
}
function getRangeList(data) {
  return request({
    url: `${URL.AUTH}/datastandard/range/${data.id}`,
    method: 'get',
    params: data,
  })
}
function getDetailList(data) {
  return request({
    url: `${URL.AUTH}/datastandard/detail/${data.id}`,
    method: 'get',
    params: data,
  })
}
function getStandardList(data) {
  return request({
    url: `${URL.AUTH}/datastandard/metadata/search`,
    method: 'get',
  })
}

export default {
  newProject,
  deleteProject,
  getDepartment,
  getProjectDetail,
  getMyProjectList,
  getAllProjectList,
  switchProject,
  getDepartmentUnderPerson,
  searchProjectList,
  searchMyProjectList,
  validProject,
  setMyDefaultProject,
  projectUpdate,
  getDatasourceList,
  copyAsOfficialDataSource,
  getProjectDatasourceList,
  getDatasourceSelect,
  getDataSourceType,
  getDataStructureType,
  validDatasource,
  datasourceAdd,
  getDatasourceDetail,
  getDatasourceAllDetail,
  getDatasourceDetailByAes,
  testDatasource,
  datasourceUpdate,
  sendDatasource,
  deleteDatasourceValid,
  deleteDatasource,
  downDatasource,
  offSafeDatasource,
  getDatasourceStatus,
  removeDatasource,
  unbindDatasource,
  unbindSafeDatasource,
  getRuleList,
  saveRule,
  updateRule,
  deleteRule,
  getRuleDetail,
  validRule,
  getAllDatasourceTree,
  getDatasourceExcelPreview,
  datasourceApply,
  searchDataStandard,
  saveStandard,
  deleteStandard,
  releaseStandard,
  getRangeList,
  getDetailList,
  getStandardList,
  testDatasourceWithId,
  projectNameValid,
  getThreeDepartment,
}
