<template>
  <div class="container">
    <div class="cf-page-title">
      非结构化数据编制
      <div class="detail-back-box" @click.prevent="goBack(false)"> 返回 </div>
    </div>
    <div
      class="white-box nc-p-16"
      style="height: calc(100% - 128px); overflow: auto"
      v-loading="state.loading"
    >
      <n-form
        :data="state.formData"
        ref="formRef"
        :rules="state.rules"
        label-width="150px"
        label-suffix="："
      >
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="name" label="非结构化数据名称">
              <n-input v-model="state.formData.name" @blur="nameExist" maxLength="200" />
            </n-form-item>
            <div class="name-exist" v-if="state.nameExist.id">
              <svgIcon icon="icon-worning-tips" class="nc-m-r-3" />
              <span>该非结构化数据已存在，非结构化数据编号 {{ state.nameExist.ruleCode }}</span>
              <n-button variant="text" color="primary" @click="onPrewiew"
                >查看非结构化数据 ></n-button
              >
            </div>
          </n-col>
          <n-col span="12">
            <n-form-item field="categoryId" label="非结构化数据分类">
              <el-tree-select
                style="width: 100%"
                v-model="state.formData.categoryId"
                :data="state.classList"
                node-key="id"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }"
                filterable
                clearable
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="confidentialityLevel" label="密级">
              <n-select v-model="state.formData.confidentialityLevel" :allow-clear="true">
                <n-option
                  v-for="item in state.secretList"
                  :key="item.id"
                  :name="item.name"
                  :value="item.id"
                />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="associationDocIds" label="关联非结构化数据">
              <div id="associationDoc" style="width: 100%">
                <el-tree-select
                  ref="treeSelectRef"
                  style="width: 100%"
                  v-model="state.formData.associationDocIds"
                  :load="loadAssociationDocs"
                  lazy
                  :default-expanded-keys="state.defaultExpandedKeys"
                  filterable
                  :filter-node-method="filterAssociationDocs"
                  multiple
                  :render-after-expand="false"
                  show-checkbox
                  check-strictly
                  check-on-click-node
                  node-key="nodeId"
                  :loading="state.assLoading"
                  collapse-tags
                  collapse-tags-tooltip
                  :props="{
                    label: 'name',
                    children: 'children',
                    class: 'nc-line-1',
                    isLeaf: 'isLeaf',
                  }"
                />
              </div>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="type" label="非结构化数据类型">
              <div class="file-type">
                <n-radio-group
                  :disabled="state.isCollected || state.formData.id"
                  v-model="state.formData.type"
                  direction="row"
                  @change="onTypeChange"
                >
                  <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
                    item.name
                  }}</n-radio>
                </n-radio-group>
                <span v-if="state.formData.type === 'word'" class="word-tips"
                  >提交表单前，请先保存word非结构化数据。</span
                >
              </div>
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="templateId" label="选择模版">
              <el-tree-select
                v-model="state.formData.templateId"
                :data="state.templateList"
                style="width: 100%"
                node-key="id"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'templateList',
                }"
                filterable
                clearable
                :disabled="state.isCollected"
                @change="templateChange"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-form-item>
          <div class="docEditorCon" v-if="state.formData.type === 'word'">
            <DocumentEditor
              v-if="state.documentServerUrl"
              id="docEditor"
              :documentServerUrl="state.documentServerUrl"
              :config="state.officeConfig"
              :events_onDocumentReady="onDocumentReady"
              :events_onError="onDocumentError"
              :onLoadComponentError="onLoadComponentError"
            />
          </div>
          <div id="luckysheet" class="luckysheet" v-else-if="state.formData.type === 'excel'"></div>
        </n-form-item>
      </n-form>
    </div>
    <div class="white-box nc-m-t-10 nc-p-16">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack(false)">取消</n-button>
        <n-button variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { DocumentEditor } from '@onlyoffice/document-editor-vue'
  import { randomGuid } from '@/utils/index'
  import { onBeforeUnmount, reactive, nextTick, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import { exportExcel } from '@/views/data-management/resource-library/export'
  import { showtoolbarConfig } from '../config/sheet'

  import api from '@/api/index'
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const state = reactive({
    isCollected: false,
    formData: {
      id: '',
      categoryId: '',
      name: '',
      confidentialityLevel: 'INTERIOR',
      associationDocIds: [],
      type: 'word',
      templateId: '',
      hasEdit: false, // 是否已编辑
    },
    nameExist: {},
    rules: {
      categoryId: [
        { required: true, message: '请选择非结构化数据分类', trigger: 'change', type: 'number' },
      ],
      name: [{ required: true, message: '请输入非结构化数据名称', trigger: 'burl' }],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
      type: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
    },
    classList: [],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    assLoading: false,
    defaultExpandedKeys: [],
    typeList: [
      { name: 'word', id: 'word' },
      { name: 'excel', id: 'excel' },
    ],
    templateList: [],
    loading: false,
    documentServerUrl: '',
    officeConfig: {},
    fileId: '',
  })
  // 获取非结构化数据服务
  async function loadWord(type) {
    const source = state.isCollected ? 'collect' : 'docation'
    let res = null
    if (type === 'template') {
      res = await api.documentManage.getWordNew({
        source: source,
        templateId: state.formData.templateId?.split('-')[1],
        docId: state.formData.id,
      })
    } else {
      if (state.formData.id) {
        res = await api.documentManage.getOnlineEditor({
          source: source,
          fileId: state.formData.id,
        })
      } else {
        res = await api.documentManage.getWordNew({
          source: source,
        })
      }
    }
    const { documentServerUrl, config, fileId } = res.data
    state.officeConfig = { ...config, events: { onDocumentStateChange: onDocumentStateChange } }
    state.documentServerUrl = documentServerUrl + '/'
    state.fileId = fileId
  }
  // word报错
  function onDocumentError(err) {
    state.formData.hasEdit = false
    console.log('onDocumentError', err)
  }
  // word加载成功
  function onDocumentReady() {
    state.formData.hasEdit = false
    console.log('Document is loaded', window.DocEditor.instances.docEditor)
  }
  // word加载失败
  function onLoadComponentError(errorCode, errorDescription) {
    state.formData.hasEdit = false
    console.log('onLoadComponentError', errorCode, errorDescription)
  }
  // word是否修改
  function onDocumentStateChange(data) {
    state.formData.hasEdit = true
    console.log('onDocumentStateChange', data)
  }
  // 非结构化数据重名校验
  function nameExist() {
    api.documentManage
      .documentationNameExist({
        name: state.formData.name,
        id: state.formData.id,
      })
      .then((res) => {
        if (res.success) {
          state.nameExist = res.data || {}
        }
      })
  }
  // 模板类型切换
  function onTypeChange() {
    getTemplateList()
    if (state.formData.type === 'word') {
      loadWord()
    } else {
      window.luckysheet.destroy()
      nextTick(() => {
        window.luckysheet.create({
          container: 'luckysheet', //luckysheet是容器id
          showinfobar: false,
          lang: 'zh', // 设定表格语言
          gridKey: state.formData.id ? state.formData.id : randomGuid(),
          showtoolbarConfig: showtoolbarConfig,
          title: state.formData.name,
          loadUrl: state.formData.id
            ? location.origin + '/api/govern-document/documentation/loadUrl'
            : '',
        })
      })
    }
  }
  function templateChange() {
    if (state.formData.type === 'word') {
      loadWord('template')
    } else {
      window.luckysheet.destroy()
      nextTick(() => {
        window.luckysheet.create({
          container: 'luckysheet', //luckysheet是容器id
          showinfobar: false,
          lang: 'zh', // 设定表格语言
          gridKey: state.formData.templateId?.split('-')[1],
          showtoolbarConfig: showtoolbarConfig,
          title: state.formData.name,
          loadUrl: location.origin + '/api/govern-document/template/config/loadUrl',
        })
      })
    }
  }
  // 确定
  const formRef = ref(null)
  function onConfirm() {
    formRef.value.validate().then((valid) => {
      if (valid) {
        const data = {
          id: state.formData.id || null,
          categoryId: state.formData.categoryId,
          name: state.formData.name,
          confidentialityLevel: state.formData.confidentialityLevel,
          associationDocIds: state.formData.associationDocIds.map((i) => {
            return i.split('-')[1] ? Number(i.split('-')[1]) : null
          }),
          type: state.formData.type,
          templateId: state.formData.templateId?.split('-')[1] || null,
          hasEdit: state.formData.hasEdit,
        }
        let apiType = state.formData.id ? 'documentationUpdate' : 'documentationSave'
        if (state.formData.type === 'word') {
          state.formData.templateData = state.documentServerUrl
          data.docFileId = state.fileId ? state.fileId : state.formData.id
        } else {
          window.luckysheet.exitEditMode()
          const sheetData = window.luckysheet.getAllSheets()
          data.jsonData = JSON.stringify(sheetData)
          state.formData.jsonData = data.jsonData
        }
        api.documentManage[apiType](data).then((res) => {
          if (res.success) {
            if (state.formData.type === 'excel') {
              exportExcel(
                window.luckysheet.getAllSheets(),
                state.formData.name,
                res.data || state.formData.id,
                'document',
              )
            }
            proxy.$message.success('保存成功')
            setTimeout(() => {
              goBack(true)
            }, 0)
          }
        })
      }
    })
  }
  // 预览
  function onPrewiew() {
    router.push({
      name: 'documentPreparationPreview',
      query: {
        id: state.nameExist.id,
        docUrl: state.nameExist.docUrl,
      },
    })
  }
  // 取消
  function goBack(refresh = false) {
    if (refresh) {
      if (state.isCollected) {
        router.push({ name: 'documentCollectionList', query: { refresh: 'true' } })
      } else {
        router.push({ name: 'documentPreparationList', query: { refresh: 'true' } })
      }
    } else {
      router.go(-1)
    }
  }
  // 获取详情
  async function getDetail() {
    const res = await api.documentManage.documentationGet({ id: state.formData.id })
    state.formData = {
      ...res.data,
      templateId: res.data.templateId ? 'tem-' + Number(res.data.templateId) : null,
      associationDocIds:
        res.data.associationDocIds?.map((i) => {
          return `doc-${i}`
        }) || [],
    }
    const { associationDocNames } = res.data
    if (associationDocNames?.length) {
      for (let i = 0; i < associationDocNames?.length; i++) {
        if (!i) {
          await remoteAssociationDocs(associationDocNames[i])
        } else {
          remoteAssociationDocs(associationDocNames[i])
        }
      }
    }
  }
  async function getClassifyTreeList() {
    const res = await api.documentManage.getClassifyTreeList()
    state.classList = res.data
    if (!state.formData.id && router.currentRoute.value?.query?.categoryId) {
      state.formData.categoryId = Number(router.currentRoute.value?.query?.categoryId)
    }
  }
  async function getTemplateList() {
    const res = await api.documentManage.templateSimpleTree({ type: state.formData.type })
    state.templateList = res.data.map((i) => {
      return {
        disabled: true,
        ...i,
        templateList: i.templateList?.map((t) => {
          return {
            ...t,
            id: 'tem-' + t.id,
          }
        }),
      }
    })
  }
  const treeSelectRef = ref(null)
  // 监听搜索输入，手动触发搜索
  function customSearch() {
    nextTick(() => {
      const queryInput = document.querySelector('#associationDoc .el-select__input')
      queryInput.addEventListener('input', (event) => {
        remoteAssociationDocs(event.target.value, true)
      })
    })
  }
  // 展开搜索节点，触发过滤
  let expandedKeys = []
  async function remoteAssociationDocs(query, needFilter = false) {
    if (query) {
      state.assLoading = true
      const res = await api.documentManage.associationTree({ name: query })
      const data = res.data
      if (data.length) {
        expandedKeys = []
        setExpendKeys(data, true)
        state.defaultExpandedKeys = expandedKeys
        if (needFilter) {
          treeSelectRef.value?.filter(query)
        }
      }
      state.assLoading = false
    }
  }
  // 过滤方法
  function filterAssociationDocs(query, data) {
    if (!query) return true
    return data.name.indexOf(query) > -1
  }
  // 设置需要展开的节点
  function setExpendKeys(data) {
    data.forEach((item) => {
      if (item.children?.length) {
        expandedKeys.push(item.nodeType + '-' + item.id)
        setExpendKeys(item.children)
      }
    })
  }
  // 关联非结构化数据分类不可选
  function setDisabled(data) {
    data.forEach((item) => {
      item.nodeId = item.nodeType + '-' + item.id
      item.name = item.name?.length > 10 ? item.name.slice(0, 10) + '...' : item.name
      if (item.nodeType === 'category' || String(item.id) === String(state.formData.id)) {
        item.disabled = true
        if (item.children?.length) setDisabled(item.children)
      }
    })
  }
  // 关联非结构化数据懒加载
  async function loadAssociationDocs(node, resolve, reject) {
    const res = await api.documentManage.associationTree({ id: node?.data.id })
    if (res.success) {
      nextTick(() => {
        const ids = state.formData.associationDocIds?.map((i) => {
          return i.nodeId ? i.nodeId : i
        })
        if (ids?.length) {
          treeSelectRef.value?.setCheckedKeys(ids)
        }
      })
      const data = res.data
      setDisabled(res.data)
      return resolve(data)
    } else {
      return reject()
    }
  }

  onBeforeUnmount(() => {
    window.luckysheet.destroy()
  })
  onMounted(async () => {
    state.loading = true
    state.formData.id = router.currentRoute.value?.query?.id
    state.isCollected = router.currentRoute.value?.query?.isCollected || false
    await getClassifyTreeList()
    if (router.currentRoute.value.query.id) {
      await getDetail()
    }
    onTypeChange()
    customSearch()
    state.loading = false
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .docEditorCon {
    width: 100%;
    height: calc(100vh - 410px);
    min-height: 400px;
  }
  .luckysheet {
    position: relative;
    width: 100%;
    height: calc(100vh - 410px);
    min-height: 400px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
  .name-exist {
    display: flex;
    align-items: center;
    margin-top: -10px;
    margin-left: 150px;
    color: var(---Warning-, #ff7d00);
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
  }
  .file-type {
    display: flex;
    align-items: center;
    .word-tips {
      &::before {
        margin-right: 4px;
        color: #f52f3e;
        font-size: 16px;
        content: '*';
      }
      margin-left: 10px;
      color: #909399;
      font-weight: 400;
      font-size: 12px;
    }
  }
</style>
