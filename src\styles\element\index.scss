$--colors: (
  'primary': (
    'base': #447dfd,
  ),
  'success': (
    'base': #18ba72,
  ),
  'warning': (
    'base': #f5a623,
  ),
  'danger': (
    'base': #f54446,
  ),
  'error': (
    'base': #f54446,
  ),
  'info': (
    'base': #999999,
  ),
);

$--menu: (
  'active-color': #fff,
  'text-color': rgba(255, 255, 255, 0.55),
  'hover-text-color': #fff,
  'bg-color': linear-gradient(90deg, #132a53 0%, #16315d 100%),
  'hover-bg-color': rgba(255, 255, 255, 0.1),
  'item-height': 56px,
  // 'item-hover-fill': getCssVar('color-primary-light-9'),
);

// 我们可以将其添加到自定义名称空间，默认为 'el'
// @forward 'element-plus/theme-chalk/src/mixins/config.scss' with (
//   $namespace: 'el'
// );
//你应该在scss中使用它们，因为我们用sass来计算它。
//注释下一行使用默认颜色
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // 不要使用相同的名称，它会覆盖。
  $colors: $--colors,
  $menu: $--menu,
  $button-padding-horizontal: ('default': 50px)
);

// 如果您想导入所有
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// 黑暗主题自定义变量
// @use "./dark.scss";
// import dark theme
// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;
