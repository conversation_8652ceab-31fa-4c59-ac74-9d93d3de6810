<template>
  <div id="graph-container"></div>
</template>

<script lang="ts" setup>
  import { Graph } from '@antv/x6'
  import { VirtualRenderer } from '@antv/x6'

  let graph = null

  // 生成节点数据
  const generateNodes = () => {
    const nodes = []
    for (let i = 0; i < 10000; i++) {
      const node = {
        id: `node-${i}`,
        x: Math.random() * 1920 * 2,
        y: Math.random() * 911 * 2,
        label: `Node ${i}`,
      }
      nodes.push(node)
    }
    return nodes
  }

  // 生成边（关系线）数据，这里简单地随机连接部分节点，你可以根据实际需求调整连接逻辑
  const generateEdges = (nodes) => {
    const edges = []
    for (let i = 0; i < 5000; i++) {
      const sourceIndex = Math.floor(Math.random() * nodes.length)
      let targetIndex = Math.floor(Math.random() * nodes.length)
      while (targetIndex === sourceIndex) {
        targetIndex = Math.floor(Math.random() * nodes.length)
      }
      const edge = {
        id: `edge-${i}`,
        source: nodes[sourceIndex].id,
        target: nodes[targetIndex].id,
      }
      edges.push(edge)
    }
    return edges
  }

  const nodes = generateNodes()
  const edges = generateEdges(nodes)

  onMounted(() => {
    const container = document.getElementById('graph-container')
    graph = new Graph({
      container,
      // width: container.offsetWidth,
      // height: container.offsetHeight,
      // 开启交互优化，减少不必要的交互计算开销
      interacting: {
        // nodeMovable: false,
        // edgeMovable: false,
        // vertexAddable: false,
        // vertexRemovable: false,
        // edgeAddable: false,
        // edgeRemovable: false,
      },
      // 启用渲染性能优化选项
      background: {
        color: '#f5f5f5',
      },
      // 使用异步渲染，避免一次性渲染大量节点和边导致卡顿
      async: true,
      // 添加滚动相关配置，使得画布可以滚动
      scroller: {
        enabled: true, // 启用滚动功能
        pannable: true, // 允许通过鼠标拖动平移来滚动画布
        pageVisible: true, // 保证滚动时页面能正常显示
        autoResize: true, // 根据滚动情况自动调整可视区域大小
        // 可以设置滚动速度等参数，这里采用默认值，如有需要可自行调整
        speed: 1,
      },
    })

    graph.addNodes(nodes)
    graph.addEdges(edges)

    graph.use(
      // 启用虚拟渲染插件
      new VirtualRenderer({
        // 设置可视区域的缓冲区大小，根据实际情况调整
        buffer: [100, 100, 100, 100],
        // 可根据节点数量和性能情况调整步长，控制渲染的精细度
        step: 100,
      }),
    )
  })
</script>
<style scoped lang="scss">
  #graph-container {
    width: 100%;
    height: 100%;
  }
</style>
