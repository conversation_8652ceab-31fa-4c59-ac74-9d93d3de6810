<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>基础指标</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="指标看板-指标追溯" transform="translate(-501.000000, -433.000000)">
            <g id="画布" transform="translate(80.000000, 212.000000)">
                <g id="编组-7" transform="translate(120.000000, 213.000000)">
                    <g id="基础指标" transform="translate(293.000000, 0.000000)">
                        <g id="编组-3" transform="translate(4.000000, 4.000000)">
                            <g id="线框/基础指标" transform="translate(4.000000, 4.000000)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <g id="基" transform="translate(1.295000, 0.730000)" fill="#447DFD" fill-rule="nonzero">
                                    <path d="M0.405,1.14 L0.405,2.055 L3.06,2.055 L3.06,7.02 L0.18,7.02 L0.18,7.98 L3.3,7.98 C2.58,8.865 1.485,9.585 0,10.155 L0.555,11.07 C2.295,10.26 3.555,9.225 4.335,7.98 L9.045,7.98 C9.825,9.225 11.145,10.245 13.005,11.025 L13.44,10.095 C11.94,9.585 10.815,8.88 10.095,7.98 L13.215,7.98 L13.215,7.02 L10.335,7.02 L10.335,2.055 L12.99,2.055 L12.99,1.14 L10.335,1.14 L10.335,0 L9.285,0 L9.285,1.14 L4.11,1.14 L4.11,0 L3.06,0 L3.06,1.14 L0.405,1.14 Z M4.11,7.02 L4.11,5.94 L9.285,5.94 L9.285,7.02 L4.11,7.02 Z M4.11,5.085 L4.11,4.02 L9.285,4.02 L9.285,5.085 L4.11,5.085 Z M4.11,3.165 L4.11,2.055 L9.285,2.055 L9.285,3.165 L4.11,3.165 Z M6.18,8.505 L6.18,9.705 L3.105,9.705 L3.105,10.665 L6.18,10.665 L6.18,12.255 L0.93,12.255 L0.93,13.26 L12.375,13.26 L12.375,12.255 L7.23,12.255 L7.23,10.665 L10.275,10.665 L10.275,9.705 L7.23,9.705 L7.23,8.505 L6.18,8.505 Z" id="形状"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
