import { Vector2 } from 'three'
class ImageAnnotator {
    events = {};
    container;
    options = {
        isDrawDisabled: false,
        isDragDisabled: false,
    };
    state = {
        points: [],
        polygons: [],
        startPoint: undefined,
        endPoint: undefined,
    };
    // 缩放比例
    canvasScale = 1;

    // 图片位置偏移量
    imgOffset = {
        x: 0,
        y: 0,
    }
    circleRadius = 3;
    previousPoint;
    isDrag = false;
    img;
    canvas;
    ctx;
    resizeObserver;
    shape = 'drag';
    // 绘制绑定的事件
    static drawEvents = ['drawPointClick', 'dragImgDown', 'dragImgMove', 'dragImgUp', 'mouseDown', 'mouseMove', 'mouseUp', 'clickPolygon', 'clickDrawPolygon', 'dblClick', 'drawRectDown', 'drawRectMove', 'drawRectUp', 'drawCircleDown', 'drawCircleMove', 'drawCircleUp', 'drawEllipseDown', 'drawEllipseMove', 'drawEllipseUp']
    constructor(container, imageUrl, options = {}) {
        // 兼容
        switch (true) {
            case !container:
                throw new Error('container is required');
            case typeof container === 'string':
                container = document.querySelector(container);
            case !imageUrl:
                throw new Error('imageUrl is required');
            case typeof imageUrl !== 'string':
                throw new Error('imageUrl must be a string');
            case typeof options !== 'object':
                throw new Error('options must be an object');
        }
        // 事件容器
        this.container = container;
        this.options = options;
        this.emit = (key, ...args) => {
            if (!options[key]) return;
            // 获取形参
            options[key](...args);
        };
        // 创建canvas并挂载到容器
        this.createCanvas();
        // 初始化加载图片
        this.loadImg(imageUrl).then(this.mountEvents(this));
        // 监听容器尺寸变化
        this.on('handleContainerResize', this.handleContainerResize.bind(this));
        this.resizeObserver = new ResizeObserver(this.getEvent('handleContainerResize'));
        this.resizeObserver.observe(this.container);
    }
    // 加载完成后挂载事件
    mountEvents(ctx) {
        const { isDrawDisabled, isDragDisabled } = this.options;
        this.emit('load-complete');
        !isDragDisabled && this.dragPoint();
        // 挂载点击图形事件
        this.mountClickPolygonEvent()
        // 挂载绘制多边形事件
        // !isDrawDisabled && this.mountDrawPolygonEvent();
        // 挂载拖动图片位置事件
        this.mountDragImageEvent();
        // 挂载绘制矩形事件
        this.mountZoomCanvasEvent();
    }

    // 挂载画布缩放事件
    mountZoomCanvasEvent() {
        this.on('zoomCanvas', this.zoomCanvas.bind(this));
        this.ctx.canvas.addEventListener('wheel', this.getEvent('zoomCanvas'));
    }

    // 挂载点标注事件
    mountDrawPointEvent() {
        this.on('drawPointClick', this.drawPointClick.bind(this));
        this.ctx.canvas.addEventListener('click', this.getEvent('drawPointClick'));
    }

    // 挂载拖动图片位置事件
    mountDragImageEvent() {
        this.on('dragImgDown', this.dragImgDown.bind(this));
        this.on('dragImgMove', this.dragImgMove.bind(this));
        this.on('dragImgUp', this.dragImgUp.bind(this));
        this.ctx.canvas.addEventListener('mousedown', this.getEvent('dragImgDown'));
        this.ctx.canvas.addEventListener('mousemove', this.getEvent('dragImgMove'));
        this.ctx.canvas.addEventListener('mouseup', this.getEvent('dragImgUp'));
    }

    // 挂载绘制多边形事件
    mountDrawPolygonEvent() {
        this.on('clickDrawPolygon', this.clickDrawPolygon.bind(this));
        this.ctx.canvas.addEventListener('click', this.getEvent('clickDrawPolygon'));
        this.on('dblClick', this.dblClick.bind(this));
        this.ctx.canvas.addEventListener('dblclick', this.getEvent('dblClick'));
    }

    // 挂载绘制矩形事件
    mountDrawRectEvent() {
        this.on('drawRectDown', this.drawRectDown.bind(this))
        this.on('drawRectMove', this.drawRectMove.bind(this))
        this.on('drawRectUp', this.drawRectUp.bind(this))
        this.ctx.canvas.addEventListener('mousedown', this.getEvent('drawRectDown'));
        this.ctx.canvas.addEventListener('mousemove', this.getEvent('drawRectMove'));
        this.ctx.canvas.addEventListener('mouseup', this.getEvent('drawRectUp'));
    }

    // 挂载绘制圆形事件
    mountDrawCircleEvent() {
        this.on('drawCircleDown', this.drawCircleDown.bind(this))
        this.on('drawCircleMove', this.drawCircleMove.bind(this))
        this.on('drawCircleUp', this.drawCircleUp.bind(this))
        this.ctx.canvas.addEventListener('mousedown', this.getEvent('drawCircleDown'));
        this.ctx.canvas.addEventListener('mousemove', this.getEvent('drawCircleMove'));
        this.ctx.canvas.addEventListener('mouseup', this.getEvent('drawCircleUp'));
    }

    // 挂载绘制椭圆事件
    mountDrawEllipseEvent() {
        this.on('drawEllipseDown', this.drawEllipseDown.bind(this))
        this.on('drawEllipseMove', this.drawEllipseMove.bind(this))
        this.on('drawEllipseUp', this.drawEllipseUp.bind(this))
        this.ctx.canvas.addEventListener('mousedown', this.getEvent('drawEllipseDown'));
        this.ctx.canvas.addEventListener('mousemove', this.getEvent('drawEllipseMove'));
        this.ctx.canvas.addEventListener('mouseup', this.getEvent('drawEllipseUp'));
    }

    // 挂载点击图形事件
    mountClickPolygonEvent() {
        this.on('clickPolygon', this.clickPolygon.bind(this));
        this.ctx.canvas.addEventListener('click', this.getEvent('clickPolygon'));
    }

    // 创建canvas并挂载到容器
    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.container.appendChild(this.canvas);
        this.ctx = this.canvas.getContext('2d');
        return this.ctx;
    }
    // 切换标注图形
    changeShape(shape) {
        switch (shape) {
            // 点
            case 'point':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                // 设置点标注模式
                this.setPointMode();
                this.mountClickPolygonEvent();
                this.dragPoint();
                break;
            case 'drag':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setDragMode();
                this.mountClickPolygonEvent();
                break;
            case 'polygon':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setPolygonMode();
                this.mountClickPolygonEvent();
                this.dragPoint();
                break;
            case 'rect':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setRectMode();
                break;
            case 'circle':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setCircleMode();
                break;
            case 'ellipse':
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setEllipseMode();
                break;
            default:
                ImageAnnotator.drawEvents.forEach((item) => {
                    this.offEvent(item);
                });
                this.setPolygonMode();
                break;
        }
        // 注销事件

    }
    // 点标注模式
    setPointMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'point';
        !isDrawDisabled && this.mountDrawPointEvent();
    }
    // 拖拽模式
    setDragMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'drag';
        !isDrawDisabled && this.mountDragImageEvent();
    }

    // 多边形模式
    setPolygonMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'polygon';
        !isDrawDisabled && this.mountDrawPolygonEvent();
    }
    // 矩形模式
    setRectMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'rect';
        !isDrawDisabled && this.mountDrawRectEvent();
    }
    // 圆形模式
    setCircleMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'circle';
        !isDrawDisabled && this.mountDrawCircleEvent();
    }
    // 椭圆模式
    setEllipseMode() {
        const { isDrawDisabled } = this.options;
        this.points = [];
        this.shape = 'ellipse';
        !isDrawDisabled && this.mountDrawEllipseEvent();
    }

    loadImg(url) {
        if (this.img === null || this.img === undefined) {
            this.img = new Image()
        }

        return new Promise((resolve, reject) => {
            this.img.crossOrigin = 'anonymous';
            this.img.src = url;
            this.img.onload = () => {
                const { width: imgWidth, height: imgHeight } = this.img;
                this.originalImgWidth = imgWidth;
                this.originalImgHeight = imgHeight;
                // 根据容器尺寸计算canvas宽高
                const { width: containerWidth } = this.container.getBoundingClientRect();
                this.scale = containerWidth / imgWidth;
                this.canvasWidth = containerWidth;
                this.canvasHeight = imgHeight * this.scale;
                // 设置canvas尺寸
                this.canvas.width = this.canvasWidth;
                this.canvas.height = this.canvasHeight;
                // 绘制背景
                this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
                resolve(this.ctx);
            };
            this.img.onerror = () => reject(new Error('图片加载失败'));
        });
    }

    // 处理容器尺寸变化
    handleContainerResize(entries) {
        const newContainerWidth = entries[0].contentRect.width;
        if (!this.originalImgWidth || !this.originalImgHeight) return;
        const oldScale = this.scale
        this.scale = newContainerWidth / this.originalImgWidth;
        // 更新canvas尺寸
        this.canvasWidth = newContainerWidth;
        this.canvasHeight = this.originalImgHeight * this.scale;
        this.canvas.width = this.canvasWidth;
        this.canvas.height = this.canvasHeight;
        this.draw(this.state.points, this.state.polygons);
    }

    // 画布缩放事件-滚动鼠标
    zoomCanvas(e) {
        e.preventDefault();
        // 滚动缩放
        const { deltaY } = e;
        if (deltaY > 0) {
            // 放大
            this.canvasScale *= 1.1;
        } else {
            // 缩小
            this.canvasScale /= 1.1;
        }
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }

    // 绘制点-点击鼠标
    drawPointClick(e) {
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = (offsetX - this.imgOffset.x) / this.scale / this.canvasScale;
        offsetY = (offsetY - this.imgOffset.y) / this.scale / this.canvasScale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        const point = { x: offsetX, y: offsetY };
        this.state.points = [point];
        // 绘制实心圆
        this.ctx.fillStyle = 'red';
        this.ctx.beginPath();
        this.ctx.arc(point.x * this.scale * this.canvasScale + this.imgOffset.x, point.y * this.scale * this.canvasScale + this.imgOffset.y, 5, 0, Math.PI * 2);
        this.ctx.fill();
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        this.state.polygons.push(this.state.points);
        this.emit('imageMarkUpdate', this.state.points, this.scale * this.canvasScale, this.imgOffset);
    }

    // 拖动图片事件-按下鼠标
    dragImgDown(e) {
        this.isDrag = true;
        let { offsetX, offsetY } = e;
        this.state.points = [];
        const point = { x: offsetX, y: offsetY };
        // 记录起始点
        this.state.startPoint = point;
        // 修改鼠标样式
        this.ctx.canvas.style.cursor = "move";
    }

    // 拖动图片事件-移动鼠标
    dragImgMove(e) {
        if (!this.isDrag) return
        this.ctx.canvas.style.cursor = "move";
        let { offsetX, offsetY } = e;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        const point = { x: offsetX, y: offsetY };
        const { x: startX, y: startY } = this.state.startPoint || { x: 0, y: 0 };
        if (this.state.startPoint && this.state.endPoint) {
            this.imgOffset.x -= this.state.endPoint.x - startX;
            this.imgOffset.y -= this.state.endPoint.y - startY;
        }
        this.state.endPoint = point;
        // 计算移动距离
        const dx = point.x - startX;
        const dy = point.y - startY;
        this.imgOffset.x += dx;
        this.imgOffset.y += dy;
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }

    // 拖动图片事件-抬起鼠标
    dragImgUp(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        let point = { x: offsetX, y: offsetY }
        const { x: startX, y: startY } = this.state.startPoint || { x: 0, y: 0 };
        if (this.state.startPoint && this.state.endPoint) {
            this.imgOffset.x -= this.state.endPoint.x - startX;
            this.imgOffset.y -= this.state.endPoint.y - startY;
        }
        // 计算移动距离
        const dx = point.x - startX;
        const dy = point.y - startY;
        this.imgOffset.x += dx;
        this.imgOffset.y += dy;
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        this.state.endPoint = point;
        this.isDrag = false;
        this.ctx.canvas.style.cursor = "default";
        this.state.startPoint = undefined;
        this.state.endPoint = undefined;
    }

    // 绘制椭圆事件-按下鼠标
    drawEllipseDown(e) {
        this.isDrag = true;
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.state.points = [];
        const point = { x: offsetX, y: offsetY };
        // 记录起始点
        this.state.points.push(point);
    }

    // 绘制椭圆事件-移动鼠标
    drawEllipseMove(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";

        const point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const width = (point.x - startX) * this.scale;
        const height = (point.y - startY) * this.scale;

        this.drawEllipse(this.ctx, Math.min(startX * this.scale, startX * this.scale + width), Math.min(startY * this.scale, startY * this.scale + height), Math.abs(width), Math.abs(height), 'rgba(255, 0, 0, 0.2)', "#000");
        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }

    // 绘制椭圆事件-抬起鼠标
    drawEllipseUp(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        let point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const width = (point.x - startX) * this.scale;
        const height = (point.y - startY) * this.scale;
        this.drawEllipse(this.ctx, Math.min(startX * this.scale, startX * this.scale + width), Math.min(startY * this.scale, startY * this.scale + height), Math.abs(width), Math.abs(height), 'rgba(255, 0, 0, 0.2)', "#000");

        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        this.state.points.push(point);
        this.state.polygons.push(this.state.points);
        this.state.points = [];
        this.isDrag = false;
    }

    // 绘制圆形事件-按下鼠标
    drawCircleDown(e) {
        this.isDrag = true;
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.state.points = [];
        const point = { x: offsetX, y: offsetY };
        // 记录起始点
        this.state.points.push(point);
    }
    // 绘制圆形事件-移动鼠标
    drawCircleMove(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";

        const point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const radius = Math.sqrt(Math.pow(point.x - startX, 2) + Math.pow(point.y - startY, 2)) * this.scale;
        this.drawCircle(this.ctx, startX * this.scale, startY * this.scale, radius, 'rgba(255, 0, 0, 0.2)', "#000");
        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }
    // 绘制圆形事件-抬起鼠标
    drawCircleUp(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        let point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const radius = Math.sqrt(Math.pow(point.x - startX, 2) + Math.pow(point.y - startY, 2)) * this.scale;
        this.drawCircle(this.ctx, startX * this.scale, startY * this.scale, radius, 'rgba(255, 0, 0, 0.2)', "#000");

        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        this.state.points.push(point);
        this.state.polygons.push(this.state.points);
        this.state.points = [];
        this.isDrag = false;
    }

    // 绘制矩形事件-按下鼠标
    drawRectDown(e) {
        this.isDrag = true;
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.state.points = [];
        const point = { x: offsetX, y: offsetY };
        // 记录起始点
        this.state.points.push(point);

    }
    // 绘制矩形事件-移动鼠标
    drawRectMove(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";

        const point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const width = (point.x - startX) * this.scale;
        const height = (point.y - startY) * this.scale;
        this.drawRect(this.ctx, startX * this.scale, startY * this.scale, width, height);
        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }
    // 绘制矩形事件-抬起鼠标
    drawRectUp(e) {
        if (!this.isDrag) return
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale;
        offsetY = offsetY / this.scale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        let point = { x: offsetX, y: offsetY };
        // 绘制矩形
        const { x: startX, y: startY } = this.state.points[0];
        const width = (point.x - startX) * this.scale;
        const height = (point.y - startY) * this.scale;
        this.drawRect(this.ctx, startX * this.scale, startY * this.scale, width, height);

        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        this.state.points.push(point);
        this.state.polygons.push(this.state.points);
        this.state.points = [];
        this.isDrag = false;
    }
    // 双击结束绘制
    dblClick(e) {
        if (this.state.points.length < 3 || this.isDrag) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        // 将this.state.points减去偏移量
        this.state.points = this.state.points.map((item) => {
            return { x: (item.x - this.imgOffset.x / this.scale / this.canvasScale), y: (item.y - this.imgOffset.y / this.scale / this.canvasScale) };
        });
        this.state.polygons.push(this.state.points)
        // 绘制背景
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
        // this.state.points转换为实际坐标
        this.emit('imageMarkUpdate', this.state.points, this.scale * this.canvasScale, this.imgOffset);
        this.state.points = [];
    }
    // 点击绘制标注区域事件
    clickDrawPolygon(e) {
        if (this.isDrag || this.previousPoint) return;
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = offsetX / this.scale / this.canvasScale;
        offsetY = offsetY / this.scale / this.canvasScale;
        // 若在多边形内则不触发
        if (this.clickPolygon(e, false)) return
        const lastPoint = this.state.points[this.state.points.length - 1];
        if (lastPoint && this.isEqual(lastPoint, { x: e.offsetX / this.scale, y: e.offsetY / this.scale })) return;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        const point = { x: offsetX, y: offsetY };
        this.state.points.push(point);
        this.markPolygon(this.state.points, false, false)
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }

    // 绘制背景
    drawBackground({ ctx, img, width, height }) {
        this.state.polygons?.forEach((polygon) => {
            if (polygon.length === 1) {
                const point = polygon[0];
                // 点标记
                this.ctx.fillStyle = 'red';
                this.ctx.beginPath();
                this.ctx.arc(point.x * this.scale * this.canvasScale + this.imgOffset.x, point.y * this.scale * this.canvasScale + this.imgOffset.y, 5, 0, Math.PI * 2);
                this.ctx.fill();

            } else {
                this.markPolygon(polygon, true);
            }
        });
        ctx.drawImage(img, this.imgOffset.x, this.imgOffset.y, width * this.canvasScale, height * this.canvasScale);
    }

    // 标注区域绘制方法
    markPolygon(points, isClose, isOffset = true) {
        // 还原为画布实际坐标
        points = points.map(point => {
            const offsetX = isOffset ? this.imgOffset.x : 0;
            const offsetY = isOffset ? this.imgOffset.y : 0;
            return { x: point.x * this.scale * this.canvasScale + offsetX, y: point.y * this.scale * this.canvasScale + offsetY };
        })
        points.forEach((point) => this.drawCircle(this.ctx, point.x, point.y, this.circleRadius));
        if (points.length > 1) this.drawLine(this.ctx, points);
        if (points.length > 2) this.drawPolygon(this.ctx, points, isClose);
    }

    // 拖拽修改点位
    dragPoint() {
        this.on('mouseDown', this.mouseDown.bind(this))
        this.on('mouseMove', this.mouseMove.bind(this))
        this.ctx.canvas.addEventListener('mousedown', this.getEvent('mouseDown'));
        this.ctx.canvas.addEventListener('mousemove', this.getEvent('mouseMove'));
    }

    mouseDown(e) {
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = (offsetX - this.imgOffset.x) / this.scale / this.canvasScale;
        offsetY = (offsetY - this.imgOffset.y) / this.scale / this.canvasScale;
        const flatPoints = this.state.polygons.flat();
        const point = flatPoints.find(({ x, y }) => this.isEqual({ x, y }, { x: offsetX, y: offsetY }, 3));
        if (point) {
            this.isDrag = true;
            this.ctx.canvas.style.cursor = 'move';
            this.previousPoint = point;
            this.on('mouseUp', this.mouseUp.bind(this))
            this.ctx.canvas.addEventListener('mouseup', this.getEvent('mouseUp'));
        }
    }

    mouseMove(e) {
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX = (offsetX - this.imgOffset.x) / this.scale / this.canvasScale;
        offsetY = (offsetY - this.imgOffset.y) / this.scale / this.canvasScale;
        if (!this.isDrag || !this.previousPoint) {
            const flatPoints = this.state.polygons.flat();
            const point = flatPoints.find(({ x, y }) => this.isEqual({ x, y }, { x: offsetX, y: offsetY }, 3));
            this.ctx.canvas.style.cursor = point ? 'move' : 'default';
            return;
        }
        this.previousPoint.x = offsetX;
        this.previousPoint.y = offsetY;
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        this.drawBackground({ ctx: this.ctx, img: this.img, width: this.canvasWidth, height: this.canvasHeight });
    }

    mouseUp(e) {
        setTimeout(() => {
            this.ctx.canvas.style.cursor = 'default';
            this.previousPoint = undefined;
            this.isDrag = false;
        });
    }
    // 点击多边形
    clickPolygon(e, isEmit = true) {
        let { offsetX, offsetY } = e;
        // 将点击位置还原为图片实际坐标
        offsetX -= this.imgOffset.x;
        offsetY -= this.imgOffset.y;
        offsetX = offsetX / this.scale / this.canvasScale;
        offsetY = offsetY / this.scale / this.canvasScale;

        const polygon = this.state.polygons.find((polygon) => this.isPointInPolygon({ x: offsetX, y: offsetY }, polygon));
        // 点击点标注
        const point = this.state.polygons.filter((polygon) => polygon.length === 1).find((polygon) => this.isEqual(polygon[0], { x: offsetX, y: offsetY }, 5));

        if (point) {
            this.emit('click-polygon', point ,this.scale * this.canvasScale, this.imgOffset)
        }
        if (polygon) {
            if (!isEmit) return polygon;
            this.emit('click-polygon', polygon,this.scale * this.canvasScale, this.imgOffset)
        }
    }
    // 是否点击到多边形
    isPointInPolygon(point, polygon) {
        const path = new Path2D();
        path.moveTo(polygon[0].x, polygon[0].y);
        for (let i = 1; i < polygon.length; i++) {
            path.lineTo(polygon[i].x, polygon[i].y);
        }
        path.closePath();
        return this.ctx.isPointInPath(path, point.x, point.y);
    }

    // 绘制
    draw(points, polygons) {
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "destination-over";
        this.markPolygon(points)
        polygons?.forEach((polygon) => {
            if (polygon.length === 1) {
                const point = polygon[0];
                // 点标记
                this.ctx.fillStyle = 'red';
                this.ctx.beginPath();
                this.ctx.arc(point.x * this.scale * this.canvasScale + this.imgOffset.x, point.y * this.scale * this.canvasScale + this.imgOffset.y, 5, 0, Math.PI * 2);
                this.ctx.fill();

            } else {
                this.markPolygon(polygon, true);
            }
        });
        this.ctx.drawImage(this.img, this.imgOffset.x, this.imgOffset.y, this.canvasWidth, this.canvasHeight);
    }

    // 销毁
    destroy() {
        this.ctx.canvas.removeEventListener('mousedown', this.getEvent('mouseDown'));
        this.ctx.canvas.removeEventListener('mousemove', this.getEvent('mouseMove'));
        this.ctx.canvas.removeEventListener('mouseup', this.getEvent('mouseUp'));
        this.ctx.canvas.removeEventListener('click', this.getEvent('clickPolygon'));
        this.ctx.canvas.removeEventListener('click', this.getEvent('clickDrawPolygon'));
        this.ctx.canvas.removeEventListener('dblclick', this.getEvent('dblClick'));
        // 移除尺寸监听
        if (this.resizeObserver) {
            this.resizeObserver.unobserve(this.container);
            this.resizeObserver.disconnect();
        }
    }

    // 根据点位画矩形
    drawRect(ctx, x, y, width, height, fill = 'rgba(255, 0, 0, 0.2)', stroke = "#000") {
        ctx.fillStyle = fill;
        ctx.strokeStyle = stroke;
        ctx.fillRect(x, y, width, height);
        ctx.strokeRect(x, y, width, height);
    }
    // 画有边框的圆点
    drawCircle(ctx, x, y, radius, fill = "#fff", stroke = "#000") {
        ctx.fillStyle = fill;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.strokeStyle = stroke;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        ctx.stroke();
    }

    // 根据点位画椭圆
    drawEllipse(ctx, x, y, width, height, fill = 'rgba(255, 0, 0, 0.2)', stroke = "#000") {
        ctx.fillStyle = fill;
        ctx.strokeStyle = stroke;
        ctx.beginPath();
        ctx.ellipse(x + width / 2, y + height / 2, width / 2, height / 2, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }

    // 根据点位画直线
    drawLine(ctx, points) {
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }
        ctx.stroke();
    }

    // 根据点位绘制多边形
    drawPolygon(ctx, points, isClose = false) {
        ctx.strokeStyle = undefined;
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }
        if (isClose) ctx.closePath();
        ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
        ctx.fill();
        ctx.stroke();
    }

    // 计算两点是否重合
    isEqual({ x: x1, y: y1 }, { x: x2, y: y2 }, tolerance = 10) {
        const point1 = new Vector2(x1, y1);
        const point2 = new Vector2(x2, y2);
        return point1.distanceTo(point2) < tolerance;
    }
    setDisabled({ isDrawDisabled, isDragDisabled }) {
        if (typeof isDrawDisabled !== 'undefined') this.options.isDrawDisabled = isDrawDisabled;
        if (typeof isDragDisabled !== 'undefined') this.options.isDragDisabled = isDragDisabled;
        // 注销事件
        if (this.options.isDrawDisabled) {
            ImageAnnotator.drawEvents.forEach((item) => {
                this.offEvent(item);
            });

        } else {
            this.changeShape(this.shape);
        }
        // if (this.options.isDragDisabled) {
        //     ImageAnnotator.drawEvents.forEach((item) => {
        //         this.offEvent(item);
        //     });
        // }
        // else {
        //     this.changeShape(this.shape);
        // }
    }

    // 注销指定事件
    offEvent(key) {
        switch (key) {
            case 'mouseDown':
                this.ctx.canvas.removeEventListener('mousedown', this.getEvent('mouseDown'));
                break;
            case 'mouseMove':
                this.ctx.canvas.removeEventListener('mousemove', this.getEvent('mouseMove'));
                break;
            case 'mouseUp':
                this.ctx.canvas.removeEventListener('mouseup', this.getEvent('mouseUp'));
                break;
            case 'clickPolygon':
                this.ctx.canvas.removeEventListener('click', this.getEvent('clickPolygon'));
                break;
            case 'clickDrawPolygon':
                this.ctx.canvas.removeEventListener('click', this.getEvent('clickDrawPolygon'));
                break;
            case 'dblClick':
                this.ctx.canvas.removeEventListener('dblclick', this.getEvent('dblClick'));
                break;
            case 'drawRectDown':
                this.ctx.canvas.removeEventListener('mousedown', this.getEvent('drawRectDown'));
                break;
            case 'drawRectMove':
                this.ctx.canvas.removeEventListener('mousemove', this.getEvent('drawRectMove'));
                break;
            case 'drawRectUp':
                this.ctx.canvas.removeEventListener('mouseup', this.getEvent('drawRectUp'));
                break;
            case 'drawCircleDown':
                this.ctx.canvas.removeEventListener('mousedown', this.getEvent('drawCircleDown'));
                break
            case 'drawCircleMove':
                this.ctx.canvas.removeEventListener('mousemove', this.getEvent('drawCircleMove'))
                break
            case 'drawCircleUp':
                this.ctx.canvas.removeEventListener('mouseup', this.getEvent('drawCircleUp'))
                break;
            case 'drawEllipseDown':
                this.ctx.canvas.removeEventListener('mousedown', this.getEvent('drawEllipseDown'));
                break
            case 'drawEllipseMove':
                this.ctx.canvas.removeEventListener('mousemove', this.getEvent('drawEllipseMove'))
                break
            case 'drawEllipseUp':
                this.ctx.canvas.removeEventListener('mouseup', this.getEvent('drawEllipseUp'))
                break;
            case 'dragImgDown':
                this.ctx.canvas.removeEventListener('mousedown', this.getEvent('dragImgDown'));
                break;
            case 'dragImgMove':
                this.ctx.canvas.removeEventListener('mousemove', this.getEvent('dragImgMove'));
                break;
            case 'dragImgUp':
                this.ctx.canvas.removeEventListener('mouseup', this.getEvent('dragImgUp'));
                break;
            case 'drawPointClick':
                this.ctx.canvas.removeEventListener('click', this.getEvent('drawPointClick'));
                break;

            default:
                break;
        }
    }
    // 根据传入的key获取事件的引用
    getEvent(key) {
        return this.events[key];
    }
    // 注册事件
    on(key, fn) {
        if (this.events[key]) {
            console.warn(`事件${key}已存在`);
            return;
        }
        this.events[key] = fn;
    }
    // 注销事件
    off(key) {
        delete this.events[key];
    }
    // 图片坐标转屏幕坐标
    imgToScreen({ x, y }) {
        return { x: x * this.scale * this.canvasScale + this.imgOffset.x, y: y * this.scale * this.canvasScale + this.imgOffset.y };
    }
}
export default ImageAnnotator;