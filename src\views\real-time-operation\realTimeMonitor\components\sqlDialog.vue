<template>
  <n-modal
    v-model="showDirDialog"
    title="查看作业"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container"> <div class="log" v-html="state.codeText"> </div> </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  const showDirDialog = ref(false)
  const state = reactive({
    codeText: '',
  })

  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    cancel()
  }
  defineExpose({
    open(content) {
      state.codeText = content
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
    .log {
      min-height: 200px;
      max-height: 600px;
      display: flex;
      padding: 16px;
      flex-direction: column;
      align-items: flex-end;
      gap: 6px;
      flex: 1 0 0;
      white-space: pre-wrap;
      border-radius: 2px;
      border: 1px solid var(---, #e5e6eb);
      background: var(--100, #fff);
      overflow-y: auto;
    }
  }
</style>
