<template>
  <!-- 结构化数据采集  -->
  <div class="doc-collection">
    <div class="add-box">
      <div class="page-title">
        采集任务管理
        <div class="detail-back-box" @click.prevent="cancel"> 返回 </div>
      </div>
    </div>
    <n-tabs v-model="state.activeName" class="base-tabs" @active-tab-change="handleClick">
      <n-tab title="API采集" id="API" />
      <n-tab title="SFTP采集" id="SFTP" />
    </n-tabs>
    <div class="add-container">
      <section v-if="state.activeName === 'SFTP'" class="cf-tools">
        <div class="row">
          <div class="col">
            <span class="label">IP：</span>
            <n-input
              class="asideTree-search-input"
              v-model="state.filterSearch.keyWord"
              clearable
              @clear="onSearch"
            >
              <template #append>
                <n-button class="search-btn" icon="search" @click.prevent="onSearch" />
              </template>
            </n-input>
          </div>
        </div>
      </section>
      <div class="table" :style="state.activeName === 'API' ? 'height:100%' : ''">
        <CfTable
          actionWidth="150"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              onSearch(false)
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              onSearch()
            },
          }"
        >
          <template #editor="{ data: { row } }">
            <n-button
              v-if="state.activeName === 'API'"
              :disabled="row.status === 'RUNNING'"
              variant="text"
              color="primary"
              @click="runTask(row)"
              >执行</n-button
            >
            <n-button
              v-if="state.activeName === 'API'"
              :disabled="row.status === 'RUNNING'"
              variant="text"
              color="primary"
              @click="editApi(row)"
              >编辑</n-button
            >
            <n-button
              v-if="state.activeName === 'SFTP'"
              variant="text"
              color="primary"
              @click="runTask(row)"
              >采集</n-button
            >
            <n-button
              variant="text"
              color="primary"
              :disabled="state.activeName === 'API' && row.status === 'RUNNING'"
              @click="onDelete(row)"
              >删除</n-button
            >
          </template>
          <template #tagList="{ row }">
            <level-tag
              :bgColor="item.bgColor"
              :borderColor="item.borderColor"
              :color="item.color"
              v-for="item in state.tagList"
              v-show="row.confidentialityName === item.name"
              :key="item.name"
              >{{ item.name }}</level-tag
            >
          </template>
          <template #status="{ row }">
            <n-switch
              class="mr-1"
              v-model="row.status"
              active-value="START"
              inactive-value="STOP"
              color="#1E89FF"
              @change="statusChange(row)"
            >
              <template #checkedContent>启动</template>
              <template #uncheckedContent>停止</template>
            </n-switch>
          </template>
          <template #duplicateLogic="{ row }">
            <span>{{ state.typeList[row.duplicateLogic] }}</span>
          </template>
        </CfTable>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'

  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const { proxy } = getCurrentInstance()
      const router = useRouter()
      const state = reactive({
        activeName: 'API',
        categoryId: null,
        filterSearch: {
          keyWord: '',
        },
        loading: false,
        tagList: [
          {
            name: '公开',
            color: '#1AA4EE',
            borderColor: 'rgba(26, 164, 238, 0.40)',
            bgColor: 'rgba(26, 164, 238, 0.08)',
          },
          {
            name: '内部',
            color: '#FE8624',
            borderColor: '#FFBA70',
            bgColor: '#FFF4E6',
          },
          {
            name: '受控',
            color: '#1E89FF',
            borderColor: '#99C9FF',
            bgColor: '#EBF4FF',
          },
          {
            name: '秘密',
            color: '#D40000',
            borderColor: '#EF7777',
            bgColor: '#FFEDED',
          },
          {
            name: '机密',
            color: '#7A0000',
            borderColor: 'rgba(122, 0, 0, 0.40)',
            bgColor: 'rgba(122, 0, 0, 0.08)',
          },
          {
            name: '核心',
            color: '#224ECD',
            borderColor: 'rgba(34, 78, 205, 0.40)',
            bgColor: 'rgba(34, 78, 205, 0.08)',
          },
        ],
        typeList: {
          SKIP: '跳过',
          COVER: '覆盖现有文件',
          RENAME: '重命名后保存',
        },
        tableHeadTitles: [
          { prop: 'filelistName', name: '非结构化数据清单接口', width: '150' },
          { prop: 'fileinfoName', name: '非结构化数据详情接口' },
          { prop: 'categoryName', name: '非结构化数据分类' },
          { prop: 'duplicateLogic', name: '重复处理', slot: 'duplicateLogic' },
          { prop: 'lastStartTime', name: '采集时间', width: '200' },
          { prop: 'statusName', name: '状态' },
          { prop: 'collectProgress', name: '采集进度' },
        ],
        pagination: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        tableList: [
          // {
          //   id:1,
          //   status:'START'
          // },{
          //   id:2,
          //   status:'STOP'
          // }
        ],
        isLoad: false,
      })

      const methods = {
        // 查询
        onSearch(init = true) {
          if (init) {
            state.pagination.pageNum = 1
            state.pagination.currentPage = 1
          }
          state.isLoad = true

          let params = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              host: state.filterSearch.keyWord,
              categoryId: state.categoryId,
            },
          }
          let fn = 'getDocApi'
          if (state.activeName === 'API') {
            fn = 'getDocApi'
            params.condition = state.categoryId
          } else if (state.activeName === 'SFTP') {
            fn = 'getDocTask'
          }
          api.documentManage[fn](params)
            .then((res) => {
              state.tableList = res.data.list
              state.pagination.total = res.data.total
              state.isLoad = false
            })
            .catch(() => {
              state.isLoad = false
            })
        },
        statusChange(row) {
          if (row.status === 'START') {
            api.documentManage
              .startTask({
                id: row.id,
              })
              .then((res) => {
                methods.onSearch(false)
              })
          } else if (row.status === 'STOP') {
            api.documentManage
              .stopTask({
                id: row.id,
              })
              .then((res) => {
                methods.onSearch(false)
              })
          }
        },
        runTask(row) {
          const fn = state.activeName === 'SFTP' ? 'runExecuteTask' : 'runExecuteApiTask'
          api.documentManage[fn]({
            id: row.id,
          }).then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '操作成功！',
                type: 'success',
              })
              methods.onSearch(false)
            }
          })
        },
        onDelete(row) {
          const deleteName = state.activeName === 'SFTP' ? '采集' : 'API'
          const fn = state.activeName === 'SFTP' ? 'taskDelete' : 'taskApiDelete'
          proxy.$MessageBoxService.open({
            title: `是否确认该条${deleteName}任务`,
            content: '删除后将不可恢复',
            save: () => {
              api.documentManage[fn]({ id: row.id }).then((res) => {
                if (res.success) {
                  methods.onSearch()
                  ElNotification({
                    title: '提示',
                    message: '操作成功！',
                    type: 'success',
                  })
                }
              })
            },
          })
        },
        editApi(row) {
          router.push({ name: 'documentCollectionApiAdd', query: { apiId: row.id } })
        },
        // 取消
        cancel() {
          router.go(-1)
        },
        handleClick(id) {
          if (id === 'API') {
            state.tableHeadTitles = [
              { prop: 'filelistName', name: '非结构化数据清单接口', width: '150' },
              { prop: 'fileinfoName', name: '非结构化数据详情接口' },
              { prop: 'categoryName', name: '非结构化数据分类' },
              { prop: 'duplicateLogic', name: '重复处理', slot: 'duplicateLogic' },
              { prop: 'lastStartTime', name: '采集时间', width: '200' },
              { prop: 'statusName', name: '状态' },
              { prop: 'collectProgress', name: '采集进度' },
            ]
          } else if (id === 'SFTP') {
            state.tableHeadTitles = [
              { prop: 'host', name: 'IP', width: '150' },
              { prop: 'port', name: '端口' },
              { prop: 'filepath', name: '文件提取地址' },
              { prop: 'duplicateLogicName', name: '重复处理' },
              { prop: 'confidentialityName', name: '密级', slot: 'tagList' },
              { prop: 'scheduleModeName', name: '调度信息' },
              { prop: 'lastTime', name: '最近一次采集时间', width: '160' },
              { prop: 'status', name: '状态', slot: 'status' },
            ]
          }
          methods.onSearch()
        },
      }
      onMounted(async () => {
        state.categoryId = router.currentRoute.value.query.categoryId || null
        if (state.categoryId) {
          state.categoryId = Number(state.categoryId)
        }
        methods.onSearch()
      })

      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .doc-collection {
    height: 100%;
    padding: 16px;
    .add-box {
      margin-bottom: 10px;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
    }
    .base-tabs {
      background-color: #fff;
      :deep(.nancalui-tabs-nav-tab) {
        padding-left: 16px;
      }
    }
    .add-container {
      height: calc(100% - 105px);
      background-color: #fff;
    }
    .asideTree-search-input {
      :deep(.nancalui-input__wrapper) {
        border: 1px solid #e5e6eb !important;
        border-radius: 2px !important;
        &:hover {
          z-index: 99;
        }
      }
      .search-btn {
        box-sizing: border-box;
        width: 30px;
      }
      :deep(.nancalui-input-slot__append) {
        box-sizing: border-box;
        min-height: 16px;
        padding-right: 0;
        border-color: #e5e6eb;
      }
    }
    .table {
      height: calc(100% - 56px);
      background-color: #fff;
      .level-tag {
        width: 40px;
        height: 20px;
        font-weight: 400;
        font-size: 12px;
        font-style: normal;
        line-height: 20px;
        text-align: center;
        border: 1px solid;
        border-radius: 2px;
        &.PUBLIC {
          color: #1aa4ee;
          background: rgba(26, 164, 238, 0.08);
          border-color: rgba(26, 164, 238, 0.4);
        }
        &.INTERIOR {
          color: #fe8624;
          background: rgba(255, 244, 230, 1);
          border-color: rgba(255, 186, 112, 1);
        }
        &.CONTROLLED {
          color: #1e89ff;
          background: rgba(153, 201, 255, 1);
          border-color: rgba(30, 137, 255, 1);
        }
        &.SECRET {
          color: #d40000;
          background: rgba(255, 237, 237, 1);
          border-color: rgba(239, 119, 119, 1);
        }
        &.CONFIDENTIAL {
          color: #7a0000;
          background: rgba(122, 0, 0, 0.08);
          border-color: rgba(122, 0, 0, 0.4);
        }
        &.CORE {
          color: #224ecd;
          background: rgba(34, 78, 205, 0.08);
          border-color: rgba(34, 78, 205, 0.4);
        }
      }
      :deep(.nancalui-switch__inner) {
        // font-size: 12px;
      }
    }
  }
</style>
