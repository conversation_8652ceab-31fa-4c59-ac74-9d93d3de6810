<svg width="18" height="8" viewBox="0 0 18 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Component 472">
<g id="Ellipse 975" filter="url(#filter0_i_261_19390)">
<circle cx="14" cy="4" r="4" fill="#F6F7FB"/>
</g>
<g id="Rectangle 3203" filter="url(#filter1_d_261_19390)">
<rect x="1" y="2" width="14" height="4" rx="2" fill="url(#paint0_linear_261_19390)"/>
</g>
</g>
<defs>
<filter id="filter0_i_261_19390" x="10" y="0" width="8" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.164706 0 0 0 0 0.196078 0 0 0 0 0.352941 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_261_19390"/>
</filter>
<filter id="filter1_d_261_19390" x="0" y="1" width="16" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_261_19390"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_261_19390" result="shape"/>
</filter>
<linearGradient id="paint0_linear_261_19390" x1="1" y1="5" x2="15" y2="5" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDF0F5"/>
<stop offset="0.302083" stop-color="white"/>
<stop offset="0.802083" stop-color="white"/>
<stop offset="1" stop-color="#EBEEF3"/>
</linearGradient>
</defs>
</svg>
