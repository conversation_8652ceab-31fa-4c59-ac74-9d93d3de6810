<?xml version="1.0" encoding="UTF-8"?>
<svg width="132px" height="154px" viewBox="0 0 132 154" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>资产体系</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#0F489C" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.018401%" id="linearGradient-2">
            <stop stop-color="#0E203C" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F5C73" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9780798%" x2="100%" y2="49.9780798%" id="linearGradient-3">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0206526%" x2="100%" y2="50.0206526%" id="linearGradient-4">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.0459347726%" id="linearGradient-5">
            <stop stop-color="#56B0FF" stop-opacity="0.6" offset="0%"></stop>
            <stop stop-color="#50ACFF" stop-opacity="0.56" offset="4%"></stop>
            <stop stop-color="#369BFF" stop-opacity="0.4" offset="26%"></stop>
            <stop stop-color="#218EFF" stop-opacity="0.27" offset="46%"></stop>
            <stop stop-color="#1285FF" stop-opacity="0.18" offset="66%"></stop>
            <stop stop-color="#097FFF" stop-opacity="0.12" offset="84%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0087951%" y1="100%" x2="50.0087951%" y2="0%" id="linearGradient-6">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-7">
            <stop stop-color="#DBFDFF" offset="0%"></stop>
            <stop stop-color="#2AD0FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0554939%" x2="100%" y2="50.0554939%" id="linearGradient-8">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0530223%" x2="100%" y2="50.0530223%" id="linearGradient-9">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-10">
            <stop stop-color="#56B0FF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#379CFF" stop-opacity="0.1" offset="24%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9941072%" y1="100%" x2="49.9941072%" y2="0%" id="linearGradient-11">
            <stop stop-color="#DEEDFF" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#D5E9FF" stop-opacity="0.28" offset="21%"></stop>
            <stop stop-color="#BCDEFF" stop-opacity="0.22" offset="45%"></stop>
            <stop stop-color="#92CBFF" stop-opacity="0.13" offset="72%"></stop>
            <stop stop-color="#57B1FF" stop-opacity="0" offset="100%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-12">
            <stop stop-color="#DAEAFF" offset="0%"></stop>
            <stop stop-color="#D5E8FF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#C8E1FF" stop-opacity="0.92" offset="24%"></stop>
            <stop stop-color="#B1D6FF" stop-opacity="0.83" offset="38%"></stop>
            <stop stop-color="#92C7FF" stop-opacity="0.7" offset="53%"></stop>
            <stop stop-color="#69B4FF" stop-opacity="0.53" offset="69%"></stop>
            <stop stop-color="#389CFF" stop-opacity="0.33" offset="84%"></stop>
            <stop stop-color="#0081FF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-13">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.011786893%" y1="50%" x2="100%" y2="50%" id="linearGradient-14">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="33%"></stop>
            <stop stop-color="#00BFFC" offset="41%"></stop>
            <stop stop-color="#00A4FB" offset="50%"></stop>
            <stop stop-color="#0091FA" offset="58%"></stop>
            <stop stop-color="#0086F9" offset="67%"></stop>
            <stop stop-color="#0082F9" offset="76%"></stop>
            <stop stop-color="#A5E9E9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9773037%" x2="100%" y2="49.9773037%" id="linearGradient-15">
            <stop stop-color="#56B0FF" offset="0%"></stop>
            <stop stop-color="#52ADFF" offset="3%"></stop>
            <stop stop-color="#3198FF" offset="30%"></stop>
            <stop stop-color="#1989FF" offset="56%"></stop>
            <stop stop-color="#0B80FF" offset="80%"></stop>
            <stop stop-color="#067DFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9913059%" y1="100%" x2="49.9913059%" y2="0%" id="linearGradient-16">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9656829%" x2="100%" y2="49.9656829%" id="linearGradient-17">
            <stop stop-color="#00459E" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#004184" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-18">
            <stop stop-color="#56E3FF" stop-opacity="0.887877138" offset="0%"></stop>
            <stop stop-color="#06B8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9884232%" y1="100%" x2="49.9884232%" y2="0%" id="linearGradient-19">
            <stop stop-color="#5098FF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#08B5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-0.12345679%" y1="50.1040944%" x2="100.123457%" y2="50.1040944%" id="linearGradient-20">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DEEDFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#009EED" offset="0%"></stop>
            <stop stop-color="#0063D4" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-22" points="22.6009016 8.76785557 0 16.9877202 0.81675948 0 13.4532151 4.9855266"></polygon>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="统一登陆页" transform="translate(-553.000000, -443.000000)">
            <g id="资产体系" transform="translate(490.000000, 355.000000)">
                <g transform="translate(63.000000, 88.000000)">
                    <g id="头像底座" transform="translate(0.000000, 56.309215)">
                        <g id="底座2-link" transform="translate(0.000000, 25.743916)" fill-rule="nonzero">
                            <g id="底座2">
                                <g id="编组" transform="translate(0.000000, 18.934837)">
                                    <path d="M67.2031222,0.267661973 L131.300963,26.0585875 L64.8085458,50.2407826 L0.710705255,24.4498571 L67.2031222,0.267661973 Z" id="路径" stroke="url(#linearGradient-2)" stroke-width="0" fill="url(#linearGradient-1)"></path>
                                    <polygon id="路径" fill="url(#linearGradient-3)" points="132 26.0703791 131.988332 28.2273648 64.7923628 52.6654303 64.8040308 50.5084446"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-4)" points="64.8040308 50.5084446 64.7923628 52.6654303 0 26.5950513 0.0116679926 24.4380655"></polygon>
                                </g>
                                <g id="编组" transform="translate(32.845399, 27.259636)">
                                    <polygon id="路径" fill="url(#linearGradient-5)" points="33.7671705 1.46908218 66.3325378 14.574228 32.5653673 26.8515577 0.0116679926 13.7580712"></polygon>
                                    <path d="M33.7555025,2.09868883 L64.6990188,14.5509092 L32.5770353,26.2102917 L1.63351896,13.78139 L33.7555025,2.1220076 M33.7555025,1.50406033 L0,13.7580712 L32.5653673,26.8515577 L66.3325378,14.574228 L33.7671705,1.48074157 L33.7555025,1.50406033 Z" id="形状" fill="url(#linearGradient-6)"></path>
                                    <polygon id="路径" fill="url(#linearGradient-7)" points="33.5221427 0 52.7509944 7.74182992 32.8103951 14.9939658 13.5698754 7.25213586"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-8)" points="52.7509944 7.74182992 52.7276584 10.9947976 32.7753911 18.2469335 32.8103951 14.9939658"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-9)" points="32.8103951 14.9939658 32.7753911 18.2469335 13.5465394 10.5167629 13.5698754 7.25213586"></polygon>
                                </g>
                                <g id="编组" transform="translate(16.510209, 0.000000)">
                                    <polygon id="路径" fill="url(#linearGradient-10)" points="50.4057279 0 98.991249 19.5527843 48.5971891 37.8813334 2.07265184e-15 18.3285491"></polygon>
                                    <path d="M50.3940599,0.62960665 L97.35773,19.5178062 L48.5971891,37.3100237 L1.63351896,18.3518679 L50.3940599,0.62960665 M50.3940599,0 L2.07265184e-15,18.3285491 L48.5971891,37.8813334 L99.002917,19.5527843 L50.4057279,0 L50.3940599,0 Z" id="形状" fill="url(#linearGradient-11)"></path>
                                </g>
                                <g id="编组" transform="translate(32.472023, 4.780347)">
                                    <polygon id="路径" fill="url(#linearGradient-12)" points="34.1522143 0 67.0676213 13.2450584 32.927075 25.6623007 0 12.4289016"></polygon>
                                    <path d="M34.1405463,0.62960665 L65.4341024,13.2217397 L32.927075,25.0443534 L1.63351896,12.4522204 L34.1405463,0.62960665 M34.1405463,0 L0,12.4289016 L32.927075,25.6623007 L67.0676213,13.2450584 L34.1522143,0 L34.1405463,0 Z" id="形状" fill="url(#linearGradient-13)"></path>
                                </g>
                            </g>
                        </g>
                        <polygon id="路径" fill="url(#linearGradient-14)" fill-rule="nonzero" points="66.9042694 25.6856194 115.489791 45.2267444 65.0957306 63.5552935 16.4985415 44.0141686"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-15)" fill-rule="nonzero" points="67.7210289 0 100.659772 13.2567178 66.4958897 25.6856194 33.5571466 12.4289016"></polygon>
                        <path d="M67.6743569,0.62960665 L98.991249,13.2217397 L66.5075577,25.0560128 L35.1789976,12.4522204 L67.6743569,0.62960665 M67.6743569,0 L33.5571466,12.4289016 L66.5075577,25.6739601 L100.659772,13.2567178 L67.6743569,0 Z" id="形状" fill="url(#linearGradient-16)" fill-rule="nonzero"></path>
                        <polygon id="路径" fill="url(#linearGradient-17)" fill-rule="nonzero" points="67.511005 4.34894964 89.2951472 13.1168052 66.6942456 21.3366698 44.9101034 12.5688142"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-18)" fill-rule="nonzero" points="33.5571466 12.4289016 16.4985415 44.0141686 65.0957306 63.5552935 66.9042694 25.6856194"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-19)" fill-rule="nonzero" points="100.659772 13.2567178 66.4958897 25.6856194 65.0957306 63.5552935 115.489791 45.2267444"></polygon>
                        <polyline id="路径" stroke="url(#linearGradient-20)" stroke-width="0" points="108.523999 31.4570137 65.2240785 47.2088394 23.4643331 30.4076693"></polyline>
                        <g id="Clipped" transform="translate(66.694246, 4.348950)">
                            <mask id="mask-23" fill="white">
                                <use xlink:href="#path-22"></use>
                            </mask>
                            <use id="路径" fill="url(#linearGradient-21)" xlink:href="#path-22"></use>
                        </g>
                    </g>
                    <g id="编组-13" transform="translate(35.058797, 0.000000)">
                        <g id="编组-12" transform="translate(0.000000, 41.594767)">
                            <path d="M4.54608578,4.48167576 L15.4566917,0.611010209 C17.9674255,-0.279702107 20.0027774,1.02489522 20.0027774,3.52490989 L20.0027774,14.3889347 C20.0027774,16.8889493 17.9674255,19.6376767 15.4566917,20.528389 C15.0429929,25.2625559 14.2058729,27.8532353 12.9453314,28.3004271 C11.7812938,28.7133832 8.30952788,27.4952172 2.53003376,24.6459292 C0.843344586,23.724805 0,22.6712135 0,21.4851549 L0,10.6211301 C0,8.1211154 2.03535193,5.37238808 4.54608578,4.48167576 Z" id="矩形备份" fill="#009FFB"></path>
                            <path d="M14.8248628,8.10301736 L25.7354687,4.2323518 C28.2462025,3.34163949 30.2815544,4.64623681 30.2815544,7.14625149 L30.2815544,18.0102763 C30.2815544,20.5102909 28.2462025,23.2590183 25.7354687,24.1497306 L14.8248628,28.0203961 C12.3141289,28.9111084 10.278777,27.6065111 10.278777,25.1064965 L10.278777,14.2424717 C10.278777,11.742457 12.3141289,8.99372967 14.8248628,8.10301736 Z" id="矩形" fill="#9BF4FB"></path>
                            <path d="M11.0757759,10.9458269 L1.36526106,7.02528671 C1.80652559,6.38523289 2.2357002,5.89498524 2.65278488,5.55454376 C2.93625686,5.3231624 4.12748512,4.629247 4.37961783,4.52152261 C6.83056308,3.47434951 10.8312358,2.06217075 16.3816358,0.284986323 C16.9593631,0.100010744 17.5825499,0.114969196 18.1507346,0.327456687 L28.2643569,4.10970956 L28.2643569,4.10970956 C21.7313029,6.20607249 17.6267287,7.56587085 15.9506342,8.18910464 C13.4364924,9.12395532 13.1776993,9.12105596 12.2341581,9.89596527 C11.6051306,10.4125715 11.2190032,10.7625254 11.0757759,10.9458269 Z" id="路径-7" fill="#009FFB"></path>
                            <path d="M2.32305474,5.85557875 C2.53920159,5.66301599 2.72508719,5.50740624 2.88071156,5.38874949 C3.31152656,5.06027195 3.90920793,4.7205504 4.37961783,4.52152261 C8.26422723,2.87796614 13.6259778,1.19276113 15.7646821,0.574471911 C15.4239907,1.39281843 16.4256706,3.05284239 18.7697219,5.55454376 L15.701908,6.58066121 L2.32305474,5.85557875 Z" id="路径-7" fill="#206DC4"></path>
                        </g>
                        <g id="编组-12备份" transform="translate(30.820921, 30.730742)">
                            <path d="M4.54608578,4.48167576 L15.4566917,0.611010209 C17.9674255,-0.279702107 20.0027774,1.02489522 20.0027774,3.52490989 L20.0027774,14.3889347 C20.0027774,16.8889493 17.9674255,19.6376767 15.4566917,20.528389 C15.4063075,25.2000066 14.7508448,27.7594114 13.4903033,28.2066033 C12.3262657,28.6195593 8.67284248,27.432668 2.53003376,24.6459292 C0.843344586,23.724805 0,22.6712135 0,21.4851549 L0,10.6211301 C0,8.1211154 2.03535193,5.37238808 4.54608578,4.48167576 Z" id="矩形备份" fill="#008FE2"></path>
                            <path d="M14.8248628,8.10301736 L25.7354687,4.2323518 C28.2462025,3.34163949 30.2815544,4.64623681 30.2815544,7.14625149 L30.2815544,18.0102763 C30.2815544,20.5102909 28.2462025,23.2590183 25.7354687,24.1497306 L14.8248628,28.0203961 C12.3141289,28.9111084 10.278777,27.6065111 10.278777,25.1064965 L10.278777,14.2424717 C10.278777,11.742457 12.3141289,8.99372967 14.8248628,8.10301736 Z" id="矩形" fill="#9BF4FB"></path>
                            <path d="M11.0757759,10.9458269 L1.36526106,7.02528671 C1.80652559,6.38523289 2.2357002,5.89498524 2.65278488,5.55454376 C2.93625686,5.3231624 4.12748512,4.629247 4.37961783,4.52152261 C6.83056308,3.47434951 10.8312358,2.06217075 16.3816358,0.284986323 C16.9593631,0.100010744 17.5825499,0.114969196 18.1507346,0.327456687 L28.2643569,4.10970956 L28.2643569,4.10970956 C21.7313029,6.20607249 17.6267287,7.56587085 15.9506342,8.18910464 C13.4364924,9.12395532 13.1776993,9.12105596 12.2341581,9.89596527 C11.6051306,10.4125715 11.2190032,10.7625254 11.0757759,10.9458269 Z" id="路径-7" fill="#009FFB"></path>
                            <path d="M2.08352502,6.1388106 C2.15248312,5.95118063 2.41397847,5.69350419 2.86801108,5.36578128 C3.17130331,5.1468635 4.12748512,4.629247 4.37961783,4.52152261 C4.79057138,4.34594157 5.6136267,4.00140185 6.91180498,3.51951914 C7.62181885,3.25596257 9.03917176,2.74642686 11.1638637,1.99091201 L20.9495406,4.40326625 L17.7157285,5.39621044 L2.08352502,6.1388106 Z" id="路径-7" fill="#206DC4"></path>
                        </g>
                        <path d="M48.4578172,36.7135541 L51.3508578,30.098944 C51.356097,29.0299664 48.4670365,28.0698888 47.3854704,27.7753889 C46.3039044,27.4808889 43.7908106,27.3998513 42.6467328,27.7746833 L32.0522408,30.8344182 L33.0521255,22.9450872 L27.0606977,23.9482519 L27.0683071,32.5784576 L15.216813,36.7593573 C14.0734702,37.1339484 12.9850255,37.8466719 12.1536742,38.7600843 C11.2304111,39.7748737 10.7057281,40.9717816 10.7122374,42.037366 L10.6142949,44.8420709 L15.1738609,47.6115454 C16.1575888,44.7457057 16.6473241,42.8995433 16.6430667,42.0730582 C16.6413402,41.7378798 17.1772851,40.1493317 17.9968793,39.8807343 L43.6369546,30.8709523 C44.4565488,30.6023549 44.9987308,30.8701159 44.9975614,31.165802 L45.169336,34.9390083 L48.4578172,36.7135541 Z" id="路径" fill="#0751A8" transform="translate(30.982576, 35.278316) rotate(-2.000000) translate(-30.982576, -35.278316) "></path>
                        <path d="M27.1131534,32.859478 L15.2616593,37.0403777 C14.1183165,37.4149688 13.0298718,38.1276923 12.1985206,39.0411046 C11.2752574,40.0558941 16.6528639,41.1449801 18.0417256,40.1617547 C24.6015739,37.8566599 29.5214602,36.1278389 32.8013843,34.9752915 C35.2192547,34.1256651 33.3231777,33.4203939 27.1131534,32.859478 Z" id="路径" fill="#206DC4" transform="translate(22.955176, 36.703449) rotate(-2.000000) translate(-22.955176, -36.703449) "></path>
                        <path d="M48.2623358,36.4124195 L51.1553763,29.7978094 C51.1606155,28.7288317 48.271555,27.7687542 47.189989,27.4742543 C46.1084229,27.1797543 43.5953291,27.0987167 42.4512513,27.4735486 L31.8567593,30.5332836 L32.856644,22.6439526 L27.866286,23.6820754 L27.8738953,32.3122811 L32.8101665,34.3056006 L43.4414731,30.5698177 C44.2610673,30.3012203 44.8032493,30.5689813 44.8020799,30.8646674 L44.9738545,34.6378737 L48.2623358,36.4124195 Z" id="路径" fill="#206DC4" transform="translate(39.510831, 29.528186) rotate(-2.000000) translate(-39.510831, -29.528186) "></path>
                        <polygon id="路径" fill="#0751A8" transform="translate(29.828214, 28.759746) rotate(-2.000000) translate(-29.828214, -28.759746) " points="31.8240427 30.870734 32.8239274 22.981403 26.8324997 23.9845676 26.8401091 32.6147733 32.4203591 34.5380889"></polygon>
                        <path d="M48.375794,36.1560469 L51.2688306,29.5414369 C51.2723261,28.8287851 48.3673364,29.0744685 42.5538617,30.2784871 C43.3734559,30.0098896 44.9167075,30.3126087 44.9155382,30.6082949 L44.7889683,34.2327829 L48.375794,36.1560469 Z" id="路径" fill="#044490" transform="translate(46.911346, 32.650010) rotate(-2.000000) translate(-46.911346, -32.650010) "></path>
                        <path d="M51.7697825,35.7548919 L51.7930629,31.0193209 C51.7982704,29.9503432 51.2716197,29.1026167 50.3482613,28.6907992 C49.5154146,28.3232888 48.4272839,28.3240134 47.2832061,28.6988454 L34.8684701,32.468242 L34.8691042,23.8340204 L31.697171,24.872414 L31.7047804,33.5026197 L19.8532863,37.6835195 C18.7099434,38.0581105 17.6214988,38.770834 16.7901475,39.6842464 C15.8668844,40.6990358 15.3422014,41.8959437 15.3487107,42.9615281 L15.1893773,47.5242391 L18.3877697,46.5471981 C18.4761556,44.9645583 18.5182199,43.7599958 18.5139625,42.9335106 C18.5122359,42.5983323 19.0481809,41.0097842 19.8677751,40.7411867 L47.2723581,31.7601563 C48.0919523,31.4915589 48.6341343,31.7593199 48.6329649,32.055006 L48.4733336,36.6262478 L51.7697825,35.7548919 Z" id="路径" fill="#008FE2" transform="translate(33.491220, 35.679130) rotate(-2.000000) translate(-33.491220, -35.679130) "></path>
                        <g id="编组-12备份-2" transform="translate(14.547474, 0.000000)">
                            <path d="M4.54608578,4.43101432 L15.4566917,0.560348767 C17.9674255,-0.330363549 20.0027774,0.974233776 20.0027774,3.47424845 L20.0027774,14.3382732 C20.0027774,16.8382879 17.9674255,19.5870152 15.4566917,20.4777275 C15.1364696,25.2641691 14.3460879,27.8809858 13.0855465,28.3281777 C11.9215088,28.7411337 8.4030046,27.4968304 2.53003376,24.5952678 C0.843344586,23.6741435 0,22.6205521 0,21.4344934 L0,10.5704686 C0,8.07045396 2.03535193,5.32172664 4.54608578,4.43101432 Z" id="矩形备份" fill="#009FFB"></path>
                            <path d="M14.8248628,8.05235592 L25.7354687,4.18169036 C28.2462025,3.29097805 30.2815544,4.59557537 30.2815544,7.09559004 L30.2815544,17.9596148 C30.2815544,20.4596295 28.2462025,23.2083568 25.7354687,24.0990691 L14.8248628,27.9697347 C12.3141289,28.860447 10.278777,27.5558497 10.278777,25.055835 L10.278777,14.1918102 C10.278777,11.6917956 12.3141289,8.94306823 14.8248628,8.05235592 Z" id="矩形" fill="#9BF4FB"></path>
                            <path d="M11.0757759,10.8951655 L1.36526106,6.97462527 C1.80652559,6.33457144 2.2357002,5.8443238 2.65278488,5.50388232 C2.93625686,5.27250096 4.12748512,4.57858555 4.37961783,4.47086116 C6.82645223,3.42544445 10.6959643,2.03142789 15.9881541,0.288811504 C16.5577733,0.101261467 17.173622,0.10867568 17.7385562,0.309897747 L28.2643569,4.05904812 L28.2643569,4.05904812 C21.7313029,6.15541105 17.6267287,7.51520941 15.9506342,8.1384432 C13.4364924,9.07329388 13.1776993,9.07039451 12.2341581,9.84530383 C11.6051306,10.36191 11.2190032,10.7118639 11.0757759,10.8951655 Z" id="路径-7" fill="#009FFB"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>