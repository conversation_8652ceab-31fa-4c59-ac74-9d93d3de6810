<template>
  <div :class="['container-padding16 ']">
    <div class="content-box scroll-bar-style">
      <div class="content-box-row">
        <div class="col">
          <div class="col-content">
            <div class="col-content-filter">
              <n-tree-select
                v-model="state.formFilter.assetClassify"
                :treeData="state.assetClassifyOptions"
                placeholder="请选择"
                node-key="id"
                :prop="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }"
                filter
                @value-change="totalCount"
              >
                <template #icon="{ nodeData, toggleNode }">
                  <span
                    @click="
                      (event) => {
                        event.stopPropagation()
                        toggleNode(nodeData)
                      }
                    "
                  >
                    <SvgIcon
                      v-if="nodeData.expanded"
                      style="font-size: 16px; margin-right: 4px"
                      icon="tree-contract-new"
                    />
                    <SvgIcon
                      v-else
                      style="font-size: 16px; margin-right: 4px"
                      icon="tree-open-new"
                    />
                  </span> </template
              ></n-tree-select>
            </div>
            <div class="col-content-row">
              <div class="col-content-row-item">
                <div class="col-content-row-item-content">
                  {{ state.tableCount }}
                </div>
                <div class="col-content-row-item-title" unit-text=""> 服务表 </div>
                <SvgIcon icon="new-service-table-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content">
                  {{ state.apiCount }}
                </div>
                <div class="col-content-row-item-title" unit-text=""> 服务接口总数</div>
                <SvgIcon icon="new-service-count-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content">
                  {{ state.objectCount }}
                </div>
                <div class="col-content-row-item-title" unit-text=""> 服务对象</div>
                <SvgIcon icon="new-service-obj-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content">
                  {{ state.callSuccessfulCount }}/{{ state.callTotalCount }}
                </div>
                <div class="col-content-row-item-title" unit-text=""> 调用成功/总次数</div>
                <SvgIcon icon="new-service-use-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content" unit-text="(条)">
                  {{ state.distributeCount }}
                </div>
                <div class="col-content-row-item-title"> 累计分发数据量</div>
                <SvgIcon icon="new-service-datacount-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content" unit-text="(%)">
                  {{ state.distributeSuccessRate * 100 }}
                </div>
                <div class="col-content-row-item-title" unit-text=""> 分发成功率</div>
                <SvgIcon icon="new-service-success-icon" class="service-count-icon" />
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-content" unit-text="(ms)">
                  {{ state.avgCallTime }}
                </div>
                <div class="col-content-row-item-title"> 接口平均调用时长</div>
                <SvgIcon icon="new-service-time-icon" class="service-count-icon" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ref="taskCollectionTrend" class="content-box-row col-1">
        <div class="col">
          <div class="title"> 数据服务趋势 <span class="sub-title">近15天数据服务监控</span> </div>
          <div class="col-content">
            <div
              :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']"
              id="taskCollectionTrend"
            ></div>
          </div>
        </div>
      </div>
      <div class="table-box">
        <CfTable
          :actionWidth="120"
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.tableData.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              getApiStatisticsPageList()
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              getApiStatisticsPageList(true)
            },
          }"
        >
          <template #pageTop>
            <div class="table-title">
              <span>数据服务统计</span>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, toRefs, onBeforeUnmount } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import * as echarts from 'echarts'
  import { timestampToTime } from '@/const/public.js'
  export default {
    title: 'List',
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        apiCount: 0,
        avgCallTime: 0,
        callSuccessfulCount: 0,
        callTotalCount: 0,
        distributeCount: 0,
        tableCount: 0,
        objCount: 0,
        distributeSuccessRate: 0,

        tableData: {},
        tableHeight: 436,
        formFilter: {
          assetClassify: 0,
          tableName: 0,
        },
        assetClassifyOptions: [
          {
            name: '全部API分类',
            id: 0,
          },
        ],

        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'assetTable', name: '表英文名', width: 350 },
          { prop: 'assetTableCN', name: '表中文名' },
          { prop: 'apiCount', name: '接口数', width: 160 },
          { prop: 'callCount', name: '累计调用次数', width: 160 },
          { prop: 'distributeCount', name: '累计分发数据量', width: 250 },
          { prop: 'avgCallTime', name: '平均调用时长(ms)' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
      })

      let myChart = null
      const methods = {
        // 请求分类
        async getClassifyTreeList() {
          const res = await api.documentManage.apiCategoryTree()
          state.assetClassifyOptions = [
            {
              id: 0,
              name: '全部API分类',
              pid: null,
              children: res.data,
              expanded: true,
            },
          ]
        },
        // 获取服务总体概况数据
        totalCount() {
          api.documentManage
            .getApiStatisticsSurvey({
              categoryId: state.formFilter.assetClassify,
            })
            .then((res) => {
              let { success, data } = res
              if (success) {
                Object.keys(data).forEach((key) => {
                  state[key] = data[key]
                })
                // state.tableCount = data
              }
            })
        },
        //获取数据服务趋势
        getApiTrends() {
          let timestamp = new Date().getTime() // 当前日期时间戳
          let endDate = timestampToTime(timestamp),
            startDate = timestampToTime(timestamp - 14 * 24 * 60 * 60 * 1000)

          let chartDom = document.getElementById('taskCollectionTrend')
          myChart = echarts.init(chartDom)

          myChart.showLoading({
            text: 'loading',
            color: '#c23531',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.2)',
            zlevel: 0,
          })
          api.documentManage
            .getApiStatisticsTrends({ startDate: startDate, endDate: endDate })
            .then((res) => {
              let { success, data } = res
              myChart?.hideLoading()
              if (success) {
                methods.initEcharts(data)
              }
            })
            .catch(() => {
              myChart?.hideLoading()
            })
        },
        //渲染echarts逻辑
        initEcharts(arr) {
          if (!myChart) {
            let chartDom = document.getElementById('taskCollectionTrend')
            myChart = echarts.init(chartDom)
          }

          let xAxisData = [],
            yAxisAddApiData = [],
            yAxisCallApiData = []
          // 获取x轴数据
          xAxisData = arr.addCounts.map((item) => {
            item.day = item.date.slice(5, 10).replace(/-/g, '.')
            return item.day
          })
          // 获取y轴新增api数据
          yAxisAddApiData = arr.addCounts.map((item) => item.addCount)
          // 获取y轴调用api数据
          yAxisCallApiData = arr.callCounts.map((item) => item.callCount)
          let option
          option = {
            Animation: true,
            title: {
              text: '',
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                label: {
                  backgroundColor: '#6a7985',
                },
              },
              formatter: function (params) {
                var showHtm = ''
                for (var i = 0; i < params.length; i++) {
                  //日期
                  var time = params[i].name
                  //名称
                  var name = params[i].seriesName
                  //值
                  var value = params[i].value
                  var color = params[i].color
                  if (i == 0) {
                    showHtm += `
                    <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                    `
                  }
                  showHtm += `
                  <div style='display:flex;justify-content: space-between;margin-top:5px'>
                    <div style='margin-right:10px'>
                      <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                      <span style='font-weight:400'>${name}</span>
                    </div>
                    <span style='font-weight:500;'>${value}</span>
                  </div>
                  `
                }
                return showHtm
              },
              textStyle: {
                fontSize: 12,
                color: '#333',
              },
            },
            color: ['#2ca340', '#2a99ff'], // 设置tooltips展示图标颜色
            legend: {
              itemWidth: 10, //粗细
              itemHeight: 3, //粗细
              data: [
                {
                  name: '新增接口',
                  icon: 'rect',
                  itemStyle: {
                    color: '#2ca340',
                  },
                },
                {
                  name: '调用接口',
                  icon: 'rect',
                  itemStyle: {
                    color: '#2a99ff', // 设置图例圆圈颜色
                  },
                },
              ],
              // data: ['成功任务', '失败任务'],
              // icon: 'circle', //  这个字段控制形状  类型包括 circle 圆形，triangle 三角形，diamond 四边形，arrow 变异三角形，none 无

              itemGap: 50, // 设置间距，
              textStyle: {
                // 图例文字的样式
                color: '#666666',
                fontSize: 12,
              },
              x: 'center', //左（left）、右（right）、居中（center）
              y: 'bottom', //上（top）、下（bottom）、居中（center）
            },

            grid: {
              left: 20,
              right: 50,
              bottom: 40,
              top: 20,
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: xAxisData,
              },
            ],
            yAxis: [
              {
                type: 'value',
                offset: 10,
              },
            ],
            series: [
              {
                name: '新增接口',
                type: 'line',
                // stack: 'Total', //是否堆叠面积
                lineStyle: {
                  color: '#2ca340',
                },
                smooth: false,
                symbol: 'circle',
                symbolSize: 7,
                data: yAxisAddApiData,
                // areaStyle: {
                //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //     {
                //       offset: 1,
                //       color: '#fff', // 0% 处的颜色
                //     },
                //     {
                //       offset: 0,
                //       color: '#2ca340ff', // 100% 处的颜色
                //     },
                //   ]), //背景渐变色
                // },
              },
              {
                name: '调用接口',
                type: 'line',
                symbol: 'circle',
                symbolSize: 7,
                smooth: false,
                data: yAxisCallApiData,
                lineStyle: {
                  color: '#2a99ff',
                },
                // areaStyle: {
                //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //     {
                //       offset: 1,
                //       color: '#ffffff', // 0% 处的颜色
                //     },
                //     {
                //       offset: 0,
                //       color: '#2a99ffff', // 100% 处的颜色
                //     },
                //   ]), //背景渐变色
                // },
              },
            ],
          }
          document.getElementById('taskCollectionTrend').setAttribute('_echarts_instance_', '')
          option && myChart.setOption(option)
        },
        //获取数据服务统计list
        getApiStatisticsPageList(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          api.documentManage
            .getApiStatisticsPageList({
              pageNum: state.pagination.currentPage,
              pageSize: state.pagination.pageSize,
            })
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.tableData = data
              }
            })
        },

        //获取指定几天前的日期
        getSomeDayAgoTime(someDay = 1, split = '-') {
          let times = new Date().getTime() - someDay * 24 * 60 * 60 * 1000
          let _someDayAgoTime = new Date(times)

          let year = _someDayAgoTime.getFullYear()
          let month =
            _someDayAgoTime.getMonth() + 1 < 10
              ? '0' + (_someDayAgoTime.getMonth() + 1)
              : _someDayAgoTime.getMonth() + 1
          let day =
            _someDayAgoTime.getDate() < 10
              ? '0' + _someDayAgoTime.getDate()
              : _someDayAgoTime.getDate()

          return year + split + month + split + day
        },
        //设置table高度
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 152 - 50 - 301
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.getApiStatisticsPageList()
        },

        // 屏幕onresize echarts重新绘制
        echartsResize() {
          //echarts重新绘制
          if (myChart) {
            myChart.resize()
          }
        },
      }

      methods.setTableHeight()

      onBeforeUnmount(() => {
        window.onresize = null
        if (myChart) {
          myChart.dispose() //销毁
        }
      })

      onMounted(() => {
        window.onresize = () => {
          methods.echartsResize()
        }
        methods.getClassifyTreeList()
        methods.totalCount()
        methods.getApiTrends()
        methods.getApiStatisticsPageList()
      })

      return {
        state,
        buttonAuthList,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 90px;
  .container-padding16 {
    width: 100%;
    overflow: hidden;
    overflow-y: auto;
    font-family: PingFangSC-Semibold, PingFang SC;

    .content-box {
      height: 100%;
      overflow: auto;
      &-row {
        & + .content-box-row {
          margin-top: 16px;
        }

        .col {
          box-sizing: border-box;
          overflow: hidden;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
          .title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            height: 52px;
            padding: 0 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            border-bottom: 1px solid #dcdfe6;

            .illustrate {
              margin-left: 8px;
              color: #8091b7;
              &:hover {
                color: $themeBlue;
              }
            }
            .sub-title {
              color: var(---, rgba(0, 0, 0, 0.55));

              font-weight: 400;
              font-size: 14px;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              line-height: 22px;
            }

            &:before {
              position: absolute;
              top: 17px;
              left: 0;
              width: 4px;
              height: 18px;
              background: #1e89ff;
              content: '';
            }
            span {
              margin-left: 8px;
              color: rgba(0, 0, 0, 0.55);
              font-weight: normal;
              font-size: 14px;
            }
          }
          .col-content {
            padding: 16px;
            .canvas {
              display: flex;
              flex-direction: column;
              gap: 16px;
              align-items: center;
              height: 350px;
            }
            .ranking {
              display: block;
              padding: 8px 24px;
              &-update-time {
                color: var(---, rgba(0, 0, 0, 0.55));

                font-weight: 400;
                font-size: 12px;
                font-family: 'Source Han Sans CN';
                font-style: normal;
                line-height: 20px;
              }
              &-list {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                height: calc(100% - 20px);
                &-item {
                  display: grid;
                  grid-template-columns: 20px 77px 1fr 34px;
                  align-items: center;
                  color: var(----, rgba(0, 0, 0, 0.75));
                  font-weight: 400;
                  font-size: 12px;
                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 20px;
                  &-index {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 16px;
                    margin-right: 4px;
                    color: var(--100, #fff);
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Source Han Sans CN';
                    font-style: normal;
                    line-height: 20px;
                    text-align: right;
                    border-radius: 13px;
                  }
                  &-name {
                    padding: 0 8px 0 4px;
                    overflow: hidden;
                    color: var(--100, rgba(0, 0, 0, 0.75));
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  &-content {
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    border-radius: 4px;
                    // 动画
                    transition: all 0.3s ease-in-out;
                  }
                  &-count {
                    color: var(----, rgba(0, 0, 0, 0.75));
                    font-weight: 400;
                    font-size: 12px;
                    font-family: 'PingFang SC';
                    font-style: normal;
                    line-height: 16px;

                    text-align: center;
                  }
                  &:nth-child(1) {
                    color: #f63838;
                    .ranking-list-item-index {
                      background: #f63838;
                    }
                  }
                  &:nth-child(2) {
                    color: #ff7d00;
                    .ranking-list-item-index {
                      background: #ff7d00;
                    }
                  }
                  &:nth-child(3) {
                    color: #04c495;
                    .ranking-list-item-index {
                      background: #04c495;
                    }
                  }
                  &:nth-child(4) {
                    color: #447dfd;
                    .ranking-list-item-index {
                      background: #447dfd;
                    }
                  }
                }
              }
            }
            &-filter {
              display: flex;
              gap: 16px;
              padding: 0 0 16px;
              .nancalui-tree-select {
                width: 220px;
              }
            }
            &-row {
              display: flex;
              flex-wrap: wrap;
              &-item {
                position: relative;
                display: flex;
                flex: 1 0 0;
                flex-direction: column-reverse;
                align-items: left;
                justify-content: center;
                height: 96px;
                padding: 16px 24px;
                border-radius: 6px;
                &::before {
                  position: absolute;
                  top: 0;
                  right: 16px;
                  bottom: 0;
                  display: block;
                  width: 32px;
                  height: 32px;
                  margin: auto;
                }
                &:nth-child(1) {
                  background: var(---, #2a99ff);
                }
                &:nth-child(2) {
                  background: var(---, #09bfa1);
                }
                &:nth-child(3) {
                  color: #ffffffb2;
                  background: var(---, #909399);
                }
                &:nth-child(4) {
                  background: var(---, #ff9e42);
                }
                &:nth-child(5) {
                  background: var(---, #31b046);
                }
                &:nth-child(6) {
                  color: #ffffffb2;
                  background: var(---, #224ecd);
                }
                &:nth-child(7) {
                  background: var(---, #ef7777);
                }
                &-title {
                  color: var(--100, #fff);

                  font-weight: 500;
                  font-size: 16px;
                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 26px;
                }
                &-content {
                  color: var(--100, #fff);

                  font-weight: 500;
                  font-size: 32px;

                  font-family: 'DIN Alternate';
                  font-style: normal;
                  line-height: 40px;
                  &[unit-text]::after {
                    color: var(--100, #fff);
                    font-weight: 400;
                    font-size: 16px;
                    font-family: 'Source Han Sans CN';
                    font-style: normal;
                    line-height: 24px;
                    content: attr(unit-text);
                  }
                }
                .service-count-icon {
                  position: absolute;
                  bottom: 12px;
                  right: 16px;
                  font-size: 32px;
                  cursor: default;
                }

                &-content-trend {
                  display: flex;
                  gap: 8px;
                  align-items: center;
                  align-self: stretch;
                  color: var(--100, #fff);
                  font-weight: 400;
                  font-size: 12px;
                  font-family: 'Source Han Sans CN';
                  font-style: normal;
                  line-height: 20px;
                  &-item {
                    display: flex;
                    gap: 2px;
                    align-items: center;
                    &-content-icon {
                      vertical-align: text-bottom;
                    }
                  }
                  .yy-icon {
                    width: 14px;
                    height: 14px;
                  }
                }
                .illustrate {
                  position: absolute;
                  top: 20px;
                  right: 16px;
                  margin-left: 8px;
                  color: #8091b7;
                  &:hover {
                    color: $themeBlue;
                  }
                }
              }
              .col-content-row-item + .col-content-row-item {
                margin-left: 16px;
              }
            }
          }
        }
      }
      .table-box {
        margin-top: 16px;
        background-color: #fff;
        border-radius: 4px;
        :deep(.page-top) {
          padding: 0;
          .table-title {
            display: flex;
            align-items: center;
            line-height: 52px;

            span {
              padding-left: 16px;
              color: rgba(0, 0, 0, 0.85);
              font-weight: bolder;
              font-size: 18px;
            }
            &::before {
              display: inline-block;
              width: 4px;
              height: 18px;
              background: #1e89ff;
              content: '';
            }
          }
        }
      }
    }
    .col-2 {
      display: grid;
      grid-template-columns: 66fr 34fr;
      gap: 16px;
      .col > .col-content {
        padding: 24px 0;
      }
    }
    .col-1 {
      .col > .col-content {
        padding: 0;
      }
    }
  }
  .refresh {
    margin-left: auto;
  }
  .icons {
    position: relative;
    & + .icons {
      margin-left: 8px;
    }
    .yy-icon {
      position: relative;
      z-index: 2;
      color: #8091b7;
      cursor: pointer;
      &:hover {
        color: $themeBlue;
      }
    }
    &:hover {
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #e3ecff;
        border-radius: 5px;
        transform: translate(-50%, -50%);
        content: '';
      }
    }
  }
</style>

<style lang="scss">
  @import '@/styles/variables.scss';
  .quality-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: center;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 72px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
  .task-collection-tooltip {
    .quality-tooltip-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #fff;
      border-radius: 4px;
      &-title {
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }
      &-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .circle {
          width: 8px;
          height: 8px;
          background-color: $themeBlue;
          border-radius: 50%;
        }
        .text {
          width: 32px;
          margin: 0 8px;
          color: rgba(0, 0, 0, 0.55);
          font-size: 14px;
          text-align: left;
        }
        .num {
          width: calc(100% - 66px);
          color: rgba(0, 0, 0, 0.75);
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
</style>
