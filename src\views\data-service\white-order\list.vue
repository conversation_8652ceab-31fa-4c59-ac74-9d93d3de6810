<template>
  <!-- 数据服务-白名单列表 -->
  <div :class="['api-management-list container', state.isLzos ? 'isLzos' : '']">
    <div class="list-box" v-loading="state.loading">
      <n-public-table
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="130"
        @tablePageChange="tablePageChange"
      >
        <template #pageTop>
          <div class="box-add">
            <search @handleSearch="handleSearch">
              <template #searchLeft>
                <div class="left">
                  <n-button
                    v-if="buttonAuthList.includes('service_apiWhiteOrder_add')"
                    code="service_apiWhiteOrder_add"
                    color="primary"
                    variant="solid"
                    @click.prevent="goJumpAdd"
                  >
                    新增
                  </n-button>
                </div>
              </template>
            </search>
          </div>
        </template>

        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="buttonAuthList.includes('service_apiWhiteOrder_edit')"
              code="service_apiWhiteOrder_edit"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="authorize(editor)"
              >编辑
            </n-button>
            <n-button
              v-if="buttonAuthList.includes('service_apiWhiteOrder_delete')"
              code="service_apiWhiteOrder_delete"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="deleteApi(editor)"
              >删除
            </n-button>
          </div>
        </template>
      </n-public-table>
    </div>
    <!-- 授权 -->

    <n-modal
      v-model="state.dialogVisible"
      :title="state.authTitle"
      class="has-top-padding"
      bodyClass="middleDialog"
      :close-on-click-overlay="false"
      :append-to-body="false"
      :draggable="false"
      width="680px"
      :before-close="closeDialog"
    >
      <div>
        <n-form ref="formRef" :data="state.form" label-width="80px" :key="state.key">
          <n-form-item
            field="authorizeId"
            label="API名称："
            :rules="[
              {
                required: true,
                type: 'number',
                message: '请选择API名称',
                trigger: 'blur',
              },
            ]"
          >
            <n-select
              v-model="state.form.authorizeId"
              filter
              placeholder="请选择API名称"
              @value-change="selectChange"
            >
              <n-option
                v-for="item in state.form.apiOpts"
                :key="item.id"
                :name="item.apiName"
                :value="item.id"
              />
            </n-select>
          </n-form-item>
        </n-form>
        <div>
          <div class="header-box">
            <div class="number">序号</div>
            <div class="ip-box">授权IP</div>
            <div class="handle-box">操作</div>
          </div>
          <n-form
            ref="form"
            :data="state.authorizeData"
            :rules="state.formRules"
            label-position="top"
          >
            <div
              class="white-data-box"
              v-for="(item, index) in state.authorizeData.ips"
              :key="index"
            >
              <div class="number">{{ index + 1 }}</div>
              <n-form-item
                label=""
                :field="'ips.' + index + '.ip'"
                :rules="{
                  required: true,
                  validator: checkCIp,
                  trigger: 'blur',
                }"
              >
                <n-input v-model="item.ip" size="small" maxlength="30" placeholder="请输入授权ip" />
              </n-form-item>
              <div class="userHandle">
                <!-- v-show="index === state.authorizeData.ips.length - 1" -->
                <!-- v-show="state.authorizeData.ips.length > 1" -->
                <div class="handle-box" @click="addParameters"
                  ><SvgIcon icon="filter-add" class="add" title="添加"
                /></div>
                <div
                  :class="{ 'handle-box': true, disabled: state.authorizeData.ips.length <= 1 }"
                  @click="removeParameters(index)"
                >
                  <SvgIcon icon="filter-cut" class="reduce" title="删除"
                /></div>
              </div>
            </div>
          </n-form>
        </div>
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            size="sm"
            color="primary"
            variant="solid"
            :loading="state.submiting"
            @click.prevent="apiAuth"
            >确 定</n-button
          >
          <n-button size="sm" @click.prevent="closeDialog">取 消</n-button></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs, getCurrentInstance } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { checkCIp } from '@/utils/validate'

  export default {
    title: 'List',
    components: { search },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const form = ref()
      const formRef = ref()
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        key: 1,
        tableHeight: 436,
        tableData: {},
        loading: false,
        dialogVisible: false,
        authTitle: '新增授权',
        form: {
          authorizeId: '',
          apiName: '',
          apiOpts: [],
        },
        authorizeData: {
          apiName: '',
          ips: [
            {
              ip: '',
            },
          ],
        },
        formRules: {
          ip: [{ required: true, validator: checkCIp, trigger: 'blur' }],
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'ips', name: '授权IP' },
          { prop: 'apiName', name: 'API名称' },
          { prop: 'apiUrl', name: 'API路径' },
          { prop: 'grantTime', name: '授权时间' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        startTime: null,
        endTime: null,
        keyword: null,
        status: '',
      })

      const methods = {
        selectChange() {
          state.key++
        },
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 160 - 50 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 160 - 50
          }
        },
        closeDialog() {
          state.dialogVisible = false
          state.authorizeData = {
            apiName: '',
            ips: [{ ip: '' }],
          }
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage

          let data = {
            condition: {
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              key: state.keyword || null,
            },

            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataService['apiWhiteList'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1, ips: item.ips.join(';') })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 删除
        deleteApi(editor) {
          let { row } = editor
          proxy.$MessageBoxService.open({
            title: '是否确认删除API',
            content: '删除后将不可恢复',
            save: () => {
              api.dataService.delWhite({ id: row.id }).then((res) => {
                let { success, message } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除成功！',
                    type: 'success',
                  })
                  if (state.tableData.length === 1) {
                    methods.initTable(true)
                  } else {
                    methods.initTable()
                  }
                } else {
                  ElNotification({
                    title: '提示',
                    message,
                    type: 'error',
                  })
                }
              })
            },
          })
        },
        //授权
        authorize(editor) {
          let { row } = editor
          state.authTitle = '编辑'
          state.form.authorizeId = row.id
          state.authorizeData.apiName = row.apiName
          api.dataService.getApiAuthInformation({ id: row.id }).then((res) => {
            let { success, data } = res
            if (success) {
              state.dialogVisible = true
              let ips = []
              if (data.ips) {
                data.ips.forEach((item) => {
                  ips.push({ ip: item })
                })
                state.authorizeData.ips = ips
              }
            }
          })
        },
        //授权
        getApiAll() {
          api.dataService.getApiAll().then((res) => {
            let { success, data } = res
            if (success) {
              let _data = data.filter((item) => item.authenticationMode?.includes('IP_WHITELIST'))

              state.form.apiOpts = _data
            }
          })
        },
        //保存授权数据
        apiAuth() {
          formRef.value.validate((flag) => {
            if (flag) {
              let data = {}
              data.id = state.form.authorizeId
              form.value.validate((valid) => {
                if (valid) {
                  let ips = state.authorizeData.ips.map((item) => {
                    return item.ip
                  })
                  data.ips = ips
                  api.dataService.apiAuth(data).then((res) => {
                    let { success } = res
                    if (success) {
                      state.dialogVisible = false
                      ElNotification({
                        title: '提示',
                        message: '授权成功',
                        type: 'success',
                      })
                      methods.initTable()
                    }
                  })
                } else {
                  return false
                }
              })
            }
          })
        },
        //新增授权ip
        addParameters() {
          state.authorizeData.ips.push({
            ip: '',
          })
        },
        //移除授权ip
        removeParameters(index) {
          if (state.authorizeData.ips.length <= 1) return
          state.authorizeData.ips.splice(index, 1)
        },

        // 搜索
        handleSearch(data) {
          let { time, keyword, status } = data
          state.keyword = keyword ? keyword : null
          state.status = status
          if (status === 'all') {
            state.status = null
          }
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }

          methods.initTable(true)
        },
        // 新增api
        goJumpAdd() {
          state.authTitle = '新增授权'
          state.form.authorizeId = ''
          state.authorizeData = {
            apiName: '',
            ips: [{ ip: '' }],
          }
          state.dialogVisible = true
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable(true)
        methods.getApiAll()
      })

      return {
        state,
        buttonAuthList,
        checkCIp,
        form,
        formRef,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    &.isLzos {
      padding: 0;
    }
    .list-box {
      height: 100%;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 4px;

      .box-add {
        .commonForm-search {
          padding: 16px 0;
        }
      }

      .left {
        .nancalui-button {
          background: $themeBlue;
        }
      }

      .seeDetails {
        color: $themeBlue;
      }
    }
  }
  .nancalui-modal {
    .api-box-name {
      height: 20px;
      margin-bottom: 10px;
      color: #333333;
      font-size: 12px;
      line-height: 20px;
    }
    .header-box {
      display: flex;
      width: 100%;
      height: 42px;
      color: #000;
      font-size: 12px;
      line-height: 42px;
      background: #f7f8fa;
      border: 1px solid #e1e1e1;
      .number {
        width: 64px;
        text-align: center;
      }
      .ip-box {
        flex: 1;
      }
      .handle-box {
        width: 94px;
        text-align: center;
      }
    }
    .nancalui-form {
      :deep(.nancalui-form__control-container) {
        max-height: 400px;
        overflow-y: auto;
        .nancalui-select {
          overflow: hidden;
        }
      }
    }
    .white-data-box {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      font-size: 12px;
      // padding: 11px 0;
      border: 1px solid #e1e1e1;
      border-top: none;

      .number {
        width: 64px;
        text-align: center;
      }
      .nancalui-form__item--horizontal {
        flex: 1;
        height: 100%;
        margin-bottom: 0;
      }
      .userHandle {
        display: flex;
        justify-content: flex-end;
        width: 94px;
        font-size: 22px;
        text-align: center;
        .handle-box {
          margin-right: 10px;
          cursor: pointer;
          &.disabled {
            color: #cfcfcf !important;
            cursor: not-allowed;
          }
        }
      }
    }
  }
</style>
