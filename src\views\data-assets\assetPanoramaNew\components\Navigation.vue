<template>
  <div class="frame">
    <div class="overlap-group">
      <div class="navbar">
        <div class="text-wrapper">迪璞数据资产管理系统(DEEP)</div>
        <div class="div">主页</div>
        <div class="text-wrapper-2">设置</div>
        <div class="text-wrapper-3">机密</div>
        <div class="text-wrapper-4">用户名</div>
        <img class="image" alt="Image" src="../assets/logo.png" />
      </div>
      <div class="ellipse" />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
  .frame {
    width: 1920px;
    height: 56px;
    background-color: #1f76cb;
  }

  .frame .overlap-group {
    position: relative;
    top: 12px;
    left: 24px;
    width: 1882px;
    height: 32px;
  }

  .frame .navbar {
    position: absolute;
    top: 0;
    left: 0;
    width: 1882px;
    height: 32px;
  }

  .frame .text-wrapper {
    position: absolute;
    top: 5px;
    left: 38px;
    height: 22px;
    color: #ffffff;
    font-weight: 700;
    font-size: 22px;
    font-family: 'Source <PERSON>s CN-Bold', Helvetica;
    line-height: 22px;
    letter-spacing: 0;
    white-space: nowrap;
  }

  .frame .div {
    position: absolute;
    top: 5px;
    left: 1666px;
    height: 22px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Source Han Sans CN-Bold', Helvetica;
    line-height: 22px;
    letter-spacing: 0;
    white-space: nowrap;
  }

  .frame .text-wrapper-2 {
    position: absolute;
    top: 5px;
    left: 1710px;
    height: 22px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Source Han Sans CN-Bold', Helvetica;
    line-height: 22px;
    letter-spacing: 0;
    white-space: nowrap;
  }

  .frame .text-wrapper-3 {
    position: absolute;
    top: 5px;
    left: 1754px;
    height: 22px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Source Han Sans CN-Bold', Helvetica;
    line-height: 22px;
    letter-spacing: 0;
    white-space: nowrap;
  }

  .frame .text-wrapper-4 {
    position: absolute;
    top: 5px;
    left: 1830px;
    height: 22px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    font-family: 'Source Han Sans CN-Bold', Helvetica;
    line-height: 22px;
    letter-spacing: 0;
    white-space: nowrap;
  }

  .frame .image {
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
    object-fit: cover;
  }

  .frame .ellipse {
    position: absolute;
    top: 2px;
    left: 1798px;
    width: 28px;
    height: 28px;
    background-color: #d9d9d9;
    border-radius: 14px;
  }
</style>
