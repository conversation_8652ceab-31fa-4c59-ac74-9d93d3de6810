<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>更多</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="指标管理" transform="translate(-670.000000, -252.000000)">
            <g id="内容" transform="translate(80.000000, 164.000000)">
                <g id="编组备份" transform="translate(288.000000, 68.000000)">
                    <g id="编组-7" transform="translate(16.000000, 16.000000)">
                        <g id="按钮" transform="translate(282.000000, 0.000000)">
                            <g id="图标按钮/更多" transform="translate(4.000000, 4.000000)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <path d="M3.5,7 C4.05228475,7 4.5,7.44771525 4.5,8 C4.5,8.55228475 4.05228475,9 3.5,9 C2.94771525,9 2.5,8.55228475 2.5,8 C2.5,7.44771525 2.94771525,7 3.5,7 Z M8,7 C8.55228475,7 9,7.44771525 9,8 C9,8.55228475 8.55228475,9 8,9 C7.44771525,9 7,8.55228475 7,8 C7,7.44771525 7.44771525,7 8,7 Z M12.5,7 C13.0522847,7 13.5,7.44771525 13.5,8 C13.5,8.55228475 13.0522847,9 12.5,9 C11.9477153,9 11.5,8.55228475 11.5,8 C11.5,7.44771525 11.9477153,7 12.5,7 Z" id="形状结合" fill="currentColor" fill-rule="nonzero"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
