<template>
  <div class="params-box mt-13">
    <div class="params-item" :key="`resform-${index}`" v-for="(item, index) in dataSource">
      <n-input v-model="item.filedName" placeholder="参数名" :maxlength="50" :disabled="disabled" />
      <n-select
        v-model="item.type"
        :disabled="disabled"
        :options="typeOptions"
        placeholder="请选择类型"
        show-arrow
      />
      <n-input v-model="item.description" placeholder="备注" :maxlength="50" :disabled="disabled" />
      <template v-if="!disabled">
        <SvgIcon class="pointer" icon="add-circle" @click.stop.prevent="onHeadAdd()" />
        <SvgIcon
          class="pointer"
          v-if="dataSource.length > 1"
          icon="icon-del"
          @click.stop.prevent="onHeadDel(index)"
        />
      </template>
    </div>
  </div>
</template>
<script setup>
  import { reactive, ref } from 'vue'
  const props = defineProps(['data', 'disabled'])
  const formRef = ref()
  const dataSource = reactive([])
  function onHeadAdd() {
    dataSource.push({ filedName: '', type: 'txt', description: '' })
  }
  function onHeadDel(index) {
    dataSource.splice(index, 1)
  }
  const typeOptions = [
    {
      value: 'txt',
      label: 'txt',
    },
    {
      value: 'multipartfile',
      label: 'multipartfile',
    },
    {
      value: 'multipartfile[]',
      label: 'multipartfile[]',
    },
  ]
  function finshData() {
    let formBodyObj = {}
    //去除参数名为空的数据
    dataSource.forEach((item, index) => {
      if (item.filedName) {
        formBodyObj[item.filedName] = item
      }
    })

    return JSON.stringify(formBodyObj)
  }

  watch(
    () => props.data,
    () => {
      dataSource.length = 0
      dataSource.push(...props.data)
    },
  )
  defineExpose({ dataSource, finshData })
</script>
<style lang="scss" scoped>
  .params-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 21px 21px;
    grid-column-gap: 4px;
    align-items: center;
    justify-items: end;
    & + .params-item {
      margin-top: 8px;
    }
  }
</style>
