<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd" opacity="0.800000012">
        <g id="图标" transform="translate(-2030.000000, -253.000000)">
            <g id="编组-2" transform="translate(2000.000000, 0.000000)">
                <g id="编组-17" transform="translate(0.000000, 60.000000)">
                    <g id="数据质量" transform="translate(14.000000, 179.000000)">
                        <g id="数据资产-数据血缘（未选中）" transform="translate(16.000000, 14.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="currentColor" fill-rule="nonzero" x="0" y="0" width="20" height="20"></rect>
                            <rect id="矩形" stroke="currentColor" stroke-width="1.25" stroke-linejoin="round" x="7.08333333" y="2.5" width="5.83333333" height="3.75"></rect>
                            <rect id="矩形" stroke="currentColor" stroke-width="1.25" stroke-linejoin="round" x="2.5" y="13.75" width="5.83333333" height="3.75"></rect>
                            <rect id="矩形" stroke="currentColor" stroke-width="1.25" stroke-linejoin="round" x="11.6666667" y="13.75" width="5.83333333" height="3.75"></rect>
                            <line x1="10" y1="6.66666667" x2="10" y2="10" id="路径" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></line>
                            <polyline id="路径" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" points="5.41666667 13.75 5.41666667 10 14.5833333 10 14.5833333 13.75"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>