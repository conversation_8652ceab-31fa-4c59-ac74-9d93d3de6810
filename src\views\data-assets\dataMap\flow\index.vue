<template>
  <section class="flow" id="flow">
    <div id="flowContainer" class="graph"></div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      modal-class="drawer-modal-bg"
      :close-on-click-modal="false"
      :modal="false"
      :with-header="false"
      :size="680"
    >
      <modelDrawer v-if="nodeType === 'node'" @changeStatus="changeStatus" :info="modelInfo" />
      <relationDrawer
        v-if="nodeType === 'edg'"
        @changeStatus="changeStatus"
        :mapData="mapData"
        :info="relationInfo"
      />
    </el-drawer>
  </section>
</template>

<script>
  import { reactive, toRefs, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
  import FlowGraph from './graph'
  import { Model } from '@antv/x6'
  import { DagreLayout } from '@antv/layout'
  import { ElNotification, ElLoading } from 'element-plus'
  import api from '@/api/index'
  import modelDrawer from './components/model'
  import relationDrawer from './components/relation'

  export default {
    components: { modelDrawer, relationDrawer },
    props: {
      checkedItem: Object,
    },
    setup(props) {
      const state = reactive({
        graph: {},
        resizeFn: null,
        cell: {},
        modelInfo: {}, // 模型详情
        relationInfo: {}, // 关系详情
        mapData: {},
        dataModelId: '',
        drawer: false,
        nodeType: '', // node:节点，edg:边
      })
      watch(
        () => props.checkedItem,
        (newVal) => {
          state.dataModelId = newVal.id && newVal.id !== 60 ? newVal.id : null
          if (state.dataModelId) {
            methods.assetsLineage(state.dataModelId)
          }
        },
      )
      const methods = {
        // 初始化创建画布
        initFn() {
          state.graph = FlowGraph.init()
          methods.boundEvent()
          state.resizeFn = () => {
            nextTick(() => {
              methods.resizeFn()
            })
          }
          state.resizeFn()
          window.addEventListener('resize', state.resizeFn)
        },
        // 计算尺寸
        resizeFn() {
          const { width, height } = methods.getContainerSize()
          state.graph.resize(width, height)
          state.graph.centerContent()
        },
        // 获取画布容器大小
        getContainerSize() {
          return {
            width: document.getElementById('flow').offsetWidth,
            height: document.getElementById('flow').offsetHeight,
          }
        },
        // 根据数据表追溯关系
        assetsLineage(dataModelId) {
          let params = {
            // 测试数据
            dataModelId,
          }
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          api.assets.assetsLineage(params).then((res) => {
            if (res.success) {
              if (res.data) {
                state.mapData = res.data
                methods.setMapData()
              } else {
                state.graph.clearCells()
                ElNotification({
                  title: '提示',
                  message: '暂无数据',
                  type: 'warning',
                })
              }
            }
            loading.close()
          })
        },
        // 切换画布数据
        setMapData() {
          const data = (Model.FromJSONData = {
            nodes: [],
            edges: [],
          })
          state.mapData.nodes.forEach((item, index) => {
            data.nodes.push({
              id: item.nId,
              data: item,
              shape: 'my-box',
              size: {
                width: 160,
                height: 52,
              },
              ports: {
                groups: {
                  top: {
                    id: 'top',
                    position: {
                      name: 'absolute',
                      args: { x: 0, y: 0 },
                    },
                    attrs: {
                      circle: {
                        x: 50,
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                  bottom: {
                    id: 'bottom',
                    position: {
                      name: 'absolute',
                      args: { x: 0, y: 0 },
                    },
                    attrs: {
                      circle: {
                        r: 3,
                        magnet: true,
                        stroke: '#697A9A',
                        strokeWidth: 1,
                        fill: '#fff',
                        style: { visibility: 'hidden' },
                      },
                    },
                  },
                },
                items: [
                  {
                    id: 'top',
                    group: 'top',
                    args: {
                      x: 80,
                      y: 0,
                    },
                  },
                  {
                    id: 'bottom',
                    group: 'bottom',
                    args: {
                      x: 80,
                      y: 52,
                    },
                  },
                ],
              },
              zIndex: 1,
            })
          })
          // debugger
          state.mapData.relationship.forEach((item, index) => {
            data.edges.push({
              source: { cell: item.nTo, port: 'bottom' },
              target: { cell: item.nFrom, port: 'top' },
              interacting: true,
              connector: 'rounded',
              router: { name: 'manhattan' },
              data: item,
              attrs: {
                line: {
                  stroke: '#666666',
                  strokeWidth: 1,
                  strokeDasharray: 0,
                  targetMarker: { name: 'classic', size: 8 },
                },
              },
              zIndex: 0,
              tools: [
                {
                  name: 'button',
                  args: {
                    markup: [
                      {
                        tagName: 'rect',
                        selector: 'button',
                        attrs: {
                          x: -44,
                          y: -14,
                          width: 88,
                          height: 22,
                          fill: '#ffffff',
                          stroke: '#9A979D',
                          'stroke-width': 1,
                          cursor: 'pointer',
                          zIndex: 2,
                        },
                      },
                      {
                        tagName: 'text',
                        textContent: item.relType,
                        selector: 'icon',
                        attrs: {
                          fill: '#333333',
                          fontSize: 10,
                          textAnchor: 'middle',
                          zIndex: 3,
                        },
                      },
                    ],
                    distance: -150,
                    onClick({ view }) {
                      state.nodeType = 'edg'
                      state.drawer = true
                      state.relationInfo = view.cell.getData()
                    },
                  },
                },
              ],
            })
          })

          const dagreLayout = new DagreLayout({
            type: 'dagre',
            rankdir: 'TB',
            align: 'UL',
            ranksep: 100,
            nodesep: 55,
            controlPoints: true,
          })
          const model = dagreLayout.layout(data)
          model.nodes.forEach((node) => {
            node.x -= node.size.width / 2
            node.y -= node.size.height / 2
          })
          state.graph.fromJSON(model)
          state.graph.centerContent()
        },
        // 改变状态
        changeStatus(item) {
          state.nodeType = ''
          state.drawer = false
          if (item) {
            methods.assetsLineage(state.dataModelId)
          }
        },
        // 点击node
        boundEvent() {
          state.graph.on('node:click', ({ cell }) => {
            state.cell = cell
            state.nodeType = 'node'
            state.drawer = true
            api.model.getModalDetail({ id: cell.id }).then((res) => {
              if (res.code === 'SUCCESS') {
                state.modelInfo = res.data
              } else {
                ElNotification({
                  title: '提示',
                  message: '暂无数据',
                  type: 'warning',
                })
              }
            })
          })
        },
      }
      onMounted(() => {
        methods.initFn()
      })
      onBeforeUnmount(() => {
        window.removeEventListener('resize', state.resizeFn)
        FlowGraph.destroy()
      })
      const params = toRefs(state)
      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .flow {
    width: 100%;
    height: 100%;

    :deep(.x6-graph-scroller) {
      &::-webkit-scrollbar {
        width: 6px; // 横向滚动条
        height: 6px; // 纵向滚动条 必写
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        background-color: rgb(177, 176, 176);
        border-radius: 2px;
      }
    }

    :deep {
      .drawer-modal-bg {
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }
</style>
