<template>
  <!-- 目录 -->
  <div class="catalog-container asideTree">
    <el-input
      v-model="searchTerm"
      placeholder="请输入关键词"
      :suffix-icon="Search"
      @input="inputHandle"
    />
    <div class="tree-list">
      <ul>
        <li>
          <span class="name"
            ><img src="@/assets/img/assets/assetPanorama/project_icon.png" alt="" />系统名称</span
          >

          <el-icon class="edit"><CirclePlus @click="proAddClick" /></el-icon>
        </li>

        <li v-for="item in filterProject" :key="item.id">
          <span class="name"
            ><img src="@/assets/img/assets/assetPanorama/project_icon.png" alt="" />{{
              item.name
            }}</span
          >
          <el-icon class="edit"><Edit @click="projectEditClick(item)" /></el-icon>
        </li>
      </ul>
    </div>
  </div>

  <div class="table-container">
    <div calss="add" style="padding: 8px">
      <el-button type="primary" @click="addRelation"> 新建关系 </el-button>
    </div>
    <div v-loading="loading" class="list">
      <el-table :data="data?.data" style="width: 100%">
        <!-- 序号 -->
        <el-table-column prop="序号" label="序号" type="index" width="80" />
        <el-table-column prop="startProjectName" label="起点系统" />
        <el-table-column prop="nextProjectName" label="终点系统" />
        <el-table-column prop="releation" label="关系类型" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" link type="primary" @click="editRelation(scope.row)"
              >编辑</el-button
            >
            <el-button size="small" link type="primary" @click="delProReleationHandle(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="table-container-pagination">
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        small="small"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>

  <!-- 项目编辑、新建弹窗 -->
  <div v-if="showProjectPop" class="newProjectPop">
    <div class="box">
      <div class="head">
        <span class="line"></span
        ><span class="title">{{ projectStatus === 'add' ? '新建' : '编辑' }}</span>
      </div>

      <div class="content">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="projectRuleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          size="small"
          status-icon
        >
          <el-form-item label="系统名称：" prop="name">
            <el-input v-model="projectRuleForm.name" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>
      <div class="footer">
        <el-button @click="showProjectPop = false">取消</el-button>
        <el-button type="primary" @click="confirmProjectClick(ruleFormRef)">确定</el-button>
      </div>
    </div>
  </div>

  <!-- 项目关系编辑、新建弹窗 -->
  <div v-if="showProRelationPop" class="newProjectPop">
    <div class="box">
      <div class="head">
        <span class="line"></span
        ><span class="title">{{ proRelationStatus === 'add' ? '新建' : '编辑' }}</span>
      </div>

      <div class="content">
        <el-form
          style="max-width: 600px"
          :model="projectRuleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          size="small"
          status-icon
        >
          <el-form-item label="起点系统：" prop="start">
            <el-select v-model="projectRuleForm.start" placeholder="请选择">
              <el-option
                v-for="item in data.projectData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="终点系统：" prop="end">
            <el-select v-model="projectRuleForm.end" placeholder="请选择">
              <el-option
                v-for="item in data.projectData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关系类型：" prop="type">
            <el-input v-model="projectRuleForm.type" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>
      <div class="footer">
        <el-button @click="showProRelationPop = false">取消</el-button>
        <el-button type="primary" @click="confirmProRelationClick">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { Search, CirclePlus, Edit } from '@element-plus/icons-vue'
  import {
    getProject,
    saveProject,
    updateProject,
    getProjectReleation,
    saveProjectReleation,
    updateProjectReleation,
    deleteProjectReleation,
  } from '@/api/dataMap.js'
  const data = ref({
    projectData: [],
    data: [],
  })
  const filterProject = ref([])
  const loading = ref<Boolean>(false)
  const showProjectPop = ref<Boolean>(false)
  const projectStatus = ref<String>('add')
  const projectInfo = ref<Object>({})
  const pageNum = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const showProRelationPop = ref<Boolean>(false)
  const proRelationStatus = ref<String>('add')
  const proRelationInfo = ref<Object>({})
  const searchTerm = ref('')
  interface RuleForm {
    name: string
  }

  const ruleFormRef = ref<FormInstance>()
  const projectRuleForm = reactive<RuleForm>({
    name: '',
    start: '',
    end: '',
    type: '',
  })
  const rules = reactive<FormRules<RuleForm>>({
    name: [{ required: true, message: '请输入系统名称', trigger: 'blur' }],
    start: [
      {
        required: true,
        message: '请选择',
        trigger: 'change',
      },
    ],
    end: [
      {
        required: true,
        message: '请选择',
        trigger: 'change',
      },
    ],
    type: [{ required: true, message: '请输入关系类型', trigger: 'blur' }],
  })

  onMounted(() => {
    getProjectHandle()
    getProReleationHandle()
  })

  const inputHandle = (val) => {
    if (val === '') {
      filterProject.value = data.value.projectData
      return
    }

    filterProject.value = data.value.projectData.filter((obj) => {
      return obj.name.includes(val)
    })
  }

  // 添加项目
  const addProjectHandle = (formEl) => {
    if (!formEl) return
    formEl.validate((valid) => {
      if (valid) {
        const params = {
          name: projectRuleForm.name,
        }
        saveProject(params)
          .then((res) => {
            showProjectPop.value = false

            if (res.code === 'SUCCESS') {
              ElMessage({
                message: `系统名称：${params.name}添加成功`,
                type: 'success',
              })
              getProjectHandle()
            } else {
              ElMessage.error(res.message)
            }
          })
          .catch(() => {
            toast.error('添加项目失败')
          })
      }
    })
  }

  // 获取项目列表
  const getProjectHandle = async () => {
    const res = await getProject({})
    data.value.projectData = res.data
    filterProject.value = res.data
  }

  // 修改项目
  const updateProjectHandle = (formEl) => {
    if (!formEl) return
    formEl.validate((valid) => {
      if (valid) {
        const params = {
          id: projectInfo.value.id,
          name: projectRuleForm.name,
        }
        updateProject(params)
          .then((res) => {
            showProjectPop.value = false

            if (res.code === 'SUCCESS') {
              ElMessage({
                message: `系统名称：${params.name}修改成功`,
                type: 'success',
              })
              getProjectHandle()
            } else {
              ElMessage.error(res.message)
            }
          })
          .catch(() => {
            toast.error('修改项目失败')
          })
      }
    })
  }

  // 项目新增点击
  const proAddClick = () => {
    showProjectPop.value = true
    projectStatus.value = 'add'
    projectRuleForm.name = ''
  }
  // 项目编辑点击
  const projectEditClick = (item) => {
    showProjectPop.value = true
    projectStatus.value = 'edit'
    projectInfo.value = item
    projectRuleForm.name = item.name
  }

  const confirmProjectClick = (formEl) => {
    projectStatus.value === 'add' ? addProjectHandle(formEl) : updateProjectHandle(formEl)
  }

  // 添加项目关系
  const addProReleationtHandle = async () => {
    if (!projectRuleForm.start) {
      ElMessage.error('请输入起点系统')
      return
    }
    if (!projectRuleForm.end) {
      ElMessage.error('请输入终点系统')
      return
    }
    if (!projectRuleForm.type) {
      ElMessage.error('请输入关系类型')
      return
    }

    const params = {
      nextProjectId: projectRuleForm.end,
      releation: projectRuleForm.type,
      startProjectId: projectRuleForm.start,
    }
    const res = await saveProjectReleation(params)
    showProRelationPop.value = false
    if (res.code === 'SUCCESS') {
      ElMessage({
        message: `系统关系添加成功`,
        type: 'success',
      })

      getProReleationHandle()
    } else {
      ElMessage.error(res.message)
    }
  }

  // 编辑项目关系
  const updateProReleationtHandle = async () => {
    if (!projectRuleForm.start) {
      ElMessage.error('请输入起点系统')
      return
    }
    if (!projectRuleForm.end) {
      ElMessage.error('请输入终点系统')
      return
    }
    if (!projectRuleForm.type) {
      ElMessage.error('请输入关系类型')
      return
    }

    const params = {
      id: proRelationInfo.value.id,
      nextProjectId: projectRuleForm.end,
      releation: projectRuleForm.type,
      startProjectId: projectRuleForm.start,
    }
    const res = await updateProjectReleation(params)
    showProRelationPop.value = false
    if (res.code === 'SUCCESS') {
      ElMessage({
        message: `系统关系编辑成功`,
        type: 'success',
      })

      getProReleationHandle()
    } else {
      ElMessage.error(res.message)
    }
  }

  // 获取项目关系列表
  const getProReleationHandle = async () => {
    const res = await getProjectReleation({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      projectId: null,
    })
    data.value.data = res.data.list
    total.value = res.data.total
  }

  // 删除项目关系列表
  const delProReleationHandle = async (item) => {
    const res = await deleteProjectReleation({
      id: item.id,
    })

    if (res.code === 'SUCCESS') {
      getProReleationHandle()
      ElMessage({
        message: `系统关系删除成功`,
        type: 'success',
      })
    } else {
      ElMessage.error(res.message)
    }
  }

  // 新增关系点击
  const addRelation = () => {
    showProRelationPop.value = true
    proRelationStatus.value = 'add'
    projectRuleForm.start = ''
    projectRuleForm.end = ''
    projectRuleForm.type = ''
  }
  // 编辑关系点击
  const editRelation = (item) => {
    showProRelationPop.value = true
    proRelationStatus.value = 'edit'
    proRelationInfo.value = item
    projectRuleForm.start = item.startProjectId
    projectRuleForm.end = item.nextProjectId
    projectRuleForm.type = item.releation
  }

  const confirmProRelationClick = () => {
    proRelationStatus.value === 'add' ? addProReleationtHandle() : updateProReleationtHandle()
  }

  const handleSizeChange = (val: number) => {
    console.log(`${val} items per page`)
    pageSize.value = val
    getProReleationHandle()
  }
  const handleCurrentChange = (val: number) => {
    console.log(`current page: ${val}`)
    pageNum.value = val
    getProReleationHandle()
  }
</script>
<style lang="scss" scope>
  .catalog-container {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
    align-self: stretch;
    width: 286px;
    margin-right: 10px;
    padding: 8px 16px;
    background: var(--100, #fff);
    border-radius: 2px;
    .tree-list {
      width: 100%;
      height: 100%;
      margin-top: 8px;
      overflow: auto;

      li {
        display: flex;
        justify-content: space-between;
        height: 30px;
        padding: 0 8px;
        color: #1d2129;
        font-weight: 400;
        font-size: 14px;
        font-family: 'PingFang SC';
        font-style: normal;
        line-height: 30px;
        cursor: pointer;

        &:hover {
          background: #ebf4ff;
        }
        .edit {
          top: 8px;
          font-size: 16px;
        }
        img {
          margin-right: 4px;
        }
      }
    }
  }
  .table-container {
    position: relative;
    display: flex;
    flex: 1;
    flex: 1 0 0;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    width: calc(100% - var(--aside-width));
    height: 100%;
    background: #fff;
    border-radius: 2px;
    .add {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
      align-self: stretch;
      padding: 8px;
    }
    .list {
      flex: 1;
      width: 100%;
      overflow-y: auto;
    }
    .table-container-pagination {
      display: flex;
      align-items: flex-start;
      align-self: stretch;
      justify-content: flex-end;
      padding: 14px 16px;
      border-top: 1px solid #dcdfe6;
      border-radius: 0px 0px 2px 2px;

      .el-pagination__sizes .el-select {
        width: 100px;
      }
      .el-select__wrapper {
        min-height: 32px;
        line-height: 24px;
      }
    }
  }

  .newProjectPop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);

    .box {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 560px;
      background: #fff;
      transform: translate(-50%, -50%);

      .head {
        height: 48px;
        line-height: 48px;
        border-bottom: 1px solid #dcdfe6;

        .title {
          color: var(----, rgba(0, 0, 0, 0.9));

          font-weight: 500;
          font-size: 16px;

          font-family: 'Source Han Sans CN';
          font-style: normal;
          line-height: 24px; /* 150% */
          vertical-align: middle;
        }
        .line {
          display: inline-block;
          width: 4px;
          height: 18px;
          margin-right: 12px;
          vertical-align: middle;
          background: #1e89ff;
        }
      }

      .content {
        padding: 24px 16px;
      }

      .footer {
        padding: 16px;
        text-align: right;
      }
    }
  }
</style>
