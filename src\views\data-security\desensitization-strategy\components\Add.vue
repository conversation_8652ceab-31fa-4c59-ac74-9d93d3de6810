<template>
  <n-drawer
    v-model="state.showDraw"
    :size="550"
    :close-on-click-overlay="false"
    :esc-key-closeable="false"
    @close="test"
  >
    <section class="container-box" v-loading="state.loading">
      <div class="page_header_common_style">
        <SvgIcon icon="icon_edit_list_common" class="page_header_common_style_icon" />
        <span>{{ props.id ? '编辑' : '新建' }}脱敏策略</span>
        <SvgIcon
          icon="icon-page-common-close"
          class="page_header_common_style_close"
          style="color: #b1bcd6"
          @click="goBack(false)"
        />
      </div>
      <div class="form">
        <n-form
          ref="form"
          :data="state.form"
          :rules="state.rules"
          label-width="82px"
          label-align="start"
          :pop-position="['right']"
        >
          <div :class="['between', 'expand-box']">
            <n-row>
              <n-col :span="24">
                <n-form-item label="策略名称：" field="name">
                  <n-input
                    v-model="state.form.name"
                    placeholder="请输入策略名称"
                    size="small"
                    maxlength="30"
                    clearable
                  />
                </n-form-item>
              </n-col>
            </n-row>

            <n-row>
              <n-col :span="24">
                <n-form-item label="脱敏算法：" field="algoType">
                  <div class="field-list">
                    <n-form-item field="algoType" class="field-list-type">
                      <n-select v-model="state.form.algoType" clearable @value-change="getAlogList">
                        <n-option value="HASH" name="哈希" />
                        <n-option value="TRUNCATION" name="截断" />
                        <n-option value="MASK" name="掩码" />
                      </n-select>
                    </n-form-item>
                    <n-form-item field="algoId" class="field-list-alog">
                      <n-select v-model="state.form.algoId" clearable>
                        <n-option
                          v-for="item in state.alogList"
                          :key="item.id"
                          :value="item.id"
                          :name="item.name"
                        />
                      </n-select>
                    </n-form-item>
                  </div>
                </n-form-item>
              </n-col>
            </n-row>

            <n-row>
              <n-col :span="24">
                <n-form-item label="脱敏字段：" field="fieldList">
                  <div class="field-list">
                    <div class="field-list-box">
                      <div
                        class="field-list-box-each"
                        v-for="(field, index) in state.selectedData.nowFieldsList"
                        :key="field.id"
                      >
                        <span :title="field.name">{{ field.name }}</span>
                        <i
                          class="icon icon-close close"
                          style="font-size: 12px; color: inherit"
                          @click="
                            () => {
                              state.selectedData.nowFieldsList.splice(index, 1)
                            }
                          "
                        ></i>
                        <!-- <SvgIcon
                          icon="icon-close-border"
                          class="close"
                          @click="
                            () => {
                              state.selectedData.nowFieldsList.splice(index, 1)
                            }
                          "
                        /> -->
                      </div>
                      <!-- <div
                        class="field-list-box-each"
                        style="width: 27px"
                        v-if="state.selectedData.nowFieldsList.length > 3"
                      >
                        <span>+{{ state.selectedData.nowFieldsList.length - 3 }}</span>
                      </div> -->
                    </div>
                    <n-button
                      @click="selectField"
                      color="primary"
                      variant="solid"
                      class="select-field"
                      :disabled="!state.form.algoId"
                    >
                      <SvgIcon
                        icon="new-add"
                        style="width: 16px; height: 16px; position: relative; top: -1px"
                      />选择字段
                    </n-button>
                  </div>
                </n-form-item>
              </n-col>
            </n-row>

            <n-row>
              <n-col :span="24">
                <n-form-item label="应用范围：" class="desc">
                  <div class="use-range">
                    <n-form-item label="用户：" field="userId">
                      <n-select
                        v-model="state.form.userId"
                        placeholder="请选择用户"
                        size="small"
                        class="use-range-select"
                        allow-clear
                        multiple
                        filter
                        :collapse-tags="true"
                        :collapse-tags-tooltip="true"
                      >
                        <n-option
                          v-for="item in state.authorList"
                          :key="item.id"
                          :name="item.name"
                          :value="item.id"
                          style="max-width: 320px"
                        />
                      </n-select>
                    </n-form-item>
                    <n-form-item label="角色：" field="roleId">
                      <n-select
                        v-model="state.form.roleId"
                        placeholder="请选择角色"
                        size="small"
                        class="use-range-select"
                        allow-clear
                        multiple
                        filter
                        :collapse-tags="true"
                        :collapse-tags-tooltip="true"
                      >
                        <n-option
                          v-for="item in state.roleAllArr"
                          :key="item.key"
                          :name="item.value"
                          :value="item.key"
                        />
                      </n-select>
                    </n-form-item>
                    <n-form-item label="部门：" field="userDeptId">
                      <TreeSelect
                        v-model="state.form.userDeptId"
                        filter
                        class="use-range-select"
                        allowClear
                        useGrayArrow
                        multiple
                        check-strictly
                        placeholder="请选择组织"
                        :data="state.treeData"
                        :enableLabelization="true"
                      />
                    </n-form-item>
                    <n-form-item field="name">
                      <n-radio-group
                        class="mb-2"
                        style="margin-top: 10px"
                        direction="row"
                        v-model="state.form.scopeType"
                      >
                        <n-radio value="APPLICABLE"> 适用于此范围 </n-radio>
                        <n-radio value="NOT_APPLICABLE"> 不适用于此范围 </n-radio>
                      </n-radio-group>
                    </n-form-item>
                  </div>
                </n-form-item>
              </n-col>
            </n-row>

            <n-row>
              <n-col :span="24">
                <n-form-item label="应用场景：" field="applicationScenarioList">
                  <n-checkbox-group
                    v-model="state.form.applicationScenarioList"
                    :isShowTitle="false"
                    direction="row"
                    :max="3"
                    style="margin-top: -2px"
                  >
                    <n-checkbox label="浏览" value="VIEW" />
                    <n-checkbox label="服务" value="SERVICE" />
                  </n-checkbox-group>
                </n-form-item>
              </n-col>
            </n-row>

            <n-row>
              <n-col :span="24">
                <n-form-item label="禁用：" field="status" class="desc">
                  <n-checkbox
                    v-model="state.form.status"
                    style="margin-top: 9px"
                    activeValue="DISABLE"
                    unactiveValue="ENABLE"
                  />
                </n-form-item>
              </n-col>
            </n-row>
          </div>
        </n-form>
      </div>
    </section>
    <div class="container-footer">
      <n-button size="sm" @click.prevent="goBack(false)" style="border-color: #a3b4db">
        取消
      </n-button>
      <n-button
        :loading="loading"
        color="primary"
        size="sm"
        variant="solid"
        @click.prevent="onConfirm"
      >
        确定
      </n-button>
    </div>
  </n-drawer>
  <AddField
    :selectedData="state.selectedData"
    :isShow="state.showSelect"
    @close="changeFields"
    :algoId="state.form.algoId"
  />
</template>

<script setup>
  import { reactive, watch, nextTick } from 'vue'
  import api from '@/api/index'
  import AddField from './AddField.vue'
  import { sceneManage } from '@/api'
  import { ElMessage } from 'element-plus'
  import TreeSelect from '@/components/cfTreeSelect'
  const test = () => {
    state.showDraw = true
  }

  const form = ref()

  const state = reactive({
    alogList: [],
    showDraw: false,
    showSelect: false,
    form: {
      userId: [],
      roleId: [],
      userDeptId: [],
      applicationScenarioList: [],
      fieldList: [],
      frontInfo: '',
      status: 'ENABLE',
    },
    selectedData: {
      treeId: '',
      selectedModel: [],
      nowFieldsList: [],
    },
    rules: {
      name: [{ required: true, message: '请输入策略名称' }],
      algoType: [{ required: true, message: '请选择脱敏算法' }],
      algoId: [{ required: true, message: '请选择脱敏算法' }],
      fieldList: [{ required: true, message: '请选择脱敏字段' }],
      applicationScenarioList: [{ required: true, message: '请选择应用场景' }],
    },
    authorList: [],
    roleAllArr: [],
    treeData: [],
    key: 0,
  })

  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: () => '',
    },
  })

  const emits = defineEmits(['close'])

  const changeFields = (data) => {
    state.showSelect = false
    if (data) {
      state.selectedData = { ...data }
    }
  }

  const onConfirm = () => {
    const fieldList = []

    state.selectedData.nowFieldsList?.forEach((field) => {
      const table = state.selectedData.selectedModel?.find((model) => model.id === field.pid)
      fieldList.push({
        fieldId: field?.id,
        fieldName: field?.name,
        tableName: table?.name,
        tableId: field.pid,
      })
    })

    state.form.fieldList = fieldList
    form.value.validate((valid) => {
      if (valid) {
        const rangeList = []
        state.form.userId.forEach((id) => {
          rangeList.push({ targetId: id, unitType: 'USER' })
        })
        state.form.roleId.forEach((id) => {
          rangeList.push({ targetId: id, unitType: 'ROLE' })
        })
        state.form.userDeptId.forEach((id) => {
          rangeList.push({ targetId: id, unitType: 'DEPARTMENT' })
        })

        const data = {
          ...state.form,
          frontInfo: JSON.stringify(state.selectedData),
          rangeList,
        }

        const methods = props.id ? sceneManage.strategyUpdate : sceneManage.strategyAdd
        methods(data).then((res) => {
          if (res.success) {
            ElMessage.success(props.id ? '编辑成功！' : '新增成功')
            state.showDraw = false
            clearForm()
            emits('close', true)
          }
        })
      }
    })
  }

  const initData = () => {
    // 获取详情
    sceneManage.strategyDetail({ id: props.id }).then((res) => {
      if (res.success) {
        state.form = { ...res.data, userId: [], roleId: [], userDeptId: [] }
        res.data.rangeList.forEach((item) => {
          if (item.unitType === 'USER') {
            state.form.userId.push(item.targetId)
          } else if (item.unitType === 'ROLE') {
            state.form.roleId.push(String(item.targetId))
          } else {
            state.form.userDeptId.push(item.targetId)
          }
        })

        state.selectedData = JSON.parse(res.data.frontInfo)
        getAlogList()
      }
    })
  }

  const getAlogList = () => {
    let data = {
      condition: {
        type: state.form.algoType || null,
      },
      pageNum: 1,
      pageSize: 1000,
    }
    api.assets.getDesensitizationAlgoList(data).then((res) => {
      if (res.success) {
        state.alogList = res.data.list.filter((item) => item.status === 'AVAILABLE')
      }
    })
  }

  const roleAll = () => {
    let params = {}
    api.system.roleAll(params).then((res) => {
      state.roleAllArr = res.data
      state.key++
    })
  }

  const getUserList = () => {
    let params = {
      pageNum: 1,
      pageSize: 1000,
    }
    api.system.userList(params).then((res) => {
      state.authorList = res.data.list
      state.key++
    })
  }

  const getDepartmentList = () => {
    api.base.getDepartmentTrees({}).then((res) => {
      if (res.data) {
        let _data = [res.data]
        _data.forEach((item) => {
          item.level = 0
          if (item.children?.length) {
            item.children.map((item2) => (item2.level = 1))
          }
        })
        res.data.type = 'ROOT'
        if (!res.data.children) {
          res.data.children = []
        }
        state.treeData = setTreeRecursion([res.data])
      } else {
        state.treeData = []
      }
      state.key++
    })
  }

  const setTreeRecursion = (data) => {
    data.map((item) => {
      item.id = Number(item.id)
      item.value = item.id
      if (item.children && item.children?.length != 0) {
        setTreeRecursion(item.children)
      }
    })
    return data
  }

  const selectField = () => {
    state.showSelect = true
  }

  const clearForm = () => {
    state.form = {
      userId: [],
      roleId: [],
      userDeptId: [],
      applicationScenarioList: [],
      fieldList: [],
      frontInfo: '',
    }
    state.selectedData = {
      treeId: '',
      selectedModel: [],
      nowFieldsList: [],
    }
  }

  const goBack = (refresh) => {
    state.showDraw = false
    clearForm()
    emits('close', refresh)
  }

  const getAllList = async () => {
    getUserList()
    getDepartmentList()
    roleAll()
    nextTick(() => {
      state.showDraw = true
    })
    if (props.id) {
      initData()
    }
  }

  watch(
    () => props.isShow,
    () => {
      if (props.isShow) {
        getAllList()
      }
    },
  )
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container {
    // padding-bottom: 70px;
    &-box {
      height: calc(100% - 67px);
      overflow: initial;
      border-radius: 4px;
      :deep(.nancalui-tree-select-input--hasvalue) {
        padding: 0 4px;
        input {
          display: none;
        }
      }
      :deep(.nancalui-tree-select-value) {
        position: relative !important;
        float: left;
        box-sizing: border-box;
        width: 68px !important;
        height: 24px;
        margin-top: 2px;
        margin-right: 4px;
        padding: 1px 10px 0 4px !important;
        overflow: hidden;
        color: var(----, rgba(0, 0, 0, 0.75));
        font-weight: 400;
        font-size: 14px;
        font-style: normal;
        line-height: 24px;
        white-space: nowrap;
        text-overflow: ellipsis;
        background: var(----, #e3ecff);
        border: 1px solid var(---, #a3b4db);
        border-radius: 6px;
        .icon-box {
          position: absolute;
          top: 5px;
          right: 2px;
        }
      }
    }

    &-footer {
      height: 65px;
      padding: 16px;
      text-align: right;
      background: #ffffff;
      border-radius: 8px 8px 0px 0px;
    }
  }

  .form {
    height: 100%;
    padding: 16px;
    color: #333;
    background: #fff;
    .field-list {
      width: 100%;
      overflow: hidden;
      &-box {
        float: left;
        width: calc(100% - 116px);
        padding: 0 0 0 10px;
        background: #fff;
        border: 1px solid #a3b4db;
        border-radius: 6px;
        &-each {
          float: left;
          width: 68px;
          height: 24px;
          margin-top: 3px;
          margin-right: 4px;
          padding: 1px 4px;
          background: var(----, #e3ecff);
          border: 1px solid var(---, #a3b4db);
          border-radius: 6px;
          span {
            display: inline-block;
            width: 36px;
            overflow: hidden;
            text-wrap: nowrap;
            text-overflow: ellipsis;
          }
          .close {
            float: right;
            width: 16px;
            height: 16px;
            margin-top: 5px;
            color: #8091b7;
            cursor: pointer;
          }
        }
      }
      .select-field {
        float: right;
        width: 108px;
        height: 32px;
        margin-left: 8px;
      }

      &-type {
        float: left;
        width: 94px;
        margin-bottom: 0;
      }
      &-alog {
        float: right;
        width: calc(100% - 102px);
        margin-bottom: 0;
      }
    }
    .expand-box {
      &.hide {
        height: 0;
        overflow: hidden;
      }
    }

    .nancalui-form {
      :deep(.el-form-item__label) {
        padding: 0;
      }
    }

    h2 {
      margin: 0 0 20px;
      padding-left: 5px;
      font-size: 14px;
      border-left: 4px solid $themeBlue;
    }

    .nancalui-form {
      height: 100%;
      height: 100%;
      margin: 0 auto;
      .tree-box-row {
        height: calc(100% - 120px);
      }
      :deep(.tree-box) {
        width: 100%;
        height: 100%;
        .nancalui-form__control,
        .nancalui-form__control-container {
          width: 100%;
          height: 100%;
        }
        > .nancalui-tree-list {
          align-items: flex-start;
        }
        .nancalui-tree {
          display: flex;
          justify-content: space-between;
          width: 100%;
          max-height: calc(100vh - 400px);
          padding: 10px 10px 10px 0;
          overflow-y: auto;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          .nancalui-checkbox.disabled .nancalui-checkbox__material {
            background-color: #f2f6fc !important;
            border-color: #dcdfe6;
          }

          .nancalui-tree__node {
            height: 26px;
          }
          .nancalui-tree__node-vline,
          .nancalui-tree__node-hline {
            display: none;
          }
          .nancalui-tree__node-title {
            font-size: 12px;
          }

          .el-tree-node__content {
            .el-tree-node__label {
              font-size: 12px;
            }
          }
        }
      }
    }

    :deep(.nancalui-tree) {
      .nancalui-tree-group {
        width: 100%;
      }
      .nancalui-tree__node {
        width: 100%;
        border-radius: 6px;
        .nancalui-tree__node-content:not(.active):hover {
          background-color: rgba(0, 0, 0, 0);
        }

        &:has(.active) {
          background-color: #ffffff;
        }

        &:hover {
          background-color: #ecf7ff;

          .nancalui-tree__node-content--value-wrapper {
            width: 100%;
            .tree-icon {
              color: $themeBlue;
            }

            .tree-label {
              max-width: calc(100% - 73px);
              color: $themeBlue;
            }

            .tree-btn {
              visibility: visible;
            }
          }
        }

        .nancalui-tree__node-vline {
          width: 0;
        }

        .nancalui-tree__node-content {
          border-radius: 6px;

          > span {
            width: 28px;
            padding-left: 10px;
          }

          .nancalui-tree__node-content--value-wrapper {
            position: relative;
            width: 100%;
          }

          &.active {
            background-color: #ecf7ff;

            .nancalui-tree__node-content--value-wrapper {
              .tree-label,
              .tree-icon {
                color: $themeBlue;
              }
            }

            & > span {
              svg {
                color: $themeBlue;
              }
            }
          }

          .tree-icon {
            min-width: 16px;
            margin: 0 4px;
            margin-right: 4px;
            color: #000000;
            font-size: 16px;
          }

          .tree-label {
            max-width: calc(100% - 20px);
            overflow: hidden;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .tree-btn {
            display: inline-block;
            width: 80px;
            visibility: hidden;

            .icon {
              margin-left: 4px;
              color: $themeBlue;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  :deep(.el-input, .el-select) {
    width: 100% !important;
  }
  :deep(.nancalui-tree) {
    border-color: #c5d0ea !important;
  }

  .use-range {
    width: 100%;
    padding: 16px;
    background: #f6f7fb;
    border-radius: 6px;
    &-select {
      width: 100%;
    }
  }
  .desc {
    align-items: flex-start;
    &::before {
      display: inline-block;
      margin-right: 2px;
      color: #fff;
      vertical-align: middle;
      content: '*';
    }
    :deep(.nancalui-form__label--md) {
      flex: 0 0 74px !important;
    }
  }
</style>
