<template>
  <!-- 日志管理-安全日志管理 -->
  <div class="api-management-add publicFormCss container-padding16">
    <div class="api-management-add-box">
      <div class="page_header_common_style dif">
        <p class="need_smallcube__title">三员管理设置</p>
      </div>
      <div
        :class="{
          'scroll-bar-style': true,
          'api-management-add-box-content': true,
        }"
        v-loading="state.loading"
      >
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="136px"
          label-aligin="end"
          class="demo-ruleForm"
        >
          <n-form-item label="系统管理员：" desc="系统管理员可管理项目及用户信息">
            <el-select
              v-model="state.ruleForm.person1"
              placeholder="请选择系统管理员"
              @change="selectChange"
              filterable
              :filter-method="filterDataSystemFn"
              @blur="getLeftTreeDataFn(false)"
            >
              <el-option
                v-for="val in state.selectDataSystem"
                :key="val.id"
                :value="val.id"
                :label="val.name + '-' + val.username"
                :disabled="val.disabled"
              />
            </el-select>
          </n-form-item>
          <n-form-item label="安全保密员：" desc="安全保密员可管理角色、查看用户日志">
            <el-select
              v-model="state.ruleForm.person2"
              placeholder="请选择安全保密员"
              @change="selectChange"
              filterable
              :filter-method="filterDataSecrecyFn"
              @blur="getLeftTreeDataFn(false)"
            >
              <el-option
                v-for="val in state.selectDataSecrecy"
                :key="val.id"
                :value="val.id"
                :label="val.name + '-' + val.username"
                :disabled="val.disabled"
              />
            </el-select>
          </n-form-item>
          <n-form-item label="安全审计员：" desc="安全审计员可查看管理员日志">
            <el-select
              v-model="state.ruleForm.person3"
              placeholder="请选择安全审计员"
              @change="selectChange"
              filterable
              :filter-method="filterDataAuditFn"
              @blur="getLeftTreeDataFn(false)"
            >
              <el-option
                v-for="val in state.selectDataAudit"
                :key="val.id"
                :value="val.id"
                :label="val.name + '-' + val.username"
                :disabled="val.disabled"
              />
            </el-select>
          </n-form-item>
        </n-form>
      </div>
    </div>
    <div class="container-padding16-footer">
      <div class="my-appliction">
        <n-button size="sm" @click.prevent="cancel">取消</n-button>
        <n-button
          color="primary"
          size="sm"
          variant="solid"
          :loading="state.saveLoading"
          @click.prevent="submitForm"
          >确定</n-button
        >
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import { checkCName, checkCRelativePath } from '@/utils/validate'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const router = useRouter()
      const ruleForm = ref()
      const state = reactive({
        loading: false,
        saveLoading: false,
        ruleForm: {
          person1: '',
          person2: '',
          person3: '',
        },
        treeData: [],
        selectDataSystem: [],
        selectDataSecrecy: [],
        selectDataAudit: [],
        timer: null,
      })
      const methods = {
        //下拉框改变
        selectChange() {
          let checkPersonIds = [
            state.ruleForm.person1,
            state.ruleForm.person2,
            state.ruleForm.person3,
          ]
          methods.setTreeRecursionWithId(state.selectDataSystem, checkPersonIds)
          methods.setTreeRecursionWithId(state.selectDataSecrecy, checkPersonIds)
          methods.setTreeRecursionWithId(state.selectDataAudit, checkPersonIds)
        },
        //使用过的项设置disabled
        setTreeRecursionWithId(data, ids = []) {
          data.map((item, index) => {
            if (ids.includes(item.id)) {
              item.disabled = true
            } else {
              item.disabled = false
            }
            if (item.children && item.children.length != 0) {
              methods.setTreeRecursionWithId(item.children, ids)
            }
          })
          return data
        },
        filterDataSystemFn(words) {
          if (words) {
            //防抖
            clearTimeout(state.timer)
            state.timer = setTimeout(() => {
              methods.getLeftTreeDataFn(words, 'selectDataSystem')
            }, 300)
          }
        },
        filterDataSecrecyFn(words) {
          if (words) {
            clearTimeout(state.timer)
            state.timer = setTimeout(() => {
              methods.getLeftTreeDataFn(words, 'selectDataSecrecy')
            }, 300)
          }
        },
        filterDataAuditFn(words) {
          if (words) {
            clearTimeout(state.timer)
            state.timer = setTimeout(() => {
              methods.getLeftTreeDataFn(words, 'selectDataAudit')
            }, 300)
          }
        },
        // 获取左侧树数据
        getLeftTreeDataFn(search = null, type = null) {
          // state.loading = true
          let params = {
            pageNum: 1,
            pageSize: 100,
            condition: {
              departmentFullId: '1',
              departmentFullName: '全部',
            },
          }
          if (search) {
            params.condition = {
              name: search,
            }
          }
          api.system
            .userList(params)
            .then((res) => {
              let { success, data } = res
              if (success) {
                if (data !== null) {
                  if (type) {
                    state[type] = [...data.list]
                  } else {
                    state.treeData = data.list

                    state.selectDataSystem = [...data.list]
                    state.selectDataSecrecy = [...data.list]
                    state.selectDataAudit = [...data.list]
                  }
                }
                if (!search && search !== false) {
                  methods.getThreeManagerInfo()
                } else {
                  state.loading = false
                }
              }
            })
            .catch(() => {
              state.loading = false
            })
        },

        //三员设置回显
        getThreeManagerInfo() {
          api.base
            .getThreeManagerInfo()
            .then((res) => {
              state.loading = false
              let { data, success } = res
              if (success) {
                data?.forEach((item) => {
                  if (item.roleCode === 1) {
                    //能否找到设置人，找不到前端push一条
                    let result = state.selectDataSystem.find((obj) => obj.id === item.userId)
                    if (!result) {
                      state.selectDataSystem.unshift({
                        ...item,
                        id: item.userId,
                      })
                    }
                    state.ruleForm.person1 = item.userId
                  }
                  if (item.roleCode === 2) {
                    let result = state.selectDataSecrecy.find((obj) => obj.id === item.userId)
                    if (!result) {
                      state.selectDataSecrecy.unshift({
                        ...item,
                        id: item.userId,
                      })
                    }
                    state.ruleForm.person2 = item.userId
                  }
                  if (item.roleCode === 3) {
                    let result = state.selectDataAudit.find((obj) => obj.id === item.userId)
                    if (!result) {
                      state.selectDataAudit.unshift({
                        ...item,
                        id: item.userId,
                      })
                    }
                    state.ruleForm.person3 = item.userId
                  }
                })

                setTimeout(() => {
                  methods.selectChange()
                }, 10)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        //保存操作
        submitForm() {
          let _threeManagerSetDTOList = []

          _threeManagerSetDTOList.push({
            roleCode: 1,
            userId: state.ruleForm.person1 || null,
          })

          _threeManagerSetDTOList.push({
            roleCode: 2,
            userId: state.ruleForm.person2 || null,
          })

          _threeManagerSetDTOList.push({
            roleCode: 3,
            userId: state.ruleForm.person3 || null,
          })

          state.saveLoading = true
          api.base
            .setThreeManager(_threeManagerSetDTOList)
            .then((res) => {
              state.saveLoading = false
              if (res.success) {
                ElNotification({
                  title: '提示',
                  message: '设置成功',
                  type: 'success',
                })
                methods.getThreeManagerInfo()
              }
            })
            .catch(() => {
              state.saveLoading = false
            })
        },
        //取消
        cancel() {
          methods.getThreeManagerInfo()
        },
      }
      onMounted(() => {
        methods.getLeftTreeDataFn()
      })
      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 100px;
  .api-management-add {
    position: relative;
    color: #333;
    .api-management-add-box {
      position: relative;
      width: 100%;
      height: calc(100% - 74px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
      .page_header_common_style.dif {
        height: 46px;
        padding: 7px 8px 7px 0;
        margin: 0;
      }
      .box-content-description {
        width: calc(100% - 4px);
        height: 38px;
        margin: 2px auto;
        padding: 13px 0;
        background: #f7f8fa;
        p {
          margin: 0 0 0 30px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          font-size: 14px;
        }
      }
      .content-bg-img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 17.5%;
        min-width: 252px;
      }
      .api-management-add-box-content {
        width: 100%;
        min-width: 720px;
        height: calc(100vh - 240px);
        max-height: calc(100% - 36px);
        // margin: 0 auto;
        padding: 16px;
        // padding-bottom: 40px;
        overflow-y: auto;

        .nancalui-form {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          .el-select {
            width: 100%;
          }

          :deep(.nancalui-form__item--horizontal) {
            position: relative;
            display: flex;
            width: 100%;
            margin-bottom: 16px;
            .nancalui-form__control,
            .nancalui-tree-select {
              width: 660px;
              flex: none;
            }
            &[desc]::after {
              color: var(---, #a8abb2);
              font-family: 'Source Han Sans CN';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              margin-left: 16px;
              content: attr(desc);
            }

            .nancalui-tree-select input {
              display: none;
            }
            &.filter-form-item {
              .top-title-box {
                display: flex;
                justify-content: space-between;
                width: 100%;
                padding-right: 65px;
                .add-filter-box {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  width: 38px;
                  height: 26px;
                  margin-left: 65px;
                  color: #fff;
                  font-size: 20px;
                  line-height: 26px;
                  text-align: center;
                  background-color: $themeBlue;
                  border-radius: 13px;
                  cursor: pointer;
                  .icon-add-svg {
                    margin-right: 0;
                  }
                }
              }
              .filter-condition-box {
                max-height: 500px;
                overflow-y: auto;
              }
            }
          }
        }
      }
    }
    .container-padding16-footer {
      height: 64px;
      padding: 16px;
      text-align: right;
      background: #ffffff;
      margin-top: 10px;
      border-radius: 0px 0px 2px 2px;
    }
  }
</style>
