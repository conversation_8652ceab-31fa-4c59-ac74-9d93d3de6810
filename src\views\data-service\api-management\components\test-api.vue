<template>
  <!-- 测试api -->
  <div class="components-test-api">
    <div class="test-api-box-top">
      <div class="api-type">{{ state.allData.requestMethod }}</div>
      <div class="api-url">{{ state.allData.showUrl }}</div>
    </div>
    <div class="test-api-box-mid">
      <div class="test-api-box-mid-top">
        <div class="title">请求参数</div>
        <n-button color="primary" variant="solid" :loading="state.loading" @click.prevent="testApi"
          >立即测试</n-button
        >
      </div>
      <div>
        <n-public-table
          ref="hasParamsTable"
          :isDisplayAction="false"
          :table-head-titles="state.tableHeadTitles"
          :showPagination="false"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
        >
          <!-- 参数 value -->
          <template #parameterValue="{ editor }">
            <n-input
              :key="state.tableKey[editor.rowIndex]"
              v-model="editor.row.value"
              :class="!editor.row.value && editor.row.isRequired ? 'required-input' : ''"
              :type="editor.row.type ? editor.row.type : 'text'"
              placeholder=""
              @keydown="onKeydown($event, editor.row)"
              @keyup="onKeyup($event, editor.row)"
              @focus="inputFocus($event, editor.row)"
              @blur="inputBlur(editor)"
            />
          </template>
          <!-- 是否必填 -->
          <template #isRequired="{ editor }">
            <div v-if="editor.row.isRequired" class="isRequired">
              <span class="required">*</span>{{ editor.row.isRequired }}
            </div>
            <div v-else>{{ editor.row.isRequired }}</div>
          </template>
        </n-public-table>
      </div>
    </div>
    <div class="test-api-box-bottom-dif">
      <div class="title">请求返回</div>
      <div class="back-content">
        <div class="content-left">
          <div class="code">Code</div>
          <div class="code-number">{{ state.statusCode }}</div>
        </div>
        <div class="content-right">
          <div class="back-title">响应内容</div>
          <div class="back-data-box">
            <div class="back-data">
              <div v-show="state.showJsonViewer">
                <json-viewer :value="state.treeData" :expand-depth="5" copyable boxed sort />
                <!-- <JsonViewer :value="state.treeData" :expand-depth="5" copyable boxed sort /> -->
              </div>

              <div v-show="!state.showJsonViewer">
                {{ state.errorMessage }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { Integer, onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'

  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const router = useRouter()
      const state = reactive({
        tableKey: [],
        tableHeight: 400,
        tableData: {},
        loading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'name', name: '参数名称' },
          { prop: 'value', name: '参数值', slot: 'parameterValue', width: 300 },
          { prop: 'fieldType', name: '参数类型' },
          { prop: 'isRequired', name: '是否必填', slot: 'isRequired' },
          { prop: 'description', name: '参数说明' },
        ],
        allData: {
          requestMethod: '',
          apiUrl: '',
          showUrl: '',
          paramColumns: [],
        },
        apiType: '',
        apiUrl: '',
        treeData: {},
        showJsonViewer: false,
        statusCode: '',
        errorMessage: '',
        queryData: '',
        apiId: '',
      })
      const hasParamsTable = ref()
      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 650
        },
        init(data) {
          if (data) {
            state.queryData = router.currentRoute.value.query // 获取路由传参
            state.apiId = Number(state.queryData.id)
            state.apiDetail = data
            Object.keys(state.allData).forEach((key) => {
              state.allData[key] = data[key]
            })
            state.allData.paramColumns.map((item) => {
              return Object.assign(item, {
                value: '',
                isRequired: false,
                type: item.fieldType === 'LONG' ? 'number' : 'text',
              })
            })

            state.allData.paramColumns = [
              {
                name: 'pageNum',
                value: 1,
                fieldType: 'Number',
                isRequired: true,
                description: '当前页',
                type: 'number',
              },
              {
                name: 'pageSize',
                value: 10,
                fieldType: 'Number',
                isRequired: true,
                description: '每页多少条,示例值(10)',
                type: 'number',
              },
              ...state.allData.paramColumns,
            ]
            state.allData.paramColumns?.forEach((item, index) => {
              state.tableKey[index] = 1
            })
            state.tableData = { list: state.allData.paramColumns }
          } else {
            state.tableData = {}
          }
        },
        // 还是通过this.$set()，但是不能直接去修改对象里的某一个值，因为el-table监听的是一整行数据，并不是某一个单元格。所以需要重新赋值一整行数据
        changeMolecule() {
          // state.key++
          // this.$set(state.allData.paramColumns, index, row)
        },
        testApi() {
          // 验证参数
          let passed = true
          let data = {}
          state.allData.paramColumns.forEach((item) => {
            data[item.name] = item.value ? item.value : null
            if (item.isRequired && !item.value) {
              passed = false
            }
          })
          if (passed) {
            state.loading = true
            let _data = []

            state.allData.paramColumns.forEach((item) => {
              if (item.id) {
                _data.push({
                  metadataId: item.id,
                  name: item.name,
                  textValue: item.value,
                })
              } else {
                _data.push({
                  name: item.name,
                  textValue: item.value,
                })
              }
            })
            let _object = { apiParamForms: _data, id: state.apiId, token: state.apiDetail.token }
            api.dataService
              .apiTest(_object)
              .then((res) => {
                let { success } = res
                state.loading = false
                if (success) {
                  state.statusCode = 200
                  state.showJsonViewer = true
                  state.treeData = res
                  ElNotification({
                    title: '提示',
                    message: '测试成功',
                    type: 'success',
                  })
                }
              })
              .catch((err) => {
                state.loading = false
                state.statusCode = '失败'
                state.showJsonViewer = false
                state.treeData = {}
                state.errorMessage = err.message
              })
          }
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },
        inputBlur(editor) {
          let item = editor.row
          if (item.type === 'number') {
            item.value = item.value.replace(/\D/g, '')
          }
          state.tableKey[editor.rowIndex]++
        },
        // input的type=number时可以输入字母e+-的解决方式 e在数学上代表的是⽆理数，是⼀个⽆限不循环的⼩数
        onKeydown(e, row) {
          if (row.type === 'number') {
            onKeydownPositiveInteger(e, row.value)
          }
        },
        onKeyup(e, row) {
          if (row.type === 'number') {
            onKeyupPositiveInteger(e)
          }
        },
      }
      methods.setTableHeight()

      return {
        state,
        hasParamsTable,
        Integer,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $labelWidth: 140px;
  .components-test-api {
    width: 100%;
    height: 100%;
    border-radius: 0 0 4px 4px;
    background-color: #fff;
    color: #333;
    position: relative;
    font-size: 14px;
    padding: 20px;
    font-family: PingFangSC-Regular, PingFang SC;
    min-width: 1220px;

    .test-api-box-top {
      width: 100%;
      display: flex;
      height: 42px;
      line-height: 42px;
      font-weight: 400;
      color: #000000;

      .api-type {
        width: 10%;
        text-align: center;
        background-color: #18ba72;
        border-radius: 4px 0 0 4px;
      }
      .api-url {
        width: 90%;
        padding-left: 30px;
        background-color: #f0faf2;
        border: 1px solid #18ba72;
        border-radius: 0 4px 4px 0;
      }
    }
    .test-api-box-mid {
      width: 100%;
      display: flex;
      flex-direction: column;
      .test-api-box-mid-top {
        padding: 20px 0 15px;
        height: 57px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .title {
        font-weight: bolder;
        font-size: 13px;
        color: #333;
      }
      .common-table {
        :deep(.nancalui-table) {
          .nancalui-table__cell {
            .required {
              color: #f54446;
            }
            .required-input .nancalui-input__wrapper {
              border: 1px solid #f54446;
              &.is-focus {
                box-shadow: none;
              }
            }
          }
        }
      }
    }
    .test-api-box-bottom-dif {
      .title {
        height: 57px;
        padding: 20px 0 15px;
        font-weight: bolder;
        font-size: 13px;
        color: #333333;
      }
      .back-content {
        display: flex;

        .content-left {
          width: 260px;
          .code {
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 5px;
          }
          .code-number {
            padding-top: 20px;
          }
        }
        .content-right {
          flex: 1;
          .back-title {
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 5px;
          }
          .back-data-box {
            padding: 10px 0;
            .back-data {
              border-radius: 4px;
              height: 200px;
              overflow: hidden;
              background: #f7f8fa;
              border-radius: 4px;
              padding-left: 20px;
              > div {
                height: 100%;
              }

              :deep(.jv-container.jv-light) {
                background-color: #f7f8fa;
                padding: 10px 0;
                height: 100%;
                .jv-code {
                  overflow: auto;
                }
              }
              :deep(.jv-container.boxed) {
                border: none;
                &:hover {
                  box-shadow: none;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
