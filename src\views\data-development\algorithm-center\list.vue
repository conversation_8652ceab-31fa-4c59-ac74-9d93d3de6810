<!-- 采集总览 -->
<template>
  <section class="data-development-algorithm-center container">
    <div class="algorithm-center-box">
      <div class="algorithm-center-top">
        <div class="top-bg-box">
          <img src="~@img/dev/algorithm-center-bg.png" alt="" />
        </div>

        <div class="content commonForm">
          <n-form :inline="true" :data="state.formData" class="demo-form-inline">
            <n-form-item class="keyWords-box" label="">
              <n-input
                v-model="state.formData.keyword"
                size="small"
                placeholder="请输入关键字"
                clearable
                prefix="search"
                @clear="onSearch"
              >
                <template #append>
                  <n-button @click.prevent="onSearch">
                    <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                      搜索
                      <!-- <SvgIcon class="icon_search" icon="icon_search" /> -->
                    </n-popover>
                  </n-button>
                </template>
              </n-input></n-form-item
            >
          </n-form>
        </div>
      </div>
      <div class="algorithm-center-mid">
        <n-tabs v-model="state.activeName" @active-tab-change="tabsChange">
          <n-tab title="我的算法" id="PRIVATE" />
          <n-tab title="公共算法" id="PUBLIC" />
        </n-tabs>
        <div class="tag-content-box">
          <div class="tag-list dif">算法类型</div>
          <div
            :class="{ 'tag-list': true, active: item.active }"
            v-for="(item, index) in state.allTags"
            :key="index"
            @click.prevent="activeTagChange(item)"
            >{{ item.name }}</div
          >
        </div>
      </div>
      <div class="algorithm-center-bottom">
        <div class="tabs-content-box">
          <div
            class="add-algorithm"
            v-if="buttonAuthList.includes('governanceManage_dataDev_algorithmCenter_add')"
          >
            <div class="div-buttom" @click.prevent="addAlgorithm">
              <SvgIcon class="add-algorithm-icon" icon="add-algorithm-icon" />
              <span>新增算法</span>
            </div>
          </div>
          <div
            v-if="state.algorithmData.length"
            v-loading="state.loading"
            class="private-algorithm-center scroll-bar-style"
            @scroll="scrollFn"
          >
            <div class="algorithm-list" v-for="(item, index) in state.algorithmData" :key="index">
              <div
                :class="{ 'algorithm-list-img': true, 'algorithm-list-img_full': item.coverUrl }"
              >
                <img
                  :src="
                    item.coverUrl
                      ? `${
                          state.isLzos ? '/indicatorsit' : ''
                        }/api/govern-engine-storage/data-govern/obj/download?bucket=data-govern&objName=${
                          item.coverUrl
                        }&${Math.random()}`
                      : getAssetsImages('algorithm-center-noCover')
                  "
                  alt=""
                />
                <!-- <img
                  :src="`/api/govern-engine-storage/data-govern/obj/download?bucket=data-govern&objName=${
                    item.coverUrl
                  }&${Math.random()}`"
                  alt=""
                /> -->

                <div class="img-shadow"></div>
                <div class="desc">{{ item.description }}</div>
                <!-- <div class="draft-box" v-if="item.displayStatus === 'TEMPORARY_SAVE'">
                  <SvgIcon class="draft-icon" icon="draft-icon" title="草稿" /> 草稿</div
                > -->
                <div
                  v-if="item.accessModifier === 'PRIVATE'"
                  :class="['draft-box', setStatus(item.displayStatus, 'class')]"
                >
                  <SvgIcon
                    class="draft-icon"
                    :icon="setStatus(item.displayStatus)"
                    :title="setStatus(item.displayStatus, 'words')"
                  />
                  {{ setStatus(item.displayStatus, 'words') }}
                </div>
              </div>
              <div class="algorithm-list-words">
                <div class="top-box">
                  <div class="title">{{
                    state.activeName === 'PUBLIC'
                      ? (item.cnName.includes('_COPY') && item.cnName.split('_COPY')[0]) ||
                        item.cnName
                      : item.cnName
                  }}</div>
                  <!--                  <div-->
                  <!--                    class="tag-buttom"-->
                  <!--                    :style="{-->
                  <!--                      background: item.tagList-->
                  <!--                        ? setTagColor(item.tagList[0].name, 'bg')-->
                  <!--                        : setTagColor(state.allTags[0].name, 'bg'),-->
                  <!--                      border: `1px solid ${-->
                  <!--                        item.tagList-->
                  <!--                          ? setTagColor(item.tagList[0].name)-->
                  <!--                          : setTagColor(state.allTags[0].name)-->
                  <!--                      } `,-->
                  <!--                      color: item.tagList-->
                  <!--                        ? setTagColor(item.tagList[0].name)-->
                  <!--                        : setTagColor(state.allTags[0].name),-->
                  <!--                    }"-->
                  <!--                    >{{ item.tagList ? item.tagList[0].name : '&#45;&#45;' }}</div-->
                  <!--                  >-->
                </div>
                <div class="bottom-box">
                  <div class="bottom-box-inline">
                    <div class="updateByName">
                      <SvgIcon class="algorithm-person" icon="algorithm-person" />{{
                        item.updateByName || item.createByName || 'null'
                      }}</div
                    >
                    <div class="updateTime"
                      ><SvgIcon class="algorithm-time" icon="algorithm-time" />{{
                        item.updateTime
                      }}</div
                    >
                  </div>
                  <div class="useTimes">
                    <SvgIcon class="algorithm-times" icon="algorithm-times" />{{
                      item.useTimes + '  '
                    }}
                    次</div
                  >
                </div>
                <div class="bottom-box-hover">
                  <n-button
                    v-if="item.cardOperation.includes('a')"
                    variant="text"
                    @click.prevent="operationFn('edit', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-edit"
                    />编辑</n-button
                  >

                  <n-button
                    v-if="item.cardOperation.includes('c')"
                    variant="text"
                    @click.prevent="operationFn('delete', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-del"
                    />删除</n-button
                  >
                  <!--                  <n-button-->
                  <!--                    v-if="item.cardOperation.includes('e')"-->
                  <!--                    variant="text"-->
                  <!--                    @click.prevent="operationFn('send', item)"-->
                  <!--                  >-->
                  <!--                    <SvgIcon-->
                  <!--                      class="operation-control-btn-icon"-->
                  <!--                      icon="icon-card-send1"-->
                  <!--                    />发布</n-button-->
                  <!--                  >-->
                  <n-button
                    v-if="item.cardOperation.includes('f')"
                    variant="text"
                    @click.prevent="operationFn('off', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-off"
                    />下架</n-button
                  >
                  <n-button
                    v-if="item.cardOperation.includes('g')"
                    variant="text"
                    @click.prevent="operationFn('cancel', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-cancel"
                    />撤回</n-button
                  >
                  <n-button
                    v-if="item.cardOperation.includes('d')"
                    variant="text"
                    title="发布到公共"
                    @click.prevent="operationFn('publish', item)"
                  >
                    <!-- icon="icon-card-sync" -->
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-sync"
                      title="发布到公共"
                    />发布到公共</n-button
                  >
                  <n-button
                    v-if="item.cardOperation.includes('h')"
                    variant="text"
                    title="发布为api服务"
                    @click.prevent="operationFn('publishApi', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-send1"
                      title="发布为api服务"
                    />发布为api服务</n-button
                  >
                  <n-button
                    v-if="item.cardOperation.includes('b')"
                    variant="text"
                    @click.prevent="operationFn('see', item)"
                  >
                    <SvgIcon
                      class="operation-control-btn-icon"
                      icon="icon-card-see"
                    />查看</n-button
                  >
                  <!-- <div v-if="item.cardOperation.length > 3" class="card-operation-more">
                    <n-popover
                      :position="['left-start']"
                      align="start"
                      trigger="hover"
                      :offset="10"
                      algorithm-class="algorithm-center-more-btn"
                    >
                      <template #content>
                        <n-button
                          v-if="item.moreOperation.includes('a')"
                          variant="text"
                          @click.prevent="operationFn('edit', item)"
                        >
                          <SvgIcon
                            class="operation-control-btn-icon"
                            icon="icon-card-edit"
                          />编辑</n-button
                        >

                        <n-button
                          v-if="item.moreOperation.includes('b')"
                          variant="text"
                          @click.prevent="operationFn('see', item)"
                        >
                          <SvgIcon
                            class="operation-control-btn-icon"
                            icon="icon-card-see"
                          />查看</n-button
                        >
                        <n-button
                          v-if="item.moreOperation.includes('c')"
                          variant="text"
                          @click.prevent="operationFn('delete', item)"
                        >
                          <SvgIcon
                            class="operation-control-btn-icon"
                            icon="icon-card-del"
                          />删除</n-button
                        >
                        <n-button
                          v-if="item.moreOperation.includes('d')"
                          variant="text"
                          @click.prevent="operationFn('publish', item)"
                        >
                          <SvgIcon
                            class="operation-control-btn-icon"
                            icon="icon-card-sync"
                          />发布到公共</n-button
                        >
                      </template>
                      <SvgIcon class="icon-more" icon="more" />
                    </n-popover>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          <div v-else v-loading="state.loading" class="private-algorithm-center scroll-bar-style">
            <div class="table-no-content">
              <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
              <div class="empty-text">暂无内容</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { ElNotification } from 'element-plus'
  import { toRef, reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
  import api from '@/api/index'
  export default {
    name: 'ModalMag',
    setup() {
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        loading: false,
        activeName: 'PRIVATE',
        tagId: null,
        allTags: [],
        formData: {
          keyword: null,
        },
        algorithmData: [],
        canLoadMore: false, //是否可以加载更多
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
      })
      const store = useStore()
      const router = useRouter()
      const projectCode = toRef(store.state.user.currentProject, 'projectCode').value
      const { proxy } = getCurrentInstance()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/dev/${name}.png`, import.meta.url).href //本地文件路径
      }
      const methods = {
        //设置状态色和文案
        setStatus(displayStatus, words = null) {
          let cName = '待发布'
          let iconClassName = 'icon-card-new'
          let className = 'blue'
          switch (displayStatus) {
            case 'DRAFT':
              cName = '草稿'
              iconClassName = 'draft-icon'
              className = ''
              break
            case 'WAIT_PUBLISH':
              cName = '待发布'
              iconClassName = 'icon-card-new'
              className = 'blue'
              break
            case 'AUDITING':
              cName = '审核中'
              iconClassName = 'icon-card-audit'
              className = 'yellow'
              break

            case 'PUBLISHED':
              cName = '已发布'
              iconClassName = 'icon-card-send'
              className = 'green'
              break
            case 'AUDIT_REJECT':
              cName = '审核不通过'
              iconClassName = 'icon-card-fail'
              className = 'red'
              break
            case 'OFFLINE':
              cName = '已下架'
              iconClassName = 'icon-card-down'
              className = 'gray'
              break
            default:
              cName = ''
              iconClassName = ''
              className = ''
              break
          }
          return words === 'words' ? cName : words === 'class' ? className : iconClassName
        },
        //设置标签颜色
        setTagColor(name, type = 'border') {
          let borderColors = ['#FF9729', '#04C495', '#447DFD']
          let bgColors = ['#FFF5ED', '#E6FFF4 ', '#F0F7FF ']
          let result = ''
          let allTagsNames = state.allTags.map((item) => item.name)
          let useColor = []
          if (type === 'border') {
            useColor = borderColors
          } else {
            useColor = bgColors
          }
          if (allTagsNames.includes(name)) {
            //第一个是写死的全部，所以-1
            result = useColor[(allTagsNames.indexOf(name) - 1) % 3] || useColor[0]
          } else {
            result = useColor[0]
          }
          return result
        },
        //获取标签列表
        async getAlgorithmTagList() {
          await api.dataDev.algorithmTagList().then((res) => {
            let { success, data } = res
            if (success) {
              data.map((item) => {
                return Object.assign(item, { value: item.id, active: false })
              })
              data.unshift({
                id: null,
                name: '全部算法',
                sort: 0,
                value: null,
                active: true,
              })
              state.allTags = data
            }
          })
        },
        // 算法类型点击
        activeTagChange(item) {
          state.allTags.map((item) => {
            return Object.assign(item, { active: false })
          })
          item.active = true
          state.tagId = item.value
          methods.onSearch()
        },
        tabsChange() {
          localStorage.algorithmCenterActiveName = state.activeName
          methods.onSearch()
        },

        //搜索
        onSearch(init = true) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          if (init) {
            state.algorithmData = []
          }
          let _parames = {
            condition: {
              accessModifier: state.activeName,
              name: state.formData.keyword || null,
              tagId: state.tagId || null,
            },
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
          }
          state.loading = true
          api.dataDev
            .algorithmSearch(_parames)
            .then((res) => {
              let { success, data } = res
              state.loading = false
              if (success) {
                //  a 编辑 b 查看  c删除 d 发布到公共
                data.list.map((item) => {
                  // item.displayStatus = 'WAIT_PUBLISH'

                  let allOptions = []
                  //个人算法
                  if (item.accessModifier === 'PRIVATE') {
                    //编辑
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_edit',
                      ) &&
                      item.displayStatus !== 'AUDITING' &&
                      item.status !== 'ONLINE'
                    ) {
                      allOptions.push('a')
                    }
                    //查看
                    if (
                      buttonAuthList.value.includes('governanceManage_dataDev_algorithmCenter_see')
                    ) {
                      allOptions.push('b')
                    }
                    //删除
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_delete',
                      ) &&
                      item.displayStatus !== 'AUDITING' &&
                      (item.status === 'CREATED' || item.status === 'OFFLINE')
                    ) {
                      allOptions.push('c')
                    }
                    //发布到公共
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_publishPublic_edit',
                      ) &&
                      item.status === 'ONLINE' &&
                      item.displayStatus !== 'AUDITING'
                    ) {
                      allOptions.push('d')
                    }
                    //发布
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_push_edit',
                      ) &&
                      item.displayStatus !== 'AUDITING' &&
                      item.displayStatus !== 'DRAFT' &&
                      item.status !== 'ONLINE'
                    ) {
                      allOptions.push('e')
                    }
                    //下架
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_off_edit',
                      ) &&
                      item.displayStatus !== 'AUDITING' &&
                      item.status === 'ONLINE'
                    ) {
                      allOptions.push('f')
                    }
                    //撤回
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_recall_edit',
                      ) &&
                      item.displayStatus === 'AUDITING'
                    ) {
                      allOptions.push('g')
                    }
                    //发布为api服务
                    if (
                      buttonAuthList.value.includes(
                        'governanceManage_dataDev_algorithmCenter_pushApi_edit',
                      ) &&
                      item.displayStatus !== 'AUDITING' &&
                      item.status === 'ONLINE' &&
                      !item.isPublishApi
                    ) {
                      allOptions.push('h')
                    }
                  }
                  //公共算法
                  if (item.accessModifier === 'PUBLIC') {
                    if (
                      buttonAuthList.value.includes('governanceManage_dataDev_algorithmCenter_see')
                    ) {
                      allOptions.push('b')
                    }
                  }
                  allOptions = allOptions.join('')
                  return Object.assign(item, {
                    cardOperation: allOptions,
                    outOperation: allOptions.slice(0, 3),
                    moreOperation: allOptions.slice(3) || null,
                  })
                })
                state.canLoadMore = false
                if (data.pageNum < data.pages) {
                  state.canLoadMore = true
                }
                state.algorithmData.push(...data.list)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 新增算法
        addAlgorithm() {
          if (!projectCode) {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'warning',
            })
            return
          }
          router.push({
            name: 'algorithmCenterAdd',
          })
        },
        // 滚动到底部 -滑动加载
        scrollFn(event) {
          let el = event.target
          if (el.scrollTop + el.clientHeight >= el.scrollHeight - 10) {
            if (state.canLoadMore) {
              state.canLoadMore = false
              state.pageInfo.currentPage++
              methods.onSearch(false)
            }
          }
        },
        //操作按钮
        operationFn(fnName, item) {
          switch (fnName) {
            case 'edit':
              //编辑
              router.push({
                name: 'algorithmCenterEdit',
                query: { editId: item.id, displayStatus: item.displayStatus },
              })
              break
            case 'see':
              //查看
              router.push({
                name: 'algorithmCenterDetail',
                query: { editId: item.id },
              })
              break
            case 'delete':
              //删除
              if (item.referenced) {
                // 已被引用
                proxy.$MessageBoxService.open({
                  title: '提示',
                  content: '当前算法已被数据作业引用，不可删除',
                })
              } else {
                proxy.$MessageBoxService.open({
                  title: '提示',
                  content: '确定删除此算法？',
                  save: () => {
                    api.dataDev.algorithmDelete({ id: item.id }).then((res) => {
                      let { success } = res
                      if (success) {
                        ElNotification({
                          title: '提示',
                          message: '删除算法成功',
                          type: 'success',
                        })
                        methods.onSearch()
                      }
                    })
                  },
                })
              }

              break
            case 'publish':
              //发布到公共
              proxy.$MessageBoxService.open({
                title: '提示',
                content: '算法发布至公共后，该算法将会被所有场景共用，是否发布？',
                save: () => {
                  api.dataDev.algorithmPublicPublish({ id: item.id }).then((res) => {
                    let { success } = res
                    if (success) {
                      ElNotification({
                        title: '提示',
                        message: '算法发布到公共成功',
                        type: 'success',
                      })
                      methods.onSearch()
                    }
                  })
                },
              })

              break
            case 'send':
              //发布
              proxy.$MessageBoxService.open({
                title: '发布算法',
                content: '确定发布此算法？',
                save: () => {
                  api.dataDev.algorithmPublish({ id: item.id }).then((res) => {
                    let { success } = res
                    if (success) {
                      ElNotification({
                        title: '提示',
                        message: '发布请求已发布至审核',
                        type: 'success',
                      })
                      methods.onSearch()
                    }
                  })
                },
              })
              break
            case 'off':
              //下架
              if (item.hasPublicAlgo) {
                //有对应公共算法

                proxy.$MessageBoxService.open({
                  title: '下架算法',
                  content: '是否将公共算法内此算法同步下架？？',
                  save: () => {
                    api.dataDev
                      .algorithmOffline({ id: item.id, asyncOfflinePublicAlgo: true })
                      .then((res) => {
                        let { success } = res
                        if (success) {
                          ElNotification({
                            title: '提示',
                            message: '下架请求已发布至审核',
                            type: 'success',
                          })
                          methods.onSearch()
                        }
                      })
                  },
                  cancel: () => {
                    api.dataDev
                      .algorithmOffline({ id: item.id, asyncOfflinePublicAlgo: false })
                      .then((res) => {
                        let { success } = res
                        if (success) {
                          ElNotification({
                            title: '提示',
                            message: '下架请求已发布至审核',
                            type: 'success',
                          })
                          methods.onSearch()
                        }
                      })
                  },
                })
              } else {
                proxy.$MessageBoxService.open({
                  title: '下架算法',
                  content: '确定下架此算法？',
                  save: () => {
                    api.dataDev
                      .algorithmOffline({ id: item.id, asyncOfflinePublicAlgo: false })
                      .then((res) => {
                        let { success } = res
                        if (success) {
                          ElNotification({
                            title: '提示',
                            message: '下架请求已发布至审核',
                            type: 'success',
                          })
                          methods.onSearch()
                        }
                      })
                  },
                })
              }

              break
            case 'cancel':
              //撤回
              proxy.$MessageBoxService.open({
                title: '撤销审批',
                content: '是否确认撤销审批？撤销后，需要重新发布审核',
                save: () => {
                  api.dataDev.algorithmRollback({ id: item.id }).then((res) => {
                    let { success } = res
                    if (success) {
                      ElNotification({
                        title: '提示',
                        message: '撤销审批成功',
                        type: 'success',
                      })
                      methods.onSearch()
                    }
                  })
                },
              })

              break
            case 'publishApi':
              //发布为api服务
              this.$store.commit('user/SET_FIRST_MENU', 'asset_manage')
              router.push({ name: 'addApi', query: { algorithmId: item.id } })
              break

            default:
              break
          }
        },
      }
      if (localStorage.algorithmCenterActiveName) {
        state.activeName = localStorage.algorithmCenterActiveName
      }
      onMounted(() => {
        // methods.getAlgorithmTagList()
        methods.onSearch()
      })

      return {
        state,
        buttonAuthList,
        getAssetsImages,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-development-algorithm-center {
    height: calc(100vh - $navbarHeight);
    .algorithm-center-box {
      height: 100%;
      // background-color: #fff;
      border-radius: 4px;

      .algorithm-center-top {
        position: relative;
        height: 224px;
        padding: 2px;
        background-color: #fff;
        border-radius: 8px 8px 0 0;
        .top-bg-box {
          width: 100%;
          overflow: hidden;
          text-align: center;
          border-radius: 8px;
          img {
            max-width: initial;
            height: 220px;
            margin-left: 50%;
            transform: translate(-50%);
          }
        }

        .content {
          position: absolute;
          right: 0;
          bottom: 62px;
          left: 0;
          width: 460px;
          height: 48px;
          margin: 0 auto;
          .nancalui-form {
            margin: 0 auto;
            :deep(.keyWords-box) {
              .nancalui-input-slot .nancalui-input__wrapper {
                padding-right: 74px;
                .nancalui-input__inner {
                  font-size: 14px;
                }
              }
            }
            .nancalui-form__item--horizontal .nancalui-input-slot {
              width: 460px;
              height: 48px;
            }
            :deep(.nancalui-form__item--horizontal .nancalui-input .nancalui-input__wrapper) {
              padding: 0 16px;
              border: none;
              border-radius: 24px;
            }
            :deep(
                .nancalui-form__item--horizontal .nancalui-input-slot .nancalui-input-slot__append
              ) {
              width: 60px;
              height: 44px;
              .nancalui-button {
                width: 100%;
                height: 44px;
                border-radius: 22px;
                .button-content {
                  color: #fff;
                  font-size: 14px;
                }
              }
            }
            :deep(.nancalui-input-slot__prefix > *) {
              margin-right: 3px;
            }
            :deep(.nancalui-input-slot__prefix .icon-search) {
              width: 16px;
              height: 16px;
              margin-top: 2px;
              &::before {
                color: #b8b8b8;
                font-size: 16px;
              }
            }

            .nancalui-input__inner::placeholder {
              color: #b8b8b8;
              font-size: 14px;
            }
          }
        }
      }
      .algorithm-center-mid {
        height: 96px;
        margin-bottom: 4px;
        padding: 0 20px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        .tag-content-box {
          // display: flex;

          // align-items: center;
          height: 48px;
          margin: 10px 0 32px 0;
          .tag-list {
            display: inline-block;
            margin-right: 8px;
            padding: 0 14px;
            color: #000000;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 32px;
            cursor: pointer;
            &.active {
              color: $themeBlue;
              background: #f0f7ff;
              border-radius: 6px;
            }
            &.dif {
              margin-right: 24px;
              padding: 0;
              color: #999999;
              cursor: default;
            }
          }
        }
      }
      .algorithm-center-bottom {
        height: calc(100% - 224px - 100px);
        padding: 0 20px;
        background-color: #fff;
        border-radius: 8px;
        .add-algorithm {
          position: relative;
          z-index: 2;
          display: flex;
          align-items: center;
          height: 48px;
          background-color: #fff;
          .div-buttom {
            display: flex;
            align-items: center;
            height: 32px;
            padding: 0 8px;
            border-radius: 6px;
            cursor: pointer;
            &:hover {
              background: #ecf7ff;
            }
            svg {
              margin-right: 3px;
              font-size: 16px;
            }
            span {
              color: #447dfd;

              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
            }
          }
        }
        .tabs-content-box {
          z-index: 1;
          height: 100%;

          .private-algorithm-center {
            display: flex;
            flex-wrap: wrap;
            // height: calc(100% - 52px);
            height: calc(100% - 48px);
            margin-top: -16px;
            overflow: auto;

            .table-no-content {
              position: relative;
              top: 50%;
              left: 50%;
              height: 180px;
              transform: translate(-50%, -50%);
              img {
                display: block;
                width: 140px;
                height: auto;
                margin: 0 auto;
              }
              .empty-text {
                margin-top: 20px;
                color: #999999;
                font-size: 12px;
                text-align: center;
              }
            }
            .algorithm-list {
              display: inline-block;
              width: calc((100% - 20px) / 2);
              // height: 266px;
              height: 258px;
              margin-top: 16px;
              margin-right: 20px;
              overflow: hidden;
              background: #ffffff;
              border: 1px solid #ebedf0;
              border-radius: 6px;

              &:nth-child(2n) {
                margin-right: 0;
              }
              &:hover {
                .algorithm-list-img {
                  .img-shadow {
                    background: linear-gradient(
                      180deg,
                      rgba(0, 0, 0, 0.2) 0%,
                      rgba(0, 0, 0, 0.6) 100%
                    );
                    border-radius: 6px 6px 0px 0px;
                  }
                  .desc {
                    display: -webkit-box;
                  }
                }

                .algorithm-list-words {
                  .bottom-box {
                    display: none;
                  }
                  .bottom-box-hover {
                    display: flex;
                  }
                }
              }
              .algorithm-list-img {
                position: relative;
                width: 100%;
                background: rgba(235, 237, 240, 0.7);
                .draft-box {
                  position: absolute;
                  top: 10px;
                  right: 12px;
                  display: flex;
                  align-items: center;
                  height: 24px;
                  padding: 0 8px;
                  color: #ffffff;
                  font-size: 12px;
                  background: rgba(0, 0, 0, 0.3);
                  border-radius: 6px;
                  filter: blur(6px);
                  backdrop-filter: blur(6px);
                  &.green {
                    background: #04c495;
                  }

                  &.yellow {
                    background: #ff7d00;
                  }
                  &.red {
                    background: #f63838;
                  }
                  &.gray {
                    background: #b8b8b8;
                  }
                  &.darkGrey {
                    background: #697a9a;
                  }

                  &.blue {
                    background: #257bff;
                  }
                  .draft-icon {
                    margin-right: 4px;
                    font-size: 14px;
                  }
                }
                .img-shadow {
                  position: absolute;
                  top: 0;
                  right: 0;
                  bottom: 0;
                  left: 0;
                }
                .desc {
                  position: absolute;
                  right: 0;
                  bottom: 12px;
                  left: 0;
                  display: none;
                  height: 34px;
                  padding: 0 12px;
                  overflow: hidden;
                  color: #ffffff;
                  font-weight: 500;
                  font-size: 12px;
                  line-height: 17px;
                  text-overflow: ellipsis;
                  word-wrap: break-word;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                }

                img {
                  display: block;
                  height: 180px;
                  margin: 0 auto;
                }
              }
              .algorithm-list-img_full {
                img {
                  width: 100%;
                }
              }
              .algorithm-list-words {
                padding: 14px 12px;
                .top-box {
                  display: flex;
                  align-items: center;
                  .title {
                    // flex: 1 0;
                    overflow: hidden;
                    color: #000000;
                    font-weight: bolder;
                    font-size: 16px;
                    line-height: 20px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  .tag-buttom {
                    display: flex;
                    align-items: center;
                    height: 20px;
                    margin-left: 4px;
                    padding: 0 8px;
                    color: #ffffff;
                    font-weight: 500;
                    font-size: 12px;
                    background: #fff5ed;
                    border: 1px solid #ff9729;
                    border-radius: 10px;
                  }
                }
                .bottom-box {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-top: 6px;
                  color: #999999;
                  font-weight: 400;
                  font-size: 12px;
                  line-height: 20px;
                  svg {
                    margin-right: 4px;
                  }
                  .bottom-box-inline {
                    display: flex;
                    align-items: center;

                    .updateByName {
                      display: flex;
                      align-items: center;
                      margin-right: 20px;
                      svg {
                        font-size: 14px;
                      }
                    }
                    .updateTime {
                      display: flex;
                      align-items: center;
                    }
                  }
                  .useTimes {
                    display: flex;
                    align-items: center;
                  }
                }

                .bottom-box-hover {
                  display: flex;
                  display: none;
                  justify-content: flex-end;
                  margin-top: 6px;
                  .nancalui-button {
                    &:hover {
                      color: $themeBlue;
                    }
                  }
                  .card-operation-more {
                    line-height: 25px;
                    cursor: pointer;
                    & > svg {
                      font-size: 18px;
                    }
                  }
                  .nancalui-button + .nancalui-button {
                    margin-left: 0;
                  }
                  .nancalui-button {
                    padding: 0 8px;
                    color: #333333;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 18px;

                    :deep(.button-content) {
                      display: inline-block;
                      max-width: 64px;
                      overflow: hidden;
                      line-height: 20px;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    }
                    svg {
                      margin-right: 6px;
                      font-size: 18px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /*屏幕宽度大于1220时展示3个卡片*/
  @media only screen and (min-width: 1220px) {
    .data-development-algorithm-center
      .algorithm-center-box
      .algorithm-center-bottom
      .tabs-content-box
      .private-algorithm-center {
      .algorithm-list {
        width: calc((100% - 40px) / 3);
        &:nth-child(2n) {
          margin-right: 20px;
        }
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }

  /*屏幕宽度大于1630时展示4个卡片*/
  @media only screen and (min-width: 1630px) {
    .data-development-algorithm-center
      .algorithm-center-box
      .algorithm-center-bottom
      .tabs-content-box
      .private-algorithm-center {
      .algorithm-list {
        width: calc((100% - 60px) / 4);
        &:nth-child(2n) {
          margin-right: 20px;
        }
        &:nth-child(3n) {
          margin-right: 20px;
        }
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
    }
  }
</style>
<style lang="scss">
  @import '@/styles/variables.scss';
  .nancalui-popover__content[algorithm-class] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 115px;

    .nancalui-button {
      padding: 0;
      color: #333333;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      &:hover {
        color: $themeBlue;
      }
      svg {
        margin-right: 6px;
      }
    }
    .nancalui-button + .nancalui-button {
      margin-left: 0;
    }
  }
</style>
