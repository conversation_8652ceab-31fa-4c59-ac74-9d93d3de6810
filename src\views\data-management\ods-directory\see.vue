<!-- 
  <div> <detailModel :isModel="true" :isTagData="false" seeName="resourceLibraryDetail" /></div>

  import detailModel from '@/views/data-assets/globalSearch/detail.vue'
   -->
<template>
  <!-- 结构化数据采集  -->
  <div class="data-collection">
    <div
      :class="{
        'data-collection-add': true,
        container: true,
        isLzos: state.isLzos,
      }"
    >
      <div class="add-box">
        <div class="page-title">
          详情信息
          <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
        </div>

        <div class="box-content scroll-bar-style">
          <div class="box-content-inside">
            <div class="inside-box">
              <div class="inside-box-content">
                <div class="content-title">
                  <span>基础信息</span>
                </div>
                <div class="info">
                  <div class="info-item">
                    <span class="info-item-label">创建人：</span>
                    <span class="info-item-value">{{ state.detail.createByName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-item-label">关联任务：</span>
                    <span class="info-item-value">{{ state.detail.collectName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-item-label">创建时间：</span>
                    <span class="info-item-value">{{ state.detail.createTime }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-item-label">最近更新：</span>
                    <span class="info-item-value">{{ state.detail.updateTime }}</span>
                  </div>
                </div>

                <div class="content-title">
                  <span>使用信息</span>
                </div>
                <div class="info">
                  <div class="info-item">
                    <span class="info-item-label">已授权人员：</span>
                    <span class="info-item-value">{{ state.detail.authorizedNum }} 个</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="inside-box-content footer">
              <div
                class="page-title"
                style="margin-bottom: 0"
                :sub-label="$route.query.securityLevelDiplay"
              >
                {{ $route.query?.cnName || "--" }}
              </div>

              <div class="detail-model">
                <div class="detail-model-radio" style="padding: 8px">
                  <el-radio-group
                    v-model="state.activeName"
                    style="margin-bottom: 30px"
                    @change="changeActive"
                  >
                    <el-radio-button value="0">表结构</el-radio-button>
                    <el-radio-button value="1">数据预览</el-radio-button>
                  </el-radio-group>
                  <div v-if="state.activeName == '1'"> 仅展示前20条数据 </div>
                </div>
                <div class="table" v-loading="state.loadingPreview">
                  <CfTable
                    v-if="state.activeName == '0'"
                    :tableConfig="{
                      data: state.tableData,
                      rowKey: 'id',
                    }"
                    isNeedIndex
                    :table-head-titles="state.tableHeadTitles"
                    emptyText="暂无数据"
                  />
                  <CfTable
                    v-else
                    :tableConfig="{
                      data: state.detail.isAuthorized ? state.listPreview : null,
                      rowKey: 'id',
                    }"
                    :emptyText="state.detail.isAuthorized ? '暂无数据' : '未授权'"
                    isNeedIndex
                    :table-head-titles="getTableHead"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import CfTable from '@/components/cfTable'
  import { useRouter } from 'vue-router'

  export default {
    name: '',
    components: { CfTable },
    props: {},
    setup() {
      const router = useRouter()
      const state = reactive({
        tableHeadTitles: [
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'fieldType', name: '字段类型' },
        ],
        detailId: router.currentRoute.value.query.id,
        modelName: router.currentRoute.value.query.modelName,
        detail: {},
        activeName: '0',
        previewPagination: {
          currentPage: 1,
          pageSize: 20,
          total: 0,
        },
      })

      const methods = {
        getTableHead: computed(() => {
          return state.tableData.map(({ name, cnName }) => {
            return {
              name: cnName || name,
              prop: name,
            }
          })
        }),
        changeActive(val) {
          if (val == '0') {
            methods.getMetaDataByModel(true)
          } else {
            state.detail.isAuthorized && methods.getDataPreview(true)
          }
        },
        closeFn() {
          router.go(-1)
        },
        // 获取详情
        getDetail() {
          api.ods.getDetail(state.detailId).then((res) => {
            state.detail = res.data
          })
        },
        getDataPreview(init) {
          state.previewPagination.currentPage = init ? 1 : state.previewPagination.currentPage
          let data = {
            condition: {
              id: state.detailId,
              limitPreview: 20,
            },
            pageNum: state.previewPagination.currentPage,
            pageSize: state.previewPagination.pageSize,
          }
          state.loadingPreview = true
          api.model
            .getDataWithProject(data)
            .then((res) => {
              state.loadingPreview = false
              let { success, data } = res
              if (success) {
                if (data.list && data.list.length > 0) {
                  state.listPreview = data.list
                }
                state.previewPagination.total = data?.total || 0
              }
            })
            .catch(() => {
              state.loadingPreview = false
            })
        },
        getMetaDataByModel(init) {
          state.loadingPreview = true
          api.dataManagement
            .getAdhocQueryLeftListAssetsDetail({ id: state.detailId })
            .then((res) => {
              state.loadingPreview = false
              let { data, success } = res
              if (success) {
                data?.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                state.tableData = data || []
              }
            })
            .catch(() => {
              state.loadingPreview = false
            })
        },
      }
      methods.getDetail()
      methods.getMetaDataByModel()

      onMounted(() => {})

      return {
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection {
    height: 100%;
  }
  .data-collection-add {
    box-sizing: border-box;
    height: calc(100%);
    padding: 12px;
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 46px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        margin-bottom: 10px;
        border-radius: 2px;
        &[sub-label]::after {
          margin-left: 12px;
          content: attr(sub-label);
          display: inline-block;
          padding: 0px 4px;
          border-radius: 2px;
          line-height: 22px;
          border: 1px solid rgba(26, 164, 238, 0.4);
          background: rgba(26, 164, 238, 0.08);

          color: #1aa4ee;

          font-family: 'Source Han Sans CN';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
        }
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          width: 4px;
          height: 18px;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 56px);
        overflow: auto;
        border-radius: 2px 2px 0 0;
        &-inside {
          height: 100%;
          display: flex;
          flex-direction: column;
          .inside-box-content {
            flex: 1;
          }
        }
        .inside-box {
          &-content {
            background-color: #fff;
            border-radius: 2px;

            &.footer {
              height: calc(100% - 224px);
              border-radius: 2px 2px 0 0;
              margin-top: 10px;
              display: flex;
              flex-direction: column;
            }
            &:first-of-type {
              padding-top: 16px;
            }
          }
          .content-title {
            margin-bottom: 16px;
            height: 30px;
            line-height: 30px;
            color: #2b71c2;
            font-size: 14px;
            background-color: #f2f6fc;
            padding-left: 14px;
            position: relative;
            &:before {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              margin: auto;
              width: 4px;
              height: 18px;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .base-form {
          padding: 0 16px;
          .inline {
            display: flex;
            align-items: flex-start;
            &-left,
            .nancalui-form__item--horizontal {
              flex: 1;
            }
          }

          .nancalui-form__item--horizontal {
            margin-bottom: 16px;
          }
          .nancalui-input,
          .nancalui-select {
            max-width: 430px;
          }

          .nancalui-textarea__div {
            max-width: 434px !important;
          }
          .check-style {
            margin-top: -6px;
            margin-bottom: 10px;
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .box-operate {
    position: absolute;
    right: 12px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    width: calc(100% - 24px);
    height: 64px;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 0 0 2px 2px;
    :deep(.nancalui-button) {
      border-radius: 2px;
    }
    &.isLzos {
      right: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0px 16px;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    padding-bottom: 16px;
    &-item {
      color: var(----, #606266);
      font-family: 'Source Han Sans CN';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      display: flex;
      align-items: center;
      &-label {
        display: inline-block;
        width: 150px;
      }
      &-value {
        display: inline-block;
        overflow: hidden;
        color: var(----, #1d2129);
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
      }
    }
  }
  .el-radio-group {
    margin-bottom: 0px !important;
  }
  .detail-model {
    height: calc(100% - 46px);
    flex: 1;
    display: flex;
    flex-direction: column;
    &-radio {
      display: flex;
      padding: 8px;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      color: var(----, #606266);

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .table {
      height: calc(100% - 48px);
    }
  }
</style>
