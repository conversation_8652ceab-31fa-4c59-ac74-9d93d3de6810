<template>
  <div v-if="showTips" class="minio-tips">
    <div>MinI0文件存储空间告警!空间占用率已超 90%，请及时处理。</div>
    <span>不再提醒</span>
  </div>
  <router-view @click="closeProjectFn" />
</template>

<script>
  import { useDraggable } from '@vueuse/core'
  import { storeStorage } from '@/utils/auth'
  export default {
    name: 'App',
    data() {
      return {
        showTips: false,
        mouseDown: false,
      }
    },
    created() {
      // 在页面加载时读取localStorage里的状态信息
      if (localStorage.getItem(storeStorage)) {
        // if (this.$store) {
        this.$store.replaceState(
          Object.assign({}, this.$store.state, JSON.parse(localStorage.getItem(storeStorage))),
        )
        // }
      }

      // 在页面刷新时将vuex里的信息保存到sessionStorage里
      window.addEventListener('beforeunload', () => {
        sessionStorage.removeItem('workCacheData')
        localStorage.setItem(storeStorage, JSON.stringify(this.$store.state))
      })

      // 监听其他浏览器标签页面变化，如果userId发生改变，则刷新页面返回统一登录页
      window.onstorage = () => {
        if (localStorage.getItem(storeStorage) && sessionStorage.getItem('pageIsIframe') !== 'Y') {
          let info = JSON.parse(localStorage.getItem(storeStorage))?.user // 储存本地的store
          let storeInfo = this.$store.state.user // 当前store
          if (info.id !== storeInfo.id) {
            this.$store.replaceState(
              Object.assign({}, this.$store.state, JSON.parse(localStorage.getItem(storeStorage))),
            )
            this.$router.push({ name: 'ConvergencePage' })
          }
        }
      }

      document.onmouseup = (e) => {
        this.mouseDown = false
      }
    },
    watch: {
      $route() {
        this.$nextTick(() => {
          const root = document.querySelector('#app .app-main')
          if (!root) return
          root.style.setProperty('--aside-width', `286px`)
          const lineEl = document.createElement('div')
          lineEl.classList.add('cf_line')
          Object.assign(lineEl.style, {
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            width: '2px',
            height: '100%',
            background: '#dcdfe6',
            cursor: 'col-resize',
            transform: 'translate(50%, 0)',
          })
          let el
          ;[
            '.asideTree',
            '.data-collection-page-tree',
            '.cf-tree',
            '.left.nc-p-t-10.nc-p-l-12.nc-p-r-12',
            '.dataQuality-index-list-tree',
          ].forEach((item) => {
            let rEl = root.querySelector(item)
            if (!rEl) return
            el = rEl
            !el.style?.position && (el.style.position = 'relative')
          })
          if (!el) return
          el.appendChild(lineEl)
          const elClientRect = el.getBoundingClientRect()
          const { x, y, style } = useDraggable(lineEl, {
            initialValue: { x: 0, y: 0 },
            onStart: () => {
              this.mouseDown = true
              console.log('开始拖拽')
              lineEl.style.setProperty('--beforeWidth', `200px`)
            },
            onEnd: () => {
              this.mouseDown = false
              console.log('结束拖拽')
              lineEl.style.setProperty('--beforeWidth', `0px`)
            },
            onMove: (position) => {
              if (!this.mouseDown) return
              const w = Math.max(position.x - elClientRect.x, 200)
              el.style.width = `${Math.min(w, 800)}px`
              root.style.setProperty('--aside-width', `${el.style.width}`)
            },
          })
        })
      },
    },

    methods: {
      // 点击空白位置时关闭右上角项目导航展示栏
      closeProjectFn() {
        this.$store.commit('app/CLOSE_PROJECT', false)
        this.$store.commit('app/BODY_CLICK', true)
      },
    },
  }
</script>

<style lang="scss" scoped>
  #app {
    width: 100%;
    min-width: 860px;
    color: #2c3e50;
    font-family: Avenir, Helvetica, Arial, sans-serif;
    background-color: var(--color-bg-1);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    .minio-tips {
      position: fixed;
      top: 66px;
      right: 0;
      left: 0;
      z-index: 9999;
      display: flex;
      justify-content: space-between;
      width: 40%;
      margin: 0 auto;
      padding: 10px;
      font-size: 12px;
      background-color: #fff7e7;
      border: 1px solid #ffcd6a;
    }
  }
</style>
<style>
  .cf_line::before {
    position: absolute;
    top: 0;
    bottom: 0;
    display: inline-block;
    width: var(--beforeWidth);
    transform: translateX(-50%);
    content: '';
  }
</style>
