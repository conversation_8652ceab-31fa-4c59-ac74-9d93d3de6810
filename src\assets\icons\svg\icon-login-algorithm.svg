<?xml version="1.0" encoding="UTF-8"?>
<svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>算法中心</title>
    <defs>
        <linearGradient x1="2.78011225%" y1="8.86965928%" x2="100%" y2="91.1303407%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E8EAF3" stop-opacity="0.82760172" offset="49.8724664%"></stop>
            <stop stop-color="#314F8D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="2-数据集成" transform="translate(-865.000000, -730.000000)">
            <g id="入口" transform="translate(280.000000, 220.000000)">
                <g id="模块" transform="translate(550.000000, 490.000000)">
                    <g id="算法中心" transform="translate(35.000000, 20.000000)">
                        <rect id="矩形" x="0" y="0" width="50" height="50"></rect>
                        <g id="编组" opacity="0.200000003" transform="translate(2.000000, 5.500000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
                            <path d="M20.5081333,27.9258088 C21.0622055,27.3415409 21.9349278,27.3415409 22.489,27.9258088 L22.489,27.9258088 L22.489,27.9226466 L30.3895333,36.280467 C30.8100284,36.7275497 30.9425727,37.4113547 30.7245876,38.0090335 C30.5066025,38.6067123 29.9816848,38.9987295 29.3976667,39 L29.3976667,39 L13.5994667,39 C13.0141509,39 12.4876013,38.6075428 12.2693507,38.0084404 C12.0511001,37.4093379 12.185002,36.7240376 12.6076,36.2773048 L12.6076,36.2773048 Z M40.1333333,0 C41.6600062,0 42.9079426,1.31646473 42.9951336,2.97644156 L43,3.16224763 L43,30.0413525 C43,31.72544 41.8065876,33.1020507 40.3017718,33.198232 L40.1333333,33.2036001 L29.5496,33.2036001 L23.1970667,26.0948674 C22.7528948,25.5862365 22.1402273,25.2988685 21.5,25.2988685 C20.9179752,25.2988685 20.358727,25.5363627 19.9281428,25.9616854 L19.8029333,26.0948674 L13.4504,33.2036001 L2.86666667,33.2036001 C1.33999382,33.2036001 0.0920573819,31.8871354 0.00486635148,30.2271585 L0,30.0413525 L0,3.16224763 C0,1.4781601 1.19341241,0.101549385 2.69822822,0.00536811922 L2.86666667,0 L40.1333333,0 Z M22.9333333,17.392362 L7.16666667,17.392362 C6.37505853,17.392362 5.73333333,18.1002552 5.73333333,18.9734858 C5.73333333,19.7982035 6.30573636,20.4754445 7.03620412,20.548148 L7.16666667,20.5546096 L22.9333333,20.5546096 C23.7249415,20.5546096 24.3666667,19.8467163 24.3666667,18.9734858 C24.3666667,18.1002552 23.7249415,17.392362 22.9333333,17.392362 Z M15.7666667,9.48674288 L7.16666667,9.48674288 C6.37505853,9.48674288 5.73333333,10.1946361 5.73333333,11.0678667 C5.73333333,11.8925845 6.30573636,12.5698254 7.03620412,12.642529 L7.16666667,12.6489905 L15.7666667,12.6489905 C16.5582748,12.6489905 17.2,11.9410973 17.2,11.0678667 C17.2,10.1946361 16.5582748,9.48674288 15.7666667,9.48674288 Z" id="形状结合"></path>
                        </g>
                        <g id="编组" transform="translate(5.000000, 5.500000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
                            <path d="M20.5081333,27.9258088 C21.0622055,27.3415409 21.9349278,27.3415409 22.489,27.9258088 L22.489,27.9258088 L22.489,27.9226466 L30.3895333,36.280467 C30.8100284,36.7275497 30.9425727,37.4113547 30.7245876,38.0090335 C30.5066025,38.6067123 29.9816848,38.9987295 29.3976667,39 L29.3976667,39 L13.5994667,39 C13.0141509,39 12.4876013,38.6075428 12.2693507,38.0084404 C12.0511001,37.4093379 12.185002,36.7240376 12.6076,36.2773048 L12.6076,36.2773048 Z M40.1333333,0 C41.6600062,0 42.9079426,1.31646473 42.9951336,2.97644156 L43,3.16224763 L43,30.0413525 C43,31.72544 41.8065876,33.1020507 40.3017718,33.198232 L40.1333333,33.2036001 L29.5496,33.2036001 L23.1970667,26.0948674 C22.7528948,25.5862365 22.1402273,25.2988685 21.5,25.2988685 C20.9179752,25.2988685 20.358727,25.5363627 19.9281428,25.9616854 L19.8029333,26.0948674 L13.4504,33.2036001 L2.86666667,33.2036001 C1.33999382,33.2036001 0.0920573819,31.8871354 0.00486635148,30.2271585 L0,30.0413525 L0,3.16224763 C0,1.4781601 1.19341241,0.101549385 2.69822822,0.00536811922 L2.86666667,0 L40.1333333,0 Z M22.9333333,17.392362 L7.16666667,17.392362 C6.37505853,17.392362 5.73333333,18.1002552 5.73333333,18.9734858 C5.73333333,19.7982035 6.30573636,20.4754445 7.03620412,20.548148 L7.16666667,20.5546096 L22.9333333,20.5546096 C23.7249415,20.5546096 24.3666667,19.8467163 24.3666667,18.9734858 C24.3666667,18.1002552 23.7249415,17.392362 22.9333333,17.392362 Z M15.7666667,9.48674288 L7.16666667,9.48674288 C6.37505853,9.48674288 5.73333333,10.1946361 5.73333333,11.0678667 C5.73333333,11.8925845 6.30573636,12.5698254 7.03620412,12.642529 L7.16666667,12.6489905 L15.7666667,12.6489905 C16.5582748,12.6489905 17.2,11.9410973 17.2,11.0678667 C17.2,10.1946361 16.5582748,9.48674288 15.7666667,9.48674288 Z" id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
