<template>
  <!-- 打标任务 -->
  <div class="marking-tag-task-list container">
    <div class="marking-tag-task-list-box">
      <div class="tag-left">
        <PublicLeftTree
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          :checkedNodes="state.allTableData"
          @treeCheckNode="treeCheckNode"
          @treeUpdateNode="treeUpdateNode"
          @treeDelNode="treeDelNode"
        >
          <!-- <template #pageTop>
            <div class="title">业务域目录树</div>
          </template> -->
        </PublicLeftTree>
      </div>
      <div class="tag-right" v-loading="state.loading">
        <n-public-table
          :isDisplayAction="true"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          :actionWidth="120"
          @tablePageChange="tablePageChange"
        >
          <template #pageTop>
            <div class="box-add commonForm-search">
              <div class="top-left"> </div>
              <div class="commonForm">
                <n-form
                  :inline="true"
                  :data="state.filterSearch"
                  class="demo-form-inline commonForm search-right"
                >
                  <n-form-item label="">
                    <n-input
                      v-model="state.filterSearch.keyword"
                      size="small"
                      placeholder="请输入模型名称"
                      clearable
                      @clear="onSearch"
                    >
                      <template #append>
                        <n-button @click.prevent="onSearch">
                          <n-popover
                            class="item"
                            content="搜索"
                            trigger="hover"
                            :position="['bottom']"
                          >
                            <SvgIcon class="icon_search" icon="icon_search" />
                          </n-popover>
                        </n-button>
                      </template>
                    </n-input>
                  </n-form-item>
                </n-form>
              </div>
            </div>
          </template>
          <template #modelCount="{ editor }">
            <div class="total-row">{{ editor.row.modelCount === '1' ? '是' : '否' }}</div>
          </template>
          <template #editor="{ editor }">
            <div class="edit-box">
              <n-button
                v-if="buttonAuthList.includes('assetsSupervise_tagManagement_view')"
                code="assetsSupervise_tagManagement_view"
                class="has-right-border"
                variant="text"
                @click.prevent="checkTask(editor)"
                >查看</n-button
              >
              <n-button
                v-if="buttonAuthList.includes('assetsSupervise_tagManagement_marking_edit')"
                code="assetsSupervise_tagManagement_marking_edit"
                class="has-right-border"
                variant="text"
                @click.prevent="markingTag(editor)"
                >打标</n-button
              >
              <!-- <n-button class="has-right-border" variant="text" @click="deleteTask(editor)"
                >删除</n-button
              > -->
            </div>
          </template>
        </n-public-table>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { formartTime } from '@/utils/index'
  // import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { sceneManage } from '@/api/index'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import ENUM from '@/const/enum'

  export default {
    name: 'AuthorizedPersonnel',
    setup() {
      // 获取当前组件实例、
      const router = useRouter()
      const { proxy } = getCurrentInstance()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const state = reactive({
        tableHeight: 436,
        tableData: {},
        loading: false,
        shortcuts: ENUM.SHORTCUTS,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '模型中文名' },
          { prop: 'name', name: '模型英文名' },
          { prop: 'bizName', name: '业务域' },
          { prop: 'modelCount', name: '是否资产表打标', slot: 'modelCount' },
          { prop: 'metaCount', name: '列打标数量 / 列总量' },
          // {
          //   prop: 'count',
          //   name: '行打标数量 / 数据总量',
          // },
          { prop: 'updateTime', name: '最后修改时间' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          keyword: '',
          time: '',
        },
        startTime: '',
        endTime: '',
        allTableData: [],
        treeData: [],
        treeAttrData: {
          isHideSearch: false,
          showCheckbox: false,
          showControl: true,
          showLeftIcon: true,
          parentControl: '',
          childControl: '',
        },

        projectCode: '',

        bizId: null,
        bizName: null,
      })

      const methods = {
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 151 - 50
        },
        //数据打标
        markingTag(editor) {
          router.push({
            name: 'addMarkingTask',
            query: {
              modelName: editor.row.name,
              modelId: editor.row.id,
              projectCode: editor.row.projectCode,
              bizName: editor.row.bizName,
            },
          })
        },
        //查看任务
        checkTask(editor) {
          router.push({
            name: 'checkMarkingTask',
            query: {
              modelName: editor.row.name,
              modelId: editor.row.id,
              projectCode: editor.row.projectCode,
              bizName: editor.row.bizName,
              type: 'DETAIL',
            },
          })
        },
        // 编辑任务
        editTask(editor) {
          router.push({ name: 'editMarkingTask', query: { type: 'EDIT', taskId: editor.row.id } })
        },
        //删除任务
        deleteTask(editor) {
          let { row } = editor
          proxy.$MessageBoxService.open({
            title: '是否确认删除该任务',
            content: '删除后该任务将不可恢复',
            save: () => {
              api.assets.deleteTagging({ id: row.id }).then((res) => {
                let { success } = res
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '删除任务成功',
                    type: 'success',
                  })
                  if (state.tableData.list?.length <= 1) {
                    state.pagination.currentPage =
                      state.pagination.currentPage > 1 ? state.pagination.currentPage - 1 : 1
                  }
                  methods.initTable(false)
                }
              })
            },
          })
        },

        // 初始化form
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              // startTime: state.startTime || null,
              // endTime: state.endTime || null,
              bizId: state.bizId,
              bizName: state.bizName || null,
              modelName: state.filterSearch.keyword || null,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.assets
            .getTaggingList(data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, {
                  number: index + 1,
                })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 获取左侧树数据
        getTreeData() {
          sceneManage.searchTreeList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                // state.treeData = [
                //   {
                //     description: '全部',
                //     id: null,
                //     name: '全部',
                //     children: data,
                //   },
                // ]
                state.treeData = data
                // data.forEach((item) => {
                //   item.type = 'ROOT'
                // })
                // state.treeData = data
              }
            }
          })
        },
        // 树新增编辑
        treeUpdateNode(item) {
          let { ruleForm, checkItem, isEdit } = item
          let _message = '新增分类成功'
          let _interfaceName = 'createTagsTreeGroup'
          let _data = {}

          if (isEdit) {
            // 编辑
            _message = '编辑分类成功'
            _interfaceName = 'updateTagsTreeGroup'
            _data = {
              description: ruleForm.desc,
              id: checkItem.id,
              level: checkItem.level,
              name: ruleForm.name,
              pid: checkItem.pid,
            }
          } else {
            // 新增
            _data = {
              description: ruleForm.desc,
              level: checkItem.level + 1,
              name: ruleForm.name,
              pid: checkItem.id,
            }
          }
          api.assets[_interfaceName](_data).then((res) => {
            let { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: _message,
                type: 'success',
              })
              methods.getTreeData()
            }
          })
        },
        // 树删除
        treeDelNode(item) {
          if (item.children && item.children.length) {
            ElNotification({
              title: '提示',
              message: '该节点含有子节点不可删除',
              type: 'warning',
            })
          } else {
            proxy.$MessageBoxService.open({
              title: '是否确认删除该分类',
              content: '删除后该分类将不可恢复',
              save: () => {
                api.assets.deleteTagsTreeGroup({ id: item.id }).then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除分类成功',
                      type: 'success',
                    })
                    methods.getTreeData()
                  }
                })
              },
            })
          }
        },

        // 选中人员执行
        treeCheckNode(data) {
          if (!data) return
          let { checkItem } = data
          state.bizId = checkItem.name === '全部' ? null : checkItem.id
          state.bizName = checkItem.name || null

          state.filterSearch.keyword = null
          state.filterSearch.time = null
          methods.onSearch()
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 搜索
        onSearch() {
          if (state.filterSearch.time) {
            if (state.filterSearch.time[0]) {
              state.startTime = formartTime(state.filterSearch.time[0])
            }
            if (state.filterSearch.time[1]) {
              state.endTime = formartTime(state.filterSearch.time[1], true)
            }
          } else {
            state.startTime = null
            state.endTime = null
          }
          methods.initTable()
        },
        init() {
          methods.getTreeData()
          methods.initTable()
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.init()
      })

      return {
        state,
        buttonAuthList,

        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .marking-tag-task-list {
    &-box {
      display: flex;
      flex-wrap: nowrap;
      background-color: #fff;
      height: 100%;
      overflow: hidden;
      border-radius: 4px;
    }
    .tag-left {
      height: calc(100% + 15px);
    }
    .tag-right {
      flex: 1;
      min-width: 0; //解决左侧收起后 右侧数据不复原问题
      padding: 0 16px;
      overflow: hidden;
      :deep(.nancalui-table) {
        .total-row {
          color: $themeBlue;
          padding-right: 5px;
          &:nth-of-type(2) {
            color: #333;
            padding: 0 5px;
          }
        }
      }
      .tag-box {
        flex: 1;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        padding: 0 20px;
        .statistics-list {
          display: flex;
          align-content: center;
          width: 25%;

          img {
            width: 106px;
            height: 106px;
          }
          &-des {
            display: flex;
            align-items: center;
          }
          &-des-box {
            color: #333;
            font-size: 12px;
            &.can-click {
              cursor: pointer;
            }
            .top {
              display: flex;
              align-items: center;
              line-height: 20px;
            }
            .number {
              color: #333333;
              font-size: 24px;
              font-weight: bolder;
              padding: 0 5px;
            }
            .unit {
              color: #666;
            }
            .yy-icon {
              width: 20px;
              color: #c9c9cb;
            }
            &.active {
              .yy-icon {
                color: #0f94d0;
              }
            }
          }
        }
        span {
          color: #25bfff;
        }
      }
    }
    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
    }
    .commonDialog {
      .content {
        padding-top: 20px;
      }
    }
  }
</style>
