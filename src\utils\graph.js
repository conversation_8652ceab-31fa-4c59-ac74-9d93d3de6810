// 设置一对多，一对一，多对一箭头样式
export function setLineArrows(lineType, keyUse, data) {
  // 多
  const arrowsImageBase64M =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAaCAYAAAC6nQw6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJnSURBVEhLtZM/aBNRHMffe5cGHBRR2yh1ECmCoyWYyTpoF5VIEYJVRKlJIEFQ65ZEOU0i6qaSVpJoKUKHLIYUIwjWtJm0gw4uTq5GURQhNHcxP3+/y2sTe3fNOfhZ7vf98u7H78977L8CALxQKChSOsLy8NfmYObT58b4iSOlYqVyE6S9IUJ+14gkcvcAWBTrOlPT809UFUxnrDAdEsA7VQKc/6Lns9SqdGwxtbZcLb30HfJvx3580houv37nWa7OP5faEssZ4U8vDo74d2LobTvMS8nJl9qE7WbeLpXKWMluDIdJU4W+kZNbqGLS67EdJOccBvqCYQxmpcVa0JqMJPJ3pPyLnkOkrdW0/FPaorTwL5GaTgevS2XQMxFBl7Py/uccMAhIC3/kN6Zuh5JS2rfWTSAQ+D3gHjqL7T6TFs4MbkVinTZ5JJ6bl3FPsArOoOXFwXukhV2y4lQqPMYjsayjJ9ANXs/vHNg2KWk+jxy1th4BbLMMiabiFjPcbp1W4Bvsx97GMdgkrTpTXMemkxOLjrZGRBO5/azFFnHI/aRxXrU+IQ7fT138SNpRa1E1P4QP91VXkm8uNz+6moTomeiKOrOH6bCA+95FGq/AD0Xhow/U4AfjgGTD1q7GZwdXQFvC6extO/yXUJTRTHLiTVt3sK3ocjrnaYC2sJaEs7pLYcetkhCWia6pczv0OqOZ7DMMzleEIvwPk6GqoS0wtYYz2drQmzgTOGAYnGuCs7FMKlQ2tA2mijRNL64mwQfRFAxO90pCmBIJptzFj45JWlyIc5l0eO2h/jOX4o9PRWPZC1I6gLE/RT/ZKFcViFYAAAAASUVORK5CYII='
  const arrowsImageBase64MLight =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAsCAYAAAAATWqyAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAuIwAALiMBeKU/dgAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAALbSURBVFiFzZhNSFRRFMd/582Yo0/MgoJIYXCmFrlo1bJF0KKNrcr8GskgI6QgSMqdtKloFyFokabOSOOuTbRoE9GqIAIXoWMK9kGL8mM+TH1zWuhMz1EibLwz/9W75/Lu+b37zr33nAvFKI3i0SieQvi23I3kSvBG0gm8XhoOBE2DSOZhcSR4xLL0HeADFlC67JZYvymQ7IxYQsM6BEAlQl88Eng6H63eawJE3I3kaPCMKn2gbuffsLhgN8aeGwNZgwnUoDxROOEyK8LD8orUNan/kjQCAqCKpCK1V1XkLlDq6hpXtVorWibeGwHJaDESrLMgDHrUZV5S1R57Yuqe9JA2AgKgA35fyuftUdUuXMEtyktUzpe3Ts4aAckoHq49KSKDwEGXeV5VOytapsLGQAB+DvirSko9vQJNOaOMLS85HXvap+eMgGSUDAfPqmg/UOUyz1hqtZW1TLwyBgKQGvX7HfUMCRx3mdOqPLBLfF3SML5sBATWDsnESu11EbkF7HJ1vXWcdGtl6NNHIyAZJUZqj6klIwKHXeaUqHaXNU/dF0GNgABotLossVJ6R4QrOeO+wFltt0MzX42AZBSPBE4JPAYOuMzfEbloN00+MwYCsDgU2G959RFI/QZnynCZY1+Wtg8JIyAZxcOBNhHtBbFd5ikRQuVNsTd/BVlblnIoXzAiUqMqNwX+jKmaRqzBcu9khzTgAHhzX3TUahTkdr5A0C2+VsQSNETC3wnTDuTkrCaVy7dpRkSYQ5nOo8MSgX24NzxVQD5jT69k/ebL4Vb6r2DNh4pi+W53Q9sUI9tVdoungFt8wQ+9okgDiiIxKniquJPJ8z+vmr+WE80GyomiKLBMlpxb/ppsEY4WrggvimuJQl7UZIMvEQ72qOqYG0Ihuur9VbfTEBtA0koUWFpvLqBcqmiOndvdMPtjpyEg59fEI8FuET3tWSXkC8UmTQBsqUJe+BaNfgOdJ3tSAAvPpgAAAABJRU5ErkJggg=='
  // 多且必需
  const arrowsImageBase64MM =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAaCAYAAAC6nQw6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGVSURBVEhLrZUxS8NAFMffu9hKQWjRQREXh4L6FcRZKOisg4NIB0sturi0hYLFxUVo1SFCB3cHBzs66AfQwdnZRRxcbJJ73jXX07QXQlJ/cMn93/DLveO4wH+B6q05bHRy543dTxWNlBudJd5znlUEa24hy9S8T7l+vdpz3LdS1d5WJSMIxAhgcjBmPt5Ri6TE86hLRDkiuImSDaNF8iuIpFolK65Mi1onxUeLYQERvvxKPFlgj8aRBUSSpLIRkSSJbOQc/eWgbq95nO6FZMqvoCfkO1bKenF77qtfA5hNpzLGFQ0IW5k4Jpt+/gXLNXuROBypbAZpngNuAFF6UBBS3Y1cEfoHkT+pWiIiW4sDVmqdvENuVWUzRFnxXBcj089D9FtT81BKNXsZODwQ0KwqAWPsinO+r2J0a0YJ4DGbYG0VNaGiMMnFafFMxQBGUVyJZESURCIJiJJKJFo0jkSiRQxYXkimVYwlkWhRu7l3hwy3xNSJK5EE9uiyWbzFNFuJK5FEnmwT4k+DlVZX3QTiuqkUvn8AgTzOOdrdi70AAAAASUVORK5CYII='
  // 多且必需
  const arrowsImageBase64MMLight =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAANCAYAAAB7AEQGAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAELSURBVChTdZGxTgJBEIb/vQMh0GiBhGhjqzWh19jLA/ACFrS8Ap0lBVErrGzsTEyo0NhqQjyNIRaaXIGnhBDuvNsbd4cFQ06+ZLMz+3/ZnWQFKaAhCQibS0ZOETpnsAtlWLqP3q7gXx+BAo9zDUVKejyBdHszCRQjHr0g6NaWxDkspXaqWKs0lfj6rzi7SZEQf75NokaVXw7R1DWtmte9Q/R8DpFZB/ke0nvHEMF9g6LBpVGSsCS9PtHkwxwB8ecDwqc2RDqnnhyzZFkbu7C3D3lpQucUIr+FzP4F95rF4PL9BsFtHSJXQvago/aiSYyUFEoczmGJ/OFKQfP3d9IH7CyXjP67fgvWZgW/VpZ6QGTwdcMAAAAASUVORK5CYII='
  // 一且必需
  const arrowsImageBase64OM =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAaCAYAAAC+aNwHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACtSURBVDhPY2QgAeS2zRP984uBF8RmYmP+OLUq/i0TWIZI8O/r306GX3/ugvC/L78aQWIkGYANjBowagAIsGRVzV75n/G/EJSPF/z//18LyoQDJgZGBjuG/wwuxOD/DAxSUH1wQLkXGBgYmxkZGXmgfPzg//+Q/wz/TaE8MCApO2dXz5737///RAiPcer0ttSc0YQ0agAIkGTAP0bGV8AEdA+EGZkY3kCFKQEMDAD9qjrG+HXSlQAAAABJRU5ErkJggg=='
  // 一且必需
  const arrowsImageBase64OMLight =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAANCAYAAACUwi84AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACSSURBVChTY/wPBAxo4N/HWwx/7qxkYFGNYmCCiqGA/58fMfy5tZDh/5fH2BUgA8oVMP55sOn//39/oFwI+PfuKtANCxjY7ecwMH5dpfOf4c93qBQqACv4+/rcf4b/f6FCEPDv9VmGXxe7wQoYQOGADv483v3/6zLl/3+e7v9PD2+C7ISy4QAUxH+f7mVglnFhAABFxVeQGTXvzgAAAABJRU5ErkJggg=='

  let sourceMarker = null
  let targetMarker = null

  if (lineType === '1') {
    // 一对多
    sourceMarker = {
      tagName: 'block',
      size: 1,
    }
    targetMarker = {
      tagName: 'image',
      // https://www.iconfinder.com/icons/15539/arrow_left_previos_icon
      'xlink:href':
        keyUse === 'true'
          ? data.bloodType
            ? arrowsImageBase64MMLight
            : arrowsImageBase64MM
          : data.bloodType
          ? arrowsImageBase64MLight
          : arrowsImageBase64M,
      width: 16,
      height: 16,
      x: -8,
      y: -8,
    }
  } else if (lineType === '2') {
    // 多对多
    sourceMarker = {
      tagName: 'image',
      // https://www.iconfinder.com/icons/15539/arrow_left_previos_icon
      'xlink:href': data.bloodType ? arrowsImageBase64MLight : arrowsImageBase64M,
      width: 16,
      height: 16,
      x: -8,
      y: -8,
    }
    targetMarker = {
      tagName: 'image',
      // https://www.iconfinder.com/icons/15539/arrow_left_previos_icon
      'xlink:href': data.bloodType ? arrowsImageBase64MLight : arrowsImageBase64M,
      width: 16,
      height: 16,
      x: -8,
      y: -8,
    }
  } else if (lineType === '3') {
    // 一对一
    sourceMarker = {
      tagName: 'block',
      size: 1,
    }
    if (keyUse === 'true') {
      targetMarker = {
        tagName: 'image',
        // https://www.iconfinder.com/icons/15539/arrow_left_previos_icon
        'xlink:href': data.bloodType ? arrowsImageBase64OMLight : arrowsImageBase64OM,
        width: 16,
        height: 16,
        x: -8,
        y: -8,
      }
    } else {
      targetMarker = {
        tagName: 'block',
        size: 1,
      }
    }
  }
  return {
    sourceMarker,
    targetMarker,
  }
}
//设置线样式：1.实线，2.虚线 3.虚-实，4.实-虚
export function setLineAttr(type, lineType, keyUse, data) {
  const LineArrows = setLineArrows(lineType, keyUse, data)
  const res = { markup: null, attrs: null }
  if (type === 1) {
    res.attrs = {
      line: {
        fill: 'none',
        stroke: data?.bloodType ? '#f5a623' : '#697A9A',
        strokeWidth: 2,
        ...LineArrows,
      },
    }
  } else if (type === 2) {
    res.attrs = {
      line: {
        stroke: data?.bloodType ? '#f5a623' : '#697A9A',
        strokeWidth: 2,
        fill: 'none',
        strokeDasharray: '5 5',
        ...LineArrows,
      },
    }
  } else {
    res.markup = [
      {
        tagName: 'path',
        selector: 'wrap',
        groupSelector: 'lines',
        attrs: {
          cursor: 'pointer',
          fill: 'none',
          stroke: 'transparent',
          strokeLinecap: 'round',
        },
      },
      {
        tagName: 'path',
        selector: 'line',
        groupSelector: 'lines',
      },
      {
        tagName: 'path',
        selector: 'line1',
        groupSelector: 'lines',
      },
    ]
    if (type == 3) {
      res.attrs = {
        line: {
          strokeWidth: 2,
          fill: 'none',
          stroke: {
            type: 'linearGradient',
            stops: [
              { offset: '0%', color: 'transparent' },
              { offset: '49%', color: 'transparent' },
              { offset: '49.01%', color: data?.bloodType ? '#f5a623' : '#697A9A' },
              { offset: '100%', color: data?.bloodType ? '#f5a623' : '#697A9A' },
            ],
          },
          ...LineArrows,
        },
        line1: {
          fill: 'none',
          strokeWidth: 2,
          strokeDasharray: '5 5',
          stroke: data?.bloodType ? '#f5a623' : '#697A9A',
        },
        wrap: { strokeWidth: 10 },
      }
    } else {
      res.attrs = {
        line: {
          fill: 'none',
          strokeWidth: 2,

          stroke: {
            type: 'linearGradient',
            stops: [
              { offset: '0%', color: data?.bloodType ? '#f5a623' : '#697A9A' },
              { offset: '49%', color: data?.bloodType ? '#f5a623' : '#697A9A' },
              { offset: '49.01%', color: 'transparent' },
              { offset: '100%', color: 'transparent' },
            ],
          },
          ...LineArrows,
        },
        line1: {
          fill: 'none',
          strokeWidth: 2,
          strokeDasharray: '5 5',
          stroke: data?.bloodType ? '#f5a623' : '#697A9A',
        },
        wrap: { strokeWidth: 10 },
      }
    }
  }
  return res
}
// 边设置路径点
export function setEdgeVertices(edge, isFirst) {
  // 源节点连接桩位置
  const { cell: sc, port: sport } = edge.getSource()
  // 目标节点连接桩位置
  const { cell: tc, port: tport } = edge.getTarget()
  const [sp] = sport.split('-')
  const [tp] = tport.split('-')
  // 源边点x,y坐标
  const { x: spx, y: spy } = edge.getSourcePoint()
  // 目标边点x,y坐标
  const { x: tpx, y: tpy } = edge.getTargetPoint()
  // 获取路径点
  const vertices = edge.getVertices()
  // 如果是平行的,清除路由和路径点直接返回
  if (spx === tpx || spy === tpy) {
    edge.removeRouter()
    for (let i = 0; i < vertices.length; i++) {
      edge.removeVertexAt(0)
    }
    return
  }
  // 如果距离太短设置manhattan路由
  if (Math.abs(tpx - spx) < 60 || Math.abs(tpy - spy) < 70) {
    for (let i = 0; i < vertices.length; i++) {
      edge.removeVertexAt(0)
    }
    edge.setRouter({ name: 'manhattan', args: { step: 10, padding: 10 } })
    return
  }
  // 如果是自关联线，设置曼哈顿路由
  if (sc === tc) {
    edge.setRouter({ name: 'manhattan', args: { step: 10, padding: 10 } })
    return
  }
  //如果已经设置过路径点，直接返回
  if (vertices?.length > 0 && isFirst) {
    return
  }
  // 清除上一次的路由
  edge.removeRouter()
  for (let i = 0; i < vertices.length; i++) {
    edge.removeVertexAt(0)
  }
  const sourceNode = edge.getSourceNode()
  const targetNode = edge.getTargetNode()
  // 源节点x,y坐标
  const { x: sx, y: sy } = sourceNode.getPosition()
  // 目标节点x,y坐标
  const { x: tx, y: ty } = targetNode.getPosition()
  // 源节点宽度、高度
  const { width: sw, height: sh } = sourceNode.size()
  // 目标节点宽度、高度
  const { width: tw, height: th } = targetNode.size()
  // 如果不是垂直或者水平，则手动设置路径点（路径点位置为连接桩位置，防止影响路由绘制路径)
  // 上下排列设置
  if (sp === 'bottom') {
    if (tp === 'top') {
      // 第二个路径点的x轴
      let cx = 0
      if (ty - (sy + sh) > 40) {
        cx = tpx
      } else {
        cx = Math.abs(tx - sx - sw) > 30 ? tx - 30 : sx - 30
      }
      // 最后一个路径点的y轴
      let ly = 0
      if (ty - (sy + sh) > 40 && ty - (sy + sh) < 60) {
        ly = sy + sh + 30
      } else {
        ly = ty - 30
      }
      edge.setVertices([
        {
          x: spx,
          y: sy + sh + 30,
        },
        {
          x: cx,
          y: sy + sh + 30,
        },
        {
          x: cx,
          y: ly,
        },
        {
          x: tpx,
          y: ly,
        },
      ])
    } else if (tp === 'bottom') {
      // 第二个路径点的x轴
      let cx = 0
      if (ty - sy < 0) {
        cx = tpx
      } else {
        cx = tx - sx > 30 ? tx - 30 : sx - 30
      }
      edge.setVertices([
        {
          x: spx,
          y: sy + sh + 30,
        },
        {
          x: cx,
          y: sy + sh + 30,
        },
        {
          x: cx,
          y: ty + th + 30,
        },
        {
          x: tpx,
          y: ty + th + 30,
        },
      ])
    } else if (tp === 'left') {
      edge.setVertices([
        {
          x: spx,
          y: sy + sh + 30,
        },
        {
          x: tx - 30,
          y: sy + sh + 30,
        },
        {
          x: tx - 30,
          y: tpy,
        },
      ])
    } else if (tp === 'right') {
      edge.setVertices([
        {
          x: spx,
          y: sy + sh + 30,
        },
        {
          x: tx + tw + 30,
          y: sy + sh + 30,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
      ])
    }
  } else if (sp === 'top') {
    if (tp === 'top') {
      // 第二个路径点的x轴
      let cx = 0
      if (ty - sy > 0) {
        cx = tpx
      } else {
        cx = tx - sx > 30 ? tx - 30 : sx - 30
      }
      edge.setVertices([
        {
          x: spx,
          y: sy - 30,
        },
        {
          x: cx,
          y: sy - 30,
        },
        {
          x: cx,
          y: ty - 30,
        },
        {
          x: tpx,
          y: ty - 30,
        },
      ])
    } else if (tp === 'bottom') {
      // 第二个路径点的x轴
      let cx = 0
      if (ty + th - sy < -40) {
        cx = tpx
      } else {
        cx = tx - sx > 30 ? tx - 30 : sx - 30
      }
      // 最后一个路径点的y轴
      let ly = 0
      if (sy - (ty + th) > 40 && sy - (ty + th) < 60) {
        ly = sy - 30
      } else {
        ly = ty + th + 30
      }
      edge.setVertices([
        {
          x: spx,
          y: sy - 30,
        },
        {
          x: cx,
          y: sy - 30,
        },
        {
          x: cx,
          y: ly,
        },
        {
          x: tpx,
          y: ly,
        },
      ])
    } else if (tp === 'left') {
      edge.setVertices([
        {
          x: spx,
          y: sy - 30,
        },
        {
          x: tx - 30,
          y: sy - 30,
        },
        {
          x: tx - 30,
          y: tpy,
        },
        {
          x: tx - 30,
          y: tpy,
        },
      ])
    } else if (tp === 'right') {
      edge.setVertices([
        {
          x: spx,
          y: sy - 30,
        },
        {
          x: tx + tw + 30,
          y: sy - 30,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
      ])
    }
  } else if (sp === 'left') {
    if (tp === 'bottom') {
      edge.setVertices([
        {
          x: sx - 30,
          y: spy,
        },
        {
          x: sx - 30,
          y: ty + th + 30,
        },
        {
          x: tpx,
          y: ty + th + 30,
        },
        {
          x: tpx,
          y: ty + th + 30,
        },
      ])
    } else if (tp === 'top') {
      edge.setVertices([
        {
          x: sx - 30,
          y: spy,
        },
        {
          x: sx - 30,
          y: ty - 30,
        },
        {
          x: tpx,
          y: ty - 30,
        },
        {
          x: tpx,
          y: ty - 30,
        },
      ])
    } else if (tp === 'left') {
      // 第一个路径点的x轴
      let fx = 0
      if (tx - sx > 0) {
        fx = sx - 30
      } else {
        fx = tx - 30
      }
      edge.setVertices([
        {
          x: fx,
          y: spy,
        },
        {
          x: fx,
          y: tpy,
        },
        {
          x: tx - 30,
          y: tpy,
        },
        {
          x: tx - 30,
          y: tpy,
        },
      ])
    } else if (tp === 'right') {
      // 第二个路径点的y轴
      let cy = 0
      if (tx + tw - sx > -40) {
        cy = ty - 30
      } else {
        cy = spy
      }
      // 最后一个路径点的x轴
      let lx = 0
      if (sx - (tx + tw) > 30 && sx - (tx + tw) < 60) {
        lx = sx - 30
      } else {
        lx = tx + tw + 30
      }
      edge.setVertices([
        {
          x: sx - 30,
          y: spy,
        },
        {
          x: sx - 30,
          y: cy,
        },
        {
          x: lx,
          y: cy,
        },
        {
          x: lx,
          y: tpy,
        },
      ])
    }
  } else if (sp === 'right') {
    if (tp === 'top') {
      // 第二个路径点的x轴
      let cx = 0
      if (ty - spy > 30) {
        cx = tpx
      } else {
        cx = tx - sx > 60 ? tx + tw + 30 : sx + sw + 30
      }
      edge.setVertices([
        {
          x: sx + sw + 30,
          y: spy,
        },
        {
          x: cx,
          y: spy,
        },
        {
          x: cx,
          y: ty - 30,
        },
        {
          x: tpx,
          y: ty - 30,
        },
      ])
    } else if (tp === 'bottom') {
      edge.setVertices([
        {
          x: sx + sw + 30,
          y: spy,
        },
        {
          x: sx + sw + 30,
          y: ty + th + 30,
        },
        {
          x: tpx,
          y: ty + th + 30,
        },
        {
          x: tpx,
          y: ty + th + 30,
        },
      ])
    } else if (tp === 'left') {
      // 第二个路径点的y轴
      let cy = 0
      if (tx - sx - sw > 40) {
        cy = tpy
      } else {
        cy = ty - 30
      }
      // 最后一个路径点的x轴
      let lx = 0
      if (tx - (sx + sw) > 40 && tx - (sx + sw) < 60) {
        lx = sx + sw + 30
      } else {
        lx = tx - 30
      }
      edge.setVertices([
        {
          x: sx + sw + 30,
          y: spy,
        },
        {
          x: sx + sw + 30,
          y: cy,
        },
        {
          x: lx,
          y: cy,
        },
        {
          x: lx,
          y: tpy,
        },
      ])
    } else if (tp === 'right') {
      // 第一个路径点的x轴
      let fx = 0
      if (tx - sx < 0) {
        fx = sx + sw + 30
      } else {
        fx = tx + tw + 30
      }
      edge.setVertices([
        {
          x: fx,
          y: spy,
        },
        {
          x: fx,
          y: tpy,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
        {
          x: tx + tw + 30,
          y: tpy,
        },
      ])
    }
  }
  // 如果最后两个点相同，并且和第二个点平行，删除最后两个点，防止拖线时出现重复路径
  const curVertices = edge.getVertices()
  if (
    curVertices[1]?.y === curVertices[2]?.y &&
    curVertices[2]?.x === curVertices[3]?.x &&
    curVertices[2]?.y === curVertices[3]?.y
  ) {
    // 和目标点平行 删除两个点
    if (curVertices[3]?.y === ty) {
      edge.removeVertexAt(2)
      edge.removeVertexAt(2)
    } else {
      // 否则删除一个点
      edge.removeVertexAt(2)
    }
    //  如果最后两个点相同，并且和第二个点垂直，删除最后一个点，防止拖线时出现重复路径
  } else if (
    curVertices[1]?.x === curVertices[2]?.x &&
    curVertices[2]?.x === curVertices[3]?.x &&
    curVertices[2]?.y === curVertices[3]?.y
  ) {
    edge.removeVertexAt(2)
  }
}
