<template>
  <!-- 结构化数据采集  -->
  <div class="data-collection">
    <div
      :class="{
        'data-collection-add': true,
        container: true,
        isLzos: state.isLzos,
      }"
    >
      <div class="add-box">
        <div class="page-title"
          >新增采集任务<div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
        </div>

        <div class="box-content scroll-bar-style">
          <div class="box-content-inside">
            <div class="inside-box">
              <div class="inside-box-content">
                <div class="content-title">
                  <span>基础信息</span>
                </div>
                <n-form
                  v-if="state.basicTitle"
                  ref="ruleForm"
                  class="base-form"
                  :data="state.ruleForm"
                  :rules="state.rules"
                  label-width="150px"
                  label-align="start"
                >
                  <div class="inline">
                    <div class="inline-left">
                      <n-form-item label="采集任务名称：" field="name">
                        <n-input
                          v-model="state.ruleForm.name"
                          placeholder="请输入采集任务名称"
                          maxlength="500"
                          @clear="collectNameClear"
                        />
                      </n-form-item>
                    </div>
                    <n-form-item label="描述信息：">
                      <n-textarea
                        v-model="state.ruleForm.description"
                        placeholder="请输入描述信息"
                        maxlength="200"
                        :autosize="{ minRows: 3 }"
                        resize="both"
                        show-count
                      />
                    </n-form-item>
                  </div>
                  <div class="inside-box-content">
                    <div class="content-title">
                      <span>选择表</span>
                    </div>
                    <div class="top-line">
                      <n-form-item field="dataSourceId" label="数据源：">
                        <n-select
                          v-model="state.ruleForm.dataSourceId"
                          placeholder="请选择"
                          filter
                          allow-clear
                          :options="state.dataSourceOptions"
                          @value-change="dataSourceChange"
                        />
                      </n-form-item>
                      <n-form-item
                        class="dataSourceTable"
                        field="dataSourceTable"
                        label="数据源表："
                      >
                        <div class="top-line-tag">
                          <el-select
                            v-model="state.ruleForm.dataSourceTable"
                            clearable
                            filterable
                            remote
                            multiple
                            reserve-keyword
                            placeholder="请选择"
                            :remote-method="filterTable"
                            :loading="state.tableLoading"
                            @change="checkFromFn('dataSourceTable')"
                            @focus="blurFn(false)"
                            @blur="blurFn(true)"
                          >
                            <el-option
                              v-for="item in state.dataSourceTableOptions"
                              :key="item.name"
                              :label="item.name"
                              :value="item.name"
                            />
                          </el-select>
                        </div>
                      </n-form-item>
                    </div>
                    <div class="content-title">
                      <span>表信息</span>
                    </div>
                    <div class="top-line">
                      <n-form-item field="confidentialityLevel" label="表密级：">
                        <n-select
                          v-model="state.ruleForm.confidentialityLevel"
                          placeholder="请选择"
                          filter
                          allow-clear
                          :options="state.confidentialityLevelOptions"
                        />
                      </n-form-item>
                      <n-form-item field="tagList" label="标签：">
                        <div class="top-line-tag">
                          <el-tree-select
                            ref="selectTree"
                            v-model="state.ruleForm.tagList"
                            :data="state.targetOptions"
                            style="width: 100%"
                            node-key="key"
                            :props="{
                              label: 'name',
                              value: 'key',
                              children: 'children',
                            }"
                            multiple
                            show-checkbox
                            :render-after-expand="false"
                            filterable
                            clearable
                          />
                        </div>
                      </n-form-item>
                    </div>
                  </div>
                </n-form>
              </div>
            </div>
            <!-- 配置信息 -->
            <div class="config-box inside-box">
              <div class="inside-box-content footer">
                <div class="content-title">
                  <span>调度信息</span>
                </div>
                <configureSchedulingRules
                  v-if="state.dispatchInfo"
                  ref="configureSchedulingRulesDom"
                  :dataSourceType="state.ruleForm.dataSourceType"
                  :showTaskRule="state.showTaskRule"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{
        'box-operate': true,
        isLzos: state.isLzos,
      }"
    >
      <n-button size="sm" @click.prevent="cancel">取消</n-button>
      <n-button
        class="save"
        :loading="state.loading"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="save"
        >确定</n-button
      >
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElNotification } from 'element-plus'
  import { checkCName500 } from '@/utils/validate'
  import { collectJobMoreAdd } from '@/api/dataManage'
  import configureSchedulingRules from './components/configure-scheduling-rules'

  export default {
    name: '',
    components: {
      configureSchedulingRules,
    },
    props: {},
    setup() {
      const router = useRouter()
      const configureSchedulingRulesDom = ref()
      const ruleForm = ref()
      const structureDom = ref()
      const selectTree = ref(null)
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        collected: false,
        tableLoading: false,
        basicTitle: true,
        selectFile: true,
        dispatchInfo: true,
        sourceCategoryId: null,
        ruleForm: {
          name: '',
          description: '',
          collectType: 'STRUCTURE',
          dataSourceType: '',
          envType: 'OFFICIAL',
          dataSourceName: '',
          database: '',
          dataSourceId: '', //绑定的数据源id
          dataSourceTable: [], //数据源id下表名
          sinkModelCreateMethod: 'REVERSE',
          confidentialityLevel: '',
          overlayOrNot: true,
          cnName: '', // 模型中文名
          tagList: [], // 标签
        },
        rules: {
          name: [
            {
              required: true,
              validator: (...args) =>
                checkCName500(...args, null, null, {
                  name: state.ruleForm.name,
                  id: null,
                }),
              trigger: 'blur',
            },
          ],
          envType: [{ required: true, message: '请选择任务环境', trigger: 'change' }],
          collectType: [{ required: true, message: '请选择采集类型', trigger: 'change' }],
          dataSourceId: {
            required: true,
            type: 'number',
            message: '请选择数据源',
            trigger: 'change',
          },
          dataSourceTable: {
            required: true,
            type: 'array',
            message: '请选择数据源表',
            trigger: 'change',
          },
          sinkModelCreateMethod: [{ required: true, message: '请选择目标表', trigger: 'change' }],
          confidentialityLevel: { required: true, message: '请选择表密级', trigger: 'change' },
        },

        disabled: false, //非创建状态下不调用逆向模型接口
        loading: false,
        isBlur: true,
        allData: {},
        editId: null,
        sinkModelCreateMethod: 'REVERSE', //建模方式
        collectTaskNameOnly: false, //重名校验是否通过
        showTaskRule: true,
        userName: '',
        datasourceType: '',
        dataSourceTableOptions: [],
        dataSourceOptions: [],
        targetOptions: [],
        confidentialityLevelOptions: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ],
        colorList: [
          { value: '#FA5924', bgColor: '#FFF2E8', checked: true },
          { value: '#ED4EA5', bgColor: '#FFF0F6', checked: false },
          { value: '#8D54DA', bgColor: '#F9F0FF', checked: false },
          { value: '#41A4FF', bgColor: '#E6F7FF', checked: false },
          { value: '#37CCCB', bgColor: '#E6FFFB', checked: false },
        ],
      })

      const methods = {
        expandTitleFn(name) {
          state[name] = !state[name]
        },
        closeFn() {
          router.push({
            name: 'dataCollectionIndex',
            query: {},
          })
        },
        // 检测表单
        checkFromFn(name) {
          ruleForm.value.validateFields([name])
          if (state.ruleForm.dataSourceTable.length > 0) {
            methods.checkMoreExists()
          }
        },
        blurFn(flag) {
          state.isBlur = flag
        },
        //采集任务同名校验
        collectNameCheck() {
          // 名称唯一
          return new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              if (valid) {
                let _data = {
                  name: state.ruleForm.name,
                  id: state.editId || null,
                }
                state.collectTaskNameOnly = false
                if (state.ruleForm.name) {
                  api.dataManagement.collectTaskNameOnly(_data).then((res) => {
                    let { success } = res
                    if (success) {
                      state.collectTaskNameOnly = success
                    }

                    resolve()
                  })
                }
              } else {
                return false
              }
            })
          })
        },
        //清空采集任务名
        collectNameClear() {
          state.collectTaskNameOnly = false
        },
        // 获取标签列表
        getTargetList() {
          api.documentManage.getTagLibraryClassListHasTag().then((res) => {
            let { success, data } = res
            if (success) {
              state.targetOptions = data
            }
          })
        },
        // 获取数据源-列表
        getDatasourceList() {
          state.ruleForm.dataSourceTable = []
          api.dataManagement.getDatasourceFeignList({ includeNonInvertible: false }).then((res) => {
            let { data, success } = res
            if (success) {
              data.length
                ? data.forEach((item) => {
                    item.value = item.id
                  })
                : ''
              state.dataSourceOptions = data
            }
          })
        },
        //  数据源下拉层-change
        dataSourceChange(data) {
          if (data) {
            let _data = state.dataSourceOptions.filter((item) => {
              return item.id === data.value
            })
            state.ruleForm.dataSourceName = _data[0].name
            state.ruleForm.database = _data[0].database
            state.userName = _data[0].userName
            state.datasourceType = _data[0].datasourceType
            state.ruleForm.confidentialityLevel = _data[0].confidentialityLevel
            state.ruleForm.dataSourceTable = []
            methods.getSourceTables({
              condition: {
                dataSourceId: state.ruleForm.dataSourceId,
                name: '',
              },
              pageNum: 1,
              pageSize: 100,
            })
          } else {
            state.ruleForm.dataSourceTable = []
            state.dataSourceTableOptions = []
          }
        },
        filterTable(query) {
          state.tableLoading = true
          methods.getSourceTables({
            condition: {
              dataSourceId: state.ruleForm.dataSourceId,
              name: query,
            },
            pageNum: 1,
            pageSize: 100,
          })
        },
        // 判断该表是否已采集
        checkMoreExists() {
          api.dataManagement
            .checkMoreExists({ tableNames: state.ruleForm.dataSourceTable.toString() })
            .then((res) => {
              if (res.success) {
                state.collected = res.data.collected
                if (res.data.collected) {
                  ElMessage({
                    message: res.data.tableNames.toString() + '已采集',
                    type: 'warning',
                    duration: 3 * 1000,
                  })
                }
              }
            })
        },
        // 根据数据源下拉层获取表数据
        async getSourceTables(data) {
          if (state.isBlur) {
            state.tableLoading = false
            return false
          }
          await api.dataManagement
            .getSourceTablesPageV2(data)
            .then((res) => {
              state.tableLoading = false
              let { data, success } = res
              if (success) {
                let _dataSourceTableOptions = []
                data.list.forEach((item) => {
                  _dataSourceTableOptions.push({
                    name: item.name,
                    value: item.name,
                    cName: item.comment || '',
                  })
                })
                state.dataSourceTableOptions = _dataSourceTableOptions
              }
            })
            .catch(() => {
              state.tableLoading = false
            })
        },

        // 采集任务保存
        async save() {
          if (state.collected) {
            ElNotification({
              title: '提示',
              message: '该表已采集',
              type: 'warning',
            })
            return false
          }
          // let passed = false
          let _result = await new Promise((resolve) => {
            ruleForm.value.validate((valid) => {
              resolve({ passed: valid })
            })
          })
          if (!_result.passed) return
          let scheduling_result = await configureSchedulingRulesDom.value.getAllData()
          if (!scheduling_result.passed) return
          let mergeAllData = { ...scheduling_result.data, ...state.ruleForm }
          state.loading = true
          let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
          //创建任务
          let newData = {
            dataTag:
              activeOptions?.map((val) => {
                return { text: val.name, color: val.color, id: val.id || null }
              }) || [],
            description: mergeAllData.description || null,
            name: mergeAllData.name || null,
            overrides: true,
            schedulingConfigInfo: {
              collectWay: mergeAllData.collectRule,
              cron: mergeAllData.schedule?.cron || null,
              effectiveFromTime: mergeAllData.schedule?.fromDateTime || null,
              effectiveThruTime: mergeAllData.schedule?.thruDateTime || null,
              periodicDeclarationMode: mergeAllData.schedule?.cron
                ? 'CRON_EXPRESSION'
                : 'APPOINTED_TIME',
              reRun: mergeAllData.retryCount > 0 ? true : false,
              reRunInterval: mergeAllData.retryTime || null,
              reRunTimes: mergeAllData.retryCount || null,
              schedulingStrategy: mergeAllData.schedule ? 'AUTO' : 'MANUAL',
              timeUnit: mergeAllData.schedule?.period || null,
              timePoint: mergeAllData.schedule?.rateTime || null,
              appointedTime: mergeAllData.schedule?.period || null,
              timeInterval: mergeAllData.schedule?.extent || null,
            },
            securityLevel: mergeAllData.confidentialityLevel || null,
            sourceCategoryId: state.sourceCategoryId || null,
            sourceId: mergeAllData.dataSourceId || null,
          }
          if (mergeAllData.schedule?.period == 'WEEK' || mergeAllData.schedule?.period == 'MONTH') {
            newData.schedulingConfigInfo.timeInterval =
              mergeAllData.schedule?.extent?.toString() || null
          }
          let promiseArr = []
          mergeAllData.dataSourceTable.forEach((val) => {
            promiseArr.push(methods.getFieldFn(val, { ...newData }))
          })
          Promise.all(promiseArr).then((resp) => {
            collectJobMoreAdd(resp)
              .then((res) => {
                let { success } = res
                state.loading = false
                if (success) {
                  ElNotification({
                    title: '提示',
                    message: '新增成功',
                    type: 'success',
                  })
                  sessionStorage.setItem('refreshListKey', 'dataCollectionRefresh')
                  router.push({ name: 'dataCollectionIndex' })
                }
              })
              .catch(() => {
                state.loading = false
              })
          })
        },
        getFieldFn(name, obj) {
          return new Promise((resolve) => {
            let sourceTableItem = state.dataSourceTableOptions.filter((val) => val.name === name)[0]
            let destinationEntry = ''
            if (state.datasourceType === 'ORACLE') {
              destinationEntry = 'ODS_' + name.replace(/\./g, '_')
            } else if (state.datasourceType === 'API') {
              destinationEntry = 'ODS_api_' + name.replace(/\./g, '_')
            } else {
              destinationEntry =
                'ODS_' +
                state.ruleForm.database +
                '_' +
                state.userName +
                '_' +
                name.replace(/\./g, '_')
            }
            let collectJob = {
              ...obj,
              destinationEntry: destinationEntry,
              destinationEntryName: sourceTableItem.comment || sourceTableItem.name,
              destinationId: null,
              sourceEntry: name,
              sourceId: state.ruleForm.dataSourceId,
              sourceName: state.ruleForm.dataSourceName,
              sourceType: state.datasourceType,
            }
            let mappingList = []
            api.dataManagement
              .getSourceStructure({
                id: state.ruleForm.dataSourceId,
                tableName: name,
              })
              .then((res) => {
                if (res.success) {
                  res.data.forEach((val, ind) => {
                    let targetCode = val.colName.substring(val.colName.lastIndexOf('.') + 1)
                    mappingList.push({
                      sourceCode: val.colName,
                      sourceDataLength: val.length,
                      sourceDataType: val.dataType,
                      sourceName: val.comment || null,
                      sourceOrderNum: ind,
                      targetCode: targetCode.toLowerCase(),
                      targetDataLength: val.length,
                      targetDataType: val.convertFileType.toUpperCase(),
                      targetName: val.comment || null,
                      targetOrderNum: ind,
                    })
                  })
                  collectJob.mappingList = mappingList
                  resolve(collectJob)
                } else {
                  resolve(collectJob)
                }
              })
              .catch(() => {
                resolve(collectJob)
              })
          })
        },

        // 取消
        cancel() {
          router.go(-1)
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId || null
        state.sourceCategoryId = router.currentRoute.value.query.sourceCategoryId || null
        methods.getTargetList()
        methods.getDatasourceList()
      })

      return {
        state,
        ruleForm,
        structureDom,
        configureSchedulingRulesDom,
        selectTree,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection {
    height: 100%;
  }
  .data-collection-add {
    box-sizing: border-box;
    height: calc(100% - 64px);
    padding: 12px;
    &.isLzos {
      height: calc(100% - 52px);
      padding: 0;
    }
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 16px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 64px);
        overflow: auto;
        border-radius: 2px 2px 0 0;
        .inside-box {
          &-content {
            background-color: #fff;
            border-radius: 2px;
            &.footer {
              margin-top: 10px;
              border-radius: 2px 2px 0 0;
            }
            &:first-of-type {
              padding-top: 16px;
            }
          }
          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 16px;
            padding-left: 14px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .top-line {
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          box-sizing: border-box;
          width: 100%;
          padding: 0 16px;
          :deep(.dataSourceTable:has(.error-message)) {
            .el-select {
              .el-select__wrapper {
                box-shadow: 0 0 0 1px #f63838 inset;
              }
            }
          }
          :deep(.el-select) {
            width: 100%;
          }
          &-tag {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
            max-width: 430px;

            //:deep(.el-select) {
            //  width: calc(100% - 110px);
            //  margin-right: 4px;
            //
            //}

            .nancalui-button {
              padding: 0 8px;

              .button-content {
                display: flex;
                align-items: center;
                justify-content: center;

                .icon {
                  margin-right: 4px;
                  font-size: 16px;
                }
              }
            }
          }
          .nancalui-form__item--horizontal {
            flex: 1;
          }
          .check-style {
            width: 100%;

            .nancalui-select {
              max-width: 328px;
            }
          }

          .nancalui-radio-group.is-row {
            display: flex;
            align-items: center;
            width: 100%;
            .nancalui-form__item--horizontal {
              margin-bottom: 0;
            }
            .nancalui-radio__wrapper:not(:last-child) {
              padding-right: 8px;
            }
            .nancalui-radio__wrapper:last-child {
              margin-left: 16px;
            }
          }
        }
        .base-form {
          .inline {
            display: flex;
            align-items: flex-start;
            padding: 0 16px;
            &-left,
            .nancalui-form__item--horizontal {
              flex: 1;
            }
          }

          .nancalui-form__item--horizontal {
            margin-bottom: 16px;
          }
          .nancalui-input,
          .nancalui-select {
            max-width: 430px;
          }

          .nancalui-textarea__div {
            max-width: 434px !important;
          }
          .check-style {
            margin-top: -6px;
            margin-bottom: 10px;
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .box-operate {
    position: absolute;
    right: 12px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    width: calc(100% - 24px);
    height: 64px;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 0 0 2px 2px;
    :deep(.nancalui-button) {
      border-radius: 2px;
    }
    &.isLzos {
      right: 0;
      bottom: 0;
      width: 100%;
    }
  }
</style>
