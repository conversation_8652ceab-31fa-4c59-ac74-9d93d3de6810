<template>
  <div class="container">
    <div class="cf-page-title">
      <div class="nc-line-1 title">{{ state.name }}</div>
      <!--      <n-button variant="solid" @click="state.tagPopup = true">标注结果</n-button>-->
      <n-button variant="solid" @click="closeFn">退出预览</n-button>
    </div>
    <!-- 预览 -->
    <div
      class="preview-content-box"
      id="preview-pint"
      ref="previewPintRef"
      v-loading="state.loading"
      element-loading-text="Loading..."
    >
      <FilePreview
        :option="state.option"
        :type="state.type"
        @pageChange="pageChange"
        isDrawDisabled
        isDragDisabled
        :data="state.annotationsPage"
        @click-polygon="(id) => tagRefs[id].showTag()"
      />
    </div>
    <template v-for="(item, key) in tagList" :key="key">
      <Tag
        :ref="(ref) => (tagRefs[item.id] = ref)"
        :tagOptions="state.tagOptions"
        :x="item.x"
        :y="item.y"
        :initialTags="item.tags"
        mode="preview"
      />
    </template>

    <!-- 标注结果 -->
    <div class="tagPopup" v-show="state.tagPopup">
      <div class="tagPopup-title">
        <span>标注结果</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          @click="state.tagPopup = false"
        >
          <path
            d="M4.5 19.4998L19.4995 4.50023"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M4.5 4.50024L19.4995 19.4998"
            stroke="#8091B7"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="tagPopup-btn">
        <!-- 增加段落 -->
        <!-- <n-button color="primary" @click="addParagraph">增加段落</n-button> -->
        <!-- 导出结果 -->
        <n-button color="primary" @click="exportResult">导出结果</n-button>
      </div>
      <div class="tagPopup-content">
        <!-- 标注段落 -->
        <div
          v-for="(item, i) in state.annotationsParagraph"
          :class="['tagPopup-paragraph', item.active && 'active']"
          @click="activeParagraph(item.id)"
          :key="i"
        >
          <!-- 标注段落的内容 -->
          <!-- 标题 -->
          <div class="tagPopup-paragraph-title"> 段落{{ i + 1 }}： </div>
          <!-- 内容 -->
          <div class="tagPopup-paragraph-content">
            <div
              class="tagPopup-content-item"
              v-for="(item, key) in paragraphLinks(item.links)"
              :key="key"
            >
              <div class="tag-content">
                <h2>内容：</h2>
                <div class="tag-content-text"> {{ item.selectedText }} </div>
              </div>
              <div class="tag-result">
                <h2>标签：</h2>
                <div
                  class="tag"
                  :style="getTagStyle(tag.color)"
                  v-for="(tag, tagKey) in item.tags"
                  :key="tagKey"
                >
                  {{ tag.text }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { throttle, isEmpty } from 'lodash-es'
  import { useRouter } from 'vue-router'
  import Tag from '@/views/document-management/document-annotation/tag.vue'
  import FilePreview from '@/views/document-management/document-annotation/components/FilePreview.vue'
  import * as d3 from 'd3'
  import api from '@/api/index'
  import { uuid } from '@/utils/index' // 工具函数
  const emit = defineEmits(['close'])
  const router = useRouter()
  const previewPintRef = ref(null)
  const tagRefs = {}
  const props = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
  const state = reactive({
    name: '文档预览',
    option: {},
    type: '',
    loading: false,
    tagPopup: false,
    tagOptions: [],
    annotations: {},
    // 分页标注data
    annotationsPage: {},
    currentPage: 1,
    tagPopupData: [], // 标签弹窗数据
    tagPopCount: 0,
    annotationsParagraph: [], // 标注段落
  })
  const pageChange = (page) => {
    setTimeout(() => {
      state.currentPage = page
      renderWaveLine()
    })
  }
  function getDocUrl(id) {
    api.documentManage.outsideGet({ id: id }).then((res) => {
      if (res.success) {
        const { name, type, docUrl } = res.data
        state.name = name
        state.type = type
        state.option = {
          value: docUrl,
          name: name,
        }
      }
    })
  }
  // 获取预览数据
  function getPreviewData(id) {
    api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
      if (res.success) {
        try {
          const { mark, paragraph } = res.data
          state.annotationsPage = JSON.parse(mark || '{}')
          state.tagPopupData = Object.values(state.annotationsPage[1] || {})
          state.annotationsParagraph = JSON.parse(paragraph || '[]')
          if (state.annotationsParagraph?.length === 0) addParagraph()
          switch (true) {
            case ['pdf', 'word'].includes(state.type):
              renderWaveLine()
              break
            case state.type.includes('image'):
              break
          }
        } catch (error) {
          state.annotationsPage = {}
        }
      }
    })
  }
  function closeFn() {
    emit('close')
  }
  onMounted(() => {
    // getDocUrl(props.info.id)
    state.name = props.info.name
    state.type = 'excel'
    state.option = {
      value: props.info.detailsUrl,
      name: props.info.name,
    }
  })

  // 获取标签列表
  const getTargetList = () => {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        state.tagOptions = data
      }
    })
  }
  // getTargetList()
  const tagList = computed(() => {
    // state.annotations为空时展示当前页的标注
    if (isEmpty(state.annotations || {})) {
      return state.annotationsPage[state.currentPage] || {}
    }
    const newAnnotations = {
      ...(state.annotationsPage[state.currentPage] || {}),
      ...(state.annotations || {}),
    }
    return newAnnotations
  })

  // 计算属性：通过id获取段落关联的标注
  const paragraphLinks = computed(() => (links) => {
    const tagPopupData = []
    Object.values(state.annotationsPage || {})
      .flatMap((item) => Object.values(item || {}))
      .map((item) => {
        links.find((link) => {
          if (item.id === link) {
            tagPopupData.push(item)
          }
        })
      })
    return tagPopupData
  })
  // 渲染本页的波浪线
  function renderWaveLine() {
    // 清除之前的data-temp为true的div
    document.querySelectorAll('[data-temp="true"]').forEach((el) => el.remove())
    // 清除所有对话框
    document.querySelectorAll('.wave-popup').forEach((el) => el.remove())
    // 重置计数器
    state.tagPopCount = 0
    // 查询
    // 渲染标记完成的波浪线
    Object.values(state.annotationsPage[state.currentPage] || {}).forEach((item) => {
      if (item.sortedRects.length) {
        const { top, left, width, height } = item.boundingRect
        // 清除重复的
        document.querySelectorAll(`[data-annotation-id="${item.id}"]`).forEach((el) => el.remove())
        item.sortedRects.forEach((rect) => {
          addWaveLine({
            dataTemp: false,
            offsetY: rect.height,
            width: rect.width,
            top: rect.top - top,
            left: rect.left - left,
            annotationId: item.id,
          })

          const showWidth = rect.left - left - 200 + rect.width

          // 只在有标签数据时才增加计数
          if (item.tags?.length) {
            state.tagPopCount++
          }

          addWaveLineShow({
            dataTemp: false,
            offsetY: rect.height,
            width: showWidth,
            height: 1,
            top: rect.top - top,
            left: 200,
            annotationId: item.id,
          })
        })
      }
    })
  }
  // 添加波浪线
  function addWaveLine(
    {
      dataTemp = true,
      offsetY = 0,
      top = 0,
      left = 0,
      width = 2000,
      height = 10,
      wavelength = 20,
      lineWidth = 1,
      annotationId,
    } = {
      dataTemp: true,
      offsetY: 0,
      top: 0,
      left: 0,
      width: 2000,
      height: 10,
      wavelength: 50,
      lineWidth: 1,
      annotationId,
    },
  ) {
    const amplitude = height * 0.5 // 振幅
    const centerY = height / 2 // 中心Y坐标

    const svg = d3
      .select('#wave-container')
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr(
        'style',
        `position: absolute;top: ${top + offsetY}px;left:${left}px;color: #f00;z-index: 100000;`,
      )
      .attr('data-annotation-id', annotationId)
      .attr('data-temp', dataTemp)

    // 添加点击事件
    svg.on('click', function (event) {
      tagRefs[annotationId]?.showTag()
    })

    const waveData = []
    for (let x = 0; x < width; x++) {
      const y = (amplitude - lineWidth * 2) * Math.sin((x / wavelength) * Math.PI * 2) + centerY
      waveData.push([x, y])
    }
    svg
      .append('path')
      .datum(waveData)
      .attr(
        'd',
        d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1])
          .curve(d3.curveBasis),
      )
      .attr('stroke', 'currentColor')
      .attr('fill', 'none')
  }

  // 添加标注展示直线
  function addWaveLineShow(
    {
      dataTemp = true,
      offsetY = 0,
      top = 0,
      left = 0,
      width = 2000,
      height = 10,
      wavelength = 20,
      lineWidth = 1,
      annotationId,
    } = {
      dataTemp: true,
      offsetY: 0,
      top: 0,
      left: 0,
      width: 2000,
      height: 10,
      wavelength: 50,
      lineWidth: 1,
      annotationId,
    },
  ) {
    const centerY = height / 2 // 中心Y坐标

    const svg = d3
      .select('#wave-container')
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .attr(
        'style',
        `position: absolute;top: ${top + offsetY}px;left:${left}px;color: #f00;z-index: 100000;`,
      )
      .attr('data-annotation-id', annotationId)
      .attr('data-temp', dataTemp)

    // 创建直线数据
    const lineData = [
      [0, centerY],
      [width, centerY],
    ]

    svg
      .append('path')
      .datum(lineData)
      .attr(
        'd',
        d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1]),
      )
      .attr('stroke', 'currentColor')
      .attr('fill', 'none')

    // 添加对话框
    const popupData = state.tagPopupData.find((item) => item.id === annotationId)
    if (popupData) {
      // 获取所有对话框并按行分组
      const existingPopups = Array.from(document.querySelectorAll('.wave-popup'))
      const popupGroups = {}

      // 按行分组
      existingPopups.forEach((popup) => {
        const top = parseInt(popup.style.top)
        const key = Math.round(top / 20) * 20 // 将相近的top值归为同一行

        if (!popupGroups[key]) {
          popupGroups[key] = []
        }
        popupGroups[key].push(popup)
      })

      // 计算当前对话框所在行
      const currentTop = top + offsetY - 10
      const currentRow = Math.round(currentTop / 20) * 20

      // 如果当前行已存在对话框，则添加到该行
      if (!popupGroups[currentRow]) {
        popupGroups[currentRow] = []
      }

      // 创建新对话框
      const popupDiv = document.createElement('div')
      popupDiv.className = 'wave-popup'
      popupDiv.setAttribute('data-selectedText', popupData.selectedText)
      popupDiv.style.cssText = `
        position: absolute;
        top: ${currentTop}px;
        left: 2px;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 4px;
        width: 200px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        z-index: 100001;
        overflow: visible;
        cursor: pointer;
      `

      // 添加内容
      const contentDiv = document.createElement('div')
      contentDiv.innerHTML = `
        <div style="display: flex; flex-wrap: wrap; gap: 4px;">
          ${popupData.tags
            .map(
              (tag) => `
            <span style="
              font-size: 12px;
              line-height: 24px;
              color: ${getTagStyle(tag.color).color};
              background: ${getTagStyle(tag.color).background};
              display: inline-block;
              padding: 0 8px;
              border: 1px solid ${getTagStyle(tag.color).borderColor};
              border-radius: 4px;
            ">${tag.text}</span>
          `,
            )
            .join('')}
        </div>
      `
      popupDiv.appendChild(contentDiv)
      document.getElementById('wave-container').appendChild(popupDiv)
      popupGroups[currentRow].push(popupDiv)

      // 添加鼠标悬停事件
      popupDiv.addEventListener('mouseenter', () => {
        // 高亮对应的波浪线
        const waveLines = document.querySelectorAll(`[data-annotation-id="${annotationId}"]`)
        waveLines.forEach((line) => {
          if (line.tagName === 'svg') {
            line.style.color = 'blue'
            line.style.transition = 'color 0.3s'
            line.style.zIndex = '100002'
          }
        })
      })

      popupDiv.addEventListener('mouseleave', () => {
        // 恢复波浪线颜色和层级
        const waveLines = document.querySelectorAll(`[data-annotation-id="${annotationId}"]`)
        waveLines.forEach((line) => {
          if (line.tagName === 'svg') {
            line.style.color = '#f00'
            line.style.transition = 'color 0.3s'
            line.style.zIndex = '100000'
          }
        })
      })

      // 重新计算所有对话框的位置
      if (state.tagPopCount === state.tagPopupData.length) {
        Object.entries(popupGroups).forEach(([rowTop, popups]) => {
          let offset = 0
          popups.forEach((popup) => {
            popup.style.top = `${parseInt(rowTop) + offset}px`
            offset += popup.offsetHeight + 2 // 加上2像素的间隔
          })
        })
      }
    }
  }

  // 获取标签样式
  function getTagStyle(tag) {
    const color = tag.split('_')
    return {
      color: color[0],
      background: color[1],
      borderColor: color[2],
    }
  }

  // 激活段落
  function activeParagraph(id) {
    state.annotationsParagraph.forEach((t) => {
      t.active = t.id === id
    })
  }
  function addParagraph() {
    // 数组长度
    const length = state.annotationsParagraph.length
    // 关联标注
    state.annotationsParagraph.push({ id: uuid(), text: '', active: length === 0, links: [] })
  }
  // 导出
  function exportResult() {
    // 通过文件流下载文件
    api.documentManage.exportDocumentAnnotationExcel(props.info.id).then((blob) => {
      // 下载文件
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '标注结果.xlsx'
      link.click()
      URL.revokeObjectURL(link.href)
    })
  }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .container {
    width: 100%;
    height: 100% !important;
    position: relative;
    overflow-y: auto;
    padding: 0 !important;
  }

  .preview-content-box {
    height: calc(100% - 56px);
    padding: 0;
    background: #fff;
    overflow-y: auto;
    box-sizing: border-box;
    .preview-content {
      height: auto !important;
    }
  }

  :deep(.img-content) {
    overflow-x: hidden;
    overflow-y: auto;

    .img-Preview {
      width: 100% !important;
      height: max-content;
      min-height: 100%;
      overflow-x: unset;
      overflow-y: unset;
    }
  }

  .title {
    width: calc(100% - 80px);
  }
  .tagPopup {
    position: absolute;
    top: 0;
    right: 0;
    width: 400px;
    height: 100%;
    background: #fff;
    z-index: 100000;
    border: 1px solid #ccc;
    &-title {
      position: relative;
      font-size: 16px;
      font-weight: bold;
      line-height: 52px;
      text-align: center;
      border-bottom: 1px solid #ccc;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;

      svg {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      ::before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }
    &-btn {
      padding: 8px 16px;
    }
    &-paragraph {
      padding: 10px;
      min-height: 300px;
      border: 3px dashed #ccc;
      border-radius: 4px;
      margin-bottom: 10px;
      padding: 10px;
      cursor: pointer;
      // 激活状态
      &.active {
        border-color: #1e89ff;
        // 动画
        animation: pulse 1s infinite;
        @keyframes pulse {
          0% {
            border-color: #1e89ff;
          }
          50% {
            border-color: #ccc;
          }
          100% {
            border-color: #1e89ff;
          }
        }
      }
    }
    &-content {
      height: calc(100% - 53px);
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;

      &-item {
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;
        background: #f5f5f5;

        h2 {
          font-size: 14px;
          font-weight: bold;
          line-height: 32px;
          margin: 0;
          border: none;
        }
        .tag-content-text {
          font-size: 14px;
          line-height: 32px;
          color: #333;
        }

        .tag-result .tag {
          font-size: 14px;
          line-height: 32px;
          color: #333;
          display: inline-block;
          padding: 0 14px;
          border: 1px solid transparent;
          border-radius: 4px;
          margin-right: 10px;
        }
      }
    }
  }
</style>
<style>
  #wave-container,
  #tag-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100000;
    width: 0;
    height: 0;
  }
</style>
