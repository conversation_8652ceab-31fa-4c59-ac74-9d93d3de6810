<template>
  <div :class="['container ', 'scroll-bar-style']">
    <div class="content-box">
      <div class="row">
        <div class="col">
          时间：
          <n-date-picker-pro v-model="state.today" placeholder="选择时间" format="YYYY-MM-DD" allow-clear />
        </div>

        <div class="search">
          <div class="btn active" @click.prevent="onSearch(true)">查询</div>
          <div class="btn" @click.prevent="resetFn">重置</div>
        </div>
      </div>
      <div class="content-box-row" v-loading="state.loading">
        <div class="col">
          <div class="col-content">
            <div class="col-content-row">
              <div class="col-content-row-item" @click="$router.push({
                name: 'collectionMonitorIndex',
                query: { time: state.today?.getTime() }
              })">
                <div class="col-content-row-item-title" unit-text=""> 任务总数 </div>
                <div class="col-content-row-item-content">
                  {{
                    isNaN(state.statistic?.todayRunTotal) ? '--' : state.statistic?.todayRunTotal
                  }}
                </div>
              </div>
              <div class="col-content-row-item">
                <div class="col-content-row-item-title"> 待运行</div>
                <div class="col-content-row-item-content">
                  {{
                    isNaN(state.statistic?.todayRunPendingTotal)
                      ? '--'
                      : state.statistic?.todayRunPendingTotal
                  }}
                </div>
              </div>
              <div class="col-content-row-item" @click="$router.push({
                name: 'collectionMonitorIndex',
                query: { time: state.today?.getTime(), type: '7' }
              })">
                <div class="col-content-row-item-title"> 成功</div>
                <div class="col-content-row-item-content">
                  {{
                    isNaN(state.statistic?.todayRunSuccessTotal)
                      ? '--'
                      : state.statistic?.todayRunSuccessTotal
                  }}
                </div>
              </div>
              <div class="col-content-row-item" @click="$router.push({
                name: 'collectionMonitorIndex',
                query: { time: state.today?.getTime(), type: '6' }
              })">
                <div class="col-content-row-item-title"> 失败</div>
                <div class="col-content-row-item-content">
                  {{
                    isNaN(state.statistic?.todayRunFailedTotal)
                      ? '--'
                      : state.statistic?.todayRunFailedTotal
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-box-row">
        <div class="col">
          <div class="title">
            任务实例监控 <span class="sub-title">近15天任务实例运行状态监控</span>
          </div>
          <div class="col-content">
            <div :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']" id="taskCollectionTrend"></div>
          </div>
        </div>
      </div>
      <div class="content-box-row">
        <div class="col">
          <div class="title"> 任务运行时长排行 </div>
          <div class="col-content">
            <div :class="['canvas', 'trend', state.isLzos ? 'isLzos' : '']" id="resourceUtilization"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ref, reactive, onMounted, toRefs, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import api from '@/api/index'
import { formartTime, formartTimeDate } from '@/utils/index'
import * as echarts from 'echarts'
import { timestampToTime } from '@/const/public.js'
export default {
  title: 'List',
  props: {},
  setup() {
    const store = useStore()
    //按钮权限
    const { buttonAuthList } = toRefs(store.state.user)
    const state = reactive({
      today: new Date(),
      key: 1,
      loading: false,
    })
    let myChart = null
    let myCircularPieChart = null
    // 环形饼图基础配置
    const monitoringEchartsOption = {
      Animation: true,
      title: {
        text: '',
      },
      tooltip: {
        trigger: 'axis',
        // axisPointer: {
        //   type: 'cross',
        //   label: {
        //     backgroundColor: '#6a7985',
        //   },
        // },
        textStyle: {
          fontSize: 12,
          color: '#333',
        },
        formatter(params, ticket, callback) {
          let str = ''
          params.forEach((item) => {
            str += item.name + '：' + item.value + 's<br />'
          })
          return str
        },
      },
      color: ['#447DFD', '#F63838'], // 设置tooltips展示图标颜色
      toolbox: {},
      grid: {
        left: 20,
        right: 50,
        bottom: 0,
        top: 20,
        containLabel: true,
      },
      yAxis: [
        {
          type: 'value',
        },
      ],
      xAxis: [
        {
          // name: '任务数量（次）',
          // nameTextStyle: { align: 'left' },
          type: 'category',
          // boundaryGap: false,
        },
      ],
      series: [
        {
          name: '运行时长',
          type: 'bar',
          data: [],
        },
      ],
    }
    const methods = {
      onSearch() {
        state.loading = true
        // 获取可用数据源
        api.dataManagement
          .getCollectTaskDailyStatistics({
            projectCode: '',
            today: formartTimeDate(state.today, '-'),
          })
          .then((res) => {
            let { success, data } = res
            if (success) {
              state.statistic = data
            }
          })
          .then(() => {
            state.loading = false
          })
        methods.getTaskRunTimeTop5()
      },
      resetFn() {
        state.today = new Date()
        methods.onSearch()
      },
      totalCount() {
        // 获取可用数据源
        api.dataManagement
          .getCollectTaskDailyStatistics({
            projectCode: '',
            today: formartTimeDate(state.today, '-'),
          })
          .then((res) => {
            let { success, data } = res
            if (success) {
              state.statistic = data
            }
          })
        let timestamp = new Date().getTime() // 当前日期时间戳
        let endDate = timestampToTime(timestamp),
          startDate = timestampToTime(timestamp - 14 * 24 * 60 * 60 * 1000)

        let chartDom = document.getElementById('taskCollectionTrend')
        myChart = echarts.init(chartDom)

        myChart.showLoading({
          text: 'loading',
          color: '#c23531',
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.2)',
          zlevel: 0,
          // spinnerRadius: 20,
          // fontSize: 20,
        })

        // 获取任务实例监控
        api.dataManagement
          .collectJobInstance({ startDate: startDate, endDate: endDate })
          .then((res) => {
            let { success, data } = res
            myChart?.hideLoading()
            if (success) {
              methods.initEcharts(data)
            }
          })
          .catch(() => {
            myChart?.hideLoading()
          })
      },
      initEcharts(arr) {
        // chartDom.setAttribute('_echarts_instance_', '')
        if (!myChart) {
          let chartDom = document.getElementById('taskCollectionTrend')
          myChart = echarts.init(chartDom)
        }

        let xAxisData = [],
          yAxisSuccessData = [],
          yAxisFailData = []
        // 获取x轴数据
        xAxisData = arr.successList.map((item) => {
          item.day = item.date.slice(5, 10).replace(/-/g, '.')
          return item.day
        })
        // 获取y轴成功数据
        yAxisSuccessData = arr.successList.map((item) => item.count)
        // 获取y轴失败数据
        yAxisFailData = arr.failList.map((item) => item.count)
        let option
        option = {
          Animation: true,
          title: {
            text: '',
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              var showHtm = ''
              for (var i = 0; i < params.length; i++) {
                //日期
                var time = params[i].name
                //名称
                var name = params[i].seriesName
                //值
                var value = params[i].value
                var color = params[i].color
                if (i == 0) {
                  showHtm += `
                  <div style='color:rgba(0,0,0,0.45)'>${time}</div>
                  `
                }
                showHtm += `
                <div style='display:flex;justify-content: space-between;margin-top:5px'>
                  <div style='margin-right:10px'>
                    <span style='display:inline-block;margin-right:4px;margin-bottom:4px;border-radius:10px;width:10px;height:2px;background-color:${color};'></span>
                    <span style='font-weight:400'>${name}</span>
                  </div>
                  <span style='font-weight:500;'>${value}</span>
                </div>
                `
              }
              return showHtm
            },
            textStyle: {
              fontSize: 12,
              color: '#333',
            },
          },
          color: ['#5AD8A6', '#F63838'], // 设置tooltips展示图标颜色
          legend: {
            bottom: 0,
          },
          toolbox: {},
          grid: {
            left: 20,
            right: 50,
            bottom: 40,
            top: 30,
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: xAxisData,
            },
          ],
          yAxis: [
            {
              name: '任务数量（次）',
              nameTextStyle: { align: 'left' },
              type: 'value',
              offset: 10,
            },
          ],
          series: [
            {
              name: '成功实例',
              type: 'line',
              // stack: 'Total', //是否堆叠面积
              lineStyle: {
                color: '#5AD8A6',
              },
              // emphasis: {
              //   focus: 'series',
              // },
              smooth: true,
              symbol: 'circle',
              symbolSize: 7,
              data: yAxisSuccessData,
            },
            {
              name: '失败实例',
              type: 'line',
              // stack: 'Total',

              // emphasis: {
              //   focus: 'series',
              // },
              symbol: 'circle',
              symbolSize: 7,
              smooth: true,
              data: yAxisFailData,
              lineStyle: {
                color: '#F63838',
              },
            },
          ],
        }
        document.getElementById('taskCollectionTrend').setAttribute('_echarts_instance_', '')

        option && myChart.setOption(option)
      },

      initData() {
        window.onresize = () => {
          methods.echartsResize()
        }
        methods.totalCount()
      },
      //获取指定几天前的日期
      getSomeDayAgoTime(someDay = 1, split = '-') {
        let times = new Date().getTime() - someDay * 24 * 60 * 60 * 1000
        let _someDayAgoTime = new Date(times)

        let year = _someDayAgoTime.getFullYear()
        let month =
          _someDayAgoTime.getMonth() + 1 < 10
            ? '0' + (_someDayAgoTime.getMonth() + 1)
            : _someDayAgoTime.getMonth() + 1
        let day =
          _someDayAgoTime.getDate() < 10
            ? '0' + _someDayAgoTime.getDate()
            : _someDayAgoTime.getDate()

        return year + split + month + split + day
      },
      // 屏幕onresize echarts重新绘制
      echartsResize() {
        //echarts重新绘制
        if (myChart) {
          myChart.resize()
        }
      },
      // 获取采集任务每日运行耗时Top5
      getTaskRunTimeTop5() {
        if (!myCircularPieChart) {
          let chartDom = document.getElementById('resourceUtilization')
          chartDom.setAttribute('_echarts_instance_', '')
          myCircularPieChart = echarts.init(chartDom)
        }
        myCircularPieChart.showLoading({
          text: 'loading',
          color: '#c23531',
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.2)',
          zlevel: 0,
        })
        api.dataManagement
          .getCollectTaskDailyStatisticsTop5({
            projectCode: '',
            today: formartTimeDate(state.today, '-'),
          })
          .then(({ data, success }) => {
            if (success) {
              myCircularPieChart?.hideLoading()
              monitoringEchartsOption.xAxis[0].data = data?.map((_) => _.jobName)
              monitoringEchartsOption.series[0].data = data?.map((_) => _.jobRunTimeSeconds)
              monitoringEchartsOption && myCircularPieChart.setOption(monitoringEchartsOption)
            }
          })
          .finally(() => {
            myCircularPieChart?.hideLoading()
          })
      },
    }

    onBeforeUnmount(() => {
      window.onresize = null

      if (myChart) {
        myChart.dispose() //销毁
      }
    })

    onMounted(() => {
      const { projectCode } = toRefs(store.state.user.currentProject)
      methods.initData()
      methods.getTaskRunTimeTop5()
    })

    return {
      state,
      buttonAuthList,

      ...methods,
    }
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';
$labelWidth: 90px;

.container {
  width: 100%;
  height: calc(100vh - 50px);
  padding: 16px;
  overflow-y: auto;
  font-family: PingFangSC-Semibold, PingFang SC;
  background-color: #eee;

  .content-box {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &-row {
      .col {
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 8px -2px rgba(30, 47, 85, 0.15);
        overflow: hidden;

        .title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 52px;
          padding: 0 16px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          border-bottom: 1px solid #c5d0ea;

          .illustrate {
            margin-left: 8px;
            color: #8091b7;

            &:hover {
              color: $themeBlue;
            }
          }

          .sub-title {
            color: var(---, rgba(0, 0, 0, 0.55));

            font-family: 'Source Han Sans CN';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
          }

          &:before {
            position: absolute;
            top: 17px;
            left: 0;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }

          span {
            margin-left: 8px;
            color: rgba(0, 0, 0, 0.55);
            font-weight: normal;
            font-size: 14px;
          }
        }

        .col-content {
          padding: 16px;

          .canvas {
            display: flex;
            height: calc(50vh - 262px);
            flex-direction: column;
            align-items: center;
            gap: 16px;
          }

          .ranking {
            padding: 8px 24px;
            display: block;

            &-update-time {
              color: var(---, rgba(0, 0, 0, 0.55));

              font-family: 'Source Han Sans CN';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }

            &-list {
              height: calc(100% - 20px);
              display: flex;
              flex-direction: column;
              justify-content: space-around;

              &-item {
                display: grid;
                grid-template-columns: 20px 77px 1fr 34px;
                align-items: center;
                color: var(----, rgba(0, 0, 0, 0.75));
                font-family: 'Source Han Sans CN';
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;

                &-index {
                  display: flex;
                  width: 100%;
                  height: 16px;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  border-radius: 13px;

                  color: var(--100, #fff);
                  text-align: right;
                  font-family: 'Source Han Sans CN';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 20px;
                  margin-right: 4px;
                }

                &-name {
                  padding: 0 8px 0 4px;
                  color: var(--100, rgba(0, 0, 0, 0.75));
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                &-content {
                  display: flex;
                  align-items: center;
                  border-radius: 4px;
                  overflow: hidden;
                  // 动画
                  transition: all 0.3s ease-in-out;
                }

                &-count {
                  color: var(----, rgba(0, 0, 0, 0.75));
                  text-align: center;
                  font-family: 'PingFang SC';
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 16px;
                  /* 133.333% */
                }

                &:nth-child(1) {
                  color: #f63838;

                  .ranking-list-item-index {
                    background: #f63838;
                  }
                }

                &:nth-child(2) {
                  color: #ff7d00;

                  .ranking-list-item-index {
                    background: #ff7d00;
                  }
                }

                &:nth-child(3) {
                  color: #04c495;

                  .ranking-list-item-index {
                    background: #04c495;
                  }
                }

                &:nth-child(4) {
                  color: #447dfd;

                  .ranking-list-item-index {
                    background: #447dfd;
                  }
                }
              }
            }
          }

          &-row {
            display: flex;
            flex-wrap: wrap;

            &-item {
              cursor: pointer;
              display: flex;
              flex-direction: column;
              padding: 16px 24px;
              height: 96px;
              // height: 10.7vh;
              // align-items: center;
              flex: 1 0 0;
              border-radius: 6px;
              position: relative;

              &::before {
                position: absolute;
                display: block;
                margin: auto;
                bottom: 12px;
                right: 16px;
                width: 32px;
                height: 32px;
              }

              &:nth-child(1) {
                background: var(---, #1e89ff);

                &::before {
                  content: url('data:image/svg+xml;base64,CiAgICAgICAgICA8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiB2aWV3Qm94PSIwIDAgMzIgMzIiIGZpbGw9Im5vbmUiPgogIDxwYXRoIGQ9Ik0xNC45MjQ2IDQuMjQ0MTRIMTQuODc3N0M3Ljc3MTQ5IDQuMjQ0MTQgMi4wMDU4NiAxMC4wMDk4IDIuMDA1ODYgMTcuMTE2QzIuMDA1ODYgMjQuMjIyMyA3Ljc3MTQ5IDI5Ljk4NzkgMTQuODc3NyAyOS45ODc5QzIxLjk2ODQgMjkuOTg3OSAyNy43MzQgMjQuMjUzNSAyNy43NDk2IDE3LjE2MjlIMTQuOTI0NlY0LjI0NDE0WiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xNy4xNzc3IDIuMDA1ODZIMTcuMTMwOVYxNC44NDY1SDMwLjA0OTZDMzAuMDM0IDcuNzQwMjQgMjQuMjY4NCAyLjAwNTg2IDE3LjE3NzcgMi4wMDU4NloiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMjgiLz4KPC9zdmc+');
                }
              }

              &:nth-child(2) {
                background: var(---, #13bfff);

                &::before {
                  content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNSIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xNiA5VjE1LjgxOUMxNiAxNS45MzU5IDE2LjA0MSAxNi4wNDkyIDE2LjExNTkgMTYuMTM5MUwyMSAyMiIgc3Ryb2tlPSIjMTNCRkZGIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4=');
                }
              }

              &:nth-child(3) {
                background: var(---, #4cb15d);

                &::before {
                  content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNSIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xMCAxNkwxNCAyMEwyMyAxMyIgc3Ryb2tlPSIjNENCMTVEIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=');
                }
              }

              &:nth-child(4) {
                background: var(---, #f63838);

                &::before {
                  content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIiBmaWxsPSJub25lIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNCIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4yOCIvPgogIDxwYXRoIGQ9Ik0xMS4wNDk4IDExLjA0OThMMjAuOTQ5MyAyMC45NDkzIiBzdHJva2U9IiNGNjM4MzgiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CiAgPHBhdGggZD0iTTIwLjk1MDIgMTEuMDQ5OEwxMS4wNTA3IDIwLjk0OTMiIHN0cm9rZT0iI0Y2MzgzOCIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+');
                }
              }

              &-title {
                color: var(--100, #fff);

                /* 常用/r400/h8 */
                font-family: 'Source Han Sans CN';
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;

                /* 150% */
                &[unit-text]::after {
                  content: attr(unit-text);
                  color: var(--100, #fff);

                  font-family: 'Source Han Sans CN';
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 22px;
                }
              }

              &-content {
                color: var(--100, #fff);
                font-feature-settings: 'liga' off, 'clig' off;

                /* 数据/d1 */
                font-family: 'DIN Alternate';
                font-size: 32px;
                font-style: normal;
                font-weight: 700;
                line-height: 40px;
                /* 125% */
              }

              &-content-trend {
                display: flex;
                align-items: center;
                gap: 8px;
                align-self: stretch;
                color: var(--100, #fff);
                font-family: 'Source Han Sans CN';
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;

                &-item {
                  display: flex;
                  align-items: center;
                  gap: 2px;

                  &-content-icon {
                    vertical-align: text-bottom;
                  }
                }

                .yy-icon {
                  width: 14px;
                  height: 14px;
                }
              }

              .illustrate {
                position: absolute;
                top: 20px;
                right: 16px;
                margin-left: 8px;
                color: #8091b7;

                &:hover {
                  color: $themeBlue;
                }
              }
            }

            .col-content-row-item+.col-content-row-item {
              margin-left: 16px;
            }
          }
        }
      }
    }
  }

  .col-2 {
    display: grid;
    grid-template-columns: 66fr 34fr;
    gap: 16px;

    .col>.col-content {
      padding: 24px;
    }
  }

  .col-1 {
    .col>.col-content {
      padding: 0;
    }
  }
}

.refresh {
  margin-left: auto;
}

.icons {
  position: relative;

  &+.icons {
    margin-left: 8px;
  }

  .yy-icon {
    position: relative;
    cursor: pointer;
    color: #8091b7;
    z-index: 2;

    &:hover {
      color: $themeBlue;
    }
  }

  &:hover {
    &::before {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background: #e3ecff;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 5px;
    }
  }
}
</style>

<style lang="scss">
@import '@/styles/variables.scss';

.quality-tooltip {
  .quality-tooltip-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 4px;

    &-title {
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;
      text-align: center;
    }

    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      .circle {
        width: 8px;
        height: 8px;
        background-color: $themeBlue;
        border-radius: 50%;
      }

      .text {
        width: 72px;
        margin: 0 8px;
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }

      .num {
        width: calc(100% - 66px);
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        text-align: right;
      }
    }
  }
}

.task-collection-tooltip {
  .quality-tooltip-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 4px;

    &-title {
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;
      text-align: left;
    }

    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      .circle {
        width: 8px;
        height: 8px;
        background-color: $themeBlue;
        border-radius: 50%;
      }

      .text {
        width: 32px;
        margin: 0 8px;
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        text-align: left;
      }

      .num {
        width: calc(100% - 66px);
        color: rgba(0, 0, 0, 0.75);
        font-size: 14px;
        text-align: right;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.row {
  display: flex;
  width: 100%;
  padding: 10px 8px 10px 16px;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: 2px;
  background: var(--100, #fff);

  .switch-label {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #1e89ff;
    font-size: 14px;
    padding: 5px 14px;
    cursor: pointer;

    .icon-switch {
      font-size: 16px;
      margin-right: 4px;
    }

    &:hover {
      color: #479dff;
    }
  }

  .search {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
  }

  .btn {
    display: flex;
    padding: 4px 16px;
    align-items: center;
    gap: 4px;
    border-radius: 2px;
    border: 1px solid var(---, #1e89ff);
    color: #1e89ff;
    font-size: 14px;
    box-sizing: border-box;

    .icon {
      font-size: 16px;
    }

    &.active,
    &:hover {
      color: #fff;
      background: #1e89ff;
    }
  }
}
</style>
