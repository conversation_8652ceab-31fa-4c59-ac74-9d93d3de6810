<template>
  <!-- 日志管理-安全日志管理 -->
  <div class="operation-index-page container-padding16">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <n-input
            class="select-input"
            v-model="state.formInline.keyword"
            size="small"
            placeholder="请输入"
          >
            <template #prepend>
              <n-select
                v-model="state.formInline.keyType"
                size="sm"
                :options="state.typeList"
                @value-change="state.formInline.keyword = ''"
              />
            </template>
          </n-input>
          <n-range-date-picker-pro
            v-model="state.formInline.time"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            allow-clear
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="cf-page-title cf-no-bottom-margin nc-m-t-10"> 管理员日志 </div>
    <div class="list-box" v-loading="state.loading">
      <CfTable
        actionWidth="180"
        isNeedIndex
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableList,
          rowKey: 'id',
        }"
        :paginationConfig="{
          total: state.pagination.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,
          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            initTable(false)
          },
          onSizeChange: (v) => {
            state.pagination.pageSize = v
            initTable()
          },
        }"
      >
        <template #roleCode="{ row }">
          <div class="edit-box">
            {{ row.role === 1 ? '系统管理员' : row.role === 2 ? '安全保密员' : '安全审计员' }}</div
          >
        </template>
      </CfTable>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs } from 'vue'
  import moment from 'moment'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import ENUM from '@/const/enum'
  import { formartTimeDate } from '@/utils/index'
  export default {
    title: 'List',
    components: {},
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const state = reactive({
        key: 1,
        loading: false,
        formInline: {
          keyword: '',
          keyType: 'username',
          time: [],
        },
        searchData: {
          keyword: '',
          keyType: 'username',
          time: [],
        },
        typeList: [
          {
            name: '工号',
            value: 'username',
          },
          {
            name: '姓名',
            value: 'name',
          },
        ],
        tableHeadTitles: [
          { prop: 'username', name: '工号' },
          { prop: 'name', name: '姓名' },
          { prop: 'role', name: '账户类型', slot: 'roleCode' },
          { prop: 'title', name: '页面' },
          { prop: 'content', name: '功能' },
          { prop: 'remoteAddr', name: 'IP' },
          { prop: 'createTime', name: '访问时间' },
        ],
        pagination: {
          total: 1,
          currentPage: 1,
          pageSize: 10,
        },
        tableList: [],
      })

      const methods = {
        resetFn() {
          state.pagination = {
            currentPage: 1,
            pageSize: 10,
            total: 0,
          }
          state.formInline = {
            keyword: '',
            keyType: 'username',
            time: [],
          }
          state.searchData = {
            keyword: '',
            keyType: 'username',
            time: [],
          }
          methods.initTable()
        },
        startSearch() {
          Object.keys(state.formInline).forEach((key) => {
            state.searchData[key] = state.formInline[key]
          })
          methods.initTable()
        },
        // 初始化form
        initTable(init = true) {
          state.key++
          if (init) {
            state.pagination.currentPage = 1
          }
          let data = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              startTime: state.searchData.time?.[0]
                ? formartTimeDate(state.searchData.time?.[0], '-', true)
                : null,
              endTime: state.searchData.time?.[1]
                ? formartTimeDate(state.searchData.time?.[1], '-', true)
                : null,
            },
          }
          if (state.searchData.keyword) {
            data.condition[state.searchData.keyType] = state.searchData.keyword
          }
          state.loading = true
          api.base
            .gitLogsFindByPageNew(data)
            .then((res) => {
              // 新增序号属性
              state.loading = false
              state.tableList = res.data.list
              state.pagination.total = res.data.total
            })
            .catch(() => {
              state.tableList = []
              state.loading = false
            })
        },
        getTime(currentDate) {
          var year = currentDate.getFullYear()
          var month = currentDate.getMonth() + 1
          var day = currentDate.getDate()
          // 格式化日期和时间
          var formattedDate =
            year + '-' + methods.addLeadingZero(month) + '-' + methods.addLeadingZero(day)
          return formattedDate
        },
        addLeadingZero(number) {
          if (number < 10) {
            return '0' + number
          }
          return number
        },
      }
      onMounted(() => {
        var currentDate = new Date()
        currentDate.setMonth(currentDate.getMonth() - 1)
        state.formInline.time[1] = new Date()
        state.formInline.time[0] = new Date(currentDate)
        state.searchData.time[1] = new Date()
        state.searchData.time[0] = new Date(currentDate)
        methods.initTable()
      })

      return {
        state,
        buttonAuthList,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container-padding16 {
    .row {
      .nancalui-input {
        width: 300px !important;
      }
      :deep(.nancalui-input-slot__prepend) {
        width: 90px;
        padding: 0;
        .nancalui-select {
          margin-right: 0 !important;
          padding: 0 10px;
          border: none !important;
          .nancalui-select__selection:hover {
            box-shadow: unset;
          }
        }
      }
    }
    .template-con-flex {
      display: flex;
      width: 100%;
      height: calc(100vh - 180px);
      .left {
        min-width: 280px;
        background: #fff;
        border-radius: $cf-border-radius;
        :deep(.tree) {
          width: 100%;
          .resize,
          .knob {
            display: none !important;
          }
        }
      }
      .right {
        flex: 1;
        width: calc(100% - 290px);
        background: #fff;
        border-radius: $cf-border-radius;
        .table {
          height: calc(100% - 32px);
        }
      }
    }
  }
  .operation-index-page {
    overflow: hidden;
    .list-box {
      height: calc(100% - 62px - 46px);

      border-radius: 4px;
      background-color: #fff;

      .seeDetails {
        color: $themeBlue;
        padding: 0;
      }

      .commonForm-search {
        display: flex;
        justify-content: space-between;
        padding: 16px 0;
      }
    }
  }
  :deep(.select-input) {
    .nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled),
    .nancalui-input-slot__prepend {
      border: 1px solid #e5e6eb !important;
    }
    .nancalui-input-slot__prepend {
      border-right: 0 !important;
    }
  }
</style>
