<template>
  <div class="problem">
    <div class="row">
      <div class="switch">
        <div :class="state.tabs === 'task' ? 'switch-label checked' : 'switch-label'"
          @click.prevent.stop="checkTabsFn('task')">任务管理</div>
        <div :class="state.tabs === 'annotation' ? 'switch-label checked' : 'switch-label'"
          @click.prevent.stop="checkTabsFn('annotation')">标注任务</div>
        <div :class="state.tabs === 'examine' ? 'switch-label checked' : 'switch-label'"
          @click.prevent.stop="checkTabsFn('examine')">审核任务</div>
      </div>
    </div>
    <div v-loading="state.loading" class="table-box">
      <router-view v-slot="{ Component }">
        <component v-if="Component" :is="Component"></component>
        <task v-else-if="state.tabs === 'task'" />
        <annotationTask v-else-if="state.tabs === 'annotation'" />
        <examine v-else-if="state.tabs === 'examine'" />
      </router-view>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, defineProps } from 'vue'
import api from '@/api/index'
import task from '../../taskManagement/list'
import annotationTask from '../../annotationTask/index.vue'
import examine from '../../auditTask/index.vue'
import { workbenchDicCode } from '@/api/dataManage.js'
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps({
  info: {
    type: Object,
    default: {},
  },
})

const state = reactive({
  key: 1,
  loading: false,
  tabs: 'task',
})

// 跳转路由
const goJump = (name, query) => {
  if (query) {
    router.push({ name, query })
  } else {
    router.push({ name })
  }
}

const checkTabsFn = (type) => {
  state.tabs = type
}

onMounted(() => { })
</script>

<style lang="scss" scoped>
.problem {
  position: relative;
  width: 100%;
  height: 100%;

  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .switch {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;

      &-label {
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #c5d0ea;
        border-left: none;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        border-radius: 2px;

        &:first-of-type {
          border-left: 1px solid #c5d0ea;
        }

        &.checked {
          color: #479dff;
          font-weight: 500;
          border: 1px solid #479dff;
        }

        &.checked+.switch-label {
          border-left: none;
        }
      }
    }
  }

  .table-box {
    position: relative;
    margin-top: 10px;
    height: calc(100% - 52px);
  }
}
</style>
