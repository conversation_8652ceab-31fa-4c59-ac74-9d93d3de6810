<template>
  <div ref="dragRef" class="image-container" @mousedown="start($event)" @mouseup="stop">
    <div class="tools">
      <div class="node" @click="saveSign">
        <n-tooltip content="嵌入" position="top">
          <SvgIcon class="icon" icon="icon-pushpin" />
        </n-tooltip>
      </div>
      <div class="node" @click="delSign">
        <n-tooltip content="删除" position="top">
          <SvgIcon class="icon" icon="icon-delete-sign" />
        </n-tooltip>
      </div>
      <div class="node" ref="origin" @click="isOpen = true">
        <n-dropdown
          :visible="isOpen"
          close-scope="none"
          trigger="manually"
          :position="position"
          align="start"
        >
          <SvgIcon class="icon" icon="icon-app" />

          <template #menu>
            <div class="applyRange">
              <div class="title">应用范围</div>
              <n-radio v-model="baseChoose" value="全部" size="sm">全部</n-radio>
              <n-radio v-model="baseChoose" value="自定义" size="sm"
                >自定义范围<n-input v-model="customRange" autofocus />(共{{
                  props.total
                }}页)</n-radio
              >
              <p>文本: 示例：1、3、5-9</p>

              <div class="footer">
                <n-button variant="text" color="primary" size="mini" @click="isOpen = false"
                  >取消</n-button
                >
                <n-button variant="solid" size="mini" @click="isOpen = false">确定</n-button>
              </div>
            </div>
          </template>
        </n-dropdown>
      </div>
    </div>
    <img :src="imageSrc" draggable="false" />
    <div class="resize-corner top-left" @mousedown="startResize($event, 'topLeft')"></div>
    <div class="resize-corner top-right" @mousedown="startResize($event, 'topRight')"></div>
    <div class="resize-corner bottom-left" @mousedown="startResize($event, 'bottomLeft')"></div>
    <div class="resize-corner bottom-right" @mousedown="startResize($event, 'bottomRight')"></div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { saveSignSet, getSignSet } from '@/api/dataManage'
  import { ElMessage } from 'element-plus'
  const router = useRoute()

  const emits = defineEmits(['delSign', 'saveSign'])
  const props = defineProps({
    total: {
      type: Number,
      default() {
        return 0
      },
    },
    signInfo: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      required: true,
    },
    historySign: {
      type: Object,
      default() {
        return {}
      },
    },
  })

  const dragRef = ref(null)
  const position = ref(['top-start'])
  const isOpen = ref(false)
  const baseChoose = ref('全部')
  const customRange = ref('1')

  let imageSrc = ref('')

  let isDragging = ref(false)
  let isResizing = ref(false)
  let resizeDirection = ref('')

  let startX = ref(0)
  let startY = ref(0)
  let startWidth = ref(100)
  let startHeight = ref(100)
  let startLeft = ref(0)
  let startTop = ref(0)
  const signType = inject('signType')
  const currentSign = ref({})
  const currentStatus = ref('create')

  const initData = (type, info) => {
    currentStatus.value = type
    currentSign.value = info

    imageSrc.value = type === 'create' ? info.webUrl : info.waterFileOssUrl
  }

  // 删除签名
  const delSign = () => {
    emits('delSign')
  }

  // 嵌入签名
  const saveSign = async () => {
    let signType = ''
    let pageList =
      baseChoose.value === '全部' ? mapPage(props.total) : filterPage(customRange.value)
    let waterFileBucket =
      currentStatus.value === 'create'
        ? currentSign.value.bucketName
        : currentSign.value.waterFileBucket
    let waterFileObjname =
      currentStatus.value === 'create'
        ? currentSign.value.objName
        : currentSign.value.waterFileObjname
    switch (signType) {
      case '图片':
        signType = 'PICTURE'
        break
      case '输入':
        signType = 'INPUT'
        break
      default:
        signType = 'WRITE_BY_HAND'
        break
    }
    const w = dragRef.value.offsetWidth
    const h = dragRef.value.offsetHeight
    const x = dragRef.value.style.left
    const y = dragRef.value.style.top

    const params = {
      fileId: router.query.id,
      imgWidth: (w / 700) * 100, //图片宽度
      imgHeight: (h / 700) * 100, //图片高度
      pageList, //加水印的页码
      signType, //签名类型：PICTURE-图片、INPUT-输入、WRITE_BY_HAND-手写
      // signTypeName: '', //签名类型名
      waterFileBucket,
      waterFileObjname,
      waterFileOssUrl: imageSrc.value,
      x: (Number(x.split('px')[0]) / 700) * 100, //x坐标
      // y: (Number(y.split('px')[0]) / 587) * 100, //y坐标
      y: (Number(y.split('px')[0]) / 990) * 100, //y坐标
    }

    const res = await saveSignSet(params)
    if (res.code === 'SUCCESS') {
      emits('saveSign', {
        w,
        h,
        x,
        y,
        src: imageSrc.value,
      })
      ElMessage.success({
        message: '嵌入成功',
      })
    }
  }

  // 过滤页码
  const filterPage = (str) => {
    const parts = str.split('、')
    const result = []
    for (const part of parts) {
      if (part.includes('-')) {
        const range = part.split('-').map(Number)
        for (let i = range[0]; i <= range[1]; i++) {
          result.push(i)
        }
      } else {
        result.push(Number(part))
      }
    }
    return result
  }
  // 遍历页码
  const mapPage = (str) => {
    let list = []
    for (let i = 1; i <= Number(str); i++) {
      list.push(i)
    }
    return list
  }

  function start(event) {
    if (isNearImage(event)) {
      isDragging.value = true
    }

    startX.value = event.clientX
    startY.value = event.clientY
    dragRef.value = dragRef.value
    startLeft.value = dragRef.value.offsetLeft
    startTop.value = dragRef.value.offsetTop

    document.addEventListener('mousemove', move)
  }

  function stop() {
    isDragging.value = false
    isResizing.value = false
    document.removeEventListener('mousemove', move)
  }

  function move(event) {
    if (isDragging || isResizing) {
      requestAnimationFrame(() => {
        if (isDragging.value) {
          const dx = event.clientX - startX.value
          const dy = event.clientY - startY.value
          dragRef.value.style.left = startLeft.value + dx + 'px'
          dragRef.value.style.top = startTop.value + dy + 'px'
        } else if (isResizing.value) {
          const dw = event.clientX - startX.value
          const dh = event.clientY - startY.value

          if (resizeDirection.value === 'topLeft') {
            dragRef.value.style.width = startWidth.value - dw + 'px'
            dragRef.value.style.height = startHeight.value - dh + 'px'
            dragRef.value.style.left = startLeft.value + dw + 'px'
            dragRef.value.style.top = startTop.value + dh + 'px'
          } else if (resizeDirection.value === 'topRight') {
            dragRef.value.style.width = startWidth.value + dw + 'px'
            dragRef.value.style.height = startHeight.value - dh + 'px'
            dragRef.value.style.top = startTop.value + dh + 'px'
          } else if (resizeDirection.value === 'bottomLeft') {
            dragRef.value.style.width = startWidth.value - dw + 'px'
            dragRef.value.style.height = startHeight.value + dh + 'px'
            dragRef.value.style.left = startLeft.value + dw + 'px'
          } else if (resizeDirection.value === 'bottomRight') {
            dragRef.value.style.width = startWidth.value + dw + 'px'
            dragRef.value.style.height = startHeight.value + dh + 'px'
          }
        }
      })
    }
  }

  function startResize(event, direction) {
    isResizing.value = true
    resizeDirection.value = direction
    startX.value = event.clientX
    startY.value = event.clientY
    startLeft.value = dragRef.value.offsetLeft
    startTop.value = dragRef.value.offsetTop
    startWidth.value = dragRef.value.offsetWidth
    startHeight.value = dragRef.value.offsetHeight
  }

  function isNearImage(event) {
    const target = event.target
    const rect = target.getBoundingClientRect()
    const offsetX = event.clientX - rect.left
    const offsetY = event.clientY - rect.top

    return offsetX >= 0 && offsetX <= target.width && offsetY >= 0 && offsetY <= target.height
  }

  // 使用 defineExpose 暴露方法
  defineExpose({ initData })
</script>

<style lang="scss" scoped>
  .image-container {
    position: absolute;
    top: 50px;
    width: 100px;
    height: 100px;
    z-index: 3;
    border: 1px solid #96bdff;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      /* user-select: none; */
    }
  }

  .resize-corner {
    width: 8px;
    height: 8px;
    position: absolute;
    background-color: #447dfd;
    border-radius: 8px;
  }

  .top-left {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
  }

  .top-right {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
  }

  .bottom-left {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
  }

  .bottom-right {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
  }

  .tools {
    position: absolute;
    top: -32px;
    left: 0;
    display: flex;

    .node {
      width: 24px;
      height: 24px;
      box-sizing: border-box;
      border-radius: 6px;
      border: 1px solid #fff;
      background: var(--100, #fff);
      margin-right: 8px;
      text-align: center;
      border: 1px solid var(---, #c5d0ea);
      color: #447dfd;

      /* 中-卡片 */
      box-shadow: 0px 4px 16px -2px rgba(30, 47, 85, 0.15);

      &:hover {
        background: var(--unnamed, #e3ecff);
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .applyRange {
    width: 331px;
    background: #fff;
    border-radius: 4px;
    color: var(----, rgba(0, 0, 0, 0.75));
    font-size: 14px;
    padding: 16px;

    .title {
      font-size: 14px;
      color: var(----, rgba(0, 0, 0, 0.9));
      margin-bottom: 20px;
    }
    .nancalui-input {
      display: inline-block;
      width: 124px;
      margin: 0 8px;
    }
    .footer {
      text-align: right;
      margin-top: 8px;
    }
  }
</style>
