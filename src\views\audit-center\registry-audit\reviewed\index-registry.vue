<template>
  <div class="main">
    <div class="commonForm">
      <n-button
        v-if="
          buttonAuthList?.includes('systemManage_auditCenter_auditRegistry_all_audit_edit') &&
          magStatus === 'WAIT'
        "
        size="sm"
        variant="solid"
        color="primary"
        @click.stop.prevent="onAudit"
      >
        批量审批
      </n-button>
      <n-form :inline="true" :data="formInline">
        <n-form-item v-if="magStatus !== 'WAIT'" label="状态：">
          <n-select
            v-model="formInline.status"
            placeholder="请选择"
            clearable
            width="220"
            @value-change="onSearch"
            @clear="onSearch"
          >
            <n-option
              v-for="item in statusList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </n-form-item>
        <!-- <n-form-item label="审批类型：">
          <n-select
            v-model="formInline.applyType"
            placeholder="请选择"
            clearable
            width="220"
            @value-change="onSearch"
            @clear="onSearch"
          >
            <n-option
              v-for="item in applyTypeList"
              :key="item.id"
              :name="item.name"
              :value="item.id"
            />
          </n-select>
        </n-form-item> -->
        <n-form-item label="业务域：">
          <n-tree-select
            v-model="formInline.bizDomainId"
            :treeData="bizDomainIdType"
            placeholder="请选择"
            :allowClear="true"
            filter
            :key="key"
            style="width: 220px"
            @valueChange="onSearch"
          >
            <template #icon="{ nodeData, toggleNode }">
              <span
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '14.5px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="12"
                  height="12"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="#8a8e99"
                  />
                </svg>
              </span>
            </template>
            <template #default="{ item }">
              {{ item.label }}
            </template>
          </n-tree-select>
        </n-form-item>
        <n-form-item label="">
          <n-input
            v-model="formInline.name"
            placeholder="请输入模型名称"
            clearable
            @clear="onSearch"
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <template #append>
              <n-button @click.stop.prevent="onSearch">
                <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                  <SvgIcon class="icon_search" icon="icon_search" />
                </n-popover>
              </n-button>
            </template>
          </n-input>
        </n-form-item>
      </n-form>
    </div>
    <div class="table">
      <n-public-table
        v-show="magStatus === 'WAIT'"
        ref="publicTable"
        :isDisplayAction="true"
        :isNeedSelection="true"
        :table-head-titles="tableHeadTitles"
        :tableData="tableData"
        :pagination="pagination"
        :tableHeight="tableHeight"
        :actionWidth="180"
        @tablePageChange="tablePageChange"
        @handle-selection-change="handleSelectionChange"
      >
        <!-- <template #auditType="{ editor }">
          {{ editor.row.auditTypeName }}
        </template> -->
        <template #auditStatus="{ editor }">
          <i v-if="editor.row.auditStatus === 'PASS'" class="passBot"></i>
          <i v-else-if="editor.row.auditStatus === 'REJECT'" class="refuseBot"></i>
          {{ editor.row.auditStatusName }}
        </template>

        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="buttonAuthList?.includes('systemManage_auditCenter_auditRegistry_view')"
              class="seeDetails has-right-border"
              link
              @click.stop.prevent="openDetail(editor)"
              >查看
            </n-button>
            <n-button
              v-if="
                magStatus === 'WAIT' &&
                buttonAuthList?.includes('systemManage_auditCenter_auditRegistry_audit_edit')
              "
              class="seeDetails has-right-border"
              link
              @click.stop.prevent="auditModel(editor)"
              >审批
            </n-button>
          </div>
        </template>
      </n-public-table>

      <n-public-table
        v-show="magStatus === 'DONE'"
        ref="publicTable"
        :isDisplayAction="true"
        :table-head-titles="AuditTableHeadTitles"
        :tableData="tableData"
        :pagination="pagination"
        :tableHeight="tableHeight"
        :actionWidth="100"
        @tablePageChange="tablePageChange"
      >
        <!-- <template #auditType="{ editor }">
          {{ editor.row.auditTypeName }}
        </template> -->
        <template #auditStatus="{ editor }">
          <i v-if="editor.row.auditStatus === 'PASS'" class="passBot"></i>
          <i v-else-if="editor.row.auditStatus === 'REJECT'" class="refuseBot"></i>
          {{ editor.row.auditStatusName }}
        </template>
        <template #auditType="{ editor }">
          {{
            editor.row.auditType === 'REGISTER'
              ? '注册'
              : editor.row.auditType === 'CANCEL_REGISTER'
              ? '注销'
              : '--'
          }}
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="buttonAuthList?.includes('systemManage_auditCenter_auditRegistry_view')"
              class="seeDetails has-right-border"
              link
              @click.stop.prevent="openDetail(editor)"
              >查看
            </n-button>
          </div>
        </template>
      </n-public-table>
    </div>
    <n-modal
      v-model="dialogVisible"
      bodyClass="largeDialog"
      :draggable="false"
      width="580px"
      :before-close="closeDialog"
      :close-on-click-overlay="false"
      :append-to-body="false"
      title="审批"
    >
      <n-form
        ref="ruleFormSub"
        :data="ruleForm"
        :rules="rules"
        label-width="80px"
        label-align="end"
        :pop-position="['right']"
      >
        <n-form-item label="审批结论：" field="auditStatus">
          <n-radio-group class="mb-2" direction="row" v-model="ruleForm.auditStatus">
            <n-radio value="PASS">通过</n-radio>
            <n-radio value="REJECT">驳回</n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="审批备注：" field="auditComment">
          <n-textarea
            v-model="ruleForm.auditComment"
            clearable
            :rows="3"
            :autosize="{ minRows: 3 }"
            maxlength="200"
            show-count
            placeholder="请输入"
            style="width: 400px"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            size="sm"
            variant="solid"
            color="primary"
            :loading="loading"
            @click.stop.prevent="auditDataSubmit"
            >保 存</n-button
          >
          <n-button size="sm" @click.stop.prevent="closeDialog">取 消</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { formartTime } from '@/utils/index'
  import { sceneManage } from '@/api'
  import ENUM from '@/const/enum'
  export default {
    name: 'Reviewed',
    components: {},
    props: {
      activeName: {
        type: String,
      },
    },
    data() {
      return {
        loading: false,
        formInline: {
          applyType: '',
          layerId: 0,
          status: 'DONE',
          name: '',
          bizDomainId: '',
        },
        ruleForm: {
          id: '',
          bizType: '',
          auditStatus: 'PASS',
          auditComment: '',
        },
        multipleAduit: [],
        rules: {
          auditStatus: [{ required: true, trigger: 'blur', message: '请选择' }],
          auditComment: [{ required: false, trigger: 'blur', message: '请输入' }],
        },
        dialogVisible: false,
        shortcuts: ENUM.SHORTCUTS,
        modelLayerList: [], // 审核来源列表
        statusList: [
          { name: '全部', id: 'DONE' },
          { name: '审核通过', id: 'PASS' },
          { name: '审核失败', id: 'REJECT' },
        ],
        applyTypeList: [
          { name: '全部', id: '' },
          { name: '注册', id: 'PUBLISH' },
          { name: '注销', id: 'DOWN' },
        ],
        bizDomainList: [],
        defaultExpandedKeys: [],
        bizDomainIdType: [],
        key: 0,
        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'assetsCnName', name: '模型中文名称' },
          { prop: 'assetsEnName', name: '模型英文名称' },
          { prop: 'projectName', name: '所属场景' },
          { prop: 'bizDomainName', name: '注册业务域' },
          { prop: 'applyTypeName', name: '审批类型' },
          { prop: 'applyByName', name: '申请人' },
          { prop: 'applyTime', name: '申请时间' },
        ],
        AuditTableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'assetsCnName', name: '模型中文名称' },
          { prop: 'assetsEnName', name: '模型英文名称' },
          { prop: 'projectName', name: '所属场景' },
          { prop: 'bizDomainName', name: '注册业务域' },
          { prop: 'applyTypeName', name: '审批类型' },
          { prop: 'auditTime', name: '审批时间' },
          { prop: 'auditByName', name: '审批人' },
          { prop: 'auditStatus', name: '审批状态', slot: 'auditStatus' },
          { prop: 'applyByName', name: '申请人' },
          { prop: 'applyTime', name: '申请时间' },
          { prop: 'auditComment', name: '审核备注' },
        ],
        tableHeight: 400,
        tableData: {},
        needOtherActionBar: {
          label: '设为默认',
          show: false,
        },
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
      }
    },
    computed: {
      ...mapState({
        currentProject: (state) => state['user'].currentProject,
        buttonAuthList: (state) => state['user'].buttonAuthList,
      }),
      magStatus() {
        return this.activeName === 'first' ? 'WAIT' : 'DONE'
      },
      // tableHeadTitles() {
      //   let headData = [
      //     { prop: 'number', name: '序号' },
      //     { prop: 'objectName', name: 'API名称' },
      //     { prop: 'auditType', name: '用户行为', slot: 'auditType' },
      //     { prop: 'auditStatus', name: '审核状态', slot: 'auditStatus' },
      //     { prop: 'applyTime', name: '申请时间' },
      //   ]
      //   if (this.activeName === 'second') {
      //     headData.push({ prop: 'auditComment', name: '审核备注' })
      //   }
      //   return headData
      // },
    },
    watch: {
      activeName(val) {
        if (val) {
          this.formInline = {
            applyType: '',
            layerId: 0,
            status: 'DONE',
            name: '',
            bizDomainId: '',
          }
          this.onSearch()
        }
      },
      'ruleForm.auditStatus': {
        handler(val) {
          val === 'PASS' && (this.rules.auditComment[0].required = false)
          val === 'REJECT' && (this.rules.auditComment[0].required = true)
        },
        deep: true,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.getSelectData()
        this.setTableHeight()
        this.onSearch()
      })
    },

    methods: {
      setTableHeight() {
        this.tableHeight = document.body.offsetHeight - 242 - 14
      },
      //获取业务域下拉
      getSelectData() {
        sceneManage.searchTreeList({}).then((res) => {
          let { success, data } = res
          if (success) {
            if (data !== null) {
              this.bizDomainIdType = this.reformData(data)
            }
          }
        })
      },
      // 对tree数据改造
      reformData(data) {
        data.forEach((val) => {
          // if (val.id === 1) {
          val.expanded = true
          // }
          val.label = val.name
          val.value = val.id.toString()
          if (val.children) {
            this.reformData(val.children)
          }
        })
        return data
      },
      handleSelectionChange(val) {
        this.multipleAduit = val
      },
      onAudit() {
        if (this.multipleAduit.length > 0) {
          this.ruleForm.id = null
          this.ruleForm.bizType = null
          this.ruleForm.auditComment = ''
          this.dialogVisible = true
        } else {
          ElNotification({
            title: '提示',
            message: '请勾选数据',
            type: 'warning',
          })
        }
      },
      // 审核列表
      onSearch(init = true) {
        this.pagination.currentPage = init ? 1 : this.pagination.currentPage
        let params = {
          // applyUser: this.$store.state['user'].id,
          condition: {
            status: this.magStatus === 'WAIT' ? this.magStatus : this.formInline.status || 'DONE',
            layerId: this.formInline.layerId || null,
            // applyType: this.formInline.applyType || null,
            name: this.formInline.name || null,
            bizDomainId: this.formInline.bizDomainId || null,
          },
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
        }
        this.$refs.publicTable.clearSelection()
        this.$api.auditCenter
          .getAuditAssetsList(params)
          .then((res) => {
            if (res.success) {
              res.data.list.map((item, index) => {
                let objContent = JSON.parse(item.objectContent)
                delete objContent.id
                return Object.assign(item, { number: index + 1, ...objContent })
              })
              this.tableData = res.data
            }
          })
          .catch(() => {})
        this.key++
      },
      // 表格操作变化
      tablePageChange(data) {
        this.pagination.currentPage = data.currentPage
        this.pagination.pageSize = data.pageSize
        this.onSearch(false)
      },
      // 查看详情
      openDetail(editor) {
        let { row } = editor
        this.$router.push({
          name: 'approveMagsRegistry',
          query: {
            title: '查看',
            modelTitle: row.assetsEnName,
            modelName: row.assetsEnName,
            id: row.objectId,
            status: this.magStatus,
          },
        })
      },

      // 审批
      auditModel(editor) {
        let { row } = editor
        this.ruleForm.id = row.id
        this.ruleForm.bizType = row.bizType
        this.ruleForm.auditComment = ''
        this.dialogVisible = true
      },
      auditDataSubmit() {
        let params = []
        this.$refs.ruleFormSub.validate((valid) => {
          if (valid) {
            if (this.ruleForm.id) {
              //单条
              params.push({
                ...this.ruleForm,
              })
            } else {
              this.multipleAduit?.forEach((item) => {
                params.push({ ...this.ruleForm, bizType: item.bizType, id: item.id })
              })
            }
            this.loading = true
            this.$api.auditCenter
              .approvalStatus(params)
              .then((res) => {
                const { success } = res
                this.loading = false

                if (success) {
                  this.$notify({
                    title: '提示',
                    message: `审批${this.ruleForm.auditStatus === 'PASS' ? '通过' : '拒绝'}`,
                    type: 'success',
                  })
                  this.dialogVisible = false
                  this.onSearch(false)
                }
              })
              .catch((err) => (this.loading = false))
          }
        })
      },
      // 关闭弹框
      closeDialog() {
        this.dialogVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .main {
    height: 100%;
    .commonForm {
      margin-bottom: 20px;

      :deep(.el-form-item__label) {
        font-size: 12px;
      }
    }

    .table {
      height: calc(100% - 120px);
      margin-top: 10px;
      :deep(.nancalui-button--outline) {
        background-color: transparent;
      }
    }

    .passBot,
    .refuseBot {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 5px;
      background-color: #18ba72;
      border-radius: 50%;
    }

    .refuseBot {
      background-color: #f54446;
    }

    :deep(.nancalui-radio-group) {
      .nancalui-radio__label {
        font-size: 12px;
      }
    }
  }
  :deep(.nancalui-modal__body) {
    .nancalui-form {
      width: 470px !important;
      margin: 0 auto;
    }
  }
  :deep(.dialog-footer) {
    // margin-top: 31px;
    display: flex;
    justify-content: center;
    .nancalui-button > span {
      font-size: 12px;
    }
  }
</style>
