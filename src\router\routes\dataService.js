import Layout from '@/layout'

// 功能模块-数据服务
export default [
  {
    path: '/service',
    component: Layout,
    redirect: '/service/api',
    name: 'service',
    code: 'service',
    meta: {
      title: '数据服务',
      icon: 'left_icon_service',
      parentRouterName: 'assetsManage',
      code: 'service',
    },
    children: [
      {
        path: 'api',
        name: 'api',
        code: 'api',
        redirect: '/service/api/apiList',
        component: () => import('@/views/data-service/api-management/index'),
        meta: { title: 'API管理', parentRouterName: 'assetsManage', code: 'api' },
        children: [
          {
            path: 'apiList',
            name: 'apiList',
            hidden: true,
            component: () => import('@/views/data-service/api-management/list'),
            meta: {
              title: 'API列表',
              keepAlive: true,
              activeMenu: '/service/api',
              parentRouterName: 'assetsManage',
              code: 'api',
            },
          },
          {
            path: 'addApi',
            name: 'addApi',
            component: () => import('@/views/data-service/api-management/add'),
            hidden: true,
            meta: {
              title: '新增API',
              activeMenu: '/service/api',
              parentRouterName: 'assetsManage',
            },
          },
          {
            path: 'editApi',
            name: 'editApi',
            component: () => import('@/views/data-service/api-management/add'),
            hidden: true,
            meta: {
              title: '编辑API',
              activeMenu: '/service/api',
              parentRouterName: 'assetsManage',
            },
          },
          {
            path: 'detailApi',
            name: 'detailApi',
            component: () => import('@/views/data-service/api-management/detail'),
            hidden: true,
            meta: {
              title: '查看',
              activeMenu: '/service/api',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
      {
        path: 'apiWhiteOrder',
        name: 'apiWhiteOrder',
        code: 'whitelistManagement',
        component: () => import('@/views/data-service/white-order/list'),
        meta: {
          title: '白名单管理',
          activeMenu: '/service/apiWhiteOrder',
          parentRouterName: 'assetsManage',
          code: 'whitelistManagement',
        },
      },
      {
        path: 'APIMonitoringLog',
        name: 'APIMonitoringLog',
        code: 'APIMonitoringLog',
        redirect: '/service/APIMonitoringLog/APIMonitoringLogList',
        component: () => import('@/views/data-service/api-monitoring-log/index'),
        meta: { title: 'API监控日志', parentRouterName: 'assetsManage', code: 'APIMonitoringLog' },
        children: [
          {
            path: 'APIMonitoringLogList',
            name: 'APIMonitoringLogList',
            hidden: true,
            component: () => import('@/views/data-service/api-monitoring-log/list'),
            meta: {
              title: 'API监控日志',
              activeMenu: '/service/APIMonitoringLog',
              parentRouterName: 'assetsManage',
              code: 'APIMonitoringLog',
            },
          },
          {
            path: 'APIMonitoringLogDetail',
            name: 'APIMonitoringLogDetail',
            component: () => import('@/views/data-service/api-monitoring-log/detail'),
            hidden: true,
            meta: {
              title: '查看实例',
              activeMenu: '/service/APIMonitoringLog',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
      {
        path: 'tokenManagement',
        name: 'tokenManagement',
        code: 'token_manage',
        redirect: '/service/tokenManagement/tokenManagementList',
        component: () => import('@/views/data-service/token-management/index'),
        meta: { title: 'token管理', parentRouterName: 'assetsManage', code: 'token_manage' },
        children: [
          {
            path: 'tokenManagementList',
            name: 'tokenManagementList',
            hidden: true,
            component: () => import('@/views/data-service/token-management/list'),
            meta: {
              title: 'token管理',
              activeMenu: '/service/tokenManagement',
              parentRouterName: 'assetsManage',
              code: 'token_manage',
            },
          },
        ],
      },
    ],
  },
]
