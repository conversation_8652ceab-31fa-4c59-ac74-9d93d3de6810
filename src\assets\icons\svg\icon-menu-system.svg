<?xml version="1.0" encoding="UTF-8"?>
<svg width="132px" height="154px" viewBox="0 0 132 154" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>系统管理</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#0F489C" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.018401%" id="linearGradient-2">
            <stop stop-color="#0E203C" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F5C73" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9780798%" x2="100%" y2="49.9780798%" id="linearGradient-3">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0206526%" x2="100%" y2="50.0206526%" id="linearGradient-4">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.0459347726%" id="linearGradient-5">
            <stop stop-color="#56B0FF" stop-opacity="0.6" offset="0%"></stop>
            <stop stop-color="#50ACFF" stop-opacity="0.56" offset="4%"></stop>
            <stop stop-color="#369BFF" stop-opacity="0.4" offset="26%"></stop>
            <stop stop-color="#218EFF" stop-opacity="0.27" offset="46%"></stop>
            <stop stop-color="#1285FF" stop-opacity="0.18" offset="66%"></stop>
            <stop stop-color="#097FFF" stop-opacity="0.12" offset="84%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0087951%" y1="100%" x2="50.0087951%" y2="0%" id="linearGradient-6">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-7">
            <stop stop-color="#DBFDFF" offset="0%"></stop>
            <stop stop-color="#2AD0FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0554939%" x2="100%" y2="50.0554939%" id="linearGradient-8">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0530223%" x2="100%" y2="50.0530223%" id="linearGradient-9">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-10">
            <stop stop-color="#56B0FF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#379CFF" stop-opacity="0.1" offset="24%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9941072%" y1="100%" x2="49.9941072%" y2="0%" id="linearGradient-11">
            <stop stop-color="#DEEDFF" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#D5E9FF" stop-opacity="0.28" offset="21%"></stop>
            <stop stop-color="#BCDEFF" stop-opacity="0.22" offset="45%"></stop>
            <stop stop-color="#92CBFF" stop-opacity="0.13" offset="72%"></stop>
            <stop stop-color="#57B1FF" stop-opacity="0" offset="100%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-12">
            <stop stop-color="#DAEAFF" offset="0%"></stop>
            <stop stop-color="#D5E8FF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#C8E1FF" stop-opacity="0.92" offset="24%"></stop>
            <stop stop-color="#B1D6FF" stop-opacity="0.83" offset="38%"></stop>
            <stop stop-color="#92C7FF" stop-opacity="0.7" offset="53%"></stop>
            <stop stop-color="#69B4FF" stop-opacity="0.53" offset="69%"></stop>
            <stop stop-color="#389CFF" stop-opacity="0.33" offset="84%"></stop>
            <stop stop-color="#0081FF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-13">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.011786893%" y1="50%" x2="100%" y2="50%" id="linearGradient-14">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="33%"></stop>
            <stop stop-color="#00BFFC" offset="41%"></stop>
            <stop stop-color="#00A4FB" offset="50%"></stop>
            <stop stop-color="#0091FA" offset="58%"></stop>
            <stop stop-color="#0086F9" offset="67%"></stop>
            <stop stop-color="#0082F9" offset="76%"></stop>
            <stop stop-color="#A5E9E9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9773037%" x2="100%" y2="49.9773037%" id="linearGradient-15">
            <stop stop-color="#56B0FF" offset="0%"></stop>
            <stop stop-color="#52ADFF" offset="3%"></stop>
            <stop stop-color="#3198FF" offset="30%"></stop>
            <stop stop-color="#1989FF" offset="56%"></stop>
            <stop stop-color="#0B80FF" offset="80%"></stop>
            <stop stop-color="#067DFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9913059%" y1="100%" x2="49.9913059%" y2="0%" id="linearGradient-16">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9656829%" x2="100%" y2="49.9656829%" id="linearGradient-17">
            <stop stop-color="#00459E" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#004184" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-18">
            <stop stop-color="#56E3FF" stop-opacity="0.887877138" offset="0%"></stop>
            <stop stop-color="#06B8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9884232%" y1="100%" x2="49.9884232%" y2="0%" id="linearGradient-19">
            <stop stop-color="#5098FF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#08B5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-0.12345679%" y1="50.1040944%" x2="100.123457%" y2="50.1040944%" id="linearGradient-20">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DEEDFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#009EED" offset="0%"></stop>
            <stop stop-color="#0063D4" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-22" points="22.6009016 8.76515283 0 16.9824836 0.81675948 0 13.4532151 4.98398979"></polygon>
        <path d="M38.7986279,6.49124341 L38.965,6.707 L38.7689112,6.4631936 L38.9579715,6.70774656 L38.9579715,6.70774656 L39.051,6.819 L38.9638706,6.72066271 C39.6040326,7.51424065 40.3955003,8.1733181 41.2925105,8.65978677 C43.0992117,9.65519783 45.141169,9.98695897 47.154343,9.5406962 C49.3051656,9.09456108 51.5707378,9.87258499 52.8097027,11.5313907 C54.3811477,13.8597182 55.8494841,16.1820461 56.8762195,18.7276331 C57.6447999,20.672629 57.2318542,22.9607469 55.8265853,24.5051786 L55.7462477,24.6081918 C52.9208705,27.6743336 52.9208705,32.3877926 55.7462477,35.454062 C57.2203409,36.9986214 57.661942,39.3095885 56.8762195,41.3344931 C55.8359645,43.8894502 54.4273449,46.279119 52.6949529,48.42785 C51.3184675,50.0580622 49.2248279,50.8245976 47.1658564,50.447138 L47.0453499,50.4185445 C45.0491975,49.9873151 42.963884,50.3006482 41.1835174,51.2993262 C39.4288328,52.3096676 38.1018905,53.9218547 37.4497366,55.8357354 C36.77568,57.9057016 34.9759279,59.40963 32.8153556,59.7083669 C31.4502555,59.8227409 29.9876758,59.9372425 28.6398457,59.9372425 C27.2747457,59.9372425 25.8121659,59.8227409 24.4585792,59.6053537 C22.5491482,59.324609 20.9056579,58.112478 20.0764686,56.3733952 L20.1568062,56.5335954 C20.0192305,56.2751541 19.9096198,56.0028323 19.8298269,55.7212337 L19.6348675,55.2179116 L19.5087323,54.8975113 C18.7401519,53.181391 17.4266061,51.8085202 15.7863369,50.9962863 C14.3107849,50.2611077 12.6525238,49.9708752 11.0143073,50.1610754 L10.2169436,50.2925544 L9.7926125,50.3613575 L9.37391017,50.407056 L9.09861308,50.4185445 L8.484,50.391 L8.22707234,50.3620781 C7.36465129,50.2408289 6.53662424,49.9273211 5.80630542,49.4403661 C5.34800696,49.1217529 4.93509849,48.7426298 4.57885392,48.3133484 C2.87219631,46.1470292 1.46579263,43.7611627 0.397587375,41.2201191 L0.328763101,41.0142204 L0.237040045,40.7282855 L0.217,40.645 L0.224,40.664 L0.179593683,40.4955213 C-0.245664403,38.7186718 0.216332335,36.8258815 1.4529782,35.436957 L1.53331582,35.3510489 C2.33055165,34.515838 2.92131466,33.5033219 3.28257814,32.3764318 L3.27106478,32.3764318 C3.627835,31.2733667 3.73560842,30.1052002 3.58665862,28.9556796 L3.53905321,28.6411973 L3.53905321,28.6411973 L3.496,28.407 L3.436,28.123 L3.38285803,27.9071711 L3.38285803,27.9071711 L3.338,27.757 L3.32708174,27.7177392 C3.26792859,27.5115686 3.19951398,27.3081 3.12203081,27.1079526 L3.183,27.3 L3.06853923,26.9953711 C3.02771667,26.8945901 2.98434371,26.7948192 2.93845677,26.6961552 L2.974,26.784 L2.85239799,26.5213714 C2.80527787,26.4258931 2.75565819,26.3316229 2.70358434,26.2386593 L2.884,26.619 L2.77966021,26.3998586 L2.77966021,26.3998586 L2.668,26.185 L2.49,25.86 L2.486,25.853 L2.3533192,25.6307431 C2.10537711,25.2366184 1.82145566,24.8656051 1.50491622,24.522539 L1.43609194,24.4310142 L1.32134218,24.3166403 L1.247,24.216 L1.16657532,24.1161562 L1.16657532,24.1161562 L1.119,24.051 L0.994362912,23.898971 C0.965707452,23.8703775 0.954194098,23.824679 0.925538638,23.7960855 L1.07,23.984 L1.01611287,23.9098773 C-0.0502384763,22.3768069 -0.300070792,20.4056354 0.374944445,18.6419803 C1.3957952,16.0963933 2.85274624,13.6595637 4.55045431,11.548751 C4.69590247,11.368922 4.85303582,11.1988242 5.0208388,11.039557 L5.1010485,10.9537765 C6.55224286,9.67243052 8.46793711,9.15187572 10.3148071,9.54082385 C12.3393665,9.98708662 14.3813239,9.65532548 16.170755,8.66004207 C17.9300762,7.65364549 19.2600773,6.04028484 19.9104204,4.12363296 C20.5825919,2.04937326 22.3816327,0.539399263 24.5448014,0.233896383 C27.3667246,-0.0807597163 30.165749,-0.0807597163 32.95326,0.251001421 L32.9530041,0.250873771 C34.1574289,0.439667431 35.2414752,0.971583036 36.0961499,1.76671198 L36.244,1.909 L36.421,2.095 L36.5350834,2.22393232 C37.003016,2.78274991 37.3608671,3.42722262 37.5873851,4.12350531 C37.7594458,4.69080111 37.9992008,5.22627317 38.290067,5.72009662 L38.458,5.992 L38.2985267,5.74248434 C38.3926592,5.91176718 38.4965376,6.07537274 38.6096124,6.23248861 L38.627,6.256 L38.4705874,6.01705849 L38.7860533,6.4631936 L38.7860533,6.4631936 L38.628,6.257 L38.7986279,6.49124341 L38.7986279,6.49124341 Z M21.5620751,38.8805585 L21.3440889,38.7088698 C22.0897705,39.3095885 22.9041484,39.8015498 23.7529385,40.1962422 L23.3974317,40.036042 C23.6191704,40.1390127 23.840909,40.2394303 24.0694705,40.332189 L24.082,40.337 C24.3160493,40.4353005 24.545789,40.5167846 24.7796739,40.5851903 L24.424,40.472 L24.7756608,40.5898755 C25.0122863,40.6652833 25.2480968,40.733902 25.4967,40.8025775 L25.206,40.715 L25.4984057,40.7970507 L25.4984057,40.7970507 L25.8637201,40.888358 L26.273,40.96 L26.5983669,41.0103909 C26.7222042,41.0281768 26.8464489,41.0446861 26.9706652,41.0599189 L28.1292924,41.1172336 C34.0369225,41.1172336 38.7573978,36.4092635 39.2678232,30.4544128 L39.2678232,30.3114453 L39.2563098,30.1684778 L39.2679511,29.836589 L39.2964786,29.4018147 C39.2964786,26.3050861 38.1352554,23.4729565 36.2459909,21.3729634 L36.019,21.128 L36.0731232,21.1816954 C35.4934826,20.5706466 34.849245,20.0240148 34.1516723,19.5513555 L34.4671382,19.7687427 C34.2832152,19.6341515 34.0951708,19.5053074 33.9032794,19.3823924 L33.824,19.333 L34.1689423,19.5456113 C33.8363343,19.3339684 33.5035983,19.1167089 33.159477,18.9450203 L33.272,19.008 L33.0124371,18.8743996 L33.0124371,18.8743996 L32.981,18.859 L32.8581389,18.7962131 C32.7607345,18.7482027 32.662547,18.7017904 32.5636162,18.6569951 L32.404,18.587 L32.702,18.725 L32.292,18.54 L32.23,18.514 L32.2646335,18.5274786 C31.9412771,18.3863976 31.6099884,18.2641591 31.2724382,18.1613798 L31.365,18.194 L31.097,18.112 L31.2839515,18.1613798 C30.9226728,18.0479836 30.5549081,17.9562945 30.1826352,17.8868056 L30.271,17.906 L30.049,17.861 L29.854,17.824 L29.229,17.742 L29.0098438,17.7203854 C28.7132366,17.6964715 28.4156155,17.6850688 28.1177791,17.6862195 C21.9635074,17.6862195 16.9161807,22.9607469 16.9161807,29.4018147 C16.9439118,33.0112906 18.4810784,36.3741411 21.0701763,38.4951963 L21.313,38.688 L21.0795216,38.4979609 C21.0010149,38.4318065 20.9237675,38.3643996 20.8482291,38.2956523 L20.6271907,38.0853019 L21.3325755,38.7031256 L21.3325755,38.7031256 L21.5620751,38.8805585 L21.5620751,38.8805585 Z" id="path-24"></path>
        <path d="M23.6946559,15.4164124 C17.4842036,15.4164124 12.4832042,21.7127875 12.5960452,29.4018236 C12.6876112,33.7270563 14.3096659,37.7556323 16.9775978,40.2859366 L17.1607347,40.4546418 L17.2011882,40.4928989 C17.2836159,40.5689851 17.36684,40.6436808 17.4504084,40.7170907 L17.2274245,40.5121371 C17.9904372,41.2292461 18.820868,41.8165258 19.684321,42.2876905 L19.3227624,42.0964513 C19.5483292,42.2193727 19.7738512,42.3392464 20.0061242,42.4499772 L20.0543529,42.47153 L20.2006377,42.5421293 L20.4576839,42.6513418 L20.3606781,42.6154639 C20.7274444,42.7657124 21.0765096,42.8885322 21.4546232,43.0115043 L21.1690323,42.9100709 L21.4562476,43.0049068 L21.8264965,43.1139049 L22.2324682,43.1977696 L22.5699876,43.259582 C22.6952669,43.2808139 22.8209351,43.3005219 22.9465522,43.3187062 L24.1167603,43.3871256 C30.0783194,43.3871256 34.7594077,37.766978 35.1701697,30.6583644 L35.1676651,30.4876967 L35.153542,30.317029 L35.1594752,29.9208362 L35.1806463,29.4018236 C35.1242248,25.5572293 33.8053724,22.0542752 31.7250218,19.5207745 L31.7838577,19.5890415 C31.2874936,18.9811744 30.7466447,18.4267086 30.1679846,17.9322453 L29.8437153,17.6655033 L30.1384587,17.9023268 C29.9504988,17.7416583 29.7584805,17.5878505 29.5626841,17.4411204 L29.3444207,17.2829039 L29.8336316,17.6359633 C29.4942795,17.3833142 29.1547,17.1239602 28.8044294,16.9190066 L28.9320009,17.0021978 L28.6548101,16.8347032 L28.6309416,16.8213871 L28.4977336,16.7413679 C28.3985989,16.6840553 28.298702,16.6286506 28.1980833,16.5751761 L28.0335042,16.4907333 L28.3433702,16.6595003 L27.9228034,16.4357909 L27.8555376,16.4028254 L27.8941023,16.4205656 C27.5653224,16.2521498 27.2288681,16.1062275 26.8864358,15.9835344 L26.9869017,16.0252207 L26.7132501,15.9263243 L26.8980543,15.9835344 C26.531491,15.8481676 26.1587628,15.7387136 25.7818741,15.6557611 L25.8625669,15.6775847 L25.6581064,15.627637 L25.4536899,15.5806862 L24.7598007,15.4747971 L24.4155311,15.4417828 C24.1755129,15.4237404 23.9350834,15.4152729 23.6946559,15.4164124 Z M32.7345781,6.70697744 L32.5652382,6.4631936 L32.7286478,6.70774656 L32.8115141,6.82285611 L32.7338242,6.72066271 C33.2866263,7.51424065 33.9653597,8.1733181 34.7307794,8.65978677 C36.2726683,9.65519783 38.0036897,9.98695897 39.6989614,9.5406962 C41.5105951,9.09456108 43.4371969,9.87258499 44.50889,11.5313907 C45.8714682,13.8597182 47.1467964,16.1820461 48.052097,18.7276331 C48.7303543,20.672629 48.4148533,22.9607469 47.2495857,24.5051786 L47.1831847,24.6081918 C44.8397716,27.6743336 44.9089442,32.3877926 47.3423539,35.454062 C48.6111343,36.9986214 49.018353,39.3095885 48.3838653,41.3344931 C47.5419894,43.8894502 46.3862935,46.279119 44.9533634,48.42785 C43.8136864,50.0580622 42.055094,50.8245976 40.3090191,50.447138 L40.2067303,50.4185445 C38.5129699,49.9873151 36.7547648,50.3006482 35.2644021,51.2993262 C33.7959205,52.3096676 32.6978599,53.9218547 32.174654,55.8357354 C31.6352235,57.9057016 30.1358882,59.40963 28.3138494,59.7083669 C27.1615513,59.8227409 25.9268513,59.9372425 24.7874737,59.9372425 C23.6334971,59.9372425 22.3954364,59.8227409 21.2480022,59.6053537 C19.6297595,59.324609 18.2226592,58.112478 17.4961885,56.3733952 L17.5664523,56.5335954 C17.446361,56.2751541 17.3497059,56.0028323 17.2781211,55.7212337 L17.1059271,55.2179116 L16.9945976,54.8975113 C16.3196992,53.181391 15.1891559,51.8085202 13.7906472,50.9962863 C12.5325117,50.2611077 11.1264544,49.9708752 9.74439219,50.1610754 L9.07227651,50.2925544 L8.71458129,50.3613575 L8.36130521,50.407056 L8.12875357,50.4185445 L7.61049982,50.3922366 L7.39117476,50.3620781 C6.66035448,50.2408289 5.95578739,49.9273211 5.33127177,49.4403661 C4.93917692,49.1217529 4.58456414,48.7426298 4.27711564,48.3133484 C2.80261406,46.1470292 1.57870779,43.7611627 0.638417609,41.2201191 L0.577215869,41.0142204 L0.495482246,40.7282855 L0.47817112,40.6454421 L0.48444036,40.669417 L0.44350447,40.4955213 C0.0579396166,38.7186718 0.420707181,36.8258815 1.44571283,35.436957 L1.51236486,35.3510489 C2.17404474,34.515838 2.65858238,33.5033219 2.94743588,32.3764318 L2.93770316,32.3764318 C3.22310797,31.2733667 3.29706989,30.1052002 3.15428648,28.9556796 L3.10942842,28.6411973 L3.06872584,28.4052582 L3.01925105,28.1445312 L3.0156058,28.12655 L2.9666178,27.9071711 L2.92563264,27.7559381 L2.91668773,27.7177392 C2.86365741,27.5115686 2.80283764,27.3081 2.73440057,27.1079526 L2.7887649,27.300415 L2.68752969,26.9953711 C2.65154164,26.8945901 2.61341245,26.7948192 2.57317434,26.6961552 L2.60364905,26.7829568 L2.49786018,26.5213714 C2.45662635,26.4258931 2.41329727,26.3316229 2.36791276,26.2386593 L2.52600932,26.6191284 L2.43458859,26.3998586 L2.3431462,26.1975697 L2.17569477,25.8469369 L2.17308545,25.8419421 L2.06289736,25.6307431 C1.84751746,25.2366184 1.60206185,24.8656051 1.32944303,24.522539 L1.26991979,24.4310142 L1.17123847,24.3166403 L1.10606639,24.2156449 L1.0374653,24.1161562 L0.996289001,24.0508175 L0.888699582,23.898971 C0.864056288,23.8703775 0.853652912,23.824679 0.829009618,23.7960855 L0.958199672,23.9898813 L0.907245797,23.9098773 C-0.0166846288,22.3768069 -0.256806465,20.4056354 0.28792975,18.6419803 C1.11354002,16.0963933 2.30940033,13.6595637 3.71356719,11.548751 C3.83388157,11.368922 3.96421673,11.1988242 4.10373033,11.039557 L4.17027609,10.9537765 C5.37822741,9.67243052 6.9900051,9.15187572 8.55695019,9.54082385 C10.2749448,9.98708662 11.9962287,9.65532548 13.4943039,8.66004207 C14.9667629,7.65364549 16.0673919,6.04028484 16.5890264,4.12363296 C17.1268004,2.04937326 18.6254457,0.539399263 20.4495801,0.233896383 C22.8304529,-0.0807597163 25.1965863,-0.0807597163 27.5578557,0.251001421 C28.5785592,0.439667431 29.5027554,0.971583036 30.2369169,1.76671198 L30.363989,1.90900117 L30.5068667,2.08281918 L30.6146759,2.22393232 C31.01844,2.78274991 31.3304046,3.42722262 31.5321082,4.12350531 C31.6858838,4.69080111 31.8964171,5.22627317 32.1495457,5.72009662 L32.2954929,5.99172634 L32.1570256,5.74248434 C32.2390841,5.91176718 32.3292979,6.07537274 32.4271906,6.23248861 L32.442226,6.25545021 L32.3065052,6.01705849 L32.5797292,6.4631936 L32.4431006,6.25744812 L32.5907707,6.49124341 L32.7345781,6.70697744 Z" id="path-26"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="统一登陆页" transform="translate(-1574.000000, -443.000000)">
            <g id="系统管理" transform="translate(1510.000000, 355.000000)">
                <g transform="translate(64.000000, 88.000000)">
                    <g id="头像底座" transform="translate(0.000000, 56.685823)">
                        <g id="底座2-link" transform="translate(0.000000, 25.735981)" fill-rule="nonzero">
                            <g id="底座2">
                                <g id="编组" transform="translate(0.000000, 18.929000)">
                                    <path d="M67.2031234,0.267651448 L131.300775,26.0505507 L64.8085446,50.2252237 L0.710893185,24.4423244 L67.2031234,0.267651448 Z" id="路径" stroke="url(#linearGradient-2)" stroke-width="0" fill="url(#linearGradient-1)"></path>
                                    <polygon id="路径" fill="url(#linearGradient-3)" points="132 26.0623427 131.988332 28.2186636 64.7923628 52.6491959 64.8040308 50.4928751"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-4)" points="64.8040308 50.4928751 64.7923628 52.6491959 0 26.5868532 0.0116679926 24.4305324"></polygon>
                                </g>
                                <g id="编组" transform="translate(32.845399, 27.251233)">
                                    <polygon id="路径" fill="url(#linearGradient-5)" points="33.7671705 1.46862933 66.3325378 14.5697354 32.5653673 26.8432806 0.0116679926 13.7538302"></polygon>
                                    <path d="M33.7555025,2.0980419 L64.6990188,14.5464239 L32.5770353,26.2022122 L1.63351896,13.7771418 L33.7555025,2.12135348 M33.7555025,1.5035967 L0,13.7538302 L32.5653673,26.8432806 L66.3325378,14.5697354 L33.7671705,1.48028512 L33.7555025,1.5035967 Z" id="形状" fill="url(#linearGradient-6)"></path>
                                    <polygon id="路径" fill="url(#linearGradient-7)" points="33.5221427 0 52.7509944 7.73944346 32.8103951 14.9893438 13.5698754 7.24990035"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-8)" points="52.7509944 7.73944346 52.7276584 10.9914084 32.7753911 18.2413088 32.8103951 14.9893438"></polygon>
                                    <polygon id="路径" fill="url(#linearGradient-9)" points="32.8103951 14.9893438 32.7753911 18.2413088 13.5465394 10.5135211 13.5698754 7.24990035"></polygon>
                                </g>
                                <g id="编组" transform="translate(16.510209, 0.000000)">
                                    <polygon id="路径" fill="url(#linearGradient-10)" points="50.4057279 0 98.991249 19.5467571 48.5971891 37.8696563 2.07265184e-15 18.3228993"></polygon>
                                    <path d="M50.3940599,0.629412571 L97.35773,19.5117897 L48.5971891,37.2985227 L1.63351896,18.3462109 L50.3940599,0.629412571 M50.3940599,0 L2.07265184e-15,18.3228993 L48.5971891,37.8696563 L99.002917,19.5467571 L50.4057279,0 L50.3940599,0 Z" id="形状" fill="url(#linearGradient-11)"></path>
                                </g>
                                <g id="编组" transform="translate(32.472023, 4.778873)">
                                    <polygon id="路径" fill="url(#linearGradient-12)" points="34.1522143 0 67.0676213 13.2409756 32.927075 25.6543901 0 12.4250704"></polygon>
                                    <path d="M34.1405463,0.629412571 L65.4341024,13.217664 L32.927075,25.0366334 L1.63351896,12.448382 L34.1405463,0.629412571 M34.1405463,0 L0,12.4250704 L32.927075,25.6543901 L67.0676213,13.2409756 L34.1522143,0 L34.1405463,0 Z" id="形状" fill="url(#linearGradient-13)"></path>
                                </g>
                            </g>
                        </g>
                        <polygon id="路径" fill="url(#linearGradient-14)" fill-rule="nonzero" points="66.9042694 25.6777017 115.489791 45.212803 65.0957306 63.5357023 16.4985415 44.000601"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-15)" fill-rule="nonzero" points="67.7210289 0 100.659772 13.2526313 66.4958897 25.6777017 33.5571466 12.4250704"></polygon>
                        <path d="M67.6743569,0.629412571 L98.991249,13.217664 L66.5075577,25.0482891 L35.1789976,12.448382 L67.6743569,0.629412571 M67.6743569,0 L33.5571466,12.4250704 L66.5075577,25.6660459 L100.659772,13.2526313 L67.6743569,0 Z" id="形状" fill="url(#linearGradient-16)" fill-rule="nonzero"></path>
                        <polygon id="路径" fill="url(#linearGradient-17)" fill-rule="nonzero" points="67.511005 4.34760905 89.2951472 13.1127619 66.6942456 21.3300927 44.9101034 12.5649398"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-18)" fill-rule="nonzero" points="33.5571466 12.4250704 16.4985415 44.000601 65.0957306 63.5357023 66.9042694 25.6777017"></polygon>
                        <polygon id="路径" fill="url(#linearGradient-19)" fill-rule="nonzero" points="100.659772 13.2526313 66.4958897 25.6777017 65.0957306 63.5357023 115.489791 45.212803"></polygon>
                        <polyline id="路径" stroke="url(#linearGradient-20)" stroke-width="0" points="108.523999 31.4473169 65.2240785 47.194287 23.4643331 30.398296"></polyline>
                        <g id="Clipped" transform="translate(66.694246, 4.347609)">
                            <mask id="mask-23" fill="white">
                                <use xlink:href="#path-22"></use>
                            </mask>
                            <use id="路径" fill="url(#linearGradient-21)" xlink:href="#path-22"></use>
                        </g>
                    </g>
                    <g id="编组-8" transform="translate(33.409241, 1.815589)">
                        <g id="路径-2" transform="translate(0.000000, 0.926716)">
                            <mask id="mask-25" fill="white">
                                <use xlink:href="#path-24"></use>
                            </mask>
                            <use id="蒙版" fill="#009FFB" xlink:href="#path-24"></use>
                            <path d="M0.250529217,23.0735743 C2.97659747,24.1822207 5.94938234,24.1439846 9.16888381,22.9588659 C12.3883853,21.7737472 14.4979181,24.8357385 15.4974824,32.1448398 L9.16888381,37.0062265 C3.0562946,38.7485491 0,38.3738987 0,35.8822751 C0,33.3906516 0.0835097388,29.1210846 0.250529217,23.0735743 Z" fill="#008FE2" mask="url(#mask-25)"></path>
                            <path d="M9.16888381,8.19304496 C13.3287652,10.4568513 17.5291016,11.2764962 21.7698929,10.6519796 C26.0106843,10.0274631 28.2974076,8.23372612 28.630063,5.27076865 C25.7249631,6.0028688 23.5892306,6.29485419 22.2228657,6.14672481 C20.1733182,5.92453075 19.5421122,4.57686732 18.8994734,3.59166284 C17.7822053,1.87882281 14.5386755,3.41261685 9.16888381,8.19304496 Z" id="路径-3" fill="#008FE2" mask="url(#mask-25)"></path>
                        </g>
                        <path d="M24.7739172,49.2400646 C32.241543,51.0150635 37.73876,55.9142525 37.4497366,56.7624516 C36.77568,58.8324179 34.9759279,60.3363462 32.8153556,60.6350832 C31.4502555,60.7494571 29.9876758,60.8639588 28.6398457,60.8639588 C27.2747457,60.8639588 25.8121659,60.7494571 24.4585792,60.53207 C22.6321669,60.2635316 21.0490728,59.1428363 20.1891966,57.5239267 L20.1413282,57.4302243 L20.0764686,57.3001114 L20.0764686,57.3001114 L20.1568062,57.4603116 C20.0192305,57.2018704 19.9096198,56.9295486 19.8298269,56.64795 L19.6348675,56.1446279 L19.5087323,55.8242275 C18.7821098,54.2017926 16.4513113,52.41379 14.81296,51.5370653 C14.7183556,51.48644 13.9267406,51.2820411 13.9765842,51.2059155 C14.1408797,50.954989 15.5335612,51.0844189 16.6022076,50.912729 C17.6708541,50.7410392 17.7863485,50.311598 18.0739026,50.3620781 C18.9120355,50.5092124 17.664264,50.1812082 18.8114814,50.4185445 C19.1752241,50.4937955 19.494845,50.3403086 19.8035652,50.1515417 L20.0662879,49.9856136 C20.2406897,49.875251 20.4146316,49.7714171 20.5943121,49.7102088 C21.9217449,49.2580182 23.2068263,48.8675788 24.7739172,49.2400646 Z" id="形状结合备份" fill="#0754AA"></path>
                        <path d="M36.4152939,3.01589034 L36.5350834,3.15064858 C37.003016,3.70946616 37.3608671,4.35393887 37.5873851,5.05022156 C37.793858,5.73097652 38.0978105,6.3659053 38.4705874,6.93803051 L38.2985267,6.66920059 C38.3926592,6.83848343 38.4965376,7.00208899 38.6096124,7.15920486 L38.6212939,7.17489034 L38.6282398,7.1846825 C38.683267,7.26350373 38.7404168,7.34105318 38.7986279,7.41795966 L38.9652939,7.63389034 L38.7689112,7.38990986 L38.9579715,7.63446281 L38.9579715,7.63446281 L39.0512939,7.74589034 L38.9638706,7.64737897 C39.6040326,8.4409569 40.3955003,9.10003436 41.2925105,9.58650303 C43.0992117,10.5819141 45.141169,10.9136752 47.154343,10.4674125 C49.3051656,10.0212773 51.5707378,10.7993012 52.8097027,12.4581069 C54.3811477,14.7864344 55.8494841,17.1087624 56.8762195,19.6543494 C57.6447999,21.5993453 57.2318542,23.8874632 55.8265853,25.4318949 L55.7462477,25.5349081 C52.9208705,28.6010498 52.9208705,33.3145089 55.7462477,36.3807783 C57.2203409,37.9253376 57.661942,40.2363048 56.8762195,42.2612093 C55.8359645,44.8161665 54.4273449,47.2058352 52.6949529,49.3545663 C51.3184675,50.9847784 49.2248279,51.7513139 47.1658564,51.3738542 L47.0453499,51.3452607 C45.0491975,50.9140313 42.963884,51.2273644 41.1835174,52.2260425 C40.5842536,52.5710965 33.0483863,48.7254118 28.1063297,42.2612093 L27.9392939,42.0348903 L28.1292924,42.0439498 C34.0369225,42.0439498 38.7573978,37.3359798 39.2678232,31.381129 L39.2678232,31.2381615 L39.2563098,31.0951941 L39.2679511,30.7633053 L39.2964786,30.328531 C39.2964786,27.2318024 38.1352554,24.3996728 36.2459909,22.2996796 L36.0192939,22.0548903 L36.0731232,22.1084116 C35.4934826,21.4973629 34.849245,20.950731 34.1516723,20.4780718 L34.4671382,20.695459 C34.2832152,20.5608678 34.0951708,20.4320237 33.9032794,20.3091086 L33.6802939,20.1708903 L34.1689423,20.4723276 C33.8363343,20.2606846 33.5035983,20.0434251 33.159477,19.8717365 L33.2882939,19.9438903 L33.0124371,19.8011159 L33.0124371,19.8011159 L32.9842939,19.7878903 L32.8581389,19.7229294 C32.7607345,19.6749189 32.662547,19.6285067 32.5636162,19.5837114 L32.4042939,19.5138903 L32.7022939,19.6518903 L32.2942939,19.4678903 L32.2222939,19.4378903 L32.2646335,19.4541949 C31.9412771,19.3131138 31.6099884,19.1908754 31.2724382,19.088096 L31.3702939,19.1218903 L31.0902939,19.0368903 L31.2839515,19.088096 C30.9226728,18.9746999 30.5549081,18.8830108 30.1826352,18.8135219 L30.2772939,18.8348903 L30.0442939,18.7868903 L29.8542939,18.7508903 L29.2252939,18.6688903 L29.0098438,18.6471017 C28.7132366,18.6231877 28.4156155,18.611785 28.1177791,18.6129358 C27.2071248,18.6129358 26.3207076,18.7284239 25.4712939,18.9460907 C27.4056862,9.29865728 31.4534026,0.999209177 32.95326,1.17771767 C34.1574289,1.36638368 35.2414752,1.89829929 36.0961499,2.69342823 L36.2562939,2.84889034 L36.1075353,2.69342823 L36.3278632,2.91649819 L36.3278632,2.91649819 L36.4042939,3.00289034 L36.2911094,2.88234954 L36.2572939,2.84989034 L36.4152939,3.01589034 Z" id="形状结合备份" fill="#0754AA"></path>
                        <g id="路径-2" transform="translate(10.682728, 0.000000)">
                            <mask id="mask-27" fill="white">
                                <use xlink:href="#path-26"></use>
                            </mask>
                            <use id="蒙版" fill="#9BF4FB" fill-rule="nonzero" xlink:href="#path-26"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>