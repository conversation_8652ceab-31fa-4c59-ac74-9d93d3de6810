<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-30.000000, -134.000000)">
            <g id="编组-17" transform="translate(0.000000, 60.000000)">
                <g id="模型管理" transform="translate(14.000000, 60.000000)">
                    <g id="模型管理-总览（未选中）" transform="translate(16.000000, 14.000000)">
                        <rect id="矩形" fill-opacity="0.01" fill="currentColor" fill-rule="nonzero" x="0" y="0" width="20" height="20"></rect>
                        <g id="编组-10" transform="translate(3.000000, 3.000000)" stroke="currentColor" stroke-linejoin="round" stroke-opacity="0.800482477" stroke-width="1.25">
                            <rect id="矩形" x="0" y="0" width="5.6" height="5.5308642" rx="0.833333333"></rect>
                            <rect id="矩形" x="0" y="8.2962963" width="5.6" height="5.5308642" rx="0.833333333"></rect>
                            <rect id="矩形" x="8.4" y="0" width="5.6" height="5.5308642" rx="0.833333333"></rect>
                            <line x1="8.4" y1="8.2962963" x2="14" y2="8.2962963" id="路径" stroke-linecap="round"></line>
                            <line x1="11.2" y1="11.0617284" x2="14" y2="11.0617284" id="路径" stroke-linecap="round"></line>
                            <line x1="8.4" y1="13.8271605" x2="14" y2="13.8271605" id="路径" stroke-linecap="round"></line>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>