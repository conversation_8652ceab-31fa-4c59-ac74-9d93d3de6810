$nancalui-animation-duration-slow: var(--nancalui-animation-duration-slow, 300ms);
$nancalui-animation-duration-base: var(--nancalui-animation-duration-base, 200ms);
$nancalui-animation-duration-fast: var(--nancalui-animation-duration-fast, 100ms);

$nancalui-animation-ease-in: var(--nancalui-animation-ease-in, cubic-bezier(0.5, 0, 0.84, 0.25));
$nancalui-animation-ease-out: var(--nancalui-animation-ease-out, cubic-bezier(0.16, 0.75, 0.5, 1));
$nancalui-animation-ease-in-out: var(--nancalui-animation-ease-in-out, cubic-bezier(0.5, 0.05, 0.5, 0.95));
$nancalui-animation-ease-in-out-smooth: var(--nancalui-animation-ease-in-out-smooth, cubic-bezier(0.645, 0.045, 0.355, 1));
$nancalui-animation-linear: var(--nancalui-animation-linear, cubic-bezier(0, 0, 1, 1));
