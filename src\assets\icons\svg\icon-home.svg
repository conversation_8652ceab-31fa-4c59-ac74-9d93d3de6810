<?xml version="1.0" encoding="UTF-8"?>
<svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>首页</title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-338.000000, -86.000000)">
            <g id="左侧导航备份-2" transform="translate(320.000000, 0.000000)">
                <g id="一级菜单" transform="translate(0.000000, 70.000000)">
                    <g id="首页icon" transform="translate(18.000000, 16.000000)">
                        <rect id="矩形" fill-rule="nonzero" x="0" y="0" width="18" height="18"></rect>
                        <g id="编组" transform="translate(1.600000, 1.600000)" stroke="currentColor" stroke-linejoin="round" stroke-width="1.3">
                            <polygon id="路径" points="14.8 14.8 14.8 5.92 7.4 0 0 5.92 0 14.8 4.44 14.8 4.44 8.14 10.36 8.14 10.36 14.8"></polygon>
                            <line x1="7.4" y1="14.8" x2="7.4" y2="11.1" id="路径" stroke-linecap="round"></line>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>