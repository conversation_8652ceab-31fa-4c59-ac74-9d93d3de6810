<template>
  <n-modal draggable :title="title" v-model="entityShow" width="1100px" :before-close="beforeClose">
    <section class="content-table">
      <n-tabs v-model="activeName" type="card" :before-change="handleClick">
        <n-tab title="实体" id="first">
          <Entity ref="entity" :cell="cell" :subjectList="subjectList" />
        </n-tab>
        <n-tab title="属性" id="second">
          <Attribute ref="attribute" :cell="cell" />
        </n-tab>
        <n-tab v-if="cell?.data.type !== 'BLOOD'" title="关系" id="third">
          <Relation ref="relation" :cell="cell" />
        </n-tab>
        <n-tab v-if="cell?.data.type !== 'BLOOD'" title="标识符组" id="fourth">
          <Identifier ref="identifier" :cell="cell" :activeName="activeName" />
        </n-tab>
      </n-tabs>
    </section>
    <template #footer v-if="!disabled">
      <n-modal-footer>
        <n-button variant="solid" color="primary" @click="onSubmit(true)">确 定</n-button>
        <n-button @click="closePop(true)">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script>
  import Entity from './Entity.vue'
  import Identifier from './Identifier.vue'
  import Attribute from './Attribute.vue'
  import Relation from './Relation.vue'
  import FlowGraph from '../../graph'
  import { reName } from '../../utils/index.js'

  export default {
    components: {
      Entity,
      Attribute,
      Relation,
      Identifier,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['cell', 'graphList', 'subjectGuid', 'isShow'],
    data() {
      return {
        title: '实体编辑', // 弹窗标题
        entityShow: false,
        activeName: 'first',
        preSubject: '',
        isCancle: false,
      }
    },
    computed: {
      disabled() {
        return this.cellData?.disableMove || this.cellData?.type === 'BLOOD'
      },
      cells() {
        const { graph } = FlowGraph
        const cells = graph?.toJSON().cells.filter((cell) => cell.id !== this.cell.id)
        return cells
      },
      cellData() {
        return this.cell?.data
      },
      subjectList() {
        return this.graphList?.map((item) => {
          return {
            subject: item.subjectName,
            guid: item.subjectGuid,
          }
        })
      },
    },
    watch: {
      activeName(val) {
        if (val === 'second') {
          this.$refs.attribute.initData()
        }
        if (val === 'third') {
          this.$refs.relation.relationSearch()
        }
        if (val === 'fourth') {
          this.$refs.identifier.initData()
        }
      },
      isShow(val) {
        this.entityShow = val
        if (!val) {
          this.activeName = 'first'
        }
      },
    },
    mounted() {},
    methods: {
      handleClick(activeName, oldActiveName) {
        if (!this.disabled) {
          this.onSubmit()
        }
        return true
      },
      async onSubmit(isClose) {
        if (this.activeName === 'first') {
          this.updateEntity(isClose)
        } else if (this.activeName === 'second') {
          this.updateAttr(isClose)
        } else if (isClose) {
          this.closePop(false)
        }
      },
      // 更新实体
      async updateEntity(isClose) {
        try {
          let params = this.$refs.entity.$data.data
          if (!params.name) {
            this.$message.error('实体名不能为空！')
            return
          }
          // 校验名称重复
          const allJsons = []
          this.graphList?.forEach((item) => {
            if (item?.json) {
              allJsons.push(...item?.json)
            }
          })
          let hasRepeat = false
          let tableRepeat = false
          allJsons.forEach((cell) => {
            // 引用的共享实体不判断重名
            if (
              cell?.data?.entity?.name === params.name &&
              this.cell.id !== cell.id &&
              !cell?.data?.isShare
            ) {
              hasRepeat = true
            }
            if (
              cell.data?.entity?.tabName &&
              cell.data?.entity?.tabName === params.tabName &&
              this.cell.id !== cell?.id &&
              !cell?.data?.isShare
            ) {
              tableRepeat = true
            }
          })
          if (hasRepeat) {
            this.$message.warning('实体名不能重复！已自动进行重命名。')
            const list = allJsons
              ?.filter((item) => item.id !== this.cell.id)
              .map((item) => {
                return {
                  name: item.data?.entity?.name,
                }
              })
            params.name = reName(list, 'name', params.name)
          }
          if (tableRepeat) {
            this.$message.warning('表名不能重复！已自动进行重命名。')
            const list = allJsons
              ?.filter((item) => item.id !== this.cell.id)
              ?.map((item) => {
                return {
                  name: item.data?.entity?.tabName,
                }
              })
            params.tabName = reName(list, 'name', params.tabName)
          }
          let data = { ...this.cell?.data }
          // 记录修改前主题域
          const subject = this.subjectList?.find((item) => item.subject === data.entity?.subject)
          if (subject) {
            this.preSubject = subject.guid
          } else {
            this.preSubject = data.entity?.subject
          }
          // 如果引用变成原生，删除引用标记
          if (this.preSubject !== this.subjectGuid && this.subjectGuid === params.subject) {
            delete data.isQuote
          }
          // 如果原生变成引用，添加引用标记
          if (this.preSubject === this.subjectGuid && this.subjectGuid !== params.subject) {
            data.isQuote = true
          }
          // 删除新增标记
          delete params.isAdd
          data.entity = params
          data.subjectGuid = params.subject
          // 防止数据改变不触发
          this.cell.setData(data, { overwrite: true })
          // 保存成功，去掉标记边框
          const attrs = this.cell.getAttrs()
          if (attrs.body.strokeWidth) {
            this.cell.setAttrs({
              body: {
                strokeWidth: 0,
              },
            })
          }
          // 替换关联的关系线实体名
          const parentEdges = this.cells?.filter(
            (cell) => this.cell.id === cell.data?.parentEntityId && cell.shape === 'edge',
          )
          const subEdges = this.cells?.filter(
            (cell) => this.cell.id === cell.data?.subEntityId && cell.shape === 'edge',
          )
          const { graph } = FlowGraph
          if (parentEdges) {
            parentEdges.forEach((item) => {
              const cell = graph?.getCellById({ id: item.id })
              cell.setData({ ...item.data, parentEntityName: params.name }, { overwrite: true })
            })
          }
          if (parentEdges) {
            subEdges.forEach((item) => {
              const cell = graph?.getCellById({ id: item.id })
              cell.setData({ ...item.data, subEntityName: params.name }, { overwrite: true })
            })
          }
          if (!hasRepeat) {
            this.$message({
              type: 'success',
              message: '实体暂存成功!',
            })
          }
          if (isClose) {
            this.closePop(false)
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 更新属性
      async updateAttr(isClose) {
        let params = this.$refs.attribute.$data.oldData
        let data = { ...this.cell?.data }
        const nameEmpty = params.findIndex((item) => !item.name) > -1
        if (nameEmpty) {
          this.$message.error('属性名不能为空')
          return
        }
        data.attr = params
        // 防止数据改变不触发
        this.cell.setData(data, { overwrite: true })
        this.$message({
          type: 'success',
          message: '属性暂存成功!',
        })
        if (isClose) {
          this.closePop(false)
        }
      },
      beforeClose(done) {
        const data = { ...this.cell?.data }
        // 如果修改了主题域或者修改了引用实体数据需要进行额外操作
        const curSubject = data.entity?.subject
        if (this.subjectGuid !== curSubject || this.preSubject !== curSubject) {
          this.$emit('originChange', this.preSubject)
        }
        // 新增取消直接删除节点
        if (this.$refs.entity.$data.data.isAdd && this.isCancle) {
          const { graph } = FlowGraph
          graph.removeNode(this.cell)
        }
        if (this.entityShow) {
          this.closePop()
        }
        done()
      },
      // 关闭弹窗
      closePop(isCancle = true) {
        this.entityShow = false
        this.isCancle = isCancle
        this.$emit('closepop')
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  :deep(.table) {
    h3 {
      line-height: 28px;
      background: rgba(37, 123, 255, 0.08);
      font-size: 14px;
      margin: 0;
      padding-left: 10px;
    }
    .table-box {
      display: grid;
      grid-template-columns: 100px auto;
      border-bottom: 1px solid #e1e1e1;
    }
    .table-node {
      height: 32px;
      line-height: 30px;
      border-top: 1px solid #e1e1e1;
      padding: 0 10px;
      box-sizing: border-box;
      color: #333333;
      font-size: 12px;

      &:nth-child(odd) {
        border-right: 1px solid #e1e1e1;
      }
      input {
        width: 100%;
        height: 24px;
        border: none;
        outline: none;
      }
      .nancalui-input__wrapper,
      .nancalui-select__selection {
        border: none;
        box-shadow: none;
      }
      &-select {
        border-color: blue;
      }
    }
  }
  :deep(.el-dialog) {
    width: 1020px;

    .el-tabs {
      &__header {
        padding: 0;
        border: none;
      }
      &__item {
        border: 1px solid #cfcfcf !important;
        margin-right: 4px;
        background: #f4f4f4;

        &.is-active {
          background: #fff;
          border-radius: 2px 2px 0px 0px;
          color: $themeFontColor !important;
          border-bottom-color: #fff !important;
        }
      }
      &__content {
        border: 1px solid #cfcfcf;
      }
    }
  }
  h3 {
    color: #999;
    font-size: 14px;
  }
</style>
