import Layout from '@/layout'
// 数据治理
export default [
  {
    path: '/workbench',
    component: Layout,
    redirect: '/workbench/',
    name: 'workbench',
    code: 'workbench',
    meta: {
      title: '数据治理',
      icon: 'icon-dev',
      parentRouterName: 'workbenchManage',
      code: 'workbench',
    },
    children: [
      // 数据治理场景
      {
        path: 'dataGovernanceScene',
        name: 'dataGovernanceScene',
        code: 'dataGovernanceScene',
        redirect: '/workbench/dataGovernanceScene/list',
        component: () => import('@/views/workbench/data-governance-scene/index'),
        meta: {
          title: '数据治理场景',
          code: 'dataGovernanceScene',
          parentRouterName: 'workbenchManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            name: 'dataGovernanceSceneList',
            component: () => import('@/views/workbench/data-governance-scene/list'),
            meta: {
              title: '数据治理场景',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
              code: 'dataGovernanceScene',
            },
          },
          {
            path: 'detail',
            name: 'dataGovernanceSceneDetail',
            component: () => import('@/views/workbench/data-governance-scene/detail'),
            meta: {
              title: '数据治理场景详情',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
            },
          },
        ],
      },

      // 个人数据治理
      {
        path: 'personalDataGovernance',
        name: 'personalDataGovernance',
        code: 'personalDataGovernance',
        component: () => import('@/views/workbench/personalDataGovernance/index'),
        meta: {
          title: '个人数据治理',
          code: 'personalDataGovernance',
          parentRouterName: 'workbenchManage',
        },
        children: [
          // 个人基本信息
          {
            path: 'personalBasicInformation',
            name: 'personalBasicInformation',
            code: 'personalBasicInformation',
            component: () =>
              import('@/views/workbench/personalDataGovernance/personalBasicInformation/index'),
            meta: {
              title: '个人基本信息',
              code: 'personalBasicInformation',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
            },
          },
          // 个人知识产权
          {
            path: 'personalIntellectualProperty',
            name: 'personalIntellectualProperty',
            code: 'personalIntellectualProperty',
            component: () =>
              import('@/views/workbench/personalDataGovernance/personalIntellectualProperty/index'),
            meta: {
              title: '个人知识产权',
              code: 'personalIntellectualProperty',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
            },
          },
        ],
      },

      // 数据标注
      {
        path: 'dataAnnotation',
        name: 'dataAnnotation',
        code: 'dataAnnotation',
        redirect: '/workbench/dataAnnotation/taskOverview',
        component: () => import('@/views/workbench/dataAnnotation/index'),
        meta: { title: '数据标注', code: 'dataAnnotation', parentRouterName: 'workbenchManage' },
        children: [
          // 任务总览
          {
            path: 'taskOverview',
            name: 'taskOverview',
            code: 'taskOverview',
            redirect: '/workbench/dataAnnotation/taskOverview/taskOverviewOldList',
            component: () => import('@/views/workbench/dataAnnotation/taskOverview/index'),
            meta: {
              title: '任务总览',
              code: 'taskOverview',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
              parentCode: 'dataAnnotation',
            },
            children: [
              {
                path: 'taskOverviewOldList',
                name: 'taskOverviewOldList',
                component: () => import('@/views/workbench/dataAnnotation/taskOverview/list'),
                meta: {
                  title: '任务总览',
                  parentRouterName: 'workbenchManage',
                  unShowProject: true,
                  parentCode: 'dataAnnotation',
                  code: 'taskOverview',
                },
              },
              // 任务管理
              {
                path: 'taskManagement',
                name: 'taskManagement',
                component: () => import('@/views/workbench/dataAnnotation/taskManagement/list'),
                meta: {
                  title: '任务管理',
                  unShowProject: true,
                  parentRouterName: 'workbenchManage',
                  parentCode: 'dataAnnotation',
                },
              },
              // 任务详情
              {
                path: 'taskDetail',
                name: 'taskDetail',
                component: () => import('@/views/workbench/dataAnnotation/taskOverview/detail'),
                meta: {
                  title: '任务详情',
                  unShowProject: true,
                  parentRouterName: 'workbenchManage',
                  parentCode: 'dataAnnotation',
                },
              },
            ],
          },

          // 标注任务
          {
            path: 'annotationTask',
            name: 'annotationTask',
            code: 'annotationTask',
            component: () => import('@/views/workbench/dataAnnotation/annotationTask/index'),
            meta: {
              title: '标注任务',
              code: 'annotationTask',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
            },
          },

          // 审核任务
          {
            path: 'auditTask',
            name: 'auditTask',
            code: 'auditTask',
            component: () => import('@/views/workbench/dataAnnotation/auditTask/index'),
            meta: {
              title: '审核任务',
              code: 'auditTask',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
            },
          },
          // 标注工具
          {
            path: 'annotationTool',
            name: 'annotationTool',
            code: 'annotationTool',
            component: () => import('@/views/workbench/dataAnnotation/annotationTool/index'),
            meta: {
              title: '标注工具',
              code: 'annotationTool',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
              parentCode: 'annotationOverview',
            },
          },
          // 团队管理
          {
            path: 'teamManagement',
            name: 'teamManagement',
            code: 'teamManagement',
            component: () => import('@/views/workbench/dataAnnotation/teamManagement/index'),
            meta: {
              title: '团队管理',
              code: 'teamManagement',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
              parentCode: 'annotationOverview',
            },
          },
        ],
      },

      // 标注总览
      {
        path: 'annotationOverview',
        name: 'annotationOverview',
        code: 'annotationOverview',
        redirect: '/workbench/annotationOverview/taskOverviewList',
        component: () => import('@/views/workbench/dataAnnotation/index'),
        meta: {
          title: '标注总览',
          code: 'annotationOverview',
          parentRouterName: 'workbenchManage',
          unShowProject: true,
          parentCode: 'annotationOverview',
        },
        children: [
          // 任务总览
          {
            path: 'taskOverviewList',
            name: 'taskOverviewList',
            component: () => import('@/views/workbench/dataAnnotation/taskOverview/list-2'),
            meta: {
              title: '任务总览',
              parentRouterName: 'workbenchManage',
              unShowProject: true,
              parentCode: 'annotationOverview',
              code: 'annotationOverview',
            },
          },
          // 任务详情
          {
            path: 'taskDetail',
            name: 'taskDetail',
            component: () => import('@/views/workbench/dataAnnotation/taskOverview/detail'),
            meta: {
              title: '任务详情',
              unShowProject: true,
              parentRouterName: 'workbenchManage',
              parentCode: 'annotationOverview',
            },
            children: [
              // 在线标注
              {
                path: 'onlineStandard',
                name: 'onlineStandard',
                component: () =>
                  import('@/views/workbench/dataAnnotation/taskOverview/onlineStandard.vue'),
                meta: {
                  title: '在线标注',
                  unShowProject: true,
                  parentRouterName: 'workbenchManage',
                  parentCode: 'annotationOverview',
                },
              },
            ],
          },
          // 预览标注
          {
            path: 'previewStandard',
            name: 'previewStandard',
            component: () =>
              import('@/views/workbench/dataAnnotation/taskOverview/previewStandard.vue'),
            meta: {
              title: '预览标注',
              unShowProject: true,
              parentRouterName: 'workbenchManage',
              parentCode: 'annotationOverview',
            },
          },
        ],
      },
    ],
  },
]
