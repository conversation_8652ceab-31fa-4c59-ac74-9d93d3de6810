<template>
  <div class="module-name">
    <span
      class="module-name-border"
      :style="info.borColor ? 'background-color:' + info.borColor : ''"
    ></span>
    <span
      class="module-name-value"
      :style="{
        'font-size': info.size ? info.size + 'px' : '',
        color: info.color ? info.color : '',
      }"
      >{{ info.name }}</span
    >
  </div>
</template>
<script>
  export default {
    props: ['info'],
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .module-name {
    position: relative;
    padding-left: 10px;

    &-border {
      width: 4px;
      height: 16px;
      background-color: $themeBlue;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

    &-value {
      color: #000;
      font-size: 16px;
      font-weight: bold;
    }
  }
</style>
