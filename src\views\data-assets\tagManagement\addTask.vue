<template>
  <div class="container out-box">
    <div class="add-marking-box">
      <moduleName :info="{ name: state.detailId ? ' 打标' : '打标' }" />
      <div class="add-marking-tabs-box">
        <n-tabs v-model="state.activeName" @active-tab-change="tabChange">
          <n-tab title="资产打标" id="TABLE" />
          <n-tab title="列打标" id="COLUMN" />
          <!-- <n-tab title="行打标" id="TABLE_DATA" /> -->
        </n-tabs>
      </div>
      <div class="add-marking-content-box" :key="state.key">
        <div v-show="state.activeName === 'TABLE'" class="content-box-list">
          <assetsMarking
            ref="assetsMarkingDom"
            @showTagDialog="showTagDialog"
            @delSingerMarking="delSingerMarking"
          />
        </div>
        <div v-show="state.activeName === 'COLUMN'" class="content-box-list">
          <columnMarking
            ref="columnMarkingDom"
            @showTagDialog="showTagDialog"
            @delSingerMarking="delSingerMarking"
          />
        </div>
        <!-- <div v-show="state.activeName === 'TABLE_DATA'" class="content-box-list">
          <lineMarking
            ref="lineMarkingDom"
            @showTagDialog="showTagDialog"
            @delSingerMarking="delSingerMarking"
          />
        </div> -->
      </div>
    </div>
    <div class="fixed-bottom">
      <n-button color="primary" size="sm" variant="solid" @click.prevent="goBack">返回</n-button>
    </div>
    <!-- 添加标签弹框 -->

    <n-modal
      title="添加标签"
      bodyClass="middleDialog"
      v-model="state.dialogVisible"
      :close-on-click-overlay="false"
      :draggable="false"
      :append-to-body="false"
      width="1020px"
      class="has-top-padding"
      :before-close="closeDialog"
    >
      <div class="dialog-content-box">
        <PublicLeftTree
          ref="publicTree"
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          @treeCheckNode="treeCheckNode"
        />
        <n-transfer
          class="ssss"
          v-model="state.activeTags"
          :titles="['选项合集', '选项合集']"
          :format="{
            noChecked: `（0 项）`,
            hasChecked: '（${checked} 项）',
          }"
          :filter="filterMethod"
          filter-placeholder="搜索/过滤标签"
          :data="state.allTags"
          @left-check-change="leftCheckChange"
          @change="checkChange"
          :render-content="renderContent"
          showSearch
        >
          <template #item="{ label, value }">
            <span :class="[optionClass(value)]">{{ label }}</span>
          </template>
        </n-transfer>
      </div>

      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button
            color="primary"
            size="sm"
            variant="solid"
            :loading="state.submiting"
            @click.prevent="save()"
            >保 存</n-button
          >
          <n-button size="sm" @click.prevent="closeDialog">取 消</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import assetsMarking from './components/assets-marking'
  import lineMarking from './components/line-marking'
  import columnMarking from './components/column-marking'
  export default {
    name: '',
    components: { assetsMarking, columnMarking, lineMarking },
    props: {},
    setup() {
      //按钮权限
      const assetsMarkingDom = ref()
      const columnMarkingDom = ref()
      const lineMarkingDom = ref()

      const router = useRouter()
      const state = reactive({
        key: 1,
        activeName: 'TABLE',
        dialogVisible: false,
        activeTags: [],
        showTagsIds: [], //记录当前目录展示的ids
        showAllTagsIds: [], //记录全量展示的ids
        allTags: [],
        treeData: [],
        treeAttrData: {
          showCheckbox: false,
          showControl: true,
          showLeftIcon: true,
          parentControl: '',
          childControl: '',
        },
        submiting: false,
        groupId: null, //标签组分页
        queryData: {},
        activeAssetsList: null, //勾选中的打标行数据
        filterDataMarkingData: null, //后端批量打标-搜索条件
      })

      const methods = {
        //打标弹框展示
        showTagDialog(data) {
          state.filterDataMarkingData = null
          state.activeTags = []

          if (state.activeName === 'TABLE') {
            let { tagInfos } = data
            if (tagInfos) {
              state.activeTags = tagInfos.map((list) => {
                return list.id
              })
            }
          } else {
            if (data?.filterDataMarkingData) {
              state.filterDataMarkingData = data.filterDataMarkingData
            } else {
              state.activeAssetsList = data.activeAssetsList
              let _tags = []
              if (state.activeAssetsList?.length === 1) {
                if (data.activeAssetsList[0].tagInfos?.length) {
                  _tags = data.activeAssetsList[0].tagInfos.map((list) => {
                    return list.id
                  })
                }

                state.activeTags = _tags
              }
            }
          }
          state.dialogVisible = true
          nextTick(() => {
            methods.jsGetDom()
          })
        },
        //删除单个标签
        delSingerMarking(data) {
          state.filterDataMarkingData = null
          if (state.activeName === 'TABLE') {
            let { tagInfos } = data
            state.activeTags = tagInfos.map((list) => {
              return list.id
            })
            methods.save(true)
          } else {
            state.activeAssetsList = data
            methods.save(true)
          }
        },
        //获取标签树
        getTreeData() {
          api.assets.getTagsTree({ type: 'TAG' }).then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                state.options = data
                data.forEach((item) => {
                  item.type = 'ROOT'
                })
                state.treeData = data
              }
            }
          })
        },
        //批量打标

        // 保存打标标签
        save(isEdit = false) {
          let _data = []
          let interface_url = 'markBatchSave'
          let _message = '添加标签成功'
          if (isEdit) {
            _message = '修改标签成功'
          }
          //资产打标
          if (state.activeName === 'TABLE') {
            _data.push({
              modelId: state.queryData.modelId,
              modelName: state.queryData.modelName,
              tags: state.activeTags,
              type: state.activeName,
            })
          }

          if (state.activeName === 'COLUMN') {
            state.activeAssetsList.forEach((item) => {
              _data.push({
                metadataCnName: item.cnName,
                metadataId: item.id,
                metadataName: item.name,
                modelId: state.queryData.modelId,
                modelName: state.queryData.modelName,
                type: state.activeName,
                tags: isEdit ? item.newTagInfos : methods.tagAddAndDuplication(item),
              })
            })
          }
          if (state.activeName === 'TABLE_DATA') {
            if (state.filterDataMarkingData) {
              //按照筛选条件后端批量打标
              interface_url = 'markTagsWithCondition'
              state.filterDataMarkingData.tags = state.activeTags
              _data = state.filterDataMarkingData
            } else {
              //勾选数据批量/单个打标
              state.activeAssetsList.forEach((item) => {
                _data.push({
                  assetsContent: item.assetsContent,
                  assetsIndex: item.assetsIndex,
                  modelId: state.queryData.modelId,
                  modelName: state.queryData.modelName,
                  type: state.activeName,
                  tags: isEdit ? item.newTagInfos : methods.tagAddAndDuplication(item),
                })
              })
            }
          }
          state.submiting = true
          api.assets[interface_url](_data)
            .then((res) => {
              let { success } = res
              state.submiting = false
              if (success) {
                state.key++
                ElNotification({
                  title: '提示',
                  message: _message,
                  type: 'success',
                })
                state.dialogVisible = false
                state.activeTags = []
                state.showTagsIds = state.showAllTagsIds
                methods.initChild('taggedInit')
              }
            })
            .catch(() => {
              state.submiting = false
            })
        },
        //初始化数据
        initChild(data = null) {
          let _dom = null
          if (state.activeName === 'TABLE') {
            _dom = assetsMarkingDom.value
          } else if (state.activeName === 'COLUMN') {
            _dom = columnMarkingDom.value
          } else {
            _dom = lineMarkingDom.value
          }
          _dom.init(data)
        },

        // 标签组合和去重
        tagAddAndDuplication(item) {
          let _tags = []

          if (state.activeTags?.length) {
            if (state.activeAssetsList.length === 1) {
              _tags = []
            } else {
              if (item.tagInfos?.length) {
                //有打标信息
                _tags = item.tagInfos.map((list) => {
                  return list.id
                })
              } else {
                //无标签
                _tags = []
              }
            }
          } else {
            _tags = []
          }

          return Array.from(new Set([..._tags, ...state.activeTags]))
        },

        goBack() {
          router.go(-1)
        },
        checkChange() {
          nextTick(() => {
            methods.jsGetDom()
          })
        },
        //左侧穿梭框全选
        leftCheckChange(data) {
          let _data = JSON.parse(JSON.stringify(data))
          if (data?.length) {
            //过滤掉隐藏的
            _data.forEach((id) => {
              if (!state.showTagsIds.includes(id)) {
                //没有的移除勾选
                let getLocation = data.indexOf(id)
                data.splice(getLocation, 1)
              }
            })
          }
          nextTick(() => {
            methods.jsGetDom()
          })
        },
        //穿梭框搜索过滤
        filterMethod(item, query) {
          return item.label.toLowerCase().includes(query.toLowerCase())
        },
        // 点击选中标签树行
        treeCheckNode(data) {
          if (!data) return
          let { checkItem } = data
          state.groupId = checkItem.id
          // state.activeTags = []
          methods.getThisTypeTags()
        },
        // 获取标签内容
        getThisTypeTags() {
          let data = {
            condition: {
              groupId: state.groupId, // 标签组id
            },
            pageNum: 1,
            pageSize: 100,
          }
          api.assets.getTagManagementList(data).then((res) => {
            let { success, data } = res
            if (success) {
              data.list.forEach((item) => {
                item.label = item.name
                item.key = item.id
                item.value = item.id
              })

              state.showTagsIds = data.list.map((item) => {
                return item.id
              })
              if (state.groupId === null) {
                //记录全量展示的ids
                state.showAllTagsIds = data.list.map((item) => {
                  return item.id
                })
                state.allTags = data.list
              }

              nextTick(() => {
                methods.jsGetDom()
              })
            }
          })
        },

        //关闭打标弹框
        closeDialog() {
          state.dialogVisible = false
          state.activeTags = []
          state.showTagsIds = state.showAllTagsIds
        },
        tabChange(name) {
          state.activeName = name
        },
        // 动态设置class名字
        optionClass(option) {
          let className = ''
          //标签分组，disabled掉其他分组的
          nextTick(() => {})

          if (state.showTagsIds.includes(option)) {
            className = 'nancalui-checkbox__label-text-child show'
          } else {
            className = 'nancalui-checkbox__label-text-child hide'
          }

          return className
        },
        renderContent(h, option) {
          return h('span', { style: {}, class: [methods.optionClass(option)] }, [option.label])
        },
        // js控制显示隐藏
        jsGetDom() {
          //check 勾选状态触发
          let dom = document.querySelectorAll('.nancalui-transfer-view')[0]
          if (dom) {
            let dom2 = dom.querySelectorAll('.nancalui-transfer-item')
            dom2.forEach((nancal) => {
              let _nancal = nancal.querySelectorAll('.nancalui-checkbox__label-text-child')[0]

              if (_nancal.className.search('hide') !== -1) {
                nancal.setAttribute('class', 'nancalui-transfer-item transfer-checkbox hide')
              } else {
                nancal.setAttribute('class', 'nancalui-transfer-item transfer-checkbox')
              }
            })
          }
        },
      }
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        methods.getTreeData()
        methods.getThisTypeTags()
      })

      return {
        state,
        assetsMarkingDom,
        columnMarkingDom,
        lineMarkingDom,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .out-box {
    .fixed-bottom {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 60px;
      padding: 17px 30px;
      text-align: center;
      background-color: #fff;
      border-radius: 8px 8px 0 0;
    }
    .add-marking-box {
      height: calc(100% - 60px);
      padding: 16px 30px;
      background-color: #fff;
      border-radius: 4px;

      .add-marking-tabs-box {
        margin-top: 10px;
        padding-left: 10px;
      }
      .add-marking-content-box {
        padding-left: 10px;
      }
    }
    :deep(.middleDialog) {
      .dialog-content-box {
        display: flex;
        justify-content: center;
        height: calc(70vh - 130px);
        .tree {
          border: 1px solid #ebeef5;
          border-right: 0;
          border-radius: 4px 0px 0px 4px;
        }
        .tree-search {
          padding: 10px 10px 0;
        }
      }

      .nancalui-transfer {
        display: flex;
        overflow: hidden;
        .nancalui-transfer-view {
          width: 320px;
          height: 100%;
          border: 1px solid #ebeef5;
          .nancalui-transfer-view__header {
            height: 40px;
            line-height: 40px;
            .nancalui-transfer-view__header-title {
              display: flex;
              justify-content: space-between;
            }
          }
          .nancalui-transfer-view__search {
            .nancalui-input__wrapper {
              padding: 1px 0 1px 10px;
            }
            .nancalui-input-slot__suffix {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 30px;
              height: 30px;
              background: #447dfd;
              border: 1px solid #ffffff;
              border-radius: 5px;
              > svg {
                margin-left: inherit;

                path {
                  stroke: #fff;
                }
              }
            }
          }

          &.nancalui-transfer__view-source {
            border-left: 0;
            border-radius: 0 4px 4px 0;
          }
          .nancalui-transfer-view__body {
            height: calc(100% - 90px) !important;
          }
          .nancalui-checkbox__group {
            .nancalui-transfer-item {
              height: 36px;
              margin-top: 8px;
              line-height: 36px;
              &.hide {
                height: 0;
                overflow: hidden;
                cursor: default;
                opacity: 0;
              }
            }
            .nancalui-checkbox .nancalui-checkbox__material {
              width: 16px;
              height: 16px;
              & > svg {
                width: 16px;
                height: 16px;
                border-radius: 2px;
              }
            }
          }
        }
        .nancalui-transfer__operations {
          .nancalui-button {
            width: 36px;
            height: 36px;
            .nancalui-button__icon-fix {
              .icon-collapse:before {
                font-size: 16px;
              }
            }
          }
          .nancalui-button + .nancalui-button {
            margin-left: 0px;
          }
        }
      }
    }
  }
</style>
