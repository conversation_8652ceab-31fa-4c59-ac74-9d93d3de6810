<template>
  <div class="tree-box">
    <el-tree v-bind="$attrs" node-key="id" ref="treeRef" class="filter-tree">
      <template #default="{ node, data }">
        <span
          class="custom-tree-node"
          :class="{
            'is-checked': $attrs.directoryIds.includes(data.id),
            'is-disabled':
              [$attrs.disabledId].includes(data.id) ||
              props.disableChecked?.(node) ||
              data.isDepartment,
          }"
          @click="checkedHandle(data, $event)"
        >
          <span class="text-label" :title="data.name">{{ data.name }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
  const treeRef = ref(null)
  const state = reactive({})
  const attrs = useAttrs()
  const {} = toRefs(state)
  const props = defineProps({
    disableChecked: {
      type: Function,
      default: () => false,
    },
  })
  const checkedHandle = (node, event) => {
    if (event.target?.className?.includes?.('text-label')) return
    if (event.target?.className?.includes?.('is-disabled')) return
    event.stopPropagation()
    const isChecked = attrs.directoryIds.includes(node.id)
    if (isChecked) {
      const index = attrs.directoryIds.indexOf(node.id)
      attrs.directoryIds.splice(index, 1)
      attrs.usreList.splice(index, 1)
    } else {
      attrs.directoryIds.push(node.id)
      attrs.usreList.push(node)
    }
  }
  // 按id递归过滤对应节点
  function filterNodeById(id, node) {
    if (node.id === id) {
      return [node]
    }
    if (node.children) {
      for (let i = 0; i < node.children.length; i++) {
        const child = node.children[i]
        const result = filterNodeById(id, child)
        if (result) {
          return result
        }
      }
    }
    return null
  }
  defineExpose({
    treeRef,
    name: '',
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/cf.scss';
  :deep(.nancalui-popper-trigger) {
    display: inline-block;
    height: 16px;
  }
  .tree-box {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 0px 8px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    overflow-y: auto;
  }
  .filter-tree {
    width: 100%;
    height: 100%;
    :deep(.el-tree-node__expand-icon) {
      padding: 4px;
      transform: rotate(0deg);
      > svg {
        display: none;
      }
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
    :deep(.expanded) {
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTkgMUgyLjk5OTg4QzEuODk1MzEgMSAwLjk5OTg3OCAxLjg5NTQzIDAuOTk5ODc4IDNWMTNDMC45OTk4NzggMTQuMTA0NiAxLjg5NTMxIDE1IDIuOTk5ODggMTVIMTIuOTk5OUMxNC4xMDQ0IDE1IDE0Ljk5OTkgMTQuMTA0NiAxNC45OTk5IDEzVjNDMTQuOTk5OSAxLjg5NTQzIDE0LjEwNDQgMSAxMi45OTk5IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTg4IDdINS45OTk4OEM1LjQ0NzU5IDcgNC45OTk4OCA3LjQ0NzcyIDQuOTk5ODggOEM0Ljk5OTg4IDguNTUyMjggNS40NDc1OSA5IDUuOTk5ODggOUg5Ljk5OTg4QzEwLjU1MjIgOSAxMC45OTk5IDguNTUyMjggMTAuOTk5OSA4QzEwLjk5OTkgNy40NDc3MiAxMC41NTIyIDcgOS45OTk4OCA3WiIgZmlsbD0iIzU4NjQ3NSIvPgo8L3N2Zz4K');
      }
    }
    :deep(.el-tree-node__content) {
      height: 30px;
      gap: 4px;
    }
    :deep(.el-tree-node__content) {
      &:hover {
        border-radius: 2px;
        background: #ebf4ff;
        .btn-box {
          display: flex;
        }
      }
    }

    .custom-tree-node {
      display: flex;
      width: calc(100% - 24px);
      align-items: center;
      gap: 4px;
      .text-label {
        // 超出省略
        flex: 1;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8cmVjdCB4PSIxLjUiIHk9IjEuNSIgd2lkdGg9IjEzIiBoZWlnaHQ9IjEzIiByeD0iMS41IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSIjRENERkU2Ii8+Cjwvc3ZnPg==');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .btn-box {
        width: 82px;
        display: none;
        align-items: flex-end;
        gap: 4px;
        color: #606266;
        :hover {
          color: #1e89ff;
        }
      }
    }

    .is-checked {
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiByeD0iMiIgZmlsbD0iIzFFODlGRiIvPgo8cGF0aCBkPSJNMTEuMzM0NiA1LjVMNi43NTEzIDEwLjA4MzNMNC42Njc5NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
      }
    }
    .is-disabled {
      color: #c0c4cc;
      &.is-checked {
        &::before {
          content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiByeD0iMiIgZmlsbD0iI2Y1ZjdmYSIvPgo8cGF0aCBkPSJNMTEuMzMzNCA1LjVMNi43NTAwOCAxMC4wODMzTDQuNjY2NzUgOCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }
      }

      &::before {
        cursor: not-allowed;
      }
    }
  }
</style>
