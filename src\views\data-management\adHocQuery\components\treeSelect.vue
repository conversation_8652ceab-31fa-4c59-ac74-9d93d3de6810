<template>
  <div class="tree-box">
    <el-tree-select v-bind="$attrs" node-key="id" ref="treeRef" filterable class="filter-tree" />
  </div>
</template>

<script setup>
  const treeRef = ref(null)
  const state = reactive({})
  const {} = toRefs(state)
  const checkedHandle = (node, event) => {
    // if (event.target?.className?.includes?.('text-label')) event.stopPropagation()
  }
  defineExpose({
    treeRef,
    name: '',
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/cf.scss';
  :deep(.nancalui-popper-trigger) {
    display: inline-block;
    height: 16px;
  }
  .filter-tree {
    width: 100%;
    height: 100%;

    .custom-tree-node {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 4px;
      .text-label {
        // 超出省略
        flex: 1;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8cmVjdCB4PSIxLjUiIHk9IjEuNSIgd2lkdGg9IjEzIiBoZWlnaHQ9IjEzIiByeD0iMS41IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSIjRENERkU2Ii8+Cjwvc3ZnPg==');
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
      .btn-box {
        width: 82px;
        display: none;
        align-items: flex-end;
        gap: 4px;
        color: #606266;
        :hover {
          color: #1e89ff;
        }
      }
    }

    .is-checked {
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiByeD0iMiIgZmlsbD0iIzFFODlGRiIvPgo8cGF0aCBkPSJNMTEuMzM0NiA1LjVMNi43NTEzIDEwLjA4MzNMNC42Njc5NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
      }
    }
  }
</style>
<style lang="scss" scoped>
  .filter-tree {
    .el-tree-node__expand-icon {
      padding: 4px;
      transform: rotate(0deg);
      > svg {
        display: none;
      }
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
    .expanded {
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTkgMUgyLjk5OTg4QzEuODk1MzEgMSAwLjk5OTg3OCAxLjg5NTQzIDAuOTk5ODc4IDNWMTNDMC45OTk4NzggMTQuMTA0NiAxLjg5NTMxIDE1IDIuOTk5ODggMTVIMTIuOTk5OUMxNC4xMDQ0IDE1IDE0Ljk5OTkgMTQuMTA0NiAxNC45OTk5IDEzVjNDMTQuOTk5OSAxLjg5NTQzIDE0LjEwNDQgMSAxMi45OTk5IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTg4IDdINS45OTk4OEM1LjQ0NzU5IDcgNC45OTk4OCA3LjQ0NzcyIDQuOTk5ODggOEM0Ljk5OTg4IDguNTUyMjggNS40NDc1OSA5IDUuOTk5ODggOUg5Ljk5OTg4QzEwLjU1MjIgOSAxMC45OTk5IDguNTUyMjggMTAuOTk5OSA4QzEwLjk5OTkgNy40NDc3MiAxMC41NTIyIDcgOS45OTk4OCA3WiIgZmlsbD0iIzU4NjQ3NSIvPgo8L3N2Zz4K');
      }
    }
    .el-tree-node__content {
      height: 30px;
      gap: 4px;
      position: relative;
    }
    .el-tree-node__content {
      &:hover {
        border-radius: 2px;
        background: #ebf4ff;
        .btn-box {
          display: flex;
        }
      }
    }
    .el-select-dropdown__item {
      height: 30px !important;
      line-height: 30px !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      color: var(----, rgba(0, 0, 0, 0.9));
      font-style: normal;
      font-weight: 400;
      line-height: 22px !important;
      gap: 4px;

      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8cmVjdCB4PSIxLjUiIHk9IjEuNSIgd2lkdGg9IjEzIiBoZWlnaHQ9IjEzIiByeD0iMS41IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSIjRENERkU2Ii8+Cjwvc3ZnPg==');
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
    }
    .is-selected {
      &::before {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMSIgeT0iMSIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiByeD0iMiIgZmlsbD0iIzFFODlGRiIvPgo8cGF0aCBkPSJNMTEuMzM0NiA1LjVMNi43NTEzIDEwLjA4MzNMNC42Njc5NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
      }
    }
  }
</style>
