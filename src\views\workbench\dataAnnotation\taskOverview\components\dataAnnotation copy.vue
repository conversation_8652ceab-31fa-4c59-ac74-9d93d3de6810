<template>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="false"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
    >
      <template #pageTop>
        <div class="upload-box">
          <!-- 上传标注成果 -->
          <n-button class="upload-btn" color="primary" @click="uploadFile"> 上传标注成果 </n-button>
          <!-- 下载原始数据 -->
          <n-button class="upload-btn" color="primary" @click="downloadSource">
            下载原始数据
          </n-button>
          <!-- 在线标注 -->
          <n-button class="upload-btn" color="primary" @click="onlineAnnotation">
            在线标注
          </n-button>
        </div>
      </template>
      <template #editor="{ row }">
        <!-- 下载、删除 -->
        <n-button color="primary" variant="text" @click="downloadFn(row)">下载</n-button>
        <n-button color="primary" variant="text" @click="deleteFn(row)">删除</n-button>
      </template>
    </CfTable>
  </div>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import { workbenchModelList } from '@/api/dataManage.js'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { name: '文件名称', prop: 'fileName' },
      { name: '上传人', prop: 'uploader' },
      { name: '上传时间', prop: 'uploadTime' },
    ],
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    // workbenchModelList(data)
    //     .then((res) => {
    //         state.loading = false
    //         if (res.success) {
    //             state.tableData = res.data
    //             state.pageInfo.total = res.data.total
    //         }
    //     })
    //     .catch(() => {
    //         state.tableData = { list: [] }
    //         state.loading = false
    //     })
    state.tableData = {
      list: [
        {
          id: 1,
          fileName: '文件1',
          uploader: '张三',
          uploadTime: '2021-01-01',
        },
      ],
    }
    state.loading = false
  }
  const uploadFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'application/pdf' // 只允许上传PDF文件
    input.onchange = (e) => {
      const file = e.target.files[0] // 获取选择的文件
      ElMessage.success('上传成功')
    }
    input.click() // 模拟点击文件选择框
  }
  const downloadSource = () => {
    ElMessage.success('下载成功')
  }
  const onlineAnnotation = () => {
    router.push({ name: 'dataLabel' })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
