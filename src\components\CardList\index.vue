<template>
  <div class="cardList">
    <div ref="scrollRef" class="card-box scroll-bar-style" @scroll="scrollFn">
      <div
        v-for="(item, index) in cardData"
        :key="index"
        :class="item.showMore ? 'card active' : 'card'"
        @click.prevent="showMoreFn(index, false)"
      >
        <img class="card-img-bg" src="~@img/card/card-bg.png" alt="" />
        <img class="card-img-bg-hover" src="~@img/card/card-bg-hover.png" alt="" />
        <div :class="cardType === 1 ? 'card-header hasLogo' : 'card-header'">
          <div
            :class="
              cardType === 2 || cardType === 3 ? 'card-header-title hasStatus' : 'card-header-title'
            "
          >
            {{ item.cardTitle }}
          </div>
          <template v-if="!noHeaderStatus">
            <div v-if="item.cardStatus === 1" class="card-header-status green">
              <SvgIcon class="card-status-svg" icon="icon-card-send" />{{
                cardType === 5 ? '已确认' : '已发布'
              }}</div
            >
            <div v-else-if="item.cardStatus === 0" class="card-header-status gray"
              ><SvgIcon class="card-status-svg" icon="icon-card-down" />已下架</div
            >
            <div v-else-if="item.cardStatus === 2" class="card-header-status darkGrey"
              ><SvgIcon class="card-status-svg" icon="icon-card-await" />待开发</div
            >
            <div v-else-if="item.cardStatus === 3" class="card-header-status yellow"
              ><SvgIcon class="card-status-svg" icon="icon-card-audit" />审核中</div
            >
            <div v-else-if="item.cardStatus === 4" class="card-header-status red"
              ><SvgIcon class="card-status-svg" icon="icon-card-fail" />审批不通过
              <div
                :class="item.hasHover ? 'red-tips' : 'red-tips tips-animation'"
                @mouseover="onmouseoverFn(index)"
              >
                <n-popover
                  class="card-tips-content"
                  :position="['bottom-end']"
                  align="start"
                  trigger="hover"
                >
                  <template #content>
                    <div class="red-tips-popover">{{ item.auditComments }}</div>
                  </template>
                  <div class="red-tips-desc"></div>
                </n-popover> </div
            ></div>
            <div v-else-if="item.cardStatus === 5" class="card-header-status green"
              ><SvgIcon class="card-status-svg" icon="icon-card-audit" />已发布</div
            >
            <div v-else-if="item.cardStatus === 6" class="card-header-status green"
              ><SvgIcon class="card-status-svg" icon="icon-card-send" />已发布
              <div
                :class="item.hasHover ? 'red-tips' : 'red-tips tips-animation'"
                @mouseover="onmouseoverFn(index)"
              >
                <n-popover
                  class="card-tips-content"
                  :position="['bottom-end']"
                  align="start"
                  trigger="hover"
                >
                  <template #content>
                    <div class="red-tips-popover">{{ item.auditComments }}</div>
                  </template>
                  <div class="red-tips-desc"></div>
                </n-popover> </div
            ></div>
            <div v-else-if="item.cardStatus === 7" class="card-header-status blue"
              ><SvgIcon class="card-status-svg" icon="icon-card-new" />{{
                cardType === 5 ? '待确认' : '待发布'
              }}</div
            >
          </template>
        </div>
        <!-- cardType 1 数据源  -->
        <div v-if="cardType === 1" class="card-type-list">
          <div class="card-date hasLogo">
            <SvgIcon class="card-date-svg" icon="create-by" />
            {{ item.cardUser }}
            <SvgIcon class="card-date-svg by" icon="create-time" />
            {{ item.cardDate }}
          </div>
          <img class="card-right-img" :src="item.cardLogo" />
          <div class="card-bg-box">
            <div class="card-content">
              <div class="card-content-name">
                <div class="circle"></div>
                <div class="card-content-name-left">
                  <img class="card-content-name-left-img" src="~@img/card/card-base.png" alt="" />
                  {{ item.cardMappingTitle }}
                </div>
                <template v-if="item.envType">
                  <div v-if="item.envType === 'TEST'" class="card-content-name-right green"
                    >开发环境</div
                  >
                  <div v-if="item.envType === 'OFFICIAL'" class="card-content-name-right blue"
                    >正式环境</div
                  >
                </template>
              </div>
              <div
                v-for="(val, ind) in item.cardMappingLabel"
                :key="ind"
                class="card-content-label"
              >
                <template v-if="val.type">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <!--                <template v-else-if="val.type === 'PUBLIC'">-->
                <!--                  <span class="card-content-label-name">{{ val.name }}：</span>-->
                <!--                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{-->
                <!--                    text-->
                <!--                  }}</span>-->
                <!--                </template>-->
              </div>
            </div>
            <!-- <div class="card-dashed">
              <div class="card-dashed-left"></div>
              <div class="card-dashed-center"></div>
              <div class="card-dashed-right"></div>
            </div> -->
            <div class="card-content">
              <div class="card-content-label">
                <div class="card-content-label-key">
                  <div class="circle"></div>
                  <img
                    class="card-content-label-key-img"
                    src="~@img/card/card-describe.png"
                    alt=""
                  />
                  描述信息
                </div>
                <n-popover
                  :position="['bottom-start']"
                  align="start"
                  trigger="hover"
                  :disabled="!item.description || item.description.length <= 50"
                >
                  <template #content>
                    <p class="card-description">{{ item.description }}</p>
                  </template>
                  <span class="card-content-label-desc">{{
                    item.description ? item.description : '暂无描述'
                  }}</span>
                </n-popover>
              </div>
            </div>
          </div>
          <div class="card-operation">
            <operationList
              :cardOperation="item.showOperation"
              :info="item"
              :cardType="cardType"
              @childOperation="childOperation"
            />
            <div
              v-if="item.cardOperation.length > 3"
              class="card-operation-more"
              @click.stop="showMoreFn(index, true)"
            >
              <n-popover :position="['top-start']" align="end" trigger="hover">
                <template #content>
                  <popoverList
                    :cardOperation="item.popoverOperation"
                    :info="item"
                    :cardType="cardType"
                    @childOperation="childOperation"
                  />
                </template>
                <SvgIcon class="icon-more" icon="more" />
              </n-popover>
            </div>
          </div>
        </div>
        <!-- 数据采集 -->
        <div v-if="cardType === 2" class="card-type-list">
          <div class="card-date">
            <SvgIcon class="card-date-svg" icon="create-by" />
            {{ item.cardUser }}
            <SvgIcon class="card-date-svg by" icon="create-time" />
            {{ item.cardDate }}
          </div>
          <div class="card-bg-box">
            <div v-if="item.cardMappingTitle" class="card-content">
              <div class="card-content-name">
                <div class="circle"></div>
                <div class="card-content-name-left">
                  <img
                    class="card-content-name-left-img collect"
                    src="~@img/card/card-collect.png"
                    alt=""
                  />
                  {{ item.cardMappingTitle }}
                </div>
                <template v-if="item.collectType">
                  <div v-if="item.schedule" class="card-content-name-right purple">自动调度</div>
                  <div v-else class="card-content-name-right yellow">手动调度</div>

                  <template v-if="item.envType">
                    <div v-if="item.envType === 'TEST'" class="card-content-name-right green"
                      >开发环境</div
                    >
                    <div v-if="item.envType === 'OFFICIAL'" class="card-content-name-right"
                      >正式环境</div
                    >
                    <div
                      v-if="item.collectRule === 'PERIODIC_FULL'"
                      class="card-content-name-right deep-blue"
                      >全量采集</div
                    >
                    <div
                      v-if="
                        item.collectRule !== 'PERIODIC_FULL' && item.collectType === 'STRUCTURE'
                      "
                      class="card-content-name-right deep-green"
                      >增量采集</div
                    >
                  </template>
                </template>
              </div>
              <div
                v-for="(val, ind) in item.cardMappingLabel"
                :key="ind"
                class="card-content-label"
              >
                <template v-if="val.type === 1">
                  <span
                    class="card-content-label-name"
                    :style="{ width: val.width ? val.width : 'atuo' }"
                    >{{ val.name }}：</span
                  >
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <template v-else-if="val.type === 2">
                  <span
                    class="card-content-label-name"
                    :style="{ width: val.width ? val.width : 'atuo' }"
                    >{{ val.name }}：</span
                  >
                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{
                    text
                  }}</span>
                </template>
              </div>
            </div>
            <!-- <div class="card-dashed">
              <div class="card-dashed-left"></div>
              <div class="card-dashed-center"></div>
              <div class="card-dashed-right"></div>
            </div> -->
            <!-- 描述信息 -->
            <div class="card-content">
              <div class="card-content-label">
                <div class="card-content-label-key">
                  <div class="circle"></div>
                  <img
                    class="card-content-label-key-img"
                    src="~@img/card/card-describe.png"
                    alt=""
                  />
                  {{ item.cardDescriptionTitle }}
                </div>
                <n-popover
                  :position="['bottom-start']"
                  align="start"
                  trigger="hover"
                  :disabled="!item.cardDescription.value || item.cardDescription.value.length <= 50"
                >
                  <template #content>
                    <p class="card-description">{{ item.cardDescription.value }}</p>
                  </template>
                  <span class="card-content-label-desc">{{
                    item.cardDescription.value ? item.cardDescription.value : '暂无描述'
                  }}</span>
                </n-popover>
              </div>
            </div>
          </div>

          <div class="card-operation">
            <operationList
              :cardOperation="item.showOperation"
              :info="item"
              :cardType="cardType"
              @childOperation="childOperation"
            />
            <div
              v-if="item.cardOperation.length > 3"
              class="card-operation-more"
              @click.stop="showMoreFn(index, true)"
            >
              <n-popover :position="['top-start']" align="end" trigger="hover">
                <template #content>
                  <popoverList
                    :cardOperation="item.popoverOperation"
                    :info="item"
                    :cardType="cardType"
                    @childOperation="childOperation"
                  />
                </template>
                <SvgIcon class="icon-more" icon="more" />
              </n-popover>
            </div>
          </div>
        </div>
        <!-- 离线作业 -->
        <div v-if="cardType === 3" class="card-type-list">
          <div class="card-date">
            <SvgIcon class="card-date-svg" icon="create-by" />
            {{ item.cardUser }}
            <SvgIcon class="card-date-svg by" icon="create-time" />
            {{ item.cardDate }}
          </div>
          <div class="card-bg-box">
            <div class="card-content">
              <div class="card-content-name has-option">
                <div class="circle"></div>
                <div class="card-content-name-left">
                  <img
                    class="card-content-name-left-img work"
                    src="~@img/card/card-dispatch.png"
                    alt=""
                  />
                  {{ item.cardContentTitle }}
                </div>
                <SvgIcon
                  v-if="item.cardStatus === 1 && !item.cardDispatchNoOption"
                  class="card-content-name-btn"
                  icon="icon-check"
                  @click.prevent="operationFn(item, 'cardDispatchFn')"
                />

                <SvgIcon
                  v-if="item.cardStatus !== 1 && !item.cardDispatchNoOption"
                  class="card-content-name-btn"
                  icon="icon-edit2"
                  @click.prevent="operationFn(item, 'cardDispatchFn')"
                />
              </div>
              <div
                v-for="(val, ind) in item.cardContentLabel"
                :key="ind"
                class="card-content-label"
              >
                <template v-if="val.type === 1">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <template v-else-if="val.type === 2">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{
                    text
                  }}</span>
                </template>
              </div>
            </div>
            <!-- <div class="card-dashed">
              <div class="card-dashed-left"></div>
              <div class="card-dashed-center"></div>
              <div class="card-dashed-right"></div>
            </div> -->
            <div class="card-content">
              <!-- 有重跑机制 -->

              <div
                v-if="item.hasRerun"
                class="card-content-name has-option"
                style="margin-top: 26px"
              >
                <div class="circle"></div>
                <div class="card-content-name-left">
                  <img
                    class="card-content-name-left-img work"
                    src="~@img/card/card-rerun.png"
                    alt=""
                  />
                  {{ item.cardRerunTitle }}
                </div>
                <SvgIcon
                  v-if="item.cardStatus === 1 && !item.cardRerunNoOption"
                  class="card-content-name-btn"
                  icon="icon-check"
                  @click.prevent="operationFn(item, 'cardRerunFn')"
                />

                <SvgIcon
                  v-if="item.cardStatus !== 1 && !item.cardRerunNoOption"
                  class="card-content-name-btn"
                  icon="icon-edit2"
                  @click.prevent="operationFn(item, 'cardRerunFn')"
                />
              </div>
              <div v-for="(val, ind) in item.cardRerunLabel" :key="ind" class="card-content-label">
                <template v-if="val.type === 1">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <template v-else-if="val.type === 2">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{
                    text
                  }}</span>
                </template>
              </div>
            </div>
          </div>

          <div class="card-operation">
            <operationList
              :cardOperation="item.showOperation"
              :info="item"
              :cardType="cardType"
              @childOperation="childOperation"
            />
            <div
              v-if="item.cardOperation.length > 3"
              class="card-operation-more"
              @click.stop="showMoreFn(index, true)"
            >
              <n-popover :position="['top-start']" align="end" trigger="hover">
                <template #content>
                  <popoverList
                    :cardOperation="item.popoverOperation"
                    :info="item"
                    :cardType="cardType"
                    @childOperation="childOperation"
                  />
                </template>
                <SvgIcon class="icon-more" icon="more" />
              </n-popover>
            </div>
          </div>
        </div>
        <!-- 实时作业-->
        <div v-if="cardType === 4" class="card-type-list">
          <div class="card-date">
            <SvgIcon v-show="item.cardUser" class="card-date-svg" icon="create-by" />
            {{ item.cardUser }}
            <SvgIcon v-show="item.cardDate" class="card-date-svg by" icon="create-time" />
            {{ item.cardDate }}
          </div>
          <div class="card-bg-box">
            <div class="card-content">
              <div class="card-content-name">
                <div class="card-content-name-left">
                  <img
                    class="card-content-name-left-img work"
                    src="~@img/card/card-base.png"
                    alt=""
                  />
                  {{ item.cardContentTitle }}
                </div>
              </div>
              <div
                v-for="(val, ind) in item.cardContentLabel"
                :key="ind"
                class="card-content-label"
              >
                <template v-if="val.type === 1">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <template v-else-if="val.type === 2">
                  <span class="card-content-label-name">{{ val.name }}：</span>
                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{
                    text
                  }}</span>
                </template>
              </div>
            </div>
            <!-- <div class="card-dashed">
              <div class="card-dashed-left"></div>
              <div class="card-dashed-center"></div>
              <div class="card-dashed-right"></div>
            </div> -->
            <div class="card-content">
              <div class="card-content-label">
                <div class="card-content-label-key">
                  <div class="circle"></div>
                  <img
                    class="card-content-label-key-img"
                    src="~@img/card/card-describe.png"
                    alt=""
                  />
                  描述信息
                </div>
                <n-popover
                  :position="['bottom-start']"
                  align="start"
                  trigger="hover"
                  :disabled="!item.description || item.description.length <= 50"
                >
                  <template #content>
                    <p class="card-description">{{ item.description }}</p>
                  </template>
                  <span class="card-content-label-desc">{{
                    item.description ? item.description : '暂无描述'
                  }}</span>
                </n-popover>
              </div>
            </div>
          </div>
          <div class="card-operation">
            <operationList
              :cardOperation="item.showOperation"
              :info="item"
              :cardType="cardType"
              @childOperation="childOperation"
            />
            <div
              v-if="item.cardOperation.length > 3"
              class="card-operation-more"
              @click.stop="showMoreFn(index, true)"
            >
              <n-popover :position="['top-start']" align="end" trigger="hover">
                <template #content>
                  <popoverList
                    :cardOperation="item.popoverOperation"
                    :info="item"
                    :cardType="cardType"
                    @childOperation="childOperation"
                  />
                </template>
                <SvgIcon class="icon-more" icon="more" />
              </n-popover>
            </div>
          </div>
        </div>
        <!-- 派发作业-->
        <div v-if="cardType === 5" class="card-type-list">
          <div class="card-date">
            <SvgIcon v-show="item.cardUser" class="card-date-svg" icon="create-by" />
            {{ item.cardUser }}
            <SvgIcon v-show="item.cardDate" class="card-date-svg by" icon="create-time" />
            {{ item.cardDate }}
          </div>
          <div class="card-bg-box">
            <div class="card-content">
              <div class="card-content-name">
                <div class="card-content-name-left">
                  <img
                    class="card-content-name-left-img work"
                    src="~@img/card/card-base.png"
                    alt=""
                  />
                  {{ item.cardContentTitle }}
                </div>
              </div>
              <div
                v-for="(val, ind) in item.cardContentLabel"
                :key="ind"
                class="card-content-label"
              >
                <template v-if="val.type === 1">
                  <span class="card-content-label-name regular">{{ val.name }}：</span>
                  <span class="card-content-label-value">{{ val.value }}</span>
                </template>
                <template v-else-if="val.type === 2">
                  <span class="card-content-label-name regular">{{ val.name }}：</span>
                  <span v-for="(text, t) in val.value" :key="t" class="card-content-label-btn">{{
                    text
                  }}</span>
                </template>
              </div>
            </div>
            <div class="card-content">
              <div class="card-content-label">
                <div class="card-content-label-key">
                  <div class="circle"></div>
                  <img
                    class="card-content-label-key-img"
                    src="~@img/card/card-describe.png"
                    alt=""
                  />
                  描述信息
                </div>
                <n-popover
                  :position="['bottom-start']"
                  align="start"
                  trigger="hover"
                  :disabled="!item.description || item.description.length <= 50"
                >
                  <template #content>
                    <p class="card-description">{{ item.description }}</p>
                  </template>
                  <span class="card-content-label-desc">{{
                    item.description ? item.description : '暂无描述'
                  }}</span>
                </n-popover>
              </div>
            </div>
          </div>
          <div class="card-operation">
            <operationList
              :cardOperation="item.showOperation"
              :info="item"
              :cardType="cardType"
              @childOperation="childOperation"
            />
            <div
              v-if="item.cardOperation.length > 3"
              class="card-operation-more"
              @click.stop="showMoreFn(index, true)"
            >
              <n-popover :position="['top-start']" align="end" trigger="hover">
                <template #content>
                  <popoverList
                    :cardOperation="item.popoverOperation"
                    :info="item"
                    :cardType="cardType"
                    @childOperation="childOperation"
                  />
                </template>
                <SvgIcon class="icon-more" icon="more" />
              </n-popover>
            </div>
          </div>
        </div>
        <!-- 更多操作 -->
        <div :class="item.showMore ? 'card-mantle active' : 'card-mantle'">
          <div class="card-mantle-control">
            <div
              v-if="item.popoverOperation.indexOf('n') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardConfirmFn')"
            >
              <!-- <n-tooltip class="item" content="一键复制" position="bottom"> -->
              <SvgIcon
                class="card-mantle-control-btn-icon"
                icon="icon-card-distribute"
                title="确认"
              />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('m') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardCopyFn')"
            >
              <!-- <n-tooltip class="item" content="一键复制" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-copy" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('l') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardSyncFn')"
            >
              <!-- <n-tooltip class="item" content="同步至生产" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-sync" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('c') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardEditFn')"
            >
              <!-- <n-tooltip class="item" content="编辑" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-edit" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('b') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardDelFn')"
            >
              <!-- <n-tooltip class="item" content="删除" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-del" />
              <!-- </n-tooltip>1 -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('d') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardOffFn')"
            >
              <!-- <n-tooltip class="item" content="下架" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-off" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('e') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardSeeFn')"
            >
              <!-- <n-tooltip class="item" content="查看" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-see" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('f') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardRunFn')"
            >
              <!-- <n-tooltip class="item" content="立即执行" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-rerun" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('g') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardCancelFn')"
            >
              <!-- <n-tooltip class="item" content="撤回审批" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-cancel" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('h') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardTestConnected')"
            >
              <!-- <n-tooltip class="item" content="测试" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-connected" />
              <!-- </n-tooltip> -->
            </div>
            <div
              v-if="item.popoverOperation.indexOf('i') !== -1"
              class="card-mantle-control-btn"
              @click.prevent="operationFn(item, 'cardUnbind')"
            >
              <!-- <n-tooltip class="item" content="解绑" position="bottom"> -->
              <SvgIcon class="card-mantle-control-btn-icon" icon="icon-card-unbind" />
              <!-- </n-tooltip> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import operationList from './operationList'
  import popoverList from './popoverList'
  import { mapState } from 'vuex'
  export default {
    //   cardType: 1, // 1:数据源样式   2:数据采集 样式  3.离线作业 样式 4.实时作业 样式
    //   noHeaderStatus:false, // 是否显示头部状态展示
    //   data: {
    //     cardTitle:   卡片头部名称
    //     cardDate:   卡片创建日期
    //     cardUser:   卡片创建角色名
    //     cardLogo:   卡片右边角图片
    //     cardStatus:   卡片右上角状态 0-已下线 1-已上线
    //     noCardContent:false  无调度策略内容
    //     cardContentTitle:   卡片内容部分名称
    //     cardContentLabel:[{name:'',value:'',type:1}]   卡片内容数组type:1表示普通文字 2表示数组按钮文字
    //     cardOperation:'abcd'   卡片更多操作 a：发布 b：删除 c：编辑 d：下架 e：查看 f:立即执行 g:撤回 h:测试 i:解绑 j:开发 k:申请 l:同步至生产,m:一键复制,n:确认
    //     cardRerunTitle:'重跑机制' 卡片重跑机制名称
    //     cardRerunNoOption:false 卡片重跑机制名称后无操作按钮
    //     cardDispatchNoOption:false 卡片调度策略名称后无操作按钮
    //     cardRerunLabel:[{name:'',value:'',type:1}]   卡片重跑机制内容数组type:1表示普通文字 2表示数组按钮文字
    //     cardRulerTitle:'' 卡片校验规则名称
    //     cardRulerLabel: ['为空效验'] 卡片展示规则列表
    //   }
    components: { operationList, popoverList },
    props: ['data', 'page', 'cardType', 'noHeaderStatus'],
    data() {
      return {
        cardData: [],
        canLoadMore: false, // 是否可以加载更多
      }
    },
    computed: {
      ...mapState({
        bodyClick: (state) => state['app'].bodyClick,
      }),
    },
    watch: {
      data(val) {
        let cardData = JSON.parse(JSON.stringify(val))
        let getAssetsImages = (name) => {
          return new URL(`/src/assets/img/dev/${name}.png`, import.meta.url).href //本地文件路径
        }
        cardData.forEach((item) => {
          if (this.cardType === 1) {
            item.cardLogo = getAssetsImages(item.datasourceType)
          }
          item.showOperation = ''
          item.popoverOperation = ''

          if (item?.cardOperation?.length > 3) {
            item.showOperation = item.cardOperation.slice(0, 3)
            item.popoverOperation = item.cardOperation.slice(3, item.cardOperation.length)
          } else {
            item.showOperation = item.cardOperation
          }
          item.showMore = false
        })
        this.cardData = cardData
        if (this.page.currentPage < this.page.pages) {
          this.canLoadMore = true
        } else {
          this.canLoadMore = false
        }
        if (this.page.currentPage === 1) {
          this.$refs.scrollRef.scrollTop = 0
        }
      },
      bodyClick() {
        this.cardData.forEach((val) => {
          val.showMore = false
        })
      },
      // deep: true,
      // immediate: true,
    },
    methods: {
      // 展示更多操作
      showMoreFn(index, flag) {
        // this.$store.commit('app/BODY_CLICK', true)
        // setTimeout(() => {
        //   this.cardData[index].showMore = flag
        // }, 60)
      },
      // 操作按钮
      operationFn(item, name) {
        this.$emit(name, item)
      },
      // 按钮组件事件
      childOperation(item) {
        this.operationFn(item.info, item.name)
      },
      // 切换分页数量
      handleSizeChange(val) {
        this.operationFn(val, 'cardSizeChange')
      },
      // 切换分页
      handleCurrentChange(val) {
        this.operationFn(val, 'cardCurrentChange')
      },
      // 滚动到底部
      scrollFn(event) {
        let el = event.target
        if (el.scrollTop + el.clientHeight >= el.scrollHeight - 10) {
          if (this.canLoadMore) {
            this.canLoadMore = false
            let pageNum = this.page.currentPage + 1
            this.operationFn(pageNum, 'cardCurrentChange')
          }
        }
      },
      // 鼠标移入红点
      onmouseoverFn(index) {
        this.cardData[index].hasHover = true
        this.operationFn(index, 'cardHoverTips')
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 28px;
  .cardList {
    position: absolute;
    width: 100%;
    height: 100%;
    // padding-bottom: 70px;
    box-sizing: border-box;

    .circle {
      display: none;
      position: absolute;
      top: 50%;
      transform: translate(0, -3px);
      left: -22px;
      width: 6px;
      height: 6px;
      border-radius: 6px;
      margin-right: 15px;
      background: #e1e1e1;
    }

    /*默认屏幕展示2个卡片*/
    .card-box {
      height: 100%;
      overflow: auto;
    }

    .card {
      display: inline-block;
      width: calc((100% - 16px) / 2);
      vertical-align: top;
      margin-right: 16px;
      margin-bottom: 16px;
      position: relative;
      border: 1px solid rgba(105, 122, 154, 0.1);
      border-radius: 8px;
      padding: 16px 20px;
      overflow: hidden;
      background: #ecf0f4;

      .card-img-bg {
        width: 179px;
        height: 203px;
        position: absolute;
        top: 0;
        right: 0;
        opacity: 1;
      }
      .card-img-bg-hover {
        width: 170px;
        height: 154px;
        position: absolute;
        top: 0;
        right: 0;
        opacity: 0;
      }

      &:hover {
        background: #e6eef7;
        border: 1px solid #96bdff;
        .card-dashed-left,
        .card-dashed-right {
          background-color: #f0f7ff;
        }

        .card-img-bg {
          opacity: 0;
        }
        .card-img-bg-hover {
          opacity: 1;
        }
        .card-bg-box {
          box-shadow: 2px 4px 6px 0 rgba(55, 99, 170, 0.04);
        }
      }

      &.active {
        filter: blur(0px); // 高斯模糊
      }

      &:nth-of-type(2n) {
        margin-right: 0;
        // width: 50%;
      }

      &-header {
        position: relative;
        z-index: 3;
        margin-bottom: 8px;
        &.hasLogo {
          padding-left: 72px;
          padding-top: 4px;
        }

        &-title {
          color: #000;
          height: 20px;
          line-height: 20px;
          font-size: 16px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: calc(100% - 68px);

          &.hasStatus {
            width: calc(100% - 90px);
          }
        }

        &-status {
          min-width: 56px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #697a9a;
          border-radius: 6px;
          color: #fff;
          font-size: 12px;
          font-weight: bold;
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 10px;
          .card-status-svg {
            // display: none;
            width: 14px;
            height: 14px;
            margin-right: 3px;
          }
          .red-tips {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #f63838;
            border: 1px solid #ffe2de;
            box-sizing: border-box;
            position: absolute;
            right: -5px;
            top: -5px;
            opacity: 1;
            cursor: pointer;
            &.tips-animation {
              animation: flicker infinite 0.8s;
            }
            &-desc {
              width: 10px;
              height: 10px;
              border-radius: 50%;
            }
          }
        }

        &-status.green {
          background: #04c495;
        }

        &-status.yellow {
          background: #ff7d00;
        }
        &-status.red {
          background: #f63838;
        }
        &-status.gray {
          background: #b8b8b8;
        }
        &-status.darkGrey {
          background: #697a9a;
        }

        &-status.blue {
          background: #257bff;
        }
      }

      &-date {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 12px;
        margin-bottom: 16px;
        position: relative;
        z-index: 2;
        &.hasLogo {
          padding-left: 72px;
          margin-bottom: 22px;
        }

        .card-date-svg {
          width: 16px;
          height: 16px;
          margin-right: 6px;

          &.by {
            margin-left: 20px;
          }
        }
      }

      &-right-img {
        width: 63px;
        height: 50px;
        border-radius: 8px;
        position: absolute;
        top: 14px;
        left: 19px;
        z-index: 9;
      }

      &-right-bg {
        width: 144px;
        height: 142px;
        position: absolute;
        bottom: 80px;
        right: 0;
        z-index: 1;
      }
      .card-bg-box {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(4px);
        padding: 20px 0;
        border-radius: 8px;
        overflow: hidden;
      }

      &-dashed {
        position: relative;
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 6px 0;
        &-left,
        &-right {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          position: relative;
          background-color: #edf2f8;
        }
        &-left {
          left: -10px;
        }
        &-right {
          right: -10px;
        }
        &-center {
          width: calc(100% - 40px);
          border-bottom: 1px dashed #ebedf0;
        }
      }

      &-content {
        position: relative;
        z-index: 2;
        padding: 0 24px;

        &-name {
          position: relative;
          color: #000;
          margin-bottom: 10px;

          &.has-option {
            display: inline-block;

            &:hover {
              color: $themeBlue;
            }
          }

          &-left {
            display: inline-block;
            vertical-align: top;
            line-height: 20px;
            overflow: hidden;
            font-size: 14px;
            font-weight: bold;
            text-overflow: ellipsis;
            white-space: nowrap;
            &-img {
              width: 18px;
              height: 18px;
              margin-right: 2px;
              vertical-align: -2px;
              &.collect {
                vertical-align: -3px;
              }
              &.work {
                vertical-align: -4px;
              }
            }
          }
          &-right {
            float: right;
            width: 64px;
            background: #f0f7ff;
            border-radius: 10px;
            color: #447dfd;
            font-size: 12px;
            text-align: center;
            border: 1px solid #bfd9ff;
            box-sizing: border-box;
            margin-right: 4px;
            &.deep-blue {
              color: #395dd4;
              background-color: rgba(80, 112, 218, 0.08);
              border: 1px solid rgba(80, 112, 218, 0.4);
            }
            &.green {
              color: #04c495;
              background-color: rgba(230, 255, 244, 0.7);
              border: 1px solid #75ebc2;
            }
            &.deep-green {
              color: #1998b9;
              background: rgba(44, 187, 224, 0.1);
              border: 1px solid rgba(44, 187, 224, 0.4);
            }
            &.purple {
              margin-left: 4px;
              color: #9261ff;
              background-color: rgba(146, 97, 255, 0.06);
              border: 1px solid rgba(146, 97, 255, 0.2);
            }
            &.yellow {
              margin-left: 4px;
              color: #ff7d00;
              background-color: rgba(255, 125, 0, 0.06);
              border: 1px solid rgba(255, 125, 0, 0.2);
            }
          }

          & svg {
            &:hover {
              color: $themeBlue;
              cursor: pointer;
            }
          }

          &-btn {
            display: inline-block;
            vertical-align: top;
            width: 26px;
            height: 26px;
            padding: 4px;
            margin-left: 10px;
            border-radius: 2px;

            &-icon {
              display: inline-block;
              vertical-align: top;
              width: 26px;
              height: 26px;
              padding: 4px;
              cursor: pointer;
              border-radius: 2px;
              color: #fff;

              &:hover {
                background-color: #eeeeee;
                padding: 4px;
                color: #fff;
              }
            }
          }
        }

        &-outBox {
          position: relative;
          overflow: hidden;
          width: 100%;
          height: 30px;

          & .card-content-label {
            display: inline-block;
            vertical-align: top;
            margin-top: 0;
            padding: 6px 0;
          }

          & .show-more {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 20px;
            height: 22px;
            line-height: 20px;
            cursor: pointer;

            &:hover {
              color: $themeBlue;
            }
          }
        }

        &-label {
          margin-top: 4px;
          color: #666666;
          font-size: 14px;
          line-height: 22px;
          // display: flex;

          &-key {
            position: relative;
            font-weight: bolder;
            color: #333333;
            font-size: 14px;
            margin: 16px 0 6px;
            &-img {
              width: 18px;
              height: 18px;
              margin-right: 2px;
              vertical-align: -3px;
            }
          }

          &-name {
            display: inline-block;
            margin-right: 2px;
            color: #333333;
            font-size: 14px;
            &.regular {
              text-align: right;
              width: 70px;
            }
          }
          &-value {
            display: inline-block;
            width: calc(100% - 86px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
            color: #000;
            font-size: 14px;
          }

          &-btn {
            display: inline-block;
            //background-color: #f2f3f6;
            border-radius: 4px;
            color: #000;
            font-size: 14px;
            margin-right: 6px;
            vertical-align: top;
          }
          &-desc {
            display: -webkit-box;
            height: 48px;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            font-size: 12px;
          }
        }
      }

      &-data-source {
        position: relative;
        z-index: 2;
        overflow: hidden;
        margin-bottom: 14px;

        &-name {
          float: left;
          color: #333333;
          font-weight: bold;
          font-size: 14px;
          line-height: 28px;

          &-icon {
            width: 16px;
            height: 16px;
            margin-left: 20px;
          }
        }

        &-btn {
          float: right;
        }
      }

      &-operation {
        position: relative;
        z-index: 2;
        overflow: hidden;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 18px;
        height: 24px;

        &-more {
          color: #333;
          font-size: 14px;
          cursor: pointer;
          text-align: center;
          margin-left: 20px;
          overflow: hidden;
          //width: 20px;
          //height: 20px;
          .icon-more {
            font-size: 24px;
            cursor: pointer;
            border-radius: 4px;
            &:hover {
              // background-color: #dfe3ea;
              background: rgba(105, 122, 154, 0.14);
              color: #333;
            }
          }
        }

        &-btn {
          float: left;
          box-sizing: border-box;
          margin-right: 24px;
          font-size: 14px;
          color: $themeBlue;
          cursor: pointer;
          height: 28px;
          line-height: 28px;

          &-icon {
            font-size: 18px;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 4px;
          }
        }
      }

      &-mantle {
        width: 100%;
        height: 100%;
        left: -100%;
        bottom: -100%;
        background: rgba(4, 12, 43, 0.48);
        border-radius: 4px;
        backdrop-filter: blur(10px);
        box-sizing: border-box;
        position: absolute;
        transition: all 0.3s;
        z-index: 10;

        &-control {
          width: 100%;
          height: 20px;
          position: relative;
          top: calc(50% - 10px);
          text-align: center;

          &-btn {
            display: inline-block;
            width: 60px;
            box-sizing: border-box;
            border-right: 1px solid rgba(255, 255, 255, 0.48);

            &-icon {
              display: block;
              margin: 0 auto;
              font-size: 32px;
              cursor: pointer;
              padding: 5px;
              color: #fff;
              border-radius: 5px;
              &:hover {
                background-color: rgba(255, 255, 255, 0.24);
              }
            }

            &:last-of-type {
              border-right: none;
            }
          }
        }
      }

      &-mantle.active {
        left: 0;
        bottom: 0;
      }
    }

    .footer-pagination {
      // position: fixed;
      // width: calc(100% - 220px);
      // width: 100%;
      height: 66px;
      // bottom: 0;
      // right: 0;
      z-index: 9;
      background-color: #fff;
      box-shadow: 0 -2px 8px 0 rgba(200, 201, 204, 0.5);
      border-radius: 4px 4px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 10px;
      margin-left: -20px;
      margin-right: -20px;

      :deep(.el-pagination) {
        justify-content: center;
        padding: 20px 0;

        button {
          height: $paginationChildHeight;
          min-width: $paginationChildHeight;
        }
        ul li {
          height: $paginationChildHeight;
          min-width: $paginationChildHeight;
        }
        .el-pagination__sizes,
        .el-pagination__jump {
          .el-pagination__editor {
            height: $paginationChildHeight;
          }
          .el-input__wrapper {
            height: $paginationChildHeight;
            .el-input__inner {
              height: 100%;
            }
          }
        }
      }
    }
  }

  @keyframes flicker {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  /*屏幕宽度大于1220时展示3个卡片*/
  @media only screen and (min-width: 1220px) {
    .cardList {
      .card {
        width: calc((100% - 32px) / 3);

        &:nth-of-type(2n) {
          margin-right: 16px;
        }

        &:nth-of-type(3n) {
          margin-right: 0;
        }
      }
    }
  }

  /*屏幕宽度大于1630时展示4个卡片*/
  @media only screen and (min-width: 1630px) {
    .cardList {
      .card {
        width: calc((100% - 48px) / 4);

        &:nth-of-type(2n) {
          margin-right: 16px;
        }

        &:nth-of-type(3n) {
          margin-right: 16px;
        }

        &:nth-of-type(4n) {
          margin-right: 0;
        }
      }
    }
  }
</style>
