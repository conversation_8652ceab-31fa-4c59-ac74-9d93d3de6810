import { Graph } from '@antv/x6'
import './shape'

let graph = null
export default class FlowGraph {
  static init() {
    graph = new Graph({
      container: document.getElementById('data-map'),
      width: 1680,
      height: 771,
      interacting: {
        nodeMovable: true,
      },
      // 画布滚动
      scroller: {
        enabled: true,
        pannable: true, // 是否开启画布平移功能
      },
      grid: {
        size: 10,
        visible: true,
        type: 'mesh',
      },
      nodeStateStyles: {
        // 配置节点状态样式,此处就先写一个，后续会有完整的案例分享
        highlight: {
          fill: '#db4437',
          shadowColor: '#fff',
          stroke: '#db4437',
          cursor: 'pointer',
          'text-shape': {
            lineWidth: 1,
            fill: '#db4437',
            stroke: '#db4437',
          },
        },
      },
      edgeStateStyles: {}, // 配置边状态样式
      comboStateStyles: {}, // 配置分组状态样式
    })
    graph.on('combo:mouseenter', (e) => {
      let edgeItem = e.item
      graph.setItemState(edgeItem, 'highlight', true)
      edgeItem.getEdges().forEach((edge) => {
        graphG.setItemState(edge.getTarget(), 'highlight', true)
        graph.setItemState(edge.getSource(), 'highlight', true)
        graphG.setItemState(edge, 'highlight', true)
      })
      graph.paint()
      graph.setAutoPaint(true)
    })
    return graph
  }
  // 销毁
  static destroy() {
    if (graph) {
      graph.dispose()
    }
  }
}
