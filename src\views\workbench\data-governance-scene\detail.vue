<template>
  <div class="container">
    <div class="content-box">
      <div class="content-box-title"
        >{{ state.info.name }}
        <!--        <div class="detail-back-box" @click.prevent="joinFn"> 我要加入 </div>-->
      </div>
      <div class="content-box-row">
        <div class="flex-box">
          <div class="flex-box-item">项目组长：{{ state.info.groupLeader }}</div>
          <div class="flex-box-item">牵头单位：{{ state.info.department }}</div>
          <div class="flex-box-item">项目状态：
            <span :class="['project-status',state.info.status ===item?'active':'' ]" v-for="(item,index) in ['定场景','建规则','做治理','见成效']">{{item}}</span>
            </div>
          <!--          <div class="flex-box-item">募集资金数：{{ state.info.raisedFunds }}</div>-->
        </div>
        <!--        <div class="flex-box">-->
        <!--          <div class="flex-box-item">需求经费：{{ state.info.requiredFunds }}</div>-->
        <!--          <div class="flex-box-item">项目属性：{{ state.info.attribute }}</div>-->
        <!--          <div class="flex-box-item">任务编号：{{ state.info.taskNumber }}</div>-->
        <!--          <div class="flex-box-item">立项时间：{{ state.info.projectApprovalTime }}</div>-->
        <!--        </div>-->
        <!--        <div class="flex-box">-->
        <!--          <div class="flex-box-item">预期完成时间：{{ state.info.expectedCompletionTime }}</div>-->
        <!--          <div class="flex-box-item">实际完成时间：{{ state.info.actualCompletionTime }}</div>-->
        <!--          <div class="flex-box-item">项目标签：{{ state.info.tag }}</div>-->
        <!--        </div>-->
        <div class="flex-box">
          <div class="flex-box-item onlyOne">项目简介：{{ state.info.introduction }}</div>
        </div>
        <div class="flex-box">
          <div class="flex-box-item onlyOne"
            >实施路径：{{ state.info.implementationDescription }}</div
          >
        </div>
        <div class="tabs-box">
          <div class="tabs">
            <div
              :class="state.checkedTabs === 0 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(0)"
              >项目概览</div
            >
            <div
              :class="state.checkedTabs === 6 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(6)"
              >流程模型</div
            >
            <div
              :class="state.checkedTabs === 1 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(1)"
              >数据模型</div
            >
            <div
              :class="state.checkedTabs === 2 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(2)"
              >质量规则</div
            >
            <div
              :class="state.checkedTabs === 3 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(3)"
              >数据治理</div
            >
            <div
              :class="state.checkedTabs === 4 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(4)"
              >计划进展</div
            >
            <div
              :class="state.checkedTabs === 5 ? 'tabs-item checked' : 'tabs-item'"
              @click.prevent.stop="checkTabsFn(5)"
              >治理成效</div
            >
          </div>
          <div v-loading="state.loading" class="tabs-content">
            <overview v-if="state.checkedTabs === 0 && state.info.id" :info="state.info" />
            <dataModel v-if="state.checkedTabs === 1 && state.info.id" :info="state.info" />
            <rule v-if="state.checkedTabs === 2 && state.info.id" :info="state.info" />
            <problem v-if="state.checkedTabs === 3 && state.info.id" :info="state.info" />
            <plan v-if="state.checkedTabs === 4 && state.info.id" :info="state.info" />
            <scene v-if="state.checkedTabs === 5 && state.info.id" :info="state.info" />
            <process v-if="state.checkedTabs === 6 && state.info.id" :info="state.info" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import overview from './components/overview'
  import dataModel from './components/dataModel'
  import rule from './components/rule'
  import problem from './components/problem.vue'
  import plan from './components/plan'
  import scene from './components/scene'
  import process from './components/process'
  import { workbenchDefinitionList, workbenchProjectDetail } from '@/api/dataManage.js'

  const store = useStore()
  const router = useRouter()

  const state = reactive({
    loading: false,
    id: null,
    checkedTabs: 0,
    info: {},
  })

  const checkTabsFn = (index) => {
    state.checkedTabs = index
  }

  // 获取详情
  const getDetailFn = () => {
    state.loading = true
    workbenchProjectDetail({ id: state.id })
      .then((res) => {
        if (res.success) {
          state.info = res.data
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  }

  const joinFn = () => {}

  onMounted(() => {
    state.id = router.currentRoute.value.query.id || null
    getDetailFn()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    width: 100%;
    height: calc(100vh - 96px);
    padding: 16px;
    .content-box {
      height: 100%;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 10px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 18px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 86px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      &-row {
        height: calc(100% - 62px);
        border-radius: 2px;
        background-color: #ffffff;
        padding: 12px 12px 0 12px;
        .flex-box {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 16px;
          &-item {
            width: 25%;
            flex-shrink: 0;
            overflow: hidden;
            color: #606266;
            font-weight: normal;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            .project-status{
              &+span{
                margin-left: 8px;
              }
              &.active{
                color: #fff;
                // background-color: #1e89ff;
                // color: #1e89ff; 
                border-radius: 2px; 
                // 发光效果
                box-shadow: 0 0 10px rgba(30, 137, 255, 0.5);
                // 文字发光效果
                text-shadow: 0 0 10px rgba(30, 137, 255, 0.5);
                // 彩色发光效果
                background: linear-gradient(45deg, #1e89ff, #479dff);
              }

            }
            &.onlyOne {
              width: 100%;
            }
          }
        }
        .tabs-box {
          position: relative;
          width: 100%;
          height: calc(100% - 106px);
          padding-left: 90px;
          padding-top: 16px;
          box-sizing: border-box;
          border-top: 1px solid #c5d0ea;
          .tabs {
            position: absolute;
            width: 90px;
            left: 0;
            top: 16px;
            &-item {
              color: #1d2129;
              font-weight: 500;
              font-size: 16px;
              height: 36px;
              line-height: 36px;
              position: relative;
              padding: 0 12px;
              cursor: pointer;
              &.checked {
                color: #1e89ff;
              }
              &:before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 4px;
                height: 18px;
                margin: auto;
                background: #1e89ff;
                content: '';
              }
            }
            &-content {
              width: 100%;
              height: 100%;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }
</style>
