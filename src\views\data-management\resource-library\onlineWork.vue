<template>
  <div v-loading="state.loading" class="onlineWork">
    <div class="navbar-title">
      <div class="label">
        <div class="name">
          <div class="h2"><SvgIcon class="icon" icon="icon-new-detail" /> {{ state.name }}</div>
          <span class="btn" @click.prevent.stop="downFn"
            ><SvgIcon class="export" icon="new-definition-export" />保存</span
          ></div
        >
      </div>
      <div v-if="!state.isLzos" class="detail-back-box">
        <div class="detail-back-box-btn" @click.prevent="cancelFn"
          ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
        </div>
        <div class="detail-back-box-btn checked" @click.prevent="closeFn"
          ><SvgIcon class="icon" icon="icon-close" />关闭
        </div>
      </div>
    </div>
    <div id="luckysheet" class="luckysheet"></div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { exportExcel } from './export'
  const router = useRouter()
  const store = useStore()
  import api from '@/api/index'
  import LuckyExcel from 'luckyexcel'
  import { ElNotification } from 'element-plus'

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS,
    loading: false,
    name: '',
    id: '',
    userId: '',
    userName: '',
    socket: null,
    socketUrl: '',
    onlineUserList: [],
    isConnected: false,
  })

  // 返回
  const cancelFn = () => {
    router.go(-1)
  }
  // 关闭
  const closeFn = () => {
    router.push({ name: 'dataCollectionResourceLibrary' })
  }

  //下载
  const downFn = () => {
    exportExcel(window.luckysheet.getAllSheets(), state.name, state.jobFileId)
  }

  // file文件转化为LuckyExcel
  const uploadExcel = (file) => {
    let gridKey = state.id + '_' + state.userId
    LuckyExcel.transformExcelToLucky(file, function (exportJson) {
      if (exportJson.sheets == null || exportJson.sheets.length == 0) {
        ElNotification({
          title: '提示',
          message: '读取excel文件内容失败！',
          type: 'warning',
        })
        state.loading = false
        return
      }
      api.dataManagement
        .fileSaveLoad({ gridKey: gridKey, data: JSON.stringify(exportJson.sheets) })
        .then((res) => {
          if (res.code === 'SUCCESS') {
            window.luckysheet.destroy()
            window.luckysheet.create({
              container: 'luckysheet', //luckysheet是容器id
              showinfobar: false,
              lang: 'zh', // 设定表格语言
              gridKey: gridKey,
              title: exportJson.info.name,
              userInfo: exportJson.info.name.creator,
              allowUpdate: true,
              loadUrl:
                location.origin +
                (state.isLzos ? '/governsit' : '') +
                '/api/govern-collect/work/online/loadUrl',
              // loadUrl: 'http://122.9.134.117:10005/api/govern-collect/work/online/loadUrl',
              updateUrl: state.socketUrl,
            })
            state.loading = false
          }
        })
    })
  }

  // 解析url地址成file文件
  const getFile = async (item) => {
    let { ossUrl } = item
    let _objName = ossUrl.split('/data-govern/')
    const res = await api.dataManagement.fileDownload({
      bucket: 'data-govern',
      objName: decodeURIComponent(_objName[_objName.length - 1]), //把转义过的地址转回来
    })
    let lastStr = ossUrl.lastIndexOf('/')
    let fileName = ossUrl.substring(lastStr + 1)
    const blob = new Blob([res], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
    })

    const file = new File([blob], fileName)

    nextTick(() => {
      uploadExcel(file)
    })
  }

  // 转化xls为xlsx
  const convertFn = async (item) => {
    let { ossUrl } = item
    const res = await api.dataManagement.fileUpConvert({ versionId: state.id })
    let lastStr = ossUrl.lastIndexOf('/')
    let fileName = ossUrl.substring(lastStr + 1)
    if (res) {
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
      })

      const file = new File([blob], fileName)
      Object.defineProperty(file, 'name', {
        value: state.name,
        writable: false,
      })
      nextTick(() => {
        uploadExcel(file)
      })
    } else {
      state.loading = false
    }
  }

  onMounted(() => {
    state.id = String(router.currentRoute.value.query.id)
    state.jobFileId = String(router.currentRoute.value.query.jobFileId)
    const { id, name } = store.state.user
    state.userId = id
    state.userName = name
    if (sessionStorage.getItem('onlineWorkData')) {
      let data = JSON.parse(sessionStorage.getItem('onlineWorkData'))
      state.name = data.name.split('.')[0] + '.xlsx'
      state.loading = true
      api.dataManagement.websocketUrl().then((res) => {
        if (res.code === 'SUCCESS') {
          state.socketUrl = `${res.data}/ws/excel/online/${id}/${state.id}/${name}`
          let suffix = data.ossUrl.substring(data.ossUrl.lastIndexOf('.') + 1)
          if (suffix === 'xls') {
            convertFn(data)
          } else {
            getFile(data)
          }
        }
      })
    }
  })
  onBeforeUnmount(() => {
    window.luckysheet.closeWebsocket()
    window.luckysheet.destroy()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .onlineWork {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 12px;
    .navbar-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 0 16px;
      line-height: 50px;
      background-color: #fff;
      border: 1px solid #e5e5e5;
      border-bottom: none;
      border-radius: 8px 8px 0 0;

      .label {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-right: 16px;
        line-height: 20px;

        .name {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 14px;

          .h2 {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            max-width: 480px;
            overflow: hidden;
            font-size: 18px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            font-size: 16px;
            vertical-align: -2px;
          }
          .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
            border-radius: 6px;
            &:hover {
              color: $themeBlue;
              background-color: #e3ecff;
            }
            .export {
              margin-right: 2px;
            }
          }
        }

        .value {
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
        }
      }

      .detail-back-box {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        z-index: 9;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 127px;
        height: 33px;
        margin: auto;
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 50%;
          width: 1px;
          height: 16px;
          margin: auto;
          background-color: #e5e5e5;
          content: '';
        }

        &-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 50%;
          color: rgba(0, 0, 0, 0.46);
          font-size: 14px;
          cursor: pointer;

          .icon {
            width: 16px;
            height: 16px;
            margin-right: 2px;
            font-size: 16px;
          }

          &.checked {
            color: $themeBlue;

            &:hover {
              color: #6e9eff;
            }

            &:active {
              color: #2f5cd6;
            }
          }

          &:hover {
            color: #6e9eff;
          }

          &:active {
            color: #2f5cd6;
          }
        }
      }
    }
    .luckysheet {
      position: relative;
      width: 100%;
      height: calc(100% - 50px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 0 0 8px 8px;
    }
  }
</style>
