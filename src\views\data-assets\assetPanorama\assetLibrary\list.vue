<template>
  <!-- 资产全景-资产目录 -->
  <div class="asset-library-page container" :class="{ lzos: state.isLzos }">
    <div class="statistics-header">
      <div class="box-add commonForm">
        <n-form :inline="true" :model="state.filterSearch">
          <n-form-item label="">
            <n-input
              v-model="state.filterSearch.keyword"
              style="width: 260px"
              placeholder="请输入模型名称"
              clearable
              @clear="initTable"
            >
              <!-- <template #append>
                <n-button @click.stop.prevent="initTable">
                  <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                    <SvgIcon class="icon_search" icon="icon_search" />
                  </n-popover>
                </n-button>
              </template> -->
            </n-input>
          </n-form-item>
          <n-form-item label="">
            <n-select
              v-model="state.filterSearch.tagName"
              placeholder="请选择标签"
              style="width: 260px"
              :allow-clear="true"
              @value-change="initTable"
              @clear="initTable"
              :key="state.key"
              :options="state.tagList"
              filter
            />
          </n-form-item>
        </n-form>

        <div class="serach">
          <div class="search-btn" @click.stop.prevent="initTable">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </div>

    <div class="statistics-table">
      <div class="statistics-left">
        <PublicLeftTree
          :data="state.treeData"
          :treeAttrData="state.treeAttrData"
          @treeCheckNode="treeCheckNode"
        />
        <!-- <div class="table-tree scroll-bar-style">
          <n-tree ref="treeRef" :data="state.treeData" @node-click="treeCheckNode">
            <template #content="{ nodeData }">
              <SvgIcon v-if="nodeData?.children?.length > 0" class="tree-icon" icon="tree-open" />
              <SvgIcon v-else class="tree-icon" icon="tree-close" />
              <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>
            </template>
            <template #icon="{ nodeData, toggleNode }">
              <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
              <span
                v-else
                @click="
                  (event) => {
                    event.stopPropagation()
                    toggleNode(nodeData)
                    treeChange(nodeData)
                  }
                "
              >
                <svg
                  :style="{
                    transform: nodeData.expanded ? 'rotate(90deg)' : '',
                    marginLeft: '-2.5px',
                    marginRight: '6px',
                    cursor: 'pointer',
                  }"
                  viewBox="0 0 1024 1024"
                  width="8"
                  height="8"
                >
                  <path
                    d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                    fill="currentColor"
                  />
                </svg>
              </span>
            </template>
          </n-tree>
        </div> -->
      </div>
      <div class="statistics-right">
        <n-public-table
          ref="publicTable"
          :isDisplayAction="true"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :tableData="state.tableData"
          :pagination="state.pagination"
          :tableHeight="state.tableHeight"
          :actionWidth="150"
          @tablePageChange="tablePageChange"
        >
          <template #createByName="{ editor }">
            <div class="ball_box">
              <div class="ball">{{ editor.row.createByName[0] }}</div
              >{{ editor.row.createByName }}
            </div>
          </template>
          <template #status="{ editor }">
            <n-status
              v-if="editor.row.status === 'AUDIT_SUCCESS' && editor.row.assetsRegistered"
              type="warning"
              >未注销</n-status
            >
            <n-status
              v-else-if="editor.row.status === 'WAIT_AUDIT' && editor.row.assetsRegistered"
              type="Waiting"
              >审核中</n-status
            >
            <n-status v-else type="error">--</n-status>
          </template>
          <template #editor="{ editor }">
            <div class="edit-box">
              <n-button
                v-if="state.buttonAuthList?.includes('assetsManage_dataAssets_assetLibrary_view')"
                class="del-button has-right-border"
                variant="text"
                @click.stop.prevent="checkThisTypeDetails(editor)"
                ><n-tooltip class="tree-btn" content="查看" position="top">
                  <SvgIcon icon="new-target-service-see" class="icon" title="查看" />
                </n-tooltip>
              </n-button>
              <n-button
                v-if="
                  state.buttonAuthList?.includes(
                    'assetsManage_dataAssets_assetLibrary_deregister_edit',
                  )
                "
                class="del-button has-right-border"
                :disabled="!editor.row.assetsRegistered || editor.row.status === 'WAIT_AUDIT'"
                variant="text"
                @click.stop.prevent="checkRegister(editor)"
                ><n-tooltip class="tree-btn" content="资产注销" position="top">
                  <SvgIcon icon="icon-exit" class="icon" title="资产注销" />
                </n-tooltip>
              </n-button>
            </div>
          </template> </n-public-table
      ></div>
    </div>
    <!-- 查看弹框 -->
    <detailPopup
      :dialogTitle="state.dialogTitle"
      :dialogVisible="state.dialogVisible"
      :detailRuleForm="state.detailRuleForm"
      :detailData="state.detailData"
      :width="862"
      @closeDialog="closeDialog"
    />
  </div>
</template>
<script>
  import { reactive, onMounted, ref, toRefs, nextTick } from 'vue'
  import { ElNotification } from 'element-plus'
  import detailPopup from './detailPopup'

  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'

  export default {
    name: 'AuthorizedPersonnel',
    components: {
      detailPopup,
    },
    setup() {
      const router = useRouter()
      const { proxy } = getCurrentInstance()
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/assets/assetLibrary/${name}.png`, import.meta.url).href //本地文件路径
      }
      // 获取当前组件实例
      // const { proxy } = getCurrentInstance()
      const store = useStore()
      const publicTable = ref()
      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        buttonAuthList: [],
        bizDomainId: null,
        allTableData: [], // 所有选中人员
        treeData: [],
        treeAttrData: {
          showLeftIcon: true,
          // showCheckbox: true,
          showControl: false,
          parentControl: '',
          childControl: '',
        },
        dialogTitle: '元数据详情',
        dialogVisible: false,
        detailRuleForm: {},
        detailData: {},
        key: 1,
        // 统计的所有类
        statisticalDataType: [
          // {
          //   title: '元数据', // 名称
          //   number: '--', // 数量
          //   viewable: true, // 可查看
          //   icon: getAssetsImages('asset-library-1'), // 图片
          //   unit: '条', // 单位
          // },
          {
            title: '数据模型',
            number: '--',
            viewable: true,
            icon: getAssetsImages('asset-library-2'),
            unit: '条',
          },
          // {
          //   title: '标签数',
          //   number: '--',
          //   viewable: true,
          //   icon: getAssetsImages('asset-library-3'),
          //   unit: '条',
          // },
          // {
          //   title: '服务数',
          //   number: '--',
          //   viewable: true,
          //   icon: getAssetsImages('asset-library-4'),
          //   unit: '条',
          // },
        ],

        tableHeadTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'cnName', name: '中文名称' },
          { prop: 'name', name: '英文名称' },
          { prop: 'bizDomainName', name: '业务域' },
          { prop: 'dataCount', name: '数据总量' },
          { prop: 'qualityScore', name: '质量评分' },
          { prop: 'createTime', name: '创建时间' },
          { prop: 'createByName', name: '创建人', slot: 'createByName' },
          { prop: 'status', name: '注销状态', slot: 'status' },
          { prop: 'description', name: '描述' },
        ], //选中类表头
        tagList: [],
        pagination: {
          pageSizes: [10, 30, 50, 100], // 每次展示条数的可配置项
          currentPage: 1,
          pageSize: 10,
        },
        tableData: {},
        tableHeight: 400,

        filterSearch: {
          keyword: '',
          tagName: '',
        },
        dataModelId: '',
        project_options: [],
        interfaceName: 'getAssetsLibraryModelList', //默认获取元数据列表
      })

      const methods = {
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 216 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 216
          }
        },
        getTagList() {
          api.assets.getAssetsTagList().then((res) => {
            let { success } = res
            if (success) {
              state.tagList = res.data?.map((item) => {
                return { ...item, value: item.id }
              })
            }
          })
        },
        // 选中人员执行
        treeCheckNode(data) {
          if (
            data.checkItem.id === 1 ||
            data.checkItem.id === 'ROOT' ||
            data.checkItem.name === '全部'
          ) {
            state.bizDomainId = null
          } else {
            state.bizDomainId = data.checkItem.id
          }

          methods.initTable()
        },
        // 获取统计数据
        getStatistics() {
          api.assets.getAssetsLibraryCount().then((res) => {
            let { success } = res
            if (success) {
              let { metadataNum, modelNum, tagNum, apiNum } = res.data
              state.statisticalDataType[0].number = metadataNum
              state.statisticalDataType[1].number = modelNum
              state.statisticalDataType[2].number = tagNum
              state.statisticalDataType[3].number = apiNum
            }
          })
        },
        // 关闭弹框
        closeDialog() {
          state.dialogVisible = false
        },
        // 初始化表格
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              name: state.filterSearch.keyword || null,
              tagId: state.filterSearch.tagName || null,
              bizDomainId: state.bizDomainId,
              assetsRegistered: true,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }

          api.assets
            .getAssetsLibraryModelList(data)
            .then((res) => {
              if (res.success) {
                // 新增序号属性
                res.data.list.map((item, index) => {
                  return Object.assign(item, {
                    number: index + 1,
                  })
                })
                state.tableData = res.data
              }
            })
            .catch(() => {})
          state.key++
        },

        // 查看详情
        checkThisTypeDetails(editor) {
          let { row } = editor
          if (row.hasViewPermission) {
            //匹配密级
            let data = {}
            data.layerId = row.layerId
            data.layerName = row.layerName

            data.projectName = row.projectName
            data.description = row.description
            router.push({
              name: 'assetLibraryDetail',
              query: {
                id: row.id,
                modelTitle: row.cnName || row.name,
                modelName: row.name,
                type: 'DATAMODEL',
                ruleForm: JSON.stringify(data),
              },
            })
          } else {
            ElNotification({
              title: '提示',
              message: '用户密级不匹配',
              type: 'warning',
            })
          }
        },
        //注销
        checkRegister(editor) {
          const { row } = editor
          proxy.$MessageBoxService.open({
            title: '资产注销',
            content: `注销后，该数据会被移除资产目录，确定是否进行资产注销？`,
            save: () => {
              methods.submitRegister(row)
            },
          })
        },
        submitRegister(row) {
          let params = [row.id]
          api.assets.deregisterAssets(params).then((res) => {
            const { success } = res
            if (success) {
              ElNotification({
                title: '提示',
                message: '发起注销成功',
                type: 'success',
              })
              methods.initTable(false)
            }
          })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        // 获取左侧树数据
        getTreeData(status) {
          sceneManage.searchTreeList().then((res) => {
            let { success, data } = res
            if (success) {
              if (data !== null) {
                let haveRoot = data.some((item) => {
                  if (item.name === '全部') {
                    return true
                  }
                })
                if (haveRoot) {
                  state.treeData = data
                } else {
                  state.treeData = [
                    {
                      description: '全部',
                      id: 0,
                      name: '全部',
                      children: data,
                    },
                  ]
                }
                if (status) {
                  // methods.initTable()
                  //默认点击
                  nextTick(() => {
                    const firstNode = document.getElementsByClassName('el-tree-node')[0]
                    // firstNode.click()
                    methods.initTable()
                  })
                }
              }
            }
          })
        },
        resetFn() {
          state.filterSearch = {
            keyword: '',
            tagName: '',
          }
          methods.initTable()
        },
      }
      onMounted(() => {
        nextTick(() => {
          const { buttonAuthList } = toRefs(store.state.user)
          state.buttonAuthList = buttonAuthList
          methods.setTableHeight()
          methods.getTreeData(true)
          methods.getTagList()
        })
      })

      return {
        state,
        publicTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .asset-library-page {
    height: 1;
    padding: 10px;
    &.container {
      height: 100%;
    }
    &.lzos {
      padding: 0;
    }

    &-top {
      padding: 0 20px;
      background-color: #fff;
      border-radius: 4px 4px 0 0;
    }

    .statistics-header {
      margin-bottom: 8px;
    }

    .statistics-table {
      display: flex;
      height: calc(100% - 12px);
      background-color: #fff;
      border-radius: 4px;
      .statistics-left {
        .title {
          height: 32px;
          color: #333333;
          font-weight: bolder;
          font-size: 12px;
          line-height: 28px;
          text-align: center;
          background: #f2f3f6;
          border: 2px solid #ffffff;
          border-radius: 4px;
        }
      }
      .statistics-right {
        flex: 1;
        min-width: 0;
        padding: 16px;
        :deep(.el-table) {
          .total-row {
            span {
              padding-right: 5px;
              &:nth-of-type(2) {
                padding: 0 5px;
                color: #333;
              }
            }
          }
          :deep(.nancalui-button--outline) {
            background-color: transparent;
          }
        }

        .ball_box {
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
        }

        .ball {
          display: inline-block;
          width: 28px;
          line-height: 28px;
          background: #257bff;
          color: #fff;
          font-size: 14px;
          text-align: center;
          border-radius: 28px;
        }
      }
      .top-left {
        display: flex;
        align-items: center;
      }

      :deep(.el-pagination) {
        background-color: #fff;
      }
    }

    .need_smallcube__title {
      display: block;
      height: 16px;
      padding-left: 6px;
      color: #333333;
      font-weight: bolder;
      font-size: 16px;
      line-height: 18px;
      border-left: 4px solid var(--themeBlue);
    }

    .top-change-project {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 78px;
      border-bottom: 1px solid #dcdcdc;

      .top-change-project-left {
        display: flex;
        align-items: center;

        span {
          color: #333333;
          font-weight: bolder;
          font-size: 16px;
          line-height: 24px;
        }
      }
    }

    .mid-statistics {
      display: flex;
      align-items: flex-start;
      padding: 18px 0 4px;

      .statistics-box-left {
        padding: 10px 20px 0 0;
      }

      .statistics-box {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        padding: 0 20px 0 0;

        .statistics-list {
          display: flex;
          align-content: center;
          justify-content: center;
          width: 25%;

          img {
            width: 106px;
            height: 106px;
          }

          &-des {
            display: flex;
            align-items: center;
          }

          &-des-box {
            color: #333;
            font-size: 12px;
            cursor: pointer;

            &.can-click {
              cursor: pointer;
            }

            .top {
              display: flex;
              align-items: center;
              line-height: 20px;
            }

            .number {
              padding: 0 5px;
              color: #333333;
              font-weight: bolder;
              font-size: 24px;
            }

            .unit {
              color: #666;
            }

            .yy-icon {
              width: 20px;
              margin-left: 10px;
              color: #c9c9cb;
            }

            &.active {
              .yy-icon {
                color: $themeFontColor;
              }
            }
          }
        }

        span {
          color: #25bfff;
        }
      }
    }

    .box-add {
      display: flex;
      justify-content: space-between;
      height: 52px;
      padding: 8px 8px 8px 16px;
      background-color: #fff;
      border-radius: 8px;

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          display: inline-block;
          margin-right: 8px;
          padding: 5px 15px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border: 1px solid $themeBlue;
          border-radius: 6px;
          cursor: pointer;

          &:last-of-type {
            margin-right: 0;
          }

          &.reset {
            padding: 5px 8px;
            border: 1px solid transparent;

            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }

          &:hover {
            background-color: #e3ecff;
            border: 1px solid #e3ecff;
          }
        }
      }
    }

    .edit-box {
      .del-button {
        &:first-child {
          border-left: 0 !important;
        }

        &:last-child {
          padding-right: 0;
        }
      }
    }
  }
</style>
