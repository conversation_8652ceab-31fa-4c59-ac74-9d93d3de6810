import { domtoimage } from 'dom-to-image'
import {
  API_BASE_URL,
  API_TARGET_URL,
  MOCK_API_BASE_URL,
  MOCK_API_TARGET_URL,
} from '../../config/constant'
import { ProxyOptions } from 'vite'

type ProxyTargetList = Record<string, ProxyOptions>

// const domainName = 'http://122.9.134.117:9006' // 正式环境
// const domainName = 'http://122.9.134.117:10005' // 测试环境
// const domainName2 = 'http://10.41.32.207:9099' // 李勇江本地
// const domainName = 'http://10.41.26.210:9999' // 成飞开发环境

// const domainName = 'http://192.168.5.248:40023' // 北京本地开发环境
// const domainName = 'http://192.168.5.84:9093' // 北京乐造环境
// const domainName = 'http://10.41.26.101:8888' // 本地开发环境
const domainName = 'http://10.41.26.51:8080/' // 本地成飞开发环境
// const domainName = 'http://122.9.134.117:10105/' // 成飞测试环境
// const domainName = 'http://10.41.55.108:9999' // java电脑环境
// const domainName2 = 'http://10.41.63.143:9005' // 局部接口配置

const init: ProxyTargetList = {
  '/ocr': {
    target: 'http://192.168.5.215:9029',
    rewrite: (path) => path.replace(/^\/ocr/, '/'),
  },
  '/zhiliang': {
    target: 'http://192.168.5.84:9033',
    rewrite: (path) => path.replace(/^\/zhiliang/, '/'),
  },
  '/api/govern-core': {
    target: domainName,
  },
  '/api/govern-auth': {
    target: domainName,
  },
  '/api/govern-management': {
    target: domainName,
  },
  '/api/govern-document': {
    target: domainName,
  },
  '/api/govern-data-model': {
    target: domainName,
  },
  '/api/govern-data-studio': {
    target: domainName,
  },
  '/api/mode': {
    target: domainName,
  },
  '/api/govern-collect': {
    target: domainName,
    // target: 'http://10.41.33.132:9015',
  },
  '/api/govern-audit-center': {
    target: domainName,
  },
  '/api/govern-data-quality': {
    target: domainName,
  },
  '/api/govern-data-assets': {
    target: domainName,
  },
  '/api/govern-data-api': {
    target: domainName,
  },
  '/api/govern-offline-work': {
    target: domainName,
  },
  '/api/govern-realtime-work': {
    target: domainName,
  },
  '/api/govern-engine-storage': {
    target: domainName,
  },
  '/api/govern-indicator': {
    target: domainName,
  },
  '/fileTemplateUrl': {
    target: 'http://10.41.49.154:9014',
    rewrite: (path) => path.replace(/^\/fileTemplateUrl/, '/'),
  },
  '/dataoperation': {
    target: 'http://10.41.50.225:9003',
  },
  '/ds': {
    target: 'http://10.41.50.225:9003',
  },
  '/biz': {
    target: 'http://10.41.51.119:9005',
  },
  '/layer': {
    target: 'http://10.41.51.119:9014',
  },
  '/metadata': {
    target: 'http://10.41.51.119:9014',
  },
  '/python': {
    target: domainName,
  },
  '/api/govern-three-dimensional': {
    target: domainName,
  },
  '/api/integration-global-model': {
    target: domainName,
  },
  // test
  [API_BASE_URL]: {
    target: API_TARGET_URL,
    changeOrigin: true,
    rewrite: (path) => path.replace(new RegExp(`^${API_BASE_URL}`), ''),
  },
  // mock
  [MOCK_API_BASE_URL]: {
    target: MOCK_API_TARGET_URL,
    changeOrigin: true,
    rewrite: (path) => path.replace(new RegExp(`^${MOCK_API_BASE_URL}`), '/api'),
  },
}

export default init
