<template>
  <div class="box" :style="isGreen ? 'background-color: #2FBA7A' : ''">
    {{ nodeInfo.nName }}
  </div>
</template>

<script>
  // import { reactive, toRefs, inject } from 'vue'

  export default {
    inject: ['getGraph', 'getNode'],
    // setup() {
    //   const state = reactive({
    //     node: null,
    //     nodeInfo: {
    //       nName: '',
    //       nEnName: '',
    //     },
    //   })
    //   state.node = inject('getNode')
    //   const params = toRefs(state)
    //   return {
    //     ...params,
    //   }
    // },
    data() {
      return {
        nodeInfo: {
          nName: '',
        },
        isGreen: false,
      }
    },
    mounted() {
      const node = this.getNode()
      const graph = this.getGraph()
      this.nodeInfo = node.getData()
      let intBorder = graph.getIncomingEdges(node) // 获取输入边
      let outBorder = graph.getOutgoingEdges(node) // 获取输出边
      if (intBorder && outBorder) {
        this.isGreen = true
      } else {
        this.isGreen = false
      }
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.nodeInfo = current
      })
    },
  }
</script>

<style lang="scss" scoped>
  .box {
    width: 48px;
    height: 48px;
    line-height: 48px;
    border-radius: 50%;
    padding: 0 6px;
    box-sizing: border-box;
    background-color: #077dfa;
    font-size: 12px;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    position: relative;
  }
</style>
