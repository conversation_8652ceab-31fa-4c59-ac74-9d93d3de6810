<template>
  <div :class="{ 'data-collection-page-out-box': true }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">资产名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="资产名称/资产编码"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">资产目录</div>
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @input="searchTreeFn"
          />
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :default-expanded-keys="state.expandedKeys"
            :current-node-key="state.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="state.treeData"
            @node-click="clickFn"
          />
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-btn">
              <n-button
                variant="solid"
                :disabled="state.selectIds.length === 0"
                @click.prevent="markFn(state.selectIds, { name: state.selectName, tag: '[]' })"
              >
                <SvgIcon class="icon" icon="icon-assets-marking" />批量打标
              </n-button>
            </div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                actionWidth="180"
                :key="state.tableData"
                :tableConfig="{
                  data: state.tableData,
                  rowKey: 'id',
                }"
                isNeedSelection
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    init(false)
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    init(true)
                  },
                }"
                @handle-selection-change="tableSelectionChange"
              >
                <template #name="{ row }">
                  <div class="taskName">
                    {{ row.name }}
                  </div>
                </template>
                <template #type="{ row }">
                  <div class="template-type-box">
                    <div>
                      <SvgIcon v-if="row.type === 'TABLE'" icon="icon-assets-table" class="icon" />
                      <SvgIcon
                        v-else-if="row.type === 'FILE'"
                        icon="icon-assets-file"
                        class="icon"
                      />
                      <SvgIcon v-else icon="icon-assets-directory" class="icon" />
                      <span v-if="row.type === 'TABLE'">表</span>
                      <span v-else-if="row.type === 'FILE'">文件</span>
                      <span v-else>资源目录</span>
                    </div>
                  </div>
                </template>
                <template #label="{ row }">
                  <cfTag :tagArr="row.tag" />
                </template>
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      v-if="buttonAuthList.includes('dataManagement_collectionMonitor_view')"
                      code="dataManagement_collectionMonitor_view"
                      variant="text"
                      @click.prevent="cardSeeFn(row)"
                      >查看</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="buttonAuthList.includes('dataManagement_collectionMonitor_run_edit')"
                      code="dataManagement_collectionMonitor_run_edit"
                      variant="text"
                      @click.prevent="markFn([row.id], row)"
                      >打标</n-button
                    >
                    <n-button
                      class="has-right-border"
                      v-if="
                        buttonAuthList.includes('dataManagement_collectionMonitor_refresh_edit')
                      "
                      code="dataManagement_collectionMonitor_refresh_edit"
                      :disabled="row.registrationStatus === 'WAIT_AUDIT'"
                      variant="text"
                      @click.prevent="delFn(row)"
                      >资产注销</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-modal
      v-model="state.markDialog"
      title="标签管理"
      class="largeDialog has-top-padding assets-label-select-color"
      width="580px"
      :close-on-click-overlay="false"
      @close="state.markDialog = false"
    >
      <n-form
        ref="markForm"
        :data="state.markForm"
        :rules="state.markRules"
        label-align="end"
        label-width="100px"
      >
        <n-form-item label="资产名称：" field="name">
          <div class="mark-name" :title="state.markForm.name">{{ state.markForm.name }}</div>
        </n-form-item>
        <n-form-item field="tagList" label="标签：">
          <div class="top-line-tag">
            <el-tree-select
              ref="selectTree"
              v-model="state.markForm.tagList"
              :data="state.targetOptions"
              style="width: 100%"
              node-key="key"
              :props="{
                label: 'name',
                value: 'key',
                children: 'children',
              }"
              multiple
              show-checkbox
              :render-after-expand="false"
              filterable
              clearable
            />
          </div>
        </n-form-item>
        <n-form-item v-if="state.showCustom" field="customName" label="自定义标签：">
          <div class="mark-label-item">
            <n-input
              v-model="state.markForm.customName"
              :maxLength="8"
              placeholder="请输入标签名称"
            />
            <n-button
              color="primary"
              :disabled="state.markForm.customName.length < 2"
              variant="solid"
              @click.prevent.stop="saveLabelFn"
              >保存</n-button
            >
          </div>
        </n-form-item>
        <n-form-item v-if="state.showCustom" field="color" label="随机颜色：">
          <div class="custom">
            <div class="custom-color"
              >随机颜色<n-switch v-model="state.customColor" size="sm"
            /></div>
            <div v-if="!state.customColor" class="custom-color second">
              <div
                v-for="(item, index) in state.colorList"
                :key="index"
                :style="'background-color:' + item.value"
                :class="{ 'custom-color-label': true, checked: item.checked }"
                @click.prevent.stop="checkFn(index)"
              >
                <SvgIcon class="icon" icon="icon-check-white" title="选中" />
              </div>
            </div>
          </div>
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button @click.prevent="state.markDialog = false">取 消</n-button>
          <n-button v-loading="state.markLoading" variant="solid" @click.prevent="saveFn"
            >保 存</n-button
          >
        </div>
      </template>
    </n-modal>
    <!--    <div style="width: 0" v-html="`<style>-->
    <!--        .assets-label-select-color{-->
    <!--          ${toStyle}-->
    <!--        }-->
    <!--    </style>`-->
    <!--      ">-->
    <!--    </div>-->
  </div>
</template>
<script>
  import { reactive, onMounted, getCurrentInstance, ref, toRefs, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { getAssetsList, getAssetsClassifyTreeList } from '@/api/sceneManage'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import cfTag from '@/components/cfTag'
  export default {
    name: '',
    components: { cfTag },
    props: {},
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const router = useRouter()
      const selectTree = ref(null)
      const state = reactive({
        tableData: [],
        tableHeight: 436,
        key: 1,
        collectType: 'ALL',
        loading: false,
        showResult: false,
        isDisplayAction: true,
        colorList: [
          { value: '#FA5924', bgColor: '#FFF2E8', checked: true },
          { value: '#ED4EA5', bgColor: '#FFF0F6', checked: false },
          { value: '#8D54DA', bgColor: '#F9F0FF', checked: false },
          { value: '#41A4FF', bgColor: '#E6F7FF', checked: false },
          { value: '#37CCCB', bgColor: '#E6FFFB', checked: false },
        ],
        markDialog: false,
        markLoading: false,
        customColor: true,
        showCustom: false,
        selectIds: [],
        selectName: [],
        markForm: {
          ids: [],
          name: '',
          customName: '',
          tagList: [],
        },
        markRules: {
          // name: [{ required: true, message: '请选择资源', trigger: 'change' }],
        },
        targetOptions: [],
        targetKey: 1,
        originalFormInline: {
          keyword: null,
        },
        formInline: {
          keyword: null,
        },
        optionItemData: {}, // 操作的任务数据
        pageInfo: {
          total: 0,
          pageSize: 10,
          currentPage: 1,
        },
        tableHeadTitles: [
          { prop: 'name', name: '资产名称', slot: 'name' },
          { prop: 'assetCode', name: '资产编码' },
          { prop: 'classificationName', name: '资产分类' },
          { prop: 'type', name: '资产类型', slot: 'type' },
          { prop: 'tag', name: '标签', slot: 'label' },
        ],
        categoryId: null,
        treeSearchText: '',
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        selectionList: [],
        expandedKeys: [], 
        selectedKey: null,
      })
      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      // css转换 计算属性
      const toStyle = computed(() => {
        return state.markForm.tagList?.map((_, i) => {
          const item = state.targetOptions.find((item) => item.name === _)
          const [color, background] = item.color.split('_')
          return `
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag {
          background: ${background} !important;
          border: 1px solid var(---, ${color}) !important;
          color: ${color} !important;
        }
        .el-select__selected-item:nth-child(${i + 1}) > .el-tag  .el-tag__close{
            color: ${color} !important;
         }
        `
        }).join(`
        `)
      })
      const methods = {
        // 获取标签列表
        getTargetList(item) {
          api.documentManage.getTagLibraryClassListHasTag().then((res) => {
            let { success, data } = res
            if (success) {
              state.targetOptions = data
              let tag = JSON.parse(item?.tag || '[]')
              state.markForm.tagList = tag?.map((item) => 'tag_' + item.id)
              state.markDialog = true
            }
          })
        },
        // 标签输入完成
        changeTargetFn(flag) {
          if (flag) {
            state.markForm.customName = ''
            state.colorList = state.colorList.map((val, ind) => {
              if (ind === 0) {
                val.checked = true
              } else {
                val.checked = false
              }
              return val
            })
          }
          state.showCustom = flag
        },
        // 自定义标签输入验证
        customNameBlur() {
          let regex = /^[\u4e00-\u9fa5A-Za-z]+$/
          let res = regex.test(state.markForm.customName)
          if (res && state.markForm.customName.length > 1) {
            let filterResult = state.targetOptions.filter(
              (val) => val.name === state.markForm.customName,
            )
            if (filterResult.length === 0) {
              return true
            } else {
              if (
                filterResult[0].id ||
                state.markForm.tagList.filter((val) => val === state.markForm.customName).length > 0
              ) {
                ElNotification({
                  title: '提示',
                  message: '标签名重复，请重新填写！',
                  type: 'warning',
                })
                return false
              }
              return true
            }
          } else {
            ElNotification({
              title: '提示',
              message: '标签为2-8个字符，支持中文和英文！',
              type: 'warning',
            })
            return false
          }
        },
        // 选中颜色
        checkFn(index) {
          state.colorList.forEach((val, ind) => {
            val.checked = false
            if (index === ind) {
              val.checked = true
            }
          })
        },
        // 保存标签
        saveLabelFn() {
          if (state.showCustom) {
            if (state.markForm.customName) {
              if (methods.customNameBlur()) {
                state.markForm.tagList.push(state.markForm.customName)
                let checkColorItem = state.colorList.filter((val) => val.checked)[0]
                if (state.customColor) {
                  let index = Math.floor(Math.random() * 5)
                  checkColorItem = state.colorList[index]
                }
                state.targetOptions = state.targetOptions.filter(
                  (val) => val.name !== state.markForm.customName,
                )
                state.targetOptions.push({
                  name: state.markForm.customName,
                  value: state.markForm.customName,
                  color: checkColorItem.value + '_' + checkColorItem.bgColor,
                })
                state.targetKey++
                state.markForm.customName = ''
              } else {
                return false
              }
            } else {
              ElNotification({
                title: '提示',
                message: '请输入标签名称！',
                type: 'warning',
              })
              return false
            }
          }
        },
        // 保存修改内容
        saveFn() {
          let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
          if (activeOptions.length === 0) {
            ElNotification({
              title: '提示',
              message: '请添加标签',
              type: 'warning',
            })
            return false
          }
          let params = {
            ids: state.markForm.ids,
            tag: JSON.stringify(
              activeOptions
                .filter((val) => val.nodeType === 'tag')
                .map((val) => {
                  return { text: val.name, color: val.color, id: val.id || null }
                }) || [],
            ),
          }
          api.assets.addMoreAssetsTags(params).then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '打标成功!',
                type: 'success',
              })
              state.markDialog = false
              state.selectName = ''
              state.selectIds = []
              methods.initTable(false)
            }
          })
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 296
        },
        closeDialog() {
          state.showResult = false
        },
        // 查看
        cardSeeFn(item) {
          sessionStorage.setItem('assetsDirectoryDetail', JSON.stringify(item))
          if (item.type === 'TABLE') {
            router.push({ name: 'assetsDirectoryDetail', query: { id: item.id } })
          } else if (item.type === 'FILE') {
            router.push(
              {
                name: 'assetsDirectoryDocView',
                query: {
                  id: item.registerFrom,
                },
              },
              { replace: false },
            )
          } else {
            router.push({ name: 'assetsDirectoryAssetsMenu', query: { tabs: 'menu' } })
          }
        },
        // 打标
        markFn(ids, item) {
          state.customColor = false
          state.showCustom = false
          state.markForm.name = item.name
          state.markForm.ids = ids
          methods.getTargetList(item)
        },
        // 注销
        delFn(item) {
          proxy.$MessageBoxService.open({
            title: '资产注销',
            content: `您将注销${item.name}【资产编码：${item.assetCode}】，提交后将发起审批流程，请确认是否继续？`,
            save: () => {
              api.assets.registerCancellation({ id: item.id }).then((res) => {
                if (res.success) {
                  methods.init(false)
                }
              })
            },
          })
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            keyword: null,
          }
          methods.searchClickFn()
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.onSearch(true)
        },
        //搜索
        onSearch() {
          state.key++
          methods.init(true)
        },
        // 获取树列表
        getTreeListFn() {
          getAssetsClassifyTreeList({ keyword: '' }).then((res) => {
            if (res.code === 'SUCCESS') {
              res.data && (res.data.name = '资产目录')
              let treeData = (res.data && [res.data]) || []
              treeData[0].id ??= 'root'
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]
              nextTick(() => {
                state.expandedKeys = [state.treeData?.[0]?.id || 1]
                state.selectedKey = state.treeData?.[0]?.id || 1
              })
              methods.onSearch(true)
            }
          })
        },
        // 树搜索
        searchTreeFn() {
          state.treeData = methods.filterTreeData(state.defaultTreeData, state.treeSearchText)
        },
        filterTreeData(treeData, text) {
          // 使用map复制一下节点，避免修改到原树
          return treeData
            .map((node) => ({ ...node }))
            .filter((node) => {
              node.children = node.children && methods.filterTreeData(node.children, text)
              return (
                String(node.name).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
                (node.children && node.children.length)
              )
            })
        },
        // 树点击事件
        clickFn(node) {
          state.selectedKey = node.id
          if (state.categoryId !== node.code) {
            state.categoryId = node.code
            methods.onSearch(true)
          }
        },
        // 初始化
        init(init = false) {
          methods.initTable(init)
        },
        // 初始化表格（列表）
        initTable(init = false) {
          state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
          let data = {
            pageNum: state.pageInfo.currentPage,
            pageSize: state.pageInfo.pageSize,
            condition: {
              keyword: state.formInline.keyword || null,
              classificationCode: state.categoryId || null,
            },
          }
          if (data.condition.classificationCode === 'DORIS_DB') {
            data.condition.classificationCode = null
            data.condition.type = 'TABLE'
          }
          state.loading = true
          getAssetsList(data)
            .then((res) => {
              state.loading = false
              if (res.success) {
                res.data.list.forEach((item, index) => {
                  item.number = index + 1
                })
                state.tableData = res.data.list
                state.pageInfo.total = res.data.total
                state.key++
              }
            })
            .catch(() => {
              state.tableData = []
              state.loading = false
            })
        },
        // 列表（表格）操作变化
        tablePageChange(data) {
          state.pageInfo.currentPage = data.currentPage
          state.pageInfo.pageSize = data.pageSize
          methods.init()
        },
        tableSelectionChange(list) {
          state.selectIds = list.map((val) => val.id)
          let names = list.map((val) => val.name)
          if (names.length > 2) {
            state.selectName = names[0] + ',' + names[1] + '等' + names.length + '个资产'
          } else {
            state.selectName = names.toString()
          }
        },
      }

      onMounted(() => {
        methods.setTableHeight()
        const { name, projectCode } = toRefs(store.state.user.currentProject)
        state.formInline.projectCode = projectCode.value
        state.formInline.projectName = name.value
        methods.getTreeListFn()
      })
      return {
        buttonAuthList,
        toStyle,
        state,
        selectTree,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;

    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;

      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;

          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }

        &.date {
          height: 36px;

          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }

        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }

        &.tabs {
          align-items: flex-end;
          height: 48px;

          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }

        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }

            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-color: #1e89ff;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;

              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }

              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }

    .content {
      height: calc(100% - 66px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }

  .mark-name {
    width: 446px;
    overflow: hidden;
    color: #1d2129;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .top-line-tag {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;

    :deep(.el-select) {
      width: calc(100% - 120px);
      margin-right: 4px;
    }
  }

  .mark-label-item {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .data-collection-page {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    height: 100%;
    padding: 0;
    border-radius: 0;

    .data-collection-page-tree {
      box-sizing: border-box;
      width: 286px;
      height: 100%;
      padding: 8px 0;
      background-color: #fff;
      border-radius: 2px;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 36px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }

      &-ipt {
        margin-bottom: 8px;
        padding: 0 12px;

        :deep(.nancalui-input-slot__suffix) {
          opacity: 0.5;

          .icon-search {
            font-weight: normal;
            transform: scale(1.4);
          }
        }
      }

      :deep(.tree-box) {
        height: calc(100% - 76px);
        padding: 0 12px;
      }
    }

    .data-collection-page-content {
      width: calc(100% - 10px - var(--aside-width));
      height: 100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;

      .out-box {
        height: 100%;

        &-btn {
          padding: 8px;

          :deep(.nancalui-button) {
            .button-content {
              display: flex;
              align-items: center;
              justify-content: center;

              .icon {
                margin-right: 4px;
              }
            }
          }
        }
      }

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 48px);
        padding: 0;

        .nancalui-table {
          .taskName {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .icon-status-svg {
              margin-right: 4px;
              font-size: 18px;
            }
          }

          .envType {
            &-name {
              width: max-content;
              padding: 0 8px;
              color: #447dfd;
              font-size: 12px;
              line-height: 20px;
              background: #f0f7ff;
              border: 1px solid #bfd9ff;
              border-radius: 10px;

              &.test {
                color: #04c495;
                background: rgba(230, 255, 244, 0.7);
                border: 1px solid #75ebc2;
              }
            }
          }

          .taskStatus {
            .circle {
              display: inline-block;
              width: 6px;
              height: 6px;
              margin-right: 4px;
              background-color: $themeBlue;
              border-radius: 50%;

              &.green {
                background-color: #00ca5f;
              }

              &.gray {
                background-color: #b8b8b8;
              }
            }
          }

          .rateTime-box {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .status-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .status {
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 6px;

              &.PUBLISH {
                background-color: #04c495;
              }

              &.CREATED {
                background-color: #447dfd;
              }

              &.OFFLINE {
                background-color: #b8b8b8;
              }
            }
          }
        }

        &.empty-list {
          height: calc(100% - 50px);
          background-color: #fff;
          border-radius: 8px;
        }

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }

      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }

      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }

      .datasourceType {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &-img {
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
      }

      .datasourceSecret {
        box-sizing: border-box;
        width: 40px;
        height: 20px;
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        background-color: #f4f4f5;
        border: 1px solid rgba(177, 179, 184, 0.53);
        border-radius: 2px;

        &.green {
          color: #31b046;
          background-color: #ebfaed;
          border: 1px solid #31b046;
        }

        &.blue {
          color: #1e89ff;
          background-color: #ebf4ff;
          border: 1px solid #1e89ff;
        }
      }
    }
  }

  .template-type-box {
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .name {
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    svg {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .custom {
    padding: 6px;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 14px;

      .icon {
        color: #8091b7;
        font-size: 16px;
        cursor: pointer;
      }
    }

    &-name {
      margin-top: 8px;
    }

    &-color {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;

      .nancalui-switch {
        margin-left: 6px;
      }

      &.second {
        margin-top: 8px;
      }

      &-label {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 4px;

        .icon {
          display: none;
          color: #fff;
          font-size: 12px;
        }

        &.checked {
          box-shadow: 0 0 0 1px #447dfd;

          .icon {
            display: block;
          }
        }
      }
    }

    &-footer {
      margin-top: 14px;
      text-align: right;

      .nancalui-button {
        min-width: 40px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }
  :deep(.el-loading-mask) {
    .el-loading-spinner {
      .circular {
        width: 48px !important;
        height: 48px !important;
      }
    }
  }
</style>
