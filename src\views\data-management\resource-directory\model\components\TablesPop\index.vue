<template>
  <n-modal draggable :title="title" v-model="entityShow" width="1100px" :before-close="beforeClose">
    <section class="content-table">
      <n-tabs v-model="activeName" :before-change="handleClick" type="card">
        <n-tab :title="type" id="first">
          <Tables ref="tables" :cell="cell" :subjectList="subjectList" />
        </n-tab>
        <n-tab title="列" id="second">
          <Column ref="col" :cell="cell" @changePK="changePK" />
        </n-tab>
        <n-tab v-if="isTable" title="关系" id="third">
          <Relation ref="relation" :cell="cell" />
        </n-tab>
        <!-- <n-tab v-if="isTable" title="索引" id="fourth">
          <Indexex ref="indexes" :activeNameTable="activeName" :cell="cell" />
        </n-tab>
        <n-tab v-if="isTable" title="约束条件" id="fifth">
          <Constraint ref="constraint" :cell="cell" :isShow="activeName === 'fifth'" />
        </n-tab>
        <n-tab v-if="cellData.type !== 'BLOOD'" :title="`${type}信息`" id="sixth">
          <TableInfo ref="tableInfo" :cell="cell" :isShow="activeName === 'sixth'" />
        </n-tab> -->
      </n-tabs>
    </section>
    <template #footer v-if="!disabled">
      <n-modal-footer>
        <n-button variant="solid" color="primary" @click="onSubmit(true)">确 定</n-button>
        <n-button @click="closePop(true)">取 消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script>
  import Tables from './Table.vue'
  import Column from './Column.vue'
  import Relation from './Relation.vue'
  import Indexex from './Indexes.vue'
  import FlowGraph from '../../graph'
  import TableInfo from './TableInfo.vue'
  import Constraint from './Constraint.vue'
  import { reName } from '../../utils/index.js'

  export default {
    components: {
      Tables,
      Column,
      Relation,
      Indexex,
      TableInfo,
      Constraint,
    },
    props: {
      cell: {
        type: Object,
        dufault: {},
      },
      graphList: {
        type: Object,
        dufault: [],
      },
      subjectGuid: {
        type: String,
        dufault: '',
      },
      isShow: {
        type: Boolean,
        dufault: false,
      },
    },
    data() {
      return {
        entityShow: false,
        activeName: 'first',
        sqlData: [],
        data: {
          table: {},
          col: [],
          relation: {},
        },
        repeatName: false,
        preSubject: '',
      }
    },
    computed: {
      isTable() {
        const { type } = this.cellData
        return type !== 'VIEW' && type !== 'BLOOD'
      },
      cellData() {
        return this.cell?.data
      },
      title() {
        return this.cellData?.type === 'VIEW' ? '视图查看' : '表编辑'
      },
      type() {
        return this.cellData?.type === 'VIEW' ? '视图' : '表'
      },
      disabled() {
        // return (
        //   this.cellData?.disableMove ||
        //   this.cellData?.type === 'BLOOD' ||
        //   this.cellData?.type === 'VIEW'
        // )
        return true
      },
      subjectList() {
        return this.graphList?.map((item) => {
          return {
            subject: item.subjectName,
            guid: item.subjectGuid,
          }
        })
      },
    },
    watch: {
      activeName(val) {
        if (val === 'third') {
          this.$refs.relation.relationSearch()
          // 防止表名修改
        } else if (val === 'fourth') {
          this.$refs.indexes.PKChanged()
        }
      },
      isShow(val) {
        this.entityShow = val
        if (!val) {
          this.activeName = 'first'
        }
      },
    },
    methods: {
      changePK() {
        this.$refs.indexes.PKChanged()
      },
      handleClick(activeName, oldActiveName) {
        if (!this.disabled) {
          this.onSubmit()
        }
      },
      onSubmit(isClose) {
        if (this.activeName === 'first') {
          this.updateTab(isClose)
        } else if (this.activeName === 'second') {
          this.updateCol(isClose)
        } else if (this.activeName === 'fourth') {
          this.updateIndex(isClose)
        } else if (this.activeName === 'sixth') {
          this.updateInfo(isClose)
        } else if (this.activeName === 'fifth') {
          this.updateConstraint(isClose)
        } else if (isClose) {
          this.closePop()
        }
      },
      checkRepeat() {
        let data = this.$refs.tables.$data.data
        // 遍历判断名称是否重复，重复之后报错
        let repeatName = false
        const allJsons = []
        this.graphList.forEach((item) => {
          if (item.json) {
            allJsons.push(...item.json)
          }
        })
        allJsons.forEach((item) => {
          if (
            item.id !== this.cell.id &&
            item.data?.table?.tabName === data?.tabName &&
            !item?.data?.isShare
          ) {
            repeatName = true
          }
        })
        if (repeatName) {
          this.$message.warning('表名不能重复！已自动进行重命名。')
        }
        return [repeatName, allJsons]
      },
      // 更新表
      async updateTab(isClose) {
        let params = this.$refs.tables.$data.data
        let data = { ...this.cell.data }
        if (!params.tabName) {
          this.$message.error('表名不能为空！')
          return
        }
        const [hasRepeat, allJsons] = this.checkRepeat()
        if (hasRepeat) {
          const list = allJsons
            ?.filter((item) => item.id !== this.cell.id)
            ?.map((item) => {
              return {
                tabName: item.data?.table?.tabName,
              }
            })
          params.tabName = reName(list, 'tabName', params.tabName)
        }
        // 记录修改前主题域
        const subject = this.subjectList?.find((item) => item.subject === data.table?.subject)
        if (subject) {
          this.preSubject = subject.guid
        } else {
          this.preSubject = data.table?.subject
        }
        // 如果引用变成原生，删除引用标记
        if (this.preSubject !== this.subjectGuid && this.subjectGuid === params.subject) {
          delete data.isQuote
        }
        // 如果原生变成引用，添加引用标记
        if (this.preSubject === this.subjectGuid && this.subjectGuid !== params.subject) {
          data.isQuote = true
        }
        data.subjectGuid = params.subject
        data.table = params
        delete data.isAdd
        this.cell.setData(data, { overwrite: true })
        // 保存成功，去掉标记边框
        const attrs = this.cell.getAttrs()
        if (attrs.body.strokeWidth) {
          this.cell.setAttrs({
            body: {
              strokeWidth: 0,
            },
          })
        }
        if (!hasRepeat) {
          this.$message({
            type: 'success',
            message: '表暂存成功!',
          })
        }
        if (isClose) {
          this.closePop()
        }
      },
      // 更新列
      async updateCol(isClose) {
        let params = this.$refs.col.$data.data
        let data = { ...this.cell.data }
        let colStatus = false
        params.forEach((item) => {
          item.length = Number(item.length)
          item.scale = Number(item.scale)
          item.tabGuid = this.cell.id

          if (item.col === '') {
            colStatus = true
          }
          if (item.standard?.code) {
            item.standard = String(item.standard.code)
          }
          if (item.charUsed?.code) {
            item.charUsed = String(item.charUsed.code)
          }
        })
        if (colStatus) {
          this.$message.error('列名不能为空')
          return
        }
        data.col = [...params]
        // 防止数据改变不触发
        this.cell.setData(data, { overwrite: true })
        this.$message({
          type: 'success',
          message: '列暂存成功!',
        })
        if (isClose) {
          this.closePop()
        }
      },
      updateInfo(isClose) {
        this.$refs.tableInfo.setInfoData()
        if (isClose) {
          this.closePop()
        }
      },
      updateConstraint(isClose) {
        this.$refs.constraint.saveConstraint()
        if (isClose) {
          this.closePop()
        }
      },
      updateIndex(isClose) {
        this.$refs.indexes.selectIndex()
        let params = this.$refs.indexes.$data.indexColumnInfoDOList
        let script = this.$refs.indexes.$data.script
        const filedEmpty =
          params.findIndex((item) => !item.indexDetailDTOList?.length && item.type === 'other') > -1
        const indexNameEmpty = params.findIndex((item) => !item.indexName) > -1
        if (indexNameEmpty) {
          this.$message.error('索引名不能为空！')
          return false
        }
        if (filedEmpty) {
          this.$message.error('索引必须选择列！')
          return false
        }
        params.forEach((item) => {
          // eslint-disable-next-line no-unused-expressions
          item.indexDetailDTOList?.forEach((row, index) => {
            row.indexOrder = index + 1
          })
        })
        let data = this.cell.data
        data.indexColumnInfoDOList = params
        data.script = script
        // 防止数据改变不触发
        this.cell.setData(data, { overwrite: true })
        if (isClose) {
          this.closePop()
        }
      },
      beforeClose(done) {
        const data = { ...this.cell.data }
        // 如果修改了主题域或者修改了引用实体数据需要进行额外操作
        const curSubject = data.table?.subject
        if (this.subjectGuid !== curSubject || this.preSubject !== curSubject) {
          this.$emit('originChange', this.preSubject)
        }
        if (this.entityShow) {
          this.closePop()
        }
        done()
      },
      // 关闭弹窗
      closePop(isCancle) {
        const data = { ...this.cell.data }
        if (data.isAdd && isCancle) {
          const { graph } = FlowGraph
          graph.removeNode(this.cell)
        }
        this.$emit('closepop')
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  :deep(.table) {
    h3 {
      margin: 0;
      padding-left: 10px;
      font-size: 14px;
      line-height: 28px;
      background: rgba(37, 123, 255, 0.08);
    }
    .table-box {
      display: grid;
      grid-template-columns: 100px auto;
      border-bottom: 1px solid #e1e1e1;
    }
    .table-node {
      box-sizing: border-box;
      height: 32px;
      padding: 0 10px;
      color: #333333;
      font-size: 12px;
      line-height: 30px;
      border-top: 1px solid #e1e1e1;

      &:nth-child(odd) {
        border-right: 1px solid #e1e1e1;
      }
      input {
        width: 100%;
        height: 24px;
        border: none;
        outline: none;
      }

      // .nancalui-input__wrapper,
      // .nancalui-select__selection {
      // border: none;
      // border-color: ;
      // box-shadow: none;
      // }
      &-select {
        border-color: blue;
      }
    }
  }
  :deep(.el-dialog) {
    width: 1020px;
    .el-tabs {
      &__header {
        padding: 0;
        border: none;
      }
      &__item {
        margin-right: 4px;
        background: #f4f4f4;
        border: 1px solid #cfcfcf !important;

        &.is-active {
          color: $themeFontColor !important;
          background: #fff;
          border-bottom-color: #fff !important;
          border-radius: 2px 2px 0px 0px;
        }
      }
      &__content {
        border: 1px solid #cfcfcf;
      }
    }
  }

  h3 {
    color: #999;
    font-size: 14px;
  }
  :deep(.nancalui-tabs) {
    height: auto !important;
  }
  :deep(.nancalui-tabs-pane .nancalui-input--disabled) {
    background: #f5f7fa !important;
  }
</style>
