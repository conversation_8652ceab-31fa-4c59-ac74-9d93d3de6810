<template>
  <div class="container" v-if="state.visible">
    <div class="btn-group">
      <!-- 关闭弹窗 -->
      <SvgIcon icon="icon-close" @click.stop.prevent="close" />
    </div>
    <el-form ref="formRef" :model="state.formData" :rules="state.rules" :label-width="100">
      <el-form-item label="数据类型：" prop="type">
        <el-select v-model="state.formData.type" placeholder="请选择">
          <el-option label="项目基本信息" value="PROJECT" />
          <el-option label="项目团队信息" value="PROJECT_MEMBER" />
          <el-option label="项目计划进展" value="PROJECT_PLAN" />
          <el-option label="项目质量规则" value="PROJECT_QUALITY_RULE" />
          <el-option label="项目数据问题" value="PROJECT_PROBLEM" />
          <el-option label="项目主题模型" value="PROJECT_TOPIC_MODEL" />
          <el-option label="项目数据标注" value="PROJECT_DATA_ANNOTATION" />
          <el-option label="项目数据标注说明" value="PROJECT_DATA_ANNOTATION_EXPLAIN" />
          <el-option label="项目数据定义" value="PROJECT_DATA_DEFINITION" />
          <el-option label="项目治理任务" value="PROJECT_GOVERNANCE_TASK" />
          <el-option label="项目业务表单" value="PROJECT_BIZTBL_BLL" />
          <el-option label="项目血缘图基础信息" value="PROJECT_BLDP_DRWNG_BSC_INFO" />
          <el-option label="项目血缘图关系" value="PROJECT_BLDP_DRWNG_REL" />
          <el-option label="项目血缘图规则维护" value="PROJECT_BLDP_DRWNG_REL_RULE" />
          <!--          <el-option label="主数据" value="PROJECT_MASTER_DATA" />-->
          <el-option label="个人基本信息" value="userInfo" />
          <el-option label="个人任职信息" value="position" />
          <el-option label="个人知识产权" value="intellectual" />
          <el-option label="标注项目" value="annotationProject" />
          <!--          <el-option label="标注项目成员" value="annotationProjectUser" />-->
          <el-option label="标注规则" value="annotationProjectRule" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据文件：" prop="file">
        <el-button type="primary" @click="upload">上传文件</el-button>
      </el-form-item>
      <el-form-item>
        <div class="upload-demo-box">
          <div v-if="state.formData.file" class="file-data">
            <div>{{ state.formData.file?.name }}</div>
            <SvgIcon
              class="icon-add-svg"
              icon="close-icon"
              title="关闭"
              @click.stop.prevent="onRemove"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <!-- 确定上传 -->
        <el-button type="primary" @click="submit">确定上传</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
  import { reactive } from 'vue'
  import { workbenchProjectUpload } from '@/api/dataManage.js'
  import request from '@/utils/request'
  // 返回值 示例 备注 messages 取第一个数组即可
  // {
  //     "data": {
  //         "status": true,
  //             "messages": [
  //                 "当前共[34]条数据,成功[34]条数据"
  //             ]
  //     },
  //     "success": true,
  //         "code": "SUCCESS"
  // }
  const formRef = ref(null)
  // 相关接口
  const apiMap = (type, data) => {
    return request({
      url: `/api/govern-management/workbench/user/import/${type}`,
      method: 'POST',
      responseType: 'blob',
      data,
    })
  }

  const state = reactive({
    visible: true, // 弹窗是否显示
    formData: { name: '', file: '' },
    rules: {
      type: [{ required: true, message: '请选择数据类型', trigger: 'blur' }],
      file: [{ required: true, message: '请选择数据文件', trigger: 'blur' }],
    },
  })
  const upload = async () => {
    // 上传文件
    // 创建文件上传dom
    const input = document.createElement('input')
    input.type = 'file'
    input.click() // 点击上传
    input.onchange = async (e) => {
      // 上传成功
      const file = e.target.files[0] // 获取文件
      state.formData.file = file // 赋值给formData
    }
  }
  const submit = async () => {
    // 确定上传
    console.log('确定上传')
    formRef.value.validate(async (valid) => {
      // 校验
      if (valid) {
        // 校验通过
        const formData = new FormData() // 创建formData
        formData.append('file', state.formData.file) // 追加文件
        if (
          [
            'userInfo',
            'position',
            'intellectual',
            'annotationProject',
            'annotationProjectUser',
            'annotationProjectRule',
          ].includes(state.formData.type)
        ) {
          const res = await apiMap(state.formData.type, formData) // 调用接口
          if (res.type == 'application/json') {
            // 假设你有一个Blob对象
            const reader = new FileReader()
            reader.onload = function (event) {
              const text = event.target.result
              const data = JSON.parse(text)
              if (data.data.status) {
                if (data.data.messages) {
                  ElMessage.success(data.data?.messages?.toString())
                } else {
                  ElMessage.success('上传成功') // 提示
                }
              } else {
                ElMessage.error(data.data?.messages?.toString() || '上传失败')
              }
            }
            reader.readAsText(res)
          }
        } else {
          const res = await workbenchProjectUpload({
            type: state.formData.type,
            file: state.formData.file,
          })
          if (res.type == 'application/json') {
            // 假设你有一个Blob对象
            const reader = new FileReader()
            reader.onload = function (event) {
              const text = event.target.result
              const data = JSON.parse(text)
              if (data.data.status) {
                if (data.data.messages) {
                  ElMessage.success(data.data?.messages?.toString())
                } else {
                  ElMessage.success('上传成功')
                }
              } else {
                ElMessage.error(data.data?.messages?.toString() || '上传失败')
              }
            }
            reader.readAsText(res)
          }
        }

        // close() // 关闭弹窗
      } else {
        // 校验失败
        ElMessage.error('请填写完整信息') // 提示
        return false // 返回false
      }
    })
  }

  const onRemove = () => {
    state.formData.file = null
  }

  const close = () => {
    // 关闭弹窗
    console.log('关闭弹窗')
    state.visible = false
  }
</script>
<style lang="scss" scoped>
  .container {
    padding: 16px;
    border-radius: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1001;
    top: 0;
    left: 0;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;

    .btn-group {
      position: absolute;
      top: 16px;
      right: 16px;
      display: flex;
    }
    .upload-demo-box {
      display: flex;
      align-items: center;

      .file-data {
        display: flex;
        align-items: center;
        div {
          margin-right: 8px;
          color: #666666;
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
        }
        svg {
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
  }
</style>
