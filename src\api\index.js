import base from './base.js'
import model from './model.js'
import assets from './assets.js'
import user from './user.js'
import dataManagement from './dataManage.js'
import dataQuality from './dataQuality.js'
import dataService from './dataService'
import dataDev from './dataDev'
import system from './system'
import project from './project.js'
import auditCenter from './auditCenter.js'
import indicatorDataSource from './indicatorDataSource'
import indicator from './indicator'
import ods from './ods'
import documentManage from './documentManage'
import offlineJob from './offlineJob'
import realTimeDevelop from './realTimeDevelop'
import dataGovernance from './dataGovernance'

export * as sceneManage from './sceneManage'
export * as dataApplication from './dataApplication'

export default {
  user,
  base,
  model,
  assets,
  dataManagement,
  dataQuality,
  dataService,
  dataDev,
  system,
  project,
  auditCenter,
  indicatorDataSource,
  indicator,
  ods,
  documentManage,
  offlineJob,
  realTimeDevelop,
  dataGovernance,
}
