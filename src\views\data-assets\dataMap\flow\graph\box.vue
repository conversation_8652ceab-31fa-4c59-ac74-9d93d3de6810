<template>
  <div class="box">
    <div class="name">{{ nodeInfo.nName }}</div>
    <div class="eng">{{ nodeInfo.nEnName }}</div>
  </div>
</template>

<script>
  // import { reactive, toRefs, inject } from 'vue'

  export default {
    inject: ['getGraph', 'getNode'],
    // setup() {
    //   const state = reactive({
    //     node: null,
    //     nodeInfo: {
    //       nName: '',
    //       nEnName: '',
    //     },
    //   })
    //   state.node = inject('getNode')
    //   const params = toRefs(state)
    //   return {
    //     ...params,
    //   }
    // },
    data() {
      return {
        nodeInfo: {
          nName: '',
          nEnName: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.nodeInfo = node.getData()
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.nodeInfo = current
      })
    },
  }
</script>

<style lang="scss" scoped>
  .box {
    width: 160px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;
    box-sizing: border-box;
    background-color: #f5faff;
    border-radius: 6px;
    color: #333;
    border: 1px solid #56b4dc;
    position: relative;

    .name,
    .eng {
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      text-align: center;
    }

    .eng {
      font-size: 12px;
      margin-top: 6px;
    }
  }
</style>
