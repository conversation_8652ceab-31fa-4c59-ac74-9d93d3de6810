<template>
  <!-- 数据服务-api管理列表 -->
  <div
    :class="['desensitization-algorithm-list container', state.isLzos ? 'isLzos' : '']"
    v-loading="state.loading"
  >
    <div class="desensitization-algorithm-page-top">
      <section class="tools">
        <div class="row">
          <div class="col">
            <span class="label">算法类型：</span>
            <n-select
              v-model="state.originalFormInline.type"
              placeholder="选择算法类型"
              allow-clear
              filter
              :options="state.algorithmTypeOptions"
            />
            <span class="label">状态：</span>
            <n-select
              v-model="state.originalFormInline.status"
              placeholder="选择算法状态"
              allow-clear
              filter
              :options="state.algorithmStatusOptions"
            />
            <span class="label">算法名称：</span>
            <n-input
              v-model="state.originalFormInline.name"
              size="small"
              placeholder="算法名称"
              clearable
            />
          </div>
          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
    </div>
    <div :class="['desensitization-algorithm-page-bottom', state.expend ? 'expend' : '']">
      <div class="desensitization-algorithm-page-bottom-table-box">
        <CfTable
          actionWidth="132"
          :isDisplayAction="true"
          :key="state.tableData"
          :tableConfig="{
            data: state.tableData,
            rowKey: 'id',
          }"
          :table-head-titles="state.tableHeadTitles"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              initTable()
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              initTable()
            },
          }"
        >
          <template #name="{ row }">
            <div class="algorithm-name-box" :title="row.name">
              {{ row.name }} <span v-if="!row.templateId">内置</span>
            </div></template
          >
          <template #type="{ row }">
            <div class="algorithm-type-box">
              <div v-if="row.type === 'HASH'"> 哈希 </div>
              <div v-else-if="row.type === 'MASK'"> 掩码 </div>
              <div v-else> 截断 </div>
            </div></template
          >
          <template #description="{ row }">
            <div class="algorithm-desc-box" :title="row.description">
              {{ row.description }}
            </div></template
          >
          <template #status="{ row }">
            <span v-if="row.status === 'NOT_CONFIGURED'" class="taskStatus"
              ><span class="circle"></span> 待配置</span
            >
            <span v-else-if="row.status === 'AVAILABLE'" class="taskStatus"
              ><span class="circle green"></span>有效</span
            >
            <span v-else-if="row.status === 'UNAVAILABLE'" class="taskStatus"
              ><span class="circle grey"></span>无效</span
            >

            <span v-else-if="row.status === 'UNTESTED'" class="taskStatus"
              ><span class="circle yellow"></span>待测试</span
            >
          </template>

          <template #editor="{ row }">
            <div class="edit-box">
              <n-button
                v-if="
                  buttonAuthList.includes(
                    'assetsManage_dataSecurity_desensitizationAlgorithm_config_edit',
                  ) && showConfigButtom(row)
                "
                variant="text"
                :disabled="row.isUsed"
                class="seeDetails has-right-border"
                @click.prevent="config(row)"
              >
                配置
              </n-button>
              <n-button
                v-if="
                  buttonAuthList.includes(
                    'assetsManage_dataSecurity_desensitizationAlgorithm_edit',
                  ) && row.templateId
                "
                variant="text"
                :disabled="row.isUsed"
                class="seeDetails has-right-border"
                @click.prevent="edit(row)"
              >
                编辑
              </n-button>
              <n-button
                v-if="
                  buttonAuthList.includes(
                    'assetsManage_dataSecurity_desensitizationAlgorithm_test_edit',
                  )
                "
                variant="text"
                class="seeDetails has-right-border"
                @click.prevent="test(row)"
              >
                测试
              </n-button>
              <n-button
                v-if="
                  buttonAuthList.includes(
                    'assetsManage_dataSecurity_desensitizationAlgorithm_delete',
                  ) && row.templateId
                "
                class="seeDetails has-right-border"
                variant="text"
                @click.prevent="deleteFn(row)"
              >
                删除
              </n-button>
            </div>
          </template>
        </CfTable>
      </div>
    </div>
    <!-- 新建自定义算法脱敏 -->
    <addCustomAlgorithm ref="addCustomAlgorithmDom" @reloadPage="reloadPage" />
    <algorithmConfigTest ref="algorithmTestDom" />
    <n-drawer
      v-model="state.drawer"
      title=""
      :size="550"
      :esc-key-closeable="false"
      :close-on-click-overlay="false"
      class="algorithm-config-drawer"
      :key="state.drawerKey"
    >
      <div class="n-drawer-body" v-loading="state.pageLoading">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">{{ state.algorithmName }}</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
        </div>
        <div class="n-drawer-body-content">
          <algorithmConfig ref="algorithmConfigDom" :desensitizer="state.desensitizer" />
        </div>
        <div class="options-box-bg">
          <div class="options-box-content">
            <n-button color="secondary" size="sm" @click.prevent="closeDrawerFn">取消</n-button>
            <n-button
              color="primary"
              size="sm"
              variant="solid"
              :loading="state.saveLoading"
              @click.prevent="submitForm"
              >确定</n-button
            >
          </div>
        </div>
      </div>
    </n-drawer>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs, getCurrentInstance } from 'vue'

  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import addCustomAlgorithm from './components/add-custom-algorithm'
  import algorithmConfigTest from './components/algorithm-config-test'
  import algorithmConfig from './components/algorithm-config-message'

  export default {
    title: 'List',
    components: { addCustomAlgorithm, algorithmConfigTest, algorithmConfig },
    props: {},
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)

      const form = ref('')
      const addCustomAlgorithmDom = ref('')
      const algorithmTestDom = ref('')
      const algorithmConfigDom = ref('')

      // 获取当前组件实例
      const { proxy } = getCurrentInstance()
      const state = reactive({
        drawerKey: 1,
        drawer: false,
        desensitizer: null,
        expend: false, //展开，收起
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        loading: false,
        saveLoading: false,
        dialogVisible: false,
        originalFormInline: {
          name: '',
          type: '',
          status: '',
          builtIn: '',
        },
        formInline: {
          name: '',
          type: '',
          status: '',
          builtIn: '',
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 100 },
          { prop: 'name', name: '算法名称', slot: 'name', width: 360 },
          { prop: 'type', name: '算法类型', slot: 'type', width: 110 },
          { prop: 'status', name: '状态', slot: 'status', width: 110 },
          { prop: 'description', name: '描述', slot: 'description' },
        ],
        tableData: [],
        tableHeight: 436,
        pagination: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        algorithmTypeOptions: [
          {
            name: '哈希',
            value: 'HASH',
          },
          {
            name: '截断',
            value: 'TRUNCATION',
          },
          {
            name: '掩码',
            value: 'MASK',
          },
        ],
        algorithmStatusOptions: [
          {
            name: '有效',
            value: 'AVAILABLE',
          },
          // {
          //   name: '无效',
          //   value: 'UNAVAILABLE',
          // },
          {
            name: '待配置',
            value: 'NOT_CONFIGURED',
          },
          // {
          //   name: '待测试',
          //   value: 'UNTESTED',
          // },
        ],
        builtInOptions: [
          {
            name: '全部',
            value: '',
          },
          {
            name: '内置',
            value: true,
          },
          {
            name: '自定义',
            value: false,
          },
        ],
        algorithmName: null,
        configId: null,
      })

      const methods = {
        //隐藏配置按钮
        showConfigButtom(row) {
          let show = false
          if (!row.templateId) {
            switch (row.name) {
              case '身份证号码掩码':
              case '银行卡号掩码':
              case 'Email掩码':
              case '电话号码掩码':
                break
              default:
                show = true
                break
            }
          }
          return show
        },
        //展开 收起
        expendSearch() {
          state.expend = !state.expend
          methods.setTableHeight()
        },
        //设置表格高度
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 151 - 112
        },

        closeDialog() {
          state.dialogVisible = false
        },
        // 重置
        resetFn() {
          state.originalFormInline = {
            name: '',
            type: '',
            status: '',
            builtIn: '',
          }
          methods.searchClickFn()
        },
        searchClickFn() {
          for (let key in state.originalFormInline) {
            state.formInline[key] = state.originalFormInline[key]
          }
          methods.initTable(true)
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            condition: {
              name: state.formInline.name || null,
              type: state.formInline.type || null,
              status: state.formInline.status || null,
              builtIn: true,
            },
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.assets['getDesensitizationAlgoList'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              let { success, data } = res
              if (success) {
                data.list.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                state.tableData = data.list
                state.pagination.total = data.total
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },

        //测试
        test(row) {
          algorithmTestDom.value?.init(row)
        },

        // 编辑
        edit(row) {
          addCustomAlgorithmDom.value?.init(row)
        },
        // 新增
        Add() {
          addCustomAlgorithmDom.value.init()
        },
        // 删除
        deleteFn(row) {
          if (row.isUsed) {
            proxy.$MessageBoxService.open({
              title: '提示',
              content: '脱敏算法已被指定列或者脱敏策略引用，不可删除，若需删除，请先修改引用关系。',
            })
          } else {
            proxy.$MessageBoxService.open({
              title: '提示',
              content: '删除操作无法撤销，请确认是否删除？',
              save: () => {
                api.assets.deletDesensitizationAlgo({ id: row.id }).then((res) => {
                  let { success } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '删除成功',
                      type: 'success',
                    })
                    if (state.tableData.length === 1) {
                      methods.initTable(true)
                    } else {
                      methods.initTable()
                    }
                  }
                })
              },
            })
          }
        },

        //新建自定义算法完成
        reloadPage() {
          methods.initTable(true)
        },
        //关闭抽屉
        closeDrawerFn() {
          state.drawerKey++
          state.drawer = false
        },
        //配置
        config(row) {
          state.algorithmName = row.name
          state.configId = row.id
          state.desensitizer = row.desensitizer
          state.drawer = true
          nextTick(() => {
            algorithmConfigDom.value?.editInit(row)
          })
        },
        //保存配置
        async submitForm() {
          if (algorithmConfigDom.value) {
            let data = {}
            let result = {}
            result = await algorithmConfigDom.value.getAllData()
            if (!result.passed) return
            if (result.passed) {
              state.saveLoading = true
              data['dateTruncConf'] = {
                desensitizationDateType: result.data.desensitizationDateType || null,
                truncPosition: result.data.truncPosition || null,
              }
              data['hashConf'] = {
                salt: result.data.salt || null,
                secKey: result.data.secKey || null,
              }
              data['maskPositionConf'] = {
                from: result.data.from || null,
                mInTheFront: result.data.mInTheFront || null,
                nInTheBack: result.data.nInTheBack || null,
                to: result.data.to || null,
              }
              data['numericalValueTruncConf'] = {
                reserveX: result.data.reserveX || null,
              }
              data.id = state.configId
              api.assets
                .algoConfigureUpdate(data)
                .then((res) => {
                  state.saveLoading = false
                  let { success, data } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message: '配置成功',
                      type: 'success',
                    })
                    state.drawer = false
                    methods.initTable(true)
                  }
                })
                .catch(() => {
                  state.saveLoading = false
                })
            }
          }
        },
      }
      methods.setTableHeight()
      onMounted(() => {
        methods.initTable(true)
      })

      return {
        buttonAuthList,
        state,
        form,
        addCustomAlgorithmDom,
        algorithmTestDom,
        algorithmConfigDom,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .desensitization-algorithm-list {
    height: 100%;
    &.isLzos {
      padding: 0;
    }
    .desensitization-algorithm-page-top {
      margin-bottom: 10px;
      background-color: #fff;
      border-radius: 8px;
      .tools {
        height: 50px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 2px;
        .row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 50px;
          padding: 0 16px;

          &-title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            width: 100%;
            height: 52px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            :deep(.button-content) {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
            }

            //&:before {
            //  position: absolute;
            //  top: 17px;
            //  left: 0;
            //  width: 4px;
            //  height: 18px;
            //  background: var(
            //    --Radial,
            //    radial-gradient(147.97% 153.19% at -7.73% -0.34%, #f7f8fd 0%, #447dfd 63.42%)
            //  );
            //  border-radius: 0 4px 4px 0;
            //  content: '';
            //}
            &-btn {
              position: absolute;
              right: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              color: $themeBlue;
              font-weight: 400;
              font-size: 16px;
              border-radius: 6px;
              cursor: pointer;

              &:hover {
                background-color: #e3ecff;
              }
            }
          }
          &.date {
            height: 36px;
            :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
              width: 260px;
            }
          }
          .col {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .label {
              color: #1d2129;
              font-size: 14px;
            }
          }
          &.tabs {
            align-items: flex-end;
            height: 48px;
            :deep(.nancalui-tabs) {
              .nancalui-tabs-nav-tab {
                border-bottom: none;
              }
            }
          }
          :deep(.button-content) {
            .add {
              display: flex;
              align-items: center;
              justify-content: center;
              .icon {
                margin-right: 4px;
              }
              .arrow {
                margin-left: 4px;
                color: #fff;
                font-size: 16px;
              }
            }
          }

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 0 8px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 14px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #ecf7ff;
            }

            .icon {
              margin-right: 4px;
            }
          }

          .nancalui-input,
          .nancalui-select {
            width: 260px;
            margin-right: 32px;
          }

          .search {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-btn {
              width: 60px;
              height: 30px;
              text-align: center;
              line-height: 30px;
              color: #fff;
              font-weight: 400;
              font-size: 14px;
              border: 1px solid #1e89ff;
              border-radius: 2px;
              cursor: pointer;
              background-color: #1e89ff;

              &.reset {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;
                color: #1d2129;
                background-color: #fff;
                border: 1px solid #dcdfe6;
                &:hover {
                  color: #479dff;
                  background-color: #fff;
                  border: 1px solid #479dff;
                }
                .icon {
                  margin-left: 4px;
                  font-size: 10px;
                }
              }

              &:hover {
                background-color: #479dff;
                border: 1px solid #479dff;
              }
            }
          }
        }
      }
    }
    .desensitization-algorithm-page-bottom {
      height: calc(100% - 60px);
      background-color: #fff;
      border-radius: 2px;
      .desensitization-algorithm-page-bottom-table-box {
        padding: 16px 0 0;
        height: 100%;
        .algorithm-name-box {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          span {
            display: inline-block;
            align-content: center;
            width: 44px;
            height: 24px;
            margin-right: 6px;
            padding: 0 4px;
            color: #ed4ea5;
            font-size: 14px;
            line-height: 22px;
            text-align: center;
            background: #fff0f6;
            border: 1px solid #ffd4e6;
            border-radius: 2px;
          }
        }
        .algorithm-type-box {
          svg {
            margin-right: 6px;
            font-size: 16px;
          }
        }
        .algorithm-desc-box {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .taskStatus {
          .circle {
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 4px;
            background-color: #b8b8b8;
            border-radius: 50%;

            &.green {
              background-color: #04c495;
            }

            &.yellow {
              background-color: #ff7d00;
            }

            &.grey {
              background-color: #b8b8b8;
            }
          }
        }
        .edit-box {
          .seeDetails {
            color: $themeBlue;
            &.nancalui-button--disabled {
              svg {
                color: #b8b8b8;
              }
            }
          }
        }
      }
    }
  }
</style>
<style lang="scss">
  .algorithm-config-drawer {
    .n-drawer-body-header {
      position: relative;
      &:before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }
    .n-drawer-body-content {
      padding: 16px !important;
    }
    .options-box-bg {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 64px;
      padding: 16px;
      background-color: #fff;
      border-radius: 0 0 8px 8px;

      .options-box-content {
        display: inline-block;
        width: 100%;
        text-align: right;
        background-color: #fff;
      }
    }
  }
</style>
