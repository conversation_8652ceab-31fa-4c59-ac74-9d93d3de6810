<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>ic分层@2x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="算法管理-查看算法" transform="translate(-96.000000, -913.000000)">
            <g id="基础信息备份" transform="translate(80.000000, 845.000000)">
                <g id="页头-标题栏备份-2" transform="translate(0.000000, 52.000000)">
                    <g id="ic分层" transform="translate(16.000000, 16.000000)">
                    
                        <g id="编组-2" transform="translate(2.898400, 3.002000)" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
                            <polyline id="路径" points="10.2012 0 5.2012 6 0.2012 0"></polyline>
                            <polyline id="路径" points="10 3.996 5 9.996 0 3.996"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>