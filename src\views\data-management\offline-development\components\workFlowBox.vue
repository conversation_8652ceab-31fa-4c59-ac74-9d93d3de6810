<template>
  <iframe ref="iframeRef" class="offlineWorkFlow" :src="url"></iframe>
</template>
<script setup>
  import { onActivated } from 'vue'
  const iframeRef = ref(null)
  const url = ref('/#/workFlow')
  const emits = defineEmits(['openLabelFn', 'changeTree', 'changeLoadingStatus'])
  const props = defineProps({
    currentId: {
      type: String,
      default: null,
    },
    processId: {
      type: String,
      default: null,
    },
  })
  onActivated(() => {
    nextTick(() => {
      const iframeWindow = iframeRef.value.contentWindow
      iframeWindow.iframeGetCurrentId = props.currentId
      iframeWindow.iframeGetProcessId = props.processId
      window.iframeWindow3678_dsfw = iframeWindow
    })
  })

  nextTick(() => {
    const iframeWindow = iframeRef.value.contentWindow
    iframeWindow.iframeGetCurrentId = props.currentId
    iframeWindow.iframeGetProcessId = props.processId
    window.iframeWindow3678_dsfw = iframeWindow
  })

  const expose = {
    get state() {
      return iframeRef.value?.contentWindow?.iframeGetState()
    },
    get FlowGraph() {
      return iframeRef.value?.contentWindow?.iframeGetFlowGraph()
    },
  }
  const handler = {
    get(expose, prop, receiver) {
      if (prop in expose) {
        return Reflect.get(expose, prop, receiver)
      } else {
        return Reflect.get(expose?.state || {}, prop, receiver)
      }
    },
  }

  const proxy = new Proxy(expose, handler)
  window.iframeGetOpenLabelFn = (data) => {
    emits('openLabelFn', data)
  }
  window.iframeChangeTree = () => {
    emits('changeTree')
  }
  window.iframeChangeLoadingStatus = (data) => {
    emits('changeLoadingStatus', data)
  }
  const funList = []
  window.iframeSetItem = (key, value) => {
    funList.push(() => {
      sessionStorage.setItem(key, value)
    })
    return true
  }
  onBeforeUnmount(() => {
    funList.forEach((fn) => {
      fn()
    })
  })
  defineExpose(proxy)
</script>
<style lang="scss" scoped>
  .offlineWorkFlow {
    width: 100%;
    height: 100%;
    border: none;
  }
</style>
