<template>
  <!-- api数据采集  -->
  <div class="doc-collection">
    <div
      :class="{
        'doc-collection-add': true,
        container: true,
      }"
    >
      <div class="add-box">
        <div class="page-title">
          新增API采集
          <div class="detail-back-box" @click.prevent="cancel"> 返回 </div>
        </div>

        <div class="box-content" style="overflow: auto">
          <div class="box-content-inside">
            <div class="inside-box inside-box-content">
              <div class="content-title">
                <span>数据源信息</span>
              </div>
              <n-form
                v-if="state.basicTitle"
                ref="sourceForm"
                class="base-form"
                :data="state.sourceForm"
                :rules="state.sourceRules"
                label-width="120px"
                label-align="start"
                label-suffix="："
              >
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="filelistId" label="非结构化数据清单接口">
                      <n-select
                        v-model="state.sourceForm.filelistId"
                        :allow-clear="true"
                        @value-change="changeApi"
                      >
                        <n-option
                          v-for="item in state.apiList"
                          :disabled="item.disabled || false"
                          :key="item.id"
                          :name="item.name"
                          :value="item.id"
                        />
                      </n-select>
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="fileinfoId" label="非结构化数据详情接口">
                      <n-select
                        v-model="state.sourceForm.fileinfoId"
                        :allow-clear="true"
                        @value-change="changeSource"
                      >
                        <n-option
                          v-for="item in state.apiDetailList"
                          :disabled="item.disabled || false"
                          :key="item.id"
                          :name="item.name"
                          :value="item.id"
                        />
                      </n-select>
                    </n-form-item>
                  </n-col>
                </n-row>
              </n-form>
            </div>
            <div class="inside-box inside-box-content">
              <div class="content-title">
                <span>采集处理</span>
              </div>
              <n-form
                v-if="state.basicTitle"
                ref="ruleForm"
                class="base-form"
                :data="state.ruleForm"
                :rules="state.rules"
                label-width="120px"
                label-align="start"
                label-suffix="："
              >
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="categoryId" label="非结构化数据分类">
                      <el-tree-select
                        style="width: 100%"
                        v-model="state.ruleForm.categoryId"
                        :data="state.classList"
                        node-key="id"
                        :props="{
                          label: 'name',
                          value: 'id',
                          children: 'children',
                        }"
                        filterable
                        clearable
                      />
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="type" label="重复处理">
                      <n-radio-group v-model="state.ruleForm.duplicateLogic" direction="row">
                        <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
                          item.name
                        }}</n-radio>
                      </n-radio-group>
                    </n-form-item>
                  </n-col>
                </n-row>
                <span class="label-span">数据映射：</span>
                <div id="table-line" v-loading="state.apiLoading">
                  <GraphCom ref="sceneTableLink" :oneline="true" :isEmpty="state.isEmpty" />
                </div>
              </n-form>
            </div>
            <!-- 配置信息 -->
            <div class="inside-box inside-box-content footer">
              <div class="content-title">
                <span>重跑策略</span>
              </div>
              <n-form
                v-if="state.basicTitle"
                ref="rerunForm"
                class="base-form"
                :data="state.rerunForm"
                label-width="120px"
                label-align="start"
                label-suffix="："
              >
                <n-row justify="between" :gutter="20">
                  <n-col span="10">
                    <n-form-item field="failRetryTimes" label="重跑次数(次)">
                      <n-input-number
                        v-model="state.rerunForm.failRetryTimes"
                        :min="0"
                        placeholder="请输入"
                      />
                    </n-form-item>
                  </n-col>
                  <n-col span="10">
                    <n-form-item field="failRetryInterval" label="重跑间隔(秒)">
                      <n-input-number
                        v-model="state.rerunForm.failRetryInterval"
                        :min="0"
                        placeholder="请输入"
                      />
                    </n-form-item>
                  </n-col>
                </n-row>
              </n-form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{
        'box-operate': true,
      }"
    >
      <n-button size="sm" @click.prevent="cancel">取消</n-button>
      <n-button
        class="save"
        :loading="state.loading"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="save"
        >确定</n-button
      >
    </div>
  </div>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import GraphCom from './components/graph'

  const router = useRouter()
  const sourceForm = ref()
  const ruleForm = ref()
  const rerunForm = ref()
  const sceneTableLink = ref(null)

  const state = reactive({
    basicTitle: true,
    sourceCategoryId: null,
    sourceForm: {
      filelistId: '',
      fileinfoId: '',
    },
    ruleForm: {
      categoryId: null,
      duplicateLogic: 'SKIP',
    },
    rerunForm: {
      failRetryTimes: 3,
      failRetryInterval: 3,
    },
    isEmpty: false,
    sourceRules: {
      filelistId: [{ required: true, message: '请选择非结构化数据清单接口', trigger: 'change' }],
      fileinfoId: [{ required: true, message: '请选择非结构化数据详情接口', trigger: 'change' }],
    },
    rules: {
      categoryId: [
        {
          required: true,
          message: '请选择非结构化数据分类',
          trigger: 'change',
          type: 'number',
        },
      ],
      duplicateLogic: [{ required: true, message: '请选择重复处理', trigger: 'blur' }],
    },

    disabled: false, //非创建状态下不调用逆向模型接口
    loading: false,

    tableList: [],
    apiLoading: false,
    classList: [],
    apiDetailList: [],
    apiList: [],
    typeList: [
      { name: '跳过', id: 'SKIP' },
      { name: '覆盖现有文件', id: 'COVER' },
      { name: '重命名后保存', id: 'RENAME' },
    ],
    leftTable: [
      // {
      //   id: 'fileName',
      //   filedNameEn: 'NAME',
      //   filedNameZh: '非结构化数据名称',
      // },
      // {
      //   id: 'fileMiji',
      //   filedNameEn: 'MIJI',
      //   filedNameZh: '密级',
      // },
      // {
      //   id: 'fileBeizhu',
      //   filedNameEn: 'BEIZHU',
      //   filedNameZh: '备注',
      // },
    ],
    fileList: [
      {
        id: 'fileName',
        filedNameEn: 'NAME',
        filedNameZh: '非结构化数据名称',
      },
      {
        id: 'fileMiji',
        filedNameEn: 'MIJI',
        filedNameZh: '密级',
      },
      {
        id: 'fileBeizhu',
        filedNameEn: 'BEIZHU',
        filedNameZh: '备注',
      },
    ],
  })
  const getClassifyTreeList = async () => {
    const res = await api.documentManage.getClassifyTreeList()
    state.classList = res.data
    if (router.currentRoute.value?.query?.categoryId) {
      state.ruleForm.categoryId = Number(router.currentRoute.value?.query?.categoryId)
    }
  }
  const getApiSelect = async () => {
    const res = await api.documentManage.getApiDetailList()
    state.apiDetailList = res.data
    state.apiList = res.data
  }
  const changeApi = (value) => {
    //文非结构化数据详情接口下拉禁用当前选择的非结构化数据清单
    state.apiDetailList = state.apiDetailList.map((item) => {
      return { ...item, disabled: item.id === value.value }
    })
  }
  const changeSource = async (value) => {
    //非结构化数据清单下拉禁用当前选择的非结构化数据详情接口
    state.apiList = state.apiList.map((item) => {
      return { ...item, disabled: item.id === value.value }
    })
    if (value.value) {
      sceneTableLink.value.changTable({
        leftData: [],
        rightData: state.fileList,
      })
      //查询api的详情接口获取参数信息
      // const apiDetailData = await api.project.getDatasourceAllDetail({ id: value.value })
      // if (!apiDetailData.success) {
      //   ElNotification({
      //     title: '提示',
      //     message: '获取非结构化数据详情失败',
      //     type: 'error',
      //   })
      //   return
      // }
      // let params = {
      //   confidentialityLevel: apiDetailData.data.confidentialityLevel,
      //   dataCategoryId: apiDetailData.data.dataCategoryId,
      //   dataStructureType: apiDetailData.data.dataStructureType,
      //   datasourceType: apiDetailData.data.datasourceType,
      //   description: apiDetailData.data.description,
      //   enabled: apiDetailData.data.enabled,
      //   envType: apiDetailData.data.envType,
      //   extConf: apiDetailData.data.extConf,
      //   name: apiDetailData.data.name,
      //   status: apiDetailData.data.status,
      //   type: apiDetailData.data.type,
      //   url: apiDetailData.data.url,
      // }

      state.apiLoading = true
      const leftDataRes = await api.documentManage.getSourceJson({ id: value.value })
      let { success, data } = leftDataRes
      state.apiLoading = false
      if (success) {
        state.leftTable = []
        data.forEach((val) => {
          state.leftTable.push({
            id: val,
            filedNameEn: val,
            filedNameZh: val,
          })
        })
        sceneTableLink.value.changTable({
          leftData: state.leftTable,
          rightData: state.fileList,
        })
      } else {
        ElNotification({
          title: '提示',
          message: '详情接口获取失败',
          type: 'error',
        })
      }
    }
  }
  const initDetailData = () => {
    //----获取映射信息，测试数据，全部删掉----
    // let data = {
    //     confidentialityLevel: 'INTERIOR',
    //     dataCategoryId: '89',
    //     dataStructureType: 'ALL_STRUCTURE',
    //     datasourceType: 'API',
    //     description: '装配任务信息api_数据源',
    //     enabled: true,
    //     envType: 'OFFICIAL',
    //     extConf:
    //       '{"methodType":"GET","table":"table_list","headers":{},"contentType":"none","params":{"pageNum":"1","pageSize":"10","token":"c5f0b1563982876e5bf5e06bb981e41c"}}',
    //     name: '装配任务信息api_数据源',
    //     status: 'CREATED',
    //     type: 'PROJECT',
    //     url: 'https://ledata.nancalcloud.com:888//api/govern-data-assets/res/ITEM',
    //   }
    //   state.apiLoading = true
    //   api.project
    //     .testDatasource(data)
    //     .then((res) => {
    //       let { success } = res
    //       state.apiLoading = false
    //       if (success) {
    //         let { success: go, data } = res.data
    //         if (go) {
    //           state.leftTable = []
    //           // const test = ["code","data","success","data.list","data.total"]
    //           data.dataStructure.forEach((val) => {
    //             state.leftTable.push({
    //               id: val,
    //               filedNameEn: val,
    //               // filedNameZh: val,
    //             })
    //           })
    //           const relationDataMap = [
    //             {
    //               sourceFiledId: "data.list.ao_code",
    //               sourceFiledNameEn: "data.list.ao_code",
    //               sourceFiledNameZh: "data.list.ao_code",
    //               targetFiledId: "fileName",
    //               targetFiledNameEn: "NAME",
    //               targetFiledNameZh: "非结构化数据名称",
    //             },
    //           ]
    //           sceneTableLink.value.changTable({
    //             leftData: state.leftTable,
    //             rightData: state.fileList,
    //             relationDataMap: relationDataMap,
    //           })
    //         }
    //       } else {
    //         ElNotification({
    //           title: '提示',
    //           message: '详情接口获取失败',
    //           type: 'error',
    //         })
    //       }
    //     })
    //     .catch(() => {
    //       state.apiLoading = false
    //     })
    //----获取映射信息，测试数据，全部删掉----

    state.apiLoading = true
    api.documentManage
      .getCollectApi({ id: state.apiId })
      .then(async (res) => {
        let { success, data } = res
        if (success) {
          state.sourceForm = {
            fileinfoId: data.fileinfoId.toString(),
            filelistId: data.filelistId.toString(),
          }
          state.ruleForm = {
            categoryId: data.categoryId,
            duplicateLogic: data.duplicateLogic,
          }
          state.rerunForm = {
            failRetryTimes: data.failRetryTimes,
            failRetryInterval: data.failRetryInterval,
          }
          changeApi({ value: data.filelistId })
          await changeSource({ value: data.fileinfoId })
          let relationDataMap = []
          if (data.jsonInfo) {
            relationDataMap = JSON.parse(data.jsonInfo)
          }
          sceneTableLink.value.changTable({
            leftData: state.leftTable,
            rightData: state.fileList,
            relationDataMap,
          })
          setTimeout(() => {
            state.apiLoading = false
          }, 1000)
        }
      })
      .catch(() => {
        state.apiLoading = false
      })
  }
  // 采集任务保存
  const save = async () => {
    let data = sceneTableLink.value.getGraphData()
    let edges = data.cells.filter((item) => item.shape === 'edge')
    if (!edges.length) {
      state.isEmpty = true
    } else {
      state.isEmpty = false
    }
    let source_result = await new Promise((resolve) => {
      sourceForm.value.validate((valid) => {
        resolve({ passed: valid })
      })
    })
    let rule_result = await new Promise((resolve) => {
      ruleForm.value.validate((valid) => {
        resolve({ passed: valid })
      })
    })
    if (!rule_result.passed || !source_result) return
    if (edges.length) {
      let synRelationList = edges.map((item) => {
        let sourceMapIndex = item.source.port.split('-')[1]
        let targetMapIndex = item.target.port.split('-')[1]
        return {
          sourceFiledId: state.leftTable[sourceMapIndex].id,
          sourceFiledNameEn: state.leftTable[sourceMapIndex].filedNameEn,
          sourceFiledNameZh: state.leftTable[sourceMapIndex].filedNameZh,
          targetFiledId: state.fileList[targetMapIndex].id,
          targetFiledNameEn: state.fileList[targetMapIndex].filedNameEn,
          targetFiledNameZh: state.fileList[targetMapIndex].filedNameZh,
        }
      })

      let params = {
        ...state.sourceForm,
        ...state.rerunForm,
        ...state.ruleForm,
        jsonInfo: JSON.stringify(synRelationList),
      }
      console.log(params, 'params')
      let fn = 'collectAddApi'
      if (state.apiId) {
        fn = 'collectUpdateApi'
        params.id = state.apiId
      }
      state.loading = true
      api.documentManage[fn](params)
        .then((res) => {
          if (res.success) {
            ElMessage({
              type: 'success',
              message: '操作成功！',
            })
            state.apiLoading = false
            cancel()
          }
        })
        .catch((err) => {
          state.apiLoading = false
        })
    } else {
      ElMessage({
        message: '请配置数据映射！',
        type: 'error',
      })
    }
  }
  // 取消
  const cancel = () => {
    router.go(-1)
  }
  onMounted(async () => {
    state.sourceCategoryId = router.currentRoute.value.query.categoryId || null
    state.apiId = router.currentRoute.value.query.apiId || null
    await getClassifyTreeList()
    await getApiSelect()
    if (state.apiId) {
      initDetailData()
    } else {
      sceneTableLink.value.changTable({
        leftData: state.leftTable,
        rightData: state.fileList,
      })
    }
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .doc-collection {
    height: 100%;
  }
  .doc-collection-add {
    box-sizing: border-box;
    height: calc(100% - 62px);
    padding: 16px;
    .add-box {
      height: 100%;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 64px);
        overflow: auto;
        border-radius: 2px 2px 0 0;
        .inside-box {
          margin-top: 10px;
          padding: 16px;
          &-content {
            background-color: #fff;
            border-radius: 2px;
            &.footer {
              margin-top: 10px;
              border-radius: 2px 2px 0 0;
            }
          }
          .content-title {
            position: relative;
            height: 30px;
            margin-bottom: 10px;
            padding-left: 14px;
            color: #2b71c2;
            font-size: 14px;
            line-height: 30px;
            background-color: #f2f6fc;
            &:before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 18px;
              margin: auto;
              background: #1e89ff;
              content: '';
            }
          }
        }
        .base-form {
          .nancalui-row__justify--between,
          .nancalui-row__justify--start {
            .nancalui-col__span--10 {
              padding: 0 !important;
            }
          }
          .nancalui-form__item--horizontal {
            margin-bottom: 16px;
          }
          .nancalui-input,
          .nancalui-select,
          .el-select,
          .nancalui-input-number {
            max-width: 100%;
          }
          :deep(.nancalui-input-number) {
            .nancalui-input__wrapper {
              width: 100%;
            }
          }
          .check-style {
            margin-top: -6px;
            margin-bottom: 10px;
          }
          .label-span {
            margin-left: -4px;
            color: rgb(96, 98, 102);
            &:before {
              display: inline-block;
              margin-right: 2px;
              color: #f52f3e;
              vertical-align: middle;
              opacity: 1;
              content: '*';
            }
          }
          #table-line {
            position: relative;
            width: 100%;
            height: 450px;
            margin: 20px 0;
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .box-operate {
    position: absolute;
    right: 16px;
    bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    width: calc(100% - 32px);
    height: 64px;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 2px;
    :deep(.nancalui-button) {
      border-radius: 2px;
    }
  }
</style>
