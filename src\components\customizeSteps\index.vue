<template>
  <!-- 自定义 步骤器 -->

  <div class="box-steps">
    <!-- <div class="steps-container"> -->
    <div
      v-for="(item, index) in stepsData"
      :key="index"
      class="step"
      :class="{
        step: true,
        active: index === activeIndex,
        finished: index < activeIndex,
      }"
      :style="{ width: `${100 / stepsData.length}` + '%' }"
    >
      <div class="step-words">
        <div class="step-nub" v-if="index < activeIndex">
          <SvgIcon icon="have-border-ok" />
        </div>
        <div class="step-nub" v-else>{{ index + 1 }}</div>
        {{ item.label }}
      </div>
      <div v-if="index !== stepsData.length - 1" class="step-line"></div>
    </div>
  </div>
  <!-- </div> -->
</template>
<script>
  export default {
    name: '',
    components: {},
    props: {
      stepsData: {
        type: Array,
        default: () => [
          {
            label: '基础信息',
          },
          {
            label: '关联数据源',
          },
          {
            label: '授权人员',
          },
          {
            label: '核对信息',
          },
        ],
      },
    },
    data() {
      return {
        activeIndex: 0,
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      updatedActive(data) {
        this.activeIndex = data.index
      },
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  $background: #eeeeee;
  $border: #ebedf0;
  $content: #fff;
  $activeBtn: $themeBlue;
  $font: #999;
  $activeFont: #333;
  $StepsHeight: 64px;
  $noActiveColor: #c8c9cc;

  .box-steps {
    width: 100%;
    height: $StepsHeight;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    background-color: $content;
    padding: 30px 0 30px;
    border-bottom: 1px dashed $border;
    // .steps-container {
    //   display: flex;
    //   justify-content: center;
    //   width: 100%;
    //   overflow: hidden;
    //   padding-bottom: 20px;
    //   border-bottom: 1px solid $border;
    // }
    .step {
      position: relative;
      display: flex;
      align-items: center;

      &:last-child {
        width: auto !important;
      }

      .step-words {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $font;
        line-height: calc(#{$StepsHeight} - 2px);
        text-align: center;

        .step-nub {
          width: 24px;
          height: 24px;
          line-height: 24px;
          color: #fff;
          border-radius: 24px;
          margin-right: 6px;
          background-color: $noActiveColor;
        }
      }

      .step-line {
        flex: 1;
        height: 2px;
        background-color: $background;
        margin: 0 16px;
      }

      &.active {
        .step-words {
          color: $activeBtn;
        }

        .step-nub {
          background-color: $activeBtn;
        }
      }

      &.finished {
        .step-words {
          // color: $activeFont;
          color: $activeFont;
        }

        .step-nub {
          color: $activeBtn;
          background-color: $content;

          .yy-icon {
            height: 24px;
            width: 24px;
            color: $activeBtn;
          }
        }

        .step-line {
          background-color: $activeBtn;
        }
      }
    }
  }
</style>
