<template>
  <div class="commonForm-search">
    <div class="search-left">
      <span class="search_name">时间范围：</span>
      <n-range-date-picker-pro
        v-model="state.formInline.time"
        :placeholder="['开始日期', '结束日期']"
        format="YYYY-MM-DD"
        :shortcuts="state.shortcuts"
        allow-clear
      />
      <span class="search_name">项目名称：</span>
      <n-input v-model="state.formInline.keyword" placeholder="请输入项目名称" clearable />
    </div>
    <div class="commonForm">
      <div class="search_btn" @click="startSearch">查询</div>
      <div class="search_btn" @click="resetData">重置</div>
    </div>
  </div>
  <div class="addProject"> <slot name="searchLeft"></slot> </div>
</template>

<script>
  import { reactive } from 'vue'
  import ENUM from '@/const/enum'
  export default {
    title: '',
    components: {},
    props: {},
    emits: ['handleSearch'],
    setup(props, { emit }) {
      const state = reactive({
        shortcuts: ENUM.SHORTCUTS,

        formInline: {
          time: [],
          keyword: '',
        },
        searchData: {
          time: [new Date(), new Date()],
          keyword: '',
        },
      })
      const startSearch = () => {
        Object.keys(state.formInline).forEach((key) => {
          state.searchData[key] = state.formInline[key]
        })
        onSearch()
      }
      const onSearch = function () {
        emit('handleSearch', state.searchData)
      }
      const resetData = () => {
        state.formInline = {
          time: [],
          keyword: '',
        }
        state.searchData = {
          time: [],
          keyword: '',
        }
        onSearch()
      }

      return {
        state,
        startSearch,
        onSearch,
        resetData,
      }
    },
  }
</script>
<style lang="scss" scoped>
  .commonForm-search {
    display: flex;
    justify-content: space-between;
    padding: 10px 8px 10px 16px;
    background: #fff;
    border-radius: 2px;
    margin-bottom: 10px;
  }
  .nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width {
    width: 240px;
    margin-right: 32px;
  }
  .nancalui-input {
    width: 240px;
    display: inline-block;
  }
  .search_name {
    color: #1d2129;
    font-family: 'Source Han Sans CN';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .search_btn {
    display: inline-flex;
    width: 60px;
    height: 32px;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #1e89ff;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    border-radius: 2px;
    box-sizing: border-box;
    border: 1px solid #1e89ff;
    cursor: pointer;

    &:nth-child(2) {
      border: 1px solid #dcdfe6;
      background: #fff;
      color: #000;
      margin-left: 8px;
    }
  }

  .addProject {
    height: 48px;
    background: #fff;
    padding: 8px;
  }
</style>
