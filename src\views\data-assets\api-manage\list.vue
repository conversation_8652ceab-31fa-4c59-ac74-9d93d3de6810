<template>
  <div class="container-padding16">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <span class="label">API名称：</span>
          <n-input v-model="tableState.filterSearch.name" size="small" clearable />
        </div>
        <div class="col">
          <span class="label">状态：</span>
          <n-select v-model="tableState.filterSearch.status" :allow-clear="true">
            <n-option
              v-for="item in tableState.statusOps"
              :key="item.code"
              :name="item.name"
              :value="item.code"
            />
          </n-select>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="template-con-flex nc-m-t-10">
      <div class="left nc-p-t-10 asideTree">
        <div class="template-list-title nc-flex">
          <div>API分类</div>
          <SvgIcon
            class="nc-right nc-m-r-12"
            icon="icon-tree-add2"
            size="16"
            @click="dataTreeClick('add', {})"
          />
        </div>
        <div class="class-list nc-m-t-10">
          <n-input
            class="data-collection-page-tree-ipt"
            v-model="tableState.treeSearchText"
            placeholder="请输入"
            suffix="search"
            @change="(val) => treeRef.treeRef.filter(val)"
          />
          <div class="nc-m-t-8 class-tree">
            <CfTtee
              ref="treeRef"
              :filter-node-method="filterNode"
              :check-on-click-node="true"
              :default-expanded-keys="[tableState.selectedKey]"
              :current-node-key="tableState.selectedKey"
              :props="{
                children: 'children',
                label: 'name',
              }"
              node-key="id"
              :data="classState.data"
              @node-click="clickFn"
            >
              <template #btns="{ node, data: nodeData }">
                <el-popover trigger="click" placement="right-start">
                  <template #reference>
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      @click="getNodeIdFn(node)"
                    >
                      <circle
                        cx="8"
                        cy="12.5"
                        r="0.5"
                        transform="rotate(-90 8 12.5)"
                        fill="currentColor"
                        stroke="currentColor"
                      />
                      <circle
                        cx="8"
                        cy="8"
                        r="0.5"
                        transform="rotate(-90 8 8)"
                        fill="currentColor"
                        stroke="currentColor"
                      />
                      <circle
                        cx="8"
                        cy="3.5"
                        r="0.5"
                        transform="rotate(-90 8 3.5)"
                        fill="currentColor"
                        stroke="currentColor"
                      />
                    </svg>
                  </template>
                  <ul class="cf-list">
                    <li class="cf-list-item" @click="dataTreeClick('add', nodeData)">
                      <SvgIcon class="tree-icon-tool" icon="icon-tree-add2" />
                      新增</li
                    >
                    <li
                      class="cf-list-item"
                      v-if="nodeData.id !== 'ROOT'"
                      @click="dataTreeClick('edit', nodeData)"
                    >
                      <SvgIcon class="tree-icon-tool" icon="icon-tree-edit2" />
                      编辑</li
                    >
                    <li
                      class="cf-list-item"
                      v-if="nodeData.id !== 'ROOT'"
                      @click="delDataTree(nodeData,node)"
                    >
                      <SvgIcon class="tree-icon-tool" icon="icon-tree-del" />
                      删除</li
                    >
                  </ul>
                </el-popover>
              </template>
            </CfTtee>
          </div>
        </div>
      </div>
      <div class="right nc-m-l-10">
        <n-button class="nc-m-8" @click="onAdd" color="primary" variant="solid">
          <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
          添加</n-button
        >
        <div class="table" v-loading="tableState.isLoad">
          <CfTable
            actionWidth="180"
            :table-head-titles="tableState.tableHeadTitles"
            :tableConfig="{
              data: tableState.tableList,
              rowKey: 'id',
            }"
            :paginationConfig="{
              total: tableState.pagination.total,
              pageSize: tableState.pagination.pageSize,
              currentPage: tableState.pagination.currentPage,
              onCurrentChange: (v) => {
                tableState.pagination.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                tableState.pagination.pageSize = v
                onSearch()
              },
            }"
          >
            <template #url="{ row }">
              <div class="urlBox">
                <div class="urlContent" :title="row.url">
                  {{ row.url }}
                </div>
                <SvgIcon icon="icon-copy" class="icon-svg" @click="onCopyUrl(row.url)" />
              </div>
            </template>
            <template #status="{ row }">
              <span class="w-publish" v-if="row.status === 'WAITING_PUBLISH'">未发布</span>
              <span class="publish" v-else-if="row.status === 'PUBLISH'">已发布</span>
              <span class="aduit" v-else-if="row.status === 'OFFLINE_AUDIT'">下架审核中</span>
              <span class="aduit" v-else-if="row.status === 'PUBLISH_AUDIT'">发布审核中</span>
            </template>
            <template #editor="{ data: { row } }">
              <n-button variant="text" color="primary" @click="onView(row)">详情</n-button>
              <n-button
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onPush(row)"
                >发布</n-button
              >
              <n-button
                variant="text"
                color="primary"
                v-else-if="row.status === 'PUBLISH'"
                @click="onUnder(row)"
                >下架</n-button
              >
              <n-button
                variant="text"
                color="primary"
                v-if="row.status === 'WAITING_PUBLISH'"
                @click="onEdit(row)"
                >编辑</n-button
              >
              <n-popover trigger="hover">
                <SvgIcon class="nc-m-l-14" icon="icon-btn-more" />
                <template #content>
                  <div>
                    <div class="popover-item">
                      <n-button variant="text" color="primary" @click="onTest(row)">测试</n-button>
                    </div>
                    <div class="popover-item">
                      <n-button variant="text" color="primary" @click="onWhiteList(row)"
                        >白名单</n-button
                      >
                    </div>
                    <div class="popover-item">
                      <n-button
                        variant="text"
                        color="primary"
                        v-if="row.status === 'WAITING_PUBLISH'"
                        @click="onDelete(row)"
                        >删除</n-button
                      >
                    </div>
                  </div>
                </template>
              </n-popover>
            </template>
          </CfTable>
        </div>
      </div>
    </section>
    <whiteList
      v-model="tableState.whiteVisiable"
      :id="tableState.whiteId"
      :ips="tableState.whiteIps"
    />
    <n-modal
      v-model="classState.dataTreeShow"
      :title="classState.editData.type === 'add' ? '新建API分类' : '编辑API分类'"
      class="largeDialog has-top-padding"
      width="560px"
      :close-on-click-overlay="false"
      @close="classState.dataTreeShow = false"
    >
      <n-form
        :data="classState.editData"
        :rules="classState.rules"
        ref="classFormRef"
        labelSuffix="："
      >
        <n-form-item field="name" label="分类名称">
          <n-input
            v-model="classState.editData.name"
            placeholder=""
            maxLength="50"
            @blur="setDisabled(classState.data)"
          />
        </n-form-item>
        <n-form-item field="pid" label="所属分类">
          <el-tree-select
            v-model="classState.editData.pid"
            :data="classState.data"
            style="width: 100%"
            node-key="id"
            check-strictly
            :default-expanded-keys="['ROOT']"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
            }"
            filterable
            clearable
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-modal-footer>
          <n-button @click="classState.dataTreeShow = false">取消</n-button>
          <n-button color="primary" variant="solid" @click="onClassSave">确定</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>
<script setup>
  import { useRouter, useRoute } from 'vue-router'
  import CfTtee from '@/components/cfTtee'
  import whiteList from './whiteList.vue'
  const router = useRouter()
  const route = useRoute()
  import api from '@/api/index'
  import { getCurrentInstance, reactive, defineComponent, nextTick } from 'vue'
  const { proxy } = getCurrentInstance()
  const classState = reactive({
    data: [],
    dataTreeShow: false,
    editData: {},
    rules: {
      name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    },
  })
  const tableState = reactive({
    statusOps: [
      {
        name: '全部',
        code: '',
      },
      {
        name: '未发布',
        code: 'WAITING_PUBLISH',
      },
      {
        name: '发布审核中',
        code: 'PUBLISH_AUDIT',
      },
      {
        name: '已发布',
        code: 'PUBLISH',
      },
      {
        name: '下架审核中',
        code: 'OFFLINE_AUDIT',
      },
    ],
    tableList: [],
    filterSearch: {
      name: '',
      status: '',
      categoryId: null,
    },
    searchData: {
      name: '',
      status: '',
      categoryId: null,
    },
    tableHeadTitles: [
      { prop: 'apiName', name: 'API名称' },
      { prop: 'modelTable', name: '数据表', width: 300 },
      { prop: 'categoryName', name: '分类' },
      { prop: 'url', name: 'API路径', slot: 'url', width: 400 },
      { prop: 'status', name: '状态', slot: 'status' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    isLoad: true,
    whiteVisiable: false,
    whiteId: '',
    whiteIps: [],
    expandedKeys: [],
    selectedKey: null,
  })

  const getNodeIdFn=(node)=>{
    tableState.selectedKey = node.data.id
  }

  function clickFn(node) {
    tableState.selectedKey = node.id
    tableState.filterSearch.categoryId = node.id === 'ROOT' ? null : node.id
    tableState.searchData.categoryId = tableState.filterSearch.categoryId
    onSearch()
  }
  const treeRef = ref(null)
  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }
  async function getClassifyTreeList() {
    const res = await api.documentManage.apiCategoryTree()
    classState.data = [
      {
        id: 'ROOT',
        name: '全部',
        pid: null,
        children: res.data,
        expanded: true,
        selected: true,
      },
    ]
    nextTick(() => {
      tableState.expandedKeys = [classState.data?.[0]?.id || 1]
      tableState.selectedKey = classState.data?.[0]?.id || 1
    })
  }
  // 编辑新增
  function dataTreeClick(type, node) {
    classState.editData.name = ''
    classState.dataTreeShow = true
    classState.editData.type = type
    if (type === 'edit') {
      classState.editData.id = node.id
      classState.editData.pid = node.pid || 'ROOT'
      classState.editData.name = node.name
    } else {
      classState.editData.pid = node.id || 'ROOT'
    }
  }
  // 当前名称不能作为父级
  function setDisabled(data) {
    data.forEach((item) => {
      if (item.name === classState.editData.name) {
        item.disabled = true
        if (item.children?.length) setDisabled(item.children)
      }
    })
  }
  // 删除
  async function delDataTree(node) {
    const res = await api.documentManage.apiCategoryDelete({ kind: 'check', id: node.id })
    if (res.success) {
      proxy.$MessageBoxService.open({
        title: '删除API分类',
        content: `确定删除${node.name}？`,
        save: () => {
          api.documentManage
            .apiCategoryDelete({
              kind: 'execute',
              id: node.id,
            })
            .then((res) => {
              if (res.success) {
                tableState.selectedKey=node.pid
                proxy.$message.success(`删除${node.name}成功!`)
                getClassifyTreeList()
              }
            })
        },
      })
    }
  }

  // 保存数据分类树
  const classFormRef = ref(null)
  function onClassSave() {
    classFormRef.value.validate((isValid) => {
      if (isValid) {
        const url = classState.editData.type === 'add' ? 'apiCategorySave' : 'apiCategoryEdit'
        const params = {
          id: classState.editData.id,
          name: classState.editData.name,
          pid: classState.editData.pid === 'ROOT' ? null : classState.editData.pid,
        }
        api.documentManage[url](params).then((res) => {
          if (res.success) {
            const hint = classState.editData.type === 'add' ? '新建' : '编辑'
            getClassifyTreeList()
            classState.dataTreeShow = false
            classState.editData.name = ''
            proxy.$message.success(hint + '成功!')
          }
        })
      }
    })
  }
  function startSearch() {
    Object.keys(tableState.filterSearch).forEach((key) => {
      tableState.searchData[key] = tableState.filterSearch[key]
    })
    onSearch()
  }
  // 查询
  function onSearch(init = true) {
    if (init) tableState.pagination.currentPage = 1
    tableState.isLoad = true
    api.documentManage
      .apiListPageSearch({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          ...tableState.searchData,
        },
      })
      .then((res) => {
        tableState.tableList = res.data.list
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  // 重置
  function resetFn() {
    tableState.pagination = {
      currentPage: 1,
      pageSize: 10,
      total: 0,
    }
    tableState.filterSearch = {
      name: '',
      status: '',
      categoryId: null,
    }
    tableState.searchData = {
      name: '',
      status: '',
      categoryId: null,
    }
    onSearch(true)
  }
  // 复制
  function onCopyUrl(url) {
    let input = document.createElement('input')
    input.value = url
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    proxy.$message.success('复制成功')
  }
  // 测试
  function onTest(row) {
    router.push({
      name: 'apiTest',
      query: { id: row.id },
    })
  }
  // 新增api
  function onAdd() {
    router.push({
      name: 'apiAdd',
      query: { categoryId: tableState.filterSearch.categoryId },
    })
  }
  // 编辑api
  function onEdit(row) {
    router.push({ name: 'apiEdit', query: { id: row.id } })
  }
  // 发布api
  function onPush(row) {
    api.documentManage.apiPublish({ apiId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        proxy.$message.success('操作成功!')
      }
    })
  }
  // 下架api
  function onUnder(row) {
    api.documentManage.apiOffline({ apiId: row.id }).then((res) => {
      if (res.success) {
        onSearch()
        proxy.$message.success('操作成功!')
      }
    })
  }
  // 白名单
  async function onWhiteList(row) {
    const res = await api.documentManage.apiAuthorizeGet({ apiId: row.id })
    const { ips } = res.data
    tableState.whiteIps = ips
    tableState.whiteId = row.id
    tableState.whiteVisiable = true
  }
  // 预览api
  function onView(row) {
    router.push({
      name: 'apiDetail',
      query: { id: row.id },
    })
  }
  // 删除api
  function onDelete(row) {
    proxy.$MessageBoxService.open({
      title: '是否确认该条api',
      content: '删除后将不可恢复',
      save: () => {
        api.documentManage.apiDelete({ apiId: row.id }).then((res) => {
          if (res.success) {
            onSearch()
            proxy.$message.success('操作成功!')
          }
        })
      },
    })
  }
  watch(
    () => route.query?.refresh,
    (newQuery, oldQuery) => {
      if (newQuery === 'true') {
        getClassifyTreeList()
        onSearch()
        // 移除 refresh 参数
        const { refresh, ...newQuery } = route.query
        router.push({ name: route.name, query: newQuery })
      }
    },
    { immediate: true }, // { immediate: true } 表示立即执行一次
  )
  onMounted(() => {
    getClassifyTreeList()
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: auto;
    .left {
      min-width: 280px;
      background: #fff;
      border-radius: $cf-border-radius;
    }
    .right {
      flex: 1;
      width: calc(100% - var(--aside-width));
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .class-list {
    padding: 12px;
    .class-tree {
      height: calc(100vh - 280px);
      overflow: auto;
      .tree-tool {
        width: 60px;
        margin-left: auto;
      }
      .tree-icon-tool {
        display: none;
        margin-left: 4px;
        font-size: 16px;
      }
      :deep(.tree-box) {
        height: calc(100% - 8px);
        .btn-box {
          opacity: 1;
          background-color: #fff;
        }
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
      &:hover .tree-icon {
        display: inline-block;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content--value-wrapper) {
      display: flex;
      width: 100%;
      > svg {
        flex-shrink: 0;
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:hover .tree-icon-tool) {
      display: inline-block;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .popover-item {
    width: 100px;
  }
  .template-con-flex {
    height: calc(100% - 62px);
  }
  .table {
    height: calc(100% - 48px);
  }
  .publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .w-publish::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .audit::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .urlBox {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .urlContent {
      width: calc(100% - 30px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: keep-all;
    }
    .icon-svg {
      width: 18px;
      height: 18px;
      cursor: pointer;
      &:hover {
        color: $themeBlue;
      }
    }
  }
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
