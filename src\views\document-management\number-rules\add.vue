<template>
  <n-drawer v-model="visiable" size="850" title="编号规则配置" @close="onClose(false)">
    <div class="form nc-p-16">
      <n-form
        ref="ruleForm"
        :data="formData"
        label-width="80px"
        label-align="start"
        label-suffix="："
        :rules="rules"
        message-type="text"
      >
        <n-form-item field="ruleName" label="规则名称">
          <n-input v-model="formData.ruleName" placeholder="" maxLength="200" />
        </n-form-item>

        <n-form-item field="codeList" label="规则内容">
          <n-button color="primary" @click="addCodeAttr">
            <SvgIcon icon="icon-rule-add" class="nc-m-r-4" />添加编号段
          </n-button>
        </n-form-item>
        <n-form-item class="nc-m-l-100">
          <n-public-table
            class="rule-table"
            ref="publicTable"
            :showPagination="false"
            :isDisplayAction="true"
            :actionWidth="96"
            rowKey="listId"
            :tableHeight="tableHeight"
            :tableData="{ list: formData.codeList }"
            :needOtherActionBar="needOtherActionBar"
            :table-head-titles="tableHeadTitles"
          >
            <template #index="{ editor }">
              <div>{{ editor.rowIndex + 1 }}</div>
            </template>
            <template #type="{ editor }">
              <n-form-item>
                <n-select
                  v-model="editor.row.type"
                  placeholder=""
                  style="width: 100%"
                  @value-change="onTypeChange(editor.row)"
                >
                  <n-option
                    v-for="item in codeTypeList"
                    :key="item.value"
                    :name="item.name"
                    :value="item.value"
                  />
                </n-select>
              </n-form-item>
            </template>
            <template #ruleContent="{ editor }">
              <n-form-item
                :field="['codeList', editor.rowIndex, 'str']"
                v-if="editor.row.type === 2"
                :rules="[{ required: true, message: '请输入固定字符', trigger: 'blur' }]"
              >
                <n-input v-model="editor.row.str" />
              </n-form-item>
              <template v-else-if="editor.row.type === 3">
                <div class="variable-grid">
                  <n-form-item
                    :field="['codeList', editor.rowIndex, 'strLength']"
                    :rules="[
                      {
                        required: true,
                        type: 'number',
                        message: '请输入变量位数（数字）',
                        trigger: 'blur',
                      },
                    ]"
                  >
                    <n-input
                      v-model.number="editor.row.strLength"
                      clearable
                      placeholder="请输入变量位数"
                    />
                  </n-form-item>
                  <n-form-item
                    :field="['codeList', editor.rowIndex, 'startValue']"
                    :rules="[
                      {
                        required: true,
                        type: 'number',
                        message: '请输入起始值（数字）',
                        trigger: 'blur',
                      },
                    ]"
                  >
                    <n-input
                      v-model.number="editor.row.startValue"
                      clearable
                      placeholder="请输入起始值"
                    />
                  </n-form-item>
                  <n-form-item
                    :field="['codeList', editor.rowIndex, 'stepValue']"
                    :rules="[
                      {
                        required: true,
                        type: 'number',
                        message: '请输入递增值（数字）',
                        trigger: 'blur',
                      },
                    ]"
                  >
                    <n-input
                      v-model.number="editor.row.stepValue"
                      clearable
                      placeholder="请输入递增值"
                    />
                  </n-form-item>
                </div>
              </template>
              <n-form-item
                v-else-if="editor.row.type === 4"
                :field="['codeList', editor.rowIndex, 'time']"
                :rules="[
                  {
                    required: true,
                    message: '请选择时间变量',
                    trigger: 'change',
                  },
                ]"
              >
                <n-select v-model="editor.row.time" allow-clear placeholder="">
                  <n-option v-for="item in timeTypeList" :key="item" :name="item" :value="item" />
                </n-select>
              </n-form-item>
            </template>
            <template #editor="{ editor }">
              <SvgIcon
                :icon="
                  editor.rowIndex && formData.codeList.length > 1 ? 'moveUp' : 'moveUp-disable'
                "
                :class="
                  editor.rowIndex && formData.codeList.length > 1 ? 'icon-svg' : 'icon-svg disabled'
                "
                @click="handleItem(editor.rowIndex, 1, editor.row)"
              />
              <SvgIcon
                :icon="
                  formData.codeList.length - 1 > editor.rowIndex ? 'moveDown' : 'moveDown-disable'
                "
                :class="
                  formData.codeList.length - 1 > editor.rowIndex ? 'icon-svg' : 'icon-svg disabled'
                "
                @click="handleItem(editor.rowIndex, 2, editor.row)"
              />
              <SvgIcon
                v-if="editor.rowIndex"
                icon="icon-delete-sign"
                class="icon-svg"
                @click="handleItem(editor.rowIndex, 3)"
              />
            </template>
          </n-public-table>
        </n-form-item>
      </n-form>
    </div>
    <div class="container-footer">
      <div class="my-appliction-right">
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
  </n-drawer>
</template>
<script setup>
  import api from '@/api/index'
  import { reactive, ref, getCurrentInstance } from 'vue'
  const { proxy } = getCurrentInstance()

  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
  })
  const emit = defineEmits(['update:modelValue', 'reload'])
  const tableHeight = computed(() => window.innerHeight - 220)
  const tableHeadTitles = [
    {
      name: '序号',
      prop: 'index',
      width: '70',
      slot: 'index',
    },
    {
      name: '码段类型',
      prop: 'type',
      width: '120',
      slot: 'type',
    },
    {
      name: '码段规则',
      prop: 'ruleContent',
      slot: 'ruleContent',
      width: '310',
    },
  ]
  const timeTypeList = ref([])
  const needOtherActionBar = {
    label: '设为默认',
    show: false,
  }
  const codeTypeList = ref([
    {
      name: '固定字符',
      value: 2,
    },
    {
      name: '时间变量',
      value: 4,
    },
    {
      name: '流水码',
      value: 3,
    },
  ])
  const ruleForm = ref(null)
  const formData = reactive({
    ruleName: '',
    id: '',
    codeList: [{ type: 2, listId: `${+new Date()}${Math.random()} ` }],
  })
  const rules = reactive({
    ruleName: [{ required: true, message: '请输入编号规则名称', trigger: 'blur' }],
    codeList: [{ required: true, trigger: 'change', message: '请输入规则内容', type: 'array' }],
  })
  function onTypeChange(row) {
    if (row.type === 4) {
      row.time = timeTypeList.value[0]
    }
  }
  function getTime() {
    api.documentManage.getCodeTimeList().then((res) => {
      if (res.success) {
        timeTypeList.value = res.data
      }
    })
  }
  // 处理编号属性上移，下移及删除
  function handleItem(index, type, data) {
    if ((type === 1 && index === 0) || (type === 2 && index === formData.codeList.length - 1)) {
      return
    }
    formData.codeList.splice(index, 1)
    switch (type) {
      //上移
      case 1: {
        if (index > 0) {
          formData.codeList.splice(index - 1, 0, data)
        }
        break
      }
      // 下移
      case 2: {
        if (index < formData.codeList.length) {
          formData.codeList.splice(index + 1, 0, data)
        }
        break
      }
      case 3:
        break
    }
  }
  // 新增编号
  function addCodeAttr() {
    formData.codeList.push({
      type: 2,
      listId: `${+new Date()}${Math.random()} `,
    })
  }
  //保存
  function onConfirm() {
    ruleForm.value.validate((valid, fields) => {
      if (valid) {
        let apiType = 'ruleSave'
        if (formData.id) {
          apiType = 'ruleEdit'
        }
        const ruleContent = [
          {
            where: '',
            list: formData.codeList,
            itemId: '',
            modelId: 'undefined',
          },
        ]
        let params = {
          id: formData.id,
          ruleName: formData.ruleName,
          codeType: '1',
          ruleContent: JSON.stringify(ruleContent),
        }
        api.documentManage[apiType](params).then((res) => {
          if (res.success) {
            proxy.$message.success('保存成功！')
            onClose(true, apiType)
          }
        })
      } else {
        proxy.$message.warning('有未填表单项！')
      }
    })
  }
  function onClose(reload = false, apiType = 'ruleSave') {
    formData.ruleName = ''
    formData.codeList = [{ type: 2, listId: `${+new Date()}${Math.random()} ` }]
    formData.id = ''
    emit('update:modelValue', false)
    if (reload) {
      emit('reload', apiType === 'ruleSave' ? true : false)
    }
  }
  // 获取编号详情
  function getCodeData() {
    api.documentManage
      .ruleDetail({
        id: formData.id,
      })
      .then((res) => {
        let arr = Object.keys(res.data)
        arr.forEach((item) => {
          if (item !== 'ruleContent') {
            formData[item] = res.data[item]
          }
        })
        formData.codeList = JSON.parse(res.data.ruleContent)[0].list
        console.log(JSON.parse(res.data.ruleContent))
      })
  }
  const visiable = ref(false)
  watch(
    () => props.modelValue,
    (val) => {
      visiable.value = val

      if (val) {
        getTime()
        if (props.id) {
          formData.id = props.id
          getCodeData()
        }
      }
    },
  )
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .variable-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 10px;
  }

  .icon-svg {
    width: 16px;
    height: 16px;
    margin-top: 5px;
    margin-right: 12px;
    cursor: pointer;

    &:hover {
      color: $themeBlue;
    }

    &.disabled {
      color: #c8c9cc;
      cursor: not-allowed;
    }
  }
  .rule-table {
    :deep(.nancalui-form__item--horizontal) {
      margin-bottom: 0 !important;
    }
  }
  :deep(.nancalui-textarea__div) {
    width: max-content !important;
  }
  .code-prev {
    display: flex;
    justify-content: space-between;
    width: 600px;
  }
  .dialog-content {
    flex: 0.9;

    :deep(.nancalui-textarea__div) {
      width: 100% !important;
    }
  }
  .dialog-text {
    height: 20px;
    color: #666666;
    font-weight: 400;
    font-size: $themeFont;
    line-height: 20px;
  }
  .dialog {
    &-title {
      display: flex;
      align-items: center;
      height: 22px;
      padding-top: 7px;
      color: #000000;
      font-weight: 500;
      font-size: 15px;
      line-height: 22px;

      > span {
        display: inline-block;
        width: 4px;
        height: 14px;
        margin-right: 4px;
        background: #18a0fb;
      }
    }

    &-code {
      margin-top: 10px;
      color: #333333;
      font-weight: 500;
      font-size: $themeFont;
      line-height: 20px;
    }
  }
</style>
