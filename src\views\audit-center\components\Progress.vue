<template>
  <n-drawer v-model="state.visible" :size="580" :before-close="() => emit('close')">
    <div class="header">跟踪 
      <svg @click="emit('close')" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path d="M4.5 19.5L19.4995 4.50047" stroke="#8091B7" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M4.5 4.5L19.4995 19.4995" stroke="#8091B7" stroke-linecap="round" stroke-linejoin="round"/>
</svg></div>
    <div class="message" v-if="state.message !== ''">{{ state.message }}</div>

    <el-timeline class="progress">
      <el-timeline-item
        :timestamp="item.exeTime"
        placement="top"
        :color="item.exeResult === 'APPROVE'||item.nodeType==='START'||isEnd ? '#0bbd87' : '#ddd'"
        v-for="item in state.nodes"
        :key="item.nodeCode"
      >
        <el-card>
          <template v-if="item.nodeType!=='TASK'">
            <h4>{{item.name}}</h4>
          </template>
          <template v-else>
            <h4>处理人：{{ item.assigneeName }}</h4>
            <p>审批结果：{{ approvalStatus(item.exeResult) }}</p>
          </template>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </n-drawer>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import api from '@/api/index'

  const emit = defineEmits(['close'])
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  })
 
  const state = reactive({
    visible: true,
    message: '数据加载中',
    nodes: [],
  })

  const isEnd=computed(() => {
    // 过滤出所有 TASK 类型的节点
  const taskNodes = state.nodes.filter(node => node.nodeType === 'TASK');
  
  // 检查是否所有任务节点的执行结果都是 APPROVE
  // 如果没有任务节点（taskNodes.length 为 0），也视为满足条件
  return taskNodes.every(node => node.exeResult === 'APPROVE');
  })


  // 审批状态转义
  const approvalStatus = (status) => {
    let name = '待审批'
    switch (status) {
      case 'APPROVE':
        name = '通过'
        break
      case 'REJECT':
        name = '驳回'
        break
      case 'CANCEL':
        name = '取消'
        break
      default:
        break
    }
    return name
  }

  const processManageGetProgressFn = async () => {
    const res = await api.system.processManageGetProgress({ id: props.id })
    if (res.success) {
      state.message = res.message === '该审批信息未配置审批流程信息' ? res.message : ''

      if(state.message===''){
        state.nodes = sortProcessNodes(res.data.nodes, res.data.relations)
      }
    } else {
      state.message = '数据获取失败'
    }
  }

  const sortProcessNodes = (nodes, relations) => {
    // 1. 创建节点编码到节点对象的映射
    const nodeMap = new Map()
    nodes.forEach((node) => nodeMap.set(node.nodeCode, node))

    // 2. 构建邻接表和入度表
    const graph = new Map() // 邻接表：存储每个节点的后继节点
    const inDegree = new Map() // 入度表：存储每个节点的入度数

    // 初始化数据结构
    nodes.forEach((node) => {
      graph.set(node.nodeCode, [])
      inDegree.set(node.nodeCode, 0)
    })

    // 填充邻接表和入度表
    relations.forEach((relation) => {
      const source = relation.sourceNodeCode
      const target = relation.targetNodeCode
      graph.get(source).push(target)
      inDegree.set(target, (inDegree.get(target) || 0) + 1)
    })

    // 3. 拓扑排序（Kahn算法）
    const queue = []
    const sortedNodeCodes = []

    // 将入度为0的节点加入队列
    inDegree.forEach((degree, nodeCode) => {
      if (degree === 0) queue.push(nodeCode)
    })

    // 处理队列
    while (queue.length > 0) {
      const currentCode = queue.shift()
      sortedNodeCodes.push(currentCode)

      // 更新后继节点的入度
      graph.get(currentCode).forEach((successor) => {
        const newDegree = inDegree.get(successor) - 1
        inDegree.set(successor, newDegree)
        if (newDegree === 0) queue.push(successor)
      })
    }

    // 4. 处理环状依赖（流程异常）
    if (sortedNodeCodes.length !== nodes.length) {
      console.error('存在环状依赖，无法完成拓扑排序')
      return nodes // 返回原始顺序
    }

    // 5. 转换为排序后的节点对象数组
    return sortedNodeCodes.map((code) => nodeMap.get(code))
  }

  onMounted(() => {
    processManageGetProgressFn()
  })
  defineExpose({
    ...toRefs(state),
  })
</script>
<style scoped lang="scss">

  .header{
    position:relative;
    display:flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height:52px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #C5D0EA;

    &::before{
      position:absolute;
      left:0;
      width:4px;
      height: 18px;
      background: var(---, #1e89ff);
      content: '';
    }
  }
  .message {
    padding: 16px;
    font-size: 18px;
    color: #333;
  }

  .progress {
    width: 570px;
    padding: 16px;
    height: 100%;
    overflow: auto;

    h4{
      margin:0;
    }
  }
</style>
