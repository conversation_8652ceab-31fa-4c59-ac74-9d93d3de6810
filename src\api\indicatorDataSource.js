import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 指标数据源管理-查询该用户的数据源列表--带分页
function getDatasourceList(data) {
  return request({
    url: `${URL.MODEL}/datasource/search`,
    method: 'post',
    data: data,
  })
}

// 指标数据源管理-不分页查询数据源列表
function getDatasourceSelect(data) {
  return request({
    url: `${URL.MODEL}/datasource/list`,
    method: 'get',
    params: data,
  })
}

// 指标数据源管理-数据源类型下拉列表
function getDataSourceType(data) {
  return request({
    url: `${URL.MODEL}/datasource/type/all`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-数据结构下拉列表
function getDataStructureType(data) {
  return request({
    url: `${URL.MODEL}/datasource/getDataStructureType`,
    method: 'get',
    params: data,
  })
}
// 判断数据源名称是否重名
function validDatasource(data) {
  return request({
    url: `${URL.MODEL}/datasource/exist`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-新增数据源
function datasourceAdd(data) {
  return request({
    url: `${URL.MODEL}/datasource/add`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}

// 指标数据源管理-编辑数据源
function datasourceUpdate(data) {
  return request({
    url: `${URL.MODEL}/datasource/update`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}
// 指标数据源管理-复制数据源
function datasourceCopy(data) {
  return request({
    url: `${URL.MODEL}/datasource/copy/${data.dataSourceId}`,
    method: 'post',
    data,
  })
}

// 指标数据源管理-查看等根据id查询信息-会脱敏
function getDatasourceDetail(data) {
  return request({
    url: `${URL.MODEL}/datasource/${data.id}`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-编辑根据id查询信息-不会脱敏
function getDatasourceDetailByAes(data) {
  return request({
    url: `${URL.MODEL}/datasource/aes/${data.id}`,
    method: 'get',
    params: data,
  })
}

// 指标数据源管理-测试数据源
function testDatasource(data) {
  return request({
    url: `${URL.MODEL}/datasource/connected`,
    method: 'post',
    timeout: 2 * 60 * 1000,
    data: data,
  })
}
//列表测试连通
function testDatasourceWithId(data) {
  return request({
    url: `${URL.MODEL}/datasource/list/connected`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-判断能否删除数据源
function deleteDatasourceValid(data) {
  return request({
    url: `${URL.BASE}/datasource/delete/valid`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-删除数据源
function deleteDatasource(data) {
  return request({
    url: `${URL.MODEL}/datasource/delete`,
    method: 'post',
    data: data,
  })
}
// 指标数据源管理-数据源状态下拉列表
function getDatasourceStatus(data) {
  return request({
    url: `${URL.MODEL}/datasource/status`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-数据源类型-全列表
function getAllDatasourceTree(data) {
  return request({
    url: `${URL.MODEL}/datasource/type/tree`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-查询指定数据源数据库的所有表
function getDatasourceTables(data) {
  return request({
    url: `${URL.MODEL}/datasource/v2/tables/${data.dataSourceId}`,
    method: 'get',
    params: data,
  })
}
// 指标数据源管理-查询指定数据源数据库的表结构
function getDatasourceTableNameWithId(data) {
  return request({
    url: `${URL.MODEL}/datasource/table/structure/${data.dataSourceId}/${data.tableName}`,
    method: 'get',
    params: data,
  })
}

export default {
  getDatasourceList,
  getDatasourceSelect,
  getDataSourceType,
  getDataStructureType,
  validDatasource,
  datasourceAdd,
  getDatasourceDetail,
  getDatasourceDetailByAes,
  testDatasource,
  datasourceUpdate,
  deleteDatasource,
  getDatasourceStatus,
  getAllDatasourceTree,
  testDatasourceWithId,
  getDatasourceTables,
  getDatasourceTableNameWithId,
  datasourceCopy,
  deleteDatasourceValid,
}
