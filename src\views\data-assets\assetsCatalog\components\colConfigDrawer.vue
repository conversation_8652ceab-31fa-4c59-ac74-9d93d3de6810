<template>
  <div v-if="drawer" class="drawer">
    <div class="drawer-title">
      列设置
      <div
        class="drawer-close"
        @click="
          () => {
            cancel()
          }
        "
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.30078 13.6992L13.8008 2.19928"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2.30078 2.19922L13.8008 13.6992"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="drawer-content">
      <VueDraggableNext
        class="drag-list"
        v-model="state.fieldList"
        animation="300"
        :group="{ name: 'people', pull: 'clone', put: false }"
        @change="change"
        :clone="
          (original) => {
            return { colName: original.name, operator: '', value: '' }
          }
        "
      >
        <template v-for="element in state.fieldList" :key="element.id">
          <div class="drag-item">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <circle
                cx="10"
                cy="3.5"
                r="0.5"
                transform="rotate(90 10 3.5)"
                fill="#606266"
                stroke="#606266"
              />
              <circle
                cx="6"
                cy="3.5"
                r="0.5"
                transform="rotate(90 6 3.5)"
                fill="#606266"
                stroke="#606266"
              />
              <circle
                cx="10"
                cy="8"
                r="0.5"
                transform="rotate(90 10 8)"
                fill="#606266"
                stroke="#606266"
              />
              <circle
                cx="6"
                cy="8"
                r="0.5"
                transform="rotate(90 6 8)"
                fill="#606266"
                stroke="#606266"
              />
              <circle
                cx="10"
                cy="12.5"
                r="0.5"
                transform="rotate(90 10 12.5)"
                fill="#606266"
                stroke="#606266"
              />
              <circle
                cx="6"
                cy="12.5"
                r="0.5"
                transform="rotate(90 6 12.5)"
                fill="#606266"
                stroke="#606266"
              />
            </svg>

            <svg
              v-if="!element.isUnChecked"
              @click="checkedHandle(element)"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="1" y="1" width="14" height="14" rx="2" fill="#1E89FF" />
              <path
                d="M11.3346 5.5L6.7513 10.0833L4.66797 8"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>

            <svg
              @click="checkedHandle(element)"
              v-else
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="1.5" y="1.5" width="13" height="13" rx="1.5" fill="white" stroke="#DCDFE6" />
            </svg>

            <span class="field-name" :title="element.cnName">{{
              element.cnName || element.name
            }}</span>

            <el-input-number
              style="width: 80px"
              v-model="element.w"
              class="mx-4"
              :min="100"
              :max="400"
              @change="changeWidth(element)"
              controls-position="right"
            />
          </div>
        </template>
      </VueDraggableNext>
    </div>
    <div class="drawer-footer">
      <n-checkbox v-model="checked" label="全选" @change="allCheckedChange" />
      <span>
        <n-button @click.prevent="resetForm">重置</n-button>
        <!-- <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button> -->
      </span>
    </div>
  </div>
</template>

<script setup>
  import { VueDraggableNext } from 'vue-draggable-next'
  const drawer = ref(false)
  const emit = defineEmits(['update:data'])
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
  })
  const state = reactive({ fieldList: [] })
  const checked = ref(false)
  const key = ref(Math.random())
  const cancel = () => {
    drawer.value = false
    key.value = Math.random()
  }
  const change = () => {
    emit('update:data', state.fieldList)
    key.value = Math.random()
  }
  const checkedHandle = (element) => {
    element.isUnChecked = !element.isUnChecked
    emit('update:data', state.fieldList)
    key.value = Math.random()
  }
  const changeWidth = () => {
    state.fieldList.forEach((item) => {
      item.width = item.w
    })
    emit('update:data', state.fieldList)
    key.value = Math.random()
  }
  const allCheckedChange = (val) => {
    state.fieldList.forEach((item) => {
      item.isUnChecked = !val
    })
    emit('update:data', state.fieldList)
    key.value = Math.random()
  }

  const isAllChecked = computed(() => {
    return state.fieldList.every((item) => !item.isUnChecked)
  })
  watch(
    isAllChecked,
    (val) => {
      checked.value = val
    },
    {
      immediate: true,
    },
  )

  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(props.data))
    return function () {
      state.fieldList = JSON.parse(JSON.stringify(resetData))
      emit('update:data', state.fieldList)
      key.value = Math.random()
    }
  })()
  defineExpose({
    open() {
      key.value = Math.random()
      drawer.value = true
      state.fieldList = props.data
    },
    key,
  })
</script>
<style></style>
<style lang="scss" scoped>
  .drawer {
    display: flex;
    width: 400px;
    height: 100%;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    position: relative;
    z-index: 1000;
    background: #fff;
    box-shadow: -8px 0px 24px -2px rgba(30, 47, 85, 0.1);
    &-title {
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 46px;
      padding: 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;
      background-color: #fff;
      border-radius: 2px;
      border-bottom: 1px solid #eceff5;
      &[sub-label]::after {
        margin-left: 12px;
        content: attr(sub-label);
        display: inline-block;
        padding: 0px 4px;
        border-radius: 2px;
        line-height: 22px;
        border: 1px solid rgba(26, 164, 238, 0.4);
        background: rgba(26, 164, 238, 0.08);

        color: #1aa4ee;

        font-family: 'Source Han Sans CN';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
      &:before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }

      .drawer-close {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        margin: auto;
        border-radius: 2px;
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #479dff;
        }
      }
    }
    &-content {
      display: flex;
      padding: 16px;
      align-items: flex-start;
      flex: 1 0 0;
      align-self: stretch;
      .drag-list {
        width: 100%;
        .drag-item {
          display: flex;
          align-items: center;
          gap: 10px;
          align-self: stretch;
          > * {
            cursor: pointer;
          }
          .field-name {
            flex: 1;
          }
          & + .drag-item {
            margin-top: 4px;
          }
        }
      }
    }
    &-footer {
      display: flex;
      padding: 16px 20px;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      :deep(.nancalui-button + .nancalui-button) {
        margin-left: 8px;
      }
    }
  }
</style>
