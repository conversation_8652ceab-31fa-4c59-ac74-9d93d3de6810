<template>
  <div class="txt-content">
    <!-- 显示pdf的canvas -->
    <pre class="txt-Preview">{{ state.content }}</pre>
  </div>
</template>

<script setup>
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
  const state = reactive({
    content: '',
  })
  // 请求文件地址，获取文本
  const getFileContent = async (filePath, fileEncoding) => {
    const docoder = new TextDecoder(fileEncoding || 'utf-8')
    const file = await fetch(filePath)
    const bytes = await file.arrayBuffer()
    const string = docoder.decode(bytes)
    state.content = string
  }
  getFileContent(option.value.value, option.value.fileEncoding)
</script>
<style lang="scss" scoped>
  .txt-content {
    position: relative;
    display: flex;
    align-self: stretch;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 8px;
    overflow-y: auto;
    background-color: #f6f7fb;
    border-radius: 6px;
    user-select: text;

    .txt-Preview {
      width: 100%;
    }
  }
</style>
