<template>
  <!-- 统一表格 -->
  <div class="common-table" ref="divDom">
    <div class="page-top">
      <slot name="pageTop"></slot>
    </div>
    <div class="page-mid" ref="pageMid" v-if="$attrs.tableConfig">
      <el-table
        style="width: 100%"
        ref="publicTable"
        :header-cell-style="{
          'background-color': '#EBF4FF',
          padding: '0',
          color: ' #1D2129',
          'font-size': '14px',
          'font-style': 'normal',
          'font-weight': '400',
        }"
        :cell-style="{
          border: 'none',
          height: '36px',
          padding: ' 0',
        }"
        :row-style="{
          height: '36px',
        }"
        border
        scrollbar-always-on="true"
        resizable
        v-bind="$attrs.tableConfig"
        :height="state.tableHeight"
        @selection-change="selectionChange"
        @select="cellSelect"
        @sort-change="sortChange"
        @header-dragend="headerDragend"
      >
        <!-- 是否需要栏 -->
        <el-table-column
          v-if="isNeedSelection"
          type="selection"
          :selectable="$attrs?.selectable"
          :reserveSelection="reserveSelection"
          width="45"
        />
        <!-- 序号 -->
        <el-table-column
          v-if="isNeedIndex && tableHeadTitles.length > 0"
          type="index"
          label="序号"
          :width="indexWidth || 80"
          :index="indexMethod"
          :sortable="$attrs.indexSortable"
        />

        <!-- 表头 -->
        <template v-for="(item, indx) in tableHeadTitles">
          <!-- 操作列/自定义列 -->
          <el-table-column
            v-if="item.slot"
            :key="item.prop"
            :label="item.name"
            :prop="item.prop"
            align="left"
            :fixed="item.fixed || false"
            :min-width="item.minWidth"
            :width="
              item.minWidth
                ? undefined
                : getLocalStorage()[item.prop]?.width ||
                  (indx === tableHeadTitles.length - 1 && indx < 8
                    ? item.width || 'auto'
                    : item.width || 180)
            "
          >
            <template #header>
              <template v-if="item.headerSlot">
                <slot :name="item.headerSlot" :column="item">
                  <span style="color:red">slot未传递</span>
                </slot>
              </template>
              <template v-else>
                <div :title="item.name"
                  >{{ item.name }}
                  <SvgIcon
                    class="illustrate"
                    v-if="item.headerIcon"
                    :icon="item.headerIcon"
                    @click.prevent="headerIconClickFn(item)"
                  />
                </div>
              </template>
            </template>
            <template #default="{ row, $index }">
              <slot :name="item.slot" :row="row" :index="$index"></slot>
            </template>
          </el-table-column>

          <el-table-column
            v-else
            :key="item.name"
            :label="item.name"
            :prop="item.prop"
            :sortable="item.sortable"
            align="left"
            :fixed="item.fixed || false"
            :min-width="item.minWidth"
            :width="
              item.minWidth
                ? undefined
                : getLocalStorage()[item.prop]?.width ||
                  (indx === tableHeadTitles.length - 1 && indx < 8
                    ? item.width || 'auto'
                    : item.width || 180)
            "
          >
            <!-- eslint-disable-next-line vue/no-deprecated-slot-attribute-->
            <template #header>
              <span :title="item.name">{{ item.name }}</span>
            </template>
            <template #default="scope">
              <span :title="scope.row[item.prop]" :class="{ needNewline: needNewline }">{{
                scope.row.hasOwnProperty(item.prop) &&
                scope.row[item.prop] !== undefined &&
                scope.row[item.prop] !== null
                  ? scope.row[item.prop]
                  : showEmpty
                  ? ''
                  : '--'
              }}</span>
            </template>
          </el-table-column>
        </template>

        <!-- 其他扩展栏目 -->
        <el-table-column
          v-if="needOtherActionBar.show"
          fixed="right"
          :label="needOtherActionBar.label"
          align="left"
        >
          <template #default="{ row, $index }">
            <slot name="secondEditor" :editor="{ row, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 操作栏目 -->
        <el-table-column
          v-if="$slots?.editor"
          :label="actionName"
          :width="actionWidth"
          align="left"
          fixed="right"
        >
          <template #default="data">
            <slot name="editor" :data="data" :row="data.row" :index="data.$index"></slot>
          </template>
        </el-table-column>
        <!-- 无数据插槽 -->

        <template #empty>
          <slot name="empty">
            <div class="table-no-content cf-empty" :tip="emptyText"> </div>
          </slot>
        </template>
      </el-table>
    </div>

    <!-- $event => (($setup.state.filterSearch.pageSize) = $event) -->
    <div class="nancalui-table-page page-footer" v-if="$attrs.paginationConfig">
      <n-pagination
        :total="$attrs.paginationConfig.total"
        v-model:pageSize="$attrs.paginationConfig.pageSize"
        v-model:pageIndex="$attrs.paginationConfig.currentPage"
        :can-view-total="true"
        :can-change-page-size="true"
        :can-jump-page="true"
        @page-index-change="$attrs.paginationConfig.onCurrentChange"
        @page-size-change="$attrs.paginationConfig.onSizeChange"
        :page-size-options="[10, 20, 50, 100]"
      />
      <!-- <el-pagination
        v-bind="$attrs.paginationConfig"
        style="--el-pagination-border-radius: 0; --el-disabled-bg-color: #f4f5f6"
        background
        layout="prev,pager,next,jumper,consists of sizes,total"
        @size-change="handleSizeChange"
      /> -->
    </div>
    <!-- <div
      v-html="
        `
    <style >.el-pagination__jump::after {
    content: '显示${
      ($attrs.paginationConfig?.currentPage - 1) * $attrs.paginationConfig?.pageSize + 1
    }到${
          ($attrs.paginationConfig?.currentPage - 1) * $attrs.paginationConfig?.pageSize +
          ($attrs?.tableConfig?.data?.length ||
            ($attrs.paginationConfig?.currentPage * $attrs.paginationConfig?.pageSize >
            $attrs.paginationConfig?.total
              ? $attrs.paginationConfig?.total % $attrs.paginationConfig?.pageSize
              : $attrs.paginationConfig?.pageSize))
        }条，';
    }
    </style>`
      "
    ></div> -->
  </div>
</template>

<script>
  import { useAttrs, onUpdated } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  export default {
    title: '',
    components: {},
    props: {
      // 表格标识
      tableKey: {
        type: String,
        default: '',
      },
      // 是否保存表头宽度
      saveWidth: {
        type: Boolean,
        default: false,
      },
      needOtherActionBar: {
        // 是否需要第二操作栏
        type: Object,
        default: function () {
          return {
            label: '设为默认',
            show: false,
          }
        },
      },
      actionWidth: {
        // 操作栏宽度
        type: Number,
      },
      actionName: {
        // 操作栏名称
        type: String,
        default: '操作',
      },
      isNeedSelection: {
        // 是否需要多选
        type: Boolean,
        default: false,
      },
      reserveSelection: {
        // 数据刷新后是否保留选项
        type: Boolean,
        default: false,
      },
      isNeedIndex: {
        type: Boolean,
        default: false,
      },
      indexWidth: {
        type: Number,
      },
      tableHeadTitles: {
        // 表格表头
        type: Array,
        default: () => [],
      },
      emptyText: {
        type: String,
        default: '暂无数据',
      },
      // 数据为空时，显示空，不显示'--'
      showEmpty: {
        type: Boolean,
        default: false,
      },
      loading: {
        //loading状态
        type: Boolean,
        default: true,
      },
      calcHeight: {
        // 是否需要计算表格高度
        type: Boolean,
        default: true,
      },
      needNewline: {
        // 是否需要换行
        type: Boolean,
        default: false,
      },
    },
    emits: [
      'cell-click',
      'handle-selection-change',
      'tablePageChange',
      'headerIconClickFn',
      'sort-change',
      'cell-select',
    ],
    setup(props, { emit }) {
      const divDom = ref(null)
      const pageMid = ref(null)
      const attrs = useAttrs()
      const publicTable = ref()
      const paginationText = ref({ aaa: '显示1到15条，共15条数据' })
      const { loading } = toRefs(props)
      const state = reactive({
        loading: loading.value,
        tableHeight: 400,
      })
      const localKey = encodeURIComponent(JSON.stringify(props.tableHeadTitles) + props.tableKey)
      localStorage.getItem(localKey)

      const methods = {
        getLocalStorage() {
          if (!props.saveWidth) return {}
          const local = localStorage.getItem(localKey)
          if (local) {
            try {
              return JSON.parse(decodeURIComponent(local) || decodeURIComponent('{}'))
            } catch (e) {
              return {}
            }
          }
          return {}
        },
        headerDragend(newWidth, oldWidth, column, event) {
          if (!props.saveWidth || !newWidth) return
          const data = {
            ...methods.getLocalStorage(),
            [column.property]: {
              width: newWidth,
            },
          }
          localStorage.setItem(localKey, encodeURIComponent(JSON.stringify(data)))
        },
        setTableHeight() {
          state.tableHeight = pageMid.value?.offsetHeight || 400
        },
        // 切换条数
        handleSizeChange(val) {},
        sortChange(data) {
          emit('sort-change', data)
        },
        selectionChange(data) {
          emit('handle-selection-change', data)
        },
        cellSelect() {
          emit('select')
        },
        indexMethod(index) {
          const num =
            1 + attrs?.paginationConfig?.pageSize * (attrs?.paginationConfig?.currentPage - 1)
          return isNaN(num) ? index + 1 : index + num
        },
      }
      if (props.calcHeight) {
        useResizeObserver(divDom, (entries) => {
          methods.setTableHeight()
        })
        setTimeout(() => {
          methods.setTableHeight()
        }, 0)
      }
      return {
        divDom,
        pageMid,
        publicTable,
        state,
        paginationText,
        ...methods,
        clearSelection() {
          publicTable.value.clearSelection()
        },
        getSelectionRows() {
          return publicTable.value.getSelectionRows()
        },
        toggleRowSelection(row) {
          return publicTable.value.toggleRowSelection(row, true)
        },
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $paginationChildHeight: 32px;

  .common-table {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    :deep(.el-pagination) {
      justify-content: end;

      .el-pagination__total,
      .el-pagination__goto,
      .el-pagination__classifier {
        color: var(----, #606266);
        font-weight: 400;
        font-size: 14px;
        font-style: normal;
        line-height: normal;
      }

      button {
        min-width: $paginationChildHeight;
        height: $paginationChildHeight;
        color: var(----, #606266);
        font-weight: 400;
        font-size: 14px;
        font-family: 'PingFang SC';
        font-style: normal;
        line-height: normal;
      }

      ul li {
        min-width: $paginationChildHeight;
        height: $paginationChildHeight;
        color: var(----, #606266);
        font-weight: 400;
        font-size: 14px;
        font-family: 'PingFang SC';
        font-style: normal;
        line-height: normal;
      }

      .el-pagination__sizes,
      .el-pagination__jump {
        .el-pagination__editor {
          height: $paginationChildHeight;
        }

        .el-input__wrapper {
          height: $paginationChildHeight;

          .el-input__inner {
            height: 100%;
          }
        }
      }
    }

    .page-top {
    }

    .page-footer {
      display: flex;
      justify-content: end;
      width: 100%;
      padding: 10px 16px;
      border-top: 1px solid var(---, #dcdfe6);

      :deep(.el-pagination.is-background) {
        .btn-prev {
          margin: 0px;
          border-right: none;

          &:disabled {
            border: none;
          }
        }

        .btn-next {
          margin: 0px;
          border-left: none;

          &:disabled {
            border: none;
          }
        }
      }

      :deep(.el-pagination.is-background) {
        .el-pager li {
          margin: 0px;

          & + li {
            border-left: none;
          }

          &.is-active {
            color: var(---, #1e89ff);
            background-color: transparent;
            border: 1px solid #1e89ff;
          }
        }
      }

      :deep(.el-pagination__goto) {
        display: none;
      }

      :deep(el-pagination__classifier) {
        margin-right: 0;
        margin-left: 10px;
      }

      :deep(.el-pagination__total) {
        margin-left: 0;
      }

      :deep(.el-pagination__jump) {
        &::before {
          display: inline-block;
          margin-right: 10px;
          margin-left: 0;
          content: '跳转至';
        }

        &::after {
          display: inline-block;
          margin-left: 16px;
        }
      }
    }

    .page-mid {
      flex: 1;

      :deep(.el-table__fixed-right::before) {
        height: 0;
      }

      :deep(.el-table__empty-text) {
        line-height: normal;
      }

      :deep(.table-no-content) {
        img {
          width: 266px;
        }

        .text {
          margin-top: 20px;
          color: var(--el-text-color-secondary);
          font-size: 12px;
          line-height: normal;
        }
      }
    }

    .page-mid {
      flex: 1;

      :deep(.el-table__fixed-right::before) {
        height: 0;
      }

      :deep(.el-table__empty-text) {
        line-height: normal;
      }

      :deep(.table-no-content) {
        img {
          width: 266px;
        }

        .text {
          margin-top: 20px;
          color: var(--el-text-color-secondary);
          font-size: 12px;
          line-height: normal;
        }
      }

      :deep(.el-scrollbar__view) {
        height: 100%;
      }

      :deep(.el-table) {
        .el-table__inner-wrapper::before {
          // el-table 设置border边框 底部边线消失
          height: 0;
        }

        &:before {
          height: 0;
        }

        .el-checkbox__inner {
          width: 16px;
          height: 16px;

          &::after {
            top: 2px;
            left: 5px;
          }
        }

        thead {
          color: #333333;
          font-weight: 400;
          font-size: 14px;

          tr {
            height: 36px !important;
          }

          th {
            border-bottom: 1px solid #ebebeb !important;

            &.el-table__cell.is-leaf {
              border-right: none;
              border-bottom: none;
            }
          }

          th.el-table__cell {
            //表头边框
            // border: 1px solid #dcdcdc;
            // border-left: none;
            .illustrate {
              margin-left: 4px;
              font-size: 16px;
              cursor: pointer;
            }

            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                padding-left: 24px;
              }
            }

            &:last-child {
              .cell {
              }
            }

            & > .cell {
              display: -webkit-box;
              padding-left: 14px;
              overflow: hidden;
              color: #1d2129;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
          }
        }

        tbody {
          .el-table__expand-icon {
            transition: none;

            > i {
              display: none;
            }

            &::before {
              display: inline-block;
              width: 16px;
              height: 16px;
              background-size: 100%;
              content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
            }
          }

          .el-table__expand-icon--expanded {
            transform: none;

            &::before {
              content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTkgMUgyLjk5OTg4QzEuODk1MzEgMSAwLjk5OTg3OCAxLjg5NTQzIDAuOTk5ODc4IDNWMTNDMC45OTk4NzggMTQuMTA0NiAxLjg5NTMxIDE1IDIuOTk5ODggMTVIMTIuOTk5OUMxNC4xMDQ0IDE1IDE0Ljk5OTkgMTQuMTA0NiAxNC45OTk5IDEzVjNDMTQuOTk5OSAxLjg5NTQzIDE0LjEwNDQgMSAxMi45OTk5IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTg4IDdINS45OTk4OEM1LjQ0NzU5IDcgNC45OTk4OCA3LjQ0NzcyIDQuOTk5ODggOEM0Ljk5OTg4IDguNTUyMjggNS40NDc1OSA5IDUuOTk5ODggOUg5Ljk5OTg4QzEwLjU1MjIgOSAxMC45OTk5IDguNTUyMjggMTAuOTk5OSA4QzEwLjk5OTkgNy40NDc3MiAxMC41NTIyIDcgOS45OTk4OCA3WiIgZmlsbD0iIzU4NjQ3NSIvPgo8L3N2Zz4K');
            }
          }

          tr {
            height: 36px !important;
          }

          td {
            border-bottom: 1px solid #ebebeb !important;

            &.el-table__cell.is-leaf {
              width: 16px;
              height: 16px;
              margin-right: 4px;
              background-size: 100% 100%;
              border-right: none;
              border-bottom: none;
              content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
            }
          }

          td.el-table__cell {
            // border-bottom: 1px solid #dcdcdc;
            border-bottom: 1px solid #ebebeb !important;
          }

          tr {
          }

          tr td {
            //body边框
            &:nth-child(1) {
              // border-left: 1px solid #dcdcdc;

              .cell {
                display: flex;
                gap: 6px;
                align-items: center;
                padding-left: 24px;
              }
            }

            &:last-child {
              // border-right: 1px solid #dcdcdc;

              .cell {
              }
            }

            & > .cell {
              padding-left: 14px;
              color: #606266;

              .nancalui-button--disabled {
                color: #c0c4cc;
              }

              & > span {
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                &.needNewline {
                  display: block;
                }
              }

              .edit-box {
                .has-right-border {
                  height: 10px;
                  padding: 0 0 0 10px;
                  color: var(--themeBlue);
                  font-size: 12px;
                  line-height: 9px;
                  border: 0;
                  border-left: 1px solid #cfcfcf !important;
                  border-radius: initial;

                  &.is-disabled {
                    color: #c9c9c9;
                  }

                  &:disabled {
                    color: #c9c9c9;
                  }
                }

                .has-right-border:first-child {
                  padding-left: 0;
                }

                .nancalui-button {
                  &:first-child {
                    border-left: 0 !important;
                  }
                }

                .btn-more {
                  color: var(--themeBlue);
                  font-size: 12px;

                  &:hover {
                    background-color: #fff;
                  }
                }
              }
            }
          }
        }

        //  解决操作栏坍塌的问题 解决固定列距表格的高度（注意：这里我写死了表头为一行文字时的高度）
        .el-table__fixed-body-wrapper {
          top: 36px !important;
        }

        // 解决多出来的横线(固定列右边)问题
        .el-table__fixed-right {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        // 若左右都有固定列时
        .el-table__fixed {
          height: 100% !important; //设置高优先，以覆盖内联样式
        }

        /**改变table的滚动条样式*/
        // 滚动条的宽度
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 5px; // 横向滚动条
          height: 5px; // 纵向滚动条 必写
        }

        // 滚动条的滑块
        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          background-color: #e1e1e1;
          border-radius: 2px;
        }
      }
    }

    :deep(.el-button) + .el-button {
      margin-left: 4px;
    }
  }
</style>
