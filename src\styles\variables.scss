// sidebar
$menuText: rgba(255, 255, 255, 0.55);
$menuBg: linear-gradient(183deg, #004899 0%, #36dcdc 100%);
$menuActiveText: #fff;
$subMenuActiveText: #fff;
$backColor: #f2f3f5;

$menuHover: rgba(255, 255, 255, 0.1);
$menuTextHover: #fff;

$subMenuBg: rgba(255, 255, 255, 0.2);
$subMenuActiveBg: #36dbdc;
$subMenuHover: rgba(255, 255, 255, 0.1);

$sideBarWidth: 220px;
// $navbarHeight: 86px;
$navbarHeight: 96px;

$themeFontColor: var(--themeBlue);

$themePlain: #fff;
$themeBgBlue: linear-gradient(90deg, #132a53 0%, #16315d 100%);
$themeBlueHover: var(--themeBlue);
$themeBlueActive: var(--themeBlue);
$themeContentBg: var(--themeContentBg);
$formDangerLineColor: var(--nancalui-danger-line);

$themeFont: 14px;
$themeFont18: 18px;

$themeButtonHeight: 32px;
$themeButtonPadding: 14px;
$themeInputHeight: 32px;
$themeInputPadding: 0 7px;
$themeBorderColor: #ebedf0;

$themeBlue: var(--themeBlue);
$themeBlueHover: var(--themeBlueHover);
$normallBtnBorderHover: var(--normallBtnBorderHover);
$normallBtnBgHover: var(--normallBtnBgHover);
$normallBtnTextHover: var(--normallBtnTextHover);
$nancaluiInputDefaultBorder: var(--nancalui-input-default-border);
$nancaluiSplitLineBorder: var(--nancalui-split-line-border);

$newLineColor: #C5D0EA;
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuTextHover: $menuTextHover;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  navbarHeight: $navbarHeight;
  themeFontColor: $themeFontColor;
  themeBlue: $themeBlue;
  themePlain: $themePlain;
  themeBlueHover: $themeBlueHover;
  themeBlueActive: $themeBlueActive;
  themeFont18: $themeFont18;
  themeButtonHeight: $themeButtonHeight;
  themeButtonPadding: $themeButtonPadding;
  themeInputHeight: $themeInputHeight;
  themeInputPadding: $themeInputPadding;
  themeBorderColor: $themeBorderColor;
  themeContentBg: $themeContentBg;
  backColor: $backColor;
}
