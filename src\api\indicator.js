import request from '@/utils/request'
import URL from '@/const/urlAddress'

// 指标数据源统计
function getDatasourceCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/datasource-statistics`,
    method: 'get',
  })
}
// 指标定义统计
function getIndicatorCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/def-statistics`,
    method: 'get',
  })
}
// 指标总数统计
function getIndicatorTotalCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/statistics`,
    method: 'get',
  })
}
// 指标看板统计
function getIndicatorBoardCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/dashboard-statistics`,
    method: 'get',
  })
}

// 指标API服务情况统计
function getIndicatorApiCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/api-service`,
    method: 'get',
  })
}

// 指标按类型总数分布情况统计
function getIndicatorTypeCount() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/category-distribution-statistics`,
    method: 'get',
  })
}
// 近10日指表数据集趋势
function getIndicatorDatasetTrend() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/data-set-trend/last-10-day`,
    method: 'get',
  })
}
// 指标最新动态信息
function getIndicatorLatestDynamic() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/latest-dynamic-message`,
    method: 'get',
  })
}
// 热门指标top5
function getHotIndicatorTop5() {
  return request({
    url: `${URL.INDICATOR}/indicator/overview/hot-indicators/top5`,
    method: 'get',
  })
}

export default {
  getDatasourceCount,
  getIndicatorCount,
  getIndicatorTotalCount,
  getIndicatorBoardCount,
  getIndicatorApiCount,
  getIndicatorTypeCount,
  getIndicatorDatasetTrend,
  getIndicatorLatestDynamic,
  getHotIndicatorTop5,
}
