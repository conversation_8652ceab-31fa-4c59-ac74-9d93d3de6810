<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">流程名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="流程名称"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="methods.searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="methods.resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-content">
          <div class="out-box">
            <div class="out-box-title">流程管理</div>
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                saveWidth
                :isDisplayAction="true"
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    methods.initTable()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    methods.initTable()
                  },
                }"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="180"
              >
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      class="has-right-border"
                      code="systemManage_linkConfig_edit"
                      variant="text"
                      @click.prevent="methods.setFn(row)"
                      >配置</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { reactive, onMounted, toRefs, ref, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  const { proxy } = getCurrentInstance()
  const store = useStore()
  //按钮权限
  const { buttonAuthList, roleCode } = toRefs(store.state.user)
  const router = useRouter()
  const editForm = ref()

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    tableData: {},
    tableHeight: 436,
    key: 1,
    loading: false,
    originalFormInline: {
      keyword: null,
    },
    formInline: {
      keyword: null,
    },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'name', name: '流程名称' },
      { prop: 'updateByName', name: '更新人' },
      { prop: 'updateTime', name: '更新时间' },
    ],
  })
  const methods = {
    setTableHeight() {
      state.tableHeight = document.body.offsetHeight - 294
    },
    // 配置
    setFn(item) {
      router.push({ name: 'processConfigSet', query: { id: item.processCode } })
    },
    // 重置
    resetFn() {
      state.originalFormInline = {
        keyword: null,
      }
      methods.searchClickFn()
    },
    searchClickFn() {
      for (let key in state.originalFormInline) {
        state.formInline[key] = state.originalFormInline[key]
      }
      methods.initTable(true)
    },
    //搜索
    initTable(init = false) {
      state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
      let data = {
        pageNum: state.pageInfo.currentPage,
        pageSize: state.pageInfo.pageSize,
        condition: {
          name: state.formInline.keyword || null,
        },
      }
      state.loading = true
      api.system
        .processManageList(data)
        .then((res) => {
          state.loading = false
          if (res.success) {
            state.tableData = res.data
            state.pageInfo.total = res.data.total
            state.key++
          }
        })
        .catch(() => {
          state.tableData = {}
          state.loading = false
        })
    },
    // 列表（表格）操作变化
    tablePageChange(data) {
      state.pageInfo.currentPage = data.currentPage
      state.pageInfo.pageSize = data.pageSize
      methods.initTable()
    },
  }

  onMounted(() => {
    methods.setTableHeight()
    methods.initTable(true)
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;
            background-color: #1e89ff;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 60px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    box-sizing: border-box;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 0;
    padding: 0;
    .data-collection-page-content {
      width: 100%;
      height: 100%;
      border-radius: 2px;
      background-color: #fff;
      overflow: hidden;
      .out-box {
        height: 100%;
        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          color: #1d2129;
          font-weight: bolder;
          font-size: 16px;

          &:before {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            margin: auto;
            width: 4px;
            height: 18px;
            background: #1e89ff;
            content: '';
          }
        }
      }
      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }
      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
    }
  }
  .upload {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    &-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 66px;
      height: 66px;
      background-color: #ffffff;
      border: 1px dashed #c9cdd4;
      border-radius: 4px;
      cursor: pointer;
      &:has(.pic) {
        border: 1px solid #c5d0ea;
      }
      .icon {
        color: #8091b7;
        font-size: 24px;
      }
      .pic {
        width: 60px;
        height: 60px;
      }
    }
    &-desc {
      height: 66px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: column;
      margin-left: 9px;
      &-top {
        color: #1e89ff;
        font-size: 14px;
        cursor: pointer;
        position: relative;
      }
      &-bottom {
        color: #a8abb2;
        font-size: 14px;
      }
    }
  }
</style>
