<template>
  <!-- 非结构化数据采集查看  -->
  <div class="data-collection-detail container">
    <div class="add-box">
      <div class="page-title">
        查看
        <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
      </div>

      <div class="box-content scroll-bar-style">
        <div class="box-content-inside">
          <div class="basic-box inside-box">
            <n-form
              ref="ruleForm"
              class="base-form disabled-form"
              :data="state.ruleForm"
              label-width="100px"
              label-align="end"
              disabled
            >
              <div class="content-box first">
                <div class="content-title">
                  <span>基础信息</span>
                </div>
                <div v-show="state.basicTitle" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item label="覆盖模式：" field="overlayOrNot">
                      <n-checkbox
                        :isShowTitle="false"
                        :disabled="true"
                        label="清空已有表数据"
                        v-model="state.ruleForm.overlayOrNot"
                      />
                    </n-form-item>
                    <n-form-item class="check-style" label="采集类型：" field="collectType">
                      <div class="form-detail-text">{{
                        state.ruleForm.collectType === 'STRUCTURE' ? '结构化数据' : '非结构化数据'
                      }}</div>
                    </n-form-item>
                  </div>

                  <div class="inline-box">
                    <n-form-item label="采集任务名称：" field="name">
                      <div class="form-detail-text">{{ state.ruleForm.name }}</div>
                    </n-form-item>
                    <n-form-item label="任务环境：">
                      <div class="form-detail-text">{{
                        state.ruleForm.envType === 'OFFICIAL'
                          ? '正式环境'
                          : state.ruleForm.envType === 'TEST'
                          ? '开发环境'
                          : ''
                      }}</div>
                    </n-form-item></div
                  >

                  <n-form-item label="描述信息：">
                    <div class="form-detail-text">{{ state.ruleForm.description }}</div>
                  </n-form-item>
                  <n-form-item label="存储目录：">
                    <div class="form-detail-text">{{ state.ruleForm.treeName }}</div>
                  </n-form-item>
                </div>
              </div>

              <div class="content-box">
                <div class="content-title">
                  <span>选择文件</span>
                </div>
                <div v-show="state.selectFile" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="second" label="数据源：">
                      <div class="form-detail-text">{{ state.ruleForm.dataSourceName }}</div>
                    </n-form-item>

                    <n-form-item class="third" field="fileName" label="文件选择：">
                      <div class="form-detail-text">{{ state.ruleForm.fileName }}</div>
                    </n-form-item>
                  </div>
                </div>
                <div class="content-title">
                  <span>文件信息</span>
                </div>
                <div v-show="state.fileInfo" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="second" label="文件密级：">
                      <div class="form-detail-text">{{
                        state.ruleForm.confidentialityLevelName
                      }}</div>
                    </n-form-item>

                    <n-form-item class="third" field="fileName" label="标签：">
                      <div class="form-detail-text">
                        <template v-if="state.ruleForm?.tagList?.length > 0">
                          <div
                            v-for="(item, index) in state.ruleForm.tagList"
                            :key="index"
                            class="label"
                            :style="
                              'color:' +
                              item.color.split('_')[0] +
                              ';background-color:' +
                              item.color.split('_')[1] +
                              ';border-color:' +
                              (item.color.split('_').length > 2
                                ? item.color.split('_')[2]
                                : item.color.split('_')[0])
                            "
                            >{{ item.name }}</div
                          >
                        </template>
                        <span v-else>暂无数据</span>
                      </div>
                    </n-form-item>
                  </div>
                </div>
                <div v-show="state.selectFile">
                  <div v-if="state.haveFileData" class="file-table-box" v-loading="state.loading">
                    <n-form-item class="file-table-item">
                      <n-public-table
                        :isDisplayAction="false"
                        :table-head-titles="state.fileTableHeadTitles"
                        :tableHeight="'auto'"
                        :maxHeight="240"
                        :tableData="state.fileTableData"
                        :showPagination="false"
                        :key="state.key"
                      />
                    </n-form-item>
                  </div>
                  <div v-else class="table-no-content-box">
                    <div class="table-no-content">
                      <img
                        class="pic-no-conyent"
                        src="@/assets/table-no-content.png"
                        alt="暂无内容"
                      />
                      <div>暂无内容</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-box">
                <div class="content-title">
                  <span>设置目标表</span>
                </div>
                <div v-show="state.setAim" class="content-box-view">
                  <div class="inline-box">
                    <n-form-item class="check-style" label="目标表：">
                      <div class="inline">
                        <div class="form-detail-text">
                          {{
                            state.ruleForm.sinkModelCreateMethod === 'NEW'
                              ? '已有数据表'
                              : '新建数据表'
                          }}
                        </div>
                        <n-select
                          v-model="state.ruleForm.sinkModelId"
                          placeholder=" "
                          filter
                          allow-clear
                        >
                          <n-option
                            v-for="item in state.modeTableOptions"
                            :key="item.value"
                            :name="item.label"
                            :value="item.value"
                          />
                        </n-select>
                      </div>
                    </n-form-item>
                    <n-form-item label="" />
                    <n-form-item field="cnName" label="模型中文名称：">
                      <n-input
                        v-model="state.ruleForm.modelcName"
                        disabled
                        noborder
                        placeholder=" "
                      />
                    </n-form-item>
                    <n-form-item field="name" label="模型英文名称：" class="dif">
                      <n-input
                        v-model="state.ruleForm.modelName"
                        placeholder=" "
                        disabled
                        noborder
                      />
                    </n-form-item>
                  </div>

                  <div v-if="state.haveLinkData" class="bottom-line" v-loading="state.loading">
                    <!-- 连线映射 -->
                    <div class="link-mapping-box">
                      <sceneTableLinkDetail ref="sceneTableLinkDetail" :oneline="true" />
                    </div>
                  </div>
                  <div v-else class="table-no-content-box">
                    <div class="table-no-content">
                      <img
                        class="pic-no-conyent"
                        src="@/assets/table-no-content.png"
                        alt="暂无内容"
                      />
                      <div>暂无内容</div>
                    </div>
                  </div>
                </div>
              </div>
            </n-form>
          </div>
          <!-- 配置信息 -->
          <div class="content-box">
            <div class="content-title">
              <span>调度信息</span>
            </div>
            <div class="content-box-view">
              <configureSchedulingRulesDetail
                v-show="state.dispatch"
                ref="configureSchedulingRulesDom"
                :showTaskRule="false"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import configureSchedulingRulesDetail from './components/configure-scheduling-rules-detail'
  import sceneTableLinkDetail from './scene-table-link/detail'

  export default {
    name: '',
    components: {
      configureSchedulingRulesDetail,
      sceneTableLinkDetail,
    },
    props: {},
    setup() {
      const router = useRouter()
      const configureSchedulingRulesDom = ref()
      const ruleForm = ref()
      const sceneTableLinkDetail = ref()
      const state = reactive({
        basicTitle: true,
        selectFile: true,
        fileInfo: true,
        setAim: true,
        dispatch: true,
        key: 1,
        ruleForm: {
          name: '',
          description: '',
          collectType: 'STRUCTURE',
          dataSourceType: '',
          overlayOrNot: true,
          showTaskRule: true,
          fileType: '',
          fileTableData: {}, //文件数据预览
          fileName: '', //文件名
          sinkModelCreateMethod: 'NEW',
          sinkModelId: '',
          modeFeign: '', //原始数据层id
          modeFeignName: '', //原始数据层 name
          modelcName: '', // 模型中文名
          modelName: '', // 模型英文名
          envType: '',
          dataSourceName: '',
          treeName: '',
          confidentialityLevelName: '',
          tagList: [],
        },
        fileTableHeadTitles: [{ prop: 'number', name: '序号', width: 80 }],
        leftData: [],
        rightData: [],
        dataSourceNames: [], //画布左表格头部title
        modelNames: [], //画布右表格头部title
        totalData: {},
        loading: false,
        allData: {},
        editId: null,
        datasourceTypeOptions: [
          {
            label: 'excel',
            value: 'EXCEL',
          },
          {
            label: 'csv',
            value: 'CSV',
          },
        ],
        haveFileData: false, //有文件解析内容
        haveLinkData: false, //有映射内容
      })

      const methods = {
        expandTitleFn(name) {
          state[name] = !state[name]
        },
        closeFn() {
          router.push({
            name: 'dataCollectionIndex',
            query: {},
          })
        },
        //数据结构化采集编辑初始化
        editGetData() {
          state.loading = true
          api.dataManagement
            .getCollectTaskDetail({ id: state.editId })
            .then(async (res) => {
              let { data, success } = res
              if (success) {
                let {
                  name,
                  description,
                  sinkModelId,
                  dataSourceId,
                  sourceTableName,
                  sinkModelEname,
                  sinkModelCname,
                  collectType,
                  sinkModelCreateMethod,
                  envType,
                  dataSourceName,
                  collectFilePath,
                  collectFileName,
                  treeName,
                  overlayOrNot,
                  confidentialityLevelName,
                  tagList,
                } = data

                state.ruleForm.overlayOrNot = overlayOrNot || false
                state.ruleForm.name = name || '暂无数据'
                state.ruleForm.description = description || '暂无数据'
                state.allData = data
                state.ruleForm.modelcName = sinkModelCname || '暂无数据'
                state.ruleForm.modelName = sinkModelEname || '暂无数据'
                state.ruleForm.sinkModelId = sinkModelId
                state.ruleForm.collectType = collectType
                state.ruleForm.fileName = sourceTableName || '暂无数据'
                state.ruleForm.envType = envType
                state.ruleForm.dataSourceName = dataSourceName || '暂无数据'
                state.ruleForm.treeName = treeName || '暂无数据'
                state.ruleForm.confidentialityLevelName = confidentialityLevelName || '暂无数据'
                state.ruleForm.tagList = tagList || []

                state.ruleForm.sinkModelCreateMethod = sinkModelCreateMethod

                await methods.getSourceTables(data)
                if (sinkModelId) {
                  await methods.modeTableChange(sinkModelId)
                }

                state.loading = false
                methods.detaileInitCanvas(state.allData)
                configureSchedulingRulesDom.value.init(state.allData)
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 文件解析
        async getSourceTables(item) {
          await api.dataManagement
            .fileDataPreview({
              fileName: item.sourceTableName || null,
              fileEncoding: item.fileEncoding || null,
              fileFrom: item.dataSourceType,
              ftpSourceFilePath: item.collectFilePath,
              ftpSourceId: item.dataSourceId,
            })
            .then((res) => {
              let { data, success } = res
              if (success) {
                state.haveFileData = true
                nextTick(() => {
                  let box_width = document.querySelectorAll('.file-table-box')[0].clientWidth
                  let _leftData = []
                  let _tableHeadTitles = []
                  let tableData = []
                  if (data.jsonData) {
                    state.showFirstHeader = false
                    data.jsonData.forEach((val) => {
                      Object.keys(val).forEach((key) => {
                        if (Object.prototype.toString.call(val[key]) === '[object Object]') {
                          val[key] = JSON.stringify(val[key])
                        }
                      })
                    })
                    Object.keys(data.jsonData[0]).forEach((key, index) => {
                      _tableHeadTitles.push({
                        prop: key,
                        name: key,
                        width: box_width / 6,
                      })
                      _leftData.push({
                        colName: '',
                        length: '',
                        comment: key,

                        fieldType: 'STRING',
                        fieldTypeName: '字符串',
                        cnName: key,
                        name: '',
                        fieldLength: '64',
                        number: index + 1,
                        isRequiredFieldLength: false,
                        isPass: true,

                        oldname: '',
                        oldcnName: key,
                        oldfieldType: '',
                        oldfieldLength: '',
                      })
                    })
                    tableData = [...data.jsonData]
                  } else if (data.tableLikeData) {
                    state.showFirstHeader = true
                    tableData = data.tableLikeData.map((val) => {
                      let obj = {}
                      val.forEach((v, i) => {
                        obj['col_' + (i + 1)] = v
                      })
                      return obj
                    })
                    data.tableLikeData[0].forEach((key, index) => {
                      let cName = key
                      if (!item.firstRowIsHeader) {
                        cName = 'col_' + (index + 1)
                      }
                      _tableHeadTitles.push({
                        prop: 'col_' + (index + 1),
                        name: cName,
                        width: box_width / 6,
                      })

                      _leftData.push({
                        colName: '',
                        length: '',
                        comment: cName,

                        fieldType: 'STRING',
                        fieldTypeName: '字符串',
                        cnName: cName,
                        name: '',
                        fieldLength: '64',
                        number: index + 1,
                        isRequiredFieldLength: false,
                        isPass: true,

                        oldname: '',
                        oldcnName: cName,
                        oldfieldType: '',
                        oldfieldLength: '',
                      })
                    })
                  }
                  if (item.firstRowIsHeader) {
                    tableData = tableData.slice(1)
                  }
                  state.fileTableHeadTitles = _tableHeadTitles
                  state.allExcelData = tableData

                  state.fileTableData = { list: tableData }
                  state.leftData = _leftData
                  state.tableLeftData = { list: _leftData }
                  state.key++
                })
              } else {
                state.haveFileData = false
              }
            })
            .catch(() => {
              state.haveFileData = false
              state.loading = false
            })
        },
        // 根据模型id读取数据
        async modeTableChange(id) {
          await api.model
            .getModeData({ id })
            .then((res) => {
              // 渲染右边表格
              let { success } = res
              if (success) {
                state.rightData = res.data
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 编辑初始化数据-映射
        async detaileInitCanvas(data) {
          if (data) {
            state.haveLinkData = true
            state.totalData = data
            let { sinkModelEname, collectFileName, dataSourceName } = data

            state.dataSourceNames = [dataSourceName, collectFileName]
            state.modelNames = ['原始数据层', sinkModelEname]
            data.graphData = {
              leftData: state.leftData,
              rightData: state.rightData,
              dataSourceNames: state.dataSourceNames,
              modelNames: state.modelNames,
            }

            data.allEdge = data.columnMapping
            if (state.leftData?.length || state.rightData?.length) {
              nextTick(() => {
                sceneTableLinkDetail.value.changTable(data.graphData, function (that) {
                  let { methods } = that
                  // 连线

                  if (state.totalData.allEdge) {
                    methods.addEdge({ data: state.totalData.allEdge, initLater: true })
                  }
                  // data.graph.scrollToPoint(600, 150)
                })
              })
            } else {
              state.haveLinkData = false
            }
          }
        },

        // 获取模型层下拉列表
        async getDataModelTree() {
          let result = await api.model.getDataModelTree().then((res) => {
            let { data } = res
            data.forEach((item) => {
              item.label = item.name
              item.value = item.id
              if (item.name === '原始数据层') {
                state.ruleForm.modeFeign = item.id
              }
            })

            state.modeFeignOptions = data
            let a = methods.modeFeignChange({ value: state.ruleForm.modeFeign })
            return a
          })
          return result
        },
        // 模型层-change
        async modeFeignChange(data) {
          let _data = state.modeFeignOptions.filter((item) => {
            return item.id === data.value
          })

          state.ruleForm.modeFeignName = _data[0].label
          let params = { layerId: state.ruleForm.modeFeign, jobType: 'COLLECT_JOB' }
          if (state.editId) {
            params.jobId = state.editId
          }
          let result = await api.model.getModelListWithLayerId(params).then((res) => {
            let { data, success } = res
            if (success) {
              data.forEach((item) => {
                item.cnName = item.cnName
                item.value = item.id
                item.label = item.name
              })

              state.modeTableOptions = data
              return { modeTableOptions: state.modeTableOptions }
            }
          })
          return result
        },

        // 取消
        cancel() {
          router.go(-1)
        },
      }
      onMounted(() => {
        state.editId = router.currentRoute.value.query.editId
        if (state.editId) {
          methods.getDataModelTree() //获取右侧数据
          methods.editGetData()
        }
      })

      return {
        state,
        ruleForm,
        configureSchedulingRulesDom,
        sceneTableLinkDetail,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-detail {
    height: 100%;
    padding: 12px;
    .add-box {
      height: 100%;
      overflow: hidden;
      border-radius: 8px;
      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 16px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;
        background-color: #fff;
        border-radius: 2px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          margin: auto;
          color: #1d2129;
          font-weight: normal;
          font-size: 14px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 2px;
          cursor: pointer;
          &:hover {
            color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
      .box-content {
        position: relative;
        height: calc(100% - 60px);
        margin-top: 8px;
        overflow: auto;
        .content-box {
          margin-top: 10px;
          padding: 16px 0;
          background-color: #fff;
          border-radius: 2px;
          &.first {
            margin-top: 0;
          }
          &-view {
            padding: 0 16px;
          }
        }
        .content-title {
          position: relative;
          height: 30px;
          margin-bottom: 16px;
          padding-left: 14px;
          color: #2b71c2;
          font-size: 14px;
          line-height: 30px;
          background-color: #f2f6fc;
          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 4px;
            height: 18px;
            margin: auto;
            background: #1e89ff;
            content: '';
          }
        }
        .inside-box {
          .box-title {
            height: 48px;
            color: #000;
            font-weight: bolder;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            line-height: 48px;
            border-bottom: 1px solid #ebedf0;

            span {
              position: relative;
              width: max-content;
            }

            i {
              position: absolute;
              right: 0;
              bottom: -2px;
              left: 0;
              height: 6px;
              background: #008ae7;
              opacity: 0.2;
            }
          }
        }
        .bottom-line {
          margin-top: 13px;
          .link-mapping-box {
            height: 360px;
          }
        }
        .base-form {
          .form-detail-text {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-right: 8px;
            color: #000000;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            .label {
              box-sizing: border-box;
              height: 24px;
              margin-right: 4px;
              padding: 0 4px;
              color: #1f84e1;
              font-size: 12px;
              line-height: 22px;
              background-color: #edf5fd;
              border: 1px solid #1f84e1;
              border-radius: 6px;
            }
          }
          .nancalui-input {
            width: 400px;
          }
          .nancalui-textarea__div {
            width: 910px !important;
          }
          .file-table-box {
            width: 100%;
            margin: 11px 0 11px 0;
            padding: 16px 20px;
            border: 1px solid #ebedf0;
            border-radius: 8px;
            :deep(.nancalui-table) {
              thead tr th {
                min-width: 25%;
              }
              tbody tr td {
                min-width: 25%;
              }
            }
          }
          .inline-box {
            display: flex;
            flex-wrap: wrap;
            .nancalui-form__item--horizontal {
              width: 40%;
            }
          }
          .check-style {
            .inline {
              display: flex;
              align-items: baseline;
              div {
                width: max-content;
              }
            }
          }
        }
        .list {
          height: 100%;
          background: #fff;
        }
        .content-bg-img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 17.5%;
          min-width: 252px;
        }
      }
    }
  }
  .table-no-content-box {
    height: 300px;
    margin: 11px 0 11px 0;
    padding: 16px 20px;
    text-align: center;
    border: 1px solid #ebedf0;
    border-radius: 8px;
    .table-no-content {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      .pic-no-conyent {
        width: 140px;
      }
      div {
        margin-top: 15px;
        color: #999999;
        font-size: 12px;
        line-height: normal;
      }
    }
  }
</style>
