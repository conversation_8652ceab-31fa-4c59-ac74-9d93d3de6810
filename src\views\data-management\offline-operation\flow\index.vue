<template>
  <section class="flow" id="flow">
    <div id="dagDetailFlow" class="graph"></div>
    <div v-if="!props.isPopup" class="search-box">
      <div class="search">
        <n-input class="ipt" v-model="state.searchText" clearable placeholder="作业名称/作业ID" />
        <div class="iconBox" @click.stop.prevent="searchFn">
          <SvgIcon class="icon" icon="icon-search" />
        </div>
      </div>
      <div class="tip">
        <SvgIcon class="icon" icon="icon-care" />
        生产环境，谨慎操作
      </div>
    </div>
    <!--    <div v-if="!state.isSee || showStatus" class="label-box">-->
    <!--      <div class="label"><SvgIcon class="icon" icon="icon-true" />成功</div>-->
    <!--      <div class="label"><SvgIcon class="icon" icon="icon-false" />失败</div>-->
    <!--      <div class="label"><SvgIcon class="icon" icon="icon-stop-new" />停止</div>-->
    <!--      <div class="label"><SvgIcon class="icon" icon="icon-run-new" />运行中</div>-->
    <!--      <div class="label"><SvgIcon class="icon" icon="icon-wait-new" />等待运行</div>-->
    <!--    </div>-->
  </section>
</template>

<script setup>
  import { reactive, onMounted, onBeforeUnmount, watch, nextTick, defineProps, provide } from 'vue'
  import { useStore } from 'vuex'
  import { Graph, Model } from '@antv/x6'
  import { DagreLayout } from '@antv/layout'
  import FlowGraph from './graph'
  const store = useStore()
  const emits = defineEmits(['showRunView', 'refreshFn'])
  const props = defineProps({
    checkedItem: {
      type: Object,
      default: {},
    },
    isPopup: {
      type: Boolean,
      default: false,
    },
    showStatus: {
      type: Boolean,
      default: false,
    },
    isParent: {
      type: Boolean,
      default: false,
    },
    collectJobsInfo: {
      type: Array,
      default: [],
    },
  })
  const state = reactive({
    graph: {},
    timeFlag: null,
    resizeFn: null,
    isSee: true,
    searchText: '',
    mapData: {},
    checkedNodes: [],
    showNodeInfo: { nodeCode: null },
  })
  // 初始化创建画布
  const initFn = () => {
    state.graph = FlowGraph.init()
    state.resizeFn = () => {
      nextTick(() => {
        resizeFn()
      })
    }
    state.resizeFn()
    window.addEventListener('resize', state.resizeFn)
  }
  // 计算尺寸
  const resizeFn = () => {
    const { width, height } = getContainerSize()
    state.graph.resize(width, height)
    state.graph.centerContent()
  }
  // 获取画布容器大小
  const getContainerSize = () => {
    return {
      width: document.getElementById('flow').offsetWidth,
      height: document.getElementById('flow').offsetHeight + 70,
    }
  }

  // 字体宽度计算
  const getTextWidth = (text, font = '14px Source Han Sans CN') => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx.font = font // 必须与实际字体完全一致[3,7](@ref)
    return ctx.measureText(text).width
  }

  // 切换画布数据,默认实时
  const setMapData = () => {
    window.ssss = state
    const cells = (Model.FromJSONData = {
      nodes: [],
      edges: [],
    })
    let relationship = state.mapData?.relationship.filter((val) =>
      state.checkedNodes.some(
        (v) =>
          (v.nodeCode === val.postNodeCode && v.showPost) ||
          (v.nodeCode === val.preNodeCode && v.showPre),
      ),
    )
    let newNodes = state.mapData?.nodes.filter((val) =>
      relationship.some((v) => v.postNodeCode === val.nodeCode || v.preNodeCode === val.nodeCode),
    )
    if (props.isParent) {
      const parentNode = FlowGraph.cerateParentNode()
      parentNode.children = newNodes?.map((item) => String(item.nodeCode))
      cells.nodes.push(parentNode)
      // relationship = relationship.concat(props.collectJobsInfo.map(_=>({preNodeCode:_.id,postNodeCode:"parentNode"})))
    }
    relationship.forEach((item) => {
      if (item.preNodeCode !== 0) {
        cells.edges.push({
          shape: 'edge',
          interacting: true,
          connector: 'normal',
          // router: { name: 'normal' },
          data: { ...item, showStatus: props.showStatus },
          attrs: {
            line: {
              stroke: '#8091B7',
              strokeWidth: 1,
              strokeDasharray: 0,
              targetMarker: { name: 'classic', size: 4 },
            },
          },
          zIndex: 0,
          source: { cell: String(item.preNodeCode), port: 'bottom' },
          target: { cell: String(item.postNodeCode), port: 'top' },
        })
      }
    })
    newNodes.forEach((item, index) => {
      item.id = String(item.nodeCode)
      item.location = { x: item?.location?.x || 20, y: item?.location?.y || index * 120 + 38 }
      const list = cells.nodes
      const fontWidth = getTextWidth(item.name) + 84
      list.push({
        position: item.location,
        id: item.id,
        data: {
          ...item,
          isSee: state.isSee,
          dsProcessCode: state.mapData.dsProcessCode,
          dsTaskId: state.mapData.dsTaskId,
          processId: state.mapData.processId,
          collectJobIds: state.mapData.collectJobIds,
          hasFailure: state.mapData.hasFailure,
          showStatus: props.showStatus,
        },
        shape: 'dag-detail-box',
        size: {
          width: fontWidth > 356 ? 356 : fontWidth,
          height: 38,
        },
        ports: {
          groups: {
            left: {
              id: 'top',
              position: 'top',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#697A9A',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
            right: {
              id: 'bottom',
              position: 'bottom',
              attrs: {
                circle: {
                  r: 3,
                  magnet: true,
                  stroke: '#697A9A',
                  strokeWidth: 1,
                  fill: '#fff',
                  style: { visibility: 'hidden' },
                },
              },
            },
          },
          items: [
            { id: 'top', group: 'top' },
            { id: 'bottom', group: 'bottom' },
          ],
        },
        zIndex: 1,
      })
    })
    const dagreLayout = new DagreLayout({
      type: 'dagre',
      rankdir: 'TB',
      align: undefined,
      ranksep: 35,
      nodesep: 15,
    })
    const model = dagreLayout.layout(cells)
    state.graph.fromJSON(model)
    state.graph.centerContent()
    state.graph.on('node:click', ({ cell }) => {
      let nodes = state.graph.getNodes()
      nodes.forEach((obj) => {
        if (obj.id === 'parentNode') return
        if (obj.getData().nodeCode === cell.getData().nodeCode) {
          obj.setData({ ...obj.getData(), checked: true })
        } else {
          obj.setData({ ...obj.getData(), checked: false })
        }
      })
      state.graph.centerCell(cell)
      emits('showRunView', cell.getData())
    })

    state.graph.batchUpdate(() => {
      setTimeout(() => {
        const parentNode = state.graph.getCellById('parentNode')
        if (parentNode) {
          parentNode.fit({
            deep: true, // 同时调整子节点和边的位置
            padding: { top: 20, bottom: 20, left: 120, right: 120 }, // 添加一些内边距
          })
          const BBox = parentNode.getBBox()
          const collectJobsInfoNum = props.collectJobsInfo?.length || 0
          if (props.collectJobsInfo) {
            props.collectJobsInfo.forEach((item, index) => {
              const fontWidth = getTextWidth(item.name) + 84
              item.location = {
                x:
                  BBox.x +
                    BBox.width / 2 +
                    index * 280 +
                    index * 20 -
                    (collectJobsInfoNum / 2) * 280 || 20,
                y: -100,
              }
              state.graph.addNode({
                position: item.location,
                id: item.id,
                shape: 'dag-detail-box',
                size: {
                  width: fontWidth > 356 ? 356 : fontWidth,
                  height: 38,
                },
                data: {
                  ...item,
                  isSee: state.isSee,
                  dsProcessCode: state.mapData.dsProcessCode,
                  dsTaskId: state.mapData.dsTaskId,
                  processId: state.mapData.processId,
                  collectJobIds: state.mapData.collectJobIds,
                  hasFailure: state.mapData.hasFailure,
                  showStatus: props.showStatus,
                },
                ports: {
                  groups: {
                    left: {
                      id: 'top',
                      position: 'top',
                      attrs: {
                        circle: {
                          r: 3,
                          magnet: true,
                          stroke: '#697A9A',
                          strokeWidth: 1,
                          fill: '#fff',
                          style: { visibility: 'hidden' },
                        },
                      },
                    },
                    right: {
                      id: 'bottom',
                      position: 'bottom',
                      attrs: {
                        circle: {
                          r: 3,
                          magnet: true,
                          stroke: '#697A9A',
                          strokeWidth: 1,
                          fill: '#fff',
                          style: { visibility: 'hidden' },
                        },
                      },
                    },
                  },
                  items: [
                    { id: 'top', group: 'top' },
                    { id: 'bottom', group: 'bottom' },
                  ],
                },
                zIndex: 1,
              })

              state.graph.addEdge({
                shape: 'edge',
                interacting: true,
                connector: 'normal',
                attrs: {
                  line: {
                    stroke: '#8091B7',
                    strokeWidth: 1,
                    strokeDasharray: 0,
                    targetMarker: { name: 'classic', size: 4 },
                  },
                },
                zIndex: 0,
                source: {
                  cell: String(item.id),
                  anchor: {
                    name: 'bottom',
                    args: {
                      dy: 0,
                    },
                  },
                  connectionPoint: 'anchor',
                },
                target: {
                  cell: 'parentNode',
                  anchor: {
                    name: 'top',
                  },
                  connectionPoint: 'anchor',
                },
              })
            })
          }
        }
      }, 0)
    })
    sessionStorage.setItem('checkedNodeGradeIsChangeCode', '0')
  }

  // 判断是否已更改节点
  const checkChangeFn = () => {
    if (sessionStorage.getItem('checkedNodeGradeIsChangeCode') !== '0') {
      if (sessionStorage.getItem('checkedNodeGradeIsChangeCode') === 'refresh') {
        emits('refreshFn', true)
        sessionStorage.setItem('checkedNodeGradeIsChangeCode', '0')
      } else {
        state.checkedNodes = JSON.parse(sessionStorage.getItem('checkedNodeGrade'))
        setMapData()
      }
    }
  }

  const searchFn = () => {
    if (state.searchText) {
      let nodes = state.graph.getNodes()
      let searchItemCellId = null
      nodes.forEach((item) => {
        item.setData({ ...item.getData(), checked: false })
        let data = item.getData()
        if (
          data.id.indexOf(state.searchText) !== -1 ||
          data.name.indexOf(state.searchText) !== -1
        ) {
          if (!searchItemCellId) {
            searchItemCellId = data.id
            item.setData({ ...item.getData(), checked: true })
          }
        }
      })
      if (searchItemCellId) {
        let cell = state.graph.getCellById(searchItemCellId)
        state.graph.centerCell(cell)
        emits('showRunView', cell.getData())
      }
    }
  }

  watch(
    () => props.checkedItem,
    (newVal) => {
      state.mapData = newVal
      if (state.mapData) {
        nextTick(() => {
          sessionStorage.setItem('checkedNodeGradeIsChangeCode', '0')
          let nodes = newVal.nodes.map((val) => {
            return { nodeCode: val.nodeCode, showPre: true, showPost: true }
          })
          state.checkedNodes = [...nodes]
          if (newVal.nodeCode) {
            state.isSee = true
            state.checkedNodes = []
            state.checkedNodes.push({
              nodeCode: newVal.nodeCode,
              showPre: true,
              showPost: true,
            })
            sessionStorage.setItem('checkedNodeGrade', JSON.stringify(state.checkedNodes))
            state.mapData.nodes = state.mapData.nodes.map((val) => {
              if (val.nodeCode === newVal.nodeCode) {
                val.checked = true
              }
              return { ...val, needGrad: true }
            })
          } else {
            state.isSee = false
          }
          setMapData()
        })
      }
    },
  )
  onMounted(() => {
    initFn()
    sessionStorage.setItem('checkedNodeGradeIsChangeCode', '0')
    if (state.timeFlag) {
      clearInterval(state.timeFlag)
      state.timeFlag = null
    }
    state.timeFlag = setInterval(() => {
      checkChangeFn()
    }, 1000)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('resize', state.resizeFn)
    FlowGraph.destroy()
    if (state.timeFlag) {
      clearInterval(state.timeFlag)
      state.timeFlag = null
    }
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .flow {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 0 0 8px 8px;
    background: #f5f7fa;
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
    .search-box {
      position: absolute;
      top: 16px;
      left: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 470px;
      .search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 262px;
        height: 32px;
        box-sizing: border-box;
        background-color: #fff;
        .ipt {
          width: calc(100% - 32px);
          box-sizing: border-box;
          color: #1d2129;
          font-size: 14px;
        }
        .iconBox {
          width: 32px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #c9cdd4;
          border-left: none;
          cursor: pointer;
          &:hover {
            border: 1px solid #2f5cd6;
            .icon {
              color: #2f5cd6;
            }
          }
          .icon {
            font-size: 16px;
            color: #909399;
          }
        }
      }
      .tip {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        color: #ff7d00;
        font-size: 16px;
        .icon {
          font-size: 20px;
          margin-right: 8px;
        }
      }
    }
    .label-box {
      position: absolute;
      top: 16px;
      right: 16px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 440px;
      .label {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: 16px;
        padding: 5px 0;
        color: #1d2129;
        font-size: 14px;
        .icon {
          font-size: 16px;
          margin-right: 4px;
          color: #fff;
        }
      }
    }

    :deep(.x6-graph-scroller) {
      overflow: hidden;
    }
  }
  .n-drawer-body-content {
    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      .label {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: flex-start;
        .name {
          width: 70px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 14px;
        }
        .value {
          width: calc(100% - 70px);
          overflow: hidden;
          color: rgba(0, 0, 0, 0.65);
          font-weight: 400;
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    &-table {
      height: calc(100% - 36px);
      .model-table {
        :deep(.nancalui-table__thead) {
          tr {
            th {
              border-bottom: none;
            }
          }
        }
        :deep(.nancalui-table__header-wrapper),
        :deep(.nancalui-table__scroll-view) {
          display: inline-table;

          tr {
            th {
              border-right: none;
            }
          }
        }
        :deep(.nancalui-table__fix-header) {
          overflow: auto !important;
          &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #e1e1e1;
            border-radius: 2px;
          }
        }
      }
    }
  }
  .n-drawer-body-footer {
    .table-content-pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;
      height: 64px;

      &-total {
        color: rgba(0, 0, 0, 0.46);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        span {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }
</style>
