<template>
  <section class="container-padding16">
    <section class="container-padding16-box">
      <section class="container-padding16-right">
        <n-tabs v-model="state.activeName" @active-tab-change="tabsChangeFn">
          <n-tab title="未读消息" id="UNREAD" />
          <n-tab title="已读消息" id="READ" />
          <n-tab title="全部消息" id="ALL" />
        </n-tabs>
        <section class="commonForm">
          <n-button
            v-if="state.activeName === 'READ'"
            variant="solid"
            @click.prevent="operateFn('DEL')"
            >批量删除</n-button
          >
          <n-button
            v-if="state.activeName === 'UNREAD'"
            variant="solid"
            @click.prevent="operateFn('ALL')"
            >全部已读</n-button
          >
          <n-button
            v-if="state.activeName === 'UNREAD'"
            color="primary"
            @click.prevent="operateFn('TAB')"
            >标记已读</n-button
          >

          <n-form :inline="true" :model="state.searchForm" class="demo-form-inline">
            <n-form-item label="消息类型：">
              <n-select
                v-model="state.searchForm.messageType"
                size="small"
                placeholder="请选择"
                :allow-clear="true"
                filter
                :options="
                  state.statusList.map((val) => {
                    return { ...val, name: val.label, value: val.value }
                  })
                "
                @value-change="onSearch(true)"
              />
            </n-form-item>
          </n-form>
        </section>

        <div class="table">
          <CfTable
            ref="tableRef"
            actionWidth="200"
            :key="state.tableData"
            :tableConfig="{
              data: state.tableData,
              rowKey: 'id',
            }"
            :table-head-titles="state.tableHeadTitles"
            :isNeedSelection="state.activeName !== 'ALL'"
            :paginationConfig="{
              total: state.pageInfo.total,
              pageSize: state.pageInfo.pageSize,
              currentPage: state.pageInfo.currentPage,
              onCurrentChange: (v) => {
                state.pageInfo.currentPage = v
                onSearch(false)
              },
              onSizeChange: (v) => {
                state.pageInfo.pageSize = v
                onSearch(false)
              },
            }"
          >
            <template #messageName="{ row }">
              <span class="messageName" @click.prevent="readMessageFn(row)">{{
                row.messageContent
              }}</span>
            </template>
          </CfTable>
        </div>

        <!-- <n-public-table
          :isNeedSelection="state.activeName !== 'ALL'"
          :isDisplayAction="false"
          :table-head-titles="state.tableHeadTitles"
          :pagination="state.pageInfo"
          :tableData="state.tableData"
          :tableHeight="state.tableHeight"
          :actionWidth="200"
          :key="state.key"
          emptyText="暂无相关消息"
          @tablePageChange="tablePageChange"
          @handle-selection-change="checkSelectionChange"
        >
          <template #messageName="{ editor }">
            <span class="messageName" @click.prevent="readMessageFn(editor.row)">{{
              editor.row.messageContent
            }}</span>
          </template>
        </n-public-table> -->
      </section>
    </section>
  </section>
</template>

<script setup>
  import { onMounted, reactive, ref, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  const store = useStore()
  const router = useRouter()
  // 获取当前组件实例
  const { proxy } = getCurrentInstance()
  const tableRef = ref()
  const state = reactive({
    key: 1,
    loading: false,
    activeName: 'UNREAD',
    isHide: false, // 是否隐藏整个左侧框
    isShow: true, // 是否展示左侧下拉列表
    searchForm: {
      messageType: 'ALL',
      projectCode: 'ALL',
    },
    tableHeadTitles: [
      // 必须为name 否则渲染不出表头
      { prop: 'num', name: '序号', width: '100px' },
      { prop: 'messageContent', name: '消息内容', slot: 'messageName', width: '480px' },
      // { prop: 'projectName', name: '所属场景', width: '100px' },
      { prop: 'messageTypeName', name: '消息类型', width: '120px' },
      { prop: 'messageTime', name: '消息时间' },
    ],
    statusList: [
      { label: '全部类型', value: 'ALL' },
      { label: '审批消息', value: 'AUDIT' },
      { label: '任务消息', value: 'JOB' },
      { label: '申请消息', value: 'APPLY' },
      { label: '告警消息', value: 'ALERT' },
    ],
    projectList: [],
    menu: [],
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableData: [], // 当前展示的table数据
    tableHeight: 290,
    checkList: [], // 选中
  })

  // 控制左侧隐藏
  const hideFn = () => {
    state.isHide = !state.isHide
  }

  // 控制左侧消息列表展示
  const showFn = () => {
    state.isShow = !state.isShow
  }

  // 查询列表
  const onSearch = (init = false) => {
    if (init) {
      state.checkList = []
      state.tableData = []
    }
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let messageType = null
    let projectCode = null
    if (state.searchForm.messageType && state.searchForm.messageType !== 'ALL') {
      messageType = state.searchForm.messageType
    }
    if (state.searchForm.projectCode && state.searchForm.projectCode !== 'ALL') {
      projectCode = state.searchForm.projectCode
    }
    let params = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        messageType: messageType,
        // projectCode: projectCode,
      },
    }
    if (state.activeName === 'ALL') {
      params.condition.status = null
    } else {
      params.condition.status = state.activeName
    }
    api.base.getMessageList(params).then((res) => {
      if (res.code === 'SUCCESS') {
        let messageTypeName = ''
        // 新增序号属性
        res.data.list.map((item, index) => {
          if (item.messageType === 'AUDIT') {
            messageTypeName = '审批消息'
          } else if (item.messageType === 'JOB') {
            messageTypeName = '任务消息'
          } else if (item.messageType === 'APPLY') {
            messageTypeName = '申请消息'
          } else if (item.messageType === 'ALERT') {
            messageTypeName = '告警消息'
          }
          return Object.assign(item, {
            num: index + 1,
            messageTypeName,
          })
        })
        state.tableData = res.data.list
        state.pageInfo.total = res.data.total
        state.key++
      }
    })
  }

  // 切换消息类型
  const tabsChangeFn = (e) => {
    setTimeout(() => {
      onSearch(true)
    }, 10)
  }

  // 表格变化
  const tablePageChange = (data) => {
    state.pageInfo.currentPage = data.currentPage
    state.pageInfo.pageSize = data.pageSize
    onSearch(false)
  }

  // 选中
  const checkSelectionChange = (e) => {
    state.checkList = e
  }

  // 操作消息
  const operateFn = (type) => {
    let ids = []
    state.checkList = tableRef.value.getSelectionRows()
    if (type === 'ALL') {
      ids = state.tableData.map((val) => val.id)
      if (ids.length > 0) {
        api.base.readMessage(ids).then((res) => {
          if (res.code === 'SUCCESS') {
            ElNotification({
              title: '提示',
              message: '当页已全部已读',
              type: 'success',
            })
            onSearch(true)
          }
        })
      }
    } else {
      if (state.checkList.length === 0) {
        ElNotification({
          title: '提示',
          message: '请先勾选列表',
          type: 'warning',
        })
        return false
      }
      ids = state.checkList.map((val) => val.id)
      if (type === 'TAB') {
        api.base.readMessage(ids).then((res) => {
          if (res.code === 'SUCCESS') {
            ElNotification({
              title: '提示',
              message: '标记已读成功',
              type: 'success',
            })
            onSearch(true)
          }
        })
      } else {
        proxy.$dialogPopup({
          title: '是否确认删除勾选消息？',
          message: '删除后，消息将不可恢复',
          save: () => {
            api.base.deleteMessage(ids).then((res) => {
              if (res.code === 'SUCCESS') {
                ElNotification({
                  title: '提示',
                  message: '删除成功',
                  type: 'success',
                })
                onSearch(true)
              }
            })
          },
        })
      }
    }
  }

  // 读取消息
  const readMessageFn = (item) => {
    let ids = []
    ids.push(item.id)
    api.base.readMessage(ids).then((res) => {
      switch (item.messageBusinessType) {
        case 'API':
          goMessageFn('audit', 'auditCenterOverview')
          break
        case 'JOB':
          goMessageFn('auditDev', 'devJobUpApproval')
          break
        case 'ASSETS':
          goMessageFn('auditRegistry', 'auditRegistry')
          break
        // case 'STANDARD':
        //   if (item.messageType === 'JOB') {
        //     goMessageFn('dataStandardManage', 'dataStandardManage')
        //   } else {
        //     goMessageFn('auditStandard', 'auditDataStandardOverview')
        //   }
        //   break
        case 'COLLECT':
          goMessageFn('dataCollection', 'DataCollection')
          break
        case 'REALTIME':
          if (item.messageType === 'JOB') {
            goMessageFn('realTimeWork', 'realTimeWork')
          } else {
            goMessageFn('realTimeOperAudit', 'realTimeOperAudit')
          }
          break
        case 'OFFLINE':
          if (item.messageType === 'JOB') {
            goMessageFn('offlineWork', 'offlineWork')
          } else {
            goMessageFn('workFlowAudit', 'workFlowAudit')
          }
          break
        case 'DATASOURC':
          goMessageFn('dataSourceManagement', 'sceneDataSourceManagement')
          break
        case 'BIZ_PROCESS':
          goMessageFn('operationFlow', 'operationFlow')
          break
        case 'BIZ_PROCESS_TASK':
          goMessageFn('offlineWork', 'offlineWork')
          break
        case 'DOCUMENT_TAG':
          goMessageFn('tagAudit', 'tagAudit')
          break
        case 'METADATA_REFRESH':
          goMessageFn('resourceDirectoryCollect', 'resourceDirectoryCollect')
          break
        // case 'QUALITY':
        //   goMessageFn('operatingLog', 'qualityManage', { id: item.bizObjectId })
        //   break
        // case 'INDICATOR':
        //   if (item.messageType === 'JOB') {
        //     goMessageFn('targetIndex', 'targetManage')
        //   } else {
        //     goMessageFn('auditTargetList', 'assetTarget')
        //   }
        //   break
        // case 'INDICATOR_ATTR':
        //   if (item.messageType === 'JOB') {
        //     goMessageFn('targetAttribute', 'targetManage')
        //   } else {
        //     goMessageFn('auditTargetattriList', 'assetTargetattri')
        //   }
        //   break
        // case 'INDICATOR_MODEL':
        //   if (item.messageType === 'JOB') {
        //     goMessageFn('modelManageList', 'targetManage')
        //   } else {
        //     goMessageFn('auditTargetsysList', 'assetTargetsys')
        //   }
        //   break
        // case 'INDICATOR_TARGET':
        //   goMessageFn('targetBoardDetail', 'indicatorView', { id: item.bizObjectId, isTable: true })
        //   break
      }
    })
  }
  // 跳转消息对应模块
  const goMessageFn = (name, code, query = {}) => {
    const obj = state.menu.find((data) => {
      return data.code === code
    })
    if (!obj) {
      ElNotification({
        title: '提示',
        message: '暂无权限',
        type: 'error',
      })
      return
    } else {
      goJump(name, query)
    }
  }
  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  // 初始化
  onMounted(() => {
    state.projectList = [...store.state.user.projectList]
    state.menu = [...store.state.user.menu]
    state.tableHeight = document.body.offsetHeight - 286
    onSearch(true)
  })
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';

  .container-padding16 {
    &-box {
      height: 100%;
      display: flex;
      overflow: hidden;
      border-radius: 4px;
    }

    &-left {
      position: relative;
      box-sizing: border-box;
      background: #fff;
      &-box {
        box-sizing: border-box;
        width: 240px;
        height: 100%;
        padding: 0 10px;
        overflow-y: auto;
        border-right: 1px solid #eeeeee;
        .parent {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 14px 10px;
          cursor: pointer;
          .left {
            font-size: 18px;
          }
          .name {
            width: calc(100% - 44px);
            margin-left: 10px;
            color: #333333;
            font-size: 14px;
          }
          .right {
            font-size: 16px;
          }
        }
        .child {
          height: 0;
          overflow: hidden;
          transition: all linear 0.3s;
          &-label {
            box-sizing: border-box;
            width: 100%;
            height: 30px;
            padding-left: 38px;
            overflow: hidden;
            color: #333333;
            font-size: 12px;
            line-height: 30px;
            white-space: nowrap;
            text-overflow: ellipsis;
            border-radius: 4px;
            cursor: pointer;
            &:hover {
              background-color: #eff1f5;
            }
            &.checked {
              background-color: #eff1f5;
            }
          }
          &.show {
            height: auto;
          }
        }
        &.hide {
          width: 1px;
          padding: 0;
        }
      }
      .knob {
        position: absolute;
        top: 0;
        right: -12px;
        bottom: 0;
        z-index: 9;
        width: 12px;
        height: 80px;
        margin: auto;
        background-color: #f0f7ff;
        border-radius: 0 9px 9px 0;
        cursor: pointer;

        &.hide {
          background-color: #ecf3ff;
          border-radius: 0 9px 9px 0;
        }

        .pic {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          margin: auto;
          color: $themeBlue;
          font-size: 12px;
          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
    }

    &-right {
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: #fff;

      .table {
        height: calc(100% - 94px);
      }
      .messageName {
        color: $themeBlue;
        cursor: pointer;
      }
      .commonForm {
        height: 48px;

        padding: 8px;
      }
      :deep(.nancalui-tabs) {
        padding: 0 8px;
        border-bottom: 1px solid var(---, #dcdfe6);

        .nancalui-tabs-nav-tab {
          height: 48px;
          .nancalui-tabs-nav-ink {
            height: 4px;
          }
        }
      }
    }
  }
</style>
