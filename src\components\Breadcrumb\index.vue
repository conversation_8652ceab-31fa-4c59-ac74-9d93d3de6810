<template>
  <div>
    <el-breadcrumb class="app-breadcrumb" separator="/">
      <transition-group name="breadcrumb">
        <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
          <span
            v-if="item.redirect === 'noRedirect' || index === levelList.length - 1"
            class="no-redirect"
            >{{ item.meta.title }}</span
          >
          <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script>
  // import pathToRegexp from 'path-to-regexp'
  import * as pathToRegexp from 'path-to-regexp'

  export default {
    data() {
      return {
        levelList: null,
      }
    },
    watch: {
      $route() {
        this.getBreadcrumb()
      },
    },
    created() {
      this.getBreadcrumb()
    },
    methods: {
      goBack() {
        this.$router.back()
      },
      getBreadcrumb() {
        if (this.$route.query.title) {
          this.$route.meta.title = this.$route.query.title
        }
        // only show routes with meta.title
        let matched = this.$route.matched.filter((item) => item.meta && item.meta.title)
        this.levelList = matched.filter(
          (item) =>
            item.meta &&
            item.meta.title &&
            item.meta.breadcrumb !== false &&
            item.meta.title !== '首页',
        )
      },
      pathCompile(path) {
        // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561
        const { params } = this.$route
        let toPath = pathToRegexp.compile(path)
        return toPath(params)
      },
      handleLink(item) {
        const { redirect, path } = item
        if (redirect) {
          this.$router.push(redirect)
          return
        }
        this.$router.push(this.pathCompile(path))
      },
    },
  }
</script>

<style lang="scss" scoped>
  .app-breadcrumb.el-breadcrumb {
    position: relative;
    //display: inline-block;
    font-size: 12px;
    line-height: 46px;
    margin-left: 16px;

    .no-redirect {
      color: #97a8be;
      cursor: text;
    }
  }

  .goBack {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
</style>
