<template>
  <div class="home-page container">
    <div class="home-page-out-box">
      <div class="home-page-box">
        <div class="home-page-box-bottom">
          <!-- <div class="svg-box"> <img src="@icons/svg/home-svg.svg" alt="" /> </div> -->

          <div class="svg-box">
            <svg
              class="bottom-bg"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="1590px"
              height="380px"
              viewBox="0 0 1590 380"
              version="1.1"
            >
              <title />
              <defs>
                <linearGradient
                  x1="101.881003%"
                  y1="82.1779522%"
                  x2="-2.03949206%"
                  y2="82.1779522%"
                  id="linearGradient-1"
                >
                  <stop stop-color="#F9FBFF" stop-opacity="0.6" offset="0%" />
                  <stop stop-color="#DFEDFD" offset="14.6144458%" />
                  <stop stop-color="#B6D2F3" offset="47.623258%" />
                  <stop stop-color="#DFEDFD" offset="81.0665165%" />
                  <stop stop-color="#F6F9FF" stop-opacity="0.60139348" offset="100%" />
                </linearGradient>
              </defs>
              <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="首页备份-5" transform="translate(63.000000, -405.000000)">
                  <g id="编组-11" transform="translate(78.000000, 96.000000)">
                    <g id="编组-27" transform="translate(-139.504779, 310.330215)">
                      <path
                        d="M0.531163905,41.2297855 L310.801301,223.392246 C313.822845,225.166222 317.552457,225.229389 320.632341,223.558751 L723.208667,5.18709541 C729.171233,1.95278822 736.366755,1.9611617 742.321778,5.20933746 L917.321427,100.663149 C922.169917,103.307765 923.956509,109.382127 921.311894,114.230617 C920.481427,115.753147 919.26927,117.033523 917.794443,117.946024 L752.245371,220.374128 C747.548793,223.279983 746.097124,229.442973 749.00298,234.139552 C749.787444,235.40744 750.845492,236.483871 752.099681,237.290058 L962.7901,372.720823 C965.987605,374.776164 970.073911,374.840981 973.334995,372.888088 L1317.50429,166.783024 C1320.6593,164.893651 1324.59675,164.888405 1327.75678,166.769363 L1586.1086,320.548927 L1586.1086,320.548927"
                        id="路径-20"
                        stroke="url(#linearGradient-1)"
                        stroke-width="6.8"
                      />
                      <path
                        d="M0,41.2297855 L310.581436,223.233546 C313.601928,225.003584 317.328128,225.065161 320.405456,223.395893 L722.677477,5.1871602 C728.640056,1.95281454 735.835612,1.96117325 741.790661,5.209363 L916.790263,100.663149 C921.638753,103.307765 923.425346,109.382127 920.78073,114.230617 C919.950263,115.753147 918.738106,117.033523 917.263279,117.946024 L751.714207,220.374128 C747.017629,223.279983 745.565961,229.442973 748.471816,234.139552 C749.25628,235.40744 750.314328,236.483871 751.568517,237.290058 L962.258936,372.720823 C965.456441,374.776164 969.542747,374.840981 972.803831,372.888088 L1317.40403,166.524975 C1320.56357,164.63289 1324.50737,164.630538 1327.66917,166.518852 L1585.57743,320.548927 L1585.57743,320.548927"
                        id="p60"
                        stroke="#FFFFFF"
                        stroke-width="0.85"
                        stroke-dasharray="2.55"
                      />
                      <g
                        id="22"
                        stroke="none"
                        stroke-width="1"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-opacity="0.245531202"
                        transform="translate(-6, -6)"
                      >
                        <g
                          id="p5"
                          transform="translate(-120.000000, -551.000000)"
                          fill="rgba(245 ,166 , 100, 1)"
                          stroke="rgba(245 ,166 , 100, 1)"
                          stroke-width="3"
                        >
                          <g id="编组-12" transform="translate(78.000000, 96.000000)">
                            <circle id="circle" cx="49.6650525" cy="462" r="5.5" />
                          </g>
                        </g>
                        <animateMotion dur="12s" id="animation" repeatCount="indefinite">
                          <mpath href="#p60" />
                        </animateMotion>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
          </div>

          <div
            class="bottom-list"
            v-for="(item, index) in state.moduleList"
            :key="index"
            :style="{
              position: 'absolute',
              left: item.left,
              top: item.top,
              width: item.width + '%',
            }"
          >
            <img :src="item.url" alt="" />
            <div
              :class="{ 'child-item': true, dif: child.class }"
              v-for="(child, index2) in item.children"
              :key="index2"
              :style="{
                position: 'absolute',
                left: child.left,
                top: child.top,
              }"
              @click.prevent="goJump(child)"
            >
              <div class="child-item-number">{{ child.number }}</div>
              <div class="child-item-name">{{ child.name }}</div>
            </div>
          </div>
        </div>
        <div class="home-page-box-top">
          <div
            class="top-item"
            v-for="(item, index) in state.topTitleData"
            :key="index"
            :style="{
              background: `linear-gradient(90deg, ${item.colorStart} 0%, ${item.colorEnd} 100%)`,
            }"
          >
            <div class="top-item-bg">
              <div class="top-item-bg-content">
                <img :src="item.url" alt="" />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  import { reactive, onMounted, toRefs, onBeforeUnmount, watch } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  import * as d3 from 'd3'
  export default {
    setup() {
      const getAssetsImages = (name) => {
        return new URL(`/src/assets/img/home/<USER>/${name}.png`, import.meta.url).href //本地文件路径
      }
      const state = reactive({
        topTitleData: [
          {
            name: '标准化采集',
            url: getAssetsImages('home-202306-img1'),
            colorStart: '#F2FEFB',
            colorEnd: '#D4F5EC',
          },
          {
            name: '价值化盘点',
            url: getAssetsImages('home-202306-img2'),
            colorStart: '#F7FBFF',
            colorEnd: '#E1F4FF',
          },
          {
            name: '场景化计算',
            url: getAssetsImages('home-202306-img3'),
            colorStart: '#F9F9FF',
            colorEnd: '#E5EAFE',
          },
          {
            name: '业务化赋能',
            url: getAssetsImages('home-202306-img4'),
            colorStart: '#FFFCF0',
            colorEnd: '#FDF1CF',
          },
        ],

        homeType: 1, //技术人员首页
        activeProjectCode: '',
        projectCode: '',
        allMenuList: [],
        currentProject: {},
        projectList: [],
        // 业务全景数据
        titleList: [
          { name: '标准化采集', icon: getAssetsImages('base-model') },
          { name: '价值化盘点', icon: getAssetsImages('base-quality') },
          { name: '场景化计算', icon: getAssetsImages('base-algorithm') },
          { name: '业务化赋能', icon: getAssetsImages('base-assets') },
        ],
        moduleList: [
          {
            name: '数据采集',
            left: '0%',
            top: '5.5%',
            width: 27,

            url: getAssetsImages('home-202306-collect'),
            children: [
              {
                name: '结构化数据采集',
                number: 0,
                left: '8%',
                top: '15.5%',
                key: 'structJobCount',
                router: 'dataCollection',
                code: 'DataCollection',
                parentCode: 'dataIntegration',
              },
              {
                name: '非结构化数据采集',
                number: 0,
                left: '65%',
                top: '13.5%',
                router: 'dataCollection',
                code: 'DataCollection',
                key: 'unstructJobCount',
                parentCode: 'dataIntegration',
              },
              {
                name: '数据源管理',
                number: 0,
                left: '38%',
                top: '57.5%',
                key: 'dataSourceCount',
                code: 'sceneDataSourceManagement',
                router: 'dataSourceManagement',
                parentCode: 'dataIntegration',
              },
            ],
          },
          {
            name: '数据盘点',
            left: '27%',
            top: '-31%',
            width: 26,

            url: getAssetsImages('home-202306-pandian'),
            children: [
              {
                name: '结构化资源',
                number: 0,
                left: '12%',
                top: '10.5%',
                class: true,
                key: 'structedDataAmount',
                code: 'resourceDirectory',
                router: 'resourceLibraryList',
                parentCode: 'assetSystem',
              },
              {
                name: '非结构化资源',
                number: 0,
                left: '68%',
                top: '48.5%',
                key: 'unstructFileCount',
                code: 'resourceDirectory',
                router: 'resourceLibraryList',
                parentCode: 'assetSystem',
              },
              {
                name: '数据模型',
                number: 0,
                left: '18%',
                top: '50.5%',
                key: 'modelCount',
                code: 'dataModal',
                router: 'dataModal',
                parentCode: 'dataIntegration',
              },
            ],
          },
          {
            name: '数据计算',
            left: '44.4%',
            top: '18%',
            width: 31,

            url: getAssetsImages('home-202306-jisuan'),
            children: [
              {
                name: '离线作业',
                number: 0,
                left: '12%',
                top: '18.5%',
                key: 'offlineJobCount',
                code: 'dataWork',
                router: 'dataWork',
                parentCode: 'dataIntegration',
              },
              {
                name: '实时作业',
                number: 0,
                left: '14%',
                top: '52.5%',
                key: 'realtimeJobCount',
                code: 'realTimeWork',
                router: 'realTimeWork',
                parentCode: 'dataIntegration',
              },
              {
                name: '算法中心',
                number: 0,
                left: '40%',
                top: '61%',
                key: 'algorithmCount',
                code: 'algorithmCenter',
                router: 'algorithmCenter',
                parentCode: 'assetSystem',
              },
              {
                name: '快速开发',
                number: 0,
                left: '68%',
                top: '49.5%',
                key: 'searchCount',
                code: 'dataScript',
                router: 'dataScript',
                parentCode: 'dataIntegration',
              },
            ],
          },
          {
            name: '数据资产',
            left: '74%',
            top: '-15%',
            width: 27,

            url: getAssetsImages('home-202306-assets'),
            children: [
              {
                name: '数据资产',
                number: 0,
                left: '6.6%',
                top: '15.5%',
                key: 'registeredModelCount',
                code: 'assetsCatalog',
                router: 'assetLibrary',
                parentCode: 'asset_manage',
              },
              {
                name: '指标资产',
                number: 0,
                left: '11%',
                top: '52.5%',
                key: 'metricModelCount',
                code: 'targetCatalog',
                router: 'targetLibraryList',
                parentCode: 'asset_manage',
              },
              {
                name: '数据质量',
                number: 0,

                left: '54%',
                top: '4.5%',
                key: 'qualityJobCount',
                code: 'qualityRule',
                router: 'DataQualityRule',
                parentCode: 'asset_manage',
              },
              {
                name: '数据服务',
                number: 0,
                left: '71%',
                top: '19.5%',
                key: 'apiCount',
                code: 'service',
                router: 'service',
                parentCode: 'service',
              },
            ],
          },
        ],
      })
      const store = useStore()
      const router = useRouter()

      const methods = {
        //svg变色
        svgChangeColor() {
          let p5 = d3.select('#p5')
          let animation = d3.select('#animation')

          let colorList = [
            {
              ratio: 0.2,
              color: [4, 196, 149],
            },
            {
              ratio: 0.4,
              color: [4, 196, 149],
            },
            {
              ratio: 0.6,
              color: [68, 125, 253],
            },
            {
              ratio: 0.82,
              color: [146, 97, 255],
            },
            {
              ratio: 1,
              color: [245, 166, 100],
            },
          ]
          function render() {
            requestAnimationFrame(render)

            const distance =
              (animation.node().getCurrentTime() % 12) / animation.node().getSimpleDuration()
            for (let i = 0; i < colorList.length; i++) {
              const item = colorList[i]
              if (distance < item.ratio) {
                p5.attr(
                  'fill',
                  `rgba(${item.color[0]} ,${item.color[1]} , ${item.color[2]}, 1)`,
                ).attr('stroke', `rgba(${item.color[0]} ,${item.color[1]} , ${item.color[2]}, 1)`)
                break
              }
            }
          }
          render()
        },

        goJump(item, needProject = true) {
          if (!item.router) {
            return false
          }
          if (!needProject || state.currentProject.projectCode) {
            const obj = state.allMenuList.find((data) => {
              return data.code === item.code
            })
            if (!obj) {
              ElNotification({
                title: '提示',
                message: '暂无权限',
                type: 'error',
              })
              return
            }
            store.commit('user/SET_FIRST_MENU', item.parentCode)
            let _router = { name: item.router }
            if (item.title) {
              _router = { name: item.router, query: { title: item.title } }
            }
            if (item.name === '结构化数据采集') {
              localStorage.activeName = 'ALL_STRUCTURE'
            }
            if (item.name === '非结构化数据采集') {
              localStorage.activeName = 'SFTP_STRUCTURE'
            }
            if (item.name === '结构化资源') {
              localStorage.resourceLibraryActiveName = 'ALL_STRUCTURE'
            }
            if (item.name === '非结构化资源') {
              localStorage.resourceLibraryActiveName = 'NOT_STRUCTURE'
            }
            router.push(_router)
          } else {
            ElNotification({
              title: '提示',
              message: '请联系管理员分配场景权限',
              type: 'error',
            })
          }
        },

        init() {
          api.base.gitHomeStatistics().then((res) => {
            let { success, data } = res
            if (success) {
              state.moduleList.forEach((item) => {
                item.children.forEach((child) => {
                  child.number = data[child.key]
                })
              })
            }
          })
        },
      }

      onMounted(() => {
        methods.svgChangeColor()
        const { menu, currentProject, homeType, projectList } = toRefs(store.state.user)
        state.projectList = projectList
        state.activeProjectCode = currentProject.value.projectCode
        state.projectCode = currentProject.value.projectCode
        state.allMenuList = menu.value
        state.currentProject = currentProject.value
        state.homeType = homeType.value
        methods.init()
      })

      return {
        state,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .home-page {
    width: 100%;
    background-color: #eee;
    font-family: PingFangSC-Semibold, PingFang SC;

    &-out-box {
      height: 100%;
      background: linear-gradient(360deg, #ffffff 0%, #f6f8fd 62%, #eff3fc 100%);
      border-radius: 8px;
      border: 2px solid #fff;
      .home-page-box {
        overflow: auto;
        background: url('@img/home/<USER>/home-202306-bg.png') no-repeat center 20%;
        background-size: 100% auto;
        // height: 794px;
        padding: 16px 16px 0;
        height: 100%;
        overflow-x: hidden;
        position: relative;
        .home-page-box-top {
          display: flex;
          justify-content: space-between;
          position: absolute;
          left: 20px;
          right: 20px;
          bottom: 20px;
          .top-item {
            // min-width: 318px;
            width: calc((100% - 48px) / 4);
            height: 90px;
            border-radius: 12px;
            // padding-left: 40px;
            display: flex;

            .top-item-bg {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              background: url(@img/home/<USER>/line.png) no-repeat;
              background-position: right 11px bottom -10px;
              background-size: auto 100%;

              .top-item-bg-content {
                display: flex;
                align-items: center;
                height: 100%;
                margin: 0 auto;
              }
            }
            img {
              width: 90px;
              height: 78px;
            }
            span {
              font-size: 18px;
              font-weight: bolder;
              color: #1a2233;
              margin-left: 20px;
            }
          }
        }
        .home-page-box-bottom {
          position: relative;
          width: 100%;
          margin-top: 170px;
          font-size: 0;

          .svg-box {
            width: 100%;
            overflow: hidden;
            .bottom-bg {
              width: 110%;
              height: auto;
              margin-left: -5%;
              max-width: inherit;
            }
          }

          .bottom-list {
            position: absolute;
            font-size: 12px;
            .child-item {
              background: rgba(255, 255, 255, 0.8);
              border-radius: 4px;
              backdrop-filter: blur(1px);
              padding: 4px 6px;
              width: max-content;
              // width: 30%;
              cursor: pointer;
              &.dif {
                min-width: 100px;
              }
              &:hover {
                .child-item-name {
                  color: $themeBlue;
                }
              }
              .child-item-number {
                font-size: 20px;
                color: $themeBlue;
                text-align: center;
                font-weight: bolder;
                text-shadow: 0px 1px 0px #ffffff;
              }
              .child-item-name {
                font-size: 12px;
                text-align: center;
                font-weight: 500;
                color: #2d497a;
              }
            }
          }
          // background: url('@icons/svg/home-svg.svg') no-repeat center top;
          // background-size: 100% auto;
        }
      }
    }
  }
  @media only screen and (min-height: 900px) {
    .home-page-out-box .home-page-box .home-page-box-bottom {
      margin-top: 210px;
    }
  }
</style>
