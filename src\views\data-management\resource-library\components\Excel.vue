<template>
  <div v-loading="state.loading" class="pdf-content excel_4567378218732423874">
    <VueOfficeDocx
      v-if="state.showExcel === 'office'"
      :src="props.option.value"
      :options="state.setting"
      style="height: 100%; width: 100%"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <div v-if="state.showExcel === 'lucky'" id="luckyExcel" class="luckyExcel"></div>
  </div>
</template>

<script setup>
  import VueOfficeDocx from '@vue-office/excel'
  import '@vue-office/excel/index.css'
  import * as XLSX from 'xlsx'
  const props = defineProps({
    option: {
      type: Object,
      default() {
        return {}
      },
    },
  })
  const { option } = toRefs(props)
  const pdfcom = ref(null)
  const state = reactive({
    loading: false,
    showExcel: null,
    pagination: {
      currentPage: 1,
      pageSize: 1,
      total: 0,
    },
    setting: {
      transformData: function (workbookData) {
        workbookData.forEach((val) => {
          for (let j in val.cols) {
            if (val.cols[j].width) {
              val.cols[j].width += 10
            }
          }
          for (let i in val.rows) {
            if (val.rows[i].height) {
              val.rows[i].height += 10
            }
          }
        })
        return workbookData
      },
    },
  })

  const renderedHandler = () => {
    console.log('渲染完成')
    state.loading = false
  }
  const errorHandler = (error) => {
    console.log('渲染失败', error)
    state.loading = false
  }

  const loadRemoteExcel = async (url) => {
    try {
      // 1. 获取远程文件
      const response = await fetch(url)
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)

      // 2. 转换为ArrayBuffer
      const arrayBuffer = await response.arrayBuffer()
      const data = new Uint8Array(arrayBuffer)

      // 3. 使用xlsx解析
      const workbook = XLSX.read(data, { type: 'array' })

      // // 4. 转换为LuckyExcel格式
      let sheetData = []
      workbook.SheetNames.forEach((val, ind) => {
        let newContainer = document.createElement('div')
        newContainer.innerHTML = XLSX.utils.sheet_to_html(workbook.Sheets[workbook.SheetNames[ind]])
        let newData = parseHtmlTable(newContainer.querySelector('table'))
        sheetData.push({
          celldata: newData,
          name: workbook.SheetNames[ind],
          index: ind,
        })
      })

      // 5. 初始化Luckysheet
      nextTick(() => {
        window.luckysheet.create({
          container: 'luckyExcel',
          data: sheetData,
          virtualScroll: true, // 启用虚拟滚动
          allowEdit: true, // 禁用编辑
          showtoolbar: false, // 隐藏工具栏
          showinfobar: false, // 隐藏标题栏
          showsheetbar: true, // 隐藏工作表标签
          showstatisticBar: false, // 隐藏状态栏
          sheetFormulaBar: false, // 隐藏公式栏
          // 高级防护
          cellRightClickConfig: {}, // 禁用右键菜单
          hook: {
            workbookCreateAfter: function () {
              state.loading = false
            },
            // sheetActivate: (index) => {
            //   nextTick(() => {
            //     let newContainer = document.createElement('div')
            //     newContainer.innerHTML = XLSX.utils.sheet_to_html(
            //       workbook.Sheets[workbook.SheetNames[index]],
            //     )
            //     let newData = parseHtmlTable(newContainer.querySelector('table'))
            //     window.luckysheet.setRangeValue({
            //       sheetIndex: index,
            //       range: 'A1',
            //       value: newData,
            //     })
            //   })
            // },
          },
        })
      })
    } catch (e) {
      console.error('加载失败:', e)
    } finally {
      state.loading = false
    }
  }

  // 表格HTML转Luckysheet数据格式
  function parseHtmlTable(table) {
    const celldata = []
    const rows = table.querySelectorAll('tr')
    rows.forEach((row, r) => {
      const cells = row.querySelectorAll('th, td')
      cells.forEach((cell, c) => {
        celldata.push({
          r: r,
          c: c,
          v: cell.textContent.trim(),
        })
      })
    })
    return celldata
  }

  // 获取文档大小
  const getFileSize = async (url) => {
    const response = await fetch(url, { method: 'HEAD' })
    return response.headers.get('Content-Length')
  }

  onMounted(() => {
    state.loading = true
    getFileSize(props.option.value).then((resp) => {
      if (resp / 1024 / 1000 > 12) {
        state.showExcel = 'lucky'
        loadRemoteExcel(props.option.value)
      } else {
        state.showExcel = 'office'
      }
    })
  })

  onUnmounted(() => {
    window.luckysheet?.destroy()
  })
</script>
<style lang="scss" scoped>
  .pdf-content {
    position: relative;
    display: flex;
    flex: 1 0 0;
    align-self: stretch;
    justify-content: center;
    height: 100%;
    overflow: hidden;
    background: #f5f6f7;
    border-radius: 6px;
    user-select: text;
    .page-btn {
      width: 0;
      height: 0;
      > div {
        position: absolute;
        top: 50%;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: var(--100, #fff);
        border: 1px solid var(---, #a3b4db);
        border-radius: 27px;
        box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);
        transform: translateY(-50%);
        cursor: pointer;
        &:hover {
          background: #e3ecff;
          border: 1px solid #c5d0ea;
          box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);
        }
        &:last-child {
          right: 16px;
        }
        &:first-child {
          left: 16px;
        }
      }
    }
    .luckyExcel {
      width: 100%;
      height: 100%;
      :deep(.luckysheet) {
        #luckysheet-cell-selected,
        #luckysheet-sheets-add,
        #luckysheet-sheets-m {
          display: none !important;
        }
      }
    }
  }
  .pagination-content {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: flex-end;
    padding: 24px 0 8px 0;

    &-total {
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;

      span {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  :deep(.x-spreadsheet-selector-area) {
    display: none !important;
  }
  :deep(.el-loading-mask) {
    .el-loading-spinner {
      .circular {
        width: 48px !important;
        height: 48px !important;
      }
    }
  }
</style>
<style lang="scss">
  body:has(.excel_4567378218732423874) {
    #luckysheet-input-box {
      z-index: -999;
    }
  }
</style>
