<template>
  <!-- 配置校验信息 -->
  <div class="data-collection-configure-scheduling">
    <div class="box-bottom">
      <div class="bottom-outBox scroll-bar-style">
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="126px"
          label-align="end"
          class="configure-ruleForm disabled-form"
          :key="state.key"
        >
          <n-form-item v-if="!state.showMore" label="调度策略：" class="collect-rule">
            <div class="form-detail-text">手动调度</div>
          </n-form-item>
          <n-form-item
            v-if="state.showMore && showTaskRule"
            label="数据库类型："
            class="collect-rule"
          >
            <div class="form-detail-text">{{ state.ruleForm.dataSourceType }}</div>
          </n-form-item>
          <n-form-item v-if="state.showMore && showTaskRule" label="">
            <div class="form-detail-text"></div>
          </n-form-item>
          <n-form-item
            v-if="state.showMore && showTaskRule"
            label="采集规则："
            class="collect-rule"
          >
            <div class="form-detail-text">{{
              state.ruleForm.collectWay === 'FULL' ? '全量采集' : '增量采集'
            }}</div>
          </n-form-item>
          <!-- <n-form-item v-if="state.showMore" label="任务执行时间：">
            <div class="form-detail-text">{{ state.ruleForm.startTime }} </div>
          </n-form-item> -->
          <n-form-item v-if="state.showMore" label="采集频次：">
            <div class="form-detail-text" v-if="state.ruleForm.cron">
              <div class="form-detail-text">cron表达式： {{ state.ruleForm.cron }}</div>
              <!-- <n-input
                v-model="state.ruleForm.cron"
                size="small"
                placeholder="请输入corn表达式"
                clearable
              /> -->
            </div>
            <div class="form-detail-text" v-else>
              {{ changeChinese(state.ruleForm) }}
            </div>
          </n-form-item>

          <n-form-item v-if="state.showMore" label="重试次数：">
            <div class="form-detail-text">{{ state.ruleForm.retryCount }}次 </div>
          </n-form-item>

          <n-form-item v-if="state.showMore" label="重试间隔：">
            <div class="form-detail-text">{{ state.ruleForm.retryTime }}分 </div>
          </n-form-item>
          <n-form-item
            v-if="state.showMore && state.ruleForm.fromDateTime"
            class="configure-effectiveDate"
            label="生效时间："
          >
            <div class="form-detail-text"
              >{{ state.ruleForm.fromDateTime }} - {{ state.ruleForm.thruDateTime }}</div
            >
          </n-form-item>
        </n-form>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'
  import { formartTime } from '@/utils/index'
  export default {
    name: '',
    components: {},
    props: {
      showTaskRule: {
        type: Boolean,
        default: true,
      },
    },
    setup(props) {
      const state = reactive({
        showMore: false,
        key: 1,

        ruleForm: {
          dataSourceType: '',
          startTime: '',
          cron: '',
          collectRule: 'PERIODIC_FULL',
          collectWay: 'FULL',
          taskType: true,
          configureType: 'auto',
          configureTimes: 'time',
          rerun: true,
          executionData: '',
          executionTime: '',
          period: 'hour',
          rateTime: '',
          extent: '',
          retryCount: 0,
          retryTime: 0,
          hour: '',
          scheduleId: null, //调度id
          fromDateTime: '', //有效区间开始时间
          thruDateTime: '', //有效区间结束时间
          effectiveDate: [], //有效时间区间
        },
        rules: {
          cron: { required: true, message: '请输入cron表达式', trigger: 'blur' },
          configureTimes: { required: true, message: '请选择采集频率', trigger: 'change' },
          collectRule: { required: true, message: '请选择采集规则', trigger: 'change' },
          executionData: {
            required: true,
            message: '请选择执行日期',
            trigger: 'change',
          },
          executionTime: { required: true, message: '请输入执行时间', trigger: 'change' },
          period: { required: true, message: '请选择采集频率周期', trigger: 'change' },
          extent: { required: true, message: '请选择或者输入', trigger: 'blur' },

          rateTime: { required: true, message: '请选择采集频率时间', trigger: 'change' },
          hour: { required: true, message: '请输入间隔小时数', trigger: 'blur' },
          // retryCount: { required: true, message: '请设置重试次数', trigger: 'blur' },
          // retryTime: { required: true, message: '请设置重试间隔', trigger: 'blur' },},
        },
        ruleOptions: [
          {
            label: '全量采集',
            value: 'FULL',
          },
          // {
          //   label: '增量采集',
          //   value: 'INCREMENT',
          // },
        ],
        sourceTableData: [],
        authorizedTableData: [],
        allData: {
          name: '',
          description: '',
          createTime: '',
          createByName: '',
          dataSourcePage: [],
          authorizedPage: [],
        },
        queryData: '',
        editId: null, // 修改模式id
        periodOptions: [
          {
            label: '小时',
            value: 'hour',
          },
          {
            label: '日',
            value: 'day',
          },
          {
            label: '周',
            value: 'week',
          },
          {
            label: '月',
            value: 'month',
          },
        ],
        weekList: [
          { label: '周一', value: '1' },
          { label: '周二', value: '2' },
          { label: '周三', value: '3' },
          { label: '周四', value: '4' },
          { label: '周五', value: '5' },
          { label: '周六', value: '6' },
          { label: '周日', value: '7' },
        ],
        monthList: [],
        totalData: {},
      })
      const router = useRouter()
      const ruleForm = ref()
      const disabledDate = (time) => {
        return time.getTime() < Date.now() - 8.64e7
      }
      const methods = {
        changeChinese(data, rateTime = false) {
          let words = ''
          let rateTime_words = '未配置'
          switch (data.period) {
            case 'HOUR':
              words = `间隔${data.extent}小时`

              rateTime_words = data.fromDateTime?.slice(11) + '  +  ' + data.extent + 'h'
              break
            case 'DAY':
              words = `每日${data.rateTime}`
              rateTime_words = data.rateTime
              break
            case 'WEEK':
              let arr = data?.extent?.split(',') || []
              arr = arr.map((v) => {
                v = methods.weekForChinese(Number(v))
                return v
              })
              words = `每周${arr.toString()} | ${data?.rateTime}`
              rateTime_words = data.rateTime
              break
            case 'MONTH':
              words = `每月${data.extent}号 | ${data?.rateTime}`
              rateTime_words = data.rateTime
              break
          }
          return rateTime ? rateTime_words : words
        },
        weekForChinese(num) {
          let word = '一'
          switch (num) {
            case 1:
              word = `一`
              break
            case 2:
              word = `二`
              break
            case 3:
              word = `三`
              break
            case 4:
              word = `四`
              break
            case 5:
              word = `五`
              break
            case 6:
              word = `六`
              break
            case 7:
              word = `日`
              break
          }
          return word
        },
        fieldBlur(key) {
          state.ruleForm[key] = state.ruleForm[key].replace(/\D/g, '')
        },
        // 点击全选input内容
        inputFocus(e) {
          e.target.select()
        },

        onKeydown(e) {
          let key = e.key
          let number = '1234567890'
          if (number.includes(key) || key === 'Backspace') {
            if (state.form.tokenExpireIncrementDays.toString().length === 1 && key === '0') {
              e.returnValue = false
            } else {
              e.returnValue = true
            }
          } else {
            e.returnValue = false
          }
        },
        //调度开始日期change
        startDateChange(e) {
          state.ruleForm.executionData = methods.formartTime(e)
        },
        formartTime(time) {
          var year = time.getFullYear()
          var mon = time.getMonth() + 1
          var day = time.getDate()
          var submitTime = ''
          submitTime += year + '/'
          if (mon >= 10) {
            submitTime += mon + '/'
          } else {
            submitTime += '0' + mon + '/'
          }
          if (day >= 10) {
            submitTime += day
          } else {
            submitTime += '0' + day
          }

          return submitTime
        },
        //周月日调度频率change
        periodChange() {
          state.ruleForm.extent = ''
        },
        // 正整数值校验
        checkingValue(value) {
          let _value = String(state.ruleForm[value])
          if (_value.includes('-') || !_value) {
            state.ruleForm[value] = 0
          }
        },
        // 调度开关改变
        switchChange(on) {
          if (on) {
            methods.changeRulesStatus()
          } else {
            methods.changeRulesStatus(false)
            state.ruleForm.rerun = false
          }
        },
        // 重启机制开关
        rerunSwitchChange(on) {
          if (!on) {
            state.ruleForm.rerun = false
          }
        },
        // 初始化数据
        init(data) {
          let {
            name,
            description,
            dataSourceType,
            schedule,
            collectRule,
            retryTime,
            retryCount,
            collectWay,
          } = data
          state.totalData = data
          state.allData.name = name
          state.allData.description = description
          state.ruleForm.dataSourceType = dataSourceType
          state.ruleForm.collectWay = collectWay
          if (schedule?.schedulingStrategy === 'AUTO') {
            state.showMore = true
            Object.keys(state.ruleForm).forEach((key) => {
              if (schedule[key]) {
                state.ruleForm[key] = schedule[key]
              } else {
                state.ruleForm[key] = data[key]
              }
            })
            // let time = schedule.startTime.replace(/\-/g, '/')
            // time = time.split(' ')
            // state.ruleForm.executionData = time[0]
            // state.ruleForm.executionTime = time[1]
            state.ruleForm.period = schedule.period
            state.ruleForm.extent = schedule.extent || null
            state.ruleForm.rateTime = schedule.rateTime
            state.ruleForm.retryCount = retryCount
            state.ruleForm.retryTime = retryTime
            state.ruleForm.scheduleId = schedule.id
            if (schedule.periodicDeclarationMode === 'CRON_EXPRESSION') {
              state.ruleForm.configureTimes = 'cron'
            } else {
              state.ruleForm.configureTimes = 'time'
            }
          } else {
            state.showMore = false
            state.ruleForm.configureType = 'hander'
            state.ruleForm.taskType = false
            state.ruleForm.rerun = false
          }
          setTimeout(() => {
            state.ruleForm.collectRule = collectRule
            state.key++
          }, 60)
        },

        // 回传数据
        async getAllData() {
          let passed = false
          // let time = ''

          if (state.ruleForm.configureType === 'auto') {
            let _result = await new Promise((resolve) => {
              ruleForm.value.validate((valid) => {
                resolve({ passed: valid })
              })
            })
            if (!_result.passed) {
              return _result
            }
            passed = true

            // time = state.ruleForm.executionData.slice(0, 10) + ' ' + state.ruleForm.executionTime
            // time = time.replace(/\//g, '-')

            state.totalData.schedule = {
              ruleForm: state.ruleForm,
              // startTime: time,
              period: state.ruleForm.cron ? '' : state.ruleForm.period, // 周期  day, week, month, interval
              rateTime: state.ruleForm.rateTime || '',
              cron: state.ruleForm.cron, // 啥意思
              extent: state.ruleForm.extent || '',
              fromDateTime: state.ruleForm.effectiveDate[0]
                ? formartTime(state.ruleForm.effectiveDate[0])
                : null, //有效期开始时间
              thruDateTime: state.ruleForm.effectiveDate[1]
                ? formartTime(state.ruleForm.effectiveDate[1], true)
                : null, //有效期结束时间
            }

            if (state.editId) {
              state.totalData.schedule.id = state.ruleForm.scheduleId
            }
            state.totalData.dataSourceType = state.ruleForm.dataSourceType
            state.totalData.retryCount = state.ruleForm.rerun ? state.ruleForm.retryCount : 0
            state.totalData.retryTime = state.ruleForm.rerun ? state.ruleForm.retryTime : 0
          } else {
            passed = true
            state.totalData.schedule = null
            state.totalData.retryCount = 0
            state.totalData.retryTime = 0
          }
          state.totalData.collectRule = state.ruleForm.collectRule
          state.totalData.collectWay = state.ruleForm.collectWay

          let _result2 = await new Promise((resolve) => {
            ruleForm.value.validate(() => {
              resolve({ passed, data: state.totalData })
            })
          })
          return _result2
        },
        changeRulesStatus(required = true) {
          Object.keys(state.rules).forEach((key) => {
            if (key !== 'collectRule') {
              state.rules[key].required = required
            }
          })
        },
      }
      watch(
        () => props.dataSourceType,
        () => {
          // 普通的watch监听
          state.ruleForm.collectRule = 'PERIODIC_FULL'
        },
      )
      onMounted(() => {
        state.queryData = router.currentRoute.value.query // 获取路由传参
        state.editId = state.queryData.editId

        let monthList = []
        for (let i = 1; i <= 31; i++) {
          monthList.push({
            label: i + '号',
            value: i.toString(),
          })
        }
        state.monthList = monthList
      })

      return {
        state,
        ruleForm,
        disabledDate,
        onKeydownPositiveInteger,
        onKeyupPositiveInteger,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $contentBoxBg: #ffffff;
  $border: #e1e1e1;
  $font: #333333;
  $spanBg: #f4f4f4;
  $outBg: #eeeeee;

  .data-collection-configure-scheduling {
    background-color: $outBg;
    :deep(.nancalui-radio-group) {
      .nancalui-radio__label {
        color: #333;
        font-weight: 400;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }

    .box-bottom {
      height: 100%;
      padding-bottom: 20px;
      background-color: $contentBoxBg;
      .nancalui-form {
        display: flex;
        flex-wrap: wrap;
        .form-detail-text {
          color: #000000;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
        }
        .nancalui-form__item--horizontal {
          width: 40%;
          margin-bottom: -2px;
        }
      }
    }
  }
</style>
