<template>
  <!-- 文件预览 -->
  <div class="file-view-dialog-box">
    <n-modal
      class="file-view-dialog"
      v-model="state.dialogVisible"
      :draggable="false"
      width="70%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="数据预览"
    >
      <div class="content">
        <div v-if="state.fileType === 'IMAGE'" class="img-box">
          <img :src="state.imgUrl" alt="" />
        </div>
        <div v-if="state.fileType === 'EXCEL' || state.fileType === 'CSV'" class="excel-box">
          <div class="excel-box-content">
            <div
              v-show="state.activeName === item.name"
              v-for="(item, index) in state.excelSheet"
              :key="index"
              v-html="item.innerHTML"
            ></div>
          </div>
          <div class="excel-box-tabs">
            <div
              :class="'excel-box-tabs-label ' + (state.activeName === item.name ? 'checked' : '')"
              v-for="(item, index) in state.excelSheet"
              :key="index"
              @click.prevent="tabsFn(item.name)"
              >{{ item.name }}</div
            >
          </div>
        </div>
        <div v-if="state.fileType === 'PDF'" class="pdf-box">
          <div>
            <n-button
              class="del-button"
              variant="text"
              :disabled="state.pdfPageNub === 1"
              @click.stop.prevent="pdfHandler('prev')"
            >
              上一页
            </n-button>
            <n-button
              class="del-button"
              variant="text"
              :disabled="state.pdfScale >= state.pdfMaximum"
              @click.stop.prevent="pdfHandler('scaleBig')"
              >放大
            </n-button>
            <n-button
              class="del-button"
              variant="text"
              :disabled="state.pdfScale <= state.pdfMinimum"
              @click.stop.prevent="pdfHandler('scaleSmall')"
              >缩小
            </n-button>
            <n-button
              class="del-button"
              variant="text"
              :disabled="state.pdfPageNub === state.pdfAllPage"
              @click.stop.prevent="pdfHandler('next')"
            >
              下一页
            </n-button>
          </div>
          <div class="pdf-content-box">
            <canvas id="the_canvas" class="canvas"></canvas>
          </div>
        </div>
        <div v-if="state.fileType === 'WORD'" class="word-box">
          <div id="docBox"></div>
        </div>
        <!-- <div class="my-iframe-box"> //在线预览方式
               <iframe
              class="my-iframe"
              :src="'http://view.xdocin.com/xdoc?_xdoc=' + state.fileUrl"
              frameborder="0"
            ></iframe>
          </div> -->
      </div>

      <template #footer>
        <n-modal-footer class="dialog-footer cenetr-footer">
          <n-button color="primary" variant="solid" @click.prevent="closeDialog">取 消</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import { ElNotification } from 'element-plus'
  import { renderAsync } from 'docx-preview'
  import * as PDFJS from 'pdfjs-dist/legacy/build/pdf'
  import workerSrc from 'pdfjs-dist/legacy/build/pdf.worker.entry.js'
  import * as XLSX from 'xlsx'
  import cptable from 'xlsx/dist/cpexcel.js'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const state = reactive({
        dialogVisible: false,
        blob: '',
        fileType: '',
        excelSheet: [],
        activeName: 'Sheet1',
        imgUrl: '',
        pdfPageNub: 1,
        pdfAllPage: 1,
        pdfScale: 1,
        fileFormat: '',
        pdfData: '',
        pdfMaximum: 3, //pdf最大放大倍数
        pdfMinimum: 0.6, //pdf最小缩放倍数
        pdfZoom: 0.1, //放大缩小倍数
      })
      const dataViewDialog = ref()

      const methods = {
        tabsFn(name) {
          state.activeName = name
        },
        pdfHandler(type) {
          switch (type) {
            case 'prev':
              methods.setPdfPrevPage()
              break
            case 'scaleBig':
              methods.setPdfZoomin()
              break
            case 'scaleSmall':
              methods.setPdfZoomout()
              break
            case 'next':
              methods.setPdfNextPage()
              break
            default:
              break
          }
        },

        // 数据预览
        init(data) {
          let { blob, fileType, fileFormat } = data
          state.blob = blob
          state.fileType = fileType
          state.fileFormat = fileFormat
          if (fileFormat !== 'DOC') {
            state.dialogVisible = true
          }

          let reader = new FileReader()
          reader.readAsArrayBuffer(blob, 'utf-8') //blob转ArrayBuffer数据类型
          reader.onload = function () {
            if (fileType === 'EXCEL' || fileType === 'CSV') {
              methods.previewExcel(reader.result)
            }
            if (fileType === 'PDF') {
              methods.previewPdf(reader.result)
            }
            if (fileType === 'WORD') {
              if (fileFormat === 'DOCX') {
                methods.previewDocx(reader.result)
              } else {
                ElNotification({
                  title: '提示',
                  message: '暂不支持doc格式文件预览,可下载预览!',
                  type: 'warning',
                })
              }
            }
            if (fileType === 'IMAGE') {
              state.imgUrl = URL.createObjectURL(blob)
            }
          }

          // methods.previewDoc(fileUrl)
          // methods.renderPdf(fileUrl)
          // methods.previewExcel(fileUrl)
        },
        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
        },
        //docx格式预览
        previewDocx(buffer) {
          let _options = {
            className: 'docx', // 默认和非结构化数据样式类的类名/前缀
            inWrapper: true, // 启用围绕非结构化数据内容渲染包装器
            ignoreWidth: false, // 禁止页面渲染宽度
            ignoreHeight: false, // 禁止页面渲染高度
            ignoreFonts: false, // 禁止字体渲染
            breakPages: true, // 在分页符上启用分页
            ignoreLastRenderedPageBreak: true, //禁用lastRenderedPageBreak元素的分页
            experimental: false, //启用实验性功能（制表符停止计算）
            trimXmlDeclaration: true, //如果为真，xml声明将在解析之前从xml非结构化数据中删除
            debug: false, // 启用额外的日志记录
          }

          renderAsync(buffer, document.getElementById('docBox'), null, _options)
        },
        previewPdf(data) {
          state.pdfData = data
          // PDFJS.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/legacy/build/pdf.worker.entry.js')
          // data是一个ArrayBuffer格式，也是一个buffer流的数据
          PDFJS.GlobalWorkerOptions.workerSrc = workerSrc
          PDFJS.getDocument(data).promise.then((pdfDoc) => {
            state.pdfAllPage = pdfDoc.numPages // pdf的总页数
            // 获取第1页的数据
            pdfDoc.getPage(state.pdfPageNub).then((page) => {
              // 设置canvas相关的属性
              const canvas = document.getElementById('the_canvas')
              const ctx = canvas.getContext('2d')
              const dpr = window.devicePixelRatio || 1
              const bsr =
                ctx.webkitBackingStorePixelRatio ||
                ctx.mozBackingStorePixelRatio ||
                ctx.msBackingStorePixelRatio ||
                ctx.oBackingStorePixelRatio ||
                ctx.backingStorePixelRatio ||
                1
              const ratio = dpr / bsr
              const viewport = page.getViewport({ scale: state.pdfScale })
              canvas.width = viewport.width * ratio
              canvas.height = viewport.height * ratio
              canvas.style.width = viewport.width + 'px'
              canvas.style.height = viewport.height + 'px'
              ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
              const renderContext = {
                canvasContext: ctx,
                viewport: viewport,
              }
              // 数据渲染到canvas画布上
              page.render(renderContext)
            })
          })
        },
        //pdf上一张
        setPdfPrevPage() {
          if (state.pdfPageNub > 1) {
            state.pdfPageNub--
            methods.previewPdf(state.pdfData)
          }
        },
        //pdf下一张
        setPdfNextPage() {
          if (state.pdfPageNub <= state.pdfAllPage) {
            state.pdfPageNub++
            methods.previewPdf(state.pdfData)
          }
        },
        // pdf放大
        setPdfZoomin() {
          if (state.pdfScale >= state.pdfMaximum) {
            return
          }
          state.pdfScale = state.pdfScale + state.pdfZoom
          methods.previewPdf(state.pdfData)
        },
        // pdf缩小
        setPdfZoomout() {
          if (state.pdfScale <= state.pdfMinimum) {
            return
          }
          state.pdfScale = state.pdfScale - state.pdfZoom
          methods.previewPdf(state.pdfData)
        },
        previewExcel(buffer) {
          // const data = new Uint8Array(buffer)
          // fileType
          let workbook = null
          if (state.fileType === 'CSV') {
            let _buffer = cptable.utils.decode(936, new Uint8Array(buffer))
            workbook = XLSX.read(_buffer, {
              type: 'string',
              // codepage: '936', //解决csv中文乱码
            })
          } else {
            workbook = XLSX.read(buffer, {
              type: 'array',
            })
          }

          //    FileReader共有4种读取方法：
          // 1.readAsArrayBuffer(file)：将文件读取为ArrayBuffer。
          // 2.readAsBinaryString(file)：将文件读取为二进制字符串
          // 3.readAsDataURL(file)：将文件读取为Data URL
          // 4.readAsText(file, [encoding])：将文件读取为文本，encoding缺省值为'UTF-8'

          // 删除掉没有数据的sheet

          Object.values(workbook.Sheets).forEach((sheet, index) => {
            if (Object.keys(sheet).indexOf('!ref') === -1) {
              delete workbook.Sheets[workbook.SheetNames[index]]
            }
          })
          methods.tableToHtml(workbook)
        },
        //Excel数据转换成table
        tableToHtml(workbook) {
          state.activeName = 'Sheet1'
          state.excelSheet = []
          // const sheetList = workbook.SheetNames.filter((v) => v.indexOf('数据源') === -1)
          const sheetList = workbook.SheetNames

          state.activeName = sheetList[0]
          sheetList.forEach((sheet) => {
            const worksheet = workbook.Sheets[sheet]
            let x = [
              'A',
              'B',
              'C',
              'D',
              'E',
              'F',
              'G',
              'H',
              'H',
              'I',
              'J',
              'K',
              'L',
              'M',
              'N',
              'O',
              'P',
              'Q',
              'R',
              'S',
              'T',
              'U',
              'V',
              'W',
              'X',
              'Y',
            ]
            let maxShowLine = 500 //excel产品规定的最大展示条数
            let y = []
            for (let i = 0; i <= maxShowLine + 1; i++) {
              console.log(worksheet['A' + i])
              if (worksheet['A' + (i + 1)]) {
                y[i] = i + 1
              } else {
                break
              }
            }
            let _worksheet = {}
            y.forEach((item) => {
              x.forEach((item2) => {
                _worksheet[item2 + item] = { v: '', t: 's', w: '' }
              })
            })
            if (worksheet) {
              _worksheet = Object.assign(_worksheet, worksheet)
            }
            _worksheet['!ref'] = 'A1:' + x[x.length - 1] + [y.length - 1]
            // else {
            //   state.excelSheet.push({
            //     name: sheet,
            //     innerHTML: '暂无数据',
            //   })
            // }
            const innerHTML = XLSX.utils.sheet_to_html(_worksheet)

            state.excelSheet.push({
              name: sheet,
              innerHTML: innerHTML,
            })
          })
        },
        closeDialog() {
          // 重置数据
          state.dialogVisible = false
          state.blob = ''
          state.fileType = ''
          state.activeName = 'Sheet1'
          state.imgUrl = ''
          state.fileFormat = ''
          state.pdfData = ''
          state.pdfScale = 1
          state.pdfAllPage = 1
          state.pdfPageNub = 1
          state.excelSheet = []
        },
      }

      return {
        state,
        dataViewDialog,
        ...methods,
      }
    },
  }
</script>
<style lang="scss">
  @import '@/styles/variables.scss';

  .file-view-dialog {
    //height: calc(100% - 281px);
    position: absolute;
    top: 50%;
    margin-top: 0 !important;
    transform: translate(0, -50%);
    .nancalui-modal__body {
      height: calc(100% - 115px);
      padding: 0 30px 0 !important;
    }
    .content {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .img-box {
        width: 100%;
        height: 100%;
        overflow: auto;
        img {
          width: 100%;
        }
      }
      .pdf-box {
        width: 100%;
        height: 100%;
        overflow: auto;

        .pdf-content-box {
          width: 100%;
          height: calc(100% - 20px);
          overflow: auto;
        }
      }
      .word-box {
        width: 100%;
        height: 100%;
        overflow: auto;
        //      #docBox {
        //   width: 100%;
        //   height: 100%;
        // }
        :deep(.docx-wrapper) {
          background: none !important;
          .docx {
            padding: 0;
            box-shadow: none;
          }
        }
      }

      .excel-box {
        width: 100%;
        height: 580px;
        position: relative;
        box-sizing: border-box;
        &-content {
          width: 100%;
          height: 550px;
          overflow: auto;
        }
        &-tabs {
          display: flex;
          width: 100%;
          justify-content: flex-start;
          align-items: center;
          &-label {
            color: #333;
            font-size: 14px;
            line-height: 30px;
            margin-right: 10px;
            &.checked {
              color: var(--themeBlue);
              font-weight: bold;
            }
          }
        }
        table {
          display: inline-block;
          margin: 0;
          border-collapse: collapse; //border-collapse:collapse;用来消除单元格间距。
        }
        tr {
          // display: flex;
          height: 50px;
          padding: 4px 0;
          line-height: 42px;
          text-align: center;
          td {
            border: 1px solid #ebebeb;
          }
        }
        tr:nth-of-type(1) {
          height: 42px;
          padding: 4px 0;
          line-height: 34px;
          text-align: center;
          background-color: #f7f8fa;
          td {
            min-width: 100px;
            white-space: nowrap;
            padding: auto;
            border: none;
            background-color: #f7f8fa;
          }
        }
      }

      // .my-iframe,
      // .my-iframe-box {
      //   width: 100%;
      //   height: 100%;
      // }
    }
  }
</style>
