import { createRouter, createWebHashHistory, onBeforeRouteLeave } from 'vue-router'
import { createApp } from 'vue'
import App from './index.vue'
const router = createRouter({
  history: createWebHashHistory(),
  routes: [],
})
import '@/styles/index.scss' // global css
//改为element-plus默认UI
import ElementPlus, { ElMessage } from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import JsonViewer from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'
import PublicTable from '@/components/publicTable/index.vue'

// import the styles
import 'vue3-treeselect/dist/vue3-treeselect.css'
import Treeselect from 'vue3-treeselect'

// nancal-ui
import nancalui from '@/vue-nancalui/vue-nancalui.es'
import '@/vue-nancalui/style.css'
import '@devui-design/icons/icomoon/devui-icon.css'
import { ThemeServiceInit } from '@/nancalui-theme'
import { infinityTheme } from '@/nancalui-theme/index.es'
import DataManager from '@/components/DataManage/index.vue'
ThemeServiceInit({ infinityTheme }, 'infinityTheme')
// 导入构造函数
import { createPinia } from 'pinia'
// 实例化 Pinia
const pinia = createPinia()

import directves from '@/utils/directves'

// 支持SVG
// import './icon/iconfont.js'
import SvgIcon from '@comp/SvgIcon/index.vue'
// 支持SVG
import 'virtual:svg-icons-register'
// import { polyfill } from '@/utils/index.js' // 无该方法
// polyfill()
// 创建vue实例
let app: any = null
app = createApp(App)

// 添加 Array.at() 的 Polyfill
if (!Array.prototype.at) {
  Array.prototype.at = function (index) {
    // 处理负数索引
    const relativeIndex = index < 0 ? this.length + index : index;
    return this[relativeIndex];
  };
}


// 全局过滤保留两位小数
app.config.globalProperties.$filters = {
  RetainDecimals(value) {
    if (!value) return '0'
    return parseFloat((Math.round(value * Math.pow(10, 2)) / Math.pow(10, 2)).toFixed(2))
  },
}

// 全局二次确认弹框
import dialogPopup from '@/components/PublicDialog/index.js'

app.use(dialogPopup)
app.component('SvgIcon', SvgIcon)
app.component('PublicTable', PublicTable)
app.component('Treeselect', Treeselect)

app
  .use(router)
  .use(ElementPlus, { locale: zhCn })
  .use(nancalui)
  .use(pinia)
  // .use(echarts)
  .use(JsonViewer)
  .mount('#app')
// 全局自定义vue指令
directves(app)
window.$dialogPopup = ElMessage
// 通过get属性触发方法，打开DataManager组件
Object.defineProperty(window, 'cacjszx', {
  get() {
    // 检查是否已经存在DataManager组件
    const dom = document.getElementById('data-manager_323423')
    if (dom) {
      // 打开DataManager组件
      dom.innerHTML = '' // 清空原有内容
    }
    const div = dom || document.createElement('div')
    div.setAttribute('id', 'data-manager_323423') // 确保唯一
    document.body.appendChild(div)
    // 绑定新的实例
    const instance = createApp(DataManager)
    instance.mount(div)
  }
})
