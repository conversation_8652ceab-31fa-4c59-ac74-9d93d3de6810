<template
  ><div class="container" style="height: 100vh">
    <div class="white-box nc-p-20" style="height: 100%">
      <n-form
        ref="formRef"
        :data="state.formData"
        :rules="state.rules"
        label-width="80px"
        message-type="text"
        label-align="end"
      >
        <n-form-item field="file" label="上传文件：">
          <div>
            <n-upload
              class="upload-demo"
              :before-upload="beforeUpload"
              :show-file-list="false"
              accept=".xlsx"
              :limit="1"
              droppable
              @click.prevent=""
            >
              <div class="upload-content">
                <n-button class="up-load-box">
                  <SvgIcon class="icon-upload" icon="icon-upload" />上传文件</n-button
                >
              </div>
            </n-upload>
            <div class="upload-demo-box">
              <div v-if="state.formData.file" class="file-data">
                <div>{{ state.formData.file?.name }}</div>
                <SvgIcon
                  class="icon-add-svg"
                  icon="close-icon"
                  title="关闭"
                  @click.stop.prevent="onRemove"
                />
              </div>
              <!-- <div class="nancal-upload__tip">
              <span @click.prevent.stop="downTemplate">下载模板</span>
              <SvgIcon class="template-icon-svg" icon="template-icon" />
            </div> -->
            </div>
          </div>
        </n-form-item>
        <n-form-item label="密钥：" field="token">
          <n-input
            style="width: 160px"
            v-model="state.formData.token"
            placeholder="请输入密钥"
            clearable
          />
        </n-form-item>
        <n-form-item label="">
          <n-button
            v-loading="state.loading"
            color="primary"
            class="nc-m-l-80 nc-m-t-10"
            variant="solid"
            @click="onSubmit"
            >提交</n-button
          >
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>
<script setup>
  import { reactive, getCurrentInstance } from 'vue'
  import md5 from 'js-md5'
  import api from '@/api/index'

  const { proxy } = getCurrentInstance()

  const state = reactive({
    loading: false,
    formData: {
      file: null,
      token: '',
    },
    rules: {
      file: [
        {
          required: true,
          message: '请上传文件',
          trigger: 'blur',
          type: 'object',
        },
      ],
      token: [
        {
          required: true,
          message: '请输入',
          trigger: 'blur',
        },
      ],
    },
  })
  //文件上传
  function beforeUpload(_data) {
    let renameFile = _data[0].file
    state.formData.file = renameFile
    return false
  }
  // 模板下载
  function downTemplate() {
    api.dataDev.algorithmDownload().then((res) => {
      // 下载文件
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheet',
      })
      const link = document.createElement('a')
      link.download = '算法模板.py'
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    })
  }
  //移除文件
  function onRemove() {
    state.formData.file = null
  }
  const formRef = ref(null)
  function onSubmit() {
    try {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          const form = new FormData()
          form.append('file', state.formData.file)
          form.append('token', md5(state.formData.token))
          state.loading = true
          const res = await api.documentManage.userTemplateImport(form)
          if (res.type == 'application/json') {
            // 假设你有一个Blob对象
            const reader = new FileReader()
            reader.onload = function (event) {
              const text = event.target.result
              const data = JSON.parse(text)
              if (data.data === 'success') {
                proxy.$message.success('上传成功')
              } else {
                proxy.$message.error(data.data)
              }
            }
            reader.readAsText(res)
          } else {
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
            const link = document.createElement('a')
            link.download = `异常用户${Date.now()}.xlsx`
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          }
          state.loading = false
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      state.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .upload-demo {
    .upload-content {
      display: flex;
      align-items: center;
      :deep(.up-load-box) {
        .button-content {
          color: #000;
        }
      }
    }
  }
  .upload-demo-box {
    display: flex;
    align-items: center;

    .file-data {
      display: flex;
      align-items: center;
      margin-left: 6px;
      div {
        margin-right: 8px;
        color: #666666;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
      }
      svg {
        font-size: 16px;
        cursor: pointer;
      }
    }
    .nancal-upload__tip {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 92px;
      height: 32px;
      margin-left: 12px;
      color: $themeBlue;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        background: #f0f7ff;
      }
      span {
        margin-right: 4px;
      }
      svg {
        font-size: 16px;
      }
    }
  }
</style>
