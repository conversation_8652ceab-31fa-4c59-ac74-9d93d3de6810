<template>
  <div class="tree-box">
    <el-tree v-bind="$attrs" ref="treeRef" class="filter-tree">
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span class="text-label" :title="node.label">{{ node.label }}</span>
          <div class="btn-box" v-if="$attrs.showBtns">
            <i class="del" @click="delFn(data)"></i>
          </div>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
  import api from '@/api/index'
  const treeRef = ref(null)
  const state = reactive({})
  const {} = toRefs(state)
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(['updateFn', 'change'])
  const updateFn = (data, isEdit) => {
    emit('updateFn', data, isEdit)
  }
  const addFn = (data) => {
    emit('addFn', data)
  }
 // 删除目录
 const delFn = (data) => {
    proxy.$dialogPopup({
      title: '是否确认删除？',
      message: '删除后，目录不再展示',
      save: () => {
        api.dataManagement.getAdhocQueryLeftListDelete(data.id).then(({ success }) => {
          if (!success) return
          emit('change')
          ElMessage.success('删除成功')
        })
      },
    })
  }
  const preventFn = (e) => {
    e.stopPropagation()
  }
  defineExpose({
    treeRef,
    name: '你是审美之间',
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/cf.scss';
  .tree-box {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
  .filter-tree {
    width: 100%;
    height: 100%;
    :deep(.el-tree-node__expand-icon) {
      display: none;
      padding: 4px;
      transform: rotate(0deg);
      > svg {
        display: none;
      }
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTggMUgyLjk5OTc2QzEuODk1MTkgMSAwLjk5OTc1NiAxLjg5NTQzIDAuOTk5NzU2IDNWMTNDMC45OTk3NTYgMTQuMTA0NiAxLjg5NTE5IDE1IDIuOTk5NzYgMTVIMTIuOTk5OEMxNC4xMDQzIDE1IDE0Ljk5OTggMTQuMTA0NiAxNC45OTk4IDEzVjNDMTQuOTk5OCAxLjg5NTQzIDE0LjEwNDMgMSAxMi45OTk4IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTc2IDcuMDAwNDlINS45OTk3NkM1LjQ0NzQ3IDcuMDAwNDkgNC45OTk3NiA3LjQ0ODIgNC45OTk3NiA4LjAwMDQ5QzQuOTk5NzYgOC41NTI3NyA1LjQ0NzQ3IDkuMDAwNDkgNS45OTk3NiA5LjAwMDQ5SDkuOTk5NzZDMTAuNTUyIDkuMDAwNDkgMTAuOTk5OCA4LjU1Mjc3IDEwLjk5OTggOC4wMDA0OUMxMC45OTk4IDcuNDQ4MiAxMC41NTIgNy4wMDA0OSA5Ljk5OTc2IDcuMDAwNDlaIiBmaWxsPSIjNTg2NDc1Ii8+CjxwYXRoIGQ9Ik04Ljk5OTc2IDEwLjAwMDVMOC45OTk3NiA2LjAwMDQ5QzguOTk5NzYgNS40NDgyIDguNTUyMDQgNS4wMDA0OSA3Ljk5OTc2IDUuMDAwNDlDNy40NDc0NyA1LjAwMDQ5IDYuOTk5NzYgNS40NDgyIDYuOTk5NzYgNi4wMDA0OUw2Ljk5OTc2IDEwLjAwMDVDNi45OTk3NiAxMC41NTI4IDcuNDQ3NDcgMTEuMDAwNSA3Ljk5OTc2IDExLjAwMDVDOC41NTIwNCAxMS4wMDA1IDguOTk5NzYgMTAuNTUyOCA4Ljk5OTc2IDEwLjAwMDVaIiBmaWxsPSIjNTg2NDc1Ii8+Cjwvc3ZnPgo=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
    :deep(.expanded) {
      &::after {
        content: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjk5OTkgMUgyLjk5OTg4QzEuODk1MzEgMSAwLjk5OTg3OCAxLjg5NTQzIDAuOTk5ODc4IDNWMTNDMC45OTk4NzggMTQuMTA0NiAxLjg5NTMxIDE1IDIuOTk5ODggMTVIMTIuOTk5OUMxNC4xMDQ0IDE1IDE0Ljk5OTkgMTQuMTA0NiAxNC45OTk5IDEzVjNDMTQuOTk5OSAxLjg5NTQzIDE0LjEwNDQgMSAxMi45OTk5IDFaIiBmaWxsPSIjRjJGNEY2Ii8+CjxwYXRoIGQ9Ik05Ljk5OTg4IDdINS45OTk4OEM1LjQ0NzU5IDcgNC45OTk4OCA3LjQ0NzcyIDQuOTk5ODggOEM0Ljk5OTg4IDguNTUyMjggNS40NDc1OSA5IDUuOTk5ODggOUg5Ljk5OTg4QzEwLjU1MjIgOSAxMC45OTk5IDguNTUyMjggMTAuOTk5OSA4QzEwLjk5OTkgNy40NDc3MiAxMC41NTIyIDcgOS45OTk4OCA3WiIgZmlsbD0iIzU4NjQ3NSIvPgo8L3N2Zz4K');
      }
    }
    :deep(.el-tree-node__content) {
      height: 32px;
      gap: 4px;
    }
    :deep(.el-tree-node__content) {
      &:hover {
        border-radius: 2px;
        background: #ebf4ff;
        .btn-box {
          display: flex;
        }
      }
    }
    .custom-tree-node {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 0 8px;
      .text-label {
        // 超出省略
        flex: 1;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &::before {
        content: url('data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CiAgPHBhdGggZD0iTTEgM1YxM0MxIDEzLjU1MjMgMS40NDc3MiAxNCAyIDE0SDE0QzE0LjU1MjMgMTQgMTUgMTMuNTUyMyAxNSAxM1Y1QzE1IDQuNDQ3NzIgMTQuNTUyMyA0IDE0IDRIOC42NjE0N0M4LjQxNTc5IDQgOC4xNzg3MSAzLjkwOTU2IDcuOTk1NDUgMy43NDU5NEw2LjMyNDU1IDIuMjU0MDZDNi4xNDEyOSAyLjA5MDQ0IDUuOTA0MjEgMiA1LjY1ODUzIDJIMkMxLjQ0NzcyIDIgMSAyLjQ0NzcyIDEgM1oiIHN0cm9rZT0iIzYwNjI2NiIvPgo8L3N2Zz4=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .btn-box {
        align-items: center;
        display: none;
        gap: 4px;
      }
      .del {
        content: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICA8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfMTc1NV8yNTQ3NykiPgogICAgPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzYwNjI2NiIvPgogICAgPHBhdGggZD0iTTExIDhMNSA4IiBzdHJva2U9IiM2MDYyNjYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgogIDwvZz4KICA8ZGVmcz4KICAgIDxjbGlwUGF0aCBpZD0iY2xpcDBfMTc1NV8yNTQ3NyI+CiAgICAgIDxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0id2hpdGUiLz4KICAgIDwvY2xpcFBhdGg+CiAgPC9kZWZzPgo8L3N2Zz4=');
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
  }
</style>
