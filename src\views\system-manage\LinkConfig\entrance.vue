<template>
  <div class="processList">
    <Navbar />
    <div class="processList-content">
      <div v-for="(val, ind) in state.list" :key="ind" class="box">
        <div class="box-title">
          <img v-if="val?.landingPage?.logoLevel2Url" :src="val?.landingPage?.logoLevel2Url" class="icon" />
          {{ val?.landingPage?.name }}
        </div>
        <div class="box-label">
          <template v-for="(item, index) in val.landingConfigList" :key="index">
            <div class="box-label-card" @click.prevent.stop="goItemFn(item)">
              <img class="box-label-card-img" :src="item.logoUrl" :title="item.name" />
              <div class="box-label-card-name" :title="item.name">{{ item.name }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { getToken } from '@/utils/auth'
  import api from '@/api/index'
  import Navbar from '@/layout/components/Navbar.vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  const router = useRouter()
  const store = useStore()

  const { roleCode } = toRefs(store.state.user)

  const state = reactive({
    list: [],
  })

  const goJump = (name, query) => {
    if (query) {
      router.push({ name: name, query })
    } else {
      router.push({ name: name })
    }
  }

  const goItemFn = (item) => {
    // let url = item.tripartiteJumpUrl + (item.tripartiteJumpUrl.indexOf('?') !== -1 ? '&' : '?') + 'token=' + getToken()
    window.open(item.tripartiteJumpUrl, '_blank')
  }

  onMounted(() => {
    api.system.loginMenuList().then((res) => {
      state.loading = false
      if (res.success) {
        res.data.forEach(val=>{
          if(val.landingConfigList){
            val.landingConfigList = val.landingConfigList.filter(val=> val.linkType === 'PROCESS')
          }
        })
        state.list = res.data.filter(val=> val?.landingPage?.type !== 'ALL')
      }
    })
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .processList {
    position: relative;
    background: #f0f2f5;
    min-height: 100vh;
    &-content {
      padding: 16px;
      box-sizing: border-box;
      overflow-y: auto;
      height: calc(100vh - $navbarHeight + 40px - 16px);
      .box {
        margin-bottom: 16px;
        &-title {
          width: 160px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #ffffff;
          border-radius: 6px 6px 0 0;
          box-sizing: border-box;
          color: rgba(0, 0, 0, 0.90);
          font-size: 16px;
          font-weight: 500;
          border-bottom: 1px solid #f0f2f5;
          .icon{
            width: 18px;
            height: 18px;
            margin-right: 4px;
          }
        }
        &-label {
          min-height: 120px;
          box-sizing: border-box;
          background-color: #ffffff;
          padding: 20px 16px 1px 48px;
          border-radius: 0 8px 8px 8px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          &-card {
            position: relative;
            width: 112px;
            cursor: pointer;
            margin-right: 24px;
            box-sizing: border-box;
            padding: 17px 0 4px 0;
            border-radius: 10px;
            margin-bottom: 16px;
            &:hover {
              background: #EBF4FF;
            }
            &-img {
              display: block;
              margin: 0 auto;
              width: 56px;
              height: 56px;
              border-radius: 8px;
              background-color: #64BFD5;
            }
            &-name {
              width: 100%;
              font-size: 14px;
              text-align: center;
              color: rgba(0, 0, 0, 0.75);
              margin-top: 6px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
</style>
