<template>
  <div v-show="state.tabs !== 'labelTask'" class="problem">
    <previewFile
      v-if="state.showPreview"
      :info="state.previewInfo"
      @close="state.showPreview = false"
    />
    <div v-else class="problem-content">
      <div class="row">
        <div class="switch">
          <div
            :class="state.tabs === 'problem' ? 'switch-label checked' : 'switch-label'"
            @click.prevent.stop="checkTabsFn('problem')"
            >数据问题</div
          >
          <div
            :class="state.tabs === 'describe' ? 'switch-label checked' : 'switch-label'"
            @click.prevent.stop="checkTabsFn('describe')"
            >根因分析</div
          >
          <div
            :class="state.tabs === 'report' ? 'switch-label checked' : 'switch-label'"
            @click.prevent.stop="checkTabsFn('report')"
            >质量报告</div
          >
          <!--        <div-->
          <!--          :class="state.tabs === 'data' ? 'switch-label checked' : 'switch-label'"-->
          <!--          @click.prevent.stop="checkTabsFn('data')"-->
          <!--          >数据标注</div-->
          <!--        >-->
        </div>
        <!--      <n-button-->
        <!--        v-if="state.tabs === 'problem'"-->
        <!--        color="primary"-->
        <!--        @click.prevent.stop="goJump('taskOverview')"-->
        <!--        ><SvgIcon class="icon-switch" icon="new-add" />发起标注任务</n-button-->
        <!--      >-->
      </div>
      <div v-loading="state.loading" class="table-box">
        <n-button v-if="state.tabs === 'report'" variant="solid" @click="downloadFile"
          >下载</n-button
        >
        <CfTable
          v-else
          :key="state.key"
          :needNewline="true"
          :isDisplayAction="state.tabs === 'describe' ? false : true"
          :table-head-titles="state.tableHeadTitles"
          :paginationConfig="{
            total: state.pageInfo.total,
            pageSize: state.pageInfo.pageSize,
            currentPage: state.pageInfo.currentPage,
            onCurrentChange: (v) => {
              state.pageInfo.currentPage = v
              initTable()
            },
            onSizeChange: (v) => {
              state.pageInfo.pageSize = v
              initTable(true)
            },
          }"
          :tableConfig="{
            data: state.tableData.list,
            rowKey: 'id',
          }"
          :actionWidth="state.tabs === 'problem' ? 300 : 0"
          :actionName="state.tabs === 'problem' ? '问题数据' : state.tabs === 'data' ? '操作' : ''"
        >
          <template #oaStatus="{ row }">
            <n-select
              v-model="row.aoConsumptionStatus"
              placeholder="请选择AO消耗状态"
              :options="state.aoOptions"
            />
          </template>
          <template #problemType="{ row }">
            <n-select
              v-model="row.problemType"
              placeholder="请选择问题类型"
              :options="state.typeOptions"
            />
          </template>
          <template #flow="{ row }">
            <n-button
              class="has-right-border"
              code="dataManagement_collectionMonitor_view"
              variant="text"
              @click.prevent="goItemFn(state.governUrl)"
              >发起</n-button
            >
          </template>
          <template #editor="{ row }">
            <div class="edit-box">
              <n-button
                v-if="state.tabs === 'data'"
                class="has-right-border"
                code="dataManagement_collectionMonitor_view"
                variant="text"
                >打标</n-button
              >
              <n-button
                v-if="state.tabs === 'data'"
                class="has-right-border"
                code="dataManagement_collectionMonitor_view"
                variant="text"
                >查看二维码</n-button
              >
              <n-button
                v-if="state.tabs === 'problem'"
                class="has-right-border"
                code="dataManagement_collectionMonitor_view"
                variant="text"
                @click.prevent="
                  () => {
                    state.problemInfo = row
                    state.tabs = 'labelTask'
                  }
                "
                >发起标注任务</n-button
              >
              <n-button
                v-if="state.tabs === 'problem'"
                class="has-right-border"
                code="dataManagement_collectionMonitor_view"
                variant="text"
                :disabled="!row.detailsUrl"
                @click.prevent="seeFn(row)"
                >问题数据详情</n-button
              >
            </div>
          </template>
        </CfTable>
      </div>
    </div>
  </div>

  <InitiateLabelTask
    v-if="state.tabs === 'labelTask'"
    :info="state.problemInfo"
    @onCancel="
      () => {
        state.tabs = 'problem'
      }
    "
  />
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import api from '@/api/index'
  import previewFile from './previewFile'
  import { workbenchDicCode, workbenchLoadKey, reportDownload } from '@/api/dataManage.js'
  import { useRouter } from 'vue-router'
  import InitiateLabelTask from './InitiateLabelingTask.vue'
  const router = useRouter()
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    key: 1,
    loading: false,
    problemInfo: {}, //问题详情
    tabs: 'problem',
    tableData: { list: [] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'name', name: '问题名称' },
      { prop: 'ruleName', name: '规则名称' },
      { prop: 'description', name: '问题详情', width: 700 },
      { prop: 'numbers', name: '数据条数' },
      { prop: 'flow', name: '治理流程', slot: 'flow', minWidth: 110, fixed: 'right' },
    ],
    typeOptions: [],
    aoOptions: [],
    showPreview: false,
    previewInfo: {},
    governUrl: '',
  })

  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }

  const checkTabsFn = (type) => {
    state.tabs = type
    if (type === 'describe') {
      state.tableHeadTitles = [
        { prop: 'problemName', name: '问题名称' },
        { prop: 'problemReason', name: '问题根因', width: 700 },
        { prop: 'solution', name: '解决措施' },
      ]
    } else if (type === 'data') {
      state.tableHeadTitles = [
        { prop: 'number', name: '序号' },
        { prop: 'aoConsumptionStatus', name: 'AO消耗状态', slot: 'oaStatus' },
        { prop: 'normal', name: '是否正常' },
        { prop: 'problemType', name: '问题类型', slot: 'problemType' },
        { prop: 'applicationScope', name: '适用范围' },
        { prop: 'responsibleDepartment', name: '责任单位' },
        { prop: 'materialType', name: '物料类型' },
        { prop: 'drawingNo', name: '图号' },
        { prop: 'name', name: '名称' },
        { prop: 'assembleDrawingNo', name: '装配图号' },
      ]
    } else if (type === 'problem') {
      state.tableHeadTitles = [
        { prop: 'name', name: '问题名称' },
        { prop: 'ruleName', name: '规则名称' },
        { prop: 'description', name: '问题详情', width: 700 },
        { prop: 'numbers', name: '数据条数' },
        { prop: 'flow', name: '治理流程', slot: 'flow', minWidth: 110, fixed: 'right' },
        // { prop: 'description', name: '问题详情' },
        // { prop: 'reasonDescription', name: '问题原因描述' },
        // { prop: 'processNo', name: '流程编号' },
        // { prop: 'processProgress', name: '流程进展' },
        // { prop: 'reportDepartment', name: '提出部门' },
        // { prop: 'responsibleDepartment', name: '问题责任部门' },
        // { prop: 'responsiblePerson', name: '问题责任人' },
        // { prop: 'improveTaskNumber', name: '问题质量整改单编号' },
        // { prop: 'launchTime', name: '问题发起时间' },
        // { prop: 'solutionFormationTime', name: '处置方案形成时间' },
        // { prop: 'keyInitiatives', name: '问题关键举措' },
        // { prop: 'plannedCompletionTime', name: '计划完结时间' },
        // { prop: 'actualZeroingTime', name: '实际归零时间' },
      ]
    }
    state.key++
    initTable(true)
  }

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    let urlInterface = 'workbenchExplainList'
    if (state.tabs === 'data') {
      urlInterface = 'workbenchAnnotationList'
    } else if (state.tabs === 'problem') {
      urlInterface = 'workbenchProblemList'
    }

    api.dataManagement[urlInterface](data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          res.data.list.forEach((item, index) => {
            item.number = index + 1
          })
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 下载
  const downloadFile = (url, name) => {
    reportDownload({
      projectCode: props.info.code,
    })
      .then((res) => {
        if (res.type === 'application/json') {
          // 说明是普通对象数据，读取错误信息
          const fileReader = new FileReader()
          fileReader.readAsText(res)
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            ElMessage.error(jsonData.message || '下载失败')
          }
        } else {
          // 下载文件
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          })
          const link = document.createElement('a')
          link.download = `质量报告_${props.info.code}.docx`
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
          ElMessage.success('下载成功')
        }
      })
      .catch(() => {
        ElMessage.error('下载失败')
      })
  }

  // 跳转
  const goItemFn = (url) => {
    window.open(url, '_blank')
  }

  // 跳转
  const seeFn = (row) => {
    state.previewInfo = {
      ...row,
    }
    if (row.detailsUrl) {
      state.showPreview = true
    }
  }

  const typeFn = (type) => {
    workbenchDicCode({ type }).then((res) => {
      if (res.success) {
        res.data.forEach((val) => {
          val.name = val.text
          val.value = val.code
        })
        if (type === 'AO_CONSUMPTION_STATUS') {
          state.aoOptions = res.data
        } else {
          state.typeOptions = res.data
        }
      }
    })
  }

  onMounted(() => {
    initTable(true)
    // typeFn('AO_CONSUMPTION_STATUS')
    // typeFn('DATA_PROBLEM_CATEGORY')
    workbenchLoadKey({}).then((res) => {
      if (res.success) {
        state.governUrl = res.data.jumpUrl
      }
    })
  })
</script>

<style lang="scss" scoped>
  .problem {
    position: relative;
    width: 100%;
    height: 100%;
    &-content {
      width: 100%;
      height: 100%;
    }
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .switch {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 10px;
        &-label {
          width: 100px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          box-sizing: border-box;
          border: 1px solid #c5d0ea;
          border-left: none;
          color: #333;
          font-size: 14px;
          cursor: pointer;
          border-radius: 2px;
          &:first-of-type {
            border-left: 1px solid #c5d0ea;
          }
          &.checked {
            color: #479dff;
            font-weight: 500;
            border: 1px solid #479dff;
          }
          &.checked + .switch-label {
            border-left: none;
          }
        }
      }
    }

    .table-box {
      position: relative;
      margin-top: 10px;
      height: calc(100% - 52px);
    }

    .nancalui-button--text {
      color: #1e89ff;
      font-size: 12px;
    }
  }
</style>
