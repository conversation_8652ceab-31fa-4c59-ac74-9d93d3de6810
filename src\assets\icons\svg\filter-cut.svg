<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="数据开发-实时数据作业-画布/数据过滤/算子配置超出" transform="translate(-1880.000000, -542.000000)">
            <g id="属性" transform="translate(940.000000, 0.000000)">
                <g id="字段" transform="translate(20.000000, 536.000000)">
                    <g id="编组" transform="translate(920.000000, 6.000000)">
                        <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="20" height="20"></rect>
                        <path d="M10,18.3333333 C14.602375,18.3333333 18.3333333,14.602375 18.3333333,10 C18.3333333,5.397625 14.602375,1.66666667 10,1.66666667 C5.397625,1.66666667 1.66666667,5.397625 1.66666667,10 C1.66666667,14.602375 5.397625,18.3333333 10,18.3333333 Z" id="路径" stroke="currentColor" stroke-width="1.25" stroke-linejoin="round"></path>
                        <line x1="6.66666667" y1="10" x2="13.3333333" y2="10" id="路径" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></line>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>