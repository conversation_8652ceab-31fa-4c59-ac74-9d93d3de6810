<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }" @click="state.visibleTag = false">
    <div class="content">
      <div :class="{
        'data-collection-page': true,
        container: true,
        table: true,
      }">
        <div class="data-collection-page-tree">
          <div class="data-collection-page-tree-title">表管理</div>
          <n-input class="data-collection-page-tree-ipt" v-model="state.treeSearchText" placeholder="请输入表名称搜索"
            suffix="search" @change="(val) => treeRef.treeRef.filter(val)" />
          <LocalTree ref="treeRef" :check-on-click-node="true" :filter-node-method="filterNode"
            :default-expanded-keys="[state.treeData?.[0]?.id || 1]" :props="{
              children: 'children',
              label: 'name',
            }" node-key="id" :data="state.treeData" @node-click="clickFn" />
        </div>
        <div class="data-collection-page-content">
          <!-- 其他 -->
          <div class="out-box">
            <div class="out-box-tab">
              <n-radio-group direction="row" v-model="state.dataType"
                @change="searchParams.condition.modelName.length > 0 && init(true)">
                <n-radio-button value="table">数据授权</n-radio-button>
                <n-radio-button value="data">数据预览</n-radio-button>
              </n-radio-group>
              <div v-if="state.dataType === 'data'" class="only">仅显示前20条数据</div>
            </div>
            <div class="table-list dif" v-loading="state.loading">
              <div class='table-list-box'>
                <CfTable :isDisplayAction="false" :showPagination="false" :table-head-titles="state.tableHeadTitles"
                  :tableConfig="{
                    data: state.tableData,
                    rowKey: 'id',
                  }" :paginationConfig="state.dataType === 'table' && {
                    total: state.total,
                    pageSize: searchParams.pageSize,
                    currentPage: searchParams.pageNum,
                    onCurrentChange: (v) => {
                      searchParams.pageNum = v
                      init(false)
                    },
                    onSizeChange: (v) => {
                      searchParams.pageSize = v
                      onSearch()
                    },
                  }">

                  <template #pageTop v-if="state.dataType === 'table'">
                    <div style="padding: 8px;">
                      <n-button variant="solid" v-if="buttonAuthList.includes('tableAuthManageAuthButton')"
                        @click="searchParams.condition.modelName.length > 0 && modalRef?.open(searchParams.condition.modelName)">授权</n-button>
                    </div>
                  </template>
                  <template #secretLevel="{ row }">
                    {{ importanceLevelMap[row.secretLevel] }}
                  </template>
                  <template v-if="state.tableData.length && state.dataType === 'table'" #editor="{ data: { row } }">
                    <n-button v-if="buttonAuthList.includes('tableAuthManageCancelButton')" variant="text"
                      color="primary" @click="cancelFn(row)">取消授权</n-button>
                  </template>
                </CfTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <LocalModal ref="modalRef" :key="searchParams.condition.modelName" @change="() => { onSearch(); }" />
  </div>

</template>
<script>
import { reactive, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue'
import { useStore } from 'vuex'
import api from '@/api/index'
import { removeTable } from '@/api/model.js'
import ENUM from '@/const/enum'
import { formartTime } from '@/utils/index'
import { collectMonitorList, collectResultRun } from '@/api/dataManage'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import cardList from '@/components/CardList'
import LocalTree from './components/tree'
import LocalModal from './components/modal'
export default {
  name: '',
  components: { cardList, LocalTree, LocalModal },
  props: {},
  setup() {
    const store = useStore()
    const modalRef = ref(null)
    const treeRef = ref(null)

    //按钮权限
    const { buttonAuthList } = toRefs(store.state.user)
    const router = useRouter()
    const state = reactive({
      isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
      tableData: [],
      dataType: 'table',
      loading: false,
      tableHeadTitles: [],
      timeFlag: null,
      treeSearchText: '',
      treeData: [],
      visibleTag: false,
      searchParams: {
        condition: {
          modelName: ""
        },
        pageNum: 1,
        pageSize: 10,
        sortConditions: [
          {
            fieldName: "",
            sort: ""
          }
        ]
      },
      total: 0,

    })
    const importanceLevelMap = { NONCLASSIFIED: '非涉密', GENERAL: '一般', IMPORTANT: '重要', CORE: '核心' }
    const { searchParams } = toRefs(state)
    // 获取当前组件实例
    const { proxy } = getCurrentInstance()
    const methods = {
      // 重置
      resetFn() {
        state.dataType = 'table'
        state.tableData = []
        state.tableHeadTitles = []
        state.total = 0
        searchParams.value = {
          condition: {
            modelName: ""
          },
          pageNum: 1,
          pageSize: 10,
          sortConditions: [
            {
              fieldName: "",
              sort: ""
            }
          ]
        }
      },
      filterNode: (value, data) => {
        if (!value) return true
        return data?.name?.includes(value) || data?.tableName?.includes(value)
      },
      //搜索
      onSearch() {
        methods.init(true)
      },
      // 获取树列表
      getTreeListFn() {
        api.dataDev
          .tablePermissionTree()
          .then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              treeData.push({
                id: -1,
                name: '全部',
                type: 'DIRECTORY',
                isTemplateDirectory: false,
                children: res?.data?.children || [],
              })
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]
            }
          })
      },
      // 树搜索
      searchTreeFn() {
        if (state.timeFlag) {
          clearTimeout(state.timeFlag)
          state.timeFlag = null
        }
        state.timeFlag = setTimeout(() => {
          methods.getTreeListFn()
        }, 300)
      },
      // 取消授权
      cancelFn(row) {
        api.dataDev
          .tablePermissionCancel({
            id: row.id,
          })
          .then((res) => {
            if (res.code === 'SUCCESS') {
              ElNotification({
                title: '成功',
                message: '取消授权成功',
                type: 'success',
              })
              methods.init(true)
            }
          })
      },
      // 树点击事件
      clickFn(node) {
        if (node.tableName && searchParams.value.condition.modelName !== node.tableName) {
          methods.resetFn()
          searchParams.value.condition.modelName = node.tableName
          methods.onSearch(true)
          methods.initTable(true)
        }
      },
      // 初始化
      init(init = false) {
        if (state.dataType === 'data') {
          methods.getTableHead()
          methods.getModalList(true)
        } else {
          methods.initTable(init)
        }
      },
      // 获取表头
      getTableHead() {
        state.tableHeadTitles = []
        api.model
          .getModelFieldsList({ name: searchParams.value.condition.modelName })
          .then((res) => {
            let { data, success } = res
            if (success) {
              state.tableHeadTitles = data?.map((item) => {
                return {
                  prop: item.name,
                  name: item?.cnName || item.name,
                }
              })
            }
          })
          .finally(() => {
          })
      },
      // 获取表数据
      getModalList(init = false) {
        let data = {
          "condition": {
            "modelName": searchParams.value.condition.modelName,
            "limitPreview": 20
          },
          "pageNum": 1,
          "pageSize": 20
        }

        api.model.getModelData(data).then((res) => {
          if (res.success) {
            state.tableData = res?.data?.list || []
          }
        })
      },
      // 获取表结构
      initTable(init = false) {
        searchParams.value.pageNum = init ? 1 : searchParams.value.pageNum
        state.loading = true
        api.dataDev.tablePermissionPage
          (searchParams.value)
          .then((res) => {
            if (res.success) {
              state.tableHeadTitles = [
                { prop: 'userName', name: '工号' },
                { prop: 'name', name: '姓名' },
                { prop: 'departmentFullName', name: '部门' },
                { prop: 'secretLevel', name: '密级', slot: "secretLevel" },
              ]
              state.tableData = res?.data?.list || []
              state.total = res.data.total
            }
          })
          .catch(() => {
            state.tableData = []
          }).finally(() => {
            state.loading = false
          })
      },
    }

    onMounted(() => {
      methods.getTreeListFn()
    })
    return {
      treeRef,
      modalRef,
      buttonAuthList,
      state,
      importanceLevelMap,
      searchParams,
      ...methods,
    }
  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.data-collection-page-out-box {
  box-sizing: border-box;
  height: calc(100vh - 90px);
  padding: 16px;

  .tools {
    height: 50px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 2px;

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 0 16px;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        width: 100%;
        height: 52px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bolder;
        font-size: 18px;

        :deep(.button-content) {
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            margin-right: 4px;
          }
        }

        &-btn {
          position: absolute;
          right: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 16px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #e3ecff;
          }
        }
      }

      &.date {
        height: 36px;

        :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
          width: 260px;
        }
      }

      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .label {
          color: #1d2129;
          font-size: 14px;
        }
      }

      &.tabs {
        align-items: flex-end;
        height: 48px;

        :deep(.nancalui-tabs) {
          .nancalui-tabs-nav-tab {
            border-bottom: none;
          }
        }
      }

      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            margin-right: 4px;
          }

          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 0 8px;
        color: $themeBlue;
        font-weight: 400;
        font-size: 14px;
        border-radius: 6px;
        cursor: pointer;

        &:hover {
          background-color: #ecf7ff;
        }

        .icon {
          margin-right: 4px;
        }
      }

      .nancalui-input,
      .nancalui-select {
        width: 260px;
        margin-right: 32px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          width: 60px;
          height: 30px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          line-height: 30px;
          text-align: center;
          background-color: #1e89ff;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background-color: #fff;
            border: 1px solid #dcdfe6;

            &:hover {
              color: #479dff;
              background-color: #fff;
              border: 1px solid #479dff;
            }

            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }

          &:hover {
            background-color: #479dff;
            border: 1px solid #479dff;
          }
        }
      }
    }
  }

  .content {
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 0;
  }
}

.data-collection-page {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  box-sizing: border-box;
  height: 100%;
  padding: 0;
  border-radius: 0;

  .data-collection-page-tree {
    box-sizing: border-box;
    width: 286px;
    height: 100%;
    padding: 8px 0;
    background-color: #fff;
    border-radius: 2px;

    :deep(.nancalui-tabs-nav-tab) {
      box-sizing: border-box;
      padding: 0 12px;
      border-bottom: none;

      .nancalui-tabs-nav-tab-list {
        width: 100%;

        .nancalui-tabs-tab {
          box-sizing: border-box;
          width: 50%;
          height: 32px;
          line-height: 32px;
          border: 1px solid #dcdfe6;

          &.nancalui-tabs-tab-active {
            border-color: #447dfd;
          }
        }
      }
    }

    &-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 36px;
      padding: 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }
    }

    &-ipt {
      margin-bottom: 8px;
      padding: 0 12px;

      :deep(.nancalui-input__wrapper:not(.nancalui-input--error):not(.nancalui-input--disabled)) {
        border: 1px solid #e5e6eb;

        .nancalui-input__inner {
          border-right: 1px solid #e5e6eb;
        }
      }

      :deep(.nancalui-input-slot__suffix) {
        opacity: 0.5;

        .icon-search {
          font-weight: normal;
          transform: scale(1.4);
        }
      }
    }

    :deep(.tree-box) {
      padding: 0 12px;
      height: calc(100% - 76px)
    }
  }

  .data-collection-page-content {
    width: calc(100% - 10px - var(--aside-width));
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 2px;

    .out-box {
      height: 100%;

      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 46px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 16px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }

      &-tab {
        position: relative;
        padding: 8px;

        .only {
          position: absolute;
          top: 0;
          right: 8px;
          bottom: 0;
          height: 22px;
          margin: auto;
          color: #1d2129;
          font-size: 14px;
        }
      }
    }

    .table-list {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 48px);
      padding: 0;
      overflow-x: auto;

      &-box {
        min-width: 100%;
        height: 100%;

        &.empty-table {
          :deep(.nancalui-table) {
            .nancalui-table__header-wrapper {
              display: none;
            }
          }

          :deep(.nancalui-table-v__header-wrapper) {
            display: none;
          }
        }
      }

      .nancalui-table {
        .taskName {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          .icon-status-svg {
            margin-right: 4px;
            font-size: 18px;
          }
        }

        .envType {
          &-name {
            width: max-content;
            padding: 0 8px;
            color: #447dfd;
            font-size: 12px;
            line-height: 20px;
            background: #f0f7ff;
            border: 1px solid #bfd9ff;
            border-radius: 10px;

            &.test {
              color: #04c495;
              background: rgba(230, 255, 244, 0.7);
              border: 1px solid #75ebc2;
            }
          }
        }

        .taskStatus {
          .circle {
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 4px;
            background-color: $themeBlue;
            border-radius: 50%;

            &.green {
              background-color: #00ca5f;
            }

            &.gray {
              background-color: #b8b8b8;
            }
          }
        }

        .rateTime-box {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .status-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .status {
            width: 6px;
            height: 6px;
            margin-right: 4px;
            border-radius: 6px;

            &.PUBLISH {
              background-color: #04c495;
            }

            &.CREATED {
              background-color: #447dfd;
            }

            &.OFFLINE {
              background-color: #b8b8b8;
            }
          }
        }
      }

      &.empty-list {
        height: calc(100% - 50px);
        background-color: #fff;
        border-radius: 8px;
      }

      .empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 266px;
        height: 180px;
        margin: auto;

        &-img {
          display: block;
          width: 140px;
          height: auto;
          margin: 0 auto;
        }

        &-text {
          margin-top: 20px;
          color: #999999;
          font-size: 12px;
          text-align: center;
        }
      }
    }

    .table-content-pagination {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-sizing: border-box;
      width: 100%;
      height: 60px;
      padding: 16px;
      border-top: 1px solid #dcdfe6;

      &-total {
        margin-right: 10px;
        color: rgba(0, 0, 0, 0.75);
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;

        span {
          padding: 0 4px;
          color: var(--themeBlue);
        }
      }

      :deep(.nancalui-pagination) {
        .nancalui-pagination__size {
          margin: 0;
        }
      }
    }

    .project-desc {
      padding: 15px 20px;
      background-color: #fff;
      border-radius: 4px 4px 0 0;

      .content {
        color: #333333;
        font-weight: 600;
        font-size: 14px;

        i {
          padding: 0 10px;
        }
      }
    }

    .datasourceType {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &-img {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
    }

    .datasourceSecret {
      box-sizing: border-box;
      width: 40px;
      height: 20px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 12px;
      line-height: 18px;
      text-align: center;
      background-color: #f4f4f5;
      border: 1px solid rgba(177, 179, 184, 0.53);
      border-radius: 2px;

      &.green {
        color: #31b046;
        background-color: #ebfaed;
        border: 1px solid #31b046;
      }

      &.blue {
        color: #1e89ff;
        background-color: #ebf4ff;
        border: 1px solid #1e89ff;
      }
    }
  }
}

.modal-body {
  &-header {
    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .col {
        display: flex;
        flex: 1;
        flex-shrink: 0;
        align-items: center;
        justify-content: flex-start;
        height: 22px;
        color: #606266;
        font-size: 14px;
        line-height: 22px;

        .name {
          width: 100px;
        }

        .value {
          width: calc(100% - 100px);
          overflow: hidden;
          color: #1d2129;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  &-table {
    width: 100%;
    overflow-x: auto;

    &-scroll {
      min-width: 100%;
    }
  }
}

.treeNodeName {
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 30px;
  overflow: hidden;
}

.tagPop {
  position: fixed;
  top: 160px;
  left: 530px;
  z-index: 9;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 4px 8px 0 rgba(37, 43, 58, 0.2);

  .node {
    padding: 0 30px;
    font-size: 12px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;

    &:hover {
      color: #1e89ff;
      background: #ebf4ff;
    }
  }
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}
</style>
