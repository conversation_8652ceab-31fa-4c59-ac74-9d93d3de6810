<template>
  <section class="template-con-flex">
    <div class="left nc-p-t-10 asideTree">
      <div class="template-list-title nc-flex">
        <div>非结构化数据分类</div>
      </div>
      <div class="class-list nc-m-t-10">
        <n-input
          class="class-list-tree-ipt"
          v-model="tableState.treeSearchText"
          placeholder="请输入关键词"
          suffix="search"
          @input="(val) => treeRef.treeRef.filter(val)"
        />
        <div class="nc-m-t-8 class-tree">
          <CfTtee
            ref="treeRef"
            :check-on-click-node="true"
            :filter-node-method="filterNode"
            :default-expanded-keys="classState.expandedKeys"
            :current-node-key="classState.selectedKey"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :data="classState.data"
            @node-click="clickFn"
          />
        </div>
      </div>
    </div>
    <div class="right">
      <div class="table" v-loading="tableState.isLoad">
        <CfTable
          actionWidth="140"
          :table-head-titles="tableState.tableHeadTitles"
          :tableConfig="{
            data: tableState.tableList,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: tableState.pagination.total,
            pageSize: tableState.pagination.pageSize,
            currentPage: tableState.pagination.currentPage,
            onCurrentChange: (v) => {
              tableState.pagination.currentPage = v
              onSearch()
            },
            onSizeChange: (v) => {
              tableState.pagination.pageSize = v
              onSearch()
            },
          }"
        >
          <template #editor="{ data: { row } }">
            <n-button variant="text" color="primary" @click="onView(row)">查看</n-button>
            <n-button
              variant="text"
              color="primary"
              v-if="row.registerStatus === 'UNREGISTERED'"
              @click="onRegister(row)"
              >注册资产</n-button
            >
          </template>
          <template #labelList="{ row }">
            <cfTag :tagArr="JSON.stringify(row.labelList || [])" />
          </template>
          <template #tagList="{ row }">
            <level-tag :bgColor="row.bgColor" :borderColor="row.borderColor" :color="row.color">{{
              row.confidentialityLevelName
            }}</level-tag>
          </template>
          <template #status="{ row }">
            <span class="un-registered" v-if="row.registerStatus === 'UNREGISTERED'">未注册</span>
            <span class="registered" v-else-if="row.registerStatus === 'REGISTERED'">注册成功</span>
            <span class="in-review" v-else-if="row.registerStatus === 'APPROVAL'">待审批</span>
          </template>
        </CfTable>
      </div>
    </div>
  </section>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  import { getCurrentInstance, reactive, nextTick } from 'vue'
  import { tagList } from '@/views/document-management/config/tag'
  import CfTtee from '@/components/cfTtee'
  import cfTag from '@/components/cfTag'
  const router = useRouter()
  import api from '@/api/index'
  const props = defineProps({
    formInline: {
      type: String,
      default: 'true',
    },
  })
  const classState = reactive({
    data: [],
    expandedKeys: [],
    selectedKey: null,
  })
  const tableState = reactive({
    filterSearch: { categoryId: '' },
    tableList: [],
    isLoad: false,
    pagination: {
      total: 0,
      pageSize: 15,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'ruleCode', name: '非结构化数据编码', width: '200' },
      { prop: 'name', name: '非结构化数据名称', width: '240' },
      { prop: 'categoryName', name: '非结构化数据分类' },
      { prop: 'registerStatus', name: '注册状态', slot: 'status' },
      { prop: 'labelList', name: '标签', width: 300, slot: 'labelList' },
      { prop: 'confidentialityLevelName', name: '密级', slot: 'tagList' },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间', width: '160' },
    ],
  })
  const filterNode = (value, data) => {
    if (!value) return true
    return data.name.includes(value)
  }
  // 预览
  function onView(row) {
    router.push(
      {
        name: 'assetsRegDocView',
        query: {
          id: row.id,
        },
      },
      { replace: false },
    )
  }
  // 注册
  function onRegister(row) {
    router.push({
      name: 'assetsRegDocRegister',
      query: {
        id: row.id,
      },
    })
  }
  // 点击树节点
  function clickFn(node) {
    classState.selectedKey = node.id
    tableState.filterSearch.categoryId = node.id
    onSearch()
  }
  // 搜索
  const treeRef = ref(null)
  async function searchClassFn() {
    treeRef.value.treeFactory.searchTree(tableState.treeSearchText, {
      isFilter: true,
      matchKey: 'name',
    })
  }
  // 获取分类树
  async function getClassifyTreeList() {
    const res = await api.documentManage.outsideTree()
    classState.data = [{ ...res.data, expanded: true }]
    nextTick(() => {
      classState.expandedKeys = [classState.data?.[0]?.id || 1]
      classState.selectedKey = classState.data?.[0]?.id || 1
    })
  }
  // 查询
  function onSearch(objPram) {
    let { assetsRegistered, name } = props.formInline
    if (objPram) {
      tableState.pagination.currentPage = 1
      assetsRegistered = objPram.assetsRegistered
      name = objPram.name
    }
    tableState.isLoad = true
    api.documentManage
      .outsideListPage({
        pageNum: tableState.pagination.currentPage,
        pageSize: tableState.pagination.pageSize,
        condition: {
          registerStatus: assetsRegistered,
          name: name || '',
          ...tableState.filterSearch,
        },
      })
      .then((res) => {
        tableState.tableList = res.data.list.map((i) => {
          const { color, bgColor, borderColor } = tagList[i.confidentialityLevelName]
          return {
            ...i,
            bgColor,
            color,
            borderColor,
            // labelList: i.labelList?.map((l) => {
            //   const [color, bgColor] = l.color.split('_')
            //   return {
            //     ...l,
            //     color,
            //     bgColor,
            //   }
            // }),
          }
        })
        tableState.pagination.total = res.data.total
        tableState.isLoad = false
      })
      .catch(() => {
        tableState.isLoad = false
      })
  }
  onMounted(() => {
    getClassifyTreeList()
    onSearch()
  })

  // 使用toRefs解构
  defineExpose({
    onSearch,
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/cf.scss';
  @import '@/styles/variables.scss';
  .template-con-flex {
    display: flex;
    width: 100%;
    height: calc(100vh - 230px);
    border-top: 1px solid #dcdfe6;
    .left {
      width: 300px;
      background: #fff;
      // border-right: 1px solid #dcdfe6;
      border-radius: $cf-border-radius;
    }
    .right {
      width: calc(100% - var(--aside-width));
      height: calc(100% - 7px);
      background: #fff;
      border-radius: $cf-border-radius;
    }
  }
  .template-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    text-indent: 8px;
    border-left: 4px solid $cf-color-primary;
  }
  .class-list {
    padding: 12px;
    .class-tree {
      height: calc(100vh - 300px);
      overflow: auto;
      :deep(.tree-box) {
        height: calc(100% - 46px);
      }
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content) {
      width: 100%;
      padding: 0 8px;
      font-size: 14px;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content.active) {
      font-size: 14px;
      background: #ebf4ff;
    }
    :deep(.nancalui-tree__node .nancalui-tree__node-content:not(.active):hover) {
      background: #ebf4ff;
    }
    .tree-icon {
      margin: 0 4px;
      font-size: 16px;
    }
    .tree-label {
      max-width: 140px;
      margin-right: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .table {
    height: calc(100% - 8px);
  }
  :deep(.page-footer) {
    z-index: 2;
  }
  .registered::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #2ca340;
    border-radius: 50%;
    content: '';
  }
  .in-review::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #1e89ff;
    border-radius: 50%;
    content: '';
  }
  .un-registered::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    vertical-align: middle;
    background: #a8abb2;
    border-radius: 50%;
    content: '';
  }
  :deep(.el-loading-mask) {
    .el-loading-spinner {
      .circular {
        width: 48px !important;
        height: 48px !important;
      }
    }
  }
</style>
