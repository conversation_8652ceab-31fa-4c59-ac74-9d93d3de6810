<template>
  <div class="table-info-content sqlCode">
    <n-tabs v-model="activeName" type="card" @active-tab-change="handleClick">
      <n-tab title="数据实例" id="first">
        <div class="sqlCode">
          <n-button style="margin-bottom: 5px" :disabled="disabled" @click="addData">添加</n-button>
          <span>（最多允许添加10条）</span>
          <n-table v-if="tableData.length" :data="tableData" height="350px">
            <n-column
              resizeable
              v-for="item in tableColumns"
              :key="item.key"
              :header="item.key"
              :field="item.key"
            >
              <template #default="scope">
                <n-input-number
                  v-if="
                    item.valid === 'INTEGER(p)' ||
                    item.valid === 'INTEGER' ||
                    item.valid === 'FLOAT' ||
                    item.valid === 'SMALLINT' ||
                    item.valid === 'REAL' ||
                    item.valid === 'BIGINT' ||
                    item.valid === 'NUMERIC' ||
                    item.valid === 'TINYINT' ||
                    item.valid === 'BOOLEAN'
                  "
                  v-model="tableData[scope.rowIndex][item.key]"
                  style="width: 100%; text-align: left"
                  hideButton
                  placeholder="请输入数字"
                  :max="maxNumber(item.length)"
                  :disabled="disabled"
                />
                <n-input
                  v-else
                  v-model="tableData[scope.rowIndex][item.key]"
                  :maxLength="maxlength(item)"
                  placeholder="请输入"
                  :disabled="disabled"
                />
              </template>
            </n-column>
            <n-column resizeable header="操作" fixed="right" width="150px">
              <template #default="scope">
                <n-icon
                  name="delete"
                  operable
                  size="14px"
                  style="cursor: pointer"
                  @click="deleteData(scope.rowIndex)"
                  :disabled="disabled"
                />
              </template>
            </n-column>
          </n-table>
        </div>
      </n-tab>
      <n-tab title="数据库代码" id="second">
        <div class="sqlCode">
          <p v-for="(item, index) in sqlData" :key="index">{{ item }}</p>
        </div>
      </n-tab>
    </n-tabs>
  </div>
</template>
<script>
  import { notSetLen } from '../../utils/index.js'

  export default {
    name: 'TableInfo',
    props: {
      cell: {
        type: Object,
        default() {
          return null
        },
      },
      isShow: {
        type: Boolean,
        default() {
          return false
        },
      },
    },
    data() {
      return {
        sqlData: [],
        activeName: 'first',
        tableData: [],
        tableColumns: [],
        tableKeys: {},
      }
    },
    computed: {
      // 不可编辑
      disabled() {
        // const { disableMove } = this.cell.data
        // return disableMove
        return true
      },
    },
    watch: {
      isShow: {
        handler(val) {
          if (val) {
            this.getTableColums()
            this.getTableData()
          }
        },
        immediate: true,
      },
    },
    methods: {
      // 最大数字
      maxNumber(length) {
        const len = Number(length)
        let max = ''
        for (let i = 0; i < len; i++) {
          max += '9'
        }
        return Number(max) > Infinity ? Infinity : Number(max)
      },
      // 最大长度
      maxlength(item) {
        const { valid, length } = item
        // 不能设置长度
        if (notSetLen(valid, 'En')) {
          return ''
        } else {
          return length
        }
      },
      getTableData() {
        const demoList = this.cell.data.demoList || []
        this.tableData = [...demoList]
      },
      setInfoData() {
        const data = { ...this.cell.data }
        data.demoList = [...this.tableData]
        this.cell.setData(data, { overwrite: true })
      },
      // 获取列信息
      getTableColums() {
        this.tableColumns = []
        const col = this.cell.data.col
        col.forEach((item) => {
          this.tableColumns.push({ key: item.col, valid: item.dataType, length: item.length })
          this.tableKeys[item.col] = null
        })
      },
      deleteData(index) {
        this.tableData.splice(index, 1)
      },
      addData() {
        if (this.tableData.length < 10) {
          this.tableData.push({ ...this.tableKeys })
        }
      },
      handleClick(activeName) {
        console.log(activeName, 'activeName')
        if (activeName === 'second') {
          const data = this.cell.data
          // 如果是视图，直接取值，无需请求后端接口
          if (data.type === 'VIEW') {
            this.sqlData = data.scripts
          } else {
            this.setInfoData()
            this.getSql()
          }
        }
      },
      getSql() {
        const data = this.cell.data
        const params = {
          ...data.table,
          modType: this.$route.query.modType,
          guid: data.id,
          columnList: data.col.map((i) => {
            return {
              ...i,
              charUsed: i.charUsed?.code ? i.charUsed.code : i.charUsed,
            }
          }),
          demoList: data.demoList,
        }
        this.$api.dataModel.getSqlCode(params).then((res) => {
          if (res.success) {
            let data = { ...this.cell.data }
            data.sql = JSON.stringify(res.data)
            // 防止数据改变不触发
            this.cell.setData(data, { overwrite: true })
            this.sqlData = res.data
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .sqlCode {
    min-height: 400px;
    padding: 10px;
  }
  .table-info-content {
    :deep(.el-input-number) {
      input {
        text-align: left;
      }
    }
  }
</style>
