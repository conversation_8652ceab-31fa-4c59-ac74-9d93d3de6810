<template>
  <!-- 基础信息 -->
  <div class="project-add-basic publicFormCss">
    <div class="add-basic-box">
      <n-form
        ref="form"
        :data="state.form"
        label-width="150px"
        :rules="state.rules"
        :pop-position="['right']"
      >
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="项目名称：" field="projectName">
              <n-input
                v-model="state.form.projectName"
                maxlength="30"
                placeholder="请输入项目名称"
                clearable
              />
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="描述信息：" field="description">
              <n-textarea
                v-model="state.form.description"
                placeholder="请输入描述信息"
                maxlength="200"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
          </n-col>
        </n-row>

        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="导出数据量：" field="exportDataNum">
              <n-input
                v-model="state.form.exportDataNum"
                maxlength="30"
                placeholder="请输入导出数据量，最大限制100000"
                clearable
              />
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>
  </div>
</template>
<script>
  import { ref, reactive } from 'vue'
  import { checkCName } from '@/utils/validate'

  const checkCount = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('请输入导出数据量，最大限制100000'))
    } else {
      if (Number(value) < 1) {
        callback(new Error('需大于等于1的正整数'))
      } else if (isNaN(Number(value))) {
        callback(new Error('请输入数字'))
      } else if (Number(value) > 100000) {
        callback(new Error('最大限制100000'))
      } else {
        callback()
      }
    }
  }

  export default {
    name: '',
    setup() {
      const form = ref()
      const state = reactive({
        form: {
          projectName: '',
          description: '',
          exportDataNum: 5000,
        },
        rules: {
          projectName: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'project', 'validProject', {
                  name: state.form.projectName,
                  id: state.form?.id || null,
                }),
              trigger: 'blur',
            },
          ],
          exportDataNum: [
            {
              required: true,
              message: '请输入导出数据量，最大限制100000',
              validator: checkCount,
              trigger: 'blur',
            },
          ],
          description: [{ required: true, message: '请输入描述信息', trigger: 'blur' }],
        },
      })

      const methods = {
        // 校验是否通过
        getAllData() {
          let passed = new Promise((resolve) => {
            form.value.validate((valid) => {
              resolve({
                passed: valid,
                data: state.form,
              })
            })
          })
          return passed
        },
        // 编辑时候回显
        editInit(data) {
          let { name, description, id, exportDataNum } = data
          state.form.projectName = name
          state.form.description = description
          state.form.id = id
          state.form.exportDataNum = exportDataNum || 5000
        },
      }
      return {
        form,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  //统一表单输入框样式
  $labelWidth: 70px;
  .project-add-basic {
    width: 100%;
    height: 100%;
    color: #333;
    background: #fff;

    .nancalui-form {
      width: 100%;

      :deep(.nancalui-form__label-span) {
        color: #606266;
        font-weight: 400;
        font-size: 14px;
      }

      :deep(.nancalui-form__item--horizontal) {
        position: relative;
        display: flex;
        width: 100%;
        margin-bottom: 20px;

        .nancalui-form__control {
          width: calc(100% - #{$labelWidth});
          margin-left: 0 !important;
        }
        .nancalui-input-number {
          .nancalui-input__wrapper {
            width: 100%;
            .nancalui-input__inner {
              text-align: left;
            }
          }
        }
      }
    }
  }
</style>
