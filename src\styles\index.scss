@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './theme.scss';
@import './sidebar.scss';
@import './element-ui.scss';
@import './nancal-ul.scss';

body {
  height: 100%;
  margin: 0;
  padding: 0 !important;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial,
    sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

label {
  font-weight: 400;
}

html {
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    display: block;
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
    content: ' ';
  }
}

.cf-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(----, #606266);
  text-align: center;
  gap: 10px;

  /* 常用/r400/h9 */
  font-family: 'Source Han Sans CN';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  /* 157.143% */
  &:before {
    content: url('data:image/svg+xml;base64,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');
  }

  &:after {
    content: attr(tip);
  }
}
.container-padding16 {
  box-sizing: border-box;
  height: calc(100vh - $navbarHeight);
  padding: 16px;
  background-color: var(--themeContentBg);
  border-radius: 2px;
}
// main-container global css
.container {
  box-sizing: border-box;
  height: calc(100vh - $navbarHeight);
  padding: 16px;
  // padding-top: 0;
  background-color: var(--themeContentBg);
  border-radius: 2px;

  &.overflow {
    overflow: auto;
  }

  &-box {
    height: 100%;
    overflow: auto;
    background: #fff;

    &::-webkit-scrollbar {
      width: 6px; // 横向滚动条
      height: 6px; // 纵向滚动条 必写
    }

    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      background-color: #b1bcd6;
      border-radius: 8px;

      &:hover {
        background-color: #b1bcd6;
      }
    }
  }

  &-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    padding-right: 20px;
    line-height: 60px;
    text-align: center;
    background: #fff;
    border-top: 1px solid rgba(200, 200, 200, 0.35);

    .my-appliction-right {
      text-align: right;
    }
  }
}

/*
 修改查询条件模块样式
 */
.commonForm {
  position: relative;
  height: 32px;

  .left-btn {
    float: left;
  }

  .el-form {
    float: right;

    &-item {
      .el-date-editor {
        width: 240px;
        padding-right: 40px;
        background-image: url('/src/assets/img/icon-date-w.png');
        background-repeat: no-repeat;
        background-position: 96% 50%;
        background-size: 18px;

        &.is-active,
        &:hover {
          background-image: url('/src/assets/img/icon-date.png');
        }

        .el-range__icon {
          display: none;
        }
      }

      .el-input-group {
        .el-input-group__prepend {
          position: relative;
          width: 92px;
          padding: 0 4px 0 0;
          background-color: #fff;
          box-shadow: none;

          &:before {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            z-index: 3;
            width: 1px;
            height: 20px;
            margin: auto;
            background-color: #e1e1e1;
            content: '';
          }

          .el-select {
            width: 100%;
            margin: 0;

            .el-input__wrapper {
              width: 100%;
              padding: 0 2px 0 10px;
              box-shadow: none;
            }
          }
        }

        .el-input__wrapper {
          box-sizing: border-box;
          width: 220px;
          padding-right: 32px;
          border-right: none;
          border-radius: 4px;
        }

        .el-input-group__append {
          position: absolute;
          top: 0;
          right: 2px;
          width: 30px;
          height: 30px;
          padding: 0;
          background-color: transparent;
          border: none;
          box-shadow: none;

          .nancalui-button {
            height: 28px;
            margin: 0;
            padding: 0 5px;
            background-color: var(--themeBlue);
            border: none;
            border-radius: 2px;

            .icon_search {
              color: white;
              font-size: 20px;
            }

            & > span {
              font-size: 12px;
            }
          }
        }
      }

      .hasPrepend {
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        .el-input__wrapper {
          border-radius: 0;
          box-shadow: none;
        }
      }

      .switch {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 76px;
        height: 32px;
        padding: 2px;
        background-color: #fff;
        border: 1px solid #ebedf0;
        border-radius: 6px;

        &-label {
          width: 36px;
          height: 28px;
          color: #999;
          font-size: 14px;
          line-height: 26px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            color: #000;
          }
        }

        .checked {
          color: #000;
          background-color: #eff1f5;
        }
      }
    }

    &-item:last-of-type {
      margin-right: 0;
    }
  }

  .nancalui-form {
    display: flex;
    justify-content: space-between;
    float: right;

    .nancalui-form__item--horizontal {
      margin-right: 16px;
      margin-bottom: 0;

      .nancalui-form__label {
        flex: 0 0 auto !important;
        height: 32px;
        color: #666;
        line-height: 32px;

        .nancalui-form__label-span {
          font-weight: inherit;
        }
      }

      .nancalui-form__control {
        margin-left: 0px !important;
      }

      .nancalui-range-date-picker-pro {
        width: 240px;

        .nancalui-range-date-picker-pro__range-picker {
          width: 240px;
          height: 32px;
        }

        // .nancalui-input-slot__prefix {
        //   display: none;
        // }
        .nancalui-input__inner {
          text-align: center;
        }

        .nancalui-input__wrapper {
          font-size: 12px;
        }
      }

      .nancalui-input-slot {
        width: 220px;

        .nancalui-input__wrapper {
          box-sizing: border-box;
          padding-right: 32px;
          // border-right: none;
          font-size: 12px;
          border-radius: 4px;

          .icon-close:before {
            content: '\E071' !important;
          }
        }

        .nancalui-input-slot__append {
          position: absolute;
          top: 0;
          right: 2px;
          width: 30px;
          height: 30px;
          padding: 0;
          background-color: transparent;
          border: none;
          box-shadow: none;

          .nancalui-button {
            height: 28px;
            margin: 0;
            padding: 0 5px;
            background-color: var(--themeBlue) !important;
            border: none;
            border-radius: 2px;

            .icon_search {
              color: white;
              font-size: 20px;
            }

            & > span {
              font-size: 12px;
            }
          }
        }
      }

      .hasPrepend {
        border: 1px solid #dcdfe6;
        border-radius: 4px;

        .el-input__wrapper {
          border-radius: 0;
          box-shadow: none;
        }
      }

      .switch {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 76px;
        height: 32px;
        padding: 2px;
        background-color: #fff;
        border: 1px solid #ebedf0;
        border-radius: 6px;

        &-label {
          width: 36px;
          height: 28px;
          color: #999;
          font-size: 14px;
          line-height: 26px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            color: #000;
          }
        }

        .checked {
          color: #000;
          background-color: #eff1f5;
        }
      }
    }

    .nancalui-form__item--horizontal:last-of-type {
      margin-right: 0;
    }

    .icon-close:before {
      content: '\E071' !important;
    }
  }
}

.icon-add-svg {
  margin-right: 4px;
  font-size: 16px;
}

.icon-add-overview {
  margin-right: 4px;
  font-size: 14px;
}

.font12 {
  font-size: $themeFont;
}

.icon-arrow-down:before {
  content: '' !important;
}

.icon-add:before {
  content: '' !important;
}

//.icon-close:before {
//  content: '' !important;
//}
// 统一滚动条样式 class 名字加 scroll-bar-style
.scroll-bar-style::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 滚动条的滑块
.scroll-bar-style::-webkit-scrollbar-thumb {
  background-color: #b1bcd6;
  border-radius: 8px;

  &:hover {
    background-color: #b1bcd6;
  }
}

// 统一滚动条样式 class 名字加 scroll-bar-style
::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #b1bcd6;
  border-radius: 8px;

  &:hover {
    background-color: #b1bcd6;
  }
}

// 表格右侧按钮中间线
.table_center_border {
  display: inline-block;
  width: 1px;
  height: 10px;
  margin: 0 10px;
  background-color: #cfcfcf;
}

// 去掉 type="number" 后面的箭头
/*添加css样式*/
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}

// 标题头部小方块样式
.page_header_common_style {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 52px;
  margin: 2px auto;
  padding: 0 16px;
  background: #fff;
  // border-bottom: 1px solid var(---, #c5d0ea);

  &_icon {
    position: relative;
    top: -2px;
    margin-right: 8px;
    font-size: 16px;
  }

  &_close {
    position: absolute;
    right: 16px;
    width: 24px !important;
    height: 24px !important;
    cursor: pointer;
  }

  span {
    color: var(----, rgba(0, 0, 0, 0.9));
    font-weight: bolder;
    font-size: 18px;
    font-style: normal;
  }
}

.need_smallcube__title {
  display: block;
  height: 14px;
  padding-left: 6px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bolder;
  font-size: 14px;
  font-size: 14px;
  line-height: 14px;
  border-left: 4px solid var(--themeBlue);
}

.common-section-header {
  position: relative;
  height: 52px;
  padding: 0px 16px;
  color: var(----, rgba(0, 0, 0, 0.9));
  font-weight: bolder;
  font-size: 18px;
  font-family: 'Source Han Sans CN';
  font-style: normal;
  line-height: 52px;
  border-bottom: 1px solid $nancaluiSplitLineBorder;

  &:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    width: 4px;
    height: 18px;
    background: #1e89ff;
    content: '';
  }
}

.add-box-top-title {
  position: relative;
  margin-bottom: 8px;
  background: var(--100, #fff);
  border-radius: 8px;

  // box-shadow: 0px 2px 8px -2px rgba(0, 0, 0, 0.1);
  .common-section-header {
    border: none !important;

    &.has-bottom-border {
      border-bottom: 1px solid $nancaluiSplitLineBorder !important;
    }
  }

  .detail-back-box {
    position: absolute;
    top: 0;
    right: 16px;
    bottom: 0;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 127px;
    height: 33px;
    margin: auto;
    background-color: #fff;
    border: 1px solid #a3b4db;
    border-radius: 8px;
    box-shadow: 0px 2px 8px -2px rgba(0, 0, 0, 0.1);

    &:before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 50%;
      width: 1px;
      height: 16px;
      margin: auto;
      background-color: #c5d0ea;
      content: '';
    }

    &-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50%;
      color: rgba(0, 0, 0, 0.46);
      font-size: 14px;
      cursor: pointer;

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
        color: #a3b4db;
        font-size: 16px;
      }

      &.checked {
        color: $themeBlue;

        .icon {
          color: $themeBlue;
        }

        &:hover {
          color: #6e9eff;
        }

        &:active {
          color: #2f5cd6;
        }
      }

      &:hover {
        color: #6e9eff;
      }

      &:active {
        color: #2f5cd6;
      }
    }
  }
}

//抽屉板样式
.params-info {
  display: flex;
  padding: 0 0 10px 0;

  .nancalui-button {
    width: 88px;
    height: 32px;
    border: 1px solid #e1e1e1;
    border-radius: 0;

    &.active {
      color: $themeBlue;
      background: #f0f7ff;
      border: 1px solid $themeBlue;
    }

    > span {
      font-size: 13px;
    }
  }

  .nancalui-button + .nancalui-button {
    margin: 0;
  }

  div {
    padding-right: 20px;
    cursor: pointer;
  }
}

.params-content {
  .common-table {
    font-size: 200px;
  }
}
//抽屉底部
.drawerFooter {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
.test-api-box-bottom {
  height: 100%;
  font-size: 12px;

  .title {
    height: 57px;
    padding: 10px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }

  .back-content {
    display: flex;
    height: calc(100% - 80px);
    font-weight: 400px;

    .content-left {
      width: 260px;

      .code {
        padding-bottom: 5px;
        line-height: 20px;
        border-bottom: 1px solid #e1e1e1;
      }

      .code-number {
        padding-top: 20px;
      }
    }

    .content-right {
      flex: 1;

      .back-title {
        padding-bottom: 5px;
        line-height: 20px;
        border-bottom: 1px solid #e1e1e1;
      }

      .back-data-box {
        height: calc(100% - 20px);
        padding: 10px 0;

        .back-data {
          height: 100%;
          overflow: auto;
          // background: #f4f4f4;
          // border-radius: 4px;

          & > div {
            height: 100%;
          }

          .jv-container.jv-light {
            height: calc(100% - 20px);
            padding: 10px 0;
            background-color: #f4f4f4;

            .jv-code {
              overflow: auto;

              &.boxed {
                height: calc(100% - 100px);
                max-height: 600px;
              }
            }
          }
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 6px !important; // 横向滚动条
  height: 6px !important; // 纵向滚动条 必写
}

.nc-flex {
  display: flex;

  .nc-right {
    margin-left: auto;
  }
}

// 定义显示几行
@for $i from 1 through 5 {
  .nc-line-#{$i} {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-line-clamp: $i;
  }
}

// 定义内外边距
@for $i from 1 through 100 {
  // margin
  .nc-m-#{$i} {
    margin: $i + px !important;
  }

  // padding
  .nc-p-#{$i} {
    padding: $i + px !important;
  }

  // 缩写 left, top, right, bottom
  @each $short, $long in l left, t top, r right, b bottom {
    // 定义外边距
    .nc-m-#{$short}-#{$i} {
      margin-#{$long}: $i + px !important;
    }

    // 定义内边距
    .nc-p-#{$short}-#{$i} {
      padding-#{$long}: $i + px !important;
    }
  }
}

.pointer,
svg {
  cursor: pointer;
}

.white-box {
  background: #fff;
  height: calc(100% - 56px);
  overflow: scroll;
}
