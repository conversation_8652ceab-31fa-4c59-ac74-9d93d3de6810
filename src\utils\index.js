import { cloneDeep } from 'lodash'
export function parseTime(time, cFormat) {
  let timer = time
  if (arguments.length === 0 || !timer) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        // eslint-disable-next-line radix
        timer = parseInt(timer)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        timer = timer.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof timer === 'number' && timer.toString().length === 10) {
      timer = timer * 1000
    }
    date = new Date(timer)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

export function base64toFile(urlString, fileName) {
  const dataArr = urlString.split(',')
  const byteString = atob(dataArr[1])
  const options = {
    type: 'image/png',
    endings: 'native',
  }
  const u8Arr = new Uint8Array(byteString.length)
  for (let i = 0; i < byteString.length; i++) {
    u8Arr[i] = byteString.charCodeAt(i)
  }
  return new File([u8Arr], `${fileName}.png`, options)
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  let timer = time
  if (String(time).length === 10) {
    // eslint-disable-next-line radix
    timer = parseInt(timer) * 1000
  } else {
    timer = Number(timer)
  }
  const d = new Date(timer)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

export function formartTime(time, type) {
  let t = time.toLocaleDateString()
  // eslint-disable-next-line prefer-regex-literals
  let reg = new RegExp('/', 'g')
  t = t.replace(reg, '-')
  let timer = type ? t + ' 23:59:59' : t + ' 00:00:00'
  return timer
}

export function formartTimeDate(time = new Date(), split = '/', needTime = false) {
  if (!time) {
    return ''
  }
  if (typeof time === 'number') {
    time = new Date(time)
  }

  let year = time.getFullYear()
  let mon = time.getMonth() + 1
  let day = time.getDate()
  let hour = time.getHours()
  let minute = time.getMinutes()
  let second = time.getSeconds()
  let submitTime = ''
  submitTime += year + split
  if (mon >= 10) {
    submitTime += mon + split
  } else {
    submitTime += '0' + mon + split
  }
  if (day >= 10) {
    submitTime += day
  } else {
    submitTime += '0' + day
  }
  if (needTime) {
    if (hour < 10) {
      hour = '0' + hour
    }
    if (minute < 10) {
      minute = '0' + minute
    }
    if (second < 10) {
      second = '0' + second
    }
    submitTime += ' ' + hour + ':' + minute + ':' + second
  }

  return submitTime
}

export function randomGuid() {
  const letters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let guid = ''
  for (let i = 0; i < 20; i += 1) {
    guid += letters[Math.floor(Math.random() * 62)]
  }
  return guid
}
export function uuid() {
  let res = ''
  const template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'
  for (let i = 0, len = template.length; i < len; i += 1) {
    const s = template[i]
    const r = (Math.random() * 16) | 0
    const v = s === 'x' ? r : s === 'y' ? (r & 0x3) | 0x8 : s
    res += v.toString(16)
  }
  return res
}
export function getConHeight(arrs) {
  let count = 0
  const codeList = localStorage.getItem('codeList').split(',') || []
  arrs.forEach((item) => {
    if (codeList.includes(item)) {
      count++
    }
  })
  return count * 40 + 30
}
// 请求接口的操作方式
export function getOperation(config) {
  const { url, method, responseType } = config
  const URL = url?.toLowerCase()
  const METHOD = method?.toLowerCase()
  const RESPONSETYPE = responseType?.toLowerCase()
  let operation = ''
  // 插入日志的接口不统计
  if (['/base/logging/v1/end', '/base/operatorlog/insert'].includes(url)) {
    return false
  }
  // 删除
  if (URL.includes('delete') || METHOD === 'delete') {
    operation = '删除'
  } else if (URL.includes('template') && RESPONSETYPE === 'blob') {
    operation = '下载模板'
  } else if (URL.includes('export') && RESPONSETYPE === 'blob') {
    operation = '导出'
  } else if ((URL.includes('update') && METHOD === 'post') || METHOD === 'put') {
    operation = '修改'
  } else if (URL.includes('insert') || URL.includes('add')) {
    operation = '新增'
  } else if (URL.includes('checkIn') || URL.includes('check/in')) {
    operation = '检入'
  } else if (URL.includes('aduit') || URL.includes('publish')) {
    operation = '送审'
  } else if (URL.includes('adminlogin')) {
    operation = '登录'
  } else {
    operation = '查询'
  }
  return operation
}
// 截取展示:str字符串，length:超过长度进行截取
export function substrLen(str, length) {
  let res = str
  if (str?.length > length) {
    res = str.substring(0, length - 1) + '...'
  }
  return res
}
let timer
export function debounce(cb, time) {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    cb()
  }, time)
}

export const confidentialityLevelOptions = [
  {
    name: '公开',
    value: 'PUBLIC',
    style: {
      color: '#1aa4ee',
      'background-color': 'rgba(26, 164, 238, 0.08)',
      border: '1px solid rgba(26, 164, 238, 0.4)',
    },
  },
  {
    name: '内部',
    value: 'INTERIOR',
    style: { color: '#fe8624', 'background-color': '#fff4e6', border: '1px solid #ffba70' },
  },
  {
    name: '受控',
    value: 'CONTROLLED',
    style: { color: '#1e89ff', 'background-color': '#ebf4ff', border: '1px solid #99c9ff' },
  },
  {
    name: '秘密',
    value: 'SECRET',
    style: { color: '#d40000', 'background-color': '#ffeded', border: '1px solid #ef7777' },
  },
  {
    name: '机密',
    value: 'CONFIDENTIAL',
    style: {
      color: '#7a0000',
      'background-color': 'rgba(122, 0, 0, 0.08)',
      border: '1px solid rgba(122, 0, 0, 0.4)',
    },
  },
  {
    name: '核心',
    value: 'CORE',
    style: {
      color: '#224ecd',
      'background-color': 'rgba(34, 78, 205, 0.08)',
      border: '1px solid rgba(34, 78, 205, 0.4)',
    },
  },
]

// 调度频率转化
export function changeFrequencyChinese(data, rateTime = false) {
  let words = '--'
  let rateTime_words = data?.schedule?.fromDateTime
    ? data?.schedule?.fromDateTime + ' - ' + data?.schedule?.thruDateTime
    : '--'
  if (data?.schedule?.isCron) {
    words = data?.schedule?.cron
  } else {
    if (data.schedule?.period) {
      data.schedule.period = data.schedule.period.toUpperCase()
    }
    switch (data?.schedule?.period) {
      case 'HOUR':
        words = `间隔${data?.schedule?.extent}小时`
        break
      case 'DAY':
        words = `每日 | ${data?.schedule?.rateTime}`
        break
      case 'WEEK':
        words = `每周${weekForChinese(data?.schedule?.extent)} | ${data?.schedule?.rateTime}`
        break
      case 'MONTH':
        words = `每月${data?.schedule?.extent}号 | ${data?.schedule?.rateTime}`
        break
    }
  }
  return rateTime ? rateTime_words : words
}
// 星期数字转化中文
export function weekForChinese(extent) {
  let num = Number(extent)
  let word = '一'
  switch (num) {
    case 1:
      word = `一`
      break
    case 2:
      word = `二`
      break
    case 3:
      word = `三`
      break
    case 4:
      word = `四`
      break
    case 5:
      word = `五`
      break
    case 6:
      word = `六`
      break
    case 7:
      word = `日`
      break
  }
  return word
}
// 对象===>数组
export function objToArray(obj) {
  let dataSource = [
    {
      ...obj?.root,
      properties: undefined,
      children: [],
    },
  ]
  if (obj?.root?.properties) {
    deepToArray(dataSource[0].children, obj.root.properties)
  }
  return dataSource
}
function deepToArray(data, properties) {
  let index = 0
  for (let key of Object.keys(properties)) {
    const value = properties[key]
    data.push({
      ...value,
      properties: undefined,
      children: [],
    })
    if (value.properties) {
      deepToArray(data[index].children, value.properties)
    }
    index++
  }
  return data
}
// 下载文件
export function downLoadFile(blob, fileName) {
  const link = document.createElement('a')
  link.download = decodeURI(fileName)
  link.style.display = 'none'
  link.href = URL.createObjectURL(blob)
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}
export function polyfill() {
  fixArray('at')
  structuredClone()
}
function structuredClone() {
  if (!window?.structuredClone) {
    window.structuredClone = (obj) => {
      return cloneDeep(obj)
    }
  }
}
function fixArray(type) {
  if (!Array.prototype.at && (type === 'at' || type === 'all')) {
    Object.defineProperty(Array.prototype, 'at', {
      value: function (index) {
        if (index >= 0) {
          return this[index]
        } else {
          return this[this.length + index]
        }
      },
      writable: true,
      configurable: true,
      enumerable: false, // 确保不破坏 for...in 循环
    })
  }
}
