<template>
  <div v-if="state.tagList?.length > 0" ref="confidentiality" class="confidentiality-level">
    <div class="confidentiality-level-left">
      <template v-for="(item, index) in state.tagList" :key="index">
        <div
          v-if="index <= state.separateIndex"
          class="confidentiality-level-label"
          :style="
            'color:' +
            item.color.split('_')[0] +
            ';background-color:' +
            item.color.split('_')[1] +
            ';border-color:' +
            (item.color.split('_').length > 2 ? item.color.split('_')[2] : item.color.split('_')[0])
          "
          >{{ item.text }}</div
        >
      </template>
    </div>
    <n-popover
      v-if="state.tagList.length - 1 > state.separateIndex"
      :position="['top']"
      :offset="6"
      trigger="hover"
    >
      <template #content>
        <div class="confidentiality-level-label-more">
          <template v-for="(item, index) in state.tagList" :key="index">
            <div
              v-if="index > state.separateIndex"
              class="confidentiality-level-label"
              :style="
                'color:' +
                item.color.split('_')[0] +
                ';background-color:' +
                item.color.split('_')[1] +
                ';border-color:' +
                (item.color.split('_').length > 2
                  ? item.color.split('_')[2]
                  : item.color.split('_')[0])
              "
              >{{ item.text }}</div
            >
          </template>
        </div>
      </template>
      <div class="confidentiality-level-label tip"
        ><SvgIcon class="icon" icon="icon-more-tag"
      /></div>
    </n-popover>
    <div v-if="state.tagList.length === 0">--</div>
  </div>
  <div v-else ref="confidentiality" class="confidentiality-level">--</div>
</template>

<script setup>
  import { reactive, defineProps, onMounted, watch, ref, nextTick } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  const props = defineProps({
    tagArr: {
      type: Object,
      default: '[]',
    },
  })
  const confidentiality = ref(null)
  const state = reactive({
    tagList: [],
    separateIndex: 0,
  })

  const checkWidthFn = () => {
    let tagParentWidth = confidentiality?.value?.offsetWidth - 46
    let tagChildWidth = 0
    let hasCheckIndex = false
    state.tagList.forEach((val, ind) => {
      tagChildWidth = tagChildWidth + val.text.length * 12 + 22
      if (tagChildWidth >= tagParentWidth && !hasCheckIndex) {
        state.separateIndex = ind - 1
        hasCheckIndex = true
      }
    })
    if (tagChildWidth < tagParentWidth) {
      state.separateIndex = state.tagList.length - 1
    }
  }

  watch(
    () => props.tagArr,
    (newVal) => {
      state.tagList = JSON.parse(newVal || '[]')
      setTimeout(() => {
        checkWidthFn()
      }, 60)
    },
  )
  onMounted(() => {
    state.tagList = JSON.parse(props.tagArr || '[]')
    setTimeout(() => {
      checkWidthFn()
    }, 60)
    useResizeObserver(confidentiality, (entries) => {
      setTimeout(() => {
        checkWidthFn()
      }, 60)
    })
  })
</script>

<style lang="scss" scoped>
  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: calc(100% - 40px);
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        background-color: #f5f7fa;
        border: 1px solid #cbd2e3;
        cursor: pointer;
        .icon {
          font-size: 14px;
        }
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
      margin-left: 0;
    }
  }
</style>
