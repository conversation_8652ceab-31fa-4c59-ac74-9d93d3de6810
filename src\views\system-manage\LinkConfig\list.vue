<template>
  <div :class="{ 'data-collection-page-out-box': true, isLzos: state.isLzos }">
    <section class="tools">
      <div class="row">
        <div class="col">
          <span class="label">链接名称：</span>
          <n-input
            v-model="state.originalFormInline.keyword"
            placeholder="链接名称"
            size="small"
            clearable
          />
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="methods.searchClickFn">查询</div>
          <div class="search-btn reset" @click.prevent="methods.resetFn">重置</div>
        </div>
      </div>
    </section>
    <div class="content">
      <div
        :class="{
          'data-collection-page': true,
          container: true,
          table: true,
        }"
      >
        <div class="data-collection-page-content">
          <div class="out-box">
            <div class="out-box-title"
              ><n-button
                v-if="
                  buttonAuthList.includes('systemManage_linkConfig_add') ||
                  roleCode === 1 ||
                  roleCode === 2 ||
                  roleCode === 3
                "
                variant="solid"
                @click.prevent.stop="methods.addFn"
                ><SvgIcon icon="new-add" class="icon" title="新建" />新建</n-button
              ></div
            >
            <div class="table-list dif" v-loading="state.loading">
              <CfTable
                saveWidth
                :isDisplayAction="true"
                :table-head-titles="state.tableHeadTitles"
                :paginationConfig="{
                  total: state.pageInfo.total,
                  pageSize: state.pageInfo.pageSize,
                  currentPage: state.pageInfo.currentPage,
                  onCurrentChange: (v) => {
                    state.pageInfo.currentPage = v
                    methods.initTable()
                  },
                  onSizeChange: (v) => {
                    state.pageInfo.pageSize = v
                    methods.initTable()
                  },
                }"
                :tableConfig="{
                  data: state.tableData.list,
                  rowKey: 'id',
                }"
                :actionWidth="180"
              >
                <template #editor="{ row }">
                  <div class="edit-box">
                    <n-button
                      v-if="
                        buttonAuthList.includes('systemManage_linkConfig_edit') ||
                        roleCode === 1 ||
                        roleCode === 2 ||
                        roleCode === 3
                      "
                      class="has-right-border"
                      code="systemManage_linkConfig_edit"
                      variant="text"
                      @click.prevent="methods.editFn(row)"
                      >编辑</n-button
                    >
                    <n-button
                      v-if="
                        buttonAuthList.includes('systemManage_linkConfig_delete') ||
                        roleCode === 1 ||
                        roleCode === 2 ||
                        roleCode === 3
                      "
                      class="has-right-border"
                      code="systemManage_linkConfig_delete"
                      variant="text"
                      @click.prevent="methods.delFn(row)"
                      >删除</n-button
                    >
                  </div>
                </template>
              </CfTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--弹窗-->
    <n-modal
      v-model="state.editDialog"
      title="链接配置"
      class="largeDialog has-top-padding"
      width="545px"
      :close-on-click-overlay="false"
      @close="state.editDialog = false"
    >
      <n-form
        ref="editForm"
        :data="state.editForm"
        :rules="state.editRules"
        label-align="start"
        label-width="135px"
      >
        <n-form-item label="链接类型：" field="linkType">
          <n-radio-group v-model="state.editForm.linkType" direction="row">
            <n-radio value="PROCESS">流程</n-radio>
            <n-radio value="FUNCTION">功能</n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="链接分类：" field="landingPageId">
          <n-select
            v-model="state.editForm.landingPageId"
            placeholder="请选择链接分类"
            :options="state.typeList"
            filter
          />
        </n-form-item>
        <n-form-item label="链接名称：" field="name">
          <n-input v-model="state.editForm.name" maxlength="50" placeholder="请输入链接名称" />
        </n-form-item>
        <n-form-item label="链接地址：" field="jumpUrl">
          <n-input v-model="state.editForm.jumpUrl" maxlength="1024" placeholder="请输入链接地址" />
        </n-form-item>
        <n-form-item label="上传文件：" field="logoUrl">
          <div class="upload">
            <n-upload accept=".jpg,.png" droppable :before-upload="methods.beforeUpload">
              <div class="upload-box">
                <img class="pic" v-if="state.editForm.logoUrl" :src="state.editForm.logoUrl" />
                <SvgIcon v-else class="icon" icon="new-add" title="上传" />
              </div>
            </n-upload>
            <div class="upload-desc">
              <div class="upload-desc-top">
                <n-dropdown
                  :visible="state.showIconPopover"
                  :show-animation="false"
                  close-scope="blank"
                  class="link-icon-dropdown"
                  :position="['bottom-start']"
                  align="start"
                  trigger="manually"
                  @toggle="
                    (flag) => {
                      state.showIconPopover = flag
                    }
                  "
                >
                  <div @click.prevent.stop="state.showIconPopover = !state.showIconPopover"
                    >图标库</div
                  >
                  <template #menu>
                    <div class="icon-dropdown">
                      <div class="icon-dropdown-close"
                        ><SvgIcon
                          class="icon"
                          icon="icon-close"
                          title="关闭"
                          @click.prevent.stop="state.showIconPopover = false"
                      /></div>
                      <div class="icon-dropdown-content">
                        <img
                          v-for="(item, index) in state.iconList"
                          :key="index"
                          class="pic"
                          :src="item.logoUrl"
                          style="background-color: #64bfd5; border-radius: 5px"
                          @click.prevent.stop="
                            () => {
                              state.editForm.logoUrl = item.logoUrl
                              state.editForm.bucketName = item.bucketName
                              state.editForm.logoObj = item.objName
                              state.showIconPopover = false
                            }
                          "
                        />
                      </div>
                    </div>
                  </template>
                </n-dropdown>
              </div>
              <div class="upload-desc-bottom">图标文件格式限PNG、JPG，建议大小56*56</div>
            </div>
          </div>
        </n-form-item>
        <n-form-item label="验证方式：" field="type">
          <n-radio-group v-model="state.editForm.type" direction="row">
            <n-radio value="TOKEN">token</n-radio>
            <n-radio value="KEY">key</n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item label="展示顺序：" field="sort">
          <n-input
            v-model="state.editForm.sort"
            @keydown="onKeydownPositiveInteger($event, state.editForm.ordinal)"
            @keyup="onKeyupPositiveInteger($event, state.editForm.ordinal)"
            @blur="methods.fieldBlur('ordinal')"
            :onpaste="
              () => {
                return false
              }
            "
            placeholder="请输入展示顺序"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <div class="nancalui-modal__footer">
          <n-button @click.prevent="state.editDialog = false">取 消</n-button>
          <n-button v-loading="state.editLoading" variant="solid" @click.prevent="methods.saveFn"
            >确 定</n-button
          >
        </div>
      </template>
    </n-modal>
  </div>
</template>
<script setup>
  import { reactive, onMounted, toRefs, ref } from 'vue'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { onKeydownPositiveInteger, onKeyupPositiveInteger } from '@/utils/validate'
  import { useRouter } from 'vue-router'
  const { proxy } = getCurrentInstance()
  import { ElNotification } from 'element-plus'
  const store = useStore()
  //按钮权限
  const { buttonAuthList, roleCode } = toRefs(store.state.user)
  const router = useRouter()
  const editForm = ref()

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    tableData: {},
    tableHeight: 436,
    key: 1,
    loading: false,
    editDialog: false,
    showIconPopover: false,
    nodeInfo: {},
    editForm: {
      id: null,
      name: '',
      jumpUrl: '',
      type: 'TOKEN',
      linkType: 'PROCESS',
      landingPageId: '',
      logoUrl: '',
      sort: '',
      bucketName: '',
      logoObj: '',
    },
    editRules: {
      name: [{ required: true, message: '请输入链接名称', trigger: 'blur' }],
      linkType: [{ required: true, message: '请选择链接类型', trigger: 'change' }],
      landingPageId: [
        { required: true, message: '请选择链接分类', type: 'number', trigger: 'change' },
      ],
      type: [{ required: true, message: '请选择验证方式', trigger: 'change' }],
      jumpUrl: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
      logoUrl: [{ required: true, message: '请上传图标', trigger: 'blur' }],
      sort: [{ required: true, message: '请输入序号', trigger: 'blur' }],
    },
    typeList: [],
    originalFormInline: {
      keyword: null,
    },
    formInline: {
      keyword: null,
    },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'linkTypeName', name: '链接类型' },
      { prop: 'landingPageName', name: '链接分类' },
      { prop: 'name', name: '链接名称' },
      { prop: 'jumpUrl', name: '链接地址' },
      { prop: 'type', name: '验证方式' },
      { prop: 'sort', name: '展示顺序' },
    ],
    iconList: [],
  })
  const methods = {
    setTableHeight() {
      state.tableHeight = document.body.offsetHeight - 294
    },
    fieldBlur(key) {
      state.editForm[key] = state.editForm[key].replace(/\D/g, '')
    },
    getIconListFn() {
      api.system.externalLinkDefaultIconNew().then((res) => {
        if (res.success) {
          state.iconList = res.data
        }
      })
    },
    getTypeListFn() {
      api.system.externalLinkDefaultType().then((res) => {
        if (res.success) {
          res.data.forEach((val) => {
            val.value = val.id
          })
          state.typeList = res.data.filter((val) => val?.type !== 'ALL')
        }
      })
    },
    beforeUpload(UploadRawFile) {
      let size = UploadRawFile[0].file.size / 1048576 // 单位M
      if (size > 100) {
        ElNotification({
          title: '提示',
          message: '文件不能超过100M！',
          type: 'warning',
        })
        return false
      }
      const formData = new FormData()
      formData.append('file', UploadRawFile[0].file)
      formData.append('bucket', 'data-govern')
      api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
        let { success, data } = res
        if (success) {
          state.editForm.logoUrl = data.url
          state.editForm.logoObj = data.objName
          state.editForm.bucketName = data.bucketName
        }
      })
      return false
    },
    addFn() {
      state.editForm = {
        id: null,
        name: '',
        jumpUrl: '',
        linkType: 'PROCESS',
        type: 'TOKEN',
        landingPageId: '',
        logoUrl: '',
        sort: '',
        bucketName: '',
        logoObj: '',
      }
      state.editDialog = true
    },
    // 删除
    delFn(item) {
      proxy.$MessageBoxService.open({
        title: '删除链接',
        content: '删除将会导致该链接不可用，是否继续删除？',
        save: () => {
          api.system.externalLinkDeleteNew({ id: item.id }).then((res) => {
            if (res.code === 'SUCCESS') {
              ElNotification({
                title: '提示',
                message: '删除成功',
                type: 'success',
              })
              methods.initTable(true)
            }
          })
        },
      })
    },
    // 编辑
    editFn(item) {
      api.system.externalLinkDetail({ id: item.id }).then((res) => {
        if (res.success) {
          state.editForm = {
            ...res.data,
            sort: String(res.data.sort),
            linkType: res.data.linkType || 'PROCESS',
          }
          state.editDialog = true
        }
      })
    },
    saveFn() {
      editForm.value.validate((val) => {
        if (val) {
          let interfaceUrl = 'externalLinkAdd'
          if (state.editForm.id) {
            interfaceUrl = 'externalLinkUpdateNew'
          }
          api.system[interfaceUrl]({ ...state.editForm }).then((res) => {
            if (res.success) {
              ElNotification({
                title: '提示',
                message: '保存成功',
                type: 'success',
              })
              state.editDialog = false
              methods.initTable(state.editForm.id ? false : true)
            }
          })
        }
      })
    },
    // 重置
    resetFn() {
      state.originalFormInline = {
        keyword: null,
      }
      methods.searchClickFn()
    },
    searchClickFn() {
      for (let key in state.originalFormInline) {
        state.formInline[key] = state.originalFormInline[key]
      }
      methods.initTable(true)
    },
    //搜索
    initTable(init = false) {
      state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
      let data = {
        pageNum: state.pageInfo.currentPage,
        pageSize: state.pageInfo.pageSize,
        condition: {
          name: state.formInline.keyword || null,
        },
      }
      state.loading = true
      api.system
        .externalLinkSearchList(data)
        .then((res) => {
          state.loading = false
          if (res.success) {
            res.data.list.forEach((val) => {
              val.linkTypeName = val.linkType
                ? val.linkType === 'FUNCTION'
                  ? '功能'
                  : '流程'
                : '--'
            })
            state.tableData = res.data
            state.pageInfo.total = res.data.total
            state.key++
          }
        })
        .catch(() => {
          state.tableData = {}
          state.loading = false
        })
    },
    // 列表（表格）操作变化
    tablePageChange(data) {
      state.pageInfo.currentPage = data.currentPage
      state.pageInfo.pageSize = data.pageSize
      methods.initTable()
    },
  }

  onMounted(() => {
    methods.setTableHeight()
    methods.initTable(true)
    methods.getIconListFn()
    methods.getTypeListFn()
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .data-collection-page-out-box {
    box-sizing: border-box;
    height: calc(100vh - 90px);
    padding: 16px;
    .tools {
      height: 50px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 2px;
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        padding: 0 16px;

        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          width: 100%;
          height: 52px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
          }

          &-btn {
            position: absolute;
            right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            color: $themeBlue;
            font-weight: 400;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: #e3ecff;
            }
          }
        }
        &.date {
          height: 36px;
          :deep(.nancalui-range-date-picker-pro.nancalui-range-date-picker-pro__range-width) {
            width: 260px;
          }
        }
        .col {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
          }
        }
        &.tabs {
          align-items: flex-end;
          height: 48px;
          :deep(.nancalui-tabs) {
            .nancalui-tabs-nav-tab {
              border-bottom: none;
            }
          }
        }
        :deep(.button-content) {
          .add {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 4px;
            }
            .arrow {
              margin-left: 4px;
              color: #fff;
              font-size: 16px;
            }
          }
        }

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32px;
          padding: 0 8px;
          color: $themeBlue;
          font-weight: 400;
          font-size: 14px;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            background-color: #ecf7ff;
          }

          .icon {
            margin-right: 4px;
          }
        }

        .nancalui-input,
        .nancalui-select {
          width: 260px;
          margin-right: 32px;
        }

        .search {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          &-btn {
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid #1e89ff;
            border-radius: 2px;
            cursor: pointer;
            background-color: #1e89ff;

            &.reset {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 8px;
              color: #1d2129;
              background-color: #fff;
              border: 1px solid #dcdfe6;
              &:hover {
                color: #479dff;
                background-color: #fff;
                border: 1px solid #479dff;
              }
              .icon {
                margin-left: 4px;
                font-size: 10px;
              }
            }

            &:hover {
              background-color: #479dff;
              border: 1px solid #479dff;
            }
          }
        }
      }
    }
    .content {
      height: calc(100% - 60px);
      margin-top: 10px;
      overflow: hidden;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 0;
    }
  }
  .data-collection-page {
    box-sizing: border-box;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 0;
    padding: 0;
    .data-collection-page-content {
      width: 100%;
      height: 100%;
      border-radius: 2px;
      background-color: #fff;
      overflow: hidden;
      .out-box {
        height: 100%;
        &-title {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          height: 46px;
          padding: 0 16px;
          :deep(.nancalui-button) {
            .button-content {
              display: flex;
              justify-content: center;
              align-items: center;
              .icon {
                font-size: 16px;
                margin-right: 4px;
              }
            }
          }
        }
      }
      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 46px);
        padding: 0;

        .empty {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 266px;
          height: 180px;
          margin: auto;

          &-img {
            display: block;
            width: 140px;
            height: auto;
            margin: 0 auto;
          }

          &-text {
            margin-top: 20px;
            color: #999999;
            font-size: 12px;
            text-align: center;
          }
        }
      }
      .nancalui-table-page {
        flex-direction: column;
        height: 34px;
        padding: 0 16px;
      }
      :deep(.nancalui-table-page) {
        padding: 16px;
      }

      .project-desc {
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 4px 4px 0 0;

        .content {
          color: #333333;
          font-weight: 600;
          font-size: 14px;

          i {
            padding: 0 10px;
          }
        }
      }
    }
  }
  .upload {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    &-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 66px;
      height: 66px;
      background-color: #ffffff;
      border: 1px dashed #c9cdd4;
      border-radius: 4px;
      cursor: pointer;
      &:has(.pic) {
        border: 1px solid #c5d0ea;
      }
      .icon {
        color: #8091b7;
        font-size: 24px;
      }
      .pic {
        width: 60px;
        height: 60px;
        background-color: #64bfd5;
        border-radius: 5px;
      }
    }
    &-desc {
      height: 66px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: column;
      margin-left: 9px;
      &-top {
        color: #1e89ff;
        font-size: 14px;
        cursor: pointer;
        position: relative;
      }
      &-bottom {
        color: #a8abb2;
        font-size: 14px;
      }
    }
  }
</style>
