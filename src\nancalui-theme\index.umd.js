(function(E,B){typeof exports=="object"&&typeof module!="undefined"?B(exports):typeof define=="function"&&define.amd?define(["exports"],B):(E=typeof globalThis!="undefined"?globalThis:E||self,B(E.nancaluiTheme={}))})(this,function(E){"use strict";var Xn=Object.defineProperty;var Zn=(E,B,_)=>B in E?Xn(E,B,{enumerable:!0,configurable:!0,writable:!0,value:_}):E[B]=_;var C=(E,B,_)=>(Zn(E,typeof B!="symbol"?B+"":B,_),_);function B(t,n){return n.forEach(function(e){e&&typeof e!="string"&&!Array.isArray(e)&&Object.keys(e).forEach(function(a){if(a!=="default"&&!(a in t)){var r=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,r.get?r:{enumerable:!0,get:function(){return e[a]}})}})}),Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}class _{constructor(n){C(this,"id");C(this,"name");C(this,"cnName");C(this,"data");C(this,"extends");C(this,"isDark");C(this,"isPreview");C(this,"isExtendable");C(this,"extra");this.id=n.id,this.name=n.name,this.cnName=n.cnName||this.name,this.data=n.data,this.extends=n.extends||null,this.isDark=n.isDark||void 0,this.isPreview=n.isPreview||!1,this.isExtendable=n.isExtendable||!0}}/*!
 * css-vars-ponyfill
 * v2.4.8
 * https://jhildenbiddle.github.io/css-vars-ponyfill/
 * (c) 2018-2022 John Hildenbiddle <http://hildenbiddle.com>
 * MIT license
 */function T(){return T=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a])}return t},T.apply(this,arguments)}/*!
 * get-css-data
 * v2.1.0
 * https://github.com/jhildenbiddle/get-css-data
 * (c) 2018-2022 John Hildenbiddle <http://hildenbiddle.com>
 * MIT license
 */function Oe(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e={mimeType:n.mimeType||null,onBeforeSend:n.onBeforeSend||Function.prototype,onSuccess:n.onSuccess||Function.prototype,onError:n.onError||Function.prototype,onComplete:n.onComplete||Function.prototype},a=Array.isArray(t)?t:[t],r=Array.apply(null,Array(a.length)).map(function(c){return null});function o(c){var l=typeof c=="string",s=l&&c.trim().charAt(0)==="<";return l&&!s}function i(c,l){e.onError(c,a[l],l)}function d(c,l){var s=e.onSuccess(c,a[l],l);c=s===!1?"":s||c,r[l]=c,r.indexOf(null)===-1&&e.onComplete(r)}var u=document.createElement("a");a.forEach(function(c,l){u.setAttribute("href",c),u.href=String(u.href);var s=Boolean(document.all&&!window.atob),h=s&&u.host.split(":")[0]!==location.host.split(":")[0];if(h){var m=u.protocol===location.protocol;if(m){var v=new XDomainRequest;v.open("GET",c),v.timeout=0,v.onprogress=Function.prototype,v.ontimeout=Function.prototype,v.onload=function(){var y=v.responseText;o(y)?d(y,l):i(v,l)},v.onerror=function(y){i(v,l)},setTimeout(function(){v.send()},0)}else console.warn("Internet Explorer 9 Cross-Origin (CORS) requests must use the same protocol (".concat(c,")")),i(null,l)}else{var b=new XMLHttpRequest;b.open("GET",c),e.mimeType&&b.overrideMimeType&&b.overrideMimeType(e.mimeType),e.onBeforeSend(b,c,l),b.onreadystatechange=function(){if(b.readyState===4){var y=b.responseText;b.status<400&&o(y)||b.status===0&&o(y)?d(y,l):i(b,l)}},b.send()}})}/**
 * Gets CSS data from <style> and <link> nodes (including @imports), then
 * returns data in order processed by DOM. Allows specifying nodes to
 * include/exclude and filtering CSS data using RegEx.
 *
 * @preserve
 * @param {object}   [options] The options object
 * @param {object}   [options.rootElement=document] Root element to traverse for
 *                   <link> and <style> nodes.
 * @param {string}   [options.include] CSS selector matching <link> and <style>
 *                   nodes to include
 * @param {string}   [options.exclude] CSS selector matching <link> and <style>
 *                   nodes to exclude
 * @param {object}   [options.filter] Regular expression used to filter node CSS
 *                   data. Each block of CSS data is tested against the filter,
 *                   and only matching data is included.
 * @param {boolean}  [options.skipDisabled=true] Determines if disabled
 *                   stylesheets will be skipped while collecting CSS data.
 * @param {boolean}  [options.useCSSOM=false] Determines if CSS data will be
 *                   collected from a stylesheet's runtime values instead of its
 *                   text content. This is required to get accurate CSS data
 *                   when a stylesheet has been modified using the deleteRule()
 *                   or insertRule() methods because these modifications will
 *                   not be reflected in the stylesheet's text content.
 * @param {function} [options.onBeforeSend] Callback before XHR is sent. Passes
 *                   1) the XHR object, 2) source node reference, and 3) the
 *                   source URL as arguments.
 * @param {function} [options.onSuccess] Callback on each CSS node read. Passes
 *                   1) CSS text, 2) source node reference, and 3) the source
 *                   URL as arguments.
 * @param {function} [options.onError] Callback on each error. Passes 1) the XHR
 *                   object for inspection, 2) soure node reference, and 3) the
 *                   source URL that failed (either a <link> href or an @import)
 *                   as arguments
 * @param {function} [options.onComplete] Callback after all nodes have been
 *                   processed. Passes 1) concatenated CSS text, 2) an array of
 *                   CSS text in DOM order, and 3) an array of nodes in DOM
 *                   order as arguments.
 *
 * @example
 *
 *   getCssData({
 *     rootElement : document,
 *     include     : 'style,link[rel="stylesheet"]',
 *     exclude     : '[href="skip.css"]',
 *     filter      : /red/,
 *     skipDisabled: true,
 *     useCSSOM    : false,
 *     onBeforeSend(xhr, node, url) {
 *       // ...
 *     }
 *     onSuccess(cssText, node, url) {
 *       // ...
 *     }
 *     onError(xhr, node, url) {
 *       // ...
 *     },
 *     onComplete(cssText, cssArray, nodeArray) {
 *       // ...
 *     }
 *   });
 */function Be(t){var n={cssComments:/\/\*[\s\S]+?\*\//g,cssImports:/(?:@import\s*)(?:url\(\s*)?(?:['"])([^'"]*)(?:['"])(?:\s*\))?(?:[^;]*;)/g},e={rootElement:t.rootElement||document,include:t.include||'style,link[rel="stylesheet"]',exclude:t.exclude||null,filter:t.filter||null,skipDisabled:t.skipDisabled!==!1,useCSSOM:t.useCSSOM||!1,onBeforeSend:t.onBeforeSend||Function.prototype,onSuccess:t.onSuccess||Function.prototype,onError:t.onError||Function.prototype,onComplete:t.onComplete||Function.prototype},a=Array.apply(null,e.rootElement.querySelectorAll(e.include)).filter(function(c){return!cn(c,e.exclude)}),r=Array.apply(null,Array(a.length)).map(function(c){return null});function o(){var c=r.indexOf(null)===-1;if(c){r.reduce(function(s,h,m){return h===""&&s.push(m),s},[]).reverse().forEach(function(s){return[a,r].forEach(function(h){return h.splice(s,1)})});var l=r.join("");e.onComplete(l,r,a)}}function i(c,l,s,h){var m=e.onSuccess(c,s,h);c=m!==void 0&&Boolean(m)===!1?"":m||c,u(c,s,h,function(v,b){r[l]===null&&(b.forEach(function(y){return e.onError(y.xhr,s,y.url)}),!e.filter||e.filter.test(v)?r[l]=v:r[l]="",o())})}function d(c,l){var s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],h={};return h.rules=(c.replace(n.cssComments,"").match(n.cssImports)||[]).filter(function(m){return s.indexOf(m)===-1}),h.urls=h.rules.map(function(m){return m.replace(n.cssImports,"$1")}),h.absoluteUrls=h.urls.map(function(m){return fe(m,l)}),h.absoluteRules=h.rules.map(function(m,v){var b=h.urls[v],y=fe(h.absoluteUrls[v],l);return m.replace(b,y)}),h}function u(c,l,s,h){var m=arguments.length>4&&arguments[4]!==void 0?arguments[4]:[],v=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],b=d(c,s,v);b.rules.length?Oe(b.absoluteUrls,{onBeforeSend:function(w,x,k){e.onBeforeSend(w,l,x)},onSuccess:function(w,x,k){var V=e.onSuccess(w,l,x);w=V===!1?"":V||w;var O=d(w,x,v);return O.rules.forEach(function(U,g){w=w.replace(U,O.absoluteRules[g])}),w},onError:function(w,x,k){m.push({xhr:w,url:x}),v.push(b.rules[k]),u(c,l,s,h,m,v)},onComplete:function(w){w.forEach(function(x,k){c=c.replace(b.rules[k],x)}),u(c,l,s,h,m,v)}}):h(c,m)}a.length?a.forEach(function(c,l){var s=c.getAttribute("href"),h=c.getAttribute("rel"),m=c.nodeName.toLowerCase()==="link"&&s&&h&&h.toLowerCase().indexOf("stylesheet")!==-1,v=e.skipDisabled===!1?!1:c.disabled,b=c.nodeName.toLowerCase()==="style";if(m&&!v){var y=s.indexOf("data:text/css")!==-1;if(y){var w=decodeURIComponent(s.substring(s.indexOf(",")+1));e.useCSSOM&&(w=Array.apply(null,c.sheet.cssRules).map(function(k){return k.cssText}).join("")),i(w,l,c,location.href)}else Oe(s,{mimeType:"text/css",onBeforeSend:function(V,O,U){e.onBeforeSend(V,c,O)},onSuccess:function(V,O,U){var g=fe(s);i(V,l,c,g)},onError:function(V,O,U){r[l]="",e.onError(V,c,O),o()}})}else if(b&&!v){var x=c.textContent;e.useCSSOM&&(x=Array.apply(null,c.sheet.cssRules).map(function(k){return k.cssText}).join("")),i(x,l,c,location.href)}else r[l]="",o()}):e.onComplete("",[])}function fe(t,n){var e=document.implementation.createHTMLDocument(""),a=e.createElement("base"),r=e.createElement("a");return e.head.appendChild(a),e.body.appendChild(r),a.href=n||document.baseURI||(document.querySelector("base")||{}).href||location.href,r.href=t,r.href}function cn(t,n){var e=t.matches||t.matchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector;return e.call(t,n)}var je=Ve;function Ve(t,n,e){t instanceof RegExp&&(t=Me(t,e)),n instanceof RegExp&&(n=Me(n,e));var a=Ie(t,n,e);return a&&{start:a[0],end:a[1],pre:e.slice(0,a[0]),body:e.slice(a[0]+t.length,a[1]),post:e.slice(a[1]+n.length)}}function Me(t,n){var e=n.match(t);return e?e[0]:null}Ve.range=Ie;function Ie(t,n,e){var a,r,o,i,d,u=e.indexOf(t),c=e.indexOf(n,u+1),l=u;if(u>=0&&c>0){if(t===n)return[u,c];for(a=[],o=e.length;l>=0&&!d;)l==u?(a.push(l),u=e.indexOf(t,l+1)):a.length==1?d=[a.pop(),c]:(r=a.pop(),r<o&&(o=r,i=c),c=e.indexOf(n,l+1)),l=u<c&&u>=0?u:c;a.length&&(d=[o,i])}return d}function de(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e={preserveStatic:!0,removeComments:!1},a=T({},e,n),r=[];function o(f){throw new Error("CSS parse error: ".concat(f))}function i(f){var p=f.exec(t);if(p)return t=t.slice(p[0].length),p}function d(){return i(/^{\s*/)}function u(){return i(/^}/)}function c(){i(/^\s*/)}function l(){if(c(),!(t[0]!=="/"||t[1]!=="*")){for(var f=2;t[f]&&(t[f]!=="*"||t[f+1]!=="/");)f++;if(!t[f])return o("end of comment is missing");var p=t.slice(2,f);return t=t.slice(f+2),{type:"comment",comment:p}}}function s(){for(var f=[],p;p=l();)f.push(p);return a.removeComments?[]:f}function h(){for(c();t[0]==="}";)o("extra closing bracket");var f=i(/^(("(?:\\"|[^"])*"|'(?:\\'|[^'])*'|[^{])+)/);if(f){var p=f[0].trim(),S,A=/\/\*/.test(p);A&&(p=p.replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,""));var L=/["']\w*,\w*["']/.test(p);L&&(p=p.replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,function(X){return X.replace(/,/g,"\u200C")}));var ue=/,/.test(p);return ue?S=p.split(/\s*(?![^(]*\)),\s*/):S=[p],L&&(S=S.map(function(X){return X.replace(/\u200C/g,",")})),S}}function m(){if(t[0]==="@")return M();i(/^([;\s]*)+/);var f=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,p=i(/^(\*?[-#/*\\\w.]+(\[[0-9a-z_-]+\])?)\s*/);if(!!p){if(p=p[0].trim(),!i(/^:\s*/))return o("property missing ':'");var S=i(/^((?:\/\*.*?\*\/|'(?:\\'|.)*?'|"(?:\\"|.)*?"|\((\s*'(?:\\'|.)*?'|"(?:\\"|.)*?"|[^)]*?)\s*\)|[^};])+)/),A={type:"declaration",property:p.replace(f,""),value:S?S[0].replace(f,"").trim():""};return i(/^[;\s]*/),A}}function v(){if(!d())return o("missing '{'");for(var f,p=s();f=m();)p.push(f),p=p.concat(s());return u()?p:o("missing '}'")}function b(){c();for(var f=[],p;p=i(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)f.push(p[1]),i(/^,\s*/);if(f.length)return{type:"keyframe",values:f,declarations:v()}}function y(){var f=i(/^@([-\w]+)?keyframes\s*/);if(!!f){var p=f[1];if(f=i(/^([-\w]+)\s*/),!f)return o("@keyframes missing name");var S=f[1];if(!d())return o("@keyframes missing '{'");for(var A,L=s();A=b();)L.push(A),L=L.concat(s());return u()?{type:"keyframes",name:S,vendor:p,keyframes:L}:o("@keyframes missing '}'")}}function w(){var f=i(/^@page */);if(f){var p=h()||[];return{type:"page",selectors:p,declarations:v()}}}function x(){var f=i(/@(top|bottom|left|right)-(left|center|right|top|middle|bottom)-?(corner)?\s*/);if(f){var p="".concat(f[1],"-").concat(f[2])+(f[3]?"-".concat(f[3]):"");return{type:"page-margin-box",name:p,declarations:v()}}}function k(){var f=i(/^@font-face\s*/);if(f)return{type:"font-face",declarations:v()}}function V(){var f=i(/^@supports *([^{]+)/);if(f)return{type:"supports",supports:f[1].trim(),rules:H()}}function O(){var f=i(/^@host\s*/);if(f)return{type:"host",rules:H()}}function U(){var f=i(/^@media([^{]+)*/);if(f)return{type:"media",media:(f[1]||"").trim(),rules:H()}}function g(){var f=i(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(f)return{type:"custom-media",name:f[1].trim(),media:f[2].trim()}}function K(){var f=i(/^@([-\w]+)?document *([^{]+)/);if(f)return{type:"document",document:f[2].trim(),vendor:f[1]?f[1].trim():null,rules:H()}}function q(){var f=i(/^@(import|charset|namespace)\s*([^;]+);/);if(f)return{type:f[1],name:f[2].trim()}}function M(){if(c(),t[0]==="@"){var f=q()||k()||U()||y()||V()||K()||g()||O()||w()||x();if(f&&!a.preserveStatic){var p=!1;if(f.declarations)p=f.declarations.some(function(A){return/var\(/.test(A.value)});else{var S=f.keyframes||f.rules||[];p=S.some(function(A){return(A.declarations||[]).some(function(L){return/var\(/.test(L.value)})})}return p?f:{}}return f}}function J(){if(!a.preserveStatic){var f=je("{","}",t);if(f){var p=/:(?:root|host)(?![.:#(])/.test(f.pre)&&/--\S*\s*:/.test(f.body),S=/var\(/.test(f.body);if(!p&&!S)return t=t.slice(f.end+1),{}}}var A=h()||[],L=a.preserveStatic?v():v().filter(function(ue){var X=A.some(function(Jn){return/:(?:root|host)(?![.:#(])/.test(Jn)})&&/^--\S/.test(ue.property),Kn=/var\(/.test(ue.value);return X||Kn});return A.length||o("selector missing"),{type:"rule",selectors:A,declarations:L}}function H(f){if(!f&&!d())return o("missing '{'");for(var p,S=s();t.length&&(f||t[0]!=="}")&&(p=M()||J());)p.type&&S.push(p),S=S.concat(s());return!f&&!u()?o("missing '}'"):S}return{type:"stylesheet",stylesheet:{rules:H(!0),errors:r}}}function Le(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e={parseHost:!1,store:{},onWarning:function(){}},a=T({},e,n),r=new RegExp(":".concat(a.parseHost?"host":"root","$"));return typeof t=="string"&&(t=de(t,a)),t.stylesheet.rules.forEach(function(o){o.type!=="rule"||!o.selectors.some(function(i){return r.test(i)})||o.declarations.forEach(function(i,d){var u=i.property,c=i.value;u&&u.indexOf("--")===0&&(a.store[u]=c)})}),a.store}function Pe(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",e=arguments.length>2?arguments[2]:void 0,a={charset:function(i){return"@charset "+i.name+";"},comment:function(i){return i.comment.indexOf("__CSSVARSPONYFILL")===0?"/*"+i.comment+"*/":""},"custom-media":function(i){return"@custom-media "+i.name+" "+i.media+";"},declaration:function(i){return i.property+":"+i.value+";"},document:function(i){return"@"+(i.vendor||"")+"document "+i.document+"{"+r(i.rules)+"}"},"font-face":function(i){return"@font-face{"+r(i.declarations)+"}"},host:function(i){return"@host{"+r(i.rules)+"}"},import:function(i){return"@import "+i.name+";"},keyframe:function(i){return i.values.join(",")+"{"+r(i.declarations)+"}"},keyframes:function(i){return"@"+(i.vendor||"")+"keyframes "+i.name+"{"+r(i.keyframes)+"}"},media:function(i){return"@media "+i.media+"{"+r(i.rules)+"}"},namespace:function(i){return"@namespace "+i.name+";"},page:function(i){return"@page "+(i.selectors.length?i.selectors.join(", "):"")+"{"+r(i.declarations)+"}"},"page-margin-box":function(i){return"@"+i.name+"{"+r(i.declarations)+"}"},rule:function(i){var d=i.declarations;if(d.length)return i.selectors.join(",")+"{"+r(d)+"}"},supports:function(i){return"@supports "+i.supports+"{"+r(i.rules)+"}"}};function r(o){for(var i="",d=0;d<o.length;d++){var u=o[d];e&&e(u);var c=a[u.type](u);c&&(i+=c,c.length&&u.selectors&&(i+=n))}return i}return r(t.stylesheet.rules)}function Re(t,n){t.rules.forEach(function(e){if(e.rules){Re(e,n);return}if(e.keyframes){e.keyframes.forEach(function(a){a.type==="keyframe"&&n(a.declarations,e)});return}!e.declarations||n(e.declarations,t)})}var sn="--",un="var";function fn(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e={preserveStatic:!0,preserveVars:!1,variables:{},onWarning:function(){}},a=T({},e,n);return typeof t=="string"&&(t=de(t,a)),Re(t.stylesheet,function(r,o){for(var i=0;i<r.length;i++){var d=r[i],u=d.type,c=d.property,l=d.value;if(u==="declaration"){if(!a.preserveVars&&c&&c.indexOf(sn)===0){r.splice(i,1),i--;continue}if(l.indexOf(un+"(")!==-1){var s=N(l,a);s!==d.value&&(s=dn(s),a.preserveVars?(r.splice(i,0,{type:u,property:c,value:s}),i++):d.value=s)}}}}),Pe(t)}function dn(t){var n=/calc\(([^)]+)\)/g;return(t.match(n)||[]).forEach(function(e){var a="calc".concat(e.split("calc").join(""));t=t.replace(e,a)}),t}function N(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2?arguments[2]:void 0;if(t.indexOf("var(")===-1)return t;var a=je("(",")",t);function r(i){var d=i.split(",")[0].replace(/[\s\n\t]/g,""),u=(i.match(/(?:\s*,\s*){1}(.*)?/)||[])[1],c=Object.prototype.hasOwnProperty.call(n.variables,d)?String(n.variables[d]):void 0,l=c||(u?String(u):void 0),s=e||i;return c||n.onWarning('variable "'.concat(d,'" is undefined')),l&&l!=="undefined"&&l.length>0?N(l,n,s):"var(".concat(s,")")}if(a)if(a.pre.slice(-3)==="var"){var o=a.body.trim().length===0;return o?(n.onWarning("var() must contain a non-whitespace string"),t):a.pre.slice(0,-3)+r(a.body)+N(a.post,n)}else return a.pre+"(".concat(N(a.body,n),")")+N(a.post,n);else return t.indexOf("var(")!==-1&&n.onWarning('missing closing ")" in the value "'.concat(t,'"')),t}var Z=typeof window!="undefined",ze=Z&&window.CSS&&window.CSS.supports&&window.CSS.supports("(--a: 0)"),Q={group:0,job:0},ee={rootElement:Z?document:null,shadowDOM:!1,include:"style,link[rel=stylesheet]",exclude:"",variables:{},onlyLegacy:!0,preserveStatic:!0,preserveVars:!1,silent:!1,updateDOM:!0,updateURLs:!0,watch:null,onBeforeSend:function(){},onError:function(){},onWarning:function(){},onSuccess:function(){},onComplete:function(){},onFinally:function(){}},P={cssComments:/\/\*[\s\S]+?\*\//g,cssKeyframes:/@(?:-\w*-)?keyframes/,cssMediaQueries:/@media[^{]+\{([\s\S]+?})\s*}/g,cssUrls:/url\((?!['"]?(?:data|http|\/\/):)['"]?([^'")]*)['"]?\)/g,cssVarDeclRules:/(?::(?:root|host)(?![.:#(])[\s,]*[^{]*{\s*[^}]*})/g,cssVarDecls:/(?:[\s;]*)(-{2}\w[\w-]*)(?:\s*:\s*)([^;]*);/g,cssVarFunc:/var\(\s*--[\w-]/,cssVars:/(?:(?::(?:root|host)(?![.:#(])[\s,]*[^{]*{\s*[^;]*;*\s*)|(?:var\(\s*))(--[^:)]+)(?:\s*[:)])/},F={dom:{},job:{},user:{}},ne=!1,j=null,G=0,he=null,me=!1;/**
 * Fetches, parses, and transforms CSS custom properties from specified
 * <style> and <link> elements into static values, then appends a new <style>
 * element with static values to the DOM to provide CSS custom property
 * compatibility for legacy browsers. Also provides a single interface for
 * live updates of runtime values in both modern and legacy browsers.
 *
 * @preserve
 * @param {object}   [options] Options object
 * @param {object}   [options.rootElement=document] Root element to traverse for
 *                   <link> and <style> nodes
 * @param {boolean}  [options.shadowDOM=false] Determines if shadow DOM <link>
 *                   and <style> nodes will be processed.
 * @param {string}   [options.include="style,link[rel=stylesheet]"] CSS selector
 *                   matching <link re="stylesheet"> and <style> nodes to
 *                   process
 * @param {string}   [options.exclude] CSS selector matching <link
 *                   rel="stylehseet"> and <style> nodes to exclude from those
 *                   matches by options.include
 * @param {object}   [options.variables] A map of custom property name/value
 *                   pairs. Property names can omit or include the leading
 *                   double-hyphen (—), and values specified will override
 *                   previous values
 * @param {boolean}  [options.onlyLegacy=true] Determines if the ponyfill will
 *                   only generate legacy-compatible CSS in browsers that lack
 *                   native support (i.e., legacy browsers)
 * @param {boolean}  [options.preserveStatic=true] Determines if CSS
 *                   declarations that do not reference a custom property will
 *                   be preserved in the transformed CSS
 * @param {boolean}  [options.preserveVars=false] Determines if CSS custom
 *                   property declarations will be preserved in the transformed
 *                   CSS
 * @param {boolean}  [options.silent=false] Determines if warning and error
 *                   messages will be displayed on the console
 * @param {boolean}  [options.updateDOM=true] Determines if the ponyfill will
 *                   update the DOM after processing CSS custom properties
 * @param {boolean}  [options.updateURLs=true] Determines if relative url()
 *                   paths will be converted to absolute urls in external CSS
 * @param {boolean}  [options.watch=false] Determines if a MutationObserver will
 *                   be created that will execute the ponyfill when a <link> or
 *                   <style> DOM mutation is observed
 * @param {function} [options.onBeforeSend] Callback before XHR is sent. Passes
 *                   1) the XHR object, 2) source node reference, and 3) the
 *                   source URL as arguments
 * @param {function} [options.onError] Callback after a CSS parsing error has
 *                   occurred or an XHR request has failed. Passes 1) an error
 *                   message, and 2) source node reference, 3) xhr, and 4 url as
 *                   arguments.
 * @param {function} [options.onWarning] Callback after each CSS parsing warning
 *                   has occurred. Passes 1) a warning message as an argument.
 * @param {function} [options.onSuccess] Callback after CSS data has been
 *                   collected from each node and before CSS custom properties
 *                   have been transformed. Allows modifying the CSS data before
 *                   it is transformed by returning any string value (or false
 *                   to skip). Passes 1) CSS text, 2) source node reference, and
 *                   3) the source URL as arguments.
 * @param {function} [options.onComplete] Callback after all CSS has been
 *                   processed, legacy-compatible CSS has been generated, and
 *                   (optionally) the DOM has been updated. Passes 1) a CSS
 *                   string with CSS variable values resolved, 2) an array of
 *                   output <style> node references that have been appended to
 *                   the DOM, 3) an object containing all custom properies names
 *                   and values, and 4) the ponyfill execution time in
 *                   milliseconds.
 * @param {function} [options.onFinally] Callback in modern and legacy browsers
 *                   after the ponyfill has finished all tasks. Passes 1) a
 *                   boolean indicating if the last ponyfill call resulted in a
 *                   style change, 2) a boolean indicating if the current
 *                   browser provides native support for CSS custom properties,
 *                   and 3) the ponyfill execution time in milliseconds.
 * @example
 *
 *   cssVars({
 *     rootElement   : document,
 *     shadowDOM     : false,
 *     include       : 'style,link[rel="stylesheet"]',
 *     exclude       : '',
 *     variables     : {},
 *     onlyLegacy    : true,
 *     preserveStatic: true,
 *     preserveVars  : false,
 *     silent        : false,
 *     updateDOM     : true,
 *     updateURLs    : true,
 *     watch         : false,
 *     onBeforeSend(xhr, node, url) {},
 *     onError(message, node, xhr, url) {},
 *     onWarning(message) {},
 *     onSuccess(cssText, node, url) {},
 *     onComplete(cssText, styleNode, cssVariables, benchmark) {},
 *     onFinally(hasChanged, hasNativeSupport, benchmark)
 *   });
 */function I(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n="cssVars(): ",e=T({},ee,t);function a(l,s,h,m){!e.silent&&window.console&&console.error("".concat(n).concat(l,`
`),s),e.onError(l,s,h,m)}function r(l){!e.silent&&window.console&&console.warn("".concat(n).concat(l)),e.onWarning(l)}function o(l){e.onFinally(Boolean(l),ze,ve()-e.__benchmark)}if(!!Z){if(e.watch){e.watch=ee.watch,hn(e),I(e);return}else e.watch===!1&&j&&(j.disconnect(),j=null);if(!e.__benchmark){if(ne===e.rootElement){mn(t);return}var i=[].slice.call(e.rootElement.querySelectorAll('[data-cssvars]:not([data-cssvars="out"])'));if(e.__benchmark=ve(),e.exclude=[j?'[data-cssvars]:not([data-cssvars=""])':'[data-cssvars="out"]',"link[disabled]:not([data-cssvars])",e.exclude].filter(function(l){return l}).join(","),e.variables=bn(e.variables),i.forEach(function(l){var s=l.nodeName.toLowerCase()==="style"&&l.__cssVars.text,h=s&&l.textContent!==l.__cssVars.text;s&&h&&(l.sheet&&(l.sheet.disabled=!1),l.setAttribute("data-cssvars",""))}),!j){var d=[].slice.call(e.rootElement.querySelectorAll('[data-cssvars="out"]'));d.forEach(function(l){var s=l.getAttribute("data-cssvars-group"),h=s?e.rootElement.querySelector('[data-cssvars="src"][data-cssvars-group="'.concat(s,'"]')):null;h||l.parentNode.removeChild(l)}),G&&i.length<G&&(G=i.length,F.dom={})}}if(document.readyState!=="loading")if(ze&&e.onlyLegacy){var u=!1;if(e.updateDOM){var c=e.rootElement.host||(e.rootElement===document?document.documentElement:e.rootElement);Object.keys(e.variables).forEach(function(l){var s=e.variables[l];u=u||s!==getComputedStyle(c).getPropertyValue(l),c.style.setProperty(l,s)})}o(u)}else!me&&(e.shadowDOM||e.rootElement.shadowRoot||e.rootElement.host)?Be({rootElement:ee.rootElement,include:ee.include,exclude:e.exclude,skipDisabled:!1,onSuccess:function(s,h,m){var v=(h.sheet||{}).disabled&&!h.__cssVars;return v?!1:(s=s.replace(P.cssComments,"").replace(P.cssMediaQueries,""),s=(s.match(P.cssVarDeclRules)||[]).join(""),s||!1)},onComplete:function(s,h,m){Le(s,{store:F.dom,onWarning:r}),me=!0,I(e)}}):(ne=e.rootElement,Be({rootElement:e.rootElement,include:e.include,exclude:e.exclude,skipDisabled:!1,onBeforeSend:e.onBeforeSend,onError:function(s,h,m){var v=s.responseURL||Ue(m,location.href),b=s.statusText?"(".concat(s.statusText,")"):"Unspecified Error"+(s.status===0?" (possibly CORS related)":""),y="CSS XHR Error: ".concat(v," ").concat(s.status," ").concat(b);a(y,h,s,v)},onSuccess:function(s,h,m){var v=(h.sheet||{}).disabled&&!h.__cssVars;if(v)return!1;var b=h.nodeName.toLowerCase()==="link",y=h.nodeName.toLowerCase()==="style"&&s!==h.textContent,w=e.onSuccess(s,h,m);return s=w!==void 0&&Boolean(w)===!1?"":w||s,e.updateURLs&&(b||y)&&(s=pn(s,m)),s},onComplete:function(s,h){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],v=T({},F.dom,F.user),b=!1;if(F.job={},m.forEach(function(g,K){var q=h[K];if(g.__cssVars=g.__cssVars||{},g.__cssVars.text=q,P.cssVars.test(q))try{var M=de(q,{preserveStatic:e.preserveStatic,removeComments:!0});Le(M,{parseHost:Boolean(e.rootElement.host),store:F.dom,onWarning:r}),g.__cssVars.tree=M}catch(J){a(J.message,g)}}),T(F.job,F.dom),e.updateDOM?(T(F.user,e.variables),T(F.job,F.user)):(T(F.job,F.user,e.variables),T(v,e.variables)),b=Q.job>0&&Boolean(Object.keys(F.job).length>Object.keys(v).length||Boolean(Object.keys(v).length&&Object.keys(F.job).some(function(g){return F.job[g]!==v[g]}))),b)pe(e.rootElement),I(e);else{var y=[],w=[],x=!1;if(e.updateDOM&&Q.job++,m.forEach(function(g,K){var q=!g.__cssVars.tree;if(g.__cssVars.tree)try{fn(g.__cssVars.tree,T({},e,{variables:F.job,onWarning:r}));var M=Pe(g.__cssVars.tree);if(e.updateDOM){var J=h[K],H=P.cssVarFunc.test(J);if(g.getAttribute("data-cssvars")||g.setAttribute("data-cssvars","src"),M.length&&H){var f=g.getAttribute("data-cssvars-group")||++Q.group,p=M.replace(/\s/g,""),S=e.rootElement.querySelector('[data-cssvars="out"][data-cssvars-group="'.concat(f,'"]'))||document.createElement("style");x=x||P.cssKeyframes.test(M),e.preserveStatic&&g.sheet&&(g.sheet.disabled=!0),S.hasAttribute("data-cssvars")||S.setAttribute("data-cssvars","out"),p===g.textContent.replace(/\s/g,"")?(q=!0,S&&S.parentNode&&(g.removeAttribute("data-cssvars-group"),S.parentNode.removeChild(S))):p!==S.textContent.replace(/\s/g,"")&&([g,S].forEach(function(A){A.setAttribute("data-cssvars-job",Q.job),A.setAttribute("data-cssvars-group",f)}),S.textContent=M,y.push(M),w.push(S),S.parentNode||g.parentNode.insertBefore(S,g.nextSibling))}}else g.textContent.replace(/\s/g,"")!==M&&y.push(M)}catch(A){a(A.message,g)}q&&g.setAttribute("data-cssvars","skip"),g.hasAttribute("data-cssvars-job")||g.setAttribute("data-cssvars-job",Q.job)}),G=e.rootElement.querySelectorAll('[data-cssvars]:not([data-cssvars="out"])').length,e.shadowDOM){for(var k=[].concat(e.rootElement).concat([].slice.call(e.rootElement.querySelectorAll("*"))),V=0,O;O=k[V];++V)if(O.shadowRoot&&O.shadowRoot.querySelector("style")){var U=T({},e,{rootElement:O.shadowRoot});I(U)}}e.updateDOM&&x&&vn(e.rootElement),ne=!1,e.onComplete(y.join(""),w,JSON.parse(JSON.stringify(F.job)),ve()-e.__benchmark),o(w.length)}}}));else document.addEventListener("DOMContentLoaded",function l(s){I(t),document.removeEventListener("DOMContentLoaded",l)})}}I.reset=function(){Q.job=0,Q.group=0,ne=!1,j&&(j.disconnect(),j=null),G=0,he=null,me=!1;for(var t in F)F[t]={}};function hn(t){function n(u){var c=e(u)&&u.hasAttribute("disabled"),l=(u.sheet||{}).disabled;return c||l}function e(u){var c=u.nodeName.toLowerCase()==="link"&&(u.getAttribute("rel")||"").indexOf("stylesheet")!==-1;return c}function a(u){return u.nodeName.toLowerCase()==="style"}function r(u){var c=!1;if(u.type==="attributes"&&e(u.target)&&!n(u.target)){var l=u.attributeName==="disabled",s=u.attributeName==="href",h=u.target.getAttribute("data-cssvars")==="skip",m=u.target.getAttribute("data-cssvars")==="src";l?c=!h&&!m:s&&(h?u.target.setAttribute("data-cssvars",""):m&&pe(t.rootElement,!0),c=!0)}return c}function o(u){var c=!1;if(u.type==="childList"){var l=a(u.target),s=u.target.getAttribute("data-cssvars")==="out";c=l&&!s}return c}function i(u){var c=!1;return u.type==="childList"&&(c=[].slice.call(u.addedNodes).some(function(l){var s=l.nodeType===1,h=s&&l.hasAttribute("data-cssvars"),m=a(l)&&P.cssVars.test(l.textContent),v=!h&&(e(l)||m);return v&&!n(l)})),c}function d(u){var c=!1;return u.type==="childList"&&(c=[].slice.call(u.removedNodes).some(function(l){var s=l.nodeType===1,h=s&&l.getAttribute("data-cssvars")==="out",m=s&&l.getAttribute("data-cssvars")==="src",v=m;if(m||h){var b=l.getAttribute("data-cssvars-group"),y=t.rootElement.querySelector('[data-cssvars-group="'.concat(b,'"]'));m&&pe(t.rootElement,!0),y&&y.parentNode.removeChild(y)}return v})),c}!window.MutationObserver||(j&&(j.disconnect(),j=null),j=new MutationObserver(function(u){var c=u.some(function(l){return r(l)||o(l)||i(l)||d(l)});c&&I(t)}),j.observe(document.documentElement,{attributes:!0,attributeFilter:["disabled","href"],childList:!0,subtree:!0}))}function mn(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;clearTimeout(he),he=setTimeout(function(){t.__benchmark=null,I(t)},n)}function vn(t){var n=["animation-name","-moz-animation-name","-webkit-animation-name"].filter(function(h){return getComputedStyle(document.body)[h]})[0];if(n){for(var e=[].slice.call(t.querySelectorAll("*")),a=[],r="__CSSVARSPONYFILL-KEYFRAMES__",o=0,i=e.length;o<i;o++){var d=e[o],u=getComputedStyle(d)[n];u!=="none"&&(d.style[n]+=r,a.push(d))}document.body.offsetHeight;for(var c=0,l=a.length;c<l;c++){var s=a[c].style;s[n]=s[n].replace(r,"")}}}function pn(t,n){var e=t.replace(P.cssComments,"").match(P.cssUrls)||[];return e.forEach(function(a){var r=a.replace(P.cssUrls,"$1"),o=Ue(r,n);t=t.replace(a,a.replace(r,o))}),t}function bn(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=/^-{2}/;return Object.keys(t).reduce(function(e,a){var r=n.test(a)?a:"--".concat(a.replace(/^-+/,""));return e[r]=t[a],e},{})}function Ue(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:location.href,e=document.implementation.createHTMLDocument(""),a=e.createElement("base"),r=e.createElement("a");return e.head.appendChild(a),e.body.appendChild(r),a.href=n,r.href=t,r.href}function ve(){return Z&&(window.performance||{}).now?window.performance.now():new Date().getTime()}function pe(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,e=[].slice.call(t.querySelectorAll('[data-cssvars="skip"],[data-cssvars="src"]'));e.forEach(function(a){return a.setAttribute("data-cssvars","")}),n&&(F.dom={})}const D = {
    userLastPreferTheme: 'user-custom-theme',
    userLastPreferThemeData: 'user-custom-theme-data',
    currentTheme: 'nancaluiCurrentTheme',
    themeCollection: 'nancaluiThemes',
    styleElementId: 'nancaluiThemeVariables',
    transitionStyleElementId: 'nancaluiThemeColorTransition',
    uiThemeAttributeName: 'ui-theme',
    themeService: 'nancaluiThemeService',
  },
  te = new _({
    id: 'nancalui-light-theme',
    name: 'Light Mode',
    cnName: '\u6D45\u8272\u4E3B\u9898',
    data: {
      'nancalui-global-bg': '#f3f6f8',
      'nancalui-global-bg-normal': '#ffffff',
      'nancalui-base-bg': '#ffffff',
      'nancalui-base-bg-dark': '#333854',
      'nancalui-brand': '#447DFD',
      'nancalui-brand-foil': '#859bff',
      'nancalui-brand-hover': '#6E9EFF',
      'nancalui-brand-active': '#2F5CD6',
      'nancalui-brand-active-focus': '#0069AF',
      'nancalui-contrast': '#F63838',
      'nancalui-text': '#252b3a',
      'nancalui-text-weak': '#575d6c',
      'nancalui-aide-text': '#8a8e99',
      'nancalui-aide-text-stress': '#575d6c',
      'nancalui-placeholder': '#8a8e99',
      'nancalui-light-text': '#ffffff',
      'nancalui-dark-text': '#252b3a',
      'nancalui-link': '#2F5CD6',
      'nancalui-link-active': '#2F5CD6',
      'nancalui-link-light': '#67C7FF',
      'nancalui-link-light-active': '#94DFFF',
      'nancalui-line': '#C8C9CC',
      'nancalui-dividing-line': '#e1e1e1',
      'nancalui-block': '#ffffff',
      'nancalui-area': '#f8f8f8',
      'nancalui-danger': '#F63838',
      'nancalui-warning': '#FF7D00',
      'nancalui-warning-hover': '#FF9729',
      'nancalui-warning-active': '#D96200',
      'nancalui-success': '#04C495',
      'nancalui-success-hover': '#26D1A1',
      'nancalui-info': '#447DFD',
      'nancalui-initial': '#F0F7FF',
      'nancalui-unavailable': '#F7F8FA',
      'nancalui-shadow': 'rgba(37, 43, 58, 0.2)',
      'nancalui-light-shadow': 'rgba(37, 43, 58, 0.1)',
      'nancalui-icon-text': '#252b3a',
      'nancalui-icon-bg': '#ffffff',
      'nancalui-icon-fill': '#71757f',
      'nancalui-icon-fill-hover': '#252b3a',
      'nancalui-icon-fill-active': '#252b3a',
      'nancalui-icon-fill-active-hover': '#2F5CD6',
      'nancalui-form-control-line': '#C8C9CC',
      'nancalui-form-control-line-hover': '#575d6c',
      'nancalui-form-control-line-active': '#447DFD',
      'nancalui-form-control-line-active-hover': '#0069AF',
      'nancalui-list-item-active-bg': '#447DFD',
      'nancalui-list-item-active-text': '#ffffff',
      'nancalui-list-item-active-hover-bg': '#2F5CD6',
      'nancalui-list-item-hover-bg': '#f2f5fc',
      'nancalui-list-item-hover-text': '#2F5CD6',
      'nancalui-list-item-selected-bg': '#F0F7FF',
      'nancalui-list-item-strip-bg': '#f2f5fc',
      'nancalui-disabled-bg': '#F7F8FA',
      'nancalui-disabled-line': '#e1e1e1',
      'nancalui-disabled-text': '#C8C9CC',
      'nancalui-primary-disabled': '#94DFFF',
      'nancalui-icon-fill-active-disabled': '#94DFFF',
      'nancalui-label-bg': '#eef0f5',
      'nancalui-connected-overlay-bg': '#ffffff',
      'nancalui-connected-overlay-line': '#2F5CD6',
      'nancalui-fullscreen-overlay-bg': '#ffffff',
      'nancalui-feedback-overlay-bg': '#464d6e',
      'nancalui-feedback-overlay-text': '#e1e1e1',
      'nancalui-embed-search-bg': '#f2f5fc',
      'nancalui-embed-search-bg-hover': '#eef0f5',
      'nancalui-float-block-shadow': 'rgba(94, 124, 224, 0.3)',
      'nancalui-highlight-overlay': 'rgba(255, 255, 255, 0.8)',
      'nancalui-range-item-hover-bg': '#F0F7FF',
      'nancalui-primary': '#447DFD',
      'nancalui-primary-hover': '#447dfd',
      'nancalui-primary-active': '#2F5CD6',
      'nancalui-contrast-hover': '#d64a52',
      'nancalui-contrast-active': '#E72B2D',
      'nancalui-danger-line': '#FFE2DE',
      'nancalui-danger-hover': '#FF6963',
      'nancalui-danger-active': '#CF252B',
      'nancalui-danger-bg': '#ffeeed',
      'nancalui-warning-line': '#FFDCA3',
      'nancalui-warning-bg': '#fff3e8',
      'nancalui-info-line': '#447DFD',
      'nancalui-info-bg': '#f2f5fc',
      'nancalui-success-line': '#75EBC2',
      'nancalui-success-bg': '#edfff9',
      'nancalui-primary-line': '#447DFD',
      'nancalui-primary-bg': '#447DFD',
      'nancalui-default-line': '#447DFD',
      'nancalui-default-bg': '#f3f6f8',
      'nancalui-font-size': '12px',
      'nancalui-font-size-card-title': '16px',
      'nancalui-font-size-page-title': '16px',
      'nancalui-font-size-modal-title': '18px',
      'nancalui-font-size-price': '20px',
      'nancalui-font-size-data-overview': '24px',
      'nancalui-font-size-icon': '16px',
      'nancalui-font-size-sm': '12px',
      'nancalui-font-size-md': '12px',
      'nancalui-font-size-lg': '14px',
      'nancalui-font-title-weight': 'bold',
      'nancalui-font-content-weight': 'normal',
      'nancalui-line-height-base': '1.5',
      'nancalui-input-placeholder': '#B8B8B8',
      'nancalui-border-radius': '2px',
      'nancalui-border-radius-feedback': '4px',
      'nancalui-border-radius-card': '6px',
      'nancalui-shadow-length-base': '0 1px 4px 0',
      'nancalui-shadow-length-slide-left': '-2px 0 8px 0',
      'nancalui-shadow-length-slide-right': '2px 0 8px 0',
      'nancalui-shadow-length-connected-overlay': '0 2px 8px 0',
      'nancalui-shadow-length-hover': '0 4px 16px 0',
      'nancalui-shadow-length-feedback-overlay': '0 4px 16px 0',
      'nancalui-shadow-fullscreen-overlay': '0 8px 40px 0',
      'nancalui-animation-duration-slow': '300ms',
      'nancalui-animation-duration-base': '200ms',
      'nancalui-animation-duration-fast': '100ms',
      'nancalui-animation-ease-in': 'cubic-bezier(0.5, 0, 0.84, 0.25)',
      'nancalui-animation-ease-out': 'cubic-bezier(0.16, 0.75, 0.5, 1)',
      'nancalui-animation-ease-in-out': 'cubic-bezier(0.5, 0.05, 0.5, 0.95)',
      'nancalui-animation-ease-in-out-smooth': 'cubic-bezier(0.645, 0.045, 0.355, 1)',
      'nancalui-animation-linear': 'cubic-bezier(0, 0, 1, 1)',
      'nancalui-z-index-full-page-overlay': '1080',
      'nancalui-z-index-pop-up': '1060',
      'nancalui-z-index-dropdown': '1062',
      'nancalui-z-index-modal': '1070',
      'nancalui-z-index-drawer': '1040',
      'nancalui-z-index-framework': '1000',
      'nancalui-menu-bg': '#2A325A',
      'nancalui-menu-item': 'rgba(255, 255, 255, 0.55)',
      'nancalui-menu-item-hover': '#fff',
      'nancalui-menu-disabled': '#919191',
      'nancalui-menu-item-sub': '#10131E',
      'nancalui-table-bg': '#F7F8FA',
      'nancalui-table-line': '#EBEDF0',
      'nancalui-table-h-color': '#000000',
      'nancalui-table-b-color': '#333333',
      'nancalui-input-border-color': '#E5E6EB',
      'nancalui-divider': '#EBEDF0',
    },
    isDark: !1,
  }),
  gn = new _({
    id: 'nancalui-green-theme',
    name: 'Green - Light Mode',
    cnName: '\u7EFF\u8272\u4E3B\u9898',
    data: {
      ...te.data,
      'nancalui-global-bg': '#f3f8f7',
      'nancalui-brand': '#3DCCA6',
      'nancalui-brand-foil': '#7fdac1',
      'nancalui-brand-hover': '#6DDEBB',
      'nancalui-brand-active': '#07c693',
      'nancalui-brand-active-focus': '#369676',
      'nancalui-link': '#07c693',
      'nancalui-link-active': '#07c693',
      'nancalui-link-light': '#96fac8',
      'nancalui-link-light-active': '#befade',
      'nancalui-info': '#079CCD',
      'nancalui-initial': '#CCCCCC',
      'nancalui-icon-fill-active': '#3DCCA6',
      'nancalui-icon-fill-active-hover': '#07c693',
      'nancalui-form-control-line-active': '#3DCCA6',
      'nancalui-form-control-line-active-hover': '#2EB28A',
      'nancalui-list-item-active-bg': '#3DCCA6',
      'nancalui-list-item-active-hover-bg': '#07c693',
      'nancalui-list-item-hover-bg': '#f3fef9',
      'nancalui-list-item-hover-text': '#07c693',
      'nancalui-list-item-selected-bg': '#f3fef9',
      'nancalui-list-item-strip-bg': '#f3fef9',
      'nancalui-connected-overlay-line': '#07c693',
      'nancalui-embed-search-bg': '#f3fef9',
      'nancalui-float-block-shadow': 'rgba(94, 224, 181, 0.3)',
      'nancalui-primary': '#3DCCA6',
      'nancalui-primary-hover': '#6DDEBB',
      'nancalui-primary-active': '#369676',
      'nancalui-info-line': '#0486b1',
      'nancalui-info-bg': '#e3f0f5',
      'nancalui-success-line': '#50d492',
      'nancalui-success-bg': '#edfff9',
      'nancalui-primary-line': '#3DCCA6',
      'nancalui-primary-bg': '#447DFD',
      'nancalui-default-line': '#3DCCA6',
      'nancalui-default-bg': '#f3f8f7',
      'nancalui-primary-disabled': '#c5f0e5',
      'nancalui-icon-fill-active-disabled': '#c5f0e5',
      'nancalui-range-item-hover-bg': '#d8f9ea',
    },
    extends: 'nancalui-light-theme',
    isDark: !1,
  }),
  ae = new _({
    id: 'nancalui-dark-theme',
    name: 'Dark Mode',
    cnName: '\u6DF1\u8272\u4E3B\u9898',
    data: {
      'nancalui-global-bg': '#202124',
      'nancalui-global-bg-normal': '#202124',
      'nancalui-base-bg': '#2E2F31',
      'nancalui-base-bg-dark': '#2e2f31',
      'nancalui-brand': '#447DFD',
      'nancalui-brand-foil': '#313a61',
      'nancalui-brand-hover': '#425288',
      'nancalui-brand-active': '#2F5CD6',
      'nancalui-brand-active-focus': '#0069AF',
      'nancalui-contrast': '#F63838',
      'nancalui-text': '#E8E8E8',
      'nancalui-text-weak': '#A0A0A0',
      'nancalui-aide-text': '#909090',
      'nancalui-aide-text-stress': '#A0A0A0',
      'nancalui-placeholder': '#8A8A8A',
      'nancalui-light-text': '#ffffff',
      'nancalui-dark-text': '#252b3a',
      'nancalui-link': '#2F5CD6',
      'nancalui-link-active': '#0069AF',
      'nancalui-link-light': '#67C7FF',
      'nancalui-link-light-active': '#94DFFF',
      'nancalui-line': '#505153',
      'nancalui-dividing-line': '#3D3E40',
      'nancalui-block': '#606061',
      'nancalui-area': '#34363A',
      'nancalui-danger': '#f66f6a',
      'nancalui-warning': '#fac20a',
      'nancalui-waiting': '#5e6580',
      'nancalui-success': '#50d4ab',
      'nancalui-info': '#447DFD',
      'nancalui-initial': '#64676e',
      'nancalui-unavailable': '#5b5b5c',
      'nancalui-shadow': 'rgba(17, 18, 19, 0.4)',
      'nancalui-light-shadow': 'rgba(17, 18, 19, 0.5)',
      'nancalui-icon-text': '#E8E8E8',
      'nancalui-icon-bg': '#2E2F31',
      'nancalui-icon-fill': '#606061',
      'nancalui-icon-fill-hover': '#73788a',
      'nancalui-icon-fill-active': '#447DFD',
      'nancalui-icon-fill-active-hover': '#2F5CD6',
      'nancalui-form-control-line': '#505153',
      'nancalui-form-control-line-hover': '#909090',
      'nancalui-form-control-line-active': '#447DFD',
      'nancalui-form-control-line-active-hover': '#0069AF',
      'nancalui-list-item-active-bg': '#447DFD',
      'nancalui-list-item-active-text': '#ffffff',
      'nancalui-list-item-active-hover-bg': '#2F5CD6',
      'nancalui-list-item-hover-bg': '#383838',
      'nancalui-list-item-hover-text': '#2F5CD6',
      'nancalui-list-item-selected-bg': '#454545',
      'nancalui-list-item-strip-bg': '#383838',
      'nancalui-disabled-bg': '#3D3E44',
      'nancalui-disabled-line': '#505153',
      'nancalui-disabled-text': '#7D7D7D',
      'nancalui-primary-disabled': '#2B3458',
      'nancalui-icon-fill-active-disabled': '#2B3458',
      'nancalui-label-bg': '#46443F',
      'nancalui-connected-overlay-bg': '#2F2F2F',
      'nancalui-connected-overlay-line': '#2F5CD6',
      'nancalui-fullscreen-overlay-bg': '#2E2F31',
      'nancalui-feedback-overlay-bg': '#4C4C4C',
      'nancalui-feedback-overlay-text': '#e1e1e1',
      'nancalui-embed-search-bg': '#383838',
      'nancalui-embed-search-bg-hover': '#3D3E40',
      'nancalui-float-block-shadow': 'rgba(94, 124, 224, 0.3)',
      'nancalui-highlight-overlay': 'rgba(255, 255, 255, 0.1)',
      'nancalui-range-item-hover-bg': '#454545',
      'nancalui-primary': '#447DFD',
      'nancalui-primary-hover': '#425288',
      'nancalui-primary-active': '#2F5CD6',
      'nancalui-contrast-hover': '#D64A52',
      'nancalui-contrast-active': '#E72B2D',
      'nancalui-danger-line': '#985C5A',
      'nancalui-danger-bg': '#4B3A39',
      'nancalui-warning-line': '#8D6138',
      'nancalui-warning-bg': '#554434',
      'nancalui-info-line': '#546BB7',
      'nancalui-info-bg': '#383D4F',
      'nancalui-success-line': '#5D887D',
      'nancalui-success-bg': '#304642',
      'nancalui-primary-line': '#546BB7',
      'nancalui-primary-bg': '#447DFD',
      'nancalui-default-line': '#447DFD',
      'nancalui-default-bg': '#383838',
      'nancalui-menu-item': '#dcdcdc',
    },
    extends: 'nancalui-light-theme',
    isDark: !0,
  }),
  yn = new _({
    id: 'nancalui-green-dark-theme',
    name: 'Green - Dark Mode',
    cnName: '\u7EFF\u8272\u6DF1\u8272\u4E3B\u9898',
    data: {
      ...ae.data,
      'nancalui-brand': '#3DCCA6',
      'nancalui-brand-foil': '#395e54',
      'nancalui-brand-hover': '#4c9780',
      'nancalui-brand-active': '#07c693',
      'nancalui-brand-active-focus': '#297058',
      'nancalui-link': '#07c693',
      'nancalui-link-active': '#08a57b',
      'nancalui-info': '#046788',
      'nancalui-initial': '#64676e',
      'nancalui-icon-fill-active': '#3DCCA6',
      'nancalui-icon-fill-active-hover': '#07c693',
      'nancalui-form-control-line-active': '#3DCCA6',
      'nancalui-form-control-line-active-hover': '#297058',
      'nancalui-list-item-active-bg': '#3DCCA6',
      'nancalui-list-item-active-hover-bg': '#07c693',
      'nancalui-list-item-hover-text': '#07c693',
      'nancalui-connected-overlay-line': '#07c693',
      'nancalui-embed-search-bg': '#3f4241',
      'nancalui-float-block-shadow': 'rgba(94, 224, 181, 0.3)',
      'nancalui-primary': '#3DCCA6',
      'nancalui-primary-hover': '#6DDEBB',
      'nancalui-primary-active': '#369676',
      'nancalui-info-line': '#035e7c',
      'nancalui-info-bg': '#383c3d',
      'nancalui-primary-line': '#3DCCA6',
      'nancalui-primary-bg': '#447DFD',
      'nancalui-default-line': '#3DCCA6',
      'nancalui-default-bg': '#383838',
      'nancalui-primary-disabled': '#28544B',
      'nancalui-icon-fill-active-disabled': '#28544B',
    },
    extends: 'nancalui-dark-theme',
    isDark: !0,
  }),
  $ = new _({
    id: 'infinity-theme',
    name: '\u65E0\u9650\u4E3B\u9898',
    data: {
      ...te.data,
      'nancalui-brand-foil': '#C8C9CC',
      'nancalui-global-bg': '#F8F8FA',
      'nancalui-base-bg': '#ffffff',
      'nancalui-text': '#333333',
      'nancalui-aide-text': '#71757f',
      'nancalui-placeholder': '#babbc0',
      'nancalui-disabled-text': '#C8C9CC',
      'nancalui-disabled-bg': '#F7F8FA',
      'nancalui-line': '#D7D8DA',
      'nancalui-dividing-line': '#F7F8FA',
      'nancalui-area': '#f5f5f5',
      'nancalui-list-item-hover-bg': '#F7F8FA',
      'nancalui-list-item-active-bg': '#F2F5FC',
      'nancalui-list-item-active-hover-bg': '#F2F5FC',
      'nancalui-list-item-selected-bg': '#EFF1F5',
      'nancalui-list-item-hover-text': '#252b3a',
      'nancalui-list-item-active-text': '#252B3A',
      'nancalui-form-control-line-hover': '#9b9fa8',
      'nancalui-form-control-line': '#CFCFCF',
      'nancalui-form-control-bg': '#ffffff',
      'nancalui-icon-text': '#71757f',
      'nancalui-icon-fill': '#71757f',
      'nancalui-icon-fill-weak': '#babbc0',
      'nancalui-icon-fill-hover': '#252b3a',
      'nancalui-icon-fill-active': '#252b3a',
      'nancalui-icon-fill-active-hover': '#252b3a',
      'nancalui-label-bg': '#F0F7FF',
      'nancalui-border-radius': '4px',
      'nancalui-font-size': '12px',
      'nancalui-font-size-md': '12px',
      'nancalui-font-size-card-title': '16px',
      'nancalui-shadow-length-fullscreen-overlay': '0 0 6px 0',
      'nancalui-border-radius-card': '8px',
      'nancalui-border-radius-full': '100px',
      'nancalui-waiting': '#94DFFF',
      'nancalui-danger-bg': '#ffd5d4',
      'nancalui-shape-icon-fill': '#d7d8da',
      'nancalui-shape-icon-fill-hover': '#babbc0',
      'nancalui-shape-icon-fill-active': '#babbc0',
      'nancalui-shape-icon-fill-disabled': '#F7F8FA',
      'nancalui-btn-padding': '0 16px',
      'nancalui-btn-common-bg': '#ebebeb',
      'nancalui-btn-common-bg-hover': '#d1d1d1',
      'nancalui-btn-common-bg-active': '#bdbdbd',
      'nancalui-btn-common-color-hover': '#252b3a',
      'nancalui-btn-common-color-active': '#252b3a',
      'nancalui-btn-common-border-color': 'transparent',
      'nancalui-btn-common-border-color-hover': 'transparent',
      'nancalui-btn-common-border-color-active': 'transparent',
      'nancalui-btn-sm-padding': '0 16px',
      'nancalui-btn-lg-padding': '0 20px',
    },
    extends: 'nancalui-light-theme',
    isDark: !1,
  }),
  Sn = new _({
    id: 'provence-theme',
    name: '\u7D2B\u7F57\u5170\u4E3B\u9898',
    data: {
      ...$.data,
      'nancalui-brand': '#7B69EE',
      'nancalui-brand-foil': '#F5F5F9',
      'nancalui-brand-active-focus': '#7B69EE',
      'nancalui-primary-active': '#7B69EE',
      'nancalui-brand-hover': '#7B69EE',
      'nancalui-global-bg': '#f9fafb',
      'nancalui-base-bg': '#ffffff',
      'nancalui-text': '#070036',
      'nancalui-aide-text': '#717087',
      'nancalui-placeholder': '#babbc0',
      'nancalui-disabled-text': '#C8C9CC',
      'nancalui-disabled-bg': '#F7F8FA',
      'nancalui-line': '#E2E2E5',
      'nancalui-dividing-line': '#F7F8FA',
      'nancalui-list-item-hover-bg': '#F5F5F9',
      'nancalui-list-item-active-bg': '#7B69EE',
      'nancalui-list-item-active-hover-bg': '#7B69EE',
      'nancalui-list-item-selected-bg': '#F4F2FF',
      'nancalui-list-item-hover-text': '#252b3a',
      'nancalui-list-item-active-text': '#ffffff',
      'nancalui-form-control-line-hover': '#A3A6AC',
      'nancalui-form-control-line': '#D7D8DA',
      'nancalui-icon-text': '#babbc0',
      'nancalui-brand-active': '#7B69EE',
      'nancalui-primary': '#7B69EE',
      'nancalui-primary-hover': '#7B69EE',
      'nancalui-form-control-line-active': '#7B69EE',
      'nancalui-form-control-line-active-hover': '#7B69EE',
      'nancalui-icon-fill-active': '#7B69EE',
      'nancalui-icon-fill-active-hover': '#7B69EE',
      'nancalui-label-bg': '#F4F2FF',
      'nancalui-embed-search-bg': '#F4F2FF',
      'nancalui-connected-overlay-line': '#7B69EE',
      'nancalui-primary-disabled': '#d8d2fa',
      'nancalui-icon-fill-active-disabled': '#d8d2fa',
    },
    extends: 'infinity-theme',
    isDark: !1,
  }),
  En = new _({
    id: 'sweet-theme',
    name: '\u871C\u7CD6\u4E3B\u9898',
    data: {
      ...$.data,
      'nancalui-brand': '#ec66ab',
      'nancalui-brand-foil': '#f8f1f5',
      'nancalui-brand-active-focus': '#ec66ab',
      'nancalui-primary-active': '#ec66ab',
      'nancalui-brand-hover': '#ec66ab',
      'nancalui-global-bg': '#f9fafb',
      'nancalui-base-bg': '#ffffff',
      'nancalui-text': '#2f272f',
      'nancalui-aide-text': '#827d82',
      'nancalui-placeholder': '#bdb8bd',
      'nancalui-disabled-text': '#cbcacb',
      'nancalui-disabled-bg': '#f6f6f6',
      'nancalui-line': '#aea6ad',
      'nancalui-dividing-line': '#eae7e9',
      'nancalui-list-item-hover-bg': '#f8f1f5',
      'nancalui-list-item-active-bg': '#ffdcee',
      'nancalui-list-item-active-hover-bg': '#ffdcee',
      'nancalui-list-item-selected-bg': '#ffdcee',
      'nancalui-list-item-hover-text': '#252b3a',
      'nancalui-list-item-active-text': '#252b3a',
      'nancalui-form-control-line-hover': '#A3A6AC',
      'nancalui-form-control-line': '#D7D8DA',
      'nancalui-icon-text': '#babbc0',
      'nancalui-brand-active': '#ec66ab',
      'nancalui-primary': '#ec66ab',
      'nancalui-primary-hover': '#ec66ab',
      'nancalui-form-control-line-active': '#ec66ab',
      'nancalui-form-control-line-active-hover': '#ec66ab',
      'nancalui-icon-fill-active': '#ec66ab',
      'nancalui-icon-fill-active-hover': '#ec66ab',
      'nancalui-label-bg': '#ffdcee',
      'nancalui-embed-search-bg': '#ffdcee',
      'nancalui-connected-overlay-line': '#ec66ab',
      'nancalui-primary-disabled': '#fad1e6',
      'nancalui-icon-fill-active-disabled': '#fad1e6',
    },
    extends: 'infinity-theme',
    isDark: !1,
  }),
  wn = new _({
    id: 'deep-theme',
    name: '\u6DF1\u9083\u591C\u7A7A\u4E3B\u9898',
    data: {
      ...$.data,
      'nancalui-brand': '#252b3a',
      'nancalui-brand-foil': '#f3f4f7',
      'nancalui-brand-active-focus': '#252b3a',
      'nancalui-primary-active': '#252b3a',
      'nancalui-brand-active': '#252b3a',
      'nancalui-brand-hover': '#252b3a',
      'nancalui-global-bg': '#f7f8fa',
      'nancalui-base-bg': '#ffffff',
      'nancalui-text': '#252b3a',
      'nancalui-aide-text': '#505c7c',
      'nancalui-placeholder': '#9ba6bf',
      'nancalui-disabled-text': '#a8b1c7',
      'nancalui-disabled-bg': '#f7f8fa',
      'nancalui-line': '#cdd2df',
      'nancalui-dividing-line': '#e6e9ef',
      'nancalui-list-item-hover-bg': '#f3f4f7',
      'nancalui-list-item-active-bg': '#252b3a',
      'nancalui-list-item-active-hover-bg': '#252b3a',
      'nancalui-list-item-selected-bg': '#252b3a',
      'nancalui-list-item-hover-text': '#252b3a',
      'nancalui-list-item-active-text': '#ffffff',
      'nancalui-form-control-line-hover': '#A3A6AC',
      'nancalui-form-control-line': '#D7D8DA',
      'nancalui-icon-text': '#babbc0',
      'nancalui-primary': '#252b3a',
      'nancalui-primary-hover': '#252b3a',
      'nancalui-form-control-line-active': '#252b3a',
      'nancalui-form-control-line-active-hover': '#252b3a',
      'nancalui-icon-fill-active': '#252b3a',
      'nancalui-icon-fill-active-hover': '#252b3a',
      'nancalui-connected-overlay-line': '#252b3a',
      'nancalui-primary-disabled': '#bebfc4',
      'nancalui-icon-fill-active-disabled': '#bebfc4',
    },
    extends: 'infinity-theme',
    isDark: !1,
  }),
  Cn = new _({
    id: 'galaxy-theme',
    name: '\u8FFD\u5149\u4E3B\u9898',
    data: {
      ...ae.data,
      'nancalui-brand-foil': '#F7F8FA',
      'nancalui-global-bg': '#000000',
      'nancalui-base-bg': '#1F1F1F',
      'nancalui-text': '#F5F5F5',
      'nancalui-aide-text': '#A3A3A3',
      'nancalui-placeholder': '#616161',
      'nancalui-disabled-text': '#838383',
      'nancalui-disabled-bg': '#3F3F3F',
      'nancalui-line': '#565656',
      'nancalui-dividing-line': '#303030',
      'nancalui-list-item-hover-bg': '#313131',
      'nancalui-list-item-active-bg': '#30333D',
      'nancalui-list-item-active-hover-bg': '#30333D',
      'nancalui-list-item-selected-bg': '#30333D',
      'nancalui-list-item-hover-text': '#F5F5F5',
      'nancalui-list-item-active-text': '#2F5CD6',
      'nancalui-primary-disabled': '#3f3f3f',
      'nancalui-form-control-bg': '#292a2e',
      'nancalui-form-control-line': '#565656',
      'nancalui-icon-text': '#A3A3A3',
      'nancalui-connected-overlay-bg': '#282828',
      'nancalui-fullscreen-overlay-bg': '#282828',
      'nancalui-warning-line': '#FFDCA3',
      'nancalui-warning-bg': '#4b2e14',
      'nancalui-success-line': '#75EBC2 ',
      'nancalui-success-bg': '#123d32',
      'nancalui-danger-line': '#9f4844',
      'nancalui-danger-bg': '#4a2120',
      'nancalui-info-line': '#3c5091',
      'nancalui-info-bg': '#1c2543',
      'nancalui-default-bg': '#313131',
      'nancalui-border-radius': '4px',
      'nancalui-font-size': '12px',
      'nancalui-font-size-md': '12px',
      'nancalui-font-size-card-title': '16px',
      'nancalui-shadow-length-fullscreen-overlay': '0 0 6px 0',
      'nancalui-border-radius-card': '4px',
    },
    extends: 'nancalui-dark-theme',
    isDark: !0,
  })function be(t){this.options=t,!t.deferSetup&&this.setup()}be.prototype={constructor:be,setup:function(){this.options.setup&&this.options.setup(),this.initialised=!0},on:function(){!this.initialised&&this.setup(),this.options.match&&this.options.match()},off:function(){this.options.unmatch&&this.options.unmatch()},destroy:function(){this.options.destroy?this.options.destroy():this.off()},equals:function(t){return this.options===t||this.options.match===t}};var Fn=be;function Dn(t,n){var e=0,a=t.length,r;for(e;e<a&&(r=n(t[e],e),r!==!1);e++);}function An(t){return Object.prototype.toString.apply(t)==="[object Array]"}function xn(t){return typeof t=="function"}var qe={isFunction:xn,isArray:An,each:Dn},_n=Fn,ge=qe.each;function ye(t,n){this.query=t,this.isUnconditional=n,this.handlers=[],this.mql=window.matchMedia(t);var e=this;this.listener=function(a){e.mql=a.currentTarget||a,e.assess()},this.mql.addListener(this.listener)}ye.prototype={constuctor:ye,addHandler:function(t){var n=new _n(t);this.handlers.push(n),this.matches()&&n.on()},removeHandler:function(t){var n=this.handlers;ge(n,function(e,a){if(e.equals(t))return e.destroy(),!n.splice(a,1)})},matches:function(){return this.mql.matches||this.isUnconditional},clear:function(){ge(this.handlers,function(t){t.destroy()}),this.mql.removeListener(this.listener),this.handlers.length=0},assess:function(){var t=this.matches()?"on":"off";ge(this.handlers,function(n){n[t]()})}};var kn=ye,Tn=kn,Se=qe,On=Se.each,Qe=Se.isFunction,Bn=Se.isArray;function Ee(){if(typeof window!="undefined"){if(!window.matchMedia)throw new Error("matchMedia not present, legacy browsers require a polyfill");this.queries={},this.browserIsIncapable=!window.matchMedia("only all").matches}}Ee.prototype={constructor:Ee,register:function(t,n,e){var a=this.queries,r=e&&this.browserIsIncapable;return a[t]||(a[t]=new Tn(t,r)),Qe(n)&&(n={match:n}),Bn(n)||(n=[n]),On(n,function(o){Qe(o)&&(o={match:o}),a[t].addHandler(o)}),this},unregister:function(t,n){var e=this.queries[t];return e&&(n?e.removeHandler(n):(e.clear(),delete this.queries[t])),this}};var jn=Ee,Vn=jn,He=new Vn,We=B({__proto__:null,default:He},[He]),we=function(t,n){return we=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,a){e.__proto__=a}||function(e,a){for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])},we(t,n)};function Y(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");we(t,n);function e(){this.constructor=t}t.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}function Ce(t){var n=typeof Symbol=="function"&&Symbol.iterator,e=n&&t[n],a=0;if(e)return e.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&a>=t.length&&(t=void 0),{value:t&&t[a++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function re(t,n){var e=typeof Symbol=="function"&&t[Symbol.iterator];if(!e)return t;var a=e.call(t),r,o=[],i;try{for(;(n===void 0||n-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(d){i={error:d}}finally{try{r&&!r.done&&(e=a.return)&&e.call(a)}finally{if(i)throw i.error}}return o}function ie(t,n,e){if(e||arguments.length===2)for(var a=0,r=n.length,o;a<r;a++)(o||!(a in n))&&(o||(o=Array.prototype.slice.call(n,0,a)),o[a]=n[a]);return t.concat(o||Array.prototype.slice.call(n))}typeof SuppressedError=="function"&&SuppressedError;function z(t){return typeof t=="function"}function Ne(t){var n=function(a){Error.call(a),a.stack=new Error().stack},e=t(n);return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}var Fe=Ne(function(t){return function(e){t(this),this.message=e?e.length+` errors occurred during unsubscription:
`+e.map(function(a,r){return r+1+") "+a.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=e}});function De(t,n){if(t){var e=t.indexOf(n);0<=e&&t.splice(e,1)}}var oe=function(){function t(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var n,e,a,r,o;if(!this.closed){this.closed=!0;var i=this._parentage;if(i)if(this._parentage=null,Array.isArray(i))try{for(var d=Ce(i),u=d.next();!u.done;u=d.next()){var c=u.value;c.remove(this)}}catch(b){n={error:b}}finally{try{u&&!u.done&&(e=d.return)&&e.call(d)}finally{if(n)throw n.error}}else i.remove(this);var l=this.initialTeardown;if(z(l))try{l()}catch(b){o=b instanceof Fe?b.errors:[b]}var s=this._finalizers;if(s){this._finalizers=null;try{for(var h=Ce(s),m=h.next();!m.done;m=h.next()){var v=m.value;try{Ye(v)}catch(b){o=o!=null?o:[],b instanceof Fe?o=ie(ie([],re(o)),re(b.errors)):o.push(b)}}}catch(b){a={error:b}}finally{try{m&&!m.done&&(r=h.return)&&r.call(h)}finally{if(a)throw a.error}}}if(o)throw new Fe(o)}},t.prototype.add=function(n){var e;if(n&&n!==this)if(this.closed)Ye(n);else{if(n instanceof t){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(e=this._finalizers)!==null&&e!==void 0?e:[]).push(n)}},t.prototype._hasParent=function(n){var e=this._parentage;return e===n||Array.isArray(e)&&e.includes(n)},t.prototype._addParent=function(n){var e=this._parentage;this._parentage=Array.isArray(e)?(e.push(n),e):e?[e,n]:n},t.prototype._removeParent=function(n){var e=this._parentage;e===n?this._parentage=null:Array.isArray(e)&&De(e,n)},t.prototype.remove=function(n){var e=this._finalizers;e&&De(e,n),n instanceof t&&n._removeParent(this)},t.EMPTY=function(){var n=new t;return n.closed=!0,n}(),t}(),Ge=oe.EMPTY;function $e(t){return t instanceof oe||t&&"closed"in t&&z(t.remove)&&z(t.add)&&z(t.unsubscribe)}function Ye(t){z(t)?t():t.unsubscribe()}var Ae={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},xe={setTimeout:function(t,n){for(var e=[],a=2;a<arguments.length;a++)e[a-2]=arguments[a];var r=xe.delegate;return r!=null&&r.setTimeout?r.setTimeout.apply(r,ie([t,n],re(e))):setTimeout.apply(void 0,ie([t,n],re(e)))},clearTimeout:function(t){var n=xe.delegate;return((n==null?void 0:n.clearTimeout)||clearTimeout)(t)},delegate:void 0};function Mn(t){xe.setTimeout(function(){throw t})}function Ke(){}var le=null;function ce(t){if(Ae.useDeprecatedSynchronousErrorHandling){var n=!le;if(n&&(le={errorThrown:!1,error:null}),t(),n){var e=le,a=e.errorThrown,r=e.error;if(le=null,a)throw r}}else t()}var Je=function(t){Y(n,t);function n(e){var a=t.call(this)||this;return a.isStopped=!1,e?(a.destination=e,$e(e)&&e.add(a)):a.destination=Rn,a}return n.create=function(e,a,r){return new ke(e,a,r)},n.prototype.next=function(e){this.isStopped||this._next(e)},n.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},n.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},n.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},n.prototype._next=function(e){this.destination.next(e)},n.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},n.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},n}(oe),In=Function.prototype.bind;function _e(t,n){return In.call(t,n)}var Ln=function(){function t(n){this.partialObserver=n}return t.prototype.next=function(n){var e=this.partialObserver;if(e.next)try{e.next(n)}catch(a){se(a)}},t.prototype.error=function(n){var e=this.partialObserver;if(e.error)try{e.error(n)}catch(a){se(a)}else se(n)},t.prototype.complete=function(){var n=this.partialObserver;if(n.complete)try{n.complete()}catch(e){se(e)}},t}(),ke=function(t){Y(n,t);function n(e,a,r){var o=t.call(this)||this,i;if(z(e)||!e)i={next:e!=null?e:void 0,error:a!=null?a:void 0,complete:r!=null?r:void 0};else{var d;o&&Ae.useDeprecatedNextContext?(d=Object.create(e),d.unsubscribe=function(){return o.unsubscribe()},i={next:e.next&&_e(e.next,d),error:e.error&&_e(e.error,d),complete:e.complete&&_e(e.complete,d)}):i=e}return o.destination=new Ln(i),o}return n}(Je);function se(t){Mn(t)}function Pn(t){throw t}var Rn={closed:!0,next:Ke,error:Pn,complete:Ke},zn=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Un(t){return t}function qn(t){return t.length===0?Un:t.length===1?t[0]:function(e){return t.reduce(function(a,r){return r(a)},e)}}var Xe=function(){function t(n){n&&(this._subscribe=n)}return t.prototype.lift=function(n){var e=new t;return e.source=this,e.operator=n,e},t.prototype.subscribe=function(n,e,a){var r=this,o=Hn(n)?n:new ke(n,e,a);return ce(function(){var i=r,d=i.operator,u=i.source;o.add(d?d.call(o,u):u?r._subscribe(o):r._trySubscribe(o))}),o},t.prototype._trySubscribe=function(n){try{return this._subscribe(n)}catch(e){n.error(e)}},t.prototype.forEach=function(n,e){var a=this;return e=Ze(e),new e(function(r,o){var i=new ke({next:function(d){try{n(d)}catch(u){o(u),i.unsubscribe()}},error:o,complete:r});a.subscribe(i)})},t.prototype._subscribe=function(n){var e;return(e=this.source)===null||e===void 0?void 0:e.subscribe(n)},t.prototype[zn]=function(){return this},t.prototype.pipe=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return qn(n)(this)},t.prototype.toPromise=function(n){var e=this;return n=Ze(n),new n(function(a,r){var o;e.subscribe(function(i){return o=i},function(i){return r(i)},function(){return a(o)})})},t.create=function(n){return new t(n)},t}();function Ze(t){var n;return(n=t!=null?t:Ae.Promise)!==null&&n!==void 0?n:Promise}function Qn(t){return t&&z(t.next)&&z(t.error)&&z(t.complete)}function Hn(t){return t&&t instanceof Je||Qn(t)&&$e(t)}var Wn=Ne(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),en=function(t){Y(n,t);function n(){var e=t.call(this)||this;return e.closed=!1,e.currentObservers=null,e.observers=[],e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return n.prototype.lift=function(e){var a=new nn(this,this);return a.operator=e,a},n.prototype._throwIfClosed=function(){if(this.closed)throw new Wn},n.prototype.next=function(e){var a=this;ce(function(){var r,o;if(a._throwIfClosed(),!a.isStopped){a.currentObservers||(a.currentObservers=Array.from(a.observers));try{for(var i=Ce(a.currentObservers),d=i.next();!d.done;d=i.next()){var u=d.value;u.next(e)}}catch(c){r={error:c}}finally{try{d&&!d.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}}})},n.prototype.error=function(e){var a=this;ce(function(){if(a._throwIfClosed(),!a.isStopped){a.hasError=a.isStopped=!0,a.thrownError=e;for(var r=a.observers;r.length;)r.shift().error(e)}})},n.prototype.complete=function(){var e=this;ce(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var a=e.observers;a.length;)a.shift().complete()}})},n.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(n.prototype,"observed",{get:function(){var e;return((e=this.observers)===null||e===void 0?void 0:e.length)>0},enumerable:!1,configurable:!0}),n.prototype._trySubscribe=function(e){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,e)},n.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},n.prototype._innerSubscribe=function(e){var a=this,r=this,o=r.hasError,i=r.isStopped,d=r.observers;return o||i?Ge:(this.currentObservers=null,d.push(e),new oe(function(){a.currentObservers=null,De(d,e)}))},n.prototype._checkFinalizedStatuses=function(e){var a=this,r=a.hasError,o=a.thrownError,i=a.isStopped;r?e.error(o):i&&e.complete()},n.prototype.asObservable=function(){var e=new Xe;return e.source=this,e},n.create=function(e,a){return new nn(e,a)},n}(Xe),nn=function(t){Y(n,t);function n(e,a){var r=t.call(this)||this;return r.destination=e,r.source=a,r}return n.prototype.next=function(e){var a,r;(r=(a=this.destination)===null||a===void 0?void 0:a.next)===null||r===void 0||r.call(a,e)},n.prototype.error=function(e){var a,r;(r=(a=this.destination)===null||a===void 0?void 0:a.error)===null||r===void 0||r.call(a,e)},n.prototype.complete=function(){var e,a;(a=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||a===void 0||a.call(e)},n.prototype._subscribe=function(e){var a,r;return(r=(a=this.source)===null||a===void 0?void 0:a.subscribe(e))!==null&&r!==void 0?r:Ge},n}(en),tn={now:function(){return(tn.delegate||Date).now()},delegate:void 0},Nn=function(t){Y(n,t);function n(e,a,r){e===void 0&&(e=1/0),a===void 0&&(a=1/0),r===void 0&&(r=tn);var o=t.call(this)||this;return o._bufferSize=e,o._windowTime=a,o._timestampProvider=r,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=a===1/0,o._bufferSize=Math.max(1,e),o._windowTime=Math.max(1,a),o}return n.prototype.next=function(e){var a=this,r=a.isStopped,o=a._buffer,i=a._infiniteTimeWindow,d=a._timestampProvider,u=a._windowTime;r||(o.push(e),!i&&o.push(d.now()+u)),this._trimBuffer(),t.prototype.next.call(this,e)},n.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var a=this._innerSubscribe(e),r=this,o=r._infiniteTimeWindow,i=r._buffer,d=i.slice(),u=0;u<d.length&&!e.closed;u+=o?1:2)e.next(d[u]);return this._checkFinalizedStatuses(e),a},n.prototype._trimBuffer=function(){var e=this,a=e._bufferSize,r=e._timestampProvider,o=e._buffer,i=e._infiniteTimeWindow,d=(i?1:2)*a;if(a<1/0&&d<o.length&&o.splice(0,o.length-d),!i){for(var u=r.now(),c=0,l=1;l<o.length&&o[l]<=u;l+=2)c=l;c&&o.splice(0,c+1)}},n}(en);const R=class{constructor(){C(this,"prefersColorSchemeSubject",new Nn(1));C(this,"prefersColorSchemeChange",this.prefersColorSchemeSubject.asObservable());C(this,"handleColorSchemeChange",n=>{this.prefersColorSchemeSubject.next(n)})}register(){R.enquire.register.bind(We)(R.Query.light,{match:()=>{this.handleColorSchemeChange("light")}}).register(R.Query.dark,{match:()=>{this.handleColorSchemeChange("dark")}}),this.prefersColorSchemeSubject.next(this.getInitValue())}unregister(){R.enquire.unregister(R.Query.light).unregister(R.Query.dark),this.prefersColorSchemeSubject.complete()}getInitValue(){return typeof window=="undefined"?"light":window.matchMedia(R.Query.light).matches&&"light"||window.matchMedia(R.Query.dark).matches&&"dark"||"no-preference"}};let W=R;C(W,"enquire",We),(t=>{(n=>{n.light="screen and (prefers-color-scheme: light)",n.dark="screen and (prefers-color-scheme: dark)",n.noPreferences="screen and (prefers-color-scheme: light)"})(t.Query||(t.Query={}))})(W||(W={}));class Te{constructor(){C(this,"eventBusCore",[])}areFuncEqual(n,e){return n.toString()===e.toString()}isKeyValueObjInArr(n,e,a){return n.filter(o=>o[e]===a).length>0}removeFuncInFuncArr(n,e){for(let a=0;a<n.length;a++)this.areFuncEqual(n[a],e)&&n.splice(a,1);return n}getKeyValueObjInArr(n,e,a){return n.filter(o=>o[e]===a)[0]}addEvent(n,e){this.isKeyValueObjInArr(this.eventBusCore,"eventName",n)?this.eventBusCore=this.eventBusCore.map(a=>(a.eventName===n&&a.eventFuncArr.push(e),a)):this.eventBusCore.push({eventName:n,eventFuncArr:[e]})}add(n,e){if(!!n){if(typeof e=="function")for(let a=1;a<arguments.length;a++)this.addEvent(n,arguments[a]);typeof e=="object"&&e.forEach&&e.forEach(a=>{this.addEvent(n,a)})}}remove(n,e){if(!!n){for(let a=0;a<this.eventBusCore.length;a++)if(this.eventBusCore[a].eventName===n){if(arguments.length===1)return this.eventBusCore.splice(a,1);const r=this.eventBusCore.splice(a,1)[0];if(typeof e=="function")for(let o=1;o<arguments.length;o++)r.eventFuncArr=this.removeFuncInFuncArr(r.eventFuncArr,arguments[o]);if(typeof e=="object"&&e.length)for(let o=0;o<e.length;o++)r.eventFuncArr=this.removeFuncInFuncArr(r.eventFuncArr,e[o]);this.eventBusCore.push(r)}}}trigger(n,e){const a=this.getKeyValueObjInArr(this.eventBusCore,"eventName",n);a&&(a.eventFuncArr||[]).forEach(r=>{r.apply(this,e)})}}class an{getDataFromNameSpace(n){return typeof window=="undefined"?null:window[n]}setDataFromNameSpace(n,e){typeof window!="undefined"&&(window[n]=e)}}class rn{tryGetLocalStorage(n){return typeof window=="undefined"?null:window.localStorage.getItem(n)}trySetLocalStorage(n,e){typeof window!="undefined"&&window.localStorage.setItem(n,e)}}class on{constructor(n,e,a){C(this,"eventBus");C(this,"storage");C(this,"context");C(this,"currentTheme");C(this,"contentElement");C(this,"colorTransitionElement");C(this,"extraData");C(this,"_appendedClasses");C(this,"mediaQuery");this.eventBus=n===void 0?new Te:n,this.storage=e===void 0?new rn:e,this.context=a===void 0?new an:a}set appendClasses(n){this._appendedClasses&&this.removeAppendedClass(this._appendedClasses),n&&this.addAppendClass(n),this._appendedClasses=n}get appendClasses(){return this._appendedClasses}initializeTheme(n,e){const a=n||this.storage.tryGetLocalStorage(D.userLastPreferTheme)||this.context.getDataFromNameSpace(D.currentTheme);let r;if(a){const o=this.context.getDataFromNameSpace(D.themeCollection);o&&Object.keys(o).length>0&&(r=o[a])}this.currentTheme=r||$,this.createColorTransition(),!(!r&&e)&&this.applyTheme(this.currentTheme)}formatCSSVariables(n){return Object.keys(n).map(e=>"--"+e+":"+n[e]).join(";")}applyTheme(n){if(this.addColorTransition(),this.currentTheme=n,!this.contentElement){const e=document.getElementById(D.styleElementId);e?this.contentElement=e:(this.contentElement=document.createElement("style"),this.contentElement.id=D.styleElementId,document.head.appendChild(this.contentElement))}this.contentElement.innerText=":root { "+this.formatCSSVariables(n.data)+" }",this.contentElement.setAttribute(D.uiThemeAttributeName,this.currentTheme.id),document.body.setAttribute(D.uiThemeAttributeName,this.currentTheme.id),this.applyExtraData(),this.saveCustomTheme(this.currentTheme),this.notify(n,"themeChanged"),setTimeout(()=>{this.removeColorTransition()},500)}saveCustomTheme(n){this.storage.trySetLocalStorage(D.userLastPreferTheme,n.id),this.storage.trySetLocalStorage(D.userLastPreferThemeData,JSON.stringify(n.data)),this.context.setDataFromNameSpace(D.currentTheme,n.id)}notify(n,e){!this.eventBus||this.eventBus.trigger(e,n)}setEventBus(n){this.eventBus=n}addAppendClass(n){document.body.classList.add(...n)}removeAppendedClass(n){document.body.classList.remove(...n)}setExtraData(n,e=!1){this.extraData=n,e&&this.applyExtraData()}applyExtraData(){const n=this.currentTheme;this.extraData&&this.extraData[n.id]&&this.extraData[n.id].cssVariables&&(this.contentElement.innerText=":root { "+this.formatCSSVariables(n.data)+" }:root { "+this.formatCSSVariables(this.extraData[n.id].cssVariables)+" }"),this.extraData&&this.extraData[n.id]&&this.extraData[n.id].appendClasses?this.appendClasses=this.extraData[n.id].appendClasses:this.appendClasses=void 0}unloadTheme(){this.contentElement&&document.contains(this.contentElement)&&this.contentElement.parentElement.removeChild(this.contentElement),this.appendClasses&&(this.appendClasses=void 0)}registerMediaQuery(){this.mediaQuery||(this.mediaQuery=new W),this.mediaQuery.register()}unregisterMediaQuery(){!this.mediaQuery||(this.mediaQuery.unregister(),this.mediaQuery=void 0)}createColorTransition(){this.colorTransitionElement=document.createElement("style"),this.colorTransitionElement.id=D.transitionStyleElementId,this.colorTransitionElement.innerText=`
      * { transition: background .3s ease-out, background-color .3s ease-out,
                    border .3s ease-out, border-color .3s ease-out,
                    box-shadow .3s ease-out, box-shadow-color .3s ease-out}
    `}addColorTransition(){document.head.appendChild(this.colorTransitionElement)}removeColorTransition(){!this.colorTransitionElement.parentElement||this.colorTransitionElement.parentElement.removeChild(this.colorTransitionElement)}}function Gn(t,n,e,a=!1,r=!1){if(typeof window=="undefined")return null;window[D.themeCollection]=t||{"nancalui-light-theme":te,"nancalui-dark-theme":ae},window[D.currentTheme]=n||"nancalui-light-theme";const o=window.globalEventBus||new Te,i=new on(o);return window[D.themeService]=i,i.setExtraData(e||{"nancalui-dark-theme":{appendClasses:["dark-mode"]}}),i.initializeTheme(null,r),a&&ln(),i}function $n(t){if(typeof window=="undefined")return null;const n=window[D.themeService];return n.registerMediaQuery(),n.mediaQuery.prefersColorSchemeChange.subscribe(e=>{e==="dark"?n.applyTheme(window[D.themeCollection][t&&t.darkThemeName||"nancalui-dark-theme"]):n.applyTheme(window[D.themeCollection][t&&t.lightThemeName||"nancalui-light-theme"])})}function Yn(t){if(typeof window=="undefined")return null;t&&t.unsubscribe(),window[D.themeService].unregisterMediaQuery()}function ln(){if(typeof window=="undefined")return null;if(window.CSS&&CSS.supports&&CSS.supports("(--a: 0)")||!1)return;I({watch:!0,silent:!0});const n=new MutationObserver(function(a){a.forEach(function(r){I({watch:!1,silent:!0}),I({watch:!0,silent:!0})})}),e={attributes:!0,attributeFilter:[D.uiThemeAttributeName]};n.observe(document.querySelector(`#${D.styleElementId}`),e)}E.ContextService=an,E.EventBus=Te,E.StorageService=rn,E.Theme=_,E.ThemeService=on,E.ThemeServiceFollowSystemOff=Yn,E.ThemeServiceFollowSystemOn=$n,E.ThemeServiceInit=Gn,E.deepTheme=wn,E.galaxyTheme=Cn,E.ieSupportCssVar=ln,E.infinityTheme=$,E.nancaluiDarkTheme=ae,E.nancaluiGreenDarkTheme=yn,E.nancaluiGreenTheme=gn,E.nancaluiLightTheme=te,E.provenceTheme=Sn,E.sweetTheme=En,Object.defineProperties(E,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
