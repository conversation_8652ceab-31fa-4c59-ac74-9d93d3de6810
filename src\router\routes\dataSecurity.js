import Layout from '@/layout'

// 功能模块-数据安全
export default [
  {
    path: '/dataSecurity',
    component: Layout,
    redirect: '/dataSecurity/secretManagement',
    name: 'dataSecurity',
    code: 'dataSecurity',
    meta: {
      title: '数据安全',
      code: 'dataSecurity',
      icon: 'icon-dataSecurity',
      parentRouterName: 'assetsManage',
    },
    children: [
      //密级管理
      {
        path: 'secretManagement',
        name: 'secretManagement',
        code: 'secretManagement',
        hidden: true,
        redirect: '/dataSecurity/secretManagement/list',
        meta: {
          title: '密级管理',
          code: 'secretManagement',
          icon: 'icon-dataSecurity',
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/secret-management/index'),
        children: [
          {
            path: 'list',
            name: 'secretManagementList',
            hidden: true,
            component: () => import('@/views/data-security/secret-management/list'),
            meta: {
              title: '密级管理',
              parentRouterName: 'assetsManage',
              keepAlive: true,
              code: 'secretManagement',
            },
          },
          {
            path: 'see',
            name: 'secretManagementSee',
            hidden: true,
            component: () => import('@/views/data-security/secret-management/see'),
            meta: { title: '密级管理查看', parentRouterName: 'assetsManage' },
          },
        ],
      },
      // 算法模板
      {
        path: 'desensitizationAlgorithmTemplate',
        name: 'desensitizationAlgorithmTemplate',
        code: 'desensitizationAlgorithmTemplate',
        hidden: true,
        redirect: '/dataSecurity/desensitizationAlgorithmTemplate/list',
        meta: {
          title: '算法模板',
          code: 'desensitizationAlgorithmTemplate',
          icon: 'icon-dataSecurity',
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/desensitization-algorithm-template/index'),
        children: [
          {
            path: 'list',
            name: 'desensitizationAlgorithmTemplateList',
            hidden: true,
            component: () =>
              import('@/views/data-security/desensitization-algorithm-template/list'),
            meta: {
              title: '脱敏算法模板',
              parentRouterName: 'assetsManage',
              code: 'desensitizationAlgorithmTemplate',
            },
          },
        ],
      },
      // 脱敏算法
      {
        path: 'desensitizationAlgorithm',
        name: 'desensitizationAlgorithm',
        code: 'desensitizationAlgorithm',
        hidden: true,
        redirect: '/dataSecurity/desensitizationAlgorithm/list',
        meta: {
          title: '脱敏算法',
          code: 'desensitizationAlgorithm',
          icon: 'icon-dataSecurity',
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/desensitization-algorithm/index'),
        children: [
          {
            path: 'list',
            name: 'desensitizationAlgorithmList',
            hidden: true,
            component: () => import('@/views/data-security/desensitization-algorithm/list'),
            meta: {
              title: '脱敏算法',
              parentRouterName: 'assetsManage',
              code: 'desensitizationAlgorithm',
            },
          },
        ],
      },
      // 脱敏策略
      {
        path: 'desensitizationStrategy',
        name: 'desensitizationStrategy',
        code: 'desensitizationStrategy',
        hidden: true,
        redirect: '/dataSecurity/desensitizationStrategy/list',
        meta: {
          title: '脱敏策略',
          code: 'desensitizationStrategy',
          icon: 'icon-dataSecurity',
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/desensitization-strategy/index'),
        children: [
          {
            path: 'list',
            name: 'desensitizationStrategyList',
            hidden: true,
            component: () => import('@/views/data-security/desensitization-strategy/list'),
            meta: {
              title: '脱敏策略',
              parentRouterName: 'assetsManage',
              code: 'desensitizationStrategy',
            },
          },
        ],
      },
      // 数据互信
      {
        path: 'dataImplantLink1',
        name: 'link1',
        code: 'link1',
        hidden: true,
        meta: {
          title: '数据互信',
          code: 'link1',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
      // 融合计算
      {
        path: 'dataImplantLink2',
        name: 'link2',
        code: 'link2',
        hidden: true,
        meta: {
          title: '融合计算',
          code: 'link2',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
      // 数据服务存证
      {
        path: 'dataImplantLink3',
        name: 'link3',
        code: 'link3',
        hidden: true,
        meta: {
          title: '数据服务存证',
          code: 'link3',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
      // 指纹新增
      {
        path: 'dataImplantLink4',
        name: 'link4',
        code: 'link4',
        hidden: true,
        meta: {
          title: '指纹新增',
          code: 'link4',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
      // 指纹添加
      {
        path: 'dataImplantLink5',
        name: 'link5',
        code: 'link5',
        hidden: true,
        meta: {
          title: '指纹添加',
          code: 'link5',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
      // 指纹解密
      {
        path: 'dataImplantLink6',
        name: 'link6',
        code: 'link6',
        hidden: true,
        meta: {
          title: '指纹解密',
          code: 'link6',
          icon: 'icon-dataSecurity',
          isIframe: true,
          parentRouterName: 'assetsManage',
        },
        component: () => import('@/views/data-security/data-implant/list'),
      },
    ],
  },
]
