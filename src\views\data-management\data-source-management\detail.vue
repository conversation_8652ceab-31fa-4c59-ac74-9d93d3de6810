<!-- 结构化数据源新增 -->
<template>
  <div class="data-source-add publicFormCss">
    <div class="data-source-add-box">
      <div
        :class="{
          'scroll-bar-style': true,
          'data-source-add-box-content': true,
          'only-detail': state.type === 'DETAIL',
        }"
        v-loading="state.loading"
      >
        <!-- 展示结构化数据 排除KAFKA -->
        <n-form
          v-if="state.type === 'DETAIL'"
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="126px"
          label-align="start"
          :pop-position="['right']"
          class="demo-ruleForm disable-hide-border disabled-form"
        >
          <n-form-item label="数据源名称：" field="name">
            <n-input
              v-model="state.ruleForm.name"
              maxlength="30"
              placeholder=" "
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <!--          <n-form-item label="数据环境：" field="envType">-->
          <!--            <n-select-->
          <!--              v-model="state.ruleForm.envType"-->
          <!--              placeholder="请选择数据环境"-->
          <!--              :disabled="state.disabled"-->
          <!--              :noborder="state.disabled"-->
          <!--            >-->
          <!--              <n-option name="生产环境" value="OFFICIAL" />-->
          <!--              <n-option name="开发环境" value="TEST" />-->
          <!--            </n-select>-->
          <!--          </n-form-item>-->

          <n-form-item label="数据分类：" field="dataCategoryId">
            <TreeSelect
              v-model="state.ruleForm.dataCategoryId"
              :data="state.treeData"
              :props="{
                label: 'name',
                children: 'children',
                value: 'id',
              }"
              check-strictly
              :render-after-expand="false"
              v-if="state.treeShow"
              placeholder="请选择数据分类"
              style="width: 100%"
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>

          <n-form-item
            label="字符编码："
            v-if="state.ruleForm.datasourceType === 'ORACLE'"
            field="charcter"
          >
            <n-select
              v-model="state.ruleForm.charcter"
              placeholder="请选择字符编码"
              :disabled="state.disabled"
              :noborder="state.disabled"
            >
              <n-option v-for="item in state.charcterOpt" :key="item" :name="item" :value="item" />
            </n-select>
          </n-form-item>

          <n-form-item
            label="是否同步数据源："
            v-if="state.ruleForm.datasourceType === 'ORACLE'"
            field="charcter"
          >
            <n-radio-group direction="row" v-model="state.asyncUse">
              <n-radio :value="true"> 是 </n-radio>
              <n-radio :value="false"> 否 </n-radio>
            </n-radio-group>
          </n-form-item>

          <n-form-item label="数据密级：" field="confidentialityLevel">
            <n-select
              v-model="state.ruleForm.confidentialityLevel"
              placeholder="请选择数据密级"
              :disabled="state.disabled"
              :noborder="state.disabled"
            >
              <n-option
                v-for="item in state.confidentialityLevelOptions"
                :key="item.value"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
          </n-form-item>

          <n-form-item
            v-if="state.ruleForm.datasourceType !== 'SFTP'"
            label="数据源类型："
            field="datasourceType"
          >
            <n-input v-model="state.ruleForm.datasourceType" placeholder=" " disabled />
          </n-form-item>

          <n-form-item label="创建时间：" field="createTime">
            <n-input
              v-model="state.ruleForm.createTime"
              placeholder=" "
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item label="创建人：" field="createByName">
            <n-input
              v-model="state.ruleForm.createByName"
              placeholder=" "
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
          <n-form-item label="描述信息：" field="description">
            <n-input
              v-model="state.ruleForm.description"
              maxlength="200"
              placeholder=" "
              :disabled="state.disabled"
              :noborder="state.disabled"
            />
          </n-form-item>
        </n-form>

        <!-- 所有类型 -->
        <n-form
          v-else
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="160px"
          label-align="start"
          :pop-position="['right']"
          class="demo-ruleForm"
        >
          <n-form-item label="数据源名称：" field="name">
            <n-input
              v-model="state.ruleForm.name"
              maxlength="30"
              placeholder="请输入数据源名称"
              :disabled="state.disabled"
            />
          </n-form-item>
          <!--          <n-form-item label="数据环境：" field="envType">-->
          <!--            <n-select-->
          <!--              v-model="state.ruleForm.envType"-->
          <!--              placeholder="请选择数据环境"-->
          <!--              allow-clear-->
          <!--              filter-->
          <!--            >-->
          <!--              <n-option name="生产环境" value="OFFICIAL" />-->
          <!--              <n-option name="开发环境" value="TEST" />-->
          <!--            </n-select>-->
          <!--          </n-form-item>-->
          <n-form-item label="数据分类：" field="dataCategoryId">
            <TreeSelect
              v-model="state.ruleForm.dataCategoryId"
              :data="state.treeData"
              :props="{
                label: 'name',
                children: 'children',
                value: 'id',
              }"
              v-if="state.treeShow"
              check-strictly
              :render-after-expand="false"
              placeholder="请选择数据分类"
              style="width: 100%"
              @change="dataTypeChange"
              size="sm"
            />
          </n-form-item>

          <n-form-item
            label="字符编码："
            v-if="state.ruleForm.datasourceType === 'ORACLE'"
            field="charcter"
          >
            <n-select
              v-model="state.ruleForm.charcter"
              placeholder="请选择字符编码"
              :disabled="state.disabled"
              :noborder="state.disabled"
            >
              <n-option v-for="item in state.charcterOpt" :key="item" :name="item" :value="item" />
            </n-select>
          </n-form-item>

          <n-form-item
            label="是否同步数据源："
            v-if="state.ruleForm.datasourceType === 'ORACLE'"
            field="charcter"
          >
            <n-radio-group direction="row" v-model="state.asyncUse">
              <n-radio :value="true"> 是 </n-radio>
              <n-radio :value="false"> 否 </n-radio>
            </n-radio-group>
          </n-form-item>

          <n-form-item label="数据密级：" field="confidentialityLevel">
            <n-select
              v-model="state.ruleForm.confidentialityLevel"
              placeholder="请选择数据密级"
              :disabled="state.disabled"
              :noborder="state.disabled"
            >
              <n-option
                v-for="item in state.confidentialityLevelOptions"
                :key="item.value"
                :name="item.name"
                :value="item.value"
              />
            </n-select>
          </n-form-item>

          <n-form-item
            v-if="state.ruleForm.datasourceType === 'SFTP'"
            label="连接方式："
            field="sftpConnectType"
          >
            <n-select
              v-model="state.ruleForm.sftpConnectType"
              placeholder="请选择连接方式"
              :disabled="state.disabled"
              :noborder="state.disabled"
              @value-change="sftpConnectTypeChangeFn"
            >
              <n-option name="SFTP" value="SFTP" />
              <n-option name="FTP" value="FTP" />
            </n-select>
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.datasourceType !== 'SFTP'"
            label="数据源类型："
            field="datasourceType"
          >
            <n-input v-model="state.ruleForm.datasourceType" placeholder="请选择数据" disabled />
          </n-form-item>

          <n-form-item label="IP地址Host：" field="host">
            <n-input
              v-model="state.ruleForm.host"
              placeholder="请输入IP地址"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item label="端口号Port：" field="port">
            <n-input
              v-model="state.ruleForm.port"
              placeholder="请输入端口"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.datasourceType === 'ORACLE'"
            label="数据库："
            field="serviceName"
          >
            <n-input
              v-model="state.ruleForm.serviceName"
              placeholder="请输入数据库名称"
              :disabled="state.disabled"
            />
          </n-form-item>

          <n-form-item
            v-if="state.ruleForm.datasourceType === 'HIVE'"
            label="模式Schema："
            field="database"
            :rules="[{ required: true, message: '请输入数据库模式', trigger: 'blur' }]"
          >
            <n-input
              v-model="state.ruleForm.database"
              placeholder="请输入数据库模式"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item
            v-else-if="state.ruleForm.datasourceType === 'CASSANDRA'"
            label="键空间/KeySpace："
            field="keyspace"
            :rules="[{ required: true, message: '请输入键空间/KeySpace', trigger: 'blur' }]"
          >
            <n-input
              v-model="state.ruleForm.keyspace"
              placeholder="请输入键空间/KeySpace"
              :disabled="state.disabled"
            />
          </n-form-item>

          <n-form-item
            v-else-if="
              state.ruleForm.datasourceType !== 'KAFKA' &&
              state.ruleForm.datasourceType !== 'RABBITMQ' &&
              state.ruleForm.datasourceType !== 'SFTP' &&
              state.ruleForm.datasourceType !== 'IOTDB' &&
              state.ruleForm.datasourceType !== 'ORACLE'
            "
            label="数据库Database："
            field="database"
            :rules="[{ required: true, message: '请输入数据库名称', trigger: 'blur' }]"
          >
            <n-input
              v-model="state.ruleForm.database"
              placeholder="请输入数据库"
              :disabled="state.disabled"
            />
          </n-form-item>

          <!--  POSTGRESQL   KING_BASE DM-->
          <n-form-item
            v-if="
              state.ruleForm.datasourceType === 'POSTGRESQL' ||
              state.ruleForm.datasourceType === 'KING_BASE' ||
              state.ruleForm.datasourceType === 'DM'
            "
            label="模式Schema："
            field="defaultSchema"
            :rules="[{ required: true, message: '请输入数据库模式', trigger: 'blur' }]"
          >
            <n-input
              v-model="state.ruleForm.defaultSchema"
              placeholder="请输入数据库模式"
              :disabled="state.disabled"
            />
          </n-form-item>

          <n-form-item
            v-if="state.ruleForm.datasourceType === 'IOTDB'"
            label="存储组StorageGroup："
            field="defaultSchema"
            :rules="[{ required: true, message: '请输入存储组', trigger: 'blur' }]"
          >
            <n-input
              v-model="state.ruleForm.defaultSchema"
              placeholder="请输入存储组"
              :disabled="state.disabled"
            />
          </n-form-item>

          <n-form-item
            v-if="state.ruleForm.datasourceType !== 'KAFKA'"
            label="用户名Username："
            :field="state.ruleForm.datasourceType === 'GRAPH' ? 'graph' : 'userName'"
          >
            <n-input
              v-model="state.ruleForm.userName"
              auto-complete="new-password"
              readonly
              onfocus="this.removeAttribute('readonly')"
              onblur="this.setAttribute('readonly',true)"
              placeholder="请输入用户名"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.datasourceType !== 'KAFKA'"
            label="密码Password："
            :field="state.ruleForm.datasourceType === 'GRAPH' ? 'graph' : 'password'"
            class="password-form-item"
          >
            <!--   readonly
            onfocus="this.removeAttribute('readonly')"
            onblur="this.setAttribute('readonly',true)"
            来改变这个状态让输入框可以输入或者只读,这样就不会被记住密码填充了
            -->
            <n-input
              ref="showPasswordDom"
              v-model="state.ruleForm.password"
              :type="state.passwordType"
              readonly
              onfocus="this.removeAttribute('readonly')"
              onblur="this.setAttribute('readonly',true)"
              placeholder="请输入密码"
              :disabled="state.disabled"
              show-password
            />
            <span class="show-password" @click="showPassword">
              <SvgIcon v-if="state.passwordType === 'text'" icon="show-password" title="展示" />
              <SvgIcon v-else icon="hide-password" title="隐藏" />
            </span>
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.sftpConnectType === 'FTP'"
            label="模式选择："
            field="fileType"
          >
            <n-select
              v-model="state.ruleForm.ftpModeType"
              placeholder="请选择模式"
              :disabled="state.disabled"
            >
              <n-option name="主动模式" value="ACTIVE_MODE" />
              <n-option name="被动模式" value="PASSIVE_MODE" />
            </n-select>
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.datasourceType === 'SFTP'"
            label="文件类型："
            field="fileType"
          >
            <n-select
              v-model="state.ruleForm.fileType"
              placeholder="请选择文件类型"
              multiple
              :options="state.fileTypeOptions"
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item
            v-if="state.ruleForm.datasourceType === 'SFTP'"
            label="文件提取地址："
            field="filePath"
          >
            <div class="enumerate">
              <n-button class="enumerate-btn" color="primary" @click.prevent.stop="addEnumerateFn"
                ><SvgIcon icon="new-add" class="icon" title="新建" />添加</n-button
              >
              <div v-if="state.ruleForm.filePath.length > 0" class="enumerate-box">
                <div
                  v-for="(item, index) in state.ruleForm.filePath"
                  :key="index"
                  class="enumerate-box-row"
                >
                  <n-input class="ipt" v-model="item.value" :disabled="state.disabled" />
                  <SvgIcon
                    icon="icon-new-delete"
                    class="icon"
                    title="删除"
                    @click.prevent.stop="delEnumerateFn(index)"
                  />
                </div>
              </div>
            </div>
          </n-form-item>

          <n-form-item label="描述信息：" field="description" class="label_align_required">
            <n-textarea
              v-model="state.ruleForm.description"
              maxlength="200"
              :autosize="{ minRows: 6 }"
              :disabled="state.disabled"
              show-count
            />
          </n-form-item>
          <n-form-item v-if="state.type === 'DETAIL'" label="创建时间：" field="createTime">
            <n-input
              v-model="state.ruleForm.createTime"
              placeholder=""
              :disabled="state.disabled"
            />
          </n-form-item>
          <n-form-item v-if="state.type === 'DETAIL'" label="创建人：" field="createByName">
            <n-input
              v-model="state.ruleForm.createByName"
              placeholder=""
              :disabled="state.disabled"
            />
          </n-form-item>
        </n-form>
      </div>
    </div>
    <div class="options-box-bg">
      <div class="options-box-content">
        <div v-if="state.type !== 'DETAIL'">
          <n-button size="sm" @click.prevent="goBack">取消</n-button>

          <n-button :loading="state.testLoading" size="sm" @click.prevent="testDatasource"
            >测试连接</n-button
          >
          <n-button
            color="primary"
            size="sm"
            variant="solid"
            :loading="state.saveLoading"
            @click.prevent="submitForm"
            >确定</n-button
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import { checkCIp, checkCPort, checkMoreFileUrl, checkCName } from '@/utils/validate'
  import { queryDataTypeTree } from '@/api/dataManage'
  import TreeSelect from '@/components/cfTreeSelect'

  export default {
    name: '',
    components: { TreeSelect },
    props: { query: {}, categoryId: {} },
    setup(props, { emit }) {
      const ruleForm = ref()
      const showPasswordDom = ref()
      const dataStructureTable = ref()
      let passwordRequire = true
      let userNameRequire = true
      // 检测输入地址
      const checkMoreFileUrlList = (rule, value, callback) => {
        if (value.length > 0) {
          let regex =
            /^[/]([/]|[\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-])*?([\,][/]([/]|[\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z_0-9-])*)*$/
          let flag = true
          value.forEach((val) => {
            let res = regex.test(val.value)
            if (!res || val.value.length < 1) {
              flag = false
            }
          })

          if (flag) {
            return callback()
          } else {
            return callback(new Error('以/开头,支持汉字、英文、数字、中划线、下划线'))
          }
        } else {
          return callback(new Error('请添加文件提取地址'))
        }
      }

      const state = reactive({
        connectStatus: false,
        loading: false,
        asyncUse: false,
        queryData: {}, // 获取路由传参
        activeName: 'baseMessage',
        saveLoading: false,
        testLoading: false,
        passwordType: 'password',
        disabled: false, // 表单不可用
        type: 'ADD', // 页面展示形式 ADD EDIT  DETAIL
        confidentialityLevelOptions: [
          { name: '公开', value: 'PUBLIC' },
          { name: '内部', value: 'INTERIOR' },
          { name: '受控', value: 'CONTROLLED' },
          { name: '秘密', value: 'SECRET' },
          { name: '机密', value: 'CONFIDENTIAL' },
          { name: '核心', value: 'CORE' },
        ],
        treeShow: true,
        TreeCount: 1,
        ruleForm: {
          name: '',
          envType: 'OFFICIAL',
          sftpConnectType: '',
          datasourceType: '',
          ftpModeType: '',
          host: '',
          port: '',
          database: '',
          userName: '',
          password: '',
          description: '',
          createByName: '',
          createTime: '',
          serviceName: '',
          defaultSchema: '',
          fileUrl: '',
          type: 'PROJECT', //类型(PROJECT-场景 PUBLIC-公共)
          fileType: [],
          filePath: [],
          keyspace: '',
          confidentialityLevel: '', //密级
          dataCategoryId: '', //数据分类
          charcter: '', //字符编码
        },
        rules: {
          name: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'project', 'validDatasource', {
                  type: 'PROJECT',
                  name: state.ruleForm.name,
                  id: state.queryData?.id || null,
                }),
              trigger: 'blur',
            },
          ],
          datasourceType: [{ required: true, message: '请选择数据库类型', trigger: 'blur' }],
          envType: [{ required: true, message: '请选择数据环境', trigger: 'blur' }],
          dataCategoryId: [{ required: true, message: '请选择数据分类', trigger: 'blur' }],
          confidentialityLevel: [{ required: true, message: '请选择数据密级', trigger: 'blur' }],
          sftpConnectType: [{ required: true, message: '请选择连接方式', trigger: 'blur' }],
          ftpModeType: [{ required: true, message: '请选择模式', trigger: 'blur' }],
          fileUrl: [{ required: true, message: '请输入URL(多个目录请用,号隔开)', trigger: 'blur' }],
          host: [{ required: true, validator: checkCIp, trigger: 'blur' }],
          port: [{ required: true, validator: checkCPort, trigger: 'blur' }],
          serviceName: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
          userName: [{ required: userNameRequire, message: '请输入对应的用户名', trigger: 'blur' }],
          password: [{ required: passwordRequire, message: '请输入对应的密码', trigger: 'blur' }],
          // 端口号非正整数:端口号为0到65535的整数。
          fileType: [
            { required: true, type: 'array', message: '请选择文件类型', trigger: 'change' },
          ],
          filePath: [
            { required: true, type: 'array', validator: checkMoreFileUrlList, trigger: 'change' },
          ],
        },
        fileTypeOptions: [
          {
            name: 'pdf',
            value: 'PDF',
          },
          {
            name: 'jpg',
            value: 'JPG',
          },
          {
            name: 'jpeg',
            value: 'JPEG',
          },
          {
            name: 'png',
            value: 'PNG',
          },
          {
            name: 'doc',
            value: 'DOC',
          },
          {
            name: 'docx',
            value: 'DOCX',
          },
          {
            name: 'txt',
            value: 'TXT',
          },
          {
            name: 'xlsx',
            value: 'XLSX',
          },
          {
            name: 'xls',
            value: 'XLS',
          },
          {
            name: 'csv',
            value: 'CSV',
          },
          {
            name: 'json',
            value: 'JSON',
          },
          {
            name: 'xml',
            value: 'XML',
          },
          {
            name: 'log',
            value: 'LOG',
          },
        ],
        dataStructure: {
          //数据结构部分字段
          dataTableOptions: [],
          tableName: '',
          tableData: [],
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 100 },
          { prop: 'colName', name: '字段' },
          { prop: 'dataType', name: '类型' },
          { prop: 'length', name: '长度' },
          { prop: 'comment', name: '描述信息' },
        ],
        treeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        defaultTreeData: [{ id: -1, name: '全部', expanded: true, children: [] }],
        charcterOpt: ['GBK', 'UTF-8', 'GB2312', 'Latin-1', 'ZHS16GBK'],
      })

      const methods = {
        // 获取树列表
        getTreeListFn() {
          queryDataTypeTree({}).then((res) => {
            if (res.code === 'SUCCESS') {
              let treeData = []
              if (res.data?.children.length > 0) {
                treeData = [res.data]
                treeData[0].selected = true
              } else {
                treeData.push({
                  children: [],
                  id: null,
                  level: 0,
                  label: '全部',
                  type: 'ROOT',
                })
              }
              if (treeData.length > 0) {
                treeData[0].expanded = true
              }
              state.treeData = [...treeData]
              state.defaultTreeData = [...treeData]
              state.ruleForm.dataCategoryId = state.ruleForm.dataCategoryId
                ? state.ruleForm.dataCategoryId
                : props.categoryId || ''
            }
          })
        },
        // 增加文件地址
        addEnumerateFn() {
          state.ruleForm.filePath.push({ value: '' })
        },
        // 删除文件地址
        delEnumerateFn(index) {
          state.ruleForm.filePath.splice(index, 1)
        },
        // sftp类型切换改变端口
        sftpConnectTypeChangeFn() {
          if (state.ruleForm.sftpConnectType === 'SFTP') {
            state.ruleForm.port = 22
          } else {
            state.ruleForm.port = 21
          }
        },
        // 获取数据源详情
        getDetail(id, interface_url = 'getDatasourceDetail') {
          state.loading = true
          state.treeShow = false
          api.project[interface_url]({ id })
            .then((res) => {
              state.loading = false
              let { success, data } = res
              if (success) {
                if (state.type === 'DETAIL') {
                  data.description = data.description || '暂无描述信息'
                }

                state.asyncUse = data.asyncUse
                state.ruleForm = JSON.parse(JSON.stringify(data))
                state.ruleForm.dataCategoryId = JSON.stringify(state.ruleForm.dataCategoryId)

                // methods.getTableList(data)
                if (data.filePath) {
                  state.ruleForm.filePath = data.filePath.map((val) => {
                    return { value: val }
                  })
                }
                setTimeout(() => {
                  state.treeShow = true
                }, 300)
              }
            })
            .catch(() => {
              state.loading = false
            })
          state.TreeCount++
        },
        //获取数据结构数据表下拉项
        getTableList(data) {
          if (state.queryData.dataStructureType === 'ALL_STRUCTURE') {
            dataStructureTable.value.initTableData({ list: [] })
            api.dataManagement.getSourceTables({ dataSourceId: data.id }).then((res) => {
              let { success, data } = res
              if (success) {
                state.dataStructure.dataTableOptions = []
                data.forEach((item) => {
                  state.dataStructure.dataTableOptions.push({
                    label: item,
                    value: item,
                  })
                })
              }
            })
          }
        },
        //数据表下拉框change
        dataSourceTableChange(data) {
          api.dataManagement
            .getSourceStructure({ id: state.queryData.id, tableName: data })
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.dataStructure.tableData = data
                state.dataStructure.tableData.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })

                dataStructureTable.value.initTableData({ list: state.dataStructure.tableData })
              }
            })
        },

        // 新增保存
        submitForm() {
          if (!state.connectStatus) {
            ElNotification({
              title: '提示',
              message: '请先测试连接成功再确定保存',
              type: 'error',
            })
            return false
          }
          methods.changeRuleParames()
          ruleForm.value.validate((valid) => {
            if (valid) {
              state.saveLoading = true
              let interface_url = 'datasourceAdd'
              let message = '新增成功'
              let data = { ...state.ruleForm }
              data.dataCategoryId = Number(data.dataCategoryId)

              if (state.ruleForm.datasourceType === 'ORACLE') {
                data.database = state.ruleForm.userName
              }
              if (state.type === 'EDIT') {
                interface_url = 'datasourceUpdate' // 更新
                data.id = state.queryData.id
                message = '更新成功'
              } else {
                message = '新增成功'
                interface_url = 'datasourceAdd' // 新增
                delete data.createByName
                delete data.createTime
              }
              if (data.filePath) {
                data.filePath = data.filePath.map((val) => {
                  return val.value
                })
              }

              if (state.ruleForm.datasourceType === 'ORACLE') {
                data.asyncUse = state.asyncUse
              }
              api.project[interface_url](data)
                .then((res) => {
                  let { success } = res
                  state.saveLoading = false
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message,
                      type: 'success',
                    })
                    emit('close', true)
                  }
                })
                .catch(() => {
                  state.saveLoading = false
                })
            }
          })
        },
        //根据数据库类型设置必填字段
        changeRuleParames() {
          switch (state.ruleForm.datasourceType) {
            case 'KAFKA':
              // state.rules.database[0].required = false
              state.rules.serviceName[0].required = false
              state.rules.userName[0].required = false
              state.rules.password[0].required = false
              break
            case 'ORACLE':
              break
            case 'HIVE':
              state.rules.password[0].required = false
              state.rules.serviceName[0].required = false
              break
            //   case 'SFTP':
            // state.rules.database[0].required = false
            // state.rules.serviceName[0].required = false
            // break
            default:
              state.rules.serviceName[0].required = false
              break
          }
        },
        // 测试数据源
        testDatasource() {
          methods.changeRuleParames()
          ruleForm.value.validate((valid) => {
            if (valid) {
              state.testLoading = true
              let data = { ...state.ruleForm }
              if (data.filePath) {
                data.filePath = data.filePath.map((val) => {
                  return val.value
                })
              }
              if (state.ruleForm.datasourceType === 'ORACLE') {
                data.database = state.ruleForm.userName
              }

              api.project
                .testDatasource(data)
                .then((res) => {
                  let { success } = res
                  state.testLoading = false
                  if (success) {
                    let { success: go } = res.data
                    if (go) {
                      state.connectStatus = true
                      ElNotification({
                        title: '提示',
                        message: '测试连接成功',
                        type: 'success',
                      })
                    } else {
                      ElNotification({
                        title: '提示',
                        message: '测试连接失败',
                        type: 'error',
                      })
                    }
                  }
                })
                .catch(() => {
                  state.testLoading = false
                })
            }
          })
        },
        // 显示密码
        showPassword() {
          if (state.passwordType === 'text') {
            showPasswordDom.value.$refs.input.type = 'password'
            state.passwordType = 'password'
          } else {
            state.passwordType = 'text'
            showPasswordDom.value.$refs.input.type = 'text'
          }
        },
        goBack() {
          state.connectStatus = false
          emit('close')
        },
      }
      onMounted(() => {
        if (props.query.databaseType === 'HIVE') {
          passwordRequire = false
        }
        state.queryData = props.query || {}
        state.type = props.query.type ? state.queryData.type : 'ADD'
        state.ruleForm.datasourceType = state.queryData.databaseType
          ? state.queryData.databaseType.toUpperCase()
          : ''

        methods.getTreeListFn()

        switch (state.type) {
          case 'DETAIL':
            methods.getDetail(state.queryData.id, 'getDatasourceAllDetail')
            state.rules = {}
            state.disabled = true
            break
          case 'EDIT':
            methods.getDetail(state.queryData.id, 'getDatasourceDetailByAes')
            break
        }
      })

      return {
        ruleForm,
        showPasswordDom,
        state,
        dataStructureTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  $labelWidth: 74px;
  .data-source-add {
    position: relative;
    height: 100%;
    color: #333;
    .options-box-bg {
      // position: absolute;
      // right: 0;
      // bottom: 0;
      // left: 0;
      height: 64px;
      .options-box-content {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        height: 100%;
        padding: 0;
      }

      button {
        border-radius: 2px;
      }
    }
    .data-source-add-box {
      position: relative;
      width: 100%;
      height: calc(100% - 64px);
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px;
      .need_smallcube__title {
        font-size: 16px;
      }
      .box-content-description {
        width: calc(100% - 4px);
        height: 38px;
        margin: 2px auto;
        padding: 13px 0;
        background: #f7f8fa;
        p {
          margin: 0 0 0 30px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          font-size: 14px;
        }
      }
      .content-bg-img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 17.5%;
        min-width: 252px;
      }
      .data-source-add-box-content {
        height: 100%;
        overflow-y: auto;
        .inlineBlock {
          position: relative;
          display: flex;
          width: 100%;
          :deep(.nancalui-input) {
            width: 100%;
          }
          :deep(.ipPort) {
            width: 180px !important;
            margin-left: 10px;
            .nancalui-input__wrapper {
              width: 180px !important;
            }
            .nancalui-input__inner {
              width: 180px !important;
            }
          }
        }
        .nancalui-form {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          margin: 0 auto;
          padding-left: 6px;
          // transform: translatex(-70px);
          .nancalui-form__item--horizontal {
            width: 100%;
          }
          .enumerate {
            width: 100%;
            &-btn {
              :deep(.button-content) {
                display: flex;
                align-items: center;
                justify-content: center;
                .icon {
                  margin-right: 4px;
                }
              }
            }
            &-box {
              width: 100%;
              margin-top: 8px;
              padding: 16px;
              background-color: #f6f7fb;
              border-radius: 6px;
              &-row {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                margin-bottom: 8px;
                :deep(.nancalui-input) {
                  &.ipt {
                    box-sizing: border-box;
                    width: calc(100% - 28px);
                    background-color: #fff;
                    .nancalui-input__wrapper {
                      border-radius: 6px;
                    }
                  }
                }
                .icon {
                  margin-left: 12px;
                  color: #8091b7;
                  font-size: 16px;
                  cursor: pointer;
                  &:hover {
                    color: $themeBlue;
                  }
                }
              }
            }
          }
        }
      }

      // 查看下样式
      .only-detail {
      }
    }
    .nancalui-tabs {
      height: 100% !important;
    }
  }
</style>
