import Layout from '@/layout'

// 系统管理-日志管理
export default [
  {
    path: '/logManagement',
    component: Layout,
    redirect: '/logManagement/secureLogManage',
    name: 'logManagement',
    code: 'logManagement',
    meta: {
      title: '日志管理',
      icon: 'log-management-mark',
      parentRouterName: 'systemManage',
      code: 'logManagement',
      unShowProject: true,
    },
    children: [
      // 用户日志管理
      {
        path: 'userLogManagement',
        name: 'userLogManagement',
        code: 'userLogManagement',
        redirect: '/logManagement/userLogManagement/userLogManagementlist',
        component: () => import('@/views/log-manage/user-log-manage/index'),
        meta: {
          title: '用户日志',
          parentRouterName: 'systemManage',
          code: 'userLogManagement',
          unShowProject: true,
        },
        children: [
          {
            path: 'userLogManagementlist',
            hidden: true,
            name: 'userLogManagementlist',
            component: () => import('@/views/log-manage/user-log-manage/list'),
            meta: {
              title: '用户日志',
              unShowProject: true,
              activeMenu: '/logManagement/userLogManagement',
              parentRouterName: 'systemManage',
              code: 'userLogManagement',
            },
          },
        ],
      },
      // 管理员日志
      {
        path: 'secureLogManage',
        name: 'secureLogManage',
        code: 'secureLogManage',
        redirect: '/logManagement/secureLogManage/secureLogManagelist',
        component: () => import('@/views/log-manage/secure-log-manage/index'),
        meta: {
          title: '管理员日志',
          parentRouterName: 'systemManage',
          code: 'secureLogManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'secureLogManagelist',
            hidden: true,
            name: 'secureLogManagelist',
            component: () => import('@/views/log-manage/secure-log-manage/list'),
            meta: {
              title: '管理员日志',
              unShowProject: true,
              activeMenu: '/logManagement/secureLogManage',
              parentRouterName: 'systemManage',
              code: 'secureLogManage',
            },
          },
        ],
      },
      //数据访问日志
      {
        path: 'dataAccessLog',
        name: 'dataAccessLog',
        code: 'dataAccessLog',
        redirect: '/logManagement/dataAccessLog/dataAccessLoglist',
        component: () => import('@/views/log-manage/data-access-log/index'),
        meta: {
          title: '数据访问日志',
          parentRouterName: 'systemManage',
          code: 'dataAccessLog',
          unShowProject: true,
        },
        children: [
          {
            path: 'dataAccessLoglist',
            hidden: true,
            name: 'dataAccessLoglist',
            component: () => import('@/views/log-manage/data-access-log/list'),
            meta: {
              title: '数据访问日志',
              activeMenu: '/logManagement/dataAccessLog',
              parentRouterName: 'systemManage',
              unShowProject: true,
              code: 'dataAccessLog',
            },
          },
        ],
      },
    ],
  },
]
