<template>
  <div class="drawer">
    <!-- {{ props }} -->
    <div class="drawer-title"> 数据选择 </div>
    <div class="drawer-content">
      <n-form style="width: 100%" label-width="62px">
        <n-form-item label="数据源：">
          <TreeSelect v-model="$attrs.modelValue.datasourceId" :data="state.dataSourceList" :props="{
            label: 'name',
            value: 'id',
            children: 'children',
          }" :render-after-expand="false" placeholder="请选择父级分类" style="width: 100%"
            @change="(val) => getSourceSelect(val, $attrs.modelValue.type)"
            @node-click="(node) => ($attrs.modelValue.type = node.type || 'DATASOURCE')" />
        </n-form-item>
        <n-form-item label="数据表：">
          <div class="table-box">
            <div class="btn" @click.prevent.stop="state.tableList.push({})">
              <SvgIcon class="icon" icon="new-add" />
              新建
            </div>
            <div class="table-list">
              <div v-for="(item, i) in state.tableList" :key="item.id" class="table-list-item">
                <el-select-v2 filterable v-model="item.operator" :key="state.tableList" :options="state.tableOpts?.map((_) => ({
                  label: _?.cnName || _?.name || _,
                  value: _?.id || _,
                  disabled: Object.keys(state.fieldList).some((key) => key == (_?.id || _)),
                }))
                  " @change="(val) => selectTable(val, i, $attrs.modelValue.type)" />
                <div class="clear" @click="clearFilter(i)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <g clip-path="url(#clip0_1314_218707)">
                      <path
                        d="M2.5 4H13.5H2.5ZM14 14C14 14.8284 13.3284 15.5 12.5 15.5H3.5C2.67157 15.5 2 14.8284 2 14H3C3 14.2761 3.22386 14.5 3.5 14.5H12.5C12.7761 14.5 13 14.2761 13 14H14ZM3.5 15.5C2.67157 15.5 2 14.8284 2 14V4H3V14C3 14.2761 3.22386 14.5 3.5 14.5V15.5ZM14 4V14C14 14.8284 13.3284 15.5 12.5 15.5V14.5C12.7761 14.5 13 14.2761 13 14V4H14Z"
                        fill="#606266" />
                      <path
                        d="M4 4H12H4ZM12.5 2C12.5 1.17157 11.8284 0.5 11 0.5H5C4.17157 0.5 3.5 1.17157 3.5 2H4.5C4.5 1.72386 4.72386 1.5 5 1.5H11C11.2761 1.5 11.5 1.72386 11.5 2H12.5ZM5 0.5C4.17157 0.5 3.5 1.17157 3.5 2V4H4.5V2C4.5 1.72386 4.72386 1.5 5 1.5V0.5ZM12.5 4V2C12.5 1.17157 11.8284 0.5 11 0.5V1.5C11.2761 1.5 11.5 1.72386 11.5 2V4H12.5Z"
                        fill="#606266" />
                      <line x1="1" y1="3.5" x2="15" y2="3.5" stroke="#606266" stroke-linecap="round" />
                      <line x1="11.5" y1="12.5" x2="4.5" y2="12.5" stroke="#606266" stroke-linecap="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_1314_218707">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </n-form-item>
      </n-form>
      <el-collapse accordion>
        <el-collapse-item v-for="(value, key) in state.fieldList" :key="key" :name="key">
          <template #title>
            <VueDraggableNext class="drag-list" animation="300" :group="{ name: 'people', pull: 'clone', put: false }"
              @change="change" :clone="(original) => {
                return original.name
              }
                ">
              <div class="drag-item">

                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M13.85 1.5H2.15C1.79101 1.5 1.5 1.82335 1.5 2.22222V13.7778C1.5 14.1767 1.79101 14.5 2.15 14.5H13.85C14.209 14.5 14.5 14.1767 14.5 13.7778V2.22222C14.5 1.82335 14.209 1.5 13.85 1.5Z"
                    stroke="#1D2129" stroke-linejoin="round" />
                  <path d="M1.5 6.32812H14.5" stroke="#1D2129" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M5.88672 6.32812V14.1281" stroke="#1D2129" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M10.1113 6.32812V14.1281" stroke="#1D2129" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M1.5 10.2285H14.5" stroke="#1D2129" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M14.5 2.42871V13.7901C14.5 14.1822 14.209 14.5001 13.85 14.5001H2.15C1.79101 14.5001 1.5 14.1822 1.5 13.7901V2.42871"
                    stroke="#1D2129" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span class="field-name" style="font-size: 16px; color: #1d2129; font-weight: bold" :title="state.tableOpts
                  ?.map((_) => ({ ..._, name: _?.name || _, id: _?.id || _ }))
                  ?.find((_) => _?.id == key)?.name
                  ">{{ (() => {
                    const data = state.tableOpts
                      ?.map((_) => ({ ..._, name: _?.name || _, id: _?.id || _ }))
                      ?.find((_) => _?.id == key)
                    let schema = state.dataSourceList.find((_) => _.id === $attrs.modelValue.datasourceId)?.schema
                    schema && (schema += '.')
                    return data?.datasourceType === "POSTGRESQL" ? data?.name : ((schema || '') + data?.name)
                  })()
                  }}</span>
              </div>
            </VueDraggableNext>
          </template>

          <VueDraggableNext class="drag-list" v-model="state.fieldList[key]" animation="300"
            :group="{ name: 'people', pull: 'clone', put: false }" @change="change" :clone="(original) => {
              return original.name
            }
              ">
            <template v-for="element in state.fieldList[key]" :key="element.id">
              <div class="drag-item">

                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <rect x="1.5" y="1.5" width="13" height="13" rx="1" stroke="#606266" />
                  <line x1="8" y1="6" x2="8" y2="11.5" stroke="#606266" stroke-linecap="round" />
                  <line x1="5.5" y1="5.5" x2="10.5" y2="5.5" stroke="#606266" stroke-linecap="round" />
                </svg>

                <span class="field-name" :title="element.colName"
                  :sub-label="` (${element.cnName || element.comment || '--'})`">{{ element.colName || element.name
                  }}</span>
              </div>
            </template>
          </VueDraggableNext>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="drawer-footer"> </div>
  </div>
</template>

<script setup>
import api from '@/api/index'
import { VueDraggableNext } from 'vue-draggable-next'
import TreeSelect from './treeSelect'
const attrs = useAttrs()
const state = reactive({
  fieldList: {},

  // 数据源下拉列表
  dataSourceList: [],
  tableList: [{}],
  tableOpts: [],
})
// 获取数据源树
const getPublishDataSource = () => {
  api.dataManagement.getPublishDataSource().then((res) => {
    const tree =
      res.data || []
    tree.forEach((_) => deepModify(_))
    state.dataSourceList = tree
  })
}
const clearFilter = (i) => {
  // if (state.tableList.length == 1) return
  const rm = state.tableList.splice(i, 1)
  delete state.fieldList[rm[0].operator]
}
// 获取数据表下拉列表
const getSourceSelect = (dataSourceId, type) => {
  state.fieldList = {}
  state.tableList = [{}]
  const url = type === 'DATASOURCE' ? 'getSourceSelect' : 'getAdhocQueryLeftListAssets'
  api.dataManagement[url]({ dataSourceId }).then((res) => {
    if (res.code === 'SUCCESS') {
      state.tableOpts = res.data || []
    }
  })
}
// 选择表事件
let oldTableList = []
const selectTable = (val, i, type) => {
  const data = oldTableList?.[i] || {}
  attrs.modelValue.tables = JSON.stringify(state.tableList)
  delete state.fieldList[data?.operator]
  const url = type === 'DATASOURCE' ? 'getSourceStructure' : 'getAdhocQueryLeftListAssetsDetail'
  const params =
    type === 'DATASOURCE'
      ? {
        id: attrs.modelValue.datasourceId,
        tableName: val,
      }
      : {
        id: val,
      }
  api.dataManagement[url](params).then((res) => {
    state.fieldList[val] = res.data || []
  })
  oldTableList = JSON.parse(JSON.stringify(state.tableList || '[]'))
}

function deepModify(data) {
  data.name = data?.databaseName || '资产库'
  data.id = data.databaseId || -999
  data.disabled = data?.children?.length === 0 && isNaN(data.databaseId || NaN)
  data?.children?.forEach((item) => deepModify(item))
}
const initFn = (data) => {
  getSourceSelect(data?.datasourceId, data.type)
  state.tableList = JSON.parse(data.tables || '[{}]')
  state.tableList?.forEach((_, i) => {
      _.operator && selectTable(_.operator, i, data.type)
  })
}

// 还原表单
const resetFn = (() => {
  const data = JSON.parse(JSON.stringify(state))
  return () => {
    Object.assign(state, JSON.parse(JSON.stringify(data)))
    getPublishDataSource()
getSourceSelect(attrs.modelValue.datasourceId,"ASSETS")
}
})()
getPublishDataSource()
getSourceSelect(attrs.modelValue.datasourceId,"ASSETS")
defineExpose({
  resetFn,
  initFn,
})
</script>
<style></style>
<style lang="scss" scoped>
.drawer {
  display: flex;
  width: 400px;
  height: 100%;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1000;
  background: #fff;
  border-left: 1px solid var(---, #dcdfe6);
  overflow: hidden;

  &-title {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    height: 48px;
    padding: 0 16px;
    color: #1d2129;
    font-weight: bolder;
    font-size: 16px;
    background-color: #fff;
    border-radius: 2px;
    border-bottom: 1px solid #eceff5;

    &[sub-label]::after {
      margin-left: 12px;
      content: attr(sub-label);
      display: inline-block;
      padding: 0px 4px;
      border-radius: 2px;
      line-height: 22px;
      border: 1px solid rgba(26, 164, 238, 0.4);
      background: rgba(26, 164, 238, 0.08);

      color: #1aa4ee;

      font-family: 'Source Han Sans CN';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }

    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }
  }

  &-content {
    flex: 1;
    width: 100%;
    padding: 16px;
    overflow-y: auto;

    .table-box {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      flex: 1 0 0;

      .table-list {
        width: 100%;
        display: flex;
        padding: 8px 10px;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex: 1 0 0;
        border-radius: 2px;
        border: 1px solid var(---, #e5e6eb);
        background: var(---, #fafafa);

        &-item {
          display: flex;
          align-items: center;
          gap: 12px;
          align-self: stretch;
        }
      }
    }

    .drag-list {
      width: 100%;

      .drag-item {
        display: flex;
        padding: 5px 0px;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;

        >* {
          cursor: pointer;
        }

        .field-name {
          text-align: left;
          flex: 1;
          overflow: hidden;
          color: var(----, #606266);
          text-overflow: ellipsis;
          white-space: nowrap;

          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;

          /* 157.143% */
          &::after {
            content: attr(sub-label);
          }
        }

        &+.drag-item {
          margin-top: 4px;
        }
      }
    }

    :deep(.el-collapse-item__content) {
      .drag-list {
        margin-left: 20px;
      }
    }
  }
}

.btn {
  display: flex;
  padding: 4px 16px;
  align-items: center;
  gap: 4px;
  border-radius: 2px;
  border: 1px solid var(---, #1e89ff);
  color: #1e89ff;
  font-size: 14px;
  box-sizing: border-box;

  .icon {
    font-size: 16px;
  }

  &.active,
  &:hover {
    color: #fff;
    background: #1e89ff;
  }
}
</style>
