<template>
  <n-modal
    class="file-view-dialog"
    v-model="state.dialogVisible"
    :draggable="false"
    width="960px"
    :close-on-click-overlay="false"
    :before-close="handleClose"
    title="编辑SQL表达式"
    bodyMaxHeight="700px"
  >
    <div v-loading="state.loading" class="modal-body">
      <div class="top">
        <!--        <div class="asset">-->
        <!--          <div class="title"><div class="title-name">资产目录</div></div>-->
        <!--          <div class="asset-container">-->
        <!--            <n-input-->
        <!--              class="asset-container-ipt"-->
        <!--              v-model="state.treeSearchText"-->
        <!--              placeholder="请输入"-->
        <!--              suffix="search"-->
        <!--              @input="searchTreeFn"-->
        <!--            />-->
        <!--            <div class="asset-container-tree scroll-bar-style">-->
        <!--              <n-tree ref="treeRef" :data="state.treeData" @node-click="clickFn">-->
        <!--                <template #content="{ nodeData }">-->
        <!--                  <SvgIcon-->
        <!--                    v-if="nodeData?.children?.length > 0"-->
        <!--                    class="tree-icon"-->
        <!--                    icon="tree-open"-->
        <!--                  />-->
        <!--                  <SvgIcon v-else class="tree-icon" icon="tree-retract" />-->
        <!--                  <div class="tree-label" :title="nodeData.name">{{ nodeData.name }}</div>-->
        <!--                </template>-->
        <!--                <template #icon="{ nodeData, toggleNode }">-->
        <!--                  <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>-->
        <!--                  <span-->
        <!--                    v-else-->
        <!--                    @click="-->
        <!--                      (event) => {-->
        <!--                        event.stopPropagation()-->
        <!--                        toggleNode(nodeData)-->
        <!--                        treeChange(nodeData)-->
        <!--                      }-->
        <!--                    "-->
        <!--                  >-->
        <!--                    <svg-->
        <!--                      :style="{-->
        <!--                        transform: nodeData.expanded ? 'rotate(90deg)' : '',-->
        <!--                        marginLeft: '-2.5px',-->
        <!--                        marginRight: '6px',-->
        <!--                        cursor: 'pointer',-->
        <!--                      }"-->
        <!--                      viewBox="0 0 1024 1024"-->
        <!--                      width="8"-->
        <!--                      height="8"-->
        <!--                    >-->
        <!--                      <path-->
        <!--                        d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"-->
        <!--                        fill="currentColor"-->
        <!--                      />-->
        <!--                    </svg>-->
        <!--                  </span>-->
        <!--                </template>-->
        <!--              </n-tree>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="code">
          <div class="title">
            <div class="title-name"
              >SQL脚本
              <n-tooltip
                class="tree-btn"
                :content="
                  props?.sqlFieldName === 'sqlScript'
                    ? '请输入完整的SQL语句，返回结果需返回一行一列的值作为阈值范围监控结果。'
                    : '请输入查询问题数据明细的SQL语句。'
                "
                position="top"
                :enterable="false"
              >
                <SvgIcon class="illustrate" icon="icon-illustrate" />
              </n-tooltip>
            </div>
            <div class="title-btn">
              <n-button color="primary" @click="clearFn"
                ><SvgIcon class="icon" icon="icon-quality-del" />清空语句</n-button
              >
              <n-button variant="solid" color="primary" @click="runFn"
                ><SvgIcon class="icon" icon="icon-quality-query" />运行查询</n-button
              >
            </div>
          </div>
          <div class="code-container">
            <codemirror
              ref="myCm"
              v-model:value="state.sql"
              class="codemirror"
              :options="state.sqlOption"
              @ready="onCmReady"
              @focus="onCmFocus"
              @input="onCmCodeChange"
            />
          </div>
        </div>
        <div class="field">
          <div class="title"
            ><div class="title-name">数据表参考<span>（点击表名查看字段）</span></div></div
          >
          <div class="field-container scroll-bar-style">
            <n-input
              v-if="state.isShowModel"
              class="each-title-ipt"
              v-model="state.searchText"
              placeholder="中文名称/英文名称"
              suffix="search"
              @input="tableSearchFn()"
              clearable
              @clear="tableSearchFn"
            />
            <template v-if="state.isShowModel">
              <div
                v-for="(item, index) in state.modelList"
                :key="index"
                class="label"
                @click="showFieldFn(item.id)"
              >
                <div class="label-name">{{ item.name }}</div>
                <SvgIcon
                  class="icon"
                  icon="icon-new-copy"
                  @click.prevent.stop="copyFn(item.name)"
                />
              </div>
            </template>
            <template v-else>
              <div class="field-container-back" @click="state.isShowModel = true"
                ><SvgIcon class="icon" icon="dataset-back" />返回</div
              >
              <div v-for="(item, index) in state.fieldList" :key="index" class="label">
                <div class="label-name fieldChild">{{ item.name }}</div>
                <SvgIcon
                  class="icon"
                  icon="icon-new-copy"
                  @click.prevent.stop="copyFn(item.name)"
                />
              </div>
            </template>
            <div
              v-if="
                (state.isShowModel && state.modelList.length === 0) ||
                (!state.isShowModel && state.fieldList.length === 0)
              "
              class="empty"
            >
              <img class="pic" src="/src/assets/table-no-content-small.png" />
              <p class="text">暂无相关{{ state.isShowModel ? '表' : '字段' }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="tab">
        <div class="tab-box">
          <n-tabs v-model="state.tabs" :key="state.tabKey" @active-tab-change="tabsChangeFn">
            <n-tab id="result" title="运行结果" />
            <n-tab v-if="props?.sqlFieldName === 'sqlScript'" id="log" title="执行记录" />
          </n-tabs>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            :style="{ transform: `rotate( ${state.arrowStatus ? '0deg' : '180deg'})` }"
            @click="state.arrowStatus = !state.arrowStatus"
          >
            <path
              d="M13.1016 3.00195L8.10156 9.00195L3.10156 3.00195"
              stroke="#606266"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.8984 6.99805L7.89844 12.998L2.89844 6.99805"
              stroke="#606266"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="tab-container" v-show="state.arrowStatus">
          <CfTable
            v-if="state.tableData.list.length > 0"
            :tableConfig="{
              data: state.tableData.list,
              rowKey: 'id',
            }"
            :table-head-titles="state.tableHeadTitles"
            :paginationConfig="
              props?.sqlFieldName === 'sqlScript'
                ? false
                : {
                    total: state.page.total,
                    pageSize: state.page.pageSize,
                    currentPage: state.page.pageNum,
                    onCurrentChange: (v) => {
                      state.page.pageNum = v
                      getAdhocQueryLeftListQuery()
                    },
                    onSizeChange: (v) => {
                      state.page.pageSize = v
                      getAdhocQueryLeftListQuery(true)
                    },
                  }
            "
          />
          <!-- errorLog -->
          <div v-else-if="state.errorLog" class="errorLog" v-html="state.errorLog"> </div>
          <div v-else class="empty">
            <img class="pic" src="/src/assets/table-no-content-small.png" />
            <p class="text">暂无运行日志</p>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <n-modal-footer class="dialog-footer cenetr-footer">
        <n-button size="sm" @click.prevent="handleClose" style="border-color: #a3b4db">
          取消
        </n-button>
        <n-button color="primary" variant="solid" @click.prevent="handleConfirm">确 定</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script setup>
  import { reactive, watch } from 'vue'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { tableManagePage } from '@/api/dataManage'
  import { ElNotification } from 'element-plus'

  import codemirror from 'codemirror-editor-vue3'
  // 核心样式
  import 'codemirror/lib/codemirror.css'
  // 引入主题后还需要在 options 中指定主题才会生效
  import 'codemirror/theme/solarized.css'
  import 'codemirror/mode/sql/sql.js'
  // require active-line.js
  import 'codemirror/addon/selection/active-line.js'
  // closebrackets
  import 'codemirror/addon/edit/closebrackets.js'
  // keyMap
  import 'codemirror/mode/clike/clike.js'
  import 'codemirror/addon/edit/matchbrackets.js'
  import 'codemirror/addon/comment/comment.js'
  import 'codemirror/addon/dialog/dialog.js'
  import 'codemirror/addon/dialog/dialog.css'
  import 'codemirror/addon/search/searchcursor.js'
  import 'codemirror/addon/search/search.js'
  import 'codemirror/keymap/emacs.js'
  // 引入代码自动提示插件
  import 'codemirror/addon/hint/show-hint.css'
  import 'codemirror/addon/hint/sql-hint'
  import 'codemirror/addon/hint/show-hint'

  import useClipboard from 'vue-clipboard3'
  const { toClipboard } = useClipboard()
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: () => '',
    },
    selectedData: {
      type: Object,
      default: () => {},
    },
    sqlFieldName: {
      type: String,
      default: () => 'sqlScript',
    },
  })

  const state = reactive({
    page: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
    },
    errorLog: '',
    arrowStatus: true,
    dialogVisible: false,
    loading: false,
    tabs: 'result',
    tabKey: 1,
    treeSearchText: '',
    treeData: [],
    defaultTreeData: [],
    checkTreeItem: { treeId: null },
    codemirror: null,
    sql: '',
    querySql: '',
    sqlFieldName: '',
    sqlOption: {
      tabSize: 2,
      styleActiveLine: true,
      lineNumbers: true,
      line: true,
      mode: 'text/x-sql',
      theme: 'solarized',
      hintOptions: {
        // 自定义提示选项
        completeSingle: false, // 当匹配只有一项的时候是否自动补全
      },
    },
    tableKey: 1,
    tableData: { list: [] },
    isShowModel: true,
    searchText: '',
    timeFlag: null,
    pagination: {
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 30, 50, 100],
    },
    resultList: [],
    logListMap: {},
    modelList: [],
    fieldList: [],
    tableHeadTitles: [],
  })
  const emits = defineEmits(['closeSql'])
  // 切换运行结果
  const tabsChangeFn = (e) => {
    if (e === 'result') {
      state.tableHeadTitles = [
        { name: '字段名称', prop: 'name' },
        { name: '结果值', prop: 'result' },
      ]
      state.tableData.list = [...state.resultList]
    } else {
      state.tableHeadTitles = [
        { name: '开始时间', prop: 'startTime' },
        { name: 'SQL语句', prop: 'sql' },
        { name: '耗时', prop: 'spend' },
        { name: '运行结果', prop: 'status' },
      ]
      state.tableData.list = [...state.logListMap[props.sqlFieldName]]
    }
    state.tabKey++
    nextTick(() => {
      state.tableKey++
    })
  }

  // 展开或收缩树
  const treeChange = () => {
    if (state.checkTreeItem.treeId) {
      state.treeData = updateTree(state.treeData, state.checkTreeItem.treeId)
    }
  }
  // 循环修改
  const updateTree = (arr, treeId) => {
    arr.forEach((val) => {
      if (val.id === treeId) {
        val.selected = true
      } else {
        val.selected = false
      }
      if (val.children) {
        updateTree(val.children, treeId)
      }
    })
    return arr
  }
  // 树搜索
  const searchTreeFn = () => {
    state.treeData = filterTreeData(state.defaultTreeData, state.treeSearchText)
  }
  // 循环过滤
  const filterTreeData = (treeData, text) => {
    // 使用map复制一下节点，避免修改到原树
    return treeData
      .map((node) => ({ ...node }))
      .filter((node) => {
        node.children = node.children && filterTreeData(node.children, text)
        return node.name.indexOf(text) !== -1 || (node.children && node.children.length)
      })
  }
  // 树点击事件
  const clickFn = (node) => {
    if (state.checkTreeItem.treeId !== node.id) {
      state.checkTreeItem.treeId = node.id
      treeCheckNode(node)
    }
  }
  // 获取树
  const getTreeData = () => {
    sceneManage.searchTreeList().then((res) => {
      let { success, data } = res
      if (success) {
        if (data !== null) {
          let haveRoot = data.some((item) => {
            if (item.name === '全部') {
              return true
            }
          })
          if (haveRoot) {
            data[0].expanded = true
            state.treeData = [...data]
            state.defaultTreeData = [...data]
            if (props.selectedData?.treeId) {
              treeCheckNode({ id: props.selectedData.treeId }, true)
            } else {
              treeCheckNode(data[0].children[0])
            }
          } else {
            state.treeData = [
              {
                description: '全部',
                id: 0,
                name: '全部',
                children: data,
              },
            ]
            if (props.selectedData?.treeId) {
              treeCheckNode({ id: props.selectedData.treeId }, true)
            } else {
              treeCheckNode(state.treeData[0].children[0])
            }
          }
        }
      }
    })
  }
  // 点击树
  const treeCheckNode = (data) => {
    if (data?.id === 1 || data?.id === 'ROOT' || data?.name === '全部') {
      state.checkTreeItem.treeId = null
    } else {
      state.checkTreeItem.treeId = data?.id
    }
    state.isShowModel = true
    getModelFn()
  }
  // 表搜索
  const tableSearchFn = () => {
    if (state.timeFlag) {
      clearTimeout(state.timeFlag)
      state.timeFlag = null
    }
    state.timeFlag = setTimeout(() => {
      state.pagination.currentPage = 1
      getModelFn()
    }, 500)
  }
  // 获取模型
  const getModelFn = () => {
    let data = {
      condition: {
        tableType: 'DORIS',
        name: state.searchText,
      },
      pageNum: state.pagination.currentPage,
      pageSize: 10,
    }
    tableManagePage(data)
      .then((res) => {
        if (res.success) {
          state.modelList = res.data.list
        }
      })
      .catch(() => {})
  }
  // 获取字段
  const showFieldFn = (id) => {
    api.model.getModeData({ id: id }).then((res) => {
      if (res.success) {
        state.isShowModel = false
        state.fieldList = res.data
      }
    })
  }
  // 清空语句
  const clearFn = () => {
    state.sqlFieldName = ''
    state.querySql = ''
    state.sql = ''
  }
  // 运行语句
  const runFn = () => {
    if (!state.sql) {
      ElNotification({
        title: '提示',
        message: '请先输入sql语句！',
        type: 'warning',
      })
      return false
    }
    state.loading = true
    props.sqlFieldName === 'sqlScript' ? getRuleSqlPreview() : getAdhocQueryLeftListQuery()
  }
  const getRuleSqlPreview = () => {
    state.errorLog = ''
    api.dataQuality.getRuleSqlPreview({ sql: state.sql }).then((res) => {
      state.loading = false
      if (res.success) {
        state.sqlFieldName = res.data.name
        state.querySql = res.data?.querySql

        if (res.data.log.errorMessage.length > 0) {
          state.errorLog = res.data.log.errorMessage.map((item) => item)
        }
        state.resultList = []
        if (res.data.resultList) {
          res.data.resultList.forEach((val) => {
            state.resultList.push({
              result: val,
              name: res.data.name,
            })
          })
        }

        state.logListMap[props.sqlFieldName] ??= []
        state.logListMap[props.sqlFieldName].push(res.data.log)
        tabsChangeFn(state.tabs)
      }
    })
  }
  const getAdhocQueryLeftListQuery = (init) => {
    init && (state.page.pageNum = 1)
    state.tableData.errorLog = undefined
    api.dataManagement
      .getAdhocQueryLeftListQuery({
        name: '',
        querySql: state.sql,
        tables: '',
        type: 'ASSETS',
        pageNum: state.page.pageNum,
        pageSize: state.page.pageSize,
      })
      .then((res) => {
        state.loading = false
        if (res.success) {
          const resultList = res.data?.[0].resultData
          state.sqlFieldName = true
          state.querySql = true
          state.resultList = resultList?.list || []
          state.tableData.errorLog = res.data?.[0]?.errorLog || ''
          tabsChangeFn(state.tabs)
          state.tableHeadTitles = Object.keys(state.resultList?.[0] || {})?.map((val) => {
            return { name: val, prop: val }
          })
          state.page.total = resultList?.total || 0
        }
      })
      .finally(() => {})
  }
  // 点击取消
  const handleClose = () => {
    state.dialogVisible = false
    emits('closeSql', false)
    return false
  }
  // 点击确认
  const handleConfirm = () => {
    if (!state.sqlFieldName || !state.querySql) {
      ElNotification({
        title: '提示',
        message: '请先运行查询！',
        type: 'warning',
      })
      return false
    }
    state.dialogVisible = false
    emits('closeSql', {
      sqlScript: state.sql,
      sqlFieldName: state.sqlFieldName,
      querySql: state.querySql,
    })
    return false
  }

  // SQL语句输入时
  const onCmCodeChange = (newCode) => {
    state.sql = newCode
  }

  // SQL语句获取焦点时
  const onCmFocus = (cm) => {}
  // SQL语句准备完成时
  const onCmReady = (cm) => {
    state.codemirror = cm
    state.codemirror.setSize('-webkit-fill-available', 'auto')
    state.codemirror.on('keypress', (e) => {
      state.codemirror.showHint()
    })
  }
  // 复制
  const copyFn = async (text) => {
    try {
      toClipboard(text)
      ElNotification({
        title: '提示',
        message: '复制成功',
        type: 'success',
      })
    } catch (e) {
      console.error(e)
    }
  }

  watch(
    () => props.isShow,
    () => {
      if (props.isShow) {
        state.tableData.errorLog = undefined
        state.page = {
          pageNum: 1,
          pageSize: 10,
          total: 0,
        }
        state.resultList = []
        state.dialogVisible = true
        state.tabs = 'result'
        tabsChangeFn(state.tabs)
        if (props.selectedData) {
          if (props.selectedData.sqlScript || props.selectedData.warnQuerySql) {
            state.sql = props.selectedData[props.sqlFieldName]
            state.querySql = props.selectedData.querySql
            state.sqlFieldName = props.selectedData.sqlFieldName
          }
        }
      }
    },
  )
  // 挂载时执行
  onMounted(async () => {
    // getTreeData()
    getModelFn()
  })
</script>
<style>
  ul.CodeMirror-hints {
    z-index: 2071;
  }
</style>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  .code-container {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 314px;
    padding-bottom: 1px;
    overflow: hidden;
    color: #1f2329;
    background: #f5f6f7;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-radius: 2px;

    .codemirror {
      height: 100% !important;
      background-color: #fff;
      :deep(.CodeMirror) {
        height: 100% !important;
        overflow: hidden;
        box-shadow: none;
      }
      :deep(.CodeMirror-scroll) {
        height: 100%;
        overflow-y: auto;
        .CodeMirror-sizer {
          .CodeMirror-cursors {
            top: 5px;
          }
          .CodeMirror-code > div {
            padding: 5px 0;
          }
          .CodeMirror-linenumber {
            padding: 0 6px;
            text-align: center;
          }
          .CodeMirror-line {
            padding: 0 10px;
            > span {
              padding-right: 10px !important;
            }
          }
          .CodeMirror-linebackground {
            right: 4px;
            left: 4px;
            border-radius: 6px;
          }
        }
      }
      :deep(.CodeMirror-gutters) {
        width: 32px;
        min-height: 100%;
        background-color: #f6f7fb;
      }
      :deep(.CodeMirror-vscrollbar) {
        visibility: initial !important;
        &::-webkit-scrollbar-thumb {
          background-color: #b1bcd6;
          border-radius: 6px;
          &:hover {
            background-color: #b1bcd6;
          }
        }
      }
    }
  }
  .modal-body {
    .top {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        margin-bottom: 8px;
        &-name {
          color: #1d2129;
          font-weight: 500;
          font-size: 16px;
          display: flex;
          align-items: center;
          span {
            color: #909399;
            font-weight: normal;
            font-size: 12px;
          }
          .illustrate {
            color: #8091b7;
            margin-left: 2px;
            &:hover {
              color: $themeBlue;
            }
          }
        }
        &-btn {
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              margin-right: 2px;
              font-size: 16px;
            }
          }
        }
      }
      .asset {
        width: 220px;
        .asset-container {
          box-sizing: border-box;
          width: 100%;
          height: 100%;
          padding: 0;

          &-ipt {
            margin-bottom: 8px;

            :deep(.nancalui-input-slot__suffix) {
              opacity: 0.5;
              .icon-search {
                font-weight: normal;
                transform: scale(1.4);
              }
            }
          }

          &-tree {
            height: 320px;
            overflow-y: auto;
            :deep(.nancalui-tree) {
              .nancalui-tree__node {
                border-radius: 6px;

                &:has(.active) {
                  background-color: #e3ecff;
                }

                &:hover {
                  background-color: #e3ecff;

                  .nancalui-tree__node-content--value-wrapper {
                    .tree-icon {
                      color: $themeBlue;
                    }

                    .tree-label {
                      max-width: calc(100% - 73px);
                      color: $themeBlue;
                    }

                    .tree-btn {
                      display: inline;
                    }
                  }
                }

                .nancalui-tree__node-vline {
                  width: 0;
                }

                .nancalui-tree__node-content {
                  border-radius: 6px;

                  &:hover {
                    background-color: #e3ecff;
                  }

                  > span {
                    width: 28px;
                    padding-left: 10px;
                    color: #8091b7;
                  }

                  .nancalui-tree__node-content--value-wrapper {
                    position: relative;
                    width: 100%;
                  }

                  &.active {
                    background-color: #e3ecff;

                    .nancalui-tree__node-content--value-wrapper {
                      .tree-label,
                      .tree-icon {
                        color: $themeBlue;
                      }
                    }

                    & > span {
                      svg {
                        color: $themeBlue;
                      }
                    }
                  }

                  .tree-icon {
                    min-width: 16px;
                    margin-right: 4px;
                    color: #8091b7;
                    font-size: 16px;
                  }

                  .tree-label {
                    max-width: calc(100% - 20px);
                    overflow: hidden;
                    color: rgba(0, 0, 0, 0.85);
                    font-size: 14px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }

                  .tree-btn {
                    display: none;
                    width: 80px;

                    .icon {
                      margin-left: 4px;
                      color: $themeBlue;
                      font-size: 12px;
                    }
                  }
                }
              }
            }
          }
        }
      }
      .code {
        width: calc(100% - 230px);
        height: 368px;
        padding: 12px 12px 0 12px;
        box-sizing: border-box;
        border: 1px solid #dcdfe6;
      }
      .field {
        width: 230px;
        height: 368px;
        padding: 12px 12px 0 12px;
        box-sizing: border-box;
        border: 1px solid #dcdfe6;
        border-left: 0;
        .field-container {
          box-sizing: border-box;
          height: 290px;
          overflow-y: auto;
          &-back {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64px;
            height: 32px;
            color: #6e9eff;
            border-radius: 6px;
            cursor: pointer;
            &:hover {
              background-color: #e3ecff;
            }
            .icon {
              margin-right: 4px;
              font-size: 16px;
            }
          }
          .label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            width: 100%;
            height: 32px;
            margin-bottom: 2px;
            padding: 0 8px;
            border-radius: 6px;
            cursor: pointer;
            &:hover {
              background-color: #e3ecff;
              .icon {
                display: block;
              }
            }
            &-name {
              width: calc(100% - 24px);
              overflow: hidden;
              color: $themeBlue;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
              &.fieldChild {
                color: rgba(0, 0, 0, 0.75);
              }
            }
            .icon {
              display: none;
              color: $themeBlue;
              font-size: 16px;
              &:hover {
                color: #6e9eff;
              }
            }
          }
        }
      }
    }
    .tab {
      &-box {
        position: relative;

        svg {
          position: absolute;
          top: 14px;
          right: 12px;
          transition: 0.1s linear;
        }
      }
      .tab-container {
        width: 100%;
        height: 206px;
        margin-top: 16px;
        transition: 0.1s linear;
      }
    }
    .errorLog {
      height: 100%;
      overflow-y: auto;
      white-space: pre-wrap;
    }

    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding-top: 100px;
      text-align: center;

      .pic {
        display: block;
        width: 48px;
        height: auto;
        margin: 0 auto;
      }

      .text {
        margin-top: 4px;
        color: rgba(0, 0, 0, 0.55);
        font-size: 14px;
        line-height: normal;
      }
    }
  }

  :deep(.CodeMirror-gutter) {
    background: #e6e8eb;
  }
  :deep(.code-container .codemirror .CodeMirror-scroll .CodeMirror-sizer .CodeMirror-linenumber) {
    color: #000;
  }
  :deep(.CodeMirror-activeline-background) {
    background: #f0f2f5;
    border-radius: 0 !important;
  }
</style>
