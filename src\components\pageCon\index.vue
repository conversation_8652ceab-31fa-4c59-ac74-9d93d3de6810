<template>
  <div :class="['page-con', isFull ? 'is-full' : '']">
    <pageRow class="border-b">
      <template #left>
        <template v-if="props.title">
          <SvgIcon v-if="props.icon" :icon="props.icon" />
          <SvgIcon class="icon-fixed" v-else icon="icon-title" />
          <span class="ml-8">{{ props.title }}</span>
        </template>
        <template v-else-if="props.btn">
          <AuditButton
            :isShow="true"
            color="primary"
            variant="solid"
            :icon="props.btn"
            :name="props.btnName"
            :auditCode="props.btnCode"
            @click.stop="btnClick"
          />
        </template>
      </template>
      <template #right>
        <SvgIcon class="pointer" icon="con-hide" size="16" v-show="!isFull" @click="onHide" />
        <SvgIcon class="pointer ml-16" icon="con-expand" size="16" @click="onFull" />
      </template>
    </pageRow>
    <div class="page-content" v-show="!isHide">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue'
  const emit = defineEmits(['resize', 'btnClick'])
  const props = defineProps(['icon', 'title', 'btn', 'btnName', 'btnCode'])
  // 折叠/展开
  const isHide = ref(false)
  function onHide() {
    isHide.value = !isHide.value
  }
  // 全屏/收起
  const isFull = ref(false)
  function onFull() {
    isFull.value = !isFull.value
    nextTick(() => {
      emit('resize')
    })
  }
  function btnClick() {
    nextTick(() => {
      emit('btnFuc')
    })
  }
</script>
<style lang="scss" scoped>
@import '/src/styles/variables.scss';
  .page-con {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 8px;
    flex: 1;
    &.is-full {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9999;
    }

    .border-b {
      border-bottom: 1px solid $newLineColor;
      position: relative;
      .ml-8 {
        font-size: 18px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.9);
      }
      .icon-fixed {
        width: 4px;
        height: 18px;
        position: absolute;
        left: 0;
      }
    }

    .page-content {
      height: calc(100% - 52px);
    }
  }
</style>
