<template>
  <div class="node">
    <div class="imgBox">
      <img :src="imageUrl" alt="" />
    </div>
    <el-tooltip :content="data.label" placement="top-start">
      <div class="content">
        {{ data.label }}
      </div>
    </el-tooltip>
  </div>
</template>

<script>
  export default {
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {},
        iconMap: [
          {
            key: 'excel',
            value: 'doc-excel',
          },
          { key: 'word', value: 'doc-word' },
          { key: 'pdf', value: 'doc-pdf' },
          { key: 'image', value: 'doc-image' },
          { key: 'video', value: 'doc-video' },
          { key: 'audio', value: 'doc-audio' },
        ],
        icon: '',
      }
    },
    computed: {
      imageUrl() {
        return new URL(`/src/assets/img/doc/${this.icon}.svg`, import.meta.url).href
      },
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      const matchIcon = this.iconMap.find((item) => this.data.type.includes(item.key))
      if (matchIcon) {
        this.icon = matchIcon.value
      } else {
        this.icon = 'doc-other'
      }
      console.log(this.icon)
    },
  }
</script>

<style scoped lang="scss">
  .node {
    width: 100px;
    height: 68px;
    cursor: pointer;
    .imgBox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: max-content;
      img {
        width: 46px;
        height: 46px;
      }
    }
    .content {
      width: 100%;
      overflow: hidden;
      color: #000;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
      word-break: keep-all;
    }
  }
</style>
