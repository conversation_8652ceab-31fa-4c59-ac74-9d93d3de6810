<template>
  <!-- 数据服务-API监控日志列表 -->
  <div :class="['api-management-list container', state.isLzos ? 'isLzos' : '']">
    <div class="list-box" v-loading="state.loading">
      <n-public-table
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="120"
        @tablePageChange="tablePageChange"
      >
        <template #pageTop>
          <div class="box-add">
            <search @handleSearch="handleSearch" />
          </div>
        </template>

        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button
              v-if="buttonAuthList.includes('service_APIMonitoringLog_view')"
              code="service_APIMonitoringLog_view"
              class="seeDetails has-right-border"
              variant="text"
              @click.prevent="searchTodo(editor)"
              >查看实例
            </n-button>
          </div>
        </template>
      </n-public-table>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, toRefs } from 'vue'
  import search from './components/search'
  import { formartTime } from '@/utils/index'
  import { useStore } from 'vuex'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'

  import { checkCIp } from '@/utils/validate'

  export default {
    title: 'List',
    components: { search },
    props: {},
    setup() {
      const router = useRouter()
      const store = useStore()
      //按钮权限
      const { buttonAuthList } = toRefs(store.state.user)
      const form = ref()
      // 获取当前组件实例

      const state = reactive({
        isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
        tableHeight: 436,
        tableData: {},
        loading: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          // { prop: 'projectName', name: '场景名称' },
          { prop: 'apiName', name: 'API名称' },
          { prop: 'apiUrl', name: 'API路径' },
          { prop: 'reqTimes', name: '调用次数' },
          // { prop: 'lastReqStatus', name: '执行状态' },
          { prop: 'lastReqTime', name: '最近调用时间' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        startTime: null,
        endTime: null,
        keyword: null,
        status: '',
        authorizeId: '',
      })

      const runState = (val) => {
        let text = ''
        switch (val) {
          case 'SUCCESS':
            text = '成功'
            break
          case 'RUNNING_EXECUTION':
            text = '运行中'
            break
          default:
            text = '-'
            break
        }
        return text
      }

      const methods = {
        setTableHeight() {
          if (state.isLzos) {
            state.tableHeight = document.body.offsetHeight - 151 - 50 + 72
          } else {
            state.tableHeight = document.body.offsetHeight - 151 - 50
          }
        },
        // 初始化table
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage

          let data = {
            condition: {
              startTime: state.startTime || null,
              endTime: state.endTime || null,
              name: state.keyword || null,
            },

            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
          }
          state.loading = true
          api.dataService['apiMonitorSearch'](data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.tableData = {}
              state.loading = false
            })
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 查看实例
        searchTodo(editor) {
          let { row } = editor
          router.push({ name: 'APIMonitoringLogDetail', query: { id: row.id, type: 'EDIT' } })
        },
        // 搜索
        handleSearch(data) {
          let { time, keyword, status } = data
          state.keyword = keyword ? keyword : null
          state.status = status
          if (status === 'all') {
            state.status = null
          }
          state.startTime = null
          state.endTime = null
          if (time) {
            if (time[0]) {
              state.startTime = formartTime(time[0])
            }
            if (time[1]) {
              state.endTime = formartTime(time[1], true)
            }
          }
          state.pagination.currentPage = 1
          methods.initTable()
        },
      }
      onMounted(() => {
        methods.initTable()
      })
      methods.setTableHeight()
      return {
        state,
        buttonAuthList,

        checkCIp,
        form,
        runState,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .api-management-list {
    .list-box {
      height: 100%;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 4px;

      .box-add {
        .commonForm-search {
          padding: 16px 0;
        }
      }

      .left {
        .nancalui-button {
          background: $themeBlue;
        }
      }

      .seeDetails {
        color: $themeBlue;
      }

      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
        background: #18ba72;
        border-radius: 50px;

        &.red {
          background: #f54446;
        }

        &.blue {
          background: $themeBlue;
        }
      }
    }
  }
</style>
