<template>
  <n-modal
    class="file-view-dialog"
    v-model="state.dialogVisible"
    :draggable="false"
    width="928px"
    :close-on-click-overlay="false"
    :before-close="handleClose"
    title="选择字段"
    bodyMaxHeight="700px"
  >
    <div class="modal-body">
      <div
        :class="[
          'between',
          'modal-body-top',
          'expand-box',
          state.expandTableStatus[0] ? '' : 'hide',
        ]"
      >
        <!-- <div class="left-tree">
          <p class="each-title">资产目录</p>
          <div class="table-tree scroll-bar-style">
            <n-input
              class="table-tree-ipt"
              v-model="state.treeSearchText"
              placeholder="请输入"
              suffix="search"
              @input="searchTreeFn()"
              clearable
              @clear="searchTreeFn"
            />
            <n-tree
              ref="treeRef"
              :data="state.treeData"
              @node-click="treeCheckNode"
              class="tree-box"
            >
              <template #content="{ nodeData }">
                <SvgIcon class="tree-icon" icon="icon-tree-group" v-if="!nodeData.expanded" />
                <SvgIcon class="tree-icon" icon="tree-open" v-else />

                <div class="tree-label" :title="nodeData.name">
                  {{ nodeData.name }}
                </div>
              </template>
              <template #icon="{ nodeData, toggleNode }">
                <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
                <span
                  v-else
                  @click="
                    (event) => {
                      event.stopPropagation()
                      toggleNode(nodeData)
                    }
                  "
                >
                  <svg
                    :style="{
                      transform: nodeData.expanded ? 'rotate(90deg)' : '',
                      marginLeft: '-2.5px',
                      marginRight: '6px',
                      cursor: 'pointer',
                    }"
                    viewBox="0 0 1024 1024"
                    width="8"
                    height="8"
                  >
                    <path
                      d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
              </template>
            </n-tree>
          </div>
        </div> -->
        <div class="center-table">
          <p class="each-title hasIpt"
            >选择模型
            <n-input
              class="each-title-ipt"
              v-model="state.searchText"
              placeholder="中文名称/英文名称"
              suffix="search"
              @input="initTable()"
              clearable
              @clear="initTable"
            />
          </p>
          <n-checkbox-group
            v-model="state.checkTableList"
            size="sm"
            style="width: 100%"
            @change="changeCheckModal"
          >
            <n-table
              :data="state.tableData"
              v-loading="state.loading"
              table-height="320px"
              fix-header
              style="width: 100%"
              class="table"
              :row-key="(item) => item.id"
              @row-click="onRowClick"
            >
              <n-column resizeable width="60">
                <template #default="scope">
                  <n-checkbox :value="scope.row.id" :key="scope.row.id" />
                </template>
              </n-column>
              <n-column resizeable field="cnName" header="中文名称" />
              <n-column resizeable field="name" header="英文名称" />
              <n-column resizeable field="dataCount" header="数据总量" />
              <template #empty>
                <div class="empty">
                  <img class="pic" src="@/assets/table-no-content-small.png" />
                  <div class="empty-word">暂无数据</div>
                </div>
              </template>
            </n-table>
          </n-checkbox-group>

          <div class="table-content-pagination">
            <div class="table-content-pagination-total"
              >共<span>{{ state.total }}</span
              >条数据</div
            >
            <n-pagination
              :total="state.total"
              v-model:pageSize="state.pagination.pageSize"
              v-model:pageIndex="state.pagination.currentPage"
              :can-change-page-size="true"
              @page-index-change="tablePageIndexChange"
              @page-size-change="tablePageSizeChange"
              :page-size-options="[10, 20, 50, 100]"
              :max-items="3"
            />
          </div>
        </div>
        <div class="right-field">
          <p class="each-title"
            >选择字段<span
              v-if="state.selectedRow.name"
              :title="state.selectedRow.cnName || state.selectedRow.name"
              >（{{ state.selectedRow.cnName || state.selectedRow.name }}）</span
            ></p
          >
          <div class="select-row-box">
            <n-checkbox-group
              v-if="state.fieldList.length"
              v-model="state.nowFields"
              size="sm"
              style="width: 100%"
              @change="changeCheckTable"
            >
              <n-checkbox
                v-for="t in state.fieldList"
                :key="t.id"
                :value="t.id"
                :label="t.name"
                :disabled="!t.usable"
                class="select-row-box-each"
              />
            </n-checkbox-group>
            <div class="empty" v-else>
              <img class="pic" src="@/assets/table-no-content-small.png" />
              <div class="empty-word">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-title" style="margin-top: 8px"> 已选字段 </div>
      <div
        :class="[
          'between',
          'modal-body-bottom',
          'expand-box',
          state.expandTableStatus[1] ? '' : 'hide',
        ]"
      >
        <div class="field-content">
          <div class="empty" v-if="state.nowFieldsList.length === 0" style="margin-top: 0">
            <img class="pic" src="@/assets/table-no-content-small.png" />
            <div class="empty-word">暂无数据</div>
          </div>
          <n-row v-else :wrap="true">
            <n-col
              :span="12"
              v-show="item.fields.length > 0"
              v-for="item in state.modelFieldsList"
              :key="item.id"
            >
              <p class="each-model">
                {{ item.cnName || item.name }}：
                <span
                  class="each-model-field"
                  v-for="(field, index) in item.fields"
                  :key="field.id"
                >
                  {{ field.name }} {{ index === item.fields?.length - 1 ? '' : '、' }}
                </span>
              </p>
            </n-col>
          </n-row>
        </div>
      </div>
    </div>
    <template #footer>
      <n-modal-footer class="dialog-footer cenetr-footer">
        <n-button size="sm" @click.prevent="handleClose" style="border-color: #a3b4db">
          取消
        </n-button>
        <n-button color="primary" variant="solid" @click.prevent="handleConfirm">确 定</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>

<script setup>
  import { reactive, watch } from 'vue'
  import api from '@/api/index'
  import { sceneManage } from '@/api'
  import { tableManagePage } from '@/api/dataManage'

  const emits = defineEmits(['close'])

  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: () => '',
    },
    selectedData: {
      type: Object,
      default: () => {},
    },
    algoId: {
      type: String,
      default: () => '',
    },
  })

  const state = reactive({
    total: 0,
    hasChecked: false,
    dialogVisible: false,
    tableData: [],
    selectedRow: {},
    checkedRows: [],
    treeData: [],
    treeAttrData: {
      showLeftIcon: true,
      // showCheckbox: true,
      showControl: false,
      parentControl: '',
      childControl: '',
    },
    bizDomainId: '',
    treeSearchText: '',
    searchText: null,
    defaultTreeData: [],
    fieldList: [],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 30, 50, 100],
    },
    nowFields: [],
    nowFieldsList: [],
    nowCheckedFields: [],
    checkTableList: [],
    selectedModel: [],
    nowSelectedModel: [],
    modelFieldsList: [],
    expandTableStatus: new Array(10).fill(true),
  })

  const filterTreeData = (treeData, text) => {
    return treeData
      .map((node) => ({ ...node }))
      .filter((node) => {
        node.children = node.children && filterTreeData(node.children, text)
        return (
          String(node.label).toLowerCase().indexOf(String(text).toLowerCase()) !== -1 ||
          (node.children && node.children.length)
        )
      })
  }

  // 树搜索
  const searchTreeFn = () => {
    state.treeData = filterTreeData(state.defaultTreeData, state.treeSearchText)
  }

  const handleClose = () => {
    state.dialogVisible = false
    emits('close', false)
  }

  const expandTable = (index) => {
    state.expandTableStatus[index] = !state.expandTableStatus[index]
  }

  const getModelFields = () => {
    state.modelFieldsList = []
    state.selectedModel.forEach((item) => {
      state.modelFieldsList.push({
        ...item,
        fields: state.nowFieldsList.filter((field) => field.pid === item.id),
      })
    })
  }

  const clearForm = () => {
    state.treeData = []
    state.tableData = []
    state.fieldList = []
    state.treeSearchText = ''
    state.bizDomainId = ''
    state.nowSelectedModel = []
    state.selectedModel = []
    state.checkTableList = []
    state.nowFields = []
    state.nowFieldsList = []
    state.nowCheckedFields = []
    state.modelFieldsList = []
    state.selectedRow = {}
  }

  const handleConfirm = () => {
    state.dialogVisible = false
    emits('close', {
      selectedModel: [...state.selectedModel],
      nowFieldsList: [...state.nowFieldsList],
      treeId: state.bizDomainId,
    })
  }

  const changeCheckTable = (val) => {
    if (val.length > state.nowCheckedFields.length) {
      const selectFieldId = val[val.length - 1]
      state.nowFieldsList.push({
        ...state.fieldList.find((field) => field.id === selectFieldId),
        pid: state.selectedRow.id,
        pname: state.selectedRow.name,
      })
      if (state.selectedModel.findIndex((item) => item.id === state.selectedRow.id) === -1) {
        state.selectedModel.push({ ...state.selectedRow })
        state.checkTableList.push(state.selectedRow.id)
        state.nowSelectedModel.push(state.selectedRow.id)
      }
    } else {
      const deleteIndex = state.nowFieldsList.findIndex((item) => val.indexOf(item.id) === -1)
      state.nowFieldsList.splice(deleteIndex, 1)
      let hasField = false

      state.nowFieldsList?.forEach((field) => {
        if (field.pid === state.selectedRow.id) {
          hasField = true
        }
      })
      if (!hasField) {
        const index = state.checkTableList.findIndex((item) => item == state.selectedRow.id)
        state.selectedModel.splice(index, 1)
        state.checkTableList.splice(index, 1)
        state.nowSelectedModel.splice(index, 1)
      }
    }
    state.nowCheckedFields = val
    getModelFields()
  }

  const changeCheckModal = (val) => {
    if (val.length > state.nowSelectedModel.length) {
      const addId = val[val.length - 1]
      const selectedRow = state.tableData.find((item) => item.id === addId)
      state.selectedModel.push({
        ...selectedRow,
      })
      state.selectedRow = selectedRow
    } else {
      const deleteIndex = state.selectedModel.findIndex((item) => val.indexOf(item.id) === -1)
      state.selectedModel.splice(deleteIndex, 1)
      state.selectedRow = state.selectedModel[state.selectedModel.length - 1] || {}
    }
    state.nowSelectedModel = val
    if (val.length) {
      state.hasChecked = true
      getFields()
    } else {
      state.nowFieldsList = state.nowFieldsList.filter(
        (field) => state.checkTableList.indexOf(field.pid) > -1,
      )
      state.nowFields = state.nowFields.filter(
        (fieldId) => state.nowFieldsList.findIndex((field) => field.id === fieldId) > -1,
      )
      state.nowCheckedFields = state.nowFields
      state.fieldList = []
    }
    getModelFields()
  }

  const onRowClick = (row) => {
    if (state.hasChecked) {
      state.hasChecked = false
      return
    }
    state.selectedRow = row.row
    getFields()
  }

  const treeCheckNode = (data, isInit = false) => {
    state.nowSelectedModel = []
    state.selectedModel = []
    state.checkTableList = []
    state.nowFields = []
    state.nowFieldsList = []
    state.nowCheckedFields = []
    if (data?.id === 1 || data?.id === 'ROOT' || data?.name === '全部') {
      state.bizDomainId = null
    } else {
      state.bizDomainId = data?.id
    }
    initTable(isInit)
  }

  // 表格操作变化
  const tablePageSizeChange = (data) => {
    state.pagination.currentPage = 1
    state.pagination.pageSize = data
    initTable(false)
  }

  // 表格操作变化
  const tablePageIndexChange = (data) => {
    state.pagination.currentPage = data
    initTable(false)
  }

  const initTable = (init = false) => {
    state.pagination.currentPage = init ? 1 : state.pagination.currentPage

    let params = {
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      condition: {
        tableType: 'DORIS',
        name: state.searchText === '' ? null : state.searchText,
      },
    }

    tableManagePage(params)
      .then((res) => {
        if (res.success) {
          state.tableData = res.data.list.map((item, index) => {
            return { ...item, number: index + 1 }
          })
          state.total = res.data.total
          if (init) {
            let viewModelList = []
            let vueFieldsList = []
            if (props.selectedData.selectedModel?.length > 0) {
              viewModelList = props.selectedData.selectedModel.map((item) => item.id)
            }
            if (props.selectedData.nowFieldsList?.length > 0) {
              vueFieldsList = props.selectedData.nowFieldsList.map((item) => item.id)
            }
            state.checkTableList = viewModelList
            state.nowSelectedModel = viewModelList
            state.selectedModel = props.selectedData.selectedModel
            state.selectedRow = state.selectedModel[state.selectedModel.length - 1]
            state.nowFields = vueFieldsList
            state.nowCheckedFields = vueFieldsList
            state.nowFieldsList = props.selectedData.nowFieldsList

            getModelFields()
            getFields()
          }
          state.key++
        }
      })
      .catch(() => {})
  }

  const getTreeData = () => {
    sceneManage.searchTreeList().then((res) => {
      let { success, data } = res
      if (success) {
        if (data !== null) {
          let haveRoot = data.some((item) => {
            if (item.name === '全部') {
              return true
            }
          })
          if (haveRoot) {
            data[0].expanded = true
            state.treeData = data
            state.defaultTreeData = data
            if (props.selectedData?.treeId) {
              treeCheckNode({ id: props.selectedData.treeId }, true)
            } else {
              treeCheckNode(data[0].children ? data[0].children[0] : {})
            }
          } else {
            state.treeData = [
              {
                description: '全部',
                id: 0,
                name: '全部',
                children: data,
              },
            ]
            if (props.selectedData?.treeId) {
              treeCheckNode({ id: props.selectedData.treeId }, true)
            } else {
              treeCheckNode(state.treeData[0]?.children ? state.treeData[0].children[0] : {})
            }
          }
        }
      }
    })
  }

  const getFields = () => {
    const params = {
      condition: {
        id: state.selectedRow.id,
        algoId: props.algoId,
      },
      pageNum: 1,
      pageSize: 1000,
    }

    api.assets
      .getModalFields(params)
      .then((res) => {
        state.loadingPreview = false
        if (res.success) {
          state.fieldList = res.data.list
        }
      })
      .catch(() => {
        state.loadingPreview = false
      })
  }

  watch(
    () => props.isShow,
    () => {
      if (props.isShow) {
        getTreeData()
        props.selectedData.selectedModel.length > 0 ? initTable(true) : initTable(false)
        state.dialogVisible = true
      } else {
        clearForm()
      }
    },
  )
</script>

<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  :deep(.nancalui-tree) {
    .nancalui-tree__node {
      border-radius: 6px;

      &:has(.active) {
        background-color: #ecf7ff;
      }

      &:hover {
        background-color: #ecf7ff;

        .nancalui-tree__node-content--value-wrapper {
          .tree-icon {
            color: $themeBlue;
          }

          .tree-label {
            max-width: calc(100% - 73px);
            color: $themeBlue;
          }

          .tree-btn {
            visibility: visible;
          }
        }
      }

      .nancalui-tree__node-vline {
        width: 0;
      }

      .nancalui-tree__node-content {
        border-radius: 6px;

        &:hover {
          background-color: #ecf7ff;
        }

        > span {
          width: 28px;
          padding-left: 10px;
        }

        .nancalui-tree__node-content--value-wrapper {
          position: relative;
          width: 100%;
        }

        &.active {
          background-color: #ecf7ff;

          .nancalui-tree__node-content--value-wrapper {
            .tree-label,
            .tree-icon {
              color: $themeBlue;
            }
          }

          & > span {
            svg {
              color: $themeBlue;
            }
          }
        }

        .tree-icon {
          min-width: 16px;
          margin-right: 4px;
          color: #8091b7;
          font-size: 16px;
        }

        .tree-label {
          max-width: calc(100% - 20px);
          overflow: hidden;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .tree-btn {
          display: inline-block;
          width: 80px;
          visibility: hidden;

          .icon {
            margin-left: 4px;
            color: $themeBlue;
            font-size: 12px;
          }
        }
      }
    }
  }
  .table-tree-ipt {
    margin-bottom: 8px;
    :deep(.nancalui-input-slot__suffix) {
      opacity: 0.5;
      .icon-search {
        font-weight: normal;
        transform: scale(1.4);
      }
    }
  }
  .modal-body {
    &-top {
      height: 400px;
      overflow: hidden;
      border: 1px solid #c5d0ea;
      border-radius: 2px;
      .each-title {
        width: 100%;
        height: 46px;
        line-height: 46px;
        margin: 0;
        padding: 0 12px;
        color: #1d2129;
        font-weight: 500;
        font-size: 16px;
        font-style: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.hasIpt {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: normal;
        }
        &-ipt {
          width: 264px;
        }
        span {
          color: #909399;
        }
      }
      .left-tree {
        float: left;
        width: 196px;
        border-right: 1px solid #c5d0ea;
        .table-tree {
          height: calc(100% - 42px);
          padding: 8px;
          .tree-box {
            height: calc(100% - 45px);
            padding: 0 12px;
            overflow-y: auto;
          }
        }
      }
      .center-table {
        position: relative;
        float: left;
        box-sizing: border-box;
        width: 674px;
        height: 100%;
        border-right: 1px solid #c5d0ea;
        :deep(.nancalui-table__thead) {
          .is-left {
            background-color: #e3ecff;
          }
        }
        .table {
          margin: 0;
          padding: 0 12px 12px;
        }
      }
      .right-field {
        float: left;
        width: calc(100% - 674px);
        height: 100%;
        .select-row {
          margin: 0;
          padding-left: 12px;
          color: var(---, rgba(0, 0, 0, 0.55));
          font-weight: 400;
          font-size: 14px;
          font-style: normal;
          line-height: 32px;
          &-box {
            height: calc(100% - 74px);
            overflow-y: auto;
            &-each {
              width: 100%;
              height: 32px;
              margin-top: 0;
              padding: 0 12px;
              line-height: 32px;
              &:hover {
                background: #e3ecff;
              }
            }
          }
        }
      }
    }
  }

  .content-title {
    margin-bottom: 16px;
    height: 30px;
    line-height: 30px;
    color: #1d2129;
    font-size: 16px;
    font-weight: 500;
    padding-left: 14px;
    position: relative;
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }
  }
  .expand-box {
    &.hide {
      height: 0;
      overflow: hidden;
    }
  }
  .each-model {
    margin: 0 0 8px 0;
    color: #606266;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 32px;
    &-field {
      overflow: hidden;
      color: #1d2129;
      font-weight: 400;
      font-size: 14px;
      font-family: 'Source Han Sans CN';
      font-style: normal;
      line-height: 32px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .table-content-pagination {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 48px;
    padding: 12px 16px;
    :deep(.nancalui-pagination) {
      float: right;
      .nancalui-pagination__size {
        margin-right: 0;
      }
    }

    &-total {
      float: left;
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      span {
        color: #447dfd;
      }
    }
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 100px);
    margin-top: 60px;

    .pic {
      width: 72px;
      height: auto;
      margin-bottom: 10px;
    }

    &-word {
      color: rgba(0, 0, 0, 0.46);
      font-weight: 400;
      font-size: 14px;
      text-align: center;
    }
  }
</style>
