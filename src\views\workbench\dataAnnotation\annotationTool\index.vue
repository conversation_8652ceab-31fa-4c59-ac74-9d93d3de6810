<template>
  <div class="container">
    <div class="tool-content">
      <div class="btn-group">
        <!-- 新增工具 -->
        <n-button class="add-tool-btn" variant="solid" @click="addTool"> 新增工具 </n-button>
      </div>
      <!-- 工具列表 -->
      <div class="tool-list">
        <CfTable
          actionWidth="220"
          ref="tableNoRef"
          :tableConfig="{
            data: state.dataSource,
            rowKey: 'id', // 唯一标识
          }"
          :table-head-titles="tableHead"
          :paginationConfig="{
            total: state.total, // 总条数
            pageSize: state.searchData.pageSize, // 每页显示条数
            currentPage: state.searchData.pageNum, // 当前页
            onCurrentChange: (v) => {
              // 页码改变
              state.searchData.pageNum = v // 当前页
              onSearch(false) // 搜索
            },
            onSizeChange: (v) => {
              // 每页显示条数改变
              state.searchData.pageSize = v // 每页显示条数
              onSearch() // 搜索
            },
          }"
        >
          <template #editor="{ row }">
            <!-- 编辑、删除、下载 -->
            <n-button class="edit-btn" variant="text" color="primary" @click="editTool(row)">
              编辑
            </n-button>
            <n-button class="delete-btn" variant="text" color="primary" @click="deleteTool(row)">
              删除
            </n-button>
            <n-button
              class="download-btn"
              variant="text"
              color="primary"
              @click="downloadTool(row)"
            >
              下载
            </n-button>
          </template>
        </CfTable>
      </div>
    </div>
    <n-drawer
      v-model="state.modalVisible"
      title=""
      :size="680"
      :esc-key-closeable="false"
      :close-on-click-overlay="true"
      :before-close="closeDrawer"
      class="team-drawer_234234246436"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">{{
              isNaN(state.formData.id) ? '新增标注工具配置' : '编辑工具标注工具配置'
            }}</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawer" />
        </div>
        <div class="n-drawer-body-content">
          <n-form :data="state.formData" :rules="rules" :label-width="130" ref="formRef">
            <!-- 工具名称 -->
            <n-form-item label="工具名称：" field="name">
              <n-input v-model="state.formData.name" />
            </n-form-item>
            <!-- 工具类型 -->
            <n-form-item label="工具类型：" field="type">
              <n-select v-model="state.formData.type" placeholder="请选择">
                <n-option label="在线" value="在线" />
                <n-option label="离线" value="离线" />
              </n-select>
            </n-form-item>
            <n-form-item label="数据类型：" field="dataType">
              <n-select v-model="state.formData.dataType" placeholder="请选择">
                <n-option label="文本标注" value="文本标注" />
                <n-option label="图像标注" value="图像标注" />
                <n-option label="视频标注" value="视频标注" />
                <n-option label="音频标注" value="音频标注" />
                <n-option label="时序数据标注" value="时序数据标注" />
                <n-option label="结构化标注" value="结构化标注" />
              </n-select>
            </n-form-item>
            <n-form-item label="版本：" field="version">
              <n-input v-model="state.formData.version" maxlength="50" />
            </n-form-item>
            <!-- 工具文件 -->
            <n-form-item label="工具文件：" field="url">
              <div style="width: 100%">
                <n-upload
                  style="width: 100%; display: flex; justify-content: center"
                  droppable
                  :before-upload="beforeUpload"
                >
                  <div class="upload-box">
                    <div class="upload-box-icon">
                      <SvgIcon class="icon" icon="upload-cloud-new" title="上传" />
                    </div>
                    <div class="h2">点击或将文件拖拽到这里上传</div>
                    <div class="p">最大支持5G</div>
                  </div>
                </n-upload>
                <div v-if="state.formData.url" class="upload-list">
                  <SvgIcon class="icon" icon="icon-attachment" title="序列" />
                  <div class="name">{{ state.formData.fileOriginalName }}</div>
                  <SvgIcon
                    class="icon del"
                    icon="icon-new-delete"
                    title="删除"
                    @click.prevent.stop="state.formData.url = undefined"
                  />
                </div>
              </div>
            </n-form-item>
          </n-form>
        </div>
        <div class="n-drawer-body-footer">
          <!-- 确定 -->
          <n-button color="primary" variant="solid" @click.prevent="handleSubmit"> 确定 </n-button>
          <!-- 取消 -->
          <n-button color="primary" @click.prevent="state.modalVisible = false"> 取消 </n-button>
        </div>
      </div>
    </n-drawer>
  </div>
</template>
<script setup>
  import api from '@/api/index' // 接口
  const formRef = ref() // 表单ref
  const state = reactive({
    // 表格数据
    modalVisible: false, // 弹窗是否显示
    formData: {
      // 表单数据
      name: '', // 工具名称
      type: '', // 工具类型
      dataType: '', // 数据类型
      url: '', // 文件URL
      fileOriginalName: '', // 文件原始名称
      version: '', // 版本
    },
    dataSource: [], // 表格数据
    searchData: {
      // 搜索数据
      pageNum: 1, // 当前页
      pageSize: 10, // 每页显示条数
      condition: {}, // 条件
    },
    total: 0, // 总条数
  })
  const rules = {
    name: [{ required: true, message: '请输入工具名称', trigger: 'blur' }], // 工具名称
    type: [{ required: true, message: '请选择工具类型', trigger: 'blur' }], // 工具类型
    url: [{ required: true, message: '请上传工具文件', trigger: 'blur' }], // 工具文件
    dataType: [{ required: true, message: '请选择数据类型', trigger: 'blur' }], // 数据类型
    version: [{ required: true, message: '请输入版本', trigger: 'blur' }], // 数据类型
  }
  const tableHead = computed(() => {
    // 表格表头
    return [
      // 表格表头
      { name: '工具名称', prop: 'name', width: 600 }, // 工具名称
      { name: '数据类型', prop: 'dataType' }, // 数据类型
      { name: '工具类型', prop: 'type' }, // 工具类型
      { name: '版本', prop: 'version' }, // 版本
      { name: '更新时间', prop: 'updateTime' }, // 更新时间
    ]
  })
  const addTool = () => {
    // 新增工具
    state.formData = {}
    console.log('新增工具')
    state.modalVisible = true // 弹窗是否显示
  }
  const closeDrawer = () => {
    // 关闭弹窗
    console.log('关闭弹窗')
    state.modalVisible = false // 弹窗是否显示
  }
  //文件上传
  function beforeUpload(_data) {
    let file = _data[0].file
    const formData = new FormData()
    formData.append('file', file)
    formData.append('bucket', 'data-govern')
    api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
      if (res.success) {
        // 上传成功
        state.formData = {
          ...state.formData,
          url: res.data.url, // 工具文件
          fileOriginalName: file.name, // 工具文件名称
        }
        ElMessage.success('上传成功') // 提示
      } else {
        // 上传失败
        ElMessage.error('上传失败') // 提示
      }
    })

    return false
  }
  const handleSubmit = () => {
    // 提交
    formRef.value.validate((valid) => {
      // 表单验证
      if (!valid) {
        // 验证失败
        ElMessage.error('请填写完整信息') // 提示
        return // 退出
      }
      state.modalVisible = false // 弹窗是否显示
      api.dataGovernance[isNaN(state.formData.id) ? 'addAnnotationTool' : 'editAnnotationTool'](
        state.formData,
      ).then((res) => {
        // 新增工具
        if (res.success) {
          // 成功
          if (state.formData.id) {
            ElMessage.success('编辑成功') // 提示
          } else {
            ElMessage.success('新增成功') // 提示
          }

          onSearch() // 搜索
        }
      })
    })
  }
  const onSearch = (reset = true) => {
    // 搜索
    if (reset) {
      // 重置页码
      state.searchData.pageNum = 1 // 当前页
    }
    api.dataGovernance.getAnnotationTool(state.searchData).then((res) => {
      // 成功
      if (res.success) {
        // 成功
        state.dataSource = res.data?.list || [] // 表格数据
        state.total = res.data?.total || 0 // 总条数
      } else {
        // 失败
        ElMessage.error('获取失败') // 提示
      }
    })
  }
  // 编辑
  const editTool = (row) => {
    // 编辑
    console.log('编辑', row)
    state.formData = { ...row } // 表单数据
    state.modalVisible = true // 弹窗是否显示
  }
  // 删除
  const deleteTool = (row) => {
    // 删除
    console.log('删除', row)
    ElMessageBox.confirm('确定删除该工具吗？', '提示', {
      // 提示框
      confirmButtonText: '确定', // 确定按钮文本
      cancelButtonText: '取消', // 取消按钮文本
      type: 'warning', // 类型
    }).then(() => {
      // 确定
      api.dataGovernance.deleteAnnotationTool(row.id).then((res) => {
        // 删除
        if (res.success) {
          // 成功
          ElMessage.success('删除成功') // 提示
          onSearch() // 搜索
        } else {
          // 失败
          ElMessage.error('删除失败') // 提示
        }
      })
    })
  }

  // 下载
  const downloadTool = (row) => {
    // 下载
    fetch(row.url).then((res) => {
      // 下载
      res.blob().then((blob) => {
        // 转换为blob
        const url = window.URL.createObjectURL(blob) // 创建url
        const a = document.createElement('a') // 创建a标签
        a.href = url // 设置href
        a.download = row.fileOriginalName // 设置下载名称
        a.click() // 点击下载
        window.URL.revokeObjectURL(url) // 释放url
      })
    })
  }
  onSearch()
</script>
<style lang="scss" scoped>
  .container {
    padding: 16px;
    border-radius: 0;

    .tool-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 0;

      .btn-group {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 16px;
      }

      .tool-list {
        width: 100%;
        height: calc(100% - 64px);
        padding: 0 16px;
      }
    }
  }
</style>
<style lang="scss">
  .team-drawer_234234246436 {
    .nancalui-upload {
      width: 100%;
      display: flex;
      justify-content: center;

      > div {
        width: 100%;
      }
    }

    .upload-box {
      display: flex;
      flex-direction: column;
      gap: 18px;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 100%;
      height: 240px;
      padding: 30px 16px;
      background: var(---, #f5f7fa);
      background-color: #f5f7fa;
      border: 1px solid var(---, #c9cdd4);
      border-radius: 1.8px;
      cursor: pointer;

      .icon {
        font-size: 48px;
      }

      .h2 {
        color: var(----, #1d2129);
        font-weight: bold;

        font-size: 16px;
        font-family: 'PingFang SC';
        font-style: normal;
        line-height: 24px;
        text-align: center;
        /* 150% */
      }

      .p {
        margin-top: 21px;

        color: var(---, #909399);
        font-weight: 400;
        font-size: 14px;
        font-family: 'Source Han Sans CN';
        font-style: normal;
        line-height: 22px;
        text-align: center;
        /* 157.143% */
      }
    }

    .upload-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-top: 12px;

      .icon {
        color: #8091b7;
        font-size: 14px;

        &.del,
        &.down {
          cursor: pointer;

          &:hover {
            color: #1e89ff;
          }
        }
      }

      .name {
        width: calc(100% - 60px);
        color: #1e89ff;
        font-size: 14px;
      }
    }
  }
</style>
