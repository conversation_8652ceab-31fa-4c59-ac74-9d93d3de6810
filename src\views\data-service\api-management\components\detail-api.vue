<template>
  <div class="components-detail-api publicFormCss">
    <div class="components-detail-api-box">
      <div
        :class="{
          'scroll-bar-style': true,
          'components-detail-api-box-content': true,
          'only-detail': state.type === 'DETAIL',
        }"
      >
        <div
          v-show="state.state === 'REJECT' || state.state === 'PASS'"
          :class="{
            'audit-box': true,
            publish: state.state === 'PASS',
            fail: state.state === 'REJECT',
          }"
        >
          <div class="audit-user-box">
            <div class="audit-state">{{ state.state === 'PASS' ? '已发布' : '审核失败' }}</div>
            <div class="audit-user">
              <div>审核人：{{ state.ruleForm.updateByName }}</div>
              <div>审核时间：{{ state.ruleForm.updateTime }}</div>
            </div>
          </div>
          <div class="audit-message">{{
            state.ruleForm.auditComments ? state.ruleForm.auditComments : '无审批意见'
          }}</div>
        </div>
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          label-width="90px"
          label-align="end"
          class="demo-ruleForm disable-hide-border"
        >
          <n-form-item label="API名称：">
            <n-input
              v-model="state.ruleForm.apiName"
              maxlength="30"
              placeholder=""
              :disabled="state.disabled"
              noborder
            />
          </n-form-item>
          <n-form-item label="参数协议：">
            <n-input
              v-model="state.ruleForm.protocol"
              placeholder=""
              :disabled="state.disabled"
              noborder
            />
          </n-form-item>
          <n-form-item label="请求方式：">
            <n-input
              v-model="state.ruleForm.requestMethod"
              placeholder=""
              :disabled="state.disabled"
              noborder
            />
          </n-form-item>

          <n-form-item label="请求Path：">
            <n-input v-model="state.ruleForm.showUrl" placeholder="" disabled noborder />
          </n-form-item>

          <n-form-item label="Token：">
            <n-input v-model="state.ruleForm.token" placeholder="" disabled noborder />
          </n-form-item>
          <n-form-item label="鉴权机制：">
            <n-input
              v-model="state.ruleForm.authenticationModeWords"
              placeholder=""
              disabled
              noborder
            />
          </n-form-item>
          <n-form-item label="创建人：">
            <n-input v-model="state.ruleForm.createByName" placeholder="" disabled noborder />
          </n-form-item>
          <n-form-item label="创建时间：">
            <n-input v-model="state.ruleForm.createTime" placeholder="" disabled noborder />
          </n-form-item>

          <n-form-item label="描述信息：">
            <n-textarea
              v-model="state.ruleForm.description"
              maxlength="200"
              :autosize="{ minRows: 3 }"
              :disabled="state.disabled"
              noborder
            />
          </n-form-item>

          <n-form-item label="请求参数：">
            <div class="hasParams">
              <n-public-table
                :isDisplayAction="false"
                :table-head-titles="state.tableHeadTitles"
                :showPagination="false"
                :tableHeight="'auto'"
                :tableData="state.tableData"
                :maxHeight="240"
              />
            </div>
          </n-form-item>
        </n-form>
      </div>
    </div>
  </div>
</template>
<script>
  import { ref, reactive, onMounted } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  export default {
    name: '',
    components: {},
    props: {},
    setup() {
      const router = useRouter()
      const state = reactive({
        tableData: {},
        saveLoading: false,
        testLoading: false,
        passwordType: 'password',
        disabled: true, // 表单不可用
        ruleForm: {
          apiName: '',
          protocol: '',
          requestMethod: '',
          example: 'https://{IP}/{PlatformName}/{ProjectName}',
          apiUrl: '/collect/status',
          showUrl: '',
          description: '',
          token: '',
          createByName: '',
          createTime: '',
          updateByName: '',
          updateTime: '',
          auditComments: '',
          paramColumns: [], // 自定义参数
          authenticationMode: [],
        },
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'name', name: '字段名称' },
          { prop: 'fieldTypeName', name: '类型' },
          { prop: 'fieldLength', name: '长度' },
          { prop: 'description', name: '描述' },
        ],
        detailId: '',
        type: 'DETAIL', // 页面展示形式 ADD EDIT  DETAIL
        state: '',
      })

      const addParameters = ref()
      const methods = {
        //获取鉴权文案
        getAuthenticationMode() {
          let words = []
          if (state.ruleForm.authenticationMode.includes('IP_WHITELIST')) {
            words.push('IP白名单鉴权')
          }
          if (state.ruleForm.authenticationMode.includes('TOKEN')) {
            words.push('Token鉴权')
          }

          return words.join('、')
        },
        init(data) {
          if (data) {
            Object.keys(state.ruleForm).forEach((key) => {
              state.ruleForm[key] = data[key]
            })
            state.state = data.auditStatus
            state.ruleForm.authenticationModeWords = methods.getAuthenticationMode()
            state.tableData = { list: data.paramColumns }
          } else {
            state.tableData = {}
          }
        },
        // 改变参数
        changeParams() {
          addParameters.value.init()
        },
        // 请求方式
        getDataSourceType() {
          api.project.getDataSourceType().then((res) => {
            if (res) {
              let options = []
              res.data.forEach((item) => {
                options.push({
                  value: item.code,
                  label: item.name,
                })
              })
              state.org_dataSourceType = options
            }
          })
        },
        // 测试API
        testDatasource() {
          state.testLoading = true
          api.project
            .testDatasource(state.ruleForm)
            .then((res) => {
              let { success, msg } = res.data
              state.testLoading = false
              if (success)
                ElNotification({
                  title: '提示',
                  message: '测试通过!',
                  type: 'success',
                })
              else {
                ElNotification({
                  title: '提示',
                  message: '测试未通过! ---' + msg,
                  type: 'error',
                })
              }
            })
            .catch(() => {
              state.testLoading = false
            })
        },
      }
      onMounted(() => {
        state.detailId = router.currentRoute.value.query.id
        state.type = router.currentRoute.value.query.type
        switch (state.type) {
          case 'DETAIL':
            state.rules = {}
            state.disabled = true
            break
          default:
            break
        }
      })

      return {
        state,

        addParameters,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  $labelWidth: 60px;
  .components-detail-api {
    background: #eeeeee;
    color: #333;
    height: 100%;
    position: relative;
    // min-width: 1440px;
    .components-detail-api-box {
      width: 100%;
      position: relative;
      height: 100%;
      // border-radius: 4px;
      background-color: #fff;

      .content-bg-img {
        position: absolute;
        bottom: 0;
        right: 0;
        min-width: 252px;
        width: 17.5%;
      }
      .components-detail-api-box-content {
        width: 100%;
        min-width: 720px;
        margin: 0 auto;
        max-height: 100%;
        overflow-y: auto;
        padding-top: 14px;
        &.only-detail {
          padding-bottom: 60px;
        }
        .audit-box {
          width: 66.6%;
          min-width: 800px;
          margin: 0 auto;
          padding: 15px 20px 0;
          margin-bottom: 20px;
          .audit-user-box {
            display: flex;
            justify-content: space-between;
            align-content: center;
            height: 32px;
            padding-bottom: 10px;
            font-size: 12px;
            .audit-state {
              box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.5);
              border-radius: 10px 2px 10px 2px;
              padding: 0 14px;
              background: #18ba72;
              color: #fff;
              height: 20px;
              line-height: 20px;
            }
            .audit-user {
              display: flex;
              justify-content: space-between;
              div {
                padding: 0 14px;
                height: 22px;
                line-height: 22px;
                background-color: #fff;
                margin-left: 12px;
                border-radius: 11px;
                color: #666;
              }
            }
          }
          .audit-message {
            padding: 10px;
            background: #ffffff;
            border-radius: 4px;
            color: #333333;
            line-height: 20px;
            font-size: 12px;
          }
          &.publish {
            background: linear-gradient(180deg, #f3f8f7 0%, #ffffff 100%);
            border-radius: 4px;
            border: 1px solid #f3f8f7;
          }

          &.fail {
            background: linear-gradient(180deg, #f8f4f3 0%, #fdfdfd 100%);
            border-radius: 4px;
            border: 1px solid #f8f4f3;
            .audit-user-box {
              .audit-state {
                background: #f54446;
              }
            }
          }
        }
        .nancalui-form {
          // margin: 0 auto;
          margin-left: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 690px;
          // &.disable-hide-border {
          //   margin: 0 0 0 90px;
          // }
          // width: 58%;
          // transform: translateX(-$labelWidth/2);

          :deep(.nancalui-form__item--horizontal) {
            width: 100%;
            position: relative;
            margin-bottom: 20px;
            display: flex;

            &.operate-box {
              display: inline-block;
              text-align: center;
              transform: translateX($labelWidth);
            }
          }
        }
      }
    }

    .operate-box {
      position: fixed;
      width: calc(100% - 220px);
      height: 66px;
      padding: 17px 30px;
      bottom: 0;
      right: 10px;
      background-color: #fff;
      text-align: right;
      border-radius: 4px 4px 0px 0px;
    }
  }
</style>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .components-detail-api {
    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: flex-end;
    }

    .hasParams {
      width: 100%;
    }
  }
</style>
