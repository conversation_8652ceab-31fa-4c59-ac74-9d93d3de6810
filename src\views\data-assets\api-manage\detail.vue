<template>
  <div class="container overflow">
    <div class="cf-page-title">
      API详情
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>
    <div class="white-box nc-p-16" v-loading="state.loading">
      <n-form
        ref="formRef"
        :data="state.formData"
        label-width="120px"
        label-align="start"
        message-type="text"
        labelSuffix="："
      >
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="API名称" field="apiName">
              <span>{{ state.formData.apiName }}</span>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="分类" field="categoryName">
              <span>{{ state.formData.categoryName }}</span>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求方式" field="requestMethod">
              <span>{{ state.formData.requestMethod }}</span>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="数据表" field="modelTable" v-if="state.formData.definitionType === 'APPOINT_TABLE'">
              <span>{{ state.formData.modelTable }}</span>
            </n-form-item>
            <n-form-item label="数据源" field="datasourceId" v-else>
              <span>{{ state.dataSourceOps?.find((item) => item.id ===( state.formData.datasourceId|| -1))?.name }}</span>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="鉴权方式" field="authKind">
              <n-checkbox-group
                v-model="state.formData.authKind"
                direction="row"
                class="nc-flex"
                style="align-items: center"
                disabled
              >
                <n-checkbox label="TOKEN" value="1" />
                <n-checkbox label="白名单" value="2" />
              </n-checkbox-group>
            </n-form-item>
          </n-col>
          <n-col :span="12" v-if="state.formData.authKind?.includes('1')">
            <n-form-item label="token到期时间" field="expireDate">
              <span>{{ state.formData.expireDate }}</span>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="接口地址" field="url">
              <span class="nc-line-1" :title="url">{{ url }}</span>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="密级" field="confidentialityLevelName">
              <span style="white-space: nowrap">{{ state.formData.confidentialityLevelName }}</span>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item class="inline" label="请求参数" field="requestParamList">
              <div class="border-box">
                <n-public-table
                  ref="publicTable"
                  :tableData="{ list: state.formData.requestParamList }"
                  :isDisplayAction="false"
                  rowKey="attributeId"
                  :table-head-titles="leftTableTitles"
                  :showPagination="false"
                  :tableHeight="432"
                >
                  <template #dataType="{ editor }">
                    <span>{{state.fieldTypeOps.filter(val=>val.value === editor.row.dataType.toLowerCase())[0]?.label || '--'}}</span>
                  </template>
                  <template #required="{ editor }">
                    <span>{{ editor.row.required ? '是' : '否' }}</span>
                  </template>
                </n-public-table>
              </div>
            </n-form-item></n-col
          >
          <n-col :span="12">
            <n-form-item label="返回参数" field="responseParamList">
              <div class="border-box">
                <n-public-table
                  :isDisplayAction="false"
                  :editDisabled="false"
                  :tableData="{ list: state.formData.responseParamList }"
                  :table-head-titles="rightTableTitles"
                  :showPagination="false"
                  :tableHeight="432"
                />
              </div>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="请求间隔" field="callInterval">
              <span>{{ state.formData.callInterval }}秒</span>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="请求超时" field="timeout">
              <span>{{ state.formData.timeout }}秒</span>
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="40">
          <n-col :span="12">
            <n-form-item label="数据过滤条件" field="whereCondition">
              <span>{{ state.formData.whereCondition }}</span>
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
    </div>
  </div>
</template>
<script setup>
  import { useRouter } from 'vue-router'
  const router = useRouter()
  import api from '@/api/index'
  const state = reactive({
    loading: false,
    formData: {
      authKind: [],
      requestMethod: 'GET',
    },
    prefix: '',
    dataSourceOps: [],
    fieldTypeOps: [
      {label:'数字',value:'number'},
      {label:'整数',value:'long'},
      {label:'小数',value:'decimal'},
      {label:'布尔',value:'boolean'},
      {label:'字符串',value:'string'},
      {label:'日期',value:'date'},
      {label:'日期时间',value:'datetime'},
    ],
  })
  const url = computed(
    () =>
      state.prefix +
      state.formData.url +
      (state.formData.token && !state.formData.url.includes('?token=')
        ? `?token=${state.formData.token}`
        : ''),
  )
  const leftTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名' },
    { prop: 'dataType', name: '字段类型', slot: 'dataType' },
    { prop: 'required', name: '是否必填', slot: 'required' },
  ]
  const rightTableTitles = [
    { prop: 'column', name: '参数英文名' },
    { prop: 'columnName', name: '参数中文名' },
  ]
  // 返回
  function goBack() {
    router.go(-1)
  }
  // 请求详情
  async function getDetail(id) {
    try {
      state.loading = true
      const res = await api.documentManage.apiDetail({ apiId: id })
      let defaultList = [{
        fieldId:"pageNumber",
        column:"pageNumber",
        columnName:"页码",
        dataType:"long",
        disabledThisRow:true,
        required:true
      },
        {
          fieldId:"pageSize",
          column:"pageSize",
          columnName:"每页条数",
          dataType:"long",
          disabledThisRow:true,
          required:true
        }]
      res.data.requestParamList = defaultList.concat(res.data.requestParamList)

      state.formData = res.data
    } finally {
      state.loading = false
    }
  }
  // 请求路径前缀
  async function getPrefix() {
    const res = await api.documentManage.apiPrefix()
    state.prefix = res.data
  }
  // 请求分类
async function getClassifyTreeList() {
  const res = await api.documentManage.apiCategoryTree()
  state.categoryTree = res.data
}
async function getDatasource() {
  const res = await api.documentManage.getDatasource({
    datasourceType: 'ORACLE',
  })
  state.dataSourceOps = (res.data || []).map((item) => {
    return {
      ...item,
      id: !item.id ? -1 : item.id, // 数据源id为-1时，为默认数据源
    }
  })
}
  onMounted(() => {
    const { id } = router.currentRoute.value.query
    getPrefix()
    getDetail(id)
    getDatasource()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  :deep(.nancalui-checkbox__group > *) {
    margin-top: 0;
  }
  .border-box {
    padding: 10px;
    border: 1px solid #e5e6eb;
  }
</style>
