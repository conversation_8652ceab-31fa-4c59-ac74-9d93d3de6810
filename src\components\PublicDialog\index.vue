<template>
  <!-- 二次确认封装弹框 -->
  <div>
    <el-dialog
      class="dialogPopup"
      :title="dialogTitle"
      v-model="dialogPopVisible"
      :width="popupWidth"
      :before-close="onBeforeClose"
      :close-on-click-modal="false"
    >
      <div class="message-box">{{ dialogMessage }}</div>
      <template #footer>
        <span slot="footer" class="dialog-footer">
          <div class="div-button cancel" @click.prevent="Cancel">取消</div>
          <div class="div-button sure" @click.prevent="Save">确认</div>
          <!-- <n-button color="primary" size="sm" variant="solid" @click.prevent="Save">确认</n-button>
          <n-button size="sm" @click.prevent="Cancel">取消</n-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '',
    },
    message: {
      type: String,
      default: '',
    },
    option: {
      type: Object,
      default: () => {},
    },
    cancel: {
      type: Function,
      default: () => {},
    },
    save: {
      type: Function,
      default: () => {},
    },
  })
  const dialogPopVisible = ref(true) // 窗体显示控制

  const popupWidth = computed(() => {
    return props.width || '430px'
  })
  const dialogTitle = computed(() => {
    return props.title || '标题'
  })
  const dialogMessage = computed(() => {
    return props.message || '内容'
  })

  const Cancel = () => {
    dialogPopVisible.value = false
    props.cancel()
  }
  const Save = () => {
    dialogPopVisible.value = false
    props.save()
  }
  const onBeforeClose = (done) => {
    done()
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  :deep(.dialogPopup) {
    padding: 0;
    // 弹框
    z-index: 99;
    margin-top: 33vh !important;
    min-width: 430px;
    border-radius: 8px;

    .el-dialog__header {
      text-align: left;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 20px 8px 16px !important;
      margin-right: 0;
      border-bottom: 1px solid #ebebeb;
      position: relative;

      .el-dialog__title {
        border-left: none;
        background: url('/src/assets/img/warning.png') no-repeat;
        background-position: left;
        background-size: 20px 20px;
        padding-left: 24px;
        color: var(----, rgba(0, 0, 0, 0.9));
        font-family: 'Source Han Sans CN';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 150% */
        height: 24px;
      }

      .el-dialog__headerbtn {
        display: flex;
        width: 16px;
        height: 16px;
        padding: 2.2px 2.2px 2.3px 2.3px;
        justify-content: center;
        align-items: center;

        position: absolute;
        top: 8px;
        right: 20px;
        padding: 0;
        background: 0 0;
        border: none;
        outline: 0;
        cursor: pointer;
      }
    }

    .el-dialog__body {
      display: flex;
      padding: 12px 20px 12px 40px;
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      align-self: stretch;

      color: var(----, rgba(0, 0, 0, 0.75));
      font-family: 'Source Han Sans CN';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-bottom: none;
    }

    .el-dialog__footer {
      display: flex;
      padding: 16px 20px;
      justify-content: flex-end;
      align-items: center;
      align-self: stretch;
      gap: 8px;
      border: none;
      .dialog-footer {
        gap: 8px;
        display: flex;
        justify-content: flex-end;
        .div-button {
          line-height: 30px;
          color: #fff;
          cursor: pointer;
          border-radius: 2px;
          border: 1px solid var(---, #dcdfe6);
          display: flex;
          padding: 5px 16px;
          align-items: center;
          gap: 4px;

          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          &.sure {
            background-color: $themeBlue;
            &:hover {
              background-color: rgba(24, 160, 251, 0.8);
            }
          }
          &.cancel {
            margin-left: 12px;
            border: 1px solid #dcdfe6;
            color: #000;
            &:hover {
              border: 1px solid $themeBlue;
              color: $themeBlue;
            }
          }

          &.nancalui-button--default {
            color: #333;
          }
        }
      }
    }
  }
</style>
