<template>
    <div class="annotation-list">
        <div class="btn-group">
            <!--返回-->
            <n-button class="back" color="primary" variant="solid" @click="emit('back')">
                返回
            </n-button>
        </div>
        <CfTable actionWidth="120" ref="tableNoRef" :tableConfig="{
            data: state.dataSource,
            rowKey: 'id',
        }" :table-head-titles="tableHead">
            <template #editor="{ row }">
                <!-- 查看详情 -->
                <n-button class="more" color="primary" variant="text"
                    @click="emit('detail', { id: props.info.id, taskId: props.info.taskId })">
                    查看详情
                </n-button>
            </template>
        </CfTable>
    </div>
</template>
<script setup>
import api from '@/api'
import { useRouter } from 'vue-router'
const emit = defineEmits(['previewStandard'])
const router = useRouter()
const { id: projectId } = router.currentRoute.value.query // 任务id
const formRef = ref(null) // 表单ref
const props = defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
})
const state = reactive({
    formData: {
        // 表单数据
        teamInfo: {
            name: '',
            userIdLeader: null,
        },
        teamUserList: [{}],
    },
    teamDrawerVisible: false, // 团队弹窗
    dataSource: [], // 表格数据
    searchData: {
        // 分页
        condition: {
            projectId,
        },
        pageNum: 1, // 当前页
        pageSize: 1000000, // 每页显示条数
    },
    total: 0, // 总条数
    tagOptions: [], // 标签列表
    autoTagResult: [],// 自动标注结果
})
// 获取标签列表
const getTargetList = () => {
    api.dataGovernance.getTaskLabelDetail(props.info.taskId).then((res) => {
        if (res.success) {
            const { data } = res
            state.tagOptions = JSON.parse(data?.labels || '[]')
        }
    })
}
const tableHead = computed(() => {
    // 表格表头
    const list = state.tagOptions?.map((tag) => {
        console.log(tag)
        return { name: tag.text, prop: tag.text }
    })
    return list
})
// 跳转路由
const goJump = (name, query) => {
    if (query) {
        router.push({ name, query })
    } else {
        router.push({ name })
    }
}
// 获取预览数据
function getPreviewData(id) {
    api.documentManage.getDocumentAnnotationPreview(id).then((res) => {
        if (res.success) {
            try {
                const { mark, paragraph } = res.data
                state.annotationsPage = JSON.parse(mark || '{}')
                state.paragraph = JSON.parse(paragraph || '{}')
                state.dataSource = Object.values(state.annotationsPage || {})
                    .flatMap((item) => Object.values(item || {})).map((item) => {
                        item.tags?.forEach((tag) => {
                            item[tag.text] = item.selectedText
                        })
                        return item
                    })
                // state.dataSource = Object.values(state.paragraph).map((item, paragraphNum) => {
                //     item.links = (item?.links || []).map((id) => {
                //         return {
                //             ...state.annotations.find((annotation) => annotation.id === id) || {},
                //             // 段落数
                //             paragraphNum: '段落' + (paragraphNum + 1),
                //         }
                //     })
                //     return item
                // }).flatMap((item) => item.links)
            } catch (error) {
                state.annotationsPage = {}
            }
        }
    })
}
// 获取自动标注结果
function getAutoTag(taskId) {
    api.dataGovernance.autoAnnotationResult({ taskId: props.info.taskId }).then((res) => {
        if (res.success) {
            const tableHead = res?.data?.excelDataList?.shift?.()?.rowData
            state.dataSource.unshift(...(res?.data?.excelDataList || []).map((item) => {
                const rowData = item.rowData
                const data = {}
                tableHead.forEach((head, index) => {
                    data[head.value] = rowData[index].value || ''
                })
                return data
            }))
        }
    })
}
getAutoTag(props.info.taskId)

getPreviewData(props.info.id)
getTargetList()
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.annotation-list {
    background-color: #fff;
    height: 100%;

    .btn-group {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 16px;
    }
}

.more {
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.taskStatus {
    .circle {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 4px;
        background-color: $themeBlue;
        border-radius: 50%;

        &.yellow {
            background-color: #ffba70;
        }

        &.red {
            background-color: #f63838;
        }
    }
}
</style>