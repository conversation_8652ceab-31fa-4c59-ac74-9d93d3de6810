<template>
  <!-- 新增参数 -->
  <div class="add-parameters">
    <n-modal
      v-model="state.dialogVisible"
      class="commonDialog parameterDialog"
      title="添加参数"
      :draggable="false"
      :append-to-body="false"
      :close-on-click-overlay="false"
      width="1080px"
      :before-close="handleClose"
    >
      <div>
        <n-form
          ref="ruleForm"
          layout="vertical"
          :data="state.ruleForm"
          :rules="state.rules"
          class="demo-ruleForm"
          :key="state.key"
        >
          <n-form-item label="资产类型：" class="horizontal" field="assetsType">
            <n-radio-group
              direction="row"
              v-model="state.ruleForm.assetsType"
              @change="assetsTypeChangeFn"
            >
              <n-radio value="REGULAR">数据资产</n-radio>
              <!-- <n-radio value="DATA_METRIC">指标资产</n-radio> -->
            </n-radio-group>
          </n-form-item>
          <div class="between">
            <n-form-item
              :label="
                state.ruleForm.assetsType === 'REGULAR' ? '选择业务域：' : '选择指标模型分类：'
              "
              field="bizDomainId"
            >
              <n-tree-select
                v-model="state.ruleForm.bizDomainId"
                :placeholder="
                  state.ruleForm.assetsType === 'REGULAR' ? '请选择业务域' : '请选择指标模型分类'
                "
                :treeData="state.treeData"
                filter
                allowClear
                @valueChange="getAssetsLibraryModelList(false)"
              >
                <template #icon="{ nodeData, toggleNode }">
                  <span
                    @click="
                      (event) => {
                        event.stopPropagation()
                        toggleNode(nodeData)
                      }
                    "
                  >
                    <svg
                      :style="{
                        transform: nodeData.expanded ? 'rotate(90deg)' : '',
                        marginLeft: '-2.5px',
                        marginRight: '14.5px',
                        cursor: 'pointer',
                      }"
                      viewBox="0 0 1024 1024"
                      width="12"
                      height="12"
                    >
                      <path
                        d="M204.58705 951.162088 204.58705 72.836889 819.41295 511.998977Z"
                        fill="#8a8e99"
                      />
                    </svg>
                  </span>
                </template>
                <template #default="{ item }">
                  {{ item.label }}
                </template>
              </n-tree-select>
            </n-form-item>
            <n-form-item
              v-if="state.ruleForm.bizDomainId"
              :label="state.ruleForm.assetsType === 'REGULAR' ? '选择数据模型：' : '选择指标模型：'"
              field="modelId"
            >
              <n-select
                v-model="state.ruleForm.modelId"
                :placeholder="
                  state.ruleForm.assetsType === 'REGULAR' ? '请选择数据模型' : '请选择指标模型'
                "
                allow-clear
                filter
                @value-change="dataModeChange"
              >
                <n-option
                  v-for="item in state.modelOptions"
                  :key="item.value"
                  :name="item.label"
                  :value="item.value"
                />
              </n-select>
            </n-form-item>
          </div>
        </n-form>
        <div v-loading="state.loading">
          <n-public-table
            ref="addParametersTable"
            :isDisplayAction="false"
            :isNeedSelection="true"
            :table-head-titles="state.tableHeadTitles"
            :tableHeight="state.tableHeight"
            :tableData="state.tableData"
            :configData="state.configData"
            :editDisabled="false"
            @tablePageChange="tablePageChange"
            @handle-selection-change="handleSelectionChange"
          >
            <template #pageTop>
              <div class="box-add">
                <div class="title">选择参数</div>
                <div class="commonForm">
                  <n-form
                    :inline="true"
                    :data="state.filterSearch"
                    class="top-right demo-form-inline commonForm"
                  >
                    <n-form-item label="">
                      <n-input
                        v-model="state.filterSearch.name"
                        placeholder="请输入字段名称"
                        clearable
                        @clear="initTable"
                      >
                        <template #append>
                          <n-button @click.prevent="initTable(true)">
                            <n-popover
                              class="item"
                              content="搜索"
                              trigger="hover"
                              :position="['bottom']"
                            >
                              <SvgIcon class="icon_search" icon="icon_search" />
                            </n-popover>
                          </n-button>
                        </template>
                      </n-input>
                    </n-form-item> </n-form
                ></div>
              </div>
            </template>
          </n-public-table>
        </div>
      </div>

      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button color="primary" size="sm" variant="solid" @click.prevent="save">保 存</n-button>
          <n-button size="sm" @click.prevent="state.dialogVisible = false"
            >取 消</n-button
          ></n-modal-footer
        >
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { sceneManage } from '@/api'

  import { ElNotification } from 'element-plus'
  export default {
    title: 'List',
    components: {},
    props: {},
    emits: ['getSelectData'],
    setup(props, { emit }) {
      const addParametersTable = ref()
      const state = reactive({
        key: 1,
        tableHeight: 350,
        tableData: {},
        configData: {},
        loading: false,
        ruleForm: {
          assetsType: 'REGULAR',
          bizDomainId: '',
          modelId: null,
        },
        businessOptions: [],
        modelOptions: [],
        dialogVisible: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号' },
          { prop: 'cnName', name: '字段-中文名' },
          { prop: 'name', name: '字段-英文名' },
          { prop: 'fieldTypeName', name: '类型' },
          { prop: 'fieldLength', name: '长度' },
          { prop: 'description', name: '描述' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        startTime: '',
        endTime: '',
        filterSearch: {
          name: null,
        },
        selectTableData: [],
        rules: {
          assetsType: [{ required: true, message: '选择业资产类型', trigger: 'change' }],
          bizDomainId: [{ required: true, message: '选择业务域', trigger: 'change' }],
          modelId: [{ required: true, type: 'number', message: '选择数据模型', trigger: 'change' }],
        },
        treeData: [],
      })
      const methods = {
        //  数据模型change
        dataModeChange(item) {
          state.ruleForm.modelId = item.value
          // 清空选中
          addParametersTable.value?.clearSelection()
          state.key++
          nextTick(() => {
            methods.initTable(true)
          })
        },

        // 初始化
        init(data) {
          state.configData = data
          state.dialogVisible = true
          if (state.configData.assetsType) {
            state.ruleForm.assetsType = state.configData?.assetsType || 'REGULAR'
          }
          if (state.configData.modelId) {
            state.ruleForm.bizDomainId = state.configData?.bizDomainId || null

            methods.getAssetsLibraryModelList(true)
            state.ruleForm.modelId = state.configData.modelId
            nextTick(() => {
              methods.initTable(true)
            })
          } else {
            state.tableData = {}
          }
        },
        // 初始化form
        initTable(init = false) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              id: state.ruleForm.modelId || null,
              name: state.filterSearch.name || null,
            },
          }
          state.loading = true
          api.model
            .getModelDataList(data)
            .then((res) => {
              state.loading = false
              // 新增序号属性
              res.data.list.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.tableData = res.data
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
        },
        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },
        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
        },
        // 保存
        save() {
          if (!state.selectTableData.length) {
            let activeRows = addParametersTable.value?.getCheckedRows()
            state.selectTableData = activeRows
          }
          if (state.selectTableData.length) {
            emit('getSelectData', {
              modelId: state.ruleForm.modelId,
              bizDomainId: state.ruleForm.bizDomainId,
              assetsType: state.ruleForm.assetsType,
              data: state.selectTableData,
            })
            state.dialogVisible = false
          } else {
            ElNotification({
              title: '提示',
              message: '请勾选数据',
              type: 'warning',
            })
          }
        },
        // 资产类型切换
        assetsTypeChangeFn() {
          state.ruleForm.bizDomainId = ''
          methods.initData()
        },
        // 获取业务域树
        getBizDomainOptions() {
          sceneManage.searchTreeList().then((res) => {
            let { success, data } = res
            if (success) {
              let treeData = []
              if (data !== null) {
                let haveRoot = data.some((item) => {
                  if (item.name === '全部') {
                    return true
                  }
                })
                if (haveRoot) {
                  treeData = methods.setTreeRecursion(data)
                } else {
                  treeData = [
                    {
                      description: '全部',
                      id: 0,
                      name: '全部',
                      label: '全部',
                      children: methods.setTreeRecursion(data),
                    },
                  ]
                }
              }
              state.treeData = treeData[0].children
            }
          })
        },
        setTreeRecursion(data) {
          data.map((item) => {
            item.value = item.id
            item.label = item.name
            if (item.children && item.children.length != 0) {
              methods.setTreeRecursion(item.children)
            }
          })
          return data
        },
        //重置数据
        initData() {
          state.ruleForm.modelId = ''
          state.modelOptions = []
          state.tableData = {}
          // 清空选中

          addParametersTable.value?.clearSelection()
        },

        //获取业务域下模型
        getAssetsLibraryModelList(init = false) {
          if (!init) {
            methods.initData()
          }

          if (!state.ruleForm.bizDomainId) return
          api.assets
            .getModelByBiz({
              bizId: state.ruleForm.bizDomainId,
              assetsType: state.ruleForm.assetsType,
            })
            .then((res) => {
              if (res.data) {
                let { data } = res
                let _modelOptions = []
                data.forEach((item) => {
                  _modelOptions.push({
                    value: item.modelId,
                    label: item.modelName + '(' + item.modelCnName + ')',
                  })
                })
                state.modelOptions = _modelOptions
              }
            })
        },
      }
      onMounted(() => {
        methods.getBizDomainOptions()
      })
      return {
        state,
        addParametersTable,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .add-parameters {
    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      .title {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bolder;
        color: rgba(0, 0, 0, 0.85);
      }
      .demo-form-inline {
        display: flex;
        justify-content: flex-end;
      }
    }

    .demo-ruleForm {
      .between {
        display: flex;
        justify-content: space-between;
      }
      :deep(.nancalui-form__item--vertical) {
        width: calc(50% - 10px);
        flex: none;
        margin-bottom: 0;
        &.horizontal {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: inherit;
          .nancalui-form__label--vertical {
            padding-bottom: 0;
          }
          .nancalui-form__control {
            flex: none;
            width: calc(100% - 80px);
            margin-bottom: -6px;
          }
        }
      }
    }
  }
</style>
