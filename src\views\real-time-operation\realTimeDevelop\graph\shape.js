import { Graph, Node } from '@antv/x6'
import '@antv/x6-vue-shape'
import Box from './shape/box'

Graph.unregisterNode('dataAsync')

// 生成自义定节点
function ChartAnimateText(params) {
  return Graph.registerNode(params.name, {
    inherit: 'vue-shape',
    width: 256,
    height: 44,
    component: {
      template: `
              <Box/>`,
      components: {
        Box,
      },
    },
    data: {
      nodeName: params.name,
      name: params.title,
      icon: params.img ? new URL(`/src/assets/${params.img}`, import.meta.url).href : '',
      taskType: params.taskType,
      showMenu: false,
    },
    ports: {
      groups: {
        top: {
          id: 'top',
          position: 'top',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#8091B7',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
        bottom: {
          id: 'bottom',
          position: 'bottom',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#8091B7',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'hidden',
              },
            },
          },
        },
      },
      items: [
        {
          id: 'top',
          group: 'top',
        },
        {
          id: 'bottom',
          group: 'bottom',
        },
      ],
    },
    // tools: {
    //   name: 'button-remove',
    //   args: { distance: -40 },
    // },
  })
}
// 数据同步节点
export const DataAsyncNode = ChartAnimateText({
  name: 'dataAsync',
  title: '同步至资源库',
  img: 'img/offlineDev/同步至资源库.png',
  taskType: 'dataAsync',
})
// PySpark
export const PySparkNode = ChartAnimateText({
  name: 'PySpark',
  title: 'PySpark',
  img: 'img/offlineDev/PySpark.png',
  taskType: 'DB_INPUT',
})
// HiveSQL
export const HiveSQLNode = ChartAnimateText({
  name: 'HiveSQL',
  title: 'HiveSQL',
  img: 'img/offlineDev/HiveSQL.png',
  taskType: 'DB_INPUT',
})
// SparkSQL
export const SparkSQLNode = ChartAnimateText({
  name: 'SparkSQL',
  title: 'SparkSQL',
  img: 'img/offlineDev/SparkSQL.png',
  taskType: 'DB_INPUT',
})
// Python
export const PythonNode = ChartAnimateText({
  name: 'Python',
  title: 'Python',
  img: 'img/offlineDev/Python.png',
  taskType: 'DB_INPUT',
})
// Shell
export const ShellNode = ChartAnimateText({
  name: 'Shell',
  title: 'Shell',
  img: 'img/offlineDev/Shell.png',
  taskType: 'DB_INPUT',
})
// HiveDDL
export const HiveDDLNode = ChartAnimateText({
  name: 'HiveDDL',
  title: 'HiveDDL',
  img: 'img/offlineDev/HiveDDL.png',
  taskType: 'DB_INPUT',
})
