<template>
  <div class="header">
    <n-button variant="solid" @click="addFn">新增</n-button>
  </div>
  <div v-loading="state.loading" class="table-box">
    <CfTable
      :key="state.key"
      :isDisplayAction="false"
      :table-head-titles="state.tableHeadTitles"
      :paginationConfig="{
        total: state.pageInfo.total,
        pageSize: state.pageInfo.pageSize,
        currentPage: state.pageInfo.currentPage,
        onCurrentChange: (v) => {
          state.pageInfo.currentPage = v
          initTable()
        },
        onSizeChange: (v) => {
          state.pageInfo.pageSize = v
          initTable(true)
        },
      }"
      :tableConfig="{
        data: state.tableData.list,
        rowKey: 'id',
      }"
    >
      <template #editor="{ row }">
        <n-button color="primary" variant="text" @click="editFn(row)"> 编辑 </n-button>
        <n-button color="primary" variant="text" @click="deleteFn(row)"> 删除 </n-button>
      </template>
    </CfTable>
  </div>

  <n-drawer
    v-model="state.showDrawer"
    title=""
    :size="560"
    :close-on-click-overlay="true"
    :before-close="closeDrawer"
    class="template-config-drawer"
  >
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">计划进展配置</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="state.showDrawer = false" />
      </div>
      <div class="n-drawer-body-content">
        <n-form
          ref="ruleForm"
          :data="state.ruleForm"
          :rules="state.rules"
          label-width="150px"
          label-align="start"
        >
          <n-form-item label="计划名称：" field="name">
            <n-input v-model="state.ruleForm.name" placeholder="请输入计划名称" maxlength="500" />
          </n-form-item>

          <n-form-item label="问题名称：" field="problemName">
            <n-input
              v-model="state.ruleForm.problemName"
              placeholder="请输入问题名称"
              maxlength="500"
            />
          </n-form-item>

          <n-form-item label="完成情况：" field="content">
            <n-input
              v-model="state.ruleForm.content"
              placeholder="请输入完成情况"
              maxlength="500"
            />
          </n-form-item>
          <n-form-item label="计划节点：" field="timeNode">
            <n-input
              v-model="state.ruleForm.timeNode"
              placeholder="请输入计划节点"
              maxlength="500"
            />
          </n-form-item>
          <n-form-item label="责任单位：" field="responsibleDepartment">
            <n-input
              v-model="state.ruleForm.responsibleDepartment"
              placeholder="请输入责任单位"
              maxlength="500"
            />
          </n-form-item>
          <n-form-item label="责任人：" field="responsiblePerson">
            <n-input
              v-model="state.ruleForm.responsiblePerson"
              placeholder="请输入责任人"
              maxlength="500"
            />
          </n-form-item>
        </n-form>
      </div>
      <div class="n-drawer-body-footer">
        <n-button @click.prevent="state.showDrawer = false">取 消</n-button>
        <n-button variant="solid" @click="submitFn">保 存</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
  import { reactive, onMounted, defineProps } from 'vue'
  import {
    workbenchPlanList,
    workbenchPlanCreate,
    workbenchPlanEdit,
    workbenchPlanDelete,
  } from '@/api/dataManage.js'

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })

  const state = reactive({
    loading: false,
    pageStatus: 'add',
    tableData: { list: [{ name: '车间' }] },
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'name', name: '计划名称' },
      { prop: 'problemName', name: '问题名称' },
      { prop: 'content', name: '完成情况', width: 'auto' },
      { prop: 'timeNode', name: '计划节点' },
      { prop: 'responsibleDepartment', name: '责任单位' },
      { prop: 'responsiblePerson', name: '责任人' },

      // { prop: 'progress', name: '当前进展' },
      // { prop: 'problemType', name: '问题类型' },
      // { prop: 'problemReason', name: '问题原因' },
      // { prop: 'solution', name: '解决举措' },
    ],
    showDrawer: false,
    ruleForm: {
      name: '',
      problemName: '',
      content: '',
      timeNode: '',
      responsibleDepartment: '',
      responsiblePerson: '',
    },
    rules: {
      name: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
      problemName: [{ required: true, message: '请输入问题名称', trigger: 'blur' }],
      content: [{ required: true, message: '请输入完成情况', trigger: 'blur' }],
      timeNode: [{ required: true, message: '请输入计划节点', trigger: 'blur' }],
      responsibleDepartment: [{ required: true, message: '请输入责任单位', trigger: 'blur' }],
      responsiblePerson: [{ required: true, message: '请输入责任人', trigger: 'blur' }],
    },
  })

  const initTable = (init = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: props.info.code,
      },
    }
    state.loading = true
    workbenchPlanList(data)
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data
          state.pageInfo.total = res.data.total
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  const addFn = () => {
    state.ruleForm = {}
    state.pageStatus = 'add'
    state.showDrawer = true
  }

  const editFn = (row) => {
    state.pageStatus = 'edit'
    state.showDrawer = true
    state.ruleForm = row
  }

  const deleteFn = (row) => {
    workbenchPlanDelete({ id: row.id }).then((res) => {
      if (res.success) {
        initTable()
        ElMessage.success('删除成功')
      }
    })
  }

  // 计划进展配置提交
  const submitFn = () => {
    const url = state.pageStatus === 'add' ? workbenchPlanCreate : workbenchPlanEdit
    url({ ...state.ruleForm, projectCode: props.info.code }).then((res) => {
      if (res.success) {
        state.showDrawer = false
        initTable()
        ElMessage.success('计划进展配置成功')
      }
    })
  }

  onMounted(() => {
    initTable(true)
  })
</script>

<style lang="scss" scoped>
  .header {
    margin-bottom: 10px;
  }
  .table-box {
    position: relative;
    width: 100%;
    height: calc(100% - 42px);
  }

  :deep(.common-table .page-mid .el-table tbody tr td > .cell > span) {
    text-overflow: inherit;
    -webkit-line-clamp: inherit;
  }
</style>
