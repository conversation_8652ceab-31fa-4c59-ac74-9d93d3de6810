<template>
  <div class="detail">
    <div class="page-title"
      >{{ state.detailInfo.tableName }}
      <div class="detail-back-box" @click.prevent="closeFn">返回</div>
    </div>
    <div class="content">
      <div class="content-radio">
        <n-radio-group direction="row" v-model="state.contentType" size="sm" @change="radioFn">
          <n-radio-button value="STRUCTURE">表结构</n-radio-button>
          <n-radio-button value="MAP">E-R图</n-radio-button>
          <n-radio-button value="LOGICAL_ENTITY_PROPERTY">逻辑实体属性</n-radio-button>
          <n-radio-button value="BUSINESS_RELATIONSHIP">业务关系</n-radio-button>
          <n-radio-button value="BUSINESS_PROCESS">业务流程</n-radio-button>
        </n-radio-group>
      </div>
      <div class="content-box">
        <div class="content-box-structure">
          <div class="label">
            <div class="name">数据源名称：</div>
            <div class="value">{{ state.detailInfo.sourceName }}</div>
          </div>
          <div class="label">
            <div class="name">表名称：</div>
            <div class="value">{{ state.detailInfo.tableName }}</div>
          </div>
          <div class="label">
            <div class="name">表描述：</div>
            <div class="value">{{ state.detailInfo.tableComment || '--' }}</div>
          </div>
        </div>
        <n-public-table
          :isDisplayAction="false"
          :isNeedSelection="false"
          :table-head-titles="state.tableHeadTitles"
          :showPagination="false"
          :tableHeight="state.tableHeight"
          :tableData="state.tableData"
          v-if="state.contentType === 'STRUCTURE'"
        />

        <erMap v-else-if="state.contentType === 'MAP'" :tab="state.detailInfo.tableName" />

        <template v-else-if="state.contentType === 'LOGICAL_ENTITY_PROPERTY'">
          <CfTable
            :table-head-titles="state.entityPropertyHeadTitles"
            :tableConfig="{
              data: state.entityPropertyData.list,
              rowKey: 'id',
            }"
      
          ></CfTable>
        </template>

        <template v-else-if="state.contentType === 'BUSINESS_RELATIONSHIP'">
          <CfTable
            :table-head-titles="state.businessRelationshipHeadTitles"
            :tableConfig="{
              data: state.businessRelationshipData.list,
              rowKey: 'id',
            }"
      
          ></CfTable>
        </template>

        <template v-else-if="state.contentType === 'BUSINESS_PROCESS'">
          <n-radio-group
            direction="row"
            v-model="state.flowUrl"
            style="margin: 0 0 10px 10px"
            size="sm"
          >
            <n-radio-button v-for="(item, index) in state.flowUrlData" :key="item" :value="item">
              流图{{ index + 1 }}
            </n-radio-button>
          </n-radio-group>

          <template v-for="item in state.flowUrlData" :key="item">
            <iframe v-if="state.flowUrl === item" :src="item" class="flow-url-iframe"></iframe>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref, reactive } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'
  import {
    collectTableDetail,
    businessObjectSearch,
    businessDataFlowGraph,
    entityProperty,
  } from '@/api/dataManage'
  import { ElNotification } from 'element-plus'
  import erMap from '@/views/data-management/resource-directory/model/index.vue'
  const router = useRouter()
  const state = reactive({
    contentType: 'STRUCTURE',
    tableHeadTitles: [
      { prop: 'colName', name: '字段名称' },
      { prop: 'dataType', name: '字段类型' },
      { prop: 'comment', name: '描述' },
    ],
    entityPropertyHeadTitles: [
      { prop: 'name', name: '属性名' },
      { prop: 'col', name: '实际列名' },
      { prop: 'substantiveKey', name: '实质键' },
      { prop: 'notNull', name: 'not null' },
      { prop: 'type', name: '类型' },
      { prop: 'dataType', name: '数据类型' },
      { prop: 'length', name: '长度' },
      { prop: 'scale', name: '小数点' },
      { prop: 'fk', name: 'FK' },
      { prop: 'uk', name: 'UK' },
      { prop: 'remarks', name: '备注' },
    ],
    businessRelationshipHeadTitles: [
      { prop: 'objectName', name: '业务对象名称' },
      { prop: 'description', name: '业务对象描述' },
      { prop: 'uniqueInfo', name: '唯一标识信息' },
      { prop: 'processDoc', name: '流程文件' },
      { prop: 'dataSet', name: '数据集' },
      { prop: 'lifeCycle', name: '生命周期状态' },
      { prop: 'business', name: '关联业务场景' },
      { prop: 'structureDegree', name: '结构化程度' },
      { prop: 'dataPeriod', name: '数据保留期' },
      { prop: 'importantDegree', name: '重要程度' },
      { prop: 'updateFrequency', name: '数据更新频率' },
      { prop: 'secretLevel', name: '密级' },
      { prop: 'archive', name: '是否归档' },
      { prop: 'archiveWay', name: '归档类型' },
      { prop: 'archiveStage', name: '归档阶段' },
      { prop: 'archiveType', name: '归档方式' },
    ],
    id: null,
    flowUrl: '',
    tableHeight: 168,
    detailInfo: { sourceName: '', tableName: '', tableComment: '' },
    tableData: {
      list: [],
    },
    entityPropertyData: {
      list: [],
    },
    businessRelationshipData: {
      list: [],
    },
    flowUrlData: [],
  })

  const radioFn = (e) => {
    if (e === 'LOGICAL_ENTITY_PROPERTY') {
      getEntityPropertyFn()
    } else if (e === 'BUSINESS_RELATIONSHIP') {
      getBusinessObjectSearchFn()
    } else if (e === 'BUSINESS_PROCESS') {
      getBusinessDataFlowGraphFn()
    }
  }

  const closeFn = () => {
    router.push({
      name: 'resourceCatalogList',
      query: {},
    })
  }

  const getDetailFn = () => {
    collectTableDetail({ id: state.id }).then((res) => {
      if (res.success) {
        state.detailInfo = res.data
        state.tableData.list = res.data.tableColumnMeta
      }
    })
  }

  // 获取业务对象搜索
  const getBusinessObjectSearchFn = () => {
    // state.detailInfo.tableName = 'table1'
    businessObjectSearch({ tabName: state.detailInfo.tableName }).then((res) => {
      if (res.success) {
        state.businessRelationshipData.list = res.data
      }
    })
  }

  // 获取业务数据流图
  const getBusinessDataFlowGraphFn = () => {
    // state.detailInfo.tableName = 'table1'
    businessDataFlowGraph({ tabName: state.detailInfo.tableName }).then((res) => {
      if (res.success) {
        state.flowUrlData = res.data
        state.flowUrl = res.data[0]
      }
    })
  }

  // 获取实体属性
  const getEntityPropertyFn = () => {
    // state.detailInfo.tableName = 'table1'
    entityProperty({ tabName: state.detailInfo.tableName }).then((res) => {
      if (res.success) {
        state.entityPropertyData.list = res.data
      }
    })
  }

  onMounted(() => {
    state.id = router.currentRoute.value.query.id || null
    state.tableHeight = document.body.offsetHeight - 290
    getDetailFn()
  })
</script>

<style lang="scss" scoped>
  .detail {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    padding: 16px;
    .page-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 52px;
      margin-bottom: 16px;
      padding: 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;
      background-color: #fff;
      border-radius: 2px;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        height: 18px;
        margin: auto;
        background: #1e89ff;
        content: '';
      }

      .detail-back-box {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        z-index: 9;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 30px;
        margin: auto;
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          color: #479dff;
          border: 1px solid #479dff;
        }
      }
    }
    .content {
      height: calc(100% - 68px);
      background-color: #fff;
      border-radius: 2px;
      &-radio {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 46px;
        padding: 0 8px;
      }
      &-box {
        height: calc(100% - 46px);
        &-structure {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 48px;
          padding: 0 24px;
          .label {
            display: flex;
            flex: 1;
            flex-shrink: 0;
            align-items: center;
            justify-content: flex-start;
            .name {
              color: #606266;
              font-size: 14px;
            }
            .value {
              width: calc(100% - 86px);
              overflow: hidden;
              color: #1a1a1a;
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  .flow-url-iframe {
    width: 100%;
    height: calc(100% - 88px);
    border: none;
  }

  .common-table{
    height:calc(100% - 48px);
  }
</style>
