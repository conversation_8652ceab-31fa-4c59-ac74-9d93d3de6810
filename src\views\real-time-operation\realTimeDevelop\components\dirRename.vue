<template>
  <n-modal
    v-model="showDirDialog"
    title="重命名"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item
          :label="
            { WORKFLOW: '业务流程', DIRECTORY: '目录名称', JOB: '目录名称' }[state.currentNode.type]
          "
          field="name"
        >
          <n-input v-model="state.syncForm.name" maxlength="30" placeholder="请输入名称" />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" @click.prevent="createDir">确 定</n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import api from '@/api/index'
  import { checkCName } from '@/utils/validate'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const state = reactive({
    currentNode: null,
    syncForm: { name: null },
    syncRules: {
      name: [
        {
          required: true,
          message: '请输入目录名称',
          trigger: 'blur',
        },
      ],
    },
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  const createDir = () => {
    syncForm.value.validate((val) => {
      if (val) {
        const { type, id } = state.currentNode || {}
        // if (!id) return ElMessage.error('请选择目录')
        api.realTimeDevelop
          .renameRealTimeDevelopTree({
            name: state.syncForm.name,
            id: id || 0,
          })
          .then(({ success }) => {
            if (!success) return
            emit('success')
            showDirDialog.value = false
            ElMessage.success('重命名成功')
          })
      }
    })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.currentNode = null
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(node) {
      resetForm()
      state.currentNode = node
      state.syncForm.name = node.name
      showDirDialog.value = true
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 24px 20px;
  }
</style>
