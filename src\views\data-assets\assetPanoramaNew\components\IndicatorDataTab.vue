<template>
  <!-- 列表	 -->
  <div class="table-container">
    <div v-loading="loading" class="list">
      <el-table :data="dataList" style="width: 100%">
        <!-- 序号 -->
        <el-table-column prop="序号" label="序号" type="index" width="80" />
        <el-table-column prop="name" label="指标卡片名称" />
        <el-table-column prop="date" label="更新时间" />
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button size="small" link type="primary" @click="handleEdit(scope.row)"
              >配置</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" :title="title" width="560" :before-close="handleClose">
    <template v-if="currentActivity?.tables" #default>
      <el-form ref="ruleFormRef" :model="currentActivity" label-width="auto">
        <div v-for="(item, i) in currentActivity?.tables" :key="item.resourceType">
          <div v-if="currentActivity?.tables?.length > 1" class="frame pseudo-icon">
            <div class="text-wrapper">{{ item.resourceType }}</div>
          </div>
          <div class="form-content">
            <el-form-item
              v-for="(item, index) in currentActivity?.tables[i]?.tableInfos"
              :key="index"
              :label="(currentActivity?.tables?.length > 1 ? '部门' : 'TOP') + (index + 1) + '：'"
              :rules="{
                required: true,
                trigger: 'blur',
                message: '请输入',
                ...(currentActivity?.tables?.length > 1
                  ? {
                      validator: (rule, value, cb) => {
                        const { departName, name } = currentActivity?.tables[i]?.tableInfos?.[index]
                        if (!departName || !name) {
                          return false
                        } else {
                          return true
                        }
                      },
                    }
                  : {}),
              }"
              :prop="`tables[${i}].tableInfos[${index}].value`"
            >
              <div
                class="row"
                :style="{
                  'grid-template-columns':
                    currentActivity?.tables?.length === 1 ? '1fr' : '1fr 1fr 100px',
                }"
              >
                <el-input
                  v-if="currentActivity?.tables?.length > 1"
                  v-model="item.departName"
                  placeholder="请输入部门名称"
                  style="width: 100%"
                />
                <el-input v-model="item.value" style="width: 100%" placeholder="请输入数据日增量" />
                <el-select
                  v-if="item?.units"
                  v-model="item.unit"
                  :value="item.units?.[0]"
                  placeholder=" "
                  style="width: 100px"
                  :implement="(item.unit ??= item.units?.[0])"
                  @change="
                    (value) =>
                      currentActivity?.tables[i]?.tableInfos.forEach((item) => (item.unit = value))
                  "
                >
                  <el-option
                    v-for="value in item?.units"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </template>
    <template v-else-if="currentActivity?.codes?.length < 7" #default>
      <div class="form-content">
        <el-form ref="ruleFormRef" :model="currentActivity" label-width="auto">
          <el-form-item
            v-for="(item, index) in currentActivity?.codes"
            :key="index"
            :label="(item.name || '')?.split('-').pop() + '：'"
            :rules="{
              required: true,
              message: '请输入',
              trigger: 'blur',
            }"
            :prop="`codes[${index}].assetsValue`"
          >
            <div class="row">
              <el-input v-model="item.assetsValue" style="width: 100%" type="number" />
              <el-select
                v-if="item?.units"
                v-model="item.unit"
                :value="item.units?.[0]"
                placeholder=" "
                style="width: 100px"
                :implement="(item.unit ??= item.units?.[0])"
              >
                <el-option
                  v-for="value in item?.units"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template v-else #default>
      <el-form ref="ruleFormRef" :model="currentActivity" label-width="auto">
        <div v-for="title in titles" :key="title">
          <div :key="title" class="frame pseudo-icon">
            <div class="text-wrapper">{{ title }}</div>
          </div>
          <div class="form-content">
            <el-form-item
              v-for="(item, index) in currentActivity?.codes?.filter((_) => _.name.includes(title))"
              :key="index"
              :label="(item.name || '')?.split('-').pop() + '：'"
              :rules="{
                required: true,
                message: '请输入',
                trigger: 'blur',
              }"
              :prop="`codes[${index}].assetsValue`"
            >
              <div class="row">
                <el-input v-model="item.assetsValue" style="width: 100%" type="number" />
                <el-select
                  v-if="item?.units"
                  v-model="item.unit"
                  placeholder=" "
                  style="width: 100px"
                  :implement="(item.unit ??= item.units?.[0])"
                >
                  <el-option
                    v-for="value in item?.units"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="save(ruleFormRef)"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getConfList, saveConfTable } from '@/api/dataMap.js'
  const dialogVisible = ref(false)
  const ruleFormRef = ref(null)
  const title = ref('数据源情况')
  const titles = computed(() => {
    return Array.from(new Set(currentActivity.value?.codes?.map?.((_) => _.name?.split('-')[1])))
  })
  const dataList = ref([])
  const getList = () => {
    getConfList().then((res) => {
      dataList.value = res.data
    })
  }
  getList()
  const currentActivity = ref(null)
  const handleEdit = (data) => {
    title.value = data.name
    currentActivity.value = data
    dialogVisible.value = true
  }
  const handleClose = () => {
    dialogVisible.value = false
  }

  // 保存
  const save = (formEl) => {
    if (!formEl) return
    formEl.validate((valid, fields) => {
      if (valid) {
        const isTableSave = Boolean(currentActivity.value.tables)
        const primary = currentActivity.value?.[isTableSave ? 'tables' : 'codes'].map((_) => {
          const { code, assetsValue, unit } = _
          return isTableSave
            ? _
            : {
                assetsType: code,
                assetsValue: assetsValue,
                unit: unit,
              }
        })

        saveConfTable(primary, isTableSave)
          .then(({ success }) => {
            success && toast.success('保存成功')
            getList()
          })
          .catch(() => {
            toast.error('保存失败')
          })
        dialogVisible.value = false
      } else {
        console.log('error submit!', fields)
      }
    })
  }
</script>
<style lang="less" scoped>
  .table-container {
    position: relative;
    display: flex;
    flex: 1;
    flex: 1 0 0;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
    align-self: stretch;
    width: 100%;
    height: 100%;
    padding-top: 8px;
    background: #fff;
    border-radius: 2px;
    .add {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
      align-self: stretch;
      padding: 8px;
    }
    .list {
      flex: 1;
      width: 100%;
      overflow-y: auto;
    }
    .table-container-pagination {
      display: flex;
      align-items: flex-start;
      align-self: stretch;
      justify-content: flex-end;
      padding: 14px 16px;
      border-top: 1px solid #dcdfe6;
      border-radius: 0px 0px 2px 2px;
    }
  }
  .form-content {
    .row {
      display: grid;
      grid-template-columns: 1fr minmax(100px, auto);
      gap: 4px;
      align-items: center;
      width: 100%;
    }
    :deep(.el-form-item) {
    }
  }

  .frame {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px 0px;
    background: #f2f6fc;
    .rectangle {
      position: relative;
      width: 4px;
      height: 18px;
    }
    .text-wrapper {
      position: relative;
      padding-left: 10px;
      color: #2b71c2;
    }
  }
</style>
