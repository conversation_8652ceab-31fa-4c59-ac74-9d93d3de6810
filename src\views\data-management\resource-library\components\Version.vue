<template>
  <div class="version">
    <div class="version-btn"
      ><n-button
        variant="solid"
        color="primary"
        :disabled="props.info.fileFrom !== 'SFTP_COLLECT' && props.info.fileFrom !== 'FTP_COLLECT'"
        @click.prevent.stop="addFn"
        ><SvgIcon class="icon" icon="icon-card-rerun" />创建新版本</n-button
      ></div
    >
    <div class="version-content">
      <n-public-table
        :isDisplayAction="true"
        :table-head-titles="state.tableHeadTitles"
        :pagination="state.pagination"
        :tableHeight="state.tableHeight"
        :tableData="state.tableData"
        :actionWidth="80"
        @tablePageChange="tablePageChange"
      >
        <template #status="{ editor }">
          <div v-if="editor.row.status === 'FAILED'" class="status"
            ><div class="circle red"></div>失败</div
          >
          <div v-else-if="editor.row.status === 'CREATING'" class="status"
            ><div class="circle yellow"></div>处理中</div
          >
          <div v-else class="status"><div class="circle"></div>成功</div>
        </template>
        <template #editor="{ editor }">
          <div class="edit-box">
            <n-button class="has-right-border" variant="text" @click.prevent="seeFn(editor.row)"
              ><n-popover
                content="查看"
                trigger="hover"
                :position="['top']"
                style="background-color: #000; color: #fff; opacity: 0.75"
              >
                <SvgIcon class="icon" icon="icon-new-see" /> </n-popover
            ></n-button>
            <n-button
              v-if="editor.row.versionTag !== 'main'"
              class="has-right-border"
              variant="text"
              @click.prevent="delFn(editor.row)"
              ><n-popover
                content="删除"
                trigger="hover"
                :position="['top']"
                style="background-color: #000; color: #fff; opacity: 0.75"
              >
                <SvgIcon class="icon" icon="icon-new-delete" /> </n-popover
            ></n-button>
          </div>
        </template>
      </n-public-table>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { ElNotification } from 'element-plus'
  const store = useStore()
  const router = useRouter()
  const emits = defineEmits(['seeFile'])
  import api from '@/api/index'
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  })
  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS, //乐造环境
    tableHeadTitles: [
      { prop: 'versionTagName', name: '版本号', width: 300 },
      { prop: 'createByName', name: '创建人' },
      { prop: 'createTime', name: '创建时间' },
      { prop: 'versionStatus', name: '生成版本', slot: 'status' },
    ],
    tableData: { list: [] },
    tableHeight: 0,
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
    },
  })
  // 切换分页
  const tablePageChange = (data) => {
    state.pagination.currentPage = data.currentPage
    state.pagination.pageSize = data.pageSize
    onSearch()
  }
  // 删除
  const delFn = (item) => {
    proxy.$MessageBoxService.open({
      title: '是否确认删除',
      content: '删除后，该版本将不可恢复！',
      save: () => {
        api.dataManagement.deleteVersion({ jobFileId: item.id }).then((res) => {
          if (res.success) {
            ElNotification({
              title: '提示',
              message: '删除成功',
              type: 'success',
            })
            state.pagination.currentPage = 1
            onSearch()
          }
        })
      },
    })
  }
  // 新增
  const addFn = () => {
    api.dataManagement.addVersion({ jobFileId: props.info.id }).then((res) => {
      if (res.success) {
        ElNotification({
          title: '提示',
          message: '新建成功',
          type: 'success',
        })
        state.pagination.currentPage = 1
        onSearch()
      }
    })
  }
  // 查看
  const seeFn = (item) => {
    emits('seeFile', item.id)
  }
  // 获取数据
  const onSearch = () => {
    let data = {
      condition: {
        jobFileId: props.info.id,
      },
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
    }
    api.dataManagement.versionList(data).then((res) => {
      if (res.success) {
        res.data.list.forEach((val) => {
          val.versionTagName = val.versionTag + (val.versionRemark ? '_' + val.versionRemark : '')
        })
        state.tableData = res.data
        state.pagination.total = res.data.total
      }
    })
  }
  // 设置表格高度
  const setTableHeight = () => {
    if (state.isLzos) {
      state.tableHeight = document.body.offsetHeight - 240
    } else {
      state.tableHeight = document.body.offsetHeight - 314
    }
  }
  onMounted(() => {
    setTableHeight()
    onSearch()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .version {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    &-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      padding: 0 10px;
      border-bottom: 1px solid #c5d0ea;
      :deep(.nancalui-button) {
        margin-left: 8px;
        padding: 0 8px;
        .button-content {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
            font-size: 16px;
          }
        }
      }
    }
    &-content {
      height: calc(100% - 52px);
      padding: 16px;
      .status {
        .circle {
          display: inline-block;
          width: 6px;
          height: 6px;
          margin-right: 4px;
          background-color: #04c495;
          border-radius: 50%;

          &.red {
            background-color: #f63838;
          }

          &.yellow {
            background-color: #ff7d00;
          }
        }
      }
    }
  }
</style>
