<template>
    <div class="container">
        <div class="detail-content">
            <div class="btn-group">
                <!-- 返回 -->
                <n-button class="add-task-btn" @click="goBack">
                    返回
                </n-button>
                <!-- 功能按钮  上传原始数据、上传标注工具、上传规则文件 -->
                <div class="tool-group">
                    <n-button class="add-task-btn" variant="solid" @click="uploadSource">
                        上传原始数据
                    </n-button>
                    <!-- <n-button class="add-task-btn" variant="solid">
                        上传标注工具
                    </n-button> -->
                    <n-button class="add-task-btn" variant="solid" @click="uploadRule">
                        上传规则文件
                    </n-button>
                </div>
            </div>
            <div class="task-list">
                <CfTable actionWidth="120" ref="tableNoRef" :tableConfig="{
                    data: state.dataSource,
                    rowKey: 'id',
                }" :table-head-titles="tableHead">
                    <template #editor="{ row }">
                        <!-- 下载 -->
                        <n-button class="download-btn" variant="text" color="primary" @click="download(row)">
                            下载
                        </n-button>
                        <!-- 删除 -->
                        <n-button class="delete-btn" variant="text" color="primary" @click="deleteTask(row)">
                            删除
                        </n-button>
                    </template>
                </CfTable>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useRouter } from 'vue-router' // 引入路由
import api from '@/api/index' // 引入api

const state = reactive({ // 表格数据
    dataSource: [], // 表格数据
})
const tableHead = computed(() => { // 表格表头
    return [
        { name: '文件类型', prop: 'fileType' },
        { name: '文件名称', prop: 'fileOriginalName' },
        { name: '上传时间', prop: 'uploadTime' },
    ]
})
const router = useRouter() // 获取路由实例
// 获取当前路由的参数
const { id } = router.currentRoute.value.query // 获取当前路由的参数
const goBack = () => { // 返回
    router.go(-1) // 返回上一页
}
const download = (row) => { // 下载
    const { fileUrl } = row // 获取文件地址
    fetch(fileUrl).then((res) => { // 下载文件
        res.blob().then((blob) => { // 将文件转换为blob
            const url = window.URL.createObjectURL(blob) // 创建文件地址
            const a = document.createElement('a') // 创建a标签
            a.href = url // 设置a标签的href属性
            a.download = row.fileOriginalName // 设置a标签的download属性
            a.click() // 点击a标签
            window.URL.revokeObjectURL(url) // 释放文件地址
        })
    })
}
const deleteTask = (row) => { // 删除
    api.dataGovernance.deleteTaskDetail({ // 删除
        fileType: row.fileType, // 文件类型
        workbenchTaskId: id, // 文件ID
    }).then((res) => { // 成功
        if (res.success) { // 成功
            ElMessage.success('删除成功') // 提示
            onSearch() // 搜索
        } else { // 失败
            ElMessage.error('删除失败') // 提示
        }
    })
}
const uploadData = (fileType = "原始数据") => { // 上传原始数据
    console.log('上传原始数据') // 打印当前行数据
    const input = document.createElement('input') // 创建input元素
    input.type = 'file' // 设置input元素类型为file
    input.onchange = (e) => { // 监听input元素的change事件
        const file = e.target.files[0] // 获取文件
        console.log(file) // 打印文件
        const formData = new FormData()
        formData.append('file', file)
        formData.append('bucket', 'data-govern')
        api.dataManagement.fileUploadLocalMd5(formData).then((res) => {
            if (res.success) { // 上传成功
                api.dataGovernance.addTaskDetail({
                    fileOriginalName: file.name,
                    fileType,
                    fileUrl: res.data.url,
                    workbenchTaskId: id
                }).then(({ success }) => { // 上传文件
                    if (success) { // 上传成功
                        ElMessage.success('上传成功') // 提示
                        onSearch() // 搜索  
                    } else { // 上传失败
                        ElMessage.error('上传失败') // 提示
                    }
                })
            } else { // 上传失败
                ElMessage.error('上传失败') // 提示
            }
        })
    }
    input.click() // 点击input元素
}
const uploadSource = () => {
    uploadData()
}
const uploadRule = () => {
    uploadData('规则文件')
}
// 搜索
const onSearch = () => {
    api.dataGovernance.addTaskDetailEcho({
        workbenchTaskId: id
    }).then((res) => { // 成功
        if (res.success) { // 成功
            state.dataSource = res.data || [] // 表格数据
        }
    })
}
onSearch()
</script>
<style lang="scss" scoped>
.container {
    padding: 16px;
    border-radius: 0;

    .detail-content {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 0;

        .btn-group {
            padding: 16px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .add-task-btn {
                margin-right: 10px;
            }

            .tool-group {
                display: flex;
                align-items: center;
            }
        }

        .task-list {
            height: calc(100% - 64px);
        }
    }
}
</style>