def project_url = "http://172.16.0.213:8200/nc-web/yy-management-platform.git"
def gitlab_auth = "fe0dcba3-4279-**********************"
node {
  stage('拉取代码') {
        checkout([$class: 'GitSCM', branches: [[name: '*/${branch}']],
        extensions: [], userRemoteConfigs: [[credentialsId: "${gitlab_auth}", url: "${project_url}"]]])
  }
  stage('打包，部署网站') {
    //使用NodeJS的npm进行打包
    nodejs('nodejs16'){
      sh "node -v"
      //sh "npm install -g cnpm --registry=https://registry.npm.taobao.org"
     // sh "npm i"
      sh "npm run build"
    }
    //=====以下为远程调用进行项目部署========
    sshPublisher(publishers: [sshPublisherDesc(configName: 'zhili_server',
    transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: '',
    execTimeout: 120000, flatten: false, makeEmptyDirs: false, noDefaultExcludes:
    false, patternSeparator: '[, ]+', remoteDirectory: '/usr/share/nginx/html',
    remoteDirectorySDF: false, removePrefix: 'dist', sourceFiles: 'dist/**')],
    usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
  }
}
