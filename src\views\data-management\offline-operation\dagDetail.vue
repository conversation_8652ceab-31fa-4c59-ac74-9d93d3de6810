<template>
  <section class="container">
    <div class="page-title">
      实例监控
      <div class="detail-back-box" @click.prevent="closeFn"> 返回 </div>
    </div>
    <div class="container-box">
      <div class="container-box-row">
        <div class="label">
          <div class="name">业务流程：</div>
          <div class="value">{{ state.info.name }}</div>
        </div>
        <div class="label">
          <div class="name">责任人：</div>
          <div class="value">{{ state.info.personInChargeName }}</div>
        </div>
        <div class="label">
          <div class="name">调度频率：</div>
          <div class="value">{{ state.info.scheduleText || '--' }}</div>
        </div>
        <div class="label">
          <div class="name">开始运行时间：</div>
          <div class="value"> {{ state.info?.instanceStartTime || '--' }} </div>
        </div>
        <div v-loading="state.loading" class="refresh" @click.prevent.stop="refreshFn">
          <SvgIcon class="icon" icon="icon-refresh" />刷新
        </div>
      </div>
      <section class="table-list">
        <flow :checkedItem="state.checkedItem" @showRunView="showRunView" @refreshFn="refreshFn" />
      </section>
      <div class="container-box-run">
        <div class="container-box-run-tabs">
          <n-tabs v-model="state.runType">
            <n-tab id="attr" title="属性" />
            <n-tab id="code" title="代码" />
          </n-tabs>
          <SvgIcon
            :class="{ icon: true, show: state.showRun }"
            icon="icon-arrow-second"
            @click.prevent.stop="showRunFn"
          />
        </div>
        <div v-if="state.showRun" class="container-box-run-text">
          <div v-if="state.runType === 'attr'" class="attr">
            <div class="row">
              <div class="col">
                <div class="name">作业名称：</div>
                <div class="value">{{ state.showNodeInfo.name }}</div>
              </div>
              <div class="col">
                <div class="name">作业ID：</div>
                <div class="value">{{ state.showNodeInfo.id }}</div>
              </div>
              <div class="col">
                <div class="name">作业类型：</div>
                <div class="value">{{ state.showNodeInfo.taskTypeName }}</div>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <div class="name">调度类型：</div>
                <div class="value">{{ state.showNodeInfo.scheduleTypeName }}</div>
              </div>
              <div class="col">
                <div class="name">实例ID：</div>
                <div class="value">{{ state.showNodeInfo.dsTaskId || '--' }}</div>
              </div>
              <div class="col">
                <div class="name">运行时长：</div>
                <div class="value">{{ state.showNodeInfo.duration || '--' }}</div>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <div class="name">任务触发时间：</div>
                <div class="value">{{ state.showNodeInfo.startTime || '--' }}</div>
              </div>
              <div class="col">
                <div class="name">任务结束时间：</div>
                <div class="value">{{ state.showNodeInfo.endTime || '--' }}</div>
              </div>
              <div class="col">
                <div class="name">责任人：</div>
                <div class="value">{{ state.showNodeInfo.personInChargeName }}</div>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <div class="name">运行状态：</div>
                <div
                  :class="{
                    value: true,
                    green: state.showNodeInfo.nodeRunstatus === 'SUCCESS',
                    red: state.showNodeInfo.nodeRunstatus === 'FAILURE',
                    blue: state.showNodeInfo.nodeRunstatus === 'RUNNING_EXECUTION',
                    gray: state.showNodeInfo.nodeRunstatus === 'WAITING_TO_RUN',
                    yellow: state.showNodeInfo.nodeRunstatus === 'STOP',
                  }"
                  >{{ state.showNodeInfo.nodeRunstatusText }}</div
                >
              </div>
            </div>
          </div>
          <div v-else class="code">
            <n-textarea
              v-if="state.runResultText"
              v-model="state.runResultText"
              placeholder="请输入描述信息"
              :autosize="{ minRows: 3 }"
              readonly
            />
          </div>
          <div v-if="!state.runResultText && state.runType === 'code'" class="empty">
            <img class="empty-pic" src="@/assets/img/empty_gray.png" />
            <div class="empty-text">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
  import { onMounted, reactive } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import flow from './flow'
  import { changeFrequencyChinese } from '@/utils/index.js'
  import { operationWorkDetail, taskDetail } from '@/api/dataManage'
  const store = useStore()
  const router = useRouter()
  const state = reactive({
    runType: 'attr',
    showRun: false,
    id: null,
    name: '',
    info: {
      name: '',
      personInChargeName: '',
      scheduleText: '',
      failRetryTimes: '',
    },
    showNodeInfo: {},
    checkedItem: null,
    runResultText: '',
  })
  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }
  // 展示属性
  const showRunFn = () => {
    state.showRun = !state.showRun
  }
  const showRunView = (item) => {
    taskDetail({ id: item.nodeId }).then((res) => {
      if (res.success) {
        res.data.dsTaskId = item.dsTaskId
        res.data.startTime = item.startTime
        res.data.endTime = item.endTime
        res.data.duration = item.duration
        res.data.nodeRunstatus = item.nodeRunstatus
        res.data.nodeRunstatusText = statusFn(item.nodeRunstatus)
        if (item.taskType === 'OW_PY_SPARK') {
          state.runResultText = res.data?.versionBO?.pySparkTaskBO?.pythonScript || ''
        } else if (item.taskType === 'OW_HIVE_SQL') {
          state.runResultText = res.data?.versionBO?.hiveSqlTaskBO?.sqlScript || ''
        } else if (item.taskType === 'OW_SPARK_SQL') {
          state.runResultText = res.data?.versionBO?.sparkSqlTaskBO?.sqlScript || ''
        } else if (item.taskType === 'OW_PYTHON') {
          state.runResultText = res.data?.versionBO?.pythonTaskBO?.pythonScript || ''
        } else if (item.taskType === 'OW_SHELL') {
          state.runResultText = res.data?.versionBO?.shellTaskBO?.shellScript || ''
        } else {
          state.runResultText = ''
        }
        state.showNodeInfo = res.data
        state.showRun = true
      }
    })
  }
  // 运行状态转译
  const statusFn = (type) => {
    if (type === 'SUCCESS') {
      return '成功'
    } else if (type === 'FAILURE') {
      return '失败'
    } else if (type === 'STOP') {
      return '停止'
    } else if (type === 'RUNNING_EXECUTION') {
      return '运行中'
    } else if (type === 'WAITING_TO_RUN') {
      return '等待运行'
    }
  }
  const cancelFn = () => {
    router.go(-1)
  }
  const closeFn = () => {
    router.push({ name: 'operationFlow' })
  }

  const refreshFn = () => {
    getDetailFn()
  }

  const getDetailFn = () => {
    state.loading = true
    operationWorkDetail({ id: state.id, nodeStatus: true })
      .then((res) => {
        if (res.success) {
          let hasFailure = false
          res.data.taskDef.forEach((val) => {
            if (!val.nodeFrozen) {
              val.nodeFrozen = false
            }
            if (val.nodeRunstatus === 'FAILURE') {
              hasFailure = true
            }
            val.nodeId = val.id
            val.checked = false
          })
          res.data.scheduleText = changeFrequencyChinese(res.data)
          state.info = res.data
          state.checkedItem = {
            nodes: res.data.taskDef,
            relationship: res.data.relation,
            dsProcessCode: res.data.dsProcessCode,
            dsTaskId: res.data.dsTaskId,
            processId: res.data.id,
            collectJobIds: res.data.collectJobIds,
            hasFailure: hasFailure,
          }
        }
        state.loading = false
      })
      .catch(() => {
        state.loading = false
      })
  }
  onMounted(() => {
    state.id = router.currentRoute.value.query.id
    getDetailFn()
  })
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';

  .container {
    .page-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      box-sizing: border-box;
      height: 46px;
      padding: 0 8px 0 16px;
      color: #1d2129;
      font-weight: bolder;
      font-size: 16px;
      background-color: #fff;
      margin-bottom: 11px;
      border-radius: 2px 2px 0 0;

      &:before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }

      .detail-back-box {
        position: absolute;
        top: 0;
        right: 16px;
        bottom: 0;
        z-index: 9;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 30px;
        margin: auto;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        color: #1d2129;
        font-weight: normal;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #479dff;
          border: 1px solid #479dff;
        }
      }
    }

    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      height: calc(100% - 57px);
      background: #fff;
      border-radius: 0 0 2px 2px;

      &-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        height: 46px;
        padding: 14px 100px 14px 16px;
        position: relative;
        .label {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-right: 60px;
          .name {
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 14px;
          }
          .value {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            max-width: 249px;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            span {
              width: 16px;
              height: 16px;
              margin-left: 6px;
            }
          }
          &:last-child {
            .value {
              width: 249px;
            }
          }
        }
        .refresh {
          position: absolute;
          width: 80px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          top: 0;
          bottom: 0;
          right: 16px;
          margin: auto;
          color: #1e89ff;
          font-size: 14px;
          border-radius: 4px;
          box-sizing: border-box;
          cursor: pointer;
          .icon {
            font-size: 16px;
            margin-right: 4px;
          }
          &:hover {
            border: 1px solid #1e89ff;
          }
        }
      }

      .table-list {
        position: relative;
        box-sizing: border-box;
        height: calc(100% - 103px);
        overflow: hidden;
        padding: 0 8px;
      }
      &-run {
        min-height: 56px;
        box-sizing: border-box;
        border-top: 1px solid #dcdfe6;
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        padding: 0 16px 8px 16px;
        background-color: #fff;
        &-tabs {
          height: 48px;
          position: relative;
          .icon {
            position: absolute;
            font-size: 16px;
            right: 12px;
            top: 0;
            bottom: 0;
            margin: auto;
            &.show {
              transform: rotate(180deg);
            }
          }
        }
        &-text {
          color: #1d2129;
          font-size: 14px;
          height: 200px;
          padding: 16px 0 8px 0;
          box-sizing: border-box;
          overflow-y: auto;
          word-break: break-all;
          .row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            .col {
              flex: 1;
              flex-shrink: 0;
              height: 32px;
              line-height: 32px;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              color: #606266;
              font-size: 14px;
              .value {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #1d2129;
                &.green {
                  color: #2ca340;
                }
                &.red {
                  color: #f63838;
                }
                &.blue {
                  color: #1e89ff;
                }
                &.gray {
                  color: #8091b7;
                }
                &.yellow {
                  color: #ff7d00;
                }
              }
            }
          }
          :deep(.nancalui-textarea__div) {
            .nancalui-textarea {
              border: 1px solid #fff;
              padding: 0;
              background-color: transparent;
              color: rgba(0, 0, 0, 0.75);
              &:hover {
                border: 1px solid #fff !important;
                box-shadow: none !important;
              }
            }
          }
          .empty {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            &-pic {
              width: 48px;
              height: 48px;
            }
            &-text {
              color: #909399;
              font-size: 14px;
              margin-top: 16px;
            }
          }
        }
      }
    }
  }
</style>
