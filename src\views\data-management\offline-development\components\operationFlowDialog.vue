<template>
  <n-modal
    v-model="showDirDialog"
    title="新建业务流程"
    width="560px"
    :close-on-click-overlay="false"
    bodyClass="modal-body"
    @close="cancel"
  >
    <div class="modal-container">
      <n-form
        ref="syncForm"
        :data="state.syncForm"
        :rules="state.syncRules"
        label-align="start"
        label-width="96px"
      >
        <n-form-item label="业务流程" field="name">
          <n-input v-model="state.syncForm.name" maxlength="500" placeholder="请输入" />
        </n-form-item>
        <n-form-item label="业务流模版" field="template">
          <n-radio-group v-model="state.syncForm.template" direction="row">
            <n-radio value="DEFAULT">默认模版</n-radio>
            <n-radio value="BLANK">空白模版</n-radio>
          </n-radio-group>
        </n-form-item>
        <!-- 告警通知 -->
        <n-form-item label="告警通知" field="alarm">
          <n-switch v-model="state.syncForm.alarm" />
        </n-form-item>
        <!-- 描述信息 -->
        <n-form-item label="描述信息" field="description">
          <n-textarea
            class="mb-2"
            v-model="state.syncForm.description"
            autofocus
            :rows="5"
            id="textArea"
            placeholder="请输入"
            maxlength="200"
          />
        </n-form-item>
        <!-- 选择目录 -->
        <n-form-item label="选择目录" field="directoryId">
          <DirSelect
            ref="treeRef"
            :disabled="!isNaN(state.currentNode?.id)"
            v-model:directoryId="state.syncForm.directoryId"
            :filter-node-method="filterNode"
            :disable-checked="(node) => containsValueInAncestors(node, 'type', 'WORKFLOW')"
            :data="$attrs.treeList"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button @click.prevent="showDirDialog = false">取 消</n-button>
        <n-button variant="solid" v-loading="state.hasClickSave" @click.prevent="confirm"
          >确 定</n-button
        >
      </div>
    </template>
  </n-modal>
</template>

<script setup>
  import api from '@/api/index'
  import DirSelect from './dirSelect.vue'
  import { checkCName500 } from '@/utils/validate'
  const showDirDialog = ref(false)
  const syncForm = ref(null)
  const emit = defineEmits(['success'])
  const treeRef = ref(null)
  const state = reactive({
    hasClickSave: false,
    currentNode: null,
    syncForm: {
      name: null,
      template: 'DEFAULT',
      alarm: false,
      description: null,
      directoryId: null,
    },
    syncRules: {
      name: [
        {
          required: true,
          validator: (...args) =>
            checkCName500(...args, null, null, {
              nameType: 'CN',
              name: state.syncForm.name,
              id: null,
            }),
          trigger: 'blur',
        },
      ],
      directoryId: [
        {
          required: true,
          message: '请选择目录',
          trigger: 'blur',
          type: 'number',
        },
      ],
    },
  })
  const cancel = () => {
    showDirDialog.value = false
  }
  // 保存
  // 确认
  const confirm = () => {
    syncForm.value.validate((val) => {
      if (val) {
        save()
      }
    })
  }
  const save = () => {
    const { type, id } = state.currentNode || {}
    // if (!id) return ElMessage.error('请选择目录')
    state.syncForm.directoryId === -1 && (state.syncForm.directoryId = undefined)
    if (state.hasClickSave) {
      return false
    }
    state.hasClickSave = true
    api.offlineJob
      .createWorkflow(state.syncForm)
      .then(({ success }) => {
        state.hasClickSave = false
        if (!success) return
        emit('success')
        showDirDialog.value = false
        ElMessage.success('创建成功')
      })
      .catch(() => {
        state.hasClickSave = false
      })
  }
  // 还原表单
  const resetForm = (() => {
    const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      state.currentNode = null
      state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()

  const filterNode = (value, data) => {
    if (!value) return true
    return data?.id === value
  }

  // 递归校验父级属性
  function containsValueInAncestors(obj, propName, value) {
    const { data } = obj
    // 检查当前对象是否包含该属性且属性值等于目标值
    if (data.hasOwnProperty(propName) && data[propName] === value) {
      return true
    }

    // 如果当前对象有父级对象，继续递归检查
    if (obj.hasOwnProperty('parent') && obj.parent) {
      return containsValueInAncestors(obj.parent, propName, value)
    }

    // 如果没有找到，返回false
    return false
  }
  defineExpose({
    open(node) {
      resetForm()
      state.currentNode = node
      state.syncForm.directoryId = node?.id
      showDirDialog.value = true
      !isNaN(node?.id) &&
        nextTick(() => {
          treeRef.value?.treeRef?.filter(node?.id)
        })
    },
  })
</script>
<style>
  .modal-body {
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .modal-container {
    padding: 16px 20px;
  }
</style>
