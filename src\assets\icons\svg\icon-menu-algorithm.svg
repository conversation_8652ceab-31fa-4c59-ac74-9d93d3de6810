<?xml version="1.0" encoding="UTF-8"?>
<svg width="92px" height="108px" viewBox="0 0 92 108" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>算法中心</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#0F489C" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.018401%" id="linearGradient-2">
            <stop stop-color="#0E203C" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F5C73" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9780798%" x2="100%" y2="49.9780798%" id="linearGradient-3">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0206526%" x2="100%" y2="50.0206526%" id="linearGradient-4">
            <stop stop-color="#103770" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#0A2554" stop-opacity="0.39" offset="29%"></stop>
            <stop stop-color="#020F32" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.0459347726%" id="linearGradient-5">
            <stop stop-color="#56B0FF" stop-opacity="0.6" offset="0%"></stop>
            <stop stop-color="#50ACFF" stop-opacity="0.56" offset="4%"></stop>
            <stop stop-color="#369BFF" stop-opacity="0.4" offset="26%"></stop>
            <stop stop-color="#218EFF" stop-opacity="0.27" offset="46%"></stop>
            <stop stop-color="#1285FF" stop-opacity="0.18" offset="66%"></stop>
            <stop stop-color="#097FFF" stop-opacity="0.12" offset="84%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0087951%" y1="100%" x2="50.0087951%" y2="0%" id="linearGradient-6">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-7">
            <stop stop-color="#DBFDFF" offset="0%"></stop>
            <stop stop-color="#2AD0FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0554939%" x2="100%" y2="50.0554939%" id="linearGradient-8">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0530223%" x2="100%" y2="50.0530223%" id="linearGradient-9">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="34%"></stop>
            <stop stop-color="#00BFFC" offset="47%"></stop>
            <stop stop-color="#00A4FB" offset="60%"></stop>
            <stop stop-color="#0091FA" offset="73%"></stop>
            <stop stop-color="#0086F9" offset="86%"></stop>
            <stop stop-color="#0082F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-10">
            <stop stop-color="#56B0FF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#379CFF" stop-opacity="0.1" offset="24%"></stop>
            <stop stop-color="#067DFF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9941072%" y1="100%" x2="49.9941072%" y2="0%" id="linearGradient-11">
            <stop stop-color="#DEEDFF" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#D5E9FF" stop-opacity="0.28" offset="21%"></stop>
            <stop stop-color="#BCDEFF" stop-opacity="0.22" offset="45%"></stop>
            <stop stop-color="#92CBFF" stop-opacity="0.13" offset="72%"></stop>
            <stop stop-color="#57B1FF" stop-opacity="0" offset="100%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-12">
            <stop stop-color="#DAEAFF" offset="0%"></stop>
            <stop stop-color="#D5E8FF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#C8E1FF" stop-opacity="0.92" offset="24%"></stop>
            <stop stop-color="#B1D6FF" stop-opacity="0.83" offset="38%"></stop>
            <stop stop-color="#92C7FF" stop-opacity="0.7" offset="53%"></stop>
            <stop stop-color="#69B4FF" stop-opacity="0.53" offset="69%"></stop>
            <stop stop-color="#389CFF" stop-opacity="0.33" offset="84%"></stop>
            <stop stop-color="#0081FF" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50.0227169%" x2="100%" y2="50.0227169%" id="linearGradient-13">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.011786893%" y1="50%" x2="100%" y2="50%" id="linearGradient-14">
            <stop stop-color="#A6F4FB" offset="0%"></stop>
            <stop stop-color="#9FF4FB" offset="4%"></stop>
            <stop stop-color="#8CF3FC" offset="9%"></stop>
            <stop stop-color="#6CF2FC" offset="15%"></stop>
            <stop stop-color="#41F0FD" offset="22%"></stop>
            <stop stop-color="#09EDFF" offset="30%"></stop>
            <stop stop-color="#00EDFF" offset="31%"></stop>
            <stop stop-color="#00E2FE" offset="33%"></stop>
            <stop stop-color="#00BFFC" offset="41%"></stop>
            <stop stop-color="#00A4FB" offset="50%"></stop>
            <stop stop-color="#0091FA" offset="58%"></stop>
            <stop stop-color="#0086F9" offset="67%"></stop>
            <stop stop-color="#0082F9" offset="76%"></stop>
            <stop stop-color="#A5E9E9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9773037%" x2="100%" y2="49.9773037%" id="linearGradient-15">
            <stop stop-color="#56B0FF" offset="0%"></stop>
            <stop stop-color="#52ADFF" offset="3%"></stop>
            <stop stop-color="#3198FF" offset="30%"></stop>
            <stop stop-color="#1989FF" offset="56%"></stop>
            <stop stop-color="#0B80FF" offset="80%"></stop>
            <stop stop-color="#067DFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9913059%" y1="100%" x2="49.9913059%" y2="0%" id="linearGradient-16">
            <stop stop-color="#DEEDFF" offset="0%"></stop>
            <stop stop-color="#DBECFF" stop-opacity="0.98" offset="11%"></stop>
            <stop stop-color="#D4E8FF" stop-opacity="0.92" offset="23%"></stop>
            <stop stop-color="#C7E3FF" stop-opacity="0.83" offset="36%"></stop>
            <stop stop-color="#B5DBFF" stop-opacity="0.7" offset="50%"></stop>
            <stop stop-color="#9ED1FF" stop-opacity="0.53" offset="65%"></stop>
            <stop stop-color="#83C4FF" stop-opacity="0.33" offset="80%"></stop>
            <stop stop-color="#62B6FF" stop-opacity="0.09" offset="95%"></stop>
            <stop stop-color="#56B0FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.9656829%" x2="100%" y2="49.9656829%" id="linearGradient-17">
            <stop stop-color="#00459E" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#004184" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-18">
            <stop stop-color="#56E3FF" stop-opacity="0.887877138" offset="0%"></stop>
            <stop stop-color="#06B8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9884232%" y1="100%" x2="49.9884232%" y2="0%" id="linearGradient-19">
            <stop stop-color="#5098FF" stop-opacity="0.5" offset="0%"></stop>
            <stop stop-color="#08B5FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-0.12345679%" y1="50.1040944%" x2="100.123457%" y2="50.1040944%" id="linearGradient-20">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DEEDFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#009EED" offset="0%"></stop>
            <stop stop-color="#0063D4" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-22" points="15.7521436 6.1469903 0 11.9097937 0.569256607 0 9.37648325 3.49526557"></polygon>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="0" fill="none" fill-rule="evenodd">
        <g id="统一登录页-默认" transform="translate(-794.000000, -538.000000)">
            <g id="编组-3" transform="translate(260.000000, 250.000000)">
                <g id="算法中心" transform="translate(494.000000, 266.000000)">
                    <g transform="translate(40.000000, 22.000000)">
                        <g id="头像底座" transform="translate(0.000000, 39.753694)">
                            <g id="底座2-link" transform="translate(0.000000, 18.048610)" fill-rule="nonzero">
                                <g id="底座2">
                                    <g id="编组" transform="translate(0.000000, 13.274883)">
                                        <path d="M46.8371483,0.267864003 L91.3045367,18.2657044 L45.1709839,35.1427237 L0.703595546,17.1448833 L46.8371483,0.267864003 Z" id="路径" stroke="url(#linearGradient-2)" stroke-width="0.5" fill="url(#linearGradient-1)"></path>
                                        <polygon id="路径" fill="url(#linearGradient-3)" points="92 18.2774871 91.9918678 19.7897121 45.1583134 36.9228127 45.1664457 35.4105877"></polygon>
                                        <polygon id="路径" fill="url(#linearGradient-4)" points="45.1664457 35.4105877 45.1583134 36.9228127 0 18.6453256 0.00813223725 17.1331006"></polygon>
                                    </g>
                                    <g id="编组" transform="translate(22.892248, 19.111254)">
                                        <polygon id="路径" fill="url(#linearGradient-5)" points="23.5346946 1.02994784 46.2317688 10.2177365 22.6970742 18.8251578 0.00813223725 9.64554329"></polygon>
                                        <path d="M23.5265624,1.47135406 L45.0932555,10.2013882 L22.7052064,18.3755774 L1.13851321,9.66189167 L23.5265624,1.48770244 M23.5265624,1.05447041 L0,9.64554329 L22.6970742,18.8251578 L46.2317688,10.2177365 L23.5346946,1.03812203 L23.5265624,1.05447041 Z" id="形状" fill="url(#linearGradient-6)"></path>
                                        <polygon id="路径" fill="url(#linearGradient-7)" points="23.3639176 0 36.7658446 5.42766165 22.8678511 10.5120073 9.45779192 5.0843457"></polygon>
                                        <polygon id="路径" fill="url(#linearGradient-8)" points="36.7658446 5.42766165 36.7495801 7.70826044 22.8434544 12.7926061 22.8678511 10.5120073"></polygon>
                                        <polygon id="路径" fill="url(#linearGradient-9)" points="22.8678511 10.5120073 22.8434544 12.7926061 9.44152745 7.37311868 9.45779192 5.0843457"></polygon>
                                    </g>
                                    <g id="编组" transform="translate(11.507116, 0.000000)">
                                        <polygon id="路径" fill="url(#linearGradient-10)" points="35.1312649 0 68.9939008 13.7081153 33.8707681 26.5579408 0 12.8498255"></polygon>
                                        <path d="M35.1231327,0.441406218 L67.8553876,13.6835928 L33.8707681,26.1574055 L1.13851321,12.8661738 L35.1231327,0.441406218 M35.1231327,0 L0,12.8498255 L33.8707681,26.5579408 L69.0020331,13.7081153 L35.1312649,0 L35.1231327,0 Z" id="形状" fill="url(#linearGradient-11)"></path>
                                    </g>
                                    <g id="编组" transform="translate(22.632016, 3.351418)">
                                        <polygon id="路径" fill="url(#linearGradient-12)" points="23.8030584 0 46.7440997 9.28587896 22.9491735 17.9913905 0 8.71368572"></polygon>
                                        <path d="M23.7949262,0.441406218 L45.6055865,9.26953058 L22.9491735,17.5581585 L1.13851321,8.7300341 L23.7949262,0.441406218 M23.7949262,0 L0,8.71368572 L22.9491735,17.9913905 L46.7440997,9.28587896 L23.8030584,0 L23.7949262,0 Z" id="形状" fill="url(#linearGradient-13)"></path>
                                    </g>
                                </g>
                            </g>
                            <polygon id="路径" fill="url(#linearGradient-14)" fill-rule="nonzero" points="46.6302484 18.0077389 80.4928843 31.70768 45.3697516 44.5575055 11.4989835 30.8575643"></polygon>
                            <polygon id="路径" fill="url(#linearGradient-15)" fill-rule="nonzero" points="47.199505 0 70.1568107 9.29405315 46.3456201 18.0077389 23.3883143 8.71368572"></polygon>
                            <path d="M47.166976,0.441406218 L68.9939008,9.26953058 L46.3537523,17.5663326 L24.5186953,8.7300341 L47.166976,0.441406218 M47.166976,0 L23.3883143,8.71368572 L46.3537523,17.9995647 L70.1568107,9.29405315 L47.166976,0 Z" id="形状" fill="url(#linearGradient-16)" fill-rule="nonzero"></path>
                            <polygon id="路径" fill="url(#linearGradient-17)" fill-rule="nonzero" points="47.0531247 3.04897258 62.2360117 9.19596288 46.4838681 14.9587663 31.3009812 8.81177599"></polygon>
                            <polygon id="路径" fill="url(#linearGradient-18)" fill-rule="nonzero" points="23.3883143 8.71368572 11.4989835 30.8575643 45.3697516 44.5575055 46.6302484 18.0077389"></polygon>
                            <polygon id="路径" fill="url(#linearGradient-19)" fill-rule="nonzero" points="70.1568107 9.29405315 46.3456201 18.0077389 45.3697516 44.5575055 80.4928843 31.70768"></polygon>
                            <polyline id="路径" stroke="url(#linearGradient-20)" stroke-width="0.5" points="75.6379387 22.0539625 45.4592062 33.0972922 16.3539291 21.3182855"></polyline>
                            <g id="Clipped" transform="translate(46.483868, 3.048973)">
                                <mask id="mask-23" fill="white">
                                    <use xlink:href="#path-22"></use>
                                </mask>
                                <use id="路径" fill="url(#linearGradient-21)" xlink:href="#path-22"></use>
                            </g>
                        </g>
                        <g id="编组" transform="translate(25.000138, 0.000000)">
                            <g id="编组-7" transform="translate(0.000000, 15.639954)">
                                <path d="M19.8800487,1.95563033 C20.5728988,1.4153601 19.1233645,0.817753376 15.5314457,0.162810175 C14.9327586,0.0536337196 14.3150268,0.129126554 13.7602464,0.379255586 L0.93009678,6.16387456 L0.93009678,6.16387456 L4.46816406,7.90170916 C13.9929031,4.52237472 19.130198,2.54034845 19.8800487,1.95563033 Z" id="路径-8" fill="#009FFB" transform="translate(10.489099, 3.950855) scale(-1, 1) translate(-10.489099, -3.950855) "></path>
                                <path d="M19.8368644,7.90170916 L20.0481002,25.9945193 L16.3274161,24.0782627 C15.9945042,23.9068038 15.7852859,23.5637144 15.7852859,23.1892435 L15.7852859,6.16387456 L15.7852859,6.16387456 L19.8368644,7.90170916 Z" id="路径-9" fill="#206DC4" transform="translate(17.916693, 16.079197) scale(-1, 1) translate(-17.916693, -16.079197) "></path>
                                <path d="M15.6308063,7.18555342 L3.28982575,1.39162246 C2.62911433,1.01737333 1.82055139,1.01737333 1.15983997,1.39162246 C0.443684225,1.81207518 0.00475006126,2.57905488 0,3.41071961 L0,18.1958704 C-0.0091110176,19.0783591 0.476026743,19.8961627 1.26148788,20.3027544 L13.6255702,26.1059261 C13.9351343,26.272259 14.2816613,26.3554255 14.6328086,26.3600458 C15.0209188,26.3461848 15.3997883,26.2306758 15.7324542,26.02738 C16.4393692,25.5976865 16.873683,24.8353272 16.8829238,24.0082828 L16.8829238,9.20465058 C16.8505812,8.3545044 16.3746842,7.58752471 15.6308063,7.18555342 Z" id="路径" fill="#008FE2"></path>
                            </g>
                            <g id="编组-6" transform="translate(21.263547, 15.639954)">
                                <path d="M19.3850213,1.40086318 L15.1210593,0.268638681 C14.4659624,0.0946885381 13.7712654,0.14770354 13.1501505,0.419046328 L0,6.16387456 L0,6.16387456 L4.05157854,7.90170916 L19.3850213,1.40086318 Z" id="路径-8" fill="#55C7FD"></path>
                                <path d="M4.05157854,7.90170916 L4.26281435,25.5765903 L0.542130186,23.6603337 C0.209218342,23.4888748 4.58594594e-17,23.1457854 0,22.7713145 L0,6.16387456 L0,6.16387456 L4.05157854,7.90170916 Z" id="路径-9" fill="#0751A8"></path>
                                <path d="M17.2966187,1.35003922 C17.9527098,1.00351225 18.7427913,1.02199369 19.3850213,1.40086318 C20.0965566,1.82593626 20.5401112,2.5882956 20.5447315,3.41996033 L20.5447315,3.41996033 L20.5447315,18.1958704 C20.563213,19.0691184 20.0965566,19.8823017 19.3341973,20.3027544 L19.3341973,20.3027544 L6.91929101,26.1059261 C6.60972691,26.2768794 6.26319994,26.3600458 5.91205261,26.3600458 C5.52856276,26.3600458 5.14969327,26.2583979 4.81240702,26.0689632 C4.105492,25.6392697 3.67117819,24.8769104 3.66193747,24.049866 L3.66193747,24.049866 L3.66193747,9.20465058 C3.68041891,8.36374512 4.13783452,7.59676543 4.8724717,7.18555342 L4.8724717,7.18555342 Z M10.7819117,16.7820404 L9.69612714,18.422268 L8.61034263,17.9047877 L8.03279768,18.7780357 L9.11858219,19.295516 L8.03279768,20.9357437 L8.61034263,21.2129652 L9.7007475,19.5681172 L10.786532,20.0855975 L11.364077,19.2123495 L10.2736721,18.6994896 L11.3594566,17.0592619 L10.7819117,16.7820404 Z M16.9131289,15.9827181 L13.831349,17.6229458 L13.831349,18.4361291 L16.9131289,16.8005218 L16.9131289,15.9827181 Z M16.9131289,14.0467875 L13.831349,15.6870151 L13.831349,16.5048188 L16.9131289,14.8645911 L16.9131289,14.0467875 Z M9.83011757,9.89770452 L9.01231392,10.3227776 L9.01231392,11.8521166 L7.47835452,12.6468185 L7.47835452,13.4600018 L9.01231392,12.6652999 L9.01231392,14.194639 L9.83011757,13.7695659 L9.83011757,12.2402268 L11.364077,11.445525 L11.364077,10.6323417 L9.83011757,11.4270436 L9.83011757,9.89770452 Z M16.9131289,7.90170916 L13.831349,9.54193683 L13.831349,10.3551201 L16.9131289,8.71951282 L16.9131289,7.90170916 Z" id="形状结合" fill="#9BF4FB" fill-rule="nonzero"></path>
                            </g>
                            <g id="编组-9" transform="translate(4.382483, 0.000000)">
                                <path d="M16.2387191,8.71803922 L16.2387191,11.4269483 C16.2387191,12.583817 15.5735336,13.6376012 14.5291601,14.1352248 L0.0420833912,21.0380345 L0.0420833912,21.0380345 L0.151128729,20.0751504 L0.0958031306,15.7207507 L16.2387191,8.71803922 Z" id="路径-10备份" fill="#206DC4" transform="translate(8.140401, 14.878037) scale(-1, 1) translate(-8.140401, -14.878037) "></path>
                                <path d="M32.2350465,8.71803922 L32.2350465,11.4536006 C32.2350622,12.5967883 31.5853524,13.6406156 30.5596285,14.1453616 C21.4801313,18.6132687 16.7883763,20.8981482 16.4843634,21 C16.0010822,21.161911 16.045184,20.8499999 16.045184,19.2218221 C16.045184,18.1363703 16.045184,17.3022157 16.045184,16.7193584 L32.2350465,8.71803922 Z" id="路径-10" fill="#9BF4FB" fill-rule="nonzero"></path>
                                <path d="M32.0369723,9.55836529 C32.5498322,8.40789575 32.032352,7.05875074 30.8818824,6.54589082 L17.8709497,0.391571804 C16.7586786,-0.130523935 15.4717475,-0.130523935 14.3594764,0.391571804 L1.35316408,6.54589082 C0.8356838,6.7769088 0.424471794,7.18812081 0.198074172,7.70098072 C-0.314785746,8.85145027 0.202694532,10.2005953 1.35316408,10.7134552 L15.0386693,17.1542365 C15.3713352,17.3298102 15.7409639,17.4268377 16.1198334,17.4360784 C16.4940826,17.4222174 16.8637113,17.3251898 17.2009976,17.1542365 L30.8865028,10.7134552 C31.3947423,10.4870576 31.8059543,10.0712252 32.0369723,9.55836529 Z" id="路径" fill="#55C7FD"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
