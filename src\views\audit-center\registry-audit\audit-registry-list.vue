<template>
  <div class="container">
    <div class="container-box">
      <div class="container-right">
        <n-tabs v-model="state.activeName" class="tabs">
          <n-tab
            v-for="(item, index) in state.tabList"
            :key="index"
            :title="item.label"
            :id="item.name"
          />
        </n-tabs>
        <div class="card">
          <v-reviewed v-if="state.activeName" class="scroll" :activeName="state.activeName" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import reviewed from './reviewed/index-registry.vue'

  export default {
    name: 'AuditCenter',
    components: {
      'v-reviewed': reviewed,
    },
    setup() {
      const router = useRouter()
      const state = reactive({
        activeName: '',
        tabList: [
          { label: '待审核', name: 'first' },
          { label: '已审核', name: 'second' },
        ],
      })
      onMounted(() => {
        if (router.currentRoute.value.query.active) {
          state.activeName = router.currentRoute.value.query.active
        } else {
          state.activeName = 'first'
        }
      })

      return {
        state,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    &-box {
      height: 100%;
      border-radius: 4px;
      background-color: #ffffff;
      display: flex;
      flex-direction: row;
      // overflow: hidden;
    }

    &-left {
      border-right: 1px solid #ebebeb;
    }

    &-right {
      width: 100%;

      .tabs {
        margin: 0 20px;
      }

      .card {
        // height: calc(100% - 110px);
        margin: 20px;
      }
    }
  }
</style>
