<template>
  <!-- 添加数据源 -->
  <div class="all-data-source">
    <n-modal
      v-model="state.dialogVisible"
      bodyClass="all-data-source-dialog commonDialog"
      :draggable="false"
      title="添加数据源"
      width="1020px"
      :close-on-click-overlay="false"
      :append-to-body="false"
      :before-close="handleClose"
    >
      <div v-loading="state.loading">
        <n-public-table
          ref="allDataSourceTable"
          :isDisplayAction="false"
          :isNeedSelection="true"
          :table-head-titles="state.tableHeadTitles"
          :tableHeight="436"
          :tableData="state.tableData"
          :configData="state.configData"
          @tablePageChange="tablePageChange"
          @handle-selection-change="handleSelectionChange"
        >
          <template #pageTop>
            <div class="box-add">
              <div class="commonForm">
                <n-form
                  :inline="true"
                  :model="state.filterSearch"
                  :key="state.key"
                  class="top-right commonForm demo-form-inline"
                >
                  <n-form-item label="数据库类型：">
                    <n-select
                      v-model="state.filterSearch.datasourceType"
                      placeholder="请选择"
                      allow-clear
                      filter
                      :options="
                        state.org_dataSourceType.map((val) => {
                          return { ...val, name: val.label, value: val.value }
                        })
                      "
                      @value-change="initTable(true)"
                    />
                  </n-form-item>
                  <n-form-item label="">
                    <n-input
                      v-model="state.filterSearch.name"
                      placeholder="请输入数据源名称"
                      clearable
                      @clear="initTable(true)"
                    >
                      <template #append>
                        <n-button @click.prevent="initTable(true)">
                          <n-popover
                            class="item"
                            content="搜索"
                            trigger="hover"
                            :position="['bottom']"
                          >
                            <SvgIcon class="icon_search" icon="icon_search" />
                          </n-popover>
                        </n-button>
                      </template>
                    </n-input>
                  </n-form-item> </n-form
              ></div>
            </div>
          </template>
          <template #dataStructureType="{ editor }">
            <div>{{
              editor.row.dataStructureType === 'ALL_STRUCTURE' ? '结构化数据源' : '非结构化数据源'
            }}</div>
          </template>
          <template #empty>
            <slot name="empty">
              <div class="table-no-content">
                <img class="pic-no-conyent" src="@/assets/table-no-content.png" alt="暂无内容" />
                <div>无可用数据源，请先新增或者发布</div>
              </div>
            </slot>
          </template>
        </n-public-table>
      </div>
      <template #footer>
        <n-modal-footer class="dialog-footer">
          <n-button size="sm" variant="solid" color="primary" @click.prevent="save">保 存</n-button>
          <n-button size="sm" @click.prevent="state.dialogVisible = false">取 消</n-button>
        </n-modal-footer>
      </template>
    </n-modal>
  </div>
</template>

<script>
  import { ref, reactive, nextTick } from 'vue'
  import { ElNotification } from 'element-plus'
  import api from '@/api/index'
  export default {
    title: 'List',
    components: {},
    props: {},
    // emits: ['cancelSelectData', 'getSelectData'],
    emits: ['getSelectData'],
    setup(props, { emit }) {
      const allDataSourceTable = ref()
      const state = reactive({
        key: 1,
        loading: false,
        dialogVisible: false,
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'name', name: '数据源名称' },
          // { prop: 'database', name: '实例名称' },
          { prop: 'dataStructureType', name: '数据结构类型', slot: 'dataStructureType' },
          { prop: 'datasourceType', name: '数据库类型' },
          { prop: 'description', name: '描述' },
        ],
        pagination: {
          currentPage: 1,
          pageSize: 10,
        },
        filterSearch: {
          datasourceType: null,
          name: null,
        },
        selectTableData: [],
        org_dataSourceType: [],
        editData: null, // 编辑数据
        configData: {},
      })
      const methods = {
        //设置table高度
        setTableHeight(key = 'tableHeight', removeHeight = 396) {
          state[key] = document.body.offsetHeight - removeHeight
        },
        // 获取所有数据库类型
        getDataSourceType() {
          api.project.getDataSourceType().then((res) => {
            let { success, data } = res
            if (success) {
              let _org_dataSourceType = []
              if (data && data.length) {
                data.forEach((item) => {
                  _org_dataSourceType.push({
                    label: item,
                    value: item,
                  })
                })
              }
              state.org_dataSourceType = _org_dataSourceType
            }
          })
        },
        // 初始化
        init(data) {
          state.filterSearch.datasourceType = null
          state.filterSearch.name = null
          state.dialogVisible = true
          if (data) {
            state.editData = data
            state.configData = { selectRow: state.editData }
          } else {
            state.editData = null
            state.configData = { selectRow: state.editData }
          }
          nextTick(() => {
            allDataSourceTable.value?.clearSelection()
            methods.initTable()
            methods.getDataSourceType()
          })
        },
        // 初始化form
        initTable(init = false) {
          state.key++
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let data = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {
              datasourceType: state.filterSearch.datasourceType || null,
              status: 'PUBLISHED', // 只查询发布的数据源
              name: state.filterSearch.name || null,
            },
          }
          state.loading = true
          api.project
            .getDatasourceList(data)
            .then((res) => {
              let { success, data } = res
              if (success) {
                state.loading = false
                // 新增序号属性
                data.list.map((item, index) => {
                  return Object.assign(item, { number: index + 1 })
                })
                nextTick(() => {
                  state.tableData = data
                })
              }
            })
            .catch(() => {
              state.loading = false
            })
        },
        // 获取勾选中的数据
        handleSelectionChange(data) {
          state.selectTableData = data
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable()
        },

        // 关闭弹框
        handleClose() {
          state.dialogVisible = false
          // emit('cancelSelectData')
        },
        // 保存
        save() {
          if (!state.selectTableData.length) {
            let activeRows = allDataSourceTable.value?.getCheckedRows()
            state.selectTableData = activeRows
          }
          if (state.selectTableData.length) {
            emit('getSelectData', state.selectTableData)
            state.dialogVisible = false
          } else {
            ElNotification({
              title: '提示',
              message: '请勾选数据',
              type: 'warning',
            })
          }
        },
      }
      return {
        allDataSourceTable,
        state,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .all-data-source {
    .box-add {
      height: 64px;
      padding: 16px 0;
      background-color: #fff;
      display: flex;
      justify-content: flex-end;
    }

    .table-no-content {
      img {
        width: 266px;
      }
    }
  }
</style>
