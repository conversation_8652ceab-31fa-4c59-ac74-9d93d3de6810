<template>
  <section class="container-padding16 role-manage-list">
    <section class="cf-tools">
      <div class="row">
        <div class="col">
          <n-input
            class="nancalui-input-demo__mt"
            v-model="state.formInline.keywords"
            size="small"
            placeholder="请输入"
          >
            <template #prepend>
              <n-select
                v-model="state.formInline.keyType"
                size="sm"
                :options="state.typeList"
                @value-change="state.formInline.keywords = ''"
              />
            </template>
          </n-input>
        </div>
        <div class="search">
          <div class="search-btn" @click.prevent="startSearch">查询</div>
          <div class="search-btn reset" @click.prevent="resetFn">重置</div>
        </div>
      </div>
    </section>
    <section class="container-box nc-m-t-10">
      <section class="tools">
        <section class="commonForm">
          <n-button
            v-if="buttonAuthList.includes('personnel_roleManage_add')"
            code="personnel_roleManage_add"
            color="primary"
            size="sm"
            variant="solid"
            @click.prevent="goJump('roleManageAdd')"
          >
            <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
            增加角色
          </n-button>
        </section>
      </section>
      <section class="table" v-loading="state.loading">
        <CfTable
          actionWidth="220"
          isNeedIndex
          :table-head-titles="state.tableHeadTitles"
          :tableConfig="{
            data: state.tableList,
            rowKey: 'id',
          }"
          :paginationConfig="{
            total: state.pagination.total,
            pageSize: state.pagination.pageSize,
            currentPage: state.pagination.currentPage,
            onCurrentChange: (v) => {
              state.pagination.currentPage = v
              initTable(false)
            },
            onSizeChange: (v) => {
              state.pagination.pageSize = v
              initTable()
            },
          }"
        >
          <template #menuNames="{ row }">
            <span>{{ row.menuNames.length > 0 ? row.menuNames.join(',') : '' }}</span>
          </template>

          <template #editor="{ data: { row } }">
            <div class="edit-box">
              <n-button
                v-if="buttonAuthList.includes('personnel_roleManage_view')"
                code="personnel_roleManage_view"
                variant="text"
                color="primary"
                @click.prevent="goJump('roleManageDetail', row)"
                >查看
              </n-button>
              <n-button
                v-if="buttonAuthList.includes('personnel_roleManage_edit')"
                code="personnel_roleManage_edit"
                variant="text"
                color="primary"
                @click.prevent="goJump('roleManageEdit', row)"
                >编辑
              </n-button>
              <n-button
                v-if="buttonAuthList.includes('personnel_roleManage_delete')"
                code="personnel_roleManage_delete"
                variant="text"
                color="primary"
                @click.prevent="deleteClick(row)"
                >删除
              </n-button>
              <n-button
                v-if="buttonAuthList.includes('personnel_roleManage_view')"
                code="personnel_roleManage_view"
                variant="text"
                color="primary"
                @click.prevent="userList(row)"
                >用户清单
              </n-button>
            </div>
          </template>
        </CfTable>
      </section>
    </section>
    <userListModal :id="state.roleId" v-model="state.userListVisible" />
  </section>
</template>

<script>
  import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
  import { ElNotification } from 'element-plus'
  import { useRouter, useRoute } from 'vue-router'
  import api from '@/api/index'
  import { useStore } from 'vuex'
  import userListModal from './userListModal.vue'
  export default {
    title: 'roleList',
    components: { userListModal },
    setup() {
      const store = useStore()
      //按钮权限
      const { buttonAuthList, pagination } = toRefs(store.state.user)
      const router = useRouter()
      const route = useRoute()

      const { proxy } = getCurrentInstance()
      const state = reactive({
        typeList: [
          {
            name: '角色名称',
            value: 'name',
          },
          {
            name: '角色编码',
            value: 'code',
          },
        ],
        tableHeadTitles: [
          // 必须为name 否则渲染不出表头
          { prop: 'id', name: 'ID', width: 100 },
          { prop: 'code', name: '角色编码', width: 200 },
          { prop: 'name', name: '角色名称', width: 200 },
          { prop: 'description', name: '说明' },
          { prop: 'menuNames', name: '权限', slot: 'menuNames' },
          { prop: 'userNum', name: '授权人数' },
        ],
        tableList: [],
        loading: false,
        pagination: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        formInline: {
          keywords: '',
          keyType: 'name',
        },
        searchData: {
          keywords: '',
          keyType: 'name',
        },
        rules: {
          name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        },
        roleId: '',
        userListVisible: false,
        routerName: 'roleManage',
      })
      const methods = {
        userList(row) {
          state.roleId = row.id
          state.userListVisible = true
        },
        setTableHeight() {
          state.tableHeight = document.body.offsetHeight - 160 - 50
        },
        // 权限名格式化
        functionNamesFormat(functionNames) {
          if (functionNames) {
            return functionNames.replace(/[\u005b\u005d]|'|"/g, '')
          } else {
            return
          }
        },
        resetFn() {
          state.pagination = {
            currentPage: 1,
            pageSize: 10,
            total: 0,
          }
          state.formInline = {
            keywords: '',
            keyType: 'name',
          }
          state.searchData = {
            keywords: '',
            keyType: 'name',
          }
          methods.initTable()
        },
        startSearch() {
          Object.keys(state.formInline).forEach((key) => {
            state.searchData[key] = state.formInline[key]
          })
          methods.initTable()
        },
        // 查询列表
        initTable(init = true) {
          state.pagination.currentPage = init ? 1 : state.pagination.currentPage
          let params = {
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            condition: {},
          }
          if (state.searchData.keywords) {
            params.condition[state.searchData.keyType] = state.searchData.keywords
          }
          state.loading = true
          //记录当前页码信息
          store.commit('user/SET_PAGINATION_INFO', {
            pagination: state.pagination,
            routerName: state.routerName,
          })
          api.system
            .roleList(params)
            .then((res) => {
              state.loading = false
              if (res.code === 'SUCCESS') {
                res.data.list.forEach((item) => {
                  item.functionNames = JSON.stringify(item.functionNames)
                })
                state.tableList = res.data.list
                state.pagination.total = res.data.total
              }
            })
            .catch(() => {
              state.tableList = []
              state.loading = false
            })
        },
        // 删除用户
        deleteClick(row) {
          if (row.id === 1 || row.id === 6) {
            ElNotification({
              title: '提示',
              message: '管理员或超级管理员不能被删除',
              type: 'warning',
            })

            return
          }
          proxy.$MessageBoxService.open({
            title: '提示',
            content: '此操作将永久删除该角色, 是否继续',
            save: () => {
              let params = {
                id: row.id,
              }
              api.system.deleteRole(params).then((res) => {
                if (res.code === 'SUCCESS') {
                  methods.initTable(state.tableList > 1 ? false : true)
                  ElNotification({
                    title: '提示',
                    message: '删除成功',
                    type: 'success',
                  })
                }
              })
            },
          })
        },
        goJump(name, row) {
          if (name === 'roleManageAdd') {
            router.push({ name: name, query: { status: 'add' } })
          } else if (name === 'roleManageEdit') {
            router.push({ name: name, query: { id: row.id, status: 'update' } })
          } else if (name === 'roleManageDetail') {
            router.push({ name: name, query: { id: row.id } })
          } else {
            router.push({ name: name })
          }
        },

        // 表格操作变化
        tablePageChange(data) {
          state.pagination.currentPage = data.currentPage
          state.pagination.pageSize = data.pageSize
          methods.initTable(false)
        },
        refreshData() {
          methods.initTable(false)
          // if (pagination && pagination.value && pagination.value[state.routerName]) {
          //   //回到上次的页码数和每页展示条数
          //   state.pagination.currentPage = pagination.value[state.routerName].currentPage
          //   state.pagination.pageSize = pagination.value[state.routerName].pageSize
          //   methods.initTable(false)
          // } else {
          //   methods.initTable(true)
          // }
        },
      }
      watch(
        () => route.query?.refresh,
        (newQuery, oldQuery) => {
          if (newQuery === 'true') {
            methods.refreshData()
            // 移除 refresh 参数
            const { refresh, ...newQuery } = route.query
            router.push({ name: route.name, query: newQuery })
          }
        },
        { immediate: true },
      )
      onMounted(() => {
        methods.refreshData()
      })

      return {
        state,
        buttonAuthList,

        ...methods,
      }
    },
  }
</script>

<style scoped lang="scss">
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container-padding16.role-manage-list {
    height: calc(100vh - 97px);
    overflow: hidden;
    .row {
      .nancalui-input {
        width: 300px !important;
      }
      :deep(.nancalui-input-slot__prepend) {
        width: 90px;
        padding: 0;
        .nancalui-select {
          margin-right: 0 !important;
          padding: 0 10px;
          border: none !important;
          .nancalui-select__selection:hover {
            box-shadow: unset;
          }
        }
      }
    }
    .container-box {
      height: calc(100% - 62px);
      border-radius: 4px;
    }

    .tools {
      position: relative;
      padding: 8px;
      background: #fff;
      .commonForm {
        .nancalui-button--sm {
          padding: 0 16px;
        }
      }
    }

    .table {
      height: calc(100% - 48px);
      padding: 0;
      overflow: auto;

      .functionNames {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .rule-button {
      text-align: right;
    }
  }
</style>
