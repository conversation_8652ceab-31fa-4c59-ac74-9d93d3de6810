<template>
  <section class="model-content">
    <div class="projectName">
      <moduleName :info="{ name: '标签设置' }" />
      <img class="close" src="@/assets/img/dev/icon-false-gray.png" @click="closeHandle(false)" />
    </div>
    <section class="content">
      <el-form ref="form" :model="relationInfo" :rules="relationRules" label-position="top">
        <el-form-item label="关系名称：" prop="relType">
          <el-input
            v-model="relationInfo.relType"
            size="small"
            maxlength="30"
            placeholder="请输入关系名称"
            clearable
          />
        </el-form-item>
      </el-form>
    </section>
    <div class="drawer__footer">
      <n-button size="sm" @click.stop.prevent="closeHandle(false)">关闭</n-button>
      <n-button size="sm" variant="solid" color="primary" @click.stop.prevent="saveFn"
        >保存</n-button
      >
    </div>
  </section>
</template>

<script>
  import moduleName from '@/components/ModuleName'
  import { reactive, toRefs } from 'vue'
  // import { checkCName } from '@/utils/validate'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'

  export default {
    components: { moduleName },
    props: {
      info: Object,
      mapData: Object,
    },
    emits: ['changeStatus'],
    setup(props, { emit }) {
      const state = reactive({
        relationInfo: { relType: '' },
        relationRules: {
          // relType: [{ validator: checkCName, trigger: 'blur', require: true }],
        },
        mapDataInfo: {},
      })
      state.relationInfo = props.info
      state.mapDataInfo = props.mapData
      const methods = {
        closeHandle(flag) {
          emit('changeStatus', flag)
        },
        saveFn() {
          // if (state.relationInfo.relType) {
          let params = {
            newRelType: state.relationInfo.relType || null,
          }
          state.mapDataInfo.relationship.forEach((val) => {
            if (val.nFrom === state.relationInfo.nFrom && val.nTo === state.relationInfo.nTo) {
              params.relationship = val
            }
          })
          api.assets.updateLineage(params).then((res) => {
            if (res.code === 'SUCCESS') {
              ElNotification({
                title: '提示',
                message: '保存成功',
                type: 'warning',
              })
              methods.closeHandle(true)
            }
          })
          // }
        },
      }
      const params = toRefs(state)
      return {
        ...params,
        ...methods,
      }
    },
  }
</script>

<style lang="scss" scoped>
  .model-content {
    position: relative;
    height: 100vh;
    padding-bottom: 60px;
    box-sizing: border-box;

    .projectName {
      background-color: #f7f8fa;
      padding: 8px 20px;
      position: relative;

      .close {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 20px;
        width: 16px;
        height: 16px;
        margin: auto;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      padding: 10px 20px;

      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 20px;

          &__label {
            font-weight: normal;
            padding: 0;
            line-height: 20px;
          }

          &__content {
            .el-select {
              width: 100%;
            }

            .el-input {
              .el-input__inner {
                background-color: #fff;
              }
            }

            .is-disabled {
              .el-input__inner {
                background-color: #f7f8fa;
              }
            }
          }
        }

        .el-form-item:last-of-type {
          margin-bottom: 0;
        }

        .is-required {
          .el-form-item__label {
            //margin-left: 8px;
          }
        }
      }

      .table {
        margin-top: 20px;
      }
    }

    .drawer__footer {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      padding: 12px 20px;
      text-align: center;
      border-top: 1px solid rgba(0, 0, 0, 0.09);
    }
  }
</style>
