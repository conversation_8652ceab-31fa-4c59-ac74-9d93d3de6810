<template>
  <section class="container">
    <section class="container-box">
      <div class="page-title">
        API接口-{{ name }}
        <div class="detail-back-box" @click.prevent="$router.go(-1)"> 返回 </div>
      </div>
      <div class="layout-box">
        <section class="container-box-table">
          <div class="table-box" v-loading="loading">
            <CfTable
              actionWidth="100"
              :table-head-titles="state.tableHeadTitles"
              :tableConfig="{
                data: state.tableList,
                rowKey: 'id',
              }"
              :paginationConfig="{
                total: state.pagination.total,
                pageSize: state.pagination.pageSize,
                currentPage: state.pagination.currentPage,
                onCurrentChange: (v) => {
                  state.pagination.currentPage = v
                  onSearch(false)
                },
                onSizeChange: (v) => {
                  state.pagination.pageSize = v
                  onSearch()
                },
              }"
            >
              <template #url="{ row }">
                <div class="urlBox">
                  <div class="urlContent" :title="row.url">
                    {{ row.url }}
                  </div>
                  <SvgIcon icon="icon-copy" class="icon-svg" @click="onCopyUrl(row.url)" />
                </div>
              </template>
              <template #editor="{ data: { row } }">
                <n-button variant="text" color="primary" @click="onView(row)">详情</n-button>
              </template>
            </CfTable>
          </div>
        </section>
      </div>
    </section>
  </section>
</template>
<script setup>
  import { onMounted, reactive, ref, getCurrentInstance} from 'vue'
  import api from '@/api/index'
  import { useRoute, useRouter } from 'vue-router'
  const { proxy } = getCurrentInstance()
  const route = useRoute()
  const router = useRouter()
  const { name,enName,id } = route.query

  const state = reactive({
    tableList: [],
    tableHeadTitles: [
      { prop: 'apiName', name: 'API名称' },
      { prop: 'modelTable', name: '数据表', width: 300 },
      { prop: 'categoryName', name: '分类' },
      { prop: 'url', name: 'API路径', slot: 'url' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
  })
  const loading = ref(false)

  // 查询
  const onSearch = (init = true) => {
    if (init) state.pagination.currentPage = 1
    loading.value = true
    api.documentManage
      .apiCatalogListPageSearch({
        pageNum: state.pagination.currentPage,
        pageSize: state.pagination.pageSize,
        condition: {
          enName,
          id,
        },
      })
      .then((res) => {
        state.tableList = res.data.list
        state.pagination.total = res.data.total
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
  // 预览api
  const onView = (row) => {
    router.push({
      name: 'apiDetail',
      query: { id: row.id },
    })
  }
  const onCopyUrl = (url) => {
    let input = document.createElement('input')
    input.value = url
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    proxy.$message.success('复制成功')
  }
  onMounted(() => {
    onSearch()
  })
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      flex: 1;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;
      gap: 10px;
      display: flex;
      flex-direction: column;
      .layout-box {
        flex: 1;
        display: flex;
        overflow: hidden;
      }
      &-table {
        width: 100%;
        height: 100%;
        gap: 10px;
        border-radius: 0px 0px 2px 2px;
        background: var(--100, #fff);
        overflow-y: auto;
        .page-top {
          display: flex;
          padding: 8px;
          justify-content: space-between;
          align-items: center;
          align-self: stretch;
        }
        .btn {
          display: flex;
          padding: 4px 16px;
          align-items: center;
          gap: 4px;
          border-radius: 2px;
          border: 1px solid #1e89ff;
          color: #1e89ff;
          font-size: 14px;

          &.active,
          &:hover {
            color: #fff;
            background: #1e89ff;
            box-shadow: none;
          }
        }
        .btns {
          display: flex;
          .line {
            display: flex;
            padding: 4px 16px;
            align-items: center;
            gap: 4px;
            border-radius: 2px;
            color: #1e89ff;
            font-size: 14px;
            cursor: pointer;
            &.active,
            &:hover {
              color: #006dea;
              box-shadow: none;
            }
          }
        }
        .table-box {
          height: 100%;
          padding: 16px 0;
          .urlBox {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .urlContent {
              width: calc(100% - 30px);
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              word-break: keep-all;
            }
            .icon-svg {
              width: 18px;
              height: 18px;
              cursor: pointer;
              &:hover {
                color: $themeBlue;
              }
            }
          }
        }
      }
    }
  }

  .page-title {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    height: 46px;
    padding: 0 16px;
    color: #1d2129;
    font-weight: bolder;
    font-size: 16px;
    background-color: #fff;
    border-radius: 2px;
    &[sub-label]::after {
      margin-left: 12px;
      content: attr(sub-label);
      display: inline-block;
      padding: 0px 4px;
      border-radius: 2px;
      line-height: 22px;
      border: 1px solid rgba(26, 164, 238, 0.4);
      background: rgba(26, 164, 238, 0.08);

      color: #1aa4ee;

      font-family: 'Source Han Sans CN';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      width: 4px;
      height: 18px;
      background: #1e89ff;
      content: '';
    }

    .detail-back-box {
      position: absolute;
      top: 0;
      right: 16px;
      bottom: 0;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 30px;
      margin: auto;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      color: #1d2129;
      font-weight: normal;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        color: #479dff;
        border: 1px solid #479dff;
      }
    }
  }

  .filter {
    display: flex;
    width: 912px;
    padding: 8px;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    &-item {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 16px;
      width: 100%;
      height: 32px;
      align-items: center;
      gap: 16px;
      align-self: stretch;
      > div {
        width: 100%;
      }
    }
    .btns {
      display: flex;
      padding: 8px 0px;
      align-items: flex-start;
      gap: 8px;
      .btn.active{
        box-shadow: none;
      }
    }
  }
</style>
<style lang="scss"></style>
