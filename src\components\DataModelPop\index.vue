<template>
  <el-dialog
    title="数据模型选择"
    v-model="dialogShow"
    class="commonDialog"
    width="1000px"
    :before-close="handleClose"
    append-to-body
  >
    <section class="tools">
      <section class="commonForm">
        <el-form inline :model="dataSearch" class="demo-form-inline">
          <el-form-item label="时间范围：">
            <el-date-picker
              v-model="dataSearch.timeRange"
              type="daterange"
              class="data-range-picker"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :shortcuts="shortcuts"
              clearable
              @change="onSearch"
            />
          </el-form-item>
          <el-form-item label="">
            <el-input
              v-model="dataSearch.keyWord"
              size="small"
              placeholder="关键字搜索"
              clearable
              @change="onSearch"
            >
              <template #append>
                <n-button @click.prevent="onSearch">
                  <n-popover class="item" content="搜索" trigger="hover" :position="['bottom']">
                    <SvgIcon class="icon_search" icon="icon_search" />
                  </n-popover>
                </n-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </section>
    </section>
    <div class="box">
      <div class="box-left">
        <PublicLeftTree :data="treeData" :treeAttrData="treeAttrData" @clickNode="clickNode">
          <template #pageTop>
            <div class="title">场景树</div>
          </template></PublicLeftTree
        >
      </div>
      <div class="box-right">
        <section class="table" v-loading="loading">
          <MyTable
            :tableData="tableData"
            :attrList="columns"
            :isCustomeSelect="false"
            :isAction="false"
            :isSelection="false"
            :exceptHeight="450"
            height="400px"
            :highlight="true"
            @selectRow="selectRow"
            @sizeChange="handleSizeChange"
            @pageChange="handleCurrentChange"
            :currentPage="pageConfig.page"
            :pageSize="pageConfig.pageSize"
            :total="pageConfig.total"
          >
            <template #checkboxAction="slotProps">
              <SvgIcon
                class="checkbox_icon"
                :icon="
                  slotProps.row.id === selectData?.id
                    ? 'icon_checkbox_checked'
                    : 'icon_checkbox_uncheck'
                "
                title="查看"
                @click.prevent="1"
              />
            </template>
          </MyTable>
        </section>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <n-button size="sm" variant="solid" color="primary" @click.prevent="confirmHandle"
          >保 存</n-button
        >
        <n-button size="sm" @click.prevent="handleClose">取 消</n-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import api from '@/api/index'
  // import { dataModel } from '@/api'
  import { ref, reactive, onMounted, defineEmits, withDefaults, defineProps, toRefs } from 'vue'
  import { shortcuts } from './type'
  import moment from 'moment'

  interface Props {
    type?: 1 | 2 // 1：iot 2：Oracle
    dataAnalysisModelType?: 1 | 2 // 1：是 2：否 数据分析结果
    isFill?: 1 | 0 // 1：是 0：否 补空
    modelId?: number // 回显
  }
  const props = withDefaults(defineProps<Props>(), {})
  // const { type, dataAnalysisModelType, isFill, modelId } = toRefs(props)
  const { modelId } = toRefs(props)

  const emit = defineEmits(['close', 'selectModelData'])
  const dialogShow = ref(true)
  const dataSearch = reactive({
    status: ['3'],
    keyWord: '',
    timeRange: undefined,
  })
  let layerId = ''

  const loading = ref<boolean>(false)
  const selectData = ref()
  const treeData = ref([])
  const treeAttrData = ref({
    // expand: false,
    // showControl: true,
    // showCustom: true,
    showLeftIcon: true,
    // parentControl: '123',
    // childControl: '123',
    dialogTitle: '目录',
    childTitle: '分类',
    isHideSearch: true,
  })
  const tableData = ref<any>([])

  const columns = reactive([
    {
      prop: 'index',
      width: 80,
      name: '序号',
      renders: (row, index) => {
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        return index + 1
      },
    },
    { prop: 'cnName', name: '中文名称' },
    { prop: 'name', name: '英文名称' },
    { prop: 'layerName', name: '模型层' },
    { prop: 'updateTime', name: '最后修改时间', width: 200 },
    // {
    //   prop: 'status',
    //   name: '状态',
    //   renders: (row, index) => {
    //     return row.status === 3 ? '发布' : '下架'
    //   },
    // },
  ])

  const pageConfig = reactive({
    page: 1,
    pageSize: 12,
    total: 0,
  })

  const onSearch = async () => {
    const { pageSize, page } = pageConfig
    const { keyWord, timeRange } = dataSearch

    const [startTime, endTime] = timeRange ?? []
    // const projectCode = 'test'
    const data = {
      pageNum: page,
      pageSize,
      condition: {
        name: keyWord || null,
        startTime: startTime ? moment(startTime).format('YYYY-M-D 00:00:00') : null,
        endTime: endTime ? moment(endTime).format('YYYY-M-D 23:59:59') : null,
        projectCode: layerId,
        // status: status.toString(),
        // projectCode,
        // type: type?.value,
        // dataAnalysisModelType: dataAnalysisModelType?.value,
        // isFill: isFill?.value,
      },
    }
    try {
      loading.value = true
      const res = await api.model.getDataModelList(data)
      const { total, list } = res.data
      tableData.value.length = 0
      tableData.value = list
      // eslint-disable-next-line require-atomic-updates
      pageConfig.total = total
    } finally {
      loading.value = false
    }
  }

  const queryTree = async () => {
    const res = await api.project.getMyProjectList()

    let _treeData: any = [
      {
        children: [],
        id: null,
        label: '全部',
        type: 'ROOT',
      },
    ]
    res.data.map((item) => (item.id = item.projectCode))

    _treeData[0].children = res.data
    treeData.value = _treeData
  }

  const clickNode = (data) => {
    if (data.id === 'ROOT') return
    layerId = data.id
    onSearch()
  }

  const handleSizeChange = (val: number) => {
    pageConfig.page = 1
    pageConfig.pageSize = val
    onSearch()
  }
  const handleCurrentChange = (val: number) => {
    pageConfig.page = val
    onSearch()
  }

  const selectRow = (val) => {
    selectData.value = val
  }

  // 关闭元数据选择弹窗
  const handleClose = () => {
    emit('close')
  }

  // 保存元数据
  const confirmHandle = () => {
    emit('selectModelData', selectData.value)
  }

  onMounted(() => {
    // 默认回显
    selectData.value = { id: modelId }
    onSearch()
    queryTree()
  })
</script>

<style scoped lang="scss">
  .box {
    display: flex;
    width: 100%;
    border-radius: 4px 0px 0px 4px;
    border: 1px solid #e1e1e1;

    &-left {
      // border-right: 1px solid #ebebeb;
    }
    &-right {
      flex: 1;
      height: 100%;

      overflow: hidden;
      .table {
        height: calc(100% - 171px);
      }
      .footer {
        padding: 31px 0;
        display: flex;
        justify-content: center;
      }
      .warning-title {
        color: #f5a623;
      }

      .tools {
        position: relative;
        height: 82px;
        padding: 20px 15px;
        background: #fff;
      }
      .table {
        padding: 0;
        .nancalui-button {
          margin: 0;
        }
      }
      .el-form--inline .el-form-item {
        margin-right: 0;
        margin-left: 10px;
      }

      .demo-form-inline {
        display: inline-block;

        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
</style>
