import Layout from '@/layout'

// 架构管理-人员管理
export default [
  {
    path: '/personnel',
    component: Layout,
    redirect: '/personnel/userManage',
    name: 'personnel',
    code: 'personnelManagement',
    meta: {
      title: '人员管理',
      code: 'personnelManagement',
      icon: 'person-mark',
      parentRouterName: 'systemManage',
      unShowProject: true,
    },
    children: [
      // 用户管理
      {
        path: 'userManage',
        name: 'userManage',
        code: 'userManage',
        redirect: '/personnel/userManage/list',
        component: () => import('@/views/system-manage/user-manage/index'),
        meta: {
          title: '用户管理',
          code: 'userManage',
          parentRouterName: 'systemManage',
          unShowProject: true,
        },
        children: [
          {
            path: 'list',
            hidden: true,
            name: 'userManageList',
            component: () => import('@/views/system-manage/user-manage/list'),
            meta: {
              title: '用户管理',
              unShowProject: true,
              activeMenu: '/personnel/userManage',
              parentRouterName: 'systemManage',
              code: 'userManage',
            },
          },
          {
            path: 'add',
            hidden: true,
            name: 'userManageAdd',
            component: () => import('@/views/system-manage/user-manage/add'),
            meta: {
              title: '新增用户',
              unShowProject: true,
              activeMenu: '/personnel/userManage',
              parentRouterName: 'systemManage',
            },
          },
          {
            path: 'eidt',
            hidden: true,
            name: 'userManageEdit',
            component: () => import('@/views/system-manage/user-manage/add'),
            meta: {
              title: '编辑用户',
              unShowProject: true,
              activeMenu: '/personnel/userManage',
              parentRouterName: 'systemManage',
            },
          },
          {
            path: 'detail',
            hidden: true,
            name: 'userManageDetail',
            component: () => import('@/views/system-manage/user-manage/detail'),
            meta: {
              title: '用户详情',
              unShowProject: true,
              activeMenu: '/personnel/userManage',
              parentRouterName: 'systemManage',
            },
          },
          {
            path: 'upload',
            hidden: true,
            name: 'userManageUpload',
            component: () => import('@/views/system-manage/user-manage/upload'),
            meta: {
              title: '上传用户',
              unShowProject: true,
              activeMenu: '/personnel/userManage',
              parentRouterName: 'systemManage',
            },
          },
        ],
      },
      // 角色管理
      {
        path: 'roleManage',
        name: 'roleManage',
        code: 'roleManage',
        redirect: '/personnel/roleManage/list',
        component: () => import('@/views/system-manage/role-manage/index'),
        meta: {
          title: '角色管理',
          code: 'roleManage',
          parentRouterName: 'systemManage',
          unShowProject: true,
        },
        // hidden: true,
        children: [
          {
            path: 'list',
            hidden: true,
            name: 'roleManageList',
            component: () => import('@/views/system-manage/role-manage/list'),
            meta: {
              title: '角色管理',
              keepAlive: true,
              activeMenu: '/personnel/roleManage',
              parentRouterName: 'systemManage',
              unShowProject: true,
              code: 'roleManage',
            },
          },
          {
            path: 'add',
            hidden: true,
            name: 'roleManageAdd',
            component: () => import('@/views/system-manage/role-manage/add'),
            meta: {
              title: '新增角色',
              activeMenu: '/personnel/roleManage',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
          {
            path: 'eidt',
            hidden: true,
            name: 'roleManageEdit',
            component: () => import('@/views/system-manage/role-manage/add'),
            meta: {
              title: '编辑角色',
              activeMenu: '/personnel/roleManage',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
          {
            path: 'detail',
            hidden: true,
            name: 'roleManageDetail',
            component: () => import('@/views/system-manage/role-manage/detail'),
            meta: {
              title: '角色详情',

              activeMenu: '/personnel/roleManage',
              parentRouterName: 'systemManage',
              unShowProject: true,
            },
          },
        ],
      },
    ],
  },
]
