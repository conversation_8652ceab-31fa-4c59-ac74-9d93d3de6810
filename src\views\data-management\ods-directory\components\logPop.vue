<template>
  <n-drawer v-model="state.showDrawer" title="" :size="720" :esc-key-closeable="false" :close-on-click-overlay="true"
    :before-close="closeFn" class="template-config-drawer">
    <div class="n-drawer-body">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">授权用户</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeFn" />
      </div>
      <div class="n-drawer-body-content">
        <n-input v-model="condition.jobNoOrName" placeholder="请输入用户名或工号" @keyup.enter="getAuthList" />
        <div style="margin-top: 10px; height: 500px;">
          <CfTable saveWidth v-loading="state.loading" ref="multipleTableRef" :tableConfig="{
            data: state.tableList,
            rowKey: 'modelId',
          }" :table-head-titles="[
            { prop: 'jobNo', name: '工号' },
            { prop: 'name', name: '姓名' },
            { prop: 'securityLevel', name: '密级', slot: 'securityLevel' },
          ]
            ">
            <template #securityLevel="{ row }">
              {{ state.confidentialityLevelMap[row.securityLevel] }}
            </template>
            <template #editor="{ row }">
              <el-button type="primary" link @click="cancelAuth(row)
                ">
                取消授权
              </el-button>
            </template>
          </CfTable>
        </div>
      </div>
      <div class="n-drawer-body-footer">
        <n-button @click="closeFn">取消</n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup>
import api from '@/api/index'
const state = reactive({
  log: '',
  showDrawer: false,
  tableList: [],
  confidentialityLevelMap: {
    'PUBLIC': '公开',
    'INTERIOR': '内部',
    'CONTROLLED': '受控',
    'SECRET': '秘密',
    'CONFIDENTIAL': '机密',
    'CORE': '核心',
  },
  searchData: {
    "condition": {
      "jobNoOrName": "",
      "modelId": 0
    },
    "pageNum": 1,
    "pageSize": 10,
    "sortConditions": [
      {
        "fieldName": "",
        "sort": ""
      }
    ]
  },
})
const { searchData } = toRefs(state)
const { condition } = toRefs(state.searchData)

const closeFn = () => {
  state.showDrawer = false
}
onMounted(() => {
})
// 取消授权
const cancelAuth = async ({ modelId, userId }) => {
  api.ods
    .cancelAuthorization({ userId, modelId })
    .then(() => {
      getAuthList()
    })
}
// 查询已授权用户列表
const getAuthList = async () => {
  api.ods
    .getAuthList(searchData.value)
    .then((rer) => {
      state.tableList = rer.data.list
    })
}
defineExpose({
  open: (data = {}) => {
    condition.value.modelId = data.modelId;
    getAuthList();
    state.showDrawer = true
  }
})
</script>
<style lang="scss" scoped>
.logPop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2001;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);

  .box {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 550px;
    background: #fff;
    border-radius: 2px;
    transform: translate(-50%, -50%);
  }

  .title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 13px 16px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bolder;
    font-size: 16px;

    .icon {
      color: #606266;
      font-size: 16px;
    }

    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 4px;
      height: 18px;
      margin: auto;
      background: #1e89ff;
      content: '';
    }
  }

  .log {
    box-sizing: border-box;
    height: 300px;
    margin: 10px;
    padding: 10px;
    overflow: auto;
    color: #1d2129;
    font-size: 12px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    border: 1px solid #e5e6eb;
    border-radius: 2px;
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;

    &-btn {
      width: 62px;
      height: 32px;
      color: #fff;
      font-size: 14px;
      line-height: 32px;
      text-align: center;
      background: #1e89ff;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: #6e9eff;
      }
    }
  }
}

.n-drawer-body {
  height: 100%;

  .n-drawer-body-content {
    .log {
      color: #1d2129;
      font-size: 12px;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    &::-webkit-scrollbar {
      width: 10px !important; // 横向滚动条
      height: 10px !important; // 纵向滚动条 必写
    }
  }

  .n-drawer-body-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 64px;
    padding: 0 16px;

    .footer-btn {
      width: 62px;
      height: 32px;
      color: #fff;
      font-size: 14px;
      line-height: 32px;
      text-align: center;
      background: #1e89ff;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background-color: #6e9eff;
      }
    }
  }
}
</style>
