# yy-admin 版本更新

## V0.1.2-2022/05/17

- 🚃 删除字节跳动的 UI 框架，改为更加亲民的 ElementPlus
- 🥵 优化提了 750 遍的 bug
- 🎸 调整了中文文档顺序，优先中文阅读
- 😈 重写 axios 封装，目前进度 100%
- 🐯 packages 里面暗藏精彩的项目，可自取

## V0.1.1-2022/01/28

- 🚃 咱的 mock 模拟的是真实登录流程，请访问`login`路由
- 🥵 修复好几卡车的 bug
- 🎸 搞了一个好看的 logo，svg 的~
- 😈 重写 axios 封装，目前进度 80%，敬请期待~
- 🐯 过年了，代码不写了，祝群里的水友们新年发发发~

## V0.1.0-2022/01/26

- 🎉 增加 vite-plugin 模块化配置,根据环境变量按需打包
- 📱 增加 mock 支持，并开启区分环境
- 🧩 统一管理全局变量`constant.ts`
- 🎎 调整了 store 的自动生成，以模块化的方式`npm run plop`
- 🧬 重写了文档，方便快速上手
- 🍡 改写了 axios，支持到处 request 或`get`，`post`
- 🎸 此次改版将更加符合大型项目的结构，下个版本会重点通过 mock，解决更加复杂的问题，例如登录，权限，鉴权，nav-menu...等。
