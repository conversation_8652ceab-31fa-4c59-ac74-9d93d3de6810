<template>
  <section v-loading="isLoad" class="tables">
    <div class="tables-left">
      <div class="table">
        <h3>{{ type }}编辑</h3>
        <div class="table-box">
          <div class="table-node"><span class="requier">*</span>{{ type }}名</div>
          <n-tooltip
            :content="data.tabName"
            :disabled="!data.tabName"
            position="tr"
            preventFocus="true"
          >
            <div class="table-node">
              <n-input :disabled="disabled" v-model="data.tabName" />
            </div>
          </n-tooltip>

          <div class="table-node">实体名</div>
          <div class="table-node">
            <n-tooltip :content="data.name" :disabled="!data.name" position="tr">
              <n-input :disabled="disabled" v-model="data.name" />
            </n-tooltip>
          </div>
          <div class="table-node">映射逻辑实体</div>
          <div class="table-node">
            <n-tooltip :content="mappingName" :disabled="!mappingName" position="tr">
              <n-select
                v-if="!disabled"
                v-model="data.mappingId"
                :loading="loading"
                remote
                allow-clear
                :filter="remoteMethods"
                placeholder="可输入实体名查询"
                @clear="remoteMethods"
                @toggle-change="remoteMethods"
                @value-change="onChange"
              >
                <n-option
                  v-for="item in mappingOptions"
                  :key="item.value"
                  :value="item.value"
                  :name="item.label"
                />
              </n-select>
              <n-input v-else disabled v-model="data.mappingName" />
            </n-tooltip>
          </div>

          <div class="table-node">DB Owner</div>
          <div class="table-node"><n-input :disabled="disabled" v-model="data.owner" /></div>
        </div>

        <h3>表设置</h3>
        <div class="table-box">
          <div class="table-node">标准化</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.standard" placeholder="请选择">
              <n-option name="继承" value="1" />
              <n-option name="对象" value="2" />
              <n-option name="非对象" value="3" />
            </n-select>
          </div>

          <div class="table-node">CharUsed</div>
          <div class="table-node">
            <n-select :disabled="disabled" v-model="data.charUsed" placeholder="请选择">
              <n-option name="继承" value="1" />
              <n-option name="对象" value="2" />
              <n-option name="非对象" value="3" />
            </n-select>
          </div>
          <div class="table-node">所属</div>
          <div class="table-node">
            <n-tooltip :content="subjectName" :disabled="!subjectName">
              <n-select v-if="!disabled" v-model="data.subject" placeholder="请选择">
                <n-option
                  v-for="item in subjectList"
                  :name="item.subject"
                  :value="item.guid"
                  :key="item.guid"
                />
              </n-select>
              <n-input v-else disabled v-model="data.subjectName" />
            </n-tooltip>
          </div>
        </div>
      </div>
    </div>

    <div class="tables-right">
      <n-tabs :disabled="disabled" v-model="activeName" type="card">
        <n-tab title="定义" id="first">
          <n-textarea
            :disabled="disabled"
            v-model="data.definition"
            placeholder="请输入内容"
            autosize
          />
        </n-tab>
      </n-tabs>
    </div>
  </section>
</template>

<script>
  export default {
    props: {
      cell: {
        type: Object,
        dufault: {},
      },
      subjectList: {
        type: Object,
        dufault: [],
      },
    },
    data() {
      return {
        activeName: 'first', // 当前tabs+
        typeOpt: ['Derived', 'Pseudo', 'Additional', 'Drop', 'System'],
        data: {
          charUsed: '1', // charUsed
          dataProcesForm: '', // 数据处理形态
          definition: '', // 定义
          guid: '', // 节点id
          kind: '', // kind
          label: '', // 标签
          modGuid: '', // 模型guid
          modId: this.$route.query.modId, // 模型ID
          notepad: '', // 记事本
          owner: '', // db_owner
          specialMatters: '', // 特别事项
          stage: '1', // 阶段:1实用性,2本质
          standard: '1', // 标准化:1继承,2对象,3非对象
          subject: '', // 主题域
          tabName: '', // 表名
          type: 'Derived', // 类型
        },
        mappingOptions: [],
        loading: false,
        isLoad: false,
      }
    },
    computed: {
      type() {
        return this.cell.data.type === 'VIEW' ? '视图' : '表'
      },
      // 视图与血缘不可编辑
      disabled() {
        // const { type, disableMove } = this.cell.data
        // return type === 'BLOOD' || type === 'VIEW' || disableMove
        return true
      },
      mappingName() {
        return (
          this.mappingOptions.find((item) => item.value === this.data.mappingId)?.label ||
          this.data.mappingName ||
          ''
        )
      },
      subjectName() {
        return (
          this.subjectList?.find((item) => item.guid === this.data.subject)?.subject ||
          this.data.subjectName ||
          ''
        )
      },
    },
    watch: {
      cell(val) {
        this.initData()
      },
    },
    async created() {
      await this.initData()
    },
    methods: {
      onChange() {
        this.data.mappingName = this.mappingOptions.find(
          (item) => item.value === this.data.mappingId,
        )?.label
        this.data.mappingOriginalName = this.mappingOptions.find(
          (item) => item.value === this.data.mappingId,
        )?.originalName
      },
      // 数据初始化
      async initData() {
        try {
          this.isLoad = true
          this.data = { ...this.cell.data.table }
          // 为了把以前的所属文本改为id
          const subject = this.subjectList?.find((item) => item.subject === this.data.subject)
          if (subject) {
            this.data.subject = subject.guid
          }
          if (!this.disabled) {
            if (this.data.mappingId) {
              await this.getEntityById(this.data.mappingId)
            } else {
              await this.remoteMethods()
            }
          }
          // 如果关联的实体找不到，则清空
          if (!this.mappingOptions.length) {
            this.data.mappingId = ''
          }
          this.isLoad = false
        } catch (e) {
          this.isLoad = false
        }
      },
      // 实体搜索
      async remoteMethods(query) {
        try {
          if (!query) {
            return
          }
          if (typeof query === 'boolean' && query) {
            query = ''
          }
          const mapList = this.$store.state.app.mapList
          if (mapList?.length === 0 || query) {
            this.loading = true
            const res = await this.$api.dataModel.entityList({ name: query, kind: 'logic' })
            if (!query) {
              this.$store.dispatch('app/setMapList', res.data)
            }
            this.mappingOptions = res.data.map((item) => {
              return {
                value: item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
            this.loading = false
          } else {
            this.mappingOptions = mapList.map((item) => {
              return {
                value: item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
          }
        } catch (e) {
          console.log(e)
          this.loading = false
        }
      },
      async getEntityById(id) {
        try {
          const mapList = this.$store.state.app.mapList
          const curMap = mapList.find((item) => item.guid === id)
          if (!curMap) {
            this.loading = true
            const res = await this.$api.dataModel.entityList({ mappingId: id, kind: 'logic' })
            this.mappingOptions = res.data.map((item) => {
              return {
                value: item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
            this.loading = false
            if (mapList.length > 50) {
              this.$store.dispatch('app/setMapList', mapList.concat(res.data))
            }
          } else {
            this.mappingOptions = mapList.map((item) => {
              return {
                value: item.guid,
                label: item.name,
                originalName: item.originalName,
              }
            })
          }
        } catch (e) {
          console.log(e)
          this.loading = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tables {
    display: flex;
    padding: 14px;

    &-left {
      width: 350px;
      height: 420px;
      margin-right: 14px;
      overflow: auto;
      border: 1px solid #cfcfcf;
      .requier {
        margin-right: 4px;
        color: #f56c6c;
      }
      .nancalui-input,
      .nancalui-select {
        height: 26px !important;
      }
    }
    &-right {
      width: calc(100% - 304px);

      :deep(.el-tabs) {
        height: 100%;

        .el-tabs__content {
          height: calc(100% - 31px);

          & > div {
            height: 100%;
          }
          .el-textarea {
            height: 100%;

            &__inner {
              height: 100%;
              background: #fff;
              border: none;
            }
          }
        }
      }
    }
  }
</style>
