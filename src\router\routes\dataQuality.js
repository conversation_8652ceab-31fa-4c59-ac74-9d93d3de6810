import Layout from '@/layout'

// 功能模块-首页
export default [
  {
    path: '/dataQuality',
    component: Layout,
    redirect: '/dataQuality/ruleTemplate',
    name: 'dataQuality',
    code: 'qualityManage',
    meta: {
      title: '数据质量',
      icon: 'icon-quality',
      parentRouterName: 'assetsManage',
      code: 'qualityManage',
    },
    children: [
      // 规则模板
      {
        path: 'ruleTemplate',
        name: 'ruleTemplate',
        code: 'ruleTemplate',
        hidden: true,
        redirect: '/dataQuality/ruleTemplate/list',
        meta: { title: '规则模板', parentRouterName: 'assetsManage', code: 'ruleTemplate' },
        component: () => import('@/views/data-quality/rule-template/index'),
        children: [
          {
            path: 'list',
            name: 'ruleTemplateList',
            hidden: true,
            component: () => import('@/views/data-quality/rule-template/list'),
            meta: { title: '规则模板', parentRouterName: 'assetsManage', code: 'ruleTemplate' },
          },
        ],
      },
      // 质量规则
      {
        path: 'index',
        name: 'qualityRule',
        code: 'qualityRule',
        hidden: true,
        redirect: '/dataQuality/index/list',
        meta: { title: '质量规则', parentRouterName: 'assetsManage', code: 'qualityRule' },
        component: () => import('@/views/data-quality/quality-rule/index'),
        children: [
          {
            path: 'list',
            name: 'DataQualityRule',
            hidden: true,
            component: () => import('@/views/data-quality/quality-rule/ruleList'),
            meta: { title: '质量规则', parentRouterName: 'assetsManage', code: 'qualityRule' },
          },
          {
            path: 'add',
            name: 'DataQualityRuleAdd',
            hidden: true,
            component: () => import('@/views/data-quality/quality-rule/add'),
            meta: { title: '新增质量规则', parentRouterName: 'assetsManage' },
          },
          {
            path: 'edit',
            name: 'DataQualityRuleEdit',
            hidden: true,
            component: () => import('@/views/data-quality/quality-rule/add'),
            meta: { title: '编辑质量规则', parentRouterName: 'assetsManage' },
          },
          {
            path: 'see',
            name: 'DataQualityRuleSee',
            hidden: true,
            component: () => import('@/views/data-quality/quality-rule/add'),
            meta: { title: '查看质量规则', parentRouterName: 'assetsManage' },
          },
        ],
      },
      // 质量任务
      {
        path: 'task',
        name: 'qualityTask',
        code: 'qualityTask',
        hidden: true,
        redirect: '/dataQuality/task/list',
        meta: { title: '质量任务', parentRouterName: 'assetsManage', code: 'qualityTask' },
        component: () => import('@/views/data-quality/quality-task/index'),
        children: [
          {
            path: 'list',
            name: 'DataQualityTaskList',
            hidden: true,
            component: () => import('@/views/data-quality/quality-task/list'),
            meta: { title: '质量任务', parentRouterName: 'assetsManage', code: 'qualityTask' },
          },
          {
            path: 'config',
            name: 'DataQualityTaskConfig',
            hidden: true,
            component: () => import('@/views/data-quality/quality-task/config'),
            meta: {
              title: '配置质量任务',
              icon: '',
              keepAlive: true,
              parentRouterName: 'assetsManage',
            },
          },
          {
            path: 'detail',
            name: 'DataQualityTaskDetail',
            hidden: true,
            component: () => import('@/views/data-quality/quality-task/detail'),
            meta: {
              title: '查看质量任务',
              icon: '',
              parentRouterName: 'assetsManage',
            },
          },
        ],
      },
      // 运行记录
      {
        path: 'operatingLog',
        name: 'operatingLog',
        code: 'operatingLog',
        hidden: true,
        redirect: '/dataQuality/operatingLog/list',
        meta: { title: '运行记录', parentRouterName: 'assetsManage', code: 'operatingLog' },
        component: () => import('@/views/data-quality/operating-log/index'),
        children: [
          {
            path: 'list',
            name: 'operatingLogList',
            hidden: true,
            component: () => import('@/views/data-quality/operating-log/list'),
            meta: { title: '运行记录', parentRouterName: 'assetsManage', code: 'operatingLog' },
          },
        ],
      },
      // 质量报告
      {
        path: 'qualityReport',
        name: 'qualityReport',
        code: 'qualityReport',
        component: () => import('@/views/data-quality/quality-report/list'),
        meta: {
          title: '质量报告',
          parentRouterName: 'assetsManage',
          code: 'qualityReport',
        },
      },
    ],
  },
]
