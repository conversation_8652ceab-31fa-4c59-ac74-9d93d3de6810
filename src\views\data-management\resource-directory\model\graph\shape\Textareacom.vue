<template>
  <textarea
    v-model="data.value"
    placeholder="请输入"
    style="
      border: 1px solid #333;
      outline: none;
      width: 100%;
      height: 100%;
      resize: none;
      border-radius: 8px;
    "
    @input="changeValue"
  ></textarea>
</template>

<script>
  export default {
    name: 'TextAreacom',
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        data: {
          value: '',
          id: Math.random(),
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.data = node.getData()
      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.data = current
      })
    },
    methods: {
      changeValue() {
        this.getNode().setData(this.data)
      },
    },
  }
</script>

<style scoped lang="scss"></style>
