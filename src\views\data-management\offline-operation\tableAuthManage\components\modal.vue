<template>
    <n-modal class="file-view-dialog" v-model="state.dialogVisible" :draggable="false" :close-on-click-modal="false"
        :before-close="handleClose" title="选择用户">
        <div class="content">
            <div class="nancalui-transfer">
                <div class="nancalui-transfer-view nancalui-transfer__view-source">
                    <div class="nancalui-transfer-view__header"><span class="nancalui-transfer-view__header-title">
                            <div class="nancalui-transfer-view__header-title-checkbox">
                                <n-checkbox v-model="state.leftAllChecked" :isShowTitle="false" label="选项合集"
                                    @change="leftAllChecked" />
                            </div><span class="nancalui-transfer-view__header-count">(<span
                                    class="nancalui-transfer-view__header-count-num">{{ state.selectedUserList.length ?
                                        `${state.selectedUserList.length}/` : '' }}{{ leftList.length
                                    }}</span><span class="nancalui-transfer-view__header-count-unit">项</span>)</span>
                        </span></div>
                    <div class="nancalui-transfer-view__search">
                        <n-input v-model="searchForm.condition.name" placeholder="请输入用户名称" @change="search">
                            <template #suffix>
                                <n-icon name="search" style="font-size: inherit;" />
                            </template>
                        </n-input>
                    </div>
                    <div class="nancalui-transfer-view__body">
                        <n-checkbox-group v-model="state.selectedUserList" class="nancalui-transfer-view__body-list"
                            v-infinite-scroll="load">
                            <div v-for="(item) in leftList" :key="i" class="nancalui-transfer-item">
                                <n-checkbox :label="item.label" :isShowTitle="false" :value="item.value" />
                            </div>
                        </n-checkbox-group>
                    </div>
                </div>
                <div class="nancalui-transfer__operations">
                    <n-button class="nancalui-transfer__operations-btn" :disabled="state.selectedUserList.length === 0"
                        variant="solid" icon="chevron-right" type="primary" @click="toRight">
                    </n-button>
                    <i />
                    <n-button class="nancalui-transfer__operations-btn"
                        :disabled="state.selectedTargetUserList.length === 0" variant="solid" icon="collapse"
                        type="primary" @click="toLeft">
                    </n-button>
                </div>
                <div class="nancalui-transfer-view nancalui-transfer__view-target">
                    <div class="nancalui-transfer-view__header"><span class="nancalui-transfer-view__header-title">
                            <n-checkbox v-model="state.rightAllChecked" :isShowTitle="false" label="选项合集"
                                @change="rightAllChecked" />

                            <span class="nancalui-transfer-view__header-count">(<span
                                    class="nancalui-transfer-view__header-count-num">{{
                                        state.selectedTargetUserList.length ?
                                            `${state.selectedTargetUserList.length}/` : '' }}{{ rightList.length
                                    }}</span><span class="nancalui-transfer-view__header-count-unit">项</span>)</span>
                        </span><!----></div>
                    <div class="nancalui-transfer-view__search">
                        <n-input v-model="state.filterValue" placeholder="请输入用户名称" @change="search">
                            <template #suffix>
                                <n-icon name="search" style="font-size: inherit;" />
                            </template>
                        </n-input>
                    </div>
                    <div class="nancalui-transfer-view__body">
                        <n-checkbox-group v-model="state.selectedTargetUserList"
                            class="nancalui-transfer-view__body-list">
                            <div v-for="(item) in rightList" :key="i" class="nancalui-transfer-item">
                                <n-checkbox :label="item.label" :isShowTitle="false" :value="item.value" />
                            </div>
                        </n-checkbox-group>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <n-modal-footer class="dialog-footer cenetr-footer">
                <n-button color="primary" variant="solid" @click.prevent="confirm">确 定</n-button>
                <n-button color="primary" @click.prevent="closeDialog">取 消</n-button>
            </n-modal-footer>
        </template>
    </n-modal>
</template>
<script setup>
import api from '@/api'
const state = reactive({
    modelName: '',
    dialogVisible: false,
    sourceUserList: [],
    searchUserList: [],
    userList: [],
    targetUserList: [],
    selectedUserList: [],
    selectedTargetUserList: [],
    // 回显的用户列表
    feedbackUserList: [],
    // 不清空的搜索用户列表
    searchUserList2: [],
    searchForm: {
        condition: { name: "", },
        pageNum: 1,
        pageSize: 10,
    }
})
const emit = defineEmits(['change'])
const { searchForm } = toRefs(state)
const handleClose = () => {
    state.dialogVisible = false
}
const closeDialog = () => {
    state.dialogVisible = false
}
// 查询当前有权限的用户ID
const getCurrentUser = () => {
    api.dataDev.tablePermissionUserList({ modelName: state.modelName }).then(res => {
        state.feedbackUserList = (res.data?.userIds || []).map(item => {
            return {
                label: item.name,
                value: item.userId,
            }
        })
        const feedbackIds = state.feedbackUserList.map(item => item.value)
        state.userList = state.userList.concat(feedbackIds)
        state.targetUserList = [].concat(state.targetUserList, feedbackIds)
    })
}
// 搜索
let oldSearchForm = JSON.parse(JSON.stringify(searchForm.value))
let oldSearchName = ''
const search = async () => {
    if (searchForm.value.condition.name === '') {
        searchForm.value = oldSearchForm
    } else {
        if (oldSearchName === searchForm.value.condition.name) {
            searchForm.value.pageNum++
            state.searchUserList = [].concat(state.searchUserList, await getUserList())
        } else {
            searchForm.value.pageNum = 1
            state.searchUserList = await getUserList()
        }
        // 深度去重
        state.searchUserList2 = Array.from(new Set([].concat(state.searchUserList2, state.searchUserList).map(item => JSON.stringify(item)))).map(item => JSON.parse(item))
        oldSearchName = searchForm.value.condition.name
    }
}
// 目标用户列表-计算属性
const rightList = computed(() => {
    // 搜索结果与原始结果合并显示
    return Array.from(new Set([].concat(state.feedbackUserList, state.sourceUserList, state.searchUserList2).map(item => JSON.stringify(item)))).map(item => JSON.parse(item))?.filter(item => state.targetUserList.includes(item.value) && (state?.filterValue?.length > 0 ? item.label.includes(state.filterValue) : true))
})
// 原始用户列表-计算属性
const leftList = computed(() => {
    // 搜索结果与原始结果合并显示
    // 在搜索时，只展示搜索结果
    const list = (searchForm.value?.condition?.name?.length > 0 ? [] : state.sourceUserList);
    return Array.from(new Set([].concat(state.feedbackUserList, list, state.searchUserList).map(item => JSON.stringify(item)))).map(item => JSON.parse(item))?.filter(item => !state.userList.includes(item.value))
})
// 获取用户列表
const getUserList = async () => {
    const res = await api.system.userList(searchForm.value)
    if (res.success) {
        const list = (res?.data?.list || []).map(item => {
            return {
                label: item.name,
                value: item.id,
            }
        })
        return list
    }
    return []
}
const toRight = () => {
    state.userList = state.userList.concat(state.selectedUserList)
    state.targetUserList = [].concat(state.targetUserList, state.selectedUserList)
    state.selectedUserList = []
    state.rightAllChecked = false
    state.leftAllChecked = false
}
const toLeft = () => {
    state.userList = state.userList?.filter(item => !state.selectedTargetUserList.includes(item))
    state.targetUserList = state.targetUserList.filter(item => !state.selectedTargetUserList.includes(item))
    state.rightAllChecked = false
    state.leftAllChecked = false
    state.selectedTargetUserList = []
}
// 全选
const leftAllChecked = (val) => {
    state.selectedUserList = val ? leftList.value.map(item => item.value) : []
}
const rightAllChecked = (val) => {
    state.selectedTargetUserList = val ? state.targetUserList : []
}
// 确认授权
const confirm = () => {
    const params = {
        modelName: state.modelName,
        userIds: state.targetUserList,
    }
    api.dataDev.tablePermissionAddUserList(params).then(res => {
        if (res.success) {
            ElMessage.success('授权成功')
            closeDialog()
        }
    }).finally(() => {
        emit('change')
    })
}
// 加载更多
const load = async () => {
    if (searchForm.value.condition.name?.length > 0) {
        search()
        return
    }
    state.sourceUserList = [].concat(state.sourceUserList, await getUserList())
    searchForm.value.pageNum++
    oldSearchForm = JSON.parse(JSON.stringify(searchForm.value))
}
defineExpose({
    open: (modelName) => {
        state.modelName = modelName
        state.selectedUserList = []
        state.dialogVisible = true
        getCurrentUser()
    }
})

</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    position: relative;

}
</style>
