export const modTypeList = {
  1: 'concept',
  2: 'logic',
  3: 'physics',
}
export const arrowsImageBase64Left =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAcCAYAAABlL09dAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEySURBVEhLzdYxSgNRFEbhCUJQUKKllY22FrqDuAAbU7kAFdxBtLVV16ArSJHUNmovZgFpbBQbQRDUnDMYGKcz9wb84WOYQO57M/PenSlmlbmfoznDK57Ks6Ts4hsfOEF1wFDmcY4vOMAdNpCWNkaw+BuOkJYWrmBxDbCKtOzhGRZ/QQdpcaZ9TGZ/jWWk5QDec4v7DHaQlnXcwuKunku4mlLi+u7C9e4AQ2wjLVt4hMUd5BS/NlUD/rBZnv0tTRxjH9Z5gLu2hzIrcOQsCyhHWsKNJ1NmEWvwCj5hvXdMnXp/uUe4v9QfXrgj+meLpC43N4jt1IJe/gXCG+QQqVu63oRsp+EmVG2bHsNt00Zve5zM0hmHG3391eS9DcWnaxusvkxdBeHM7PVv/GBxR/3nFMUYUldkMTA/6/sAAAAASUVORK5CYII='

export const commandList = [
  '1:M Optional',
  '1:M Mandatory',
  '1:M Indentify',
  'M:M',
  '1:1 Mandatory',
  '1:1 Indentify',
]
// 逻辑和物理数据类型集合
export const dataTypeName = {
  ORACLE: [
    ['变长字符串', 'VARCHAR2'],
    ['日期', 'DATE'],
    ['定长字符串', 'CHAR'],
    ['CLOB', 'CLOB'],
    ['整数', 'NUMBER'],
    ['小数', 'NUMBER'],
    ['时间戳', 'TIMESTAMP'],
  ],
  MYSQL: [
    ['变长字符串', 'varchar'],
    ['日期', 'datetime'],
    ['定长字符串', 'char'],
    ['CLOB', 'text'],
    ['整数', 'bigint'],
    ['小数', 'decimal'],
    ['时间戳', 'timestamp'],
  ],
  POSTGRESQL: [
    ['变长字符串', 'varchar'],
    ['日期', 'date'],
    ['定长字符串', 'char'],
    ['CLOB', 'text'],
    ['整数', 'bigint'],
    ['小数', 'Numeric'],
    ['时间戳', 'timestamp'],
  ],
  DM: [
    ['变长字符串', 'varchar'],
    ['日期', 'date'],
    ['定长字符串', 'char'],
    ['CLOB', 'clob'],
    ['整数', 'bigint'],
    ['小数', 'Numeric'],
    ['时间戳', 'timestamp'],
  ],
  SQLSERVER: [
    ['变长字符串', 'varchar'],
    ['日期', 'datetime'],
    ['定长字符串', 'char'],
    ['CLOB', 'text'],
    ['整数', 'bigint'],
    ['小数', 'decimal'],
    ['时间戳', 'timestamp'],
  ],
}
export const dataType = {
  ORACLE: [
    'NUMBER',
    'INTEGER',
    'VARCHAR2',
    'NVARCHAR2',
    'CHAR',
    'DATE',
    'TIMESTAMP',
    'CLOB',
    'NCLOB',
    'BLOB',
    'TIMESTAMP WITH TIME ZONE',
    'TIMESTAMP WITH LOCAL TIME ZONE',
    'INTERVAL YEAR TO MONTH',
    'INTERVAL DAY TO SECOND',
  ],
  MYSQL: [
    'binary',
    'bigint',
    'int',
    'double',
    'float',
    'integer',
    'decimal',
    'numeric',
    'char',
    'varchar',
    'text',
    'blob',
    'real',
    'datetime',
    'date',
    'time',
    'year',
    'timestamp',
    'tinyint',
    'tinytext',
    'longtext',
    'tinyblob',
    'mediumblob',
    'longblob',
    'json',
  ],
}
// 可以设置长度的数据类型
export const dataTypeCanLen = [
  'char',
  'varchar',
  'decimal',
  'bigint',
  'int',
  'tinyint',
  'double',
  'float',
  'VARCHAR2',
  'NVARCHAR2',
  'CHAR',
  'NUMBER',
]
// 可以设置长度的数据类型
export const dataTypeNameCanLen = ['定长字符串', '变长字符串', '整数', '小数']
// 可以设置小数点的数据类型
export const dataTypeNameCanScale = ['整数', '小数']
// 可以设置小数点的数据类型
export const dataTypeCanScale = ['decimal', 'bigint', 'int', 'tinyint', 'double', 'float', 'NUMBER']

export const layoutOps = {
  //100 以下：
  1: {
    type: 'gForce',
    preset: {
      type: 'radial',
      unitRadius: 300,
      strictRadial: false,
    },
    preventOverlap: true,
    nodeSize: 60,
    gravity: 5,
    animate: false,
    getCenter: (d, degree) => {
      if (degree === 0) return [width / 2, height / 2, 30]
      return [
        width / 2 + (d.domain % 5) * 1 * d.nums,
        height / 2 + Math.ceil(d.domain / 5) * 10,
        60,
      ]
    },
  },
  //300以下：
  2: {
    type: 'gForce',
    preset: {
      type: 'radial',
      unitRadius: 300,
      strictRadial: false,
    },
    preventOverlap: true,
    nodeSize: 60,
    gravity: 30,
    animate: false,
    getCenter: (d, degree) => {
      if (degree === 0) return [width / 2, height / 2, 10]
      return [
        width / 2 + (d.domain % 5) * 2 * d.nums,
        height / 2 + Math.ceil(d.domain / 3) * 80,
        30,
      ]
    },
  },
  //1000以下：
  3: {
    preset: {
      type: 'fruchterman',
      gpuEnabled: true,
      gravity: 10,
      width: 1000,
      height: 1000,
    },
    type: 'force2',
    linkDistance: 50,
    maxSpeed: 2000,
    damping: 0.3,
    interval: 0.05,
    minMovement: 5,
    getCenter: (d, degree) => {
      if (degree === 0) return [width / 2, height / 2, 10]
      return [
        width / 2 + (d.domain % 5) * 2 * d.nums,
        height / 2 + Math.ceil(d.domain / 3) * 80,
        30,
      ]
    },
  },
  // 3000：
  4: {
    preset: {
      type: 'fruchterman',
      gpuEnabled: true,
      gravity: 1,
      width: 1000,
      height: 1000,
    },
    type: 'force2',
    linkDistance: 50,
    maxSpeed: 2000,
    damping: 0.3,
    interval: 0.05,
    minMovement: 5,
  },
}
export const RES ={
  "kind": "ORACLE",
  "modId": 24050,
  "modGuid": "ZxvleUqQA8anTzlWlAUm",
  "version": null,
  "offMappingFlag": "N",
  "modType": null,
  "graphs": [
    {
      "id": 4782,
      "name": "测试模型1111",
      "kind": "ORACLE",
      "entityList": null,
      "localEntityList": null,
      "entityMappingList": null,
      "tabList": null,
      "localTabList": [
        {
          "id": 3666,
          "guid": "df2723f5-8a86-40bb-9ef7-27550e38fd52",
          "modId": 24050,
          "modGuid": "ZxvleUqQA8anTzlWlAUm",
          "tableType": "TABLE",
          "mappingId": null,
          "mappingName": null,
          "tabName": "表2",
          "name": null,
          "kind": "ORACLE",
          "owner": null,
          "stage": {
            "code": 1,
            "desc": "实用性"
          },
          "type": "Derived",
          "subject": "yO4TwahdguSJkFaTl3Nx",
          "subjectName": "主题域1",
          "charUsed": {
            "code": 1,
            "desc": "继承"
          },
          "standard": {
            "code": 1,
            "desc": "继承"
          },
          "label": null,
          "definition": null,
          "dataProcesForm": null,
          "specialMatters": null,
          "sharedEntity": "false",
          "sourceEntity": "true",
          "sharedReference": "false",
          "notepad": null,
          "createTime": "2024-11-12 14:17:48",
          "createBy": 1,
          "updateTime": "2024-11-12 14:17:48",
          "updateBy": 1,
          "subjectId": 24051,
          "columnList": [
            {
              "id": 42002,
              "mappingDictionId": 0,
              "mappingDictionName": null,
              "guid": "17313922541900.9568815708269249",
              "tabId": null,
              "modId": 24050,
              "tabGuid": "df2723f5-8a86-40bb-9ef7-27550e38fd52",
              "name": null,
              "col": "列名1",
              "notNull": true,
              "pk": false,
              "type": "Derived",
              "dataType": "CHAR",
              "length": 0,
              "scale": 0,
              "fk": true,
              "uk": "false",
              "defVal": null,
              "remarks": "表1(列名1)",
              "definition": null,
              "charUsed": {
                "code": 1,
                "desc": "继承"
              },
              "standard": {
                "code": 1,
                "desc": "继承"
              },
              "protect": "false",
              "privacyClassify": null,
              "infoSecurityLevel": null,
              "encryptFlag": "false",
              "encryption": null,
              "dataOwner": null,
              "createTime": "2024-11-12 14:17:48",
              "createBy": 1,
              "updateTime": "2024-11-12 14:17:48",
              "updateBy": 1,
              "sort": null,
              "subjectId": 24051,
              "parentTable": null,
              "parentColumn": null,
              "standardCol": null
            }
          ],
          "viewList": null,
          "columnModify": null,
          "demoList": null,
          "indexColumnInfoDOList": null,
          "storageSaveDTO": null,
          "originGuid": null,
          "fromModId": null,
          "subjectGuid": null,
          "changeSubjectFlag": null,
          "referFlag": "N",
          "referToNativeFlag": "N",
          "commentName": null,
          "componentKind": "2",
          "fieldList": null
        },
        {
          "id": 3667,
          "guid": "fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a",
          "modId": 24050,
          "modGuid": "ZxvleUqQA8anTzlWlAUm",
          "tableType": "TABLE",
          "mappingId": null,
          "mappingName": null,
          "tabName": "表1",
          "name": null,
          "kind": "ORACLE",
          "owner": null,
          "stage": {
            "code": 1,
            "desc": "实用性"
          },
          "type": "Derived",
          "subject": "yO4TwahdguSJkFaTl3Nx",
          "subjectName": "主题域1",
          "charUsed": {
            "code": 1,
            "desc": "继承"
          },
          "standard": {
            "code": 1,
            "desc": "继承"
          },
          "label": null,
          "definition": null,
          "dataProcesForm": null,
          "specialMatters": null,
          "sharedEntity": "false",
          "sourceEntity": "true",
          "sharedReference": "false",
          "notepad": null,
          "createTime": "2024-11-12 14:17:48",
          "createBy": 1,
          "updateTime": "2024-11-12 14:17:48",
          "updateBy": 1,
          "subjectId": 24051,
          "columnList": [
            {
              "id": 42003,
              "mappingDictionId": 0,
              "mappingDictionName": null,
              "guid": "17313921654180.799768629695915",
              "tabId": null,
              "modId": 24050,
              "tabGuid": "fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a",
              "name": null,
              "col": "列名1",
              "notNull": true,
              "pk": true,
              "type": "Derived",
              "dataType": "CHAR",
              "length": 10,
              "scale": 0,
              "fk": false,
              "uk": "false",
              "defVal": null,
              "remarks": null,
              "definition": null,
              "charUsed": {
                "code": 1,
                "desc": "继承"
              },
              "standard": {
                "code": 1,
                "desc": "继承"
              },
              "protect": "false",
              "privacyClassify": null,
              "infoSecurityLevel": null,
              "encryptFlag": "false",
              "encryption": null,
              "dataOwner": null,
              "createTime": "2024-11-12 14:17:48",
              "createBy": 1,
              "updateTime": "2024-11-12 14:17:48",
              "updateBy": 1,
              "sort": null,
              "subjectId": 24051,
              "parentTable": null,
              "parentColumn": null,
              "standardCol": null
            },
            {
              "id": 42004,
              "mappingDictionId": 0,
              "mappingDictionName": null,
              "guid": "17313921694590.4052191677852237",
              "tabId": null,
              "modId": 24050,
              "tabGuid": "fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a",
              "name": null,
              "col": "列名2",
              "notNull": false,
              "pk": false,
              "type": "Derived",
              "dataType": "CHAR",
              "length": 10,
              "scale": 0,
              "fk": false,
              "uk": "false",
              "defVal": null,
              "remarks": null,
              "definition": null,
              "charUsed": {
                "code": 1,
                "desc": "继承"
              },
              "standard": {
                "code": 1,
                "desc": "继承"
              },
              "protect": "false",
              "privacyClassify": null,
              "infoSecurityLevel": null,
              "encryptFlag": "false",
              "encryption": null,
              "dataOwner": null,
              "createTime": "2024-11-12 14:17:48",
              "createBy": 1,
              "updateTime": "2024-11-12 14:17:48",
              "updateBy": 1,
              "sort": null,
              "subjectId": 24051,
              "parentTable": null,
              "parentColumn": null,
              "standardCol": null
            },
            {
              "id": 42005,
              "mappingDictionId": 0,
              "mappingDictionName": null,
              "guid": "17313921696300.9576174267672388",
              "tabId": null,
              "modId": 24050,
              "tabGuid": "fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a",
              "name": null,
              "col": "列名3",
              "notNull": false,
              "pk": false,
              "type": "Derived",
              "dataType": "CHAR",
              "length": 10,
              "scale": 0,
              "fk": false,
              "uk": "false",
              "defVal": null,
              "remarks": null,
              "definition": null,
              "charUsed": {
                "code": 1,
                "desc": "继承"
              },
              "standard": {
                "code": 1,
                "desc": "继承"
              },
              "protect": "false",
              "privacyClassify": null,
              "infoSecurityLevel": null,
              "encryptFlag": "false",
              "encryption": null,
              "dataOwner": null,
              "createTime": "2024-11-12 14:17:48",
              "createBy": 1,
              "updateTime": "2024-11-12 14:17:48",
              "updateBy": 1,
              "sort": null,
              "subjectId": 24051,
              "parentTable": null,
              "parentColumn": null,
              "standardCol": null
            }
          ],
          "viewList": null,
          "columnModify": null,
          "demoList": null,
          "indexColumnInfoDOList": null,
          "storageSaveDTO": null,
          "originGuid": null,
          "fromModId": null,
          "subjectGuid": null,
          "changeSubjectFlag": null,
          "referFlag": "N",
          "referToNativeFlag": "N",
          "commentName": null,
          "componentKind": "2",
          "fieldList": null
        }
      ],
      "tabMappingList": null,
      "relationList": null,
      "localRelationList": [
        {
          "id": 2805,
          "name": null,
          "guid": "fa0197f2-28e3-412b-be54-9219fa1d7520",
          "modId": 24050,
          "modGuid": "ZxvleUqQA8anTzlWlAUm",
          "modType": null,
          "type": "Normal",
          "lineType": {
            "code": 1,
            "desc": "1:M"
          },
          "keyUse": "false",
          "nonInheritanceProces": "false",
          "mainKey": "pk",
          "fatherChildKeys": [
            {
              "fatherKey": "列名1",
              "synchronization": false,
              "childKey": "列名1",
              "keyType": "1"
            }
          ],
          "parentKey": null,
          "subKey": null,
          "subjectId": 24051,
          "subjectGuid": null,
          "parentEntityId": "fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a",
          "parentEntityName": "表1",
          "parentRelativeVerb": null,
          "parentMust": "false",
          "subEntityId": "df2723f5-8a86-40bb-9ef7-27550e38fd52",
          "subEntityName": "表2",
          "parentAttrGuid": null,
          "subAttrGuid": null,
          "subRelativeVerb": null,
          "subMust": "true",
          "definition": null,
          "specialMatters": null,
          "notepad": null,
          "ruleInsert": null,
          "ruleUpdate": null,
          "ruleDelete": null,
          "fk": null,
          "fkId": null,
          "sourceEntity": "false",
          "sharedReference": "false",
          "originGuid": null
        }
      ],
      "relationMappingList": null,
      "subjectName": "主题域1",
      "subjectId": 24051,
      "subjectGuid": "yO4TwahdguSJkFaTl3Nx",
      "type": "SUBJECT",
      "guid": "yO4TwahdguSJkFaTl3Nx",
      "modId": 24050,
      "modGuid": "ZxvleUqQA8anTzlWlAUm",
      "modType": null,
      "json": "[{\"position\":{\"x\":-524,\"y\":-433},\"size\":{\"width\":200,\"height\":228},\"view\":\"vue-shape-view\",\"shape\":\"nc-table\",\"component\":{},\"ports\":{\"groups\":{\"top\":{\"position\":\"top\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"right\":{\"position\":\"right\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"bottom\":{\"position\":\"bottom\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"left\":{\"position\":\"left\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}}},\"items\":[{\"id\":\"top\",\"group\":\"top\"},{\"id\":\"right\",\"group\":\"right\"},{\"id\":\"bottom\",\"group\":\"bottom\"},{\"id\":\"left\",\"group\":\"left\"},{\"id\":\"top-1\",\"group\":\"top\"},{\"id\":\"right-1\",\"group\":\"right\"},{\"id\":\"bottom-1\",\"group\":\"bottom\"},{\"id\":\"left-1\",\"group\":\"left\"},{\"id\":\"top-2\",\"group\":\"top\"},{\"id\":\"right-2\",\"group\":\"right\"},{\"id\":\"bottom-2\",\"group\":\"bottom\"},{\"id\":\"left-2\",\"group\":\"left\"},{\"id\":\"top-3\",\"group\":\"top\"},{\"id\":\"right-3\",\"group\":\"right\"},{\"id\":\"bottom-3\",\"group\":\"bottom\"},{\"id\":\"left-3\",\"group\":\"left\"},{\"id\":\"top-4\",\"group\":\"top\"},{\"id\":\"right-4\",\"group\":\"right\"},{\"id\":\"bottom-4\",\"group\":\"bottom\"},{\"id\":\"left-4\",\"group\":\"left\"},{\"id\":\"top-5\",\"group\":\"top\"},{\"id\":\"right-5\",\"group\":\"right\"},{\"id\":\"bottom-5\",\"group\":\"bottom\"},{\"id\":\"left-5\",\"group\":\"left\"},{\"id\":\"top-6\",\"group\":\"top\"},{\"id\":\"right-6\",\"group\":\"right\"},{\"id\":\"bottom-6\",\"group\":\"bottom\"},{\"id\":\"left-6\",\"group\":\"left\"},{\"id\":\"top-7\",\"group\":\"top\"},{\"id\":\"right-7\",\"group\":\"right\"},{\"id\":\"bottom-7\",\"group\":\"bottom\"},{\"id\":\"left-7\",\"group\":\"left\"},{\"id\":\"top-8\",\"group\":\"top\"},{\"id\":\"right-8\",\"group\":\"right\"},{\"id\":\"bottom-8\",\"group\":\"bottom\"},{\"id\":\"left-8\",\"group\":\"left\"},{\"id\":\"top-9\",\"group\":\"top\"},{\"id\":\"right-9\",\"group\":\"right\"},{\"id\":\"bottom-9\",\"group\":\"bottom\"},{\"id\":\"left-9\",\"group\":\"left\"}]},\"id\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"data\":{\"table\":{\"charUsed\":\"1\",\"dataProcesForm\":\"\",\"definition\":\"\",\"guid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"kind\":\"ORACLE\",\"label\":\"\",\"modGuid\":\"ZxvleUqQA8anTzlWlAUm\",\"modId\":24050,\"notepad\":\"\",\"owner\":\"\",\"specialMatters\":\"\",\"stage\":\"1\",\"standard\":\"1\",\"subject\":\"yO4TwahdguSJkFaTl3Nx\",\"tabName\":\"表1\",\"mappingId\":\"\",\"type\":\"Derived\",\"tableType\":\"TABLE\",\"sharedEntity\":\"false\",\"sharedReference\":\"false\",\"sourceEntity\":\"false\"},\"col\":[{\"guid\":\"17313921654180.799768629695915\",\"mappingDictionId\":0,\"charUsed\":\"1\",\"col\":\"列名1\",\"dataOwner\":\"\",\"dataType\":\"CHAR\",\"defVal\":\"\",\"definition\":\"\",\"encryptFlag\":\"false\",\"encryption\":\"\",\"fk\":false,\"pk\":true,\"infoSecurityLevel\":\"\",\"length\":10,\"name\":\"\",\"notNull\":true,\"privacyClassify\":\"\",\"protect\":\"false\",\"remarks\":\"\",\"scale\":0,\"standard\":\"1\",\"type\":\"Derived\",\"uk\":false,\"tabGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"checked\":false},{\"guid\":\"17313921694590.4052191677852237\",\"mappingDictionId\":0,\"charUsed\":\"1\",\"col\":\"列名2\",\"dataOwner\":\"\",\"dataType\":\"CHAR\",\"defVal\":\"\",\"definition\":\"\",\"encryptFlag\":\"false\",\"encryption\":\"\",\"fk\":false,\"pk\":false,\"infoSecurityLevel\":\"\",\"length\":10,\"name\":\"\",\"notNull\":false,\"privacyClassify\":\"\",\"protect\":\"false\",\"remarks\":\"\",\"scale\":0,\"standard\":\"1\",\"tabGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"type\":\"Derived\",\"uk\":false,\"checked\":false},{\"guid\":\"17313921696300.9576174267672388\",\"mappingDictionId\":0,\"charUsed\":\"1\",\"col\":\"列名3\",\"dataOwner\":\"\",\"dataType\":\"CHAR\",\"defVal\":\"\",\"definition\":\"\",\"encryptFlag\":\"false\",\"encryption\":\"\",\"fk\":false,\"pk\":false,\"infoSecurityLevel\":\"\",\"length\":10,\"name\":\"\",\"notNull\":false,\"privacyClassify\":\"\",\"protect\":\"false\",\"remarks\":\"\",\"scale\":0,\"standard\":\"1\",\"tabGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"type\":\"Derived\",\"uk\":false,\"checked\":false}],\"relation\":{},\"subjectGuid\":\"yO4TwahdguSJkFaTl3Nx\",\"modId\":24050,\"kind\":\"ORACLE\",\"guid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"indexColumnInfoDOList\":[{\"tabGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"tableName\":\"表1\",\"indexName\":\"表1_PK\",\"type\":\"pk\",\"uniqueness\":1,\"indexDetailDTOList\":[{\"columnGuId\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"descFlag\":0,\"col\":\"\",\"columnName\":\"列名1\",\"guid\":\"17313921654180.799768629695915\",\"indexOrder\":1}]},{\"uniqueness\":1,\"type\":\"other\",\"tableName\":\"表1\",\"indexName\":\"333\",\"tabGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"indexDetailDTOList\":[{\"columnGuId\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"descFlag\":0,\"col\":\"\",\"columnName\":\"列名2\",\"guid\":\"17313921694590.4052191677852237\",\"indexOrder\":1},{\"columnGuId\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"descFlag\":0,\"col\":\"\",\"columnName\":\"列名3\",\"guid\":\"17313921696300.9576174267672388\",\"indexOrder\":2}],\"index\":1}],\"script\":\"\",\"constraint\":{\"id\":1065,\"guid\":\"f75fcd13-9f11-4b37-9b49-814e6ce812be\",\"linkGuid\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"kind\":null,\"tableSpace\":null,\"stInitial\":null,\"stPctused\":3,\"stPctfree\":3,\"stNext\":null,\"stPctincrease\":null,\"stInitrans\":3,\"stMaxtrans\":null,\"stMinextents\":null,\"stMaxextents\":null,\"stMaxextentsUnlimited\":null,\"initialExtent\":null,\"nextExtent\":null,\"freeLists\":null,\"freeGroups\":null,\"bufferPool\":null,\"initialExtentUnit\":null,\"nextExtentUnit\":null,\"minExtentUnit\":null,\"maxExtentUnit\":null},\"demoList\":[{\"列名1\":\"1\",\"列名2\":\"1\",\"列名3\":\"1\"},{\"列名1\":\"2\",\"列名2\":\"2\",\"列名3\":\"2\"}],\"sql\":\"[\\\"DROP TABLE \\\\\\\"表1\\\\\\\";\\\",\\\"CREATE TABLE \\\\\\\"表1\\\\\\\" (\\\\\\\"列名1\\\\\\\" CHAR (10) NOT NULL, \\\\\\\"列名2\\\\\\\" CHAR (10), \\\\\\\"列名3\\\\\\\" CHAR (10), PRIMARY KEY (\\\\\\\"列名1\\\\\\\"));\\\",\\\"INSERT INTO \\\\\\\"表1\\\\\\\" (\\\\\\\"列名1\\\\\\\", \\\\\\\"列名2\\\\\\\", \\\\\\\"列名3\\\\\\\") VALUES ('1', '1', '1');\\\",\\\"INSERT INTO \\\\\\\"表1\\\\\\\" (\\\\\\\"列名1\\\\\\\", \\\\\\\"列名2\\\\\\\", \\\\\\\"列名3\\\\\\\") VALUES ('2', '2', '2');\\\"]\"},\"zIndex\":1},{\"position\":{\"x\":-161,\"y\":-433},\"size\":{\"width\":200,\"height\":228},\"view\":\"vue-shape-view\",\"shape\":\"nc-table\",\"component\":{},\"ports\":{\"groups\":{\"top\":{\"position\":\"top\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"right\":{\"position\":\"right\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"bottom\":{\"position\":\"bottom\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}},\"left\":{\"position\":\"left\",\"attrs\":{\"circle\":{\"r\":4,\"magnet\":true,\"stroke\":\"#5F95FF\",\"fill\":\"#fff\",\"style\":{\"visibility\":\"hidden\"}}}}},\"items\":[{\"id\":\"top\",\"group\":\"top\"},{\"id\":\"right\",\"group\":\"right\"},{\"id\":\"bottom\",\"group\":\"bottom\"},{\"id\":\"left\",\"group\":\"left\"},{\"id\":\"top-1\",\"group\":\"top\"},{\"id\":\"right-1\",\"group\":\"right\"},{\"id\":\"bottom-1\",\"group\":\"bottom\"},{\"id\":\"left-1\",\"group\":\"left\"},{\"id\":\"top-2\",\"group\":\"top\"},{\"id\":\"right-2\",\"group\":\"right\"},{\"id\":\"bottom-2\",\"group\":\"bottom\"},{\"id\":\"left-2\",\"group\":\"left\"},{\"id\":\"top-3\",\"group\":\"top\"},{\"id\":\"right-3\",\"group\":\"right\"},{\"id\":\"bottom-3\",\"group\":\"bottom\"},{\"id\":\"left-3\",\"group\":\"left\"},{\"id\":\"top-4\",\"group\":\"top\"},{\"id\":\"right-4\",\"group\":\"right\"},{\"id\":\"bottom-4\",\"group\":\"bottom\"},{\"id\":\"left-4\",\"group\":\"left\"},{\"id\":\"top-5\",\"group\":\"top\"},{\"id\":\"right-5\",\"group\":\"right\"},{\"id\":\"bottom-5\",\"group\":\"bottom\"},{\"id\":\"left-5\",\"group\":\"left\"},{\"id\":\"top-6\",\"group\":\"top\"},{\"id\":\"right-6\",\"group\":\"right\"},{\"id\":\"bottom-6\",\"group\":\"bottom\"},{\"id\":\"left-6\",\"group\":\"left\"},{\"id\":\"top-7\",\"group\":\"top\"},{\"id\":\"right-7\",\"group\":\"right\"},{\"id\":\"bottom-7\",\"group\":\"bottom\"},{\"id\":\"left-7\",\"group\":\"left\"},{\"id\":\"top-8\",\"group\":\"top\"},{\"id\":\"right-8\",\"group\":\"right\"},{\"id\":\"bottom-8\",\"group\":\"bottom\"},{\"id\":\"left-8\",\"group\":\"left\"},{\"id\":\"top-9\",\"group\":\"top\"},{\"id\":\"right-9\",\"group\":\"right\"},{\"id\":\"bottom-9\",\"group\":\"bottom\"},{\"id\":\"left-9\",\"group\":\"left\"}]},\"id\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\",\"zIndex\":2,\"data\":{\"table\":{\"charUsed\":\"1\",\"dataProcesForm\":\"\",\"definition\":\"\",\"guid\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\",\"kind\":\"ORACLE\",\"label\":\"\",\"modGuid\":\"ZxvleUqQA8anTzlWlAUm\",\"modId\":24050,\"notepad\":\"\",\"owner\":\"\",\"specialMatters\":\"\",\"stage\":\"1\",\"standard\":\"1\",\"subject\":\"yO4TwahdguSJkFaTl3Nx\",\"tabName\":\"表2\",\"mappingId\":\"\",\"type\":\"Derived\",\"tableType\":\"TABLE\",\"sharedEntity\":\"false\",\"sharedReference\":\"false\",\"sourceEntity\":\"false\"},\"col\":[{\"guid\":\"17313922541900.9568815708269249\",\"mappingDictionId\":0,\"charUsed\":\"1\",\"col\":\"列名1\",\"dataOwner\":\"\",\"dataType\":\"CHAR\",\"defVal\":\"\",\"definition\":\"\",\"encryptFlag\":\"false\",\"encryption\":\"\",\"fk\":true,\"pk\":false,\"infoSecurityLevel\":\"\",\"length\":0,\"name\":\"\",\"notNull\":true,\"privacyClassify\":\"\",\"protect\":\"false\",\"remarks\":\"表1(列名1)\",\"scale\":0,\"standard\":\"1\",\"type\":\"Derived\",\"uk\":false,\"tabGuid\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\"}],\"relation\":{},\"subjectGuid\":\"yO4TwahdguSJkFaTl3Nx\",\"modId\":24050,\"kind\":\"ORACLE\",\"guid\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\"}},{\"shape\":\"edge\",\"attrs\":{\"line\":{\"strokeWidth\":2,\"fill\":\"none\",\"stroke\":{\"type\":\"linearGradient\",\"stops\":[{\"offset\":\"0%\",\"color\":\"transparent\"},{\"offset\":\"49%\",\"color\":\"transparent\"},{\"offset\":\"49.01%\",\"color\":\"#3c4260\"},{\"offset\":\"100%\",\"color\":\"#3c4260\"}]},\"sourceMarker\":{\"tagName\":\"block\",\"size\":1},\"targetMarker\":{\"tagName\":\"image\",\"xlink:href\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAcCAYAAABlL09dAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEySURBVEhLzdYxSgNRFEbhCUJQUKKllY22FrqDuAAbU7kAFdxBtLVV16ArSJHUNmovZgFpbBQbQRDUnDMYGKcz9wb84WOYQO57M/PenSlmlbmfoznDK57Ks6Ts4hsfOEF1wFDmcY4vOMAdNpCWNkaw+BuOkJYWrmBxDbCKtOzhGRZ/QQdpcaZ9TGZ/jWWk5QDec4v7DHaQlnXcwuKunku4mlLi+u7C9e4AQ2wjLVt4hMUd5BS/NlUD/rBZnv0tTRxjH9Z5gLu2hzIrcOQsCyhHWsKNJ1NmEWvwCj5hvXdMnXp/uUe4v9QfXrgj+meLpC43N4jt1IJe/gXCG+QQqVu63oRsp+EmVG2bHsNt00Zve5zM0hmHG3391eS9DcWnaxusvkxdBeHM7PVv/GBxR/3nFMUYUldkMTA/6/sAAAAASUVORK5CYII=\",\"width\":20,\"height\":20,\"x\":-10,\"y\":-10}},\"line1\":{\"fill\":\"none\",\"strokeWidth\":2,\"strokeDasharray\":\"5 5\",\"stroke\":\"#3c4260\"},\"wrap\":{\"strokeWidth\":10}},\"id\":\"fa0197f2-28e3-412b-be54-9219fa1d7520\",\"defaultLabel\":{\"markup\":[{\"tagName\":\"rect\",\"selector\":\"body\"},{\"tagName\":\"text\",\"selector\":\"label\"}],\"attrs\":{\"label\":{\"fill\":\"#000\",\"fontSize\":14,\"textAnchor\":\"middle\",\"textVerticalAnchor\":\"middle\",\"pointerEvents\":\"none\"},\"body\":{\"ref\":\"label\",\"fill\":\"#fff\",\"stroke\":\"none\",\"strokeWidth\":2,\"rx\":4,\"ry\":4,\"refWidth\":\"140%\",\"refHeight\":\"140%\",\"refX\":\"-20%\",\"refY\":\"-20%\"}},\"position\":{\"distance\":0.5,\"angle\":0,\"options\":{\"absoluteDistance\":true,\"reverseDistance\":true}}},\"source\":{\"cell\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"port\":\"right-3\"},\"target\":{\"cell\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\",\"port\":\"left-3\"},\"zIndex\":3,\"data\":{\"parentEntityName\":\"表1\",\"parentEntityId\":\"fbae03d1-86b1-4f3a-92f8-c74c36c7ee0a\",\"subEntityName\":\"表2\",\"subEntityId\":\"df2723f5-8a86-40bb-9ef7-27550e38fd52\",\"modGuid\":\"ZxvleUqQA8anTzlWlAUm\",\"modId\":24050,\"lineType\":\"1\",\"command\":2,\"parentMust\":\"false\",\"subMust\":\"true\",\"keyUse\":\"false\",\"nonInheritanceProces\":\"false\",\"type\":\"Normal\",\"id\":0,\"fatherChildKeys\":[{\"fatherKey\":\"列名1\",\"childKey\":\"列名1\",\"keyType\":\"1\",\"synchronization\":false}],\"subjectGuid\":\"yO4TwahdguSJkFaTl3Nx\",\"mainKey\":\"pk\"},\"markup\":[{\"tagName\":\"path\",\"selector\":\"wrap\",\"groupSelector\":\"lines\",\"attrs\":{\"cursor\":\"pointer\",\"fill\":\"none\",\"stroke\":\"transparent\",\"strokeLinecap\":\"round\"}},{\"tagName\":\"path\",\"selector\":\"line\",\"groupSelector\":\"lines\"},{\"tagName\":\"path\",\"selector\":\"line1\",\"groupSelector\":\"lines\"}]}]",
      "createTime": "2024-11-12 14:17:48",
      "createBy": null,
      "updateTime": "2024-11-12 14:17:48",
      "updateBy": null
    }
  ],
  "processBusinessObjectList": null,
  "physicsMappingDataModel": null
}
