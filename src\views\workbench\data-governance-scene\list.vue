<template>
  <div class="container">
    <div class="content-box">
      <div class="content-box-title">
        <div class="search-box">
          <span class="label">项目名称：</span>
          <n-input
            v-model="state.formInline.keyword"
            placeholder="请输入项目名称"
            size="small"
            clearable
          />
          <span class="label">项目简介说明：</span>
          <n-input
            v-model="state.formInline.desc"
            placeholder="请输入项目简介说明"
            size="small"
            clearable
          />
        </div>
        <div class="btn-box">
          <n-button variant="solid" @click.prevent="searchClickFn">搜索</n-button>
          <n-button variant="solid" @click.prevent="addFn"
            ><SvgIcon class="icon-switch" icon="new-add" />新增场景</n-button
          >
        </div>
      </div>
      <div class="content-box-row">
        <div class="content-box-row-col right">
          <div class="tabs">
            <div
              :class="['tabs-item', state.tabsActive === 'project' ? 'active' : '']"
              @click="tabsActiveFn('project')"
            >
              项目列表
            </div>
            <div
              :class="['tabs-item', state.tabsActive === 'collect' ? 'active' : '']"
              @click="tabsActiveFn('collect')"
            >
              我的收藏
            </div>
          </div>
          <div
            v-for="(item, index) in state.projectList"
            :key="index"
            class="list"
            @click.prevent.stop="
              goJump('dataGovernanceSceneDetail', { id: item.id, code: item.code })
            "
          >
            <img v-if="item.posterUrl" class="pic" :src="item.posterUrl" />
            <div class="info">
              <div class="name">
                <span>{{ item.name }}</span>
                <div class="btn">
                  <n-button
                    v-if="state.tabsActive === 'project'"
                    @click.stop.prevent="collectFn(item)"
                  >
                    收藏
                  </n-button>

                  <n-button v-else @click.stop.prevent="cancelCollectFn(item)"> 取消收藏 </n-button>
                </div>
              </div>
              <div class="row">
                <div class="col"
                  >主管领导：<span :title="item.leaderInCharge">{{
                    item.leaderInCharge
                  }}</span></div
                >
                <div class="col"
                  >项目组长：<span :title="item.createByName">{{ item.createByName }}</span></div
                >
                <div class="col"
                  >创建时间：<span>{{ item.createTime }}</span></div
                >
                <div class="col"
                  >更新时间：<span>{{ item.updateTime }}</span></div
                >
              </div>
              <div class="desc">{{ item.introduction }}</div>
            </div>
          </div>
          <div v-if="state.projectList.length === 0" class="empty">
            <img class="empty-img" src="@/assets/table-no-content.png" />
            <p class="empty-text">暂无相关项目</p>
          </div>
        </div>
      </div>
      <div class="content-box-footer">
        <div class="title">我参与的</div>
        <!--        <n-tabs v-model="state.tabs" @active-tab-change="handleClick">-->
        <!--          <n-tab id="mark" :title="'标注任务(' + state.markNum + ')'" />-->
        <!--          <n-tab id="govern" :title="'治理任务(' + state.governNum + ')'" />-->
        <!--        </n-tabs>-->
        <div v-loading="state.loading" class="table-box">
          <CfTable
            :key="state.key"
            :isDisplayAction="false"
            :table-head-titles="state.tableHeadTitles"
            :paginationConfig="{
              total: state.pageInfo.total,
              pageSize: state.pageInfo.pageSize,
              currentPage: state.pageInfo.currentPage,
              onCurrentChange: (v) => {
                state.pageInfo.currentPage = v
                initTable(false, state.tabs)
              },
              onSizeChange: (v) => {
                state.pageInfo.pageSize = v
                initTable(true, state.tabs)
              },
            }"
            :tableConfig="{
              data: state.tableData.list,
              rowKey: 'id',
              'header-cell-style': {
                'font-weight': 'bolder',
              },
            }"
          >
          </CfTable>
        </div>
      </div>
    </div>
    <n-drawer
      v-model="state.showDrawer"
      title=""
      :size="560"
      :esc-key-closeable="false"
      :close-on-click-overlay="true"
      :before-close="
        () => {
          state.showDrawer = false
        }
      "
      class="template-config-drawer"
    >
      <div class="n-drawer-body">
        <div class="n-drawer-body-header">
          <div class="n-drawer-body-header-name">
            <div class="title">治理场景配置</div>
          </div>
          <SvgIcon class="close" icon="icon-close" @click.stop.prevent="state.showDrawer = false" />
        </div>
        <div class="n-drawer-body-content">
          <n-form
            ref="ruleForm"
            :data="state.ruleForm"
            :rules="state.rules"
            label-width="150px"
            label-align="start"
          >
            <n-form-item label="项目名称：" field="name">
              <n-input v-model="state.ruleForm.name" placeholder="请输入项目名称" maxlength="500" />
            </n-form-item>
            <n-form-item label="项目团长：" field="groupLeader">
              <n-select
                v-model="state.ruleForm.groupLeader"
                placeholder="请选择项目团长"
                filter
                allow-clear
                :options="state.teamLeader"
              />
            </n-form-item>
            <n-form-item label="牵头单位：" field="department">
              <n-input
                v-model="state.ruleForm.department"
                placeholder="请输入牵头单位"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="项目状态：" field="status">
              <n-select
                v-model="state.ruleForm.status"
                placeholder="请选择项目状态"
                filter
                allow-clear
                :options="state.teamStatus"
              />
            </n-form-item>
            <n-form-item label="项目简介：" field="introduction">
              <n-textarea
                v-model="state.ruleForm.introduction"
                placeholder="请输入项目简介"
                maxlength="200"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <n-form-item label="实施路径：" field="implementationDescription">
              <n-textarea
                v-model="state.ruleForm.implementationDescription"
                placeholder="请输入实施路径"
                maxlength="200"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <n-form-item label="数据集名称：" field="dataAssetName">
              <n-input
                v-model="state.ruleForm.dataAssetName"
                placeholder="请输入数据集名称"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="场景所属流程：" field="flow">
              <n-input
                v-model="state.ruleForm.flow"
                placeholder="请输入场景所属流程"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="数据范围：" field="dataRange">
              <n-input
                v-model="state.ruleForm.dataRange"
                placeholder="请输入数据范围"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="数据责任部门：" field="managementDepartment">
              <n-input
                v-model="state.ruleForm.managementDepartment"
                placeholder="请输入数据责任部门"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="数据使用部门：" field="dataUser">
              <n-input
                v-model="state.ruleForm.dataUser"
                placeholder="请输入数据使用部门"
                maxlength="500"
              />
            </n-form-item>
            <n-form-item label="当前现状及问题：" field="actuality">
              <n-textarea
                v-model="state.ruleForm.actuality"
                placeholder="请输入当前现状及问题"
                maxlength="200"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
            <n-form-item label="治理目标：" field="expectedResults">
              <n-textarea
                v-model="state.ruleForm.expectedResults"
                placeholder="请输入治理目标"
                maxlength="200"
                :autosize="{ minRows: 3 }"
                resize="both"
                show-count
              />
            </n-form-item>
          </n-form>
        </div>
        <div class="n-drawer-body-footer">
          <n-button @click.prevent="state.showDrawer = false">取 消</n-button>
          <n-button variant="solid" @click="state.showDrawer = false">确 定</n-button>
        </div>
      </div>
    </n-drawer>
  </div>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import api from '@/api/index'
  import { workbenchProjectList, workbenchProjectCollectList } from '@/api/dataManage.js'
  const store = useStore()
  const router = useRouter()

  const state = reactive({
    dateList: [{ date: 2025, checked: true }],
    key: 1,
    tabs: 'govern',
    loading: false,
    showDrawer: false,
    tabsActive: 'project',
    formInline: {
      keyword: '',
      desc: '',
    },
    ruleForm: {
      name: '',
      groupLeader: '',
      department: '',
      status: '',
      introduction: '',
      implementationDescription: '',
      dataAssetName: '',
      flow: '',
      dataRange: '',
      managementDepartment: '',
      dataUser: '',
      actuality: '',
      expectedResults: '',
    },
    rules: {
      name: [{ required: true, message: '请输入名称', trigger: 'change' }],
      groupLeader: [{ required: true, message: '请选择项目团长', trigger: 'change' }],
      department: [{ required: true, message: '请输入牵头单位', trigger: 'change' }],
      status: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
      introduction: [{ required: true, message: '请输入项目简介', trigger: 'change' }],
      implementationDescription: [{ required: true, message: '请输入实施路径', trigger: 'change' }],
      dataAssetName: [{ required: true, message: '请输入数据集名称', trigger: 'change' }],
      flow: [{ required: true, message: '请输入场景所属流程', trigger: 'change' }],
      dataRange: [{ required: true, message: '请输入数据范围', trigger: 'change' }],
      managementDepartment: [{ required: true, message: '请输入数据责任部门', trigger: 'change' }],
      dataUser: [{ required: true, message: '请输入数据使用部门', trigger: 'change' }],
      actuality: [{ required: true, message: '请输入当前现状及问题', trigger: 'change' }],
      expectedResults: [{ required: true, message: '请输入治理目标', trigger: 'change' }],
    },
    teamLeader: [
      { name: '张三（19001）', value: '19001' },
      { name: '李四（19002）', value: '19002' },
    ],
    teamStatus: [
      { name: '定场景', value: '1' },
      { name: '建规则', value: '2' },
      { name: '做治理', value: '3' },
      { name: '见成效', value: '4' },
    ],
    projectList: [],
    tableData: { list: [] },
    originalData: [],
    pageInfo: {
      total: 0,
      pageSize: 10,
      currentPage: 1,
    },
    tableHeadTitles: [
      { prop: 'projectName', name: '项目名称' },
      { prop: 'taskName', name: '任务名称' },
      { prop: 'problemName', name: '问题名称' },
      { prop: 'expectedCompletionTime', name: '预计完成时间' },
      // { prop: 'role', name: '角色' },
      { prop: 'progress', name: '当前进展' },
    ],
    markNum: 0,
    governNum: 0,
  })

  const handleClick = () => {
    initTable(true, state.tabs)
  }

  const searchClickFn = () => {
    console.log(state.formInline.keyword)
    if (state.formInline.keyword || state.formInline.desc) {
      if (state.formInline.keyword && state.formInline.desc) {
        state.projectList = state.originalData.filter(
          (val) =>
            String(val.name).includes(state.formInline.keyword) ||
            String(val?.introduction)?.includes(state.formInline.desc),
        )
      } else if (state.formInline.keyword) {
        state.projectList = state.originalData.filter((val) =>
          String(val.name).includes(state.formInline.keyword),
        )
      } else if (state.formInline.desc) {
        state.projectList = state.originalData.filter((val) =>
          String(val?.introduction).includes(state.formInline.desc),
        )
      }
    } else {
      state.projectList = [...state.originalData]
    }
  }

  const addFn = () => {
    state.ruleForm = {
      name: '',
      groupLeader: '',
      department: '',
      status: '',
      introduction: '',
      implementationDescription: '',
      dataAssetName: '',
      flow: '',
      dataRange: '',
      managementDepartment: '',
      dataUser: '',
      actuality: '',
      expectedResults: '',
    }
    state.showDrawer = true
  }

  // 切换tabs
  const tabsActiveFn = (type) => {
    state.tabsActive = type
    state.projectList = []
    getProjectList()
  }

  const initTable = async (init = false, type, isFirst = false) => {
    state.pageInfo.currentPage = init ? 1 : state.pageInfo.currentPage
    let data = {
      pageNum: state.pageInfo.currentPage,
      pageSize: state.pageInfo.pageSize,
      condition: {
        projectCode: null,
      },
    }
    state.loading = true
    let urlInterface = 'workbenchProjectTask'
    if (type === 'mark') {
      urlInterface = 'workbenchProjectCollectList'
    }
    await api.dataManagement[urlInterface](data)
      .then((res) => {
        if (res.success) {
          res.data.list.forEach((val) => {
            val.taskName = val.taskName || val.name
            val.completionTime = val.completionTime || val.expectedCompletionTime
            val.taskStage = val.taskStage || val.progress
          })
          state.tableData = res.data
          state.pageInfo.total = res.data.total
          if (type === 'mark') {
            state.markNum = res.data.total
          } else {
            state.governNum = res.data.total
          }
          state.key++
        }
        if (!isFirst) {
          state.loading = false
        }
      })
      .catch(() => {
        state.tableData = {}
        state.loading = false
      })
  }

  // 选择时间
  const checkLabel = (item) => {
    state.dateList.forEach((val) => {
      val.checked = false
    })
    item.checked = true
  }

  // 跳转路由
  const goJump = (name, query) => {
    if (query) {
      router.push({ name, query })
    } else {
      router.push({ name })
    }
  }

  // 获取项目列表
  const getProjectList = () => {
    const url = state.tabsActive === 'project' ? workbenchProjectList : workbenchProjectCollectList
    const params =
      state.tabsActive === 'project'
        ? {}
        : {
            condition: {
              projectCode: '',
              projectType: 'GOVERNANCE',
            },
            pageNum: 1,
            pageSize: 1000,
          }
    url(params).then((res) => {
      if (res.success) {
        const list = state.tabsActive === 'project' ? res.data : res.data.list

        state.projectList = list
        state.originalData = [...list]
      }
    })
  }

  // 收藏
  const collectFn = (item) => {
    api.dataGovernance
      .addProjectCollection({ projectCode: item.code, projectType: 'GOVERNANCE' })
      .then((res) => {
        if (res.success) {
          ElMessage.success('收藏成功')
        }
      })
  }

  // 取消收藏
  const cancelCollectFn = (item) => {
    api.dataGovernance
      .deleteProjectCollection({ projectCode: item.code, projectType: 'GOVERNANCE' })
      .then((res) => {
        if (res.success) {
          ElMessage.success('取消收藏成功')
          getProjectList()
        }
      })
  }

  onMounted(() => {
    getProjectList()
    initTable(true, 'govern')
    // initTable(true, 'govern', true).finally(() => {
    //   initTable(true, 'mark')
    // })
  })
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: calc(100vh - 96px);
    padding: 16px;
    .content-box {
      height: 100%;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 52px;
        margin-bottom: 10px;
        padding: 0 16px;
        color: #1d2129;
        font-weight: bolder;
        font-size: 18px;
        background-color: #fff;
        border-radius: 2px;
        .search-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #1d2129;
            font-size: 14px;
            font-weight: normal;
          }
          .nancalui-input {
            width: 260px;
            margin-right: 32px;
          }
        }
        .btn-box {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          :deep(.button-content) {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon {
              margin-right: 4px;
            }
          }
        }
      }
      &-row {
        height: 350px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        &-col {
          background-color: #fff;
          height: 100%;
          overflow-y: auto;
          border-radius: 2px;
          // padding: 0 12px;

          .tabs {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 16px;
            border-bottom: 1px solid #c5d0ea;
            padding: 10px 16px;
            box-sizing: border-box;

            &-item {
              font-size: 16px;
              font-weight: 500;
              border-bottom: 2px solid transparent;
              cursor: pointer;

              &:hover {
                color: #1e89ff;
                border-bottom: 2px solid #1e89ff;
              }

              &.active {
                color: #1e89ff;
                border-bottom: 2px solid #1e89ff;
              }
            }
          }
          &.right {
            width: 100%;
            .empty {
              width: 100%;
              height: calc(100% - 50px);
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;

              &-img {
                display: block;
                width: 140px;
                height: auto;
                margin: 0 auto;
              }

              &-text {
                margin-top: 20px;
                color: #999999;
                font-size: 12px;
                text-align: center;
              }
            }
          }

          .label {
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 16px;
            margin-top: 8px;
            &.checked,
            &:hover {
              border: 1px solid #1e89ff;
            }
            &-title {
              color: #1d2129;
              font-weight: bolder;
              font-size: 24px;
            }
            &-time {
              text-align: right;
              font-size: 14px;
            }
          }

          .list {
            border-bottom: 1px solid #c5d0ea;
            border-radius: 0;
            padding: 10px 12px;
            margin: 0 12px;
            cursor: pointer;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            margin-top: 8px;
            &:hover {
              border-bottom: 1px solid #fff;
              border-radius: 6px;
              box-shadow: 0 4px 16px -2px rgba(30, 47, 85, 0.15);
            }
            .pic {
              width: 90px;
              height: auto;
              flex-shrink: 0;
              margin-right: 16px;
            }
            .info {
              width: 100%;

              .name {
                display: flex;
                justify-content: space-between;
                align-items: center;

                span {
                  overflow: hidden;
                  color: #1d2129;
                  font-weight: bolder;
                  font-size: 20px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
              .row {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-top: 10px;
                .col {
                  width: calc(25% - 60px);
                  overflow: hidden;
                  color: #606266;
                  font-weight: normal;
                  font-size: 18px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  margin-right: 60px;
                  span {
                    font-weight: bolder;
                    color: #1d2129;
                  }
                }
              }
              .desc {
                margin-top: 10px;
                color: #909399;
                font-size: 18px;
              }
            }
          }
        }
      }
      &-footer {
        height: calc(100% - 422px);
        background-color: #fff;
        overflow-y: auto;
        border-radius: 2px;
        padding: 0 12px;
        margin-top: 10px;
      }
      .title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 36px;
        position: relative;
        padding: 0 12px;
        color: #1d2129;
        font-weight: 500;
        font-size: 16px;
        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 4px;
          height: 18px;
          margin: auto;
          background: #1e89ff;
          content: '';
        }
      }
      .table-box {
        margin-top: 10px;
        position: relative;
        height: calc(100% - 46px);
      }
    }
  }

  :deep(.common-table .page-mid .el-table tbody tr td > .cell > span) {
    text-overflow: inherit;
    -webkit-line-clamp: inherit;
  }
</style>
