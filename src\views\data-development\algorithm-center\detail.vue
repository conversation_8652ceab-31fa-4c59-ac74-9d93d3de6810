<template>
  <!-- 算法中心查看  -->
  <div>
    <div class="algorithm-center-detail container">
      <div class="add-box">
        <div class="page-title-box">
          <div class="page-title">
            <!-- <moduleName :info="{ name: '查看' }" /> -->
            <SvgIcon class="algorithm-see" icon="algorithm-see1" title="算法查看" /><span
              >查看</span
            >
            <div class="detail-back-box">
              <div class="detail-back-box-btn" @click.prevent="cancel"
                ><SvgIcon class="icon" icon="icon-canvas-cancel-g" />返回
              </div>
              <div class="detail-back-box-btn checked" @click.prevent="cancel"
                ><SvgIcon class="icon" icon="icon-close" />关闭
              </div>
            </div>
          </div>
        </div>

        <div class="box-content">
          <div class="box-content-inside scroll-bar-style">
            <div class="basic-box inside-box">
              <div class="box-title">
                <SvgIcon class="algorithm-base" icon="algorithm-base" title="基础信息" />
                <span>基础信息<i></i></span>
              </div>
              <n-form
                ref="ruleForm"
                class="base-form disabled-form"
                :data="state.ruleForm"
                label-width="100px"
                label-align="end"
                disabled
              >
                <div class="inline-box">
                  <n-form-item label="中文名称：">
                    <div class="form-detail-text">{{ state.ruleForm.cnName }}</div>
                  </n-form-item>
                  <n-form-item label="英文名称：">
                    <div class="form-detail-text">{{ state.ruleForm.name }}</div>
                  </n-form-item>
                  <n-form-item label="算法标签：">
                    <div class="form-detail-text tagList">
                      <template v-if="state.ruleForm?.tagList?.length > 0">
                        <div
                          v-for="(item, index) in state.ruleForm.tagList"
                          :key="index"
                          class="label"
                          :style="
                            'color:' +
                            item.color.split('_')[0] +
                            ';background-color:' +
                            item.color.split('_')[1] +
                            ';border-color:' +
                            (item.color.split('_').length > 2
                              ? item.color.split('_')[2]
                              : item.color.split('_')[0])
                          "
                          >{{ item.text }}</div
                        >
                      </template>
                      <span v-else>无</span>
                    </div>
                  </n-form-item>
                </div>
                <div class="inline-box">
                  <n-form-item label="创建时间：">
                    <div class="form-detail-text">{{ state.ruleForm.createTime }}</div>
                  </n-form-item>
                  <n-form-item label="创建人：">
                    <div class="form-detail-text">{{ state.ruleForm.createByName }}</div>
                  </n-form-item>
                  <n-form-item label="使用次数：">
                    <div class="form-detail-text">{{ state.ruleForm.bizUseCount }}</div>
                  </n-form-item>
                </div>
                <div class="inline-box">
                  <n-form-item label="使用项目数：">
                    <div class="form-detail-text">{{ state.ruleForm.projectUseCount }}</div>
                  </n-form-item>
                  <n-form-item label="最近引用时间：">
                    <div class="form-detail-text">{{
                      state.ruleForm.lastReferencedTime || '--'
                    }}</div>
                  </n-form-item>
                </div>

                <n-form-item label="描述信息：">
                  <div class="form-detail-text">{{ state.ruleForm.description }}</div>
                </n-form-item>
              </n-form>
            </div>
            <!-- 算法脚本 -->
            <div
              class="script-box inside-box"
              v-if="state.ruleForm.createMethod !== 'EXTERNAL_API'"
            >
              <div class="box-title">
                <SvgIcon class="algorithm-script" icon="algorithm-script" title="算法脚本" />
                <span>算法脚本<i></i></span>
              </div>
              <div class="textarea-box">
                <n-textarea
                  v-model="state.ruleForm.code"
                  disabled
                  resize="none"
                  class="script-textarea scroll-bar-style"
                  color="primary"
                  placeholder="当前仅支持Python、R、Jupyter、Java、C++语言......."
                />
              </div>
            </div>
            <div class="debug-box inside-box">
              <div class="box-title">
                <SvgIcon class="algorithm-debug" icon="algorithm-debug" title="算法脚本" />
                <span>调试信息<i></i></span>
              </div>
              <div class="tabs-box">
                <div class="debug-title">
                  <SvgIcon
                    v-if="state.expandTableStatus[0].expand"
                    icon="table-arrow-down"
                    title="参数信息收起"
                    @click="expandTable(0)"
                  />
                  <SvgIcon
                    v-else
                    class="noExpand"
                    icon="table-arrow-down"
                    title="参数信息展开"
                    @click="expandTable(0)"
                  />
                  <span>参数信息</span>
                  <i></i>
                </div>
                <div :class="['table-box', state.expandTableStatus[0].expand ? '' : 'hide']">
                  <n-public-table
                    rowKey="id"
                    :isDisplayAction="false"
                    :table-head-titles="state.tableHeadParamsTitles"
                    :showPagination="false"
                    :tableHeight="'auto'"
                    :maxHeight="246"
                    :tableData="state.tableParamsData"
                    :isNeedSelection="false"
                  />
                </div>

                <div class="debug-title">
                  <SvgIcon
                    v-if="state.expandTableStatus[1].expand"
                    icon="table-arrow-down"
                    title="示例数据收起"
                    @click="expandTable(1)"
                  />
                  <SvgIcon
                    v-else
                    class="noExpand"
                    icon="table-arrow-down"
                    title="示例数据展开"
                    @click="expandTable(1)"
                  />

                  <span>示例数据</span>
                  <i></i>
                </div>
                <div :class="['table-box', state.expandTableStatus[1].expand ? '' : 'hide']">
                  <n-public-table
                    rowKey="number"
                    :key="state.tableTestDataKey"
                    :isDisplayAction="false"
                    :table-head-titles="state.tableHeadTestDataTitles"
                    :showPagination="false"
                    :tableHeight="'auto'"
                    :maxHeight="246"
                    :tableData="state.testData"
                    :isNeedSelection="false"
                  />
                </div>

                <div class="debug-title">
                  <SvgIcon
                    v-if="state.expandTableStatus[2].expand"
                    icon="table-arrow-down"
                    title="调试结果收起"
                    @click="expandTable(2)"
                  />
                  <SvgIcon
                    v-else
                    class="noExpand"
                    icon="table-arrow-down"
                    title="调试结果展开"
                    @click="expandTable(2)"
                  />

                  <span>调试结果</span>
                  <i></i>
                </div>
                <div :class="['table-box', state.expandTableStatus[2].expand ? '' : 'hide']">
                  <n-public-table
                    rowKey="number"
                    :key="state.tableResultDataKey"
                    :isDisplayAction="false"
                    :table-head-titles="state.tableHeadResultDataTitles"
                    :showPagination="false"
                    :tableHeight="'auto'"
                    :maxHeight="246"
                    :tableData="state.resultData"
                    :isNeedSelection="false"
                /></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="box-operate">
      <n-button
        class="save"
        :loading="state.loading"
        size="sm"
        variant="solid"
        color="primary"
        @click.prevent="save"
        >保存</n-button
      >
      <n-button size="sm" variant="solid" color="primary" @click.prevent="cancel">返回</n-button>
    </div> -->
  </div>
</template>
<script>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import api from '@/api/index'
  import { useRouter } from 'vue-router'

  export default {
    name: '',
    components: {},
    props: {
      tId: {
        type: Number,
        default: null,
      },
      tStatus: {
        type: String,
        default: null,
      },
    },
    setup(props) {
      const router = useRouter()
      const ruleForm = ref()
      const state = reactive({
        ruleForm: {
          name: '',
          cnName: '',
          code: '',
          tagList: [],
          parameterList: [],
          description: '',
          createMethod: 'EXTERNAL_API',
          apiUrl: '',
          createByName: '',
          createTime: '',
          bizUseCount: '',
          projectUseCount: '',
          lastReferencedTime: '',
        },
        tableHeadParamsTitles: [
          { prop: 'number', name: '序号', width: 80 },
          { prop: 'parameterTypeCname', name: '参数类型' },
          { prop: 'parameterName', name: '参数名' },
          { prop: 'valueTypeName', name: '取值类型' },
          { prop: 'description', name: '描述信息' },
        ],
        tableParamsData: {},
        tableHeadTestDataTitles: [],
        testData: {},
        tableHeadResultDataTitles: [],
        resultData: {},
        editId: null,
        tableTestDataKey: 1,
        tableResultDataKey: 1,
        expandTableStatus: [
          {
            expand: true,
          },
          {
            expand: true,
          },
          {
            expand: true,
          },
        ],
        allTags: [],
      })

      const methods = {
        //获取标签列表
        getAlgorithmTagList() {
          api.dataDev.algorithmTagList().then((res) => {
            let { success, data } = res
            if (success) {
              data.map((item) => {
                return Object.assign(item, { value: item.id, active: false })
              })

              state.allTags = data
            }
          })
        },
        //设置标签颜色
        setTagColor(name, type = 'border') {
          let borderColors = ['#FF9729', '#04C495', '#447DFD']
          let bgColors = ['#FFF5ED', '#E6FFF4 ', '#F0F7FF ']
          let result = ''
          let allTagsNames = state.allTags.map((item) => item.name)
          let useColor = []

          if (type === 'border') {
            useColor = borderColors
          } else {
            useColor = bgColors
          }
          if (allTagsNames.includes(name)) {
            //第一个是写死的全部，所以-1
            result = useColor[allTagsNames.indexOf(name) % 3] || useColor[0]
          } else {
            result = useColor[0]
          }

          return result
        },
        //展开收起table内容
        expandTable(index) {
          state.expandTableStatus[index].expand = !state.expandTableStatus[index].expand
        },
        //算法中心查看
        getAlgorithmDetail() {
          api.dataDev.algorithmDetail({ id: state.editId }).then((res) => {
            let { success, data } = res
            if (success) {
              data.parameterList?.forEach((item, index) => {
                item.parameterTypeCname = ''
                item.number = index + 1
                if (item.parameterType === 'INPUT') {
                  item.parameterTypeCname = '入参'
                }
                if (item.parameterType === 'OUTPUT') {
                  item.parameterTypeCname = '出参'
                }
              })

              Object.keys(state.ruleForm).forEach((key) => {
                state.ruleForm[key] = data[key] === 0 ? data[key] : data[key] || null
              })
              state.tableParamsData = { list: state.ruleForm.parameterList }
            }
          })
        },
        //获取最后一次调试结果-通过的
        getAlgorithmDebugLogLast() {
          let box_width = document.querySelectorAll('.add-box')[0].clientWidth || 940
          api.dataDev.algorithmDebugLogLast({ id: state.editId, status: 'SUCCESS' }).then((res) => {
            let { success, data } = res
            if (success) {
              //处理示例数据
              let _testData = JSON.parse(data.testData) || []
              _testData.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.testData = { list: _testData }
              if (_testData[0]) {
                Object.keys(_testData[0]).forEach((key) => {
                  if (key === 'number') {
                    state.tableHeadTestDataTitles.unshift({ prop: key, name: '序号', width: 80 })
                    return
                  }
                  state.tableHeadTestDataTitles.push({ prop: key, name: key, width: box_width / 6 })
                })
              }
              state.tableTestDataKey++

              //处理调试结果
              let _resultData = data.result || []
              _resultData.map((item, index) => {
                return Object.assign(item, { number: index + 1 })
              })
              state.resultData = { list: _resultData }
              if (_resultData[0]) {
                Object.keys(_resultData[0]).forEach((key) => {
                  if (key === 'number') {
                    state.tableHeadResultDataTitles.unshift({ prop: key, name: '序号', width: 80 })
                    return
                  }
                  state.tableHeadResultDataTitles.push({
                    prop: key,
                    name: key,
                    width: box_width / 6,
                  })
                })
              }
              state.tableResultDataKey++
            }
          })
        },
        cancel() {
          //审批跳转记录状态
          if (props.tStatus) {
            if (props.tStatus === 'DONE') {
              let params = {
                active: 'second',
              }
              router.push({
                name: 'auditAlgorithmList',
                query: params,
              })
            } else {
              let params = {
                active: 'first',
              }
              router.push({
                name: 'auditAlgorithmList',
                query: params,
              })
            }
          } else {
            router.go(-1)
          }
        },
      }
      onMounted(() => {
        // methods.getAlgorithmTagList()
        nextTick(() => {
          state.editId = router.currentRoute.value.query.editId || props.tId
          if (state.editId) {
            methods.getAlgorithmDetail()
            methods.getAlgorithmDebugLogLast()
          }
        })
      })

      return {
        state,
        ruleForm,
        ...methods,
      }
    },
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .container {
    height: calc(100vh - 50px);
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .algorithm-center-detail {
    .add-box {
      height: 100%;
      border-radius: 8px;

      .page-title {
        position: relative;
        display: flex;
        align-items: center;
        padding: 16px;
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
        border-radius: 8px;
        .algorithm-see {
          margin-right: 8px;
          font-size: 16px;
        }
        span {
          color: rgba(0, 0, 0, 0.85);
          font-weight: bolder;
          font-size: 18px;
          line-height: 20px;
        }

        .detail-back-box {
          position: absolute;
          top: 0;
          right: 16px;
          bottom: 0;
          z-index: 9;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 127px;
          height: 33px;
          margin: auto;
          background-color: #fff;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
          &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 50%;
            width: 1px;
            height: 16px;
            margin: auto;
            background-color: #e5e5e5;
            content: '';
          }
          &-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50%;
            color: rgba(0, 0, 0, 0.46);
            font-size: 14px;
            cursor: pointer;
            .icon {
              width: 16px;
              height: 16px;
              margin-right: 2px;
              font-size: 16px;
            }
            &.checked {
              color: $themeBlue;
              &:hover {
                color: #6e9eff;
              }
              &:active {
                color: #2f5cd6;
              }
            }
            &:hover {
              color: #6e9eff;
            }
            &:active {
              color: #2f5cd6;
            }
          }
        }
      }

      .box-content {
        // padding: 0 20px;
        position: relative;
        height: calc(100% - 56px);
        margin-top: 4px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 8px;
        .box-content-inside {
          height: 100%;
          overflow: auto;
          background-color: #ebedf0;
          border-radius: 8px;
        }
        .inside-box {
          padding: 0;
          background-color: #fff;
          border-radius: 8px;

          .textarea-box {
            padding: 16px 20px;
          }
          :deep(.script-textarea) {
            width: 100%;
            height: 373px;
            padding: 16px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            background: rgba(242, 243, 246, 0.3);
            border: none;
            border-radius: 6px;
            &::placeholder {
              font-size: 14px;
              line-height: 363px;
              text-align: center;
            }
          }
          &.script-box,
          &.debug-box {
            margin-top: 4px;
            padding-bottom: 20px;
          }

          .box-title {
            display: flex;
            align-items: center;
            height: 52px;
            padding: 0 16px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            line-height: 48px;
            border-bottom: 1px solid #ebedf0;
            svg {
              margin-right: 8px;
              font-size: 16px;
            }
            span {
              position: relative;
              width: max-content;
            }

            // i {
            //   position: absolute;
            //   left: 0;
            //   right: 0;
            //   bottom: -2px;
            //   height: 6px;
            //   background: #008ae7;
            //   opacity: 0.2;
            // }
          }
          .tabs-box {
            padding: 0 16px;

            .table-box {
              margin-bottom: 8px;
              &.hide {
                height: 0;
                overflow: hidden;
              }
            }
            .debug-title {
              display: flex;
              align-items: center;
              height: 48px;
              color: rgba(0, 0, 0, 0.85);
              font-weight: bolder;
              font-size: 16px;
              line-height: 24px;
              svg {
                margin-right: 4px;
                font-size: 16px;
                cursor: pointer;
                &.noExpand {
                  transform: rotate(180deg);
                }
              }
              i {
                display: block;
                flex: 1;
                height: 1px;
                margin-left: 8px;
                border-bottom: 1px solid #e5e5e5;
              }
            }
            .common-table {
              :deep(.nancalui-table) {
                .nancalui-table__thead .header-container .title {
                  color: rgba(0, 0, 0, 0.85);
                  font-weight: bolder;
                  font-size: 14px;
                  line-height: 22px;
                }
                tbody > tr > td {
                  color: rgba(0, 0, 0, 0.65);
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 22px;
                }
              }
              :deep(.nancalui-table__empty) {
                padding: 0;
              }
            }
          }
        }

        .base-form {
          padding: 16px 20px 26px;
          :deep(.nancalui-form__label) {
            color: rgba(0, 0, 0, 0.85);
            font-weight: bolder;
            font-size: 14px;
            .nancalui-form__label-span {
              color: rgba(0, 0, 0, 0.85);
              font-weight: bolder;
              font-size: 14px;
            }
          }
          .form-detail-text {
            width: calc(100% - 8px);
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.65);
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
            &.have_bg {
              display: flex;
              align-items: center;
              height: 20px;
              margin-left: 4px;
              padding: 0 8px;
              color: #ffffff;
              font-weight: 500;
              font-size: 12px;
              background: #fff5ed;
              border: 1px solid #ff9729;
              border-radius: 10px;
            }
            .label {
              display: inline-block;
              box-sizing: border-box;
              height: 20px;
              margin-right: 6px;
              margin-bottom: 6px;
              padding: 0 4px;
              color: #1f84e1;
              font-size: 12px;
              line-height: 18px;
              background-color: #edf5fd;
              border: 1px solid #1f84e1;
              border-radius: 2px;
            }
          }
          .nancalui-form__item--horizontal {
            margin-bottom: 10px;
          }
          .nancalui-input {
            width: 400px;
          }

          .inline-box {
            display: flex;
            flex-wrap: wrap;

            .nancalui-form__item--horizontal {
              width: 33%;
            }
          }
          .check-style {
            .inline {
              display: flex;
              align-items: baseline;
              div {
                width: max-content;
              }
            }
          }
        }
      }
    }
  }
  .box-operate {
    height: 60px;
    margin-right: -10px;
    margin-left: -10px;
    padding: 17px 30px;
    text-align: center;
    background-color: #fff;
    border-radius: 8px 8px 0 0;
  }
</style>
