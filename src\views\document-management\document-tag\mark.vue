<template>
  <n-modal
    v-model="visiable"
    width="580px"
    class="largeDialog tagDialog"
    title="标签管理"
    @close="onClose(false)"
  >
    <n-form
      :data="state.formData"
      ref="formRef"
      label-width="100px"
      label-suffix="："
      label-align="end"
    >
      <n-form-item label="非结构化数据名称">
        <div class="name-content">
          <span class="name-left">{{ state.formData.name }}</span
          ><span v-if="ids.length > 1">等{{ ids.length }}个非结构化数据</span>
        </div>
      </n-form-item>
      <n-form-item label="标签">
        <div class="top-line-tag">
          <el-tree-select
            ref="selectTree"
            v-model="state.formData.tagList"
            :data="state.targetOptions"
            style="width: 100%"
            node-key="key"
            :props="{
              label: 'name',
              value: 'key',
              children: 'children',
            }"
            multiple
            show-checkbox
            :render-after-expand="false"
            filterable
            clearable
          />
        </div>
      </n-form-item>
    </n-form>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
</template>
<script setup>
  import api from '@/api/index'
  import { getCurrentInstance, reactive, ref } from 'vue'
  import { useRouter } from 'vue-router'
  const emit = defineEmits(['update:modelValue', 'needRefresh'])
  const { proxy } = getCurrentInstance()
  const visiable = ref(false)
  const formRef = ref(null)
  const selectTree = ref(null)
  const router = useRouter()
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    name: { type: String, default: '' },
    id: { type: String, default: '' },
    ids: { type: Array, default: [] },
  })
  const state = reactive({
    formData: {
      name: '',
      tagList: [],
    },
    targetOptions: [],
  })

  // 获取标签列表
  function getTargetList(val) {
    api.documentManage.getTagLibraryClassListHasTag().then((res) => {
      let { success, data } = res
      if (success) {
        state.targetOptions = data
      }
    })
  }
  // 获取详情
  function getDetail(docId) {
    api.documentManage
      .tagDetail({
        docId,
      })
      .then((res) => {
        if (res.success) {
          state.formData.tagList = res.data?.map((item) => 'tag_' + item.id)
        }
      })
  }
  //保存标签
  function onConfirm() {
    let params = {
      documentTagRelationList: [],
    }
    let activeOptions = selectTree.value?.getCheckedNodes().filter((val) => val.color)
    props.ids?.forEach((item) => {
      params.documentTagRelationList.push({
        docId: item,
        tags:
          activeOptions
            .filter((val) => val.nodeType === 'tag')
            .map((val) => {
              return { text: val.name, color: val.color, id: val.id || null }
            }) || [],
      })
    })
    api.documentManage.documentTagging(params).then((res) => {
      if (res.success) {
        proxy.$message.success('操作成功!')
        onClose(true)
      }
    })
  }
  //关闭弹框
  function onClose(needRefresh) {
    state.formData = {
      name: '',
      tagList: [],
    }
    visiable.value = false
    emit('update:modelValue', false)
    if (needRefresh) {
      emit('needRefresh')
    }
  }

  watch(
    () => props.modelValue,
    async (val) => {
      visiable.value = val
      state.formData.name = props.name
      if (props.id && val) {
        getDetail(props.id)
      }
      getTargetList()
    },
  )
</script>
<style lang="scss" scoped>
  .nancalui-form {
    :deep(.nancalui-form__control) {
      width: calc(100% - 100px) !important;
    }
    .name-content {
      display: flex;
      align-items: center;
      width: 100%;
      font-size: 14px;
      .name-left {
        max-width: calc(100% - 90px);
        margin: 4px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .top-line-tag {
    width: 100%;
    :deep(.nancalui-select) {
      width: 100%;
      height: auto;
      margin-bottom: 8px;
      .nancalui-select__multiple {
        max-height: 72px;
        overflow-y: auto;
        .nancalui-tag {
          flex-shrink: 0;
        }
      }
    }
    .nancalui-button {
      padding: 0 8px;
      .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        .icon {
          margin-right: 4px;
          font-size: 16px;
        }
      }
    }
  }
  .mark-name {
    width: 446px;
    overflow: hidden;
    color: #1d2129;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .mark-label-item {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .custom {
    padding: 6px;

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.9);
      font-weight: bolder;
      font-size: 14px;

      .icon {
        color: #8091b7;
        font-size: 16px;
        cursor: pointer;
      }
    }

    &-name {
      margin-top: 8px;
    }

    &-color {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      color: rgba(0, 0, 0, 0.55);
      font-size: 14px;

      .nancalui-switch {
        margin-left: 6px;
      }

      &-label {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 4px;

        .icon {
          display: none;
          color: #fff;
          font-size: 12px;
        }

        &.checked {
          box-shadow: 0 0 0 1px #447dfd;

          .icon {
            display: block;
          }
        }
      }
      &.second {
        margin-top: 8px;
      }
    }

    &-footer {
      margin-top: 14px;
      text-align: right;

      .nancalui-button {
        min-width: 40px;
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 24px;
      }
    }
  }
</style>
