import request from '@/utils/request'
// 个人数据治理-个人知识产权
export function getPersonDataGovernance(data) {
  return request({
    url: '/api/govern-management/workbench/user/intellectual',
    method: 'post',
    data,
  })
}

// 个人数据治理-个人只是产权信息获取-修改附件
export function getPersonDataGovernanceDetail(data) {
  return request({
    url: `/api/govern-management/workbench/user/intellectual/file/change`,
    method: 'post',
    data,
  })
}

// 数据标注-任务分页获取
export function getTaskPage(data) {
  return request({
    url: '/api/govern-management/workbench/task/search',
    method: 'post',
    data,
  })
}

// 数据标注-任务分页获取（新）
export function getTaskPageNew(data) {
  return request({
    url: '/api/govern-management/workbench/task/category/search',
    method: 'post',
    data,
  })
}

// 数据标注-任务分页获取详情
export function getTaskPageDetail(data) {
  return request({
    url: '/api/govern-management/workbench/task/search',
    method: 'post',
    data,
  })
}

// 数据标注-任务分页获取-全部
export function getTaskPageAll(data) {
  return request({
    url: '/api/govern-management/workbench/task/search/all',
    method: 'post',
    data,
  })
}

// 数据标注-获取标签明细
export function getTaskLabelDetail(data) {
  return request({
    url: `/api/govern-management/workbench/task/load/detail/${data}`,
    method: 'post',
  })
}

// 数据标注-新增任务
export function addTask(data) {
  return request({
    url: '/api/govern-management/workbench/task/add',
    method: 'post',
    data,
  })
}

// 数据标注-放弃任务
export function abandonTask(data) {
  return request({
    url: `/api/govern-management/workbench/task/abandon`,
    method: 'post',
    data,
  })
}

// 数据标注-删除任务目录
export function deleteTaskMenu(data) {
  return request({
    url: `/api/govern-management/workbench/task/remove/category/${data.projectId}/${data.documentCategoryId}`,
    method: 'post',
  })
}

// 数据标注-删除任务
export function deleteTask(data) {
  return request({
    url: `/api/govern-management/workbench/task/remove/${data}`,
    method: 'post',
  })
}

// 数据标注-编辑任务
export function editTask(data) {
  return request({
    url: '/api/govern-management/workbench/task/update',
    method: 'post',
    data,
  })
}

// 数据标注-新增任务-新增详情(这里的编辑是修改三种附件)
export function addTaskDetail(data) {
  return request({
    url: '/api/govern-management/workbench/task/detail',
    method: 'post',
    data,
  })
}

// 数据标注-新增任务-新增详情-回显(这里的编辑是修改三种附件)
export function addTaskDetailEcho(data) {
  return request({
    url: `/api/govern-management/workbench/task/detail/${data.workbenchTaskId}`,
    method: 'post',
    data,
  })
}

// 数据标注-新增任务-新增详情-删除
export function deleteTaskDetail(data) {
  return request({
    url: `/api/govern-management/workbench/task/detail/remove`,
    method: 'post',
    data,
  })
}

// 数据标注-标注工具-新增
export function addAnnotationTool(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/tool/add`,
    method: 'post',
    data,
  })
}

// 数据标注-标注工具-分页获取
export function getAnnotationTool(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/tool/search`,
    method: 'post',
    data,
  })
}

// 数据标注-标注工具-删除
export function deleteAnnotationTool(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/tool/remove/${data}`,
    method: 'post',
    data,
  })
}
// 数据标注-标注工具-编辑
export function editAnnotationTool(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/tool/update`,
    method: 'post',
    data,
  })
}

// 数据标注-团队管理-新增
export function addTeamManagement(data) {
  return request({
    url: `/api/govern-management/workbench/team/add`,
    method: 'post',
    data,
  })
}

// 数据标注-团队管理-分页获取
export function getTeamManagement(data) {
  return request({
    url: `/api/govern-management/workbench/team/search`,
    method: 'post',
    data,
  })
}

// 数据标注-团队管理-编辑
export function editTeamManagement(data) {
  return request({
    url: `/api/govern-management/workbench/team/update`,
    method: 'post',
    data,
  })
}

// 数据标注-团队管理-删除
export function deleteTeamManagement(data) {
  return request({
    url: `/api/govern-management/workbench/team/remove/${data}`,
    method: 'post',
    data,
  })
}
// 数据标注-团队管理-详情
export function getTeamManagementDetail(data) {
  return request({
    url: `/api/govern-management/workbench/team/detail/${data}`,
    method: 'post',
    data,
  })
}

// 团队下拉框-[type: ALL 展示所有团队； OWN 只展示自己团队]
export function getTeamList(data = { type: 'ALL' }) {
  return request({
    url: `/api/govern-management/workbench/team/info/${data.type}`,
    method: 'post',
    data,
  })
}

// 数据标注-团队管理-领取任务
export function claimTask(data) {
  return request({
    url: `/api/govern-management/workbench/team/task/add`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-任务分页获取
export function getAnnotationTask(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/search`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-提交审批
export function submitApproval(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/submit/${data.id}`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-放弃
export function annotationDelete(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/abandon`,
    method: 'post',
    data,
  })
}
// 数据标注-审核任务接口-任务分页获取
export function getAuditTask(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/audit/search`,
    method: 'post',
    data,
  })
}

// 数据标注-审核任务接口-标注后数据文件
export function getAnnotationFile(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/upload`,
    method: 'post',
    data,
  })
}

// 数据标注-审核任务接口-上传审核结论
export function uploadAuditConclusion(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/audit/conclusion/upload`,
    method: 'post',
    data,
  })
}

// 数据标注-标注任务接口-审批通过
export function approvalPass(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/task/audit/pass/${data.id}`,
    method: 'post',
    data,
  })
}

export function getUserList(data) {
  return request({
    url: `/api/govern-management/feign/user/list`,
    method: 'post',
    data,
  })
}

// 分页获取标注总览项目
export function getProjectPage(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/search`,
    method: 'post',
    data,
  })
}
// 全量项目获取
export function getProjectAll(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/all`,
    method: 'post',
    data,
  })
}

// 新增标注总览项目
export function addProject(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/add`,
    method: 'post',
    data,
  })
}
// 标注规则
export function getRule(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/rule/search`,
    method: 'post',
    data,
  })
}

// 新增标注总览项目-收藏
export function addProjectCollection(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/collection/add`,
    method: 'post',
    data,
  })
}

// 新增标注总览项目-取消收藏
export function deleteProjectCollection(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/collection/cancel`,
    method: 'post',
    data,
  })
}

// 新增标注总览项目-收藏列表
export function getProjectCollection(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/collection/search`,
    method: 'post',
    data,
  })
}
// 募集资金累加
export function getRaisedFunds(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/raiseFunds`,
    method: 'post',
    data,
  })
}
// 标注任务领取
export function receiveTask(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/project/task/receive`,
    method: 'post',
    data,
  })
}

// 自动标注
export function autoAnnotation(data) {
  return request({
    url: `/api/govern-management/workbench/auto/annotation/push`,
    method: 'post',
    data,
  })
}

// 自动标注结果获取
export function autoAnnotationResult(data) {
  return request({
    url: `/api/govern-management/workbench/auto/annotation/result`,
    method: 'post',
    data,
  })
}

// excel打标列表
export function excelAnnotationList(data) {
  const formData = new FormData()
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key])
    }
  }
  return request({
    url: `/api/govern-management/workbench/annotation/excel/data-list`,
    method: 'post',
    data: formData,
  })
}

// excel打标保存
export function excelAnnotationSave(data) {
  return request({
    url: `/api/govern-management/workbench/annotation/excel/save`,
    method: 'post',
    data,
  })
}

export default {
  getPersonDataGovernance,
  getPersonDataGovernanceDetail,
  getTaskPage,
  getTaskPageNew,
  getTaskPageDetail,
  getTaskPageAll,
  getTaskLabelDetail,
  addTask,
  abandonTask,
  deleteTaskMenu,
  deleteTask,
  editTask,
  addTaskDetail,
  addTaskDetailEcho,
  deleteTaskDetail,
  addAnnotationTool,
  getAnnotationTool,
  deleteAnnotationTool,
  editAnnotationTool,
  addTeamManagement,
  getTeamManagement,
  editTeamManagement,
  deleteTeamManagement,
  getTeamManagementDetail,
  getTeamList,
  claimTask,
  getAnnotationTask,
  submitApproval,
  getAuditTask,
  getAnnotationFile,
  uploadAuditConclusion,
  approvalPass,
  getUserList,
  getProjectPage,
  getProjectAll,
  addProject,
  getRule,
  addProjectCollection,
  deleteProjectCollection,
  getProjectCollection,
  annotationDelete,
  getRaisedFunds,
  receiveTask,
  autoAnnotation,
  autoAnnotationResult,
}
