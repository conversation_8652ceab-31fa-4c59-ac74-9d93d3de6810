<template>
  <div class="transfer-container">
    <div class="row" v-if="$attrs.info">
      <div class="item">
        <div> 中文名称： </div
        ><div :title="$attrs.info?.cnName">{{ $attrs.info?.cnName }}</div></div
      >
      <div class="item">
        <div> 英文名称： </div><div :title="$attrs.info?.name">{{ $attrs.info?.name }}</div></div
      >
    </div>
    <div class="select-container">
      <div class="all-tree">
        <!-- 搜素框 -->
        <div class="search">
          <n-input
            class="class-list-tree-ipt"
            v-model="state.treeSearchText"
            placeholder="请输入关键词"
            suffix="search"
            @change="(val) => dirSelectRef.treeRef.filter(val)"
          />
        </div>
        <DirSelect
          v-model:directoryIds="$attrs.selected"
          v-model:usreList="$attrs.usreList"
          :data="$attrs.data"
          :disableChecked="
            (node) => {
              return $attrs.disabled?.some((id) => id === node.data.id)
            }
          "
          :filter-node-method="filterNode"
          ref="dirSelectRef"
        />
      </div>
      <div class="select-list">
        <div class="search">
          <div class="btns" syule="heigth:32px;">
            <div class="tip"> {{ $attrs.selected?.length }} </div>
            <div
              class="clear-btn"
              @click="
                () => {
                  $attrs.usreList.splice(0, $attrs.selected.length)
                  $attrs.selected.splice(0, $attrs.selected.length)
                  $attrs.disabled.splice(0, $attrs.disabled.length)
                }
              "
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M13.2008 8H2.80078V13.5C2.80078 14.0522 3.2485 14.5 3.80078 14.5H12.2008C12.7531 14.5 13.2008 14.0522 13.2008 13.5V8Z"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M1.5 5.02776C1.5 4.47548 1.94772 4.02776 2.5 4.02776H5.05C5.60229 4.02776 6.05 3.58005 6.05 3.02776V2.5C6.05 1.94771 6.49772 1.5 7.05 1.5H8.95C9.50229 1.5 9.95 1.94772 9.95 2.5V3.02776C9.95 3.58005 10.3977 4.02776 10.95 4.02776H13.5C14.0523 4.02776 14.5 4.47548 14.5 5.02776V6.99996C14.5 7.55224 14.0523 7.99996 13.5 7.99996H2.5C1.94772 7.99996 1.5 7.55224 1.5 6.99996V5.02776Z"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path d="M5.0752 10.8889V14.5" stroke="currentColor" stroke-linecap="round" />
                  <path d="M8 10.8889V14.5" stroke="currentColor" stroke-linecap="round" />
                  <path d="M10.9258 10.8889V14.5" stroke="currentColor" stroke-linecap="round" />
                </svg>
              </span>
              全部清除</div
            >
          </div>
        </div>
        <div class="list">
          <div class="list-item" v-for="item in $attrs.usreList" :key="item">
            {{ item.username }}
            <div
              class="icon"
              @click="
                () => {
                  $attrs.usreList.splice($attrs.selected.indexOf(item.id), 1)
                  $attrs.selected.splice($attrs.selected.indexOf(item.id), 1)
                  $attrs.disabled.splice($attrs.disabled.indexOf(item.id), 1)
                }
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import api from '@/api/index'
  import DirSelect from './dirSelect'
  const emit = defineEmits(['success'])
  const state = reactive({
    treeSearchText: '',
  })
  const dirSelectRef = ref(null)
  const cancel = () => {}
  const createDir = () => {}

  const filterNode = (value, data) => {
    if (!value) return true
    return data.username?.includes(value) || data.departmentFullName?.includes(value)
  }
  // 还原表单
  const resetForm = (() => {
    // const resetData = JSON.parse(JSON.stringify(state.syncForm))
    return function () {
      //   state.currentNode = null
      //   state.syncForm = JSON.parse(JSON.stringify(resetData))
    }
  })()
  defineExpose({
    open(node) {
      resetForm()
    },
  })
</script>
<style></style>
<style lang="scss" scoped>
  .transfer-container {
    .select-container {
      display: flex;
      align-items: center;
      align-self: stretch;
      & > div {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1 0 0;
        align-self: stretch;
        height: 408px;
      }
      .all-tree {
        border-radius: 2px 0px 0px 2px;
        border-top: 1px solid var(---, #e5e6eb);
        border-bottom: 1px solid var(---, #e5e6eb);
        border-left: 1px solid var(---, #e5e6eb);
        background: var(--100, #fff);
        padding-bottom: 16px;
      }
      .select-list {
        border-radius: 0px 2px 2px 0px;
        border: 1px solid var(---, #e5e6eb);
        background: var(--100, #fff);
        .btns {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex: 1 0 0;
          height: 32px;
          .clear-btn {
            display: flex;
            padding: 5px 0px;
            align-items: center;
            gap: 4px;
            color: var(---, #1e89ff);
            cursor: pointer;
          }
        }
        .tip {
          color: var(----, #1d2129);

          /* 常用/r400/h9 */
          font-family: 'Source Han Sans CN';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          display: flex;
          gap: 2px;
          &:before {
            content: '已选';
            display: inline-block;
            color: var(---, #909399);
          }
          &:after {
            content: '个用户';
            display: inline-block;
            color: var(---, #909399);
          }
        }

        .list {
          display: flex;
          padding: 0px 8px;
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
          flex: 1 0 0;
          align-self: stretch;
          &-item {
            display: flex;
            margin-bottom: 4px;
            padding: 5px 8px;
            justify-content: space-between;
            align-items: center;
            gap: 8px;
            align-self: stretch;
            height: 32px;
            overflow: hidden;
            color: var(----, #1d2129);
            text-overflow: ellipsis;
            white-space: nowrap;

            /* 常用/r400/h9 */
            font-family: 'Source Han Sans CN';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            cursor: default;

            .icon {
              content: url('data:image/svg+xml;base64,CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CiAgPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzE5ODZfNDQ4Nzk2KSI+CiAgICA8cGF0aCBkPSJNMi41IDRIMTMuNUgyLjVaTTE0IDE0QzE0IDE0LjgyODQgMTMuMzI4NCAxNS41IDEyLjUgMTUuNUgzLjVDMi42NzE1NyAxNS41IDIgMTQuODI4NCAyIDE0SDNDMyAxNC4yNzYxIDMuMjIzODYgMTQuNSAzLjUgMTQuNUgxMi41QzEyLjc3NjEgMTQuNSAxMyAxNC4yNzYxIDEzIDE0SDE0Wk0zLjUgMTUuNUMyLjY3MTU3IDE1LjUgMiAxNC44Mjg0IDIgMTRWNEgzVjE0QzMgMTQuMjc2MSAzLjIyMzg2IDE0LjUgMy41IDE0LjVWMTUuNVpNMTQgNFYxNEMxNCAxNC44Mjg0IDEzLjMyODQgMTUuNSAxMi41IDE1LjVWMTQuNUMxMi43NzYxIDE0LjUgMTMgMTQuMjc2MSAxMyAxNFY0SDE0WiIgZmlsbD0iIzFFODlGRiIvPgogICAgPHBhdGggZD0iTTQgNEgxMkg0Wk0xMi41IDJDMTIuNSAxLjE3MTU3IDExLjgyODQgMC41IDExIDAuNUg1QzQuMTcxNTcgMC41IDMuNSAxLjE3MTU3IDMuNSAySDQuNUM0LjUgMS43MjM4NiA0LjcyMzg2IDEuNSA1IDEuNUgxMUMxMS4yNzYxIDEuNSAxMS41IDEuNzIzODYgMTEuNSAySDEyLjVaTTUgMC41QzQuMTcxNTcgMC41IDMuNSAxLjE3MTU3IDMuNSAyVjRINC41VjJDNC41IDEuNzIzODYgNC43MjM4NiAxLjUgNSAxLjVWMC41Wk0xMi41IDRWMkMxMi41IDEuMTcxNTcgMTEuODI4NCAwLjUgMTEgMC41VjEuNUMxMS4yNzYxIDEuNSAxMS41IDEuNzIzODYgMTEuNSAyVjRIMTIuNVoiIGZpbGw9IiMxRTg5RkYiLz4KICAgIDxsaW5lIHgxPSIxIiB5MT0iMy41IiB4Mj0iMTUiIHkyPSIzLjUiIHN0cm9rZT0iIzFFODlGRiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgICA8bGluZSB4MT0iMTEuNSIgeTE9IjEyLjUiIHgyPSI0LjUiIHkyPSIxMi41IiBzdHJva2U9IiMxRTg5RkYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgogIDwvZz4KICA8ZGVmcz4KICAgIDxjbGlwUGF0aCBpZD0iY2xpcDBfMTk4Nl80NDg3OTYiPgogICAgICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9IndoaXRlIi8+CiAgICA8L2NsaXBQYXRoPgogIDwvZGVmcz4KPC9zdmc+');
              display: none;
              width: 16px;
              height: 16px;
              cursor: pointer;
            }

            &:hover {
              border-radius: 2px;
              background: var(---, #ebf4ff);
              .icon {
                display: inline-block;
              }
            }
          }
        }
      }
      .search {
        display: flex;
        padding: 8px;
        align-items: flex-start;
        align-self: stretch;

        :deep(.nancalui-input__inner) {
          border-right: 1px solid #e5e6eb;
        }
      }
    }
  }

  .row {
    padding: 5px 0px;
    display: flex;
    align-items: center;
    gap: 53px;
    // & > div {
    // }
    .item {
      display: flex;
      align-items: center;
      max-width: 50%;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */

      // 禁止换行-超出隐藏
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      & > div:nth-child(1) {
        color: var(----, #1d2129);
        white-space: nowrap;
      }
      & > div:nth-child(2) {
        color: var(----, #909399);

        // 禁止换行-超出隐藏
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>
