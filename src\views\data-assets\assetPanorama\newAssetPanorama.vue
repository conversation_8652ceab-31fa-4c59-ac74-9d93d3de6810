<template>
  <div class="newAssetPanorama">
      <iframe ref="myIframe" class="iframe" src="http://**************:10005?isFromLeData=true"></iframe>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const myIframe = ref(null);
onMounted(() => {
  // 确保iframe加载完成
  // myIframe.value.onload = () => {
  //   const iframeDocument = myIframe.value.contentDocument || myIframe.value.contentWindow.document;
  //   const appContent = iframeDocument.querySelector('div.app-container');
  //   //通过contentWindow来查询iframe内部元素
  //   appContent.style.padding = '16px'
  // };
});
</script>

<style lang="scss" scoped>
  .newAssetPanorama{
    position: relative;
    width: 100%;
    height: 100%;
    .iframe{
      width: 100%;
      height: 100%;
      border: none;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
      flex-shrink: 0;
    }
  }
</style>
