<template>
  <div :class="'node ' + colorClass + (form.incomplete ? ' highlight' : '')">
    <img class="icon" :src="form.icon" />
    <div class="name">{{ form.name }}</div>

    <svg
      v-if="!form.isVisualization"
      @click="showMenu = !showMenu"
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
    >
      <circle cx="8.5" cy="3.5" r="0.5" fill="currentColor" stroke="currentColor" />
      <circle cx="8.5" cy="8" r="0.5" fill="currentColor" stroke="currentColor" />
      <circle cx="8.5" cy="12.5" r="0.5" fill="currentColor" stroke="currentColor" />
    </svg>
    <div v-if="showMenu" class="list-menu">
      <div class="menu-item" @click="delNode">删除</div>
      <div class="menu-item" @click="openLog">查看日志</div>
    </div>
  </div>

  <logPop ref="logPopRef" @success="emit('change')" />
</template>

<script>
  import logPop from './logPop.vue'
  export default {
    name: 'Box',
    components: { logPop },
    inject: ['getGraph', 'getNode'],
    data() {
      return {
        showMenu: false,
        colorClass: '', // 颜色控制
        highlightClass: '', // 配置不完全高亮控制
        form: {
          nodeName: '',
          incomplete: false,
          isVisualization: false,
          name: '',
          input: '',
          output: '',
          description: '',
          sql: '',
          sqlType: '0',
          taskType: '',
          icon: '',
        },
      }
    },
    mounted() {
      const node = this.getNode()
      this.form = node.getData()

      // 监听数据改变事件
      node.on('change:data', ({ current }) => {
        this.form = current
      })
      document.addEventListener('click', this.handleDocumentClick)
    },
    beforeUnmount() {
      document.removeEventListener('click', this.handleDocumentClick)
    },
    methods: {
      delNode() {
        const graph = this.getGraph()
        const node = this.getNode()
        graph.removeNode(node)
      },
      handleDocumentClick(event) {
        // 判断点击是否在节点之外
        if (!this.isClickInsideNode(event)) {
          // 执行隐藏逻辑
          this.showMenu = false
        }
      },
      isClickInsideNode(event) {
        return event.target.className.baseVal ? false : true
      },
      openLog() {
        this.$refs.logPopRef.open(this.form)
      },
    },
  }
</script>

<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  .node {
    position: relative;
    width: 256px;
    height: 44px;
    display: inline-flex;
    padding: 8px 6px 8px 10px;
    align-items: center;
    gap: 8px;
    border-radius: 2px;
    border: 1px solid var(---, #c9cdd4);
    background: var(--100, #fff);

    /* 小-二级菜单 */
    box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);
    &:hover {
      border-color: #479dff;
      background: #ebf4ff;

      svg {
        color: #1e89ff;
      }
    }

    .name {
      width: 180px;
      overflow: hidden;
      color: var(----, rgba(0, 0, 0, 0.75));
      text-overflow: ellipsis;
      white-space: nowrap;

      /* 常用/r400/h9 */
      font-family: 'Source Han Sans CN';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }

    .list-menu {
      position: absolute;
      bottom: -64px;
      right: 0;
      width: 100px;
      border-radius: 2px;
      border: 1px solid var(---, #c9cdd4);
      background: #fff;

      /* 小-二级菜单 */
      box-shadow: 0px 2px 8px -2px rgba(30, 47, 85, 0.15);
      .menu-item {
        height: 30px;
        line-height: 30px;
        padding: 0 12px;
        color: var(----, #1d2129);

        /* 常用/r400/h9 */
        font-family: 'Source Han Sans CN';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;

        &:hover {
          background: #ebf4ff;
        }
      }
    }
  }
</style>
