{"0 debug pnpm:scope": {"selected": 1, "workspacePrefix": "E:\\nc\\yy-management-platform"}, "1 error pnpm": {"errno": 134, "code": "ELIFECYCLE", "pkgid": "yy-admin@0.0.2", "stage": "dev", "script": "vite --mode development", "pkgname": "yy-admin", "err": {"name": "pnpm", "message": "yy-admin@0.0.2 dev: `vite --mode development`\nExit status 134", "code": "ELIFECYCLE", "stack": "pnpm: yy-admin@0.0.2 dev: `vite --mode development`\nExit status 134\n    at EventEmitter.<anonymous> (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\pnpm.cjs:108600:20)\n    at EventEmitter.emit (node:events:526:28)\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\pnpm.cjs:95166:18)\n    at ChildProcess.emit (node:events:526:28)\n    at maybeClose (node:internal/child_process:1092:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:302:5)"}}}