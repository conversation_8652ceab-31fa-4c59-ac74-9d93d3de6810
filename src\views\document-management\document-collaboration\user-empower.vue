<template>
  <n-modal v-model="visiable" width="1100px" title="协作人员列表" @close="onClose(false)">
    <div class="nc-m-b-8">
      <n-button @click="onAdd($event)" color="primary" variant="solid">
        <SvgIcon icon="tree-add-icon" class="nc-m-r-4" />
        添加协作
      </n-button>
    </div>
    <div style="height: 460px" v-loading="state.loading">
      <CfTable
        actionWidth="80"
        :calcHeight="false"
        :table-head-titles="state.tableHeadTitles"
        :tableConfig="{
          data: state.tableData,
          rowKey: 'id',
        }"
        :paginationConfig="{
          total: state.pagination.total,
          pageSize: state.pagination.pageSize,
          currentPage: state.pagination.currentPage,
          onCurrentChange: (v) => {
            state.pagination.currentPage = v
            onSearch(false)
          },
          onSizeChange: (v) => {
            state.pagination.pageSize = v
            onSearch()
          },
        }"
      >
        <template #editor="{ data: { row } }">
          <n-button variant="text" color="primary" @click="onDelete(row)">删除</n-button>
        </template>
        <template #tagList="{ row }">
          <level-tag
            :bgColor="row.bgColor"
            :borderColor="row.borderColor"
            v-show="row.secretLevelName"
            :color="row.color"
            >{{ row.secretLevelName }}</level-tag
          >
        </template>
      </CfTable>
    </div>
    <template #footer>
      <n-modal-footer>
        <n-button style="margin-left: 8px" plain @click="onClose(false)">取消</n-button>
      </n-modal-footer>
    </template>
  </n-modal>
  <n-modal
    v-model="state.isShow"
    title="添加协作人员"
    class="add-tree-modal"
    width="560px"
    :close-on-click-overlay="false"
    @close="
      () => {
        state.isShow = false
        state.treeSearchText = ''
      }
    "
  >
    <section class="tree-body">
      <n-input
        class="asideTree-search-input"
        v-model="state.treeSearchText"
        placeholder="请输入姓名查询"
        @input="searchUsersFn"
      >
        <template #append>
          <n-button class="search-btn" icon="search" />
        </template>
      </n-input>
      <n-tree
        ref="treeRef"
        :data="state.treeData"
        check
        :prop="{
          label: 'name',
          value: 'id',
          children: 'children',
        }"
      >
        <template #icon="{ nodeData, toggleNode }">
          <span v-if="nodeData.isLeaf" class="nancalui-tree-node__indent"></span>
          <span
            v-else
            @click="
              (event) => {
                event.stopPropagation()
                toggleNode(nodeData)
              }
            "
          >
            <SvgIcon
              v-if="nodeData.expanded"
              class="nancalui-tree-switch nc-m-r-4"
              icon="tree-contract-new"
            />
            <SvgIcon v-else icon="tree-open-new" class="nancalui-tree-switch nc-m-r-4" />
          </span>
        </template>
      </n-tree>
    </section>
    <template #footer>
      <div class="nancalui-modal__footer">
        <n-button
          @click.prevent="
            () => {
              state.isShow = false
              state.treeSearchText = ''
            }
          "
          >取 消</n-button
        >
        <n-button v-loading="state.editLoading" variant="solid" @click.prevent="onConfirm"
          >确 定</n-button
        >
      </div>
    </template>
  </n-modal>
</template>
<script setup>
  import api from '@/api/index'
  import { getCurrentInstance, reactive, ref } from 'vue'
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
  })
  const state = reactive({
    tableData: [],
    tagList: {
      非涉密: {
        color: '#1AA4EE',
        borderColor: 'rgba(26, 164, 238, 0.40)',
        bgColor: 'rgba(26, 164, 238, 0.08)',
      },
      一般: {
        color: '#FE8624',
        borderColor: '#FFBA70',
        bgColor: '#FFF4E6',
      },
      重要: {
        color: '#D40000',
        borderColor: '#EF7777',
        bgColor: '#FFEDED',
      },
      核心: {
        color: '#224ECD',
        borderColor: 'rgba(34, 78, 205, 0.40)',
        bgColor: 'rgba(34, 78, 205, 0.08)',
      },
    },
    tableHeadTitles: [
      { prop: 'staffCode', name: '工号' },
      { prop: 'staffName', name: '姓名' },
      { prop: 'deptName', name: '部门' },
      { prop: 'secretLevelName', name: '密级', slot: 'tagList' },
    ],
    pagination: {
      total: 0,
      currentPage: 1,
      pageSize: 10,
    },
    loading: false,
    isShow: false,
    treeSearchText: '',
    treeData: [],
    editLoading: false,
    timer: null,
  })
  const treeRef = ref(null)
  function searchUsersFn() {
    //防抖
    clearTimeout(state.timer)
    state.timer = setTimeout(() => {
      onAdd('', state.treeSearchText)
    }, 300)

    // console.log('searchUsersFn', treeRef)
    // treeRef.value.treeFactory.searchTree(state.treeSearchText, {
    //   isFilter: true,
    //   matchKey: 'name',
    // })
  }
  // 获取人员列表
  function onSearch(init = true) {
    if (init) {
      state.pagination.currentPage = 1
    }
    state.loading = true
    api.documentManage
      .collaborateAuthListPage({
        condition: props.id,
        pageNum: state.pagination.currentPage,
        pageSize: state.pagination.pageSize,
      })
      .then((res) => {
        state.loading = false
        if (res.success) {
          state.tableData = res.data.list.map((i) => {
            if (i.secretLevelName) {
              const { color, bgColor, borderColor } = state.tagList[i.secretLevelName]
              return {
                ...i,
                bgColor,
                color,
                borderColor,
              }
            } else {
              return i
            }
          })
          state.pagination.total = res.data.total
        }
      })
      .catch((e) => {
        console.log(e)
        state.loading = false
        state.tableData = []
      })
  }
  async function onAdd(el, search = '') {
    state.isShow = true
    let params = {
      pageNum: 1,
      pageSize: 100,
      condition: {
        departmentFullId: '1',
        departmentFullName: '全部',
      },
    }
    if (search) {
      params.condition = {
        name: search,
        username: search,
      }
    }
    api.system.userList(params).then((res) => {
      let { success, data } = res
      if (success) {
        const tableDataIds = new Set(state.tableData.map((item) => item.staffId))
        let newList = []
        data.list.forEach((item) => {
          if (!tableDataIds.has(item.id)) {
            newList.push({ ...item, name: item.name + '-' + item.username })
          }
        })
        state.treeData = [
          {
            departmentFullName: null,
            fullId: '1',
            id: 1,
            isDepartment: true,
            job: null,
            label: null,
            name: '公司',
            username: null,
            expanded: true,
            children: newList || [],
          },
        ]

        state.isShow = true
      }
    })
  }
  async function onDelete(row) {
    const res = await api.documentManage.collaborateAuthDelete({
      id: row.id,
    })
    if (res.success) {
      proxy.$message.success('删除成功')
      onSearch()
    }
  }
  async function onConfirm() {
    try {
      state.editLoading = true
      const staffIdList = treeRef.value.treeFactory
        .getCheckedNodes()
        ?.filter((node) => !node.isDepartment)
        ?.map((node) => node.id)
      if (staffIdList.length) {
        const res = await api.documentManage.collaborateAuthSave({
          docId: props.id,
          staffIdList: staffIdList,
        })
        if (res.success) {
          proxy.$message.success('添加成功')
          state.isShow = false
          state.treeSearchText = ''
          onSearch()
        }
      } else {
        proxy.$message.error('请选择人员')
      }
    } finally {
      state.editLoading = false
    }
  }
  const emit = defineEmits(['update:modelValue', 'needRefresh'])
  function onClose(needRefresh) {
    visiable.value = false
    emit('update:modelValue', false)
    if (needRefresh) {
      emit('needRefresh')
    }
  }

  const visiable = ref(false)
  watch(
    () => props.modelValue,
    async (val) => {
      visiable.value = val
      if (props.id) {
        onSearch()
      }
    },
  )
</script>
<style lang="scss" scoped>
  .add-tree-modal {
    :deep(.nancalui-modal__body) {
      overflow: hidden;
      overflow-y: hidden !important;
    }
  }
  .tree-body {
    height: 300px;
    padding: 16px;
    overflow: hidden;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    :deep(.nancalui-tree) {
      width: calc(100% + 16px);
      height: 240px;
      overflow: auto;
    }
  }
  :deep(.el-table--fit, .el-table__inner-wrapper) {
    height: 100% !important;
  }
  :deep(.nancalui-tree-switch) {
    width: 18px;
    height: 18px;
  }
</style>
