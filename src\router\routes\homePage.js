import Layout from '@/layout'
// 功能模块-首页
export default [
  // 根据环境变量判断是否显示adminlogin登录页，只有在开发环境下才显示
  ...(import.meta.env.MODE === 'development'
    ? [
        {
          path: '/adminlogin',
          component: () => import('@/views/login/index'),
          // component: () => import('@/views/login/index-inte'),
          hidden: true,
        },
      ]
    : []),
  // 单点登录，成飞专用
  {
    path: '/singlesignon',
    name: 'singlesignon',
    component: () => import('@/views/single-signon/index.vue'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  {
    path: '/',
    name: 'ConvergencePage',
    component: () => import('@/views/login/convergence-page'),
    hidden: true,
  },
  {
    path: '/dataGovernance',
    component: Layout,
    name: 'DataGovernance',
    hidden: true,
    code: 'dataGovernance',
    meta: {
      title: '数据治理',
      icon: 'icon-dataGovernance',
      parentRouterName: '',
      code: 'dataGovernance',
    },
  },
  {
    path: '/dataAssets',
    component: Layout,
    name: 'DataAssets',
    hidden: true,
    code: 'dataAssets',
    meta: { title: '数据资产', icon: 'icon-dataAssets', parentRouterName: '', code: 'dataAssets' },
  },
  {
    path: '/systemManagement',
    component: Layout,
    name: 'SystemManagement',
    hidden: true,
    code: 'systemManagement',
    meta: { title: '系统管理', icon: 'icon-home', parentRouterName: '', code: 'systemManagement' },
  },
  {
    path: '/data_application',
    component: Layout,
    name: 'Data_application',
    hidden: true,
    code: 'data_application',
    meta: {
      title: '数据应用',
      icon: 'icon-data_application',
      parentRouterName: '',
      code: 'data_application',
    },
  },
  // 数据编辑，成飞专用
  {
    path: '/dataWeaving',
    name: 'dataWeaving',
    component: () => import('@/views/data-weaving/index.vue'),
    hidden: true,
  },
  // 上传，成飞专用
  {
    path: '/fileTokenUpload',
    name: 'fileTokenUpload',
    component: () => import('@/views/file-token-upload/index.vue'),
    hidden: true,
  },
  {
    path: '/workFlow',
    component: () => import('@/views/data-management/offline-development/components/workFlow.vue'),
    hidden: true,
    meta: {
      title: '离线开发',
      isIframe: true,
    },
  },
  {
    path: '/Entrance',
    name: 'Entrance',
    component: () => import('@/views/system-manage/LinkConfig/entrance'),
    hidden: true,
    meta: {
      title: '链接配置',
    },
  },
  // 404 page must be placed at the end !!!
  { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true },
]
