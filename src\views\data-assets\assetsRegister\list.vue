<template>
  <section class="container">
    <section class="container-box">
      <section class="tools">
        <div class="row">
          <div class="col">
            名称：
            <n-input
              v-model="state.originalFormInline.name"
              placeholder="请输入中文名/英文名"
              clearable
            />
            注册状态：
            <n-select v-model="state.originalFormInline.assetsRegistered">
              <n-option value="" name="全部" />
              <n-option value="REGISTERED" name="注册成功" />
              <n-option value="UNREGISTERED" name="未注册" />
              <n-option value="WAIT_AUDIT" name="待审批" />
              <n-option value="INVALID" name="已失效" />
            </n-select>
          </div>

          <div class="search">
            <div class="search-btn" @click.prevent="searchClickFn">查询</div>
            <div class="search-btn reset" @click.prevent="resetFn">重置</div>
          </div>
        </div>
      </section>
      <section class="container-box-table">
        <section ref="tableRef" class="table-container">
          <div class="tabs">
            <div
              :class="['tab', state.tabActive === 'menu' ? 'active' : '']"
              @click="tabActiveFn('menu')"
              >结构化数据</div
            >
            <!-- <div
              :class="['tab', state.tabActive === 'form' ? 'active' : '']"
              @click="tabActiveFn('form')"
              >表</div
            > -->
            <div
              :class="['tab', state.tabActive === 'file' ? 'active' : '']"
              @click="tabActiveFn('file')"
              >非结构化数据</div
            >
          </div>

          <template v-if="state.tabActive === 'form'">
            <CfTable
              actionWidth="180"
              :key="state.dataSource"
              ref="tableNoRef"
              :tableConfig="{
                data: state.dataSource,
                rowKey: 'id',
              }"
              :table-head-titles="state.formTableHead"
              :paginationConfig="{
                total: state.page.total,
                pageSize: state.page.pageSize,
                currentPage: state.page.pageNum,
                onCurrentChange: (v) => {
                  state.page.pageNum = v
                  getRegisterList()
                },
                onSizeChange: (v) => {
                  state.page.pageSize = v
                  getRegisterList()
                },
              }"
            >
              <template #assetsRegistered="{ row }">
                <i
                  :style="{
                    background: registerStatusColor(row.registerStatus),
                    width: '6px',
                    height: '6px',
                    display: 'inline-block',
                    'margin-right': '6px',
                    'border-radius': '20px',
                  }"
                  class="status"
                ></i
                >{{ registerStatus(row.registerStatus) }}
              </template>
              <template #label="{ row }">
                <cfTag :tagArr="row.label || '[]'" />
              </template>
              <template #securityLevel="{ row }">
                <div class="levelTag" :style="getSecurityLevelFn(row.securityLevel)?.style">{{
                  getSecurityLevelFn(row.securityLevel)?.name
                }}</div>
              </template>

              <template #editor="{ data: { row } }">
                <n-button variant="text" color="primary" @click="goJump('assetsRegSee', row)"
                  >查看</n-button
                >
                <n-button
                  v-if="row.registerStatus == 'UNREGISTERED' || row.registerStatus == 'INVALID'"
                  variant="text"
                  color="primary"
                  @click="goJump('assetsRegRegister', row)"
                  >注册资产</n-button
                >
              </template>
            </CfTable>
          </template>

          <template v-else-if="state.tabActive === 'file'">
            <DOC ref="docRef" :formInline="state.formInline" />
          </template>
          <template v-else>
            <Menu
              ref="menuRef"
              :formInline="state.formInline"
              @updateTab="
                (type) => {
                  state.tabActiveTwo = type
                }
              "
            />
          </template>
        </section>
      </section>
    </section>
  </section>
</template>

<script setup>
  import { useRoute, useRouter } from 'vue-router'
  import { confidentialityLevelOptions } from './components/utlis.js'
  import { assetsRegisterList } from '@/api/assets.js'
  import DOC from './document.vue'
  import Menu from './assets-menu.vue'
  import cfTag from '@/components/cfTag'

  /**
   * 路由对象
   */
  const route = useRoute()
  /**
   * 路由实例
   */
  const router = useRouter()

  const tableRef = ref()
  const menuRef = ref()
  const docRef = ref()

  const state = reactive({
    showCom: true,
    treeSearchText: '',
    tabActive: 'menu',
    tabActiveTwo: 'form',
    formTableHead: [
      { prop: 'cnName', name: '中文名称' },
      { prop: 'name', name: '英文名称' },
      { prop: 'assetsRegistered', name: '注册状态', slot: 'assetsRegistered' },
      { prop: 'dataNum', name: '数据条数' },
      { prop: 'label', name: '标签', slot: 'label', width: 300 },
      { prop: 'securityLevel', name: '密级', slot: 'securityLevel' },
    ],
    originalFormInline: {
      name: null,
      assetsRegistered: '',
    },
    formInline: {
      name: null,
      assetsRegistered: '',
    },
    treeList: [
      {
        id: -1,
        name: '全部',
        type: 'DIRECTORY',
        isTemplateDirectory: false,
        children: [],
      },
    ],
    dataSource: [],
    page: {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    tableHeight: 500,
  })

  onMounted(() => {
    state.tabActive = route.query.tabs ? route.query.tabs : 'menu'
    state.tableHeight = tableRef.value.clientHeight - 107

    getRegisterList()
  })

  const searchClickFn = () => {
    for (let key in state.originalFormInline) {
      state.formInline[key] = state.originalFormInline[key]
    }
    state.page.pageNum = 1
    getRegisterList()
  }

  const tabActiveFn = (active) => {
    state.tabActive = active
    const currentRoute = router.currentRoute.value
    router.push({ name: currentRoute.name, query: { tabs: active } }, { replace: true })
    searchClickFn()
  }

  // 注册状态转换
  const registerStatus = (status) => {
    const obj = {
      UNREGISTERED: '未注册',
      WAIT_AUDIT: '待审批',
      REGISTERED: '注册成功',
      INVALID: '已失效',
    }

    return obj[status]
  }
  const registerStatusColor = (status) => {
    const obj = {
      UNREGISTERED: '#A8ABB2',
      WAIT_AUDIT: '#1e89ff',
      REGISTERED: '#2CA340',
      INVALID: '#f54446',
    }

    return obj[status]
  }

  // 获取注册列表
  const getRegisterList = async () => {
    const { assetsRegistered, name } = state.formInline
    if (state.tabActive === 'menu') {
      if (state.tabActiveTwo === 'menu') {
        menuRef.value?.onSearch(state.formInline)
      } else {
        menuRef.value?.getFormList(state.formInline)
      }
      return false
    } else if (state.tabActive === 'file') {
      docRef.value?.onSearch(state.formInline)
      return false
    }

    const res = await assetsRegisterList({
      condition: {
        registerStatus: assetsRegistered,
        name,
      },
      pageNum: state.page.pageNum,
      pageSize: state.page.pageSize,
    })
    if (res.code === 'SUCCESS') {
      const { list, total } = res.data
      state.page.total = total
      state.dataSource = list
    }
  }

  // 重置查询参数
  const resetFn = () => {
    state.originalFormInline = {
      name: null,
      assetsRegistered: '',
    }
    searchClickFn()
  }
  const pageChange = (pageNum) => {
    state.page.pageNum = pageNum === 0 ? 1 : pageNum
    getRegisterList()
  }
  const pageSizeChange = (pageSize) => {
    state.page.pageNum = 1
    state.page.pageSize = pageSize
    getRegisterList()
  }

  // 获取密级数组
  const getSecurityLevelFn = (node) => {
    return confidentialityLevelOptions.filter((item) => item.value === node)[0]
  }

  const goJump = (name, row) => {
    sessionStorage.setItem('assetsReg', JSON.stringify(row))
    router.push({ name })
  }
</script>
<style lang="scss" scoped>
  @import '@/styles/variables.scss';
  @import '/src/styles/cf.scss';

  .tools {
    background-color: #fff;
    border-radius: 2px;
    &.open {
      height: 146px;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 10px 16px;
      .createTime {
        width: 260px;
        margin-right: 32px;
      }
      .col {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      :deep(.button-content) {
        .add {
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            margin-right: 4px;
          }
          .arrow {
            margin-left: 4px;
            color: #fff;
            font-size: 16px;
          }
        }
      }

      .nancalui-input,
      .nancalui-select,
      .nancalui-tree-select {
        width: 260px;
        margin-right: 16px;
      }

      .search {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          padding: 5px 16px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          background: #1e89ff;
          border: 1px solid #1e89ff;
          border-radius: 2px;
          cursor: pointer;

          &.reset {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #1d2129;
            background: #fff;
            border: 1px solid #dcdfe6;
            .icon {
              margin-left: 4px;
              font-size: 10px;
            }
          }
        }
      }

      &:first-of-type {
        box-sizing: border-box;
      }
    }
  }

  .container {
    padding: 16px;
    border-radius: 0;
    &-box {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      box-sizing: border-box;
      min-width: 0;
      background: transparent;

      &-table {
        display: flex;
        flex: 1;
        gap: 10px;
        height: calc(100% - 60px);
        .table-container {
          position: relative;
          display: flex;
          flex: 1 0 0;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
          height: calc(100%);
          overflow: hidden;
          background: var(--100, #fff);
          border-radius: 0px 0px 2px 2px;

          .tabs {
            display: flex;
            align-items: center;
            align-self: stretch;
            justify-content: flex-start;
            padding: 8px;

            .tab {
              display: flex;
              gap: 4px;
              align-items: center;
              align-self: stretch;
              justify-content: center;
              box-sizing: content-box;
              // width: 60px;
              height: 22px;
              padding: 5px 16px;
              color: #1d2129;
              font-size: 14px;
              background: #fff;
              border: 1px solid #dcdfe6;
              border-radius: 2px 0px 0px 2px;
              cursor: pointer;

              &.active {
                color: #fff;
                background: #1e89ff;
                border-color: #1e89ff;
              }
            }
          }

          .levelTag {
            width: 40px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
          }

          .nancalui-pagination {
            position: absolute;
            bottom: 0;
            left: 0;
            justify-content: flex-end;
            width: 100%;
            padding: 14px 16px;
            border-top: 1px solid #dcdfe6;
          }
        }
      }
    }
  }

  .confidentiality-level {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &-left {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: flex-start;
      max-width: 220px;
      padding-right: 1px;
      overflow: hidden;
    }

    &-label {
      flex-shrink: 0;
      height: 24px;
      margin-left: 6px;
      padding: 0 8px;
      color: rgba(0, 0, 0, 0.75);
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      background-color: #e3ecff;
      border: 1px solid #6e9eff;
      border-radius: 2px;

      &:first-of-type {
        margin-left: 0;
      }

      &.tip {
        margin-right: 0;
        cursor: pointer;
      }
    }
  }

  .confidentiality-level-label-more {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: 344px;
    padding: 6px;

    .confidentiality-level-label {
      margin-bottom: 8px;
    }
  }
</style>
<style lang="scss">
  .cf-list {
    margin: -10px;
    padding: 6px 0;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    background: var(--100, #fff);
    border-radius: 4px;
    &-item {
      display: flex;
      flex: 1 0 0;
      gap: 8px;
      align-items: center;
      width: 132px;
      padding: 5px 12px;
      color: #606266;
      cursor: pointer;
      &:hover {
        color: #1e89ff;
      }
    }
  }
</style>
