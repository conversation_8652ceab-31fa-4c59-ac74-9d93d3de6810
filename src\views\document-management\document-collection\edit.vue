<template>
  <div class="container overflow">
    <div class="cf-page-title">
      非结构化数据编制
      <div class="detail-back-box" @click.prevent="goBack"> 返回 </div>
    </div>
    <div class="white-box nc-p-16">
      <n-form
        :data="state.formData"
        ref="formRef"
        :rules="state.rules"
        label-width="150px"
        label-suffix="："
      >
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="name" label="非结构化数据名称">
              <n-input v-model="state.formData.name" @blur="nameExist" maxLength="200" />
            </n-form-item>
            <div class="name-exist" v-if="state.nameExist.id">
              <svgIcon icon="icon-worning-tips" class="nc-m-r-3" />
              <span
                >该非结构化数据已存在，非结构化数据编号 {{ state.nameExist.ruleCode }}
                <n-button variant="text" color="primary" @click="onPrewiew"
                  >查看非结构化数据 ></n-button
                ></span
              >
            </div>
          </n-col>
          <n-col span="12">
            <n-form-item field="categoryId" label="非结构化数据分类">
              <el-tree-select
                style="width: 100%"
                v-model="state.formData.categoryId"
                :data="state.classList"
                node-key="id"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }"
                filterable
                clearable
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="confidentialityLevel" label="密级">
              <n-select v-model="state.formData.confidentialityLevel" :allow-clear="true">
                <n-option
                  v-for="item in state.secretList"
                  :key="item.id"
                  :name="item.name"
                  :value="item.id"
                />
              </n-select>
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="associationDocIds" label="关联非结构化数据">
              <el-tree-select
                style="width: 100%"
                v-model="state.formData.associationDocIds"
                :data="state.docsList"
                multiple
                :render-after-expand="false"
                show-checkbox
                check-strictly
                check-on-click-node
                node-key="nodeId"
                :props="{
                  label: 'name',
                  children: 'children',
                  class: 'nc-line-1',
                }"
                filterable
                clearable
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-row :gutter="20">
          <n-col span="12">
            <n-form-item field="type" label="模板类型">
              <n-radio-group v-model="state.formData.type" direction="row" @change="onTypeChange">
                <n-radio v-for="item in state.typeList" :key="item.id" :value="item.id">{{
                  item.name
                }}</n-radio>
              </n-radio-group>
            </n-form-item>
          </n-col>
          <n-col span="12">
            <n-form-item field="templateId" label="选择模版">
              <el-tree-select
                v-model="state.formData.templateId"
                :data="state.templateList"
                style="width: 100%"
                node-key="id"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'templateList',
                }"
                filterable
                clearable
                @change="templateChange"
              />
            </n-form-item>
          </n-col>
        </n-row>
        <n-form-item>
          <n-textarea v-if="state.formData.type === 'word'" v-model="state.formData.content" />
          <div id="luckysheet" class="luckysheet" v-else-if="state.formData.type === 'excel'"></div>
        </n-form-item>
      </n-form>
    </div>
    <div class="white-box nc-m-t-10 nc-p-16">
      <div class="my-appliction" style="text-align: right">
        <n-button style="margin-left: 8px" plain @click="goBack">取消</n-button>
        <n-button :loading="loading" variant="solid" @click="onConfirm">确定</n-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { randomGuid } from '@/utils/index'
  import { onBeforeUnmount, reactive, nextTick, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import { exportExcel } from '@/views/data-management/resource-library/export'
  import { showtoolbarConfig } from '../config/sheet'

  import api from '@/api/index'
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const state = reactive({
    isLzos: import.meta.env.VITE_APP_LZOS,
    formData: {
      id: '',
      categoryId: '',
      name: '',
      confidentialityLevel: 'INTERIOR',
      associationDocIds: [],
      type: 'excel',
      templateId: '',
    },
    nameExist: {},
    rules: {
      categoryId: [
        { required: true, message: '请选择非结构化数据分类', trigger: 'change', type: 'number' },
      ],
      name: [{ required: true, message: '请输入非结构化数据名称', trigger: 'burl' }],
      confidentialityLevel: [{ required: true, message: '请选择密级', trigger: 'change' }],
      type: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
    },
    classList: [],
    secretList: [
      { id: 'PUBLIC', name: '公开' },
      { id: 'INTERIOR', name: '内部' },
      { id: 'CONTROLLED', name: '受控' },
      { id: 'SECRET', name: '秘密' },
      { id: 'CONFIDENTIAL', name: '机密' },
      { id: 'CORE', name: '核心' },
    ],
    docsList: [],
    typeList: [
      { name: 'word', id: 'word' },
      { name: 'excel', id: 'excel' },
    ],
    templateList: [],
  })
  // 非结构化数据重名校验
  function nameExist() {
    api.documentManage
      .documentationNameExist({
        name: state.formData.name,
      })
      .then((res) => {
        if (res.success) {
          state.nameExist = res.data || {}
        }
      })
  }
  // 模板类型切换
  function onTypeChange() {
    getTemplateList()
    if (state.formData.type === 'word') {
    } else {
      nextTick(() => {
        window.luckysheet.create({
          container: 'luckysheet', //luckysheet是容器id
          showinfobar: false,
          lang: 'zh', // 设定表格语言
          gridKey: state.formData.id ? state.formData.id : randomGuid(),
          showtoolbarConfig: showtoolbarConfig,
          title: state.formData.name,
          loadUrl: state.formData.id
            ? location.origin + '/api/govern-document/documentation/loadUrl'
            : '',
        })
      })
    }
  }
  function templateChange() {
    if (state.formData.type === 'word') {
    } else {
      nextTick(() => {
        window.luckysheet.create({
          container: 'luckysheet', //luckysheet是容器id
          showinfobar: false,
          lang: 'zh', // 设定表格语言
          gridKey: state.formData.templateId,
          showtoolbarConfig: showtoolbarConfig,
          title: state.formData.name,
          loadUrl: location.origin + '/api/govern-document/template/config/loadUrl',
        })
      })
    }
  }
  // 确定
  const formRef = ref(null)
  function onConfirm() {
    window.luckysheet.exitEditMode()
    formRef.value.validate().then((valid) => {
      if (valid) {
        const sheetData = window.luckysheet.getAllSheets()
        let apiType = state.formData.id ? 'documentationUpdate' : 'documentationSave'
        api.documentManage[apiType]({
          id: state.formData.id || null,
          categoryId: state.formData.categoryId,
          name: state.formData.name,
          confidentialityLevel: state.formData.confidentialityLevel,
          associationDocIds: state.formData.associationDocIds?.map((i) => {
            return i.split('-')[1] ? Number(i.split('-')[1]) : null
          }),
          type: state.formData.type,
          templateId: state.formData.templateId || null,
          jsonData: JSON.stringify(sheetData),
        }).then((res) => {
          if (res.success) {
            exportExcel(
              window.luckysheet.getAllSheets(),
              state.formData.name,
              res.data || state.formData.id,
              'document',
            )
            proxy.$message.success('保存成功')
            goBack()
          }
        })
      }
    })
  }
  // 预览
  function onPrewiew() {
    router.push({
      name: 'documentPreparationPreview',
      query: {
        id: state.nameExist.id,
        docUrl: state.nameExist.docUrl,
      },
    })
  }
  // 取消
  function goBack() {
    router.go(-1)
  }
  // 获取详情
  function getDetail() {
    api.documentManage.documentationGet({ id: state.formData.id }).then((res) => {
      state.formData = {
        ...res.data,
        templateId: res.data.templateId ? Number(res.data.templateId) : null,
        associationDocIds: res.data.associationDocIds?.map((i) => {
          return `doc-${i}`
        }),
      }
      if (state.formData.type === 'excel') {
        nextTick(() => {
          window.luckysheet.destroy()
          window.luckysheet.create({
            container: 'luckysheet', //luckysheet是容器id
            showinfobar: false,
            lang: 'zh', // 设定表格语言
            gridKey: state.formData.id,
            title: state.formData.name,
            showtoolbarConfig: showtoolbarConfig,
            loadUrl: location.origin + '/api/govern-document/documentation/loadUrl',
          })
        })
      }
    })
  }
  async function getClassifyTreeList() {
    const res = await api.documentManage.getClassifyTreeList()
    state.classList = res.data
    if (!state.formData.id && router.currentRoute.value?.query?.categoryId) {
      state.formData.categoryId = Number(router.currentRoute.value?.query?.categoryId)
    }
  }
  async function getTemplateList() {
    const res = await api.documentManage.templateSimpleTree({ type: state.formData.type })
    state.templateList = res.data.map((i) => {
      return {
        disabled: true,
        ...i,
      }
    })
  }
  // 获取关联非结构化数据
  async function getDocxList() {
    const res = await api.documentManage.associationTree({})
    state.docsList = res.data
    setDisabled(state.docsList)
  }
  function setDisabled(data) {
    data.forEach((item) => {
      item.nodeId = item.nodeType + '-' + item.id
      item.name = item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name
      if (item.nodeType === 'category' || String(item.id) === String(state.formData.id)) {
        item.disabled = true
        if (item.children?.length) setDisabled(item.children)
      }
    })
  }
  onBeforeUnmount(() => {
    window.luckysheet.destroy()
  })
  onMounted(async () => {
    state.formData.id = router.currentRoute.value?.query?.id
    await getClassifyTreeList()
    await getDocxList()
    if (router.currentRoute.value.query.id) {
      getDetail()
    }
    onTypeChange()
  })
</script>
<style lang="scss" scoped>
  @import '/src/styles/variables.scss';
  @import '/src/styles/cf.scss';
  .luckysheet {
    position: relative;
    width: 100%;
    height: calc(100vh - 410px);
    min-height: 400px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
  .name-exist {
    display: flex;
    align-items: center;
    margin-top: -10px;
    margin-left: 150px;
    color: var(---Warning-, #ff7d00);
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
  }
</style>
