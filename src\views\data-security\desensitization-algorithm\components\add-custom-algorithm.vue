<template>
  <!-- 自定义算法脱敏抽屉 -->
  <n-drawer
    v-model="state.drawer"
    title=""
    :size="550"
    :esc-key-closeable="false"
    :close-on-click-overlay="false"
    class="add-custom-algorithm-drawer"
  >
    <div class="n-drawer-body" v-loading="state.pageLoading">
      <div class="n-drawer-body-header">
        <div class="n-drawer-body-header-name">
          <div class="title">{{ state.editId ? '编辑' : '新建' }}自定义脱敏算法</div>
        </div>
        <SvgIcon class="close" icon="icon-close" @click.stop.prevent="closeDrawerFn" />
      </div>
      <div class="n-drawer-body-content">
        <n-tabs v-model="state.tabType">
          <n-tab id="basic" title="基础信息" />
          <n-tab id="setting" title="配置信息" :disabled="!state.ruleForm.templateId || !state.ruleForm.checked" />
        </n-tabs>
        <div v-show="state.tabType === 'basic'" :class="['base-content', state.expandTableStatus[0] ? '' : 'hide']">
          <n-form
            ref="ruleForm"
            :data="state.ruleForm"
            :rules="state.rules"
            label-width="81px"
            label-align="start"
            :pop-position="['right']"
            class="custom-algorithm-ruleForm scroll-box scroll-bar-style"
          >
            <n-form-item label="算法名称：" field="name">
              <n-input v-model="state.ruleForm.name" maxlength="30" placeholder="请输入算法名称" />
            </n-form-item>
            <n-form-item label="描述信息：" class="label_align_required">
              <n-textarea
                v-model="state.ruleForm.description"
                show-count
                maxlength="200"
                :autosize="{ minRows: 6 }"
              />
            </n-form-item>
            <n-form-item label="算法模板：" field="templateId">
              <div class="form-item-half">
                <n-select
                  v-model="state.ruleForm.type"
                  placeholder="请选择模板类型"
                  :options="state.algorithmTypeOptions"
                  @value-change="algorithmTypeChange"
                >
                </n-select>
                <n-select
                  :key="state.key"
                  v-model="state.ruleForm.templateId"
                  placeholder="请选择算法模板"
                  :options="state.algorithmTemplateDataOptions"
                  @value-change="algorithmChange"
                >
                </n-select>
              </div>
            </n-form-item>
<!--            <n-form-item v-if="state.ruleForm.templateId" label=" ">-->
<!--              <n-checkbox-->
<!--                :disabled="!state.ruleForm.templateId"-->
<!--                label="是否配置"-->
<!--                :isShowTitle="false"-->
<!--                v-model="state.ruleForm.checked"-->
<!--              />-->
<!--            </n-form-item>-->
          </n-form>
        </div>
        <div
          :class="['config-content', state.expandTableStatus[1] ? '' : 'hide']"
          v-if="state.ruleForm.templateId && state.ruleForm.checked && state.tabType === 'setting'"
        >
          <algorithmConfigMessage
            :key="state.drawerKey"
            ref="algorithmConfigMessageDom"
            :desensitizer="state.ruleForm.desensitizer"
          />
        </div>
      </div>
      <div class="options-box-bg">
        <div class="options-box-content">
          <n-button color="secondary" size="sm" @click.prevent="closeDrawerFn">取消</n-button>
          <n-button
            color="primary"
            size="sm"
            variant="solid"
            :loading="state.saveLoading"
            @click.prevent="submitForm"
            >确定</n-button
          >
        </div>
      </div>
    </div>
  </n-drawer>
</template>
<script>
  import { ref, reactive } from 'vue'
  import api from '@/api/index'
  import { ElNotification } from 'element-plus'
  import algorithmConfigMessage from './algorithm-config-message'
  import { checkCName } from '@/utils/validate'

  export default {
    components: { algorithmConfigMessage },

    emits: ['reloadPage'],
    setup(props, { emit }) {
      const ruleForm = ref()
      const algorithmConfigMessageDom = ref()

      const state = reactive({
        drawerKey: 1,
        key: 1,
        tabType: 'basic',
        expandTableStatus: new Array(10).fill(true),
        drawer: false,
        loading: false,
        pageLoading: false,
        saveLoading: false,
        editId: null,
        ruleForm: {
          name: null,
          description: null,
          type: null,
          templateId: null,
          desensitizer: '',
          checked: true,
        },
        rules: {
          name: [
            {
              required: true,
              validator: (...args) =>
                checkCName(...args, 'assets', 'desensitizationAlgoTestName', {
                  name: state.ruleForm.name,
                  id: state.editId || null,
                }),
              trigger: 'blur',
            },
          ],

          templateId: [
            { type: 'number', required: true, message: '请选择算法模板', trigger: 'change' },
          ],
        },

        algorithmTypeOptions: [
          {
            name: '哈希',
            value: 'HASH',
          },
          {
            name: '截断',
            value: 'TRUNCATION',
          },
          {
            name: '掩码',
            value: 'MASK',
          },
        ],
        algorithmTemplateData: {
          HASH: [],
          TRUNCATION: [],
          MASK: [],
        },
        algorithmTemplateDataOptions: [],
        allTemplateData: [],
      })
      const hasParamsTable = ref()
      const methods = {
        //展开收起table内容
        expandTable(index) {
          state.expandTableStatus[index] = !state.expandTableStatus[index]
        },

        algorithmTypeChange() {
          if (state.ruleForm.type) {
            state.algorithmTemplateDataOptions = state.algorithmTemplateData[state.ruleForm.type]
          } else {
            state.algorithmTemplateDataOptions = []
          }
          state.ruleForm.templateId = null
          state.ruleForm.desensitizer = ''
          state.key++
        },
        algorithmChange(data) {
          if (data?.desensitizer) {
            state.ruleForm.desensitizer = data.desensitizer
          } else {
            state.ruleForm.desensitizer = ''
          }
        },
        // 获取内置算法并分类
        async getTemplateAlgorithm() {
          let data = {
            condition: {
              builtIn: true,
            },
            pageNum: 1,
            pageSize: 100,
          }

          await api.assets['getDesensitizationAlgoList'](data).then((res) => {
            let { success, data } = res
            if (success) {
              state.allTemplateData = data.list || []
              state.algorithmTemplateData = {
                HASH: [],
                TRUNCATION: [],
                MASK: [],
              }

              data.list?.forEach((item) => {
                if (state.algorithmTemplateData[item.type]) {
                  if (
                    item.name === '身份证号码掩码' ||
                    item.name === '银行卡号掩码' ||
                    item.name === 'Email掩码' ||
                    item.name === '电话号码掩码'
                  ) {
                    return
                  }

                  state.algorithmTemplateData[item.type].push({
                    name: item.name,
                    value: item.id,
                    desensitizer: item.desensitizer,
                  })
                }
              })
            }
          })
        },

        //关闭抽屉
        closeDrawerFn() {
          state.ruleForm = {
            name: null,
            description: null,
            type: null,
            templateId: null,
            desensitizer: '',
            checked: true,
          }
          state.editId = null
          state.key++
          state.drawerKey++
          state.drawer = false
          state.tabType = 'basic'
        },

        //保存脱敏算法
        submitForm() {
          ruleForm.value.validate(async (valid) => {
            if (valid) {

              let result = {}
              let interface_url = 'desensitizationAlgoAdd'
              let message = '新建成功'
              if (algorithmConfigMessageDom.value) {
                result = await algorithmConfigMessageDom.value.getAllData()
                if (!result.passed) return
                if (result.passed) {
                  state.ruleForm['dateTruncConf'] = {
                    desensitizationDateType: result.data.desensitizationDateType || null,
                    truncPosition: result.data.truncPosition || null,
                  }
                  state.ruleForm['hashConf'] = {
                    salt: result.data.salt || null,
                    secKey: result.data.secKey || null,
                  }
                  state.ruleForm['maskPositionConf'] = {
                    from: result.data.from || null,
                    mInTheFront: result.data.mInTheFront || null,
                    nInTheBack: result.data.nInTheBack || null,
                    to: result.data.to || null,
                  }
                  state.ruleForm['numericalValueTruncConf'] = {
                    reserveX: result.data.reserveX || null,
                  }
                }
              }
              state.saveLoading = true
              if (state.editId) {
                interface_url = 'desensitizationAlgoUpdate' // 更新
                state.ruleForm.id = state.editId
                message = '更新成功'
              }


              api.assets[interface_url](state.ruleForm)
                .then((res) => {
                  state.saveLoading = false
                  let { success, data } = res
                  if (success) {
                    ElNotification({
                      title: '提示',
                      message,
                      type: 'success',
                    })

                    state.ruleForm = {
                      name: null,
                      description: null,
                      type: null,
                      templateId: null,
                      desensitizer: '',
                      checked: true,
                    }
                    state.editId = null
                    state.key++
                    state.drawerKey++
                    state.drawer = false
                    state.tabType = 'basic'
                    emit('reloadPage')
                  }
                })
                .catch(() => {
                  state.saveLoading = false
                })
            }
          })
        },

        //编辑回显数据
        async init(data) {
          if (data) {
            state.pageLoading = true
            state.editId = data.id || null
            await methods.getTemplateAlgorithm()

            methods.getAlgorithmMessage()
          } else {
            state.editId = null
            methods.getTemplateAlgorithm()
          }

          state.drawer = true
        },
        getAlgorithmMessage() {
          api.assets
            .getDesensitizationAlgoMessage({ id: state.editId })
            .then((res) => {
              state.pageLoading = false
              let { success, data } = res
              if (success) {
                Object.keys(state.ruleForm).forEach((key) => {
                  if (key === 'desensitizer') {
                    let _item = state.allTemplateData.filter((item) => item.id === data.templateId)

                    state.ruleForm[key] = _item[0]?.desensitizer
                  } else if (key === 'checked') {
                    state.ruleForm[key] = true
                  } else {
                    state.ruleForm[key] = data[key]
                  }
                })

                if (state.ruleForm.type) {
                  state.algorithmTemplateDataOptions =
                    state.algorithmTemplateData[state.ruleForm.type]
                } else {
                  state.algorithmTemplateDataOptions = []
                }
                state.key++
                nextTick(() => {
                  if (state.ruleForm.checked) {
                    algorithmConfigMessageDom.value?.editInit(data)
                  }
                })
              }
            })
            .catch(() => {
              state.pageLoading = false
            })
        },
      }

      return {
        state,
        hasParamsTable,
        ruleForm,
        algorithmConfigMessageDom,
        ...methods,
      }
    },
  }
</script>

<style lang="scss">
  .add-custom-algorithm-drawer {
    .nancalui-drawer__title {
      display: none;
    }
    .n-drawer-body {
      height: 100%;
    }

    &.nancalui-drawer .n-drawer-body-content {
      height: calc(100% - 52px);
      padding: 0;
      .nancalui-tabs-nav-tab {
        padding: 0 16px;
        border-bottom: 1px solid #c5d0ea;
        .nancalui-tabs-tab-title {
          padding: 0 10px;
        }
      }
      .base-content {
        padding: 0 16px;
        &.hide {
          height: 0;
          overflow: hidden;
        }
        .custom-algorithm-ruleForm {
          padding-bottom: 8px;
          .form-item-half {
            display: flex;
            width: 100%;
            .nancalui-select {
              width: calc((100% - 8px) / 2);
              &:first-child {
                margin-right: 8px;
              }
            }
          }
        }
      }
      .config-content {
        padding: 0 16px;
        &.hide {
          height: 0;
          overflow: hidden;
        }
      }
    }
    .n-drawer-body-header{
      position: relative;
      &:before {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        width: 4px;
        height: 18px;
        background: #1e89ff;
        content: '';
      }
    }
    .n-drawer-body-content {
      .nancalui-tabs{
        margin-bottom: 16px;
      }
      .content-title {
        display: flex;
        align-items: center;

        padding: 0 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bolder;
        font-size: 16px;
        line-height: 48px;
        svg {
          margin-right: 4px;
          font-size: 16px;
          cursor: pointer;
          &.noExpand {
            transform: rotate(180deg);
          }
        }
        i {
          display: block;
          flex: 1;
          height: 1px;
          margin-left: 8px;
          border-bottom: 1px solid #c5d0ea;
        }
      }
    }
    .options-box-bg {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 64px;
      padding: 16px;
      background-color: #fff;
      border-radius: 0 0 8px 8px;

      .options-box-content {
        display: inline-block;
        width: 100%;
        text-align: right;
        background-color: #fff;
      }
    }
  }
</style>
